namespace Config.CommonModel.Xbb;

public class XbbConfig
{
    public static string? Token = "1f106e102999d8f163e1e0dfdc10df63";
    public static string? Corpid = "ding310af7dd98e3ebdc35c2f4657eb6378f";
    public static string? UserId = "16248677402851943";
    public static string? ApiBaseUrl = "https://proapi.xbongbong.com";
}

/// <summary>
/// 销帮帮接口响应结果
/// </summary>
/// <typeparam name="T"></typeparam>
public class XBBResponse<T> where T : class
{
    /// <summary>
    /// 错误码。1表示操作成功。
    /// </summary>
    public int code { get; set; }

    /// <summary>
    /// 提示信息。
    /// </summary>
    public string? msg { get; set; }

    /// <summary>
    /// 返回数据信息，不同接口返回结构有所差异。
    /// </summary>
    public T? result { get; set; }

    /// <summary>
    /// 成功标志。true表示操作成功。
    /// </summary>
    public bool success { get; set; }
}

public class GetXbbRequest
{
    public long formId { get; set; }
    public string? corpid { get; set; }
    public string? userId { get; set; }
    public GetXbbBaseSort? sortMap { get; set; }
    public List<object>? conditions { get; set; }
}

public class GetXbbBaseSort
{
    public string? field { get; set; }
    public string? sort { get; set; }
}

public class GetXbbBaseConditions<T>
{
    public string? attr { get; set; }
    public string? symbol { get; set; }
    public List<T> value { get; set; } = new List<T>();
}

public class GetProjectList : GetXbbRequest
{
    public int page { get; set; }
    public int pageSize { get; set; }
    public string? attr { get; set; }
    public string? symbol { get; set; }
    public List<long> value { get; set; } = new List<long>();
}

public class GetProjectListResponse
{
    public int totalPage { get; set; }
    public int totalCount { get; set; }
    public List<GetProjectListInfo> list { get; set; } = default!;
}

public class GetProjectListInfo
{
    public long addTime { get; set; }
    public long updateTime { get; set; }
    public long dataId { get; set; }
    public GetProjectListInfoData? data { get; set; }
}

public class GetProjectListInfoData
{
    /// <summary>
    /// 数字诺亚编码
    /// 例：WY-YWWB-2023-0049
    /// </summary>
    public string? text_33 { get; set; }

    /// <summary>
    /// 合同主题
    /// </summary>
    public string? text_1 { get; set; }

    /// <summary>
    /// 合同状态(1=签约,2=执行中,3=完毕,4=终止,5=意外终止,6=过期)
    /// </summary>
    public string? text_6 { get; set; }

    /// <summary>
    /// 我方签约人(id)
    /// </summary>
    public string? text_8 { get; set; }

    /// <summary>
    /// 合同附件
    /// </summary>
    public string? file_1 { get; set; }

    /// <summary>
    /// 合同编号(前缀：SO.)
    /// 例：SO.20220919016
    /// </summary>
    public string? serialNo { get; set; }

    /// <summary>
    /// 合同分类(id)
    /// </summary>
    public string? text_14 { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public long date_3 { get; set; }

    /// <summary>
    /// 到期日期
    /// </summary>
    public long date_2 { get; set; }

    /// <summary>
    /// 合同性质(id)
    /// </summary>
    public string? text_16 { get; set; }

    /// <summary>
    /// 客户名称(其实是id)
    /// 例：8772266
    /// </summary>
    public string? text_2 { get; set; }

    /// <summary>
    /// 客户名称-引用
    /// 例：石家庄市高速公路集团有限公司
    /// </summary>
    public string? text_56 { get; set; }

    /// <summary>
    /// 已收金额
    /// 例：5400
    /// </summary>
    public decimal num_6 { get; set; }

    /// <summary>
    /// 未收金额
    /// 例：5400
    /// </summary>
    public decimal num_7 { get; set; }

    /// <summary>
    /// 开票金额
    /// 例：5400
    /// </summary>
    public decimal num_9 { get; set; }

    /// <summary>
    /// 未开票金额
    /// 例：5400
    /// </summary>
    public decimal num_10 { get; set; }

    /// <summary>
    /// 是否用于合同统计
    /// 例：1
    /// </summary>
    public int num_13 { get; set; }

    /// <summary>
    /// 归档状态
    /// 例：1
    /// </summary>
    public int num_11 { get; set; }

    /// <summary>
    /// 是否冲红
    /// 例：1
    /// </summary>
    public int num_20 { get; set; }

    /// <summary>
    /// 是否被冲红
    /// 例：1
    /// </summary>
    public int num_21 { get; set; }

    /// <summary>
    /// 坏账金额
    /// 例：0
    /// </summary>
    public decimal num_31 { get; set; }

    /// <summary>
    /// 创建人
    /// 例："16535288697112050"
    /// </summary>
    public string? creatorId { get; set; }

    /// <summary>
    /// 负责人
    /// 例："[\"16552063306603078\"]"
    /// </summary>
    public string? ownerId { get; set; }

    /// <summary>
    /// 协同人
    /// 例："[\"16552063306603078\"]"
    /// </summary>
    public string? coUserId { get; set; }

    /// <summary>
    /// 所属部门
    /// 例：606284791
    /// </summary>
    public int departmentId { get; set; }

    /// <summary>
    /// 受益人
    /// 例：16552063204931842
    /// </summary>
    public string? text_32 { get; set; }

    /// <summary>
    /// 受益部门
    /// 例：606284794
    /// </summary>
    public int? text_31 { get; set; }

    /// <summary>
    /// 业务类型(ConstDict.ContractText_30)
    /// 例：7debec53-aead-8f82-d6ea-1473516ebf75
    /// </summary>
    public string? text_30 { get; set; }

    /// <summary>
    /// 我方签约单位(ConstDict.ContractText_29)
    /// 例：6c4d6c4d-f8be-e711-37e9-3d62a202c2fe
    /// </summary>
    public string? text_29 { get; set; }
}

public class GetFormList : GetXbbRequest
{
    public string? name { get; set; }

    /// <summary>
    /// 表单类型，1系统表单， 2自定义表单
    /// </summary>
    public int saasMark { get; set; }

    /// <summary>
    /// 表单业务类型,客户：100，合同订单：201，退货退款：202，销售机会：301，联系人：401，跟进记录：501，回款计划：701，回款单：702，销项发票：901，供应商：1001，采购合同：1101，采购入库单：1404，其他入库单：1406，销售出库单：1504，其他出库单：1506，调拨单：1601，盘点单：1701，产品：2401；报价单：4700；线索：8000；市场活动：8100；仓库：1801；工作报告：2101；日报：2102；周报：2103；月报：2104；访客计划：601；自定义表单：不传
    /// </summary>
    public int? businessType { get; set; }
}

public class GetFormListResponse
{
    public int totalPage { get; set; }
    public int totalCount { get; set; }
    public List<GetFormListInfo> formList { get; set; } = default!;
}

public class GetFormListInfo
{
    public string? name { get; set; }
    public string? appId { get; set; }
    public int businessType { get; set; }
    public int isProcessForm { get; set; }
    public long menuId { get; set; }
    public long formId { get; set; }
}

public class GetForm : GetXbbRequest
{

}

public class GetFormResponse
{
    public List<GetFormExplainList> explainList { get; set; } = default!;
}

public class GetFormExplainList
{
    public string? attr { get; set; }
    public string? attrName { get; set; }
    public int fieldType { get; set; }
    public int noRepeat { get; set; }
    public int showType { get; set; }
    public List<GetFormExplainListiIems> items { get; set; } = default!;
}

public class GetFormExplainListiIems
{
    public string? text { get; set; }
    public string? value { get; set; }
}

public class GetFormData : GetXbbRequest
{

}

public class GetFormDataResponse
{
    public List<GetFormDataList> list { get; set; } = default!;
}

public class GetFormDataList
{
    public long dataId { get; set; }
    public long addTime { get; set; }
    public long updateTime { get; set; }
    public List<GetFormDataListData> data { get; set; } = default!;
}

public class GetFormDataListData
{
    public string? text_1 { get; set; }
    public string? text_2 { get; set; }
}

public class GetFormDataDetail : GetXbbRequest
{
    public long dataId { get; set; }
}

public class GetFormDataDetailResponse
{
    public string data { get; set; } = default!;
}

public class GetUserList : GetXbbRequest
{
    public int page { get; set; }
    public int pageSize { get; set; }
    public string? nameLike { get; set; }
    public List<string?> userIdIn { get; set; } = default!;
}

public class GetUserListResponse
{
    public int totalPage { get; set; }
    public int totalCount { get; set; }
    public List<GetUserListInfo> userList { get; set; } = default!;
}

public class GetUserListInfo
{
    public string? userId { get; set; }
    public string? name { get; set; }
}

public class GetXsjh : GetXbbRequest
{
    public int page { get; set; }
    public int pageSize { get; set; }
    public string? attr { get; set; }
    public string? symbol { get; set; }
    public List<long> value { get; set; } = new List<long>();
}

public class GetXsjhResponse
{
    public int totalPage { get; set; }
    public int totalCount { get; set; }
    public List<GetXsjhDetail> list { get; set; } = default!;
}

public class GetXsjhDetail
{
    /// <summary>
    /// 
    /// </summary>
    public long addTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public GetXsjhData? data { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public long dataId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int formId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public long updateTime { get; set; }
}

/// <summary>
/// 销售机会
/// </summary>
public class GetXsjhData
{
    /// <summary>
    /// 销售机会编号(前缀OPP.)
    /// 例:OPP.20230220041
    /// </summary>
    public string? serialNo { get; set; }

    /// <summary>
    /// 销售机会名称
    /// 例：河北泽沃信息技术有限公司-业务外包
    /// </summary>
    public string? text_1 { get; set; }

    /// <summary>
    /// 销售阶段(ConstDict.OpportunityText_17)
    /// 例：1
    /// </summary>
    public string? text_17 { get; set; }

    /// <summary>
    /// 客户标识
    /// 例：8860684
    /// </summary>
    public string? text_3 { get; set; }

    /// <summary>
    /// 关联产品ID列表
    /// 例：[14288996]
    /// </summary>
    public List<int>? array_1 { get; set; }

    /// <summary>
    /// 预计金额
    /// 例：3000
    /// </summary>
    public decimal num_1 { get; set; }

    /// <summary>
    /// 德信相关业务(ConstDict.OpportunityText_29)
    /// 例：761473bf-633e-4626-f71d-8796e1ae252d
    /// </summary>
    public string? text_29 { get; set; }

    /// <summary>
    /// 机会渠道(ConstDict.OpportunityText_2)
    /// 例：cb61a2c1-5349-7a26-8e0e-916b5ad441f2
    /// </summary>
    public string? text_2 { get; set; }

    /// <summary>
    /// 机会类型(ConstDict.OpportunityText_5)
    /// 例：ab106fa2-90a2-fec4-1ef8-4b1ec2669352
    /// </summary>
    public string? text_5 { get; set; }

    /// <summary>
    /// 机会级别(ConstDict.OpportunityText_6,数据有可能为空)
    /// 例：2b38d4e0-56cc-951b-de41-8817660bc5ee
    /// </summary>
    public string? text_6 { get; set; }

    /// <summary>
    /// 意向承接部门
    /// 例：607144103
    /// </summary>
    public int text_7 { get; set; }

    /// <summary>
    /// 承接人
    /// 例：16194248459886998
    /// </summary>
    public string? text_10 { get; set; }

    /// <summary>
    /// 预计成单日期
    /// 例：1679846400
    /// </summary>
    public int date_1 { get; set; }

    /// <summary>
    /// 创建人
    /// 例：255902546420548491
    /// </summary>
    public string? creatorId { get; set; }

    /// <summary>
    /// 负责人
    /// 例："[\"171459261124160769\"]"
    /// </summary>
    public string? ownerId { get; set; }

    /// <summary>
    /// 协同人
    /// 例："[]"
    /// </summary>
    public string? coUserId { get; set; }

    /// <summary>
    /// 赢率
    /// 例：20
    /// </summary>
    public int long_3 { get; set; }

    /// <summary>
    /// 客户名称-引
    /// 例：河北泽沃信息技术有限公司
    /// </summary>
    public string? text_30 { get; set; }

    /// <summary>
    /// 产品名称-引
    /// 例：业务外包
    /// </summary>
    public string? text_31 { get; set; }

    /// <summary>
    /// 最后跟进时间
    /// 例：1659196800
    /// </summary>
    public int date_2 { get; set; }

    /// <summary>
    /// 是否归档(ConstDict.OpportunityNum_8)
    /// 例：2
    /// </summary>
    public int num_8 { get; set; }

    /// <summary>
    /// 汇率
    /// 例：0
    /// </summary>
    public int num_5 { get; set; }
}