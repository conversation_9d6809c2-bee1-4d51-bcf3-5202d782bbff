﻿using Config.CommonModel.ThirdTalentInfo;

namespace Infrastructure.CommonInterface;

public interface IPostOrder<T, P> where T : IThirdTalentInfo where P : IProjectPostInfo
{
    // /// <summary>
    // /// 入库
    // /// </summary>
    // /// <param name="PostId"></param>
    // void PostStockIn(string PostId);

    // /// <summary>
    // /// 交付失败
    // /// </summary>
    // /// <param name="orderId"></param>
    // /// <param name="description"></param>
    // void PayFailed(string orderId, string description = "");

    // /// <summary>
    // /// 交付成功
    // /// </summary>
    // /// <param name="orderId"></param>
    // void PaySuccess(string orderId);
}

public class PostOrderExceptionMessage
{
    public string? PostName { get; set; }
    public string? SeekerName { get; set; }
    public List<string>? ExceptionMessage { get; set; }
}
