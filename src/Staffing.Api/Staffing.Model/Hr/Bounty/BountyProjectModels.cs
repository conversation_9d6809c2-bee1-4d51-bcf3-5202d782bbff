﻿using Config.CommonModel;
using Config.CommonModel.Ndn;
using Staffing.Entity.Staffing;

namespace Staffing.Model.Hr.Bounty;

public class BountyProjectModels
{
}

public class BountyProjectSumData
{
    /// <summary>
    /// 交付中订单
    /// </summary>
    public int? ExecOrderNum { get; set; }

    /// <summary>
    /// 交付中总佣金
    /// </summary>
    public decimal? ExecOrderMoney { get; set; }

    /// <summary>
    /// 交付成功订单
    /// </summary>
    public int? SucceedOrderNum { get; set; }

    /// <summary>
    /// 交付成功总佣金
    /// </summary>
    public decimal? SucceedOrderMoney { get; set; }

    /// <summary>
    /// 平台冻结资金
    /// </summary>
    public decimal? FreezedMoney { get; set; }

    /// <summary>
    /// 平台剩余库存
    /// </summary>
    public int? LeftStockNum { get; set; }

    /// <summary>
    /// 平台收入
    /// </summary>
    public decimal? IncomeMoney { get; set; }

    /// <summary>
    /// 平台支出（结算过的金额）
    /// </summary>
    public decimal? ExpendMoney { get; set; }

    /// <summary>
    /// 平台毛利
    /// </summary>
    public decimal? ProfitMoney { get; set; }

}

public class TodoListReqeust : QueryRequest
{
    public TabEnum TabSelect { get; set; } = TabEnum.全部;
}

public enum TabEnum
{
    全部,
    进行中
}

public class TodoListResponse : QueryResponse
{
    public List<TodoInfo> Rows { get; set; } = new List<TodoInfo>();

    /// <summary>
    /// 建项数
    /// </summary>
    public int? ProjectNum { get; set; }

    /// <summary>
    /// 转账报销数
    /// </summary>
    public int? TransferNum { get; set; }

    /// <summary>
    /// 服务奖金数
    /// </summary>
    public int? BonusNum { get; set; }

    /// <summary>
    /// 发票数
    /// </summary>
    public int? InvoiceNum { get; set; }

    /// <summary>
    /// 项目编号
    /// </summary>
    public string? ProjectCode { get; set; }
}

public class TodoInfo
{
    /// <summary>
    /// 待办主键id
    /// </summary>
    public string Id { get; set; } = default!;

    /// <summary>
    /// 关联实体id
    /// </summary>
    public string? RelatedEntityId { get; set; }

    /// <summary>
    /// 待办名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 待办描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// 待办类型
    /// </summary>
    public NdnTodoType? Type { get; set; }

    /// <summary>
    /// 待办状态
    /// </summary>
    public NdnTodoStatus? Status { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }
}

public class PersonRankResponse : QueryResponse
{
    /// <summary>
    /// 列表数据
    /// </summary>
    public List<PersonRankInfo> Rows { get; set; } = new List<PersonRankInfo>();

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
}

public class PersonRankInfo
{
    /// <summary>
    /// 头像地址
    /// </summary>
    public string? Avatar { get; set; }

    public string? HrId { get; set; }

    /// <summary>
    /// 交付人
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// 交付人职位名称
    /// </summary>
    public string? HrPostName { get; set; }

    /// <summary>
    /// 交付人公司名称
    /// </summary>
    public string? HrCompany { get; set; }

    /// <summary>
    /// 交付成功总金额
    /// </summary>
    public decimal? Money { get; set; }

    /// <summary>
    /// 交付成功单量
    /// </summary>
    public int? Count { get; set; }
}

public class ProjectDetail
{
    /// <summary>
    /// 项目主键Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 主体账套ID
    /// </summary>
    public string? BookCode { get; set; }

    /// <summary>
    /// 主体账套名称
    /// </summary>
    public string? BookName { get; set; }

    /// <summary>
    /// 客户账套ID
    /// </summary>
    public string? ClientBookCode { get; set; }

    /// <summary>
    /// 客户账套名称
    /// </summary>
    public string? ClientBookName { get; set; }

    /// <summary>
    /// 项目类型
    /// </summary>
    public NdnProjType? Type { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public NdnProjStatus? Status { get; set; }

    /// <summary>
    /// 操作人名称
    /// </summary>
    public string? OperatorName { get; set; }

    public DateTime? CreatedTime { get; set; }
    public DateTime? UpdatedTime { get; set; }
}

public class ProjectListRequest : QueryRequest
{
    /// <summary>
    /// 项目类型-tab
    /// </summary>
    public NdnProjType? Type { get; set; } = NdnProjType.诺聘主体;

    /// <summary>
    /// 项目编码
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 搜索
    /// </summary>
    public string? Search { get; set; }
}

public class ProjectListResponse : QueryResponse
{
    public List<ProjectDetail>? Rows { get; set; }
}

public class ProjectSaveRequest
{
    /// <summary>
    /// 项目主键
    /// </summary>
    public string Id { get; set; } = default!;
    /// <summary>
    /// 销帮帮编码
    /// </summary>
    public string ContractCode { get; set; } = default!;
}