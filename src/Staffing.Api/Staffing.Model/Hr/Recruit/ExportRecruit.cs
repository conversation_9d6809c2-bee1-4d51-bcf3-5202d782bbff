﻿using MiniExcelLibs.Attributes;

namespace Staffing.Model.Hr.Recruit;

public class ExportRecruitRequest
{
    /// <summary>
    /// 协同的项目Id
    /// </summary>
    public string? TeamProjectId { get; set; }
}

public class ExportRecruitData_bak
{
    [ExcelIgnore]
    public string? RecruitId { get; set; }
    public string? 职位名称 { get; set; }
    public string? 职位类别1 { get; set; }
    public string? 职位类别2 { get; set; }
    public string? 职位类别3 { get; set; }
    public string? 项目名称 { get; set; }
    public DateTime? 投递时间 { get; set; }
    public string? 招聘状态 { get; set; }
    public string? 招聘状态描述 { get; set; }
    public string? 求职者 { get; set; }
    public string? 求职者电话 { get; set; }
    public string? 性别 { get; set; }
    public string? 学历 { get; set; }
    public string? 求职意向 { get; set; }
    public string? 来源 { get; set; }
    public DateTime? 用户注册时间 { get; set; }
    public DateTime? 用户活跃时间 { get; set; }
    public string? 顾问 { get; set; }
    public string? 顾问电话 { get; set; }
    public string? 协同顾问 { get; set; }
    public string? 协同顾问电话 { get; set; }
    public string? 备注包含自流转 { get; set; }
    // public int? 处理时常_分钟 { get; set; }
}

public class ExportRecruitData
{
    [ExcelIgnore]
    public string? RecruitId { get; set; }
    public string? 求职者 { get; set; }
    public string? 求职者电话 { get; set; }
    public string? 身份证号 { get; set; }
    public string? 性别 { get; set; }
    public string? 学历 { get; set; }
    public DateTime? 投递时间 { get; set; }
    public string? 投递岗位 { get; set; }
    public string? 投递公司 { get; set; }
    public string? 招聘流程 { get; set; }
    public DateTime? 面试时间 { get; set; }
    public string? 邀面人 { get; set; }
    public string? 邀面人电话 { get; set; }

    // [ExcelColumnName("面试是否到场（填未通过/已通过/已取消）")]
    // public string? 面试是否到场 { get; set; }

    [ExcelColumnName("入职结果（填日期/2025-02-28）")]
    public string? 入职时间 { get; set; }
}
