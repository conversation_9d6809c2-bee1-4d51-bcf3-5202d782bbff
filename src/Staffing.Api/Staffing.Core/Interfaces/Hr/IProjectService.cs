﻿using Config.CommonModel;
using Config.CommonModel.Business;
using Config.CommonModel.DataScreen;
using Microsoft.AspNetCore.Http;
using Staffing.Model.Hr.Project;


namespace Staffing.Core.Interfaces.Hr;

public interface IProjectService
{
    // UpdateAgentEntResponse UpdateAgentEnt(UpdateAgentEnt model);

    // GetAgentEntListResponse GetAgentEntList(GetAgentEntList model);

    // GetAgentEntLessResponse GetAgentEntLess(GetAgentEntLess model);

    // GetAgentEntLessResponse GetAgentEntRecently();

    Task<GetAgentEntDetail> GetAgentEntInfo(string id);

    Task<EmptyResponse> SetProjectPostStatus(SetProjectPostStatus model);

    GetBountyConfigResponse GetBountyConfig();

    // EmptyResponse DeleteAgentEnt(string id);

    // EmptyResponse UpdateProjectAgentEnt(UpdateProjectAgentEnt model);

    Task<UpdateProjectResponse> UpdateProject(UpdateProject model);

    Task<UpdateProjectResponse> NewUpdateProject(NewUpdateProject model);

    Task<UpdateProjectSurveyResponse> UpdateProjectSurvey(UpdateProjectSurvey model);
    GetProjectSurveyResponse GetProjectSurvey(string? projectId, string? postId);
    Task<UpdateProjectSurveyResponse> ImportProjectSurvey(string? projectId, string? postId, IFormFile? file);

    GetProjectDetail GetProject(string id);

    Task<UpdatePostResponse> UpdatePost(UpdatePost model);

    Task<HrGetPostDetail> GetPost(string id);

    GetProjectTeamHrResponse GetProjectHr(GetProjectTeamHr model);

    MyProjectsResponse MyProjects(MyProjects model);

    MyProjectsResponse GetProjectsByHR(GetHRProsQuery model);

    HRProSimpleResponse GetProSimpleByHR(GetHRProsQuery model);

    MyProjectPostsResponse MyProjectPosts(MyProjectPosts model);

    EmptyResponse SetPostStatus(SetPostStatus model);

    EmptyResponse DeletePost(string id);

    EmptyResponse ProjectTransfer(ProjectTransfer model);
    Task<HrGetPostCopywritingResponse> HrGetPostCopywriting(HrGetPostCopywriting model);

    GetPostDeliverysResponse GetPostDeliverys(GetPostDeliverys model);

    EmptyResponse HrUpdatePostCopywriting(HrUpdatePostCopywriting model);

    GetPostBusinessSmsTextResponse GetPostBusinessSmsText(GetPostBusinessSmsText model);

    /// <summary>
    /// 获取项目自流转信息详情
    /// </summary>
    /// <param name="projectId"></param>
    /// <returns></returns>
    GetProjectAutomaticResponse GetProjectAutomatic(string projectId);

    /// <summary>
    /// 修改项目自流转信息
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse EditProjectAutomatic(EditProjectAutomaticRequest model);
    EmptyResponse TopPost(TopPost model);
    MyProjectPostsResponse GetShowPosts(GetShowPosts model);
    GetAdviserCityResponse GetAdviserCity(GetAdviserCity model);
    EmptyResponse ExcellentPost(ExcellentPost model);
    Task<GetNoahProjResponse> GetNoahProj(GetNoahProj model);
    GetPostInterviewResponse GetPostInterview(string? postId, string? recruitId);
    Task UpdatePostHrAgentChatStatus(TeamPostHrAgentChat model);

    /// <summary>
    /// 修改项目众包模式
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse EditProjectCrowdsource(ProjectCrowdsourceReq model);

    /// <summary>
    /// 获取全部hr
    /// </summary>
    /// <returns></returns>
    GetAllHrResp GetAllHr();

    /// <summary>
    /// 获取项目众包设置
    /// </summary>
    /// <returns></returns>
    ProjectCrowdsourceResp? GetProjectCrowdsource();
    /// <summary>
    /// 获取帐套信息
    /// </summary>
    /// <returns></returns>
    List<NoahBookResp> GetNoahBooks();

    /// <summary>
    /// 销售获取商机列表
    /// </summary>
    /// <returns></returns>
    GetMyProjectResp GetMyProjects(GetMyProjectReq req);
    /// <summary>
    /// 项目接单
    /// </summary>
    /// <param name="projectId"></param>
    /// <returns></returns>
    EmptyResponse ProjectAcceptOrder(string projectId);
    /// <summary>
    /// 获取诺快聘财务人员
    /// </summary>
    /// <returns></returns>
    GetAllHrResp GetAllfinance();
    /// <summary>
    /// 获取诺快聘项目经理
    /// </summary>
    /// <returns></returns>
    GetAllHrResp GetAllProjectManager();

    /// <summary>
    /// 获取项目资产概述
    /// </summary>
    /// <param name="projectId"></param>
    /// <returns></returns>
    ProjectAssetOverviewResp GetProjectAssetOverview(string projectId);
}