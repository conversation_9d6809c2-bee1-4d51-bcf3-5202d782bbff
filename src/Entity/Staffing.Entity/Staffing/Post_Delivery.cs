﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// hr职位
/// </summary>
[Table("post_delivery")]
public class Post_Delivery
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string DeliveryId { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 求职者Id
    /// </summary>
    public string SeekerId { get; set; } = default!;

    /// <summary>
    /// Hr职位Id
    /// </summary>
    public string TeamPostId { get; set; } = default!;

    /// <summary>
    /// 原始职位Id，冗余
    /// </summary>
    public string PostId { get; set; } = default!;

    /// <summary>
    /// 渠道Id
    /// </summary>
    public string? ChannelId { get; set; }

    // /// <summary>
    // /// 合作模式
    // /// </summary>
    // public ProjectPaymentNode? PaymentNode { get; set; }

    // /// <summary>
    // /// 打款天数
    // /// </summary>
    // public int? PaymentDays { get; set; }

    // /// <summary>
    // /// 金额/人
    // /// </summary>
    // public decimal? Money { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public int Source { get; set; }

    public long? ChannelSource { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public User_Seeker User_Seeker { get; set; } = default!;
    public User_Channel_Source User_Channel_Source { get; set; } = default!;
    public Post Post { get; set; } = default!;
    public Post_Team Post_Team { get; set; } = default!;
    public Qd_Hr_Channel Qd_Hr_Channel { get; set; } = default!;
    public User_Resume User_Resume { get; set; } = default!;
}