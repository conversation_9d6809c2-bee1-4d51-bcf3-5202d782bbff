﻿using Config.Enums;
using Config.CommonModel;
using Config.CommonModel.Business;

namespace Staffing.Model.Hr.User;

public class HrLogin
{
    /// <summary>
    /// 登录类型
    /// </summary>
    public HrLoginType Type { get; set; } = HrLoginType.Web;

    /// <summary>
    /// 表示哪儿个小程序(零工市场需要)
    /// </summary>
    public string? AppId { get; set; }

    /// <summary>
    /// 微信code
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 微信EncryptedData
    /// </summary>
    public string? EncryptedData { get; set; }

    /// <summary>
    /// 微信Iv
    /// </summary>
    public string? Iv { get; set; }

    /// <summary>
    /// 密钥
    /// </summary>
    public string? Key { get; set; }
}

public class HrLoginResponse : TokenInfo
{

}

public enum HrLoginType
{
    Web, Applet
}


public class HrApplyApp
{
    /// <summary>
    /// 登录类型
    /// </summary>
    public HrLoginType Type { get; set; } = HrLoginType.Web;

    /// <summary>
    /// 是否零工市场
    /// </summary>
    public bool IsQuickJob = false;

    /// <summary>
    /// 企业名称
    /// </summary>
    public CompanyInfo? CompanyInfo { get; set; }

    /// <summary>
    /// 零工市场 - 真实姓名
    /// </summary>
    public string? NickName { get; set; } = string.Empty;
}

public class CompanyInfo
{
    /// <summary>
    /// 企业名称
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 企业地址
    /// </summary>
    public string? CompanyAddress { get; set; }

    /// <summary>
    /// 担任职位
    /// </summary>
    public string? PositionName { get; set; }
}

public class HrApplyAppResponse
{
    /// <summary>
    /// 用户状态
    /// </summary>
    public UserStatus Status { get; set; }

    /// <summary>
    /// 审批拒绝说明
    /// </summary>
    public string? Rejection { get; set; }

    /// <summary>
    /// 审核状态
    /// </summary>
    public AppApplyStatus ApplyStatus { get; set; } = AppApplyStatus.未申请;
}

public class HrRefreshToken
{
    /// <summary>
    /// 刷新token
    /// </summary>
    public string? RefreshToken { get; set; }
}