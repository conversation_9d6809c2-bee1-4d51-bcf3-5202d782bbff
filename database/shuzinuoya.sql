-- ----------------------------
-- 数字诺亚合同
-- ----------------------------
DROP TABLE IF EXISTS `rpt_szny_contract`;
CREATE TABLE `rpt_szny_contract` (
  `Id` varchar(18) NOT NULL,
  `JobNumber` varchar(50) NOT NULL COMMENT '员工工号',
  `SJHT_CODE` varchar(50) NOT NULL COMMENT '合同编码',
  `SJHT_NAME` varchar(500) NOT NULL COMMENT '合同名称',
  `SJHT_SY_RYCODE` varchar(50) NOT NULL COMMENT '受益人编码',
  `SJHT_XBBBM` varchar(50) NOT NULL COMMENT '销帮帮合同编码',
  `SJHT_CPID` varchar(50) NOT NULL COMMENT '产品编码',
  `SJHT_CPMC` varchar(500) NOT NULL COMMENT '产品名称',
  `SJHT_SETDATE` datetime(6) NOT NULL DEFAULT NOW(6) COMMENT '创建时间',
  `SJHT_BEGINDATE` datetime(6) NOT NULL DEFAULT NOW(6) COMMENT '开始时间',
  `SJHT_ENDDATE` datetime(6) NOT NULL DEFAULT NOW(6) COMMENT '结束时间',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_rpt_szny_contract_SJHT_CODE` (`SJHT_CODE`),
  KEY `IX_rpt_szny_contract_JobNumber` (`JobNumber`),
  KEY `IX_rpt_szny_contract_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 数字诺亚项目
-- ----------------------------
DROP TABLE IF EXISTS `rpt_szny_project`;
CREATE TABLE `rpt_szny_project` (
  `Id` varchar(18) NOT NULL,
  `JobNumber` varchar(50) NOT NULL COMMENT '员工工号',
  `XM_CODE` varchar(50) NOT NULL COMMENT '项目编码',
  `XM_NAME` varchar(500) NOT NULL COMMENT '项目名称',
  `XM_PRODUCTID` varchar(50) NOT NULL COMMENT '产品编码',
  `XM_PRODUCTNAME` varchar(500) NOT NULL COMMENT '产品名称',
  `XM_SETDATE` datetime(6) NOT NULL COMMENT '创建时间',
  `XM_BEGTIME` datetime(6) NOT NULL COMMENT '开始时间',
  `XM_ENDTIME` datetime(6) NOT NULL COMMENT '结束时间',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_rpt_szny_project_XM_CODE` (`XM_CODE`),
  KEY `IX_rpt_szny_project_JobNumber` (`JobNumber`),
  KEY `IX_rpt_szny_project_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;