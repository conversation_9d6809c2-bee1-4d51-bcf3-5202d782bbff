﻿using Config.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Staffing.Entity.Staffing
{
    [Table("yunsheng_postid_relation")]
    public class YunSheng_PostId_Relation
    {
        [Key]
        public string Id { get; set; } = EntityTools.SnowflakeId();
        //诺聘岗位id
        public string? PostId { get; set; }
        /// <summary>
        /// 协同岗位Id
        /// </summary>
        public string? TeamPostId {  get; set; }
        //云生岗位id
        public string? YunShengPostId { get; set; }
        //状态
        public YunShengPostStatus Status { get; set; }
        //创建时间
        public DateTime? CreatedTime { get; set; } = DateTime.Now;
        //更新时间
        public DateTime? UpdatedTime { get; set;} = DateTime.Now;
    }
}
