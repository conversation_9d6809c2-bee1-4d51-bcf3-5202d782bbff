﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 招聘流程标签字典表
/// </summary>
[Table("dic_recruit_label")]
public class Dic_Recruit_Label
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } =  EntityTools.SnowflakeId()!;

    /// <summary>
    /// hrid
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 标签名称
    /// </summary>
    public string LabelName { get; set; } = default!;
}

