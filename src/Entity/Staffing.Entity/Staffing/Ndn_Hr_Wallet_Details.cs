﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 数字诺亚顾问钱包详情
/// </summary>
[Table("ndn_hr_wallet_details")]
public class Ndn_Hr_Wallet_Details
{
    /// <summary>
    /// 主键
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 顾问ID
    /// </summary>
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 职位ID
    /// </summary>
    public string? PostId { get; set; }
    
    /// <summary>
    /// Ndn项目编码
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    [ConcurrencyCheck]
    public decimal Amount { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Ndn_Hr_Wallet Ndn_Hr_Wallet { get; set; } = default!;
}
