﻿using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using Config;
using Config.CommonModel;
using Config.Enums;
using Microsoft.EntityFrameworkCore.Metadata;
using ServiceStack.Text;
using Yitter.IdGenerator;

namespace Infrastructure.Common;

public class Tools
{
    /// <summary>
    /// 身份证号验证
    /// </summary>
    /// <param name="name"></param>
    /// <param name="identityCard"></param>
    /// <returns></returns>
    public static bool Certification(string identityCard, string? name = null)
    {
        if (string.IsNullOrWhiteSpace(identityCard))
            return false;

        var reg = new Regex(@"^[\u4e00-\u9fa5]{0,}$");
        if (!string.IsNullOrWhiteSpace(name) && !reg.IsMatch(name))
            return false;

        long n = 0;
        if (identityCard.Length < 18 || long.TryParse(identityCard.Remove(17), out n) == false || n < Math.Pow(10, 16) || long.TryParse(identityCard.Replace('x', '0').Replace('X', '0'), out n) == false)
        {
            return false;
        }
        string address = "11x22x35x44x53x12x23x36x45x54x13x31x37x46x61x14x32x41x50x62x15x33x42x51x63x21x34x43x52x64x65x71x81x82x91";
        if (address.IndexOf(identityCard.Remove(2), StringComparison.Ordinal) == -1)
        {
            return false;
        }
        string birth = identityCard.Substring(6, 8).Insert(6, "-").Insert(4, "-");
        DateTime time = new DateTime();
        if (DateTime.TryParse(birth, out time) == false)
        {
            return false;
        }
        string[] arrVarifyCode = ("1,0,x,9,8,7,6,5,4,3,2").Split(',');
        string[] Wi = ("7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2").Split(',');
        char[] Ai = identityCard.Remove(17).ToCharArray();
        int sum = 0;
        for (int i = 0; i < 17; i++)
        {
            sum += int.Parse(Wi[i]) * int.Parse(Ai[i].ToString());
        }
        int y = -1;
        Math.DivRem(sum, 11, out y);
        if (arrVarifyCode[y] != identityCard.Substring(17, 1).ToLower())
        {
            return false;//校验码验证  
        }

        return true;
    }

    /// <summary>
    /// 判断是否手机号
    /// </summary>
    /// <param name="mobile"></param>
    /// <returns></returns>
    public static bool IsMobile(string? mobile)
    {
        return Regex.IsMatch(mobile ?? string.Empty, @"^1[3-9]\d{9}$");
    }

    /// <summary>
    /// 解析身份证
    /// </summary>
    /// <param name="idCard"></param>
    /// <returns></returns>
    public static IdCardInfo? GetIdCardInfo(string? idCard)
    {
        var result = new IdCardInfo();
        if (string.IsNullOrWhiteSpace(idCard) || idCard.Count() < 17)
            return null;

        if (int.TryParse(idCard.Substring(16, 1), out var s17))
            result.Sex = Convert.ToBoolean(s17 & 1) ? Sex.男 : Sex.女;

        result.RegionId = idCard.Substring(0, 6);

        if (DateOnly.TryParse($"{idCard.Substring(6, 4)}-{idCard.Substring(10, 2)}-{idCard.Substring(12, 2)}", out var birthday))
            result.Birthday = birthday;

        return result;
    }

    /// <summary>
    /// 身份证号加*
    /// </summary>
    /// <param name="idCard"></param>
    /// <returns></returns>
    public static string IdCardToX(string? idCard)
    {
        if (string.IsNullOrWhiteSpace(idCard) || idCard.Length < 18)
            return string.Empty;

        return string.Join('*', new string[idCard.Length - 3]) + idCard.Substring(14, 4);
    }

    public static string MobilToX(string? mobile)
    {
        if (mobile?.Length != 11)
            return string.Empty;

        return Regex.Replace(mobile, "(\\d{3})(\\d{4})(\\d{4})", "$1****$3");
    }

    //获取本周
    public static DateTime ThisWeek()
    {
        var weeknow = Convert.ToInt32(DateTime.Today.DayOfWeek);
        weeknow = weeknow == 0 ? 7 : weeknow;
        return DateTime.Today.AddDays(1 - weeknow);
    }

    /// <summary>
    /// 获取本月
    /// </summary>
    /// <returns></returns>
    public static DateTime ThisMonth()
    {
        return DateTime.Today.AddDays(1 - DateTime.Today.Day);
    }

    /// <summary>
    /// 计算年龄
    /// </summary>
    /// <param name="birthdate"></param>
    /// <returns></returns>
    public static int? GetAgeByBirthdate(DateOnly? birthdate)
    {
        if (birthdate == null)
            return null;

        DateTime now = DateTime.Now;
        int age = now.Year - birthdate.Value.Year;
        if (now.Month < birthdate.Value.Month || (now.Month == birthdate.Value.Month && now.Day < birthdate.Value.Day))
            age--;
        return age < 0 ? 0 : age;
    }

    /// <summary>
    /// 根据年龄计算生日
    /// </summary>
    /// <param name="age"></param>
    /// <returns></returns>
    public static DateOnly? GetBirthdateByAge(int? age)
    {
        if (age == null || age <= 0)
            return null;

        return DateOnly.FromDateTime(DateTime.Now.AddYears(-age.Value));
    }

    /// <summary>
    /// 过滤表情
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static string FilterEmoji(string str)
    {
        string origin = str;
        try
        {
            //关键代码
            foreach (var a in str)
            {
                byte[] bts = Encoding.UTF32.GetBytes(a.ToString());
                if (bts[0].ToString() == "253" && bts[1].ToString() == "255")
                {
                    str = str.Replace(a.ToString(), "");
                }
            }
        }
        catch (Exception)
        {
            str = origin;
        }
        return str;
    }

    /// <summary>
    /// 过滤特殊字符
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static string FilterSpecialString(string? str, int maxLen = 0)
    {
        if (string.IsNullOrEmpty(str))
            return string.Empty;

        var result = new string(str.Where(char.IsLetterOrDigit).ToArray());

        if (maxLen > 0 && result.Length > maxLen)
            result = $"{result.Substring(0, maxLen)}..";

        return result;
    }

    /// <summary>
    /// 字符串数组转枚举
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="str"></param>
    /// <returns></returns>
    public static List<T> StringToEnumsList<T>(string str) where T : Enum
    {
        var ids = str.Split(',').Select(s => Convert.ToInt32(s)).ToList();

        var result = ids.Select(s => (T)Enum.ToObject(typeof(T), s)).ToList();

        return result;
    }

    /// <summary>
    /// 生成签名
    /// 需要签名的数组,正序排序后(不区分大小写),
    /// md5加密,截取从第5位开始后的24位
    /// </summary>
    /// <param name="param"></param>
    /// <returns>签名后的字符串</returns>
    public static string MakeSign(params String[] param)
    {
        if (param == null || param.Length == 0)
        {
            throw new Exception("加密对象不能为空!");
        }
        string[] list = param.Select(t => t.ToUpper()).Where(t => !string.IsNullOrEmpty(t)).ToArray();
        if (list.Length == 0)
        {
            throw new Exception("加密对象不能为空!");
        }
        var value = Md5Helper.Md5(string.Join("", list)).ToCharArray(5, 24);
        return new string(value);
    }

    public static string? TrimRegionId(string? RegionId)
    {
        if (RegionId?.Length > 0 && RegionId?.Length <= 6)
            RegionId = RegionId?.Replace("0000", string.Empty).Replace("00", string.Empty);

        return RegionId;
    }

    /// <summary>
    /// 验证签名
    /// </summary>
    /// <param name="sign">签名</param>
    /// <param name="param">数组</param>
    /// <returns></returns>
    public static bool CheckSign(string sign, params string[] param)
    {
        var val = MakeSign(param);
        return val.Equals(sign, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 实体转换
    /// </summary>
    /// <typeparam name="TFrom"></typeparam>
    /// <typeparam name="TTo"></typeparam>
    /// <param name="model"></param>
    /// <returns></returns>
    public static TTo ModelConvert<TTo>(object model)
    {
        var json = JsonSerializer.SerializeToString(model);
        var result = JsonSerializer.DeserializeFromString<TTo>(json);

        return result!;
    }

    private static bool SnowflakeConfig = false;

    /// <summary>
    /// 注册雪花worker
    /// </summary>
    /// <param name="workderId">最大16383</param>
    public static void SnowflakeWorkerInit(ushort workderId)
    {
        var options = new IdGeneratorOptions
        {
            WorkerId = workderId,
            WorkerIdBitLength = 14,
            SeqBitLength = 7
        };
        YitIdHelper.SetIdGenerator(options);
        SnowflakeConfig = true;
    }

    /// <summary>
    /// 自动注册雪花worker
    /// </summary>
    public static async Task SnowflakeWorkerInit(bool isProd)
    {
        using var locker = await MyRedis.Lock("SnowflakeLocker", 20);

        if (locker == null)
            throw new Exception("自动注册雪花worker获取redis锁失败");

        var snowflakeKey = string.Empty;
        var minId = 3000;
        var maxId = 16000;
        if (isProd)
            snowflakeKey = "snowflakeworkeyprod";
        else
        {
            minId = 101;
            maxId = 1000;
            snowflakeKey = "snowflakeworkey";
        }



        var nextWokerId = MyRedis.Client.Incr(snowflakeKey);
        if (nextWokerId < minId || nextWokerId > maxId)
        {
            nextWokerId = minId;
            MyRedis.Client.Set(snowflakeKey, nextWokerId);
        }

        var options = new IdGeneratorOptions
        {
            WorkerId = (ushort)nextWokerId,
            WorkerIdBitLength = 14,
            SeqBitLength = 7
        };
        YitIdHelper.SetIdGenerator(options);
        SnowflakeConfig = true;
    }

    /// <summary>
    /// 雪花Id
    /// </summary>
    /// <returns></returns>
    public static string NextId()
    {
        if (!SnowflakeConfig)
            throw new Exception("尚未注册worker");

        return YitIdHelper.NextId().ToString();
    }

    /// <summary>
    /// 集合转树结构
    /// </summary>
    /// <typeparam name="object"></typeparam>
    public static IEnumerable<object> GetChildrenTreeNode<T, K>(IEnumerable<T> notes, Func<T, K> idSelector,
        Func<T, K> parentIdSelector, K? rootId = default(K), string childrenNodeName = "Children") where T : class
    {
        var dView = notes.Where(x =>
        {
            K pid = parentIdSelector(x);
            return (rootId == null && pid == null) || (rootId != null && pid?.Equals(rootId) == true);
        }).ToList();
        // List<Object> objList = new List<object>();
        foreach (var dr in dView)
        {
            var testType = typeof(T);
            var propertyInfos = testType.GetProperties(BindingFlags.Instance | BindingFlags.Public
            | BindingFlags.NonPublic | BindingFlags.Static);

            //******************获取源对象数据****************//
            Dictionary<string, object> dicObj = new Dictionary<string, object>();

            foreach (var property in propertyInfos)
            {
                var value = property?.GetValue(dr);
                if (value != null)
                {
                    dicObj.Add(property!.Name, value);
                }
            }
            var aa = idSelector(dr);
            var children = GetChildrenTreeNode(notes, idSelector, parentIdSelector, idSelector(dr), childrenNodeName);
            if (children?.Count() > 0)
                dicObj.Add(childrenNodeName, children);
            //objList.Add(dicObj);
            yield return dicObj;
        }
        // return objList;
    }

    public static string FormattingSalary(int Min, int Max, PostSalaryType SalaryType, PostWorkNature WorkNature, int Months = 12)
    {
        var salaryValue = WorkNature switch
        {
            PostWorkNature.全职 => FormattingSalary(Min, Max),
            PostWorkNature.应届毕业生 => FormattingSalary(Min, Max),
            _ => Min + "-" + Max
        };

        return SalaryType switch
        {
            PostSalaryType.面议 => "面议",
            PostSalaryType.月薪 => salaryValue + (Months > 12 ? $"·{Months}薪" : string.Empty),
            PostSalaryType.年薪 => salaryValue,
            PostSalaryType.周薪 => salaryValue + "元/周",
            PostSalaryType.日薪 => salaryValue + "元/天",
            PostSalaryType.时薪 => salaryValue + "元/时",
            _ => string.Empty,
        };
    }

    /// <summary>
    /// 格式化薪资显示
    /// </summary>
    /// <param name="Min"></param>
    /// <param name="Max"></param>
    /// <returns></returns>
    private static string FormattingSalary(int Min, int Max)
    {
        return (Min / 1000) + "k" + "-" + (Max / 1000) + "k";
    }

    /// <summary>
    /// 计算在线状态
    /// </summary>
    /// <param name="activeTime"></param>
    /// <returns></returns>
    public static OnlineStatus GetOnlineStatus(DateTime? activeTime)
    {
        if (!activeTime.HasValue)
            return OnlineStatus.离线;

        return (DateTime.Now - activeTime.Value).TotalHours switch
        {
            < 24 => OnlineStatus.刚刚活跃,
            < 24 * 7 => OnlineStatus.最近活跃,
            _ => OnlineStatus.离线
        };
    }

    /// <summary>
    /// 根据登录时间计算活跃度积分
    /// </summary>
    /// <param name="activeTime"></param>
    /// <returns></returns>
    public static int GetOnlineScore(DateTime? activeTime)
    {
        if (!activeTime.HasValue)
            return 0;

        return (DateTime.Now - activeTime.Value).TotalHours switch
        {
            < 24 => 100,
            < 24 * 3 => 90,
            < 24 * 7 => 80,
            < 24 * 30 => 60,
            _ => 50
        };
    }

    /// <summary>
    /// 根据积分计算评级
    /// </summary>
    /// <param name="score"></param>
    /// <returns></returns>
    public static string CountGradeByScore(int score)
    {
        return score switch
        {
            >= 100 => "S",
            >= 90 => "A",
            >= 80 => "B",
            >= 50 => "C",
            _ => "D"
        };
    }

    /// <summary>
    /// 计算hr评级
    /// </summary>
    /// <param name="dataScore">数据完整性</param>
    /// <param name="loginTime">登录时间</param>
    /// <returns></returns>
    public static string GetHrGrade(int dataScore, DateTime? loginTime)
    {
        return $"{CountGradeByScore(dataScore)}{CountGradeByScore(GetOnlineScore(loginTime))}S";
    }

    /// <summary>
    /// imid转用户id
    /// </summary>
    /// <param name="tencentImId"></param>
    /// <returns></returns>
    public static string? GetUserIdByImId(string? tencentImId)
    {
        if (tencentImId?.StartsWith(Constants.ImSeekerIdPre) == true)
            tencentImId = tencentImId.Substring(Constants.ImSeekerIdPre.Length);

        if (tencentImId?.StartsWith(Constants.ImHrIdPre) == true)
            tencentImId = tencentImId.Substring(Constants.ImHrIdPre.Length);

        return tencentImId;
    }

    public static string GetNumHz(int i)
    {
        if (i > 10) i = 10;

        if (i < 0) i = 0;

        var hz = new string[] { "零", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十" };

        return hz[i];
    }

    public static List<double> GetRandomMmoney(double money, int n)
    {
        double[] array = new double[n];
        redpackage red = new redpackage() { money = money, count = n };
        for (int i = 0; i < n; i++)
        {
            array[i] = GetRMPri(red);
        }
        return array.OrderBy(o => Guid.NewGuid()).ToList();
    }
    /// <summary>
    /// 即开即中，考虑机会均等，减少金额差较大的几率
    /// 随机产生，额度在0.01和剩余平均值*2之间
    /// </summary>
    /// <returns></returns>
    private static double GetRMPri(redpackage redpackage)
    {
        //如果最后一个，返回全部
        if (redpackage.count == 1)
        {
            redpackage.count--;
            return Math.Round(redpackage.money);
        }
        var perAmount = (int)(redpackage.money / redpackage.count);

        //随机生成
        Random ran = new Random();
        double min = perAmount / 2;
        double max = perAmount * 2;
        min = min > 1 ? min : 1;
        max = max > 1 ? max : 1;
        double money = ran.NextDouble() * max;

        money = money <= min ? min : money;
        money = Convert.ToInt32(money);
        redpackage.count--;
        redpackage.money -= money;
        return money;
    }

    // /// <summary>
    // /// 是否零工市场小程序
    // /// </summary>
    // /// <param name="clientid"></param>
    // /// <returns></returns>
    // public static bool IsQuickJobClient(string? clientid)
    // {
    //     return clientid?.StartsWith("npsc-wx-") == true;
    // }

    /// <summary>
    /// 是否是抖音小程序
    /// </summary>
    /// <param name="clientid"></param>
    /// <returns></returns>
    public static bool IsDyClient(string? clientid)
    {
        return clientid?.StartsWith("nkp-dy") == true;
    }

    public static string GetErrMsg(Exception e)
    {
        if (string.IsNullOrWhiteSpace(e.InnerException?.Message))
            return e.Message;
        else
            return $"{e.Message}::{e.InnerException?.Message}";
    }

    /// <summary>
    /// 图片样式300x300
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static string? ImageStyle300(string? str)
    {
        var result = str;

        if (!string.IsNullOrWhiteSpace(str))
            result = $"{str}?x-oss-process=style/300x300";

        return result;
    }

    public static List<NScoresStoreInfo> GetNScoreCfg(SeekerOrHr userType)
    {
        var rdsk = $"{RedisKey.NScore.Key}{RedisKey.NScore.ScoreConfig}";
        var scoreConfig = MyRedis.Client.Get<List<NScoresStoreInfo>?>(rdsk);
        var result = scoreConfig ?? Constants.NScore.DefaultNScore;
        result = result.Where(x => x.UserType == userType).ToList();
        return result;
    }

    /// <summary>
    /// 日期区间转为字符串
    /// </summary>
    /// <param name="start"></param>
    /// <param name="end"></param>
    /// <returns></returns>
    public static string DateRangeToString(DateTime? start, DateTime? end)
    {
        if (start == null && end == null)
            return string.Empty;

        if (start == null)
            return $"~{end:yyyy-MM}";

        if (end == null)
            return $"{start:yyyy-MM}~";

        return $"{start:yyyy-MM}~{end:yyyy-MM}";
    }

    public static string? MailToX(string? mailbox)
    {
        if (string.IsNullOrWhiteSpace(mailbox))
            return string.Empty;

        var index = mailbox.IndexOf('@');
        if (index < 2)
            return string.Empty;

        return $"{mailbox.Substring(0, 2)}****{mailbox.Substring(index)}";
    }

    public static string? WeChatToX(string? weChat)
    {
        if (string.IsNullOrWhiteSpace(weChat) || weChat.Length < 4)
            return string.Empty;

        return $"{weChat.Substring(0, 2)}****";
    }

    public static string? QQToX(string? qQ)
    {
        if (string.IsNullOrWhiteSpace(qQ) || qQ.Length <= 4)
            return string.Empty;

        return $"{qQ.Substring(0, 4)}****";
    }

    public static (DateTime beginTime, DateTime endTime) AdjustTimeRange(DateTime? beginTime, DateTime? endTime, DateTime defaultTime)
    {
        if (!beginTime.HasValue || beginTime < defaultTime)
            beginTime = defaultTime;

        if (!endTime.HasValue || endTime > DateTime.Now)
            endTime = DateTime.Now;

        beginTime = beginTime.Value.Date;
        endTime = endTime.Value.Date.AddDays(1);

        return (beginTime.Value, endTime.Value);
    }

    //字符串转hash，返回int类型，不随环境变化而改变
    public static int StringToHash(string str)
    {
        var hash = 0;
        if (str.Length == 0)
            return hash;

        for (int i = 0; i < str.Length; i++)
        {
            hash = str[i] + ((hash << 5) - hash);
            hash &= hash;
        }

        return hash;
    }

    public static string GenerateDatabaseDocumentation(List<IEntityType> entityTypes, string xmlPath)
    {
        var sb = new StringBuilder();
        var xml = XDocument.Load(xmlPath);

        foreach (var entityType in entityTypes)
        {
            var clrType = entityType.ClrType;
            var tableName = GetTableName(entityType);
            var tableDescription = GetClassDescription(clrType, xml);

            sb.AppendLine($"### {tableName} 表");
            sb.AppendLine($"#### {tableDescription}");
            sb.AppendLine("| 字段名 | 数据类型 | 描述 |");
            sb.AppendLine("| ------ | -------- | ---- |");

            foreach (var property in entityType.GetProperties())
            {
                var propInfo = clrType.GetProperty(property.Name);
                var isRelationship = IsRelationship(propInfo!.PropertyType, entityTypes);
                if (isRelationship)
                {
                    // 处理关系逻辑，例如添加一个关系说明
                    sb.AppendLine($"| {property.Name} | {propInfo.PropertyType.Name} | 关系 |");
                }
                else if (propInfo.PropertyType.IsEnum)
                {
                    sb.AppendLine($"| {property.Name} | 枚举 | {GetEnumValuesDescription(propInfo.PropertyType)} |");
                }
                else
                {
                    var description = GetPropertyDescription(propInfo, xml);
                    sb.AppendLine($"| {property.Name} | {propInfo.PropertyType.Name} | {description} |");
                }
            }

            // 处理外键
            foreach (var foreignKey in entityType.GetForeignKeys())
            {
                var principalEntityType = foreignKey.PrincipalEntityType.ClrType;
                var principalTableName = GetTableName(foreignKey.PrincipalEntityType);
                var fkColumnName = foreignKey.Properties.First().Name;
                sb.AppendLine($"| {fkColumnName} | 外键 | 外键到 {principalTableName} 表 |");
            }

            sb.AppendLine();
        }

        return sb.ToString();
    }

    private static string GetTableName(IEntityType entityType)
    {
        var tableNameAnnotation = entityType.GetAnnotation("Relational:TableName");
        return tableNameAnnotation != null ? tableNameAnnotation.Value!.ToString()! : entityType.ClrType.Name!;
    }

    private static bool IsRelationship(Type type, List<IEntityType> entityTypes)
    {
        var clrTypes = entityTypes.Select(e => e.ClrType).ToList();
        return clrTypes.Contains(type) || (type.IsGenericType && clrTypes.Contains(type.GetGenericArguments().First()));
    }

    private static string GetPropertyDescription(PropertyInfo prop, XDocument xml)
    {
        var fullName = $"{prop.DeclaringType!.FullName}.{prop.Name}";
        var member = xml.Descendants("member")
                        .FirstOrDefault(m => m.Attribute("name")!.Value == $"P:{fullName}");
        return member?.Element("summary")?.Value.Trim() ?? string.Empty;
    }

    private static string GetClassDescription(Type clrType, XDocument xml)
    {
        var fullName = clrType.FullName;
        var member = xml.Descendants("member")
                        .FirstOrDefault(m => m.Attribute("name")!.Value == $"T:{fullName}");
        return member?.Element("summary")?.Value.Trim() ?? string.Empty;
    }

    private static string GetEnumValuesDescription(Type enumType)
    {
        var values = Enum.GetValues(enumType);
        var descriptions = values.Cast<object>().Select(v => $"{v} = {(int)v}");
        return string.Join(", ", descriptions);
    }

    /// <summary>
    /// 根据标记处理字符串
    /// </summary>
    /// <param name="str"></param>
    /// <param name="begin"></param>
    /// <param name="end"></param>
    /// <returns></returns>
    public static string? HandleStringByMark(string? str, string begin, string end)
    {
        if (string.IsNullOrWhiteSpace(str))
            return str;

        // 去掉字符串中以begin开始，end结束的部分
        var escapedBegin = Regex.Escape(begin);
        var escapedEnd = Regex.Escape(end);
        var pattern = $"{escapedBegin}.*?{escapedEnd}";
        return Regex.Replace(str, pattern, string.Empty);
    }
}

public class redpackage
{
    /// <summary>
    /// 剩余红包数量
    /// </summary>
    public int count;
    /// <summary>
    /// 剩余金额
    /// </summary>
    public double money;
}