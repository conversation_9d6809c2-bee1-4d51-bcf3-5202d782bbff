﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 数字诺亚帐套-人员关系表
/// </summary>
[Table("ndn_book_user")]
public class Ndn_Book_User
{
    /// <summary>
    /// 主键
    /// </summary>
    [Key]
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 工号
    /// </summary>
    public string UserNo { get; set; } = default!;

    /// <summary>
    /// 账套ID
    /// </summary>
    public string BookCode { get; set; } = default!;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Ndn_Books Ndn_Books { get; set; } = default!;
}