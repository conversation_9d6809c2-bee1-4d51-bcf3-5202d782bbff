namespace Config.CommonModel.JavaDataApi;

public class JavaPostBehaviorData
{
    /// <summary>
    /// case type 1 -> postId，case type 2 -> teamPostId	
    /// </summary>
    public string? id { get; set; }

    /// <summary>
    /// 1->主创项目类型，2->协同新项目类型
    /// </summary>
    public int type { get; set; }
}

public class JavaPostBehaviorDataResponse
{
    public string? id { get; set; }
    public string? postId { get; set; }
    public string? postName { get; set; }
    public string? postTypeName { get; set; }
    //访问人数
    public int postVisitNum { get; set; }
    //主创渠道访问人数
    public int postSelfVisitNum { get; set; }
    //企业协同访问人数
    public int postEntVisitNum { get; set; }
    //平台协同访问人数
    public int postPlatformVisitNum { get; set; }
    //诺聘协同访问人数
    public int postNuopinVisitNum { get; set; }
    //报名人数
    public int postRegistrationNum { get; set; }
    //主创渠道报名人数
    public int postSelfRegistrationNum { get; set; }
    //平台协同报名人数
    public int postPlatformRegistrationNum { get; set; }
    //诺聘协同报名人数
    public int postNuopinRegistrationNum { get; set; }
    //企业协同报名人数
    public int postEntRegistrationNum { get; set; }
    //已面试人数
    public int postInterviewedNum { get; set; }
    //主创渠道已面试人数
    public int postSelfInterviewedNum { get; set; }
    //平台协同已面试人数
    public int postPlatformInterviewedNum { get; set; }
    //诺聘协同已面试人数
    public int postNuopinInterviewedNum { get; set; }
    //企业协同已面试人数
    public int postEntInterviewedNum { get; set; }
    //入职人数
    public int postTakeOfficeNum { get; set; }
    //主创渠道入职人数
    public int postSelfTakeOfficeNum { get; set; }
    //平台协同入职人数
    public int postPlatformTakeOfficeNum { get; set; }
    //诺聘协同入职人数
    public int postNuopinTakeOfficeNum { get; set; }
    //企业协同入职人数
    public int postEntTakeOfficeNum { get; set; }
}