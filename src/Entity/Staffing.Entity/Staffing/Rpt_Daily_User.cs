﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 每日用户统计
/// </summary>
[Table("rpt_daily_user")]
public class Rpt_Daily_User
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 新用户
    /// </summary>
    public int NewUser { get; set; } = default!;

    /// <summary>
    /// c端注册
    /// </summary>
    public int NewSeeker { get; set; } = default!;

    /// <summary>
    /// b端注册
    /// </summary>
    public int NewHr { get; set; } = default!;

    /// <summary>
    /// 用户日活
    /// </summary>
    public int ActiveUser { get; set; } = default!;

    /// <summary>
    /// c端日活
    /// </summary>
    public int ActiveSeeker { get; set; } = default!;

    /// <summary>
    /// b端日活
    /// </summary>
    public int ActiveHr { get; set; } = default!;

    /// <summary>
    /// 面试官日活
    /// </summary>
    public int ActiveInterviewer { get; set; } = default!;

    /// <summary>
    /// 投递简历数
    /// </summary>
    public int DeliverResume { get; set; } = default!;

    /// <summary>
    /// 日期
    /// </summary>
    public DateOnly EventDate { get; set; } = DateOnly.FromDateTime(DateTime.Now);

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 累计发布或系统过项目的hr数量
    /// </summary>
    public int AllHrHadProj { get; set; }
}