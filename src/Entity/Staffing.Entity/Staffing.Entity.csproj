<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup>
    <NoWarn>1701;1702;1591;NU1803;CS8981;</NoWarn>
    <DocumentationFile>bin\Debug\net8.0\Staffing.Entity.xml</DocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EFCore.NamingConventions" Version="8.0.3" />
    <PackageReference Include="EntityFrameworkCore.Data.AutoHistory" Version="8.0.3" />
    <PackageReference Include="NEST" Version="7.16.0" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.3" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql.NetTopologySuite" Version="8.0.3" />
    <PackageReference Include="ServiceStack.Text.Core" Version="8.4.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
    <PackageReference Include="Yitter.IdGenerator" Version="1.0.15" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Infrastructure\Config\Config.csproj" />
  </ItemGroup>
</Project>