﻿using System.Text;

namespace Infrastructure.Common;

public class StringBuilderHelper
{
    private StringBuilder _stringBuilder;
    private int _count;

    public StringBuilderHelper()
    {
        _stringBuilder = new StringBuilder();
        _count = 0;
    }

    public void AppendLine(string text) 
    { 
        _stringBuilder.Append(text);
        _count++;
    }

    public override string ToString() => _stringBuilder.ToString();

    public int AppendLineCount() => _count;
}
