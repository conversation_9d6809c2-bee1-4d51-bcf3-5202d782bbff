﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 数字诺亚顾问钱包
/// </summary>
[Table("ndn_hr_wallet")]
public class Ndn_Hr_Wallet
{
    /// <summary>
    /// 顾问ID
    /// </summary>
    [Key]
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 金额
    /// </summary>
    [ConcurrencyCheck]
    public decimal Amount { get; set; }
}
