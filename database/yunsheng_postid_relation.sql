/*
 Navicat Premium Data Transfer

 Source Server         : 诺聘**************（测试环境）
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : **************:3306
 Source Schema         : staffing

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 13/02/2025 13:54:15
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for yunsheng_postid_relation
-- ----------------------------
DROP TABLE IF EXISTS `yunsheng_postid_relation`;
CREATE TABLE `yunsheng_postid_relation`  (
  `id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `PostId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '诺聘职位ID',
  `YunShengPostId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '云生职位ID',
  `Status` int NULL DEFAULT NULL COMMENT '状态  1：上线 2：更新 3：下架 4：上架',
  `UpdatedTime` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `CreatedTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `PK_Yunsheng_PostId_Relation`(`id` ASC) USING BTREE COMMENT '主键索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '云生职位id关联表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
