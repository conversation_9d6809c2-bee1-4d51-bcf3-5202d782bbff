namespace Config.CommonModel.FigureNoah;

public class NoahGetProjectMembersResponse
{
    public int count { get; set; }

    /// <summary>
    /// 总数量
    /// </summary>
    public int allcount { get; set; }

    /// <summary>
    /// 负责人姓名
    /// </summary>
    public string? fwzymc { get; set; }

    /// <summary>
    /// 负责人编号
    /// </summary>
    public string? fwzybm { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? xmname { get; set; }

    /// <summary>
    /// 合同编号
    /// </summary>
    public string? htcode { get; set; }

    /// <summary>
    /// 不知道是什么
    /// </summary>
    public string? khcode { get; set; }

    /// <summary>
    /// xxx公司
    /// </summary>
    public string? khname { get; set; }

    /// <summary>
    /// 合同地址|分割
    /// </summary>
    public string? hturl { get; set; }

    public NoahProjectMemberData data { get; set; } = new NoahProjectMemberData();
}

public class NoahProjectMemberData
{
    public List<NoahProjectMember> rows { get; set; } = new List<NoahProjectMember>();
}

/// <summary>
/// 数字诺亚项目成员
/// </summary>
public class NoahProjectMember
{
    /// <summary>
    /// 员工姓名
    /// </summary>
    public string? YG_NAME { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? YG_MOBILEPHONE { get; set; }

    public string? YG_EMAIL { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    public string? YG_IDNUMBER { get; set; }

    /// <summary>
    /// 岗位
    /// </summary>
    public string? LDHT_GW { get; set; }

    /// <summary>
    /// 用工形式ID 0合同制、1聘用制、2临时工、3非全日制、4其他
    /// </summary>
    public int? YG_LABORFORM { get; set; }

    /// <summary>
    /// 派遣时间
    /// </summary>
    public string? LDHT_SENDTIME { get; set; }

    /// <summary>
    /// 试用期
    /// </summary>
    public int LDHT_PROBATION { get; set; }
}

public class NoahGetEmployeeProjDataResponse
{
    /// <summary>
    /// 合同数量
    /// </summary>
    public int htcount { get; set; }

    /// <summary>
    /// 执行中项目数量
    /// </summary>
    public int xmcount { get; set; }

    /// <summary>
    /// 合同数据集
    /// </summary>
    public NoahGetEmployeeProjHtData htdata { get; set; } = new NoahGetEmployeeProjHtData();

    /// <summary>
    /// 项目数据集
    /// </summary>
    public NoahGetEmployeeProjXmData xmdata { get; set; } = new NoahGetEmployeeProjXmData();
}

public class NoahGetEmployeeProjHtData
{
    public List<NoahGetEmployeeProjHt> rows { get; set; } = new List<NoahGetEmployeeProjHt>();
}

/// <summary>
/// 数字诺亚项目成员
/// </summary>
public class NoahGetEmployeeProjHt
{
    /// <summary>
    /// 产品编码
    /// </summary>
    public string? SJHT_CPID { get; set; }

    /// <summary>
    /// 劳务派遣
    /// </summary>
    public string? SJHT_CPMC { get; set; }

    /// <summary>
    /// 每一产品对应合同数
    /// </summary>
    public string? SL { get; set; }
}

public class NoahGetEmployeeProjXmData
{
    public List<NoahGetEmployeeProjXm> rows { get; set; } = new List<NoahGetEmployeeProjXm>();
}

/// <summary>
/// 数字诺亚项目成员
/// </summary>
public class NoahGetEmployeeProjXm
{
    /// <summary>
    /// 产品编码
    /// </summary>
    public string? XM_PRODUCTID { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string? XM_PRODUCTNAME { get; set; }

    /// <summary>
    /// 每一产品对应合同数
    /// </summary>
    public string? SL { get; set; }
}

/// <summary>
/// 数字诺亚合同项目详情
/// </summary>
public class NoahGetEmployeeProjDataDetailResponse
{
    /// <summary>
    /// 合同数量
    /// </summary>
    public int htcount { get; set; }

    /// <summary>
    /// 执行中项目数量
    /// </summary>
    public int xmcount { get; set; }

    /// <summary>
    /// 合同数据集
    /// </summary>
    public NoahGetEmployeeProjHtDataDetail htdata { get; set; } = new NoahGetEmployeeProjHtDataDetail();

    /// <summary>
    /// 项目数据集
    /// </summary>
    public NoahGetEmployeeProjXmDataDetail xmdata { get; set; } = new NoahGetEmployeeProjXmDataDetail();
}

public class NoahGetEmployeeProjHtDataDetail
{
    public List<NoahGetEmployeeProjHtDetail> rows { get; set; } = new List<NoahGetEmployeeProjHtDetail>();
}

public class NoahGetEmployeeProjXmDataDetail
{
    public List<NoahGetEmployeeProjXmDetail> rows { get; set; } = new List<NoahGetEmployeeProjXmDetail>();
}

/// <summary>
/// 数字诺亚合同详情
/// </summary>
public class NoahGetEmployeeProjHtDetail
{
    /// <summary>
    /// 合同编码
    /// </summary>
    public string? SJHT_CODE { get; set; }

    /// <summary>
    /// 合同名称
    /// </summary>
    public string? SJHT_NAME { get; set; }

    /// <summary>
    /// 受益人编码
    /// </summary>
    public string? SJHT_SY_RYCODE { get; set; }

    /// <summary>
    /// 销帮帮合同编码
    /// </summary>
    public string? SJHT_XBBBM { get; set; }

    /// <summary>
    /// 产品编码
    /// </summary>
    public string? SJHT_CPID { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string? SJHT_CPMC { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? SJHT_SETDATE { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? SJHT_BEGINDATE { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? SJHT_ENDDATE { get; set; }
}

/// <summary>
/// 数字诺亚项目详情
/// </summary>
public class NoahGetEmployeeProjXmDetail
{
    /// <summary>
    /// 项目编码
    /// </summary>
    public string? XM_CODE { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? XM_NAME { get; set; }

    /// <summary>
    /// 产品编码
    /// </summary>
    public string? XM_PRODUCTID { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string? XM_PRODUCTNAME { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? XM_SETDATE { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? XM_BEGTIME { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? XM_ENDTIME { get; set; }
}

public class SznyProjectExoprt
{
    public List<SznyProjectExoprtDetail> Rows { get; set; } = new List<SznyProjectExoprtDetail>();
}

public class SznyProjectExoprtDetail
{
    public string? 姓名 { get; set; }
    public string? 工号 { get; set; }

    public string? 部门1 { get; set; }

    public string? 部门2 { get; set; }

    public string? 部门3 { get; set; }

    public string? 部门4 { get; set; }

    public string? 部门5 { get; set; }

    public string? 项目名称 { get; set; }
    public string? 项目编码 { get; set; }
    public string? 产品编码 { get; set; }
    public string? 产品名称 { get; set; }
    public string? 创建时间 { get; set; }
    public string? 开始时间 { get; set; }
    public string? 结束时间 { get; set; }
}