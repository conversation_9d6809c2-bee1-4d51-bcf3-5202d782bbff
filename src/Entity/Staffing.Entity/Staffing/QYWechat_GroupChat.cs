﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Staffing.Entity.Staffing
{
    /// <summary>
    /// 企业微信群记录
    /// </summary>
    /// <remarks>
    /// 用于当成员进群或退出时，查询群主ID使用，
    /// 当根据群主手机号匹配不上对应顾问时，也需获取群详情，
    /// 检测群主是否更换，若更换，则更新群主ID
    /// </remarks>
    [Table("qywechat_groupchat")]
    public class QYWechat_GroupChat
    {
        /// <summary>
        /// 微信群ID（主键）
        /// </summary>
        [Key]
        public required string GroupChatId { get; set; }

        /// <summary>
        /// 微信群主ID
        /// </summary>
        public required string OwnerId { get; set; }

        /// <summary>
        /// 群创建时间
        /// </summary>
        public DateTime GroupCreateTime { get; set; }

        /// <summary>
        /// 群名称
        /// </summary>
        public required string Name { get; set; }

        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 记录最后更新时间
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        public User_QYWechat User_QYWechat { get; set; } = default!;
        public List<QYWechat_GroupMember> QYWechat_GroupMember { get; set; } = default!;
    }
}
