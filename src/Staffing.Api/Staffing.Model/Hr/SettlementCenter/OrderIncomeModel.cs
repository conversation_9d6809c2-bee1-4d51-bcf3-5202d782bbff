using Config.CommonModel;
using MiniExcelLibs.Attributes;

namespace Staffing.Model.Hr.SettlementCenter;

/// <summary>
/// 订单收入请求模型
/// </summary>
public class OrderIncomeRequest : QueryRequest
{
    /// <summary>
    /// 商户ID
    /// </summary>
    public string? MerchantId { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 订单收入响应模型
/// </summary>
public class OrderIncomeListResponse : QueryResponse
{
    public List<OrderIncomeResponse> Rows { get; set; } = new List<OrderIncomeResponse>();
}

/// <summary>
/// 订单收入响应模型
/// </summary>
public class OrderIncomeResponse
{
    /// <summary>
    /// 订单单号
    /// </summary>
    [ExcelColumnName("订单单号")]
    public string OrderNumber { get; set; } = string.Empty;

    /// <summary>
    /// 交易成功时间
    /// </summary>
    [ExcelColumnName("交易成功时间")]
    public DateTime TransactionSuccessTime { get; set; }

    /// <summary>
    /// 出金方
    /// </summary>
    [ExcelColumnName("出金方")]
    public string? OutMerchantName { get; set; }

    /// <summary>
    /// 接单人
    /// </summary>
    [ExcelColumnName("接单人")]
    public string Receiver { get; set; } = string.Empty;

    /// <summary>
    /// 工号
    /// </summary>
    [ExcelColumnName("工号")]
    public string JobNumber { get; set; } = string.Empty;

    /// <summary>
    /// 收支类型
    /// </summary>
    [ExcelColumnName("收支类型")]
    public string IncomeType { get; set; } = string.Empty;

    /// <summary>
    /// 交易类型
    /// </summary>
    [ExcelColumnName("交易类型")]
    public string TransactionType { get; set; } = string.Empty;

    /// <summary>
    /// 金额
    /// </summary>
    [ExcelColumnName("金额")]
    public decimal Amount { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [ExcelColumnName("备注")]
    public string Remark { get; set; } = string.Empty;

    /// <summary>
    /// 结算状态(0=未结算，1=已结算)
    /// </summary>
    [ExcelIgnore]
    public int SettlementStatus { get; set; }

    /// <summary>
    /// 结算状态
    /// </summary>
    [ExcelColumnName("结算状态")]
    public string SettlementStatusText { get; set; } = string.Empty;

    /// <summary>
    /// 结算金额
    /// </summary>
    [ExcelColumnName("结算金额")]
    public decimal SettlementAmount { get; set; }
}