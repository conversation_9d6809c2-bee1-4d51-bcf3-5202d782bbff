﻿// using Config.CommonModel.Recruit;
// using Config.CommonModel.ThirdTalentInfo;

// namespace Infrastructure.CommonService.ShareProfit;

// /// <summary>
// /// 交付抽象方法 - 定义招聘流程处理订单方法
// /// </summary>
// public abstract class DeliveryService
// {
//     /// <summary>
//     /// Hr初筛
//     /// </summary>
//     public abstract void HrScreening(string orderId);

//     /// <summary>
//     /// 面试官筛选
//     /// </summary>
//     public abstract void InterviewerScreening(string orderId);

//     /// <summary>
//     /// 面试安排
//     /// </summary>
//     public abstract void Interview(string orderId);

//     /// <summary>
//     /// 发offer
//     /// </summary>
//     public abstract void Offer(string orderId);

//     /// <summary>
//     /// 入职
//     /// </summary>
//     public abstract void Induction(string orderId);

//     /// <summary>
//     /// 签约
//     /// </summary>
//     public abstract void Contract();

//     /// <summary>
//     /// 归档
//     /// </summary>
//     public abstract void FileAway(string orderId);
// }

// /// <summary>
// /// 默认实现类，空实现方法，需要在具体场景类里去实现
// /// </summary>
// public class CommonDeliveryService : DeliveryService
// {
//     protected readonly ProjectPostInfo _projectPostInfo;
//     protected readonly RecruitInfos _recruit;
//     protected readonly CommonPostOrder _orderService;
//     public CommonDeliveryService(ProjectPostInfo projectPostInfo, RecruitInfos recruit, CommonPostOrder orderService)
//     {
//         _projectPostInfo = projectPostInfo;
//         _recruit = recruit;
//         _orderService = orderService;
//     }

//     public override void Contract()
//     {
        
//     }

//     public override void FileAway(string orderId)
//     {
        
//     }

//     public override void HrScreening(string orderId)
//     {
        
//     }

//     public override void Induction(string orderId)
//     {
        
//     }

//     public override void Interview(string orderId)
//     {
        
//     }

//     public override void InterviewerScreening(string orderId)
//     {
        
//     }

//     public override void Offer(string orderId)
//     {
        
//     }
// }