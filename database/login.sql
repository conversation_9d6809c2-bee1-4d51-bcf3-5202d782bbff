DROP TABLE IF EXISTS `user_openid`;
CREATE TABLE `user_openid` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL COMMENT '用户Id',
  `Type` int NOT NULL COMMENT '类型',
  `AppId` varchar(50) NOT NULL COMMENT 'app/小程序Id',
  `OpenId` varchar(50) NOT NULL COMMENT 'OpenId',
  `UnionId` varchar(50) NOT NULL COMMENT 'UnionId',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_user_OpenId_AppId` (`AppId`,`OpenId`),
  KEY `IX_user_openid_UserId_AppId_OpenId_UserId` (`AppId`,`OpenId`,`UnionId`,`UserId`),
  KEY `IX_user_openid_UnionId` (`UnionId`),
  KEY `IX_user_openid_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户openid';


DROP TABLE IF EXISTS `app`;
CREATE TABLE `app` (
  `Id` int NOT NULL,
  `AppId` varchar(50) NOT NULL COMMENT 'AppId',
  `AppSecret` varchar(50) NOT NULL COMMENT 'AppSecret',
  `Type` int NOT NULL COMMENT '类型',
  `Describe` varchar(50) COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_app_AppId_Type` (`AppId`,`Type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='app';


INSERT INTO `app` (`Id`, `AppId`, `AppSecret`, `Type`, `Describe`) VALUES (1, 'wx8b4490f70e538c0e', 'eef62b094083343fd25b8d37f472a1ad', 0, '求职者小程序');
INSERT INTO `app` (`Id`, `AppId`, `AppSecret`, `Type`, `Describe`) VALUES (2, 'wxe38d12b563661718', '64e77b38b3ba84adac8fde1e22a45036', 1, '面试官小程序');
INSERT INTO `app` (`Id`, `AppId`, `AppSecret`, `Type`, `Describe`) VALUES (3, 'wx96b65aafffa82a6b', '36695648cb721126328ca0e57f8e8b44', 2, 'Hr小程序');
INSERT INTO `app` (`Id`, `AppId`, `AppSecret`, `Type`, `Describe`) VALUES (4, 'ks654147849006784091', 'dDfB0acfdjRKr7cNEORVwA', 4, '求职者快手小程序');
INSERT INTO `app` (`Id`, `AppId`, `AppSecret`, `Type`, `Describe`) VALUES (5, 'wx88562bda0455bba0', 'c1086d8aaade9974e01a91de46592a6d', 9, '张垣快聘求职者');
INSERT INTO `app` (`Id`, `AppId`, `AppSecret`, `Type`, `Describe`) VALUES (6, 'wx5ba12f1257fee0e0', '8f53b504ed8dc454857ee8c04d440831', 10, '张垣快聘B端');


INSERT INTO user_openid SELECT FLOOR(10000000000 + (RAND() * 90000000000)),
a.UserId,0,'wx8b4490f70e538c0e',a.WeChatAppletId,b.WeChatId,NOW()
FROM `user_seeker` a JOIN `user` b on a.UserId = b.UserId
WHERE LENGTH(a.WeChatAppletId) > 0;

INSERT INTO user_openid SELECT FLOOR(10000000000 + (RAND() * 90000000000)),
a.UserId,1,'wxe38d12b563661718',a.WeChatInterviewerAppletId,b.WeChatId,NOW()
FROM `user_seeker` a JOIN `user` b on a.UserId = b.UserId
WHERE LENGTH(a.WeChatInterviewerAppletId) > 0;

INSERT INTO user_openid SELECT FLOOR(10000000000 + (RAND() * 90000000000)),
a.UserId,2,'wx96b65aafffa82a6b',a.WeChatAppletId,b.WeChatId,NOW()
FROM `user_hr` a JOIN `user` b on a.UserId = b.UserId
WHERE LENGTH(a.WeChatAppletId) > 0;

INSERT INTO user_openid SELECT FLOOR(10000000000 + (RAND() * 90000000000)),
UserId,4,'ks654147849006784091',KsOpenId,KsOpenId,NOW()
FROM `user`
WHERE LENGTH(KsOpenId) > 0;


ALTER TABLE project ADD AgentHrId VARCHAR(18) NULL COMMENT'代填企业hrId';
ALTER TABLE project_team ADD AgentHrId VARCHAR(18) NULL COMMENT'代填企业hrId';

CREATE UNIQUE INDEX IX_project_HrId_AgentHrId ON project (HrId, AgentHrId);


ALTER TABLE post_team ADD QuickJobAppletQrCode VARCHAR(1024) NULL COMMENT'零工市场小程序二维码';


DROP TABLE IF EXISTS `qd_hr_channel_qrcode`;
CREATE TABLE `qd_hr_channel_qrcode` (
  `Id` varchar(18) NOT NULL,
  `ChannelId` varchar(18) NOT NULL COMMENT '渠道Id',
  `AppId` varchar(50) NOT NULL COMMENT 'AppId',
  `ChannelQrCode` varchar(1024) NOT NULL COMMENT '渠道二维码',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_qd_hr_channel_qrcode_ChannelId` (`ChannelId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='渠道二维码';


DROP TABLE IF EXISTS `user_qrcode`;
CREATE TABLE `user_qrcode` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL COMMENT '用户d',
  `AppId` varchar(50) NOT NULL COMMENT 'AppId',
  `Type` int NOT NULL COMMENT '类型',
  `QrCode` varchar(1024) NOT NULL COMMENT '二维码',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_user_qrcode_UserId` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户二维码';