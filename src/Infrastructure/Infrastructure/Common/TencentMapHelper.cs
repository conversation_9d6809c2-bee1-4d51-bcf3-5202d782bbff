﻿using Flurl.Http;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using ServiceStack.Text;
using Noah.Aliyun.Storage;
using Config;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json.Linq;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Transient)]
public class TencentMapHelper
{
    private readonly IObjectStorage _objectStorage;
    private readonly ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private RequestContext _user;
    private readonly LogManager _log;
    public TencentMapHelper(IObjectStorage objectStorage, IOptionsSnapshot<ConfigManager> config,
    IHostEnvironment hostingEnvironment, RequestContext user, LogManager log)
    {
        _objectStorage = objectStorage;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _user = user;
        _log = log;
    }

    /// <summary>
    /// 生成静态地图
    /// </summary>
    /// <param name="teamPostId"></param>
    /// <returns></returns>
    public async Task<string> GetStaticMap(string postId, double lat, double lng)
    {
        var center = $"{lat},{lng}";
        var url = $"https://apis.map.qq.com/ws/staticmap/v2/?key={Constants.TencentMapKey}&size=750x420&center={center}&markers=size:large|{center}&zoom=17";

        var bts = await url.GetBytesAsync();

        var key = $"map.png";
        using var memoryStream = new MemoryStream(bts);

        if (bts.Length < 1024)
        {
            try
            {
                var msg = JsonSerializer.DeserializeFromStream<TencentMapResponse>(memoryStream);
                _log.Error("生成职位地图出错", JsonSerializer.SerializeToString(msg), $"{postId}_{lat},{lng}");
                return string.Empty;
            }
            catch
            {
                _log.Error("生成职位地图出错（图片长度小于1024）", postId, $"{lat},{lng}");
                return string.Empty;
            }
        }

        var imgUrl = await _objectStorage.OrdinaryUploadFile(memoryStream, key, key, $"{_config.Aliyun!.Oss!.Dir!}/post/{postId}");

        if (string.IsNullOrWhiteSpace(imgUrl))
            _log.Error("oss上传职位地图出错", postId, $"{lat},{lng}");

        return imgUrl;
    }

    public async Task<LocationInfo?> GetLocationByMapAsync(string address)
    {
        string url = "https://apis.map.qq.com/ws/place/v1/suggestion/?" + "region=&keyword=" + address + "&key=" + Constants.TencentMapKey;
        //string url = "https://apis.map.qq.com/ws/geocoder/v1/?address=" + address + "&key=" + Constants.TencentMapKey;
        try
        {
            var t = JToken.Parse(await url.GetStringAsync());
            if (t != null && t.Value<int>("status") == 0 && t.Value<JToken>("result") != null)
            {
                JToken info = t.Value<JToken>("result")!;
                return new LocationInfo
                {
                    Province = info.Value<JToken>("address_components")?.Value<string>("province") ?? "",
                    City = info.Value<JToken>("address_components")?.Value<string>("city") ?? "",
                    District = info.Value<JToken>("address_components")?.Value<string>("district") ?? "",
                    Street = info.Value<JToken>("address_components")?.Value<string>("street") ?? "",
                    Address = info.Value<string>("address") ?? "",
                    RegionCode = info.Value<JToken>("ad_info")?.Value<string>("adcode") ?? "",
                    Level = info.Value<int>("level"),
                    Lat = info.Value<JToken>("location")?.Value<double>("lat") ?? 0,
                    Lng = info.Value<JToken>("location")?.Value<double>("lng") ?? 0
                };
            }
        }
        catch
        {
        }

        return null;
    }
}

public class TencentMapResponse
{
    public int status { get; set; }
}

public class LocationInfo
{
    public string? Province { get; set; }

    public string? City { get; set; }

    public string? District { get; set; }

    public string? Street { get; set; }

    public string? Address { get; set; }

    public string? RegionCode { get; set; }

    public int Level { get; set; }

    public double Lat { get; set; }

    public double Lng { get; set; }
}