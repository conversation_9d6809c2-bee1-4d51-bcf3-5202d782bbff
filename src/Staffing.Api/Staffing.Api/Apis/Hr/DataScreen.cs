using Staffing.Model.Hr.Project;
using Staffing.Core.Interfaces.Hr;
using Staffing.Model.Hr.Enterprise;
using Config.CommonModel;
using Staffing.Model.Hr.DataScreen;
using Config;
using Infrastructure.Extend;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class DataScreenRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("datascreen").WithTags("数据大屏");
        group.MapGet("/platinfo", (IDataScreenService rep) => rep.GetPlatformData())
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "获取平台级别统计数据" });

        group.MapGet("/plat/recruitinfo", (IDataScreenService rep) => rep.GetPlatformOfRecruitData())
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "获取 平台级-招聘流程 统计数据" });

        group.MapGet("/plat/seekerinfo", (IDataScreenService rep) => rep.GetPlatformOfSeekerData())
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "获取 平台级-用户概况 统计数据" });

        group.MapGet("/plat/projectinfo", (IDataScreenService rep) => rep.GetPlatformOfProjectData())
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "获取 平台级-项目看板 统计数据" });

        group.MapPost("/plat/seekerinfo/area", (AreaSearch model, IDataScreenService rep) =>
        {
            string? provinceName = model.ProvinceName;
            string? cityName = model.CityName;
            if (model.Area == 1)
            {
                return rep.GetSeekerDataForAreaByAreaName(cityName, provinceName, 1);
            }
            else
            {
                return rep.GetSeekerDataForAreaByAreaName(cityName, provinceName);
            }
        }).WithMetadata(new ApiMeta())
          .WithOpenApi(operation => new(operation) { Summary = "获取 平台级-用户概况-地区钻取数据" });

        group.MapPost("/getent", (QuerySearch model, IEntService rep) => rep.GetEnt(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "获取公司列表" });

        group.MapPost("/gethr", (QuerySearch model, IHrService rep) => rep.GetHrSimpleSearch(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "获取顾问列表" });

        group.MapPost("/getentorgtree", (GetDdOrg model, IEntService rep) => rep.GetDdOrg(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "(二级页面)获取所有组织架构" });

        group.MapPost("/getentusers", (GetDdUsers model, IEntService rep) => rep.GetDdUsers(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "(二级页面)分页获取组织下所有顾问列表" });

        group.MapPost("/getentorgdata", (GetEntOrgDataQuery model, IDataScreenService rep) => rep.GetEntOrgData(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "(二级页面)获取组织统计数据" });

        group.MapPost("/getentorgteamdata", (GetEntOrgDataQuery model, IDataScreenService rep) => rep.GetEntOrgTeamData(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "(二级页面)获取组织累计协同概况数据" });

        group.MapPost("/gethrdata", (GetHRDataQuery model, IDataScreenService rep) => rep.GetHRData(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "(三级页面)获取指定顾问的统计数据" });

        group.MapPost("/gethrproject", (GetHRProsQuery model, IProjectService rep) => rep.GetProSimpleByHR(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "(三级页面)分页获取指定顾问主创+协同的所有项目列表(不含已归档)" });
    }
}