﻿using Config.Enums;
using Nest;

namespace Staffing.Entity.Elasticsearch;

[ElasticsearchType(IdProperty = "Id")]
public class EsTalentResume
{
    [Keyword]
    public string Id { get; set; } = default!;

    /// <summary>
    /// 简历来源
    /// </summary>
    [Keyword]
    public TalentResumeSource Source { get; set; } = default!;

    /// <summary>
    /// 简历状态
    /// </summary>
    [Keyword]
    public TalentResumeStatus? Status { get; set; }

    /// <summary>
    /// 求职者Id，建立关系后有值
    /// </summary>
    [Keyword]
    public string? SeekerId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    [Wildcard]
    public string? Name { get; set; }

    /// <summary>
    /// 手机
    /// </summary>
    [Keyword]
    public string Mobile { get; set; } = string.Empty;

    /// <summary>
    /// 头像
    /// </summary>
    [Text(Index = false)]
    public string? HeadPortrait { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [Keyword]
    public Sex? Sex { get; set; }

    /// <summary>
    /// 最高学历
    /// </summary>
    [Keyword]
    public TalentVirtualEducation? Education { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    [Keyword]
    public string? Mailbox { get; set; }

    /// <summary>
    /// 微信
    /// </summary>
    [Keyword]
    public string? WeChat { get; set; }

    /// <summary>
    /// QQ
    /// </summary>
    [Keyword]
    public string? QQ { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    [Date]
    public DateTime? Birthday { get; set; }

    /// <summary>
    /// 开始工作日期
    /// </summary>
    [Date]
    public DateTime? WorkTime { get; set; }

    /// <summary>
    /// 简历完善度
    /// </summary>
    [Keyword]
    public int Perfection { get; set; } = 0;

    /// <summary>
    /// 自我评价
    /// </summary>
    [Keyword]
    public string? SelfEvaluation { get; set; }

    #region 技能分析（小析补充信息字段）
    /// <summary>
    /// 专业技能集合
    /// </summary>
    [Wildcard]
    public List<string>? SkillProfessional { get; set; }

    /// <summary>
    /// IT技能集合
    /// </summary>
    [Wildcard]
    public List<string>? SkillIT { get; set; }

    /// <summary>
    /// 商业技能集合
    /// </summary>
    [Wildcard]
    public List<string>? SkillBusiness { get; set; }

    #endregion

    /// <summary>
    /// 通用标签
    /// </summary>
    [Keyword]
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 行业标签（来源小析职业标签字段）
    /// </summary>
    [Keyword]
    public List<string>? IndustryLabel { get; set; }

    /// <summary>
    /// 职位标签（来源小析职业标签字段）
    /// </summary>
    [Keyword]
    public List<string>? PostLabel { get; set; }

    /// <summary>
    /// 自定义标签
    /// </summary>
    [Keyword]
    public List<string>? OtherLabel { get; set; }

    // todo: 亮点，风险 缺失

    /// <summary>
    /// 原始简历地址
    /// </summary>
    [Text(Index = false)]
    public string? OriginalUrl { get; set; }

    /// <summary>
    /// 所在城市
    /// </summary>
    [Keyword]
    public string? City { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    [Wildcard]
    public string? Address { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    [Wildcard]
    public string? RegionId { get; set; }

    /// <summary>
    /// 最后一次上线时间
    /// </summary>
    [Date]
    public DateTime? LastLoginTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [Date]
    public DateTime UpdatedTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Date]
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 首次注册时间
    /// </summary>
    [Date]
    public DateTime? RegistedTime { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    [Nested]
    public List<EsTalentResumeWork>? Works { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    [Nested]
    public List<EsTalentResumeEdu>? Edus { get; set; }

    /// <summary>
    /// 求职期望
    /// </summary>
    [Nested]
    public List<EsTalentResumeHope>? Hopes { get; set; }

    /// <summary>
    /// 项目经历
    /// </summary>
    [Nested]
    public List<EsTalentResumeProject>? Projects { get; set; }


    /// <summary>
    /// 作品集
    /// </summary>
    [Wildcard]
    public List<string>? Products { get; set; }

    /// <summary>
    /// 荣誉奖项
    /// </summary>
    [Nested]
    public List<EsTalentResumeHonour>? Honours { get; set; }

    /// <summary>
    /// 证书
    /// </summary>
    [Wildcard]
    public List<string>? Certificates { get; set; }

    /// <summary>
    /// 检索关键字
    /// </summary>
    [Wildcard]
    public List<string>? TalentSearch { get; set; }

    /// <summary>
    /// 通用标签
    /// </summary>
    [Boolean]
    public bool Deleted { get; set; } = false;

    /// <summary>
    /// 哪儿些顾问可以查看，为空则所有顾问可查看
    /// </summary>
    [Keyword]
    public List<string>? HrIds { get; set; }

    /// <summary>
    /// 哪儿些部门可以查看，为空则所有部门可查看
    /// </summary>
    [Keyword]
    public List<string>? DeptIds { get; set; }

    /// <summary>
    /// 隐藏失效时间
    /// </summary>
    [Date]
    public DateTime? HideInvalidTime { get; set;}
}

public class EsTalentResumeWork
{
    /// <summary>
    /// 公司名称
    /// </summary>
    [Wildcard]
    public string CompanyName { get; set; } = default!;

    /// <summary>
    /// 部门
    /// </summary>
    [Wildcard]
    public string Department { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    [Wildcard]
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 工作开始时间
    /// </summary>
    [Date]
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 工作结束时间
    /// </summary>
    [Date]
    public DateTime? EndTime { get; set; }
}

public class EsTalentResumeEdu
{
    /// <summary>
    /// 学校名称
    /// </summary>
    [Wildcard]
    public string SchoolName { get; set; } = default!;

    /// <summary>
    /// 是否全日制
    /// </summary>
    [Keyword]
    public bool IsFullTime { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    [Keyword]
    public TalentVirtualEducation? Education { get; set; }

    /// <summary>
    /// 专业名称
    /// </summary>
    [Wildcard]
    public string? MajorName { get; set; }

    /// <summary>
    /// 教育开始时间
    /// </summary>
    [Date]
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 教育结束时间
    /// </summary>
    [Date]
    public DateTime? EndTime { get; set; }
}

public class EsTalentResumeHope
{
    /// <summary>
    /// 期望城市
    /// </summary>
    [Wildcard]
    public string? HopeCity { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    [Wildcard]
    public string? RegionId { get; set; }

    /// <summary>
    /// 职位类别 1级别
    /// </summary>
    [Keyword]
    public string? CategoryLevel1 { get; set; }

    /// <summary>
    /// 职位类别 2级别
    /// </summary>
    [Wildcard]
    public string? CategoryLevel2 { get; set; }

    /// <summary>
    /// 职位类别 3级别
    /// </summary>
    [Wildcard]
    public string? CategoryLevel3 { get; set; }

    /// <summary>
    /// 职位类别名称 - 1级
    /// </summary>
    [Wildcard]
    public string? CategoryLevel1Name { get; set; }

    /// <summary>
    /// 职位类别名称 - 2级
    /// </summary>
    [Wildcard]
    public string? CategoryLevel2Name { get; set; }

    /// <summary>
    /// 职位类别名称 - 3级
    /// </summary>
    [Wildcard]
    public string? CategoryLevel3Name { get; set; }

    /// <summary>
    /// 非诺快聘职位类别名称 - 1级
    /// </summary>
    [Wildcard]
    public string? PositionLevel1Name { get; set; }

    /// <summary>
    /// 非诺快聘职位类别名称 - 2级
    /// </summary>
    [Wildcard]
    public string? PositionLevel2Name { get; set; }

    /// <summary>
    /// 非诺快聘职位类别名称 - 3级
    /// </summary>
    [Wildcard]
    public string? PositionLevel3Name { get; set; }

    /// <summary>
    /// 所在行业
    /// </summary>
    [Wildcard]
    public string? Industry { get; set; }

    /// <summary>
    /// 非诺快聘所在行业名称 - 1级
    /// </summary>
    [Wildcard]
    public string? IndustryLevel1Name { get; set; }

    /// <summary>
    /// 非诺快聘所在行业名称 - 2级
    /// </summary>
    [Wildcard]
    public string? IndustryLevel2Name { get; set; }

    /// <summary>
    /// 最低薪资
    /// </summary>
    [Keyword]
    public decimal MinSalary { get; set; } = 0;

    /// <summary>
    /// 最高薪资
    /// </summary>
    [Keyword]
    public decimal MaxSalary { get; set; } = 0;
}

public class EsTalentResumeHonour
{
    /// <summary>
    /// 荣誉名称
    /// </summary>
    [Wildcard]
    public string? HonourName { get; set; }

    /// <summary>
    /// 颁发时间
    /// </summary>
    [Date]
    public DateTime? HonourTime { get; set; }

    /// <summary>
    /// 颁发机构
    /// </summary>
    [Wildcard]
    public string? HonourCompany { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [Wildcard]
    public string? Remarks { get; set; }
}

public class EsTalentResumeProject
{
    /// <summary>
    /// 项目名称
    /// </summary>
    [Wildcard]
    public string ProjectName { get; set; } = string.Empty;

    /// <summary>
    /// 岗位名称
    /// </summary>
    [Wildcard]
    public string PostName { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间
    /// </summary>
    [Date]
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    [Date]
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 项目描述
    /// </summary>
    [Wildcard]
    public string? ProjectRemarks { get; set; }
}