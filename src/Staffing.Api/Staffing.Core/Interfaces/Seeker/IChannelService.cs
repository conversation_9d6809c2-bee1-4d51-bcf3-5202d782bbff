﻿using Config.CommonModel;
using Staffing.Model.Seeker.TalentVirtual;
using Staffing.Model.Seeker.Channel;
using Config.CommonModel.Business;
using Staffing.Model.Hr.TalentVirtual;

namespace Staffing.Core.Interfaces.Seeker;

public interface IChannelService
{
    CheckChannelResponse CheckChannel(CheckChannel model);
    EmptyResponse JoinChannel(JoinChannel model);
    GetChannelResponse GetChannelInfo(GetChannel model);
    GetHrsResponse GetHrs(GetHrs model);
    EmptyResponse SetAdviser(SetAdviser model);
    GetRecruitResponse GetRecruit(GetRecruit model);
    GetTalentPlatformResponse GetTalentPlatform(GetTalentPlatform model);
    GetTalentVirtualResponse GetTalentVirtual(GetTalentVirtual model);
    TalentVitualMainIdResponse UpdateTalentVirtual(UpdateTalentVirtaual model);
    UpdateTalentVirtualProjectsResponse UpdateTalentVirtualProjects(UpdateTalentVirtualResumeProjectSub model);
    UpdateTalentVirtualWorksResponse UpdateTalentVirtualWorks(UpdateTalentVirtualResumeWorkSub model);
    EmptyResponse DeleteTalentVirtualProjects(string id);
    EmptyResponse DeleteTalentVirtualWorks(string id);
    Task<TalentPlatformResumeDetailsResponse> GetTalentPlatformDetailsAsync(string platformId);
    Task<TalentVirtualBaseInfoResponse> GetTalentVirtualInfoAsync(string virtualId);
    Task<GetChannelQrCodeResponse> GetChannelQrCode(GetChannelQrCode model);
    Task<EmptyResponse> SendMessageToActive(string id);
    EmptyResponse TopPost(TopPost model);
    SeekerGetPostsResponse SeekerGetPosts(SeekerGetPosts model);
    Task<SeekerGetPostInfo> SeekerGetPost(SeekerGetPost model);

    /// <summary>
    /// 上传简历（调用小析服务解析简历）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse UpLoadResume(UpLoadResumeRequest model);
}