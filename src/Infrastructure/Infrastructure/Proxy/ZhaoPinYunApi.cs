﻿using Config;
using Config.CommonModel.Business;
using Config.CommonModel.ZhaoPinYun;
using Config.Enums;
using DocumentFormat.OpenXml.Bibliography;
using Flurl;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using NPOI.SS.Formula.Functions;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using Senparc.Weixin.MP;
using Senparc.Weixin.MP.AdvancedAPIs.Wxa.MerchantJson;
using ServiceStack;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Noah;
using Staffing.Entity.Staffing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Reflection;
using System.Security.Policy;
using System.Text;
using System.Threading.Tasks;
using static Infrastructure.Proxy.NdnApi;
using static Infrastructure.Proxy.ZhaoPinYunApi;

namespace Infrastructure.Proxy
{
    [Service(ServiceLifetime.Singleton)]
    public class Zhao<PERSON>inYunApi
    {
        private readonly ConfigManager _config;
        private readonly CacheHelper _cacheHelper;
        private readonly LogManager _log;
        private IDbContextFactory<StaffingContext> _staffingContextFactory;
        private readonly CommonDicService _commonDicService;
        private string api_endpoint = @"https://gwdaily.zhaopinyun.com/zhaopintong-api/openapi";
        public ZhaoPinYunApi(IOptionsSnapshot<ConfigManager> config, CacheHelper cacheHelper, LogManager log, IDbContextFactory<StaffingContext> staffingContextFactory, CommonDicService commonDicService)
        {
            _config = config.Value;
            _cacheHelper = cacheHelper;
            _log = log;
            _staffingContextFactory = staffingContextFactory;
            _commonDicService = commonDicService;
        }
        /// <summary>
        /// 发布职位信息
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<ZPYunResponse<GetJobId>> PublishJobInfo(string? postid)
        {
            try
            {
                // 根据 jobid 获取职位信息
                ZPYPublishJob jobinfo = JobInfo(postid, "");
                jobinfo.categoryId = GetYunshengPostId(jobinfo.categoryId);
                jobinfo.industryId = GetYunshengIndustryId(jobinfo.industryId);
                // 发布职位信息
                var result = await Post<GetJobId>("/job/publishJob", jobinfo);
                if (result == null)
                {
                    throw new InvalidOperationException("发布职位信息失败，服务器返回空结果");
                }
                return result!;
            }
            catch (Exception ex)
            {
                // 记录异常日志或进行其他处理
                _log.Error("发布职位信息失败", ex.Message, ex.StackTrace!);
                throw;
            }
        }
        /// <summary>
        /// 更新职位信息
        /// </summary>
        /// <returns></returns>
        public async Task<ZPYunResponse<object>> UpdateJobInfo(string? postid, string? yunshengPostId)
        {
            try
            {
                ZPYPublishJob jobinfo = JobInfo(postid, yunshengPostId);                
                jobinfo.categoryId = GetYunshengPostId(jobinfo.categoryId);
                jobinfo.industryId = GetYunshengIndustryId(jobinfo.industryId);
                var result = await Post<object>("/job/updateJob", jobinfo);
                if (result == null)
                {
                    throw new InvalidOperationException("更新职位信息失败，服务器返回空结果");
                }
                return result!;
            }
            catch (Exception ex)
            {
                // 记录异常日志
                _log.Error("更新职位信息失败", ex.Message, ex.StackTrace!);
                throw; //重新抛出异常，以便调用者处理
            }
        }
        /// <summary>
        /// 下架职位信息
        /// </summary>
        /// <param name="yunshengPostId"></param>
        /// <returns></returns>
        public async Task<ZPYunResponse<object>> DownShelfJobInfo(string? yunshengPostId)
        {
            try
            {
                Job job = new Job()
                {
                    openId = "105060",
                    jobId = yunshengPostId
                };
                var result = await Post<object>("/job/downShelfJob", job);
                if (result == null)
                {
                    throw new InvalidOperationException("下架职位信息失败");
                }
                return result!;
            }
            catch (Exception ex)
            {
                // 记录异常日志
                _log.Error("下架职位信息失败", ex.Message, ex.StackTrace!);
                throw; //重新抛出异常，以便调用者处理
            }
        }
        /// <summary>
        /// 上架职位信息
        /// </summary>
        /// <param name="yunshengPostId"></param>
        /// <returns></returns>
        public async Task<ZPYunResponse<object>> UpShelfJobInfo(string? yunshengPostId)
        {
            try
            {
                Job job = new Job()
                {
                    openId = "105060",
                    jobId = yunshengPostId
                };
                var result = await Post<object>("/job/upShelfJob", job);
                if (result == null)
                {
                    throw new InvalidOperationException("上架职位信息失败");
                }
                return result!;
            }
            catch (Exception ex)
            {
                // 记录异常日志
                _log.Error("上架职位信息失败", ex.Message, ex.StackTrace!);
                throw; //重新抛出异常，以便调用者处理
            }
        }
        public async Task<ZPYunResponse<object>> UpdateRobotConfig(string? jobid)
        {
            try
            {
                RobotConfig config = new RobotConfig()
                {
                    openId = "105060",
                    jobId = jobid,
                    chatGptState = 1,
                    workTargetState = 1,
                    exchangePhoneState = 1,
                    exchangeWeixinState = 1,
                    receiveUnmatchedResume = 1
                };
                var result = await Post<object>("/job/updateRobotConfig", config);
                return result!;
            }
            catch (Exception ex)
            {
                // 记录异常日志
                _log.Error("更新机器人配置失败", ex.Message, ex.StackTrace!);
                throw; //重新抛出异常，以便调用者处理
            }
            
        }
        public async Task<ZPYunResponse<ResumeRet>> AddResumeInfo(string? userid)
        {
            try 
            {
                PersonResumeInfo? personresumeinfo = PersonResumeInfo(userid);
                _log.Info("新增简历信息接口", "traceId："+ personresumeinfo?.traceId);
                var result = await Post<ResumeRet>("/personnel/addResume", personresumeinfo);
                if (result == null)
                {
                    throw new InvalidOperationException("新增简历信息失败，服务器返回空结果");
                }
                return result!;
            }
            catch (Exception ex)
            {
                // 记录异常日志
                _log.Error("新增简历信息失败", ex.Message, ex.StackTrace!);
                throw; //重新抛出异常，以便调用者处理
            }
        }
        public async Task<ZPYunResponse<ChatRet>> NewChat(string? jobid, string? resumeid)
        {
            try 
            {
                Chat chat = new Chat()
                {
                    openId = "105060",
                    jobId = jobid,
                    resumeId = resumeid
                };
                var result = await Post<ChatRet>("/chat/add", chat);
                return result!;
            }
            catch (Exception ex)
            {
                // 记录异常日志
                _log.Error("创建聊天会话失败", ex.Message, ex.StackTrace!);
                throw; //重新抛出异常，以便调用者处理
            }
        }
        public async Task<ZPYunResponse<ChatMessageReceive>> GetChatMessage(ChatMessage? chat)
        {
            try
            {
                var result = await Post<ChatMessageReceive>("/chat/message", chat);
                return result!;
            }
            catch (Exception ex)
            {
                // 记录异常日志
                _log.Error("获取招聘云聊天回复信息失败", ex.Message, ex.StackTrace!);
                throw; //重新抛出异常，以便调用者处理
            }
        }
        public async Task<ZPYunResponse<ChatMessageHistoryRecieve>> GetMessageHistory(GetChatMessageHistory param)
        {
            try
            {
                var result = await Post<ChatMessageHistoryRecieve>("/chat/history", param);
                return result!;
            }
            catch (Exception ex)
            {
                _log.Error($"获取招聘云聊天记录失败",ex.Message, ex.StackTrace!);
                throw;
            }
        }

        /// <summary>
        /// 招聘云获取token
        /// </summary>
        /// <returns></returns>
        public async Task<ZhaoPinYunAccessToken> GetSign(string timestamp)
        {
            ZhaoPinYunAccessToken result = new ZhaoPinYunAccessToken()
            {
                appId = "202414095688",
                appSecret = "PLDUEQRSAEBLNGRNHTMVFLIJDUDEFKEI",
                openid = "105060"
            };

            string str = $"{result.appId}&{result.appSecret}&{timestamp}";
            result.signature = Md5Helper.Md5(str).ToLower();
            try
            {                
                if (string.IsNullOrWhiteSpace(result?.signature))
                    throw new Exception("招聘云生成签名失败!");
            }
            catch (FlurlHttpException ex)
            {
                var error = await ex.GetResponseStringAsync();
                throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
            }

            return result;
        }
        /// <summary>
        /// 获取职位信息
        /// </summary>
        /// <param name="jobid"></param>
        /// <returns></returns>
        public ZPYPublishJob JobInfo(string? postid, string? yunshengPostId)
        {
            using var _context = _staffingContextFactory.CreateDbContext();

            const string DefaultOpenId = "105060";
            const string DefaultCustomerId = "";
            const string DefaultBusiness = "";
            string describe = "";

            var content = _context.Post.
                Where(post => post.PostId == postid).
                Join(_context.Project_Survey,
                      post => post.ProjectId,
                      projectSurvey => projectSurvey.ProjectId,
                      (post, projectSurvey) => projectSurvey.Content).FirstOrDefault();


            if (content != null)
            {
                var description = new StringBuilder();
                var fields = new[] {
                    content.SalaryBenefits,
                    content.HardConditions,
                    content.WorkingHours,
                    content.WorkLocation,
                    content.InterviewProcess,
                    content.OnboardingProcess,
                    content.WorkCondition
                };
                foreach (var field in fields)
                {
                    AppendDescription(field, description);
                }

                void AppendDescription(List<ProjectSurveyContentValue>? items, StringBuilder descriptionBuilder)
                {
                    if (items != null)
                    {
                        foreach (var item in items)
                        {
                            if (item.Content != null)
                            {
                                descriptionBuilder.AppendLine($"{item.Title}:{item.Content}");
                            }
                        }
                        describe = descriptionBuilder.ToString();                        
                    }
                }
            }
            //_log.Info("获取项目调研描述：", describe);

            var post = _context.Post.Where(post => post.PostId == postid
                && post.Status == PostStatus.发布中
                && post.Project.Status == ProjectStatus.已上线).
                Include(post => post.Agent_Ent).FirstOrDefault();
            if (post != null)
            {
                return new ZPYPublishJob
                {
                    openId = DefaultOpenId,
                    jobId = yunshengPostId,
                    customerId = DefaultCustomerId,
                    name = post.Name,
                    workAddress = post.Address,
                    categoryId = post.Category.ToString(),
                    industryId = post.Agent_Ent.Industry.ToString(),
                    employeeType = post.WorkNature == PostWorkNature.全职 ? 1 : post.WorkNature == PostWorkNature.兼职 ? 2 : 3,
                    description = post.Describe + describe,
                    salaryType = post.SalaryType == PostSalaryType.月薪 ? 1 : 2,
                    salaryStart = post.MinSalary,
                    salaryEnd = post.MaxSalary,
                    zhaopinPersonNum = post.DeliveryNumber,
                    workAddressGuide = post.LocationMap,
                    city = _commonDicService.GetCityById(post.RegionId).CityName == null ? _commonDicService.GetCityByAddress(post.Address)!.CityName : _commonDicService.GetCityById(post.RegionId).CityName,
                    district = _commonDicService.GetCityById(post.RegionId).CountyName == null ? _commonDicService.GetCityByAddress(post.Address)!.CountyName : _commonDicService.GetCityById(post.RegionId).CountyName,
                    business = DefaultBusiness
                };
            }
            else
            {
                return new ZPYPublishJob();
            }
        }
        public PersonResumeInfo? PersonResumeInfo(string? userid)
        {
            using var _context = _staffingContextFactory.CreateDbContext();
            const string DefaultOpenId = "105060";
            return _context.User_Seeker.
                Where(x => x.UserId == userid).
                Select(s => new PersonResumeInfo
            {
                openId = DefaultOpenId,
                traceId = userid,
                basicInfo = new basicInfo
                {
                    name = s.NickName,
                    gender = s.User_Resume.Sex == Sex.女 ? "女" : "男",
                    dateOfBirth = s.User_Resume.Birthday == null ? "" : Convert.ToDateTime(s.User_Resume.Birthday.ToString()).ToString("yyyy-MM-dd")
                },
                contactInfo = new contactInfo
                {
                    phoneNumber = Md5Helper.Md5(s.User.Mobile)
                }

                }).FirstOrDefault();
        }

        /// <summary>
        /// 通用Post请求
        /// </summary>
        /// <param name="path"></param>
        /// <param name="param"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public async Task<ZPYunResponse<T>?> Post<T>(string path, object? param)
        {
            long timestampInMilliseconds = DateTime.Now.ToUnixTimeMs();
            string timesecond = ((int)(timestampInMilliseconds / 100000)).ToString();
            var sign = await GetSign(timesecond);
            var url = api_endpoint + path;
            #region Post请求
            try
            {                
                var restr = await url.
                    WithHeader("appId", "202414095688").
                    WithHeader("timestamp", timestampInMilliseconds).
                    WithHeader("signature", sign.signature).
                    PostJsonAsync(param).ReceiveString();

                _log.Info($"招聘云Post请求", $"参数：url:{url} appId:202414095688 timestamp:{timestampInMilliseconds} signature:{sign.signature} param:{JsonSerializer.SerializeToString(param)}", $"{restr}");
                if (string.IsNullOrWhiteSpace(restr))
                    return default;

                var result = JsonSerializer.DeserializeFromString<ZPYunResponse<T>>(restr);

                return result!;
            }
            catch (FlurlHttpException ex)
            {
                var error = await ex.GetResponseStringAsync();
                _log.Error("招聘云Post请求", $"参数：url:{url} appId:202414095688 timestamp:{timestampInMilliseconds} signature:{sign.signature} param:{JsonSerializer.SerializeToString(param)}", $"httpstatus:{ex.StatusCode}，error:{error}");
                throw new Exception($"httpstatus:{ex.StatusCode}，error:{error}，请求内容：{JsonSerializer.SerializeToString(param)}");
            }
            catch (NdnApiException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _log.Error("招聘云Post请求", $"参数：url:{url} appId:202414095688 timestamp:{timestampInMilliseconds} signature:{sign.signature} param:{JsonSerializer.SerializeToString(param)}", $"error:{ex.Message}");
                throw new Exception($"error:{ex.Message}，请求内容：{JsonSerializer.SerializeToString(param)}");
            }
            #endregion
        }
        /// <summary>
        /// 检测相应结果
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="result"></param>
        /// <exception cref="Exception"></exception>

        /// <summary>
        /// 转义为招聘云职位类别id
        /// </summary>
        /// <param name="postId"></param>
        /// <returns></returns>
        public string GetYunshengPostId(string? postId)
        {
            using var _context = _staffingContextFactory.CreateDbContext();
            string? yunpostid = _context.YunSheng_Dic_Post_Relation.
                Where(x => x.NpPostId == postId).
                Select(s => s.YunshengPostId).FirstOrDefault() ?? string.Empty;
            return yunpostid!;
        }
        /// <summary>
        /// 转义为招聘云行业类别id
        /// </summary>
        /// <param name="industryId"></param>
        /// <returns></returns>
        public string GetYunshengIndustryId(string? industryId)
        {
            using var _context = _staffingContextFactory.CreateDbContext();
            string? yunindustryid = _context.YunSheng_Dic_Industry_Relation.
                Where(x => x.NpIndustryId == industryId).
                Select(s => s.YunshengIndustryId).FirstOrDefault() ?? string.Empty;
            if(industryId == "")
                yunindustryid = "KjB4GUgm_Ek";
            return yunindustryid!;
        }

        public class ZhaoPinYunAccessToken
        {
            public string? appId {  get; set; }            
            public string? appSecret { get; set; }
            public string? signature { get; set; }
            public string? openid {  get; set; }
        }

        public class ZPYunResponse<T>
        {
            /// <summary>
            /// 请求状态码，200 为成功，其他为失败
            /// </summary>
            public int code { get; set; }
            /// <summary>
            /// 请求状态描述
            /// </summary>
            public string? message { get; set; }
            /// <summary>
            /// 接口业务数据对象，不同接口返回结构有所差异。
            /// </summary>
            public T? data { get; set; }
            /// <summary>
            /// 成功标志。true表示操作成功。
            /// </summary>
            public bool? status { get; set; }
        }

        public class GetJobId
        {
            public string? jobId {  get; set; }
        }

        public class Job
        {
            public string? openId { get; set; }
            public string? jobId { get; set; }
        }
        public class RobotConfig
        {
            public string? openId { get; set; }//企业会员唯一标识
            public string? jobId { get; set; }//职位 ID
            public int? chatGptState { get; set; }//机器人配置：0 关闭；1 开启
            public int? workTargetState { get; set; }//机器人配置开启时必填 1、获取简历或联系方式；2、获取简历并邀约面试
            public int? exchangePhoneState { get; set; }//机器人配置开启时必填 交换手机号：1 开启；0 关闭
            public int? exchangeWeixinState { get; set; }//机器人配置开启时必填 交换微信：1 开启；0 关闭
            public int? receiveUnmatchedResume { get; set; }//机器人配置开启时必填 主投简历不匹配时仍接收：1开启；0 关闭
        }
        public class ResumeRet
        {
            public string? personnelId {  get; set; }//人才 ID
            public string? resumeId { get; set; }//简历版本 ID
        }

        public class Chat
        {
            public string? openId { get; set; }//企业会员唯一标识
            public string? jobId { get; set; }//职位 ID
            public string? resumeId { get; set; }//简历版本 ID
        }
        public class ChatRet
        {
            public string? chatId { get; set; }//聊天会话 ID
            public string? mateScore {  get; set; }//AI 匹配分
        }
    }
}
