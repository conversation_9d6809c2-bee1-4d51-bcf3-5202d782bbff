-- ----------------------------
-- 项目资金
-- ----------------------------
DROP TABLE IF EXISTS `project_funds`;
CREATE TABLE `project_funds` (
  `ProjectId` varchar(18) NOT NULL,
  `Amount` DECIMAL(16, 2) NOT NULL COMMENT '金额',
  PRIMARY KEY (`ProjectId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目资金';

-- ----------------------------
-- 项目资金流水
-- ----------------------------
DROP TABLE IF EXISTS `project_funds_transfers`;
CREATE TABLE `project_funds_transfers` (
  `Id` varchar(18) NOT NULL,
  `ProjectId` varchar(18) NOT NULL COMMENT '项目Id',
  `PostId` varchar(18) NULL COMMENT '职位ID',
  `UserId` varchar(18) NOT NULL COMMENT '用户Id',
  `Type` INT NOT NULL COMMENT '类型',
  `Amount` DECIMAL(16, 2) NOT NULL COMMENT '金额',
  `Balance` DECIMAL(16, 2) NOT NULL COMMENT '余额',
  `Description` VARCHAR(5000) COMMENT '描述',
  `CreatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  PRIMARY KEY (`Id`),
  KEY `IX_project_funds_transfers_PostId` (`PostId`),
  KEY `IX_project_funds_transfers_ProjectId` (`ProjectId`),
  KEY `IX_project_funds_transfers_UserId` (`UserId`),
  KEY `IX_project_funds_transfers_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目资金流水';
