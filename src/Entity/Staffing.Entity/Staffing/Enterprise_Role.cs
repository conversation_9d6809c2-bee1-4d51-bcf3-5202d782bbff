﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 企业角色
/// </summary>
[Table("enterprise_role")]
public class Enterprise_Role
{
    [Key]
    public string RoleId { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 企业Id
    /// </summary>
    public string? EntId { get; set; }

    /// <summary>
    /// 0=系统，1=自定义
    /// </summary>
    public RoleType Type { get; set; } = RoleType.Custom;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 权限列表
    /// </summary>
    public List<string> Powers { get; set; } = new List<string>();

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 管辖范围
    /// </summary>
    public string? Scope { get; set; }

    /// <summary>
    /// 图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string? Creator { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public User_Hr Creator_User { get; set; } = default!;
}

public enum RoleType
{
    [Description("系统")]
    System,

    [Description("自定义")]
    Custom
}