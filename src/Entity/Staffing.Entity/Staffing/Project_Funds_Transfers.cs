﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config;
using Config.CommonModel;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 项目资金流水
/// </summary>
[Table("project_funds_transfers")]
public class Project_Funds_Transfers
{
    /// <summary>
    /// ID
    /// </summary>
    [Key]
    public string Id { get; set; } = default!;

    /// <summary>
    /// 项目ID
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 职位ID
    /// </summary>
    public string? PostId { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 类型
    /// </summary>  
    public ProjectFundsTransfersTypeEnums Type { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 余额
    /// </summary>
    public decimal Balance { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }
}