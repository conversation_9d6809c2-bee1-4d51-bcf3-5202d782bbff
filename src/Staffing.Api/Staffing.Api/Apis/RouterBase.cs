namespace Staffing.Api.Apis;

public interface ISeekerRouterBase
{
    void AddRoutes(RouteGroupBuilder group);
}

public interface IHrRouterBase
{
    void AddRoutes(RouteGroupBuilder group);
}

public interface IInterviewerRouterBase
{
    void AddRoutes(RouteGroupBuilder group);
}

public interface IAdminRouterBase
{
    void AddRoutes(RouteGroupBuilder group);
}

public interface IInternalRouterBase
{
    void AddRoutes(RouteGroupBuilder group);
}

public interface ICommonRouterBase
{
    void AddRoutes(RouteGroupBuilder group);
}

public interface ICallbackRouterBase
{
    void AddRoutes(RouteGroupBuilder group);
}