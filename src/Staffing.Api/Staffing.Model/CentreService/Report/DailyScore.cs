using Config.Enums;
using Config.CommonModel;

namespace Staffing.Model.CentreService.Report;

public class GetDailyScore : QueryRequest
{
    /// <summary>
    /// 用户类型
    /// </summary>
    public SeekerOrHr? UserType { get; set; }

    /// <summary>
    /// 起始时间
    /// </summary>
    public DateTime? BeginTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

public class GetDailyScoreResponse : QueryResponse
{
    public List<GetDailyScoreInfo> Rows { get; set; } = default!;
}

public class GetDailyScoreInfo
{
    public string Date { get; set; } = string.Empty;

    /// <summary>
    /// 积分
    /// </summary>
    public int Score { get; set; }

    /// <summary>
    /// 用户
    /// </summary>
    public int User { get; set; }

    /// <summary>
    /// 注册
    /// </summary>
    public GetDailyScoreSubInfo Zc { get; set; } = new GetDailyScoreSubInfo();

    /// <summary>
    /// 每日登录
    /// </summary>
    public GetDailyScoreSubInfo Dl { get; set; } = new GetDailyScoreSubInfo();

    /// <summary>
    /// 首次报名
    /// </summary>
    public GetDailyScoreSubInfo Bm { get; set; } = new GetDailyScoreSubInfo();

    /// <summary>
    /// 分享
    /// </summary>
    public GetDailyScoreSubInfo Fx { get; set; } = new GetDailyScoreSubInfo();

    /// <summary>
    /// 首次入职
    /// </summary>
    public GetDailyScoreSubInfo Rz { get; set; } = new GetDailyScoreSubInfo();

    /// <summary>
    /// 分享好友登录
    /// </summary>
    public GetDailyScoreSubInfo Fxdl { get; set; } = new GetDailyScoreSubInfo();

    /// <summary>
    /// 分享好友注册
    /// </summary>
    public GetDailyScoreSubInfo FxZc { get; set; } = new GetDailyScoreSubInfo();

    /// <summary>
    /// 分享好友报名
    /// </summary>
    public GetDailyScoreSubInfo Fxbm { get; set; } = new GetDailyScoreSubInfo();

    /// <summary>
    /// 分享好友入职
    /// </summary>
    public GetDailyScoreSubInfo Fxrz { get; set; } = new GetDailyScoreSubInfo();

    /// <summary>
    /// 发布职位
    /// </summary>
    public GetDailyScoreSubInfo Fbzw { get; set; } = new GetDailyScoreSubInfo();

    /// <summary>
    /// 协同项目
    /// </summary>
    public GetDailyScoreSubInfo Xtxm { get; set; } = new GetDailyScoreSubInfo();
}

public class GetDailyScoreSubInfo
{
    /// <summary>
    /// 积分
    /// </summary>
    public int Score { get; set; }

    /// <summary>
    /// 用户
    /// </summary>
    public int User { get; set; }
}