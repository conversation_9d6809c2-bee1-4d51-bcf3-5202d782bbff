using Config;
using Infrastructure.Aliyun;
using Infrastructure.Common;
using Infrastructure.Extend;

namespace Staffing.Api.Apis.Callback;

[Service(ServiceLifetime.Transient)]
public class AliyunRouter : ICallbackRouterBase
{
    public void AddRoutes(RouteGroupBuilder group)
    {
        group.MapPost("/thirdabut/alysecretno/callrecord", (List<CallRecord> model, SecretNoHelper secretNoHelper, LogManager log) =>
        {
            var result = new SecretNoResponse();
            try
            {
                secretNoHelper.CallRecord(model);
            }
            catch (Exception e)
            {
                log.Error("阿里云隐私号码通话记录回调失败", Tools.GetErrMsg(e), model);
                return new SecretNoResponse
                {
                    code = 500,
                    msg = "内部错误，已通知程序员"
                };
            }

            return result;
        })
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "阿里云隐私号码通话记录回调" });
    }
}