﻿using Config.Enums;
using System.Text.Json.Serialization;

namespace Config.CommonModel.Business;

#region 详情相关模型

/// <summary>
/// 平台人才库详情模型
/// </summary>
public class TalentPlatformResumeDetailsResponse
{
    /// <summary>
    /// 平台人才库id
    /// </summary>
    public string PlatformId { get; set; } = default!;

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 用户头像
    /// </summary>
    public string? HeadPortrait { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    [JsonIgnore]
    public string? _IdentityCard { get; set; }

    /// <summary>
    /// 求职者ImId
    /// </summary>
    public string? SeekerTencentImId { get; set; }

    /// <summary>
    /// 是否实名
    /// </summary>
    public bool IsRealName { get; set; } = false;

    /// <summary>
    /// 生日
    /// </summary>
    [JsonIgnore]
    public DateOnly? _Birthday { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 性别解释
    /// </summary>
    public string? SexName { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public OccupationType? Occupation { get; set; }

    /// <summary>
    /// 职业解释
    /// </summary>
    public string? OccupationName { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 寻访意向
    /// </summary>
    public string? SearchIntention { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Mailbox { get; set; }

    /// <summary>
    /// 微信
    /// </summary>
    public string? WeChat { get; set; }

    /// <summary>
    /// QQ
    /// </summary>
    public string? QQ { get; set; }

    /// <summary>
    /// 简历完整度
    /// </summary>
    public int Perfection { get; set; } = 0;

    /// <summary>
    /// 标签
    /// </summary>
    public List<string>? Label { get; set; }

    /// <summary>
    /// 性格
    /// </summary>
    [JsonIgnore]
    public List<string> Nature { get; set; } = new List<string>();

    /// <summary>
    /// 技能
    /// </summary>
    [JsonIgnore]
    public List<string> Skill { get; set; } = new List<string>();

    /// <summary>
    /// 外貌
    /// </summary>
    [JsonIgnore]
    public List<string> Appearance { get; set; } = new List<string>();

    /// <summary>
    /// 自我评价
    /// </summary>
    public string? SelfEvaluation { get; set; }

    /// <summary>
    /// 简历附件地址
    /// </summary>
    public string? AttachmentUrl { get; set; }

    /// <summary>
    /// 技能证书
    /// </summary>
    public List<string>? SkillsCert { get; set; }

    /// <summary>
    /// 简历二维码
    /// </summary>
    public string? HrAppletQrCode { get; set; }

    /// <summary>
    /// 活跃度
    /// </summary>
    public OnlineStatus Active { get; set; }

    /// <summary>
    /// 活跃度解释
    /// </summary>
    public string? ActiveName { get; set; }

    /// <summary>
    /// 顾问数量 - 对他感兴趣
    /// </summary>
    public int Advisers { get; set; }

    /// <summary>
    /// 位置信息
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    public List<TalentPlatformResumeWorkSub>? WorkSub { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public TalentPlatformResumeEduSub? EduSub { get; set; }

    /// <summary>
    /// 实践经历
    /// </summary>
    public List<TalentPlatformResumePracticeSub>? PracticeSub { get; set; }
}

/// <summary>
/// 工作经历（详情子表）
/// </summary>
public class TalentPlatformResumeWorkSub
{
    /// <summary>
    /// 公司名称
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 公司Logo
    /// </summary>
    public string? CompanyLogo { get; set; }

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 公司描述
    /// </summary>
    public string? CompanyRemarks { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateOnly? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateOnly? EndTime { get; set; }

    /// <summary>
    /// 开始时间 - 年月
    /// </summary>
    public string? StartTimeYM { get; set; }

    /// <summary>
    /// 结束时间 - 年月
    /// </summary>
    public string? EndTimeYM { get; set; }
}

/// <summary>
/// 教育经历（详情子表）
/// </summary>
public class TalentPlatformResumeEduSub
{
    /// <summary>
    /// 学校名称
    /// </summary>
    public string? SchoolName { get; set; }

    /// <summary>
    /// 学校logo
    /// </summary>
    public string? SchoolLogo { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }


    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 专业
    /// </summary>
    public string? MajorName { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateOnly? EndTime { get; set; }

    /// <summary>
    /// 结束时间 - 年月
    /// </summary>
    public string? EndTimeYM { get; set; }
}

/// <summary>
/// 实践经历（详情子表）
/// </summary>
public class TalentPlatformResumePracticeSub
{
    /// <summary>
    /// 实践名称
    /// </summary>
    public string? PracticeName { get; set; }

    /// <summary>
    /// 实践logo
    /// </summary>
    public string? ProjectLogo { get; set; }

    /// <summary>
    /// 职位奖项
    /// </summary>
    public string? PostPrize { get; set; }

    /// <summary>
    /// 经历业绩
    /// </summary>
    public string? Experience { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateOnly? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateOnly? EndTime { get; set; }

    /// <summary>
    /// 开始时间 - 年月
    /// </summary>
    public string? StartTimeYM { get; set; }

    /// <summary>
    /// 结束时间 - 年月
    /// </summary>
    public string? EndTimeYM { get; set; }
}

/// <summary>
/// 删除平台人才库
/// </summary>
public class DeleteTalentPlatformRequest
{
    /// <summary>
    /// 删除人才库id
    /// </summary>
    public List<string> PlatformId { get; set; } = default!;
}

/// <summary>
/// 获取寻访意向（月斌模型）
/// </summary>
public class GetSearchIntentionModel
{
    public bool Success { get; set; }

    public int Code { get; set; }

    public string? Message { get; set; }

    public string? Data { get; set; }
}

#endregion