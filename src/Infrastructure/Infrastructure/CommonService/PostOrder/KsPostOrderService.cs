﻿using Config;
using Config.CommonModel.Recruit;
using Config.CommonModel.ThirdTalentInfo;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.CommonInterface;
using Infrastructure.CommonService;
using Infrastructure.CommonService.Ndn;
using Infrastructure.CommonService.ShareProfit;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Staffing.Entity;

namespace Staffing.Core.Service.PostOrder;

/// <summary>
/// 快招工下单服务
/// </summary>
[TypeService(ServiceLifetime.Transient)]
public class KsPostOrderService : CommonPostOrder
{
    private readonly PaymentStep _step;

    public KsPostOrderService(IDbContextFactory<StaffingContext> contextFactory, INdnService ndnService, PaymentStep step, LogManager log)
        : base(contextFactory, ndnService, log)
    {
        _step = step;
    }

    public void SaveToOrderTable(ThirdTalentInfo TalentInfo, ProjectPostInfo ProjectInfo, PostOrderExceptionMessage ex)
    {
        ex.PostName = ProjectInfo.PostName;
        ex.SeekerName = TalentInfo.Name;
        ex.ExceptionMessage = new List<string>();
        // 加全局锁
        var lockerKey = $"st:postorder:{KuaishouConst.POST_ORDER_CONST_LOCK_KEY_STR}";
        using var locker = MyRedis.Lock(lockerKey, 15).GetAwaiter().GetResult();
        if (locker == null)
            throw new BadRequestException("获取订单锁失败");

        var ResumeBufferId = TalentInfo.ResumeBufferId;
        var TeamPostId = TalentInfo.SendTeamPostId;

        using var _context = _contextFactory.CreateDbContext();
        ProjectInfo = _context.Post_Team
            .Select(s => new ProjectPostInfo
            {
                ProjectId = s.Project_Team.ProjectId,
                ProjectName = s.Project_Team.Project.Agent_Ent.Name,
                ProjectCode = Constants.ProjectIdPre + s.Project_Team.Project.AutoId.ToString().PadLeft(6, '0'),
                TeamProjectId = s.Project_Team.TeamProjectId,
                ResumeBufferId = ResumeBufferId,
                TeamPostId = s.TeamPostId,
                PostId = s.PostId,
                PostName = s.Post.Name,
                SeekerName = TalentInfo.Name,
                SeekerMobile = TalentInfo.Phone,
                HrId = s.Project_Team.Project.HrId,// 主创顾问id
                HrName = s.Project_Team.Project.User_Hr.NickName,// 主创顾问名称
                TeamHrId = s.Project_Team.HrId,// 协同顾问id
                TeamHrName = s.Project_Team.User_Hr.NickName,// 协同顾问名称
                ChannelHrId = TalentInfo.ChannelHrid,// 渠道商id

                ChannelHrName = TalentInfo.ChannelHrName,// 渠道商名称
                PaymentNode = s.Post.PaymentNode,
                PaymentDays = s.Post.PaymentDays,
                PaymentType = s.Project_Team.Project.PaymentType,
                Money = s.Post.Money,
                Status = BountyStatus.交付中,// 默认交付中
                Source = TeamBountySource.快招工,
                Sex = s.Post.Sex,
                Education = s.Post.Education,
                WorkNature = s.Post.WorkNature,
                GraduationYear = s.Post.GraduationYear,
                MinAge = s.Post.MinAge,
                MaxAge = s.Post.MaxAge,
                ChannelId = TalentInfo.ChannelId,
                ProjectType = s.Project_Team.Type,
                Show = s.Show
            })
            .FirstOrDefault(f => f.TeamPostId == TeamPostId) ?? new ProjectPostInfo();

        // 走招聘流程
        TalentInfo.Source = RegisterSource.KsTalent;
        _step.ResumeToSeeker(TalentInfo, ex);

        // 判断职位是否已下线
        if (!ProjectInfo.Show)
        {
            return;
            //throw new BadRequestException("该职位已关闭");
        }

        _step.ToRecruit(TalentInfo, ProjectInfo, ex);
        if (ex.ExceptionMessage != null && ex.ExceptionMessage.Count > 0)
            return;
    }
}
