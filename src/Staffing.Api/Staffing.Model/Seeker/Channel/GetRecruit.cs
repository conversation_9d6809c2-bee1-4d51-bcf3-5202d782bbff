using Config.CommonModel;
using Config.Enums;

namespace Staffing.Model.Seeker.Channel;

/// <summary>
/// 招聘流程
/// </summary>
public class GetRecruit : QueryRequest
{
    /// <summary>
    /// 渠道Id
    /// </summary>
    public string? ChannelId { get; set; }

    /// <summary>
    /// 搜索（姓名、电话）
    /// </summary>
    public string Search { get; set; } = string.Empty;
}

public class GetRecruitResponse : QueryResponse
{

    public List<GetRecruitInfo> Rows { get; set; } = default!;
}

/// <summary>
/// 招聘流程
/// </summary>
public class GetRecruitInfo
{
    /// <summary>
    /// 招聘流程Id
    /// </summary>
    public string? RecruitId { get; set; } = default!;

    /// <summary>
    /// 求职者Id
    /// </summary>
    public string? SeekerId { get; set; } = default!;

    /// <summary>
    /// 投递职位
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public string? EduExp { get; set; }

    /// <summary>
    /// 招聘流程状态
    /// </summary>
    public RecruitStatus RecruitStatus { get; set; }

    /// <summary>
    /// 招聘流程状态
    /// </summary>
    public string? RecruitStatusName { get; set; }

    /// <summary>
    /// 顾问名称
    /// </summary>
    public string? HrName { get; set; } = default!;

    /// <summary>
    /// 顾问头像
    /// </summary>
    public string? HrAvatar { get; set; } = default!;

    /// <summary>
    /// 顾问职位
    /// </summary>
    public string? HrPost { get; set; } = default!;

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 用户头像
    /// </summary>
    public string? HeadPortrait { get; set; }

    /// <summary>
    /// 是否实名
    /// </summary>
    public bool? IsRealName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 性别解释
    /// </summary>
    public string? SexName { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public OccupationType? Occupation { get; set; }

    /// <summary>
    /// 职业解释
    /// </summary>
    public string? OccupationName { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 所在地
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatTime { get; set; }

    /// <summary>
    /// 最后登录时间
    /// </summary>
    public DateTime? LastTime { get; set; }

    /// <summary>
    /// 活跃度
    /// </summary>
    public OnlineStatus Active { get; set; }

    /// <summary>
    /// 活跃度解释
    /// </summary>
    public string ActiveName { get; set; } = default!;

    /// <summary>
    /// 简历二维码
    /// </summary>
    public string? HrAppletQrCode { get; set; }

    /// <summary>
    /// 用户手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 学校
    /// </summary>
    public string? School { get; set; }

    /// <summary>
    /// 专业
    /// </summary>
    public string? Major { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? SeekerRegionId { get; set; }

    /// <summary>
    /// 毕业时间
    /// </summary>
    public DateOnly? GraduationDate { get; set; }
}