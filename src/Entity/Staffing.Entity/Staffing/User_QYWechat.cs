﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;

namespace Staffing.Entity.Staffing
{
    /// <summary>
    /// 顾问绑定企业微信的记录表
    /// </summary>
    [Table("user_qywechat")]
    public class User_QYWechat
    {
        /// <summary>
        /// user_hr的UserId
        /// </summary>
        [Key]
        public required string UserId { get; set; }

        /// <summary>
        /// 企业微信的UserId
        /// *根据Mobile字段，调用企业微信接口获取
        /// </summary>
        public required string QWUserId { get; set; }

        /// <summary>
        /// 企业微信手机号
        /// </summary>
        public required string Mobile { get; set; }


        /// <summary>
        /// 用户企微姓名
        /// </summary>
        public required string QWName { get; set; }
        /// <summary>
        /// 企微职务信息
        /// </summary>
        public required string QWPosition { get; set; }
        /// <summary>
        /// 性别。0表示未定义，1表示男性，2表示女性	
        /// </summary>
        public int QWGender { get; set; }
        /// <summary>
        /// 企微头像url
        /// </summary>
        public required string QWAvatar { get; set; }
        /// <summary>
        /// 企微头像缩略图url
        /// </summary>
        public required string QWThumb_Avatar { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedTime { get; set; } = DateTime.Now;

        public User_Hr User_Hr { get; set; } = default!;

        public List<QYWechat_GroupChat> QYWechat_GroupChat { get; set; } = default!;
    }
}
