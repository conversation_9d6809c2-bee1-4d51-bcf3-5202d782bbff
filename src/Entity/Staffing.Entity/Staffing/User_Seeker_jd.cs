﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;
using NetTopologySuite.Geometries;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 求职者——京东家政报名表
/// </summary>
[Table("user_seeker_jd")]
public class User_Seeker_Jd
{
     [Key]
     public string Id { get; set; } = EntityTools.SnowflakeId();

     /// <summary>
     /// 求职者Id
     /// </summary>
     public string SeekerId { get; set; } = default!;

     /// <summary>
     /// 岗位
     /// </summary>
     public string TeamPostId { get; set; } = default!;

     /// <summary>
     /// 名称
     /// </summary>
     public string Name { get; set; } = default!;

     /// <summary>
     /// 电话
     /// </summary>
     public string Phone { get; set; } = default!;

     /// <summary>
     /// 身份证
     /// </summary>
     public string IDCard { get; set; } = default!;

     /// <summary>
     /// 是否知晓健康证
     /// </summary>
     public bool IsHealthCard { get; set; } = default!;

     /// <summary>
     /// 是否知晓购买工具箱
     /// </summary>
     public bool IsToolbox { get; set; } = default!;

     /// <summary>
     /// 创建时间
     /// </summary>
     public DateTime CreatedTime { get; set; } = DateTime.Now;

     /// <summary>
     /// 培训时间
     /// </summary>
     public DateTime TrainingTime { get; set; } = default!;
}
