﻿using System.ComponentModel;
using Config.CommonModel;
using Config.CommonModel.TalentResume;
using Config.Enums;

namespace Staffing.Model.Hr.TalentResume;

/// <summary>
/// 职位选择框查询
/// </summary>
public class PostTeamPageListRequest : QueryRequest
{
    public string? Search { get; set; }
}

/// <summary>
/// 职位选择框返回信息
/// </summary>
public class PostTeamInfoPageListResponse : QueryResponse
{
    public List<PostTeamInfo> Rows { get; set; } = default!;
}

public class PostTeamInfo
{
    /// <summary>
    /// 职位Id
    /// </summary>
    public string TeamPostId { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 职位编码
    /// </summary>
    public string? PostCode { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    /// 地区id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 城市
    /// </summary>
    public string? City { get; set; }

    /// <summary>
    /// 代招公司
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 公司地址
    /// </summary>
    public string? CompanyAddress { get; set; }

    /// <summary>
    /// 职位状态
    /// </summary>
    public PostStatus? Status { get; set; }

    /// <summary>
    /// 职位状态名称
    /// </summary>
    public string? StatusName { get; set; }

    public string? TeamAutoId { get; set; }

    public HrProjectType? hrProjectType { get; set; }
}

/// <summary>
/// 职位选择框查询
/// </summary>
public class TalentResumePageListRequest : QueryRequest
{
    /// <summary>
    /// 职位id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// tab选择
    /// </summary>
    public TabSelect? TabSelect { get; set; }

    /// <summary>
    /// 地区regionid集合
    /// </summary>
    public List<string>? RegionIds { get; set; }

    /// <summary>
    /// 简历来源
    /// </summary>
    public TalentResumeSource? Source { get; set; }

    /// <summary>
    /// 关键字筛选，岗位、公司、学校、技能
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 自定义筛选
    /// </summary>
    public TalentResumeSelect? SelfSelect { get; set; }
}

public class TalentResumeSelect
{
    /// <summary>
    /// 最小年龄
    /// </summary>
    public int? MinAge { get; set; }

    /// <summary>
    /// 最大年龄
    /// </summary>
    public int? MaxAge { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 最小工作经验，单位年
    /// </summary>
    public int? MinWorkTime { get; set; }

    /// <summary>
    /// 最大工作经验，单位年
    /// </summary>
    public int? MaxWorkTime { get; set; }

    /// <summary>
    /// 活跃日期
    /// </summary>
    public ActiveTime? ActiveTime { get; set; }

    // /// <summary>
    // /// 学历性质 todo:暂时先不做这个筛选条件，后续再做
    // /// </summary>
    // public EducationNature? EducationNature { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public List<EducationType>? EducationType { get; set; }
}

/// <summary>
/// 活跃日期
/// </summary>
public enum ActiveTime
{
    今日活跃 = 1,

    [Description("3天内活跃")]
    三天内活跃 = 3,

    [Description("7天内活跃")]
    七天内活跃 = 7,

    [Description("30天内活跃")]
    一个月内活跃 = 30,
}

/// <summary>
/// 学历性质
/// </summary>
public enum EducationNature
{
    九八五,
    二一一
}


public enum TabSelect
{
    最新, 最准
}

/// <summary>
/// 人才列表返回信息
/// </summary>
public class TalentResumeListResponse : QueryResponse
{
    public List<TalentResumeListModel> Rows { get; set; } = default!;

    public int? Count { get; set; }
}

public class TalentResumeListModel
{
    /// <summary>
    /// 人才库id
    /// </summary>
    public string Id { get; set; } = default!;

    /// <summary>
    /// 诺快聘 - 求职者Id，如果不为空就是已注册
    /// </summary>
    public string? SeekerId { get; set; }

    /// <summary>
    /// 腾讯ImId
    /// </summary>
    public string? TencentImId { get; set; }

    /// <summary>
    /// 简历来源
    /// </summary>
    public TalentResumeSource? Source { get; set; }

    /// <summary>
    /// 简历来源解释
    /// </summary>
    public string? SourceName { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 用户头像
    /// </summary>
    public string? HeadPortrait { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 性别解释
    /// </summary>
    public string? SexName { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateTime? Birthday { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public TalentVirtualEducation? Education { get; set; }

    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 期望从事职业
    /// </summary>
    public List<string>? HopePosts { get; set; }

    /// <summary>
    /// 活跃时间
    /// </summary>
    public DateTime? LastLoginTime { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public List<string>? Label { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    public string? WorkExp { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public string? EduExp { get; set; }

    /// <summary>
    /// 是否联系
    /// </summary>
    public bool IfConnected { get; set; } = false;

    /// <summary>
    /// 简历完善度
    /// </summary>
    public int Perfection { get; set; }

    /// <summary>
    /// 所在市
    /// </summary>
    public string? City { get; set; }

    /// <summary>
    /// 具体地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 电话号码 131****5052
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 开始工作时间
    /// </summary>
    public DateTime? WorkTime { get; set; } 

    public DateTime? CreatedTime { get; set; }

    public DateTime? UpdatedTime { get; set; }

    public DateTime? HideInvalidTime { get; set; }
}

/// <summary>
/// 人才融合库人才详情返回信息
/// </summary>
public class TalentResumeDetailResponse
{
    /// <summary>
    /// 主键id
    /// </summary>
    public string Id { get; set; } = default!;

    /// <summary>
    /// 腾讯ImId
    /// </summary>
    public string? TencentImId { get; set; }

    /// <summary>
    /// 用户来源
    /// </summary>
    public TalentResumeSource Source { get; set; } = TalentResumeSource.未知;

    /// <summary>
    /// 简历状态
    /// </summary>
    public TalentResumeStatus Status { get; set; } = TalentResumeStatus.UnRegistered;

    /// <summary>
    /// 简历状态描述
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 求职者Id，建立关系后有值
    /// </summary>
    public string? SeekerId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 手机
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 模糊手机号
    /// </summary>
    public string Phone { get; set; } = default!;

    /// <summary>
    /// 头像
    /// </summary>
    public string? HeadPortrait { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 性别描述
    /// </summary>
    public string? SexName { get; set; }

    /// <summary>
    /// 最高学历
    /// </summary>
    public TalentVirtualEducation? Education { get; set; }

    /// <summary>
    /// 最高学历描述
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Mailbox { get; set; }

    /// <summary>
    /// 微信
    /// </summary>
    public string? WeChat { get; set; }

    /// <summary>
    /// QQ
    /// </summary>
    public string? QQ { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateTime? Birthday { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 开始工作日期
    /// </summary>
    public DateTime? WorkTime { get; set; }

    /// <summary>
    /// 简历完善度
    /// </summary>
    public int Perfection { get; set; } = 0;

    /// <summary>
    /// 自我评价
    /// </summary>
    public string? SelfEvaluation { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public List<Edus>? Edus { get; set; }

    /// <summary>
    /// 求职期望
    /// </summary>
    public List<NHopes>? Hopes { get; set; }

    /// <summary>
    /// 项目经历
    /// </summary>
    public List<Projects>? Projects { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    public List<Works>? Works { get; set; }

    /// <summary>
    /// 作品集
    /// </summary>
    public List<string>? Products { get; set; }

    /// <summary>
    /// 荣誉奖项
    /// </summary>
    public List<Honours>? Honours { get; set; }

    /// <summary>
    /// 证书
    /// </summary>
    public List<string>? Certificates { get; set; }

    /// <summary>
    /// 技能分析
    /// </summary>
    public TalentResumeSkillSub? SkillAnalysis { get; set; }

    /// <summary>
    /// 行业标签（来源小析职业标签字段）
    /// </summary>
    public List<string>? IndustryLabel { get; set; }

    /// <summary>
    /// 职位标签（来源小析职业标签字段）
    /// </summary>
    public List<string>? PostLabel { get; set; }

    /// <summary>
    /// 自定义标签
    /// </summary>
    public List<string>? OtherLabel { get; set; }

    /// <summary>
    /// 通用标签
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 亮点
    /// </summary>
    public TalentResumeHighlights? Highlights { get; set; }

    /// <summary>
    /// 风险
    /// </summary>
    public TalentResumeRisks? Risks { get; set; }

    /// <summary>
    /// 原始简历地址 - 附件地址
    /// </summary>
    public string? OriginalUrl { get; set; }

    /// <summary>
    /// 所在城市
    /// </summary>
    public string? City { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 最后一次上线时间
    /// </summary>
    public DateTime? LastLoginTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 联系次数
    /// </summary>
    public int ConnectTimes { get; set; } = 0;

    /// <summary>
    /// 二维码
    /// </summary>
    public string? HrAppletQrCode { get; set; }
}

/// <summary>
/// 短信内容模型返回信息
/// </summary>
public class MessageModelResponse
{
    /// <summary>
    /// 短信内容
    /// </summary>
    public string Message { get; set; } = default!;
}

/// <summary>
/// 发送短信请求模型
/// </summary>
public class SendMessageRequest
{
    /// <summary>
    /// 人才库id
    /// </summary>
    public string ResumeId { get; set; } = default!;

    /// <summary>
    /// 职位ID
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 短信内容
    /// </summary>
    public string? Message { get; set; }
}

/// <summary>
/// 获取手机号模型
/// </summary>
public class GetTelephoneNumber
{
    /// <summary>
    /// 真实手机号
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// 虚拟手机号
    /// </summary>
    public string? VirtualPhoneNumber { get; set; }
}

/// <summary>
/// 短信沟通模型
/// </summary>
public class MessageConnectionModel
{
    /// <summary>
    /// 沟通职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 短信邀约流程状态
    /// </summary>
    public MessageConnectionStatus? MessageConnectionStatus { get; set; }
}

public enum MessageConnectionStatus
{
    短信邀约报名,
    投递简历,
    面试邀请
}

public class ProcessRecordsResponse
{
    /// <summary>
    /// 处理记录
    /// </summary>
    public List<ProcessRecordModel> Rows { get; set; } = new List<ProcessRecordModel>();

    /// <summary>
    /// 总数
    /// </summary>
    public int Total { get; set; }

    /// <summary>
    /// 短信沟通
    /// </summary>
    public MessageConnectionModel? MessageConnection { get; set; }
}

/// <summary>
/// 人才处理记录模型
/// </summary>
public class ProcessRecordModel
{
    /// <summary>
    /// 处理人
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// 处理人职位名称
    /// </summary>
    public string? HrPostName { get; set; }

    /// <summary>
    /// 沟通职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 处理方式
    /// </summary>
    public ConnectType? ConnectType { get; set; }

    /// <summary>
    /// 处理方式名称
    /// </summary>
    public string? ConnectTypeName { get; set; }

    /// <summary>
    /// 处理时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }
}

public class SelectFields : TalentResumeSelect
{
    public int? Category { get; set; }
    public string? RegionId { get; set; }
    public EducationType? Education { get; set;}
}
