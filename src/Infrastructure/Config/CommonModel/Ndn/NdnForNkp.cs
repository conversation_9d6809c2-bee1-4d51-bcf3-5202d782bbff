namespace Config.CommonModel.Ndn;

public class FreezeBudgetByPost
{
    /// <summary>
    /// 项目编码
    /// </summary>
    public string projectCode { get; set; } = default!;

    /// <summary>
    /// 冻结/恢复标志（1：冻结；2：恢复）
    /// </summary>
    public BudgetFreezeType type { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal amount { get; set; }

    /// <summary>
    /// 职位Id
    /// </summary>
    public string? postId { get; set; }

    public string HrId { get; set; } = default!;
    public string ProjectId { get; set; } = default!;
}

// public class ProcessAutoTasks
// {
//     public string? TodoId { get; set; }
// }

public class ProcessTasks
{
    public string? Id { get; set; }
    public string? HrNo { get; set; }
}

public class CreateNdnProject : CreateProject
{
    /// <summary>
    /// 数字诺亚项目Id（诺快聘表key）
    /// </summary>
    public string? ProjId { get; set; }
}

public class CreateNdnServiceBonus
{
    public string? projectCode { get; set; }
}