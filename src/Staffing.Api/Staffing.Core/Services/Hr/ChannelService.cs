﻿using Config.Enums;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces.Hr;
using Config;
using Infrastructure.CommonService;
using Staffing.Model.Hr.Channel;

namespace Staffing.Core.Services.Hr;

[Service(ServiceLifetime.Transient)]
public class ChannelService : IChannelService
{
    private readonly StaffingContext _context;
    private readonly RequestContext _user;
    private readonly ConfigManager _config;
    private readonly CacheHelper _cacheHelper;
    private readonly ICommonService _commonService;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CommonDicService _commonDicService;
    private readonly WeChatHelper _weChatHelper;
    public ChannelService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, ICommonService commonService,
        LogManager log, CommonDicService commonDicService, WeChatHelper weChatHelper)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _commonService = commonService;
        _log = log;
        _commonDicService = commonDicService;
        _weChatHelper = weChatHelper;
    }

    /// <summary>
    /// 获取顾问信息
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    /// <exception cref="BadRequestException"></exception>
    public GetCounselorResponse GetCounselorInfo(SetCounselor model)
    {
        var result = new GetCounselorResponse();
        var predicate = PredicateBuilder.New<Qd_Hr_Channel>(x => x.HrId == _user.Id);
        if (model.Type.HasValue)
        {
            predicate = predicate.And(x => x.Type == model.Type);
        }
        var sql = _context.Qd_Hr_Channel.Where(predicate);
        result.Total = sql.Count();
        result.HrId = _user.Id;
        result.ChannelNum = sql.Count();
        result.Rows = sql
            .OrderByDescending(o => o.IsDefaultHr)
            .ThenByDescending(o => o.Type == model.Type)
            .ThenByDescending(o => o.CreatedTime)
            .ThenBy(x => x.Id)
            // .Skip((model.PageIndex - 1) * model.PageSize)
            .Take(1000)//model.PageSize
            .Select(s => new CounselorChannel
            {
                Id = s.Id,
                HrId = s.HrId,
                ChannelUserId = s.User_Seeker.UserId,
                Name = s.User_Seeker.NickName,
                Avatar = s.User_Seeker.Avatar,
                Describe = s.Describe,
                Mobile = s.User_Seeker.User.Mobile,
                CreatedTime = s.CreatedTime,
                EndTime = s.EndTime,
                GroupQrCode = s.GroupQrCode,
                Type = s.Type

            }).ToList();

        return result;

    }

    /// <summary>
    /// 搜索求职者
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public SearchChannelNameResponse SearchChannel(SearchChannelName model)
    {
        if (String.IsNullOrEmpty(model.SearchText))
        {
            throw new BadRequestException("搜索内容为空");
        }
        var result = new SearchChannelNameResponse();
        var predicate = PredicateBuilder.New<User_Seeker>();

        if (!string.IsNullOrWhiteSpace(model.SearchText))
            predicate = predicate.And(x => x.User.Mobile == model.SearchText);

        var sql = _context.User_Seeker.Where(predicate);

        result = sql
            .Select(s => new SearchChannelNameResponse
            {
                SeekerId = s.UserId,
                // HrId = s.HrId,
                ChannelUserId = s.UserId,
                Name = s.NickName,
                Avatar = s.Avatar,
                Mobile = s.User.Mobile,
                CreatedTime = s.CreatedTime
                // EndTime = s.EndTime,
                // GroupQrCode = s.GroupQrCode
            }).FirstOrDefault();

        if (result == null)
        {
            throw new BadRequestException("搜索用户不存在");
        }

        var channel = _context.Qd_Hr_Channel.Where(x => x.HrId == _user.Id && x.ChannelUserId == result.SeekerId)
            .Select(s => new
            {
                s.HrId,
                s.EndTime,
                s.GroupQrCode,
                s.Type,
                s.Describe
            }).FirstOrDefault();

        result.EndTime = channel?.EndTime;
        result.GroupQrCode = channel?.GroupQrCode;
        result.Type = channel?.Type;
        result.Describe = channel?.Describe;

        return result;
    }

    /// <summary>
    /// 修改渠道相关信息
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    /// <exception cref="BadRequestException"></exception>
    public EmptyResponse UpdateChannelInfo(CounselorChannel model)
    {

        Qd_Hr_Channel? result = null;

        if (string.IsNullOrWhiteSpace(model.ChannelUserId))
            throw new BadRequestException("缺少渠道Id");

        if (!model.Type.HasValue)
        {
            throw new BadRequestException("缺少type");
        }

        if (!string.IsNullOrWhiteSpace(model.Id))
        {
            result = _context.Qd_Hr_Channel.FirstOrDefault(x => x.Id == model.Id && x.HrId == _user.Id && x.ChannelUserId == model.ChannelUserId);
            if (result == null)
            {
                throw new BadRequestException("内容不存在");
            }
        }
        else
        {

            if (_context.Qd_Hr_Channel.Any(x => x.HrId == _user.Id && x.ChannelUserId == model.ChannelUserId))
            {
                throw new BadRequestException("顾问渠道已绑定关系");
            }

            result = new Qd_Hr_Channel
            {
                HrId = _user.Id,
                ChannelUserId = model.ChannelUserId,
                Status = ActiveStatus.Active
            };
            _context.Add(result);
        }

        result.Status = model.EndTime >= DateTime.Now ? ActiveStatus.Active : ActiveStatus.Inactive;
        if (!String.IsNullOrEmpty(model.Describe) && model.Describe != result.Describe)
        {
            result.Describe = model.Describe;
        }

        if (model.Type != result.Type)
        {
            result.Type = model.Type;
        }

        if (model.EndTime != result.EndTime)
        {
            result.EndTime = model.EndTime;
        }

        if (model.GroupQrCode != null)
        {
            result.GroupQrCode = model.GroupQrCode;
        }

        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 获取顾问 邀请 渠道 二维码
    /// </summary>
    /// <returns></returns>
    public async Task<GetCounselorChannelCodeResponse> GetInviteChannelCode(SetCounselorChannelCode model)
    {
        var result = new GetCounselorChannelCodeResponse();

        var appIds = _commonDicService.GetAppIds();

        var appId = appIds.Where(x => x.Type == ClientType.SeekerApplet).FirstOrDefault()?.AppId;

        if (string.IsNullOrWhiteSpace(appId))
        {
            _log.Error("顾问获取渠道二维码失败", "顾问获取渠道二维码失败", _user);
            throw new BadRequestException("获取二维码失败，请稍后再试");
        }

        var userQrcode = _context.User_Qrcode.FirstOrDefault(x => x.UserId == _user.Id
        && x.Type == UserQrcodeType.顾问渠道小程序二维码 && x.AppId == appId);

        if (!string.IsNullOrEmpty(userQrcode?.QrCode))
        {
            result.ChannelInvQrCode = userQrcode.QrCode;
            return result;
        }

        var lockerKey = $"counselorInviteCode_{_user.Id}";
        //redis分布式锁，自动释放
        using var locker = await MyRedis.Lock(lockerKey, 5);

        if (locker == null)
            throw new BadRequestException("获取二维码失败，请稍后再试");

        userQrcode = _context.User_Qrcode.FirstOrDefault(x => x.UserId == _user.Id && x.Type == UserQrcodeType.顾问渠道小程序二维码);

        if (userQrcode == null)
        {
            var url = await RetryHelper.Do(async () => await _weChatHelper.GetWxaCodeUnlimit(appId, _user.Id, "Mypackagedetail/informationacq/Gridman"));

            if (string.IsNullOrWhiteSpace(url))
                throw new BadRequestException("获取二维码失败，请稍后再试");

            userQrcode = new User_Qrcode
            {
                AppId = appId,
                Type = UserQrcodeType.顾问渠道小程序二维码,
                QrCode = url,
                UserId = _user.Id
            };
            _context.Add(userQrcode);

            _context.SaveChanges();
        }
        result.ChannelInvQrCode = userQrcode.QrCode;

        return result;
    }

}