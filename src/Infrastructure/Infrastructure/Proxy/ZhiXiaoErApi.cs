﻿using Config;
using Config.Enums;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Math;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Infrastructure.Proxy.WbMoFangApi;
using static Microsoft.Extensions.Logging.EventSource.LoggingEventSource;

namespace Infrastructure.Proxy
{
    [Service(ServiceLifetime.Transient)]
    public class ZhiXiaoErApi
    {
        private readonly ConfigManager _config;
        private readonly CacheHelper _cacheHelper;
        private readonly LogManager _log;
        private IDbContextFactory<StaffingContext> _staffingContextFactory;
        public ZhiXiaoErApi(IOptionsSnapshot<ConfigManager> config, CacheHelper cacheHelper, LogManager log, IDbContextFactory<StaffingContext> staffingContextFactory)
        {
            _config = config.Value;
            _cacheHelper = cacheHelper;
            _log = log;
            _staffingContextFactory = staffingContextFactory;
        }

        public Task<string> ZhiXiaoErLoginToken()
        {
            GetAuth auth = new GetAuth
            {
                username = "zhenghe",
                password = "Wym133hebei",
                typeid = "18",
                image = "",
                imageback = "",
            };
            var url = "http://api.ttshitu.com/predict";
            var token = url.PostJsonAsync(auth).ReceiveString();
            return token;
        }
        private static string utoken= "e78d475d2f95479743760c21557ca3a8";
        /// <summary>
        /// 获取职小二职位列表
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<List<ZhiXiaoErModel>> GetZhiXiaoErJobList(int page, int limit)
        {
            var ordertype = $"%E5%B9%B3%E5%8F%B0%E8%AE%A2%E5%8D%95";
            var url1 = $"https://dcapi.chinalao.com/api/jobs/platformorders?token={utoken}&ordertype={ordertype}&limit={limit}&page={page}";
            var url2 = $"https://dcapi.chinalao.com/api/jobs/index";
            try
            {
                #region 订单池
                var restr1 = await url1.WithHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .WithHeader("Referer", "https://dc.chinalao.com/")
                    .WithHeader("X-Requested-With", "XMLHttpRequest") // 表示请求从Ajax发出
                    .WithHeader("Host", "dcapi.chinalao.com")
                    .WithHeader("Origin", "https://dc.chinalao.com")
                    .PostUrlEncodedAsync(new
                    {
                        c = string.Empty,
                        p = "download"
                    }).ReceiveString();
                string derestr1 = System.Text.RegularExpressions.Regex.Unescape(restr1);
                var res1 = JsonSerializer.DeserializeFromString<OrderReturn>(derestr1);
                #endregion

                if (res1.result == "true")
                {
                    #region 我的订单
                    var restr2 = await url2.WithHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                        .WithHeader("Referer", "https://dc.chinalao.com/")
                        .WithHeader("X-Requested-With", "XMLHttpRequest") // 表示请求从Ajax发出
                        .WithHeader("Host", "dcapi.chinalao.com")
                        .WithHeader("Origin", "https://dc.chinalao.com")
                        .PostJsonAsync(new
                        {
                            firstid = "",
                            industrytype = "全部",
                            jobtype = "",
                            keywords = "",
                            light = "",
                            limit,
                            ordertype = "全部",
                            page = 1,
                            salary = "",
                            secondid = "",
                            sex = "",
                            status = "",
                            token = utoken
                        }).ReceiveString();
                    #endregion
                    string derestr = System.Text.RegularExpressions.Regex.Unescape(restr2);
                    var res = JsonSerializer.DeserializeFromString<OrderReturn>(derestr);
                    if (res.result == "false")
                    {
                        throw new Exception($"职小二接口调用异常：{derestr}");
                    }
                    res1.data!.data!.data!.AddRange(res.data!.data!.data!); //将两个接口数据拼接
                }
                return res1.data!.data!.data!;
            }
            catch (FlurlHttpException ex)
            {
                _log.Error("职小二岗位列表接口调用异常", ex.Message);
                var error = await ex.GetResponseStringAsync();
                throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
            }
        }

        public async Task DeletePostRelation()
        {
            try
            {
                using var _context = _staffingContextFactory.CreateDbContext();
                // 删除关联表数据
                var relation = _context.ThirdParty_Postid_Relation.Where(x => x.Source == PostSouce.职多多);
                _context.ThirdParty_Postid_Relation.RemoveRange(relation);
                _context.SaveChanges();
            }
            catch (FlurlHttpException ex)
            {
                _log.Error("职多多岗位关联表删除任务出错！", Tools.GetErrMsg(ex));
                var error = await ex.GetResponseStringAsync();
                throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
            }
        }

        /// <summary>
        /// 职小二岗位详情
        /// </summary>
        /// <param name="zhaopinid"></param>
        /// <param name="zhaopinname"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<ZhiXiaoErDetailModel> GetZhiXiaoErJobDetail(string zhaopinid, string zhaopinname)
        {
            var token = $"658c86c6960922a4b788e8228fe050b5";//不固定
            var url = $"https://dcapi.chinalao.com/api/jobs/detail?token={utoken}&zhaopinid={zhaopinid}&zhaopinname={zhaopinname}";
            try
            {
                if (utoken != "")
                {
                    var restr = await url.WithHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .WithHeader("Referer", "https://dc.chinalao.com/")
                    .WithHeader("X-Requested-With", "XMLHttpRequest") // 表示请求从Ajax发出
                    .WithHeader("Host", "dcapi.chinalao.com")
                    .WithHeader("Origin", "https://dc.chinalao.com")
                    .PostUrlEncodedAsync(new
                    {
                        c = string.Empty,
                        p = "download"
                    }).ReceiveString();
                    string derestr = System.Text.RegularExpressions.Regex.Unescape(restr);
                    var res = JsonSerializer.DeserializeFromString<OrderDetailReturn>(derestr);
                    if (res.result == "false")
                    {
                        throw new Exception($"职小二岗位详情接口调用异常：{derestr}");
                    }
                    _log.Info($"职小二岗位详情接口返回", $"参数：url:{url}", $"{derestr}");
                    return res?.data!;
                }
                else
                    return null!;
            }
            catch (FlurlHttpException ex) 
            {
                _log.Error("职小二岗位详情接口调用异常", ex.Message);
                var error = await ex.GetResponseStringAsync();
                throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
            }
        }

        /// <summary>
        /// 招聘岗位内容
        /// </summary>
        public class ZhiXiaoErModel
        {
            public required string zhaopinid { get; set; }//招聘id
            public string? companyname { get; set;}//公司名称
            public string? jobname { get; set; }//职位名称
            public string? v6_duo_type { get; set;}//
            public string? title { get; set;}//职位标题
            public string? zhaopindefect { get; set;}//招聘描述
            public string? worktype {  get; set; }//用工性质
            public string? wk_job { get; set; }//招聘岗位
            public List<string>? label { get; set; }//岗位标签
            public List<string>? zhaopinlight {  get; set; }//岗位亮点
            public int pay1 { get; set; }//最低岗位薪资
            public int pay2 { get; set; }//最高岗位薪资
            public Sex sex { get; set; }//性别 1男 2女
            public int sex1start {  get; set; }//男年龄低值
            public int sex1end { get; set; }//男年龄高值
            public int sex2start { get; set; }//女年龄低值
            public int sex2end { get; set; }//女年龄高值
            public decimal brokerage_money { get; set;}
            public string? thirdid {  get; set; }//第三方招聘id
            public string? jobthirdid { get; set;}//第三方岗位id
            public string? jobfirstid { get; set;}
            public int status { get; set;}//招聘状态
            public string? order_type {  get; set; }//招聘类型
            public string? partnerid { get; set;}//合作渠道id
            public decimal? fanfeimoney1 {  get; set; }//招聘费用 主
            public decimal? fanfeimoney2 { get; set;}//招聘费用
            public decimal? fanfeimoney3 { get; set;}//招聘费用
            public string? fanfeiunit1 { get; set;}//招聘费用单位 主
            public string? fanfeiunit2 { get; set;}//招聘费用单位
            public string? fanfeiunit3 { get; set;}//招聘费用单位
            public int fanfeicategory {  get; set;}//招聘费用类别
            public int? fanfeitype1 {  get; set;}//招聘费用类型 主
            public int? fanfeitype2 { get; set; }//招聘费用类型
            public int? fanfeitype3 { get; set; }//招聘费用类型
            public bool? is_self { get; set; }//
            public int order_options {  get; set;}//订单选项
            public int live_status { get; set;}//是否提供住宿
            public string? firstname {  get; set;}//所属省
            public string? secondname {  get; set;}//所属市
            public RecieveOrderStatus receiving_orders_status {  get; set;}//是否接单 1是 2否
        }
        public class ZhiXiaoErDetailModel
        {
            public required string zhaopinid { get; set; }//招聘id
            public string? companyid { get; set; }//公司id
            public string? companyname { get; set; }//公司名称
            public string? jobid { get; set; }//岗位id
            public string? jobname { get; set; }//岗位名称
            public string? v6_duo_type { get; set; }//
            public string? title { get; set; }//职位标题
            public string? zhaopindefect { get; set; }//招聘描述
            public string? worktype { get; set; }//用工性质
            public string? wk_job { get; set; }//招聘岗位
            public List<string>? label { get; set; }//岗位标签
            public List<string>? zhaopinlight { get; set; }//岗位亮点
            public int pay1 { get; set; }//最低岗位薪资
            public int pay2 { get; set; }//最高岗位薪资
            public Sex sex { get; set; }//性别 1男 2女
            public int? sex1start { get; set; }//男年龄低值
            public int? sex1end { get; set; }//男年龄高值
            public int? sex2start { get; set; }//女年龄低值
            public int? sex2end { get; set; }//女年龄高值
            public decimal brokerage_money { get; set; }
            public string? thirdid { get; set; }//第三方招聘id
            public string? jobthirdid { get; set; }//第三方岗位id
            public string? jobfirstid { get; set; }
            public int status { get; set; }//招聘状态
            public string? order_type { get; set; }//招聘类型
            public string? partnerid { get; set; }//合作渠道id
            public decimal? fanfeimoney1 { get; set; }//招聘费用 主
            public decimal? fanfeimoney2 { get; set; }//招聘费用
            public decimal? fanfeimoney3 { get; set; }//招聘费用
            public string? fanfeiunit1 { get; set; }//招聘费用单位 主
            public string? fanfeiunit2 { get; set; }//招聘费用单位
            public string? fanfeiunit3 { get; set; }//招聘费用单位
            public int fanfeicategory { get; set; }//招聘费用类别
            public int? fanfeitype1 { get; set; }//招聘费用类型 主
            public int? fanfeitype2 { get; set; }//招聘费用类型
            public int? fanfeitype3 { get; set; }//招聘费用类型
            public bool? is_self { get; set; }//
            public int order_options { get; set; }//订单选项
            public int live_status { get; set; }//是否提供住宿
            public string? firstname { get; set; }//所属省
            public string? secondname { get; set; }//所属市
            public string? cityid {  get; set;}//城市id
            public string? content {  get; set;}//描述内容
            public string? ext_number {  get; set;}//招聘人数
            public string? address { get; set; }//工作地址
            public double? v6_lat { get; set; }//纬度
            public double? v6_lng { get; set; }//经度
            public Ext? ext { get; set; }//企业扩展信息
            public string? gatherplace {  get; set; }//面试集合地点
            //public string? xz_welfare {  get; set; }//福利待遇 其他福利
            public string? xz_probation {  get; set; }//试用期工资
            public string? sy_contract {  get; set;}//所属用人单位/部门
            public string? wk_maincontent {  get; set; }//工作内容
            public string? xz_detail { get; set; }//工资计算方式
            public string? real_pay { get; set; }//到手工资
            public string? hy_content_source { get; set;}//企业描述
        }
        public class LabelType
        {
            public string? label { get; set; }
        }
        public class ZhaopinLight
        {
            public string? zhaopinlight { get; set; }
        }

        public class OrderReturn
        {
            public string? result { get; set; }//true or false
            public string? msg {  get; set; }//OK
            public Data? data { get; set; }//返回的结果
        }
        public class OrderDetailReturn
        {
            public string? result { get; set; }//true or false
            public string? msg { get; set; }//OK
            public ZhiXiaoErDetailModel? data { get; set; }//返回的结果
        }

        public class Data
        {
            public string? current_page { get; set;}
            public DataList? data { get; set; }
        }

        public class DataList 
        {
            public List<ZhiXiaoErModel>? data { get; set; }
        }

        public class Ext
        {
            public string? hy_scale {  get; set; } //企业规模
            public string? xz_day {  get; set; }//工作满多少天发工资
            public string? yq_edu {  get; set; }//学历、教育要求
            public string? gk_overage {  get; set; }//工作要求
            public string? xz_detail { get; set; }//工资计算方式 薪资结构
            public string? ms_iscriminalrecord { get; set; }//是否查案底
            public string? xz_issocialsecurity { get; set; }//社保
            public string? xz_iscommercialinsurance { get; set; }//商保
            public string? wk_monthrestday { get; set; }//月休天数
            public string? wk_manhour { get; set; }//工作时间
            public string? wk_mode { get; set; }//工作方式
            public string? xz_salaryday { get; set; }//发薪时间
            public string? wk_type { get; set; }//工作类型 上下班次
            public string? yq_tattoo { get; set; }//纹身烟疤
            public string? yq_plate { get; set; }//钢板钢钉
            public string? yq_deformity { get; set; }//残疾人
            public string? yq_overage { get; set; }//超龄协调
            public string? bk_isstation { get; set; }//是否接站
            public string? ms_material { get; set;}//报道资料
            public string? tj_yesorno { get; set;}//要求体检
            public string? ms_inspect {  get; set; }//体检安排
            public string? tj_isself { get; set;}//体检费用
            public string? tj_project { get; set;}//体检项目
            public string? ss_sleepover { get; set;}//是否外宿
            public string? ss_spouseroom {  get; set;}//是否提供夫妻住宿
            public string? ss_sleepoversubsidy { get; set;}//外宿补贴
            public string? ss_stay { get; set;}//是否提供住宿
            public string? ss_room { get; set; }//宿舍标准
            public string? xz_welfare { get; set;}//福利
        }

        public class GetAuth 
        {
            public string? username { get; set; }
            public string? password { get; set; }
            public string? typeid { get; set; }
            public string? image { get; set; }
            public string? imageback { get; set; }
        }
        public enum Sex
        {
            [Description("男女不限")]
            All = 0,
            [Description("男")]
            Man = 1,
            [Description("女")]
            Woman = 2
        }
        public enum RecieveOrderStatus
        {
            [Description("接单")]
            ReceiveOrder = 1,
            [Description("不接单")]
            NotReceiveOrder = 2
        }
    }
}
