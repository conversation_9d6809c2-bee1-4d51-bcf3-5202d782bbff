﻿using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Core.Interfaces.CentreService;
using Config;
using Staffing.Model.CentreService.Transit;
using Staffing.Entity.Staffing;
using Infrastructure.Exceptions;
using Infrastructure.CommonService;
using Config.CommonModel;
using Microsoft.EntityFrameworkCore;
using Config.CommonModel.Business;
using Config.CommonModel.TalentResume;
using Staffing.Model.CentreService.Report;
using ServiceStack.Text;
using Config.CommonModel.DataScreen;

namespace Staffing.Core.Services.CentreService;

[Service(ServiceLifetime.Transient)]
public class TransitService : ITransitService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CommonUserService _commonUserService;
    private readonly CommonDicService _commonDicService;
    private readonly CommonDashboardService _commonDashboardService;

    private readonly Staffing.Entity.Noah.NoahContext _noahContext;
    public TransitService(StaffingContext context, RequestContext user, CommonDicService commonDicService,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment, CommonDashboardService commonDashboardService,
        CacheHelper cacheHelper, LogManager log, CommonUserService commonUserService,
        Entity.Noah.NoahContext noahContext)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _log = log;
        _commonUserService = commonUserService;
        _commonDicService = commonDicService;
        _commonDashboardService = commonDashboardService;
        _noahContext = noahContext;
    }

    public GetHrsResponse GetHrs(GetHrs model)
    {
        var result = new GetHrsResponse();

        //如果登录
        var predicate = PredicateBuilder.New<User_Hr>(x => x.Status == UserStatus.Active);

        if (!string.IsNullOrWhiteSpace(model.Name))
            predicate = predicate.And(x => x.NickName.Contains(model.Name));

        if (!string.IsNullOrWhiteSpace(model.Mobile))
            predicate = predicate.And(x => x.User.Mobile == model.Name);

        if (!string.IsNullOrWhiteSpace(model.Search))
            predicate = predicate.And(x => x.NickName.Contains(model.Search) || x.User.Mobile.Contains(model.Search));

        if (model.AdviserIds?.Count > 0)
            predicate = predicate.And(x => model.AdviserIds.Contains(x.UserId));

        if (!string.IsNullOrWhiteSpace(model.Power))
        {
            var phrs = _context.User_Hr.Where(x => x.Status == UserStatus.Active).Select(s => new { s.UserId, s.Powers }).ToList();
            var ids = phrs.Where(x => x.Powers?.Contains(model.Power) == true).Select(s => s.UserId).Take(1000).ToList();
            predicate = predicate.And(x => ids.Contains(x.UserId));
        }

        var sql = _context.User_Hr.Where(predicate);
        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(o => o.User_Num.Talent)
        .ThenBy(x => x.UserId)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetHrsDetail
        {
            Avatar = s.Avatar,
            EntName = s.Enterprise.Name,
            Name = s.NickName,
            Talent = s.User_Num.Talent,
            VirtualTalent = s.User_Num.VirtualTalent,
            AdviserId = s.UserId,
            LoginTime = s.User_Extend.LoginTime,
            EntAbbr = string.IsNullOrWhiteSpace(s.Enterprise.Abbreviation) ? s.Enterprise.Name : s.Enterprise.Abbreviation,
            Mobile = s.User.Mobile,
            Post = s.Post,
            EntWeChatQrCode = s.EntWeChatQrCode,
            AppletQrCode = s.AppletQrCode,
            Tags = s.Tags
        }).ToList();

        foreach (var item in result.Rows)
        {
            item.OnlineStatus = Tools.GetOnlineStatus(item.LoginTime);
            if (!string.IsNullOrEmpty(item.Avatar))
            {
                item.Avatar = item.Avatar.Split('?')[0];
                item.Avatar = $"{item.Avatar}?x-oss-process=style/600x600";
            }
        }

        return result;
    }

    public async Task<SubmitResumeResponse> SubmitResume(SubmitResume model)
    {
        var result = new SubmitResumeResponse();

        if (string.IsNullOrWhiteSpace(model.Mobile))
            throw new BadRequestException("缺少电话");

        if (string.IsNullOrWhiteSpace(model.Name))
            throw new BadRequestException("缺少姓名");

        if (!model.Sex.HasValue)
            throw new BadRequestException("缺少性别");

        if (!model.Birthday.HasValue)
            throw new BadRequestException("缺少生日");

        if (!model.Education.HasValue)
            throw new BadRequestException("缺少教育");

        var lockerKey = $"st:submitres:{model.Mobile}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var recruit = _context.Recruit.Where(x => x.Post_Delivery.TeamPostId == model.TeamPostId
        && x.User_Seeker.User.Mobile == model.Mobile && x.Status != RecruitStatus.FileAway)
        .Select(s => new { s.DeliveryId }).FirstOrDefault();

        if (recruit != null)
        {
            result.DeliveryId = recruit.DeliveryId;
            return result;
        }

        var userId = string.Empty;
        var seeker = _context.User_Seeker.Include(i => i.User_Resume).FirstOrDefault(f => f.User.Mobile == model.Mobile);
        userId = seeker?.UserId;
        if (seeker == null)
        {
            var teamPost = _context.Post_Team.Where(x => x.TeamPostId == model.TeamPostId).Select(s => new
            {
                TeamHrId = s.Project_Team.HrId
            }).First();

            var createSeekerRes = _commonUserService.CreateSeeker(new CreateSeekerModel
            {
                Birthday = model.Birthday,
                Education = model.Education,
                Mobile = model.Mobile,
                Name = model.Name,
                Sex = model.Sex,
                Source = RegisterSource.NuoPin,
                GraduationDate = model.GraduationDate,
                ChannelId = model.ChannelId,
                AdviserId = teamPost.TeamHrId,
                ChannelSource = model.ChannelSource
            });

            userId = createSeekerRes.UserId;
        }
        else
        {
            if (model.Birthday.HasValue)
                seeker.User_Resume.Birthday = model.Birthday;

            if (model.Education.HasValue)
                seeker.User_Resume.Education = model.Education;

            if (model.Sex.HasValue)
                seeker.User_Resume.Sex = model.Sex;

            if (!string.IsNullOrWhiteSpace(model.Name) && string.IsNullOrWhiteSpace(seeker.NickName))
                seeker.NickName = model.Name;

            if (model.GraduationDate.HasValue)
                seeker.User_Resume.GraduationDate = model.GraduationDate;

            _context.SaveChanges();
        }

        await Task.Delay(10);

        //投递
        var deliverResponse = _commonUserService.DeliverResume(new CommonDeliverResume
        {
            TeamPostId = model.TeamPostId,
            UserId = userId,
            UserClient = model.UserClient ?? Config.Enums.ClientType.NuoPin,
            ChannelId = model.ChannelId
        });

        result.DeliveryId = deliverResponse.DeliveryId;

        return result;
    }

    public RecommendEntResponse RecommendEnt(RecommendEnt model)
    {
        var lockerKey = $"st:recommendent:{model.Mobile}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        if (string.IsNullOrEmpty(model.AdviserId))
            throw new BadRequestException("缺少AdviserId");

        if (!model.Source.HasValue)
            throw new BadRequestException("缺少Source");

        if (string.IsNullOrEmpty(model.TEntId) &&
            model.Source == HrRecommendEntSource.诺聘平台)
            throw new BadRequestException("缺少TEntId");

        if (string.IsNullOrEmpty(model.EntName))
            throw new BadRequestException("缺少EntName");

        if (string.IsNullOrEmpty(model.HrName))
            throw new BadRequestException("缺少HrName");

        if (string.IsNullOrEmpty(model.Mobile))
            throw new BadRequestException("缺少Mobile");

        var result = new RecommendEntResponse();

        var oldModel = _context.User_Hr_RecommendEnt.FirstOrDefault(x => x.EntName == model.EntName && x.Mobile == model.Mobile && x.Source == model.Source);
        if (oldModel != null)
        {
            if (model.IsCover)
            {
                result.Id = oldModel.Id;
                //if (oldModel.AdviserId == model.AdviserId)
                //{
                if (!string.IsNullOrEmpty(model.AdviserId))
                    oldModel.AdviserId = model.AdviserId;

                if (!string.IsNullOrEmpty(model.HrName))
                    oldModel.HrName = model.HrName;

                if (!string.IsNullOrEmpty(model.Post))
                    oldModel.Post = model.Post;

                if (!string.IsNullOrEmpty(model.Remark))
                    oldModel.Remark = model.Remark;

                if (!string.IsNullOrEmpty(model.OutletId))
                    oldModel.OutletId = model.OutletId;

                if (!string.IsNullOrEmpty(model.OutletName))
                    oldModel.OutletName = model.OutletName;

                _context.SaveChanges();
                //}
            }
            else
                throw new BadRequestException("该电话+企业名称已经存在");
        }
        else
        {
            oldModel = new User_Hr_RecommendEnt
            {
                AdviserId = model.AdviserId,
                Source = model.Source.Value,
                TEntId = model.TEntId ?? string.Empty,
                EntName = model.EntName,
                HrName = model.HrName,
                Mobile = model.Mobile,
                Remark = model.Remark,
                Post = model.Post ?? string.Empty,
                OutletId = model.OutletId,
                OutletName = model.OutletName
            };
            _context.Add(oldModel);
            _context.SaveChanges();

            result.Id = oldModel.Id;
        }

        //同步企业线索至【致趣百川】
        MyRedis.Client.SAdd(SubscriptionKey.SyncToSCRMBeschannel, oldModel.Id);

        //发送钉钉通知
        var entMd5 = Md5Helper.Md5($"{model.EntName}_{model.AdviserId}");
        if (MyRedis.Client.HSetNx(RedisKey.Idempotency.QixsTzHr, entMd5, 1))
        {
            MsgHelper.SendDingdingNotify(new MsgNotifyModel[] {
            new MsgNotifyModel
            {
                Type = MsgNotifyType.企业线索池分配企业提醒,
                EventTime = DateTime.Now,
                UserId = model.AdviserId,
                Data = new MsgNotifyRecommendEntAlloc { EntName =  model.EntName, HrName = model.HrName, HrMobile = model.Mobile, OutletName = model.OutletName }
            }});
        }
        return result;
    }

    public EmptyResponse UpdateRecommendEnt(UpdateRecommendEnt model)
    {
        var lockerKey = $"st:recommendent:{model.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var re = _context.User_Hr_RecommendEnt.FirstOrDefault(x => x.Id == model.Id);

        if (re == null)
            throw new BadRequestException("内容不存在");

        var result = new EmptyResponse();

        if (!string.IsNullOrEmpty(model.AdviserId))
            re.AdviserId = model.AdviserId;

        if (!string.IsNullOrEmpty(model.Remark))
            re.Remark = model.Remark;

        if (!string.IsNullOrEmpty(model.OutletId))
            re.OutletId = model.OutletId;

        if (!string.IsNullOrEmpty(model.OutletName))
            re.OutletName = model.OutletName;

        if (model.Source.HasValue)
        {
            re.Source = model.Source.Value;
            if (re.Source != HrRecommendEntSource.诺聘网点)
            {
                re.OutletId = null;
                re.OutletName = null;
            }
        }

        _context.SaveChanges();
        //同步企业线索至【致趣百川】
        MyRedis.Client.SAdd(SubscriptionKey.SyncToSCRMBeschannel, re.Id);
        return result;
    }

    public void SyncRecommendEntToSCRMForSH(string? OutletId)
    {
        int PageIndex = 1, PageSize = 1000, DataCount = 0;
        do
        {
            var ids = _context.User_Hr_RecommendEnt.Where(x => x.Source == HrRecommendEntSource.网点提交线索 && (string.IsNullOrEmpty(OutletId) || x.OutletId == OutletId)).Select(x => x.Id).Skip((PageIndex - 1) * PageSize).Take(PageSize).ToArray();
            DataCount = ids.Length;
            if (DataCount > 0)
            {
                //同步企业线索至【致趣百川】
                MyRedis.Client.SAdd(SubscriptionKey.SyncToSCRMBeschannel, ids);
            }
            PageIndex++;
        } while (DataCount == PageSize);
    }

    public GetPostsByIdsResponse GetPostsByIds(GetPostsByIds model)
    {
        var result = new GetPostsByIdsResponse();

        result.Rows = _context.Post_Team.Where(x => model.TeamPostIds.Contains(x.TeamPostId))
        .Select(s => new GetPostsByIdsInfo
        {
            AgentEnt = new GetAgentEntDetail
            {
                Display = s.Post.Agent_Ent.Display,
                AgentEntId = s.Post.Project.AgentEntId,
                Scale = s.Post.Agent_Ent.Scale,
                ScaleName = s.Post.Agent_Ent.Scale.GetDescription(),
                LogoUrl = s.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post.Agent_Ent.LogoUrl : string.Empty,
                DisplayName = s.Post.Agent_Ent.DisplayName,
                Name = s.Post.Agent_Ent.Name,
                Abbr = s.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post.Agent_Ent.Abbr : s.Post.Agent_Ent.DisplayName,
                Nature = s.Post.Agent_Ent.Nature,
                NatureName = s.Post.Agent_Ent.Nature.GetDescription(),
                Industry = s.Post.Agent_Ent.Industry.ToString(),
                Capital = s.Post.Agent_Ent.Capital,
                RegionId = s.Post.Agent_Ent.RegionId,
                Address = s.Post.Agent_Ent.Address,
                Lat = s.Post.Agent_Ent.Location.X,
                Lng = s.Post.Agent_Ent.Location.Y,
                IndustryName = s.Post.Agent_Ent.Dic_Industry.Name
            },
            Hr = new HrModel
            {
                AppletQrCode = s.Project_Team.HrId == Constants.PlatformHrId ? s.Post.Project.User_Hr.AppletQrCode : s.Project_Team.User_Hr.AppletQrCode,
                HrId = s.Project_Team.HrId == Constants.PlatformHrId ? s.Post.Project.HrId : s.Project_Team.HrId,
                NuoId = s.Project_Team.HrId == Constants.PlatformHrId ? s.Post.Project.User_Hr.NuoId : s.Project_Team.User_Hr.NuoId,
                HrName = s.Project_Team.HrId == Constants.PlatformHrId ? s.Post.Project.User_Hr.NickName : s.Project_Team.User_Hr.NickName,
                Avatar = s.Project_Team.HrId == Constants.PlatformHrId ? s.Post.Project.User_Hr.Avatar : s.Project_Team.User_Hr.Avatar,
                Post = s.Project_Team.HrId == Constants.PlatformHrId ? s.Post.Project.User_Hr.Post : s.Project_Team.User_Hr.Post,
                LoginTime = s.Project_Team.HrId == Constants.PlatformHrId ? s.Post.Project.User_Hr.User_Extend.LoginTime : s.Project_Team.User_Hr.User_Extend.LoginTime,
                EntName = s.Project_Team.HrId == Constants.PlatformHrId ? s.Post.Project.User_Hr.Enterprise.Name : s.Project_Team.User_Hr.Enterprise.Name,
                EntAbbr = s.Project_Team.HrId == Constants.PlatformHrId ? s.Post.Project.User_Hr.Enterprise.Abbreviation : s.Project_Team.User_Hr.Enterprise.Abbreviation,
                Mobile = s.Project_Team.HrId == Constants.PlatformHrId ? s.Post.Project.User_Hr.User.Mobile : s.Project_Team.User_Hr.User.Mobile,
                TencentImId = s.Project_Team.HrId == Constants.PlatformHrId ? s.Post.Project.User_Hr.TencentImId : s.Project_Team.User_Hr.TencentImId,
                Identity = !string.IsNullOrEmpty(s.Project_Team.Project.User_Hr.User.IdentityCard),
            },
            Show = s.Show,
            TeamPostId = s.TeamPostId,
            ProjectId = s.Post.ProjectId,
            Name = s.Post.Name,
            Status = s.Post.Status,
            TieType = s.Post.TieType,
            Describe = s.Post.Describe,
            PaymentNode = s.Post.PaymentNode,
            PaymentDays = s.Post.PaymentDays,
            ProjectType = s.Post.Project.Type,
            Address = s.Post.Address,
            Category = s.Post.Category,
            CategoryName = s.Post.Dic_Post.Name,
            CreatedTime = s.CreatedTime,
            UpdatedTime = s.UpdatedTime,
            Department = s.Post.Department,
            Education = s.Post.Education,
            EducationName = s.Post.Education.GetDescription(),
            Highlights = s.Post.Highlights,
            Lat = s.Post.Location.X,
            Lng = s.Post.Location.Y,
            MaxSalary = s.Post.MaxSalary,
            MinSalary = s.Post.MinSalary,
            Money = s.Post.Money,
            PaymentType = s.Post.Project.PaymentType,
            PostId = s.PostId,
            RecruitNumber = s.Post.DeliveryNumber,
            RegionId = s.Post.RegionId,
            Salary = s.Post.Salary,
            SettlementType = s.Post.SettlementType,
            WorkingDays = s.Post.WorkingDays,
            WorkingHours = s.Post.WorkingHours,
            MinMonths = s.Post.MinMonths,
            DaysPerWeek = s.Post.DaysPerWeek,
            GraduationYear = s.Post.GraduationYear,
            Tags = s.Post.Tags,
            Welfare = s.Post.Welfare,
            WelfareCustom = s.Post.WelfareCustom,
            WorkNature = s.Post.WorkNature,
            SalaryType = s.Post.SalaryType,
            SalaryTypeName = s.Post.SalaryType.GetDescription(),
            WorkNatureName = s.Post.WorkNature.GetDescription(),
            Sex = s.Post.Sex,
            MinAge = s.Post.MinAge,
            MaxAge = s.Post.MaxAge,
            AppletQrCode = s.AppletQrCode,
            RegistrationNum = s.Post.Post_Extend.DeliveryNum,
            LocationMap = s.Post.LocationMap,
            TeamProjectId = s.TeamProjectId,
            Top = s.TopTime > Config.Constants.DefaultTime
        }).ToList();

        foreach (var item in result.Rows)
        {
            item.PostNo = $"{Constants.PostIdPre}{item.PostAutoId.ToString()!.PadLeft(2, '0')}";

            item.PaymentNodeName = item.PaymentNode?.GetDescription();
            item.PaymentTypeName = item.PaymentType?.GetDescription();
            item.ProjectTypeName = item.ProjectType?.GetDescription();

            item.SalaryName = Tools.FormattingSalary(item.MinSalary, item.MaxSalary, item.SalaryType, item.WorkNature, item.Salary);

            if (item.Welfare?.Count > 0)
            {
                var welfare = _commonDicService.GetWelfare();
                item.Welfare = welfare.Where(x => item.Welfare.Any(a => a.Id == x.Id)).ToList();
            }

            if (!string.IsNullOrWhiteSpace(item.RegionId))
                item.Region = _commonDicService.GetCityById(item.RegionId);


            if (!(item.Show == true))
                item.Status = PostStatus.关闭;

            item.StatusName = item.Status.GetDescription();

            item.Hr!.OnlineStatus = Tools.GetOnlineStatus(item.Hr.LoginTime!.Value);
        }

        return result;
    }

    public EmptyResponse PostNotify(PostNotify model)
    {
        var result = new EmptyResponse();

        var hrId = _context.User_Hr_RecommendEnt.Where(x => x.TEntId == model.EntId && x.Source == HrRecommendEntSource.诺聘平台)
        .Select(s => s.AdviserId).FirstOrDefault();

        if (string.IsNullOrWhiteSpace(hrId))
            return result;

        var msg = new MsgNotifyModel
        {
            Type = MsgNotifyType.诺聘发布职位,
            EventTime = Convert.ToDateTime(model.CreateTime),
            UserId = hrId,
            Data = new MsgNotifyNpCreatePost
            {
                EntId = model.EntId,
                EntName = model.EntName,
                PostId = model.PostId,
                PostName = model.PostName
            }
        };

        MyRedis.Client.RPush(SubscriptionKey.MsgDingDingNotify, msg);

        return result;
    }

    public EmptyResponse ResumeNotify(ResumeNotify model)
    {
        var result = new EmptyResponse();

        var hrId = _context.User_Hr_RecommendEnt.Where(x => x.TEntId == model.EntId && x.Source == HrRecommendEntSource.诺聘平台)
        .Select(s => s.AdviserId).FirstOrDefault();

        if (string.IsNullOrWhiteSpace(hrId))
            return result;

        var msg = new MsgNotifyModel
        {
            Type = MsgNotifyType.诺聘投递简历,
            EventTime = Convert.ToDateTime(model.CreateTime),
            UserId = hrId,
            Data = new MsgNotifyNpDeliver
            {
                EntId = model.EntId,
                EntName = model.EntName,
                PostName = model.PostName,
                DeliverId = model.DeliverId,
                UserMobile = model.UserMobile,
                UserName = model.UserName
            }
        };

        MyRedis.Client.RPush(SubscriptionKey.MsgDingDingNotify, msg);

        return result;
    }

    public GetXqdpNkpResponse GetXqdpNkp(GetXqdpNkp model)
    {
        var result = new GetXqdpNkpResponse
        {
            Seeker = _context.User_Seeker.Count(),
            ProjectTotal = _context.Project.Count(),
            PostOnline = _context.Post.Where(x => x.Status == PostStatus.发布中).Count()
        };

        if (model?.NewCityHrIds?.Count > 0)
        {
            result.NewCitySeeker = _context.Talent_Platform.Where(x => model.NewCityHrIds.Contains(x.HrId)).Count();
        }
        else
        {
            //查询某部门人才数量
            var ddDeptIds = new List<long> { 606284803, 822186182 };
            var ddOrgLevel = _context.Dd_Dept.Where(x => ddDeptIds.Contains(x.DdDeptId))
            .Select(s => s.Level).ToList();

            var predicate = PredicateBuilder.New<Dd_User_Dept>(x => false);
            foreach (var item in ddOrgLevel)
            {
                predicate = predicate.Or(x => x.Dd_Dept.Level.StartsWith(item));
            }
            var hrIds = _context.Dd_User_Dept.Where(predicate).Select(s => s.Dd_User.User.UserId).Distinct().Where(x => x != null).ToList();
            foreach (var item in hrIds.Chunk(20))
            {
                result.NewCitySeeker += _context.Talent_Platform.Count(x => item.Contains(x.HrId));
            }

            //高松干提出，临时加人数补充离职员工导致数量减少问题
            result.NewCitySeeker += 777;
        }

        result.ProjectCity = _context.Project_Region.GroupBy(g => g.RegionId)
            .Select(s => new GetXqdpNkpCity
            {
                CityId = s.Key,
                Num = s.Count()
            }).ToList();

        result.PostOnlineCity = _context.Post.Where(x => x.Status == PostStatus.发布中).GroupBy(g => g.RegionId)
            .Select(s => new GetXqdpNkpCity
            {
                CityId = s.Key,
                Num = s.Count()
            }).ToList();

        result.PostOnlineCity = result.PostOnlineCity.Where(x => x.CityId != null && x.CityId.Length >= 4).ToList();
        result.PostOnlineCity = result.PostOnlineCity.GroupBy(g => g.CityId.Substring(0, 4))
        .Select(s => new GetXqdpNkpCity
        {
            CityId = s.Key,
            Num = s.Sum(c => c.Num)
        }).ToList();

        result.SeekerCity = _context.User_Seeker.GroupBy(g => g.RegionId)
            .Select(s => new GetXqdpNkpCity
            {
                CityId = s.Key,
                Num = s.Count()
            }).ToList();

        result.SeekerCity = result.SeekerCity.Where(x => x.CityId != null && x.CityId.Length >= 4).ToList();
        result.SeekerCity = result.SeekerCity.GroupBy(g => g.CityId.Substring(0, 4))
        .Select(s => new GetXqdpNkpCity
        {
            CityId = s.Key,
            Num = s.Sum(c => c.Num)
        }).ToList();

        return result;
    }

    public GetWeChatH5Response GetWeChatH5(GetWeChatH5 model)
    {
        var result = new GetWeChatH5Response
        {
            Rows = _context.WeChat.Where(x => model.UnionIds.Contains(x.WeChatId))
            .Select(s => new GetWeChatH5Info
            {
                CreatedTime = s.CreatedTime,
                UpdatedTime = s.UpdatedTime,
                WeChatH5OpenId = s.WeChatH5OpenId,
                WeChatH5OpenId2 = s.WeChatH5OpenId2,
                WeChatH5Subscribe = s.WeChatH5Subscribe,
                WeChatH5Subscribe2 = s.WeChatH5Subscribe2,
                WeChatId = s.WeChatId
            }).ToList()
        };

        return result;
    }

    public GetHrYscResponse GetHrYsc(GetHrYsc model)
    {
        var result = new GetHrYscResponse();

        if (string.IsNullOrEmpty(model.PK_EAID))
            return result;

        var nkp = _context.User_Hr.Where(x => x.NuoId == model.PK_EAID).Select(s => new
        {
            s.Status,
            s.Enterprise.Name
        }).FirstOrDefault();

        if (nkp == null)
            return result;

        if (nkp.Name.Contains("诺亚") || nkp.Name.Contains("诺聘") || nkp.Status == UserStatus.Active || nkp.Status == UserStatus.Ignore)
            result.Ysc = true;

        return result;
    }

    public EmptyResponse NykResumeNotify(List<NykTalentResume> model)
    {
        if (model?.Count > 0)
            MyRedis.Client.SAdd(SubscriptionKey.Nyk.NykResumeNotify, model.ToArray());

        return new EmptyResponse();
    }

    public EmptyResponse NykUserLoginNotify(List<NykTalentUpdateLoginTime> model)
    {
        MyRedis.DelayQueue(SubscriptionKey.Nyk.NykUserLoginNotify,
        model.Select(s => JsonSerializer.SerializeToString(s)).ToArray(), 3600 * 24 * 7);

        return new EmptyResponse();
    }

    public GetTopologyResponse GetTopology(TopologyDateRequest model)
    {
        (model.StartTime, model.EndTime) = Tools.AdjustTimeRange(model.StartTime, model.EndTime, new DateTime(2022, 1, 1));
        var cacheKey = $"topology:dash_{model.StartTime:yyyyMM}-{model.EndTime:yyyyMM}";
        var result = _cacheHelper.GetRedisCache(() =>
        {
            var cache = MyRedis.Client.HGet<GetTopologyResponse?>(RedisKey.DataTopology.DashBoradKey, Tools.DateRangeToString(model.StartTime, model.EndTime));
            if (cache == null)
                cache = _commonDashboardService.GetTopology(model.StartTime, model.EndTime);

            return cache;
        }, cacheKey, 1800);

        return result;
    }

    public GetTopologyTalentResponse TopologyTalent(TopologyDateRequest model)
    {
        (model.StartTime, model.EndTime) = Tools.AdjustTimeRange(model.StartTime, model.EndTime, new DateTime(2022, 1, 1));
        var cacheKey = $"topology:talent_{model.StartTime:yyyyMM}-{model.EndTime:yyyyMM}";
        var result = _cacheHelper.GetRedisCache(() =>
        {
            var cache = MyRedis.Client.HGet<GetTopologyTalentResponse?>(RedisKey.DataTopology.TopologyTalent, Tools.DateRangeToString(model.StartTime, model.EndTime));
            if (cache == null)
                cache = _commonDashboardService.GetTopologyTalent(model.StartTime, model.EndTime);

            return cache;
        }, cacheKey, 1800);

        return result;
    }

    public GetTopologyProjectResponse TopologyProject(TopologyDateRequest model)
    {
        (model.StartTime, model.EndTime) = Tools.AdjustTimeRange(model.StartTime, model.EndTime, new DateTime(2022, 1, 1));
        var cacheKey = $"topology:proj_{model.StartTime:yyyyMM}-{model.EndTime:yyyyMM}";
        var result = _cacheHelper.GetRedisCache(() =>
        {
            var cache = MyRedis.Client.HGet<GetTopologyProjectResponse?>(RedisKey.DataTopology.TopologyProject, Tools.DateRangeToString(model.StartTime, model.EndTime));
            if (cache == null)
                cache = _commonDashboardService.GetTopologyProject(model.StartTime, model.EndTime);

            return cache;
        }, cacheKey, 1800);

        return result;
    }

    /// <summary>
    /// 根据钉钉部门名称获取钉钉部门下的所有员工姓名及工号
    /// </summary>
    /// <param name="DepartName"></param>
    /// <returns></returns>
    public List<DataScreenExpansion_DDUser> GetDDUserByDepartName(string DepartName)
    {
        string sql = $@"SELECT u.`Name`,u.JobNumber FROM
                        (
	                        SELECT DdDeptId FROM dd_dept 
	                        WHERE `Level` LIKE (
		                        SELECT CONCAT(`Level`,'%')
		                        FROM dd_dept AS D
		                        WHERE `Name`='{DepartName}'
	                        )
                        )as d
                        JOIN dd_user_dept ud on ud.DdDeptId=d.DdDeptId
                        join dd_user u on u.DdUserId=ud.DdUserId;";
        var data = _context.SqlQueryDynamic(sql)
            .Select(x => new DataScreenExpansion_DDUser { Name = x.Name, JobNumber = x.JobNumber })
            .ToList();
        return data;
    }

    #region 数字诺亚查询经营损益明细表【为高松干--数据拓扑需求开发】
    /// <summary>
    /// 获取招聘会合同金额
    /// </summary>
    /// <param name="Sponsors">限制的招聘会主办/协办单位名称列表，限制企业名称列表最多1024条</param>
    /// <param name="SABeginTime">限制合同创建时间，null时不限制</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public decimal Noah_GetContractAmountBySponsors(List<GetContractAmountBySponsorsRequest> Sponsors)
    {
        string sql = $@"SELECT SUM(d.expense_amount) as ContractAmount
                        FROM ehrx_management.a_operating_detail AS d 
                        JOIN ({string.Join(@"
                                UNION ", Sponsors.Select(x => $"SELECT '%{x.Sponsors}%' AS ProName,'{x.FirstTime.ToString("yyyy-MM-dd HH:mm:ss")}' AS SATime"))}) AS p ON d.billing_time>=p.SATime and d.project_name like p.ProName
                        where d.del_flag=0 and
		                        (d.product_name='活动承办' or d.product_name='雇主品牌' or d.product_name='招聘会服务' or d.product_name='招聘活动承办')
                                    ";
        var ContractAmount = _noahContext.SqlQueryDynamic(sql).FirstOrDefault();
        if (ContractAmount != null)
        {
            Newtonsoft.Json.Linq.JToken jt = Newtonsoft.Json.Linq.JToken.Parse(Newtonsoft.Json.JsonConvert.SerializeObject(ContractAmount));
            return jt.Value<decimal?>("ContractAmount") ?? 0;
        }
        else
            return 0;
    }
    /// <summary>
    /// 获取合同金额
    /// </summary>
    /// <param name="EntName">限制的企业名称列表，限制企业名称列表最多1024条</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public decimal Noah_GetContractAmountByEnt(List<string> EntName)
    {
        string sql = $@"SELECT SUM(D.expense_amount) AS ContractAmount
                                    FROM ehrx_management.a_operating_detail AS d
                                    JOIN ehrx_management.v_customer AS c ON d.customer_id=c.customer_id AND d.del_flag=0 AND c.del_flag=0
                                    JOIN ({string.Join(@"
                                                         UNION ", EntName.Select(x => $"SELECT '{x}' AS EntName"))}) AS EN ON C.`name`=EN.EntName";
        var ContractAmount = _noahContext.SqlQueryDynamic(sql).FirstOrDefault();
        if (ContractAmount != null && ContractAmount.ContractAmount is decimal)
            return ContractAmount.ContractAmount;
        else
            return 0;
    }
    /// <summary>
    /// 获取合同数量
    /// </summary>
    /// <param name="EntName">限制的企业名称列表，限制企业名称列表最多1024条</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public long Noah_GetContractCountByEnt(List<string> EntName)
    {
        string sql = $@"SELECT COUNT(c.customer_id) AS ContractCount
                                    FROM ehrx_management.a_operating_detail AS d
                                    JOIN ehrx_management.v_customer AS c ON d.customer_id=c.customer_id AND d.del_flag=0 AND c.del_flag=0
                                    JOIN ({string.Join(@"
                                                         UNION ", EntName.Select(x => $"SELECT '{x}' AS EntName"))}) AS EN ON C.`name`=EN.EntName";
        var ContractAmount = _noahContext.SqlQueryDynamic(sql).FirstOrDefault();
        if (ContractAmount != null && ContractAmount.ContractCount is long)
            return ContractAmount.ContractCount;
        else
            return 0;
    }
    /// <summary>
    /// 获取有合同的企业数量
    /// </summary>
    /// <param name="EntName">限制的企业名称列表，限制企业名称列表最多1024条</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public long Noah_GetHaveContractEntCount(List<string> EntName)
    {
        string sql = $@"SELECT COUNT(DISTINCT c.customer_id) AS ContractCount
                                    FROM ehrx_management.a_operating_detail AS d
                                    JOIN ehrx_management.v_customer AS c ON d.customer_id=c.customer_id AND d.del_flag=0 AND c.del_flag=0
                                    JOIN ({string.Join(@"
                                                         UNION ", EntName.Select(x => $"SELECT '{x}' AS EntName"))}) AS EN ON C.`name`=EN.EntName";
        var ContractAmount = _noahContext.SqlQueryDynamic(sql).FirstOrDefault();
        if (ContractAmount != null && ContractAmount.ContractCount is long)
            return ContractAmount.ContractCount;
        else
            return 0;
    }
    /// <summary>
    /// 获取有新签合同的企业数量
    /// </summary>
    /// <param name="EntName">限制的企业名称列表，限制企业名称列表最多1024条</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public long Noah_GetHaveNewContractEntCount(List<string> EntName, DateTime BeginTime)
    {
        string sql = $@"SELECT COUNT(DISTINCT EN.EntName) AS ContractCount
                        FROM ehrx_customer.customer AS CU
                        JOIN ehrx_customer.contract AS CO ON CO.customer_id=CU.customer_id 
		                    AND CO.nature='d733f176-dc9f-657b-7047-8a4026b9b100' 
		                    AND CO.business_type<>'7debec53-aead-8f82-d6ea-1473516ebf75' 
		                    AND CO.sign_date>'{BeginTime.ToString("yyyy-MM-dd")}' AND CU.del_flag=0 AND CO.del_flag=0
                        JOIN ({string.Join(@"
                                                         UNION ", EntName.Select(x => $"SELECT '{x}' AS EntName"))}) AS EN ON cu.`name`=EN.EntName";
        var ContractCount = _noahContext.SqlQueryDynamic(sql).FirstOrDefault();
        if (ContractCount != null && ContractCount.ContractCount is long)
            return ContractCount.ContractCount;
        else
            return 0;
    }
    /// <summary>
    /// 获取销售机会企业数量
    /// </summary>
    /// <param name="EntName">限制的企业名称列表，限制企业名称列表最多1024条</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public long Noah_GetOpportunityCount(List<string> EntName)
    {
        //var linq = from a in _noahContext.OpportUnity
        //           join b in _noahContext.Customer.Where(g => EntName.Contains(g.Name))
        //           .Select(s => s.CustomerId)
        //           on a.CustomerId equals b
        //           select b;

        //var OpportunityCount = linq.Distinct().Count();
        //return OpportunityCount;

        string sql = $@"SELECT COUNT(DISTINCT o.customer_id) AS OpportunityCount
                        FROM ehrx_customer.opportunity AS o
                        JOIN ehrx_customer.`customer` AS c ON o.customer_id=C.customer_id AND o.del_flag=0 AND c.del_flag=0
                        JOIN ({string.Join(@"
                                                         UNION ", EntName.Select(x => $"SELECT '{x}' AS EntName"))}) AS EN ON C.`name`=EN.EntName
                        -- WHERE d.create_time>@BeginTime
                        ;";

        var data = _noahContext.SqlQueryDynamic(sql).FirstOrDefault();
        if (data != null && data.OpportunityCount is long)
            return data.OpportunityCount;
        else
            return 0;
    }

    /// <summary>
    /// 获取招聘需求线索
    /// </summary>
    /// <param name="EntName">限制的企业名称列表，限制企业名称列表最多1024条</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public List<DataScreenExpansion_ChartItem> Noah_GetRecruitOpportunity(List<string> EntName)
    {
        var data = _noahContext.SqlQueryDynamic($@"SELECT COUNT(d.customer_id) AS `value`,d.product_type_name AS `name`
                        FROM ehrx_management.a_operating_detail AS d
                        JOIN ehrx_management.v_customer AS c ON d.customer_id=c.customer_id AND d.del_flag=0 AND c.del_flag=0
                        JOIN (SELECT '招聘流程外包' AS PTName
			                        UNION ALL
			                        SELECT '人才速聘' AS PTName
			                        UNION ALL
			                        SELECT '委托招聘' AS PTName
			                        UNION ALL
			                        SELECT '人才派遣现金流老业务' AS PTName
			                        UNION ALL
			                        SELECT '人才派遣现金流新业务' AS PTName
			                        UNION ALL
			                        SELECT '招聘外包现金流老业务' AS PTName
			                        UNION ALL
			                        SELECT '零工兼职现金流新业务' AS PTName
			                        UNION ALL
			                        SELECT '劳务派遣现金流新业务' AS PTName
			                        UNION ALL
			                        SELECT '劳务派遣现金流老业务' AS PTName) AS PT ON d.product_type_name=PT.PTName
                        JOIN ({string.Join(@"
                                                         UNION ", EntName.Select(x => $"SELECT '{x}' AS EntName"))}) AS EN ON C.`name`=EN.EntName
                        -- WHERE d.create_time>@BeginTime
                        GROUP BY d.product_type_name;")
            .Select(x => new DataScreenExpansion_ChartItem { name = x.name, value = (int)x.value })
            .ToList();
        return data;
    }

    /// <summary>
    /// 获取销售产品分类
    /// </summary>
    /// <param name="EntName">限制的企业名称列表，限制企业名称列表最多1024条</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public List<DataScreenExpansion_ChartItem> Noah_GetOperatingPro(List<string> EntName)
    {
        string sql = $@"SELECT COUNT(d.customer_id) AS CCount,d.product_type_name
FROM ehrx_management.a_operating_detail AS d
JOIN ehrx_management.v_customer AS c ON d.customer_id=c.customer_id AND d.del_flag=0 AND c.del_flag=0
JOIN ({string.Join(@"
                                                         UNION ", EntName.Select(x => $"SELECT '{x}' AS EntName"))}) AS EN ON C.`name`=EN.EntName
-- WHERE d.create_time>@BeginTime
WHERE d.product_type_name is not null
GROUP BY d.product_type_name;";
        var data = _noahContext.SqlQueryDynamic(sql)
            .Select(x => new DataScreenExpansion_ChartItem { name = x.product_type_name, value = (int)x.CCount })
            .ToList();
        return data;
    }

    #endregion

    #region 数字诺亚查询经营损益明细表【为高松干--数据大屏需求开发】
    /// <summary>
    /// 获取部门营收
    /// </summary>
    /// <param name="BeginTime"></param>
    /// <param name="EndTime"></param>
    /// <returns></returns>
    public decimal Noah_GetContractAmountByDepart(string? DepartName, DateTime BeginTime, DateTime EndTime)
    {
        string sql_DepartCode = $"SELECT dept_code FROM `ehrx_management`.`v_sys_dept` WHERE `name`='{DepartName}'";
        var dept_code = _noahContext.SqlQueryDynamic(sql_DepartCode).FirstOrDefault();
        if (dept_code != null && dept_code.dept_code is string)
        {
            string sql = $@"SELECT
	                            SUM(expense_amount) AS ContractAmount
                            FROM
	                            `ehrx_management`.`a_operating_detail` 
                            WHERE
	                            `cost_center_no` LIKE '%{dept_code.dept_code}%' 
	                            -- AND LENGTH( cost_center_no )= 8 
	                            AND expense_type IN ( 1, 2, 5, 7 ) 
	                            AND budget_account_code IN ( SELECT budget_account_code FROM `ehrx_management`.`a_operating_detail` WHERE expense_type IN ( 1, 2, 5 ) GROUP BY budget_account_code )
	                            and del_flag='0'
                            and cost_time between '{BeginTime.ToString("yyyy-MM-dd")}' and '{EndTime.ToString("yyyy-MM-dd")} 23:59:59'
                                    ";
            var ContractAmount = _noahContext.SqlQueryDynamic(sql).FirstOrDefault();
            if (ContractAmount != null && ContractAmount.ContractAmount is decimal)
                return ContractAmount.ContractAmount;
            else
                return 0;
        }
        else
            return 0;
    }
    /// <summary>
    /// 获取产品收入
    /// </summary>
    /// <param name="BeginTime"></param>
    /// <param name="EndTime"></param>
    /// <returns></returns>
    public List<DataScreenExpansion_TimeDetail> Noah_GetContractAmountByPro(DateTime BeginTime, DateTime EndTime)
    {
        bool IsStatisticsByWeek = false;//false=按月，true=按周
        //按周统计
        if (BeginTime.Year == EndTime.Year && BeginTime.Month == EndTime.Month)
            IsStatisticsByWeek = true;

        int number = IsStatisticsByWeek ? (int)Math.Ceiling((EndTime - BeginTime).TotalDays / 7.0) :
                    (((EndTime.Year - BeginTime.Year) * 12) + EndTime.Month - BeginTime.Month);
        string sql_Number = $@"WITH number AS({string.Join(@"
                                union ", Enumerable.Range(0, number + 1).Select(s => $"select {s} as n"))})";
        string sql_TEMPKDATE;
        if (IsStatisticsByWeek)
            sql_TEMPKDATE = @",TEMPKDATE AS (
	                            SELECT @BeginTime AS BeginTime,DATE_ADD(@TempBeginTime,INTERVAL -1 SECOND) EndTime WHERE @BeginTime<>@TempBeginTime
	                            UNION
		                            SELECT DATE_ADD(@TempBeginTime,INTERVAL n WEEK) as BeginTime, 
					                            DATE_ADD(DATE_ADD(@TempBeginTime,INTERVAL n+1 WEEK),INTERVAL -1 SECOND) as EndTime
		                            FROM number
		                            WHERE n <TIMESTAMPDIFF(WEEK,@TempBeginTime,@TempEndTime) 
	                            UNION
	                            SELECT DATE_ADD(DATE_ADD(@TempEndTime,INTERVAL -1 DAY),INTERVAL -1 SECOND) AS BeginTime,DATE_ADD(DATE_ADD(@EndTime,INTERVAL 1 DAY),INTERVAL -1 SECOND) AS EndTime WHERE @TempEndTime<>@EndTime
                            )";
        else
            sql_TEMPKDATE = @",TEMPKDATE AS (
	                                SELECT DATE_ADD(@BeginTime,INTERVAL n Month) as BeginTime, 
				                                DATE_ADD(DATE_ADD(@BeginTime,INTERVAL n+1 Month),INTERVAL -1 SECOND) as EndTime
	                                FROM number
	                                WHERE n<=TIMESTAMPDIFF(MONTH,@BeginTime,@EndTime) 
                                )";

        string sql_TempSet = @"
                        set @TempBeginTime =DATE_ADD(@BeginTime,INTERVAL (7-WEEKDAY(@BeginTime)-1) DAY);
                        set @TempEndTime =DATE_ADD(DATE_ADD(@EndTime,INTERVAL -WEEKDAY(@EndTime) DAY),INTERVAL 1 SECOND);";
        string sql = $@"SET @BeginTime ='{BeginTime.ToString("yyyy-MM-dd")}';
                SET @EndTime ='{EndTime.ToString("yyyy-MM-dd")} 23:59:59';
                {sql_TempSet}

                {sql_Number}
                {sql_TEMPKDATE}

				,Pro AS(
				select '雇主品牌' as product_type_name
                union all select '人才速聘' as product_type_name
                union all select 'SaaS服务' as product_type_name
                -- union all select 'SaaS服务(软件)' as product_type_name
                -- union all select 'SaaS服务(硬件)' as product_type_name
				)
				,TimePro AS (																
					select *
					from TEMPKDATE
					cross JOIN Pro
				)

                SELECT TP.BeginTime,TP.EndTime,TP.product_type_name as `Name`,IFNULL(T.`Value`,0) AS `Value`
                FROM TimePro TP
                LEFT JOIN (
                    SELECT w.BeginTime,w.EndTime,w.product_type_name as `Name`,
		                sum(CASE i.income_categary
			                WHEN '1' THEN IFNULL(b.bill_amount ,0)
			                WHEN '2' THEN IFNULL(i.total_amount ,0)
			                WHEN '3' THEN IFNULL(i.total_amount ,0)
			                WHEN '4' THEN IFNULL(b.bill_amount ,0)
			                ELSE 0
		                END ) AS `Value`
                    FROM TimePro as w
                    JOIN ehrx_management.a_operating_detail AS d ON d.cost_time between w.BeginTime and w.EndTime AND d.`product_name`=w.product_type_name and d.del_flag=0
                            and d.expense_type='1' and d.expense_table='p_cost_income'
                    LEFT JOIN ehrx_project.p_cost_income i ON d.expense_table_id = i.id and i.del_flag='0' -- 取回款金额、开票金额
                    LEFT JOIN ehrx_project.p_cost_income_bill b ON i.id = b.income_id and b.del_flag='0' and i.income_categary in ('1','4')-- 取开票金额
                    GROUP BY w.BeginTime,w.EndTime,w.product_type_name
				)T ON TP.BeginTime=T.BeginTime AND TP.EndTime=T.EndTime AND TP.product_type_name=T.`Name`;";
        var ContractAmount = _noahContext.SqlQueryDynamic(sql)
            .Select(x => new DataScreenExpansion_TimeDetail { BeginTime = DateTime.Parse(x.BeginTime), EndTime = DateTime.Parse(x.EndTime), Name = x.Name, Value = (int)x.Value })
            .ToList();
        return ContractAmount;
    }

    /// <summary>
    /// 获取总收入收入
    /// </summary>
    /// <param name="BeginTime"></param>
    /// <param name="EndTime"></param>
    /// <returns></returns>
    public decimal Noah_GetContractAmount(DateTime BeginTime, DateTime EndTime)
    {
        string sql = $@"SET @BeginTime ='{BeginTime.ToString("yyyy-MM-dd")}';
                        SET @EndTime ='{EndTime.ToString("yyyy-MM-dd")} 23:59:59';
                        SELECT IfNULL(sum(expense_amount),0) AS `ContractAmount`
                        FROM ehrx_management.a_operating_detail AS d
                        where d.cost_time between @BeginTime and @EndTime and d.del_flag=0;";
        var ContractAmount = _noahContext.SqlQueryDynamic(sql).FirstOrDefault();
        if (ContractAmount != null && ContractAmount.ContractAmount is decimal)
            return ContractAmount.ContractAmount;
        else
            return 0;
    }

    /// <summary>
    /// 获取诺优考合同金额
    /// </summary>
    /// <param name="BeginTime"></param>
    /// <param name="EndTime"></param>
    /// <returns></returns>
    public decimal Noah_GetNYKContractAmount(DateTime BeginTime, DateTime EndTime)
    {
        string sql = $@"SET @BeginTime ='{BeginTime.ToString("yyyy-MM-dd")}';
                        SET @EndTime ='{EndTime.ToString("yyyy-MM-dd")} 23:59:59';
                        SELECT
	                        IfNULL(SUM(expense_amount),0) AS `ContractAmount`
                        FROM
	                        `ehrx_management`.`a_operating_detail` as d
                        WHERE
	                         (budget_account_no='招聘流程外包新客户收入' OR budget_account_no='SaaS服务(软件)新客户收入')
                            and d.cost_time between @BeginTime and @EndTime and d.del_flag=0;";
        var ContractAmount = _noahContext.SqlQueryDynamic(sql).FirstOrDefault();
        if (ContractAmount != null && ContractAmount.ContractAmount is decimal)
            return ContractAmount.ContractAmount;
        else
            return 0;
    }
    #endregion

}
