﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 项目成员任务
/// </summary>
[Table("tasks")]
public class Tasks
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string TaskId { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 相关Id，根据type区分类型
    /// </summary>
    public string? TargetId { get; set; } = default!;

    public string UserId { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; } = string.Empty;

    /// <summary>
    /// 任务内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 任务类型
    /// </summary>
    public TaskHandlingType Type { get; set; }

    /// <summary>
    /// 任务状态
    /// </summary>
    [ConcurrencyCheck]
    public TaskHandlingStatus Status { get; set; } = TaskHandlingStatus.进行中;

    // /// <summary>
    // /// 结果
    // /// </summary>
    // public TaskHandlingResult Result { get; set; } = TaskHandlingResult.成功;

    /// <summary>
    /// 结果文本
    /// </summary>
    public string? ResultText { get; set; }

    /// <summary>
    /// 任务结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 总数
    /// </summary>
    public int Total { get; set; }

    /// <summary>
    /// 成功数
    /// </summary>
    public int Successful { get; set; }

    /// <summary>
    /// 失败数
    /// </summary>
    public int Failed { get; set; }

    /// <summary>
    /// 重复数
    /// </summary>
    public int Duplicate { get; set; }

    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Project Project { get; set; } = default!;
}
