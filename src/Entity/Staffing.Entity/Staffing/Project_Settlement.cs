﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// teambounty订单结算表
/// </summary>
[Table("project_settlement")]
public class Project_Settlement
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 订单表(post_bounty)主键-订单号
    /// </summary>
    public string OrderId { get; set; } = default!;

    /// <summary>
    /// 结算人id
    /// </summary>
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 结算人id
    /// </summary>
    public string? UserNo { get; set; }

    /// <summary>
    /// 支付单号-调用结算接口返回唯一标识
    /// </summary>
    public string? SettlementNum { get; set; }

    /// <summary>
    /// 类型:0-协同，1-渠道
    /// </summary>
    public SettleType Type { get; set; } = default!;

    /// <summary>
    /// 结算状态 0-结算中,1-已结算,2-结算失败
    /// </summary>
    [ConcurrencyCheck]
    public SettleStatus Status { get; set; } = default!;

    /// <summary>
    /// 是否发起服务奖金
    /// </summary>
    [ConcurrencyCheck]
    public bool IsServiceBonusInitiated { get; set; }

    /// <summary>
    /// 实际结算金额
    /// </summary>
    public decimal ActualSettlementMoney { get; set; } = default!;

    /// <summary>
    /// 应结算金额
    /// </summary>
    public decimal? SettlementMoney { get; set; }

    /// <summary>
    /// 结算凭据-发票号或其他
    /// </summary>
    public string? SettementVoucher { get; set; }

    /// <summary>
    /// 手续费
    /// </summary>
    public decimal? ServiceCharge { get; set; }

    /// <summary>
    /// 税费
    /// </summary>
    public decimal? Taxes { get; set; } = default!;

    /// <summary>
    /// 结算时间
    /// </summary>
    public DateTime? SettlementTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreatedUser { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    public string? UpdatedUser { get; set; }

    /// <summary>
    /// 是否删除
    /// </summary>
    public bool Deleted { get; set; }

    /// <summary>
    /// 结算方式
    /// </summary>
    public OrderSettlementType? SettlementType { get; set; }

    public User_Hr User_Hr { get; set; } = default!;

    [ForeignKey("OrderId")]
    public Post_Bounty Post_Bounty { get; set; } = default!;
}

public enum SettleType
{
    协同, 渠道, 跟进人
}

public enum SettleStatus
{
    结算中, 已结算, 结算失败
}

public enum OrderSettlementType
{
    工资卡
}