﻿using System.Text.Json.Serialization;
using Config.CommonModel;


namespace Staffing.Model.Hr.Project;

public class GetProjectTeamHr : QueryRequest
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }
}

public class GetProjectTeamHrResponse : QueryResponse
{
    public List<GetProjectTeamHrDetail> Rows { get; set; } = default!;
}

public class GetProjectTeamHrDetail
{
    /// <summary>
    /// hrid
    /// </summary>
    public string? HrId { get; set; }

    public string? TeamProjectId { get; set; }

    /// <summary>
    /// hr名称
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 企业Id
    /// </summary>
    public string? EntId { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// 协同天数
    /// </summary>
    public int CreationDays { get; set; }

    /// <summary>
    /// 推送人才
    /// </summary>
    public string? RecmdTalents { get; set; }

    /// <summary>
    /// 交付成功
    /// </summary>
    public string? ProjDeliver { get; set; }

    /// <summary>
    /// 项目分润
    /// </summary>
    public string? ProjBounty { get; set; }

    [JsonIgnore]
    public DateTime CreatedTime { get; set; }
}