﻿using System.ComponentModel;

namespace Config.Enums;

/// <summary>
/// 招聘流程状态
/// </summary>
public enum RecruitStatus
{
    /// <summary>
    /// Hr初筛
    /// </summary>
    [Description("Hr初筛")]
    HrScreening = 1,

    /// <summary>
    /// 面试官筛选
    /// </summary>
    [Description("面试官筛选")]
    InterviewerScreening = 2,

    /// <summary>
    /// 面试
    /// </summary>
    [Description("面试")]
    Interview = 3,

    /// <summary>
    /// Offer
    /// </summary>
    [Description("Offer")]
    Offer = 4,

    /// <summary>
    /// 入职
    /// </summary>
    [Description("入职")]
    Induction = 5,

    /// <summary>
    /// 签约
    /// </summary>
    [Description("签约")]
    Contract = 6,

    /// <summary>
    /// 邀约面试
    /// </summary>
    [Description("邀约面试")]
    Yaoyue = 7,

    /// <summary>
    /// 面试通过
    /// </summary>
    [Description("面试通过")]
    Inductioned = 8,

    /// <summary>
    /// 计费待确认
    /// </summary>
    [Description("计费待确认")]
    Jifei = 9,

    /// <summary>
    /// 计费过保
    /// </summary>
    [Description("计费过保")]
    Guobao = 10,

    /// <summary>
    /// 归档
    /// </summary>
    [Description("归档")]
    FileAway = 99
}

/// <summary>
/// 招聘流程归档状态
/// </summary>
public enum RecruitFileAway
{
    /// <summary>
    /// 人才储备
    /// </summary>
    [Description("人才储备")]
    TalentReserve = 0,

    /// <summary>
    /// 淘汰
    /// </summary>
    [Description("淘汰")]
    Eliminate = 1,

    /// <summary>
    /// 候选人放弃
    /// </summary>
    [Description("候选人放弃")]
    CandidateAbandonment = 2,

    /// <summary>
    /// 离职
    /// </summary>
    [Description("离职")]
    Quit = 3,

    /// <summary>
    /// 无效线索
    /// </summary>
    [Description("无效线索")]
    Invalid = 4
}

/// <summary>
/// 招聘流程类型
/// </summary>
public enum RecruitType
{
    /// <summary>
    /// 主动投递
    /// </summary>
    [Description("主动投递")]
    Driving = 1,

    /// <summary>
    /// 被动投递
    /// </summary>
    [Description("被动投递")]
    Passive = 2,
}

/// <summary>
/// 招聘流程面试过程
/// </summary>
public enum RecruitInterviewProcess
{
    /// <summary>
    /// 初试
    /// </summary>
    [Description("初试")]
    Preliminary = 0,

    /// <summary>
    /// 复试
    /// </summary>
    [Description("复试")]
    Retest = 1,

    /// <summary>
    /// 终试
    /// </summary>
    [Description("终试")]
    Final = 2
}

/// <summary>
/// 招聘流程面试结果
/// </summary>
public enum RecruitInterviewOutcome
{
    /// <summary>
    /// 待反馈
    /// </summary>
    [Description("待反馈")]
    Waiting = 0,

    /// <summary>
    /// 淘汰
    /// </summary>
    [Description("淘汰")]
    Fail = 1,

    /// <summary>
    /// 通过
    /// </summary>
    [Description("通过")]
    Passed = 2,

    /// <summary>
    /// 备用
    /// </summary>
    [Description("备用")]
    Spare = 3,

    /// <summary>
    /// 已取消
    /// </summary>
    [Description("已取消")]
    Cancelled = 4,

    /// <summary>
    /// 候选人没来
    /// </summary>
    [Description("候选人没来")]
    NotCome = 5
}

/// <summary>
/// 招聘流程面试用户反馈
/// </summary>
public enum RecruitInterviewUserFeedBack
{
    /// <summary>
    /// 待反馈
    /// </summary>
    [Description("待反馈")]
    Waiting = 0,

    /// <summary>
    /// 已确认
    /// </summary>
    [Description("已确认")]
    Confirmed = 1,

    /// <summary>
    /// 已拒绝
    /// </summary>
    [Description("已拒绝")]
    Rejected = 2,
}

/// <summary>
/// 招聘流程面试方式
/// </summary>
public enum RecruitInterviewForms
{
    /// <summary>
    /// 现场面试
    /// </summary>
    [Description("现场面试")]
    Scene = 0,

    /// <summary>
    /// 视频面试
    /// </summary>
    [Description("视频面试")]
    Video = 1,

    /// <summary>
    /// 电话面试
    /// </summary>
    [Description("电话面试")]
    Phone = 2
}

/// <summary>
/// 面试官待办状态
/// </summary>
public enum RecruitInterviewerTodoStatus
{
    /// <summary>
    /// 未处理
    /// </summary>
    [Description("未处理")]
    Untreated = 0,

    /// <summary>
    /// 已处理
    /// </summary>
    [Description("已处理")]
    Processed = 1,

    /// <summary>
    /// 未知
    /// </summary>
    [Description("未知")]
    Unknown = 2,
}

/// <summary>
/// 面试官待办类型
/// </summary>
public enum RecruitInterviewerTodoType
{
    /// <summary>
    /// 面试官筛选
    /// </summary>
    [Description("面试官筛选")]
    InterviewScreen = 0,

    /// <summary>
    /// 面试
    /// </summary>
    [Description("面试")]
    Interview = 1,
}

/// <summary>
/// 面试官筛选状态
/// </summary>
public enum RecruitInterviewerScreenStatus
{
    /// <summary>
    /// 待反馈
    /// </summary>
    [Description("待反馈")]
    NoFedBack = 0,

    /// <summary>
    /// 未通过
    /// </summary>
    [Description("未通过")]
    NoAdopt = 1,

    /// <summary>
    /// 已通过
    /// </summary>
    [Description("已通过")]
    Adopt = 2,

    /// <summary>
    /// 已取消
    /// </summary>
    [Description("已取消")]
    Cancelled = 3
}

/// <summary>
/// 简历上传重复类型
/// </summary>
public enum RecruitUpLoadRepeatType
{
    未投递,
    投递正常,
    投递异常
}

/// <summary>
/// 简历上传导入记录状态
/// </summary>
public enum RecruitUpLoadStatus
{
    待处理,
    已完成,
    解析失败
}

public enum RecruitAcceptOrderEnum
{
    未接单,
    已经接单
}