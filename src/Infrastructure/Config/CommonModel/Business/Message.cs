using Config.Enums;

namespace Config.CommonModel.Business;

public enum MsgNotifyType
{
    职位报名, 入职信息, 协同交付, 面试官日程, 面试用户日程, 归档,
    职位优选面试, 职位优选入职, 自流转入职提醒, 自流转归档提醒, 企业线索池跟进提醒, 销售易企业线索池分配企业提醒, 企业线索池顾问未绑定钉钉,
    企业线索池分配企业提醒, 销售易诺聘企业审核未通过, 职位需审核通知, 诺优考人才线索提醒, 招聘流程变更面试, 招聘流程变更归档,
    诺聘发布职位 = 101, 诺聘投递简历, 钉钉机器人投递简历提醒, 钉钉机器人HR初筛定时提醒
}

public enum HrMsgType
{
    职位报名, 面试日程, 入职信息, 协同交付, 归档消息
}

/// <summary>
/// 消息基类
/// </summary>
public class MsgNotifyModel
{
    /// <summary>
    /// 消息类型
    /// </summary>
    public MsgNotifyType? Type { get; set; }

    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    // /// <summary>
    // /// 协同Id
    // /// </summary>
    // public string? TeamHrId { get; set; }

    /// <summary>
    /// 时间
    /// </summary>
    public DateTime EventTime { get; set; }

    /// <summary>
    /// 数据
    /// </summary>
    public object Data { get; set; } = default!;
}

// /// <summary>
// /// Im单聊消息通知
// /// </summary>
// public class MsgNotifyImC2CMsg
// {
//     /// <summary>
//     /// Im账号Id
//     /// </summary>
//     public string? ImId { get; set; }

//     /// <summary>
//     /// To_Account 未读的单聊消息总数量（包含所有的单聊会话）。若该条消息下发失败（例如被脏字过滤），该字段值为-1
//     /// </summary>
//     public int UnreadMsgNum { get; set; }
// }

/// <summary>
/// 招聘流程模型
/// </summary>
public class MsgNotifyRecruit
{
    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 是否自己项目
    /// </summary>
    public bool IsSelfProj { get; set; }

    /// <summary>
    /// 招聘流程Id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// 协同项目Id
    /// </summary>
    public string? TeamProjectId { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? SeekerName { get; set; }

    /// <summary>
    /// 代招企业名称
    /// </summary>
    public string? AgentEntName { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 投递时间
    /// </summary>
    public DateTime? DeliveryTime { get; set; }
}

/// <summary>
/// 协同交付
/// </summary>
public class MsgNotifyTeamDelivery
{
    /// <summary>
    /// 主创Id
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// 招聘流程Id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// 协同Id
    /// </summary>
    public string? TeamHrId { get; set; }

    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 协同项目Id
    /// </summary>
    public string? TeamProjectId { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? SeekerName { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Money { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public BountyStatus Status { get; set; }
}

/// <summary>
/// 招聘流程信息
/// </summary>
public class RecruitInfo : MsgNotifyTeamDelivery
{
    public string? HrName { get; set; }

    public HrProjectType? Type { get; set; }

    public DateTime EventTime { get; set; }

    public string? OrderId { get; set; }
}

/// <summary>
/// 项目结束
/// </summary>
public class MsgNotifyProjectEnd
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    // /// <summary>
    // /// 协同项目Id
    // /// </summary>
    // public string? TeamProjectId { get; set; }

    /// <summary>
    /// 是否自己项目
    /// </summary>
    public bool IsSelfProj { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 顾问名称
    /// </summary>
    public string? HrName { get; set; }
}

/// <summary>
/// 诺聘发布职位
/// </summary>
public class MsgNotifyNpCreatePost
{
    /// <summary>
    /// 职位Id
    /// </summary>
    public string PostId { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 企业Id
    /// </summary>
    public string EntId { get; set; } = default!;

    /// <summary>
    /// 企业名称
    /// </summary>
    public string EntName { get; set; } = default!;
}

/// <summary>
/// 诺聘投递简历
/// </summary>
public class MsgNotifyNpDeliver
{
    /// <summary>
    /// 投递Id
    /// </summary>
    public string DeliverId { get; set; } = default!;

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string UserName { get; set; } = default!;

    /// <summary>
    /// 用户手机号
    /// </summary>
    public string UserMobile { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 企业Id
    /// </summary>
    public string EntId { get; set; } = default!;

    /// <summary>
    /// 企业名称
    /// </summary>
    public string EntName { get; set; } = default!;
}

/// <summary>
/// 职位优选
/// </summary>
public class MsgExcellentPost
{
    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 招聘id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// 求职者姓名
    /// </summary>
    public string? SeekerName { get; set; }
}

/// <summary>
/// 面试日程
/// </summary>
public class MsgNotifyInterview
{
}


/// <summary>
/// 自流转
/// </summary>
public class MsgAutomatic
{
    /// <summary>
    /// 主创姓名
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 求职者姓名
    /// </summary>
    public string? SeekerName { get; set; }

    /// <summary>
    /// 订单Id
    /// </summary>
    public string? OrderId { get; set; }

    /// <summary>
    /// 是否自己项目
    /// </summary>
    public bool IsSelfProj { get; set; }
}

/// <summary>
/// 企业线索池
/// </summary>
public class MsgNotifyRecommendEnt
{
    /// <summary>
    /// 数量
    /// </summary>
    public int Num { get; set; }

    /// <summary>
    /// 小时
    /// </summary>
    public int Hours { get; set; }
}

/// <summary>
/// 企业线索池分配顾问
/// </summary>
public class MsgNotifyRecommendEntAlloc
{
    public string? AdviserNo { get; set; }
    public string? AdviserName { get; set; }

    /// <summary>
    /// 企业联系人姓名
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// 企业联系人姓名
    /// </summary>
    public string? HrMobile { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 网点名称
    /// </summary>
    public string? OutletName { get; set; }

    /// <summary>
    /// 诺聘企业审核状态
    /// </summary>
    public string? NpEnStatus { get; set; }
}

/// <summary>
/// 职位需审核通知
/// </summary>
public class MsgNotifyPostNeedAudit
{
    public string? EntName { get; set; }

    public string? PostName { get; set; }

    /// <summary>
    /// 发布顾问
    /// </summary>
    public string? HrName { get; set; }
}