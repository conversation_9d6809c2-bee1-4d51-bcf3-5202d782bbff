﻿namespace Staffing.Entity.Staffing;

// /// <summary>
// /// 用户的顾问列表
// /// </summary>
// [Table("user_seeker_adviser")]
// public class User_Seeker_Adviser
// {
//     [Key]
//     public string Id { get; set; } = EntityTools.SnowflakeId();

//     /// <summary>
//     /// 求职者Id
//     /// </summary>
//     public string SeekerId { get; set; } = default!;

//     /// <summary>
//     /// 顾问Id
//     /// </summary>
//     public string AdviserId { get; set; } = default!;

//     /// <summary>
//     /// 创建时间
//     /// </summary>
//     public DateTime VisitTime { get; set; } = DateTime.Now;

//     /// <summary>
//     /// 创建时间
//     /// </summary>
//     public DateTime CreatedTime { get; set; } = DateTime.Now;


//     public User_Hr User_Hr { get; set; } = default!;
//     public User_Seeker User_Seeker { get; set; } = default!;
// }