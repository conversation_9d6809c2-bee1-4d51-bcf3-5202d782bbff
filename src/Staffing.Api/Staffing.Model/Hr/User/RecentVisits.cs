﻿using System.Text.Json.Serialization;
using Config.CommonModel;
using Config.Enums;

namespace Staffing.Model.Hr.User;

public class GetRecentVisits : QueryRequest
{
}

public class GetRecentVisitsResponse : QueryResponse
{
    public List<GetRecentVisitsInfo> Rows { get; set; } = new List<GetRecentVisitsInfo>();
}

public class GetRecentVisitsInfo
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// 人才级别
    /// </summary>
    public TalentPlatformLevel TalentLevel { get; set; }

    /// <summary>
    /// 人才级别
    /// </summary>
    public string? TalentLevelName { get; set; }

    /// <summary>
    /// 是否实名
    /// </summary>
    public bool Identity { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public OccupationType? Occupation { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public string? OccupationName { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 时间
    /// </summary>
    public DateTime EventTime { get; set; } = DateTime.Now;
}

//最近投递
public class GetRecentDelivery : QueryRequest
{
}

public class GetRecentDeliveryResponse : QueryResponse
{
    public List<GetRecentDeliveryInfo> Rows { get; set; } = new List<GetRecentDeliveryInfo>();
}

public class GetRecentDeliveryInfo
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 投递状态
    /// </summary>
    public SeekerDeliveryStatus? SeekerDeliveryStatus { get; set; }

    /// <summary>
    /// 投递状态
    /// </summary>
    public string? SeekerDeliveryStatusName { get; set; }

    /// <summary>
    /// 招聘状态
    /// </summary>
    [JsonIgnore]
    public RecruitStatus? Status { get; set; }

    /// <summary>
    /// 归档状态
    /// </summary>
    [JsonIgnore]
    public RecruitFileAway? FileAwayStatus { get; set; }

    /// <summary>
    /// 招聘流程Id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 是否实名
    /// </summary>
    public bool Identity { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public OccupationType? Occupation { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public string? OccupationName { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 时间
    /// </summary>
    public DateTime EventTime { get; set; } = DateTime.Now;
}

public class GetNewTalent : QueryRequest
{
}

public class GetNewTalentResponse : QueryResponse
{
    public List<GetNewTalentInfo> Rows { get; set; } = new List<GetNewTalentInfo>();
}

public class GetNewTalentInfo
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// 是否实名
    /// </summary>
    public bool Identity { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public OccupationType? Occupation { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public string? OccupationName { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 人才级别
    /// </summary>
    public TalentPlatformLevel TalentLevel { get; set; }

    /// <summary>
    /// 人才级别
    /// </summary>
    public string? TalentLevelName { get; set; }

    /// <summary>
    /// 时间
    /// </summary>
    public DateTime EventTime { get; set; } = DateTime.Now;
}