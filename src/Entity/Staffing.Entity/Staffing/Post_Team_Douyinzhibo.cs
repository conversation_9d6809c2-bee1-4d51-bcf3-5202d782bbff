﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 抖音直播职位列表 - 特殊部分
/// </summary>
[Table("post_team_douyinzhibo")]
public class Post_Team_Douyinzhibo
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 抖音直播id
    /// </summary>
    public string DouyinzhiboId { get; set; } = default!;

    public string TeamPostId { get; set; } = default!;

    public string ChannelId { get; set; } = default!;

    /// <summary>
    /// 是否讲解
    /// </summary>
    public bool IsSpeek { get; set; } = false;

    /// <summary>
    /// 是否下架
    /// </summary>
    public bool IsRemove {  get; set; } = false;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;


    public Post_Team Post_Team { get; set; } = default!;
}
