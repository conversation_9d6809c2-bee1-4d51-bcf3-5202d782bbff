﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 学校
/// </summary>
[Table("dic_school")]
public class Dic_School
{
    [Key]
    public string Id { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 主管部门
    /// </summary>
    public string Department { get; set; } = string.Empty;

    /// <summary>
    /// 所在市
    /// </summary>
    public string City { get; set; } = string.Empty;

    /// <summary>
    /// 类别,0=专科，1=本科，2=成人
    /// </summary>
    public SchoolType Type { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Describe { get; set; } = string.Empty;

    /// <summary>
    /// logo
    /// </summary>
    public string? Logo { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ActiveStatus Status { get; set; }
}