﻿using Config.CommonModel;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Nest;
using Newtonsoft.Json;
using Staffing.Entity;
using Staffing.Entity.Elasticsearch;
using Staffing.Entity.Staffing;
using Staffing.Model.Hr.KuaiShouTalentInfo;
using Staffing.Core.Interfaces.Hr;
using Microsoft.EntityFrameworkCore;
using Config.CommonModel.ThirdTalentInfo;
using System.Text;
using Microsoft.Extensions.Hosting;
using System.Linq.Dynamic.Core;
using Infrastructure.CommonService;
using Staffing.Core.Service.PostOrder;
using Config;
using Infrastructure.Proxy;
using Staffing.Model.Hr.Dashboard;
using System.Data;
using Infrastructure.CommonInterface;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Options;

namespace Staffing.Core.Services.Service.Hr;

/// <summary>
/// 快招工人才库服务
/// </summary>
[Service(ServiceLifetime.Transient)]
public partial class KuaishouTalentInfoService : IThirdTalentInfoService<KsTalentInfo>
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private readonly EsHelper _esHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly KsPostOrderService _ksPostOrder;
    private readonly CommonDicService _commonDicService;
    private readonly IServiceProvider _provider;
    private readonly CommonUserService _commonUserService;
    private readonly NuoPinApi _nuoPinApi;
    private readonly string baseType = "kzg_invalid_resume_enum";
    private readonly string ValidMemoBaseType = "kzg_valid_resume_enum";
    private readonly LogManager _logger;
    private readonly WeChatHelper _weChatHelper;
    private ConfigManager _config;

    public KuaishouTalentInfoService(StaffingContext context,
                                        RequestContext user,
                                        EsHelper esHelper,
                                        KsPostOrderService ksPostOrder,
                                        IHostEnvironment hostEnvironment,
                                        CommonDicService commonDicService,
                                        IServiceProvider provider, IOptionsSnapshot<ConfigManager> config,
                                        CommonUserService commonUserService,
                                        NuoPinApi nuoPinApi, WeChatHelper weChatHelper,
                                        LogManager logger)
    {
        _context = context;
        _user = user;
        _esHelper = esHelper;
        _ksPostOrder = ksPostOrder;
        _hostingEnvironment = hostEnvironment;
        _commonDicService = commonDicService;
        _provider = provider;
        _commonUserService = commonUserService;
        _nuoPinApi = nuoPinApi;
        _logger = logger;
        _weChatHelper = weChatHelper;
        _config = config.Value;
    }

    public TalentInfoPageListResponse GetKuaiShouTalentInfoFromDBPageList(TalentInfoPageListRequest requst)
    {
        var hrInfo = _context.User_Hr.FirstOrDefault(f => f.UserId == _user.Id);
        if (hrInfo == null)
            throw new BadRequestException("内容为空");

        var response = new TalentInfoPageListResponse();
        var predicate = PredicateBuilder.New<Kuaishou_Talent_Infos>(x => x.RecommenderId.Equals("**********"));// 只处理“河北招聘”简历，2023/6/25运营提需求
        // 待同事跟进总数
        var TeamWorkerNum = _context.Kuaishou_Talent_Infos.Where(x => x.RecommenderId.Equals("**********") && x.Kuaishou_Hr_Talent_Relations != null && !x.Kuaishou_Hr_Talent_Relations.HrId.Equals(_user.Id)).Count();
        // 待我跟进总数
        var myNum = _context.Kuaishou_Talent_Infos.Where(x => x.RecommenderId.Equals("**********") && ((x.Kuaishou_Hr_Talent_Relations != null && x.Kuaishou_Hr_Talent_Relations.HrId.Equals(_user.Id))
            || x.Kuaishou_Hr_Talent_Relations == null)).Count();
        // 等会再打总数 - 我自己的
        var WaitToPhoneNum = _context.Kuaishou_Talent_Infos.Where(x => x.RecommenderId.Equals("**********") && x.Kuaishou_Hr_Talent_Relations != null && x.Kuaishou_Hr_Talent_Relations.HrId.Equals(_user.Id)
            && x.Kuaishou_Hr_Talent_Relations.WaitToPhone == WaitToPhone.是).Count();
        // 无效简历总数 - 平台总数
        var InvalidNum = _context.Kuaishou_Talent_Infos.Where(x => x.RecommenderId.Equals("**********") && x.Kuaishou_Hr_Talent_Relations != null && x.Kuaishou_Hr_Talent_Relations.Result == ResultStatus.无效).Count();
        response.TabNum = new TabNum
        {
            myNum = myNum,
            TeamWorkerNum = TeamWorkerNum,
            WaitToPhoneNum = WaitToPhoneNum,
            InvalidNum = InvalidNum,
        };

        if (requst.TabStatus == TabStatus.待同事跟进)
            predicate = predicate.And(x => x.Kuaishou_Hr_Talent_Relations != null && !x.Kuaishou_Hr_Talent_Relations.HrId.Equals(_user.Id));
        else if (requst.TabStatus == TabStatus.待我跟进)
            predicate = predicate.And(x => (x.Kuaishou_Hr_Talent_Relations != null && x.Kuaishou_Hr_Talent_Relations.HrId.Equals(_user.Id))
            || x.Kuaishou_Hr_Talent_Relations == null);
        else if (requst.TabStatus == TabStatus.等会再打)
            predicate = predicate.And(x => x.Kuaishou_Hr_Talent_Relations != null && x.Kuaishou_Hr_Talent_Relations.HrId.Equals(_user.Id)
            && x.Kuaishou_Hr_Talent_Relations.WaitToPhone == WaitToPhone.是);
        else if (requst.TabStatus == TabStatus.无效简历)
            predicate = predicate.And(x => x.Kuaishou_Hr_Talent_Relations != null && x.Kuaishou_Hr_Talent_Relations.Result == ResultStatus.无效);

        // 4个标签页通用查询条件
        if (requst.ReturnVisit.HasValue)
        {
            if (requst.ReturnVisit == ReturnVisit.未回访)
                predicate = predicate.And(x => (x.Kuaishou_Hr_Talent_Relations != null && x.Kuaishou_Hr_Talent_Relations.ReturnVisit == requst.ReturnVisit)
            || x.Kuaishou_Hr_Talent_Relations == null);
            else
                predicate = predicate.And(x => x.Kuaishou_Hr_Talent_Relations != null && x.Kuaishou_Hr_Talent_Relations.ReturnVisit == requst.ReturnVisit);
        }
        if (requst.Result.HasValue)
        {
            if (requst.Result == ResultStatus.有效)
                predicate = predicate.And(x => (x.Kuaishou_Hr_Talent_Relations != null && x.Kuaishou_Hr_Talent_Relations.Result == requst.Result)
            || x.Kuaishou_Hr_Talent_Relations == null);
            else
                predicate = predicate.And(x => x.Kuaishou_Hr_Talent_Relations != null && x.Kuaishou_Hr_Talent_Relations.Result == requst.Result);
        }
        if (requst.BindJob.HasValue)
        {
            if (requst.BindJob == BindJob.已绑定)
                predicate = predicate.And(x => x.Post_Team_Third_Jobid_Rel != null);
            else if (requst.BindJob == BindJob.未绑定)
                predicate = predicate.And(x => x.Post_Team_Third_Jobid_Rel == null);
        }
        if (requst.Status.HasValue)
        {
            if (requst.Status == KuaishouStatus.未推送)
                predicate = predicate.And(x => (x.Kuaishou_Hr_Talent_Relations != null && x.Kuaishou_Hr_Talent_Relations.Status == requst.Status)
            || x.Kuaishou_Hr_Talent_Relations == null);
            else
                predicate = predicate.And(x => x.Kuaishou_Hr_Talent_Relations != null && x.Kuaishou_Hr_Talent_Relations.Status == requst.Status);
        }
        if (!string.IsNullOrWhiteSpace(requst.StartTime))
            predicate = predicate.And(x => x.ApplyTime >= Convert.ToDateTime(requst.StartTime));
        if (!string.IsNullOrWhiteSpace(requst.EndTime))
            predicate = predicate.And(x => x.ApplyTime <= Convert.ToDateTime(requst.EndTime).AddDays(1).AddSeconds(-1));
        // 综合查询字段：姓名/手机号/快手职位/同步快手职位/实际推送职位
        if (!string.IsNullOrEmpty(requst.Search))
            predicate = predicate.And(x => x.Name.IndexOf(requst.Search) >= 0 || x.Phone.IndexOf(requst.Search) >= 0
            || x.JobName.IndexOf(requst.Search) >= 0 || x.SendTeamPostName.IndexOf(requst.Search) >= 0
            || (x.Post_Team_Third_Jobid_Rel != null && x.Post_Team_Third_Jobid_Rel.Post_Team.Post.Name.IndexOf(requst.Search) >= 0));

        var sql = _context.Kuaishou_Talent_Infos.Where(predicate);
        response.Total = sql.Select(s => new { a = 1 }).Count();
        var result = sql
            .OrderByDescending(o => o.ApplyTime)
            .ThenByDescending(o => o.CreatedTime)
            .Skip((requst.PageIndex - 1) * requst.PageSize)
            .Take(requst.PageSize)
            .Select(s => new KuaishouTalentInfo
            {
                ApplicationId = s.ApplicationId,
                ApplyTime = s.ApplyTime,
                ChannelName = s.ChannelName,
                Name = s.Name,
                Phone = s.Phone,
                JobId = s.JobId,
                JobName = s.JobName,
                SendTeamPostId = s.SendTeamPostId,
                SendTeamPostName = s.SendTeamPostName,
                SendPostId = s.SendPostId,
                TeamPostId = s.Post_Team_Third_Jobid_Rel == null ? string.Empty : s.Post_Team_Third_Jobid_Rel.TeamPostId,
                PostId = s.Post_Team_Third_Jobid_Rel == null ? string.Empty : s.Post_Team_Third_Jobid_Rel.Post_Team.PostId,
                PostName = s.Post_Team_Third_Jobid_Rel == null ? "无关联" : s.Post_Team_Third_Jobid_Rel.Post_Team.Post.Name,
                KuaishouTalentHrRelation = s.Kuaishou_Hr_Talent_Relations == null
                ? new KuaishouTalentHrRelation
                {
                    ReturnVisit = ReturnVisit.未回访,
                    Result = ResultStatus.有效,
                    Status = KuaishouStatus.未推送
                }
                : new KuaishouTalentHrRelation
                {
                    HrId = s.Kuaishou_Hr_Talent_Relations.HrId,
                    HrName = s.Kuaishou_Hr_Talent_Relations.User_Hr.NickName,
                    VisitMemo = s.Kuaishou_Hr_Talent_Relations.VisitMemo,
                    ReturnVisit = s.Kuaishou_Hr_Talent_Relations.ReturnVisit,
                    Result = s.Kuaishou_Hr_Talent_Relations.Result,
                    Status = s.Kuaishou_Hr_Talent_Relations.Status,
                    WaitToPhone = s.Kuaishou_Hr_Talent_Relations.WaitToPhone,
                    InvalidType = s.Kuaishou_Hr_Talent_Relations.InvalidType
                },
                GenderName = s.GenderName,
                Age = s.Age,
                EducationName = s.DataDynamicResumeInfo.HighestDegree
            })
            .ToList();
        response.Rows = result;
        return response;
    }

    /// <summary>
    /// 快招工列表 - es查询，暂时不用
    /// </summary>
    /// <param name="requst"></param>
    /// <returns></returns>
    /// <exception cref="BadRequestException"></exception>
    public TalentInfoPageListResponse GetKuaiShouTalentInfoFromESPageList(TalentInfoPageListRequest request)
    {
        var hrInfo = _context.User_Hr.FirstOrDefault(f => f.UserId == _user.Id);
        // 非顾问不能查看
        if (hrInfo == null)
            throw new BadRequestException("内容为空");
        var response = new TalentInfoPageListResponse();
        var client = _esHelper.GetClient();
        var mustQuerys = new List<Func<QueryContainerDescriptor<EsKuaishouTalentInfo>, QueryContainer>>();

        if (!string.IsNullOrEmpty(request.Search))
            mustQuerys.Add(q => q.Wildcard(t => t.Field(f => f.Search).Value($"*{request.Search}*")));

        var resultModel = client.Search<EsKuaishouTalentInfo>(s => s
        .Index(_esHelper.GetIndex().KuaishouTalentInfo)
        .TrackTotalHits()
        .Skip((request.PageIndex - 1) * request.PageSize)
        .Take(request.PageSize)
        .Query(q => q.Bool(b => b.Must(mustQuerys)))
        .Sort(s => s.Field(f =>
        {
            f.Order(SortOrder.Descending);
            f.Field(c => c.ApplyTime);
            return f;
        })));

        response.Total = (int)resultModel.Total;
        var rows = new List<KuaishouTalentInfo>();
        resultModel.Documents.ForEach(f =>
        {
            rows.Add(new KuaishouTalentInfo
            {
                ApplicationId = f.ApplicationId,
                NickName = f.Name,
                Name = f.Name,
                GenderName = f.GenderName,
                Age = f.Age,
                JobName = f.JobName,
                ChannelName = f.ChannelName,
                ApplyTime = f.ApplyTime,
                //DataQualityLabel = f.DataQualityLabel,
                //DataDynamicResumeInfo = f.DataDynamicResumeInfo,
                //DataUserCity = f.DataUserCity,
                //ExtInfoIntentionJob = f.ExtInfoIntentionJob,
                //ExtInfoIntentionCity = f.ExtInfoIntentionCity,
                //ExtInfoJobHuntingStatus = f.ExtInfoJobHuntingStatus
            });
        });
        response.Rows = rows;
        return response;
    }

    public async Task<KuaishouTalentInfosOriginal> TryParseTalentInfoAsync(object infos)
    {
        var result = new KuaishouTalentInfosOriginal();
        var talentInfos = JsonConvert.DeserializeObject<KuaishouTalentInfosOriginal>(infos.ToString());
        if (!string.IsNullOrWhiteSpace(talentInfos.extInfo))
        {
            var extInfo = JsonConvert.DeserializeObject<ExtInfo>(talentInfos.extInfo);
            talentInfos.ExtInfos = extInfo;
        }
        if (!string.IsNullOrWhiteSpace(talentInfos.data))
        {
            var data = JsonConvert.DeserializeObject<Data>(talentInfos.data);
            if (!string.IsNullOrWhiteSpace(data.locationCity))
            {
                var locationCity = JsonConvert.DeserializeObject<LocationCity>(data.locationCity);
                data.LocationCitys = locationCity;
            }
            talentInfos.Datas = data;
        }
        result = talentInfos;
        Kuaishou_Talent_Infos kuaishou_Talent_Infos = new Kuaishou_Talent_Infos
        {
            ApplicationId = talentInfos.applicationId,
            OpenTenantId = talentInfos.openTenantId,
            ResumeId = talentInfos.resumeId,
            Name = talentInfos.name,
            GenderCode = talentInfos.gender?.code,
            GenderName = talentInfos.gender?.name,
            Phone = talentInfos.phone,
            Age = talentInfos.age,
            JobId = talentInfos.job.jobId ?? string.Empty,
            JobName = talentInfos.job.name,
            ChannelName = talentInfos.channelName,
            Recommender = talentInfos.recommender,
            RecommenderId = talentInfos.recommenderId,
            ApplyTime = DateTimeOffset.FromUnixTimeMilliseconds(talentInfos.applyTime).ToLocalTime().DateTime,// 13位时间戳
            CompanyId = talentInfos.company?.companyId,
            CompanyBusinessName = talentInfos.company?.businessName,
            CompanyBusinessCode = talentInfos.company?.businessCode,
            Platform = talentInfos.platform,
            DataChannelSourceCode = talentInfos.Datas?.channelSourceCode,
            DataChannelSourceName = talentInfos.Datas?.channelSourceName,
            DataQualityLabel = new DataQualityLabel
            {
                Age = talentInfos.Datas?.qualityLabel?.age,
                IntentionCity = talentInfos.Datas?.qualityLabel?.intentionCity,
                IntentionJobCategory = talentInfos.Datas?.qualityLabel?.intentionJobCategory,
                MultiApplyRecently = talentInfos.Datas?.qualityLabel?.multiApplyRecently,
                LiveCity = talentInfos.Datas?.qualityLabel?.liveCity
            },
            // 这些是动态字段，随着接口变动需要手动改，同时修改实体DataDynamicResumeInfo
            DataDynamicResumeInfo = talentInfos.Datas?.dynamicResumeInfo == null ? new DataDynamicResumeInfo() : new DataDynamicResumeInfo
            {
                WorkWay = talentInfos.Datas?.dynamicResumeInfo.Where(w => w.paramName.Equals("workWay")).Select(s => s.content).FirstOrDefault(),
                HighestDegree = talentInfos.Datas?.dynamicResumeInfo.Where(w => w.paramName.Equals("highestDegree")).Select(s => s.content).FirstOrDefault(),
                WorkExperience = talentInfos.Datas?.dynamicResumeInfo.Where(w => w.paramName.Equals("workExperience")).Select(s => s.content).FirstOrDefault(),
                DriveExperience = talentInfos.Datas?.dynamicResumeInfo.Where(w => w.paramName.Equals("driveExperience")).Select(s => s.content).FirstOrDefault(),
                BasicSituation = talentInfos.Datas?.dynamicResumeInfo.Where(w => w.paramName.Equals("basicSituation")).Select(s => s.content).FirstOrDefault(),
                Skill = talentInfos.Datas?.dynamicResumeInfo.Where(w => w.paramName.Equals("skill")).Select(s => s.content).FirstOrDefault(),
                IntentionCity = talentInfos.Datas?.dynamicResumeInfo.Where(w => w.paramName.Equals("intentionCity")).Select(s => s.content).FirstOrDefault(),
                IntentionJobCategory = talentInfos.Datas?.dynamicResumeInfo.Where(w => w.paramName.Equals("intentionJobCategory")).Select(s => s.content).FirstOrDefault(),
                JobHuntingStatus = talentInfos.Datas?.dynamicResumeInfo.Where(w => w.paramName.Equals("jobHuntingStatus")).Select(s => s.content).FirstOrDefault(),
                Certificate = talentInfos.Datas?.dynamicResumeInfo.Where(w => w.paramName.Equals("certificate")).Select(s => s.content).FirstOrDefault()
            },
            DynamicResumeInfoOriginal = talentInfos.data,
            DataTrafficSources = talentInfos.Datas?.trafficSources,
            DataUserCity = talentInfos.Datas?.userCity,
            DataLocationCitys = talentInfos.Datas?.LocationCitys ?? new LocationCity(),
            ExtInfoAgeMin = talentInfos.ExtInfos?.age?.min,
            ExtInfoAgeMax = talentInfos.ExtInfos?.age?.max,
            ExtInfoIntentionJob = talentInfos.ExtInfos?.intentionJob ?? new List<IntentionJob>(),
            ExtInfoIntentionCity = talentInfos.ExtInfos?.intentionCity ?? new List<IntentionCity>(),
            ExtInfoJobHuntingStatus = talentInfos.ExtInfos?.jobHuntingStatus,
            CreatedUser = _user.Id
        };
        _context.Add(kuaishou_Talent_Infos);
        //_logger.Info(SubscriptionKey.KuaishouResumeDelivery, "快招工投递-数据解析完毕", kuaishou_Talent_Infos.ApplicationId);
        try
        {
            _context.SaveChanges();

            //短信通知用户完善简历
            var hrId = string.Empty;
            if (!string.IsNullOrWhiteSpace(talentInfos.job.jobId))
                hrId = _context.Post_Team_Third_Jobid_Rel.Where(x => x.JobId == talentInfos.job.jobId).Select(s => s.Post_Team.Project_Team.HrId).FirstOrDefault();

            if (string.IsNullOrEmpty(hrId))
                hrId = Constants.PlatformHrId;

            var appletShortLink = await _weChatHelper.GetWxShortLinkForJump(ClientApps.SeekerApplet.AppID, "Mypackagedetail/informationacq/informationacq", $"adviser={hrId}&ksid={kuaishou_Talent_Infos.ApplicationId}");
            if (!string.IsNullOrWhiteSpace(appletShortLink))
            {
                appletShortLink = appletShortLink.Split('/')[^1];
                var str = $"【诺快聘】{Tools.FilterSpecialString(kuaishou_Talent_Infos.Name, 5)}先生/女士您好，感谢您在快手平台上向我们投递简历。我们已经收到您的申请，正根据公司的招聘需求和流程进行评估。请您莅临以下网审地址：https://wxaurl.cn/{appletShortLink} 完善您的简历，并密切关注通知。";
                var sdsms = new Sub_SendSms
                {
                    Id = -1,
                    // JsonData = ServiceStack.Text.JsonSerializer.SerializeToString(new { seeker = Tools.FilterSpecialString(kuaishou_Talent_Infos.Name, 5), jumpurl = appletShortLink }),
                    Mobile = kuaishou_Talent_Infos.Phone!,
                    // TempCode = "SMS_464485291",
                    Content = str,
                    Sender = SMSSender.庄点科技通用
                    // Sgin = "诺快聘"
                };

                MyRedis.Client.RPush(SubscriptionKey.SendSms, sdsms);
            }
        }
        catch (Exception ex)
        {
            if (ex.Message.IndexOf("Duplicate") >= 0)
            {
                Console.WriteLine($"Duplicate key{kuaishou_Talent_Infos.ApplicationId}");
                _logger.Info("testduplicatekey", "快招工投递", kuaishou_Talent_Infos.ApplicationId);
            }
        }

        //_logger.Info(SubscriptionKey.KuaishouResumeDelivery, "快招工投递-简历入公海池", kuaishou_Talent_Infos.RecommenderId);
        if (!kuaishou_Talent_Infos.RecommenderId.Equals("**********"))// 非河北招聘账号
        {
            var obj = new object();
            //MyRedis.Client.RPush(SubscriptionKey.KuaishouResumeDelivery, kuaishou_Talent_Infos.ApplicationId);
            MyRedis.Client.SAdd(SubscriptionKey.KuaishouResumeDelivery, kuaishou_Talent_Infos.ApplicationId);
            _logger.Info("add消息", kuaishou_Talent_Infos.ApplicationId, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "_" + Thread.CurrentThread.ManagedThreadId + "_" + obj.GetHashCode());
        }
        return result;
    }

    public EmptyResponse RelationHrAndKuaishouTalent(RelationRequest request)
    {
        // 加全局锁
        var lockerKey = $"st:ks:relation:{KuaishouConst.RELATION_CONST_LOCK_KEY_STR}";
        using var locker = MyRedis.Lock(lockerKey, 15).GetAwaiter().GetResult();

        if (locker == null)
            throw new BadRequestException("获取推送诺快聘锁失败");

        if (request.ApplicationIds == null || request.ApplicationIds.Count <= 0)
            throw new BadRequestException("简历id：ApplicationId，不能为空");

        // 开启事务
        using var transaction = _context.Database.BeginTransaction();
        // 简历信息
        var talentInfos = _context.Kuaishou_Talent_Infos.AsNoTracking().Where(w => request.ApplicationIds.Contains(w.ApplicationId)).ToList();
        //var talentInfos = _context.Kuaishou_Talent_Infos.Where(w => request.ApplicationIds.Contains(w.ApplicationId)).ToList();// 这种写法编辑的时候（关系存在）会循环引用
        var ksTalentInfos = JsonConvert.DeserializeObject<List<KsTalentInfo>>(JsonConvert.SerializeObject(talentInfos));// 简历信息 + 回访信息
        // 回访
        var projectPostInfos = new List<ProjectPostInfo>();
        try
        {
            RelationCheck(request);
            var relationInfos = _context.Kuaishou_Hr_Talent_Relations
                .Where(f => f.HrId == _user.Id && request.ApplicationIds.Contains(f.ApplicationId))
                .ToList();
            var Hrid = string.IsNullOrWhiteSpace(request.HrId) ? _user.Id : request.HrId; // 要转同事的信息
            var hrInfo = _context.User_Hr.FirstOrDefault(f => f.UserId == Hrid);
            if (relationInfos == null)
                relationInfos = new List<Kuaishou_Hr_Talent_Relations>();
            var hsHrExInfo = new HashSet<string>();// 保存已推送且要转同事的异常信息
            var hsSendedInfo = new HashSet<string>();// 保存已推送且还要推送的异常信息
            var hsSendedEdit = new HashSet<string>();// 保存已推送且还要编辑的异常信息
            var hsInvalidInfo = new HashSet<string>();// 推送无效简历的异常信息
            foreach (var id in request.ApplicationIds)
            {
                var relationInfo = relationInfos.FirstOrDefault(f => f.ApplicationId == id);
                if (relationInfo == null)
                {
                    relationInfo = new Kuaishou_Hr_Talent_Relations
                    {
                        ApplicationId = id,
                        CreatedUser = _user.Id,
                    };
                    _context.Add(relationInfo);
                }
                else
                {
                    relationInfo.UpdatedTime = DateTime.Now;
                    relationInfo.UpdatedUser = _user.Id;
                }
                relationInfo.ReturnVisit = ReturnVisit.已回访;
                relationInfo.HrId = Hrid;
                relationInfo.VisitMemo = request.VisitMemo ?? relationInfo.VisitMemo;// 不传就保持原来
                relationInfo.Result = request.Result.HasValue ? request.Result.Value : relationInfo.Result;
                var oldStatus = relationInfo.Status;
                relationInfo.Status = request.Status.HasValue ? request.Status.Value : relationInfo.Status;
                relationInfo.WaitToPhone = request.WaitToPhone.HasValue ? request.WaitToPhone.Value : relationInfo.WaitToPhone;
                relationInfo.InvalidType = request.InvalidType;
                if (request.InvalidType == "0")// 撤销无效简历
                {
                    relationInfo.VisitMemo = string.Empty;
                }
                // 合并信息
                var info = ksTalentInfos.FirstOrDefault(f => f.ApplicationId == id);
                info.KSHrTalentRelations = new KSHrTalentRelations
                {
                    HrId = Hrid,
                    HrName = hrInfo.NickName,
                    ApplicationId = id,
                    ReturnVisit = ReturnVisit.已回访,
                    VisitMemo = request.VisitMemo,
                    Result = relationInfo.Result,
                    InvalidType = relationInfo.InvalidType,
                    WaitToPhone = relationInfo.WaitToPhone,
                    Status = relationInfo.Status
                };
                // 已推送不能转同事（可以转给自己）
                if (oldStatus == KuaishouStatus.已推送 && !string.IsNullOrWhiteSpace(request.HrId) && request.HrId != _user.Id)
                    hsHrExInfo.Add(info.Name);
                // 已推送的不能再次推送
                if (oldStatus == KuaishouStatus.已推送 && request.Status.HasValue && request.Status == KuaishouStatus.已推送)
                    hsSendedInfo.Add(info.Name);
                // 已推送的不能再次编辑
                if (oldStatus == KuaishouStatus.已推送 && (request.Result.HasValue || request.WaitToPhone.HasValue || !string.IsNullOrWhiteSpace(request.VisitMemo)))
                    hsSendedEdit.Add(info.Name);
                // 无效简历不能推送
                if (relationInfo.Result == ResultStatus.无效 && request.Status == KuaishouStatus.已推送)
                    hsInvalidInfo.Add(info.Name);
            }

            if (hsHrExInfo.Count > 0)
                throw new BadRequestException($"联系人：{string.Join(',', hsHrExInfo)}，已推送，不能转同事");
            if (hsSendedInfo.Count > 0)
                throw new BadRequestException($"联系人：{string.Join(',', hsSendedInfo)}，已推送，不能再次推送");
            if (hsSendedEdit.Count > 0)
                throw new BadRequestException($"联系人：{string.Join(',', hsSendedEdit)}，已推送，不能进行其他操作");
            if (hsInvalidInfo.Count > 0)
                throw new BadRequestException($"联系人：{string.Join(',', hsInvalidInfo)}，存在无效简历，不能推送");
            // 推送失败，先保存跟进信息，先不用这个逻辑，用的时候要注意status状态控制
            //_context.SaveChanges();
            //transaction.CreateSavepoint("SaveBaseData");

            // 推送
            if (request.Status == KuaishouStatus.已推送)
            {
                if (string.IsNullOrWhiteSpace(request.TeamPostId))
                    throw new BadRequestException("职位id：TeamPostId，不能为空");
                CheckPostValid(request.TeamPostId);

                var exs = new List<PostOrderExceptionMessage>();// 保存推送下单时异常信息
                foreach (var ksTalentInfo in ksTalentInfos)
                {
                    // 公海池推送校验昵称 20231226
                    // 昵称超过四个字，不能推送
                    if (ksTalentInfo.Name.Length > 4)
                    {
                        throw new BadRequestException($"不合规的昵称(长度超过4个字符):{ksTalentInfo.Name}");
                    }
                    // 昵称非中文，不能推送
                    if (!NotChineseCharacters().IsMatch(ksTalentInfo.Name))
                    {
                        throw new BadRequestException($"不合规的昵称(存在非中文字符):{ksTalentInfo.Name}");
                    }
                    // 1.关联职位,职位关系是否存在，不存在则添加 -- 这个逻辑暂时去掉
                    var postInfo = _context.Post_Team.Include(i => i.Post).AsNoTracking().FirstOrDefault(f => f.TeamPostId == request.TeamPostId);// 职位信息

                    // 推送的时候更新最终推送职位
                    var talentInfo = _context.Kuaishou_Talent_Infos.FirstOrDefault(f => f.ApplicationId == ksTalentInfo.ApplicationId);
                    talentInfo.SendTeamPostId = request.TeamPostId;
                    talentInfo.SendTeamPostName = postInfo.Post.Name;
                    talentInfo.SendPostId = postInfo.Post.PostId;
                    _context.Attach(talentInfo);
                    ksTalentInfo.SendTeamPostId = request.TeamPostId;

                    // 2.简历投进简历池
                    var resume = new Resume_Buffer()
                    {
                        ResumeId = ksTalentInfo.ApplicationId,
                        Source = ThirdPlatType.快招工,
                        HrId = ksTalentInfo.KSHrTalentRelations.HrId,
                        HrName = ksTalentInfo.KSHrTalentRelations.HrName,
                        Name = ksTalentInfo.Name ?? string.Empty,
                        Sex = ksTalentInfo.GenderName,
                        Mobile = ksTalentInfo.Phone ?? string.Empty,
                        Age = ksTalentInfo.Age,
                        PostId = request.TeamPostId,
                        PostName = postInfo.Post.Name,
                        TopEducation = ksTalentInfo.DataDynamicResumeInfo.HighestDegree,
                        ApplyTime = ksTalentInfo.ApplyTime,
                        Avatar = "",// todo:第三方肯定不会提供图床url，应该有地方下载或者使用默认头像
                        RegionId = ksTalentInfo.DataLocationCitys?.adCode,
                        Location = ksTalentInfo.DataUserCity,
                        LocationPoint = new NetTopologySuite.Geometries.Point(Convert.ToDouble(ksTalentInfo.DataLocationCitys.longitude), Convert.ToDouble(ksTalentInfo.DataLocationCitys.latitude)),
                        Hopes = GetHopesFromTalentInfo(ksTalentInfo),
                        Educations = GetEducationFromTalentInfo(ksTalentInfo),
                        Works = GetWorksFromTalentInfo(ksTalentInfo)
                    };
                    _context.Add(resume);

                    // 3.下单
                    ksTalentInfo.ResumeBufferId = resume.Id;
                    ksTalentInfo.Education = GetMappingEducation(ksTalentInfo.DataDynamicResumeInfo.HighestDegree);
                    // 这里的channelhrid都是user表userid,user_hr,user_seeker共用一个
                    var channelId = GetChannelInfo(ksTalentInfo.KSHrTalentRelations.HrId, "1");
                    ksTalentInfo.ChannelId = channelId;
                    ksTalentInfo.ChannelHrid = ksTalentInfo.KSHrTalentRelations.HrId;// 
                    ksTalentInfo.ChannelHrName = ksTalentInfo.KSHrTalentRelations.HrName;
                    var projectPostInfo = new ProjectPostInfo();
                    var ex = new PostOrderExceptionMessage();
                    projectPostInfo.PostName = postInfo.Post.Name;

                    _ksPostOrder.SaveToOrderTable(ksTalentInfo, projectPostInfo, ex);
                    projectPostInfos.Add(projectPostInfo);
                    if (ex.ExceptionMessage.Count > 0)
                        exs.Add(ex);
                }
                // 存在下单异常
                if (exs.Count > 0)
                {
                    var sb = new StringBuilder();
                    foreach (var item in exs)
                        sb.AppendLine($"{item.SeekerName}:{string.Join(",", item.ExceptionMessage)}");
                    throw new BadRequestException(sb.ToString());
                }
            }
            _context.SaveChanges();
            transaction.Commit();
        }
        catch (Exception)
        {
            //transaction.RollbackToSavepoint("SaveBaseData");
            //transaction.Commit();
            transaction.Rollback();
            throw;
        }

        // 求职者信息完善 - 异步处理,放在事务提交以后
        foreach (var ksTalentInfo in ksTalentInfos)
        {
            if (ksTalentInfo.newSeeker)
            {
                _commonUserService.SetAdviser(ksTalentInfo.SeekerId, "1");
            }
        }
        return new EmptyResponse();
    }

    /// <summary>
    /// 获取渠道关系
    /// </summary>
    /// <param name="channelUserId"></param>
    /// <param name="hrId"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    private string? GetChannelInfo(string channelUserId, string hrId)
    {
        return _context.Qd_Hr_Channel.FirstOrDefault(f => f.ChannelUserId == channelUserId && f.HrId == hrId)?.Id;
    }

    private EducationType? GetMappingEducation(string? highestDegree)
    {
        if (highestDegree == null)
            return null;
        var baseInfo = _context.Dic_Base_Info.FirstOrDefault(f => f.Type == "kgz_nkp_education_enum_map" && f.Code == highestDegree);
        if (baseInfo == null)
            return null;
        System.Enum.TryParse(typeof(EducationType), baseInfo.Value, out object? result);
        return result == null ? null : (EducationType)result;
    }

    /// <summary>
    /// 回访校验
    /// </summary>
    /// <param name="request"></param>
    /// <exception cref="BadRequestException"></exception>
    private void RelationCheck(RelationRequest request)
    {
        // 已回访且跟进人非本人则无法操作
        var applicationIds = request.ApplicationIds;
        var rels = _context.Kuaishou_Hr_Talent_Relations.Include(i => i.User_Hr).Where(w => applicationIds.Contains(w.ApplicationId)).ToList();
        var hs = new HashSet<string>();
        foreach (var r in rels)
            if (!r.HrId.Equals(_user.Id))
                hs.Add(r.User_Hr.NickName);
        if (hs.Count > 0)
            throw new BadRequestException($"已被其他同事：{string.Join(',', hs)} 跟进");

        // 同一个手机号不能重复推送
        if (request.Status == KuaishouStatus.已推送)
        {
            var kc = _context.Kuaishou_Talent_Infos.Where(w => applicationIds.Contains(w.ApplicationId)).GroupBy(g => g.Phone).Select(s => new { K = s.Key, C = s.Count() });
            var sb = new StringBuilder();
            foreach (var item in kc)
                if (item.C > 1)
                    sb.AppendLine($"手机号：{item.K},重复推送");
            if (sb.Length > 0)
                throw new BadRequestException(sb.ToString());
        }
    }

    /// <summary>
    /// 校验职位是否下架，项目是否归档，库存是否有剩余
    /// </summary>
    /// <param name="teamPostId"></param>
    /// <exception cref="NotImplementedException"></exception>
    private void CheckPostValid(string teamPostId)
    {
        var postInfo = _context.Post_Team.Include(i => i.Project_Team).Where(f => f.TeamPostId == teamPostId)
        .Select(s => new
        {
            s.Post.LeftStock,
            PostId = s.PostId,
            Status = s.Status,
            ProjectTeamStatus = s.Project_Team.Status,
        }).FirstOrDefault();

        if (postInfo == null)
            throw new BadRequestException($"职位已被删除");
        if (postInfo.Status != PostStatus.发布中)
            throw new BadRequestException($"职位已关闭");
        else if (postInfo.ProjectTeamStatus == ProjectStatus.归档)
            throw new BadRequestException($"项目已归档");

        // 校验库存
        if (postInfo.LeftStock <= 0)
            throw new BadRequestException($"交付数量剩余0，无法投递");
    }

    public ResumeBufferEducations GetEducationFromTalentInfo(KsTalentInfo talentInfo)
    {
        return new ResumeBufferEducations();
    }

    public ResumeBufferHopes GetHopesFromTalentInfo(KsTalentInfo talentInfo)
    {
        return new ResumeBufferHopes();
    }

    public ResumeBufferWorks GetWorksFromTalentInfo(KsTalentInfo talentInfo)
    {
        return new ResumeBufferWorks();
    }

    public List<TeamHrResponse> GetTeamWorkers()
    {
        return _context.User_Hr
            //.Where(w => w.Powers.Exists(e => e.Equals("kuaishou"))) // 做了值转换，不知道咋写条件
            .FromSqlRaw($"select * from user_hr where powers like '%kuaishou%'")
            .Select(s => new TeamHrResponse
            {
                HrId = s.UserId,
                HrName = s.NickName
            }).ToList();
    }

    public PostTeamInfoPageListResponse GetPostsByStr(PostTeamPageListRequest request)
    {
        //string entId = string.Empty;
        //if (!_hostingEnvironment.IsProduction())
        //    entId = "100989262137917573";
        //else
        //    entId = "";

        var response = new PostTeamInfoPageListResponse();
        var predicate = PredicateBuilder.New<Post_Team>(true);

        if (!string.IsNullOrWhiteSpace(request.Search))
        {
            if (request.Search.StartsWith(Constants.ProjectIdPre) && int.TryParse(request.Search.Substring(1), out var id))
                predicate = predicate.And(x => x.Project_Team.Project.AutoId == id || x.Post.Name.IndexOf(request.Search) >= 0);
            else
                predicate = predicate.And(x => x.Post.Name.IndexOf(request.Search) >= 0);
        }
        predicate = predicate.And(x => x.Project_Team.HrId == "1"
        && x.Project_Team.Project.User_Hr.UserId != null
        && x.Status == PostStatus.发布中
        && x.Show == true
        && x.Project_Team.Status != ProjectStatus.归档);
        // 置顶teamPostId
        string teamPostId = string.Empty;
        if (request.ApplicationIds != null && request.ApplicationIds.Count > 0)
        {
            var rel = _context.Post_Team_Third_Jobid_Rel.Where(w => request.ApplicationIds.Contains(w.Kuaishou_Talent_Infos.ApplicationId)).Distinct().OrderByDescending(o => o.Id).FirstOrDefault();
            teamPostId = rel?.TeamPostId ?? string.Empty;
        }
        var sql = _context.Post_Team.Where(predicate);
        response.Total = sql.Select(s => new { a = 1 }).Count();
        var result = sql
            .OrderByDescending(o => o.TeamPostId == teamPostId)
            .ThenByDescending(o => o.CreatedTime)
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(s => new PostTeamInfo
            {
                HrName = s.Project_Team.Project.User_Hr.NickName,
                Phone = s.Project_Team.Project.User_Hr.User.Mobile,
                ProjectName = s.Project_Team.Project.Agent_Ent.Name,
                ProjectCode = $"{Constants.ProjectIdPre}{s.Project_Team.Project.AutoId.ToString().PadLeft(6, '0')}",
                ProjectCreateTime = s.Project_Team.Project.CreatedTime,
                TeamPostId = s.TeamPostId,
                PostName = s.Post.Name,
                Describe = s.Post.Describe,
                RecruitNumber = s.Post.DeliveryNumber,
                LeftStock = s.Post.LeftStock,
                WorkNature = s.Post.WorkNature.ToString(),
                WorkNatureType = s.Post.WorkNature,
                GraduationYear = s.Post.GraduationYear,
                RegionId = s.Post.RegionId,
                City = _commonDicService.GetCityById(s.Post.RegionId).CityName,
                CategoryName = s.Post.Dic_Post.Name,
                //Location = s.Post.Location,
                Address = s.Post.Address,
                MinSalary = s.Post.MinSalary,
                MaxSalary = s.Post.MaxSalary,
                Salary = s.Post.Salary,
                SalaryType = s.Post.SalaryType,
                Education = s.Post.Education.ToString(),
                Welfare = s.Post.Welfare,
                Sex = s.Post.Sex.ToString(),
                MinAge = s.Post.MinAge,
                MaxAge = s.Post.MaxAge
            })
            .ToList();

        // 薪酬描述获取
        foreach (var item in result)
            item.SalaryName = Tools.FormattingSalary(item.MinSalary, item.MaxSalary, item.SalaryType, item.WorkNatureType, item.Salary);
        response.Rows = result;
        return response;
    }

    public TalentInfoEditResponse GetKuaiShouTalentInfoForEditPage(string applicationId)
    {
        var ksInfo = _context.Kuaishou_Talent_Infos.Include(i => i.Kuaishou_Hr_Talent_Relations).FirstOrDefault(f => f.ApplicationId == applicationId);
        var talentInfoEditResponse = new TalentInfoEditResponse
        {
            Name = ksInfo.Name,
            Age = ksInfo.Age,
            GenderName = ksInfo.GenderName,
            Phone = ksInfo.Phone,
            ChannelName = ksInfo.ChannelName,
            IntentionCity = ksInfo.ExtInfoIntentionCity.FirstOrDefault()?.cityName,
            IntentionJob = ksInfo.ExtInfoIntentionJob.FirstOrDefault()?.content,
            Education = ksInfo.DataDynamicResumeInfo?.HighestDegree,
            UpdatedTime = ksInfo.Kuaishou_Hr_Talent_Relations != null ? ksInfo.Kuaishou_Hr_Talent_Relations.UpdatedTime : ksInfo.CreatedTime,
            VisitMemo = ksInfo.Kuaishou_Hr_Talent_Relations != null ? ksInfo.Kuaishou_Hr_Talent_Relations.VisitMemo : string.Empty,
            Result = ksInfo.Kuaishou_Hr_Talent_Relations != null ? ksInfo.Kuaishou_Hr_Talent_Relations.Result : ResultStatus.有效,
            Status = ksInfo.Kuaishou_Hr_Talent_Relations != null ? ksInfo.Kuaishou_Hr_Talent_Relations.Status : KuaishouStatus.未推送,
            WaitToPhone = ksInfo.Kuaishou_Hr_Talent_Relations != null ? ksInfo.Kuaishou_Hr_Talent_Relations.WaitToPhone : WaitToPhone.不是,
            InvalidType = ksInfo.Kuaishou_Hr_Talent_Relations != null ? ksInfo.Kuaishou_Hr_Talent_Relations.InvalidType : "0",
            HrId = ksInfo.Kuaishou_Hr_Talent_Relations != null ? ksInfo.Kuaishou_Hr_Talent_Relations.HrId : null
        };
        if (!string.IsNullOrWhiteSpace(talentInfoEditResponse.HrId))
        {
            var userInfo = _context.User_Hr
                .Select(s => new
                {
                    s.NickName,
                    s.UserId,
                    s.Avatar,
                })
                .FirstOrDefault(f => f.UserId == talentInfoEditResponse.HrId);
            talentInfoEditResponse.HrName = userInfo.NickName;
            talentInfoEditResponse.Avatar = userInfo.Avatar;
        }
        // 招聘流程
        var rb = _context.Resume_Buffer.FirstOrDefault(f => f.Source == ThirdPlatType.快招工 && f.ResumeId == applicationId);
        if (rb != null)
        {
            var pt = _context.Post_Bounty.FirstOrDefault(f => f.Source == TeamBountySource.快招工 && f.ResumeBufferId == rb.Id);
            if (pt != null)
                talentInfoEditResponse.RecruitId = pt.RecruitId;
        }
        // 基本信息
        var baseInfos = new List<EnumInfo>
        {
            new EnumInfo { name = "求职状态", value = ((ExtInfoJobHuntingStatus)(ksInfo.ExtInfoJobHuntingStatus ?? 1)).GetDescription() },
            !string.IsNullOrWhiteSpace(ksInfo.DataDynamicResumeInfo?.HighestDegree) ? new EnumInfo { name = "学历", value = ksInfo.DataDynamicResumeInfo.HighestDegree} : null,
            !string.IsNullOrWhiteSpace(ksInfo.ExtInfoIntentionJob.FirstOrDefault()?.content) ? new EnumInfo { name = "意向岗位", value = ksInfo.ExtInfoIntentionJob.FirstOrDefault()?.content!} : null,
            !string.IsNullOrWhiteSpace(ksInfo.ExtInfoIntentionCity.FirstOrDefault()?.cityName) ? new EnumInfo { name = "意向城市", value = ksInfo.ExtInfoIntentionCity.FirstOrDefault()?.cityName! } : null,
            //new EnumInfo { name = "拥有技能", value = "" },
            //new EnumInfo { name = "拥有证书", value = "" },
            !string.IsNullOrWhiteSpace(ksInfo.DataDynamicResumeInfo?.WorkWay) ? new EnumInfo { name = "工作方式", value = ksInfo.DataDynamicResumeInfo.WorkWay!} : null,
            !string.IsNullOrWhiteSpace(ksInfo.DataLocationCitys?.address) ? new EnumInfo { name = "用户地区", value = ksInfo.DataLocationCitys?.address!} : null,
            !string.IsNullOrWhiteSpace(ksInfo.JobName) ? new EnumInfo { name = "投递职位", value = ksInfo.JobName!} : null,
            !string.IsNullOrWhiteSpace(ksInfo.CompanyBusinessName) ? new EnumInfo { name = "投递公司", value = ksInfo.CompanyBusinessName!} : null,
        };
        baseInfos.RemoveAll(r => r == null);
        var resumeFeedBack = new List<EnumInfo>
        {
            new EnumInfo { name = "年龄是否匹配", value = ksInfo.DataQualityLabel.Age ?? "不匹配" },
            new EnumInfo { name = "意向城市是否匹配", value = ksInfo.DataQualityLabel.IntentionCity ?? "不匹配" },
            new EnumInfo { name = "意向工作是否匹配", value = ksInfo.DataQualityLabel.IntentionJobCategory ?? "不匹配" },
            new EnumInfo { name = "最近是否多次申请", value = ksInfo.DataQualityLabel.MultiApplyRecently ?? "不匹配" },
            new EnumInfo { name = "居住城市是否匹配", value = ksInfo.DataQualityLabel.LiveCity ?? "不匹配" },
            new EnumInfo { name = "流量来源", value = ksInfo.DataChannelSourceName ?? "不匹配" },
        };
        talentInfoEditResponse.BaseInfos = baseInfos;
        talentInfoEditResponse.ResumeFeedBack = resumeFeedBack;
        return talentInfoEditResponse;
    }

    public List<EnumInfo> GetInValidMemo()
    {
        List<EnumInfo> enums = new();
        var baseInfo = _commonDicService.GetBaseInfo(baseType);
        baseInfo.ForEach(f =>
        {
            enums.Add(new EnumInfo { name = f.Code, value = f.Value });
        });
        return enums;
    }

    public List<EnumInfo> GetValidMemo()
    {
        List<EnumInfo> enums = new();
        var baseInfo = _commonDicService.GetBaseInfo(ValidMemoBaseType);
        baseInfo.ForEach(f =>
        {
            enums.Add(new EnumInfo { name = f.Code, value = f.Value });
        });
        return enums;
    }

    public PostSendToKuaishouResponse GetPostSendToKuaishou(PostSendToKuaishouRequest model)
    {
        // 加全局锁
        var lockerKey = $"st:ks:export:{KuaishouConst.EXPORT_EXCEL_CONST_LOCK_KEY_STR}";
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("获取导出文件锁失败");

        var predicate = PredicateBuilder.New<Post_Team_Third_Jobid_Rel>(true);
        if (model.BeginDate != null)
            predicate = predicate.And(x => x.CreatedTime >= model.BeginDate);
        if (model.EndDate != null)
            predicate = predicate.And(x => x.CreatedTime <= model.EndDate);

        var result = _context.Post_Team_Third_Jobid_Rel
            .Where(predicate)
            .OrderByDescending(o => o.CreatedTime)
            .Select(s => new PostSendToKuaishou
            {
                PostType = s.Post_Team.Post.WorkNature.ToString(),
                WorkNature = s.Post_Team.Post.WorkNature,
                Company = s.Post_Team.Project_Team.Project.Agent_Ent.Name,
                CompanyType = s.Post_Team.Project_Team.Project.Agent_Ent.Nature.GetDescription(),
                PostName = s.Post_Team.Post.Name,
                RecruitNumber = s.Post_Team.Post.DeliveryNumber,
                Education = s.Post_Team.Post.Education.GetDescription(),
                MinAge = s.Post_Team.Post.MinAge,
                MaxAge = s.Post_Team.Post.MaxAge,
                WorkExperience = s.Post_Team.Post.GraduationYear > 0 ? $"{DateTime.Now.Year - s.Post_Team.Post.GraduationYear}年" : "无",// 工作经验：当前年份 - 要求毕业年份
                Sex = s.Post_Team.Post.Sex == null ? "不限" : s.Post_Team.Post.Sex.ToString(),
                MinSalary = s.Post_Team.Post.MinSalary,
                MaxSalary = s.Post_Team.Post.MaxSalary,
                Salary = s.Post_Team.Post.Salary,
                SalaryType = s.Post_Team.Post.SalaryType,
                Describe = s.Post_Team.Post.Describe,
                Address = s.Post_Team.Post.Address,
                Welfares = s.Post_Team.Post.Welfare
            }).ToList();

        int sort = 0;
        foreach (var item in result)
        {
            item.Sort = ++sort;
            item.SalaryName = Tools.FormattingSalary(item.MinSalary, item.MaxSalary, item.SalaryType, item.WorkNature, item.Salary);
            item.AgeRange = GetAgeRange(item.MinAge, item.MaxAge);
            item.Welfare = GetWelfare(item.Welfares);
        }
        var response = new PostSendToKuaishouResponse();
        response.Rows = result;
        return response;
    }

    private string GetAgeRange(int? minAge, int? maxAge)
    {
        string ageRange = "不限";
        if (minAge == 0 && maxAge == 0)
            ageRange = "不限";
        else if (minAge > 0 && maxAge == 0)
            ageRange = $"{minAge}岁以上";
        else if (maxAge > 0 && minAge == 0)
            ageRange = $"{maxAge}岁以下";
        else
            ageRange = $"{minAge}至{maxAge}岁";
        return ageRange;
    }

    private string GetWelfare(List<WelfareModel> welfares)
    {
        string wareFare = string.Empty;
        if (welfares != null && welfares.Count > 0)
        {
            var names = welfares.Select(s => s.Name).ToList();
            wareFare = string.Join(',', names);
        }
        return wareFare;
    }

    public EmptyResponse EditKuaiShouTalent(EditKuaiShouTalent request)
    {
        var resume = _context.Kuaishou_Talent_Infos.FirstOrDefault(x => x.ApplicationId == request.ApplicationId)
        ?? throw new BadRequestException("内容不存在");

        if (resume.SendTeamPostId != null)
            throw new BadRequestException("该简历已推送，禁止修改");

        if (!string.IsNullOrWhiteSpace(request.Name))
            resume.Name = request.Name;

        if (!string.IsNullOrWhiteSpace(request.Phone))
            resume.Phone = request.Phone;

        if (request.Age != null)
            resume.Age = request.Age.Value;

        if (request.Education.HasValue)
        {
            resume.DataDynamicResumeInfo ??= new DataDynamicResumeInfo();
            resume.DataDynamicResumeInfo.HighestDegree = request.Education.GetDescription();
        }

        if (request.GenderCode.HasValue)
        {
            resume.GenderCode = (int)request.GenderCode;
            resume.GenderName = request.GenderCode.GetDescription();
        }

        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 仅中文字符
    /// </summary>
    /// <returns></returns>
    [GeneratedRegex(@"^[\u4e00-\u9FFF]+$")]
    private static partial Regex NotChineseCharacters();
}


