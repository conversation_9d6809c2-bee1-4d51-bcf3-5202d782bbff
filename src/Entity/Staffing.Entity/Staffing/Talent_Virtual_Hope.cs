﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 虚拟人才库求职期望
/// </summary>
[Table("talent_virtual_hope")]
public class Talent_Virtual_Hope
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 虚拟人才库id
    /// </summary>
    public string VirtualId { get; set; } = string.Empty;

    /// <summary>
    /// 期望城市
    /// </summary>
    public string HopeCity { get; set; } = string.Empty;

    /// <summary>
    /// 地区Id
    /// </summary>
    public string RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 行业名称（多个）
    /// </summary>
    public string IndustryName { get; set; } = string.Empty;

    /// <summary>
    /// 职位名称（多个）
    /// </summary>
    public string PostName { get; set; } = string.Empty;

    /// <summary>
    /// 最低薪资
    /// </summary>
    public decimal MinSalary { get; set; } = 0;

    /// <summary>
    /// 最高薪资
    /// </summary>
    public decimal MaxSalary { get; set; } = 0;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Talent_Virtual Talent_Virtual { get; set; } = default!;
}

