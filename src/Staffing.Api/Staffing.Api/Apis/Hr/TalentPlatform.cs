using Config.CommonModel.Business;
using Infrastructure.Extend;
using Staffing.Model.Hr.TalentPlatform;
using Staffing.Core.Interfaces.Hr;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class TalentPlatformRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("talentplatform").WithTags("平台人才库");
        group.MapPost("/list", (TalentPlatformListRequest model, ITalentPlatformService rep) => rep.GetTalentPlatformList(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取平台人才库列表" });

        group.MapGet("/resume/{platformId}", async (string platformId, ITalentPlatformService rep) => await rep.GetTalentPlatformDetails(platformId))
            .WithOpenApi(operation => new(operation) { Summary = "获取平台人才库简历详情" });

        group.MapGet("/resume/user/{userId}", async (string userId, ITalentPlatformService rep) => await rep.GetResumeDetails(userId))
            .WithOpenApi(operation => new(operation) { Summary = "获取简历详情（根据用户Id）" });

        group.MapPost("/del", async (DeleteTalentPlatformRequest model, ITalentPlatformService rep) => await rep.DeleteTalentPlatform(model))
            .WithOpenApi(operation => new(operation) { Summary = "删除平台人才库" });

        group.MapPost("/comment/list", (TalentPlatformCommentListRequest model, ITalentPlatformService rep) => rep.GetTalentPlatformCommentList(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取平台人才库简历评论列表" });

        group.MapPost("/comment", (AddTalentPlatformCommentRequest model, ITalentPlatformService rep) => rep.AddTalentPlatformComment(model))
            .WithOpenApi(operation => new(operation) { Summary = "添加虚拟人才库简历评论" });

        group.MapDelete("/comment/{commentId}", (string commentId, ITalentPlatformService rep) => rep.DeleteTalentPlatformComment(commentId))
            .WithOpenApi(operation => new(operation) { Summary = "删除平台人才库简历评论" });

        group.MapGet("/platformlabel/{platformId}", (string platformId, ITalentPlatformService rep) => rep.GetTalentPlatformLabel(platformId))
            .WithOpenApi(operation => new(operation) { Summary = "获取平台人才库标签" });

        group.MapPost("/platformlabel", async (EditTalentPlatformLabelRequest model, ITalentPlatformService rep) => await rep.EditTalentVirtualLabel(model))
            .WithOpenApi(operation => new(operation) { Summary = "变更平台人才库标签" });

        group.MapGet("/resume/down/{id}", (string id, ITalentPlatformService rep) => rep.DownLoadResume(id))
            .WithOpenApi(operation => new(operation) { Summary = "下载用户信息简历" });

        group.MapGet("/channel", (ITalentPlatformService rep) => rep.GetChannelInfo())
            .WithOpenApi(operation => new(operation) { Summary = "获取渠道商列表信息" });
    }
}