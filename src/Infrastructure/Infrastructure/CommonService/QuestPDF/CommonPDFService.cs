﻿using Config;
using Config.CommonModel.QuestPDF;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Staffing.Entity.Staffing;

namespace Infrastructure.CommonService.QuestPDF;

[Service(ServiceLifetime.Scoped)]
public class CommonPDFService
{
    private readonly Noah.Aliyun.Storage.IObjectStorage _objectStorageService;
    private readonly LogManager _log;

    public CommonPDFService(Noah.Aliyun.Storage.IObjectStorage objectStorageService, LogManager log)
    {
        _objectStorageService = objectStorageService;
        _log = log;
    }

    /// <summary>
    /// 生成服务协议(合同)pdf，并返回oss地址
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<string> GenerateContractPdfToOssAsync(ContractModel model)
    {
        string ossPath = string.Empty;
        try
        {
            var document = new CreateContractDocument(model);
            var fileBytes = PDFHelper.GeneratePdfToByte(document);

            // 将文件上传到OSS
            string fileName = (model.FileName ?? DateTime.Now.Ticks + "") + ".pdf";
            ossPath = await UploadToOssAsync(fileBytes, fileName, "bountypdf", model.Id, "BountyPDF");
        }
        catch (Exception ex)
        {
            _log.Error($"生成服务协议(合同)pdf出错", ex.Message, model.ContractNumber);
        }

        return ossPath;
    }

    /// <summary>
    /// 生成服务协议(合同)pdf，并返回oss地址
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<string> GenerateContractPdfToOss2Async(ContractModel2 model)
    {
        string ossPath = string.Empty;
        var document = new CreateContractDocument2(model);
        var fileBytes = PDFHelper.GeneratePdfToByte(document);

        // 将文件上传到OSS
        string fileName = (model.FileName ?? DateTime.Now.Ticks + "") + ".pdf";
        ossPath = await UploadToOssAsync(fileBytes, fileName, "bountypdf", model.Id, "BountyPDF");

        return ossPath;
    }

    /// <summary>
    /// 文件上传至OSS
    /// </summary>
    /// <param name="fileBytes">文件字节数组</param>
    /// <param name="fileName">文件名称</param>
    /// <param name="fileDir">文件目录</param>
    /// <param name="businessTableId">业务id</param>
    /// <param name="businessType">业务类型</param>
    /// <returns></returns>
    private async Task<string> UploadToOssAsync(byte[] fileBytes, string fileName, string fileDir, string? businessTableId, string businessType)
    {
        string ossPath = await _objectStorageService.OrdinaryUploadFile(fileBytes, fileName, fileDir);
        return ossPath;
    }
}
