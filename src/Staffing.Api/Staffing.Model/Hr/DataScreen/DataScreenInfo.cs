﻿using Config.CommonModel.DataScreen;

namespace Staffing.Model.Hr.DataScreen;

public class GetHRDataQuery
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }
}

public class GetEntOrgDataQuery
{
    /// <summary>
    /// 组织架构（必填）
    /// </summary>
    public long? OrgId { get; set; }
}


/// <summary>
/// 平台级别统计数据
/// </summary>
public class DSPlatDataResponse
{
    public DSPlatDataResponse(bool IsInit)
    {
        if (IsInit)
        {
            Info = new DataScreenInfoResponse();
            AdvTalRanking = new List<AdvTalRankingInfo>();
            EntTalRanking = new List<EntTalRankingInfo>();
            EntAdvRanking = new List<EntAdvRankingInfo>();
            RegionEnt = new List<string>();
        }
    }
    public DataScreenInfoResponse Info { get; set; } = default!;
    
    /// <summary>
    /// 顾问人才储备TOP榜
    /// </summary>
    public List<AdvTalRankingInfo>? AdvTalRanking { get; set; }

    /// <summary>
    /// 人才储备最大的机构
    /// </summary>
    public List<EntTalRankingInfo>? EntTalRanking { get; set; }

    /// <summary>
    /// 顾问规模最大的机构
    /// </summary>
    public List<EntAdvRankingInfo>? EntAdvRanking { get; set; }

    /// <summary>
    /// 涉及地区名称列
    /// </summary>
    public List<string>? RegionEnt { get; set; }

    /// <summary>
    /// 各城市项目数量
    /// </summary>
    public List<RegionProjectInfo> CityProjectList { get; set; } = new List<RegionProjectInfo>();
}

#region 数据大屏 平台级-招聘流程

public class DSPlatRecruitDataResponse 
{
    /// <summary>
    /// Hr初筛
    /// </summary>
    public int RecruitHrScreening { get; set; }

    /// <summary>
    /// 面试安排(面试官筛选)
    /// </summary>
    public int RecruitInterviewerScreening { get; set; }

    /// <summary>
    /// 面试进行中
    /// </summary>
    public int RecruitInterview { get; set; }

    /// <summary>
    /// Offer
    /// </summary>
    public int RecruitOffer { get; set; }

    /// <summary>
    /// 入职
    /// </summary>
    public int RecruitInduction { get; set; }

    /// <summary>
    /// 不合适(归档)
    /// </summary>
    public int RecruitFileAway { get; set; }

    /// <summary>
    /// 面试官概况
    /// </summary>
    public DSPlatInterviewerOfRecruitInfo? DSPlatInterviewerOfRecruitInfo { get; set; }

    /// <summary>
    /// 投递实时播报
    /// </summary>
    public List<DSPlatDeliveryTop30Info>? DSPlatDeliveryTop30Infos { get; set; }

    #region 历史概况

    /// <summary>
    /// 累计投递用户数
    /// </summary>
    public int RecruitDelivery { get; set; }

    /// <summary>
    /// 累计联系
    /// =========此字段暂定固定为0，后期再补充==============
    /// </summary>
    public int RecruitContactTotal { get; set; } = 0;

    /// <summary>
    /// 累计面试
    /// </summary>
    public int RecruitInterviewTotal { get; set; }

    /// <summary>
    /// 累计入职
    /// </summary>
    public int RecruitInductionTotal { get; set; }

    #endregion

    #region 联系概况



    #endregion

    #region 投递概况

    public DSPlatDeliverySumInfo? DSPlatDeliverySumInfo { get; set; }

    #endregion

    #region 合同概况


    #endregion

    #region 处理效率

    /// <summary>
    /// 注册转化率
    /// </summary>
    public decimal? RegisterPercent { get; set; } = 0.5m;

    /// <summary>
    /// 投递转化率
    /// </summary>
    public decimal? DeliveryPercent { get; set; } = 0.5m;

    /// <summary>
    /// 反馈转化率
    /// </summary>
    public decimal? ReturnPercent { get; set; } = 0.5m;

    /// <summary>
    /// 入职转化率
    /// </summary>
    public decimal? EntryPercent { get; set; } = 0.5m;

    /// <summary>
    /// 入职转化率
    /// </summary>
    public decimal? ContractPercent { get; set; } = 0.5m;

    #endregion
}

#endregion

#region 数据大屏 平台级-用户概况

public class DSPlatSeekerDataResponse
{
    /// <summary>
    /// 今日登录
    /// </summary>
    public int LoginedNum { get; set; }

    /// <summary>
    /// 今日新注册
    /// </summary>
    public int NewRegisterNum { get; set; }

    /// <summary>
    /// 用户注册来源
    /// </summary>
    public List<UserSourceResponse>? RegisterResource { get; set; }

    /// <summary>
    /// 平台简历储备数量
    /// </summary>
    public int ResumeNum { get; set; }

    /// <summary>
    /// 平台人才储备数量
    /// </summary>
    public int TalentNum { get; set; }

    /// <summary>
    /// 访问次数
    /// </summary>
    public int VisitNum { get; set; }

    /// <summary>
    /// 访客数(UV)
    /// </summary>
    public int VisiterNum { get; set; }

    /// <summary>
    /// 用户性别概况
    /// </summary>
    public List<DSPlatSeekerSexResponse>? SexInfo { get; set; }

    /// <summary>
    /// 用户学历概况
    /// </summary>
    public List<DSPlatSeekerEducationResponse>? EducationInfo { get; set; }

    /// <summary>
    /// 用户年龄概况
    /// </summary>
    public List<DSPlatSeekerAgeResponse>? AgeInfo { get; set; }

    /// <summary>
    /// 行业人才储备Top5
    /// </summary>
    public List<DSPlatSeekerIndustryInfo>? IndustryTop5 { get; set; }

    /// <summary>
    /// 求职意向职位Top5
    /// </summary>
    public List<DSPlatSeekerHopePostInfo>? HopePostTop5 { get; set; }

    /// <summary>
    /// 地区人才储备
    /// </summary>
    public List<DSPlatSeekerAreaInfo>? AreaInfos { get; set; }
}

/// <summary>
/// 用户性别概况
/// </summary>
public class DSPlatSeekerSexResponse
{
    /// <summary>
    /// 性别
    /// </summary>
    public string Sex { get; set; } = "其他";

    /// <summary>
    /// 性别人数
    /// </summary>
    public int SexCount { get; set; }

    /// <summary>
    /// 性别占比
    /// </summary>
    public decimal SexPercent { get; set; }
}

/// <summary>
/// 用户学历概况
/// </summary>
public class DSPlatSeekerEducationResponse
{
    /// <summary>
    /// 学历
    /// </summary>
    public string? Education { get; set; } = "其他";

    /// <summary>
    /// 学历人数
    /// </summary>
    public int EduCount { get; set; }

    /// <summary>
    /// 各学历占比
    /// </summary>
    public decimal EduPercent { get; set; }
}

/// <summary>
/// 用户年龄概况
/// </summary>
public class DSPlatSeekerAgeResponse
{
    /// <summary>
    /// 年龄段
    /// </summary>
    public string? AgeGroup { get; set; } = "其他";

    /// <summary>
    /// 年龄段人数
    /// </summary>
    public int AgeCount { get; set; }

    /// <summary>
    /// 各年龄段占比
    /// </summary>
    public decimal AgePercent { get; set; }
}

/// <summary>
/// 用户注册来源
/// </summary>
public class UserSourceResponse
{
    /// <summary>
    /// 头像
    /// </summary>
    public string? Avator { get; set; }

    public string? SourceName { get; set; }

    public int Quantity { get; set; }

    public decimal? QuantityPercent { get; set; }
}
#endregion

#region 数据大屏 平台级-项目看板

public class DSPlatProjectDataResponse
{
    /// <summary>
    /// 项目累计数
    /// </summary>
    public int ProjectSum { get; set; }

    /// <summary>
    /// 运行中项目数
    /// </summary>
    public int ProjectExecNum { get; set; }

    /// <summary>
    /// 已归档项目数
    /// </summary>
    public int ProjectArchivedNum { get; set; }

    /// <summary>
    /// 平台交付中项目数
    /// </summary>
    public int PlatProjectExecNum { get; set; }

    /// <summary>
    /// 平台项目入驻数
    /// </summary>
    public int PlatProjectSum { get; set; }

    /// <summary>
    /// 平台项目已归档
    /// </summary>
    public int PlatProjectArchivedNum { get; set; }

    /// <summary>
    /// 诺亚项目数
    /// </summary>
    public int NoahProjectSum { get; set; }

    /// <summary>
    /// 诺亚项目占比
    /// </summary>
    public decimal NoahProjectSumPercent { get; set; }

    /// <summary>
    /// 平台项目入驻数占比
    /// </summary>
    public decimal PlatProjectSumPercent { get; set; }

    /// <summary>
    /// 第三方项目数
    /// </summary>
    public int OtherProjectSum { get; set; }

    /// <summary>
    /// 第三方项目数占比
    /// </summary>
    public decimal OtherProjectSumPercent { get; set; }

    /// <summary>
    /// 项目概况
    /// </summary>
    public List<ProjectResponse>? ProjectInfos { get; set; } 

    /// <summary>
    /// 项目行业概况
    /// </summary>
    public List<ProjectIndustryResponse>? ProjectIndustryInfos { get; set; }

    /// <summary>
    /// 项目地区概况
    /// </summary>
    public List<ProjectAreaInfo>? ProjectAreaInfos { get; set; }
}

/// <summary>
/// 项目概况
/// </summary>
public class ProjectResponse
{
    /// <summary>
    /// 项目类别
    /// </summary>
    public string? ProjectType { get; set; }

    /// <summary>
    /// 运行中项目数
    /// </summary>
    public int ExecNum { get; set; }

    /// <summary>
    /// 占比
    /// </summary>
    public decimal ExecNumPercent { get; set; }
}

/// <summary>
/// 项目行业概况
/// </summary>
public class ProjectIndustryResponse 
{
    /// <summary>
    /// 项目行业
    /// </summary>
    public string? ProjectIndustry { get; set; }

    /// <summary>
    /// 运行中项目数
    /// </summary>
    public int IndustryExecNum { get; set; }

    /// <summary>
    /// 占比
    /// </summary>
    public decimal IndustryExecNumPercent { get; set; }
}

#endregion


public class DataScreenInfoResponse : DataScreenInfo
{
    /// <summary>
    /// 顾问人才库平均储备(人才库数据/顾问数量=平均 )
    /// </summary>
    public int TalentAverage { get; set; }
    /// <summary>
    /// 顾问人才库平均储备状态(＜100=较差；100＜1000=良好；＞1001=优秀)
    /// </summary>
    public string? TalentAverageText { get; set; }
}

public class DSHRDataResponse
{
    /// <summary>
    /// 昵称
    /// </summary>
    public string NickName { get; set; } = string.Empty;
    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// 历史累计发布项目数量
    /// </summary>
    public int MyProjectTotal { get; set; }

    /// <summary>
    /// 历史累计协同项目数量
    /// </summary>
    public int TeamProjectTotal { get; set; }

    /// <summary>
    /// 进行中的项目（主创）
    /// </summary>
    public int MyProjectOnline { get; set; }

    /// <summary>
    /// 进行中的项目（协同）
    /// </summary>
    public int TeamProjectOnline { get; set; }

    /// <summary>
    /// 历史累计职位数量（主创）
    /// </summary>
    public int MyPostTotal { get; set; }

    /// <summary>
    /// 历史累计职位数量（协同）
    /// </summary>
    public int TeamPostTotal { get; set; }

    /// <summary>
    /// 进行中职位数量（主创）
    /// </summary>
    public int MyPostOnline { get; set; }

    /// <summary>
    /// 进行中职位数量（协同）
    /// </summary>
    public int TeamPostOnline { get; set; }

    /// <summary>
    /// 招聘中的人数（主创）
    /// </summary>
    public int MyRecruitOnline { get; set; }

    /// <summary>
    /// 招聘中的人数（协同）
    /// </summary>
    public int TeamRecruitOnline { get; set; }

    /// <summary>
    /// 累计投递用户数
    /// </summary>
    public int RecruitDelivery { get; set; }

    /// <summary>
    /// Hr初筛
    /// </summary>
    public int RecruitHrScreening { get; set; }

    /// <summary>
    /// 面试官筛选
    /// </summary>
    public int RecruitInterviewerScreening { get; set; }

    /// <summary>
    /// 面试
    /// </summary>
    public int RecruitInterview { get; set; }

    /// <summary>
    /// Offer
    /// </summary>
    public int RecruitOffer { get; set; }

    /// <summary>
    /// 入职
    /// </summary>
    public int RecruitInduction { get; set; }

    /// <summary>
    /// 签约
    /// </summary>
    public int RecruitContract { get; set; }

    /// <summary>
    /// 归档
    /// </summary>
    public int RecruitFileAway { get; set; }

    /// <summary>
    /// 今日访问数量
    /// </summary>
    public int TodayVisits { get; set; }

    /// <summary>
    /// 最近7日访问量
    /// </summary>
    public List<DayVisitsInfo>? DayVisits { get; set; }

    /// <summary>
    /// 总访问数量
    /// </summary>
    public int FullVisits { get; set; }

    /// <summary>
    /// 今日报名数量
    /// </summary>
    public int TodayDelivery { get; set; }
    /// <summary>
    /// 总报名数量
    /// </summary>
    public int FullDelivery { get; set; }

    /// <summary>
    /// 真实用户储备
    /// </summary>
    public int RealTalent { get; set; }

    /// <summary>
    /// 简历库储备
    /// </summary>
    public int Talent { get; set; }

    /// <summary>
    /// 职位分布
    /// </summary>
    public List<DistributeInfo> PostDistribute { get; set; } = default!;

    /// <summary>
    /// 人才行业储备
    /// </summary>
    public List<DistributeInfo> IndustryDistribute { get; set; } = default!;
}

/// <summary>
/// 每日访问量Info
/// </summary>
public class DayVisitsInfo
{
    /// <summary>
    /// 日期
    /// </summary>
    public string Day { get; set; } = default!;
    /// <summary>
    /// 当日访问量
    /// </summary>
    public int Visits { get; set; }
}

public class DSEntOrgDataResponse
{
    /// <summary>
    /// 入驻顾问数量
    /// </summary>
    public int HRCount { get; set; }
    /// <summary>
    /// 今日在线顾问数量(24小时内在线)
    /// </summary>
    public int HROnlineCount { get; set; }

    /// <summary>
    /// 在线率(HROnlineCount/HRCount*100,单位：%)
    /// </summary>
    public double HROnlineRatio { get; set; }

    /// <summary>
    /// 绑定钉钉顾问数
    /// </summary>
    public int HRDingDing { get; set; }

    /// <summary>
    /// 绑定微信顾问数
    /// </summary>
    public int HRWechat { get; set; }

    /// <summary>
    /// 累计投递用户数
    /// </summary>
    public int RecruitDelivery { get; set; }

    /// <summary>
    /// 累计联系
    /// =========此字段暂定固定为0，后期再补充==============
    /// </summary>
    public int RecruitContactTotal { get; set; } = 0;

    /// <summary>
    /// 累计面试
    /// </summary>
    public int RecruitInterviewTotal { get; set; }

    /// <summary>
    /// 累计入职
    /// </summary>
    public int RecruitInductionTotal { get; set; }

    /// <summary>
    /// 真实用户储备
    /// </summary>
    public int RealTalent { get; set; }

    /// <summary>
    /// 简历库储备
    /// </summary>
    public int VirtualTalent { get; set; }

    /// <summary>
    /// 储备共计(RealTalent+Talent)
    /// </summary>
    public int TotalTalent { get; set; }

    /// <summary>
    /// 历史累计发布项目数量（主创）
    /// </summary>
    public int ProjectTotal { get; set; }

    /// <summary>
    /// 进行中的项目（主创）
    /// </summary>
    public int ProjectOnline { get; set; }

    /// <summary>
    /// 已归档的项目（主创）（ProjectTotal-ProjectOnline）
    /// </summary>
    public int ProjectAway { get; set; }

    /// <summary>
    /// 进行中职位数量（主创）
    /// </summary>
    public int PostOnline { get; set; }

    /// <summary>
    /// 累计招聘职位数量（主创）
    /// </summary>
    public int PostTotal { get; set; }

    /// <summary>
    /// 累计招聘人数（主创）
    /// </summary>
    public int RecruitTotal { get; set; }

    /// <summary>
    /// 招聘中的人数（主创）
    /// </summary>
    public int RecruitOnline { get; set; }

    /// <summary>
    /// 缺口人数（RecruitOnline-RecruitInduction）
    /// </summary>
    public int Enrolling { get; set; }

    /// <summary>
    /// 报名中（RecruitHrScreening+RecruitInterviewerScreening+RecruitInterview+RecruitInduction+RecruitOffer+RecruitFileAway）
    /// </summary>
    public int Applicant { get; set; }

    /// <summary>
    /// Hr初筛
    /// </summary>
    public int RecruitHrScreening { get; set; }

    /// <summary>
    /// 面试安排(面试官筛选)
    /// </summary>
    public int RecruitInterviewerScreening { get; set; }

    /// <summary>
    /// 面试进行中
    /// </summary>
    public int RecruitInterview { get; set; }

    /// <summary>
    /// Offer
    /// </summary>
    public int RecruitOffer { get; set; }

    /// <summary>
    /// 入职
    /// </summary>
    public int RecruitInduction { get; set; }

    /// <summary>
    /// 不合适(归档)
    /// </summary>
    public int RecruitFileAway { get; set; }
}

/// <summary>
/// 组织协同情况返回结果
/// </summary>
public class DSEntOrgTeamDataResponse
{
    /// <summary>
    /// 集团协同岗位数量(主创)
    /// </summary>
    public int PostCount_JT { get; set; }
    /// <summary>
    /// 集团协同岗位数量(协同)
    /// </summary>
    public int TeamPostCount_JT  { get; set; }
    /// <summary>
    /// 集团协同比例(协同)
    /// </summary>
    public string? PostRate_JT { get; set; }

    /// <summary>
    /// 公司协同岗位数量(主创)
    /// </summary>
    public int PostCount_GS { get; set; }
    /// <summary>
    /// 公司协同岗位数量(协同)
    /// </summary>
    public int TeamPostCount_GS { get; set; }
    /// <summary>
    /// 公司协同比例(协同)
    /// </summary>
    public string? PostRate_GS { get; set; }

    /// <summary>
    /// 部门协同岗位数量(主创)
    /// </summary>
    public int PostCount_BM { get; set; }
    /// <summary>
    /// 部门协同比例(协同)
    /// </summary>
    public string? PostRate_BM { get; set; }
    /// <summary>
    /// 部门协同岗位数量(协同)
    /// </summary>
    public int TeamPostCount_BM { get; set; }

    /// <summary>
    /// 组织内顾问数量(含树级结构所有顾问(即下级+下下级...))
    /// </summary>
    public int HRCount { get; set; }
    /// <summary>
    /// 平台交付
    /// </summary>
    public int SharePlatCount { get; set; }
    /// <summary>
    /// 跨公司协同
    /// </summary>
    public int ShareAnyoneCount { get; set; }
}


public class DSDingDingDataInfo
{
    /// <summary>
    /// 组织ID
    /// </summary>
    public string? ID { get; set; }
    /// <summary>
    /// 组织名称
    /// </summary>
    public string? Name { get; set; }
    /// <summary>
    /// 人才库数量
    /// </summary>
    public int Value { get; set; }
}

public class DSDingDingDataInfoResponse
{
    public List<DSDingDingDataInfo> Rows { get; set; } = new List<DSDingDingDataInfo>();
}