﻿using Config.Enums;
using Nest;

namespace Staffing.Entity.Elasticsearch;

[ElasticsearchType(IdProperty = "Id")]
public class EsTalentVirtual
{
    [Keyword]
    public string Id { get; set; } = default!;

    [Keyword]
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 渠道Id
    /// </summary>
    [Keyword]
    public string? ChannelId { get; set; }

    /// <summary>
    /// 简历渠道
    /// </summary>
    [Keyword]
    public TalentVirtualChannel? Channel { get; set; }

    /// <summary>
    /// 简历状态
    /// </summary>
    [Keyword]
    public TalentVirtualStatus? Status { get; set; }

    /// <summary>
    /// 求职者Id，建立关系后有值
    /// </summary>
    [Keyword]
    public string? SeekerId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    [Wildcard]
    public string? Name { get; set; }

    /// <summary>
    /// 手机
    /// </summary>
    [Keyword]
    public string Mobile { get; set; } = string.Empty;

    /// <summary>
    /// 头像
    /// </summary>
    [Text(Index = false)]
    public string? HeadPortrait { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [Keyword]
    public Sex? Sex { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    [Keyword]
    public TalentVirtualEducation? Education { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    [Keyword]
    public string? Mailbox { get; set; }

    /// <summary>
    /// 微信
    /// </summary>
    [Text(Index = false)]
    public string? WeChat { get; set; }

    /// <summary>
    /// QQ
    /// </summary>
    [Text(Index = false)]
    public string? Qq { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    [Date]
    public DateTime? Birthday { get; set; }

    /// <summary>
    /// 开始工作日期
    /// </summary>
    [Date]
    public DateTime? WorkTime { get; set; }

    /// <summary>
    /// 简历完善度
    /// </summary>
    [Keyword]
    public int Perfection { get; set; } = 0;

    /// <summary>
    /// 专业技能集合
    /// </summary>
    [Wildcard]
    public List<string>? SkillProfessional { get; set; }

    /// <summary>
    /// IT技能集合
    /// </summary>
    [Wildcard]
    public List<string>? SkillIT { get; set; }

    /// <summary>
    /// 商业技能集合
    /// </summary>
    [Wildcard]
    public List<string>? SkillBusiness { get; set; }

    /// <summary>
    /// 自定义标签
    /// </summary>
    [Keyword]
    public List<string> Tags { get; set; } = default!;

    /// <summary>
    /// 行业标签（来源小析职业标签字段）
    /// </summary>
    [Keyword]
    public List<string> IndustryLabel { get; set; } = new List<string>();

    /// <summary>
    /// 职位标签（来源小析职业标签字段）
    /// </summary>
    [Keyword]
    public List<string> PostLabel { get; set; } = new List<string>();

    /// <summary>
    /// 其他标签（来源小析职业标签字段）
    /// </summary>
    [Keyword]
    public List<string> OtherLabel { get; set; } = new List<string>();

    /// <summary>
    /// 原始简历地址
    /// </summary>
    [Text(Index = false)]
    public string OriginalUrl { get; set; } = string.Empty;

    /// <summary>
    /// 所在地
    /// </summary>
    [Wildcard]
    public string Location { get; set; } = string.Empty;

    /// <summary>
    /// 所在地（标准地址）
    /// </summary>
    [Text(Index = false)]
    public string LocationNorm { get; set; } = string.Empty;

    /// <summary>
    /// 详细位置
    /// </summary>
    [Text(Index = false)]
    public string DetailedLocation { get; set; } = string.Empty;

    /// <summary>
    /// 地区Id
    /// </summary>
    [Wildcard]
    public string RegionId { get; set; } = default!;

    /// <summary>
    /// 更新时间
    /// </summary>
    [Date]
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    [Date]
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    [Nested]
    public List<EsTalentWork>? Works { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    [Nested]
    public List<EsTalentEdu>? Edus { get; set; }

    /// <summary>
    /// 求职期望
    /// </summary>
    [Nested]
    public List<EsTalentHope>? Hopes { get; set; }

    /// <summary>
    /// 检索关键字
    /// </summary>
    [Wildcard]
    public List<string> TalentSearch { get; set; } = default!;
}

public class EsTalentWork
{
    /// <summary>
    /// 公司名称
    /// </summary>
    [Wildcard]
    public string CompanyName { get; set; } = default!;

    /// <summary>
    /// 部门
    /// </summary>
    [Wildcard]
    public string Department { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    [Wildcard]
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 工作开始时间
    /// </summary>
    [Date]
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 工作结束时间
    /// </summary>
    [Date]
    public DateTime? EndTime { get; set; }
}

public class EsTalentEdu
{
    /// <summary>
    /// 学校名称
    /// </summary>
    [Wildcard]
    public string SchoolName { get; set; } = default!;

    /// <summary>
    /// 是否全日制
    /// </summary>
    [Keyword]
    public bool IsFullTime { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    [Wildcard]
    public TalentVirtualEducation Education { get; set; } = TalentVirtualEducation.Junior;

    /// <summary>
    /// 专业名称
    /// </summary>
    [Wildcard]
    public string MajorName { get; set; } = default!;

    /// <summary>
    /// 教育开始时间
    /// </summary>
    [Date]
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 教育结束时间
    /// </summary>
    [Date]
    public DateTime? EndTime { get; set; }
}

public class EsTalentHope
{
    /// <summary>
    /// 期望城市
    /// </summary>
    [Wildcard]
    public string HopeCity { get; set; } = string.Empty;

    /// <summary>
    /// 地区Id
    /// </summary>
    [Wildcard]
    public string RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 行业名称（多个）
    /// </summary>
    [Wildcard]
    public string IndustryName { get; set; } = string.Empty;

    /// <summary>
    /// 职位名称（多个）
    /// </summary>
    [Wildcard]
    public string PostName { get; set; } = string.Empty;

    /// <summary>
    /// 最低薪资
    /// </summary>
    [Keyword]
    public decimal MinSalary { get; set; } = 0;

    /// <summary>
    /// 最高薪资
    /// </summary>
    [Keyword]
    public decimal MaxSalary { get; set; } = 0;
}