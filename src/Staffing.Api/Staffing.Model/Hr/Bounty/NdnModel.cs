﻿using Config;
using Config.CommonModel;
using Config.CommonModel.Business;
using Config.CommonModel.Ndn;
using Staffing.Entity.Staffing;

namespace Staffing.Model.Hr.Bounty;

public class GetInvoiceList : QueryRequest
{
    /// <summary>
    /// 数字诺亚项目编码
    /// </summary>
    public string? ProjectCode { get; set; }
}

public class InvoiceListResponse : QueryResponse
{
    public List<InvoiceList> Rows { get; set; } = new List<InvoiceList>();
}

public class InvoiceList
{
    /// <summary>
    /// 发票Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public NdnAuditStatus Status { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public string? StatusText { get; set; }

    /// <summary>
    /// 开票类型
    /// </summary>
    public string? InvoiceType { get; set; }

    /// <summary>
    /// 发票数量
    /// </summary>
    public int InvoiceQuantity { get; set; } = 1;

    /// <summary>
    /// 税率
    /// </summary>
    public string? TaxRate { get; set; }

    /// <summary>
    /// 发票编码
    /// </summary>
    public string? InvoiceCode { get; set; }

    /// <summary>
    /// 发票地址
    /// </summary>
    public List<string>? InvoiceUrl { get; set; }

    /// <summary>
    /// 合同地址
    /// </summary>
    public string? ContractUrl { get; set; }

    /// <summary>
    /// 客户名称
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// 操作人姓名
    /// </summary>
    public string? OperatorName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }
}

public class GetTransferList : QueryRequest
{
    /// <summary>
    /// 数字诺亚项目编码
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// 公司名称
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public NdnAuditStatus? Status { get; set; }

    /// <summary>
    /// 金额范围
    /// </summary>
    public decimal? MinAmount { get; set; }

    /// <summary>
    /// 金额范围
    /// </summary>
    public decimal? MaxAmount { get; set; }
}

public class TransferListResponse : QueryResponse
{
    public List<TransferList> Rows { get; set; } = new List<TransferList>();
}

public class TransferList
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 费用名称
    /// </summary>
    public string? FeeName { get; set; } = "招聘费";

    /// <summary>
    /// 科目编码
    /// </summary>
    public string? SubjectCode { get; set; } = Constants.Ndn.FrKmCode;

    /// <summary>
    /// 报销科目
    /// </summary>
    public string? ReimbursementSubject { get; set; } = Constants.Ndn.FrKmName;

    // /// <summary>
    // /// 费用名称
    // /// </summary>
    // public string? FeeName { get; set; }

    /// <summary>
    /// 实施金额
    /// </summary>
    public decimal ImplementationAmount { get; set; }

    /// <summary>
    /// 公司名称
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 报销方式
    /// </summary>
    public string? ReimbursementMethod { get; set; } = "电汇";

    /// <summary>
    /// 报销账户
    /// </summary>
    public string? ReimbursementAccount { get; set; }

    /// <summary>
    /// 报销金额
    /// </summary>
    public decimal ReimbursementAmount { get; set; }

    /// <summary>
    /// 发票
    /// </summary>
    public List<string>? InvoiceUrl { get; set; }

    /// <summary>
    /// 合同
    /// </summary>
    public string? ContractUrl { get; set; }

    /// <summary>
    /// 流程状态
    /// </summary>
    public NdnAuditStatus? Status { get; set; }

    /// <summary>
    /// 流程状态
    /// </summary>
    public string? StatusText { get; set; }

    /// <summary>
    /// 操作人
    /// </summary>
    public string? OperatorName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }
}

public class GetServiceBonusBatch : QueryRequest
{
    /// <summary>
    /// 数字诺亚项目编码
    /// </summary>
    public string? ProjectCode { get; set; }
}

public class GetServiceBonusBatchResponse : QueryResponse
{
    public List<ServiceBonusBatch> Rows { get; set; } = new List<ServiceBonusBatch>();
}

public class ServiceBonusBatch
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 批次名称
    /// </summary>
    public string? BatchName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public NdnAuditStatus Status { get; set; }

    /// <summary>
    /// 状态文本
    /// </summary>
    public string? StatusText { get; set; }
}

public class GetServiceBonusList : QueryRequest
{
    /// <summary>
    /// 批次Id
    /// </summary>
    public string? BatchId { get; set; }
}

public class ServiceBonusListResponse : QueryResponse
{
    public List<ServiceBonusList> Rows { get; set; } = new List<ServiceBonusList>();
}

public class ServiceBonusList
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 诺快聘项目名称
    /// </summary>
    public string? NkpProjectName { get; set; }

    /// <summary>
    /// 诺快聘职位名称
    /// </summary>
    public string? NkpPostName { get; set; }

    /// <summary>
    /// 佣金金额
    /// </summary>
    public decimal CommissionAmount { get; set; }

    /// <summary>
    /// 交付人
    /// </summary>
    public string? DeliveryUserName { get; set; }

    /// <summary>
    /// 订单No
    /// </summary>
    public string? OrderNo { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    /// 结算状态
    /// </summary>
    public NdnAuditStatus Status { get; set; }

    /// <summary>
    /// 结算状态
    /// </summary>
    public string? StatusText { get; set; }
}

public class GetProjectSummaryResponse
{
    /// <summary>
    /// 项目编码
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }
}

public class GetProjectRequest
{
    /// <summary>
    /// 项目编码
    /// </summary>
    public string? ProjectCode { get; set; }
}

public class GetProjectInfo : GetProjectSummaryResponse
{
    /// <summary>
    /// 签约公司名称
    /// </summary>
    public string? BookName { get; set; }

    /// <summary>
    /// 服务专员的名称
    /// </summary>
    public string? ServiceSpecialist { get; set; }

    /// <summary>
    /// 项目回款状态
    /// </summary>
    public string? RemittanceStatus { get; set; }

    /// <summary>
    /// 客户名称
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// 项目业务类型
    /// </summary>
    public string? ProjectBusinessType { get; set; }

    /// <summary>
    /// 项目状态
    /// </summary>
    public string? ProjectStatus { get; set; }

    /// <summary>
    /// 项目完成时间
    /// </summary>
    public DateTime? ProjectCompletionTime { get; set; }

    /// <summary>
    /// 项目创建时间
    /// </summary>
    public DateTime? ProjectCreateTime { get; set; }

    /// <summary>
    /// 项目收入金额
    /// </summary>
    public decimal ProjectIncomeAmount { get; set; }

    /// <summary>
    /// 项目标志
    /// </summary>
    public string? ProjectFlag { get; set; }

    /// <summary>
    /// 诺快聘项目预算
    /// </summary>
    public decimal ProjectBudget { get; set; }

    /// <summary>
    /// 冻结金额
    /// </summary>
    public decimal FrozenAmount { get; set; }

    /// <summary>
    /// 发放服务奖金金额
    /// </summary>
    public decimal ServiceBonusAmount { get; set; }

    /// <summary>
    /// 合同编号
    /// </summary>
    public string? ContractNo { get; set; }

    /// <summary>
    /// 合同地址
    /// </summary>
    public string? ContractUrl { get; set; }

    /// <summary>
    /// 项目类型
    /// </summary>
    public NdnProjType? Type { get; set; }
}

public class InvoiceOverviewResponse
{
    /// <summary>
    /// 项目编码
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 帐套编码
    /// </summary>
    public string? BookCode { get; set; }

    /// <summary>
    /// 公司名称
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 公司行业
    /// </summary>
    public string? EntIndustry { get; set; }

    /// <summary>
    /// 法人
    /// </summary>
    public string? EntLegalPerson { get; set; }

    /// <summary>
    /// 成立日期
    /// </summary>
    public string? EntEstiblishTime { get; set; }

    /// <summary>
    /// 开票员
    /// </summary>
    public string? BillingClerk { get; set; }

    /// <summary>
    /// 出纳
    /// </summary>
    public string? Cashier { get; set; }

    /// <summary>
    /// 记账会计
    /// </summary>
    public string? Accountant { get; set; }

    /// <summary>
    /// 财务部长
    /// </summary>
    public string? FinanceMinister { get; set; }

    /// <summary>
    /// 已开发票金额
    /// </summary>
    public decimal InvoicedAmount { get; set; }

    /// <summary>
    /// 待提交发票金额
    /// </summary>
    public decimal PendingSubmissionAmount { get; set; }

    /// <summary>
    /// 审核中发票金额
    /// </summary>
    public decimal UnderReviewAmount { get; set; }

    /// <summary>
    /// 驳回发票金额
    /// </summary>
    public decimal RejectedAmount { get; set; }

    /// <summary>
    /// 发票信息
    /// </summary>
    public NdnInvoiceInfo InvoiceInfo { get; set; } = new NdnInvoiceInfo();

    /// <summary>
    /// 合同用章
    /// </summary>
    public string? ContractSeal { get; set; }
}

public class UpdateBook : NdnInvoiceInfo
{
    /// <summary>
    /// 帐套编号
    /// </summary>
    public string? BookCode { get; set; }

    /// <summary>
    /// 公章
    /// </summary>
    public string? ContractSeal { get; set; }
}

public class GetFlowInfo : QueryRequest
{
    /// <summary>
    /// 数字诺亚项目编码
    /// </summary>
    public string? ProjectCode { get; set; }
}

public class FlowInfoResponse : QueryResponse
{
    public List<FlowInfo> Rows { get; set; } = new List<FlowInfo>();
}

public class FlowInfo
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 通知类型
    /// </summary>
    public string? TypeText { get; set; }

    /// <summary>
    /// 通知状态
    /// </summary>
    public NdnAuditStatus? Status { get; set; }

    /// <summary>
    /// 通知状态
    /// </summary>
    public string? StatusText { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }
}

public class SaveInvoice
{
    /// <summary>
    /// 发票Id
    /// </summary>
    public string? Id { get; set; }
}

public class SaveTransfer
{
    /// <summary>
    /// 转账Id
    /// </summary>
    public string? Id { get; set; }
}

public class SaveServiceBonus
{
    /// <summary>
    /// 转账Id
    /// </summary>
    public string? Id { get; set; }
}