namespace Staffing.Worker.Hosting;

public abstract class HostingBase : IHostedService
{
    protected List<Task> _bgTasks = new List<Task>();
    protected readonly CancellationToken _cancellationToken;

    public HostingBase(IHostApplicationLifetime lifetime)
    {
        _cancellationToken = lifetime.ApplicationStopping;
    }

    public virtual Task StartAsync(CancellationToken cancel)
    {
        // _bgTasks.Add(await Task.Factory.StartNew(() => Worker1Hour(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancel)
    {
        //优雅退出
        if (_bgTasks?.Count > 0)
            Task.WaitAll(_bgTasks.ToArray(), 5000, cancel);
        return Task.FromResult(cancel);
    }
}