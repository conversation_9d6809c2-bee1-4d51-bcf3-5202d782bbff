﻿using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.DingTalk;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Infrastructure.Proxy;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json.Linq;
using Staffing.Entity;
using Staffing.Model.Hr.User;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces.Hr;
namespace Staffing.Core.Services.Service.Hr;

[Service(ServiceLifetime.Transient)]
public class DingTalkService : IDingTalkService
{
    private readonly DingTalkTokenService _dingTalkTokenService;
    private readonly LogManager _logger;
    private readonly StaffingContext _context;
    private ICommonService _commonService;
    private readonly NuoPinApi _nuoPinApi;

    public DingTalkService(DingTalkTokenService dingTalkTokenService,
        LogManager logManager,
        StaffingContext context,
        ICommonService commonService,
        NuoPinApi nuoPinApi)
    {
        _dingTalkTokenService = dingTalkTokenService;
        _logger = logManager;
        _context = context;
        _commonService = commonService;
        _nuoPinApi = nuoPinApi;
    }

    public HrLoginResponse GetCookies(string code)
    {
        // 1.获取access_token
        string access_token = _dingTalkTokenService.GetToken(DingTalkConstKey.NkpAppKey, DingTalkConstKey.NkpAppSecret);
        // 2.获取用户userid
        string url = $"https://oapi.dingtalk.com/topapi/v2/user/getuserinfo?access_token={access_token}";
        var response = url.PostJsonAsync(new { code }).ReceiveString().Result;

        var r = JToken.Parse(response);
        var res = new HrLoginResponse();
        if (r.Value<int>("errcode") == 0)
        {
            var result = r.Value<JToken>("result");
            string userId = result?.Value<string>("userid") + "";
            if (!string.IsNullOrEmpty(userId))
            {
                var dingUserInfo = _context.User_DingDing.FirstOrDefault(f => f.DingUserid.Equals(userId)) ?? throw new UnauthorizedException("未获取到钉钉用户信息");
                var hrInfo = _context.User_Hr.FirstOrDefault(f => f.User.Mobile == dingUserInfo.DingMobile) ?? throw new UnauthorizedException("顾问未绑定钉钉");
                
                // 获取诺快聘token
                //var token = _commonService.CreateRefreshToken(hrInfo.UserId, TokenType.企业端);
                //res = new HrLoginResponse
                //{
                //    AccessToken = token.AccessToken,
                //    RefreshToken = token.RefreshToken,
                //    TokenExpiresTime = token.TokenExpiresTime
                //};

                // 获取诺聘token
                var np_token = _nuoPinApi.NoahHrLogin(dingUserInfo.DingMobile, null).GetAwaiter().GetResult();
                res = new HrLoginResponse
                {
                    AccessToken = np_token.AccessToken,
                    RefreshToken = np_token.RefreshToken,
                    TokenExpiresTime = np_token.TokenExpiresTime
                };
            }
        }
        else
        {
            _logger.Error("钉钉接口请求失败", response);
            throw new UnauthorizedException(response);
        }

        return res;
    }
}
