﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//
//     对此文件的更改可能导致不正确的行为，并在以下条件下丢失:
//     代码重新生成。
// </auto-generated>
//------------------------------------------------------------------------------

namespace FigureNoah
{


    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName = "FigureNoah.HRServiceSoap")]
    public interface HRServiceSoap
    {

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/FN_GetProjectByCusName", ReplyAction = "*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults = true)]
        System.Threading.Tasks.Task<FN_GetProjectByCusNameResponse> FN_GetProjectByCusNameAsync(FN_GetProjectByCusNameRequest request);

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/FN_GetEmployeeByProjectCODE", ReplyAction = "*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults = true)]
        System.Threading.Tasks.Task<FN_GetEmployeeByProjectCODEResponse> FN_GetEmployeeByProjectCODEAsync(FN_GetEmployeeByProjectCODERequest request);

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/FN_DEMO11", ReplyAction = "*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults = true)]
        System.Threading.Tasks.Task<string> FN_DEMO11Async(string Pro_Code, int PageIndex, int PageCount);

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/FN_GetProjectByEmpCODE", ReplyAction = "*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults = true)]
        System.Threading.Tasks.Task<FN_GetProjectByEmpCODEResponse> FN_GetProjectByEmpCODEAsync(FN_GetProjectByEmpCODERequest request);

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/FN_GetHTANDXMByEmpCODE", ReplyAction = "*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults = true)]
        System.Threading.Tasks.Task<FN_GetHTANDXMByEmpCODEResponse> FN_GetHTANDXMByEmpCODEAsync(FN_GetHTANDXMByEmpCODERequest request);

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/FN_GetHTANDXMByEmpCODEVIEW", ReplyAction = "*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults = true)]
        System.Threading.Tasks.Task<FN_GetHTANDXMByEmpCODEVIEWResponse> FN_GetHTANDXMByEmpCODEVIEWAsync(FN_GetHTANDXMByEmpCODEVIEWRequest request);

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/FN_GetContactByIDNumber", ReplyAction = "*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults = true)]
        System.Threading.Tasks.Task<FN_GetContactByIDNumberResponse> FN_GetContactByIDNumberAsync(FN_GetContactByIDNumberRequest request);

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/FN_Update_T_ENTRYAPPLICATION", ReplyAction = "*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults = true)]
        System.Threading.Tasks.Task<FN_Update_T_ENTRYAPPLICATIONResponse> FN_Update_T_ENTRYAPPLICATIONAsync(FN_Update_T_ENTRYAPPLICATIONRequest request);

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/FN_Insert_Update_T_ENTRYAPPLICATION_RZSQ", ReplyAction = "*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults = true)]
        System.Threading.Tasks.Task<FN_Insert_Update_T_ENTRYAPPLICATION_RZSQResponse> FN_Insert_Update_T_ENTRYAPPLICATION_RZSQAsync(FN_Insert_Update_T_ENTRYAPPLICATION_RZSQRequest request);

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/FN_Insert_Update_T_ENTRYAPPLICATION_LZSQ", ReplyAction = "*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults = true)]
        System.Threading.Tasks.Task<FN_Insert_Update_T_ENTRYAPPLICATION_LZSQResponse> FN_Insert_Update_T_ENTRYAPPLICATION_LZSQAsync(FN_Insert_Update_T_ENTRYAPPLICATION_LZSQRequest request);

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/FN_Update_T_CONTRACTINFO_LDHT_DWYG", ReplyAction = "*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults = true)]
        System.Threading.Tasks.Task<FN_Update_T_CONTRACTINFO_LDHT_DWYGResponse> FN_Update_T_CONTRACTINFO_LDHT_DWYGAsync(FN_Update_T_CONTRACTINFO_LDHT_DWYGRequest request);

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/FN_Update_T_PROJECT_FWZY", ReplyAction = "*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults = true)]
        System.Threading.Tasks.Task<FN_Update_T_PROJECT_FWZYResponse> FN_Update_T_PROJECT_FWZYAsync(FN_Update_T_PROJECT_FWZYRequest request);
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://tempuri.org/")]
    public partial class CredentialSoapHeader
    {

        private string userIDField;

        private string passWordField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order = 0)]
        public string UserID
        {
            get
            {
                return this.userIDField;
            }
            set
            {
                this.userIDField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order = 1)]
        public string PassWord
        {
            get
            {
                return this.passWordField;
            }
            set
            {
                this.passWordField = value;
            }
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_GetProjectByCusName", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_GetProjectByCusNameRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace = "http://tempuri.org/")]
        public CredentialSoapHeader CredentialSoapHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string Customer_Name;

        public FN_GetProjectByCusNameRequest()
        {
        }

        public FN_GetProjectByCusNameRequest(CredentialSoapHeader CredentialSoapHeader, string Customer_Name)
        {
            this.CredentialSoapHeader = CredentialSoapHeader;
            this.Customer_Name = Customer_Name;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_GetProjectByCusNameResponse", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_GetProjectByCusNameResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string FN_GetProjectByCusNameResult;

        public FN_GetProjectByCusNameResponse()
        {
        }

        public FN_GetProjectByCusNameResponse(string FN_GetProjectByCusNameResult)
        {
            this.FN_GetProjectByCusNameResult = FN_GetProjectByCusNameResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_GetEmployeeByProjectCODE", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_GetEmployeeByProjectCODERequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace = "http://tempuri.org/")]
        public CredentialSoapHeader CredentialSoapHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string Pro_Code;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 1)]
        public int PageIndex;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 2)]
        public int PageCount;

        public FN_GetEmployeeByProjectCODERequest()
        {
        }

        public FN_GetEmployeeByProjectCODERequest(CredentialSoapHeader CredentialSoapHeader, string Pro_Code, int PageIndex, int PageCount)
        {
            this.CredentialSoapHeader = CredentialSoapHeader;
            this.Pro_Code = Pro_Code;
            this.PageIndex = PageIndex;
            this.PageCount = PageCount;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_GetEmployeeByProjectCODEResponse", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_GetEmployeeByProjectCODEResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string FN_GetEmployeeByProjectCODEResult;

        public FN_GetEmployeeByProjectCODEResponse()
        {
        }

        public FN_GetEmployeeByProjectCODEResponse(string FN_GetEmployeeByProjectCODEResult)
        {
            this.FN_GetEmployeeByProjectCODEResult = FN_GetEmployeeByProjectCODEResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_GetProjectByEmpCODE", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_GetProjectByEmpCODERequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace = "http://tempuri.org/")]
        public CredentialSoapHeader CredentialSoapHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string Pro_Code;

        public FN_GetProjectByEmpCODERequest()
        {
        }

        public FN_GetProjectByEmpCODERequest(CredentialSoapHeader CredentialSoapHeader, string Pro_Code)
        {
            this.CredentialSoapHeader = CredentialSoapHeader;
            this.Pro_Code = Pro_Code;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_GetProjectByEmpCODEResponse", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_GetProjectByEmpCODEResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string FN_GetProjectByEmpCODEResult;

        public FN_GetProjectByEmpCODEResponse()
        {
        }

        public FN_GetProjectByEmpCODEResponse(string FN_GetProjectByEmpCODEResult)
        {
            this.FN_GetProjectByEmpCODEResult = FN_GetProjectByEmpCODEResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_GetHTANDXMByEmpCODE", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_GetHTANDXMByEmpCODERequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace = "http://tempuri.org/")]
        public CredentialSoapHeader CredentialSoapHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string Pro_Code;

        public FN_GetHTANDXMByEmpCODERequest()
        {
        }

        public FN_GetHTANDXMByEmpCODERequest(CredentialSoapHeader CredentialSoapHeader, string Pro_Code)
        {
            this.CredentialSoapHeader = CredentialSoapHeader;
            this.Pro_Code = Pro_Code;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_GetHTANDXMByEmpCODEResponse", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_GetHTANDXMByEmpCODEResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string FN_GetHTANDXMByEmpCODEResult;

        public FN_GetHTANDXMByEmpCODEResponse()
        {
        }

        public FN_GetHTANDXMByEmpCODEResponse(string FN_GetHTANDXMByEmpCODEResult)
        {
            this.FN_GetHTANDXMByEmpCODEResult = FN_GetHTANDXMByEmpCODEResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_GetHTANDXMByEmpCODEVIEW", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_GetHTANDXMByEmpCODEVIEWRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace = "http://tempuri.org/")]
        public CredentialSoapHeader CredentialSoapHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string Pro_Code;

        public FN_GetHTANDXMByEmpCODEVIEWRequest()
        {
        }

        public FN_GetHTANDXMByEmpCODEVIEWRequest(CredentialSoapHeader CredentialSoapHeader, string Pro_Code)
        {
            this.CredentialSoapHeader = CredentialSoapHeader;
            this.Pro_Code = Pro_Code;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_GetHTANDXMByEmpCODEVIEWResponse", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_GetHTANDXMByEmpCODEVIEWResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string FN_GetHTANDXMByEmpCODEVIEWResult;

        public FN_GetHTANDXMByEmpCODEVIEWResponse()
        {
        }

        public FN_GetHTANDXMByEmpCODEVIEWResponse(string FN_GetHTANDXMByEmpCODEVIEWResult)
        {
            this.FN_GetHTANDXMByEmpCODEVIEWResult = FN_GetHTANDXMByEmpCODEVIEWResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_GetContactByIDNumber", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_GetContactByIDNumberRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace = "http://tempuri.org/")]
        public CredentialSoapHeader CredentialSoapHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string Pro_Code;

        public FN_GetContactByIDNumberRequest()
        {
        }

        public FN_GetContactByIDNumberRequest(CredentialSoapHeader CredentialSoapHeader, string Pro_Code)
        {
            this.CredentialSoapHeader = CredentialSoapHeader;
            this.Pro_Code = Pro_Code;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_GetContactByIDNumberResponse", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_GetContactByIDNumberResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string FN_GetContactByIDNumberResult;

        public FN_GetContactByIDNumberResponse()
        {
        }

        public FN_GetContactByIDNumberResponse(string FN_GetContactByIDNumberResult)
        {
            this.FN_GetContactByIDNumberResult = FN_GetContactByIDNumberResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_Update_T_ENTRYAPPLICATION", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_Update_T_ENTRYAPPLICATIONRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace = "http://tempuri.org/")]
        public CredentialSoapHeader CredentialSoapHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string xm_code;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 1)]
        public ArrayOfXElement ds;

        public FN_Update_T_ENTRYAPPLICATIONRequest()
        {
        }

        public FN_Update_T_ENTRYAPPLICATIONRequest(CredentialSoapHeader CredentialSoapHeader, string xm_code, ArrayOfXElement ds)
        {
            this.CredentialSoapHeader = CredentialSoapHeader;
            this.xm_code = xm_code;
            this.ds = ds;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_Update_T_ENTRYAPPLICATIONResponse", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_Update_T_ENTRYAPPLICATIONResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string FN_Update_T_ENTRYAPPLICATIONResult;

        public FN_Update_T_ENTRYAPPLICATIONResponse()
        {
        }

        public FN_Update_T_ENTRYAPPLICATIONResponse(string FN_Update_T_ENTRYAPPLICATIONResult)
        {
            this.FN_Update_T_ENTRYAPPLICATIONResult = FN_Update_T_ENTRYAPPLICATIONResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_Insert_Update_T_ENTRYAPPLICATION_RZSQ", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_Insert_Update_T_ENTRYAPPLICATION_RZSQRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace = "http://tempuri.org/")]
        public CredentialSoapHeader CredentialSoapHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string xm_code;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 1)]
        public ArrayOfXElement ds;

        public FN_Insert_Update_T_ENTRYAPPLICATION_RZSQRequest()
        {
        }

        public FN_Insert_Update_T_ENTRYAPPLICATION_RZSQRequest(CredentialSoapHeader CredentialSoapHeader, string xm_code, ArrayOfXElement ds)
        {
            this.CredentialSoapHeader = CredentialSoapHeader;
            this.xm_code = xm_code;
            this.ds = ds;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_Insert_Update_T_ENTRYAPPLICATION_RZSQResponse", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_Insert_Update_T_ENTRYAPPLICATION_RZSQResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string FN_Insert_Update_T_ENTRYAPPLICATION_RZSQResult;

        public FN_Insert_Update_T_ENTRYAPPLICATION_RZSQResponse()
        {
        }

        public FN_Insert_Update_T_ENTRYAPPLICATION_RZSQResponse(string FN_Insert_Update_T_ENTRYAPPLICATION_RZSQResult)
        {
            this.FN_Insert_Update_T_ENTRYAPPLICATION_RZSQResult = FN_Insert_Update_T_ENTRYAPPLICATION_RZSQResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_Insert_Update_T_ENTRYAPPLICATION_LZSQ", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_Insert_Update_T_ENTRYAPPLICATION_LZSQRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace = "http://tempuri.org/")]
        public CredentialSoapHeader CredentialSoapHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string xm_code;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 1)]
        public ArrayOfXElement ds;

        public FN_Insert_Update_T_ENTRYAPPLICATION_LZSQRequest()
        {
        }

        public FN_Insert_Update_T_ENTRYAPPLICATION_LZSQRequest(CredentialSoapHeader CredentialSoapHeader, string xm_code, ArrayOfXElement ds)
        {
            this.CredentialSoapHeader = CredentialSoapHeader;
            this.xm_code = xm_code;
            this.ds = ds;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_Insert_Update_T_ENTRYAPPLICATION_LZSQResponse", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_Insert_Update_T_ENTRYAPPLICATION_LZSQResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string FN_Insert_Update_T_ENTRYAPPLICATION_LZSQResult;

        public FN_Insert_Update_T_ENTRYAPPLICATION_LZSQResponse()
        {
        }

        public FN_Insert_Update_T_ENTRYAPPLICATION_LZSQResponse(string FN_Insert_Update_T_ENTRYAPPLICATION_LZSQResult)
        {
            this.FN_Insert_Update_T_ENTRYAPPLICATION_LZSQResult = FN_Insert_Update_T_ENTRYAPPLICATION_LZSQResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_Update_T_CONTRACTINFO_LDHT_DWYG", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_Update_T_CONTRACTINFO_LDHT_DWYGRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace = "http://tempuri.org/")]
        public CredentialSoapHeader CredentialSoapHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string xm_code;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 1)]
        public ArrayOfXElement ds;

        public FN_Update_T_CONTRACTINFO_LDHT_DWYGRequest()
        {
        }

        public FN_Update_T_CONTRACTINFO_LDHT_DWYGRequest(CredentialSoapHeader CredentialSoapHeader, string xm_code, ArrayOfXElement ds)
        {
            this.CredentialSoapHeader = CredentialSoapHeader;
            this.xm_code = xm_code;
            this.ds = ds;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_Update_T_CONTRACTINFO_LDHT_DWYGResponse", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_Update_T_CONTRACTINFO_LDHT_DWYGResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string FN_Update_T_CONTRACTINFO_LDHT_DWYGResult;

        public FN_Update_T_CONTRACTINFO_LDHT_DWYGResponse()
        {
        }

        public FN_Update_T_CONTRACTINFO_LDHT_DWYGResponse(string FN_Update_T_CONTRACTINFO_LDHT_DWYGResult)
        {
            this.FN_Update_T_CONTRACTINFO_LDHT_DWYGResult = FN_Update_T_CONTRACTINFO_LDHT_DWYGResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_Update_T_PROJECT_FWZY", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_Update_T_PROJECT_FWZYRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace = "http://tempuri.org/")]
        public CredentialSoapHeader CredentialSoapHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string xm_code;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 1)]
        public string Pro_Code;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 2)]
        public string Pro_Name;

        public FN_Update_T_PROJECT_FWZYRequest()
        {
        }

        public FN_Update_T_PROJECT_FWZYRequest(CredentialSoapHeader CredentialSoapHeader, string xm_code, string Pro_Code, string Pro_Name)
        {
            this.CredentialSoapHeader = CredentialSoapHeader;
            this.xm_code = xm_code;
            this.Pro_Code = Pro_Code;
            this.Pro_Name = Pro_Name;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "FN_Update_T_PROJECT_FWZYResponse", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class FN_Update_T_PROJECT_FWZYResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string FN_Update_T_PROJECT_FWZYResult;

        public FN_Update_T_PROJECT_FWZYResponse()
        {
        }

        public FN_Update_T_PROJECT_FWZYResponse(string FN_Update_T_PROJECT_FWZYResult)
        {
            this.FN_Update_T_PROJECT_FWZYResult = FN_Update_T_PROJECT_FWZYResult;
        }
    }

    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    public interface HRServiceSoapChannel : HRServiceSoap, System.ServiceModel.IClientChannel
    {
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    public partial class HRServiceSoapClient : System.ServiceModel.ClientBase<HRServiceSoap>, HRServiceSoap
    {

        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);

        public HRServiceSoapClient(EndpointConfiguration endpointConfiguration) :
                base(HRServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), HRServiceSoapClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }

        public HRServiceSoapClient(EndpointConfiguration endpointConfiguration, string remoteAddress) :
                base(HRServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }

        public HRServiceSoapClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) :
                base(HRServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }

        public HRServiceSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) :
                base(binding, remoteAddress)
        {
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<FN_GetProjectByCusNameResponse> HRServiceSoap.FN_GetProjectByCusNameAsync(FN_GetProjectByCusNameRequest request)
        {
            return base.Channel.FN_GetProjectByCusNameAsync(request);
        }

        public System.Threading.Tasks.Task<FN_GetProjectByCusNameResponse> FN_GetProjectByCusNameAsync(CredentialSoapHeader CredentialSoapHeader, string Customer_Name)
        {
            FN_GetProjectByCusNameRequest inValue = new FN_GetProjectByCusNameRequest();
            inValue.CredentialSoapHeader = CredentialSoapHeader;
            inValue.Customer_Name = Customer_Name;
            return ((HRServiceSoap)(this)).FN_GetProjectByCusNameAsync(inValue);
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<FN_GetEmployeeByProjectCODEResponse> HRServiceSoap.FN_GetEmployeeByProjectCODEAsync(FN_GetEmployeeByProjectCODERequest request)
        {
            return base.Channel.FN_GetEmployeeByProjectCODEAsync(request);
        }

        public System.Threading.Tasks.Task<FN_GetEmployeeByProjectCODEResponse> FN_GetEmployeeByProjectCODEAsync(CredentialSoapHeader CredentialSoapHeader, string Pro_Code, int PageIndex, int PageCount)
        {
            FN_GetEmployeeByProjectCODERequest inValue = new FN_GetEmployeeByProjectCODERequest();
            inValue.CredentialSoapHeader = CredentialSoapHeader;
            inValue.Pro_Code = Pro_Code;
            inValue.PageIndex = PageIndex;
            inValue.PageCount = PageCount;
            return ((HRServiceSoap)(this)).FN_GetEmployeeByProjectCODEAsync(inValue);
        }

        public System.Threading.Tasks.Task<string> FN_DEMO11Async(string Pro_Code, int PageIndex, int PageCount)
        {
            return base.Channel.FN_DEMO11Async(Pro_Code, PageIndex, PageCount);
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<FN_GetProjectByEmpCODEResponse> HRServiceSoap.FN_GetProjectByEmpCODEAsync(FN_GetProjectByEmpCODERequest request)
        {
            return base.Channel.FN_GetProjectByEmpCODEAsync(request);
        }

        public System.Threading.Tasks.Task<FN_GetProjectByEmpCODEResponse> FN_GetProjectByEmpCODEAsync(CredentialSoapHeader CredentialSoapHeader, string Pro_Code)
        {
            FN_GetProjectByEmpCODERequest inValue = new FN_GetProjectByEmpCODERequest();
            inValue.CredentialSoapHeader = CredentialSoapHeader;
            inValue.Pro_Code = Pro_Code;
            return ((HRServiceSoap)(this)).FN_GetProjectByEmpCODEAsync(inValue);
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<FN_GetHTANDXMByEmpCODEResponse> HRServiceSoap.FN_GetHTANDXMByEmpCODEAsync(FN_GetHTANDXMByEmpCODERequest request)
        {
            return base.Channel.FN_GetHTANDXMByEmpCODEAsync(request);
        }

        public System.Threading.Tasks.Task<FN_GetHTANDXMByEmpCODEResponse> FN_GetHTANDXMByEmpCODEAsync(CredentialSoapHeader CredentialSoapHeader, string Pro_Code)
        {
            FN_GetHTANDXMByEmpCODERequest inValue = new FN_GetHTANDXMByEmpCODERequest();
            inValue.CredentialSoapHeader = CredentialSoapHeader;
            inValue.Pro_Code = Pro_Code;
            return ((HRServiceSoap)(this)).FN_GetHTANDXMByEmpCODEAsync(inValue);
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<FN_GetHTANDXMByEmpCODEVIEWResponse> HRServiceSoap.FN_GetHTANDXMByEmpCODEVIEWAsync(FN_GetHTANDXMByEmpCODEVIEWRequest request)
        {
            return base.Channel.FN_GetHTANDXMByEmpCODEVIEWAsync(request);
        }

        public System.Threading.Tasks.Task<FN_GetHTANDXMByEmpCODEVIEWResponse> FN_GetHTANDXMByEmpCODEVIEWAsync(CredentialSoapHeader CredentialSoapHeader, string Pro_Code)
        {
            FN_GetHTANDXMByEmpCODEVIEWRequest inValue = new FN_GetHTANDXMByEmpCODEVIEWRequest();
            inValue.CredentialSoapHeader = CredentialSoapHeader;
            inValue.Pro_Code = Pro_Code;
            return ((HRServiceSoap)(this)).FN_GetHTANDXMByEmpCODEVIEWAsync(inValue);
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<FN_GetContactByIDNumberResponse> HRServiceSoap.FN_GetContactByIDNumberAsync(FN_GetContactByIDNumberRequest request)
        {
            return base.Channel.FN_GetContactByIDNumberAsync(request);
        }

        public System.Threading.Tasks.Task<FN_GetContactByIDNumberResponse> FN_GetContactByIDNumberAsync(CredentialSoapHeader CredentialSoapHeader, string Pro_Code)
        {
            FN_GetContactByIDNumberRequest inValue = new FN_GetContactByIDNumberRequest();
            inValue.CredentialSoapHeader = CredentialSoapHeader;
            inValue.Pro_Code = Pro_Code;
            return ((HRServiceSoap)(this)).FN_GetContactByIDNumberAsync(inValue);
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<FN_Update_T_ENTRYAPPLICATIONResponse> HRServiceSoap.FN_Update_T_ENTRYAPPLICATIONAsync(FN_Update_T_ENTRYAPPLICATIONRequest request)
        {
            return base.Channel.FN_Update_T_ENTRYAPPLICATIONAsync(request);
        }

        public System.Threading.Tasks.Task<FN_Update_T_ENTRYAPPLICATIONResponse> FN_Update_T_ENTRYAPPLICATIONAsync(CredentialSoapHeader CredentialSoapHeader, string xm_code, ArrayOfXElement ds)
        {
            FN_Update_T_ENTRYAPPLICATIONRequest inValue = new FN_Update_T_ENTRYAPPLICATIONRequest();
            inValue.CredentialSoapHeader = CredentialSoapHeader;
            inValue.xm_code = xm_code;
            inValue.ds = ds;
            return ((HRServiceSoap)(this)).FN_Update_T_ENTRYAPPLICATIONAsync(inValue);
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<FN_Insert_Update_T_ENTRYAPPLICATION_RZSQResponse> HRServiceSoap.FN_Insert_Update_T_ENTRYAPPLICATION_RZSQAsync(FN_Insert_Update_T_ENTRYAPPLICATION_RZSQRequest request)
        {
            return base.Channel.FN_Insert_Update_T_ENTRYAPPLICATION_RZSQAsync(request);
        }

        public System.Threading.Tasks.Task<FN_Insert_Update_T_ENTRYAPPLICATION_RZSQResponse> FN_Insert_Update_T_ENTRYAPPLICATION_RZSQAsync(CredentialSoapHeader CredentialSoapHeader, string xm_code, ArrayOfXElement ds)
        {
            FN_Insert_Update_T_ENTRYAPPLICATION_RZSQRequest inValue = new FN_Insert_Update_T_ENTRYAPPLICATION_RZSQRequest();
            inValue.CredentialSoapHeader = CredentialSoapHeader;
            inValue.xm_code = xm_code;
            inValue.ds = ds;
            return ((HRServiceSoap)(this)).FN_Insert_Update_T_ENTRYAPPLICATION_RZSQAsync(inValue);
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<FN_Insert_Update_T_ENTRYAPPLICATION_LZSQResponse> HRServiceSoap.FN_Insert_Update_T_ENTRYAPPLICATION_LZSQAsync(FN_Insert_Update_T_ENTRYAPPLICATION_LZSQRequest request)
        {
            return base.Channel.FN_Insert_Update_T_ENTRYAPPLICATION_LZSQAsync(request);
        }

        public System.Threading.Tasks.Task<FN_Insert_Update_T_ENTRYAPPLICATION_LZSQResponse> FN_Insert_Update_T_ENTRYAPPLICATION_LZSQAsync(CredentialSoapHeader CredentialSoapHeader, string xm_code, ArrayOfXElement ds)
        {
            FN_Insert_Update_T_ENTRYAPPLICATION_LZSQRequest inValue = new FN_Insert_Update_T_ENTRYAPPLICATION_LZSQRequest();
            inValue.CredentialSoapHeader = CredentialSoapHeader;
            inValue.xm_code = xm_code;
            inValue.ds = ds;
            return ((HRServiceSoap)(this)).FN_Insert_Update_T_ENTRYAPPLICATION_LZSQAsync(inValue);
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<FN_Update_T_CONTRACTINFO_LDHT_DWYGResponse> HRServiceSoap.FN_Update_T_CONTRACTINFO_LDHT_DWYGAsync(FN_Update_T_CONTRACTINFO_LDHT_DWYGRequest request)
        {
            return base.Channel.FN_Update_T_CONTRACTINFO_LDHT_DWYGAsync(request);
        }

        public System.Threading.Tasks.Task<FN_Update_T_CONTRACTINFO_LDHT_DWYGResponse> FN_Update_T_CONTRACTINFO_LDHT_DWYGAsync(CredentialSoapHeader CredentialSoapHeader, string xm_code, ArrayOfXElement ds)
        {
            FN_Update_T_CONTRACTINFO_LDHT_DWYGRequest inValue = new FN_Update_T_CONTRACTINFO_LDHT_DWYGRequest();
            inValue.CredentialSoapHeader = CredentialSoapHeader;
            inValue.xm_code = xm_code;
            inValue.ds = ds;
            return ((HRServiceSoap)(this)).FN_Update_T_CONTRACTINFO_LDHT_DWYGAsync(inValue);
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<FN_Update_T_PROJECT_FWZYResponse> HRServiceSoap.FN_Update_T_PROJECT_FWZYAsync(FN_Update_T_PROJECT_FWZYRequest request)
        {
            return base.Channel.FN_Update_T_PROJECT_FWZYAsync(request);
        }

        public System.Threading.Tasks.Task<FN_Update_T_PROJECT_FWZYResponse> FN_Update_T_PROJECT_FWZYAsync(CredentialSoapHeader CredentialSoapHeader, string xm_code, string Pro_Code, string Pro_Name)
        {
            FN_Update_T_PROJECT_FWZYRequest inValue = new FN_Update_T_PROJECT_FWZYRequest();
            inValue.CredentialSoapHeader = CredentialSoapHeader;
            inValue.xm_code = xm_code;
            inValue.Pro_Code = Pro_Code;
            inValue.Pro_Name = Pro_Name;
            return ((HRServiceSoap)(this)).FN_Update_T_PROJECT_FWZYAsync(inValue);
        }

        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }

        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }

        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.HRServiceSoap))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            if ((endpointConfiguration == EndpointConfiguration.HRServiceSoap12))
            {
                System.ServiceModel.Channels.CustomBinding result = new System.ServiceModel.Channels.CustomBinding();
                System.ServiceModel.Channels.TextMessageEncodingBindingElement textBindingElement = new System.ServiceModel.Channels.TextMessageEncodingBindingElement();
                textBindingElement.MessageVersion = System.ServiceModel.Channels.MessageVersion.CreateVersion(System.ServiceModel.EnvelopeVersion.Soap12, System.ServiceModel.Channels.AddressingVersion.None);
                result.Elements.Add(textBindingElement);
                System.ServiceModel.Channels.HttpTransportBindingElement httpBindingElement = new System.ServiceModel.Channels.HttpTransportBindingElement();
                httpBindingElement.AllowCookies = true;
                httpBindingElement.MaxBufferSize = int.MaxValue;
                httpBindingElement.MaxReceivedMessageSize = int.MaxValue;
                result.Elements.Add(httpBindingElement);
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }

        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.HRServiceSoap))
            {
                return new System.ServiceModel.EndpointAddress("http://**************:9700/HRService.asmx");
            }
            if ((endpointConfiguration == EndpointConfiguration.HRServiceSoap12))
            {
                return new System.ServiceModel.EndpointAddress("http://**************:9700/HRService.asmx");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }

        public enum EndpointConfiguration
        {

            HRServiceSoap,

            HRServiceSoap12,
        }
    }

    [System.Xml.Serialization.XmlSchemaProviderAttribute(null, IsAny = true)]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("dotnet-svcutil-lib", "*******")]
    public partial class ArrayOfXElement : object, System.Xml.Serialization.IXmlSerializable
    {

        private System.Collections.Generic.List<System.Xml.Linq.XElement> nodesList = new System.Collections.Generic.List<System.Xml.Linq.XElement>();

        public ArrayOfXElement()
        {
        }

        public virtual System.Collections.Generic.List<System.Xml.Linq.XElement> Nodes
        {
            get
            {
                return this.nodesList;
            }
        }

        public virtual System.Xml.Schema.XmlSchema GetSchema()
        {
            throw new System.NotImplementedException();
        }

        public virtual void WriteXml(System.Xml.XmlWriter writer)
        {
            System.Collections.Generic.IEnumerator<System.Xml.Linq.XElement> e = nodesList.GetEnumerator();
            for (
            ; e.MoveNext();
            )
            {
                ((System.Xml.Serialization.IXmlSerializable)(e.Current)).WriteXml(writer);
            }
        }

        public virtual void ReadXml(System.Xml.XmlReader reader)
        {
            for (
            ; (reader.NodeType != System.Xml.XmlNodeType.EndElement);
            )
            {
                if ((reader.NodeType == System.Xml.XmlNodeType.Element))
                {
                    System.Xml.Linq.XElement elem = new System.Xml.Linq.XElement("default");
                    ((System.Xml.Serialization.IXmlSerializable)(elem)).ReadXml(reader);
                    Nodes.Add(elem);
                }
                else
                {
                    reader.Skip();
                }
            }
        }
    }
}