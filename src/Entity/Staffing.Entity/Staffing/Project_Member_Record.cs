﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 项目成员记录
/// </summary>
[Table("project_member_record")]
public class Project_Member_Record
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 项目Id
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 证件类型
    /// </summary>
    public IdCardType IdentityCardType { get; set; } = IdCardType.身份证;

    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdentityCard { get; set; } = string.Empty;

    /// <summary>
    /// 身份证姓名
    /// </summary>
    public string IdentityCardName { get; set; } = string.Empty;

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ProjectMemberRecordType Type { get; set; }

    /// <summary>
    /// 应聘职位
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 入职部门
    /// </summary>
    public string? Department { get; set; }

    /// <summary>
    /// 入职时间
    /// </summary>
    public DateOnly? EntryTime { get; set; }

    /// <summary>
    /// 离职时间
    /// </summary>
    public DateOnly? QuitTime { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Project Project { get; set; } = default!;
}
