﻿using Config.CommonModel.Business;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Staffing.Model.Callback
{

    /// <summary>
    /// 导入指定员工的企微群参数
    /// </summary>
    public class QWLoadGroupByEmpRequest
    {
        /// <summary>
        /// 员工手机号
        /// </summary>
        public string? Mobile { get; set; } 
    }

    /// <summary>
    /// 导入指定群ID的企微群参数
    /// </summary>
    public class QWLoadGroupByIdRequest
    {
        /// <summary>
        /// 企微群ID
        /// </summary>
        public string? GropChatId { get; set; }
    }

    /// <summary>
    /// 导入指定员工的企微群的响应结果
    /// </summary>
    public class QWLoadGroupByEmpResponse: GetQWBindingStatsuRsponse
    {
        /// <summary>
        /// 企微群列表
        /// </summary>
        public List<QWLoadGroupByEmpResponseItem>? Groups { get; set; }
    }
    public class QWLoadGroupByEmpResponseItem
    {
        /// <summary>
        /// 群ID
        /// </summary>
        public string? Value { get; set; }
        /// <summary>
        /// 群名称
        /// </summary>
        public string? Label { get; set; }
        /// <summary>
        /// 群ID
        /// </summary>
        public string? GropChatId { get; set; }
        /// <summary>
        /// 群名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 群成员数量
        /// </summary>
        public int MemberCount { get; set; }

        /// <summary>
        /// 建群时间
        /// </summary>
        public DateTime CreateTime {  get; set; }
    }
}
