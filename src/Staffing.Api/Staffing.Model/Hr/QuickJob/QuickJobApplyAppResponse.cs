﻿using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;

namespace Staffing.Model.Hr.QuickJob;

public class QuickJobApplyAppResponse
{
    /// <summary>
    /// 零工市场id
    /// </summary>
    public string QuickJobHrId { get; set; } = default!;
    /// <summary>
    /// 用户状态
    /// </summary>
    public UserStatus Status { get; set; }

    /// <summary>
    /// 审批拒绝说明
    /// </summary>
    public string? Rejection { get; set; }

    /// <summary>
    /// 审核状态
    /// </summary>
    public AppApplyStatus ApplyStatus { get; set; } = AppApplyStatus.未申请;
}

public class QuickJobResponse : QueryResponse
{
    public List<QuickJobAuditInfo> Rows { get; set; } = default!;
}

public class QuickJobAuditInfo
{
    public string Id { get; set; } = default!;

    public string UserId { get; set; } = default!;

    public string QuickJobHrId { get; set; } = default!;

    /// <summary>
    /// 状态
    /// </summary>
    public AppApplyStatus Status { get; set; }

    /// <summary>
    /// 业务信息json
    /// </summary>
    public QuickJobInfoResponse Info { get; set; } = default!;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 管理员Id
    /// </summary>
    public string AdminId { get; set; } = default!;

    /// <summary>
    /// 管理员Name
    /// </summary>
    public string AdminName { get; set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;
}

public class QuickJobInfoResponse
{
    /// <summary>
    /// 项目行业 = 项目执行/发布项目（项目行业）
    /// </summary>
    public string? Industry { get; set; }

    /// <summary>
    /// 联系人姓名 = 项目执行 /编辑名片（姓名）
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 联系人手机 = 项目执行 /编辑名片（有手机号码不可修改）
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 联系人电子邮箱 = 项目执行 /编辑名片（邮箱）
    /// </summary>
    public string Email { get; set; } = default!;

    /// <summary>
    /// 对接单位
    /// </summary>
    public string? ContactCompany { get; set; }

    /// <summary>
    /// 推荐码
    /// </summary>
    public string? ReferralCode { get; set; }

    /// <summary>
    /// 营业执照
    /// </summary>
    public string? CompanyLicense { get; set; }

    /// <summary>
    /// 公司介绍
    /// </summary>
    public string? CompanyInfo { get; set; }

    /// <summary>
    /// 纳税人类型
    /// </summary>
    public string? TaxpayerType { get; set; }

    /// <summary>
    /// 企业logo
    /// </summary>
    public string LogoUrl { get; set; } = string.Empty;

    /// <summary>
    /// 公司名称
    /// </summary>
    public string CompanyName { get; set; } = default!;

    /// <summary>
    /// 公司简称
    /// </summary>
    public string CompanyAbbr { get; set; } = string.Empty;

    /// <summary>
    /// 工作地址
    /// </summary>
    public string CompanyAddress { get; set; } = default!;

    /// <summary>
    /// 工作地址坐标 - X
    /// </summary>
    public double? Lat { get; set; }

    /// <summary>
    /// 工作地址坐标 - Y
    /// </summary>
    public double? Lng { get; set; }

    /// <summary>
    /// 企业性质
    /// </summary>
    public string? Nature { get; set; }

    /// <summary>
    /// 企业规模
    /// </summary>
    public string? Scale { get; set; }

    /// <summary>
    /// 融资阶段
    /// </summary>
    public string? Capital { get; set; }

    /// <summary>
    /// 地区ID
    /// </summary>
    public string RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 职位信息
    /// </summary>
    public PostInfo? PostInfo { get; set; }
}

public class QuickJobAuditDetail
{
    /// <summary>
    /// 状态
    /// </summary>
    public AppApplyStatus Status { get; set; }

    /// <summary>
    /// 业务信息json
    /// </summary>
    public QuickJobInfo Info { get; set; } = default!;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;
}
