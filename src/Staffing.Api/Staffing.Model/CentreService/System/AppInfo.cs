using Config.Enums;
using Staffing.Entity.Staffing;
using Config.CommonModel.Business;

namespace Staffing.Model.CentreService.System;

public class GetSystemAppTree
{
    /// <summary>
    /// 显示
    /// </summary>
    public bool? Show { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ActiveStatus? Status { get; set; }
}

public class GetSystemAppTreeResponse
{
    public List<GetSystemAppTreeInfo> Rows { get; set; } = default!;
}

public class GetSystemAppTreeInfo
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 父Id
    /// </summary>
    public string? ParentId { get; set; }

    /// <summary>
    /// 应用Id
    /// </summary>
    public string? AppId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ActiveStatus Status { get; set; }

    /// <summary>
    /// 子节点
    /// </summary>
    public List<GetSystemAppTreeInfo>? Children { get; set; }
}

public class SystemAppInfo
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 应用Id
    /// </summary>
    public string? AppId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 别名
    /// </summary>
    public string Alias { get; set; } = string.Empty;

    /// <summary>
    /// 图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// Url
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public SystemAppType Type { get; set; }

    /// <summary>
    /// 概要
    /// </summary>
    public string? Abstract { get; set; }

    /// <summary>
    /// 短概要
    /// </summary>
    public string? ShortAbstract { get; set; }

    /// <summary>
    /// 应用详情
    /// </summary>
    public string? AppInfo { get; set; }

    /// <summary>
    /// 使用说明
    /// </summary>
    public string? Manual { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ActiveStatus Status { get; set; }

    /// <summary>
    /// 显示
    /// </summary>
    public bool Show { get; set; }

    /// <summary>
    /// 父Id
    /// </summary>
    public string? ParentId { get; set; }

    /// <summary>
    /// 层级
    /// </summary>
    public string Level { get; set; } = string.Empty;

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 操作者Id
    /// </summary>
    public string AdminId { get; set; } = string.Empty;

    /// <summary>
    /// 操作者名称
    /// </summary>
    public string AdminName { get; set; } = string.Empty;

    /// <summary>
    /// 所需权限
    /// </summary>
    public string? PowersStr { get; set; }

    /// <summary>
    /// 所需权限
    /// </summary>
    public List<string>? Powers { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = default!;
}

public class AddSystemApp
{
    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 别名
    /// </summary>
    public string Alias { get; set; } = string.Empty;

    /// <summary>
    /// 图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// Url
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public SystemAppType Type { get; set; } = SystemAppType.模块;

    /// <summary>
    /// 概要
    /// </summary>
    public string? Abstract { get; set; }

    /// <summary>
    /// 短概要
    /// </summary>
    public string? ShortAbstract { get; set; }

    /// <summary>
    /// 应用详情
    /// </summary>
    public string? AppInfo { get; set; }

    /// <summary>
    /// Manual
    /// </summary>
    public string? Manual { get; set; }

    /// <summary>
    /// 显示
    /// </summary>
    public bool Show { get; set; }

    /// <summary>
    /// 父Id，不传或0代表根节点
    /// </summary>
    public string? ParentId { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 所需权限
    /// </summary>
    public List<string>? Powers { get; set; }
}

public class AddSystemAppResponse
{
    /// <summary>
    /// 应用Id
    /// </summary>
    public string? AppId { get; set; }
}

public class UpdateSystemApp : AddSystemApp
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }
}

public class DeleteSystemApp
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }
}

public class UpdateSystemAppStatus
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ActiveStatus? Status { get; set; }
}