
using Config;
using Infrastructure.Common;
using Microsoft.EntityFrameworkCore;
using Senparc.NeuChar.Entities;
using Senparc.Weixin.MP.AdvancedAPIs;
using Senparc.Weixin.MP.AdvancedAPIs.User;
using Senparc.Weixin.MP.Entities;
using Senparc.Weixin.MP.Entities.Request;
using Senparc.Weixin.MP.MessageHandlers;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Staffing.Api.MessageHandlers;

/// <summary>
/// 自定义MessageHandler
/// 把MessageHandler作为基类，重写对应请求的处理方法
/// </summary>
public partial class CustomMessageHandler : MessageHandler<CustomMessageContext>  /*如果不需要自定义，可以直接使用：MessageHandler<DefaultMpMessageContext> */
{
    /*
     * 重要提示：v1.5起，MessageHandler提供了一个DefaultResponseMessage的抽象方法，
     * DefaultResponseMessage必须在子类中重写，用于返回没有处理过的消息类型（也可以用于默认消息，如帮助信息等）；
     * 其中所有原OnXX的抽象方法已经都改为虚方法，可以不必每个都重写。若不重写，默认返回DefaultResponseMessage方法中的结果。
     */

    /// <summary>
    /// 为中间件提供生成当前类的委托
    /// </summary>
    public static Func<Stream, PostModel, int, IServiceProvider, CustomMessageHandler> GenerateMessageHandler = (stream, postModel, maxRecordCount, serviceProvider)
                     => new CustomMessageHandler(stream, postModel, maxRecordCount, false /* 是否只允许处理加密消息，以提高安全性 */, serviceProvider: serviceProvider);

    /// <summary>
    /// 自定义 MessageHandler
    /// </summary>
    /// <param name="inputStream"></param>
    /// <param name="postModel"></param>
    /// <param name="maxRecordCount"></param>
    /// <param name="onlyAllowEncryptMessage"></param>
    /// <param name="serviceProvider"></param>
    public CustomMessageHandler(Stream inputStream, PostModel postModel, int maxRecordCount = 0, bool onlyAllowEncryptMessage = false, IServiceProvider? serviceProvider = null)
        : base(inputStream, postModel, maxRecordCount, onlyAllowEncryptMessage, serviceProvider: serviceProvider)
    {
        //这里设置仅用于测试，实际开发可以在外部更全局的地方设置，
        //比如MessageHandler<MessageContext>.GlobalGlobalMessageContext.ExpireMinutes = 3。
        GlobalMessageContext.ExpireMinutes = 3;

        OnlyAllowEncryptMessage = true;//是否只允许接收加密消息，默认为 false
    }


    /// <summary>
    /// 订阅（关注）事件
    /// </summary>
    /// <returns></returns>
    public async override Task<IResponseMessageBase> OnEvent_SubscribeRequestAsync(RequestMessageEvent_Subscribe requestMessage)
    {
        //data-miniprogram-path=\"pages/index/index\"
        // var nkp = "<a data-miniprogram-appid=\"wx8b4490f70e538c0e\">“诺快聘”</a>";
        // 打开{nkp}小程序，入职快人一步
        var responseMessage = ResponseMessageBase.CreateFromRequestMessage<ResponseMessageText>(requestMessage);
        var msg = $@"Hi，欢迎关注“诺聘”官方公众号，这里会定期发布河北省政企事业单位招聘信息，想要找工作的小伙伴们不要错过哦~
好工作，从上诺聘开始~

诺聘手机官网：https://m.nuopin.cn/

诺聘官方网站：https://www.nuopin.cn/

更多热招岗位尽在诺聘APP：https://s.nuopin.cn/B28M5

也可添加【诺聘专属求职群】，给您提供更精准的工作
求职路上，有我在，不迷茫~";

        // responseMessage.Content = msg;

        _ = WeChatH5Subscribe(requestMessage.FromUserName);

        CustomApi.SendText(Constants.WeChatH5AppId, requestMessage.FromUserName, msg);
        CustomApi.SendImage(Constants.WeChatH5AppId, requestMessage.FromUserName, "0BF1c9GQgGgy32iKjfBWscdrLY17lw1pbMcGI_Ws6RBGk2zssKOQlLdlT4p6tDM3");

        return await Task.FromResult(responseMessage);
    }

    /// <summary>
    /// 退订
    /// 实际上用户无法收到非订阅账号的消息，所以这里可以随便写。
    /// unsubscribe事件的意义在于及时删除网站应用中已经记录的OpenID绑定，消除冗余数据。并且关注用户流失的情况。
    /// </summary>
    /// <returns></returns>
    public override async Task<IResponseMessageBase> OnEvent_UnsubscribeRequestAsync(RequestMessageEvent_Unsubscribe requestMessage)
    {
        // MyRedisHelper.MyRedis.Set("abc:tuiding", JsonSerializer.SerializeToString(requestMessage));
        var responseMessage = base.CreateResponseMessage<ResponseMessageText>();
        responseMessage.Content = string.Empty;

        _ = Task.Run(() => WeChatH5UnSubscribe(requestMessage.FromUserName));

        return await Task.FromResult(responseMessage);
    }

    public override IResponseMessageBase DefaultResponseMessage(IRequestMessageBase requestMessage)
    {
        /* 所有没有被处理的消息会默认返回这里的结果，
        * 因此，如果想把整个微信请求委托出去（例如需要使用分布式或从其他服务器获取请求），
        * 只需要在这里统一发出委托请求，如：
        * var responseMessage = MessageAgent.RequestResponseMessage(agentUrl, agentToken, RequestDocument.ToString());
        * return responseMessage;
        */

        if (requestMessage.MsgType == Senparc.NeuChar.RequestMsgType.Text)
        {
            var ct = requestMessage as RequestMessageText;
            if (ct?.Content?.Trim() == "召唤神兽")
            {
                var resp = this.CreateResponseMessage<ResponseMessageImage>();
                resp.Image.MediaId = "0BF1c9GQgGgy32iKjfBWscdrLY17lw1pbMcGI_Ws6RBGk2zssKOQlLdlT4p6tDM3";
                return resp;
            }
            else if (ct?.Content?.Trim() == "人才测评")
            {
                var msg = $@"方式一：手机点链接作答
方式二：电脑输入链接作答

测评链接：https://bsurl.cn/v2/ETGV4Dms";
                CustomApi.SendText(Constants.WeChatH5AppId, requestMessage.FromUserName, msg);
                // CustomApi.SendImage(Constants.WeChatH5AppId, requestMessage.FromUserName, "tGDv9Y8jgXs-pBqHrhKvFr5V8QjQsdMTiZum0NHvMa7VNKnMjru6I7blEJ6saaT2");

                var resp = this.CreateResponseMessage<ResponseMessageText>();
                //                 resp.Content = $@"职业性格测评（100个）
                // https://bsurl.cn/v2/FBeu3O9Q";
                return resp;
            }
            // else if (ct?.Content?.Trim() == "简历模板哈哈")
            // {
            //     var resp = this.CreateResponseMessage<ResponseMessageText>();
            //     var imageList = new List<string>
            //     {
            //     };
            //     var oc = "<a href=\"https://noah-resources.oss-cn-beijing.aliyuncs.com/npresumetemp/%E5%88%9B%E6%84%8F%E9%BB%91%E8%89%B2%E6%A9%99%E8%89%B2%E7%AE%80%E5%8E%86%E6%A8%A1%E6%9D%BF.docx\">点击下载简历模板</a>";
            //     resp.Content = oc;
            //     return resp;
            // }
        }

        var responseMessage = this.CreateResponseMessage<ResponseMessageText>();
        // responseMessage.Content = $"这条消息来自DefaultResponseMessage。\r\n您收到这条消息，表明该公众号没有对【{requestMessage.MsgType}】类型做处理。";
        // responseMessage.Content = @"Hi，欢迎关注诺聘官方公众号，如果您有任何问题可直接拨打客服电话************进行咨询。好工作，从上诺聘开始~";
        responseMessage.Content = string.Empty;
        return responseMessage;
    }

    #region 重写执行过程

    public override async Task OnExecutingAsync(CancellationToken cancellationToken)
    {
        //演示：MessageContext.StorageData
        // MyRedisHelper.MyRedis.Set("abc:OnExecutingAsync", DateTime.Now.ToShortTimeString());
        // var currentMessageContext = await base.GetUnsafeMessageContext();//为了在分布式缓存下提高读写效率，使用此方法，如果需要获取实时数据，应该使用 base.GetCurrentMessageContext()
        // if (currentMessageContext.StorageData == null || !(currentMessageContext.StorageData is int))
        // {
        //     currentMessageContext.StorageData = (int)0;
        //     //await GlobalMessageContext.UpdateMessageContextAsync(currentMessageContext);//储存到缓存
        // }
        await base.OnExecutingAsync(cancellationToken);
    }

    public override async Task OnExecutedAsync(CancellationToken cancellationToken)
    {
        //演示：MessageContext.StorageData
        // MyRedisHelper.MyRedis.Set("abc:OnExecutedAsync", DateTime.Now.ToShortTimeString());
        // var currentMessageContext = await base.GetUnsafeMessageContext();//为了在分布式缓存下提高读写效率，使用此方法，如果需要获取实时数据，应该使用 base.GetCurrentMessageContext()
        // currentMessageContext.StorageData = ((int)currentMessageContext.StorageData) + 1;
        // GlobalMessageContext.UpdateMessageContext(currentMessageContext);//储存到缓存
        await base.OnExecutedAsync(cancellationToken);
    }

    #endregion

    private async Task WeChatH5Subscribe(string openId)
    {
        var accessToken = string.Empty;
        UserInfoJson? userInfo = null;
        try
        {
            //查询用户unionid
            var contextFactory = ServiceLocator.Instance.GetService<IDbContextFactory<StaffingContext>>();
            using var _context = contextFactory!.CreateDbContext();

            var wechat = _context.WeChat.FirstOrDefault(x => x.WeChatH5OpenId == openId);
            var unionId = string.Empty;

            if (wechat == null)
            {
                var _weChatHelper = ServiceLocator.Instance.GetService<WeChatHelper>();
                userInfo = await _weChatHelper!.GetMpUserInfo(Constants.WeChatH5AppId, openId);

                if (string.IsNullOrWhiteSpace(userInfo.unionid))
                {
                    var _log = ServiceLocator.Instance.GetService<LogManager>();
                    _log!.Error("公众号订阅消息出错", "未能拿到unionid", openId);
                    return;
                }

                unionId = userInfo.unionid;
                wechat = _context.WeChat.FirstOrDefault(x => x.WeChatId == unionId);
            }

            if (wechat == null)
            {
                wechat = new WeChat
                {
                    WeChatId = unionId
                };
                _context.Add(wechat);
            }

            wechat.WeChatH5OpenId = openId;
            wechat.WeChatH5Subscribe = true;
            wechat.UpdatedTime = DateTime.Now;

            var user = _context.User_OpenId.Where(x => x.UnionId == wechat.WeChatId)
            .Select(s => s.User)
            .FirstOrDefault();

            // var user = _context.User.Where(x => x.WeChatId == wechat.WeChatId).FirstOrDefault();
            if (user != null)
            {
                user.WeChatH5Subscribe = true;
                user.WeChatH5OpenId = openId;
            }

            _context.SaveChanges();

            //订阅状态redis实时更新
            MyRedis.Client.HSet(RedisKey.WeChatMpSub, openId, true);
        }
        catch (Exception e)
        {
            var _log = ServiceLocator.Instance.GetService<LogManager>();
            _log!.Error("公众号订阅消息出错", $"{e.Message}:{accessToken}:{userInfo?.unionid}", openId);
        }
    }

    private void WeChatH5UnSubscribe(string openId)
    {
        try
        {
            var contextFactory = ServiceLocator.Instance.GetService<IDbContextFactory<StaffingContext>>();
            using var _context = contextFactory!.CreateDbContext();

            var wechat = _context.WeChat.FirstOrDefault(x => x.WeChatH5OpenId == openId);

            if (wechat != null)
            {
                wechat.WeChatH5Subscribe = false;
                wechat.UpdatedTime = DateTime.Now;
            }

            var user = _context.User.Where(x => x.WeChatH5OpenId == openId).FirstOrDefault();
            if (user != null)
            {
                user.WeChatH5Subscribe = false;
                user.WeChatH5OpenId = openId;
            }

            _context.SaveChanges();

            //订阅状态redis实时更新
            MyRedis.Client.HSet(RedisKey.WeChatMpSub, openId, false);
        }
        catch (Exception e)
        {
            var _log = ServiceLocator.Instance.GetService<LogManager>();
            _log!.Error("订阅消息出错", e.Message, openId);
        }
    }
}