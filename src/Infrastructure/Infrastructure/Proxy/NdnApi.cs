using Config;
using Config.CommonModel.Ndn;
using Flurl;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using ServiceStack.Text;

namespace Infrastructure.Proxy;

/// <summary>
/// 数字诺亚
/// </summary>
[Service(ServiceLifetime.Transient)]
public class NdnApi
{
    private readonly ConfigManager _config;
    private readonly LogManager _log;
    public NdnApi(IOptionsSnapshot<ConfigManager> config, LogManager log)
    {
        _config = config.Value;
        _log = log;
    }

    /// <summary>
    /// 查询员工帐套信息
    /// </summary>
    /// <param name="hrNo"></param>
    /// <returns></returns>
    public async Task<NdnEmployee> GetEmployee(string hrNo)
    {
        var result = await Get<NdnEmployee>("project/novapin/getEmployeeInfoByHrNo",
        new { hrNo = hrNo });

        return result!;
    }

    /// <summary>
    /// 创建项目
    /// </summary>
    /// <param name="project"></param>
    /// <returns></returns>
    public async Task<CreateProjectResponse> CreateProject(CreateProject project)
    {
        var result = await Post<CreateProjectResponse>("project/novapin/createNovapinProject", project);

        return result!;
    }

    /// <summary>
    /// 根据项目编码查询项目信息
    /// </summary>
    /// <param name="projectCode"></param>
    /// <returns></returns>
    public async Task<GetProjectResponse?> GetProjectInfo(string projectCode)
    {
        try
        {
            var result = await Get<GetProjectResponse?>("project/novapin/getProjectInfoByCode",
                    new { projectCode = projectCode });

            // 处理预算
            if (result?.projectBudget != null)
                result.budget = result?.projectBudget?.FirstOrDefault(x => x.planClassifyName == Constants.Ndn.FrKmName) ?? new ProjectBudget();

            return result!;
        }
        catch (Exception e)
        {
            _log.Error($"根据项目编码查询项目信息报错：{projectCode}", e.Message);
            return null;
        }
    }

    /// <summary>
    /// 冻结/解冻预算
    /// </summary>
    /// <param name="projectCode"></param>
    /// <param name="feeName"></param>
    /// <param name="type"></param>
    /// <param name="amount"></param>
    /// <returns></returns>
    public async Task<bool> FreezeBudget(FreezeBudget request)
    {
        var result = await Post<object>("project/novapin/projectBudgetFreeze", request);

        return true;
    }

    /// <summary>
    /// 报销流程发起（项目类）
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<string> Reimbursement(Reimbursement request)
    {
        var result = await Post<string>("project/novapin/initiateReimbursement", request);

        return result!;
    }

    /// <summary>
    /// 报销流程发起（现金流）
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<string> ReimbursementCashFlow(Reimbursement request)
    {
        var result = await Post<string>("project/novapin/initiateCashFlowReimbursement", request);

        return result!;
    }

    /// <summary>
    /// 服务奖金发起流程
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<string> ServiceBonus(ServiceBonus request)
    {
        var result = await Post<string>("project/novapin/initiateServiceBonus", request);

        return result!;
    }

    /// <summary>
    /// 申请开发票
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<string> InitiateInvoice(InitiateInvoice request)
    {
        var result = await Post<string>("project/novapin/initiateInvoice", request);

        return result!;
    }

    /// <summary>
    /// 查询所有帐套
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<List<GetBooksInfo>> GetBooks(GetBooks model)
    {
        var result = await Get<List<GetBooksInfo>>("project/novapin/getSetBookList", model);

        return result!;
    }

    /// <summary>
    /// 通用Get请求
    /// </summary>
    /// <param name="path"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<T?> Get<T>(string path, object param)
    {
        var token = await GetToken();

        try
        {
            var restr = await $"{_config.Ndn!.ApiDomain}/{path}"
            .WithOAuthBearerToken(token.access_token)
            .SetQueryParams(param)
            .GetStringAsync();

            _log.Info($"数字诺亚Get请求", $"{path}参数：{JsonSerializer.SerializeToString(param)}", $"{restr}");

            if (string.IsNullOrWhiteSpace(restr))
                return default;

            var result = JsonSerializer.DeserializeFromString<NdnData<T>>(restr);
            CheckResult(result);

            return result.data!;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            _log.Error("数字诺亚Get请求失败", $"httpstatus:{ex.StatusCode}，error:{error}，请求内容：{JsonSerializer.SerializeToString(param)}");
            throw new Exception($"httpstatus:{ex.StatusCode}，error:{error}，请求内容：{JsonSerializer.SerializeToString(param)}");
        }
        catch (NdnApiException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _log.Error("数字诺亚Get请求失败", $"error:{Tools.GetErrMsg(ex)}，请求内容：{JsonSerializer.SerializeToString(param)}");
            throw new Exception($"error:{Tools.GetErrMsg(ex)}，请求内容：{JsonSerializer.SerializeToString(param)}");
        }
    }

    /// <summary>
    /// 通用Post请求
    /// </summary>
    /// <param name="path"></param>
    /// <param name="param"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<T?> Post<T>(string path, object param)
    {
        var token = await GetToken();

        try
        {
            var restr = await $"{_config.Ndn!.ApiDomain}/{path}"
            .WithOAuthBearerToken(token.access_token)
            .PostJsonAsync(param).ReceiveString();

            _log.Info($"数字诺亚Post请求", $"{path}参数：{JsonSerializer.SerializeToString(param)}", $"{restr}");

            if (string.IsNullOrWhiteSpace(restr))
                return default;

            var result = JsonSerializer.DeserializeFromString<NdnData<T>>(restr);
            CheckResult(result);

            return result.data!;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            _log.Error("数字诺亚Post请求", $"{path}参数：{JsonSerializer.SerializeToString(param)}", $"httpstatus:{ex.StatusCode}，error:{error}");
            throw new Exception($"httpstatus:{ex.StatusCode}，error:{error}，请求内容：{JsonSerializer.SerializeToString(param)}");
        }
        catch (NdnApiException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _log.Error("数字诺亚Post请求", $"{path}参数：{JsonSerializer.SerializeToString(param)}", $"error:{ex.Message}");
            throw new Exception($"error:{ex.Message}，请求内容：{JsonSerializer.SerializeToString(param)}");
        }
    }

    /// <summary>
    /// 检测相应结果
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="result"></param>
    /// <exception cref="Exception"></exception>
    public void CheckResult<T>(NdnData<T> result)
    {
        if (result.code != 200)
            throw new NdnApiException(result.msg, result.code.ToString());
    }

    /// <summary>
    /// 数字诺亚获取token
    /// </summary>
    /// <returns></returns>
    public async Task<NdnToken> GetToken()
    {
        var cacheKey = $"ndn:token";

        var result = MyRedis.Client.Get<NdnToken?>(cacheKey);

        if (!string.IsNullOrWhiteSpace(result?.access_token))
            return result;

        using var locker = await MyRedis.Lock("getndntoken", 30);

        if (locker == null)
            throw new Exception("获取数字诺亚token获取redis锁失败");

        result = MyRedis.Client.Get<NdnToken?>(cacheKey);

        if (!string.IsNullOrWhiteSpace(result?.access_token))
            return result;

        string url = $"{_config.Ndn!.OAuthDomain}/oauth/token";

        try
        {
            var str = await url.PostUrlEncodedAsync(new
            {
                grant_type = _config.Ndn.GrantType,
                client_id = _config.Ndn.ClientId,
                client_secret = _config.Ndn.ClientSecret
            }).ReceiveString();

            result = JsonSerializer.DeserializeFromString<NdnToken>(str);

            if (string.IsNullOrWhiteSpace(result?.access_token))
                throw new Exception("获取数字诺亚token失败");

            MyRedis.Client.Set(cacheKey, result, result.expires_in / 2);

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            _log.Error("获取数字诺亚token失败", $"httpstatus:{ex.StatusCode}_{error}");
            throw;
        }
    }

    public class NdnToken
    {
        public string? access_token { get; set; }
        public int expires_in { get; set; }
        public string? token_type { get; set; }
        public string? scope { get; set; }
    }

    public class NdnData<T>
    {
        public int code { get; set; }
        public string msg { get; set; } = string.Empty;
        public T? data { get; set; }
    }
}

public class NdnApiException : CustomException
{
    public NdnApiException(string? message, string errorCode = "") : base(message, errorCode) { }
    public NdnApiException(string? message, Exception inner) : base(message, inner) { }
}