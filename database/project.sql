-- ----------------------------
-- 代招企业
-- ----------------------------
DROP TABLE IF EXISTS `user_agent_ent`;
CREATE TABLE `user_agent_ent` (
  `AgentEntId` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL,
  `Name` varchar(255) NOT NULL COMMENT '企业名称',
  `Abbr` varchar(50) NOT NULL COMMENT '企业简称',
  `Display` int(2) NOT NULL COMMENT '展示项',
  `DisplayName` varchar(255) NOT NULL COMMENT '展示名称',
  `LogoUrl` varchar(1024) NULL COMMENT '企业Logo',
  `AuthorizationUrl` varchar(1024) NULL COMMENT '企业授权证明',
  `Industry` int(3) NOT NULL COMMENT '公司行业',
  `Nature` int(3) NOT NULL COMMENT '企业性质',
  `Scale` int(3) NOT NULL COMMENT '企业规模',
  `Capital` int(3) NOT NULL COMMENT '融资阶段',
  `Describe` varchar(5000) CHARACTER SET utf8mb4 NULL COMMENT '描述',
  `Status` int(4) NOT NULL COMMENT '状态',
  `RegionId` varchar(15) NOT NULL COMMENT '地区Id',
  `Address` varchar(1024) NULL COMMENT '地址',
  `Location` point NOT NULL DEFAULT (point(0,0)),
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`AgentEntId`),
  KEY `IX_user_agent_ent_UserId` (`UserId`),
  KEY `IX_user_agent_ent_Name` (`Name`),
  KEY `IX_user_agent_ent_RegionId` (`RegionId`),
  KEY `IX_user_agent_ent_Status` (`Status`),
  KEY `IX_user_agent_ent_Industry` (`Industry`),
  KEY `IX_user_agent_ent_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 项目
-- ----------------------------
DROP TABLE IF EXISTS `project`;
CREATE TABLE `project` (
  `ProjectId` varchar(18) NOT NULL,
  `AutoId` int(11) NOT NULL AUTO_INCREMENT,
  `AgentEntId` varchar(18) NOT NULL COMMENT '代招企业Id',
  `HrId` varchar(18) NOT NULL,
  `Name` varchar(255) NOT NULL COMMENT '项目名称',
  `Level` int(2) NOT NULL DEFAULT 0 COMMENT '项目级别',
  `Type` int(2) NOT NULL DEFAULT 0 COMMENT '项目类型',
  `Industry` int(2) NOT NULL DEFAULT 0 COMMENT '项目行业',
  `WorkCycleType` int(2) NOT NULL DEFAULT 0 COMMENT '工作周期类型',
  `ClearingType` int(2) NOT NULL DEFAULT 0 COMMENT '结算方式',
  `ClearingChannel` int(2) NOT NULL DEFAULT 0 COMMENT '结算渠道',
  `NuoId` varchar(255) NOT NULL COMMENT '诺亚项目编码',
  `Describe` varchar(2000) CHARACTER SET utf8mb4 NULL COMMENT '描述',
  `RegionData` text NULL COMMENT '地区Json',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '状态',
  `EndTime` datetime(6) NOT NULL DEFAULT NOW(6) COMMENT '项目截止日期',
  `ShareInternal` int(2) NOT NULL DEFAULT 0 COMMENT '分享公司内部',
  `SharePlatform` int(2) NOT NULL DEFAULT 0 COMMENT '分享给平台',
  `Share` bit NOT NULL DEFAULT 0 COMMENT '是否协同',
  `ShareAnyone` int(2) NOT NULL DEFAULT 0 COMMENT '全网公开',
  `PaymentNode` int(2) NULL COMMENT '交付类型',
  `PaymentDays` int(6) NULL COMMENT '打款天数',
  `PaymentType` int(2) NULL COMMENT '打款方式',
  `SharePost` bit NOT NULL DEFAULT 0 COMMENT '是否有协同的职位（项目大厅检索用）',
  `Deleted` bit NOT NULL DEFAULT 0 COMMENT '删除',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`ProjectId`),
  UNIQUE KEY `IX_project_AutoId` (`AutoId`),
  KEY `IX_project_AgentEntId` (`AgentEntId`),
  KEY `IX_project_HrId` (`HrId`),
  KEY `IX_project_Share` (`Share`),
  KEY `IX_project_Status` (`Status`),
  KEY `IX_project_NuoId` (`NuoId`),
  KEY `IX_project_Deleted` (`Deleted`),
  KEY `IX_project_SharePost` (`SharePost` DESC),
  KEY `IX_project_EndTime` (`EndTime` DESC),
  KEY `IX_project_UpdatedTime` (`UpdatedTime` DESC),
  KEY `IX_project_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 项目扩展表
-- ----------------------------
DROP TABLE IF EXISTS `project_extend`;
CREATE TABLE `project_extend` (
  `ProjectId` varchar(18) NOT NULL,
  `SyncNum` int(9) NOT NULL COMMENT '协同次数',
  `RegistrationNum` int(9) NOT NULL COMMENT '报名人数',
  `DeliveriesNum` int(9) NOT NULL COMMENT '交付人数',
  PRIMARY KEY (`ProjectId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 项目地区
-- ----------------------------
DROP TABLE IF EXISTS `project_region`;
CREATE TABLE `project_region` (
  `Id` varchar(18) NOT NULL,
  `ProjectId` varchar(18) NOT NULL COMMENT '项目Id',
  `RegionId` varchar(15) NOT NULL COMMENT '地区Id',
  PRIMARY KEY (`Id`),
  KEY `IX_project_region_ProjectId` (`ProjectId`),
  KEY `IX_project_region_RegionId` (`RegionId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 项目成员
-- ----------------------------
DROP TABLE IF EXISTS `project_member`;
CREATE TABLE `project_member` (
  `Id` varchar(18) NOT NULL,
  `ProjectId` varchar(18) NOT NULL,
  `IdentityCardType` int(2) NOT NULL DEFAULT 0 COMMENT '证件类型',
  `IdentityCard` varchar(20) NOT NULL COMMENT '身份证号',
  `IdentityCardName` varchar(50) NOT NULL DEFAULT '' COMMENT '身份证名字',
  `UserId` varchar(18) NOT NULL,
  `Mobile` varchar(20) NULL COMMENT '手机号',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '状态',
  `Source` int(2) NOT NULL DEFAULT 0 COMMENT '用户来源',
  `Birthday` date NULL COMMENT '生日',
  `Sex` int(2) NULL COMMENT '性别',
  `RegionId` varchar(15) NOT NULL DEFAULT '' COMMENT '地区Id',
  `EmploymentMode` int(2) NOT NULL COMMENT '用工形式',
  `InductionTimes` int(3) NOT NULL COMMENT '入职次数',
  `ProbationMonth` int(3) NOT NULL COMMENT '试用期（月）',
  `ContractNum` int(6) NOT NULL COMMENT '合同数量',
  `PostName` varchar(255) NULL COMMENT '应聘职位',
  `Department` varchar(255) NULL COMMENT '入职部门',
  `EntryTime` date NULL COMMENT '入职时间',
  `QuitTime` date NULL COMMENT '离职时间',
  `AuditTime` datetime(6) NULL COMMENT '审批通过时间',
  `QuitStatus` int(2) NOT NULL DEFAULT 0 COMMENT '离职状态',
  `EMail` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `Describe` varchar(1024) CHARACTER SET utf8mb4 COMMENT '描述',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_project_member_UserId` (`UserId` DESC),
  KEY `IX_project_member_IdentityCard` (`IdentityCard` DESC),
  KEY `IX_project_member_QuitStatus_QuitTime` (`QuitStatus` DESC, `QuitTime`),
  KEY `IX_project_member_ProjectIdFull` (`ProjectId`, `IdentityCard`, `IdentityCardName`, `PostName`, `Status`, `Mobile`, `CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 项目成员记录
-- ----------------------------
DROP TABLE IF EXISTS `project_member_record`;
CREATE TABLE `project_member_record` (
  `Id` varchar(18) NOT NULL,
  `ProjectId` varchar(18) NOT NULL,
  `IdentityCardType` int(2) NOT NULL DEFAULT 0 COMMENT '证件类型',
  `IdentityCard` varchar(20) NOT NULL COMMENT '身份证号',
  `IdentityCardName` varchar(50) NOT NULL DEFAULT '' COMMENT '身份证名字',
  `Type` int(2) NOT NULL DEFAULT 0 COMMENT '记录类型',
  `Mobile` varchar(20) NULL COMMENT '手机号',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '状态',
  `PostName` varchar(255) NULL COMMENT '应聘职位',
  `Department` varchar(255) NULL COMMENT '入职部门',
  `EntryTime` date NULL COMMENT '入职时间',
  `QuitTime` date NULL COMMENT '离职时间',
  `Describe` varchar(1024) CHARACTER SET utf8mb4 COMMENT '描述',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_project_member_record_ProjectId` (`ProjectId` DESC),
  KEY `IX_project_member_record_IdentityCard` (`IdentityCard` DESC),
  KEY `IX_project_member_record_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 项目面试官
-- ----------------------------
DROP TABLE IF EXISTS `project_interviewer`;
CREATE TABLE `project_interviewer` (
  `Id` varchar(18) NOT NULL,
  `ProjectId` varchar(18) NOT NULL COMMENT '项目Id',
  `Name` varchar(10) NOT NULL COMMENT '姓名',
  `Phone` varchar(15) NOT NULL COMMENT '手机号',
  `Post` varchar(20) NOT NULL COMMENT '职位',
  `Mail` varchar(20) NOT NULL COMMENT '邮箱',
  `OfficeAddress` varchar(100) NOT NULL COMMENT '办公地址',
  PRIMARY KEY (`Id`),
  KEY `IX_project_interviewer_ProjectId` (`ProjectId`),
  KEY `IX_project_interviewer_Phone` (`Phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 职位
-- ----------------------------
DROP TABLE IF EXISTS `post`;
CREATE TABLE `post` (
  `PostId` varchar(18) NOT NULL,
  `AutoId` int(11) NOT NULL AUTO_INCREMENT,
  `ProjectId` varchar(18) NOT NULL COMMENT '项目Id',
  `Name` varchar(255) NOT NULL COMMENT '名称',
  `Describe` text NULL COMMENT '描述',
  `Category` int(11) NOT NULL DEFAULT 0 COMMENT '职位类别',
  `WorkNature` int(2) NOT NULL DEFAULT 0 COMMENT '工作性质',
  `SalaryType` int(2) NOT NULL DEFAULT 0 COMMENT '薪资类型',
  `RecruitNumber` int(11) NOT NULL DEFAULT 0 COMMENT '招聘人数',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '状态',
  `Share` bit NOT NULL DEFAULT 0 COMMENT '是否协同',
  `RegionId` varchar(15) NOT NULL COMMENT '地区Id',
  `Address` varchar(1024) NULL COMMENT '地址',
  `LocationMap` varchar(1024) NULL COMMENT '静态地图',
  `Location` point NOT NULL DEFAULT (point(0,0)),
  `Department` varchar(50) NOT NULL COMMENT '部门',
  `Education` int(2) NOT NULL DEFAULT 0 COMMENT '教育',
  `MinSalary` int(11) NOT NULL DEFAULT 0 COMMENT '薪资要求(最低)',
  `MaxSalary` int(11) NOT NULL DEFAULT 0 COMMENT '薪资要求(最高)',
  `Salary` int(3) NOT NULL DEFAULT 0 COMMENT '几薪',
  `SettlementType` int(2) NULL COMMENT '结算方式（日结、月结）',
  `WorkingDays` varchar(255) NULL COMMENT '工作日',
  `WorkingHours` varchar(255) NULL COMMENT '工作时间',
  `MinMonths` int(11) NULL COMMENT '最少实习月数',
  `DaysPerWeek` int(11) NULL COMMENT '最少周到岗天数',
  `GraduationYear` int(2) NULL COMMENT '毕业年份',
  `Money` decimal(18,2) NULL DEFAULT 0 COMMENT '金额/人',
  `Tags` text NULL COMMENT '标签',
  `Highlights` text NULL COMMENT '职位亮点',
  `Welfare` text NULL COMMENT '福利',
  `WelfareCustom` text NULL COMMENT '福利自定义',
  `WelfareSearch` varchar(1000) NOT NULL COMMENT '福利检索',
  `Sex` int(2) NULL COMMENT '性别',
  `MinAge` int(3) NOT NULL COMMENT '最小年龄',
  `MaxAge` int(3) NOT NULL COMMENT '最大年龄',
  `Deleted` bit NOT NULL DEFAULT 0 COMMENT '删除',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`PostId`),
  UNIQUE KEY `IX_post_AutoId` (`AutoId`),
  KEY `IX_post_ProjectId` (`ProjectId`),
  KEY `IX_post_Category` (`Category`),
  KEY `IX_post_WelfareSearch` (`WelfareSearch`),
  KEY `IX_post_WorkNature` (`WorkNature`),
  KEY `IX_post_RegionId` (`RegionId`),
  KEY `IX_post_Status` (`Status`),
  KEY `IX_post_Deleted` (`Deleted`),
  KEY `IX_post_UpdatedTime` (`UpdatedTime` DESC),
  KEY `IX_post_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 职位扩展表
-- ----------------------------
DROP TABLE IF EXISTS `post_extend`;
CREATE TABLE `post_extend` (
  `PostId` varchar(18) NOT NULL,
  `SyncNum` int(11) NOT NULL COMMENT '协同次数',
  `DeliveryNum` int(11) NOT NULL COMMENT '投递人数累计去重',
  `HrScreeningNum` int(11) NOT NULL COMMENT '简历初筛人数',
  `InterviewerScreeningNum` int(11) NOT NULL COMMENT '面试官筛选人数',
  `InterviewNum` int(11) NOT NULL COMMENT '面试人数',
  `OfferNum` int(11) NOT NULL COMMENT 'Offer人数',
  `InductionNum` int(11) NOT NULL COMMENT '入职人数',
  `ContractNum` int(11) NOT NULL COMMENT '签约人数',
  `SettlementNum` int(11) NOT NULL COMMENT '结算人数',
  PRIMARY KEY (`PostId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 项目大厅
-- ----------------------------
DROP TABLE IF EXISTS `project_hall`;
CREATE TABLE `project_hall` (
  `Id` varchar(18) NOT NULL,
  `ProjectId` varchar(18) NOT NULL COMMENT '项目Id',
  `HrId` varchar(18) NOT NULL COMMENT '用户Id',
  `Type` int(2) NOT NULL COMMENT '类型',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '状态',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_project_hall_ProjectId_Type` (`ProjectId`, `Type`),
  KEY `IX_project_hall_HrId` (`HrId`),
  KEY `IX_project_hall_ProjectId` (`ProjectId`),
  KEY `IX_project_hall_Type` (`Type`),
  KEY `IX_project_hall_Status` (`Status`),
  KEY `IX_project_hall_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 协同项目
-- ----------------------------
DROP TABLE IF EXISTS `project_team`;
CREATE TABLE `project_team` (
  `TeamProjectId` varchar(18) NOT NULL,
  `HrId` varchar(18) NOT NULL,
  `ProjectId` varchar(18) NOT NULL COMMENT '原始项目Id',
  `Type` int(2) NOT NULL DEFAULT 0 COMMENT '类别',
  `Source` int(2) NOT NULL DEFAULT 0 COMMENT '来源',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '状态',
  `UpdatedBy` varchar(18) NOT NULL COMMENT '更新人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`TeamProjectId`),
  KEY `IX_project_team_HrId_Status` (`HrId`, `Status`),
  KEY `IX_project_team_Type` (`Type`),
  KEY `IX_project_team_ProjectId_Status` (`ProjectId`, `Status`),
  KEY `IX_project_team_UpdatedBy` (`UpdatedBy`),
  KEY `IX_project_team_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 协同职位
-- ----------------------------
DROP TABLE IF EXISTS `post_team`;
CREATE TABLE `post_team` (
  `TeamPostId` varchar(18) NOT NULL,
  `TeamProjectId` varchar(18) NOT NULL COMMENT 'Hr项目Id',
  `AutoId` int(11) NOT NULL AUTO_INCREMENT,
  `PostId` varchar(18) NOT NULL COMMENT '原始职位Id',
  `Type` int(2) NOT NULL DEFAULT 0 COMMENT '类别',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '状态',
  `Show` bit NOT NULL DEFAULT 0 COMMENT '上架状态，小程序端是否可以展示',
  `AppletQrCode` varchar(1024) NULL COMMENT '小程序二维码',
  `AppletShortLink` varchar(1024) NULL COMMENT '小程序短链接',
  `AppletShortLinkExp` datetime(6) NULL COMMENT '小程序短链接有效期',
  `UpdatedBy` varchar(18) NOT NULL COMMENT '更新人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`TeamPostId`),
  UNIQUE KEY `IX_post_team_AutoId` (`AutoId`),
  KEY `IX_post_team_Show` (`Show` DESC),
  KEY `IX_post_team_TeamProjectId` (`TeamProjectId`),
  KEY `IX_post_team_PostId` (`PostId`),
  KEY `IX_post_team_Status` (`Status`),
  KEY `IX_post_team_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 协同职位扩展表
-- ----------------------------
DROP TABLE IF EXISTS `post_team_extend`;
CREATE TABLE `post_team_extend` (
  `TeamPostId` varchar(18) NOT NULL,
  `DeliveryNum` int(11) NOT NULL COMMENT '投递人数累计去重',
  `HrScreeningNum` int(11) NOT NULL COMMENT '简历初筛人数',
  `InterviewerScreeningNum` int(11) NOT NULL COMMENT '面试官筛选人数',
  `InterviewNum` int(11) NOT NULL COMMENT '面试人数',
  `OfferNum` int(11) NOT NULL COMMENT 'Offer人数',
  `InductionNum` int(11) NOT NULL COMMENT '入职人数',
  `ContractNum` int(11) NOT NULL COMMENT '签约人数',
  `SettlementNum` int(11) NOT NULL COMMENT '结算人数',
  PRIMARY KEY (`TeamPostId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 职位投递简历
-- ----------------------------
DROP TABLE IF EXISTS `post_delivery`;
CREATE TABLE `post_delivery` (
  `DeliveryId` varchar(18) NOT NULL,
  `SeekerId` varchar(18) NOT NULL COMMENT '求职者Id',
  `TeamPostId` varchar(18) NOT NULL COMMENT 'Hr职位Id，可空',
  `PostId` varchar(18) NOT NULL COMMENT '原始职位Id',
  `PaymentNode` int(2) NULL COMMENT '交付类型',
  `PaymentDays` int(6) NULL COMMENT '打款天数',
  `Money` decimal(18,2) NULL DEFAULT 0 COMMENT '金额/人',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '状态',
  `Source` int(3) NOT NULL DEFAULT 0 COMMENT '来源',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`DeliveryId`),
  KEY `IX_post_delivery_SeekerId_Status` (`SeekerId`, `Status`),
  KEY `IX_post_delivery_PostId` (`PostId`),
  KEY `IX_post_delivery_TeamPostId` (`TeamPostId` DESC),
  KEY `IX_post_delivery_SeekerId` (`SeekerId`),
  KEY `IX_post_delivery_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 职位投诉
-- ----------------------------
DROP TABLE IF EXISTS `post_complaint`;
CREATE TABLE `post_complaint` (
  `Id` varchar(18) NOT NULL,
  `DeliveryId` varchar(18) NOT NULL COMMENT '投递Id',
  `SeekerId` varchar(18) NOT NULL COMMENT '求职者Id',
  `TeamPostId` varchar(18) NOT NULL COMMENT '协同职位Id',
  `Describe` varchar(5000) CHARACTER SET utf8mb4 NULL COMMENT '描述',
  `Type` int(2) NOT NULL DEFAULT 0 COMMENT '类型',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '状态',
  `UpdatedBy` varchar(18) NOT NULL COMMENT '更新人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_post_complaint_DeliveryId` (`DeliveryId` DESC),
  KEY `IX_post_complaint_SeekerId` (`SeekerId`),
  KEY `IX_post_complaint_Type` (`Type`),
  KEY `IX_post_complaint_TeamPostId` (`TeamPostId` DESC),
  KEY `IX_post_complaint_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 项目协同分润表
-- ----------------------------
DROP TABLE IF EXISTS `project_teambounty`;
CREATE TABLE `project_teambounty` (
  `Id` varchar(18) NOT NULL,
  `ProjectId` varchar(18) NOT NULL COMMENT '项目Id',
  `TeamProjectId` varchar(18) NOT NULL COMMENT '协同项目Id',
  `RecruitId` varchar(18) NULL COMMENT '招聘流程Id',
  `TeamPostId` varchar(18) NOT NULL COMMENT '协同职位Id',
  `PostId` varchar(18) NOT NULL COMMENT '原始职位Id',
  `SeekerId` varchar(18) NOT NULL COMMENT '求职者Id',
  `HrId` varchar(18) NOT NULL COMMENT '主创Id',
  `TeamHrId` varchar(18) NOT NULL COMMENT '协同HrId',
  `PaymentNode` int(2) NULL COMMENT '交付类型',
  `PaymentDays` int(6) NULL COMMENT '打款天数',
  `PaymentType` int(2) NULL COMMENT '打款方式',
  `Money` decimal(18,2) NULL DEFAULT 0 COMMENT '金额',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '状态',
  `Source` int(3) NOT NULL DEFAULT 0 COMMENT '来源',
  `SettlementTime` datetime(6) NULL COMMENT '结算时间',
  `SettlementMoney` decimal(18,2) NULL DEFAULT 0 COMMENT '结算金额',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_project_teambounty_Status` (`Status`),
  KEY `IX_project_teambounty_RecruitId` (`RecruitId`),
  KEY `IX_project_teambounty_ProjectId_Full` (`ProjectId`, `TeamProjectId`, `TeamPostId`, `PostId`, `SeekerId`, `HrId`, `TeamHrId`, `Status`, `Source`, `CreatedTime` DESC),
  KEY `IX_project_teambounty_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 项目自流转表
-- ----------------------------
DROP TABLE IF EXISTS `project_automatic`;
CREATE TABLE `project_automatic` (
  `Id` varchar(18) NOT NULL,
  `ProjectId` varchar(18) NOT NULL COMMENT '项目Id',
  `RecruitmentCycle` bit NOT NULL DEFAULT 0 COMMENT '招聘周期',
  `RecruitmentNumber` bit NOT NULL DEFAULT 0 COMMENT '招聘人数',
  `InterviewerOutCome` bit NOT NULL DEFAULT 0 COMMENT '面试官筛选结果',
  `ScreenFileStatus` int(2) NOT NULL DEFAULT 0 COMMENT '筛选归档类型',
  `ScreenFileRemarks` varchar(1000) NOT NULL DEFAULT '' COMMENT '筛选归档描述',
  `OfferStatus` bit NOT NULL DEFAULT 0 COMMENT 'offer状态',
  `OfferOrInduction` bit NOT NULL DEFAULT 0 COMMENT 'offer或入职',
  `QuitJob` bit NOT NULL DEFAULT 0 COMMENT '离职操作',
  `QuitFileStatus` int(2) NOT NULL DEFAULT 0 COMMENT '离职归档类型',
  `QuitFileRemarks` varchar(1000) NOT NULL DEFAULT '' COMMENT '离职归档描述',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_project_automatic_ProjectId` (`ProjectId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;