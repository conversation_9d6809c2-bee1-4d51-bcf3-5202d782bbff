using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;

public class CanalRocketResp
{
    public string? Id { get; set; }
    public string? Database { get; set; }
    public long Es { get; set; }
    public Boolean IsDdl { get; set; }
    public string? Table { get; set; }
    public long Ts { get; set; }
    public string? Type { get; set; }

    // 使用JArray来处理不固定的data和old字段
    public JArray? Data { get; set; }
    public JArray? Old { get; set; }

    // 强类型的PKNames字段
    public List<string>? PkNames { get; set; }

    // SQL, SqlType等其他强类型字段
    public string? Sql { get; set; }
    public Dictionary<string, int>? SqlType { get; set; }

}