﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "sqlCon": "Server=**************;port=3306;Database=staffing;User=root;Password=**************;max pool size=100;connect timeout = 30",
    "noahCon": "Server=rm-2ze6r4dh3n01af30azo.mysql.rds.aliyuncs.com;port=3306;Database=ehrx_customer;User=nkp_view;Password=****************;SslMode=None;max pool size=100;connect timeout = 30"
  },
  "Security": {
    "AesKey": "SzlNTVWC+bh6RG2OeBw)qgor5#HF0JjKmupU"
  },
  "LogsOptions": {
    "IsLocalDebug": true,
    "Endpoint": "cn-beijing.log.aliyuncs.com",
    "Project": "noahplatform",
    "LogStore": "noahlogs-test",
    "AccessKeyId": "LTAI4Fq6HCM4X2ZDWVZCUagu",
    "SecretAccessKey": "A1hDO4ehDokLnOK2EubAJVJX-9hcJvCNn24cksMqWG8="
  },
  "Settings": {
    "RedisAddress": "**************,password=k8s_20220315:4vA0KjQFDzB1VhUn,defaultDatabase=8",
    "PublicRedisAddress": "r-2ze75zijbicae2qi2mpd.redis.rds.aliyuncs.com,password=NZPredis!@#$%,defaultDatabase=7",
    "ElasticSearch": {
      "Address": "http://**************:9200",
      "UserName": "elastic",
      "Password": "elastic@9128"
    },
    "Canal": {
      "Server": "**************",
      "Port": 11111,
      "ClientId": "111"
    },
    "Aliyun": {
      "AliSMS": {
        "AccessKeyId": "LTAI4Fq6HCM4X2ZDWVZCUagu",
        "AccessKeySecret": "******************************",
        "Domain": "dysmsapi.aliyuncs.com",
        "Version": "2017-05-25",
        "Action": "SendSms",
        "SignName": "诺快聘",
        "TemplateCode": "SMS_190788647",
        "OfferTemplateCode": "SMS_242575608",
        "FacingInterviewTemplateCode": "SMS_242600650",
        "UnFacingInterviewTemplateCode": "SMS_242660593",
        "ExpireSeconds": 300,
        "SendInterval": 59,
        "MaxEveryDay": 20
      },
      "FullAccess": {
        "AccessKeyId": "LTAI5tAj9XP4rRbJGKpoLQiC",
        "AccessKeySecret": "******************************"
      },
      "Oss": {
        "Dir": "teststaffing"
      }
    },
    "RequestLog": "true",
    "TencentOptions": {
      "FaceIdSecretId": "AKIDrYy82g4B2cvxjFUTBcplQpypPjmY4JHy",
      "FaceIdSecretKey": "RdXBYeT0GEwNYR5SXLsBE0VkiPp0uXx7",
      "ImAppId": "1400690536",
      "ImAppKey": "840732c056f38994c7734ae82507ba480c38eab74233f90fd901c37e72a5f32c"
    },
    "Ndn": {
      "ApiDomain": "http://task.royeinfo.com:9999",
      "OAuthDomain": "http://*************:3000",
      "ClientId": "fenrun",
      "ClientSecret": "fenrun",
      "GrantType": "client_credentials",
      "NuoPinBookCode": "38"
    },
    "WeChat": {
      "SeekerApplet": {
        "AppId": "wx8b4490f70e538c0e",
        "Secret": "eef62b094083343fd25b8d37f472a1ad"
      },
      "InterviewApplet": {
        "AppId": "wxe38d12b563661718",
        "Secret": "64e77b38b3ba84adac8fde1e22a45036"
      },
      "HrApplet": {
        "AppId": "wx96b65aafffa82a6b",
        "Secret": "36695648cb721126328ca0e57f8e8b44"
      }
    },
    "InternalService": {
      "Domain": "https://test-nkpapi.nuopin.cn",
      "LocalServiceKeys": {
        "Settlement": "C1B7FDE9F7A742D2A87E641C7D8B99NC"
      }
    },
    "Warning": {
      "SendInterval": 300,
      "EMails": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
      ],
      "SendMail": "<EMAIL>",
      "SendMailKey": "nuoPin@%2021",
      "SendMailSmtp": "smtp.qiye.aliyun.com"
    },
    "ParseResumeDomain": "http://************",
    "ParseResumeModeType": "fast",
    "DataRdsServer": "https://test-openapi.nuopin.cn",
    "ZhuangDian": {
      "Enterprise_No": "10383",
      "Account": "nynyk",
      "Http_Sign_Key": "0D25142A1FE08BDF97F3BD230E14A8D5",
      "Api_IP": "api.sms.drondea.com",
      "SMSSignature": [
        "【诺快聘】",
        "【诺聘】",
        "【诺优考】"
      ]
    },
    "RocketMq": {
      "RocketmqBrokerAddress": "rocketmq-broker-svc:8081",
      "RocketmqTopic": "canal_topic",
      "RocketConsumerGroup": "canal_consumer"
    }
  }
}