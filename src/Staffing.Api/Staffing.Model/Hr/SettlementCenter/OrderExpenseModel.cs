using Config.CommonModel;
using Config.Enums;
using MiniExcelLibs.Attributes;

namespace Staffing.Model.Hr.SettlementCenter;

/// <summary>
/// 订单消费请求模型
/// </summary>
public class OrderExpenseRequest : QueryRequest
{
    /// <summary>
    /// 商户ID
    /// </summary>
    public string? MerchantId { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 订单消费响应模型
/// </summary>
public class OrderExpenseListResponse : QueryResponse
{
    public List<OrderExpenseResponse> Rows { get; set; } = new List<OrderExpenseResponse>();
}

/// <summary>
/// 订单消费响应模型
/// </summary>
public class OrderExpenseResponse
{
    /// <summary>
    /// 订单单号
    /// </summary>
    [ExcelColumnName("订单单号")]
    public string OrderNumber { get; set; } = string.Empty;

    /// <summary>
    /// 平台项目
    /// </summary>
    [ExcelColumnName("平台项目")]
    public string PlatformProject { get; set; } = string.Empty;

    [ExcelIgnore]
    public int AutoId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    [ExcelColumnName("项目编码")]
    public string ProjectCode { get; set; } = string.Empty;

    /// <summary>
    /// 签约主体
    /// </summary>
    [ExcelColumnName("签约主体")]
    public string SigningEntity { get; set; } = string.Empty;

    /// <summary>
    /// 项目类型
    /// </summary>
    [ExcelIgnore]
    public ProjectType? ProjectType { get; set; }

    /// <summary>
    /// 项目类型
    /// </summary>
    [ExcelColumnName("项目类型")]
    public string? ProjectTypeText { get; set; }

    /// <summary>
    /// 收支类型
    /// </summary>
    [ExcelColumnName("收支类型")]
    public string ExpenseType { get; set; } = string.Empty;

    /// <summary>
    /// 交易类型
    /// </summary>
    [ExcelColumnName("交易类型")]
    public string TransactionType { get; set; } = string.Empty;

    /// <summary>
    /// 收款方
    /// </summary>
    [ExcelColumnName("收款方")]
    public string Payee { get; set; } = string.Empty;

    /// <summary>
    /// 金额
    /// </summary>
    [ExcelColumnName("金额")]
    public decimal Amount { get; set; }

    /// <summary>
    /// 交易成功时间
    /// </summary>
    [ExcelColumnName("交易成功时间")]
    public DateTime TransactionSuccessTime { get; set; }
}
