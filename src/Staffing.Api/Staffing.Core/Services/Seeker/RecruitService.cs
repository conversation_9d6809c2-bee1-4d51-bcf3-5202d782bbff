﻿using Config.Enums;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces.Seeker;
using Config;
using Microsoft.EntityFrameworkCore;
using Infrastructure.CommonService;
using Config.CommonModel.Business;
using Infrastructure.Proxy;
using Staffing.Model.Seeker.Recruit;
using Microsoft.AspNetCore.Http;

namespace Staffing.Core.Services.Seeker;

[Service(ServiceLifetime.Transient)]
public class RecruitService : IRecruitService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private ISharedService _sharedService;
    private ICommonService _commonService;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private CommonUserService _commonUserService;
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly CommonDicService _commonDicService;
    private WeChatHelper _weChatHelper;
    private readonly NoahData _noahData;
    private readonly IHttpContextAccessor _httpContextAccessor;
    public RecruitService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, ICommonService commonService,
        LogManager log, WeChatHelper weChatHelper, ISharedService sharedService,
        IDbContextFactory<StaffingContext> contextFactory, CommonUserService commonUserService,
        CommonDicService commonDicService, NoahData noahData, IHttpContextAccessor httpContextAccessor)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _commonService = commonService;
        _log = log;
        _weChatHelper = weChatHelper;
        _sharedService = sharedService;
        _contextFactory = contextFactory;
        _commonUserService = commonUserService;
        _commonDicService = commonDicService;
        _noahData = noahData;
        _httpContextAccessor = httpContextAccessor;
    }

    public GetJDInfoResponse GetJDInfo(GetJDInfo model)
    {
        var result = new GetJDInfoResponse();

        var jdPostId = _hostingEnvironment.IsProduction() ? Constants.JdPostIdProd : Constants.JdPostIdTest;

        var post = _context.Post_Team.Where(x => x.TeamPostId == model.TeamPostId)
        .Select(s => new { s.PostId, HrMobile = s.Project_Team.User_Hr.User.Mobile, s.Project_Team.HrId }).FirstOrDefault();

        result.IsJdPost = jdPostId == post?.PostId;
        result.HrMobile = post?.HrMobile;
        result.HrId = post?.HrId;

        return result;
    }

    public RegisterJDReq GetRegisterJD()
    {
        var result = new RegisterJDReq();

        if (string.IsNullOrWhiteSpace(_user.Id))
            return result;

        result = _context.User_Seeker_Jd.Where(x => x.SeekerId == _user.Id)
        .Select(s => new RegisterJDReq
        {
            IDCard = s.IDCard,
            Name = s.Name,
            TrainingTime = s.TrainingTime,
            IsHealthCard = s.IsHealthCard,
            IsToolbox = s.IsToolbox
        }).FirstOrDefault();

        result ??= new RegisterJDReq();

        // 通过用户简历信息补充
        var userResume = _context.User_Resume.Where(x => x.UserId == _user.Id)
        .Select(s => new
        {
            s.User_Seeker.NickName,
            s.User.IdentityCard,
            s.User.IdentityCardName
        }).FirstOrDefault();

        if (string.IsNullOrWhiteSpace(result.Name))
            result.Name = !string.IsNullOrWhiteSpace(userResume?.IdentityCardName) ? userResume.IdentityCardName : userResume?.NickName;

        if (string.IsNullOrWhiteSpace(result.IDCard))
            result.IDCard = userResume?.IdentityCard;

        return result;
    }

    public async Task<RegisterJDReqResponse> RegisterJDAsync(RegisterJDReq model)
    {
        var result = new RegisterJDReqResponse();

        if (string.IsNullOrWhiteSpace(model.TeamPostId))
            throw new BadRequestException("缺少TeamPostId");

        if (string.IsNullOrWhiteSpace(model.Name))
            throw new BadRequestException("姓名不能为空");

        if (!model.TrainingTime.HasValue)
            throw new BadRequestException("培训时间不能为空");

        // if (string.IsNullOrWhiteSpace(model.IDCard))
        //     throw new BadRequestException("证件号不能为空");

        IdCardInfo? idCardInfo = null;
        if (!string.IsNullOrWhiteSpace(model.IDCard))
        {
            if (!Tools.Certification(model.IDCard))
            {
                result.Result = 4;
                return result;
            }

            idCardInfo = Tools.GetIdCardInfo(model.IDCard);

            if (idCardInfo.Sex == Sex.男)
            {
                result.Result = 2;
                return result;
            }

            // 需要判断20-48岁，当年年减去出生年大于等于20岁，当年年减去出生年小于等于48岁
            if (idCardInfo.Birthday.HasValue)
            {
                var age = DateTime.Now.Year - idCardInfo.Birthday.Value.Year;
                if (age < 20 || age > 48)
                {
                    result.Result = 3;
                    return result;
                }
            }
        }

        var postId = _context.Post_Team.Where(x => x.TeamPostId == model.TeamPostId)
        .Select(s => s.PostId).FirstOrDefault();

        // 改为30天内不允许投递重复职位
        if (_context.Recruit.Any(x => x.Post_Delivery.PostId == postId
            && x.SeekerId == _user.Id && x.CreatedTime > DateTime.Now.AddDays(-30)))
            throw new BadRequestException("您已投递该职位，请勿重复操作");

        var userInfo = _context.User.Where(x => x.UserId == _user.Id)
        .Select(s => new
        {
            s.IdentityCard,
            s.IdentityCardName,
            s.Mobile
        }).First();

        var userJd = _context.User_Seeker_Jd.Where(x => x.SeekerId == _user.Id).FirstOrDefault();
        if (userJd == null)
        {
            userJd = new User_Seeker_Jd
            {
                SeekerId = _user.Id,
            };
            _context.Add(userJd);
        }

        userJd.TeamPostId = model.TeamPostId;
        userJd.IDCard = model.IDCard ?? userInfo.IdentityCard;
        userJd.Name = model.Name;
        userJd.TrainingTime = model.TrainingTime.Value;
        userJd.Phone = userInfo.Mobile;
        userJd.IsHealthCard = model.IsHealthCard ?? false;
        userJd.IsToolbox = model.IsToolbox ?? false;

        if (!string.IsNullOrWhiteSpace(model.IDCard))
        {
            // 补充身份证信息
            var userIdCard = _context.User
            .Where(x => x.UserId == _user.Id)
            .Select(s => new
            {
                s.IdentityCard,
                s.IdentityCardName
            }).FirstOrDefault();

            // 如果没有实名过，进行实名认证
            if (string.IsNullOrWhiteSpace(userIdCard?.IdentityCard))
            {
                await _commonService.IdVerification(new Model.Service.IdVerificationModel
                {
                    IdCard = model.IDCard,
                    Name = model.Name
                });
            }
            else
            {
                // 对比两次实名是否一致
                if (userIdCard.IdentityCard != model.IDCard)
                    throw new BadRequestException($"该用户已经使用其他身份证认证：{Tools.IdCardToX(userIdCard.IdentityCard)}");

                // 使用以前实名过的数据，防止姓名输入错误
                userJd.Name = userIdCard.IdentityCardName;
                userJd.IDCard = userIdCard.IdentityCard;
            }
        }

        // 记录用户信息
        var resume = _context.User_Resume.Where(x => x.UserId == _user.Id).FirstOrDefault();
        resume.Birthday = idCardInfo?.Birthday;

        _context.SaveChanges();

        var dv = DeliverResume(new DeliverResume { TeamPostId = model.TeamPostId });

        if (dv.Result != 1)
        {
            _log.Error("京东家政报名失败", "简历不符合要求", _user.Id);
            throw new BadRequestException("简历不符合要求");
        }

        result.DeliveryId = dv.DeliveryId;

        return result;
    }

    public DeliverResumeResponse DeliverResume(DeliverResume model)
    {
        if (string.IsNullOrWhiteSpace(model.TeamPostId))
            throw new BadRequestException("缺少TeamPostId");

        var lockerKey = $"st:deliver:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);
        if (locker == null)
            throw new BadRequestException("操作频繁");

        var result = new DeliverResumeResponse { Result = 1 };

        var seeker = _context.User_Resume.Where(x => x.UserId == _user.Id)
        .Select(s => new
        {
            s.User_Seeker.NickName,
            s.Sex,
            s.Birthday,
            s.Occupation
        }).FirstOrDefault();

        // 京东家政特殊处理
        var postId = _context.Post_Team.Where(x => x.TeamPostId == model.TeamPostId).Select(s => s.PostId).FirstOrDefault();
        var jdPostId = _hostingEnvironment.IsProduction() ? Constants.JdPostIdProd : Constants.JdPostIdTest;
        var isJdPost = jdPostId == postId;
        if (!isJdPost)
        {
            if (string.IsNullOrWhiteSpace(seeker!.NickName) || !seeker.Sex.HasValue
            || !seeker.Birthday.HasValue || !seeker.Occupation.HasValue)
            {
                result.Result = 2;
                return result;
            }
        }

        var deliverResp = _commonUserService.DeliverResume(new CommonDeliverResume
        {
            ChannelId = _user.ChannelId,
            TeamPostId = model.TeamPostId,
            UserId = _user.Id,
            UserClient = _user.ClientType,
            ChannelSource = _user.ChannelSource,
        });

        result.DeliveryId = deliverResp.DeliveryId;

        //腾讯广告
        if (_httpContextAccessor.HttpContext.Request.Headers.TryGetValue("gdt_vid", out var clickId))
        {
            if (!string.IsNullOrWhiteSpace(clickId))
                MyRedis.Client.LPush(SubscriptionKey.TencentAdReport, new Sub_TencentAdReport
                {
                    ClickId = clickId,
                    SetId = **********,
                    AccountId = ********,
                    At = "a03beede8a939571736ac2c9a2563608"
                });
        }

        return result;
    }

    public GetDeliverInfo GetDeliver(string id)
    {
        var result = _context.Recruit.Where(x => x.DeliveryId == id)
        .Select(s => new GetDeliverInfo
        {
            Hr = new HrModel
            {
                HrId = s.HrId,
                HrName = s.User_Hr.NickName,
                Avatar = s.User_Hr.Avatar,
                Post = s.User_Hr.Post,
                TencentImId = s.User_Hr.TencentImId,
            },
            TeamHr = new HrModel
            {
                HrId = s.Post_Delivery.Post_Team.Project_Team.HrId,
                HrName = s.Post_Delivery.Post_Team.Project_Team.User_Hr.NickName,
                Avatar = s.Post_Delivery.Post_Team.Project_Team.User_Hr.Avatar,
                Post = s.Post_Delivery.Post_Team.Project_Team.User_Hr.Post,
                TencentImId = s.Post_Delivery.Post_Team.Project_Team.User_Hr.TencentImId,
            },
            Post = new GetPostDetail
            {
                Name = s.Post_Delivery.Post.Name,
                Status = s.Post_Delivery.Post.Status,
                MaxSalary = s.Post_Delivery.Post.MaxSalary,
                MinSalary = s.Post_Delivery.Post.MinSalary,
                PostId = s.Post_Delivery.PostId,
                RegionId = s.Post_Delivery.Post.RegionId,
                Salary = s.Post_Delivery.Post.Salary,
                WorkNature = s.Post_Delivery.Post.WorkNature,
                SettlementType = s.Post_Delivery.Post.SettlementType,
                SalaryType = s.Post_Delivery.Post.SalaryType,
                TieType = s.Post_Delivery.Post.TieType,
                SalaryTypeName = s.Post_Delivery.Post.SalaryType.GetDescription(),
                WorkNatureName = s.Post_Delivery.Post.WorkNature.GetDescription()
            },
            DeliveryId = s.DeliveryId,
            RecruitStatus = s.Status,
            PostId = s.Post_Delivery.PostId,
            TeamPostId = s.Post_Delivery.TeamPostId,
            HrMobile = s.Post_Delivery.Post_Team.Project_Team.User_Hr.User.Mobile,
            CreatedTime = s.CreatedTime
        }).FirstOrDefault();

        if (result == null)
            throw new NotFoundException("内容不存在");

        result.Post!.SalaryName = Tools.FormattingSalary(result.Post.MinSalary, result.Post.MaxSalary, result.Post.SalaryType, result.Post.WorkNature, result.Post.Salary);
        result.DeliverStatus = DeliverStatusTools.RecruitStatusToDeliverStatus(result.RecruitStatus);
        result.DeliverStatusName = result.DeliverStatus.GetDescription();
        result.CanCancel = result.DeliverStatus != DeliverStatus.取消 && result.DeliverStatus != DeliverStatus.签约;

        if (!string.IsNullOrWhiteSpace(result.Post.RegionId))
            result.Post.Region = _commonDicService.GetCityById(result.Post.RegionId);

        var recordStatus = new List<RecruitStatus> { RecruitStatus.Offer, RecruitStatus.FileAway, RecruitStatus.HrScreening };
        result.Record = _context.Recruit_Record
        .Where(x => x.Recruit.Post_Delivery.SeekerId == _user.Id
        && x.Recruit.Post_Delivery.PostId == result.PostId && recordStatus.Contains(x.Status))
        .OrderByDescending(o => o.CreatedTime)
        .Take(20)
        .Select(s => new GetDeliverRecord
        {
            CreatedTime = s.CreatedTime,
            FileAway = s.FileAway,
            Id = s.Id,
            RecruitId = s.RecruitId,
            Status = s.Status
        }).ToList();

        foreach (var item in result.Record)
        {
            if (item.Status == RecruitStatus.HrScreening)
                item.Content = "你已经报名成功";
            else if (item.Status == RecruitStatus.Offer)
                item.Content = "恭喜你已经被录取";
            else if (item.Status == RecruitStatus.FileAway)
            {
                if (item.FileAway == RecruitFileAway.CandidateAbandonment)
                    item.Content = "你已主动放弃此职位";
                else if (item.FileAway == RecruitFileAway.Quit)
                    item.Content = "您已经离职";
                else
                    item.Content = "企业\"取消录取\"";
            }
        }

        if (result.TeamHr!.HrId == Constants.PlatformHrId)
        {
            result.HrMobile = Constants.PlatformMobile;
        }

        if (result.TeamHr!.HrId != Constants.PlatformHrId)
        {
            result.Hr = Tools.ModelConvert<HrModel>(result.TeamHr);
            result.TeamHr = null;
        }

        return result;
    }

    public EmptyResponse Complain(ComplainRequest model)
    {
        if (string.IsNullOrWhiteSpace(model.Describe))
            throw new BadRequestException("请填写投诉内容");

        var pd = _context.Post_Delivery.Where(x => x.DeliveryId == model.DeliveryId && x.SeekerId == _user.Id)
        .Select(s => new
        {
            s.TeamPostId
        }).FirstOrDefault();

        if (pd == null)
            throw new NotFoundException("内容不存在");

        var complaint = new Post_Complaint
        {
            DeliveryId = model.DeliveryId!,
            Describe = model.Describe,
            SeekerId = _user.Id,
            TeamPostId = pd.TeamPostId
        };
        _context.Add(complaint);

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public GetDeliversResponse GetDelivers(GetDelivers model)
    {
        var result = new GetDeliversResponse();

        var predicate = PredicateBuilder.New<Recruit>(x => x.Post_Delivery.SeekerId == _user.Id);

        if (model.DeliverStatus == DeliverStatus.取消)
            predicate = predicate.And(x => x.Status == RecruitStatus.FileAway);
        else if (model.DeliverStatus == DeliverStatus.录取)
            predicate = predicate.And(x => x.Status == RecruitStatus.Induction || x.Status == RecruitStatus.Offer);
        else if (model.DeliverStatus == DeliverStatus.报名)
            predicate = predicate.And(x => x.Status == RecruitStatus.HrScreening || x.Status == RecruitStatus.InterviewerScreening);
        else if (model.DeliverStatus == DeliverStatus.签约)
            predicate = predicate.And(x => x.Status == RecruitStatus.Contract);
        else if (model.DeliverStatus == DeliverStatus.面试)
            predicate = predicate.And(x => x.Status == RecruitStatus.Interview);

        var sql = _context.Recruit.Where(predicate);
        result.Total = sql.Count();

        result.Rows = sql.OrderByDescending(o => o.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetDeliverInfo
        {
            Hr = new HrModel
            {
                HrId = s.HrId,
                HrName = s.User_Hr.NickName,
                Avatar = s.User_Hr.Avatar,
                Post = s.User_Hr.Post,
                TencentImId = s.User_Hr.TencentImId
            },
            TeamHr = new HrModel
            {
                HrId = s.Post_Delivery.Post_Team.Project_Team.HrId,
                HrName = s.Post_Delivery.Post_Team.Project_Team.User_Hr.NickName,
                Avatar = s.Post_Delivery.Post_Team.Project_Team.User_Hr.Avatar,
                Post = s.Post_Delivery.Post_Team.Project_Team.User_Hr.Post,
                TencentImId = s.Post_Delivery.Post_Team.Project_Team.User_Hr.TencentImId
            },
            Post = new GetPostDetail
            {
                Name = s.Post_Delivery.Post.Name,
                Status = s.Post_Delivery.Post.Status,
                MaxSalary = s.Post_Delivery.Post.MaxSalary,
                MinSalary = s.Post_Delivery.Post.MinSalary,
                PostId = s.Post_Delivery.PostId,
                RegionId = s.Post_Delivery.Post.RegionId,
                Salary = s.Post_Delivery.Post.Salary,
                WorkNature = s.Post_Delivery.Post.WorkNature,
                SettlementType = s.Post_Delivery.Post.SettlementType,
                SalaryType = s.Post_Delivery.Post.SalaryType,
                SalaryTypeName = s.Post_Delivery.Post.SalaryType.GetDescription(),
                WorkNatureName = s.Post_Delivery.Post.WorkNature.GetDescription()
            },
            AgentEnt = new GetAgentEntDetail
            {
                AgentEntId = s.Post_Delivery.Post.AgentEntId,
                Scale = s.Post_Delivery.Post.Agent_Ent.Scale,
                ScaleName = s.Post_Delivery.Post.Agent_Ent.Scale.GetDescription(),
                LogoUrl = s.Post_Delivery.Post.Agent_Ent.LogoUrl,
                DisplayName = s.Post_Delivery.Post.Agent_Ent.DisplayName,
                Abbr = s.Post_Delivery.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post_Delivery.Post.Agent_Ent.Abbr : s.Post_Delivery.Post.Agent_Ent.DisplayName,
                Industry = s.Post_Delivery.Post.Agent_Ent.Industry.ToString(),
                IndustryName = s.Post_Delivery.Post.Agent_Ent.Dic_Industry.Name
            },
            DeliveryId = s.DeliveryId,
            RecruitStatus = s.Status,
            CreatedTime = s.CreatedTime,
            TeamPostId = s.Post_Delivery.TeamPostId,
            HrMobile = s.Post_Delivery.Post_Team.Project_Team.User_Hr.User.Mobile
        }).ToList();

        foreach (var item in result.Rows)
        {
            item.Post!.SalaryName = Tools.FormattingSalary(item.Post.MinSalary, item.Post.MaxSalary, item.Post.SalaryType, item.Post.WorkNature, item.Post.Salary);
            item.DeliverStatus = DeliverStatusTools.RecruitStatusToDeliverStatus(item.RecruitStatus);
            item.DeliverStatusName = item.DeliverStatus.GetDescription();
            if (!string.IsNullOrWhiteSpace(item.Post.RegionId))
                item.Post.Region = _commonDicService.GetCityById(item.Post.RegionId);

            if (item.TeamHr!.HrId != Constants.PlatformHrId)
            {
                item.Hr = Tools.ModelConvert<HrModel>(item.TeamHr);
                item.TeamHr = null;
            }
        }

        return result;
    }

    public EmptyResponse SeekerSetInterview(SeekerSetInterview model)
    {
        if (!model.UserFeedBack.HasValue || model.UserFeedBack == RecruitInterviewUserFeedBack.Waiting)
            throw new BadRequestException("操作状态参数无效");

        var interview = _context.Recruit_Interview.Where(x => x.SeekerId == _user.Id
        && x.Recruit.DeliveryId == model.DeliveryId).OrderByDescending(o => o.CreatedTime)
        .FirstOrDefault();

        if (interview == null)
            throw new NotFoundException("内容不存在");

        if (interview.UserFeedBack != RecruitInterviewUserFeedBack.Waiting)
            throw new BadRequestException("重复操作");

        interview.UserFeedBack = model.UserFeedBack.Value;

        if (model.UserFeedBack == RecruitInterviewUserFeedBack.Rejected)
        {
            var userModel = _context.User_Seeker.Where(o => o.UserId == _user.Id).First();

            interview.Outcome = RecruitInterviewOutcome.Cancelled;
            interview.TodoTime = DateTime.Now;
            interview.Remarks = "求职者拒绝本次面试";
            interview.CancelName = userModel.NickName;

            //尝试修改面试待办状态
            var todoModel = _context.Recruit_Interviewer_Todo.Where(o => o.RelationId == interview.InterviewId).FirstOrDefault();
            if (todoModel is not null)
            {
                todoModel.Status = RecruitInterviewerTodoStatus.Unknown;
            }
        }

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public SeekerGetInterviewInfo SeekerGetInterview(SeekerGetInterview model)
    {
        var result = _context.Recruit_Interview.Where(x => x.SeekerId == _user.Id
        && x.Recruit.DeliveryId == model.DeliveryId).OrderByDescending(o => o.CreatedTime)
        .Select(s => new SeekerGetInterviewInfo
        {
            Address = s.Address,
            CreatedTime = s.CreatedTime,
            HrId = s.HrId,
            HrName = s.Recruit.User_Hr.NickName,
            HrMobile = s.Recruit.User_Hr.User.Mobile,
            InterviewerId = s.InterviewerId,
            InterviewerName = s.Project_Interviewer.Name,
            InterviewerPhone = s.Project_Interviewer.Phone,
            InterviewTime = s.InterviewTime,
            Lat = s.Lat,
            Lng = s.Lng,
            Process = s.Process,
            RecruitId = s.RecruitId,
            RegionId = s.RegionId,
            Remarks = s.Remarks,
            UserFeedBack = s.UserFeedBack,
            InterviewId = s.InterviewId,
            Forms = s.Forms,
            FormsName = s.Forms.GetDescription(),
            UserFeedBackName = s.UserFeedBack.GetDescription(),
            Post = new GetPostDetail
            {
                Name = s.Recruit.Post_Delivery.Post.Name,
                MinSalary = s.Recruit.Post_Delivery.Post.MinSalary,
                MaxSalary = s.Recruit.Post_Delivery.Post.MaxSalary,
                PostId = s.Recruit.Post_Delivery.Post.PostId,
                TeamPostId = s.Recruit.Post_Delivery.TeamPostId,
                RegionId = s.Recruit.Post_Delivery.Post.RegionId,
                Salary = s.Recruit.Post_Delivery.Post.Salary,
                WorkNature = s.Recruit.Post_Delivery.Post.WorkNature,
                SettlementType = s.Recruit.Post_Delivery.Post.SettlementType,
                SalaryType = s.Recruit.Post_Delivery.Post.SalaryType,
                Education = s.Recruit.Post_Delivery.Post.Education,
                WorkNatureName = s.Recruit.Post_Delivery.Post.WorkNature.GetDescription(),
                EducationName = s.Recruit.Post_Delivery.Post.Education.GetDescription()
            },
            AgentEnt = new GetAgentEntDetail
            {
                AgentEntId = s.Recruit.Post_Delivery.Post.AgentEntId,
                DisplayName = s.Recruit.Post_Delivery.Post.Agent_Ent.DisplayName
            }
        }).FirstOrDefault();

        if (result == null)
            throw new NotFoundException("内容不存在");

        if (!string.IsNullOrWhiteSpace(result.RegionId))
            result.Region = _commonDicService.GetCityById(result.RegionId);

        if (!string.IsNullOrWhiteSpace(result.Post?.RegionId))
            result.Post.Region = _commonDicService.GetCityById(result.Post.RegionId);

        //如果没有面试官
        if (string.IsNullOrWhiteSpace(result.InterviewerId))
        {
            result.NoInterviewRequired = true;
            result.InterviewerName = result.HrName;
            result.InterviewerPhone = result.HrMobile;
        }

        result.Post!.SalaryName = Tools.FormattingSalary(result.Post.MinSalary, result.Post.MaxSalary, result.Post.SalaryType, result.Post.WorkNature, result.Post.Salary);
        if (result.InterviewTime.HasValue)
        {
            result.MinuteLeft = (int)(result.InterviewTime.Value - DateTime.Now).TotalMinutes + 1;
            result.MinuteLeft = result.MinuteLeft < 0 ? 0 : result.MinuteLeft;
            result.InterviewTimeText = result.InterviewTime.Value.ToString("yyyy年M月d日 HH:mm");

            if (result.MinuteLeft <= 0)
                result.OvertimeText = "已超时";
            else if (result.MinuteLeft <= 60)
                result.OvertimeText = $"{result.MinuteLeft}分钟后超时";
            else
                result.OvertimeText = $"{result.MinuteLeft / 60}小时后超时";
        }

        return result;
    }

    public EmptyResponse CancelDeliver(CancelDeliver model)
    {
        var recruit = _context.Recruit.Include(i => i.Post_Delivery)
        .Where(x => x.DeliveryId == model.DeliveryId && x.SeekerId == _user.Id)
        .FirstOrDefault();

        if (recruit == null)
            throw new NotFoundException("内容不存在");

        if (recruit.Status == RecruitStatus.Offer || recruit.Status == RecruitStatus.Induction)
            throw new BadRequestException("该投递无法取消");

        recruit.Status = RecruitStatus.FileAway;
        recruit.StatusTime = DateTime.Now;
        recruit.FileAway = RecruitFileAway.CandidateAbandonment;
        recruit.FileRemarks = model.Describe ?? string.Empty;
        recruit.UpdatedTime = DateTime.Now;

        var postInfo = _context.Recruit.Where(x => x.DeliveryId == model.DeliveryId && x.SeekerId == _user.Id)
            .Select(s => new
            {
                teamHrId = s.Post_Delivery.Post_Team.Project_Team.HrId
            }).First();
        //产品要求修改为协同hrid
        var recruitRecord = new Recruit_Record
        {
            RecruitId = recruit.RecruitId,
            Status = RecruitStatus.FileAway,
            Creator = postInfo.teamHrId,
            FileAway = RecruitFileAway.CandidateAbandonment,
            FileAwayRemarks = model.Describe ?? string.Empty
        };
        _context.Add(recruitRecord);



        //更新人才库用户等级
        var talent = _context.Talent_Platform.IgnoreQueryFilters()
        .Where(x => x.HrId == postInfo.teamHrId && x.SeekerId == _user.Id)
        .FirstOrDefault();

        if (talent != null)
        {
            if (talent.Level < TalentPlatformLevel.归档用户)
                talent.Level = TalentPlatformLevel.归档用户;
        }

        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 用户端获取offer通知
    /// </summary>
    /// <param name="offerId"></param>
    /// <returns></returns>
    public GetOfferNoticeResponse GetOfferNotice(string offerId)
    {
        var offerModel = _context.Recruit_Offer.Where(o => o.Id == offerId).Select(o => new GetOfferNoticeResponse
        {
            CarryInformationText = o.CarryInformation,
            NoticeContent = o.NoticeContent,
        }).FirstOrDefault();

        if (offerModel is null)
        {
            throw new BadRequestException("offer不存在！");
        }

        if (!string.IsNullOrEmpty(offerModel.CarryInformationText))
        {
            offerModel.CarryInformation = JsonSerializer.DeserializeFromString<List<string>>(offerModel.CarryInformationText);
        }

        return offerModel;
    }
}