using Staffing.Worker.Service;

namespace Staffing.Worker.Hosting;

public class UserWorker : HostingBase
{
    private readonly UserService _userService;
    private readonly UserTaskService _userTaskService;

    public UserWorker(IHostApplicationLifetime lifetime, UserService userSubService
    , UserTaskService userTaskService) : base(lifetime)
    {
        _userService = userSubService;
        _userTaskService = userTaskService;
    }

    public override async Task StartAsync(CancellationToken cancel)
    {
        _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SubHrRegister(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SubSeekerRegister(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SubNewSeekerMobile(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        // _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SubAdviserCountChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SubTalentCountChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SubVirtualTalentCountChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SubHrRank(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SubUpdateSeeker(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SubUpdateSkImAct(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SubUpdateHrImAct(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SubTeamPostId(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SubVisitAdviser(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userTaskService.ProjectMemberEntryImport(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userTaskService.HrSendSms(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userTaskService.UpdateHrData(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SubUserPostVisit(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SubCountUserHope(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SubTencentAdReport(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));

        //发送短信
        _bgTasks.Add(await Task.Factory.StartNew(() => _userTaskService.CommonSendSms(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));

        _bgTasks.Add(await Task.Factory.StartNew(() => _userTaskService.SendSms(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        // return Task.CompletedTask;

        // 求职者注册时，关联人才融合库
        _bgTasks.Add(await Task.Factory.StartNew(() => _userService.SeekerRegisterToTalentResume(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
    }
}