﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 数字诺亚顾问钱包流水
/// </summary>
[Table("ndn_hr_wallet_transfers")]
public class Ndn_Hr_Wallet_Transfers
{
    /// <summary>
    /// 主键
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 顾问ID
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 职位ID
    /// </summary>
    public string? PostId { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// Ndn项目编码
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public NdnWalletTransfersType Type { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}

public enum NdnWalletTransfersType
{
    诺快聘冻结, 诺快聘解冻, 交付消费
}