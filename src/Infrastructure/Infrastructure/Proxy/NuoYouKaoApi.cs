using Config;
using Config.CommonModel.Business;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace Infrastructure.Proxy;

[Service(ServiceLifetime.Transient)]
public class NuoYouKaoApi
{
    private readonly ConfigManager _config;
    private readonly CacheHelper _cacheHelper;
    private readonly LogManager _log;
    public NuoYouKaoApi(IOptionsSnapshot<ConfigManager> config, CacheHelper cacheHelper, LogManager log)
    {
        _config = config.Value;
        _cacheHelper = cacheHelper;
        _log = log;
    }

    /// <summary>
    /// 获取项目数据看板
    /// </summary>
    /// <returns></returns>
    public async Task<NuoyoukaoProjectPanel> GetProjectPanel()
    {
        string url = $"{Constants.NuoYouKaoUrl}/Flow/MicroService/v1/Project/GetProjectPanel";

        var token = await GetToken();

        try
        {
            var result = await url.WithOAuthBearerToken(token.access_token).GetJsonAsync<NuoyoukaoProjectPanel>();

            if (result == null)
                throw new Exception("诺优考获取项目数据看板失败");

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 获取隐式授权请求编号
    /// </summary>
    /// <returns></returns>
    public async Task<GetNykRequestNoResponse> GetNykRequestNo(GetNykRequestNo model)
    {
        string url = $"{Constants.NuoYouKaoUrl}/Expand/MicroService/v1/Common/GetRequestNo";

        var token = await GetToken();

        try
        {
            var result = await url.WithOAuthBearerToken(token.access_token).PostJsonAsync(model).ReceiveJson<GetNykRequestNoResponse>();

            if (result == null)
                throw new Exception("诺优考获取隐式授权请求编号出错");

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 诺优考获取token
    /// </summary>
    /// <returns></returns>
    public async Task<NuoYouKaoAccessToken> GetToken()
    {
        var cacheKey = $"nuoyoukaotoken";

        var result = MyRedis.Client.Get<NuoYouKaoAccessToken?>(cacheKey);

        if (!string.IsNullOrWhiteSpace(result?.access_token))
            return result;

        string url = $"{Constants.NuoYouKaoOAuthUrl}/connect/token";

        try
        {
            result = await url.PostUrlEncodedAsync(new
            {
                grant_type = "client_credentials",
                client_id = "7021113482001090012",
                client_secret = "BDEA3AC3D2B94E0DB6616E95A9A334F3"
            }).ReceiveJson<NuoYouKaoAccessToken>();

            if (string.IsNullOrWhiteSpace(result?.access_token))
                throw new Exception("获取诺优考token失败");
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }

        MyRedis.Client.Set(cacheKey, result, result.expires_in / 2);

        return result;
    }
}

public class NuoYouKaoAccessToken
{
    public string access_token { get; set; } = string.Empty;
    public int expires_in { get; set; }
    public string token_type { get; set; } = string.Empty;
    public string scope { get; set; } = string.Empty;
}