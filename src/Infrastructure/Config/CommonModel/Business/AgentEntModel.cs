
using Config.Enums;

namespace Config.CommonModel.Business;

public class UpdateAgentEnt
{
    /// <summary>
    /// 天眼查id
    /// </summary>
    public string? TycEntId { get; set; }

    /// <summary>
    /// 代招企业Id
    /// </summary>
    public string? AgentEntId { get; set; }

    /// <summary>
    /// 名字
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 企业简称
    /// </summary>
    public string Abbr { get; set; } = string.Empty;

    /// <summary>
    /// 展示项
    /// </summary>
    public EntDisplayType Display { get; set; } = EntDisplayType.企业名称;

    /// <summary>
    /// 展示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 企业Logo
    /// </summary>
    public string? LogoUrl { get; set; }

    /// <summary>
    /// 企业授权证明
    /// </summary>
    public string? AuthorizationUrl { get; set; } = string.Empty;

    /// <summary>
    /// 公司行业Id
    /// </summary>
    public string? Industry { get; set; }

    /// <summary>
    /// 企业性质
    /// </summary>
    public EnterpriseNature Nature { get; set; } = EnterpriseNature.个体工商户;

    /// <summary>
    /// 企业规模
    /// </summary>
    public EnterpriseScale Scale { get; set; } = EnterpriseScale.二十人以下;

    /// <summary>
    /// 融资阶段
    /// </summary>
    public EnterpriseCapital Capital { get; set; } = EnterpriseCapital.不需要融资;

    /// <summary>
    /// 状态
    /// </summary>
    public EnterpriseStatus Status { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    public double Lat { get; set; }
    public double Lng { get; set; }
}

public class GetAgentEntDetail : UpdateAgentEnt
{
    /// <summary>
    /// 公司行业名称
    /// </summary>
    public string? IndustryName { get; set; }

    public int AutoId { get; set; }

    /// <summary>
    /// 企业规模名称
    /// </summary>
    public string? ScaleName { get; set; }

    /// <summary>
    /// 企业性质名称
    /// </summary>
    public string? NatureName { get; set; }

    /// <summary>
    /// 融资阶段名称
    /// </summary>
    public string? CapitalName { get; set; }

    /// <summary>
    /// 地区名称
    /// </summary>
    public CityModel? Region { get; set; }

    /// <summary>
    /// 企业成立时间（年）
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 企业成立时间
    /// </summary>
    public DateTime? RegisterDate { get; set; }

    /// <summary>
    /// 企业标签
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 发布中的职位数量
    /// </summary>
    public int Posts { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; }
}