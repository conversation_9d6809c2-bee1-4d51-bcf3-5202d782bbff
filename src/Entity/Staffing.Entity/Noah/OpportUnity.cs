﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Noah;

/// <summary>
/// 销售机会
/// </summary>
[Table("opportunity")]
public class OpportUnity
{
    /// <summary>
    /// id
    /// </summary>
    [Key]
    public long OpportunityId { get; set; }
    /// <summary>
    /// 销售机会编号
    /// </summary>
    public string? OpportunityCode { get; set; }
    /// <summary>
    /// 销售机会名称
    /// </summary>
    public string? OpportunityName { get; set; }
    /// <summary>
    /// 客户名称
    /// </summary>
    public long? CustomerId { get; set; }
    /// <summary>
    /// 预计金额
    /// </summary>
    public decimal? EstimatedAmount { get; set; }
    /// <summary>
    /// 机会渠道\\r\\n7125a271-e124-734f-e3dd-3b69a0f89845：独立开发，\\r\\n6d65957c-cf32-0501-5bcc-69b87c014f8a：营销活动，\\r\\n01a82455-08a6-af8b-f310-59c6fd8e02e0：客户介绍，\\r\\n5325b255-2460-581d-743f-0ecb8f251de0：媒体宣传，\\r\\n9e7a4cd7-faab-9ede-0291-9437d8910392：电话来访
    /// </summary>
    public string? Channel { get; set; }
    /// <summary>
    /// 机会类型\\r\\nab106fa2-90a2-fec4-1ef8-4b1ec2669352：自主开发，\\r\\ncfa8ba6b-f4a7-d278-06b7-678465ccdda7：内部交易
    /// </summary>
    public string? Type { get; set; }
    /// <summary>
    /// 机会级别 3497c7d8-1151-d963-7821-530fc3e9d79b:一般，2b38d4e0-56cc-951b-de41-8817660bc5ee:重大
    /// </summary>
    public string? Level { get; set; }
    /// <summary>
    /// 意向承接部门
    /// </summary>
    public string? DeptCode { get; set; }
    /// <summary>
    /// 承接人
    /// </summary>
    public string? EmployeeCode { get; set; }
    /// <summary>
    /// 销售阶段\r\n1有效商机\\r\\n2接近客户，\\r\\n3方案呈现，\\r\\n4商务谈判，\\r\\n6赢单，\\r\\n7输单，\\r\\n8取消
    /// </summary>
    public string? SalesStage { get; set; }
    /// <summary>
    /// 预计成单日期
    /// </summary>
    public DateTime? OrderTime { get; set; }
    /// <summary>
    /// 成交概率
    /// </summary>
    public string? Probability { get; set; }
    /// <summary>
    /// 这个字段无法确定
    /// </summary>
    public string? ContactId { get; set; }
    /// <summary>
    /// 客户需求
    /// </summary>
    public string? Demand { get; set; }
    /// <summary>
    /// 负责人
    /// </summary>
    public string? Principal { get; set; }
    /// <summary>
    /// 协同人
    /// </summary>
    public string? CollaborativePeople { get; set; }
    /// <summary>
    /// 历史数据导入说明
    /// </summary>
    public string? HistoricalData { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreateBy { get; set; }
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 更新人
    /// </summary>
    public string? UpdateBy { get; set; }
    /// <summary>
    /// 删除标志
    /// </summary>
    public string? DelFlag { get; set; }
    /// <summary>
    /// 归档状态:1.归档 2.未归档;404:未知状态
    /// </summary>
    public string? ArchivingStatus { get; set; }
    /// <summary>
    /// 收取的其它费用
    /// </summary>
    public decimal? OtherCount { get; set; }
    /// <summary>
    /// 最后跟进时间
    /// </summary>
    public DateTime? FollowTime { get; set; }
    /// <summary>
    /// 意向承接人
    /// </summary>
    public string? IntentionEmployeeCode { get; set; }
    /// <summary>
    /// 销售机会dataId
    /// </summary>
    public string? DataId { get; set; }
    /// <summary>
    /// e1b04d5f-a743-032f-17b7-379925ad2a6c： 德信相关业务\r\n761473bf-633e-4626-f71d-8796e1ae252d：其他
    /// </summary>
    public string? DexinStatus { get; set; }

    public Customer Customer { get; set; } = default!;
    public VEmployee CreateByModel { get; set; } = default!;
}
