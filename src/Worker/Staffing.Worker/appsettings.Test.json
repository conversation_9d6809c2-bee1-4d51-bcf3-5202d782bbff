﻿{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft.Hosting.Lifetime": "Information"
        }
    },
    "ConnectionStrings": {
        "sqlCon": "Server=mysql-svc;port=3306;Database=staffing;User=root;Password=**************;max pool size=100;connect timeout = 30",
        "logSqlCon": "Server=mysql-svc;port=3306;Database=staffing_log;User=root;Password=**************;max pool size=100;connect timeout = 30"
    },
    "Settings": {
        "RedisAddress": "redis-svc:6379,password=k8s_20220315:4vA0KjQFDzB1VhUn,defaultDatabase=8",
        "ElasticSearch": {
            "Address": "http://elasticsearch-svc:9200",
            "UserName": "elastic",
            "Password": "elastic@9128"
        },
        "Canal": {
            "Server": "staffing-canal-server-svc",
            "Port": 11111,
            "ClientId": "112"
        }
    }
}