-- ----------------------------
-- 省市县街道
-- ----------------------------
DROP TABLE IF EXISTS `dic_region`;
CREATE TABLE `dic_region` (
  `Id` varchar(15) NOT NULL,
  `Name` varchar(50) NOT NULL COMMENT '名称',
  `ParentId` varchar(15) NULL COMMENT '父Id',
  `County` varchar(12) NOT NULL COMMENT '区',
  `City` varchar(12) NOT NULL COMMENT '市',
  `Province` varchar(12) NOT NULL COMMENT '省',
  `Status` int(2) NOT NULL DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `idx_dic_region_name` (`name`),
  KEY `idx_dic_region_ParentId` (`ParentId` DESC),
  KEY `idx_dic_region_county` (`county`),
  KEY `idx_dic_region_city` (`city`),
  KEY `idx_dic_region_province` (`province`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 全国高校
-- ----------------------------
DROP TABLE IF EXISTS `dic_school`;
CREATE TABLE `dic_school` (
  `Id` varchar(15) NOT NULL,
  `Name` varchar(50) NOT NULL COMMENT '名称',
  `Department` varchar(50) NOT NULL COMMENT '主管部门',
  `City` varchar(50) NOT NULL COMMENT '所在市',
  `Type` int(2) NOT NULL COMMENT '类别,0=专科，1=本科，2=成人',
  `Describe` varchar(50) NOT NULL COMMENT '描述',
  `Logo` varchar(1024) DEFAULT NULL COMMENT 'logo',
  `Status` int(2) NOT NULL DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_dic_school_Name` (`Name`),
  KEY `idx_dic_school_City` (`City`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 全国高校专业
-- ----------------------------
DROP TABLE IF EXISTS `dic_major`;
CREATE TABLE `dic_major` (
  `Id` varchar(15) NOT NULL,
  `Name` varchar(50) NOT NULL COMMENT '名称',
  `Level2Name` varchar(50) NOT NULL COMMENT 'Level2Name',
  `Level3Name` varchar(50) NOT NULL COMMENT 'Level3Name',
  `Type` int(2) NOT NULL COMMENT '类别,0=专科，1=本科',
  `Year` varchar(10) NOT NULL COMMENT '几年',
  `Status` int(2) NOT NULL DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `idx_dic_major_Name` (`Name`),
  KEY `idx_dic_major_Type` (`Type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 职位类别
-- ----------------------------
DROP TABLE IF EXISTS `dic_post`;
CREATE TABLE `dic_post` (
  `Id` int(11) NOT NULL,
  `Name`  varchar(50) NOT NULL COMMENT '名称',
  `ParentId` int(11) NULL COMMENT '父Id',
  `Level` varchar(100) DEFAULT NULL COMMENT '层级',
  `Tags` varchar(5000) DEFAULT NULL COMMENT '标签json',
  `Sort` int(11) NOT NULL COMMENT '排序',
  `Status` int(2) NOT NULL DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`Id`),
  KEY `IX_dic_post_Status` (`Status` DESC),
  KEY `IX_dic_post_ParentId` (`ParentId` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 行业字典
-- ----------------------------
DROP TABLE IF EXISTS `dic_industry`;
CREATE TABLE `dic_industry` (
  `Id` int(11) NOT NULL,
  `Name`  varchar(50) NOT NULL COMMENT '名称',
  `ParentId` int(11) NULL COMMENT '父Id',
  `Level` varchar(100) DEFAULT NULL COMMENT '层级',
  `Sort` int(11) NOT NULL COMMENT '排序',
  `Status` int(2) NOT NULL DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`Id`),
  KEY `IX_dic_industry_Status` (`Status` DESC),
  KEY `IX_dic_industry_ParentId` (`ParentId` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 福利
-- ----------------------------
DROP TABLE IF EXISTS `dic_welfare`;
CREATE TABLE `dic_welfare` (
  `Id` int(11) NOT NULL,
  `Name`  varchar(50) NOT NULL COMMENT '名称',
  `Logo`  varchar(1024) NOT NULL COMMENT 'Logo',
  `Describe` varchar(500) NULL COMMENT '描述',
  `Sort` int(11) NOT NULL COMMENT '排序',
  `Status` int(2) NOT NULL DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`Id`),
  KEY `IX_dic_welfare_Status` (`Status` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 证书字典
-- ----------------------------
DROP TABLE IF EXISTS `dic_cert`;
CREATE TABLE `dic_cert` (
  `Id` int(11) NOT NULL,
  `Name`  varchar(50) NOT NULL COMMENT '名称',
  `ParentId` int(11) NULL COMMENT '父Id',
  `Level` varchar(100) DEFAULT NULL COMMENT '层级',
  `Sort` int(11) NOT NULL COMMENT '排序',
  `Status` int(2) NOT NULL DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`Id`),
  KEY `IX_dic_cert_Status` (`Status` DESC),
  KEY `IX_dic_cert_ParentId` (`ParentId` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;