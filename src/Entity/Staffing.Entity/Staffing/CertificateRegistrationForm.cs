using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 证书报名表
/// </summary>
[Table("certificate_registrationform")]
public class CertificateRegistrationForm
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();
    
    /// <summary>
    /// 证书规格id
    /// </summary>
    public string CertificateSpecsId { get; set; } = default!;

    /// <summary>
    /// 证书id
    /// </summary>
    public string CertificateId { get; set; } = default!;

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 身份证号
    /// </summary>
    public string IdentityCardNumber { get; set; } = default!;

    /// <summary>
    /// 联系方式（手机号码）
    /// </summary>
    public string ContactNumber { get; set; } = default!;

    /// <summary>
    /// 工作单位
    /// </summary>
    public string? WorkUnit { get; set; } 

    /// <summary>
    /// 所在城市
    /// </summary>
    [Column("City", TypeName = "varchar(255)")]
    public string? City { get; set; }

    /// <summary>
    /// 城市代码
    /// </summary>
    [Column("CityCode", TypeName = "varchar(255)")]
    public string? CityCode { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Gender Gender { get; set; }

    /// <summary>
    /// 培训批次
    /// </summary>
    [Column("TrainingBatch", TypeName = "varchar(255)")]
    public string? TrainingBatch { get; set; }

    /// <summary>
    /// 报名时间，默认当前时间
    /// </summary>
    [Column("RegistrationTime", TypeName = "timestamp")]
    public DateTime? RegistrationTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 报考项目
    /// </summary>
    [Column("RegistrationProject", TypeName = "varchar(255)")]
    public string? RegistrationProject { get; set; }

    /// <summary>
    /// 推荐人
    /// </summary>
    public string? Recommender { get; set; }

    /// <summary>
    /// 推荐人id
    /// </summary>
    public string RecommenderId { get; set; } = default!;

    /// <summary>
    /// 收费
    /// </summary>
    public decimal? Fee { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string? Creator { get; set; }

    /// <summary>
    /// 创建时间，默认当前时间
    /// </summary>
    public DateTime? CreateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间，默认当前时间，更新时自动更新为当前时间
    /// </summary>
    public DateTime? UpdateTime { get; set; } = DateTime.Now;
    
    
    /// <summary>
    /// 价格（元）
    /// </summary>
    public decimal? Price { get; set; }
    
    /// <summary>
    /// 推荐佣金（元）
    /// </summary>
    public decimal? RecommendCommission { get; set; }
    
    /// <summary>
    /// 实际培训批次
    /// </summary>
    public string? ActualTrainingBatch { get; set; }
    
    /// <summary>
    /// 企业微信群名称
    /// </summary>
    public string? WeChatGroupName { get; set; }
    
        
    /// <summary>
    /// 证书报名入群状态
    /// </summary>
    public CertificateGroupTypeEnum? CertificateGroupType { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public RegistrationformStatusEnum? Status { get; set; } = RegistrationformStatusEnum.待审核;

    /// <summary>
    /// 结算状态
    /// </summary>
    public CertificateSettlementEnum? SettlementStatus { get; set; } = CertificateSettlementEnum.未结佣金;
    
    /// <summary>
    /// 等会再打
    /// </summary>
    public TodoEnum? ToDo { get; set; } = TodoEnum.等会再打;

    /// <summary>
    /// 支付状态
    /// </summary>
    public RegistrationformPayStatusEnum? PayStatus { get; set; } = RegistrationformPayStatusEnum.待支付;
    
    /// <summary>
    /// 关联的证书规格
    /// </summary>
    public virtual CertificateSpec? CertificateSpec { get; set; }
    
    /// <summary>
    /// 关联的证书表
    /// </summary>
    public virtual CertificateTable CertificateTable { get; set; } = default!;
}
public enum TodoEnum
{
    等会再打,撤销等会再打
}

public enum Gender
{
    男,
    女
}
public enum CertificateGroupTypeEnum
{
    未入群,已入群
}


public enum CertificateSettlementEnum
{
    未结佣金,已结佣金,已退回佣金
}
public enum RegistrationformStatusEnum
{
  待审核,资料已上传,培训中,培训完成,已下证,无效报名,
}

public enum RegistrationformPayStatusEnum
{
    待支付,已支付
}