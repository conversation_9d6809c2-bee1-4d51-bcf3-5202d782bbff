-- ----------------------------
-- 数字诺亚帐套表
-- ----------------------------
DROP TABLE IF EXISTS `ndn_books`;
CREATE TABLE `ndn_books` (
  `Id` varchar(18) NOT NULL,
  `BookCode` VARCHAR(50) NOT NULL COMMENT '账套ID',
  `BookName` VARCHAR(500) NOT NULL COMMENT '账套名称',
  `ContractSeal` VARCHAR(5000) NULL COMMENT '印章',
  `InvoiceInfo` TEXT NULL COMMENT '财务信息',
  `OperatorId` VARCHAR(18) COMMENT '操作人',
  `OperatorName` VARCHAR(50) COMMENT '操作人姓名',
  `UpdatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  `CreatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_ndn_books_BookCode` (`BookCode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字诺亚帐套';


-- ----------------------------
-- 数字诺亚项目表
-- ----------------------------
DROP TABLE IF EXISTS `ndn_project`;
CREATE TABLE `ndn_project` (
  `Id` varchar(18) NOT NULL,
  `ProjectCode` VARCHAR(50) NULL COMMENT '项目编码',
  `ProjectName` VARCHAR(500) NOT NULL COMMENT '项目名称',
  `BookCode` VARCHAR(50) NOT NULL COMMENT '主体账套ID',
  `BookName` VARCHAR(500) NOT NULL COMMENT '主体账套名称',
  `ClientBookCode` VARCHAR(50) NULL COMMENT '客户账套ID',
  `ClientBookName` VARCHAR(500) NULL COMMENT '客户账套名称',
  `ContractNo` VARCHAR(50) NULL COMMENT '合同编号',
  `ContractUrl` VARCHAR(5000) NULL COMMENT '合同地址',
  `Type` INT NOT NULL COMMENT '项目类型',
  `Status` INT NOT NULL COMMENT '状态',
  `OperatorId` VARCHAR(18) COMMENT '操作人',
  `OperatorName` VARCHAR(50) COMMENT '操作人姓名',
  `UpdatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  `CreatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_ndn_project_ProjectCode` (`ProjectCode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字诺亚项目表';


-- ----------------------------
-- 数字诺亚待办表
-- ----------------------------
DROP TABLE IF EXISTS `ndn_todo`;
CREATE TABLE `ndn_todo` (
  `Id` varchar(18) NOT NULL,
  `Title` VARCHAR(500) NOT NULL COMMENT '待办标题',
  `Description` VARCHAR(5000) COMMENT '待办描述',
  `Status` INT NOT NULL COMMENT '状态',
  `ProjectCode` VARCHAR(50) NULL COMMENT 'Ndn项目编码',
  `RelatedEntityId` VARCHAR(50) NOT NULL COMMENT '关联实体ID',
  `RelatedEntityType` INT NOT NULL COMMENT '关联实体类型',
  `UpdatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  `CreatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  PRIMARY KEY (`Id`),
  KEY `IX_ndn_todo_RelatedEntityId` (`RelatedEntityId`),
  KEY `IX_ndn_todo_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字诺亚待办表';


-- ----------------------------
-- 数字诺亚顾问钱包
-- ----------------------------
DROP TABLE IF EXISTS `ndn_hr_wallet`;
CREATE TABLE `ndn_hr_wallet` (
  `UserId` varchar(18) NOT NULL,
  `Amount` DECIMAL(10, 2) NOT NULL COMMENT '金额',
  PRIMARY KEY (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字诺亚顾问钱包';


-- ----------------------------
-- 数字诺亚顾问钱包详情
-- ----------------------------
DROP TABLE IF EXISTS `ndn_hr_wallet_details`;
CREATE TABLE `ndn_hr_wallet_details` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL COMMENT '顾问ID',
  `PostId` varchar(18) NULL COMMENT '职位ID',
  `ProjectCode` VARCHAR(50) NULL COMMENT 'Ndn项目编码',
  `Amount` DECIMAL(10, 2) NOT NULL COMMENT '金额',
  `UpdatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  `CreatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_ndn_hr_wallet_details_UserId_PostId_ProjectCode` (`UserId`, `PostId`, `ProjectCode`),
  KEY `IX_ndn_hr_wallet_details_PostId` (`PostId`),
  KEY `IX_ndn_hr_wallet_details_ProjectCode` (`ProjectCode`),
  KEY `IX_ndn_hr_wallet_details_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字诺亚顾问钱包详情';


-- ----------------------------
-- 数字诺亚顾问钱包流水
-- ----------------------------
DROP TABLE IF EXISTS `ndn_hr_wallet_transfers`;
CREATE TABLE `ndn_hr_wallet_transfers` (
  `Id` varchar(18) NOT NULL,
  `HrId` varchar(18) NOT NULL COMMENT '顾问ID',
  `PostId` varchar(18) NULL COMMENT '职位ID',
  `ProjectId` varchar(18) NULL COMMENT '项目ID',
  `ProjectCode` VARCHAR(50) NULL COMMENT 'Ndn项目编码',
  `Type` INT NOT NULL COMMENT '类型',
  `Amount` DECIMAL(10, 2) NOT NULL COMMENT '金额',
  `Status` INT NOT NULL COMMENT '状态',
  `Description` VARCHAR(5000) COMMENT '描述',
  `CreatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  PRIMARY KEY (`Id`),
  KEY `IX_ndn_hr_wallet_transfers_HrId` (`HrId`),
  KEY `IX_ndn_hr_wallet_transfers_PostId` (`PostId`),
  KEY `IX_ndn_hr_wallet_transfers_ProjectId` (`ProjectId`),
  KEY `IX_ndn_hr_wallet_transfers_ProjectCode` (`ProjectCode`),
  KEY `IX_ndn_hr_wallet_transfers_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字诺亚顾问钱包流水';


-- ----------------------------
-- 数字诺亚项目转账
-- ----------------------------
DROP TABLE IF EXISTS `ndn_project_transfers`;
CREATE TABLE `ndn_project_transfers` (
  `Id` varchar(18) NOT NULL,
  `FromProjectId` VARCHAR(50) NOT NULL COMMENT '转帐方的项目编码',
  `ToProjectId` VARCHAR(50) NOT NULL COMMENT '收款方的项目编码',
  `FromProjectName` VARCHAR(500) NULL COMMENT '转帐方的项目名称',
  `ToProjectName` VARCHAR(500) NULL COMMENT '收款方的项目名称',
  `Amount` DECIMAL(10, 2) NOT NULL COMMENT '金额',
  `Status` INT NOT NULL COMMENT '转账状态',
  `BonusStatus` INT NOT NULL COMMENT '奖金状态',
  `BonusTime` datetime(3) NULL,
  `Description` VARCHAR(5000) COMMENT '描述',
  `NdnId` VARCHAR(50) COMMENT '数字诺亚服务奖金Id',
  `OperatorId` VARCHAR(18) COMMENT '操作人',
  `OperatorName` VARCHAR(50) COMMENT '操作人姓名',
  `UpdatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  `CreatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  PRIMARY KEY (`Id`),
  KEY `IX_ndn_project_transfers_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字诺亚项目转账';


-- ----------------------------
-- 数字诺亚项目转账明细
-- ----------------------------
DROP TABLE IF EXISTS `ndn_project_transfer_details`;
CREATE TABLE `ndn_project_transfer_details` (
  `Id` varchar(18) NOT NULL,
  `TransferId` varchar(18) NOT NULL COMMENT '关联的项目转账ID',
  `Step` INT NOT NULL COMMENT '转账步骤',
  `FromProjectId` VARCHAR(50) NOT NULL COMMENT '转帐方的项目编码',
  `ToProjectId` VARCHAR(50) NOT NULL COMMENT '收款方的项目编码',
  `FromProjectName` VARCHAR(500) NULL COMMENT '转帐方的项目名称',
  `ToProjectName` VARCHAR(500) NULL COMMENT '收款方的项目名称',
  `ContractUrl` VARCHAR(5000) NULL COMMENT '合同地址',
  `InvoiceUrl` VARCHAR(5000) COMMENT '发票地址',
  `Status` INT NOT NULL COMMENT '状态',
  `Description` VARCHAR(5000) COMMENT '描述',
  `NdnId` VARCHAR(50) COMMENT '数字诺亚报销Id',
  `OperatorId` VARCHAR(18) COMMENT '操作人',
  `OperatorName` VARCHAR(50) COMMENT '操作人姓名',
  `UpdatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  `CreatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  PRIMARY KEY (`Id`),
  KEY `IX_ndn_project_transfer_details_TransferId` (`TransferId`),
  KEY `IX_ndn_project_transfer_details_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字诺亚项目转账明细';


-- ----------------------------
-- 数字诺亚项目发票
-- ----------------------------
DROP TABLE IF EXISTS `ndn_project_invoice`;
CREATE TABLE `ndn_project_invoice` (
  `Id` varchar(18) NOT NULL,
  `TransferStepId` VARCHAR(18) NOT NULL COMMENT '数字诺亚项目转账步骤Id',
  `Status` INT NOT NULL COMMENT '状态',
  `Description` VARCHAR(5000) COMMENT '描述',
  `InvoiceUrl` VARCHAR(5000) COMMENT '附件地址',
  `NdnId` VARCHAR(50) COMMENT '数字诺亚开票Id',
  `ReceivedAmount` DECIMAL(10, 2) NULL COMMENT '回款金额',
  `OperatorId` VARCHAR(18) COMMENT '操作人',
  `OperatorName` VARCHAR(50) COMMENT '操作人姓名',
  `UpdatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  `CreatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  PRIMARY KEY (`Id`),
  KEY `IX_ndn_project_invoice_TransferStepId` (`TransferStepId`),
  KEY `IX_ndn_project_invoice_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字诺亚项目发票';


-- ----------------------------
-- 数字诺亚服务奖金
-- ----------------------------
DROP TABLE IF EXISTS `ndn_service_bonus`;
CREATE TABLE `ndn_service_bonus` (
  `Id` varchar(18) NOT NULL,
  `SettlementId` VARCHAR(50) NOT NULL COMMENT '结算单Id',
  `TransferId` VARCHAR(18) NULL COMMENT '项目转账流水Id',
  `BookCode` VARCHAR(50) NULL COMMENT '发放帐套',
  `ProjectCode` VARCHAR(50) NULL COMMENT '发放项目',
  `ProjectName` VARCHAR(500) NULL COMMENT '发放项目名称',
  `HrId` VARCHAR(18) NOT NULL COMMENT '顾问Id',
  `Status` INT NOT NULL COMMENT '状态',
  `Description` VARCHAR(5000) COMMENT '描述',
  `OperatorId` VARCHAR(18) COMMENT '操作人',
  `OperatorName` VARCHAR(50) COMMENT '操作人姓名',
  `UpdatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  `CreatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  PRIMARY KEY (`Id`),
  KEY `IX_ndn_service_bonus_HrId` (`HrId`),
  KEY `IX_ndn_service_bonus_TransferId` (`TransferId`),
  UNIQUE KEY `IX_ndn_service_bonus_SettlementId` (`SettlementId`),
  KEY `IX_ndn_service_bonus_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字诺亚服务奖金';


ALTER TABLE project_settlement ADD `IsServiceBonusInitiated` BIT NOT NULL DEFAULT 0 COMMENT '是否发起服务奖金';

-- ALTER TABLE ndn_project ADD `ContractNo` VARCHAR(50) NULL COMMENT '合同编号';
-- ALTER TABLE ndn_project ADD `ContractUrl` VARCHAR(5000) NULL COMMENT '合同地址';
-- ALTER TABLE ndn_todo ADD `ProjectCode` VARCHAR(50) NULL COMMENT 'Ndn项目编码';
-- ALTER TABLE ndn_project_invoice ADD `ReceivedAmount` DECIMAL(10, 2) NULL COMMENT '回款金额';
-- ALTER TABLE ndn_project_transfers ADD `BonusTime` DateTime(3) NULL COMMENT '奖金时间';
