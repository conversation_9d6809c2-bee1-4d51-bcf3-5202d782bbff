﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using Config.CommonModel.Business;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 数字诺亚帐套表
/// </summary>
[Table("ndn_books")]
public class Ndn_Books
{
    /// <summary>
    /// 主键
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 账套ID
    /// </summary>
    public string BookCode { get; set; } = default!;

    /// <summary>
    /// 账套名称
    /// </summary>
    public string BookName { get; set; } = default!;

    /// <summary>
    /// 印章
    /// </summary>
    public string? ContractSeal { get; set; }

    /// <summary>
    /// 财务信息
    /// </summary>
    public NdnInvoiceInfo? InvoiceInfo { get; set; } = new NdnInvoiceInfo();

    /// <summary>
    /// 操作人
    /// </summary>
    public string? OperatorId { get; set; }

    /// <summary>
    /// 操作人姓名
    /// </summary>
    public string? OperatorName { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}
