﻿using Config;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Staffing.Entity;
using Staffing.Core.Interfaces.Hr;
using Config.CommonModel;
using Config.Enums;
using Infrastructure.Exceptions;
using LinqKit;
using Staffing.Entity.Staffing;
using Microsoft.Extensions.Options;
using Config.CommonModel.JavaDataApi;
using Infrastructure.Proxy;
using Staffing.Entity.Elasticsearch;
using Nest;
using Infrastructure.CommonService;
using Staffing.Model.Hr.TalentPlatform;
using Config.CommonModel.Business;
using Newtonsoft.Json.Linq;
using Noah.ImExportTools.Native;
using Microsoft.EntityFrameworkCore;

namespace Staffing.Core.Services.Service.Hr;

/// <summary>
/// 平台人才库服务
/// </summary>
[Service(ServiceLifetime.Transient)]
public class TalentPlatformService : ITalentPlatformService
{
    private readonly StaffingContext _context;
    private readonly LogManager _log;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private readonly JavaDataApi _javaDataApi;
    private readonly CommonDicService _commonDicService;
    private readonly EsHelper _esHelper;
    private readonly Noah.Aliyun.Storage.IObjectStorage _objectStorageService;

    /// <summary>
    /// 注入
    /// </summary>
    public TalentPlatformService(StaffingContext context, LogManager log, RequestContext user,
    IOptionsSnapshot<ConfigManager> config, CacheHelper cacheHelper, JavaDataApi javaDataApi,
    CommonDicService commonDicService, EsHelper esHelper, Noah.Aliyun.Storage.IObjectStorage objectStorageService)
    {
        _context = context;
        _log = log;
        _user = user;
        _config = config.Value;
        _cacheHelper = cacheHelper;
        _javaDataApi = javaDataApi;
        _commonDicService = commonDicService;
        _esHelper = esHelper;
        _objectStorageService = objectStorageService;
    }

    /// <summary>
    /// 获取平台人才库列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public TalentPlatformListResponse GetTalentPlatformList(TalentPlatformListRequest model)
    {
        var client = _esHelper.GetClient();

        //排序
        // Func<SortDescriptor<EsSeeker>, IPromise<IList<ISort>>> sort = sd =>
        // {
        //     sd.Field(f =>
        //     {
        //         f.Order(Nest.SortOrder.Descending);
        //         f.Nested(a =>
        //         {
        //             a.Filter(b => b.Term(c => c.Field(f => f.Hrs[0].HrId).Value("149755147231179397")));
        //             a.Path(p => p.Hrs);
        //             return a;
        //         });
        //         f.Field(f => f.Hrs[0].SeekerVisitTime);
        //         return f;
        //     });
        //     return sd;
        // };

        //条件
        // var mustQuerys = new List<Func<QueryContainerDescriptor<EsSeeker>, QueryContainer>>();
        // mustQuerys.Add(q => q.Nested(n => n.Path(p => p.Hrs).Query(q => q.Term(t => t.Field(f => f.Hrs[0].HrId).Value("149755147231179397")))));

        var mustQuerys = new List<Func<QueryContainerDescriptor<EsTalentPlatform>, QueryContainer>>();

        //149761703500854021
        mustQuerys.Add(q => q.Term(t => t.Field(f => f.HrId).Value(_user.Id))
            && q.Term(t => t.Field(f => f.TalentDeleted).Value(false)));
        if (!string.IsNullOrEmpty(model.Search))
        {
            mustQuerys.Add(q => q.Wildcard(t => t.Field(f => f.SeekerSearch).Value($"*{model.Search}*")));
            // model.Search = "*" + model.Search + "*";
            // mustQuerys.Add(q => q.QueryString(qs => qs.Query(model.Search)
            // .Fields(fs => fs.Field(f => f.SeekerNickName)
            //                 .Field(f => f.SeekerMobile)
            //                 .Field(f => f.SeekerSchool)
            //                 .Field(f => f.SeekerCompany)))
            // || q.Nested(n => n.Path(p => p.Works).Query(qt => qt.QueryString(t => t.Query(model.Search).Fields(fs => fs.Field(f => f.Works.First().Company))))));
        }
        if (model.Level != null)
            mustQuerys.Add(q => q.Term(t => t.Field(f => f.TalentLevel).Value(model.Level)));
        if (model.SearchPost != null)
            mustQuerys.Add(q => q.Term(t => t.Field(f => f.DesiredPost).Value(model.SearchPost)));
        if (model.Sex != null)
            mustQuerys.Add(q => q.Term(t => t.Field(f => f.SeekerSex).Value(model.Sex)));
        if (model.Education != null)
            mustQuerys.Add(q => q.Term(t => t.Field(f => f.SeekerEducation).Value(model.Education)));
        if (model.Channel != null)
            mustQuerys.Add(q => q.Term(t => t.Field(f => f.TalentSource).Value(model.Channel)));

        var regionQuerys = new List<Func<QueryContainerDescriptor<EsTalentPlatform>, QueryContainer>>();
        if (model.RegionIdArray != null && model.RegionIdArray.Count > 0)
        {
            model.RegionIdArray = model.RegionIdArray.Take(10).ToList();
            foreach (var item in model.RegionIdArray)
                regionQuerys.Add(q => q.Prefix(t => t.Field(f => f.SeekerRegionId).Value(item)));
        }
        // mustQuerys.Add(q => q.Terms(t => t.Field(f => f.SeekerRegionId).Terms(model.RegionIdArray)));
        if (model.MinAge != null && model.MinAge > 0)
            mustQuerys.Add(q => q.DateRange(t => t.Field(f => f.SeekerBirthday).LessThanOrEquals(DateTime.Now.Date.AddYears(model.MinAge.Value * -1))));
        if (model.MaxAge != null && model.MaxAge > 0)
            mustQuerys.Add(q => q.DateRange(t => t.Field(f => f.SeekerBirthday).GreaterThanOrEquals(DateTime.Now.Date.AddYears(model.MaxAge.Value * -1))));
        if (!string.IsNullOrEmpty(model.StartTime) && DateTime.TryParse(model.StartTime, out var dtStartTime))
            mustQuerys.Add(q => q.DateRange(t => t.Field(f => f.TalentCreatedTime).GreaterThanOrEquals(model.StartTime)));
        if (!string.IsNullOrEmpty(model.EndTime) && DateTime.TryParse(model.EndTime, out var dtEndTime))
            mustQuerys.Add(q => q.DateRange(t => t.Field(f => f.TalentCreatedTime).LessThan(dtEndTime.Date.AddDays(1))));
        if (!string.IsNullOrWhiteSpace(model.ChannelId))
            mustQuerys.Add(q => q.Term(t => t.Field(f => f.ChannelId).Value(model.ChannelId)));

        var talent = client.Search<EsTalentPlatform>(s => s
        .Index(_esHelper.GetIndex().TalentPlatform)
        .TrackTotalHits()
        // .Source(sf => sf.Excludes(e => e.Fields(f => f.Hrs)))
        // .Source(sf => sf.Includes(e => e.Fields(f => f.UserId)))
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Query(q => q.Bool(b => b.Must(mustQuerys)) && q.Bool(b => b.Should(regionQuerys)))
        .Sort(s => s.Field(f =>
        {
            f.Order(SortOrder.Descending);
            f.Field(c => c.SeekerVisitTime);
            return f;
        })));

        var reslt = new TalentPlatformListResponse { Rows = new List<TalentPlatformModel>() };
        reslt.Total = (int)talent.Total;
        if (reslt.Total < 0)
            reslt.Total = 0;

        // 获取当前页channelId集合,并从mysql查询对应name
        var ids = talent.Documents.Select(s => s.ChannelId);
        var channelInfos = _context.Qd_Hr_Channel.Where(w => ids.Contains(w.Id)).Select(s => new { ChannelId = s.Id, ChannelUserName = s.User_Seeker.NickName }).ToList();

        foreach (var item in talent.Documents)
        {
            var wk = item.Works.OrderByDescending(o => o.BeginDate).FirstOrDefault();
            var city = !string.IsNullOrWhiteSpace(item.SeekerRegionId) ? _commonDicService.GetCityById(item.SeekerRegionId) : new CityModel();
            var obj = new TalentPlatformModel
            {
                Active = Tools.GetOnlineStatus(item.SeekerLoginTime),
                Age = Tools.GetAgeByBirthdate(item.SeekerBirthday.HasValue ? DateOnly.FromDateTime(item.SeekerBirthday.Value) : null),
                CreatTime = item.TalentCreatedTime,
                SeekerId = item.SeekerId,
                Education = item.SeekerEducation,
                EducationName = item.SeekerEducation?.GetDescription(),
                EduExp = $"{item.SeekerSchool}-{item.SeekerMajor} {item.SeekerEducation?.GetDescription()} {item.SeekerGraduationDate:yyyy-MM}",
                HeadPortrait = item.SeekerAvatar,
                HrAppletQrCode = item.HrAppletQrCode,
                Id = item.Id,
                IsRealName = !string.IsNullOrWhiteSpace(item.SeekerIdentityCard),
                LastTime = item.SeekerLoginTime,
                Level = item.TalentLevel,
                LevelName = item.TalentLevel.GetDescription(),
                Location = $"{city.ProvinceName}-{city.CityName}-{city.CountyName}",
                Mobile = item.SeekerMobile,
                Name = item.SeekerNickName,
                Occupation = item.SeekerOccupation,
                OccupationName = item.SeekerOccupation?.GetDescription(),
                Sex = item.SeekerSex,
                SexName = item.SeekerSex?.GetDescription(),
                Source = item.TalentSource,
                SourceName = item.TalentSource.GetDescription(),
                WorkExp = $"{wk?.Company}-{wk?.Post}/{wk?.BeginDate:yyyy-MM}-{wk?.EndDate:yyyy-MM}",
                TalentLabel = item.Tags?.Select(s => new TalentLabelClass
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = s
                }).ToList() ?? new List<TalentLabelClass>(),
                HopeIndustry = string.Join(',', item.DesiredPostName ?? new List<string>()),
                ChannelId = item.ChannelId,
                ChannelUserName = channelInfos.FirstOrDefault(f => f.ChannelId == item.ChannelId)?.ChannelUserName
            };
            reslt.Rows.Add(obj);
        }

        //获取seekerIds
        var seekerIds = reslt.Rows.Select(s => s.SeekerId).ToList();

        //根据seekerIds查询用户信息
        var seekers = _context.User_Seeker.Where(w => seekerIds.Contains(w.UserId))
        .Select(s => new { s.TencentImId, s.UserId }).ToList();

        foreach (var item in reslt.Rows)
        {
            item.ActiveName = item.Active.GetDescription();
            item.SeekerTencentImId = seekers.FirstOrDefault(x => x.UserId == item.SeekerId)?.TencentImId;
        }

        reslt.Count = reslt.Total;

        // var usernum = _context.User_Num.Where(x => x.UserId == _user.Id).Select(s => new
        // {
        //     s.Talent,
        //     s.VirtualTalent
        // }).First();

        // reslt.Talent = new List<int> { usernum.Talent, reslt.Total }.Max();
        // reslt.VirtualTalent = usernum.VirtualTalent;

        return reslt;

        // TalentPlatformListResponse retModel = new TalentPlatformListResponse()
        // {
        //     Rows = new List<TalentPlatformModel>(),
        //     Total = 0
        // };

        // PlatformExternalRequest reqModel = new PlatformExternalRequest()
        // {
        //     city = model.RegionIdArray,
        //     education = model.Education == null ? null : (int)model.Education,
        //     endDate = model.EndTime,
        //     startDate = model.StartTime,
        //     unionSearch = model.Search,
        //     channel = model.Channel == null ? null : (int)model.Channel,
        //     jobType = model.SearchPost,
        //     level = model.Level == null ? null : (int)model.Level,
        //     maxAge = model.MaxAge,
        //     minAge = model.MinAge,
        //     pageNum = model.PageIndex,
        //     pageSize = model.PageSize,
        //     hrId = _user.Id,
        //     sex = model.Sex == null ? null : (int)model.Sex,
        //     talentLabelId = model.TalentLabelId
        // };

        // var resultModel = await _javaDataApi.PlatformTalentSearch(reqModel);

        // if (resultModel == null || resultModel.total == 0 || resultModel.list == null)
        // {
        //     return retModel;
        // }

        #region 楷哥和月斌的装逼写法
        //retmodel.rows.addrange(resultmodel.list.select(s => new talentplatformmodel
        //{

        //}));

        //resultmodel.list.foreach(i => retmodel.rows.add(new talentplatformmodel
        //{

        //}));
        #endregion

        // foreach (var i in resultModel.list)
        // {
        //     TalentPlatformModel addModel = new TalentPlatformModel()
        //     {
        //         Name = i.name,
        //         Age = i.birthday == null ? null : DateTime.Now.Year - i.birthday.Value.Year,
        //         CreatTime = i.createdTime,
        //         Education = i.education,
        //         EducationName = i.educationName,
        //         EduExp = i.eduExp,
        //         WorkExp = i.workExp,
        //         HeadPortrait = i.headPortrait,
        //         HopeIndustry = i.expectedJob,
        //         Id = i.id,
        //         LastTime = i.loginTime,
        //         Location = i.city,
        //         Occupation = i.occupation,
        //         OccupationName = i.occupationName,
        //         Sex = i.sex,
        //         SexName = i.sexName,
        //         Source = i.source,
        //         SourceName = i.sourceName,
        //         Level = i.level,
        //         LevelName = i.levelName,
        //         Active = Tools.GetOnlineStatus(i.loginTime),
        //         IsRealName = i.ifCertification,
        //         TalentLabel = i.talentLabel,
        //         HrAppletQrCode = i.hrAppletQrCode,
        //         Mobile = i.mobile,
        //     };

        //     addModel.ActiveName = addModel.Active.GetDescription();

        //     retModel.Rows.Add(addModel);
        // }

        // retModel.Total = resultModel.total ?? 0;
        // retModel.Count = resultModel.count ?? 0;

        // return retModel;
    }

    /// <summary>
    /// 获取平台人才库简历详情
    /// </summary>
    /// <param name="platformId"></param>
    /// <returns></returns>
    public async Task<TalentPlatformResumeDetailsResponse> GetTalentPlatformDetails(string platformId)
    {
        //TalentPlatformResumeDetailsResponse retModel = new TalentPlatformResumeDetailsResponse();
        await Task.FromResult(1);

        var talentPlatformModel = _context.Talent_Platform.Where(o => o.Id == platformId && o.HrId == _user.Id && o.Status == ActiveStatus.Active).Select(o => new TalentPlatformResumeDetailsResponse
        {
            Name = o.User_Seeker.NickName,
            UserId = o.User_Seeker.UserId,
            _Birthday = o.User_Seeker.User_Resume.Birthday,
            AttachmentUrl = o.User_Seeker.User_Resume_Attach.Url,
            HeadPortrait = o.User_Seeker.Avatar,
            _IdentityCard = o.User_Seeker.User.IdentityCard,
            Mailbox = o.User_Seeker.User_Resume.EMail,
            Mobile = o.User_Seeker.User.Mobile,
            WeChat = o.User_Seeker.User_Resume.WeChatNo,
            QQ = o.User_Seeker.User_Resume.Qq,
            Occupation = o.User_Seeker.User_Resume.Occupation,
            Education = o.User_Seeker.User_Resume.Education,
            Perfection = o.User_Seeker.User_Resume.Score,
            PlatformId = o.Id,
            Sex = o.User_Seeker.User_Resume.Sex,
            SelfEvaluation = o.User_Seeker.User_Resume.Describe,
            Nature = o.User_Seeker.User_Resume.Nature,
            Skill = o.User_Seeker.User_Resume.Skill,
            Appearance = o.User_Seeker.User_Resume.Appearance,
            SkillsCert = o.User_Seeker.User_Resume.Certificate,
            HrAppletQrCode = o.User_Seeker.HrAppletQrCode,
            SeekerTencentImId = o.User_Seeker.TencentImId,
            Active = Tools.GetOnlineStatus(o.User_Seeker.User_Extend.LoginTime),
            SearchIntention = o.User_Seeker.User_Extend.DesiredPostName,
            EduSub = string.IsNullOrEmpty(o.User_Seeker.User_Resume.School) ? null : new TalentPlatformResumeEduSub
            {
                Education = o.User_Seeker.User_Resume.Education,
                SchoolName = o.User_Seeker.User_Resume.School,
                MajorName = o.User_Seeker.User_Resume.Major,
                EndTime = o.User_Seeker.User_Resume.GraduationDate,
            },
            WorkSub = o.User_Seeker.User_Resume.User_Work.Select(m => new TalentPlatformResumeWorkSub
            {
                CompanyName = m.Company,
                CompanyRemarks = m.Describe,
                PostName = m.Post,
                EndTime = m.EndDate,
                StartTime = m.BeginDate
            }).ToList(),
            PracticeSub = o.User_Seeker.User_Resume.User_Campus.Select(m => new TalentPlatformResumePracticeSub
            {
                PracticeName = m.Name,
                Experience = m.Describe,
                PostPrize = m.Award,
                EndTime = m.EndDate,
                StartTime = m.BeginDate
            }).ToList()
        }).FirstOrDefault();

        if (talentPlatformModel == null)
        {
            throw new NotFoundException("内容不存在！");
        }

        //整理数据
        talentPlatformModel.ActiveName = talentPlatformModel.Active.GetDescription();
        talentPlatformModel.Age = talentPlatformModel._Birthday == null ? null : DateTime.Now.Year - talentPlatformModel._Birthday.Value.Year;
        talentPlatformModel.IsRealName = !string.IsNullOrEmpty(talentPlatformModel._IdentityCard);
        talentPlatformModel.OccupationName = talentPlatformModel.Occupation == null ? null : talentPlatformModel.Occupation.GetDescription();
        talentPlatformModel.EducationName = talentPlatformModel.Education == null ? null : talentPlatformModel.Education.GetDescription();
        talentPlatformModel.SexName = talentPlatformModel.Sex == null ? null : talentPlatformModel.Sex.GetDescription();
        if (talentPlatformModel.EduSub != null)
        {
            talentPlatformModel.EduSub.EducationName = talentPlatformModel.EduSub.Education == null ? null : talentPlatformModel.EduSub.Education.GetDescription();
        }

        //拼接标签
        if (talentPlatformModel.Nature.Count > 0 || talentPlatformModel.Skill.Count > 0 || talentPlatformModel.Appearance.Count > 0)
        {
            talentPlatformModel.Label = talentPlatformModel.Nature.Union(talentPlatformModel.Skill).Union(talentPlatformModel.Appearance).ToList();
        }

        // //获取寻访意向
        // try
        // {
        //     string url = _config.DataRdsServer + "/datards/linggong/platform/talent/expected/jobs";
        //     var result = await url.SetQueryParams(new { id = platformId }).GetAsync().ReceiveJson<dynamic>();
        //     if (result is not null)
        //     {
        //         talentPlatformModel.SearchIntention = string.Join(",", result.data.expectedJobTypeList);
        //     }
        // }
        // catch
        // {
        // }

        return talentPlatformModel;
    }

    /// <summary>
    /// 删除平台人才库
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<EmptyResponse> DeleteTalentPlatform(DeleteTalentPlatformRequest model)
    {
        var platformList = _context.Talent_Platform.Where(o => model.PlatformId.Contains(o.Id));

        platformList.ForEach(o => o.Deleted = true);

        _context.SaveChanges();

        ////通知月斌删除平台人才库
        //string url = _config.DataRdsServer + "/datards/linggong/platform/talent/remove";
        //var result = await url.PostJsonAsync(new { ids = model.PlatformId }).ReceiveString();

        //删除ES上的人才库
        var client = _esHelper.GetClient();
        await client.BulkAsync(b => b.UpdateMany<string, object>(model.PlatformId, (b, u) => b.Id(u).Index(_esHelper.GetIndex().TalentPlatform).Doc(new { TalentDeleted = true })).Refresh(Elasticsearch.Net.Refresh.True));

        //删除人才库通知楷哥消息
        MyRedis.Client.SAdd(SubscriptionKey.TalentCountChange, new Sub_TalentCount_Change
        {
            HrId = _user.Id
        });

        return new EmptyResponse();
    }

    /// <summary>
    /// 获取平台人才库简历评论列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public TalentPlatformCommentListResponse GetTalentPlatformCommentList(TalentPlatformCommentListRequest model)
    {
        if (string.IsNullOrEmpty(model.PlatformId))
        {
            throw new BadRequestException("请求参数错误！");
        }

        TalentPlatformCommentListResponse retModel = new TalentPlatformCommentListResponse();

        var predicate = PredicateBuilder.New<Talent_Platform_Comment>(o => o.HrId == _user.Id && o.PlatformId == model.PlatformId);

        var sql = _context.Talent_Platform_Comment.Where(predicate);

        retModel.Total = sql.Count();

        retModel.Rows = sql
        .OrderByDescending(o => o.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new TalentPlatformCommentModel
        {
            Id = s.Id,
            Content = s.Content,
            CreatedTime = s.CreatedTime,
            HeadPortrait = s.User_Hr.Avatar ?? "",
            HrName = s.HrName
        }).ToList();

        return retModel;
    }

    /// <summary>
    /// 新增平台人才库简历评论
    /// </summary>
    /// <param name="mdoel"></param>
    /// <returns></returns>
    public EmptyResponse AddTalentPlatformComment(AddTalentPlatformCommentRequest model)
    {
        bool isExist = _context.Talent_Platform.Any(o => o.HrId == _user.Id && o.Id == model.PlatformId);

        if (!isExist)
        {
            throw new BadRequestException("内容不存在！");
        }

        Talent_Platform_Comment addModel = new Talent_Platform_Comment()
        {
            CreatedTime = DateTime.Now,
            Content = model.Content,
            HrId = _user.Id,
            HrName = _user.Name ?? "",
            PlatformId = model.PlatformId
        };

        _context.Talent_Platform_Comment.Add(addModel);
        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 删除平台人才库简历评论
    /// </summary>
    /// <param name="commentId"></param>
    /// <returns></returns>
    public EmptyResponse DeleteTalentPlatformComment(string commentId)
    {
        var commentModel = _context.Talent_Platform_Comment.Where(o => o.HrId == _user.Id && o.Id == commentId).FirstOrDefault();
        if (commentModel == null)
        {
            throw new BadRequestException("内容不存在！");
        }

        _context.Talent_Platform_Comment.Remove(commentModel);
        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 获取平台人才库标签
    /// </summary>
    /// <param name="platformId"></param>
    /// <returns></returns>
    public TalentPlatformLabelResponse GetTalentPlatformLabel(string platformId)
    {
        TalentPlatformLabelResponse retModel = new TalentPlatformLabelResponse();

        var predicate = PredicateBuilder.New<Talent_Label>(o => o.PlatformId == platformId);

        var sql = _context.Talent_Label.Where(predicate);

        retModel.Rows = sql
        .Select(s => new TalentPlatformLabelModel
        {
            Id = s.Dic_Talent_Label.Id,
            Name = s.Dic_Talent_Label.LabelName
        }).ToList();

        return retModel;
    }

    /// <summary>
    /// 变更平台人才库标签
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<EmptyResponse> EditTalentVirtualLabel(EditTalentPlatformLabelRequest model)
    {
        await Task.FromResult(1);
        if (string.IsNullOrEmpty(model.PlatformId))
        {
            throw new BadRequestException("请求参数错误！");
        }

        bool IsVirtualIdExist = _context.Talent_Platform.Any(o => o.Id == model.PlatformId);
        if (!IsVirtualIdExist)
        {
            throw new BadRequestException("人才库不存在！");
        }

        foreach (var i in model.DicLabelId)
        {
            bool IsDicLabelIdExist = _context.Dic_Talent_Label.Any(o => o.Id == i);
            if (!IsDicLabelIdExist)
            {
                throw new BadRequestException("标签不存在！");
            }
        }

        var labelRemoveList = _context.Talent_Label.Where(o => o.PlatformId == model.PlatformId).ToList();
        if (labelRemoveList.Count > 0)
        {
            _context.Talent_Label.RemoveRange(labelRemoveList);
        }

        List<TalentPlatformLabelModel> postList = new List<TalentPlatformLabelModel>();
        if (model.DicLabelId.Count > 0)
        {
            List<Talent_Label> addList = new List<Talent_Label>();
            foreach (var i in model.DicLabelId)
            {
                addList.Add(new Talent_Label()
                {
                    DicLabelId = i,
                    PlatformId = model.PlatformId,
                    VirtualId = ""
                });
            }

            _context.Talent_Label.AddRange(addList);

            postList = _context.Dic_Talent_Label.Where(o => addList.Select(m => m.DicLabelId).Contains(o.Id)).Select(n => new TalentPlatformLabelModel
            {
                Id = n.Id,
                Name = n.LabelName
            }).ToList();
        }
        _context.SaveChanges();

        // //通知月斌变更平台人才库标签
        // string url = _config.DataRdsServer + "/datards/linggong/platform/talent/tag/update";
        // var result = await url.PostJsonAsync(new { id = model.PlatformId, labels = postList }).ReceiveString();

        return new EmptyResponse();
    }

    public string DownLoadResume(string id)
    {
        string type = "TalentResume";
        if (!MyRedis.Client.SetNx($"resume:down:{type}", 1, 3))
            throw new BadRequestException("操作频繁，限3秒一次");

        var info = _commonDicService.GetExistFileFromOss(type, id);
        if (info != null)
            return info.OssPath;
        // 获取用户简历信息
        var resumeInfo = GetTalentPlatformDetails(id).GetAwaiter().GetResult();
        // 处理数据
        // 处理生日
        string birthday = string.Empty;
        if (resumeInfo._Birthday != null)
            birthday = Convert.ToDateTime(resumeInfo._Birthday.ToString()).ToString("yyyy-MM-dd");
        // 处理头像
        //if (!string.IsNullOrWhiteSpace(resumeInfo.HeadPortrait))
        //    resumeInfo.HeadPortrait = resumeInfo.HeadPortrait.Split('?')[0];
        // 处理教育经历
        var eduList = new List<TalentPlatformResumeEduSub>();
        if (resumeInfo.EduSub != null)
        {
            var endTime = resumeInfo.EduSub.EndTime;
            if (endTime != null)
                resumeInfo.EduSub.EndTimeYM = Convert.ToDateTime(endTime.ToString()).ToString("yyyy-MM");
            eduList.Add(resumeInfo.EduSub);
        }
        // 处理工作经历
        if (resumeInfo.WorkSub != null && resumeInfo.WorkSub.Count > 0)
        {
            resumeInfo.WorkSub.ForEach(f =>
            {
                f.StartTimeYM = f.StartTime != null ? Convert.ToDateTime(f.StartTime.ToString()).ToString("yyyy-MM") : null;
                f.EndTimeYM = f.EndTime != null ? Convert.ToDateTime(f.EndTime.ToString()).ToString("yyyy-MM") : "至今";
            });
        }
        // 处理项目经历
        if (resumeInfo.PracticeSub != null && resumeInfo.PracticeSub.Count > 0)
        {
            resumeInfo.PracticeSub.ForEach(f =>
            {
                f.StartTimeYM = f.StartTime != null ? Convert.ToDateTime(f.StartTime.ToString()).ToString("yyyy-MM") : null;
                f.EndTimeYM = f.EndTime != null ? Convert.ToDateTime(f.EndTime.ToString()).ToString("yyyy-MM") : "至今";
            });
        }

        Dictionary<string, object> dicResume = new Dictionary<string, object>();
        dicResume.Add("人才库ID", resumeInfo.PlatformId);
        dicResume.Add("简历生成时间", DateTime.Now);
        dicResume.Add("姓名", resumeInfo.Name);
        dicResume.Add("寻访意向", resumeInfo.SearchIntention);
        dicResume.Add("性别", resumeInfo.SexName);
        dicResume.Add("生日", birthday);
        dicResume.Add("年龄", resumeInfo.Age);
        dicResume.Add("头像", resumeInfo.HeadPortrait);
        dicResume.Add("邮箱", resumeInfo.Mailbox);
        dicResume.Add("自我评价", resumeInfo.SelfEvaluation);
        dicResume.Add("联系电话", resumeInfo.Mobile);
        dicResume.Add("学历", resumeInfo.EducationName);
        dicResume.Add("工作经历", JArray.FromObject(resumeInfo.WorkSub));
        dicResume.Add("实践经历", JArray.FromObject(resumeInfo.PracticeSub));
        dicResume.Add("教育经历", JArray.FromObject(eduList));
        dicResume.Add("标签", resumeInfo.Label == null ? null : string.Join(' ', resumeInfo.Label));
        dicResume.Add("QQ", resumeInfo.QQ);
        dicResume.Add("微信", resumeInfo.WeChat);
        dicResume.Add("证书", (resumeInfo.SkillsCert == null || resumeInfo.SkillsCert.Count == 0) ? null : string.Join(' ', resumeInfo.SkillsCert));
        dicResume.Add("职业", resumeInfo.OccupationName);

        // 获取模板路径及生成文件路径
        var rootPath = Path.Combine(Directory.GetCurrentDirectory(), "TemplateFiles", "resume/Talent");
        if (!Directory.Exists(rootPath))
            Directory.CreateDirectory(rootPath);
        string tempPath = Path.Combine(rootPath, "ResumeTempTalent.docx");
        string newName = DateTime.Now.ToString("yyyyMMddHHmmssfff") + (new Random()).Next(100) + ".docx";
        string outWordPath = rootPath + @"\" + newName;

        //Noah.ImExportTools.WordHelper.ExportWithArray(tempPath, outWordPath, dicResume).Wait();
        WordHelper.ExportWithArray(tempPath, outWordPath, dicResume).Wait();
        //将文件上传到OSS
        string ossPath = string.Empty;
        using (var stream = new FileStream(outWordPath, FileMode.Open))
        {
            ossPath = _objectStorageService.OrdinaryUploadFile(stream, Path.GetFileName(outWordPath), DateTime.Now.Ticks + Path.GetExtension(outWordPath)).Result;
        }
        if (File.Exists(outWordPath))
        {
            try
            {
                File.Delete(outWordPath);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }
        // oss地址存本地,用set不会重复
        MyRedis.Client.SAdd(SubscriptionKey.OssPathKey, new File_Path_Of_Oss { Type = type, PrimaryId = id, OssPath = ossPath, UserId = resumeInfo.UserId ?? string.Empty });
        return ossPath;
    }

    public List<ChannelInfo> GetChannelInfo()
    {
        var channelInfos = new List<ChannelInfo>();
        var list = _context.Qd_Hr_Channel.Include(i => i.User_Seeker).Where(w => w.HrId == _user.Id).ToList();
        if (list.Count == 0)
            return channelInfos;
        list.ForEach(f => channelInfos.Add(new ChannelInfo { ChannelId = f.Id, ChannelUserName = f.User_Seeker.NickName }));
        return channelInfos;
    }

    public async Task<TalentPlatformResumeDetailsResponse> GetResumeDetails(string userId)
    {
        await Task.FromResult(1);

        var talentPlatformModel = _context.User_Seeker.Where(o => o.UserId == userId).Select(o => new TalentPlatformResumeDetailsResponse
        {
            Name = o.NickName,
            UserId = o.UserId,
            _Birthday = o.User_Resume.Birthday,
            AttachmentUrl = o.User_Resume_Attach.Url,
            HeadPortrait = o.Avatar,
            _IdentityCard = o.User.IdentityCard,
            Mailbox = o.User_Resume.EMail,
            Mobile = o.User.Mobile,
            WeChat = o.User_Resume.WeChatNo,
            QQ = o.User_Resume.Qq,
            Occupation = o.User_Resume.Occupation,
            Education = o.User_Resume.Education,
            Perfection = o.User_Resume.Score,
            Sex = o.User_Resume.Sex,
            SelfEvaluation = o.User_Resume.Describe,
            Nature = o.User_Resume.Nature,
            Skill = o.User_Resume.Skill,
            Appearance = o.User_Resume.Appearance,
            SkillsCert = o.User_Resume.Certificate,
            HrAppletQrCode = o.HrAppletQrCode,
            Active = Tools.GetOnlineStatus(o.User_Extend.LoginTime),
            SearchIntention = o.User_Extend.DesiredPostName,
            EduSub = string.IsNullOrEmpty(o.User_Resume.School) ? null : new TalentPlatformResumeEduSub
            {
                Education = o.User_Resume.Education,
                SchoolName = o.User_Resume.School,
                MajorName = o.User_Resume.Major,
                EndTime = o.User_Resume.GraduationDate,
            },
            WorkSub = o.User_Resume.User_Work.Select(m => new TalentPlatformResumeWorkSub
            {
                CompanyName = m.Company,
                CompanyRemarks = m.Describe,
                PostName = m.Post,
                EndTime = m.EndDate,
                StartTime = m.BeginDate
            }).ToList(),
            PracticeSub = o.User_Resume.User_Campus.Select(m => new TalentPlatformResumePracticeSub
            {
                PracticeName = m.Name,
                Experience = m.Describe,
                PostPrize = m.Award,
                EndTime = m.EndDate,
                StartTime = m.BeginDate
            }).ToList()
        }).FirstOrDefault();

        if (talentPlatformModel == null)
            throw new NotFoundException("内容不存在！");

        //如果不在人才库，隐藏电话
        if (!_context.Talent_Platform.Any(x => x.HrId == _user.Id && x.SeekerId == userId))
            talentPlatformModel.Mobile = Tools.MobilToX(talentPlatformModel.Mobile);

        //整理数据
        talentPlatformModel.ActiveName = talentPlatformModel.Active.GetDescription();
        talentPlatformModel.Age = talentPlatformModel._Birthday == null ? null : DateTime.Now.Year - talentPlatformModel._Birthday.Value.Year;
        talentPlatformModel.IsRealName = !string.IsNullOrEmpty(talentPlatformModel._IdentityCard);
        talentPlatformModel.OccupationName = talentPlatformModel.Occupation == null ? null : talentPlatformModel.Occupation.GetDescription();
        talentPlatformModel.EducationName = talentPlatformModel.Education == null ? null : talentPlatformModel.Education.GetDescription();
        talentPlatformModel.SexName = talentPlatformModel.Sex == null ? null : talentPlatformModel.Sex.GetDescription();
        if (talentPlatformModel.EduSub != null)
        {
            talentPlatformModel.EduSub.EducationName = talentPlatformModel.EduSub.Education == null ? null : talentPlatformModel.EduSub.Education.GetDescription();
        }

        //拼接标签
        if (talentPlatformModel.Nature.Count > 0 || talentPlatformModel.Skill.Count > 0 || talentPlatformModel.Appearance.Count > 0)
        {
            talentPlatformModel.Label = talentPlatformModel.Nature.Union(talentPlatformModel.Skill).Union(talentPlatformModel.Appearance).ToList();
        }

        return talentPlatformModel;
    }
}