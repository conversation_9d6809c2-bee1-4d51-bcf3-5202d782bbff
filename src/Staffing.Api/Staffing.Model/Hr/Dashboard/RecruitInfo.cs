using System.Text.Json.Serialization;
using Config.CommonModel;
using Config.Enums;
using Staffing.Model.Hr.Recruit;

namespace Staffing.Model.Hr.Dashboard;

public class GetRecentRecruits : QueryRequest
{
    /// <summary>
    /// 是否主创
    /// </summary>
    public bool IsOriginal { get; set; }

    /// <summary>
    /// 姓名或手机号
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 项目编号或名称
    /// </summary>
    public string? ProjectNoOrName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public RecruitStatus? Status { get; set; }

    /// <summary>
    /// 推荐开始时间
    /// </summary>
    public DateTime? BeginTime { get; set; }

    /// <summary>
    /// 推荐结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 发单顾问
    /// </summary>
    public string? HrSearch { get; set; }

    /// <summary>
    /// 候选人
    /// </summary>
    public string? SeekerSearch { get; set; }

}

public class GetRecentRecruitsResponse : QueryResponse
{
    public List<RecruitInfo> Rows { get; set; } = default!;
}

public class RecruitInfo
{
    /// <summary>
    /// 岗位金额/人
    /// </summary>
    public decimal? PostMoney { get; set; }
    /// <summary>
    /// 线索佣金
    /// </summary>
    public decimal? ClueBounty { get; set; }

    /// <summary>
    /// 当前人是否是项目经理
    /// </summary>
    public bool IsManager { get; set; }

    /// <summary>
    /// 邀面佣金(邀面)
    /// </summary>
    public decimal? FollowerBounty { get; set; }
    
    /// <summary>
    /// 销售佣金
    /// </summary>
    public decimal SalesBounty { get; set; }
    
    /// <summary>
    /// 项目管理佣金
    /// </summary>
    public decimal ManagerBounty { get; set; }
    
    /// <summary>
    /// 交付佣金
    /// </summary>
    public decimal DeliveriesBounty { get; set; }
    /// <summary>
    /// 销售是否需要分佣
    /// </summary>
    public bool? IsSalesCommission { get; set; }

    
    /// <summary>
    /// 是否为线索 0 否 1是
    /// </summary>
    public ProjectTeamIsClues? IsClues { get; set; }
    /// <summary>
    /// 是否接单0 否 1是
    /// </summary>
    public RecruitAcceptOrderEnum? AcceptOrder { get; set; } 
    /// <summary>
    /// 接收人id
    /// </summary>
    public string? ReceiverId { get; set; }
    /// <summary>
    /// 邀面人id
    /// </summary>
    public string? FollowerId { get; set; }
    /// <summary>
    /// 合作模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }

    /// <summary>
    /// 结算周期
    /// </summary>
    public string? PaymentCycleName { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public BountyStatus? BountyStatus { get; set; }

    /// <summary>
    /// 是否减库存，默认0-否，1-是
    /// </summary>
    public int? IfStockOut { get; set; }

    /// <summary>
    /// 计费开始时间（计费倒计时）
    /// </summary>
    public DateTime? EndCountTime { get; set; }

    /// <summary>
    /// 入职时间
    /// </summary>
    public DateTime? InductionTime { get; set; }

    /// <summary>
    /// 入职过保时间
    /// </summary>
    public int? PaymentDays { get; set; }

    /// <summary>
    /// 是否显示"无效线索" true:显示，false:不显示
    /// </summary>
    public bool InvalidDisplay { get; set; }

    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string? Id { get; set; } = default!;

    /// <summary>
    /// 项目id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 用户头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 是否实名
    /// </summary>
    public bool? IsRealName { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 性别解释
    /// </summary>
    public string? SexName { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    [JsonIgnore]
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public OccupationType? Occupation { get; set; }

    /// <summary>
    /// 职业解释
    /// </summary>
    public string? OccupationName { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 地区id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 地区
    /// </summary>
    public CityModel? City { get; set; }

    /// <summary>
    /// 招聘流程列表工作经验数据库查询模型（不返回前端）
    /// </summary>
    [JsonIgnore]
    public WorkExpClass? WorkExpClass { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    public string? WorkExp { get; set; }

    /// <summary>
    /// 教育经历学校（不返回前端）
    /// </summary>
    [JsonIgnore]
    public string? EduExpSchool { get; set; }

    /// <summary>
    /// 教育经历专业（不返回前端）
    /// </summary>
    [JsonIgnore]
    public string? EduExpMajor { get; set; }

    /// <summary>
    /// 教育经历学历（不返回前端）
    /// </summary>
    [JsonIgnore]
    public EducationType? EduExpEducation { get; set; }

    /// <summary>
    /// 教育经历学历解释（不返回前端）
    /// </summary>
    [JsonIgnore]
    public string? EduExpEducationName { get; set; }

    /// <summary>
    /// 毕业时间
    /// </summary>
    [JsonIgnore]
    public DateOnly? GraduationDate { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public string? EduExp { get; set; }

    /// <summary>
    /// 应聘职位
    /// </summary>
    public string? HopeIndustry { get; set; }

    /// <summary>
    /// 顾问名称
    /// </summary>
    public string? Adviser { get; set; }
    
    /// <summary>
    /// 约面经理
    /// </summary>
    public string? Invitation { get; set; }

    /// <summary>
    /// 协同人
    /// </summary>
    public string? TeamHrId { get; set; }

    /// <summary>
    /// 用户来源
    /// </summary>
    public HrProjectSource? Source { get; set; }
    /// <summary>
    /// 投递公司
    /// </summary>
    public string? AgentEntName { get; set; }
    /// <summary>
    /// 用户来源解释
    /// </summary>
    public string? SourceName { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? StatusTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    /// 登录时间
    /// </summary>
    public DateTime? LoginTime { get; set; }

    /// <summary>
    /// 招聘流程状态
    /// </summary>
    public RecruitStatus? Status { get; set; }

    /// <summary>
    /// 招聘流程状态
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 活跃度
    /// </summary>
    public OnlineStatus Active { get; set; }

    /// <summary>
    /// 活跃度解释
    /// </summary>
    public string ActiveName { get; set; } = default!;

    /// <summary>
    /// 简历二维码
    /// </summary>
    public string? HrAppletQrCode { get; set; }

    /// <summary>
    /// 归档状态
    /// </summary>
    public RecruitFileAway? FileAway { get; set; }

    /// <summary>
    /// 归档状态解释
    /// </summary>
    public string? FileAwayName { get; set; }

    /// <summary>
    /// 所在地
    /// </summary>
    public string? Location { get; set; }

    #region 面试官筛选单独返回字段
    /// <summary>
    /// 面试官筛选发起时间
    /// </summary>
    public DateTime? InterviewScreenLaunchTime { get; set; }

    /// <summary>
    /// 面试管筛选状态
    /// </summary>
    public RecruitInterviewerScreenStatus? InterviewScreenStatus { get; set; }

    /// <summary>
    /// 面试管筛选状态解释
    /// </summary>
    public string? InterviewScreenStatusName { get; set; }
    #endregion

    #region 面试安排单独返回字段
    /// <summary>
    /// 面试发起时间
    /// </summary>
    public DateTime? InterviewLaunchTime { get; set; }

    /// <summary>
    /// 面试官名称
    /// </summary>
    public string? InterviewerName { get; set; }

    /// <summary>
    /// 面试官反馈
    /// </summary>
    public RecruitInterviewOutcome? InterviewerFeedback { get; set; }

    /// <summary>
    /// 面试官反馈解释
    /// </summary>
    public string? InterviewerFeedbackName { get; set; }

    /// <summary>
    /// 候选人反馈
    /// </summary>
    public RecruitInterviewUserFeedBack? CandidateFeedback { get; set; }

    /// <summary>
    /// 候选人反馈解释
    /// </summary>
    public string? CandidateFeedbackName { get; set; }

    /// <summary>
    /// 面试方式
    /// </summary>
    public RecruitInterviewProcess? InterviewProcess { get; set; }

    /// <summary>
    /// 面试方式解释
    /// </summary>
    public string? InterviewProcessName { get; set; }
    #endregion

    #region 发放offer单独返回字段
    /// <summary>
    /// offer发起时间
    /// </summary>
    public DateTime? OfferLaunchTime { get; set; }

    /// <summary>
    /// 是否已通知
    /// </summary>
    public bool? IsNotice { get; set; }
    #endregion

    #region 归档单独返回字段
    /// <summary>
    /// 归档时间
    /// </summary>
    public DateTime? FileTime { get; set; }
    #endregion

    /// <summary>
    /// 面试流程标签
    /// </summary>
    public List<RecruitLabelClass>? RecruitLabel { get; set; }
    /// <summary>
    /// 金额字段
    /// </summary>
    public AmountInfo? Amount { get; set; }

    /// <summary>
    /// 奖励时效类型（单次结算, 长期结算）
    /// </summary>
    public PostRewardType? RewardType { get; set; }
}

public class AmountInfo
{
    //线索佣金
    public decimal? leadCommission { get; set; }
    //结算佣金
    public decimal? settlementCommission { get; set; }
    //邀面佣金
    public decimal? inviteCommission { get; set; }
}

public class SummaryDataResponse
{
    /// <summary>
    /// 近30天访问量
    /// </summary>
    public int ThirtyDaysVisits { get; set; }

    /// <summary>
    /// 本月新增人才储备
    /// </summary>
    public int MonthTalentAdded { get; set; }

    /// <summary>
    /// 主投简历
    /// </summary>
    public int Zhutou { get; set; }

    /// <summary>
    /// 内推简历
    /// </summary>
    public int Neitui { get; set; }

    /// <summary>
    /// 初筛待处理
    /// </summary>
    public int Chushai { get; set; }

    /// <summary>
    /// 待面试官筛选
    /// </summary>
    public int Mianshiguan { get; set; }

    /// <summary>
    /// 面试待安排
    /// </summary>
    public int Mianshi { get; set; }

    /// <summary>
    /// 入职待确认
    /// </summary>
    public int Ruzhi { get; set; }

    /// <summary>
    /// 计费待确认
    /// </summary>
    public int Jifei { get; set; }

    /// <summary>
    /// 佣金未结算
    /// </summary>
    public int Yongjin { get; set; }

    /// <summary>
    /// 逾期强制扣款
    /// </summary>
    public int Yuqi { get; set; }

    /// <summary>
    /// 职位冻结金额
    /// </summary>
    public decimal PostFreezeAmount { get; set; }
}