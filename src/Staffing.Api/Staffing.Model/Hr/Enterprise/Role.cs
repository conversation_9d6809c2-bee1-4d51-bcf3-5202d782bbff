﻿using Config.CommonModel;
using Staffing.Entity.Staffing;

namespace Staffing.Model.Hr.Enterprise;


public class GetEntRoles : QueryRequest
{
    /// <summary>
    /// 角色名称
    /// </summary>
    public string? Name { get; set; }
}

public class GetEntRolesResponse : QueryResponse
{
    public List<GetEntRolesInfo> Rows { get; set; } = default!;
}

public class GetEntRolesInfo
{
    public string? RoleId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 管辖范围
    /// </summary>
    public string? Scope { get; set; }

    /// <summary>
    /// 图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// 0=系统，1=自定义
    /// </summary>
    public RoleType Type { get; set; }

    /// <summary>
    /// 角色类型名称
    /// </summary>
    public string? TypeName { get; set; }

    /// <summary>
    /// 权限列表
    /// </summary>
    public List<string>? Powers { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string? Creator { get; set; }

    /// <summary>
    /// 关联人数
    /// </summary>
    public int Numbers { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = default!;
}

public class AddEntRole
{
    /// <summary>
    /// 角色名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 权限
    /// </summary>
    public List<string>? Powers { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }
}

public class AddEntRoleResponse
{
    /// <summary>
    /// 角色Id
    /// </summary>
    public string? RoleId { get; set; }
}

public class UpdateEntRole : AddEntRole
{
    /// <summary>
    /// 角色Id
    /// </summary>
    public string? RoleId { get; set; }
}

public class DeleteEntRole
{
    /// <summary>
    /// 角色Id
    /// </summary>
    public string? RoleId { get; set; }
}

public class UpdateUserRole
{
    /// <summary>
    /// 角色Id
    /// </summary>
    public string? RoleId { get; set; }

    /// <summary>
    /// hrId
    /// </summary>
    public List<string>? HrIds { get; set; }
}

public class UpdateHr
{
    /// <summary>
    /// hrid
    /// </summary>
    public List<string> HrIds { get; set; } = new List<string>();

    /// <summary>
    /// 角色Id，null不处理
    /// </summary>
    public string? RoleId { get; set; }

    /// <summary>
    /// 组织架构，null不处理
    /// </summary>
    public List<string>? OrgIds { get; set; }

    /// <summary>
    /// 同时删除原有部门
    /// </summary>
    public bool RemoveFromDept { get; set; }
}

public class RemoveHrFromOrg
{
    /// <summary>
    /// hrid
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// 组织架构Id
    /// </summary>
    public string? OrgId { get; set; }
}

public class GetEntHrLess
{
    /// <summary>
    /// 姓名或手机号
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 显示集团人员，默认不显示
    /// </summary>
    public bool ShowGroup { get; set; }
}

public class GetEntHrLessResponse
{
    public List<GetEntHrLessInfo> Rows { get; set; } = default!;
}

public class GetEntHrLessInfo
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 角色Id
    /// </summary>
    public string? RoleId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 手机
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }
}