﻿namespace Config.CommonModel.DingDingRoot;

/// <summary>
/// 钉钉机器人授权模型
/// </summary>
public class DingDingRootAccountToken
{
    /// <summary>
    /// 错误码
    /// </summary>
    public int errcode { get; set; }

    /// <summary>
    /// token内容
    /// </summary>
    public string? access_token { get; set; }

    /// <summary>
    /// 错误提示
    /// </summary>
    public string? errmsg { get; set; }

    /// <summary>
    /// access_token的过期时间，单位秒
    /// </summary>
    public int expires_in { get; set; }
}

