﻿using Config.CommonModel;
using Config.CommonModel.Tasks;
using Staffing.Model.Hr.Project;


namespace Staffing.Core.Interfaces.Hr;

public interface IProjectMemberService
{
    Task<UpdateProjectMemberResponse> UpdateProjectMember(UpdateProjectMember model);
    GetProjectMembersResponse GetProjectMembers(GetProjectMembers model);
    EmptyResponse DeleteProjectMember(DeleteProjectMember model);
    Task<EmptyResponse> ProjectMemberEntry(ProjectMemberEntry model);
    Task<EmptyResponse> ProjectMemberQuit(ProjectMemberQuit model);
    EmptyResponse EntryImport(EntryImport model);
    EmptyResponse EntryImportForNoah(EntryImportForNoah model);
    GetMyTasksResponse GetMyTasks(GetMyTasks model);
    GetMyTaskDetailsResponse<MyTaskContent> GetMyTaskDetails(GetMyTaskDetails model);
    Task<GetNoahProjectByNoResponse> GetNoahProjectByNo(GetNoahProjectByNo model);
}