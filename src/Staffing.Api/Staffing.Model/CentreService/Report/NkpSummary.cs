namespace Staffing.Model.CentreService.Report;

public class GetNkpSummary
{
    /// <summary>
    /// 起始时间
    /// </summary>
    public DateTime? BeginTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

public class ExportNkpRecruitSummary
{

}

public class GetNkpWeekReport
{
    /// <summary>
    /// 起始时间
    /// </summary>
    public DateTime? BeginTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    public string? Key { get; set; }
}

public class GetNkpWeekReportResponse
{
    public List<GetNkpWeekReportWeek1> Week { get; set; } = new List<GetNkpWeekReportWeek1>();
    public List<GetNkpWeekReportWeek2> Week2 { get; set; } = new List<GetNkpWeekReportWeek2>();

    public int 项目运行数 { get; set; }
    public int 上线项目 { get; set; }
    public int 在招岗位 { get; set; }
    public int 需求人数 { get; set; }
    public int 入职人数 { get; set; }
    public int 实缺人数 { get; set; }

    public int 微信注册人数 { get; set; }
    public int 快招工注册人数 { get; set; }
    public int 诺聘注册人数 { get; set; }
    public int 快手注册人数 { get; set; }
    public int 零工市场注册 { get; set; }

    public int 平台用户储备 { get; set; }
    public int 平台简历储备 { get; set; }
}

public class GetNkpWeekReportWeek1
{
    public string? Day { get; set; }
    public string? Week { get; set; }
    public int 注册量 { get; set; }
    public int 报名量 { get; set; }
    public int 面试量 { get; set; }
    public int 入职量 { get; set; }
}

public class GetNkpWeekReportWeek2
{
    public string? Day { get; set; }
    public string? Week { get; set; }
    public int 用户登录量 { get; set; }
    public int 顾问登录量 { get; set; }
}