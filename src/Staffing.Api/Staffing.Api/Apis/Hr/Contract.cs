using Infrastructure.Extend;
using Staffing.Model.Hr.Contract;
using Staffing.Core.Interfaces.Hr;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class ContractRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("contract").WithTags("合同管理");
        group.MapPost("/ent/org", async (UpdateEntOrg model, IContractService rep) => await rep.UpdateEntOrg(model))
            .WithOpenApi(operation => new(operation) { Summary = "添加签署机构" });

        group.MapDelete("/ent/org/{Id}", ([AsParameters] DeleteEntOrg model, IContractService rep) => rep.DeleteEntOrg(model))
            .WithOpenApi(operation => new(operation) { Summary = "移除签署机构" });

        group.MapGet("/ent/org", ([AsParameters] GetEntOrg model, IContractService rep) => rep.GetEntOrg(model))
            .WithOpenApi(operation => new(operation) { Summary = "查询签署机构" });
    }
}