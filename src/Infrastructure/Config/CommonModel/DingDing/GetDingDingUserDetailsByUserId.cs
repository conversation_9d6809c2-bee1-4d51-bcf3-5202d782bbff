﻿namespace Config.CommonModel.DingDing;

public class GetDingDingUserDetailsByUserId
{
}

/// <summary>
/// 根据userid获取用户详情请求模型
/// </summary>
public class GetDingDingUserDetailsByUserIdRequest
{
    /// <summary>
    /// 钉钉userid
    /// </summary>
    public string? userid { get; set; }
}

/// <summary>
/// 根据userid获取用户详情返回模型
/// </summary>
public class GetDingDingUserDetailsByUserIdResponse
{
    /// <summary>
    /// 是否激活了钉钉
    /// </summary>
    public bool active { get; set; }

    /// <summary>
    /// 是否为企业的管理员
    /// </summary>
    public bool admin { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? avatar { get; set; }

    /// <summary>
    /// 是否为企业的老板
    /// </summary>
    public bool boss { get; set; }

    /// <summary>
    /// 所属部门id列表
    /// </summary>
    public List<int>? dept_id_list { get; set; }

    /// <summary>
    /// 员工在对应的部门中的排序
    /// </summary>
    public List<dept_order_listModel>? dept_order_list { get; set; }

    /// <summary>
    /// 是否为专属帐号
    /// </summary>
    public bool exclusive_account { get; set; }

    /// <summary>
    /// 是否号码隐藏
    /// </summary>
    public bool hide_mobile { get; set; }

    /// <summary>
    /// 入职时间，Unix时间戳，单位毫秒
    /// </summary>
    public long hired_date { get; set; }

    /// <summary>
    /// 员工工号
    /// </summary>
    public string? job_number { get; set; }

    /// <summary>
    /// 员工所在部门信息及是否是领导
    /// </summary>
    public List<leader_in_deptModel>? leader_in_dept { get; set; }

    /// <summary>
    /// 员工的直属主管userid
    /// </summary>
    public string? manager_userid { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? mobile { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? name { get; set; }

    /// <summary>
    /// 是否完成了实名认证
    /// </summary>
    public bool real_authed { get; set; }

    /// <summary>
    /// 角色列表
    /// </summary>
    public List<role_listModel>? role_list { get; set; }

    /// <summary>
    /// 是否为企业的高管
    /// </summary>
    public bool senior { get; set; }

    /// <summary>
    /// 国际电话区号
    /// </summary>
    public string? state_code { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? title { get; set; }

    /// <summary>
    /// 员工在当前开发者企业账号范围内的唯一标识
    /// </summary>
    public string? unionid { get; set; }

    /// <summary>
    /// 员工的userId
    /// </summary>
    public string? userid { get; set; }
}

public class dept_order_listModel
{
    /// <summary>
    /// 部门id
    /// </summary>
    public int dept_id { get; set; }

    /// <summary>
    /// 员工在部门中的排序
    /// </summary>
    public int order { get; set; }
}

public class leader_in_deptModel
{
    /// <summary>
    /// 部门ID
    /// </summary>
    public int dept_id { get; set; }

    /// <summary>
    /// 是否是领导
    /// </summary>
    public bool leader { get; set; }
}

public class role_listModel
{
    /// <summary>
    /// 角色ID
    /// </summary>
    public string? group_name { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    public int id { get; set; }

    /// <summary>
    /// 角色组名称
    /// </summary>
    public string? name { get; set; }
}

/// <summary>
/// 根据部门id获取部门名称返回模型
/// </summary>
public class GetGetDingBranchByBranchIdResponse
{
    /// <summary>
    /// 部门ID
    /// </summary>
    public int? dept_id { get; set; }

    /// <summary>
    /// 部门名称
    /// </summary>
    public string? name { get; set; }

    /// <summary>
    /// 父部门ID
    /// </summary>
    public int? parent_id { get; set; }

    /// <summary>
    /// 部门标识字段(第三方企业应用不返回该参数)
    /// </summary>
    public string? source_identifier { get; set; }

    /// <summary>
    /// 是否同步创建一个关联此部门的企业群(true：创建,false：不创建)
    /// </summary>
    public bool? create_dept_group { get; set; }

    /// <summary>
    /// 当部门群已经创建后，是否有新人加入部门会自动加入该群(true：自动加入群,false：不会自动加入群)
    /// </summary>
    public bool? auto_add_user { get; set; }

    /// <summary>
    /// 是否默认同意加入该部门的申请(true：表示加入该部门的申请将默认同意,false：表示加入该部门的申请需要有权限的管理员同意)
    /// </summary>
    public bool? auto_approve_apply { get; set; }

    /// <summary>
    /// 部门是否来自关联组织(true：是,false：不是,第三方企业应用不返回该参数)
    /// </summary>
    public bool? from_union_org { get; set; }

    /// <summary>
    /// 教育部门标签(campus：校区,period：学段,grade：年级,class：班级)
    /// </summary>
    public string? tags { get; set; }

    /// <summary>
    /// 在父部门中的次序值
    /// </summary>
    public int? order { get; set; }

    /// <summary>
    /// 部门群ID
    /// </summary>
    public string? dept_group_chat_id { get; set; }

    /// <summary>
    /// 部门群是否包含子部门(true：包含,false：不包含)
    /// </summary>
    public bool? group_contain_sub_dept { get; set; }

    /// <summary>
    /// 企业群群主userId
    /// </summary>
    public string? org_dept_owner { get; set; }
}

