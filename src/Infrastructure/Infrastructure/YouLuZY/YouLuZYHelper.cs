﻿using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.YouLuZY.Model;
using Microsoft.AspNetCore.Cryptography.KeyDerivation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure.YouLuZY
{
    /// <summary>
    /// 优路云--蓝领证书在线培训
    /// </summary>
    public class YouLuZYHelper
    {
        #region 基本配置
        /// <summary>
        /// 接口的基础URL
        /// </summary>
        public const string ApiUrl_Base = "https://open.youluzy.com/youyun/open/";

        /// <summary>
        /// Token的缓存Key
        /// </summary>
        private const string _TokenCacheKey = "YouLuZY:Token";
        /// <summary>
        /// 优路云的【企业ID】
        /// </summary>
        private static string Client_Id(bool IsProduction)
        {
            if (IsProduction)
                return "f8e44617c8764ac7919fdf692a519039E5uXm3";
            else
                return "a888c4407a6846c9b95ef940211ba3dawAfBUY";
        }
        /// <summary>
        /// 优路云的Secret
        /// </summary>
        private static string Client_Secret(bool IsProduction)
        {
            if (IsProduction)
                return "ZGUwZWQ2YzY0ODJkNDRmMzkwM2RmODBhYjU5YmVjNGQxV0t4eU0=";
            else
                return "NDJkNzk1ZmZmOTUzNDFiYzlkNzZjMDBhNTg5YTc1NzdieXRtV1Q=";
        }
        /// <summary>
        /// 字符串AES加密密钥
        /// </summary>
        private static string EncodingAESKey(bool IsProduction)
        {
            if (IsProduction)
                return "YdWGquplJk";
            else
                return "axaYQJ0WUX";
        }
        #endregion

        public static async Task<(bool Result, string? ErrMsg, string Url)> GetToUrl(string redirect_url,
            string account_name, string name, bool IsProduction)
        {
            var authCodeResponse = await GetAuthCode(redirect_url, account_name, name, IsProduction);
            if (!authCodeResponse.Result)
                return (false, authCodeResponse.Msg ?? string.Empty, string.Empty);

            string[] urlArr = redirect_url.Split('#');
            return (true, string.Empty, $"{urlArr[0]}?authCode={authCodeResponse.Data!.Auth_Code}&scene=COURSE_DETAIL#{(urlArr.Length == 1 ? string.Empty : urlArr[1])}");
        }

        /// <summary>
        /// 系统跳转优路云系统时调用，获取一次性Code
        /// </summary>
        /// <param name="redirect_url">跳转的url（urlencode编码 格式:UTF-8） 用于服务端识别资源id</param>
        /// <param name="account_name">
        /// 1.企业内登录账号必须唯一，作为企业内成员唯一标识，创建后不可变更。(最大长度20)
        /// 2.aes加密传输aes 加密key联系优路云产品获取
        /// </param>
        /// <param name="name">
        /// 1.成员姓名，长度为1~10个utf8字符 
        /// 2.aes加密传输aes 加密key联系优路云产品获取。
        /// </param>
        /// <returns></returns>
        public static async Task<Response<Response_AuthCode>> GetAuthCode(string redirect_url,
            string account_name, string name, bool IsProduction)
        {
            if (string.IsNullOrEmpty(account_name))
                return new Response<Response_AuthCode> { Msg = "account_name不能为空" };
            var Result = await Post<Response_AuthCode>("api/v1/user/exemption/auth/code",
                new Dictionary<string, object> {
                    {"timestamp",GetTimestamp()},
                    {"nonce",GetNonce()},
                    {"scene","COURSE_DETAIL"},
                    {"redirect_url",WebUtility.UrlEncode(redirect_url) },
                    {"account_name",AesEncryptHelper.Encrypt(account_name,GetAESKey(IsProduction),null) },
                    {"name",AesEncryptHelper.Encrypt(name??"匿名用户",GetAESKey(IsProduction),null) }
                }, IsProduction);
            return Result;
        }

        private static string GetAESKey(bool IsProduction)
        {
            return (EncodingAESKey(IsProduction).Trim() + "***************").Substring(0, 16);
        }

        /// <summary>
        /// 获取免密登陆Token
        /// </summary>
        /// <param name="param"></param>
        /// <param name="deviceId">设备ID</param>
        /// <returns></returns>
        private static async Task<Response<Response_AccessToken>> GetAccessToken(bool IsProduction)
        {
            var Result = await MyRedis.Client.GetAsync<Response<Response_AccessToken>>(_TokenCacheKey);
            if (Result == null)
            {
                Result = await Post<Response_AccessToken>("api/v1/token/get",
                    new Dictionary<string, object> {
                        { "client_id",Client_Id(IsProduction)},
                        {"client_secret",Client_Secret(IsProduction) }
                    }, IsProduction, false);
                if (Result.Result)
                {
                    Result.Data!.Expires_Time = DateTime.Now.AddSeconds(Result.Data.Expires_In - 10);
                    await MyRedis.Client.SetAsync(_TokenCacheKey, Result, Result.Data.Expires_In - 10);
                }
            }
            return Result;
        }

        /// <summary>
        /// API POST请求方法
        /// </summary>
        /// <param name="method">方法名称</param>
        /// <param name="param">请求参数</param>
        /// <returns></returns>
        private static async Task<Response<T>> Post<T>(string method, Dictionary<string, object> param, bool IsProduction, bool IsWithToken = true) where T : class, new()
        {
            if (IsWithToken)
            {
                var Token = await GetAccessToken(IsProduction);
                if (!Token.Result)
                    return new Response<T> { Code = Token.Code, Msg = Token.Msg };
                // 设置要请求的 URL  
                method += $"?access_token={Token.Data!.Access_Token}";

                //生成签名
                method += GetSign(param, IsProduction);
            }

            // 设置要请求的 URL  
            string url = $"{ApiUrl_Base.TrimEnd('/')}/{method.TrimStart('/')}";

            string content = Newtonsoft.Json.JsonConvert.SerializeObject(param),
                ret = string.Empty;

            try
            {
                // 读取响应内容  
                ret = await url.PostJsonAsync(param).ReceiveString();
                try
                {
                    Response<T>? result = Newtonsoft.Json.JsonConvert.DeserializeObject<Response<T>>(ret);

                    if (result == null)
                        return new Response<T> { Msg = ret };
                    else
                    {
                        result.Result = result.Code == "0000";
                        if (!result.Result)
                            result.Msg = GetErrMsg(result.Code) + "," + result.Msg;
                    }

                    return result;
                }
                catch
                {
                    return new Response<T> { Msg = "解析返回结果失败:" + ret };
                }
            }
            catch (FlurlHttpException ex)
            {
                var error = await ex.GetResponseStringAsync();
                throw new Exception($"url:{url},param:{content},httpstatus:{ex.StatusCode}_{error}");
            }
        }

        /// <summary>
        /// API GET请求方法
        /// </summary>
        /// <param name="method">方法名称</param>
        /// <param name="param">请求参数</param>
        /// <returns></returns>
        private static async Task<Response<T>> Get<T>(string method, Dictionary<string, object> param, bool IsProduction, bool IsWithToken = true) where T : class, new()
        {
            var Token = await GetAccessToken(IsProduction);
            if (!Token.Result)
                return new Response<T> { Code = Token.Code, Msg = Token.Msg };
            // 设置要请求的 URL  
            method += $"?access_token={Token.Data!.Access_Token}";
            //生成签名
            method += GetSign(param, IsProduction);

            // 设置要请求的 URL  
            var url = $"{ApiUrl_Base.TrimEnd('/')}/{method.TrimStart('/')}";
            var ret = string.Empty;
            try
            {
                // 创建 HTTP GET 请求的内容
                if (param != null && param.Count > 0)
                {
                    foreach (var item in param)
                    {
                        url += $"&{item.Key}={item.Value}";
                    }
                }
                // 读取响应内容  
                ret = await url.GetAsync().ReceiveString(); ;
                try
                {
                    Response<T>? result = Newtonsoft.Json.JsonConvert.DeserializeObject<Response<T>>(ret);

                    if (result == null)
                        return new Response<T> { Msg = ret };
                    else
                    {
                        result.Result = result.Code == "0000";
                        if (!result.Result)
                            result.Msg = GetErrMsg(result.Code) + "," + result.Msg;
                    }
                    return result;
                }
                catch (Exception ee)
                {
                    throw new Exception($"解析返回结果失败，参数：url={url}，method={method},返回结果={ret}", ee);
                }
            }
            catch (FlurlHttpException ex)
            {
                var error = await ex.GetResponseStringAsync();
                throw new Exception($"url:{url},httpstatus:{ex.StatusCode}_{error}");
            }
        }

        private static string GetSign(Dictionary<string, object> paramsMap, bool IsProduction)
        {
            if (paramsMap == null || paramsMap.Count == 0)
                return string.Empty;
            else
            {
                // 按字母顺序排序键
                var sortedKeys = paramsMap.Keys.OrderBy(k => k).ToList();

                var sb = new StringBuilder();
                foreach (var key in sortedKeys)
                {
                    // 值为空时处理为空字符
                    sb.Append($"{key}={(paramsMap[key] ?? string.Empty)}&");
                }

                //// 移除最后一个多余的&
                //if (sb.Length > 0 && sb[sb.Length - 1] == '&')
                //    sb.Length--;

                sb.Append(Client_Id(IsProduction));

                // 计算MD5（保持与Java兼容）
                var sign = Md5Helper.Md5(sb.ToString()).ToLower();

                return "&sign=" + sign;
            }
        }
        private static string GetTimestamp()
        {
            //获取当前Unix时间戳（秒级）
            return DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();
        }
        private static string GetNonce()
        {
            //生成8位安全随机字符串
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, 8)
                .Select(s => s[random.Next(s.Length)])
                .ToArray());
        }

        static Lazy<Dictionary<string, string>> ErrDic = new(() =>
        {
            return new Dictionary<string, string>() {
                { "0000","请求成功"},
                { "2001","处理异常"},
                { "401","token校验失败"}
            };
        });
        public static string GetErrMsg(string errCode)
        {
            if (string.IsNullOrEmpty(errCode))
                return string.Empty;
            return ErrDic.Value.TryGetValue(errCode, out var errMsg) ? errMsg : "未知错误";
        }
    }
}
