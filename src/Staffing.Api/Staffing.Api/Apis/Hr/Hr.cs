using Config;
using Config.CommonModel;
using Config.CommonModel.Business;
using Config.CommonModel.Tencent;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Extend;
using Staffing.Model.Hr.Balance;
using Staffing.Model.Hr.User;
using Staffing.Model.Service;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces.Hr;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class HrRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("user").WithTags("用户");
        group.MapPost("/token", async (HrLogin model, IHrService rep) => await rep.HrLogin(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "Hr登录" });

        group.MapPost("/token/refresh", async (HrRefreshToken model, IHrService rep) => await rep.HrRefreshToken(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "刷新token" });

        group.MapPost("/apply/app", async (HrApplyApp model, IHrService rep) => await rep.HrApplyApp(model))
            .WithMetadata(new ApiMeta { AuthType = ApiAuthType.诺聘验证 })
            .WithOpenApi(operation => new(operation) { Summary = "申请开通应用" });

        group.MapGet("", (IHrService rep) => rep.GetHr())
            .WithMetadata(new ApiMeta { AuthType = ApiAuthType.诺聘验证 })
            .WithOpenApi(operation => new(operation) { Summary = "用户信息" });

        group.MapGet("/apps", (IHrService rep) => rep.GetApps())
            .WithMetadata(new ApiMeta { AuthType = ApiAuthType.诺聘验证 })
            .WithOpenApi(operation => new(operation) { Summary = "获取我的应用列表" });

        group.MapGet("/apps/{id}", (string id, IHrService rep) => rep.GetAppInfo(id))
            .WithMetadata(new ApiMeta { AuthType = ApiAuthType.诺聘验证 })
            .WithOpenApi(operation => new(operation) { Summary = "获取应用详情" });

        group.MapPost("/app/used", (UseApp model, IHrService rep) => rep.UseApp(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "使用App" });

        group.MapPost("/list/simple", (QuerySearch model, IHrService rep) => rep.GetHrSimpleSearch(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取顾问列表" });

        group.MapGet("/card", (string? hrId, IHrService rep) => rep.GetHrCard(hrId ?? string.Empty))
            .WithOpenApi(operation => new(operation) { Summary = "hr卡片" });

        group.MapPut("/card", (UpdateHrCard model, IHrService rep) => rep.UpdateHrCard(model))
            .WithOpenApi(operation => new(operation) { Summary = "更新hr信息" });

        group.MapGet("/settings", (IHrService rep) => rep.GetHrSetting())
            .WithOpenApi(operation => new(operation) { Summary = "Hr设置查询" });

        group.MapPut("/settings", (HrSettingInfo model, IHrService rep) => rep.UpdateHrSetting(model))
            .WithOpenApi(operation => new(operation) { Summary = "Hr设置更新" });

        group.MapPost("/mobile/check", (UserCheckMobile model, ICommonService rep) => rep.UserCheckMobile(model))
            .WithOpenApi(operation => new(operation) { Summary = "验证用户手机号" });

        group.MapGet("/im/sig", async (IHrService rep) => await rep.GetImUserSig())
            .WithOpenApi(operation => new(operation) { Summary = "获取im即时通讯usersig" });

        group.MapPost("/im/msg/read", (ImMsgRead model, RequestContext user, TencentImHelper tencentImHelper) =>
        {
            var result = tencentImHelper.ImMsgRead(new ImMsgReadFull
            {
                ImId = $"{Constants.ImHrIdPre}{user.Id}",
                ToImId = model.ToImId
            });
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "消息本地已读（非IM）" });

        group.MapGet("/idcard/check", ([AsParameters] GetIdVerification model, ICommonService rep) => rep.GetIdVerification(model))
            .WithOpenApi(operation => new(operation) { Summary = "检测身份证号是否被其他人使用" });

        group.MapPost("/idcard", async (IdVerificationModel model, ICommonService rep) => await rep.IdVerification(model))
            .WithOpenApi(operation => new(operation) { Summary = "实名认证" });

        group.MapGet("/msg/words", (ICommonService rep) => rep.GetMsgWords(MsgWordsType.顾问))
            .WithOpenApi(operation => new(operation) { Summary = "常用语列表" });

        group.MapPost("/msg/words", (MsgWordsInfo model, ICommonService rep) => rep.UpdateMsgWords(model, MsgWordsType.顾问))
            .WithOpenApi(operation => new(operation) { Summary = "更新常用语" });

        group.MapDelete("/msg/words/{id}", (string id, ICommonService rep) => rep.DeleteMsgWords(id))
            .WithOpenApi(operation => new(operation) { Summary = "删除常用语" });

        group.MapGet("/resume/{id}", (string id, ICommonService rep) => rep.GetResume(id, false))
            .WithOpenApi(operation => new(operation) { Summary = "简历详情" });

        group.MapGet("/delivery/posts", (string seekerId, RequestContext user, ICommonService rep) =>
        {
            var result = rep.GetDeliveryPosts(new GetDeliveryPosts
            {
                HrId = user.Id,
                SeekerId = seekerId
            });
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "获取用户报名的所有职位" });

        group.MapGet("/msg", (RequestContext user, IHrService rep) =>
        {
            if (string.IsNullOrEmpty(user.Id))
                return new GetHrMessageSumResponse();

            var result = rep.GetHrMessageSum();
            return result;
        }).WithMetadata(new ApiMeta { AuthType = ApiAuthType.诺聘验证 })
          .WithOpenApi(operation => new(operation) { Summary = "消息汇总" });

        group.MapGet("/msg/list", ([AsParameters] GetHrMessage model, RequestContext user, IHrService rep) =>
        {
            if (string.IsNullOrEmpty(user.Id))
                return new GetHrMessageResponse();

            var result = rep.GetHrMessage(model);
            return result;
        }).WithMetadata(new ApiMeta { AuthType = ApiAuthType.诺聘验证 })
          .WithOpenApi(operation => new(operation) { Summary = "消息" });

        group.MapGet("/data", (IHrService rep) => rep.GetHrData())
            .WithOpenApi(operation => new(operation) { Summary = "hr数据" });

        group.MapGet("/recent/delivery", ([AsParameters] GetRecentDelivery model, IHrService rep) => rep.GetRecentDelivery(model))
            .WithOpenApi(operation => new(operation) { Summary = "最近投递" });

        group.MapGet("/recent/newtalent", ([AsParameters] GetNewTalent model, IHrService rep) => rep.GetNewTalent(model))
            .WithOpenApi(operation => new(operation) { Summary = "人才库最近新增" });

        group.MapGet("/recent/visits", ([AsParameters] GetRecentVisits model, IHrService rep) => rep.GetRecentVisits(model))
            .WithOpenApi(operation => new(operation) { Summary = "最近访问" });

        group.MapPost("/seeker/phone", async (GetPrivacyPhoneNumber model, IHrService rep) => await rep.GetPrivacyPhoneNumber(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取可拨打的隐私号码（在人才库则不需要加密）" });

        group.MapPost("/invite/sms", async (SendInviteSms model, IHrService rep) => await rep.SendInviteSms(model))
            .WithOpenApi(operation => new(operation) { Summary = "发短信邀约" });

        group.MapGet("/dingding", (IHrService rep) => rep.GetDingDingBindingStatsu())
            .WithOpenApi(operation => new(operation) { Summary = "获取钉钉绑定状态" });

        group.MapPost("/dingding", async (BindingDingDingRequest model, IHrService rep) => await rep.BindingDingDing(model))
            .WithOpenApi(operation => new(operation) { Summary = "绑定钉钉，获取钉钉用户信息" });

        group.MapDelete("/dingding", (IHrService rep) => rep.RelieveDingDingBinding())
            .WithOpenApi(operation => new(operation) { Summary = "移除钉钉绑定" });

        group.MapGet("/balance", ([AsParameters] GetBalance model, IHrService rep) => rep.GetBalance(model))
            .WithOpenApi(operation => new(operation) { Summary = "我的钱包" });

        group.MapGet("/balance/record", ([AsParameters] GetBalanceRecord model, IHrService rep) => rep.GetBalanceRecord(model))
            .WithOpenApi(operation => new(operation) { Summary = "我的钱包流水" });

        group.MapPost("/withdraw", async (Withdraw model, IHrService rep) => await rep.Withdraw(model))
            .WithOpenApi(operation => new(operation) { Summary = "提现" });

        group.MapPut("/card/show", (HrCardShow model, IHrService rep) => rep.SetHrCardShow(model))
            .WithOpenApi(operation => new(operation) { Summary = "hr卡片显示设置" });

        group.MapGet("/card/show", (IHrService rep) => rep.GetHrCardShow())
            .WithOpenApi(operation => new(operation) { Summary = "hr卡片显示查询" });


        group.MapGet("/qywechat", (IQYWechatService rep) => rep.CheckIsBinding())
        .WithOpenApi(operation => new(operation) { Summary = "获取企微绑定状态" });

        group.MapPost("/qywechat", async (BindingQYWechatRequest model, IQYWechatService rep) => await rep.Binding(model))
            .WithOpenApi(operation => new(operation) { Summary = "绑定企微" });

        group.MapDelete("/qywechat", (IQYWechatService rep) => rep.RelieveBinding())
            .WithOpenApi(operation => new(operation) { Summary = "移除企微绑定" });

        group.MapPost("/qywechat/grouplist", async (QuerySearch request, IQYWechatService rep) => await rep.GetMyQWGroup(request))
           .WithOpenApi(operation => new(operation) { Summary = "获取我的企微群列表" });
    }
}