using Config.CommonModel;
using Staffing.Entity.Staffing;

namespace Staffing.Model.CentreService.System;

public class AddUserSms
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Amount { get; set; }
}

public class SendSms
{
    /// <summary>
    /// 不存在的手机号（多个手机号换行，excel直接复制）
    /// </summary>
    public string? Mobiles { get; set; }
}

public class SendSmsResponse
{
    /// <summary>
    /// 不存在的手机号
    /// </summary>
    public List<string> NotExistsMobiles { get; set; } = new List<string>();
}

/// <summary>
/// 自定义内容发送短信参数
/// </summary>
public class SendSmsCustom
{
    /// <summary>
    /// 不存在的手机号（多个手机号换行，excel直接复制）
    /// </summary>
    public string[]? Mobiles { get; set; }
    /// <summary>
    /// 发送内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 小程序地址
    /// </summary>
    public string? Url { get; set; }
}

/// <summary>
/// 获取自定义内容短信发送列表参数
/// </summary>
public class GetSmsCustom: QueryRequest
{
    /// <summary>
    /// 开始时间
    /// </summary>
    public string? BeginTime { get; set; }
    /// <summary>
    /// 截止时间
    /// </summary>
    public string? EndTime { get; set; }

    /// <summary>
    /// 内容(模糊检索)
    /// </summary>
    public string? Content { get; set; }
}


/// <summary>
/// 获取自定义内容短信发送列表响应内容
/// </summary>
public class GetSmsCustomResponse: QueryResponse
{
    public List<SmsCustomRecord> Rows { get; set; } = new List<SmsCustomRecord>();
}

/// <summary>
/// 获取自定义内容短信发送列表响应内容
/// </summary>
public class SmsCustomRecord
{
    public int Id { get; set; }

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 发送数量
    /// </summary>
    public int MobileCount { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public SmsTasksStatus Status { get; set; }

    /// <summary>
    /// 短信模板内容(页面输入的发送内容；SendType=庄点科技drondea批量时，Sms_Tasks_Detail表内就无需记录实际发送内容了(因与此字段一样))
    /// </summary>
    public string Content { get; set; } = default!;
}