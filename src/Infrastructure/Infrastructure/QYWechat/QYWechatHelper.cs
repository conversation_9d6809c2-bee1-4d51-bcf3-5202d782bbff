﻿using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.QYWechat.Model;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace Infrastructure.QYWechat
{
    public class QYWechatHelper
    {
        #region 基本配置
        /// <summary>
        /// 接口的基础URL
        /// </summary>
        public const string ApiUrl_Base = "https://qyapi.weixin.qq.com/";
        /// <summary>
        /// 获取Token的接口URL
        /// </summary>
        public const string ApiUrl_GetToken = "https://qyapi.weixin.qq.com/cgi-bin/gettoken";
        /// <summary>
        /// 企业微信的【企业ID】
        /// </summary>
        public const string Corpid = "wx1b8cae121eb0612a";
        /// <summary>
        /// 企业微信的应用中【诺快聘求职者端】小程序的Secret
        /// </summary>
        public const string Corpsecret = "1jKwCKJzTUjyClLr4wa0ZMe6POx0rPJzX3kB-7j2Cx0";

        public const string EncodingAESKey = "bPzXVAW3BlyaOKLoBY2Loo1rqVP1z6S4trKJdRv7PhD";
        public const string Token = "VBDr3eHUWNO5DL1Bl";

        /// <summary>
        /// Token的缓存Key
        /// </summary>
        const string _TokenCacheKey = "QYWechat:Token";
        #endregion

        #region 回调消息验证
        /// <summary>
        /// 验证URL
        /// </summary>
        /// <param name="sMsgSignature">签名串，对应URL参数的msg_signature</param>
        /// <param name="sTimeStamp">时间戳，对应URL参数的timestamp</param>
        /// <param name="sNonce">随机串，对应URL参数的nonce</param>
        /// <param name="sEchoStr">随机串，对应URL参数的echostr</param>
        /// <returns>成功0，失败返回对应的错误码；
        /// ReplyEchoStr=解密之后的echostr</returns>
        public static (bool Result, string ErrMsg, string ReplyEchoStr) VerifyURL(string sMsgSignature, string sTimeStamp, string sNonce, string sEchoStr)
        {
            string ReplyEchoStr = string.Empty;
            var result = WXBizMsgCrypt.VerifyURL(sMsgSignature, sTimeStamp, sNonce, sEchoStr, ref ReplyEchoStr);
            return (result == 0, WXBizMsgCrypt.GetErrMsg(result), ReplyEchoStr);
        }

        /// <summary>
        /// 检验消息的真实性，并且获取解密后的明文
        /// </summary>
        /// <param name="sMsgSignature"> 签名串，对应URL参数的msg_signature</param>
        /// <param name="sTimeStamp">时间戳，对应URL参数的timestamp</param>
        /// <param name="sNonce">随机数，对应URL参数的nonce</param>
        /// <param name="sPostData"> 密文，对应POST请求的数据</param>
        /// <returns>成功0，失败返回对应的错误码；
        /// Msg=解密后的原文</returns>
        public static (bool Result, string ErrMsg, string Msg) DecryptMsg(string sMsgSignature, string sTimeStamp, string sNonce, string sPostData)
        {
            string Msg = string.Empty;
            var result = WXBizMsgCrypt.DecryptMsg(sMsgSignature, sTimeStamp, sNonce, sPostData, ref Msg);
            return (result == 0, WXBizMsgCrypt.GetErrMsg(result), Msg);
        }

        /// <summary>
        /// 将企业号回复用户的消息加密打包
        /// </summary>
        /// <param name="sReplyMsg">企业号待回复用户的消息，xml格式的字符串</param>
        /// <param name="sTimeStamp">时间戳，可以自己生成，也可以用URL参数的timestamp</param>
        /// <param name="sNonce">随机串，可以自己生成，也可以用URL参数的nonce</param>
        /// <returns>成功0，失败返回对应的错误码；
        /// EncryptMsg=加密后的可以直接回复用户的密文，包括msg_signature, timestamp, nonce, encrypt的xml格式的字符串</returns>
        public static (bool Result, string ErrMsg, string EncryptMsg) EncryptMsg(string sReplyMsg, string sTimeStamp, string sNonce)
        {
            string EncryptMsg = string.Empty;
            var result = WXBizMsgCrypt.EncryptMsg(sReplyMsg, sTimeStamp, sNonce, ref EncryptMsg);
            return (result == 0, WXBizMsgCrypt.GetErrMsg(result), EncryptMsg);
        }
        #endregion

        #region Employee【通讯录】获取成员
        /// <summary>
        /// 获取成员ID列表
        /// </summary>
        /// <param name="limit">分页，预期请求的数据量，取值范围 1 ~ 10000</param>
        /// <param name="cursor">用于分页查询的游标，字符串类型，由上一次调用返回，首次调用不填</param>
        /// <returns></returns>
        public static async Task<Response_EmployeeListId> GetEmployeeListId(int limit = 1000, string? cursor = null)
        {
            if (limit < 1)
                limit = 1000;
            else if (limit > 1000)
                return new Response_EmployeeListId { ErrCode = -1, ErrMsg = "请求的数据量最多为1000" };
            var Result = await Post<Response_EmployeeListId>("cgi-bin/user/list_id", new Dictionary<string, object> {
                    { "cursor",cursor??string.Empty},
                    {"limit",limit }
                });
            return Result;
        }

        /// <summary>
        /// 获取成员信息详情
        /// </summary>
        /// <param name="userid">	成员UserID。对应管理端的账号，企业内必须唯一。不区分大小写，长度为1~64个字节</param>
        /// <returns></returns>
        public static async Task<Response_EmployeeInfo> GetEmployeeInfo(string userid)
        {
            if (string.IsNullOrEmpty(userid))
                return new Response_EmployeeInfo { ErrCode = -1, ErrMsg = "userid不能为空" };
            var Result = await Get<Response_EmployeeInfo>("cgi-bin/user/get", new Dictionary<string, object> {
                    { "userid",userid}
                });
            return Result;
        }

        /// <summary>
        /// 根据手机号获取userid
        /// </summary>
        /// <param name="mobile">用户在企业微信通讯录中的手机号码。长度为5~32个字节</param>
        /// <returns></returns>
        public static async Task<Response_EmployeeUserId> GetUserIdByMobile(string mobile)
        {
            if (string.IsNullOrEmpty(mobile))
                return new Response_EmployeeUserId { ErrCode = -1, ErrMsg = "userid不能为空" };
            var Result = await Post<Response_EmployeeUserId>("cgi-bin/user/getuserid", new Dictionary<string, object> {
                    { "mobile",mobile}
                });
            return Result;
        }

        /// <summary>
        /// 获取部门列表
        /// </summary>
        /// <param name="id">部门id。获取指定部门及其下的子部门（以及子部门的子部门等等，递归）。 如果不填，默认获取全量组织架构</param>
        /// <returns></returns>
        public static async Task<Response_DepartmentList> GetDepartmentList(string? id = null)
        {
            var Result = await Get<Response_DepartmentList>("cgi-bin/department/list",
                new Dictionary<string, object> {
                    { "id",id??string.Empty}
                });
            return Result;
        }

        /// <summary>
        /// 获取部门成员详情
        /// </summary>
        /// <param name="department_id">获取的部门id</param>
        /// <returns></returns>
        public static async Task<Response_EmployeeByDepartment> GetEmployeeByDepartment(string department_id)
        {
            if (string.IsNullOrEmpty(department_id))
                return new Response_EmployeeByDepartment { ErrCode = -1, ErrMsg = "部门ID为空" };
            var Result = await Get<Response_EmployeeByDepartment>("cgi-bin/user/list",
                new Dictionary<string, object> {
                    { "department_id",department_id}
                });
            return Result;
        }
        #endregion

        #region GroupChat【客户群】
        /// <summary>
        /// 获取客户群列表
        /// </summary>
        /// <param name="limit">分页，预期请求的数据量，取值范围 1 ~ 10000</param>
        /// <param name="cursor">用于分页查询的游标，字符串类型，由上一次调用返回，首次调用不填</param>
        /// <param name="owner_userids">群主过滤。如果不填，表示获取应用可见范围内全部群主的数据，当群主为离职成员时，必须要指定群主过滤才可拉取对应数据</param>
        /// <returns></returns>
        public static async Task<Response_GroupChatList> GetGroupChatList(int limit = 1000, string? cursor = null, List<string>? owner_userids = null)
        {
            if (limit < 1)
                limit = 1000;
            else if (limit > 1000)
                return new Response_GroupChatList { ErrCode = -1, ErrMsg = "请求的数据量最多为1000" };
            var param = new Dictionary<string, object>
            {
                { "limit", limit.ToString() },
                { "cursor", cursor ?? string.Empty }
            };
            if (owner_userids != null && owner_userids.Count > 0)
                param.Add("owner_filter", new { userid_list = owner_userids });
            var Result = await Post<Response_GroupChatList>("cgi-bin/externalcontact/groupchat/list", param);
            return Result;
        }

        /// <summary>
        /// 获取客户群详情
        /// </summary>
        /// <param name="chat_id">客户群ID</param>
        /// <param name="need_name">是否需要返回群成员的名字，true=返回</param>
        /// <returns></returns>
        public static async Task<Response_GroupChatInfo> GetGroupChatInfo(string chat_id, bool need_name = true)
        {
            if (string.IsNullOrEmpty(chat_id))
                return new Response_GroupChatInfo { ErrCode = -1, ErrMsg = "chat_id不能为空" };
            var Result = await Post<Response_GroupChatInfo>("cgi-bin/externalcontact/groupchat/get", new Dictionary<string, object> {
                    { "chat_id",chat_id},
                    { "need_name",need_name?1:0}//0-不返回；1-返回。默认不返回
                });
            return Result;
        }
        #endregion

        /// <summary>
        /// 获取免密登陆Token
        /// </summary>
        /// <param name="param"></param>
        /// <param name="deviceId">设备ID</param>
        /// <returns></returns>
        private static async Task<Response_AccessToken> GetAccessToken()
        {
            var Result = await MyRedis.Client.GetAsync<Response_AccessToken>(_TokenCacheKey);
            if (Result == null)
            {
                Result = await Get<Response_AccessToken>("cgi-bin/gettoken", new Dictionary<string, object> {
                    { "corpid",Corpid},
                    {"corpsecret",Corpsecret }
                }, false);
                if (Result.Result)
                {
                    Result.Expires_Time = DateTime.Now.AddSeconds(Result.Expires_In - 10);
                    await MyRedis.Client.SetAsync(_TokenCacheKey, Result, Result.Expires_In - 10);
                }
            }
            return Result;
        }

        /// <summary>
        /// API POST请求方法
        /// </summary>
        /// <param name="method">方法名称</param>
        /// <param name="param">请求参数</param>
        /// <returns></returns>
        private static async Task<T> Post<T>(string method, Dictionary<string, object> param) where T : ResponseBase, new()
        {
            var Token = await GetAccessToken();
            if (!Token.Result)
                return new T { ErrCode = Token.ErrCode, ErrMsg = Token.ErrMsg };
            // 设置要请求的 URL  
            string url = $"{ApiUrl_Base.TrimEnd('/')}/{method.TrimStart('/')}?access_token={Token.Access_Token}";

            string content = Newtonsoft.Json.JsonConvert.SerializeObject(param),
                ret;

            try
            {
                // 读取响应内容  
                ret = await url.PostJsonAsync(param).ReceiveString();
                try
                {
                    T? result = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(ret);

                    if (result == null)
                        return new T { ErrCode = -1, ErrMsg = ret };
                    else
                    {
                        result.Result = result.ErrCode == 0;
                        if (!result.Result)
                            result.ErrMsg = GetErrMsg(result.ErrCode) + "," + result.ErrMsg;
                    }

                    return result;
                }
                catch
                {
                    return new T { ErrCode = -991, ErrMsg = "解析返回结果失败:" + ret };
                }
            }
            catch (FlurlHttpException ex)
            {
                var error = await ex.GetResponseStringAsync();
                throw new Exception($"url:{url},param:{content},httpstatus:{ex.StatusCode}_{error}");
            }
        }

        /// <summary>
        /// API GET请求方法
        /// </summary>
        /// <param name="method">方法名称</param>
        /// <param name="param">请求参数</param>
        /// <returns></returns>
        private static async Task<T> Get<T>(string method, Dictionary<string, object> param, bool IsWithToken = true) where T : ResponseBase, new()
        {
            // 设置要请求的 URL  
            var url = $"{ApiUrl_Base.TrimEnd('/')}/{method.TrimStart('/')}";
            if (IsWithToken)
            {
                var Token = await GetAccessToken();
                if (!Token.Result)
                    return new T { ErrCode = Token.ErrCode, ErrMsg = Token.ErrMsg };
                // 设置要请求的 URL  
                url += $"?access_token={Token.Access_Token}";
            }
            else
                url += "?";
            string ret;
            try
            {
                // 创建 HTTP GET 请求的内容
                if (param != null && param.Count > 0)
                {
                    foreach (var item in param)
                    {
                        url += $"&{item.Key}={item.Value}";
                    }
                }
                // 读取响应内容  
                ret = await url.GetAsync().ReceiveString(); ;
                try
                {
                    T? result = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(ret);

                    if (result == null)
                        return new T { ErrCode = -1, ErrMsg = ret };
                    else
                    {
                        result.Result = result.ErrCode == 0;
                        if (!result.Result)
                            result.ErrMsg = GetErrMsg(result.ErrCode) + "," + result.ErrMsg;
                    }
                    return result;
                }
                catch (Exception ee)
                {
                    throw new Exception($"解析返回结果失败，参数：url={url}，method={method},返回结果={ret}", ee);
                }
            }
            catch (FlurlHttpException ex)
            {
                var error = await ex.GetResponseStringAsync();
                throw new Exception($"url:{url},httpstatus:{ex.StatusCode}_{error}");
            }
        }

        /// <summary>
        /// 将时间戳转为DateTime
        /// </summary>
        /// <param name="timeStamp"></param>
        /// <returns></returns>
        public static DateTime ExchangeTimeStamp(long timeStamp)
        {
            //处理毫秒级时间戳
            if (timeStamp > 10000000000)
                return DateTimeOffset.FromUnixTimeMilliseconds(timeStamp).LocalDateTime;
            else
                return DateTimeOffset.FromUnixTimeSeconds(timeStamp).LocalDateTime;
        }

        public static string CreateGroupMemberId(string GroupId, string UserId)
        {
            return GroupId + UserId;
        }
        public static string GetGroupMemberId(string GroupId, string MemberId)
        {
            if (MemberId.StartsWith(GroupId))
                return MemberId[GroupId.Length..];
            return MemberId;
        }

        static readonly Lazy<Dictionary<int, string>> ErrDic = new(() =>
        {
            return new Dictionary<int, string>() {
                {-1,"系统繁忙"},
                {0,"请求成功"},
                {6000,"数据版本冲突"},
                {40001,"不合法的secret参数"},
                {40003,"无效的UserID"},
                {40004,"不合法的媒体文件类型"},
                {40005,"不合法的type参数"},
{40006,"不合法的文件大小"},
{40007,"不合法的media_id参数"},
{40008,"不合法的msgtype参数"},
{40009,"上传图片大小不是有效值"},
{40011,"上传视频大小不是有效值"},
{40013,"不合法的CorpID"},
{40014,"不合法的access_token"},
{40016,"不合法的按钮个数"},
{40017,"不合法的按钮类型"},
{40018,"不合法的按钮名字长度"},
{40019,"不合法的按钮KEY长度"},
{40020,"不合法的按钮URL长度"},
{40022,"不合法的子菜单级数"},
{40023,"不合法的子菜单按钮个数"},
{40024,"不合法的子菜单按钮类型"},
{40025,"不合法的子菜单按钮名字长度"},
{40026,"不合法的子菜单按钮KEY长度"},
{40027,"不合法的子菜单按钮URL长度"},
{40029,"不合法的oauth_code"},
{40031,"不合法的UserID列表"},
{40032,"不合法的UserID列表长度"},
{40033,"不合法的请求字符"},
{40035,"不合法的参数"},
{40036,"不合法的模板id长度"},
{40037,"无效的模板id"},
{40039,"不合法的url长度"},
{40050,"chatid不存在"},
{40054,"不合法的子菜单url域名"},
{40055,"不合法的菜单url域名"},
{40056,"不合法的agentid"},
{40057,"不合法的callbackurl或者callbackurl验证失败"},
{40058,"不合法的参数"},
{40059,"不合法的上报地理位置标志位"},
{40063,"参数为空"},
{40066,"不合法的部门列表"},
{40068,"不合法的标签/标签组ID"},
{40070,"指定的标签范围节点全部无效"},
{40071,"不合法的标签名字"},
{40072,"不合法的标签名字长度"},
{40073,"不合法的openid"},
{40074,"news消息不支持保密消息类型"},
{40077,"不合法的pre_auth_code参数"},
{40078,"不合法的auth_code参数"},
{40080,"不合法的suite_secret"},
{40082,"不合法的suite_token"},
{40083,"不合法的suite_id"},
{40084,"不合法的permanent_code参数"},
{40085,"不合法的的suite_ticket参数"},
{40086,"不合法的第三方应用appid"},
{40088,"jobid不存在"},
{40089,"批量任务的结果已清理"},
{40091,"secret不合法"},
{40092,"导入文件存在不合法的内容"},
{40093,"jsapi签名错误"},
{40094,"不合法的URL"},
{40096,"不合法的外部联系人userid"},
{40097,"该成员尚未离职"},
{40098,"成员尚未实名认证"},
{40099,"成员的外部联系人数量已达上限"},
{40100,"此用户的外部联系人已经在转移流程中"},
{40102,"域名或IP不可与应用市场上架应用重复"},
{40106,"device_access_token非法"},
{40123,"上传临时图片素材，图片格式非法"},
{40124,"推广活动里的sn禁止绑定"},
{40125,"无效的openuserid参数"},
{40126,"企业标签个数达到上限，最多为3000个"},
{40127,"不支持的uri schema"},
{40128,"客户转接过于频繁（90个自然日内，在职成员的每位客户仅可被转接2次）"},
{40129,"当前客户正在转接中"},
{40130,"原跟进人与接手人一样，不可继承"},
{40131,"handover_userid 并不是外部联系人的跟进人"},
{40132,"团队号不支持此操作"},
{40133,"当前企业禁止成员授权"},
{40134,"此成员授权应用已被禁用"},
{40135,"禁止授权相似应用"},
{40136,"应用试用到期"},
{40137,"应用服务到期"},
{40138,"应用付费可用人数超限"},
{40139,"成员授权使用人数超限"},
{40140,"企业安装了套件中其它应用"},
{40141,"企业账号异常"},
{40142,"无效的场所码"},
{40143,"转接成员被封禁，无法进行客户继承操作"},
{40145,"mediaid已过期失效"},
{40165,"已经升级了客户群ID，无法再次升级"},
{40166,"还未升级客户群ID，无法调用接口"},
{40167,"指定的升级时间不合法"},
{40168,"已经超过了指定的升级时间"},
{40201,"当前操作包含敏感信息，被反垃圾拦截"},
{40203,"请求参数已废弃"},
{40204,"微信反垃圾"},
{40205,"成员微信票据过期"},
{40206,"请求body字节数超过限制"},
{40207,"不合法的tfa_code"},
{40208,"验证的用户不在二次验证生效范围内"},
{40209,"oauth跳转域名与二次验证的域名不匹配"},
{40210,"未配置二次验证url"},
{40211,"不合法的RSA公钥"},
{40212,"公钥版本号不能低于旧公钥的版本号"},
{41001,"缺少access_token参数"},
{41002,"缺少corpid参数"},
{41004,"缺少secret参数"},
{41006,"缺少media_id参数"},
{41008,"缺少auth code参数"},
{41009,"缺少userid参数"},
{41010,"缺少url参数"},
{41011,"缺少agentid参数"},
{41016,"缺少title参数"},
{41017,"缺少tagid参数"},
{41018,"缺少标签名"},
{41019,"缺少 department 参数"},
{41021,"缺少suite_id参数"},
{41022,"缺少suite_access_token参数"},
{41023,"缺少suite_ticket参数"},
{41024,"缺少secret参数"},
{41025,"缺少permanent_code参数"},
{41033,"缺少 description 参数"},
{41035,"缺少外部联系人userid参数"},
{41036,"不合法的企业对外简称"},
{41037,"缺少「联系我」type参数"},
{41038,"缺少「联系我」scene参数"},
{41039,"无效的「联系我」type参数"},
{41040,"无效的「联系我」scene参数"},
{41041,"「联系我」使用人数超过限制"},
{41042,"无效的「联系我」style参数"},
{41043,"缺少「联系我」config_id参数"},
{41044,"无效的「联系我」config_id参数"},
{41045,"API添加「联系我」达到数量上限"},
{41046,"缺少企业群发消息id"},
{41047,"无效的企业群发消息id"},
{41048,"无可发送的客户"},
{41049,"缺少欢迎语code参数"},
{41050,"无效的欢迎语code"},
{41051,"客户和服务人员已经开始聊天了"},
{41052,"无效的发送时间"},
{41053,"客户未同意聊天存档"},
{41054,"该用户尚未激活"},
{41055,"群欢迎语模板数量达到上限"},
{41056,"外部联系人id类型不正确"},
{41057,"企业或服务商未绑定微信开发者账号"},
{41058,"无此群欢迎语模板的编辑权限"},
{41059,"缺少moment_id参数"},
{41060,"不合法的moment_id参数"},
{41061,"不合法朋友圈发送成员userid，当前朋友圈并非此用户发表"},
{41062,"企业创建的朋友圈尚未被成员userid发表"},
{41063,"群发消息正在被派发中，请稍后再试"},
{41064,"附件数量超过限制"},
{41065,"无效的附件类型"},
{41066,"用户视频号名称错误"},
{41067,"朋友圈moment_id类型错误"},
{41068,"聊天敏感词列表超过了限制"},
{41069,"聊天敏感词规则总数超过了限制"},
{41070,"无效的聊天敏感词规则id"},
{41071,"聊天敏感词规则已经被删除"},
{41072,"资源附件场景使用错误"},
{41073,"商品图册描述不符合标准"},
{41074,"商品图册数据已经被删除"},
{41075,"无效的商品图册id或者数据不存在"},
{41076,"聊天敏感词规则适用范围超过限制"},
{41077,"聊天敏感词规则名称不唯一"},
{41078,"创建朋友圈正在进行的异步任务总数超过了限制"},
{41079,"朋友圈正在被派发中，请稍后再试"},
{41080,"附件资源大小超过限制"},
{41081,"附件资源的图片分辨率超过限制"},
{41082,"附件资源的视频时长超过限制"},
{41083,"敏感词关键字错误"},
{41084,"敏感词拦截语义规则错误"},
{41085,"无效商品编码"},
{41086,"无效商品价格"},
{41087,"无效商品描述"},
{41088,"附件列表为empty"},
{41089,"朋友圈附件与内容不能同时为empty"},
{41090,"视频格式不合法"},
{41091,"配置客户群进群方式最多只能关联5个群"},
{41093,"群发任务已取消"},
{41094,"群发提醒超过频率限制"},
{41095,"获客链接数量超过上限"},
{41096,"欢迎语已在发送中"},
{41098,"组件关联应用创建的获客链接授权给了其他组件"},
{41099,"不是服务商代支付模式"},
{41102,"缺少菜单名"},
{41200,"仅部分经营类目企业支持"},
{41201,"无效的获客助手会话信息chatkey"},
{41202,"商品图册数量超过上限"},
{42001,"access_token已过期"},
{42003,"code已过期"},
{42006,"使用会话展示组件或通讯展示组件时登录态失效"},
{42007,"pre_auth_code已过期"},
{42009,"suite_access_token已过期"},
{42012,"jsapi_ticket不可用，一般是没有正确调用接口来创建jsapi_ticket"},
{42013,"小程序未登陆或登录态已经过期"},
{42014,"任务卡片消息的task_id不合法"},
{42015,"更新的消息的应用与发送消息的应用不匹配"},
{42016,"更新的task_id不存在"},
{42017,"按钮key值不存在"},
{42018,"按钮key值不合法"},
{42019,"缺少按钮key值不合法"},
{42020,"缺少按钮名称"},
{42021,"device_access_token 过期"},
{42022,"code已经被使用过。只能使用一次"},
{42027,"Template_Card.horizontal_content_list.keyname 字段缺失"},
{42028,"Template_Card 缺失 Url，card_action、horizontal_content_list、jump_list缺失该字段都可能报此错误"},
{42029,"Template_Card 缺失 mediaid，Template_Card.horizontal_content_list.mediaid 字段缺失"},
{42030,"Template_Card 缺失 appid，card_action、jump_list 缺失该字段都可能报此错误"},
{42031,"Template_Card.CardType 字段不合法"},
{42033,"Template_Card.缺失 Title，vertical_content_list、jump_list缺失该字段都可能报此错误"},
{42035,"Template_Card.horizontal_content_list 数组长度不合法"},
{42036,"Template_Card.vertical_content_list 数组长度不合法"},
{42037,"Template_Card.option_list 数组长度不合法"},
{42038,"Template_Card.button_list.text 缺失或不合法"},
{42039,"Template_Card.button_list.key 缺失或不合法"},
{42040,"Template_Card.option_list.id 缺失或不合法"},
{42041,"Template_Card.option_list.text 缺失或不合法"},
{42042,"Template_Card.jump_list 数组长度不合法"},
{42043,"Template_Card.question_Key 缺失或不合法"},
{42044,"Template_Card.card_image.url 缺失或不合法"},
{42045,"Template_Card.card_action 缺失或不合法"},
{42046,"Template_Card.submit_button.key 缺失或不合法"},
{42047,"Template_Card.select_list 数组长度不合法"},
{42049,"Template_Card.submit_button.text 缺失或不合法"},
{42050,"Template_Card.horizontal_content_list.userid 缺失或不合法"},
{42051,"Template_Card.action_menu.action_list.key key冲突"},
{42052,"msgid已过期"},
{43004,"指定的userid未绑定微信或未关注微信插件（原企业号）"},
{43009,"企业未验证主体"},
{43012,"应用需配置回调url"},
{43016,"未获取到用户登录信息"},
{43017,"用户OpenData请求校验失败"},
{44001,"多媒体文件为空"},
{44004,"文本消息content参数为空"},
{45001,"多媒体文件大小超过限制"},
{45002,"消息内容大小超过限制"},
{45004,"应用description参数长度不符合系统限制"},
{45007,"语音播放时间超过限制"},
{45008,"图文消息的文章数量不符合系统限制"},
{45009,"接口调用超过限制"},
{45022,"应用name参数长度不符合系统限制"},
{45024,"账号数量超过上限"},
{45026,"触发删除用户数的保护"},
{45029,"回包大小超过上限"},
{45031,"企业corpsecret不够安全，请重置"},
{45032,"图文消息author参数长度超过限制"},
{45033,"接口并发调用超过限制"},
{45034,"url必须有协议头"},
{45035,"并发操作冲突"},
{45036,"数据访问超过限制"},
{46003,"菜单未设置"},
{46004,"指定的用户不存在"},
{48001,"API功能未授权"},
{48002,"API接口无权限调用"},
{48003,"不合法的suite_id"},
{48004,"授权关系无效"},
{48005,"API接口已废弃"},
{48006,"接口权限被收回"},
{48007,"API接口无权限调用，由于对应的客服账号未授权"},
{48008,"API接口无权限调用，通讯录编辑授权只能调用部分通讯录接口"},
{48009,"API接口无权限调用，为保障企业数据安全，不再允许通讯录同步助手从新增IP读取通讯录详情"},
{48010,"使用了不支持的TLS版本"},
{49004,"签名不匹配"},
{49008,"群已经解散"},
{49009,"buyer_userid非法"},
{50001,"redirect_url未登记可信域名"},
{50002,"成员不在权限范围"},
{50003,"应用已禁用"},
{50006,"系统应用已禁用"},
{50100,"分页查询的游标无效"},
{60001,"部门长度不符合限制"},
{60003,"部门ID不存在"},
{60004,"父部门不存在"},
{60005,"部门下存在成员"},
{60006,"部门下存在子部门"},
{60007,"不允许删除根部门"},
{60008,"部门已存在"},
{60009,"部门名称含有非法字符"},
{60010,"部门存在循环关系"},
{60011,"指定的成员/部门/标签参数无权限"},
{60012,"不允许删除默认应用"},
{60020,"不安全的访问IP"},
{60021,"userid不在应用可见范围内"},
{60028,"不允许修改第三方应用的主页 URL"},
{60030,"已超出应用可见范围"},
{60031,"当前应用已禁止调用API"},
{60102,"UserID已存在"},
{60103,"手机号码不合法"},
{60104,"手机号码已存在"},
{60105,"邮箱不合法"},
{60106,"邮箱已存在"},
{60107,"微信号不合法"},
{60110,"用户所属部门数量超过限制"},
{60111,"UserID不存在"},
{60112,"成员name参数不合法"},
{60123,"无效的部门id"},
{60124,"无效的父部门id"},
{60125,"非法部门名字"},
{60127,"缺少department参数"},
{60129,"成员手机和邮箱都为空"},
{60132,"is_leader_in_dept和department的元素个数不一致"},
{60136,"记录不存在"},
{60137,"家长手机号重复"},
{60140,"无效的response_code"},
{60141,"个人邮箱非法，且没有手机号"},
{60142,"个人邮箱更新失败"},
{60143,"企业邮箱非法"},
{60144,"企业邮箱更新失败"},
{60145,"企业邮箱已存在"},
{60146,"未找到手机号"},
{60147,"未找到邮箱"},
{60148,"未找到企业邮箱"},
{60149,"学生手机号已存在"},
{60150,"扩展属性类型不正确"},
{60151,"description字段长度超过限制"},
{60152,"拓展属性description字段非法"},
{60203,"不合法的模版ID"},
{60204,"模版状态不可用"},
{60205,"模版关键词不匹配"},
{60206,"该种类型的消息只支持第三方独立应用使用"},
{60207,"第三方独立应用只允许发送模板消息"},
{60208,"第三方独立应用不支持指定@all，不支持参数toparty和totag"},
{60209,"缺少操作者vid"},
{60210,"选择成员列表为空"},
{60211,"SelectedTicket为空"},
{60214,"仅支持第三方应用调用"},
{60215,"传入SelectedTicket数量超过最大限制（10个）"},
{60217,"当前操作者无权限，操作者需要授权或者在可见范围内"},
{60218,"仅支持成员授权模式的应用可调用"},
{60219,"消费SelectedTicket和创建SelectedTicket的应用appid不匹配"},
{60220,"缺少corpappid"},
{60221,"open_userid对应的服务商不是当前服务商"},
{60222,"非法SelectedTicket"},
{60223,"非法BundleId"},
{60224,"非法PackageName"},
{60225,"当前操作者并非SelectedTicket相关人，不能创建群聊"},
{60226,"选人数量超过最大限制（2000）"},
{60227,"缺少ServiceCorpid"},
{60228,"缺少bind_corpid字段"},
{60229,"成员或者部门id不正确"},
{60230,"缺少shareticket"},
{60231,"shareticket非法"},
{60233,"shareticket非法"},
{60234,"shareticket非法"},
{60235,"缺少payment_id字段"},
{60236,"缺少trade_no字段"},
{60237,"传入的payment_id对应的收款项目不是由当前应用发起的"},
{60238,"传入的partyid有误"},
{60239,"收款人未实名"},
{60240,"收款企业尚未验证或者认证"},
{60241,"付款学生或者部门id不正确"},
{60242,"shareticket不能跨域名使用"},
{60243,"trade_no不合法"},
{60244,"shareticket不能跨APP使用"},
{60246,"第三方应用未上线"},
{60251,"缺少openkfid"},
{60252,"非法的openkfid"},
{60253,"客服不在接待人员列表中"},
{60257,"设置直属上级数量超过最大数量限制（5个）"},
{60267,"时间参数不合法"},
{60268,"cursor参数不合法"},
{60269,"企业邮箱别名非法"},
{60270,"企业邮箱别名已存在"},
{60271,"企业邮箱别名非法"},
{60272,"成员没有专属域名邮箱，不能设置企业邮箱别名"},
{60273,"别名邮箱超过限制，不能设置企业邮箱"},
{65000,"学校已经迁移"},
{65001,"无效的关注模式"},
{65002,"导入家长信息数量过多"},
{65003,"学校尚未迁移"},
{65004,"组织架构不存在"},
{65005,"无效的同步模式"},
{65006,"无效的管理员类型"},
{65007,"无效的家校部门类型"},
{65008,"无效的入学年份"},
{65009,"无效的标准年级类型"},
{65010,"此userid并不是学生"},
{65011,"家长userid数量超过限制"},
{65012,"学生userid数量超过限制"},
{65013,"学生已有家长"},
{65014,"非学校企业"},
{65015,"父部门类型不匹配"},
{65018,"家长人数达到上限"},
{65022,"家校通迅录无权限"},
{65023,"家校通迅录无权限"},
{65024,"家长关联的老师不在应用可见范围"},
{71044,"设备已绑定，删除失败"},
{71045,"operid非法"},
{71046,"无设备数据权限"},
{71047,"openuserid参数非法"},
{71048,"应用无授权设备"},
{71049,"考勤规则达到通过APP设置的上限"},
{71050,"门禁规则名称非法"},
{71051,"门禁规则非法"},
{71052,"获取测温数据错误"},
{71056,"userid不在设备可见范围内"},
{71057,"门禁规则id非法"},
{71058,"userid不在应用可见范围内"},
{71059,"门禁规则的配置范围超过授权应用可见范围，不可删除和修改"},
{71060,"门禁规则的配置范围超过授权应用可见范围，不可删除和修改"},
{71062,"文件名称非法"},
{71063,"同时缺少media_id和download_url参数"},
{71064,"download_url非法或者从download_url下载文件失败"},
{71065,"硬件不支持打印功能禁止调用"},
{71066,"硬件不支持扫描功能禁止调用"},
{71067,"打印文件转码配置版本不合法"},
{71068,"打印文件转码配置版本过期"},
{71069,"打印文件转码页码不合法"},
{71070,"userid 不在设备使用范围内"},
{71071,"时间戳不合法"},
{71072,"设备型号未发布，禁止调用此接口"},
{71075,"扫描上传文件授权码不合法"},
{71076,"禁止修改或者删除默认不可通行规则"},
{71077,"非法的bio_info_type"},
{71078,"硬件型号不支持该bio_info_type"},
{72023,"发票已被其他公众号锁定"},
{72024,"发票状态错误"},
{72037,"存在发票不属于该用户"},
{80001,"可信域名不正确，或者无ICP备案"},
{81001,"部门下的节点数超过限制（3W）"},
{81002,"部门最多15层"},
{81003,"标签下节点个数超过30000个"},
{81011,"无权限操作标签"},
{81012,"缺失可见范围"},
{81013,"UserID、部门ID、标签ID全部非法或无权限"},
{81014,"标签添加成员，单次添加user或party过多"},
{81015,"邮箱域名需要跟企业邮箱域名一致"},
{81016,"logined_userid字段缺失"},
{81017,"请求个数超过限制"},
{81018,"该服务商可获取名字数量配额不足"},
{81019,"items数组成员缺少id字段"},
{81020,"items数组成员缺少type字段"},
{81021,"items数组成员的type字段不合法"},
{81023,"logined_userid 不合法"},
{81024,"logined_userid 不合法"},
{81026,"相关功能处于系统维护状态，暂时无法调用"},
{82001,"指定的成员/部门/标签全部为空"},
{82002,"不合法的PartyID列表长度"},
{82003,"不合法的TagID列表长度"},
{82004,"不合法的消息内容"},
{82101,"指定的更新对象为空"},
{82102,"指定的更新对象不在多人消息内"},
{84005,"第三方应用不存在"},
{84014,"成员票据过期"},
{84015,"成员票据无效"},
{84019,"缺少templateid参数"},
{84020,"templateid不存在"},
{84021,"缺少register_code参数"},
{84022,"无效的register_code参数"},
{84023,"不允许调用设置通讯录同步完成接口"},
{84024,"无注册信息"},
{84025,"不符合的state参数"},
{84052,"缺少caller参数"},
{84053,"缺少callee参数"},
{84054,"缺少auth_corpid参数"},
{84055,"超过拨打公费电话频率"},
{84056,"被拨打用户安装应用时未授权拨打公费电话权限"},
{84057,"公费电话余额不足"},
{84058,"caller 呼叫号码不支持"},
{84059,"号码非法"},
{84060,"callee 呼叫号码不支持"},
{84061,"不存在外部联系人的关系"},
{84062,"未开启公费电话应用"},
{84063,"caller不存在"},
{84064,"callee不存在"},
{84065,"caller跟callee电话号码一致"},
{84066,"服务商拨打次数超过限制"},
{84067,"管理员收到的服务商公费电话个数超过限制"},
{84069,"拨打方被限制拨打公费电话"},
{84070,"不支持的电话号码"},
{84071,"不合法的外部联系人授权码"},
{84072,"应用未配置客服"},
{84073,"客服userid不在应用配置的客服列表中"},
{84074,"没有外部联系人权限"},
{84075,"不合法或过期的authcode"},
{84076,"缺失authcode"},
{84077,"订单价格过高，无法受理"},
{84078,"购买人数不正确"},
{84079,"价格策略不存在"},
{84080,"订单不存在"},
{84081,"存在未支付订单"},
{84082,"存在申请退款中的订单"},
{84083,"非服务人员"},
{84084,"非跟进用户"},
{84085,"应用已下架"},
{84086,"订单人数超过可购买最大人数"},
{84087,"打开订单支付前禁止关闭订单"},
{84088,"禁止关闭已支付的订单"},
{84089,"订单已支付"},
{84090,"缺失user_ticket"},
{84091,"订单价格不可低于下限"},
{84092,"无法发起代下单操作"},
{84093,"代理关系已占用，无法代下单"},
{84094,"该应用未配置代理分润规则，请先联系应用服务商处理"},
{84095,"免费试用版，无法扩容"},
{84096,"免费试用版，无法续期"},
{84097,"当前企业有等待生效的订单，无法下新单"},
{84098,"固定总量，无法扩容"},
{84099,"非购买状态，无法扩容"},
{84100,"未购买过此应用，无法续期"},
{84101,"企业已试用付费版本，无法全新购买"},
{84102,"企业当前应用状态已过期，无法扩容"},
{84103,"仅可修改未支付订单"},
{84104,"订单已支付，无法修改"},
{84105,"订单已被取消，无法修改"},
{84106,"企业含有该应用的待支付订单，无法代下单"},
{84107,"企业含有该应用的退款中订单，无法代下单"},
{84108,"企业含有该应用的待生效订单，无法代下单"},
{84109,"订单定价不能未0"},
{84110,"新安装应用不在试用状态，无法升级为付费版"},
{84111,"无足够可用优惠券"},
{84112,"无法关闭未支付订单"},
{84113,"无付费信息"},
{84114,"虚拟版本不支持下单"},
{84115,"虚拟版本不支持扩容"},
{84116,"虚拟版本不支持续期"},
{84117,"在虚拟正式版期内不能扩容"},
{84118,"虚拟正式版期内不能变更版本"},
{84119,"当前企业未报备，无法进行代下单"},
{84120,"当前应用版本已删除"},
{84121,"应用版本已删除，无法扩容"},
{84122,"应用版本已删除，无法续期"},
{84123,"非虚拟版本，无法升级"},
{84124,"非行业方案订单，不能添加部分应用版本的订单"},
{84125,"购买人数不能少于最少购买人数"},
{84126,"购买人数不能多于最大购买人数"},
{84127,"无应用管理权限"},
{84128,"无该行业方案下全部应用的管理权限"},
{84129,"付费策略已被删除，无法下单"},
{84130,"订单生效时间不合法"},
{84131,"超过五年不能续期"},
{84132,"行业方案扩容，至少需要有一个应用扩容"},
{84133,"优惠后金额不能低于历史最低价"},
{84134,"企业当前应用状态已过期，无法变更版本"},
{84135,"应用被禁用安装"},
{84136,"此应用通过应用代理商推广安装，请联系应用代理商进行下单操作"},
{84137,"该客户为应用代理商报备的客户，请联系应用代理商进行下单操作"},
{84138,"该客户非您的应用报备客户，您无法代下单"},
{84139,"License灰度企业不能代下单"},
{84140,"仅支持上下游群会话工具栏的入口"},
{84141,"仅支持上下游群单聊工具栏的入口"},
{84142,"剩余时间都是赠送时间，不能扩容"},
{84143,"没权限确认合同, 确认者需为超管，或服务商应用（第三方应用、代开发应用）的分管"},
{84144,"确认者的gid不在当前服务商的通讯录中"},
{84145,"确认者gid所在的企业数不能超过5个"},
{84146,"确认者gid最多只能在3个企业下确认合同"},
{84147,"预下单已经过期"},
{84148,"预下单状态已经变更,无法下单"},
{84149,"预下单已经取消"},
{84150,"预下单与指定的企业不符"},
{84151,"预下单指定的应用未安装"},
{84152,"预下单已经被其他企业下单"},
{84153,"已经购买过会议"},
{84154,"不是k12企业,无法购买"},
{84155,"服务商代下单不能超过90天"},
{84156,"预下单状态已发生变更,请刷新"},
{84157,"没有验证/认证"},
{84158,"服务商未开通商户号, 无法代下单"},
{84159,"该客户没有历史授权, 无法代下单"},
{84160,"不能指定今天及以前的时间"},
{84161,"不是测试企业，不能添加测试订单"},
{84162,"服务商未开通商户号, 无法代下单"},
{84163,"没有购买过应用，无法续期或扩容"},
{84172,"不是付费版本"},
{84175,"指定的生效日期不合法"},
{84178,"该应用存在未支付订单"},
{84180,"客户企业没有授权安装自定义应用"},
{84182,"已购买的版本才允许续期或扩容"},
{84186,"当前版本的剩余时长超过一年，不支持新购"},
{84188,"如果付费版本未发布，就不能创建预下单"},
{84189,"(正在生效)的应用设置新购的时候不支持设置开始时间"},
{84195,"提交的续期任务中有重复的userid"},
{84200,"文件转译解析错误"},
{85002,"包含不合法的词语"},
{85004,"每企业每个月设置的可信域名不可超过20个"},
{85005,"可信域名未通过所有权校验"},
{85006,"IP列表超过限制"},
{85007,"不合法的IP地址"},
{85008,"可信域名ICP备案不通过"},
{85009,"应用信息不完整"},
{85010,"回调参数填写不符合要求"},
{85011,"校验回调地址失败"},
{85012,"存在不合法的权限名称"},
{85111,"可信域名是第三方服务商域名"},
{86001,"参数 chatid 不合法"},
{86003,"参数 chatid 不存在"},
{86004,"参数 群名不合法"},
{86005,"参数 群主不合法"},
{86006,"群成员数过多或过少"},
{86007,"不合法的群成员"},
{86008,"非法操作非自己创建的群"},
{86101,"仅群主才有操作权限"},
{86201,"参数 需要chatid"},
{86202,"参数 需要群名"},
{86203,"参数 需要群主"},
{86204,"参数 需要群成员"},
{86205,"参数 字符串chatid过长"},
{86206,"参数 数字chatid过大"},
{86207,"群主不在群成员列表"},
{86214,"群发类型不合法"},
{86215,"会话ID已经存在"},
{86216,"存在非法会话成员ID"},
{86217,"会话发送者不在会话成员列表中"},
{86220,"指定的会话参数不合法"},
{86222,"应用多人消息成员必须要包含至少一个下游企业员工"},
{86224,"不是受限群，不允许使用该接口"},
{86225,"不允许删除课程群群主"},
{86226,"指定的课程群群主没有课程群创建权限"},
{86227,"调用课程群API接口的企业不是高等教育行业"},
{90001,"未认证摇一摇周边"},
{90002,"缺少摇一摇周边ticket参数"},
{90003,"摇一摇周边ticket参数不合法"},
{90100,"非法的对外属性类型"},
{90101,"对外属性：文本类型长度不合法"},
{90102,"对外属性：网页类型标题长度不合法"},
{90103,"对外属性：网页url不合法"},
{90104,"对外属性：小程序类型标题长度不合法"},
{90105,"对外属性：小程序类型pagepath不合法"},
{90106,"对外属性：请求参数不合法"},
{90142,"无效的场所码id"},
{90200,"缺少小程序appid参数"},
{90201,"小程序通知的content_item个数超过限制"},
{90202,"小程序通知中的key长度不合法"},
{90203,"小程序通知中的value长度不合法"},
{90204,"小程序通知中的page参数不合法"},
{90206,"小程序未关联到企业中"},
{90207,"不合法的小程序appid"},
{90208,"小程序appid不匹配"},
{90210,"需要用户进行成员授权"},
{90211,"登录时传入的suiteid不合法"},
{90212,"登录时传入的suiteid和使用的小程序绑定的第三方应用不匹配"},
{90300,"orderid 不合法"},
{90302,"付费应用已过期"},
{90303,"付费应用超过最大使用人数"},
{90304,"订单中心服务异常，请稍后重试"},
{90305,"参数错误，errmsg中有提示具体哪个参数有问题"},
{90306,"商户设置不合法，详情请见errmsg"},
{90307,"登录态过期"},
{90308,"在开启IP鉴权的前提下，识别为无效的请求IP"},
{90309,"订单已经存在，请勿重复下单"},
{90310,"找不到订单"},
{90311,"关单失败, 可能原因：该单并没被拉起支付页面; 已经关单；已经支付；渠道失败；单处于保护状态；等等"},
{90312,"退款请求失败, 详情请看errmsg"},
{90313,"退款调用频率限制，超过规定的阈值"},
{90314,"订单状态错误，可能未支付，或者当前状态操作受限"},
{90315,"退款请求失败，主键冲突，请核实退款refund_id是否已使用"},
{90316,"退款原因编号不对"},
{90317,"尚未注册成为供应商"},
{90318,"参数nonce_str 为空或者重复，判定为重放攻击"},
{90319,"时间戳为空或者与系统时间间隔太大"},
{90320,"订单token无效"},
{90321,"订单token已过有效时间"},
{90322,"旧套件（包含多个应用的套件）不支持支付系统"},
{90323,"单价超过限额"},
{90324,"商品数量超过限额"},
{90325,"预支单已经存在"},
{90326,"预支单单号非法"},
{90327,"该预支单已经结算下单"},
{90328,"结算下单失败，详情请看errmsg"},
{90329,"该订单号已经被预支单占用"},
{90330,"创建供应商失败"},
{90331,"更新供应商失败"},
{90332,"还没签署合同"},
{90333,"创建合同失败"},
{90338,"已经过了可退款期限"},
{90339,"供应商主体名包含非法字符"},
{90340,"创建客户失败，可能信息真实性校验失败"},
{90341,"退款金额大于付款金额"},
{90342,"退款金额超过账户余额"},
{90343,"退款单号已经存在"},
{90344,"指定的付款渠道无效"},
{90345,"超过5w人民币不可指定微信支付渠道"},
{90346,"同一单的退款次数超过限制"},
{90347,"退款金额不可为0"},
{90348,"管理端没配置支付密钥"},
{90349,"记录数量太大"},
{90350,"银行信息真实性校验失败"},
{90351,"应用状态异常"},
{90352,"延迟试用期天数超过限制"},
{90353,"预支单列表不可为空"},
{90354,"预支单列表数量超过限制"},
{90355,"关联有退款预支单，不可删除"},
{90356,"不能0金额下单"},
{90357,"代下单必须指定支付渠道"},
{90358,"预支单或代下单，不支持部分退款"},
{90359,"预支单与下单者企业不匹配"},
{90381,"参数 refunded_credit_orderid 不合法"},
{90405,"图片类型不合法"},
{90406,"图片大小不合法"},
{90407,"企业属于K12企业"},
{90408,"图片不合法"},
{90409,"图片上传错误"},
{90410,"已存在进行中的申请单编号"},
{90412,"达到超级管理员申请单数量上限"},
{90413,"达到企业申请单数量上限"},
{90414,"参数错误"},
{90415,"不存在的申请单编号"},
{90416,"超级管理员没有权限"},
{90418,"指定的提现人员未实名认证"},
{90419,"上传图片出现错误"},
{90420,"指定的提现人员的实名认证信息与超级管理员不符"},
{90421,"所选择的经营范围与主体类型不符"},
{90431,"一个应用一个企业一天内最多只能创建3个免支付订单"},
{90432,"充值账户未开通"},
{90433,"账户余额不足"},
{90456,"必须指定组织者"},
{90457,"日历ID异常"},
{90458,"日历ID列表不能为空"},
{90459,"日历已删除"},
{90460,"日程已删除"},
{90461,"日程ID异常"},
{90462,"日程ID列表不能为空"},
{90463,"不能变更组织者"},
{90464,"参与者数量超过限制"},
{90465,"不支持的重复类型"},
{90466,"不能操作别的应用创建的日历/日程"},
{90467,"星期参数异常"},
{90468,"不能变更组织者"},
{90469,"每页大小超过限制"},
{90470,"页数异常"},
{90471,"提醒时间异常"},
{90472,"没有日历/日程操作权限"},
{90473,"颜色参数异常"},
{90474,"组织者不能与参与者重叠"},
{90475,"不是组织者的日历"},
{90477,"仅系统应用“日程”允许访问用户创建的日程"},
{90479,"不允许操作用户创建的日程"},
{90480,"非法的timezone"},
{90481,"组织者的日历数量超过限制"},
{90482,"非法的op_start_time"},
{90483,"公共日历或全员日历需要提供public_range"},
{90484,"“仅修改此日程”模式，仅可用于重复日程"},
{90485,"非法的op_mode"},
{90486,"缺失repeate_interval参数"},
{90487,"非法的day_of_month"},
{90488,"start_time需要在op_start_time之后"},
{90489,"仅公共日历支持设置自动订阅"},
{90490,"订阅人数超过上限"},
{90491,"userid不在可订阅范围内"},
{90492,"管理员不在成员列表中"},
{90493,"使用了废弃的参数"},
{90494,"不是创建者所属日历"},
{90495,"此应用类型不支持使用管理员字段"},
{90496,"不允许在只读日历中指定管理员"},
{90497,"不允许更新邮件创建的日程"},
{90498,"日程已预定其他会议室"},
{90499,"不可取消其由他应用预定的会议室"},
{90500,"群主并未离职"},
{90501,"该群不是客户群"},
{90502,"群主已经离职"},
{90503,"满人 & 99个微信成员，没办法踢，要客户端确认"},
{90504,"群主没变"},
{90507,"离职群正在继承处理中"},
{90508,"离职群已经继承"},
{90509,"非企业微信客户群"},
{90510,"企业一年内无活跃用户"},
{90511,"opengid不存在或者无效"},
{90602,"会议Id不合法"},
{90603,"事件分类id不合法"},
{90604,"网格单元id不合法"},
{90606,"该网格单元管理员达到上限，一个网格单元最多有20个管理员"},
{90607,"含有成员的网格单元不能被删除"},
{90608,"网格单元的名字重复了"},
{90609,"网格单元的成员数超过上限"},
{90610,"网格单元的成员数超过上限"},
{90613,"成员管理的网格超过上限"},
{90700,"更新日程前需要取消会议室预定"},
{90704,"remind_time_diffs数组包含不合法的值"},
{90705,"日程的创建者已经离职，不可删除"},
{90706,"无法将非周期日程的起始时间修改成早于当前时间"},
{90707,"日程关联的会议正在进行中，无法修改"},
{90708,"日程创建者没有默认日历，需要传日历参数"},
{90710,"日程关联的会议已经结束，无法修改"},
{90711,"会议室已被停用，无法预定"},
{90712,"无法删除默认日历本"},
{91040,"获取ticket的类型无效"},
{92000,"成员不在应用可见范围之内"},
{92001,"应用没有敏感信息权限"},
{92002,"不允许跨企业调用"},
{92003,"不允许跨应用调用"},
{92006,"该直播已经开始或取消"},
{92007,"该直播回放不能被删除"},
{92008,"当前应用没权限操作这个直播"},
{93000,"机器人webhookurl不合法或者机器人已经被移除出群"},
{93004,"机器人被停用"},
{93006,"不合法的群ID"},
{93008,"不在群里"},
{93017,"发消息的请求内容不能为空"},
{93018,"图片大小超过限制"},
{94000,"应用未开启工作台自定义模式"},
{94001,"不合法的type类型"},
{94002,"缺少keydata字段"},
{94003,"keydata的items列表长度超出限制"},
{94005,"缺少list字段"},
{94006,"list的items列表长度超出限制"},
{94007,"缺少webview字段"},
{94008,"应用未设置自定义工作台模版类型"},
{94010,"缺少title字段"},
{94011,"webview高度类型不正确"},
{94012,"缺少data字段"},
{94013,"image配置的url字段不合法"},
{94014,"list配置的title字段不合法"},
{94015,"webview配置的url字段不合法"},
{94016,"用户配置的height参数跟应用模板不一致"},
{95000,"不合法的open_kfid"},
{95001,"发送客服消息次数限制"},
{95002,"发送客服消息时间限制"},
{95003,"发送客服消息可接待客户咨询数限制"},
{95004,"open_kfid不存在"},
{95005,"客服账号数超过限制"},
{95006,"不合法的客服账号名"},
{95007,"不合法的msgtoken"},
{95008,"菜单消息的菜单项个数超过上限"},
{95009,"不合法的菜单消息的菜单项类型"},
{95011,"已在企业微信使用微信客服"},
{95012,"未在企业微信使用微信客服"},
{95013,"会话已经结束"},
{95014,"用户不是接待人员"},
{95015,"管理端已经配置了专属服务"},
{95016,"不允许这种状态转移"},
{95017,"基础应用权限下，api开关处于关闭状态"},
{95018,"发送客服消息时当前会话状态不允许发送"},
{95019,"接待人员已停止接待或暂时挂起，无法完成指定的操作"},
{95020,"接待人员配置超过上限"},
{95022,"location_type非法"},
{95023,"基础应用权限下，api开关已经授权给代开发自建应用"},
{95026,"客服账号使用率较低，暂无法创建"},
{95027,"企业未认证，仅可创建10个客服账号"},
{95030,"客服组件设置的禁发消息类型"},
{95031,"客户48小时内未发起过咨询"},
{95032,"客服组件获取客服聊天记录权限已失效"},
{95033,"msgid重复"},
{301002,"无权限操作指定的应用"},
{301005,"不允许删除创建者"},
{301007,"企业不可用，可能已经被解散或者被禁封"},
{301012,"参数 position 不合法"},
{301013,"参数 telephone 不合法"},
{301014,"参数 english_name 不合法"},
{301015,"参数 mediaid 不合法"},
{301016,"上传语音文件不符合系统要求"},
{301017,"上传语音文件仅支持AMR格式"},
{301019,"文件的md5不合法"},
{301021,"参数 userid 无效"},
{301022,"获取打卡数据失败"},
{301023,"useridlist非法或超过限额"},
{301024,"获取打卡记录时间间隔超限"},
{301025,"审批开放接口参数错误"},
{301026,"获取审批模板数据失败"},
{301036,"不允许更新该用户的userid"},
{301039,"请求参数错误，请检查输入参数"},
{301042,"ip白名单限制，请求ip不在设置白名单范围"},
{301048,"sdkfileid对应的文件不存在或已过期"},
{301049,"调用接口的应用未在紧急通知应用中关联"},
{301050,"紧急通知应用未开启"},
{301051,"紧急通知应用余额不足"},
{301052,"会话存档服务已过期"},
{301053,"会话存档服务未开启"},
{301055,"无审批应用权限/无审批应用数据拉取权限"},
{301056,"审批应用已停用"},
{301057,"通用错误码，提交审批单内部接口失败"},
{301058,"拉取会话数据请求超过大小限制，可减少limit参数"},
{301059,"非内部群，不提供数据"},
{301060,"拉取同意情况请求量过大，请减少到100个参数以下"},
{301061,"userid或者exteropenid用户不存在"},
{301062,"没有假勤权限"},
{301063,"参数错误"},
{301064,"内部错误"},
{301073,"设置排班的时间参数不合法"},
{301079,"审批单假勤时间有冲突"},
{301080,"应打卡时间非法"},
{301081,"打卡时间非法"},
{301082,"应打卡日期非法"},
{301083,"打卡接口调用参数非法"},
{301084,"journal_uuid非法"},
{301085,"docid非法"},
{301086,"参数错误"},
{301087,"审批模板数超过上限"},
{301088,"无操作权限"},
{301089,"groupid非法"},
{301090,"打卡规则包含无权限id"},
{301091,"打卡规则range冲突"},
{301092,"应用无法修改此打卡规则"},
{301093,"打卡规则字段非法"},
{301094,"打卡规则字段非法"},
{301095,"企业打卡规则总数超限"},
{301096,"创建打卡规则并发超限"},
{301097,"打卡规则字段非法"},
{301099,"所属的打卡规则，不可提交【迟到】补卡"},
{301101,"所属的打卡规则，不可提交【早退】补卡"},
{301102,"所属的打卡规则，不可提交【缺卡】补卡"},
{301103,"所属的打卡规则，不可提交【其他异常】补卡"},
{301104,"所属的打卡规则，不可提交【迟到】、【其他异常】补卡"},
{301105,"所属的打卡规则，不可提交【早退】、【其他异常】补卡"},
{301111,"会话存档SDK版本过低，请更新到最新版本的SDK"},
{301112,"请缩小查询时间范围重试"},
{301113,"审批中的审批打卡不能补卡，补卡后原审批打卡的信息会清除"},
{301115,"模板已配置自定义打印格式，不支持API修改模板"},
{301116,"第三方不可调用该企业会话内容存档SDK"},
{302003,"批量导入任务的文件中userid有重复"},
{302004,"组织架构不合法（1不是一棵树，2 多个一样的partyid，3 partyid空，4 partyid name 空，5 同一个父节点下有两个子节点 部门名字一样 可能是以上情况，请一一排查）"},
{302005,"批量导入系统失败，请重新尝试导入"},
{302006,"批量导入任务的文件中partyid有重复"},
{302007,"批量导入任务的文件中，同一个部门下有两个子部门名字一样"},
{302008,"已经有一个导入任务在进行中，需要等待上一个任务完成后再能调用"},
{400010,"block_hint非法"},
{400011,"block_hint对应的拦截已过期"},
{400012,"不允许通过该block_hint解除拦截"},
{400013,"解拦截次数超过限制"},
{400020,"会议参与人超过限制"},
{400021,"会议参与人或者主持人不合法，或者不在应用可见范围。若为外部联系人，则外部联系人跟进人不在应用可见范围"},
{400025,"remind_scope参数不合法。"},
{400026,"repeat_type参数不合法。"},
{400027,"主持人超过会议主持人上限。"},
{400028,"会议系统应用关闭，不允许调用会议相关接口"},
{400029,"主持人必须在参与人列表中"},
{400030,"不合法的会议密码"},
{400031,"不合法的会议settings"},
{400032,"不合法的repeat_until参数"},
{400033,"不合法的repeat_interval参数"},
{400034,"不合法的meeting_start参数"},
{400035,"不合法的meeting_duration参数"},
{400036,"会议的创建者不具备指定的日历权限"},
{400037,"指定的响铃用户不在会议参与人列表中"},
{400038,"缺少userid参数"},
{400039,"会议管理员不在参与者列表。"},
{400040,"缺少admin_userid参数"},
{400041,"会议已取消"},
{400042,"会议title不合法"},
{400043,"已废弃该参数，请使用admin_userid"},
{400044,"系统应用仅支持creator_userid参数"},
{400045,"不允许更新或者取消快速会议"},
{400218,"有参会成员未购买专业版账号"},
{400219,"会议发起人未购买专业版账号"},
{400220,"创建会议行为异常，被风控策略拦截"},
{400221,"正在进行中或者已经结束的会议不允许更新"},
{400222,"会议开始时间不允许小于当前时间"},
{400223,"开始时间或者结束时间不合法"},
{400224,"会中或者已经结束的会议不能取消"},
{400226,"企业购买「会议高级功能」后才可以使用该字段"},
{400227,"非法的重复类型"},
{400228,"重复次数不合法"},
{400237,"location的长度超过最大限制"},
{400301,"5分钟内有相同的会议正在创建中，暂不可再创建"},
{400302,"相同的会议已经创建成功"},
{400303,"用户没有权限创建会议"},
{400304,"用户没有操作权限"},
{400307,"不允许获取其他企业成员创建的会议详情"},
{400308,"用户非会议参与人或者创建者，不允许获取会议详情"},
{511020,"问题重复"},
{600001,"不合法的sn"},
{600002,"设备已注册"},
{600003,"不合法的硬件activecode"},
{600004,"该硬件尚未授权任何企业"},
{600005,"硬件Secret无效"},
{600006,"硬件deviceid无效"},
{600007,"缺少硬件sn"},
{600008,"缺少nonce参数"},
{600009,"缺少timestamp参数"},
{600010,"缺少signature参数"},
{600011,"签名校验失败"},
{600012,"长连接已经注册过设备"},
{600013,"缺少activecode参数"},
{600014,"设备未网络注册"},
{600015,"缺少secret参数"},
{600016,"设备未激活"},
{600017,"无效打卡时间"},
{600018,"无效的起始结束时间"},
{600019,"无效结束时间"},
{600020,"设备未登录"},
{600021,"设备sn已存在"},
{600023,"时间戳已失效"},
{600024,"固件大小超过5M"},
{600025,"固件名为空或者超过20字节"},
{600026,"固件信息不存在"},
{600027,"非法的固件参数"},
{600028,"固件版本已存在"},
{600029,"非法的固件版本"},
{600030,"缺少固件版本参数"},
{600031,"硬件固件不允许升级"},
{600032,"无法解析硬件二维码"},
{600033,"设备型号id冲突"},
{600034,"指纹数据大小超过限制"},
{600035,"人脸数据大小超过限制"},
{600036,"设备sn冲突"},
{600037,"缺失设备型号id"},
{600038,"设备型号不存在"},
{600039,"不支持的设备类型"},
{600040,"打印任务id不存在"},
{600041,"无效的offset或limit参数值"},
{600042,"无效的设备型号id"},
{600043,"门禁规则未设置"},
{600044,"门禁规则不合法"},
{600045,"设备已订阅企业信息"},
{600046,"操作id和用户userid不匹配"},
{600047,"secretno的status非法"},
{600048,"无效的指纹算法"},
{600049,"无效的人脸识别算法"},
{600050,"无效的算法长度"},
{600051,"设备过期"},
{600052,"无效的文件分块"},
{600053,"该链接已经激活"},
{600054,"该链接已经订阅"},
{600055,"无效的用户类型"},
{600056,"无效的健康状态"},
{600057,"缺少体温参数"},
{600063,"硬件不支持扫描功能，禁止调用"},
{610001,"永久二维码超过每个员工5000的限制"},
{610003,"scene参数不合法"},
{610004,"输入参数不是企业客户"},
{610014,"无效的unionid"},
{610015,"小程序对应的开放平台账号未认证"},
{610016,"企业未认证"},
{610017,"小程序和企业主体不一致"},
{610018,"openid和unionid不在同一个开放平台账号下"},
{610019,"没有安装第三方应用"},
{610020,"没有安装与待开发应用是同一个服务商的第三方应用"},
{610021,"已经安装的与待开发应用为同一个服务商的第三方应用暂无企业客户权限"},
{610022,"授权企业与服务商为同一企业，无需设置迁移"},
{610023,"企业外部联系人规模已达上限，当前已无法管理客户，在客户端上购买后即可继续管理。"},
{630005,"无效的launch_code"},
{630010,"不合法的sdk_func"},
{630011,"不合法的singlechat参数"},
{630012,"操作者身份不一致"},
{630013,"launch_code中应用身份跟调用sdk接口应用身份不一致"},
{630014,"launch_code中申请的sdk接口跟调用sdk接口不一致"},
{630015,"单聊对象已离职"},
{640001,"微盘不存在当前空间"},
{640002,"文件不存在"},
{640003,"文件已删除"},
{640004,"无权限访问"},
{640005,"成员不在空间内"},
{640006,"超出当前成员拥有的容量"},
{640007,"超出微盘的容量"},
{640008,"没有空间权限"},
{640009,"非法文件名"},
{640010,"超出空间的最大成员数"},
{640011,"json格式不匹配"},
{640012,"非法的userid"},
{640013,"非法的departmentid"},
{640014,"空间没有有效的管理员"},
{640015,"不支持设置预览权限"},
{640016,"不支持设置文件水印"},
{640017,"微盘管理端未开通API 权限"},
{640018,"微盘管理端未设置编辑权限"},
{640019,"API 调用次数超出限制"},
{640020,"非法的权限类型"},
{640021,"非法的fatherid"},
{640022,"非法的文件内容的base64"},
{640023,"非法的权限范围"},
{640024,"非法的fileid"},
{640025,"非法的space_name"},
{640026,"非法的spaceid"},
{640027,"参数错误"},
{640028,"空间设置了关闭成员邀请链接"},
{640029,"只支持下载普通文件，不支持下载文件夹等其他非文件实体类型"},
{640032,"企业管理员已禁止此成员创建空间"},
{640035,"免费账号调用微盘api累计上限为1000次/月，每个企业内所有免费账号共用此次数限制"},
{640039,"分块上传的size和block_sha不匹配"},
{640044,"空间已开启保密模式"},
{640045,"没有权限设置“可上传下载”权限"},
{640046,"没有权限设置“自定义”权限"},
{640047,"上传文件大小超过限制"},
{640048,"上传流程异常"},
{640051,"参数错误"},
{640052,"文档名不合法"},
{640053,"管理员数量达到上限"},
{640054,"文档类型不合法"},
{640055,"文档成员达到上限"},
{640056,"欲删除的成员当前不在文档成员列表中"},
{640060,"空间文件数已达上限"},
{640061,"智能表api内容权限规则名称重复"},
{640062,"智能表api内容权限规则名称长度超限"},
{640063,"智能表api指定成员权限规则数量超限返回"},
{640065,"智能表api额外指定成员权限不存在"},
{640067,"智能表api删除的成员不存在"},
{640069,"智能表api规则成员数目超限"},
{640070,"智能表api设置区域权限-全局行规则不能设置局部行规则"},
{640071,"智能表api设置区域权限-行规则数目非法"},
{640072,"智能表api设置区域权限-行规则缺少fieldid"},
{640073,"智能表api设置区域权限-行规则操作类型无需有值"},
{640074,"智能表api设置区域权限-行规则操作类型必须有值"},
{640075,"智能表api设置区域权限-行规则操作类型未定义"}
            };
        });
        public static string GetErrMsg(int errCode)
        {
            return ErrDic.Value.TryGetValue(errCode, out var errMsg) ? errMsg : "未知错误";
        }
    }

    class WXBizMsgCrypt
    {
        enum QYWechatWXBizMsgCryptErrorCode
        {
            //-40001 ： 签名验证错误
            //-40002 :  xml解析失败
            //-40003 :  sha加密生成签名失败
            //-40004 :  AESKey 非法
            //-40005 :  corpid 校验错误
            //-40006 :  AES 加密失败
            //-40007 ： AES 解密失败
            //-40008 ： 解密后得到的buffer非法
            //-40009 :  base64加密异常
            //-40010 :  base64解密异常
            WXBizMsgCrypt_OK = 0,
            WXBizMsgCrypt_ValidateSignature_Error = -40001,
            WXBizMsgCrypt_ParseXml_Error = -40002,
            WXBizMsgCrypt_ComputeSignature_Error = -40003,
            WXBizMsgCrypt_IllegalAesKey = -40004,
            WXBizMsgCrypt_ValidateCorpid_Error = -40005,
            WXBizMsgCrypt_EncryptAES_Error = -40006,
            WXBizMsgCrypt_DecryptAES_Error = -40007,
            WXBizMsgCrypt_IllegalBuffer = -40008,
            WXBizMsgCrypt_EncodeBase64_Error = -40009,
            WXBizMsgCrypt_DecodeBase64_Error = -40010
        };
        static readonly Dictionary<int, string> _ErrorMsgDic = new()
        {
            {0 ,string.Empty},//"成功"
            {-40001 ,"签名验证错误"},
            {-40002 ,"xml解析失败"},
            {-40003 ,"sha加密生成签名失败"},
            {-40004 ,"AESKey 非法"},
            {-40005 ,"corpid 校验错误"},
            {-40006 ,"AES 加密失败"},
            {-40007 ,"AES 解密失败"},
            {-40008 ,"解密后得到的buffer非法"},
            {-40009 ,"base64加密异常"},
            {-40010 ,"base64解密异常"}
        };
        //验证URL
        // @param sMsgSignature: 签名串，对应URL参数的msg_signature
        // @param sTimeStamp: 时间戳，对应URL参数的timestamp
        // @param sNonce: 随机串，对应URL参数的nonce
        // @param sEchoStr: 随机串，对应URL参数的echostr
        // @param sReplyEchoStr: 解密之后的echostr，当return返回0时有效
        // @return：成功0，失败返回对应的错误码
        public static int VerifyURL(string sMsgSignature, string sTimeStamp, string sNonce, string sEchoStr, ref string sReplyEchoStr)
        {
            if (QYWechatHelper.EncodingAESKey.Length != 43)
            {
                return (int)QYWechatWXBizMsgCryptErrorCode.WXBizMsgCrypt_IllegalAesKey;
            }
            int ret = VerifySignature(QYWechatHelper.Token, sTimeStamp, sNonce, sEchoStr, sMsgSignature);
            if (0 != ret)
            {
                return ret;
            }
            string cpid = "";
            try
            {
                sReplyEchoStr = QYWechatCryptography.AES_decrypt(sEchoStr, QYWechatHelper.EncodingAESKey, ref cpid); //m_sReceiveId);
            }
            catch (Exception)
            {
                sReplyEchoStr = "";
                return (int)QYWechatWXBizMsgCryptErrorCode.WXBizMsgCrypt_DecryptAES_Error;
            }
            if (cpid != QYWechatHelper.Corpid)
            {
                sReplyEchoStr = "";
                return (int)QYWechatWXBizMsgCryptErrorCode.WXBizMsgCrypt_ValidateCorpid_Error;
            }
            return 0;
        }

        // 检验消息的真实性，并且获取解密后的明文
        // @param sMsgSignature: 签名串，对应URL参数的msg_signature
        // @param sTimeStamp: 时间戳，对应URL参数的timestamp
        // @param sNonce: 随机串，对应URL参数的nonce
        // @param sPostData: 密文，对应POST请求的数据
        // @param sMsg: 解密后的原文，当return返回0时有效
        // @return: 成功0，失败返回对应的错误码
        public static int DecryptMsg(string sMsgSignature, string sTimeStamp, string sNonce, string sPostData, ref string sMsg)
        {
            if (QYWechatHelper.EncodingAESKey.Length != 43)
            {
                return (int)QYWechatWXBizMsgCryptErrorCode.WXBizMsgCrypt_IllegalAesKey;
            }
            XmlDocument doc = new();
            XmlNode? root;
            string? sEncryptMsg;
            try
            {
                doc.LoadXml(sPostData);
                root = doc.FirstChild;
                sEncryptMsg = root?["Encrypt"]?.InnerText;
            }
            catch (Exception)
            {
                return (int)QYWechatWXBizMsgCryptErrorCode.WXBizMsgCrypt_ParseXml_Error;
            }
            //verify signature
            int ret = VerifySignature(QYWechatHelper.Token, sTimeStamp, sNonce, sEncryptMsg, sMsgSignature);
            if (ret != 0)
                return ret;
            //decrypt
            string cpid = "";
            try
            {
                sMsg = QYWechatCryptography.AES_decrypt(sEncryptMsg ?? string.Empty, QYWechatHelper.EncodingAESKey, ref cpid);
            }
            catch (FormatException)
            {
                sMsg = "";
                return (int)QYWechatWXBizMsgCryptErrorCode.WXBizMsgCrypt_DecodeBase64_Error;
            }
            catch (Exception)
            {
                sMsg = "";
                return (int)QYWechatWXBizMsgCryptErrorCode.WXBizMsgCrypt_DecryptAES_Error;
            }
            if (cpid != QYWechatHelper.Corpid)
                return (int)QYWechatWXBizMsgCryptErrorCode.WXBizMsgCrypt_ValidateCorpid_Error;
            return 0;
        }

        //将企业号回复用户的消息加密打包
        // @param sReplyMsg: 企业号待回复用户的消息，xml格式的字符串
        // @param sTimeStamp: 时间戳，可以自己生成，也可以用URL参数的timestamp
        // @param sNonce: 随机串，可以自己生成，也可以用URL参数的nonce
        // @param sEncryptMsg: 加密后的可以直接回复用户的密文，包括msg_signature, timestamp, nonce, encrypt的xml格式的字符串,
        //						当return返回0时有效
        // return：成功0，失败返回对应的错误码
        public static int EncryptMsg(string sReplyMsg, string sTimeStamp, string sNonce, ref string sEncryptMsg)
        {
            if (QYWechatHelper.EncodingAESKey.Length != 43)
            {
                return (int)QYWechatWXBizMsgCryptErrorCode.WXBizMsgCrypt_IllegalAesKey;
            }
            string raw;
            try
            {
                raw = QYWechatCryptography.AES_encrypt(sReplyMsg, QYWechatHelper.EncodingAESKey, QYWechatHelper.Corpid);
            }
            catch (Exception)
            {
                return (int)QYWechatWXBizMsgCryptErrorCode.WXBizMsgCrypt_EncryptAES_Error;
            }
            string MsgSigature = "";
            int ret = GenarateSinature(QYWechatHelper.Token, sTimeStamp, sNonce, raw, ref MsgSigature);
            if (0 != ret)
                return ret;
            sEncryptMsg = "";

            string EncryptLabelHead = "<Encrypt><![CDATA[";
            string EncryptLabelTail = "]]></Encrypt>";
            string MsgSigLabelHead = "<MsgSignature><![CDATA[";
            string MsgSigLabelTail = "]]></MsgSignature>";
            string TimeStampLabelHead = "<TimeStamp><![CDATA[";
            string TimeStampLabelTail = "]]></TimeStamp>";
            string NonceLabelHead = "<Nonce><![CDATA[";
            string NonceLabelTail = "]]></Nonce>";
            sEncryptMsg = sEncryptMsg + "<xml>" + EncryptLabelHead + raw + EncryptLabelTail;
            sEncryptMsg = sEncryptMsg + MsgSigLabelHead + MsgSigature + MsgSigLabelTail;
            sEncryptMsg = sEncryptMsg + TimeStampLabelHead + sTimeStamp + TimeStampLabelTail;
            sEncryptMsg = sEncryptMsg + NonceLabelHead + sNonce + NonceLabelTail;
            sEncryptMsg += "</xml>";
            return 0;
        }

        public static string GetErrMsg(int errCode)
        {
            if (_ErrorMsgDic.TryGetValue(errCode, out string? value))
            {
                return value;
            }
            return string.Empty;
        }
        public class DictionarySort : System.Collections.IComparer
        {
            public int Compare(object? oLeft, object? oRight)
            {
                string? sLeft = oLeft as string;
                string? sRight = oRight as string;
                sLeft ??= string.Empty;
                sRight ??= string.Empty;
                int iLeftLength = sLeft!.Length;
                int iRightLength = sRight!.Length;
                int index = 0;
                while (index < iLeftLength && index < iRightLength)
                {
                    if (sLeft[index] < sRight[index])
                        return -1;
                    else if (sLeft[index] > sRight[index])
                        return 1;
                    else
                        index++;
                }
                return iLeftLength - iRightLength;

            }
        }
        //Verify Signature
        private static int VerifySignature(string sToken, string sTimeStamp, string sNonce, string? sMsgEncrypt, string sSigture)
        {
            string hash = "";
            int ret  = GenarateSinature(sToken, sTimeStamp, sNonce, sMsgEncrypt, ref hash);
            if (ret != 0)
                return ret;
            if (hash == sSigture)
                return 0;
            else
            {
                return (int)QYWechatWXBizMsgCryptErrorCode.WXBizMsgCrypt_ValidateSignature_Error;
            }
        }

        public static int GenarateSinature(string sToken, string sTimeStamp, string sNonce, string? sMsgEncrypt, ref string sMsgSignature)
        {
            ArrayList AL = [sToken, sTimeStamp, sNonce, sMsgEncrypt];
            AL.Sort(new DictionarySort());
            string raw = "";
            for (int i = 0; i < AL.Count; ++i)
            {
                raw += AL[i];
            }

            SHA1 sha;
            ASCIIEncoding enc;
            string hash;
            try
            {
                sha = new SHA1CryptoServiceProvider();
                enc = new ASCIIEncoding();
                byte[] dataToHash = enc.GetBytes(raw);
                byte[] dataHashed = sha.ComputeHash(dataToHash);
                hash = BitConverter.ToString(dataHashed).Replace("-", "");
                hash = hash.ToLower();
            }
            catch (Exception)
            {
                return (int)QYWechatWXBizMsgCryptErrorCode.WXBizMsgCrypt_ComputeSignature_Error;
            }
            sMsgSignature = hash;
            return 0;
        }
    }
    class QYWechatCryptography
    {
        public static UInt32 HostToNetworkOrder(UInt32 inval)
        {
            UInt32 outval = 0;
            for (int i = 0; i < 4; i++)
                outval = (outval << 8) + ((inval >> (i * 8)) & 255);
            return outval;
        }

        public static Int32 HostToNetworkOrder(Int32 inval)
        {
            Int32 outval = 0;
            for (int i = 0; i < 4; i++)
                outval = (outval << 8) + ((inval >> (i * 8)) & 255);
            return outval;
        }
        /// <summary>
        /// 解密方法
        /// </summary>
        /// <param name="Input">密文</param>
        /// <param name="EncodingAESKey"></param>
        /// <returns></returns>
        /// 
        public static string AES_decrypt(String Input, string EncodingAESKey, ref string corpid)
        {
            byte[] Key;
            Key = Convert.FromBase64String(EncodingAESKey + "=");
            byte[] Iv = new byte[16];
            Array.Copy(Key, Iv, 16);
            byte[] btmpMsg = AES_decrypt(Input, Iv, Key);

            int len = BitConverter.ToInt32(btmpMsg, 16);
            len = System.Net.IPAddress.NetworkToHostOrder(len);


            byte[] bMsg = new byte[len];
            byte[] bCorpid = new byte[btmpMsg.Length - 20 - len];
            Array.Copy(btmpMsg, 20, bMsg, 0, len);
            Array.Copy(btmpMsg, 20 + len, bCorpid, 0, btmpMsg.Length - 20 - len);
            string oriMsg = Encoding.UTF8.GetString(bMsg);
            corpid = Encoding.UTF8.GetString(bCorpid);


            return oriMsg;
        }

        public static String AES_encrypt(String Input, string EncodingAESKey, string corpid)
        {
            byte[] Key;
            Key = Convert.FromBase64String(EncodingAESKey + "=");
            byte[] Iv = new byte[16];
            Array.Copy(Key, Iv, 16);
            string Randcode = CreateRandCode(16);
            byte[] bRand = Encoding.UTF8.GetBytes(Randcode);
            byte[] bCorpid = Encoding.UTF8.GetBytes(corpid);
            byte[] btmpMsg = Encoding.UTF8.GetBytes(Input);
            byte[] bMsgLen = BitConverter.GetBytes(HostToNetworkOrder(btmpMsg.Length));
            byte[] bMsg = new byte[bRand.Length + bMsgLen.Length + bCorpid.Length + btmpMsg.Length];

            Array.Copy(bRand, bMsg, bRand.Length);
            Array.Copy(bMsgLen, 0, bMsg, bRand.Length, bMsgLen.Length);
            Array.Copy(btmpMsg, 0, bMsg, bRand.Length + bMsgLen.Length, btmpMsg.Length);
            Array.Copy(bCorpid, 0, bMsg, bRand.Length + bMsgLen.Length + btmpMsg.Length, bCorpid.Length);

            return AES_encrypt(bMsg, Iv, Key);

        }
        private static string CreateRandCode(int codeLen)
        {
            string codeSerial = "2,3,4,5,6,7,a,c,d,e,f,h,i,j,k,m,n,p,r,s,t,A,C,D,E,F,G,H,J,K,M,N,P,Q,R,S,U,V,W,X,Y,Z";
            if (codeLen == 0)
            {
                codeLen = 16;
            }
            string[] arr = codeSerial.Split(',');
            string code = "";
            int randValue;
            Random rand = new(unchecked((int)DateTime.Now.Ticks));
            for (int i = 0; i < codeLen; i++)
            {
                randValue = rand.Next(0, arr.Length - 1);
                code += arr[randValue];
            }
            return code;
        }

        private static String AES_encrypt(byte[] Input, byte[] Iv, byte[] Key)
        {
            var aes = new RijndaelManaged
            {
                //秘钥的大小，以位为单位
                KeySize = 256,
                //支持的块大小
                BlockSize = 128,
                //填充模式
                //aes.Padding = PaddingMode.PKCS7;
                Padding = PaddingMode.None,
                Mode = CipherMode.CBC,
                Key = Key,
                IV = Iv
            };
            var encrypt = aes.CreateEncryptor(aes.Key, aes.IV);
            byte[]? xBuff = null;

            #region 自己进行PKCS7补位，用系统自己带的不行
            byte[] msg = new byte[Input.Length + 32 - Input.Length % 32];
            Array.Copy(Input, msg, Input.Length);
            byte[] pad = KCS7Encoder(Input.Length);
            Array.Copy(pad, 0, msg, Input.Length, pad.Length);
            #endregion

            #region 注释的也是一种方法，效果一样
            //ICryptoTransform transform = aes.CreateEncryptor();
            //byte[] xBuff = transform.TransformFinalBlock(msg, 0, msg.Length);
            #endregion

            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, encrypt, CryptoStreamMode.Write))
                {
                    cs.Write(msg, 0, msg.Length);
                }
                xBuff = ms.ToArray();
            }

            String Output = Convert.ToBase64String(xBuff);
            return Output;
        }

        private static byte[] KCS7Encoder(int text_length)
        {
            int block_size = 32;
            // 计算需要填充的位数
            int amount_to_pad = block_size - (text_length % block_size);
            if (amount_to_pad == 0)
            {
                amount_to_pad = block_size;
            }
            // 获得补位所用的字符
            char pad_chr = Chr(amount_to_pad);
            string tmp = "";
            for (int index = 0; index < amount_to_pad; index++)
            {
                tmp += pad_chr;
            }
            return Encoding.UTF8.GetBytes(tmp);
        }
        /**
         * 将数字转化成ASCII码对应的字符，用于对明文进行补码
         * 
         * @param a 需要转化的数字
         * @return 转化得到的字符
         */
        static char Chr(int a)
        {

            byte target = (byte)(a & 0xFF);
            return (char)target;
        }
        private static byte[] AES_decrypt(String Input, byte[] Iv, byte[] Key)
        {
            RijndaelManaged aes = new()
            {
                KeySize = 256,
                BlockSize = 128,
                Mode = CipherMode.CBC,
                Padding = PaddingMode.None,
                Key = Key,
                IV = Iv
            };
            var decrypt = aes.CreateDecryptor(aes.Key, aes.IV);
            byte[]? xBuff = null;
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, decrypt, CryptoStreamMode.Write))
                {
                    byte[] xXml = Convert.FromBase64String(Input);
                    byte[] msg = new byte[xXml.Length + 32 - xXml.Length % 32];
                    Array.Copy(xXml, msg, xXml.Length);
                    cs.Write(xXml, 0, xXml.Length);
                }
                xBuff = Decode2(ms.ToArray());
            }
            return xBuff;
        }
        private static byte[] Decode2(byte[] decrypted)
        {
            int pad = (int)decrypted[^1];
            if (pad < 1 || pad > 32)
            {
                pad = 0;
            }
            byte[] res = new byte[decrypted.Length - pad];
            Array.Copy(decrypted, 0, res, 0, decrypted.Length - pad);
            return res;
        }
    }

    public enum QYWechatCallbackEventType
    {
        /// <summary>
        /// Event=change_external_contact 且 ChangeType=add_external_contact
        /// *配置了客户联系功能的成员添加外部联系人时，回调该事件
        /// </summary>
        添加企业客户事件 = 1,

        /// <summary>
        /// Event=change_external_contact 且 ChangeType=edit_external_contact
        /// *配置了客户联系功能的成员修改外部联系人的备注、手机号或标签时，回调该事件
        /// </summary>
        编辑企业客户事件 = 2,

        /// <summary>
        /// Event=change_external_contact 且 ChangeType=add_half_external_contact
        /// *外部联系人添加了配置了客户联系功能且开启了免验证的成员时（此时成员尚未确认添加对方为好友），回调该事件
        /// </summary>
        外部联系人免验证添加成员事件 = 3,

        /// <summary>
        /// Event=change_external_contact 且 ChangeType=del_external_contact
        /// *配置了客户联系功能的成员删除外部联系人时，回调该事件
        /// </summary>
        删除企业客户事件 = 4,

        /// <summary>
        /// Event=change_external_contact 且 ChangeType=del_follow_user
        /// *配置了客户联系功能的成员被外部联系人删除时，回调该事件
        /// </summary>
        删除跟进成员事件 = 5,

        /// <summary>
        /// Event=change_external_contact 且 ChangeType=transfer_fail
        /// *企业将客户分配给新的成员接替后，客户添加失败时回调该事件
        /// </summary>
        客户接替失败事件 = 6,

        /// <summary>
        /// Event=change_external_chat 且 ChangeType=create
        /// *有新增客户群时，回调该事件。收到该事件后，企业可以调用获取客户群详情接口获取客户群详情。
        /// </summary>
        客户群创建事件 = 7,

        /// <summary>
        /// Event=change_external_chat 且 ChangeType=update
        /// *客户群被修改后（群名变更，群成员增加或移除，群主变更，群公告变更），回调该事件。收到该事件后，企业需要再调用获取客户群详情接口，以获取最新的群详情。
        /// </summary>
        客户群变更事件 = 8,

        /// <summary>
        /// Event=change_external_chat 且 ChangeType=dismiss
        /// *当客户群被群主解散后，回调该事件。
        /// *需注意的是，如果发生群信息变动，会立即收到此事件，但是部分信息是异步处理，可能需要等一段时间(例如2秒)调用获取客户群详情接口才能得到最新结果
        /// </summary>
        客户群解散事件 = 9,

        /// <summary>
        /// *企业客户标签相关事件暂时不用处理
        /// </summary>
        其他 = 10
    }

    public enum QYWechatCallbackGropChatUpdateDetail
    {
        /// <summary>
        /// UpdateDetail=add_member
        /// </summary>
        成员入群 = 1,
        /// <summary>
        /// UpdateDetail=del_member
        /// </summary>
        成员退群 = 2,
        /// <summary>
        /// UpdateDetail=change_owner
        /// </summary>
        群主变更 = 3,
        /// <summary>
        /// UpdateDetail=change_name
        /// </summary>
        群名变更 = 4,
        /// <summary>
        /// UpdateDetail=change_notice
        /// </summary>
        群公告变更 = 5,

        其他 = 99
    }
}
