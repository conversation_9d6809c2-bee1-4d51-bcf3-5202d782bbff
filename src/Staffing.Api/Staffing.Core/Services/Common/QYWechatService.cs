﻿using Config.Enums;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.Common;
using Config;
using LinqKit;
using ServiceStack.Text;
using Infrastructure.CommonService;
using Config.CommonModel.Business;
using Infrastructure.Exceptions;
using Staffing.Model.Service;
using TencentCloud.Faceid.V20180301.Models;
using Staffing.Model.Common.System;
using Staffing.Model.Hr.Dashboard;
using Staffing.Model.Hr.Recruit;
using Org.BouncyCastle.Asn1.X9;
using DocumentFormat.OpenXml.Spreadsheet;
using System.Reflection;
using Infrastructure.QYWechat.Model;
using ServiceStack;
using Aliyun.OSS;
using Infrastructure.QYWechat;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Pipelines.Sockets.Unofficial.Buffers;
using Config.CommonModel.ThirdTalentInfo;
using Staffing.Model.Callback;
using System.Runtime.ConstrainedExecution;
using Nest;
using SkiaSharp;
using System.Collections.Concurrent;
using Config.CommonModel.QYWechat;
using DocumentFormat.OpenXml.Drawing;

namespace Staffing.Core.Services.Common;

[Service(ServiceLifetime.Transient)]
public class QYWechatService : IQYWechatService
{
    private readonly RequestContext _user;
    private readonly LogManager _log;
    private readonly StaffingContext _context;
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    public QYWechatService(
        RequestContext user, LogManager log,
        IDbContextFactory<StaffingContext> contextFactory,
        StaffingContext context)
    {
        _user = user;
        _log = log;
        _contextFactory = contextFactory;
        _context = context;
    }

    /// <summary>
    /// 绑定企业微信
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<EmptyResponse> Binding(BindingQYWechatRequest model)
    {
        if (string.IsNullOrEmpty(model.BindingPhone))
            throw new BadRequestException("缺少绑定的手机号！");

        bool IsBinding = _context.User_QYWechat.Any(o => o.UserId == _user.Id);

        if (IsBinding)
            throw new BadRequestException("用户已绑定企业微信，无法重复绑定！");

        string qwUserID = string.Empty, ErrMsg = string.Empty;
        var qwModel = _context.QYWechat_Employee.Where(x => x.Mobile == model.BindingPhone).FirstOrDefault();
        if (qwModel == null)
        {
            var QWUserId = await QYWechatHelper.GetUserIdByMobile(model.BindingPhone);
            qwUserID = QWUserId.Userid ?? string.Empty;
            ErrMsg = QWUserId.ErrMsg ?? string.Empty;
        }
        else
            qwUserID = qwModel.UserId;

        if (!string.IsNullOrEmpty(qwUserID))
        {
            bool IsBinding_dingding = _context.User_QYWechat.Any(o => o.QWUserId == qwUserID);
            if (IsBinding_dingding)
                throw new BadRequestException("企业微信用户已存在！");

            var QWUserDetails = await QYWechatHelper.GetEmployeeInfo(qwUserID);
            if (QWUserDetails.Result)
            {
                //保存企业微信用户信息
                User_QYWechat user_Binding = new()
                {
                    UserId = _user.Id,
                    CreatedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now,
                    QWUserId = qwUserID,
                    Mobile = model.BindingPhone,
                    QWAvatar = QWUserDetails.Avatar ?? string.Empty,
                    QWName = QWUserDetails.Name ?? string.Empty,
                    QWPosition = QWUserDetails.Position ?? string.Empty,
                    QWGender = QWUserDetails.Gender,
                    QWThumb_Avatar = QWUserDetails.Thumb_Avatar ?? string.Empty
                };
                _context.Add(user_Binding);
                _context.SaveChanges();

                //保存企业微信群信息
                await LoadGroupChatByOwner(qwUserID);

                //重新生成海报
                MyRedis.Client.SAdd(SubscriptionKey.AdviserShareCertificate, _user.Id);
            }
            else
                throw new BadRequestException("获取企微的用户信息错误！");
        }
        else
        {
            _log.Error($"企业微信：绑定企微时获取企微用户ID失败", $"手机号={model.BindingPhone}，返回错误：{ErrMsg}");
            throw new BadRequestException("获取手机号关联企微的用户失败，手机号不匹配！");
        }

        return new EmptyResponse { };
    }
    /// <summary>
    /// 移除企业微信绑定
    /// </summary>
    /// <returns></returns>
    public EmptyResponse RelieveBinding()
    {
        var bindingModel = _context.User_QYWechat.Where(o => o.UserId == _user.Id).FirstOrDefault() ?? throw new BadRequestException("用户未绑定企业微信，无法解除绑定！");
        _context.Remove(bindingModel);
        _context.SaveChanges();

        //重新生成海报
        MyRedis.Client.SAdd(SubscriptionKey.AdviserShareCertificate, _user.Id);
        return new EmptyResponse { };
    }

    /// <summary>
    /// 根据手机号获取企微二维码 
    /// </summary>
    /// <param name="Mobile"></param>
    /// <returns></returns>
    public string GetEntQRCode(string Mobile)
    {
        return _context.QYWechat_Employee.Where(x => x.Mobile == Mobile).FirstOrDefault()?.QRCodeURL ?? string.Empty;
    }

    /// <summary>
    /// 验证企业微信是否绑定
    /// </summary>
    /// <returns></returns>
    public GetQWBindingStatsuRsponse CheckIsBinding()
    {
        GetQWBindingStatsuRsponse retModel = new() { BindingStatus = false };

        var model = _context.User_QYWechat.Where(o => o.UserId == _user.Id).FirstOrDefault();

        if (model != null)
        {
            retModel.BindingStatus = true;
            retModel.QWMobile = model.Mobile;
            retModel.QWName = model.QWName;
            retModel.QWAvatar = model.QWAvatar;
        }
        return retModel;
    }

    /// <summary>
    /// 获取我的企微群列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<QWLoadGroupByEmpResponse> GetMyQWGroup(QuerySearch model)
    {
        QWLoadGroupByEmpResponse result = new ();
        var qwEmployee = _context.User_QYWechat.Where(o => o.UserId == _user.Id).FirstOrDefault();

        if (qwEmployee == null)
        {
            //throw new BadRequestException("用户未绑定企业微信，请绑定后重试！");
            result.BindingStatus = false;
        }
        else
        {
            result.BindingStatus = true;
            result.QWName = qwEmployee.QWName;
            result.QWAvatar = qwEmployee.QWAvatar;

            var predicate = PredicateBuilder.New<QYWechat_GroupChat>(x => x.OwnerId == qwEmployee.QWUserId);
            if (!string.IsNullOrWhiteSpace(model.Search))
                predicate.And(x => x.Name.Contains(model.Search));

            var GropChatIds = _context.QYWechat_GroupChat
                .Where(predicate)
                .Select(x => x.GroupChatId)
                .ToList();
            //获取企微群列表，并验证是否增加了新群，若增加了则推送到后台进行加载
            var qyw_groupList = await QYWechatHelper.GetGroupChatList(owner_userids: [qwEmployee.QWUserId]);
            if (qyw_groupList.Result)
            {
                var newGroups = qyw_groupList.Group_Chat_List.Where(n => !GropChatIds.Contains(n.Chat_Id)).ToList();
                if (newGroups.Count > 0)
                {
                    foreach (var item in newGroups)
                        await PushToLoadGroupInfo(item.Chat_Id, false);
                }
            }

            result.Groups = _context.QYWechat_GroupChat
                .Where(predicate)
                .OrderByDescending(x => x.GroupCreateTime)
                .Select(x => new QWLoadGroupByEmpResponseItem
                {
                    Value = x.GroupChatId,
                    Label = x.Name,
                    GropChatId = x.GroupChatId,
                    Name = x.Name,
                    CreateTime = x.GroupCreateTime,
                    MemberCount = x.QYWechat_GroupMember.Count
                })
                .ToList();
        }

        return result;
    }
    /// <summary>
    /// 建群
    /// </summary>
    /// <param name="ChatId">群ID</param>
    /// <returns></returns>
    private async Task<(bool Result, string ErrMsg)> SaveCreateGroupChat(string ChatId)
    {
        //获取群详情
        var groupInfo = await Infrastructure.QYWechat.QYWechatHelper.GetGroupChatInfo(ChatId);
        if (groupInfo == null || groupInfo.Group_Chat == null)
            return (false, "根据群ID未获取到群信息");
        else if (!groupInfo.Result)
            return (false, groupInfo.ErrMsg ?? "根据群ID获取群信息失败");
        else if (string.IsNullOrEmpty(groupInfo.Group_Chat.Owner))
            return (false, "获取的群信息中群主ID为空");

        using (var db = _contextFactory.CreateDbContext())
        {
            string oldName = string.Empty;
            //更新群信息到数据库
            var old = db.QYWechat_GroupChat.Find(ChatId);
            if (old == null)
                db.QYWechat_GroupChat.Add(new QYWechat_GroupChat()
                {
                    GroupChatId = groupInfo.Group_Chat.Chat_Id!,
                    Name = groupInfo.Group_Chat.Name ?? string.Empty,
                    GroupCreateTime = Infrastructure.QYWechat.QYWechatHelper.ExchangeTimeStamp(groupInfo.Group_Chat.Create_Time),
                    OwnerId = groupInfo.Group_Chat.Owner!
                });
            else
            {
                oldName = old.Name;
                old.Name = groupInfo.Group_Chat.Name ?? string.Empty;
                old.GroupCreateTime = Infrastructure.QYWechat.QYWechatHelper.ExchangeTimeStamp(groupInfo.Group_Chat.Create_Time);
                old.OwnerId = groupInfo.Group_Chat.Owner!;
            }

            db.SaveChanges();
            if (oldName != groupInfo.Group_Chat.Name)
                MyRedis.Client.LPush(SubscriptionKey.QYWechatMemUpToCerRegs,
                                groupInfo.Group_Chat.Member_List.Where(x => !string.IsNullOrEmpty(x.UnionId))
                                .Select(x => new QYWechat_MemChangeCacheInfo
                                {
                                    //ChangeType = QYWechat_MemChangeType.群名变更,
                                    //GroupChatID = groupInfo.Group_Chat.Chat_Id!,
                                    WeChatUnionId = x.UnionId!
                                }).ToArray());
        }

        //当有群成员时，更新群成员信息
        SaveGroupMember(groupInfo.Group_Chat.Chat_Id, groupInfo.Group_Chat.Member_Version,
            groupInfo.Group_Chat.Owner!,
            groupInfo.Group_Chat.Member_List);

        return (true, string.Empty);
    }

    /// <summary>
    /// 加载群信息
    /// </summary>
    /// <param name="GroupChatId">企微群ID</param>
    /// <param name="IsPushToBackground">true=记录需要加载的群ID，在后台加载群信息；false=直接请求加载群信息并返回加载结果</param>
    /// <returns></returns>
    public async Task<(bool Result, string ErrMsg)> PushToLoadGroupInfo(string GroupChatId, bool IsPushToBackground = true, List<string>? MemChangeList = null, QYWechatCallbackGropChatUpdateDetail? CallbackUpdateDetail = null)
    {
        if (IsPushToBackground)
        {
            MyRedis.Client.SAdd(SubscriptionKey.QYWechatLoadGroupIds, GroupChatId);
            return (true, string.Empty);
        }
        else
            return await SaveCreateGroupChat(GroupChatId);
    }
    /// <summary>
    /// 解散群
    /// </summary>
    /// <param name="ChatId">群ID</param>
    /// <returns></returns>
    public (bool Result, string ErrMsg) DismissGroupChat(string ChatId)
    {
        List<string> WeChatUnionIds;
        using (var db = _contextFactory.CreateDbContext())
        {
            WeChatUnionIds = db.QYWechat_GroupMember.Where(x => x.GroupChatID == ChatId && !string.IsNullOrEmpty(x.WeChatUnionId))
                .Select(x => x.WeChatUnionId)
                .ToList();
            // 删除微信群成员
            db.QYWechat_GroupMember.Where(x => x.GroupChatID == ChatId).ExecuteDelete();
            //.ExecuteUpdate(u => u.SetProperty(s => s.IsDel, true));

            // 删除微信群
            db.QYWechat_GroupChat.Where(x => x.GroupChatId == ChatId).ExecuteDelete();

            db.SaveChanges();
        }
        MyRedis.Client.LPush(SubscriptionKey.QYWechatMemUpToCerRegs,
                                WeChatUnionIds
                                .Select(x => new QYWechat_MemChangeCacheInfo
                                {
                                    //ChangeType = QYWechat_MemChangeType.退群,
                                    //GroupChatID = ChatId,
                                    WeChatUnionId = x
                                }).ToArray());
        return (true, string.Empty);
    }

    /// <summary>
    /// 客户群变更事件
    /// </summary>
    /// <param name="ChatId">群ID</param>
    /// <returns></returns>
    public async Task<(bool Result, string ErrMsg)> UpdateGroupChat(string ChatId, QYWechatCallbackGropChatUpdateDetail UpdateDetail, List<string>? MemChangeList)
    {
        if (MemChangeList == null || MemChangeList.Count == 0)
            return (true, "入群/退群的成员数据为空，无需处理");
        switch (UpdateDetail)
        {
            case QYWechatCallbackGropChatUpdateDetail.成员退群:
                using (var db = _contextFactory.CreateDbContext())
                {
                    // 微信群成员 状态变更为已退群
                    var member = db.QYWechat_GroupMember.Where(x => x.GroupChatID == ChatId && MemChangeList.Contains(x.GroupMemberID)).FirstOrDefault();
                    if (member != null)
                    {
                        member.IsDel = true;
                        if (!string.IsNullOrEmpty(member.WeChatUnionId))
                            MyRedis.Client.LPush(SubscriptionKey.QYWechatMemUpToCerRegs, new QYWechat_MemChangeCacheInfo
                            {
                                //ChangeType = QYWechat_MemChangeType.退群,
                                //GroupChatID = ChatId,
                                WeChatUnionId = member.WeChatUnionId
                            });
                        db.SaveChanges();
                    }
                }
                break;
            case QYWechatCallbackGropChatUpdateDetail.成员入群:
                return await PushToLoadGroupInfo(ChatId);
            case QYWechatCallbackGropChatUpdateDetail.群主变更:
            case QYWechatCallbackGropChatUpdateDetail.群名变更:
                _context.QYWechat_GroupChat.Where(x => x.GroupChatId == ChatId).ExecuteDelete();
                _context.SaveChanges();
                return await PushToLoadGroupInfo(ChatId);
            default://其他枚举在上一层逻辑中处理
                break;
        }

        return (true, string.Empty);
    }

    /// <summary>
    /// 根据群主ID获取群主手机号
    /// </summary>
    /// <param name="OwnerId">群主ID</param>
    /// <returns></returns>
    public async Task<(bool Result, string? ErrMsg, string? Mobile)> GetOwnerMobile(string OwnerId)
    {
        using var db = _contextFactory.CreateDbContext();
        var model = db.QYWechat_Employee.Find(OwnerId);
        if (model != null)
            return (true, string.Empty, model.Mobile);
        else
        {
            //根据群主手机号未匹配到顾问时，再次拉取群主信息，检测手机号是否变更
            var employeeInfo = await Infrastructure.QYWechat.QYWechatHelper.GetEmployeeInfo(OwnerId);
            if (employeeInfo.Result)
            {
                db.QYWechat_Employee.Add(new QYWechat_Employee
                {
                    UserId = employeeInfo.Userid!,
                    Mobile = employeeInfo.Mobile!,
                    Name = employeeInfo.Name ?? string.Empty,
                    QRCodeURL = employeeInfo.Qr_Code ?? string.Empty,
                    Gender = employeeInfo.Gender,
                    Status = (int)employeeInfo.Status,
                    Open_userid = employeeInfo.Open_Userid ?? string.Empty,
                    DepartmentIds = employeeInfo.Department?.ToJsonString() ?? "[]"
                });
                db.SaveChanges();
                return (true, string.Empty, employeeInfo.Mobile);
            }
            else
                return (false, employeeInfo.ErrMsg, employeeInfo.Mobile);
        }
    }

    ///// <summary>
    ///// 根据群主ID获取群主关联的顾问Model
    ///// </summary>
    ///// <param name="OwnerId">群主ID</param>
    ///// <returns></returns>
    //public async Task<(bool Result, string? ErrMsg, User_Hr? Hr)> GetOwner(string OwnerId)
    //{
    //    var result = false;

    //    using (var db = _contextFactory.CreateDbContext())
    //    {
    //        var model = db.QYWechat_Employee.Find(OwnerId);
    //        if (model != null)
    //        {
    //            //根据手机号获取关联的顾问ID
    //            var hr = GetHrByMobile(model.Mobile);
    //            if (hr == null)
    //            {
    //                //根据群主手机号未匹配到顾问时，再次拉取群主信息，检测手机号是否变更
    //                var employeeInfo = await Infrastructure.QYWechat.QYWechatHelper.GetEmployeeInfo(OwnerId);
    //                if (employeeInfo.Result)
    //                {
    //                    //检测手机号是否变更
    //                    if (employeeInfo.Mobile == model.Mobile)
    //                    {
    //                        //未变更时，报错未获取到关联顾问信息
    //                    }
    //                    else
    //                    {
    //                        //变更时，更新QYWechat_Employee
    //                        model.Mobile = employeeInfo.Mobile!;
    //                        model.Name = employeeInfo.Name ?? string.Empty;
    //                        model.Gender = employeeInfo.Gender;
    //                        model.Status = (int)employeeInfo.Status;
    //                        model.Open_userid = employeeInfo.Open_Userid ?? string.Empty;
    //                        model.DepartmentIds = employeeInfo.Department?.ToJsonString() ?? "[]";
    //                        db.SaveChanges();

    //                        //再次根据手机号获取关联的顾问ID
    //                        hr = GetHrByMobile(model.Mobile);
    //                    }
    //                }
    //                else
    //                    return (false, employeeInfo.ErrMsg, employeeInfo.Mobile);
    //            }
    //            return (true, string.Empty, model.Mobile);
    //        }

    //    }

    //    return result;
    //}

    /// <summary>
    /// 当有群成员时，更新群成员信息
    /// </summary>
    /// <param name="GroupChatID">群ID</param>
    /// <param name="Member_List">群成员列表</param>
    /// <returns></returns>
    private (bool Result, string? ErrMsg) SaveGroupMember(string GroupChatID, string Member_Version,
        string OwnerId,
        List<GroupChatMember>? Member_List)
    {
        if (Member_List == null || Member_List.Count == 0)
            return (true, "无群成员");

        ////获取关注对应的HR
        //var hr = GetHrByMobile(OwnerId);
        //if (hr == null)
        //    return (false, "获取HR信息失败");

        #region 暂不做群员和Seeker.UserId的关联了
        ////根据UnionID批量获取关联的求职者ID
        //var seekers = GetSeekersByWechatUnionID(Member_List.Where(x => !string.IsNullOrEmpty(x.UnionId)).Select(x => x.UnionId!).ToList());
        ////排除根据UnionID未获取到关联求职者的记录
        //var tempSUnionIDs = seekers.Select(x => x.UnionId).ToList();
        //if (Member_List.Exists(x => !tempSUnionIDs.Contains(x.UnionId)))
        //{
        //    _log.Error("企业微信群：更新群成员", $"存在UnionID不存在的情况，不存在的记录：{Member_List.Where(x => !tempSUnionIDs.Contains(x.UnionId)).ToList().ToJsonString()}");
        //    Member_List.RemoveAll(x => !tempSUnionIDs.Contains(x.UnionId));
        //    if (Member_List.Count == 0)
        //        return (false, "获取HR信息失败");
        //}
        #endregion

        using (var db = _contextFactory.CreateDbContext())
        {
            bool IsUpdateDB = false;//确认是否需更新数据库

            var members = db.QYWechat_GroupMember.Where(x => x.GroupChatID == GroupChatID).ToList();
            var mIds = members.Select(x => x.MemberID).ToList();

            List<QYWechat_MemChangeCacheInfo> cacheData = new List<QYWechat_MemChangeCacheInfo>();
            //添加不存在的记录
            var newModel = Member_List.Where(x => !mIds.Contains(x.UserId))
                .Select(x => new QYWechat_GroupMember
                {
                    GroupChatID = GroupChatID,
                    GroupMemberID = QYWechatHelper.CreateGroupMemberId(GroupChatID, x.UserId!),
                    MemberID = x.UserId!,
                    MemberVersion = Member_Version ?? string.Empty,
                    WeChatUnionId = x.UnionId ?? string.Empty,
                    UserType = (int)x.Type,
                    JoinTime = Infrastructure.QYWechat.QYWechatHelper.ExchangeTimeStamp(x.Join_Time),
                    JoinScene = (int)x.Join_Scene,
                    InvitorWeChatId = x.Invitor?.UserId ?? string.Empty,
                    NickName = x.Group_Nickname ?? string.Empty
                })
                .ToList();
            if (newModel.Count > 0)
            {
                IsUpdateDB = true;
                db.QYWechat_GroupMember.AddRange(newModel);

                if (newModel.Exists(x => !string.IsNullOrEmpty(x.WeChatUnionId)))
                    cacheData.AddRange(newModel.Where(x => !string.IsNullOrEmpty(x.WeChatUnionId))
                        .Select(x => new QYWechat_MemChangeCacheInfo
                        {
                            //ChangeType = QYWechat_MemChangeType.入群,
                            //GroupChatID = x.GroupChatID,
                            WeChatUnionId = x.WeChatUnionId
                        }));
            }
            //更新已存在的记录
            if (Member_List.Any(x => mIds.Contains(x.UserId)))
            {
                IsUpdateDB = true;
                Member_List.Where(x => mIds.Contains(x.UserId)).ForEach((GroupChatMember x) =>
                {
                    var temp = members.First(t => t.MemberID == x.UserId);

                    if (temp.IsDel && !string.IsNullOrEmpty(x.UnionId))
                        cacheData.Add(new QYWechat_MemChangeCacheInfo
                        {
                            //ChangeType = QYWechat_MemChangeType.入群,
                            //GroupChatID = GroupChatID,
                            WeChatUnionId = x.UnionId
                        });

                    temp.IsDel = false;
                    temp.MemberVersion = Member_Version ?? string.Empty;
                    temp.WeChatUnionId = x.UnionId ?? string.Empty;
                    temp.UserType = (int)x.Type;
                    temp.JoinTime = Infrastructure.QYWechat.QYWechatHelper.ExchangeTimeStamp(x.Join_Time);
                    temp.JoinScene = (int)x.Join_Scene;
                    temp.InvitorWeChatId = x.Invitor?.UserId ?? string.Empty;
                    temp.NickName = x.Group_Nickname ?? string.Empty;
                });
            }
            //删除退群的记录
            var tempids = Member_List.Select(x => x.UserId).ToList();
            if (members.Any(x => !tempids.Contains(x.MemberID)))
            {
                IsUpdateDB = true;
                members.Where(x => !tempids.Contains(x.MemberID)).ForEach((QYWechat_GroupMember x) =>
                {
                    x.IsDel = true;

                    if (!string.IsNullOrEmpty(x.WeChatUnionId))
                        cacheData.AddRange(
                            newModel.Where(x => !string.IsNullOrEmpty(x.WeChatUnionId))
                            .Select(x => new QYWechat_MemChangeCacheInfo
                            {
                                //ChangeType = QYWechat_MemChangeType.退群,
                                //GroupChatID = x.GroupChatID,
                                WeChatUnionId = x.WeChatUnionId
                            }));
                });
            }

            if (IsUpdateDB)
                db.SaveChanges();

            if (cacheData.Count > 0)
                MyRedis.Client.LPush(SubscriptionKey.QYWechatMemUpToCerRegs, cacheData.ToArray());
        }

        return (true, string.Empty);
    }

    #region 初始化
    /// <summary>
    /// 导入指定群主的群信息
    /// </summary>
    /// <param name="ChatId">群ID</param>
    /// <param name="IsPushToBackground">true=记录需要加载的群ID，在后台加载群信息；false=直接请求加载群信息并返回加载结果</param>
    /// <returns></returns>
    public async Task<(bool Result, string ErrMsg)> LoadGroupChatByOwner(string OwnerId, bool IsPushToBackground = true)
    {
        int limit = 500, AllGroupCount = 0;
        string? cursor = null;
        List<string>? owner_userids = null;
        if (!string.IsNullOrEmpty(OwnerId))
            owner_userids = new List<string> { OwnerId };
        List<string> errorGroupIds = new();
        do
        {
            //获取群列表
            var GroupList = await QYWechatHelper.GetGroupChatList(limit, cursor, owner_userids);
            if (!GroupList.Result)
                return (false, GroupList.ErrMsg ?? "获取企微群列表失败");

            foreach (var item in GroupList.Group_Chat_List!)
            {
                AllGroupCount++;
                var createResult = await PushToLoadGroupInfo(item.Chat_Id, IsPushToBackground);
                if (!createResult.Result)
                    errorGroupIds.Add(item.Chat_Id + ":" + createResult.ErrMsg + "|");
            }
            //分页游标，下次请求时填写以获取之后分页的记录。如果该字段返回空则表示已没有更多数据
            cursor = GroupList.Next_Cursor;
        } while (!string.IsNullOrEmpty(cursor));

        if (errorGroupIds.Count > 0)
        {
            if (errorGroupIds.Count == AllGroupCount)
                return (false, $"企微群导入失败：{errorGroupIds.ToJsonString()}");
            else
                _log.Error("企业微信：导入群信息", $"如下群导入失败：{errorGroupIds.ToJsonString()}");
        }
        return (true, $"成功导入当前员工创建的{AllGroupCount}个企微群");
    }

    /// <summary>
    /// 导入所有企微的群信息
    /// </summary>
    /// <returns></returns>
    public async Task<(bool Result, string ErrMsg)> LoadGroupChatByAll()
    {
        var result = await LoadGroupChatByOwner(string.Empty, false);
        //if (!result.Result)
        //    _log.Error("企业微信：导入所有企微的群信息", result.ErrMsg);
        return result;
    }

    /// <summary>
    /// 导入所有企微的员工信息
    /// </summary>
    /// <returns></returns>
    public async Task<(bool Result, string? ErrMsg)> LoadEmploeeByAll()
    {
        try
        {
            var departmentList = await QYWechatHelper.GetDepartmentList();
            if (departmentList.Result && departmentList.Department != null)
            {
                List<QYWechat_Employee> Employees = new();
                List<string> errors = new();
                foreach (var item in departmentList.Department)
                {
                    var QWEmployees = await QYWechatHelper.GetEmployeeByDepartment(item.Id.ToString());
                    if (!QWEmployees.Result)
                    {
                        errors.Add($"部门Id={item.Id},获取员工列表失败:{QWEmployees.ErrMsg}");
                        continue;
                    }

                    if (QWEmployees.UserList != null && QWEmployees.UserList.Count > 0)
                    {
                        Employees.AddRange(QWEmployees.UserList.Where(x => !Employees.Any(e => e.UserId == x.Userid)).Select(x => new QYWechat_Employee
                        {
                            UserId = x.Userid!,
                            Mobile = x.Mobile ?? string.Empty,
                            Name = x.Name ?? string.Empty,
                            QRCodeURL = x.Qr_Code ?? string.Empty,
                            DepartmentIds = x.Department?.ToJsonString() ?? string.Empty,
                            Gender = x.Gender,
                            Open_userid = x.Open_Userid,
                            Status = (int)x.Status
                        }));
                    }
                }

                //将Employees更新到数据库中

                using (var db = _contextFactory.CreateDbContext())
                {
                    bool IsUpdateDB = false;//确认是否需更新数据库

                    var members = db.QYWechat_Employee.ToList();
                    var eIds = members.Select(x => x.UserId).ToList();

                    //添加不存在的记录
                    var newModel = Employees.Where(x => !eIds.Contains(x.UserId))
                        .Select(x => new QYWechat_Employee
                        {
                            UserId = x.UserId!,
                            Name = x.Name,
                            Mobile = x.Mobile,
                            QRCodeURL = x.QRCodeURL ?? string.Empty,
                            Gender = x.Gender,
                            Status = x.Status,
                            DepartmentIds = x.DepartmentIds,
                            Open_userid = x.Open_userid ?? string.Empty,
                            CreatedTime = DateTime.Now
                        })
                        .ToList();
                    if (newModel.Count > 0)
                    {
                        IsUpdateDB = true;
                        db.QYWechat_Employee.AddRange(newModel);
                    }
                    //更新已存在的记录
                    if (Employees.Any(x => eIds.Contains(x.UserId)))
                    {
                        IsUpdateDB = true;
                        Employees.Where(x => eIds.Contains(x.UserId)).ForEach((QYWechat_Employee x) =>
                        {
                            var temp = members.First(t => t.UserId == x.UserId);
                            temp.UserId = x.UserId!;
                            temp.Name = x.Name;
                            temp.Mobile = x.Mobile;
                            temp.QRCodeURL = x.QRCodeURL ?? string.Empty;
                            temp.Gender = x.Gender;
                            temp.Status = x.Status;
                            temp.DepartmentIds = x.DepartmentIds;
                            temp.Open_userid = x.Open_userid ?? string.Empty;
                        });
                    }
                    //删除退群的记录
                    var tempids = Employees.Select(x => x.UserId).ToList();
                    if (members.Any(x => !tempids.Contains(x.UserId)))
                    {
                        IsUpdateDB = true;
                        db.QYWechat_Employee.Where(x => !tempids.Contains(x.UserId)).ExecuteDelete();
                    }

                    if (IsUpdateDB)
                        db.SaveChanges();
                }
                return (true, $"导入成功，共下载{Employees.Count}条人员数据，{(errors.Count == 0 ? "已全部导入" : $"部分导入失败，原因如下:{errors.ToJsonString()}")}");
            }
            return (departmentList.Result, departmentList.ErrMsg);
        }
        catch (Exception e)
        {
            _log.Error("企业微信：获取企业微信全量员工信息", Tools.GetErrMsg(e));
            return (false, e.Message);
        }
    }
    #endregion

    #region 由于项目需要大动，需要在IHrService/ISeekerService添加的方法临时写到这里吧
    /// <summary>
    /// 根据企微的群主ID，获取关联的顾问信息
    /// </summary>
    /// <param name="Mobile"></param>
    /// <returns></returns>
    /// <exception cref="BadRequestException"></exception>
    private User_Hr? GetHrByMobile(string QWUserId)
    {
        if (string.IsNullOrEmpty(QWUserId))
            return null;
        var binding = _context.User_QYWechat.Include(i => i.User_Hr).Where(x => x.QWUserId == QWUserId).FirstOrDefault();
        if (binding == null || binding.User_Hr == null)
            return null;
        else
            return binding.User_Hr;
    }

    /// <summary>
    /// 根据UnionID获取关联的求职者信息
    /// </summary>
    /// <param name="Mobile"></param>
    /// <returns></returns>
    /// <exception cref="BadRequestException"></exception>
    private List<User_OpenId>? GetSeekersByWechatUnionID(List<string> UnionID, StaffingContext? context = null)
    {
        if (UnionID == null || UnionID.Count == 0)
            return null;
        UnionID.RemoveAll(x => string.IsNullOrEmpty(x));
        if (UnionID.Count == 0)
            return null;

        context ??= _context;
        var uos = context.User_OpenId
            .AsNoTracking()
            .Where(x => UnionID.Contains(x.UnionId))
            .ToList();
        return uos;
    }
    /// <summary>
    /// 根据UnionID获取关联的求职者信息
    /// </summary>
    /// <param name="Mobile"></param>
    /// <returns></returns>
    /// <exception cref="BadRequestException"></exception>
    private User_OpenId? GetSeekersByWechatUnionID(string UnionID, StaffingContext? context = null)
    {
        if (string.IsNullOrEmpty(UnionID))
            return null;
        context ??= _context;
        var uos = context.User_OpenId
            .AsNoTracking()
            .Where(x => x.UnionId == UnionID)
            .FirstOrDefault();
        return uos;
    }
    #endregion
}
