using Config;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Staffing.Model.Hr.Kzg;
using Staffing.Core.Interfaces.Hr;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class KzgRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("kzg").WithTags("其他服务");
        group.MapGet("/user", (IKzgService rep) => rep.GetUser())
            .WithOpenApi(operation => new(operation) { Summary = "查询快手绑定情况" });

        group.MapGet("/dic/post", async ([AsParameters] GetKzgTree model, IKzgService rep) => await rep.GetKzgPost(model))
            .WithOpenApi(operation => new(operation) { Summary = "职位类别" });

        group.MapGet("/dic/post/tags", async ([AsParameters] GetKzgTree model, IKzgService rep) => await rep.GetKzgTag(model))
            .WithOpenApi(operation => new(operation) { Summary = "职位标签" });

        group.MapGet("/dic/city", async ([AsParameters] GetKzgTree model, IKzgService rep) => await rep.GetKzgRegion(model))
            .WithOpenApi(operation => new(operation) { Summary = "省市区" });

        group.MapGet("/post/bynkp", ([AsParameters] GetKsPostByNkp model, IKzgService rep) => rep.GetKsPostByNkp(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "诺快聘职位转快手" });

        group.MapPost("/xbb/ht/list", (GetXbbHt model, IKzgService rep) => rep.GetXbbHt(model))
            .WithOpenApi(operation => new(operation) { Summary = "查询销帮帮合同" });

        group.MapPost("/xbb/ht/mark", (SetXbbHt model, IKzgService rep) => rep.SetXbbHt(model))
            .WithOpenApi(operation => new(operation) { Summary = "销帮帮合同回执" });

        group.MapPost("/xbb/ht/notify", async (XbbHtNotify model, IKzgService rep) => await rep.XbbHtNotify(model))
            .WithOpenApi(operation => new(operation) { Summary = "销帮帮合同通知" });

        group.MapPost("/post/sync", async (SendPostToKzg model, IKzgService rep) => await rep.SendPostToKzg(model))
            .WithOpenApi(operation => new(operation) { Summary = "发送职位到快手" });

        group.MapGet("/recommendents/outlets", async (IKzgService rep) => await rep.GetOutlets())
            .WithOpenApi(operation => new(operation) { Summary = "获取诺聘网点列表" });

        group.MapGet("/recommendents/dic/remark", (IKzgService rep) => rep.GetRemarkDic())
            .WithOpenApi(operation => new(operation) { Summary = "企业商机备注字典" });

        group.MapGet("/recommendents/hrs", (IKzgService rep) => rep.GetRecommendHrs())
            .WithOpenApi(operation => new(operation) { Summary = "企业商机顾问列表" });

        group.MapPost("/recommendents/list", async (GetRecommendEnts model, IKzgService rep) => await rep.GetRecommendEnts(model))
            .WithOpenApi(operation => new(operation) { Summary = "企业商机列表" });

        group.MapPost("/recommendents/update", (UpdateRecommendEnts model, IKzgService rep) => rep.UpdateRecommendEnts(model))
            .WithOpenApi(operation => new(operation) { Summary = "更新企业商机" });

        group.MapPost("/recommendents/npwplist", async (GetNPEntJobPositions model, IKzgService rep) => await rep.GetNPEntJobPositions(model))
            .WithOpenApi(operation => new(operation) { Summary = "查看诺聘岗位" });

        group.MapPost("/recommendents/npdeliverlist", async (GetNPDeliver model, IKzgService rep) => await rep.GetNPDeliverList(model))
            .WithOpenApi(operation => new(operation) { Summary = "查看诺聘岗位的投递记录" });

        group.MapPost("/recommendents/npsaveusercontact", async (SaveNPUserContact model, IKzgService rep, RequestContext user) =>
        {
            if (!string.IsNullOrEmpty(model.Remark) && model.Remark.Length > 500)
                throw new BadRequestException($"备注限制500字符，请简化或分多条保存!");
            return await rep.SaveNPUserContact(model, user.Id, user.Name);
        }).WithOpenApi(operation => new(operation) { Summary = "新增与求职者的沟通记录" });

        group.MapPost("/recommendents/npgetusercontact", async (GetNPUserContact model, IKzgService rep, RequestContext user) =>
            await rep.GetNPUserContactList(model, user.Id))
            .WithOpenApi(operation => new(operation) { Summary = "获取与求职者的沟通记录(仅获取快聘操作的记录)" });
    }
}