﻿// using System;
// using Config.Enums;
// using EntityFrameworkCore.AutoHistory.Extensions;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.EntityFrameworkCore.ChangeTracking;
// using Microsoft.EntityFrameworkCore.Metadata.Builders;
// using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
// using ServiceStack.Text;

// namespace Staffing.Entity.Nyk;

// public partial class NykContext : DbContext
// {
//     public NykContext(DbContextOptions<NykContext> options)
//         : base(options)
//     {

//     }

//     // //主
//     // public virtual DbSet<UserApplyArchive> UserApplyArchive { get; set; } = default!;
//     // public virtual DbSet<OldApply> OldApply { get; set; } = default!;
//     // public virtual DbSet<OldApply2> OldApply2 { get; set; } = default!;
//     // public virtual DbSet<UserLoginRecords> UserLoginRecords { get; set; } = default!;
//     // public virtual DbSet<UserAccount> UserAccount { get; set; } = default!;
//     // public virtual DbSet<UserCertificate> UserCertificate { get; set; } = default!;

//     protected override void OnModelCreating(ModelBuilder modelBuilder)
//     {
//         // modelBuilder.Entity<UserApplyArchive>().HasQueryFilter(b => b.IsRemove == 0);
//         // modelBuilder.Entity<OldApply>().HasQueryFilter(b => b.IsRemove == 0);

//         // modelBuilder.Entity<VEmployee>().HasNoKey().ToView("v_employee");

//         // modelBuilder.Entity<Contract>()
//         //     .HasOne(x => x.Customer)
//         //     .WithMany()
//         //     .HasForeignKey(x => x.CustomerId);

//         // modelBuilder.Entity<Contract>()
//         //     .HasOne(x => x.SignerModel)
//         //     .WithOne().IsRequired(false)
//         //     .HasForeignKey<Contract>(x => x.Signer)
//         //     .HasPrincipalKey<VEmployee>(x => x.EmployeeCode);

//         // modelBuilder.Entity<ContractProductRelation>()
//         //     .HasOne(x => x.Contract)
//         //     .WithMany(x => x.ContractProductRelation)
//         //     .HasForeignKey(x => x.ContractId);
//     }
// }
