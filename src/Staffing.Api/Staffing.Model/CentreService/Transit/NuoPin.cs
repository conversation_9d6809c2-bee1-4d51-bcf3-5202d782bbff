namespace Staffing.Model.CentreService.Transit;

public class PostNotify
{
    /// <summary>
    /// 职位Id
    /// </summary>
    public string PostId { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 企业Id
    /// </summary>
    public string EntId { get; set; } = default!;

    /// <summary>
    /// 企业名称
    /// </summary>
    public string EntName { get; set; } = default!;

    /// <summary>
    /// 发布时间
    /// </summary>
    public string CreateTime { get; set; } = default!;
}

public class ResumeNotify
{
    /// <summary>
    /// 投递Id
    /// </summary>
    public string DeliverId { get; set; } = default!;

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string UserName { get; set; } = default!;

    /// <summary>
    /// 用户手机号
    /// </summary>
    public string UserMobile { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 企业Id
    /// </summary>
    public string EntId { get; set; } = default!;

    /// <summary>
    /// 企业名称
    /// </summary>
    public string EntName { get; set; } = default!;

    /// <summary>
    /// 投递时间
    /// </summary>
    public string CreateTime { get; set; } = default!;
}