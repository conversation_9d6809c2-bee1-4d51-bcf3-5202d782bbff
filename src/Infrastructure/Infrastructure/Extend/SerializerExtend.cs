using System.Text.Json;
using System.Text.Json.Serialization;

namespace Infrastructure.Extend;

public class ServiceStackJsonConverter : JsonConverter<object>
{
    public override bool CanConvert(Type typeToConvert)
    {
        return true;
    }

    public override object Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        using (var doc = JsonDocument.ParseValue(ref reader))
        {
            var json = doc.RootElement.GetRawText();
            return ServiceStack.Text.JsonSerializer.DeserializeFromString(json, typeToConvert);
            // return JsonConvert.DeserializeObject(json, typeToConvert);
        }
    }

    public override void Write(Utf8JsonWriter writer, object value, JsonSerializerOptions options)
    {
        // var json = JsonConvert.SerializeObject(value);
        var json = ServiceStack.Text.JsonSerializer.SerializeToString(value);
        using (var doc = JsonDocument.Parse(json))
        {
            doc.WriteTo(writer);
        }
    }
}
