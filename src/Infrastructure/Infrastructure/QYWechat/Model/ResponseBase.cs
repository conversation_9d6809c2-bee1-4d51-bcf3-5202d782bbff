﻿using System;

namespace Infrastructure.QYWechat.Model
{
    /// <summary>
    /// 返回的基类
    /// </summary>
    public class ResponseBase
    {
        public bool Result { get; set; }
        /// <summary>
        /// 错误码
        /// *0=成功，其他值表示失败
        /// </summary>
        public int ErrCode { get; set; }
        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrMsg { get; set; }
    }

    /// <summary>
    /// 分页返回的基类
    /// </summary>
    public class ResponseBase_Page : ResponseBase
    {
        /// <summary>
        /// 分页游标，下次请求时填写以获取之后分页的记录。如果该字段返回空则表示已没有更多数据
        /// </summary>
        public string? Next_Cursor { get; set; }
    }

    public class Response_AccessToken : ResponseBase
    {
        /// <summary>
        /// 获取到的凭证
        /// </summary>
        public string? Access_Token { get; set; }
        /// <summary>
        /// 有效时间，单位：秒
        /// </summary>
        public int Expires_In { get; set; }
        /// <summary>
        /// 有效截止时间
        /// </summary>
        public DateTime Expires_Time { get; set; } = DateTime.Now;
    }
}
