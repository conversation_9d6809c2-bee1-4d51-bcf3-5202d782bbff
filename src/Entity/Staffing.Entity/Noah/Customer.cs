﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Noah;

/// <summary>
/// 客户
/// </summary>
[Table("customer")]
public class Customer
{
    /// <summary>
    /// ID
    /// </summary>
    [Key]
    public long CustomerId { get; set; }
    /// <summary>
    /// 编号
    /// </summary>
    public string? CustomerCode { get; set; }
    /// <summary>
    /// 销帮帮的客户id
    /// </summary>
    public string? DataId { get; set; }
    /// <summary>
    /// 客户名称
    /// </summary>
    public string? Name { get; set; }
    /// <summary>
    /// 简称
    /// </summary>
    public string? Abbreviation { get; set; }
    /// <summary>
    /// 证件号
    /// </summary>
    public string? IdNumber { get; set; }
    /// <summary>
    /// 财务编码
    /// </summary>
    public string? FinanceNo { get; set; }
    /// <summary>
    /// 电话号码
    /// </summary>
    public string? Tel { get; set; }
    /// <summary>
    /// 电话号码2
    /// </summary>
    public string? Tel2 { get; set; }
    /// <summary>
    /// 电话3
    /// </summary>
    public string? Tel3 { get; set; }
    /// <summary>
    /// 传真
    /// </summary>
    public string? Fax { get; set; }
    /// <summary>
    /// 47cdb7de-8ae8-ce7d-92e0-ea70ad174486 金融业;dd9b0469-4858-457c-5c9f-c1282bb81141 政府机关及社会组织;739c9b3b-0fe9-cda0-c67d-d059fd14f86d 制造业-医药制造类;b562eb59-2c09-6e6b-f7da-7a1c6d6b975d 制造业-节能环保类;6979f3d6-91f3-0f6a-5d30-2061ead147d1 制造业-其他类;833c2ce2-1786-0c86-2a29-d9a26256e7ff 批发和零售业;cfc8132a-c970-cc04-351b-82ae9504a6e4 信息传输、软件和信息技术服务业;88e62a36-7f38-73b0-1756-ed72ef0b34b3 交通运输、仓储和邮政业;4becd87a-de6f-ff44-7205-924f4ac638ae 教育行业;4766f040-6260-7f44-069f-43a8528eb43f 电力、热力、燃气、水等生产和供应业;6dcfb688-195e-a3c1-9e94-5d5a99e51d24 房地产业;3f9e2cd3-ae5f-f3ae-1bea-0f0c18dbbf79 建筑业;3958fed6-b0b9-412d-b720-171fa73aaf86 卫生和社会工作;f535795e-0708-da49-0fbf-a66a9113ca84 住宿和餐饮业;7afdda25-1665-4bfa-96e8-335e11a89300 租赁和商务服务业;0e27a96c-5d0d-92e9-0deb-d08e3628bc23 科学研究和技术服务业;d3addd63-3f88-12b6-d321-b4b3b1fa52d2 水利、环境和公共设施管理业;0fa877a8-d486-fa63-ee19-f1412fcc3c25 文化、体育和娱乐业;de4cb176-ae28-bcf7-9459-5a842b7bb910 居民服务、修理和其他服务业;09475214-b7b2-9a62-90ba-7a5674558511 农、林、牧、渔业;6e0ebace-44fd-d74e-7a64-153cb542bc3f 采矿业;edb9e341-75f7-c077-7753-32e6c93452ed 个人客户
    /// </summary>
    public string? Industry { get; set; }
    /// <summary>
    /// 6c3d3110-cadd-a95f-04d1-aa4a35208c05 20人以下;bdac7b0c-6d17-dd63-9cab-0ad0721bb14e 20-99人;b998059e-07e2-7482-5ed7-fb43065b4dd0 100-499人;c9590813-228e-8729-7ca2-bc9c13d4282e 500-999人;2543823d-8a1f-1764-0981-321e0d7f145d 1000人以上
    /// </summary>
    public string? StaffSize { get; set; }
    /// <summary>
    /// 1独立开发；2营销活动；3客户介绍；4媒体宣传；5电话来访
    /// </summary>
    public string? Source { get; set; }
    /// <summary>
    /// 74313a76-363e-7836-e160-9b8190682e30 合作渠道;49cb2da3-4e0d-f8d1-e0f6-2fca07af88b6 内部客户;c9be93fc-3199-09b4-2c1c-2163a6e20192 合作客户;24f493ed-5d97-d58e-5d21-da72fe8e3046 外部客户
    /// </summary>
    public string? Classify { get; set; }
    /// <summary>
    /// 1事业部级；2部门级（年收入20万或人数达到500人或名牌企业为事业部级客户，否则为部门级）3.待分级
    /// </summary>
    public string? Grade { get; set; }
    /// <summary>
    /// 20298cc63bba4958bc1a5ea70e22a993 国有企业;9b65b47c7fed479593b8baefe67b7a55 民营企业;710a04eba9f044848605c5bfcd237184 事业单位;c2668dddfff149859500e57ef0c09cf9 合资企业;4c4e4541711a4ff8b8d6eae6a1c7ddb8 股份制企业;a953647b42934337875d91c11b0638a6 上市公司;f443071766bd4824839477619c471ad7 办事处;7f6fc7d5b145477f857bedfc6fc94744 外商独资;e98bfeb8fe794c02a59faf1bc8b67d2c 个人客户;d16a4830-ac3c-eea5-b531-af564f60ee95 企业客户;abafb483-98c4-4a56-26ea-ee8756521c5f 其他
    /// </summary>
    public string? Nature { get; set; }
    /// <summary>
    /// 1很高，2较高，3一般，4较低，5很低
    /// </summary>
    public string? ValueAssessment { get; set; }
    /// <summary>
    /// 1AAA（特优），2AA（优），3A（良），4B(一般)，5C（差）
    /// </summary>
    public string? RelationGrade { get; set; }
    /// <summary>
    /// 1一星级客户，2二星级客户，3三星级客户
    /// </summary>
    public string? Level { get; set; }
    /// <summary>
    /// 1普通客户，2VIP客户，3合作伙伴，4失效客户
    /// </summary>
    public string? Type { get; set; }
    /// <summary>
    /// 主要包括客户主营业务、年销售额、发展类型等信息
    /// </summary>
    public string? Intro { get; set; }
    /// <summary>
    /// 法人代表
    /// </summary>
    public string? LealPerson { get; set; }
    /// <summary>
    /// 注册资本
    /// </summary>
    public string? RegisteredCapital { get; set; }
    /// <summary>
    /// 组织机构代码
    /// </summary>
    public string? OrganizationCode { get; set; }
    /// <summary>
    /// 不超过9个文件，每个文件不超过20m，当变更营业执照时 请继续上传 不要删除原来的营业执照附件
    /// </summary>
    public string? BusinessLicense { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    /// <summary>
    /// 当前用户，不可修改
    /// </summary>
    public string? CreateBy { get; set; }
    /// <summary>
    /// 部门员工树上勾选，可多选
    /// </summary>
    public string? Principal { get; set; }
    /// <summary>
    /// 协同人
    /// </summary>
    public string? CollaborativePeople { get; set; }
    /// <summary>
    /// 创建人的所属部门
    /// </summary>
    public string? DeptCode { get; set; }
    /// <summary>
    /// 历史数据
    /// </summary>
    public string? HistoricalData { get; set; }
    /// <summary>
    /// 省
    /// </summary>
    public int? Province { get; set; }
    /// <summary>
    /// 市
    /// </summary>
    public int? City { get; set; }
    /// <summary>
    /// 区
    /// </summary>
    public int? District { get; set; }
    /// <summary>
    /// 街道(销帮帮使用高德地图)
    /// </summary>
    public string? Street { get; set; }
    /// <summary>
    /// 所在位置的经纬度
    /// </summary>
    public string? Location { get; set; }
    /// <summary>
    /// 邮编
    /// </summary>
    public string? Postcode { get; set; }
    /// <summary>
    /// 客户网址
    /// </summary>
    public string? Url { get; set; }
    /// <summary>
    /// 1位于公海池；2已捞取
    /// </summary>
    public string? Status { get; set; }
    /// <summary>
    /// 公海池状态变更时间
    /// </summary>
    public DateTime? StatusUpdateTime { get; set; }
    /// <summary>
    /// 1销帮帮；2数字诺亚
    /// </summary>
    public string? SourceSystem { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 删除标志：0正常；1：删除
    /// </summary>
    public string? DelFlag { get; set; }
    /// <summary>
    /// 更新人
    /// </summary>
    public long? UpdateBy { get; set; }
    /// <summary>
    /// 最后跟进时间
    /// </summary>
    public DateTime? FollowTime { get; set; }
    /// <summary>
    /// 归档状态:1.归档 2.未归档;404:未知状态
    /// </summary>
    public string? ArchivingStatus { get; set; }
    /// <summary>
    /// 分配时间
    /// </summary>
    public DateTime? AllocateTime { get; set; }
}