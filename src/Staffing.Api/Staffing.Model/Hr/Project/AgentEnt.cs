﻿using Config.CommonModel;
using Config.CommonModel.Business;

namespace Staffing.Model.Hr.Project;

public class UpdateAgentEntResponse
{
    /// <summary>
    /// 代理企业Id
    /// </summary>
    public string? AgentEntId { get; set; }
}

public class GetAgentEntList : QueryRequest
{
    /// <summary>
    /// 搜索
    /// </summary>
    public string? Search { get; set; }
}

public class GetAgentEntListResponse : QueryResponse
{
    public List<GetAgentEntListDetail> Rows { get; set; } = default!;
}

public class GetAgentEntListDetail : GetAgentEntDetail
{
}

public class GetAgentEntLess
{
    /// <summary>
    /// 搜索
    /// </summary>
    public string? Search { get; set; }
}

public class GetAgentEntLessResponse
{
    /// <summary>
    /// 企业列表
    /// </summary>
    public List<GetAgentEntLessDetail> Rows { get; set; } = default!;
}

public class GetAgentEntLessDetail
{
    /// <summary>
    /// Id
    /// </summary>
    public string? AgentEntId { get; set; }

    /// <summary>
    /// 名字
    /// </summary>
    public string Name { get; set; } = string.Empty;
}