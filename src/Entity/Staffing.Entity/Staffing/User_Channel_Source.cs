﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing
{
    /// <summary>
    /// 渠道来源
    /// </summary>
    [Table("user_channel_source")]
    public class User_Channel_Source
    {
        /// <summary>
        /// 获取或设置Id。自增
        /// </summary>
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }

        /// <summary>
        /// 获取或设置渠道名称。
        /// </summary>
        public string Name { get; set; } = default!;

        /// <summary>
        /// 获取或设置创建时间。
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 获取或设置更新时间。
        /// </summary>
        public DateTime UpdatedTime { get; set; }
    }
}
