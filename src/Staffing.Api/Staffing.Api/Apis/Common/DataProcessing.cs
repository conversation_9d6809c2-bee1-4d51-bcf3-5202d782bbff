using System.Net.NetworkInformation;
using Config;
using Config.CommonModel;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.CommonRepository.ZhaoPinYun;
using Infrastructure.CommonService;
using Infrastructure.CommonService.ShareProfit;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Infrastructure.Proxy;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.Options;
using ServiceStack.Text;
using Staffing.Core.Interfaces;
using Staffing.Core.Interfaces.Common;
using Staffing.Entity;
using Staffing.Entity.Elasticsearch;
using Staffing.Entity.Staffing;

namespace Staffing.Api.Apis.Common;

[Service(ServiceLifetime.Transient)]
public class DataProcessingRouter : ICommonRouterBase
{
    private const string skey = "e4b0c4423f8b4e2b8a5f7d8c9a1e6f3d";
    private readonly EsHelper _esHelper;
    private readonly CommonProjectService _commonProjectService;
    public DataProcessingRouter(EsHelper esHelper, CommonProjectService commonProjectService)
    {
        _commonProjectService = commonProjectService;
        _esHelper = esHelper;
    }
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("dataprocessing").WithTags("数据处理");
        group.MapGet("/initinterviewer", async ([FromQuery] string key, StaffingContext context) =>
        {
            if (key != skey)
                return "error";

            var projectIds = context.Project.Where(x => x.AcceptOrder == Config.Enums.RecruitAcceptOrderEnum.已经接单).Select(s => new { s.ProjectId, Phone = s.User_Hr.User.Mobile, s.HrId }).ToList();
            var interviewerIds = context.Project_Interviewer.Select(s => new { s.ProjectId, s.Phone }).ToList();

            var projs = projectIds.Where(w => !interviewerIds.Any(a => a.ProjectId == w.ProjectId && a.Phone == w.Phone)).ToList();

            foreach (var item in projs.Chunk(100))
            {
                var hrIds = item.Select(s => s.HrId).ToList();
                var hrs = context.User_Hr.Where(w => hrIds.Contains(w.UserId))
                    .Select(s => new
                    {
                        s.NickName,
                        s.UserId,
                        s.EMail,
                        s.Post
                    }).ToDictionary(x => x.UserId);

                var interviewers = item.Select(s => new Project_Interviewer
                {
                    ProjectId = s.ProjectId,
                    Phone = s.Phone,
                    Name = hrs[s.HrId!].NickName,
                    Mail = hrs[s.HrId!].EMail ?? string.Empty,
                    OfficeAddress = string.Empty,
                    Post = hrs[s.HrId!].Post ?? string.Empty,
                }).ToList();

                context.AddRange(interviewers);
                context.SaveChanges();
            }

            await Task.FromResult(1);
            return "成功";
        }).WithOpenApi(operation => new(operation) { Summary = "初始化面试官" })
        .WithMetadata(new ApiMeta());

        // 初始化人才库
        group.MapGet("/inittalent", async (string key, StaffingContext context) =>
        {
            if (key != skey)
                return "error";

            // 查询所有人才库Id
            var talentIds = context.Talent_Platform.Select(s => s.Id).ToArray();

            //1000个一组，插入redis 
            foreach (var item in talentIds.Chunk(1000))
            {
                MyRedis.Client.SAdd(SubscriptionKey.TalentTableChange, item
                .Select(s => new Sub_TalentTableChange { TalentId = s }).ToArray());
                await Task.Delay(10);
            }

            return "成功";
        }).WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "初始化人才库" });

        // 初始化简历库
        group.MapGet("/initresume", async (string key, StaffingContext context) =>
        {
            if (key != skey)
                return "error";

            // 查询所有简历库Id
            var resumeIds = context.Talent_Virtual.Select(s => s.Id).ToArray();

            //1000个一组，插入redis 
            foreach (var item in resumeIds.Chunk(1000))
            {
                MyRedis.Client.SAdd(SubscriptionKey.VirtualTalentTableChange, item
                .Select(s => new Sub_VirtualTableChange { TalentId = s }).ToArray());
                await Task.Delay(10);
            }

            return "成功";
        }).WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "初始化简历库" });

        // 初始化人才线索池
        group.MapGet("/inittalentclue", async (string key, StaffingContext context) =>
        {
            if (key != skey)
                return "error";

            // 查询所有人才线索池Id
            var talentClueIds = context.Talent_Resume.Select(s => s.Id).ToArray();

            //1000个一组，插入redis 
            foreach (var item in talentClueIds.Chunk(1000))
            {
                MyRedis.Client.SAdd(SubscriptionKey.TalentResumeChange,
                item.ToArray());
                await Task.Delay(10);
            }

            return "成功";
        }).WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "初始化人才线索池" });

        group.MapGet("/initpoststatistics", async ([FromQuery] string key, StaffingContext context) =>
        {
            if (key != skey)
                return "error";

            var recruitIds = context.Recruit.GroupBy(g => g.Post_Delivery.PostId)
                .Select(s => s.Max(m => m.RecruitId)).ToArray();

            if (recruitIds?.Count() > 0)
                MyRedis.Client.SAdd(SubscriptionKey.PostStatis, recruitIds);

            var teamBountyIds = context.Post_Bounty.Select(s => s.Id).ToArray();

            if (teamBountyIds?.Count() > 0)
                MyRedis.Client.SAdd(SubscriptionKey.TeamBountyChange, teamBountyIds);

            await Task.FromResult(1);
            return "成功";
        }).WithOpenApi(operation => new(operation) { Summary = "初始化岗位统计" })
        .WithMetadata(new ApiMeta());

        // 初始化简历处理时长
        group.MapGet("/resumeprocesstime", async (string key, StaffingContext context) =>
        {
            if (key != skey)
                return "error";

            // 查询所有简历库Id
            var resumeIds = context.Recruit.Select(s => s.RecruitId).ToArray();

            //1000个一组，插入redis 
            foreach (var item in resumeIds.Chunk(1000))
            {
                MyRedis.Client.SAdd(SubscriptionKey.ResumeProcessTime, item
                .Select(s => new Sub_Recruit_StatusChange { RecruitId = s }).ToArray());
                await Task.Delay(10);
            }

            return "成功";
        }).WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "初始化简历库" });

        //测试一下get接口
        group.MapGet("/xxx", ([AsParameters] TestParaModel model, EsHelper _esHelper,
            IOptions<ConfigManager> _config, IHostEnvironment _hostingEnvironment, StaffingContext _context) =>
        {
            var rec = _context.Recruit.Where(x => x.HrId == "101")
                    .GroupBy(g => new { RecruitStatus = (Config.Enums.RecruitStatus?)g.Status, ProjectStatus = (Config.Enums.ProjectStatus?)g.Post_Delivery.Post.Project.Status })
                    .Select(s => new
                    {
                        s.Key,
                        Ct = s.Count()
                    }).ToList();
        })
        .WithMetadata(new ApiMeta());

        group.MapPost("/autopostsynctest", (CommonProjectService commonProjectService) => commonProjectService.PostSync("101", "319305381031472133", "182642219925139461"))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "岗位自动接单测试" });


        group.MapGet("/test", async (NdnApi _ndnApi) =>
        {
            // 查询数字诺亚帐套
            var ndnBooks = await _ndnApi.GetBooks(new Config.CommonModel.Ndn.GetBooks { });

            return ndnBooks;
        }).WithOpenApi(operation => new(operation) { Summary = "测试" })
        .WithMetadata(new ApiMeta());

        // 同步历史职位
        group.MapGet("/syncpost", (string key, IDbContextFactory<StaffingContext> staffingContextFactory,
        CommonProjectService _commonProjectService) =>
        {
            if (key != skey)
                return "error";
            using var context = staffingContextFactory.CreateDbContext();

            var posts = context.Post.ToList();

            foreach (var item in posts.Chunk(50))
            {
                using var context2 = staffingContextFactory.CreateDbContext();
                var postIds = item.Select(s => s.PostId).ToArray();
                var stages = context2.Post_Profit_Stage.Where(w => postIds.Contains(w.PostId)).ToList();

                foreach (var item2 in item)
                {
                    if (item2.Money == null || item2.Money <= 0)
                        continue;

                    if (!stages.Any(a => a.PostId == item2.PostId))
                    {
                        var stage = new Post_Profit_Stage
                        {
                            PostId = item2.PostId,
                            Amount = item2.Money.Value,
                            GuaranteeDays = item2.PaymentNode == ProjectPaymentNode.简历交付 ? 7 : (item2.PaymentDays ?? 0)
                        };

                        context2.Add(stage);
                    }

                    if (!item2.RewardType.HasValue)
                    {
                        if (item2.PaymentNode == ProjectPaymentNode.按天入职过保)
                            item2.RewardType = PostRewardType.长期结算;
                        else
                            item2.RewardType = PostRewardType.单次结算;
                    }

                    if (!item2.PaymentCycle.HasValue)
                        item2.PaymentCycle = PostPaymentCycle.天;
                }

                context2.SaveChanges();
            }

            return "成功";

        }).WithOpenApi(operation => new(operation) { Summary = "同步历史职位" })
        .WithMetadata(new ApiMeta());

        // 同步历史订单
        group.MapGet("/syncorder", async (string key, IDbContextFactory<StaffingContext> staffingContextFactory,
        ProfitService _profitService) =>
        {
            if (key != skey)
                return "error";
            using var context = staffingContextFactory.CreateDbContext();

            var recruits = context.Recruit.Where(x => !context.Post_Bounty
            .Any(a => a.RecruitId == x.RecruitId) && !string.IsNullOrEmpty(x.Post_Delivery.DeliveryId)
            && !string.IsNullOrEmpty(x.Post_Delivery.Post.PostId)).OrderBy(o => o.RecruitId).ToList();
            // 所有的招聘流程补订单，100个一组

            var settings = context.Sys_Settings.FirstOrDefault();

            if (settings == null)
                throw new BadRequestException("系统配置缺失");

            foreach (var item in recruits.Chunk(50))
            {
                using var context2 = staffingContextFactory.CreateDbContext();
                var recruitIds = item.Select(s => s.RecruitId).ToArray();

                // var recruitInfos2 = context2.Recruit.AsNoTracking()
                //     .Where(w => recruitIds.Contains(w.RecruitId))
                //     .Select(s => new
                //     {
                //         SeekerSource = s.User_Seeker.Source,
                //         s.RecruitId,
                //         s.HrId,
                //         TeamHrId = s.Post_Delivery.Post_Team.Project_Team.HrId,
                //         s.Post_Delivery.ChannelId,
                //         s.Post_Delivery.TeamPostId,
                //         s.Post_Delivery.Post.IsSalesCommission,
                //         s.FollowerId,
                //         ClueEntSpecific = s.Post_Delivery.Post_Team.Project_Team.User_Hr.Enterprise.Specific,
                //         ManagerEntSpecific = s.Post_Delivery.Post_Team.Project_Team.Project.User_Hr.Enterprise.Specific,
                //         SalesEntSpecific = s.Post_Delivery.Post_Team.Post.Project.SaleUser.Enterprise.Specific,
                //         FollowerEntSpecific = s.FollowerUser.Enterprise.Specific,
                //         s.ResumeBufferId,
                //         SaleUserId = s.Post_Delivery.Post_Team.Project_Team.Project.SaleUserId,
                //         PostId = s.Post_Delivery.PostId,
                //         Postame = s.Post_Delivery.Post.Name,
                //         SeekerId = s.User_Seeker.UserId,
                //         PaymentNode = s.Post_Delivery.Post.PaymentNode,
                //         PaymentDays = s.Post_Delivery.Post.PaymentDays,
                //         PaymentType = s.Post_Delivery.Post.Project.PaymentType,
                //         RewardType = s.Post_Delivery.Post.RewardType,
                //         PaymentCycle = s.Post_Delivery.Post.PaymentCycle,
                //         PaymentDuration = s.Post_Delivery.Post.PaymentDuration,
                //         Money = s.Post_Delivery.Post.Money,
                //     }).ToList();

                var recruitInfos = context2.Recruit.AsNoTracking()
                    .Where(w => recruitIds.Contains(w.RecruitId))
                    .Select(s => new
                    {
                        SeekerSource = s.User_Seeker.Source,
                        s.RecruitId,
                        s.HrId,
                        TeamHrId = s.Post_Delivery.Post_Team.Project_Team.HrId,
                        s.Post_Delivery.ChannelId,
                        s.Post_Delivery.TeamPostId,
                        s.Post_Delivery.Post.IsSalesCommission,
                        s.FollowerId,
                        ClueEntSpecific = s.Post_Delivery.Post_Team.Project_Team.User_Hr.Enterprise.Specific,
                        ManagerEntSpecific = s.Post_Delivery.Post_Team.Project_Team.Project.User_Hr.Enterprise.Specific,
                        SalesEntSpecific = s.Post_Delivery.Post_Team.Post.Project.SaleUser.Enterprise.Specific,
                        FollowerEntSpecific = s.FollowerUser.Enterprise.Specific,
                        s.ResumeBufferId,
                        SaleUserId = s.Post_Delivery.Post_Team.Project_Team.Project.SaleUserId,
                        PostId = s.Post_Delivery.PostId,
                        Postame = s.Post_Delivery.Post.Name,
                        SeekerId = s.User_Seeker.UserId,
                        PaymentNode = s.Post_Delivery.Post.PaymentNode,
                        PaymentDays = s.Post_Delivery.Post.PaymentDays,
                        PaymentType = s.Post_Delivery.Post.Project.PaymentType,
                        RewardType = s.Post_Delivery.Post.RewardType,
                        PaymentCycle = s.Post_Delivery.Post.PaymentCycle,
                        PaymentDuration = s.Post_Delivery.Post.PaymentDuration,
                        Money = s.Post_Delivery.Post.Money,
                    }).ToList();

                var postIds = recruitInfos.Select(s => s.PostId).Distinct().ToArray();

                var fullPostStages = context2.Post_Profit_Stage.Where(f => postIds.Contains(f.PostId))
                .Select(s => new
                {
                    // BountyId = postBounty.Id,
                    // Status = BountyStageStatus.交付中,
                    s.PostId,
                    GuaranteeDays = s.GuaranteeDays,
                    Money = s.Amount
                }).ToList();

                foreach (var item2 in item)
                {
                    var recruitInfo = recruitInfos.First(w => w.RecruitId == item2.RecruitId);

                    // 新建订单
                    // 判断用户是否诺亚公司，采用不同的分佣规则

                    var managerIsNoah = recruitInfo.ManagerEntSpecific?.Contains(EntSpecific.Noah) == true;
                    var clueIsNoah = recruitInfo.ClueEntSpecific?.Contains(EntSpecific.Noah) == true;
                    var salesIsNoah = recruitInfo.SalesEntSpecific?.Contains(EntSpecific.Noah) == true;

                    BountyConfig? managerConfig;
                    BountyConfig? clueConfig;
                    BountyConfig? salesConfig;

                    if (recruitInfo.IsSalesCommission)
                    {
                        salesConfig = salesIsNoah ? settings.NoahFull : settings.PlatformFull;
                        managerConfig = managerIsNoah ? settings.NoahFull : settings.PlatformFull;
                        clueConfig = clueIsNoah ? settings.NoahFull : settings.PlatformFull;
                    }
                    else
                    {
                        salesConfig = salesIsNoah ? settings.NoahNoSale : settings.PlatformNoSale;
                        managerConfig = managerIsNoah ? settings.NoahNoSale : settings.PlatformNoSale;
                        clueConfig = clueIsNoah ? settings.NoahNoSale : settings.PlatformNoSale;
                    }

                    var platType = recruitInfo.SeekerSource switch
                    {
                        RegisterSource.KsTalent => TeamBountySource.快招工,
                        RegisterSource.NuoPin => TeamBountySource.诺聘平台,
                        RegisterSource.Applet => TeamBountySource.微信小程序,
                        RegisterSource.KsApplet => TeamBountySource.快手小程序,
                        RegisterSource.Upload => TeamBountySource.简历上传,
                        RegisterSource.SeekerDyApplet => TeamBountySource.抖音小程序,
                        _ => TeamBountySource.其他
                    };

                    var postBounty = new Post_Bounty
                    {
                        ResumeBufferId = recruitInfo.ResumeBufferId,
                        SaleUserId = recruitInfo.SaleUserId,
                        RecruitId = recruitInfo.RecruitId,
                        TeamPostId = recruitInfo.TeamPostId,
                        PostId = recruitInfo.PostId,
                        PostName = recruitInfo.Postame,
                        SeekerId = recruitInfo.SeekerId,
                        HrId = recruitInfo.HrId!,// 主创顾问id
                        TeamHrId = recruitInfo.TeamHrId,// 协同顾问id
                        PaymentNode = recruitInfo.PaymentNode,
                        PaymentDays = recruitInfo.PaymentDays,
                        PaymentType = recruitInfo.PaymentType,
                        RewardType = recruitInfo.RewardType ?? PostRewardType.单次结算,
                        PaymentCycle = recruitInfo.PaymentCycle,
                        PaymentDuration = recruitInfo.PaymentDuration,
                        Money = recruitInfo.Money ?? 0,
                        Status = BountyStatus.交付中,
                        Source = platType,
                        ChannelId = recruitInfo.ChannelId
                    };

                    postBounty.SalesRate = salesConfig!.Sales;
                    postBounty.ManagerRate = managerConfig!.Manager;
                    postBounty.ClueRate = clueConfig!.Clue;

                    context2.Add(postBounty);

                    var postStages = fullPostStages.Where(f => f.PostId == postBounty.PostId)
                    .Select(s => new Post_Bounty_Stage
                    {
                        BountyId = postBounty.Id,
                        Status = BountyStageStatus.交付中,
                        GuaranteeDays = s.GuaranteeDays,
                        Money = s.Money
                    }).ToList();

                    context2.AddRange(postStages);

                    // 处理订单状态
                    var needStockOut = false;
                    var needStockIn = false;
                    DateTime? guaranteeStartDate = null;

                    switch (recruitInfo.PaymentNode)
                    {
                        case ProjectPaymentNode.入职过保:
                            needStockOut = item2.Status == RecruitStatus.Induction;
                            needStockIn = item2.Status == RecruitStatus.FileAway;
                            guaranteeStartDate = item2.InductionTime;
                            break;
                        case ProjectPaymentNode.简历交付:
                            needStockOut = new List<RecruitStatus?> {
                                RecruitStatus.InterviewerScreening,
                                RecruitStatus.Interview,
                                RecruitStatus.Offer,
                                RecruitStatus.Induction,
                                RecruitStatus.Contract
                            }.Contains(item2.Status);
                            // needStockIn = model.Recruit.Status == RecruitStatus.FileAway && model.Recruit.FileAway == RecruitFileAway.Invalid;
                            if (item2.Status != RecruitStatus.FileAway)
                            {
                                if (guaranteeStartDate == null)
                                    guaranteeStartDate = DateTime.Now;
                            }
                            break;
                    }

                    if (guaranteeStartDate.HasValue)
                    {
                        postBounty.GuaranteeStatus = GuaranteeStatus.未过保;
                        postBounty.GuaranteeStartDate = guaranteeStartDate;

                        postStages.ForEach(f =>
                        {
                            f.GuaranteeStatus = GuaranteeStatus.未过保;
                        });
                    }

                    if (needStockOut && postBounty.IfStockOut != 1)
                    {
                        postBounty.IfStockOut = 1;
                    }

                    if (item2.Status == RecruitStatus.FileAway)
                    {
                        postBounty!.Status = BountyStatus.结束;
                        postBounty!.UpdatedTime = DateTime.Now;

                        postStages.ForEach(f =>
                        {
                            f.Status = BountyStageStatus.交付完成;
                        });
                    }

                    // 如果不是初筛，补充邀面人
                    if (item2.Status != RecruitStatus.HrScreening)
                    {
                        var followerId = item2.FollowerId;
                        var followerEntSpecific = recruitInfo.FollowerEntSpecific;
                        if (string.IsNullOrEmpty(followerId))
                        {
                            followerId = recruitInfo.TeamHrId;
                            followerEntSpecific = recruitInfo.ClueEntSpecific;
                        }

                        item2.FollowerId = followerId;
                        item2.AcceptOrder = RecruitAcceptOrderEnum.已经接单;



                        var followerIsNoah = followerEntSpecific?.Contains(EntSpecific.Noah) == true;

                        BountyConfig? followerConfig;
                        if (recruitInfo.IsSalesCommission)
                            followerConfig = followerIsNoah ? settings.NoahFull : settings.PlatformFull;
                        else
                            followerConfig = followerIsNoah ? settings.NoahNoSale : settings.PlatformNoSale;

                        var followerRate = followerConfig!.Follower;

                        // 计算平台佣金，减去销售佣金和经理佣金和线索佣金和邀面佣金
                        var platformRate = 1 - postBounty.SalesRate - postBounty.ManagerRate - postBounty.ClueRate - followerRate;
                        platformRate = platformRate.ToFixed(2);


                        if (platformRate < 0 || platformRate >= 1)
                            throw new Exception("佣金比例错误");

                        platformRate = platformRate < 0 ? 0 : platformRate;
                        platformRate = platformRate > 1 ? 1 : platformRate;

                        postBounty.FollowerId = followerId;
                        postBounty.FollowerRate = followerRate;
                        postBounty.PlatformRate = platformRate;
                        postBounty.UpdatedTime = DateTime.Now;
                    }
                }

                context2.SaveChanges();
                await Task.Delay(50);
            }

            return "成功";
        }).WithOpenApi(operation => new(operation) { Summary = "同步历史订单" })
        .WithMetadata(new ApiMeta());
    }
}

public class TestParaModel
{
    public DateTime? Date { get; set; }
    public int? Num { get; set; }
}