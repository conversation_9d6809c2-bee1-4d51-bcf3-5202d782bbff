using System.Security.Cryptography;
using System.Text;
using Config;
using Config.CommonModel.Xbb;
using Config.Enums;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using ServiceStack.Text;

namespace Infrastructure.Proxy;

/// <summary>
/// 销帮帮
/// </summary>
[Service(ServiceLifetime.Transient)]
public class InternalApi
{
    private readonly ConfigManager _config;
    private readonly LogManager _log;
    public InternalApi(IOptionsSnapshot<ConfigManager> config, LogManager log)
    {
        _config = config.Value;
        _log = log;
    }

    /// <summary>
    /// 内部请求
    /// </summary>
    /// <typeparam name="TReturn"></typeparam>
    /// <param name="type"></param>
    /// <param name="route"></param>
    /// <param name="obj"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<TReturn> RequestAsync<TReturn>(InternalSerciceType type, string route, object? obj = null) where TReturn : class
    {
        var domain = _config.InternalService?.Domain;
        var result = await RetryHelper.Do(async () =>
        {
            var url = $"{domain}/{type.ToString().ToLower()}/{route}";

            var rstStr = await url
                .WithOAuthBearerToken(_config.InternalService?.LocalServiceKeys?.Settlement)
                .PostJsonAsync(obj)
                .ReceiveString();

            TReturn? rst = null;
            try
            {
                rst = JsonSerializer.DeserializeFromString<TReturn>(rstStr);
            }
            catch (Exception e)
            {
                throw new Exception($"内部请求返回内容解析出错：{Tools.GetErrMsg(e)}", new Exception(rstStr));
            }

            return rst;
        });

        return result;
    }
}

