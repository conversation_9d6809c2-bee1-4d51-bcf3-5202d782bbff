﻿using Config.CommonModel.ThirdTalentInfo;
using Config.Enums;
using NetTopologySuite.Geometries;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 第三方简历池：快招工，诺聘，导入简历等
/// </summary>
[Table("resume_buffer")]
public class Resume_Buffer
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();
    /// <summary>
    /// 第三方表主键
    /// </summary>
    public string ResumeId { get; set; } = default!;
    /// <summary>
    /// 来源：0-快招工，1-导入
    /// </summary>
    public ThirdPlatType Source { get; set; } = ThirdPlatType.快招工;
    /// <summary>
    /// 0-待筛选，1-有效线索，2-无效线索
    /// </summary>
    public int Valid { get; set; } = 0;
    /// <summary>
    /// 诺快聘职位名称
    /// </summary>
    public string PostName { get; set; } = default!;
    /// <summary>
    /// 诺快聘职位Id-TeamPostId
    /// </summary>
    public string PostId { get; set; } = default!;
    /// <summary>
    /// 报名时间/导入时间
    /// </summary>
    public DateTime ApplyTime { get; set; }
    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; } = default!;
    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }
    /// <summary>
    /// 性别
    /// </summary>
    public string? Sex { get; set; }
    /// <summary>
    /// 生日
    /// </summary>
    public DateTime Birthday { get; set; }
    /// <summary>
    /// 年龄
    /// </summary>
    public int Age { get; set; }
    /// <summary>
    /// 电话
    /// </summary>
    public string Mobile { get; set; } = default!;
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? HrId { get; set; }
    /// <summary>
    /// 顾问名称
    /// </summary>
    public string? HrName { get; set; }
    /// <summary>
    /// 渠道商Id
    /// </summary>
    public string? ChannelId { get; set; }
    /// <summary>
    /// 渠道商名称
    /// </summary>
    public string? ChannelName { get; set; }
    /// <summary>
    /// 微信号码
    /// </summary>
    public string? WeChat { get; set; }
    /// <summary>
    /// QQ号码
    /// </summary>
    public string? QQ { get; set; }
    /// <summary>
    /// 邮箱号
    /// </summary>
    public string? Mailbox { get; set; }
    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; }
    /// <summary>
    /// 所在地CityName
    /// </summary>
    public string? Location { get; set; }
    /// <summary>
    /// 所在地经纬度
    /// </summary>
    public Point LocationPoint { get; set; } = new Point(0, 0);
    /// <summary>
    /// 最高学历
    /// </summary>
    public string? TopEducation { get; set; }
    /// <summary>
    /// 自我评价
    /// </summary>
    public string? SelfEvaluation { get; set; }
    /// <summary>
    /// 技能分析
    /// </summary>
    public ResumeBufferSkill Skills { get; set; } = new ResumeBufferSkill();
    /// <summary>
    /// 行业标签（来源小析职业标签字段）
    /// </summary>
    public List<string> IndustryLabel { get; set; } = new List<string>();
    /// <summary>
    /// 职位标签（来源小析职业标签字段）
    /// </summary>
    public List<string> PostLabel { get; set; } = new List<string>();
    /// <summary>
    /// 其他标签（来源小析职业标签字段）
    /// </summary>
    public List<string> OtherLabel { get; set; } = new List<string>();
    /// <summary>
    /// 求职期望
    /// </summary>
    public ResumeBufferHopes Hopes { get; set; } = new ResumeBufferHopes();
    /// <summary>
    /// 教育经历
    /// </summary>
    public ResumeBufferEducations Educations { get; set; } = new ResumeBufferEducations();
    /// <summary>
    /// 项目经历
    /// </summary>
    public ResumeBufferProjects Projects { get; set; } = new ResumeBufferProjects();
    /// <summary>
    /// 工作经历
    /// </summary>
    public ResumeBufferWorks Works { get; set; } = new ResumeBufferWorks();

    public DateTime CreatedTime { get; set; } = DateTime.Now;
    public DateTime UpdatedTime { get; set; } = DateTime.Now;
    public string? CreatedUser { get; set; }
    public string? UpdatedUser { get; set; }
    public int Deleted { get; set; } = 0;

}
