using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;
/// <summary>
/// 证书培训批次
/// </summary>
[Table("certificate_training_batch")]
public class CertificateTrainingBatch
{

    [Key] 
    public string Id { get; set; } = EntityTools.SnowflakeId();
    
    /// <summary>
    /// 批次名称
    /// </summary>
    public string BatchName { get; set; } = default!;
    
    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; } 
    
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = default!;
    
    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; } = default!;
    
    /// <summary>
    /// 联系人
    /// </summary>
    public string ContactPerson { get; set; } = default!;
    
    /// <summary>
    /// 联系方式
    /// </summary>
    public string ContactPhone { get; set; } = default!;
    
    /// <summary>
    /// 关联证书id
    /// </summary>
    public string CertificateId { get; set; } = default!;
    
    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreatedBy { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    
    public DateTime UpdatedAt { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 导航属性 - 关联的证书
    /// </summary>
    public CertificateTable Certificate { get; set; } = default!;
    
}