// using Microsoft.AspNetCore.Mvc;
// using Infrastructure.DingTalk;
// using Staffing.Core.Interfaces.Hr;
// using Infrastructure.Extend;
// using Config;

// namespace Staffing.Api.Apis.Common;

// [Service(ServiceLifetime.Transient)]
// public class DingTalkRouter : ICommonRouterBase
// {
//     private DingTalkCallbackService _dingdingcallback;
//     private IDingTalkService _dingService;

//     public DingTalkRouter(DingTalkCallbackService dingdingcallback, IDingTalkService dingTalkService)
//     {
//         _dingdingcallback = dingdingcallback;
//         _dingService = dingTalkService;
//     }

//     public void AddRoutes(RouteGroupBuilder parentGroup)
//     {
//         var group = parentGroup.MapGroup("dingtalk").WithTags("钉钉");
        
//         group.MapPost("/dingding/callback", ([FromQuery] string signature, [FromQuery] string timestamp, [FromQuery] string nonce) =>
//         {
//             return _dingdingcallback.GetCallBack(signature, timestamp, nonce);
//         })
//         .WithMetadata(new ApiMeta())
//         .WithOpenApi(operation => new(operation) { Summary = "钉钉回调" });

//         group.MapGet("/dingding/token/{code}", ([FromRoute] string code) =>
//         {
//             return _dingService.GetCookies(code);
//         })
//         .WithMetadata(new ApiMeta())
//         .WithOpenApi(operation => new(operation) { Summary = "钉钉内登录获取token" });
//     }
// }