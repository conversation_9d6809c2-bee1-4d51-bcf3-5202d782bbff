-- ----------------------------
-- 个人账号表
-- ----------------------------
DROP TABLE IF EXISTS `contract_user`;
CREATE TABLE `contract_user` (
  `Id` varchar(18) NOT NULL,
  `IdNumber` varchar(20) NOT NULL COMMENT '身份证号',
  `Name` varchar(20) NOT NULL COMMENT '身份证姓名',
  `Mobile` varchar(20) NULL COMMENT '手机号',
  `AccountId` varchar(50) NOT NULL COMMENT '个人账号id',
  `ThirdPartyUserId` varchar(50) NOT NULL COMMENT '账号的唯一标识',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_contract_user_IdNumber` (`IdN<PERSON>ber`),
  KEY `IX_contract_user_AccountId` (`AccountId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 机构账号表
-- ----------------------------
DROP TABLE IF EXISTS `contract_org`;
CREATE TABLE `contract_org` (
  `OrgId` varchar(18) NOT NULL,
  `IdType` varchar(20) NOT NULL COMMENT '证件类型，默认CRED_ORG_USCC',
  `IdNumber` varchar(20) NOT NULL COMMENT '企业证件号，需传入真实存在的证件信息',
  `Name` varchar(50) NOT NULL COMMENT '机构名称',
  `Mobile` varchar(20) NULL COMMENT '手机号',
  `EOrgId` varchar(50) NOT NULL COMMENT 'E签宝机构Id',
  `CreatedBy` varchar(18) NOT NULL COMMENT '创建人',
  `UpdatedBy` varchar(18) NOT NULL COMMENT '更新人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`OrgId`),
  UNIQUE KEY `IX_contract_org_IdNumber` (`IdNumber`),
  KEY `IX_contract_org_EOrgId` (`EOrgId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 机构账号-企业关系表
-- ----------------------------
DROP TABLE IF EXISTS `contract_org_ent`;
CREATE TABLE `contract_org_ent` (
  `Id` varchar(18) NOT NULL,
  `EntId` varchar(18) NOT NULL COMMENT '企业Id',
  `OrgId` varchar(18) NOT NULL COMMENT '机构账号Id',
  `CreatedBy` varchar(18) NOT NULL COMMENT '创建人',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_contract_org_ent_OrgId_EntId` (`EntId`,`OrgId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 印章表
-- ----------------------------
DROP TABLE IF EXISTS `contract_seal`;
CREATE TABLE `contract_seal` (
  `SealId` varchar(18) NOT NULL,
  `OrgId` varchar(18) NOT NULL COMMENT '机构Id',
  `Name` varchar(50) NOT NULL COMMENT '印章名称',
  `Type` int NOT NULL COMMENT '印章类型',
  `Width` int NOT NULL COMMENT '印章宽度, 个人默认95px, 机构默认159px',
  `Height` int NOT NULL COMMENT '印章高度, 个人默认95px, 机构默认159px',
  `Url` varchar(1024) NULL COMMENT '印章地址',
  `Default` bit NOT NULL DEFAULT 0 COMMENT '是否默认印章',
  `ESealId` varchar(50) NOT NULL COMMENT 'E签宝印章ID',
  `EFileKey` varchar(50) NOT NULL COMMENT 'E签宝印章fileKey',
  `Deleted` bit NOT NULL DEFAULT 0 COMMENT '删除',
  `CreatedBy` varchar(18) NOT NULL COMMENT '创建人',
  `UpdatedBy` varchar(18) NOT NULL COMMENT '更新人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`SealId`),
  KEY `IX_contract_seal_Deleted` (`Deleted`),
  KEY `IX_contract_seal_EntIdFull` (`OrgId`, `ESealId`, `CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 合同文件模板
-- ----------------------------
DROP TABLE IF EXISTS `contract_doctemplate`;
CREATE TABLE `contract_doctemplate` (
  `TemplateId` varchar(18) NOT NULL,
  `Name` varchar(200) NOT NULL COMMENT '名称',
  `EntId` varchar(18) NOT NULL COMMENT '企业Id',
  `Type` int NOT NULL COMMENT '模板类型',
  `GroupType` int NOT NULL COMMENT '模板组类型',
  `ETemplateId` varchar(50) NOT NULL COMMENT 'E签宝模板Id',
  `Status` int NOT NULL COMMENT '状态',
  `Deleted` bit NOT NULL DEFAULT 0 COMMENT '删除',
  `CreatedBy` varchar(18) NOT NULL COMMENT '创建人',
  `UpdatedBy` varchar(18) NOT NULL COMMENT '更新人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`TemplateId`),
  UNIQUE KEY `IX_contract_doctemplate_ETemplateId` (`ETemplateId`),
  KEY `IX_contract_doctemplate_Deleted` (`Deleted`),
  KEY `IX_contract_doctemplate_Type` (`Type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 合同文件模板组件
-- ----------------------------
DROP TABLE IF EXISTS `contract_doctemplate_component`;
CREATE TABLE `contract_doctemplate_component` (
  `TemplateId` varchar(18) NOT NULL,
  `SystemComponents` text NOT NULL COMMENT '系统组件',
  `CustomComponents` text NOT NULL COMMENT '自定义组件',
  PRIMARY KEY (`TemplateId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



-- ----------------------------
-- 合同表
-- ----------------------------
DROP TABLE IF EXISTS `contract`;
CREATE TABLE `contract` (
  `ContractId` varchar(18) NOT NULL,
  `EFlowId` varchar(18) NOT NULL COMMENT 'E签宝签署流程Id',
  `Name` varchar(255) NOT NULL DEFAULT '' COMMENT '合同名称',
  `Type` int(2) NOT NULL DEFAULT 0 COMMENT '合同类型',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '状态',
  `IdentityCardType` int(2) NOT NULL DEFAULT 0 COMMENT '证件类型',
  `IdentityCard` varchar(20) NOT NULL COMMENT '身份证号',
  `IdentityCardName` varchar(50) NOT NULL DEFAULT '' COMMENT '身份证名字',
  `ProjectId` varchar(18) NOT NULL COMMENT '项目Id',
  `Post` varchar(255) NOT NULL DEFAULT '' COMMENT '职位',
  `UserMobile` varchar(20) NOT NULL DEFAULT '' COMMENT '用户手机号',
  `SigningBeginTime` datetime(6) NULL COMMENT '开始签署时间',
  `SigningEndTime` datetime(6) NULL COMMENT '完成签署时间',
  `ContractBeginTime` datetime(6) NULL COMMENT '合同开始时间',
  `ContractEndTime` datetime(6) NULL COMMENT '合同截止时间',
  `CreatedBy` varchar(18) NOT NULL COMMENT '创建人',
  `UpdatedBy` varchar(18) NOT NULL COMMENT '更新人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`ContractId`),
  KEY `IX_contract_ProjectId` (`ProjectId`),
  KEY `IX_contract_EFlowId` (`EFlowId`),
  KEY `IX_contract_IdentityCard` (`IdentityCard`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 合同文件
-- ----------------------------
DROP TABLE IF EXISTS `contract_file`;
CREATE TABLE `contract_file` (
  `FileId` varchar(18) NOT NULL,
  `ContractId` varchar(18) NOT NULL COMMENT '合同Id',
  `TemplateId` varchar(18) NOT NULL COMMENT '模板Id',
  `Name` varchar(200) NOT NULL COMMENT '名称',
  `EFileId` varchar(50) NOT NULL COMMENT 'E签宝文件Id',
  PRIMARY KEY (`FileId`),
  UNIQUE KEY `IX_contract_file_EFileId` (`EFileId`),
  KEY `IX_contract_file_ContractId` (`ContractId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 合同文件组件
-- ----------------------------
DROP TABLE IF EXISTS `contract_file_component`;
CREATE TABLE `contract_file_component` (
  `FileId` varchar(18) NOT NULL,
  `SystemComponents` text NOT NULL COMMENT '系统组件',
  `CustomComponents` text NOT NULL COMMENT '自定义组件',
  PRIMARY KEY (`FileId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;