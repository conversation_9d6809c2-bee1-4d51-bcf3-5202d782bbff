using System.Text;
using System.Text.RegularExpressions;
using Config;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using ServiceStack.Text;

namespace Infrastructure.Proxy;

[Service(ServiceLifetime.Transient)]
public class WxSheQunApi
{
    private readonly ConfigManager _config;
    private readonly LogManager _log;
    public WxSheQunApi(IOptionsSnapshot<ConfigManager> config, LogManager log)
    {
        _config = config.Value;
        _log = log;
    }

    /// <summary>
    /// 获取微信社群汇总
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<WxSheQunSummyInfo> GetWxSheQunSummy(int page = 1, int time = 1)
    {
        var user = await WxSheQunLogin();

        var token = $"{user!.ex!.i!.username}&{user!.userid2}&{user!.ex!.i!.key}";
        token = Md5Helper.Md5(token).ToLower();

        var url = "https://www.qmlm2022.com/s/?m=initialize&v=v4.0";

        try
        {
            var restr = await url.WithHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            .WithHeader("Referer", "https://www.qmlm2022.com/download.php")
            .WithHeader("X-Requested-With", "XMLHttpRequest") // 表示请求从Ajax发出
            .PostUrlEncodedAsync(new
            {
                c = string.Empty,
                p = "download"
            }).ReceiveString();

            var res = JsonSerializer.DeserializeFromString<WxSheQunSummyRes>(restr);

            if (res?.s != 1)
            {
                var unstr = Regex.Unescape(restr);
                throw new Exception($"微信社群请求错误：{unstr}");
            }

            return res.tc ?? new WxSheQunSummyInfo();
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 获取微信社群列表
    /// </summary>
    /// <param name="page">第几页</param>
    /// <param name="time">几小时内的群</param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<List<WxSheQunListInfo>> GetWxList(int page = 1, int time = 1)
    {
        var user = await WxSheQunLogin();

        var token = $"{user!.ex!.i!.username}&{user!.userid2}&{user!.ex!.i!.key}";
        token = Md5Helper.Md5(token).ToLower();

        var url = "https://www.qmlm2022.com/s/?m=qrCodePay&v=v4.0";

        var sendData = $"province=0&province_key=&filename=&type=0&people=0&time={time}&page={page}&t={DateTime.Now.ToUnixTime()}";
        var sign = sqbs64(sendData);

        try
        {
            var restr = await url.WithHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            .WithHeader("Referer", "https://www.qmlm2022.com/download.php")
            .WithHeader("Token", token) // 新增
            .WithHeader("X-Requested-With", "XMLHttpRequest") // 表示请求从Ajax发出
            .PostUrlEncodedAsync(new
            {
                sign = sign,
                token = token,
                us = user!.userid2
            }).ReceiveString();

            var res = JsonSerializer.DeserializeFromString<WxSheQunListRes>(restr);

            if (res?.s != 1)
            {
                var unstr = Regex.Unescape(restr);
                if (unstr?.Contains("没有数据") == true)
                    return new List<WxSheQunListInfo>();
                throw new Exception($"微信社群请求错误：{unstr}");
            }

            var deres = dcde(res.d!);
            var result = JsonSerializer.DeserializeFromString<List<WxSheQunListInfo>>(deres);

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    public string WxSheQunSign()
    {
        // var cacheKey = $"wxshequn:sign";
        // var sign = MyRedis.Client.Get(cacheKey);
        // if (string.IsNullOrWhiteSpace(sign))
        // {
        //     sign = $"{sqbs64("fengliu944")}${Md5Helper.Md5("wuqing521")}${DateTime.Now.ToUnixTime()}";
        //     sign = sqbs64(sign);
        //     MyRedis.Client.Set(cacheKey, sign, TimeSpan.FromDays(10));
        // }
        var sign = $"{sqbs64("fengliu944")}${Md5Helper.Md5("wuqing521")}${DateTime.Now.ToUnixTime()}";
        sign = sqbs64(sign);
        return sign;


        // 1. sign += H.base64_encode("fengliu944");
        // 2. sign += "$" + hex_md5("wuqing521");
        // 5. sign += "$" + parseInt(new Date().getTime() / 1000);
        // 6. sign = H.base64_encode(sign);
    }

    /// <summary>
    /// 获取服务器key
    /// </summary>
    /// <returns></returns>
    public async Task<WxSheQunLoginRes> WxSheQunLogin()
    {
        var cacheKey = $"wxshequn:token";

        var result = MyRedis.Client.Get<WxSheQunLoginRes?>(cacheKey);

        if (!string.IsNullOrWhiteSpace(result?.ex?.i?.key))
            return result;

        var sign = WxSheQunSign();

        //var url = "https://www.qmlm2022.com/s/?m=login&e=0&p=download&v=v3.2.4";
        //var url = "https://www.qmlm2022.com/s/?m=login&e=1&p=index&v=v4.0"; // https://www.qmlm2022.com/index.php
        var url = "https://www.qmlm2022.com/s/?m=login&e=0&p=download&v=v4.0"; // https://www.qmlm2022.com/download.php

        try
        {
            var restr = await url.WithHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            .WithHeader("Referer", "https://www.qmlm2022.com/download.php")
            .WithHeader("X-Requested-With", "XMLHttpRequest") // 表示请求从Ajax发出
            .PostUrlEncodedAsync(new
            {
                sign = sign
            }).ReceiveString();

            result = JsonSerializer.DeserializeFromString<WxSheQunLoginRes>(restr);

            if (result?.s != 1)
            {
                var unstr = Regex.Unescape(restr);
                throw new Exception($"微信社群请求错误：{unstr}");
            }

            result.sign = sign;
            result.userid2 = sqbs64(result.ex!.i!.userid!);

            MyRedis.Client.Set(cacheKey, result, TimeSpan.FromHours(2));

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    private string sqbs64(string str)
    {
        var staticchars = "PXhw7UT1B0a9kQDKZsjIASmOezxYG4CHo5Jyfg2b8FLpEvRr3WtVnlqMidu6cN";
        var encodechars = new StringBuilder();
        char code;
        var ran = new Random(Guid.NewGuid().GetHashCode());
        for (var i = 0; i < str.Length; i++)
        {
            var num0 = staticchars.IndexOf(str[i]);
            if (num0 == -1)
                code = str[i];
            else
                code = staticchars[(num0 + 3) % 62];

            var r1 = ran.Next(0, 62);
            var r2 = ran.Next(0, 62);
            // var num1 = Convert.ToInt32(((int)r1).ToString(), 10);
            // var num2 = Convert.ToInt32(((int)r2).ToString(), 10);
            encodechars.Append($"{staticchars[r1]}{code}{staticchars[r2]}");
        }
        return encodechars.ToString();
    }

    public string dcde(string str)
    {
        var key = "123456";
        var len = key.Length;
        var code = new StringBuilder();
        var base64Bytes = System.Convert.FromBase64String(str);
        str = System.Text.Encoding.UTF8.GetString(base64Bytes);

        for (var i = 0; i < str.Length; i++)
        {
            var k = i % len;
            code.Append((char)((int)str[i] ^ (int)key[k]));
        }
        base64Bytes = System.Convert.FromBase64String(code.ToString());
        var ccc = System.Text.Encoding.UTF8.GetString(base64Bytes);

        return ccc;
    }
}

public class WxSheQunLoginRes
{
    /// <summary>
    /// 1=成功
    /// </summary>
    public int s { get; set; }
    public WxSheQunLoginResEx? ex { get; set; }
    public string? sign { get; set; }
    public string? userid2 { get; set; }
}

public class WxSheQunLoginResEx
{
    public WxSheQunLoginResI? i { get; set; }
}

public class WxSheQunLoginResI
{
    public string? key { get; set; }
    public string? userid { get; set; }
    public string? username { get; set; }
    public string? usernumber { get; set; }
}

public class WxSheQunListRes
{
    /// <summary>
    /// 1=成功
    /// </summary>
    public int s { get; set; }
    public string? d { get; set; }
}

public class WxSheQunListInfo
{
    /// <summary>
    /// 
    /// </summary>
    public long qrid { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public int type { get; set; }

    /// <summary>
    /// 鱼泡网湖北招工找活群78(yupao)
    /// </summary>
    public string? filename { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public int people { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string? status { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public long time { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string? url { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public int view { get; set; }
}

public class WxSheQunSummyRes
{
    /// <summary>
    /// 1=成功
    /// </summary>
    public int s { get; set; }

    public string? m { get; set; }
    public WxSheQunSummyInfo? tc { get; set; }
}

public class WxSheQunSummyInfo
{
    /// <summary>
    /// 检索数量
    /// </summary>
    public long allcheck { get; set; }

    /// <summary>
    /// 在线微信群
    /// </summary>
    public long wx { get; set; }

    /// <summary>
    /// 在线企业群
    /// </summary>
    public long qywx { get; set; }
}