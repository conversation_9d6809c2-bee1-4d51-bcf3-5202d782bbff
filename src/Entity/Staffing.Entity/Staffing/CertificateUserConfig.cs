using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

[Table("certificate_user_config")]
public class CertificateUserConfig
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    [Column("Id")]
    public long Id { get; set; }
    
    [Column("UserId", TypeName = "varchar(64)")]
    public string UserId { get; set; } = default!;
    
    [Column("IsEnabled")]
    public bool IsEnabled { get; set; } = false;

    [Column("CreatedBy", TypeName = "varchar(64)")]
    public string? CreatedBy { get; set; }
    
    [Column("CreatedAt")]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    [Column("UpdatedBy", TypeName = "varchar(64)")]
    public string? UpdatedBy { get; set; }
    
    [Column("UpdatedAt")]
    public DateTime UpdatedAt { get; set; } = DateTime.Now;
}