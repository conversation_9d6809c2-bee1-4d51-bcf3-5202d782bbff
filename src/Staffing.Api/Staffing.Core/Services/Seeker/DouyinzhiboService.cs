﻿using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Staffing.Entity;
using Staffing.Core.Interfaces.Seeker;
using Infrastructure.CommonService;
using Staffing.Model.Seeker.Douyinzhibo;
using Config.CommonModel;
using Config.Enums;
using Staffing.Entity.Staffing;
using LinqKit;
using Infrastructure.Exceptions;

namespace Staffing.Core.Services.Seeker;

[Service(ServiceLifetime.Transient)]
public class DouyinzhiboService : IDouyinzhiboService
{
    private readonly StaffingContext _context;
    private readonly RequestContext _user;
    private readonly CommonDicService _commonDicService;
    private readonly CommonPostService _commonPostService;
    public DouyinzhiboService(StaffingContext context, 
        RequestContext user,
        CommonDicService commonDicService,
        CommonPostService commonPostService)
    {
        _user = user;
        _context = context;
        _commonDicService = commonDicService;
        _commonPostService = commonPostService;
    }

    public async Task<PostsGroupsResponse> GetPostGroups(GroupRequest request)
    {
        await Task.FromResult(1);
        //if (string.IsNullOrWhiteSpace(request.ZhiboId))
        //    throw new Exception("直播id为空");

        // 根据渠道关系找顾问;
        if (string.IsNullOrWhiteSpace(request.ChannelId))
            throw new Exception("渠道id为空");
        var qdInfo = _context.Qd_Hr_Channel.FirstOrDefault(f => f.Id == request.ChannelId);
        if (qdInfo == null)
            throw new Exception("渠道关系不存在，请联系管理员");

        //var predicate = PredicateBuilder.New<Post_Team>(w => w.Show
        //&& w.Project_Team.HrId == qdInfo.HrId
        //&& (w.Post_Team_Douyinzhibo == null || (w.Post_Team_Douyinzhibo.Any(a => a.DouyinzhiboId == request.ZhiboId && a.IsRemove == false))));

        var predicate = PredicateBuilder.New<Post_Team>(w => w.Show && w.Project_Team.HrId == qdInfo.HrId);

        // 抖音小程序职位过滤
        predicate = _commonPostService.GetFilterPostCondition(predicate);

        var result = _context.Post_Team.Where(predicate)
            .Select(s => new DouyinGroupType
            {
                TeamPostId = s.TeamPostId,
                Category = s.Post.Category + "",
                RegionId = s.Post.RegionId,
                AgentEntId = s.Post.Project.AgentEntId,
                AgentEntName = s.Post.Project.Agent_Ent.Name
            })
            .ToList();

        var zhiboPost = _context.Post_Team_Douyinzhibo.Where(w => w.DouyinzhiboId == request.ZhiboId).ToList();

        // 用户端去掉下架岗位
        if (!string.IsNullOrWhiteSpace(request.ZhiboId) && zhiboPost.Count > 0 && request.UserType == UserTypeEnum.用户端)
        {
            var removePostIds = zhiboPost.Where(w => w.IsRemove == true).Select(s => s.TeamPostId).ToList();
            result = result.Where(w => !removePostIds.Contains(w.TeamPostId)).ToList();
        }

        // 正在讲解,开启热度 打标
        result.ForEach(f => f.IsSpeek = zhiboPost.Any(a => a.TeamPostId == f.TeamPostId && a.IsSpeek == true));
        var speekPost = result?.FirstOrDefault(f => f.IsSpeek == true);
        var hotGroup = _context.Douyinzhibo_Post_Type.Where(w => w.DouyinzhiboId == request.ZhiboId && w.HotTime != null).ToList(); 

        // 找职位一级分类，地级市
        foreach(var item in result)
        {
            if(!string.IsNullOrWhiteSpace(item.Category))
            {
                var categoryInfo = _commonDicService.GetCategory(item.Category + "");
                item.Category = categoryInfo?.CategoryLevel1;
                item.CategoryName = categoryInfo?.CategoryLevel1Name;
            }

            if (!string.IsNullOrWhiteSpace(item.RegionId))
            {
                var cityinfo = _commonDicService.GetCityById(item.RegionId);
                item.RegionId = cityinfo?.CityId;
                item.RegionName = cityinfo?.CityName;
            }
        }

        // 排序按职位数量排序
        // 按职位一级类别分组
        var categoryGroups = result.Where(w => !string.IsNullOrWhiteSpace(w.Category))
            .GroupBy(g => new { g.Category, g.CategoryName })
            .Select(s => new PostsGroups
            {
                GroupType = PostGroupEnum.职位一级分类,
                TypeId = s.Key.Category!,
                TypeName = s.Key.CategoryName!,
                Count = s.Count(),
                IsSpeek = s.Key.Category == speekPost?.Category,
                IsHot = hotGroup.FirstOrDefault(f => f.Type == PostGroupEnum.职位一级分类 && f.TypeContent == s.Key.Category)?.HotTime != null
            })
            .OrderByDescending(o => o.Count)
            .ToList();

        // 按城市分组
        var cityGroups = result.Where(w => !string.IsNullOrWhiteSpace(w.RegionId))
            .GroupBy(g => new { g.RegionId, g.RegionName })
            .Select(s => new PostsGroups
            {
                GroupType = PostGroupEnum.城市,
                TypeId = s.Key.RegionId!,
                TypeName = s.Key.RegionName!,
                Count = s.Count(),
                IsSpeek = s.Key.RegionId == speekPost?.RegionId,
                IsHot = hotGroup.FirstOrDefault(f => f.Type == PostGroupEnum.城市 && f.TypeContent == s.Key.RegionId)?.HotTime != null
            })
            .OrderByDescending(o => o.Count)
            .ToList();

        // 按公司分组
        var companyGroups = result.Where(w => !string.IsNullOrWhiteSpace(w.AgentEntId))
            .GroupBy(g => new { g.AgentEntId, g.AgentEntName })
            .Select(s => new PostsGroups
            {
                GroupType = PostGroupEnum.公司,
                TypeId = s.Key.AgentEntId!,
                TypeName = s.Key.AgentEntName!,
                Count = s.Count(),
                IsSpeek = s.Key.AgentEntId == speekPost?.AgentEntId,
                IsHot = hotGroup.FirstOrDefault(f => f.Type == PostGroupEnum.公司 && f.TypeContent == s.Key.AgentEntId)?.HotTime != null
            })
            .OrderByDescending(o => o.Count)
            .ToList();

        var allDatas = new List<PostsGroups>().Concat(categoryGroups).Concat(cityGroups).Concat(companyGroups).ToList();

        return new PostsGroupsResponse 
        { 
            //Rows = allDatas,
            CategoryGroups = categoryGroups,
            CityGroups = cityGroups,
            CompanyGroups = companyGroups
        };
    }

    public PostListForDouyinZhiboResponse GetPostListForCreate(DouyinzhiboPostRequestForCreate request)
    {
        // 根据渠道关系找顾问
        if (string.IsNullOrWhiteSpace(request.ChannelId))
            throw new Exception("渠道id为空");
        var qdInfo = _context.Qd_Hr_Channel.FirstOrDefault(f => f.Id == request.ChannelId);
        if (qdInfo == null)
            throw new Exception("渠道关系不存在，请联系管理员");

        var predicate = PredicateBuilder.New<Post_Team>(w => w.Show && w.Project_Team.HrId == qdInfo.HrId);

        // 抖音小程序职位过滤
        predicate = _commonPostService.GetFilterPostCondition(predicate);

        List<DouyinzhiboPostInfo>? postList = GetPostList(predicate, request);

        var result = new PostListForDouyinZhiboResponse()
        {
            Rows = postList ?? new List<DouyinzhiboPostInfo>(),
            Total = postList?.Count ?? 0,
        };

        return result;
    }

    public async Task<PostListForDouyinZhiboResponse> GetPostListByZhiboId(DouyinzhiboPostRequestForEdit request)
    {
        await Task.FromResult(true);
        // 根据渠道关系找顾问
        if (string.IsNullOrWhiteSpace(request.ZhiboId))
            throw new Exception("直播id为空");

        // 根据渠道关系找顾问，todo：这个可以不传，可以通过zhiboid获取
        if (string.IsNullOrWhiteSpace(request.ChannelId))
            throw new Exception("渠道id为空");
        var qdInfo = _context.Qd_Hr_Channel.FirstOrDefault(f => f.Id == request.ChannelId);
        if (qdInfo == null)
            throw new Exception("渠道关系不存在，请联系管理员");

        var predicate = PredicateBuilder.New<Post_Team>(w => w.Show && w.Project_Team.HrId == qdInfo.HrId);

        // 抖音小程序职位过滤
        predicate = _commonPostService.GetFilterPostCondition(predicate);

        // 下架岗位
        if (request.IsRemoved == true)
        {
            predicate = predicate.And(w => w.Post_Team_Douyinzhibo.Any(a => a.DouyinzhiboId == request.ZhiboId && a.IsRemove == true));
        }
        // 非下架岗位(默认)
        else
        {
            // todo: 这里需要看看怎么写
            //predicate = predicate.And(w => w.Post_Team_Douyinzhibo == null || (w.Post_Team_Douyinzhibo.Any(a => a.DouyinzhiboId == request.ZhiboId && a.IsRemove == false)));
        }

        List<DouyinzhiboPostInfo>? postList = GetPostList(predicate, request, request.ZhiboId);

        if(request.UserType == UserTypeEnum.用户端)
            postList = postList?.Where(w => w.IsRemoved == false).ToList();// todo:在这里去掉下架岗位,如果分页就歇菜

        var result = new PostListForDouyinZhiboResponse()
        {
            Rows = postList ?? new List<DouyinzhiboPostInfo>(),
            Total = postList?.Count ?? 0,
        };

        return result;
    }

    private List<DouyinzhiboPostInfo>? GetPostList(ExpressionStarter<Post_Team> predicate, DouyinzhiboPostRequest request, string? zhiboId = null)
    {
        // 按职位一级类别筛选
        if (request.Group?.GroupType == PostGroupEnum.职位一级分类)
        {
            var categories = _context.Dic_Post.Where(x => x.Status == ActiveStatus.Active && x.Level.StartsWith(request.Group.TypeId + "."))
                    .Select(s => s.Id).ToList();
            predicate = predicate.And(w => categories.Contains(w.Post.Category));
        }

        // 按城市类别筛选
        if (request.Group?.GroupType == PostGroupEnum.城市)
        {
            predicate = predicate.And(w => w.Post.RegionId.StartsWith(request.Group.TypeId));
        }

        // 按公司类别筛选
        if (request.Group?.GroupType == PostGroupEnum.公司)
        {
            predicate = predicate.And(w => w.Post.Project.AgentEntId == request.Group.TypeId);
        }

        // 获取顾问的职位列表 todo:暂时没有分页
        var postList = _context.Post_Team
            .Where(predicate)
            .OrderByDescending(o => o.CreatedTime)// 按创建时间倒序排序
            .Select(s => new DouyinzhiboPostInfo
            {
                PostId = s.PostId,
                TeamPostId = s.TeamPostId,
                ProjectId = s.Project_Team.ProjectId,
                PostName = s.Post.Name,
                Describe = s.Post.Describe,
                Category = s.Post.Category,
                TieType = s.Post.TieType,
                WorkNature = s.Post.WorkNature,
                SalaryType = s.Post.SalaryType,
                MinSalary = s.Post.MinSalary,
                MaxSalary = s.Post.MaxSalary,
                Money = s.Post.Money,
                Salary = s.Post.Salary,
                PaymentNode = s.Post.PaymentNode,
                PaymentDays = s.Post.PaymentDays,
                EducationName = s.Post.Education.GetDescription(),
                Welfare = s.Post.Welfare,
                WelfareCustom = s.Post.WelfareCustom,
                RegionId = s.Post.RegionId,
                Address = s.Post.Address,
            }).ToList();

        if (postList != null && postList.Count > 0)
        {
            // 一次性获取职位投递数据
            var deliveryCount = _context.Post_Delivery.Where(w => postList.Select(s => s.TeamPostId).Contains(w.TeamPostId))
                .Select(s => s.TeamPostId)
                .GroupBy(g => g)
                .Select(g => new { g.Key, Ct = g.Count() })
                .ToList();

            // 数据处理
            var welfare = _commonDicService.GetWelfare();
            var zhiboPostInfo = _context.Post_Team_Douyinzhibo.Where(w => w.DouyinzhiboId == zhiboId).ToList();
            foreach (var item in postList)
            {
                if (!string.IsNullOrWhiteSpace(zhiboId))
                {
                    item.IsRemoved = zhiboPostInfo.FirstOrDefault(f => f.TeamPostId == item.TeamPostId)?.IsRemove ?? false;
                    item.IsSpeek = zhiboPostInfo.FirstOrDefault(f => f.TeamPostId == item.TeamPostId)?.IsSpeek ?? false;
                }

                item.CategoryName = _commonDicService.GetCategory(item.Category + "")?.CategoryLevel3Name;
                item.SalaryName = Tools.FormattingSalary(item.MinSalary, item.MaxSalary, item.SalaryType, item.WorkNature, item.Salary);
                item.DeliveryCount = deliveryCount.FirstOrDefault(f => f.Key == item.TeamPostId)?.Ct ?? 0;

                switch (item.PaymentNode)
                {
                    case ProjectPaymentNode.入职过保: item.PaymentNodeName = $"（入职过保{item.PaymentDays ?? 0}天）"; item.MoneyName = (int)(item.Money ?? 0) + "元/人"; break;
                    case ProjectPaymentNode.简历交付: item.PaymentNodeName = "（简历交付）"; item.MoneyName = (int)(item.Money ?? 0) + "元/份"; break;
                    case ProjectPaymentNode.到面交付: item.PaymentNodeName = "（到面交付）"; item.MoneyName = (int)(item.Money ?? 0) + "元/次"; break;
                    // case ProjectPaymentNode.按天入职过保: item.PaymentNodeName = $"（按天入职过保{item.PaymentDays ?? 0}天）"; item.MoneyName = (int)(item.Money ?? 0) + "元/人/天"; break;
                }

                if (item.Welfare?.Count > 0)
                    item.Welfare = welfare.Where(x => item.Welfare.Any(a => a.Id == x.Id)).ToList();

                if (!string.IsNullOrWhiteSpace(item.RegionId))
                    item.Region = _commonDicService.GetCityById(item.RegionId);
            }
        }

        return postList;
    }

    public EmptyResponse ZhiboSave(ZhiboSaveRequest request)
    {
        // 信息校验
        if (string.IsNullOrWhiteSpace(request.ChannelId))
            throw new BadRequestException("未获取到渠道ID");

        // 基本信息保存
        Douyinzhibo? zhiboInfo = null;
        if (!string.IsNullOrWhiteSpace(request.ZhiboId))
        {
            zhiboInfo = _context.Douyinzhibo.FirstOrDefault(f => f.Id == request.ZhiboId);
            if(zhiboInfo == null)
                throw new BadRequestException("无效的直播id");
            zhiboInfo.UpdatedTime = DateTime.Now;
            zhiboInfo.UpdatedUserId = _user.Id;
            zhiboInfo.PostCount = GetPostCount(request.ChannelId, request.RemoveTeamposts, request.ZhiboId);
        }

        if(zhiboInfo == null)
        {
            zhiboInfo = new Douyinzhibo();
            zhiboInfo.ChannelId = request.ChannelId;
            zhiboInfo.CreatedTime = DateTime.Now;
            zhiboInfo.CreatedUserId = _user.Id;
            zhiboInfo.PostCount = GetPostCount(request.ChannelId, request.RemoveTeamposts);
            _context.Add(zhiboInfo);
        }

        zhiboInfo.Name = request.Name ?? zhiboInfo.Name ?? string.Empty;
        zhiboInfo.StartTime = request.StartTime ?? zhiboInfo.StartTime;
        zhiboInfo.EndTime = request.EndTime ?? zhiboInfo.EndTime;
        zhiboInfo.CoverUrl = request.CoverUrl ?? zhiboInfo.CoverUrl;
        

        // 职位上下架保存
        if (request.RemoveTeamposts != null && request.RemoveTeamposts.Count > 0)
        {
            var checkLists = new List<RemoveOrSpeekTeamposts>();
            request.RemoveTeamposts.ForEach(f => checkLists.Add(new RemoveOrSpeekTeamposts { TeampostId = f.TeampostId, IsRemove = f.IsRemove}));
            // 验证岗位是否真实下架
            CheckPostStatus(checkLists);
            var teamPostIds = request.RemoveTeamposts.Select(s => s.TeampostId).ToList();
            var exists = _context.Post_Team_Douyinzhibo
                .Where(w => w.DouyinzhiboId == zhiboInfo.Id && teamPostIds.Contains(w.TeamPostId))
                .ToList();

            if(exists != null && exists.Count > 0)
            {
                foreach(var item in exists)
                {
                    
                    var newInfo = request.RemoveTeamposts.FirstOrDefault(f => f.TeampostId == item.TeamPostId);

                    if (item.IsSpeek && newInfo.IsRemove)
                    {
                        throw new BadRequestException("存在正在讲解岗位，无法下架");
                    }

                    item.IsRemove = newInfo.IsRemove;
                    item.UpdatedTime = DateTime.Now;
                    request.RemoveTeamposts.Remove(newInfo);// 删除已存在记录，剩下的是新增记录
                }
            }

            // 新增记录
            foreach (var item in request.RemoveTeamposts)
            {
                var createInfo = new Post_Team_Douyinzhibo();
                createInfo.DouyinzhiboId = zhiboInfo.Id;
                createInfo.TeamPostId = item.TeampostId;
                createInfo.IsRemove = item.IsRemove;
                createInfo.ChannelId = request.ChannelId;
                createInfo.CreatedTime = DateTime.Now;
                _context.Add(createInfo);
            }
        }

        _context.SaveChanges();

        return new EmptyResponse();
    }

    private void CheckPostStatus(List<RemoveOrSpeekTeamposts> teamposts)
    {
        if(teamposts == null || teamposts.Count == 0) 
            return;

        var postIds = teamposts.Select(s => s.TeampostId).ToList();
        var showPosts = _context.Post_Team.Where(w => w.Show && !w.Post.Deleted && postIds.Contains(w.TeamPostId)).Select (s => s.TeamPostId).ToList();

        foreach(var item in teamposts)
        {
            // 上架，讲解判断
            if(!item.IsRemove || item.IsSpeek)
            {
                if(showPosts == null || !showPosts.Contains(item.TeampostId))
                    throw new BadRequestException("存在已下架岗位，请刷新重试");
            }
        }
    }

    private int? GetPostCount(string channelId, List<RemoveTeamposts>? removeLists = null,string? zhiboId = null)
    {
        int postCount = 0;
        PostListForDouyinZhiboResponse response = new PostListForDouyinZhiboResponse();
        // 编辑
        if (!string.IsNullOrWhiteSpace(zhiboId))
        {
            response = GetPostListByZhiboId(new DouyinzhiboPostRequestForEdit { ZhiboId = zhiboId, ChannelId = channelId}).GetAwaiter().GetResult();
            var notRemove = response.Rows.Where(w => w.IsRemoved == false).ToList();
            if (removeLists == null || removeLists.Count == 0)
                postCount = notRemove.Count;
            else
            {
                var rows = response.Rows;
                // todo:如果该职位已下架或上架，再次下架或上架时，不计算
                int addCount = removeLists.Count(c => c.IsRemove == false && !rows.Any(a => a.TeamPostId == c.TeampostId && a.IsRemoved == false));
                int subCount = removeLists.Count(c => c.IsRemove == true && !rows.Any(a => a.TeamPostId == c.TeampostId && a.IsRemoved == true));
                postCount = notRemove.Count + addCount - subCount;
            }
        }
        else
        {
            response = GetPostListForCreate(new DouyinzhiboPostRequestForCreate { ChannelId = channelId });
            if (removeLists == null || removeLists.Count == 0)
                postCount = response.Total;
            else
                postCount = response.Total - (removeLists.Where(w => w.IsRemove == true)?.Count() ?? 0);
        }

        return postCount;
    }

    public EmptyResponse SpeekThePost(SpeekRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.ZhiboId))
        {
            throw new BadRequestException("缺少直播ID");
        }

        if (string.IsNullOrWhiteSpace(request.TeamPostId))
        {
            throw new BadRequestException("缺少岗位ID");
        }

        // 验证岗位是否真实下架
        var checkLists = new List<RemoveOrSpeekTeamposts>{ new RemoveOrSpeekTeamposts { TeampostId = request.TeamPostId, IsSpeek = true}};
        CheckPostStatus(checkLists);

        // 1.关闭正在讲解的岗位
        var exist_speek = _context.Post_Team_Douyinzhibo.Where(w => w.DouyinzhiboId == request.ZhiboId && w.IsSpeek == true).FirstOrDefault();
        if (exist_speek != null)
        {
            exist_speek.IsSpeek = false;
            exist_speek.UpdatedTime = DateTime.Now;
        }

        // 2.开启新的岗位讲解
        var exist = _context.Post_Team_Douyinzhibo.Where(w => w.DouyinzhiboId == request.ZhiboId && w.TeamPostId == request.TeamPostId).FirstOrDefault();
        if (exist != null)
        {
            exist.IsSpeek = request.IsSpeek ?? true;
            exist.UpdatedTime = DateTime.Now;
        }
        else
        {
            var newSpeek = new Post_Team_Douyinzhibo { CreatedTime = DateTime.Now };
            newSpeek.ChannelId = request.ChannelId ?? string.Empty;// todo:这里的channelid暂时没什么用
            newSpeek.DouyinzhiboId = request.ZhiboId;
            newSpeek.TeamPostId = request.TeamPostId;
            newSpeek.IsSpeek = true;// 新增不可能是false
            _context.Add(newSpeek);
        }

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public ZhiboEditResponse ZhiboEdit(string id)
    {
        var response = _context.Douyinzhibo.Where(w => w.Id == id)
            .Select(s => new ZhiboEditResponse 
            {
                ZhiboId = s.Id,
                Name = s.Name,
                StartTime = s.StartTime,
                EndTime = s.EndTime,
                CoverUrl = s.CoverUrl,
                ChannelId = s.ChannelId,
                PostCount = s.PostCount ?? 0,
            }).FirstOrDefault();

        return response ?? new ZhiboEditResponse();
    }

    public EmptyResponse ZhiboLoad(string id)
    {
        var zb = _context.Douyinzhibo.FirstOrDefault(f => f.Id == id);

        if (zb == null)
            throw new BadRequestException("未找到直播");

        zb.IsLoad = true;
        zb.UpdatedTime = DateTime.Now;
        zb.UpdatedUserId = _user.Id;
        _context.SaveChanges();
        return new EmptyResponse();
    }

    public EmptyResponse HotTheGroup(HotRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.ZhiboId))
        {
            throw new BadRequestException("缺少直播ID");
        }

        var groupInfo = _context.Douyinzhibo_Post_Type.FirstOrDefault(f => f.DouyinzhiboId == request.ZhiboId 
        && f.Type == request.Group.GroupType 
        && f.TypeContent == request.Group.TypeId);

        if(groupInfo != null)
        {
            if(!request.IsHot)
                groupInfo.HotTime = null;
            else
                groupInfo.HotTime = DateTime.Now;
        }
        else
        {
            var newGroup = new Douyinzhibo_Post_Type
            {
                DouyinzhiboId = request.ZhiboId,
                ChannelId = request.ChannelId,
                Type = request.Group.GroupType,
                TypeContent = request.Group.TypeId,
                HotTime = DateTime.Now
            };
            _context.Add(newGroup);
        }

        _context.SaveChanges();

        return new EmptyResponse();
    }

    private List<string> special_users = new List<string>
    {
        "101","220197413701354373","219660638555797893","272371363902223365"
    };
    public ZhiboListResponse ZhiboList(ZhiboListRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.ChannelId))
            throw new BadRequestException("渠道Id为空");

        var predicate = PredicateBuilder.New<Douyinzhibo>(true);

        if(!special_users.Contains(_user.Id))
            predicate = predicate.And(w => w.ChannelId == request.ChannelId && w.CreatedUserId == _user.Id);

        if(request.ZhiboType != null)
        {
            if(request.ZhiboType == ZhiboTypeEnum.未开始)
            {
                predicate = predicate.And(w => w.StartTime > DateTime.Now);
            }
            else if(request.ZhiboType == ZhiboTypeEnum.进行中)
            {
                predicate = predicate.And(w => w.StartTime <= DateTime.Now && w.EndTime > DateTime.Now);
            }
            else if(request.ZhiboType == ZhiboTypeEnum.已结束)
            {
                predicate = predicate.And(w => w.EndTime < DateTime.Now);
            }
        }

        var response = _context.Douyinzhibo
            .Where(predicate)
            .OrderByDescending(o => o.CreatedTime)
            .Select(s => new ZhiboInfo
            {
                ZhiboId = s.Id,
                Name = s.Name,
                StartTime = s.StartTime,
                EndTime = s.EndTime,
                CoverUrl = s.CoverUrl,
                ChannelId = s.ChannelId,
                PostCount = s.PostCount ?? 0
            })
            .ToList();

        return new ZhiboListResponse { Rows = response };
    }
}