﻿using AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Tea;

namespace Infrastructure.DingTalk;

[Service(ServiceLifetime.Transient)]
public class DingTalkOAProcessService
{
    private readonly DingTalkTokenService _dingTalkTokenService;
    private StaffingContext _context;
    private readonly LogManager _log;

    public DingTalkOAProcessService(DingTalkTokenService dingTalkTokenService, StaffingContext context, LogManager log)
    {
        _dingTalkTokenService = dingTalkTokenService;
        _context = context;
        _log = log;
    }

    /// <summary>
    /// 创建审批流客户端
    /// </summary>
    /// <returns></returns>
    public static AlibabaCloud.SDK.Dingtalkworkflow_1_0.Client CreateClient()
    {
        AlibabaCloud.OpenApiClient.Models.Config config = new AlibabaCloud.OpenApiClient.Models.Config();
        config.Protocol = "https";
        config.RegionId = "central";
        return new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Client(config);
    }

    /// <summary>
    /// 创建审批流模板
    /// </summary>
    public void CreateWorkflowModel()
    {
        AlibabaCloud.SDK.Dingtalkworkflow_1_0.Client client = CreateClient();
        AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.FormCreateHeaders formCreateHeaders = new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.FormCreateHeaders();
        formCreateHeaders.XAcsDingtalkAccessToken = _dingTalkTokenService.GetToken(DingTalkConstKey.WorkFlowAppKey, DingTalkConstKey.WorkFlowAppSecret);

        AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.FormComponentProps formComponentProps1 = new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.FormComponentProps()
        {
            ComponentId = "TextField-abcd",
            Placeholder = "请输入",
            Label = "单行输入",
            Required = true
        };

        AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.FormComponentProps formComponentProps2 = new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.FormComponentProps()
        {
            Label = "姓名",
            AsyncCondition = true,
            Required = true,
            Content = "我是说明文字控件",
            Format = "yyyy-MM-dd",
            Upper = "1",
            Unit = "天",
            Placeholder = "请输入",
            BizAlias = "finance_name",
            BizType = "attendance.leave",
            Duration = true,
            Choice = "0",
            Disabled = true,
            Align = "top",
            Invisible = true,
            Link = "http://www.baidu.com"
        };

        AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.FormComponent formComponent1 = new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.FormComponent()
        {
            ComponentType = "TextField",
            Props = formComponentProps1
        };

        AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.FormComponent formComponent2 = new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.FormComponent()
        {
            ComponentType = "TextField",
            Props = formComponentProps2
        };

        AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.FormCreateRequest formCreateRequest = new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.FormCreateRequest()
        {
            Name = "出差报销审批",
            Description = "用于员工差旅费用报销使用",
            FormComponents = new List<AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.FormComponent> { formComponent1, formComponent2 },
        };

        try
        {
            var result = client.FormCreateWithOptions(formCreateRequest, formCreateHeaders, new AlibabaCloud.TeaUtil.Models.RuntimeOptions());
        }
        catch (TeaException err)
        {
            if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
            {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        }
        catch (Exception _err)
        {
            TeaException err = new TeaException(new Dictionary<string, object>
                {
                    { "message", _err.Message }
                });
            if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
            {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        }
    }

    /// <summary>
    /// 发起审批流实例
    /// </summary>
    /// <param name="processCode">钉钉流程ID</param>
    /// <param name="componentValues">表单内容</param>
    /// <param name="approvers">审批人列表</param>
    /// <param name="ccUserList">抄送人UserID列表</param>
    /// <param name="sendUserInfo">发起人信息</param>
    /// <returns>流程实例ID</returns>
    /// <exception cref="Exception"></exception>
    public string StartProcessInstance(string processCode,string businessId,
        Dictionary<string, string> componentValues,
        List<ApproverModel>? approvers,
        List<string>? ccUserList,
        SendUserInfo sendUserInfo)
    {
        // 参数校验 todo
        if (approvers != null && approvers.Any())
        {
            foreach (var approver in approvers)
            {
                switch (approver.actionType)
                {
                    case "AND": if (approver.approvers.Count() < 2) { throw new Exception("审批人少于两个，不能用会签"); } break;
                    case "OR": break;
                    case "NONE": if (approver.approvers.Count() > 1) { throw new Exception("审批人大于一个，不能用单人审批"); } break;
                }
            }
        }

        AlibabaCloud.SDK.Dingtalkworkflow_1_0.Client client = CreateClient();
        AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.StartProcessInstanceHeaders startProcessInstanceHeaders = new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.StartProcessInstanceHeaders();
        startProcessInstanceHeaders.XAcsDingtalkAccessToken = _dingTalkTokenService.GetToken(DingTalkConstKey.WorkFlowAppKey, DingTalkConstKey.WorkFlowAppSecret);

        var formComponentValues = new List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues>();
        foreach (var item in componentValues)
        {
            formComponentValues.Add(new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues
            {
                Name = item.Key,
                Value = item.Value
            });
        }

        AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.StartProcessInstanceRequest startProcessInstanceRequest = new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.StartProcessInstanceRequest
        {
            OriginatorUserId = sendUserInfo.SendUserId,
            ProcessCode = processCode,
            DeptId = sendUserInfo.SendUserDeptId,
            MicroappAgentId = DingTalkConstKey.WorkFlowAgentId,
            FormComponentValues = formComponentValues
        };

        if (approvers != null && approvers.Count > 0)
        {
            var Approvers = new List<AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.StartProcessInstanceRequest.StartProcessInstanceRequestApprovers>();
            foreach (var approver in approvers)
            {
                Approvers.Add(new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.StartProcessInstanceRequest.StartProcessInstanceRequestApprovers()
                {
                    ActionType = approver.actionType,
                    UserIds = approver.approvers
                });
            }
            startProcessInstanceRequest.Approvers = Approvers;
        }

        if (ccUserList != null)
        {
            startProcessInstanceRequest.CcList = ccUserList;
        }

        string processInstanceId = string.Empty;
        try
        {
            var result = client.StartProcessInstanceWithOptions(startProcessInstanceRequest, startProcessInstanceHeaders, new AlibabaCloud.TeaUtil.Models.RuntimeOptions());
            processInstanceId = result.Body.InstanceId;
            SaveProcessInstance(processInstanceId, businessId);
        }
        catch (TeaException err)
        {
            if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
            {
                processInstanceId = err.Message;
            }
        }
        catch (Exception _err)
        {
            processInstanceId = _err.Message;
        }

        return processInstanceId;
    }

    /// <summary>
    /// 获取审批流详情
    /// </summary>
    /// <param name="processInstanceId"></param>
    public void GetProcessInstance(string processInstanceId)
    {
        AlibabaCloud.SDK.Dingtalkworkflow_1_0.Client client = CreateClient();
        AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.GetProcessInstanceHeaders getProcessInstanceHeaders = new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.GetProcessInstanceHeaders();
        getProcessInstanceHeaders.XAcsDingtalkAccessToken = _dingTalkTokenService.GetToken(DingTalkConstKey.WorkFlowAppKey, DingTalkConstKey.WorkFlowAppSecret);

        AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.GetProcessInstanceRequest getProcessInstanceRequest = new AlibabaCloud.SDK.Dingtalkworkflow_1_0.Models.GetProcessInstanceRequest
        {
            ProcessInstanceId = processInstanceId
        };
        try
        {
            var result = client.GetProcessInstanceWithOptions(getProcessInstanceRequest, getProcessInstanceHeaders, new AlibabaCloud.TeaUtil.Models.RuntimeOptions());
            SaveProcessInstance(processInstanceId, null, result.Body.Result);
        }
        catch (TeaException err)
        {
            if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
            {
                _log.Info("获取审批流详情", err.Message, processInstanceId);
            }
        }
        catch (Exception _err)
        {
            _log.Info("获取审批流详情", _err.Message, processInstanceId);
        }
    }

    /// <summary>
    /// 审批流程详写库
    /// </summary>
    /// <param name="result"></param>
    /// <exception cref="NotImplementedException"></exception>
    public void SaveProcessInstance(string processInstanceId, string? businessId, GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult? result = null)
    {
        var instance = _context.Dingding_Process_Instancet.FirstOrDefault(f => f.ProcessInstanceId == processInstanceId);

        // 创建
        if(instance == null)
        {
            instance = new Dingding_Process_Instancet()
            {
                ProcessCode = DingTalkConstKey.ProcessCode,
                BussinessId = businessId,
                ProcessInstanceId = processInstanceId,
                CreatedTime = DateTime.Now
            };
            _context.Add(instance);
        }
        // 修改
        else if(result != null)
        {
            instance.ApproverUserIds = result.ApproverUserIds;
            instance.BizAction = result.BizAction;
            instance.BizData = result.BizData;
            instance.CcUserIds = result.CcUserIds;
            instance.CreateTime = result.CreateTime;
            instance.OriginatorDeptId = result.OriginatorDeptId;
            instance.OriginatorDeptName = result.OriginatorDeptName;
            instance.OriginatorUserId = result.OriginatorUserId;
            instance.FinishTime = result.FinishTime;
            instance.Result = result.Result;
            instance.Title = result.Title;
            instance.UpdatedTime = DateTime.Now;

            if (result.FormComponentValues != null && result.FormComponentValues.Count > 0)
            {
                var formValues = new List<FormComponentValues>();
                foreach (var item in result.FormComponentValues)
                {
                    formValues.Add(new FormComponentValues
                    {
                        Id = item.Id,
                        Name = item.Name,
                        Value = item.Value,
                    });
                }
                instance.FormComponentValues = formValues;
            }

            if (result.OperationRecords != null && result.OperationRecords.Count > 0)
            {
                var operationRecords = new List<OperationRecords>();
                foreach (var item in result.OperationRecords)
                {
                    var record = new OperationRecords
                    {
                        UserId = item.UserId,
                        Date = item.Date,
                        Type = item.Type,
                        Result = item.Result,
                        Remark = item.Remark,
                        CcUserIds = item.CcUserIds
                    };

                    if (item.Attachments != null && item.Attachments.Count > 0)
                    {
                        var attachs = new List<Attachments>();
                        foreach (var attachment in item.Attachments)
                        {
                            attachs.Add(new Attachments
                            {
                                FileName = attachment.FileName,
                                FileSize = attachment.FileSize,
                                FileId = attachment.FileId,
                                FileType = attachment.FileType
                            });
                        }
                        record.Attachments = attachs;
                    }
                    operationRecords.Add(record);
                }
                instance.OperationRecords = operationRecords;
            }

            if (result.Tasks != null && result.Tasks.Count > 0)
            {
                var tasks = new List<DingdingTasks>();
                foreach (var item in result.Tasks)
                {
                    tasks.Add(new DingdingTasks
                    {
                        TaskId = item.TaskId,
                        UserId = item.UserId,
                        Status = item.Status,
                        Result = item.Result,
                        CreateTime = item.CreateTime,
                        FinishTime = item.FinishTime,
                        MobileUrl = item.MobileUrl,
                        PcUrl = item.PcUrl,
                        ProcessInstanceId = item.ProcessInstanceId,
                        ActivityId = item.ActivityId
                    });
                }
                instance.Tasks = tasks;
            }
        }
        
        _context.SaveChanges();
    }
}

public class ApproverModel
{
    public string actionType { get; set; } = "NONE";
    public List<string> approvers { get; set; } = new List<string>();
}

/// <summary>
/// 流程发起人
/// </summary>
public class SendUserInfo
{
    /// <summary>
    /// 发起人UserId
    /// </summary>
    public string SendUserId { get; set; } = default!;

    /// <summary>
    /// 发起人部门Id
    /// </summary>
    public long? SendUserDeptId { get; set; }
}

/// <summary>
/// 测试审批流程
/// </summary>
public class TestWorkflowService
{
    private readonly DingTalkOAProcessService _dingTalkOAProcessService;
    public TestWorkflowService(DingTalkOAProcessService dingTalkOAProcessService)
    {
        _dingTalkOAProcessService = dingTalkOAProcessService;
    }

    public static SendUserInfo GetSendUserInfo()
    {
        return new SendUserInfo { SendUserId = "0125060108371213970" };
    }

    /// <summary>
    /// 测试发起审批
    /// </summary>
    public void TestStartProcessInstance()
    {
        // 钉钉流程ID
        string processCode = "PROC-B524F6D0-22EA-428F-B36A-A430660ED6DB";

        // 发起人
        var sendUserInfo = GetSendUserInfo();

        // 获取表单数据
        var formData = new Dictionary<string, string>();
        formData.Add("项目名称", "凯悦汽车操作工");
        formData.Add("职位名称", "操作工");
        formData.Add("项目经理", "嗯哼");
        formData.Add("协同顾问", "哈哈");
        formData.Add("订单号", "213843171495484677");
        //formData.Add("订单信息", "这是一个订单信息，这是一个订单信息，这是一个订单信息，这是一个订单信息，这是一个订单信息");

        // 审批人
        var approvers = new List<ApproverModel>
        { 
            new ApproverModel { actionType = "NONE", approvers = new List<string> { "121210094821247395" } },
            new ApproverModel { actionType = "NONE", approvers = new List<string> { "0125060108371213970" } } 
        };

        // 发起请求
        string processInstanceId = _dingTalkOAProcessService.StartProcessInstance(processCode, "213843171495484677", formData, approvers, null, sendUserInfo);
}

    /// <summary>
    /// 测试获取审批实例详情
    /// </summary>
    public void TestGetProcessInstance()
    {
        _dingTalkOAProcessService.GetProcessInstance("jQCRKzACTuG4RoxIMXJZ7g09911703122442");
    }
}