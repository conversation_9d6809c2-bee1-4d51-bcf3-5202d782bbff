﻿using Config.CommonModel.Business;
using Config.Enums;
using Staffing.Entity.Staffing;

namespace Staffing.Model.Seeker.TalentVirtual;

#region 虚拟简历库ViewModel

public class TalentVirtualBaseInfoResponse
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public string VirtualId { get; set; } = string.Empty;

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? HeadPortrait { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Mailbox { get; set; }

    /// <summary>
    /// 微信
    /// </summary>
    public string? WeChat { get; set; }

    /// <summary>
    /// 手机
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// QQ
    /// </summary>
    public string? QQ { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 自我评价
    /// </summary>
    public string? SelfEvaluation { get; set; }

    /// <summary>
    /// 附件
    /// </summary>
    public string OriginalUrl { get; set; } = string.Empty;

    /// <summary>
    /// 职业
    /// </summary>
    public OccupationType? Occupation { get; set; }

    /// <summary>
    /// 职业解释
    /// </summary>
    public string? OccupationName { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public TalentVirtualEducation? Education { get; set; }

    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 性别解释
    /// </summary>
    public string? SexName { get; set; }

    /// <summary>
    /// 简历完整度
    /// </summary>
    public int Perfection { get; set; }

    /// <summary>
    /// 性格
    /// </summary>
    public List<string> Nature { get; set; } = new List<string>();

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 技能
    /// </summary>
    public List<string> Skill { get; set; } = new List<string>();

    /// <summary>
    /// 技能分析
    /// </summary>
    public TalentVirtaualResumeSkillSub SkillAnalysis { get; set; } = new TalentVirtaualResumeSkillSub();

    /// <summary>
    /// 外貌
    /// </summary>
    public List<string> Appearance { get; set; } = new List<string>();

    /// <summary>
    /// 证书
    /// </summary>
    public List<string> SkillsCert { get; set; } = new List<string>();

    /// <summary>
    /// hr小程序简历二维码
    /// </summary>
    public string? HrAppletQrCode { get; set; }

    /// <summary>
    /// 活跃度
    /// </summary>
    public OnlineStatus Active { get; set; }

    /// <summary>
    /// 活跃度解释
    /// </summary>
    public string? ActiveName { get; set; }

    /// <summary>
    /// 真实用户userid
    /// </summary>
    public string? SeekerId { get; set; }

    /// <summary>
    /// 顾问数量 - 对他感兴趣
    /// </summary>
    public int? Advisers { get; set; }

    /// <summary>
    /// 位置信息
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    public List<TalentVirtaualResumeWorkSubResponse>? WorkSub { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public List<TalentVirtaualResumeEduSubResponse>? EduSub { get; set; }

    /// <summary>
    /// 项目经历
    /// </summary>
    public List<TalentVirtaualResumeProjectSubResponse>? ProjectSub { get; set; }

    /// <summary>
    /// 求职期望
    /// </summary>
    public TalentVirtaualResumeHopeSubResponse? HopeSub { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public List<string>? Label { get; set; }
}

/// <summary>
/// 工作经历（详情子表）
/// </summary>
public class TalentVirtaualResumeWorkSubResponse
{
    /// <summary>
    /// 主键id - 列表编辑时要传
    /// </summary>
    public string? Id { get; set; }
    /// <summary>
    /// 公司名称
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 最低薪资
    /// </summary>
    public decimal? MinSalary { get; set; }

    /// <summary>
    /// 最高薪资
    /// </summary>
    public decimal? MaxSalary { get; set; }

    /// <summary>
    /// 公司描述
    /// </summary>
    public string? CompanyRemarks { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public string? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public string? EndTime { get; set; }
}

/// <summary>
/// 教育经历（详情子表）
/// </summary>
public class TalentVirtaualResumeEduSubResponse
{
    /// <summary>
    /// 学校名称
    /// </summary>
    public string? SchoolName { get; set; }

    /// <summary>
    /// 是否全日制
    /// </summary>
    public bool? IsFullTime { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public TalentVirtualEducation? Education { get; set; }

    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 专业
    /// </summary>
    public string? MajorName { get; set; }

    /// <summary>
    /// 学校描述
    /// </summary>
    public string? SchoolRemarks { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public string? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public string? EndTime { get; set; }
}

/// <summary>
/// 项目经历（详情子表）
/// </summary>
public class TalentVirtaualResumeProjectSubResponse
{
    /// <summary>
    /// 主键id - 列表编辑时要传
    /// </summary>
    public string? Id { get; set; }
    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 项目描述
    /// </summary>
    public string? ProjectRemarks { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public string? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public string? EndTime { get; set; }
}

/// <summary>
/// 求职期望（详情子表）
/// </summary>
public class TalentVirtaualResumeHopeSubResponse
{
    /// <summary>
    /// 期望城市
    /// </summary>
    public string? HopeCity { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 最低薪资
    /// </summary>
    public decimal? MinSalary { get; set; }

    /// <summary>
    /// 最高薪资
    /// </summary>
    public decimal? MaxSalary { get; set; }
}

public class UpdateTalentVirtualProjectsResponse
{
    public string Id { get; set; } = string.Empty;
}

public class UpdateTalentVirtualWorksResponse
{
    public string Id { get; set; } = string.Empty;
}

public class UpdateTalentVirtualResumeProjectSub : TalentVirtaualResumeProjectSub
{
    public string Id { get; set; } = string.Empty;
    public string VirtualId { get; set; } = string.Empty;
}

public class UpdateTalentVirtualResumeWorkSub : TalentVirtaualResumeWorkSub
{
    public string Id { get; set; } = string.Empty;
    public string VirtualId { get; set; } = string.Empty;
}

public class TalentVitualMainIdResponse
{
    public string VirtualId { get; set; } = string.Empty;
}

#endregion
