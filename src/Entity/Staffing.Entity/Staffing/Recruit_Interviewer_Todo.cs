﻿using Config.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 面试官待办表
/// </summary>
[Table("recruit_interviewer_todo")]
public class Recruit_Interviewer_Todo
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 关联id（面试官筛选表id或面试表id）
    /// </summary>
    public string? RelationId { get; set; }

    /// <summary>
    /// 招聘流程id，冗余
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 面试官Id
    /// </summary>
    public string InterviewerId { get; set; } = default!;

    /// <summary>
    /// 状态
    /// </summary>
    public RecruitInterviewerTodoStatus Status { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public RecruitInterviewerTodoType Type { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Project_Interviewer Project_Interviewer { get; set; } = default!;

    public Recruit_Interview Recruit_Interview { get; set; } = default!;

    public Recruit_Interviewer_Screen Recruit_Interviewer_Screen { get; set; } = default!;

    public Recruit Recruit { get; set; } = default!;
}

