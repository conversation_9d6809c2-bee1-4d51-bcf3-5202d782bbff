﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 抖音直播实体类
/// </summary>
[Table("douyinzhibo")]
public class Douy<PERSON>zhibo
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 渠道id
    /// </summary>
    public string ChannelId { get; set; } = default!;

    /// <summary>
    /// 直播名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 直播开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 直播结束时间
    /// </summary>
    public DateTime EndTime { get; set; } = DateTime.Now.AddHours(1);

    /// <summary>
    /// 直播是否挂载
    /// </summary>
    public bool IsLoad { get; set; } = false;

    /// <summary>
    /// 直播封面地址
    /// </summary>
    public string CoverUrl { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime {  get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set;} = DateTime.Now;

    /// <summary>
    /// 创建人
    /// </summary>
    public string CreatedUserId { get; set; } = default!;
    
    /// <summary>
    /// 更新人
    /// </summary>
    public string? UpdatedUserId { get; set; }

    /// <summary>
    /// 职位数量
    /// </summary>
    public int? PostCount { get; set; }
}


/// <summary>
/// 抖音直播职位分类
/// </summary>
[Table("douyinzhibo_post_type")]
public class Douyinzhibo_Post_Type
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 抖音直播id
    /// </summary>
    public string DouyinzhiboId { get; set; } = default!;

    /// <summary>
    /// 渠道id
    /// </summary>
    public string? ChannelId { get; set; }

    /// <summary>
    /// 职位类别
    /// </summary>
    public PostGroupEnum Type { get; set; } = PostGroupEnum.职位一级分类;

    /// <summary>
    /// 职位分类内容：id或名称
    /// </summary>
    public string TypeContent { get; set; } = default!;

    /// <summary>
    /// 开启加热时间，置为null则为关闭加热
    /// </summary>
    public DateTime? HotTime { get; set; }

}

public enum PostGroupEnum
{
    职位一级分类,城市,公司
}