﻿using Flurl.Http;
using Newtonsoft.Json.Linq;

namespace Infrastructure.Common.Contract
{
    /// <summary>
    /// 合同签署工具
    /// </summary>
    public class SignTool
    {
        private const string ApiUrl = "https://openapi.esign.cn";
        private const string appKey = "**********";
        private const string AppSecret = "45e1894b8ad81ecc0a2cae972ebc4370";

        #region 印章接口
        /// <summary>
        /// 上传印章(创建个人/机构图片印章)
        /// </summary>
        /// <param name="accountId"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public async Task<string> UploadStamp(string accountId, string data)
        {
            string api = ApiUrl + "/v1/accounts/" + accountId + "/seals/image";
            JObject dataJson = new JObject();
            dataJson.Add("type", "BASE64");
            dataJson.Add("data", data);
            string result = await api
            .WithHeader("X-Tsign-Open-App-Id" , appKey)
            .WithHeader("X-Tsign-Open-Token" , ESignAccessToken.GetAccessToken(appKey, AppSecret))
            .PostJsonAsync(dataJson).ReceiveString();
            //Dictionary<string, string> headers = Http.SendHttp.buildHeaders(appKey, ESignAccessToken.GetAccessToken());
            //// HTTP请求内容类型
            //string contentType = "application/json";
            //string result = Http.SendHttp.Post(api, headers, dataJson.ToString(), contentType);
            var r = JToken.Parse(result);
            if (r.Value<int>("code") == 0)
            {
                return result;// r.Value<JToken>("data").Value<string>("sealId");
            }
            else
            {
                throw new Exception("请求错误");
            }
        }
        /// <summary>
        /// 删除印章
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="sealId"></param>
        /// <returns></returns>
        public async Task<bool> DeleteStamp(string orgId, string sealId)
        {
            string api = ApiUrl + "/v1/organizations/" + orgId + "/seals/" + sealId;
            string result = await api.WithHeader("X-Tsign-Open-App-Id", appKey)
            .WithHeader("X-Tsign-Open-Token", ESignAccessToken.GetAccessToken(appKey, AppSecret))
            .PostStringAsync("").ReceiveString();
            //Dictionary<string, string> headers = Http.SendHttp.buildHeaders(Config.AppKey, ESignAccessToken.GetAccessToken());
            //// HTTP请求内容类型
            //string contentType = "application/json";
            //string result = Http.SendHttp.Post(api, headers, null, contentType);
            var r = JToken.Parse(result);
            if (r.Value<int>("code") == 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion
        /// <summary>
        /// 上传合同模板
        /// </summary>
        /// <returns></returns>
        public async Task<string> UploadTemplate(string contentMd5, string fileName = "合同.pdf", string filePath = "")
        {
            string api = ApiUrl + "/v1/docTemplates/createByUploadUrl";
            JObject dataJson = new JObject();
            dataJson.Add("contentMd5", contentMd5);
            dataJson.Add("contentType", "application/octet-stream");
            dataJson.Add("fileName", fileName);
            bool convert2Pdf = true;
            if (fileName.Contains(".pdf"))
            {
                convert2Pdf = false;
            }
            dataJson.Add("convert2Pdf", convert2Pdf);
            
            string result = await api
            .WithHeader("X-Tsign-Open-App-Id", appKey)
            .WithHeader("X-Tsign-Open-Token", ESignAccessToken.GetAccessToken(appKey, AppSecret))
            .PostJsonAsync(dataJson).ReceiveString();
            //Dictionary<string, string> headers = Http.SendHttp.buildHeaders(Config.AppKey, ESignAccessToken.GetAccessToken());
            //JObject dataJson = new JObject();
            //dataJson.Add("contentMd5", contentMd5);
            //dataJson.Add("contentType", "application/octet-stream");
            //dataJson.Add("fileName", fileName);
            //bool convert2Pdf = true;
            //if (fileName.Contains(".pdf"))
            //{
            //    convert2Pdf = false;
            //}
            //dataJson.Add("convert2Pdf", convert2Pdf);
            //// HTTP请求内容类型
            //string contentType = "application/json";
            //string result = Http.SendHttp.Post(api, headers, dataJson.ToString(), contentType);
            var r = JToken.Parse(result);
            if (r.Value<int>("code") == 0)
            {
                string uploadUrl = r.Value<JToken>("data")!.Value<string>("uploadUrl") ?? "";
                //headers = new Dictionary<string, string>();
                //headers.Add("Content-MD5", contentMd5);
                //int fileresult = Http.SendHttp.SendPUT(uploadUrl, filePath, Http.SendHttp.getPUTHeaders(contentMd5, "UTF-8"), "application/octet-stream");
                var uploadResult = await uploadUrl
                   .WithHeader("Content-MD5", contentMd5)
                   .PutAsync();
                int fileResult = uploadResult.StatusCode;
                string templateId = r.Value<JToken>("data")!.Value<string>("templateId")!;
                if (fileResult == 200)
                    return templateId;
                else
                    return "";
            }
            else
            {
                return "";
            }
        }
        /// <summary>
        /// Content-MD5计算方法
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static string GetContentMD5FromFile(string filePath)
        {
            string ContentMD5 = String.Empty;
            try
            {
                FileStream file = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                System.Security.Cryptography.MD5 md5 = System.Security.Cryptography.MD5.Create();
                // 先计算出上传内容的MD5，其值是一个128位（128 bit）的二进制数组
                byte[] retVal = md5.ComputeHash(file);
                file.Close();
                // 再对这个二进制数组进行base64编码
                ContentMD5 = Convert.ToBase64String(retVal).ToString();
                return ContentMD5;
            }
            catch (Exception ex)
            {
                throw new Exception("计算文件的Content-MD5值时发生异常：" + ex.Message);
            }
        }

        /// <summary>
        /// 静默授权签署
        /// </summary>
        /// <param name="accountId"></param>
        /// <returns></returns>
        public async Task<bool> SignAuth(string accountId)
        {
            string api = ApiUrl + "/v1/signAuth/" + accountId;
            string result = await api
            .WithHeader("X-Tsign-Open-App-Id", appKey)
            .WithHeader("X-Tsign-Open-Token", ESignAccessToken.GetAccessToken(appKey, AppSecret))
            .PostAsync().ReceiveString();

            //Dictionary<string, string> headers = Http.SendHttp.buildHeaders(Config.AppKey, ESignAccessToken.GetAccessToken());
            //// HTTP请求内容类型
            //string contentType = "application/json";
            //string result = Http.SendHttp.Post(api, headers, "", contentType);
            var r = JToken.Parse(result);
            if (r.Value<int>("code") == 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 创建合同
        /// </summary>
        /// <param name="templateId">模板编号</param>
        /// <param name="name">文件名称（必须带上文件扩展名，不然会导致后续发起流程校验过不去 示例：合同.pdf </param>
        /// <param name="simpleFormFields"></param>
        /// <returns></returns>
        public async Task<string?> CreateContract(string templateId, string name, JToken simpleFormFields)
        {
            string api = ApiUrl + "/v1/files/createByTemplate";
            JObject dataJson = new JObject();
            dataJson.Add("templateId", templateId);
            dataJson.Add("name", name);
            dataJson.Add("simpleFormFields", simpleFormFields);
            string result = await api
            .WithHeader("X-Tsign-Open-App-Id", appKey)
            .WithHeader("X-Tsign-Open-Token", ESignAccessToken.GetAccessToken(appKey, AppSecret))
            .PostJsonAsync(dataJson).ReceiveString();

           
            //Dictionary<string, string> headers = Http.SendHttp.buildHeaders(Config.AppKey, ESignAccessToken.GetAccessToken());
            //// HTTP请求内容类型
            //string contentType = "application/json";
            //string result = Http.SendHttp.Post(api, headers, dataJson.ToString(), contentType);
            var r = JToken.Parse(result);
            if (r.Value<int>("code") == 0)
            {
                return r.Value<JToken>("data")!.Value<string>("fileId");
            }
            else
            {
                return "请求错误1：" + result;
            }
        }

        /// <summary>
        /// 生成签署流程
        /// </summary>
        /// <param name="fileId">创建合同返回的id</param>
        /// <param name="businessScene"></param>
        /// <param name="corporationAccountId"></param>
        /// <param name="accountId"></param>
        /// <param name="noticeDeveloperUrl"></param>
        /// <param name="redirectUrl"></param>
        /// <param name="sealId">法人印章</param>
        /// <returns></returns>
        public async Task<string> CreateFlow(string fileId, string businessScene, string corporationAccountId, string accountId, string noticeDeveloperUrl, string redirectUrl, string sealId = "")
        {
            string json = Newtonsoft.Json.JsonConvert.SerializeObject(new
            {
                docs = new object[]
                {
                    new {
                    fileId= fileId
                    }
                },
                flowInfo = new
                {
                    businessScene,
                    autoArchive = true,
                    autoInitiate = true,
                    flowConfigInfo = new
                    {
                        noticeDeveloperUrl = noticeDeveloperUrl,
                        redirectUrl = redirectUrl
                    }
                },
                signers = new object[] {
                    new
                    {
                        platformSign=true,
                        signOrder=1,
                        signfields=new object[]{
                            new {
                                autoExecute=true,
                                actorIndentityType=2,
                                fileId=fileId,
                                posBean=new
                                {
                                    posPage=4,
                                    posX=199,
                                    posY=313
                                }
                            }
                        }
                    },
                    new
                    {
                        platformSign=false,
                        signOrder=2,
                        signerAccount=new
                        {
                            signerAccountId=corporationAccountId
                        },
                        signfields=new object[]{
                            new {
                                autoExecute=true,
                                fileId=fileId,
                                sealId=sealId,
                                posBean=new
                                {
                                    posPage=4,
                                    posX=197,
                                    posY=246
                                }
                            }
                        }
                    },
                    new
                    {
                        platformSign=false,
                        signOrder=3,
                        signerAccount=new
                        {
                            signerAccountId=accountId
                        },
                        signfields=new object[]{
                            new {
                                autoExecute=false,
                                fileId=fileId,
                                posBean=new
                                {
                                    posPage=4,
                                    posX=446,
                                    posY=295
                                }
                            }
                        }
                    }
                }
            });
            string api = ApiUrl + "/api/v2/signflows/createFlowOneStep";
            //Dictionary<string, string> headers = Http.SendHttp.buildHeaders(Config.AppKey, ESignAccessToken.GetAccessToken());
            //// HTTP请求内容类型
            //string contentType = "application/json";
            //string result = Http.SendHttp.Post(api, headers, json, contentType);
            string result = await api
            .WithHeader("X-Tsign-Open-App-Id", appKey)
            .WithHeader("X-Tsign-Open-Token", ESignAccessToken.GetAccessToken(appKey, AppSecret))
            .PostAsync().ReceiveString();
            var r = Newtonsoft.Json.Linq.JToken.Parse(result);
            if (r.Value<int>("code") == 0)
            {
                return r.Value<JToken>("data")!.Value<string>("flowId")!;
            }
            else
            {
                throw new Exception("请求错误2:" + result);
            }
        }
        public async Task<string> GetSignUrl(string flowId, string accountId)
        {
            string api = ApiUrl + "/v1/signflows/" + flowId + "/executeUrl?accountId=" + accountId;
            string result = await api
           .WithHeader("X-Tsign-Open-App-Id", appKey)
           .WithHeader("X-Tsign-Open-Token", ESignAccessToken.GetAccessToken(appKey, AppSecret))
           .GetStringAsync();


            //Dictionary<string, string> headers = Http.SendHttp.buildHeaders(Config.AppKey, ESignAccessToken.GetAccessToken());
            //// HTTP请求内容类型
            //string contentType = "application/json";
            //string result = Http.SendHttp.Get(api, headers, contentType);
            var r = JToken.Parse(result);
            if (r.Value<int>("code") == 0)
            {
                return r.Value<JToken>("data")!.Value<string>("url")!;
            }
            else
            {
                throw new Exception("请求错误3:" + result);
            }
        }
        /// <summary>
        /// 直接获取可签署的地址
        /// </summary>
        /// <param name="name"></param>
        /// <param name="templateId">模板编号</param>
        /// <param name="simpleFormFields"></param>
        /// <param name="businessScene">场景</param>
        /// <param name="corporationAccountId">法人id</param>
        /// <param name="accountId">签署人的id</param>
        /// <param name="noticeDeveloperUrl">通知开发者地址</param>
        /// <param name="redirectUrl">签署完成重定向地址</param>
        /// <param name="flowId"></param>
        /// <returns></returns>
        public async Task<(string, string)> GetSignUrl(string name, string templateId, JToken simpleFormFields, string businessScene, string corporationAccountId, string accountId, string noticeDeveloperUrl, string redirectUrl, string sealId = "")
        {
            string fileId = await CreateContract(templateId, name, simpleFormFields) ?? "";
            string flowId = await CreateFlow(fileId, businessScene, corporationAccountId, accountId, noticeDeveloperUrl, redirectUrl, sealId);
            var signUrl = await GetSignUrl(flowId, accountId);
            return (flowId, signUrl);
        }
        /// <summary>
        /// 获取离职签署地址
        /// </summary>
        /// <param name="name"></param>
        /// <param name="templateId"></param>
        /// <param name="simpleFormFields"></param>
        /// <param name="businessScene"></param>
        /// <param name="corporationAccountId"></param>
        /// <param name="accountId"></param>
        /// <param name="noticeDeveloperUrl"></param>
        /// <param name="redirectUrl"></param>
        /// <param name="flowId"></param>
        /// <param name="sealId"></param>
        /// <returns></returns>
        public async Task<string> GetTurnoverSignUrl(string name, string templateId, JToken simpleFormFields, string businessScene, string accountId, string noticeDeveloperUrl, string redirectUrl)
        {
            string fileId = await CreateContract(templateId, name, simpleFormFields) ?? "";
            string flowId = await CreateTurnoverFlow(fileId, businessScene, accountId, noticeDeveloperUrl, redirectUrl);
            return await GetSignUrl(flowId, accountId);
        }
        public async Task<string> CreateTurnoverFlow(string fileId, string businessScene, string accountId, string noticeDeveloperUrl, string redirectUrl)
        {
            string json = Newtonsoft.Json.JsonConvert.SerializeObject(new
            {
                docs = new object[]
                {
                    new {
                    fileId= fileId
                    }
                },
                flowInfo = new
                {
                    businessScene,
                    autoArchive = true,
                    autoInitiate = true,
                    flowConfigInfo = new
                    {
                        noticeDeveloperUrl = noticeDeveloperUrl,
                        redirectUrl = redirectUrl
                    }
                },
                signers = new object[] {
                    new
                    {
                        platformSign=true,
                        signOrder=1,
                        signfields=new object[]{
                            new {
                                autoExecute=true,
                                actorIndentityType=2,
                                fileId=fileId,
                                posBean=new
                                {
                                    posPage=1,
                                    posX=455,
                                    posY=238
                                }
                            }
                        }
                    },
                    new
                    {
                        platformSign=true,
                        signOrder=1,
                        signfields=new object[]{
                            new {
                                autoExecute=true,
                                actorIndentityType=2,
                                fileId=fileId,
                                posBean=new
                                {
                                    posPage=2,
                                    posX=467,
                                    posY=245
                                }
                            }
                        }
                    },
                    new
                    {
                        platformSign=false,
                        signOrder=2,
                        signerAccount=new
                        {
                            signerAccountId=accountId
                        },
                        signfields=new object[]{
                            new {
                                autoExecute=false,
                                fileId=fileId,
                                posBean=new
                                {
                                    posPage=1,
                                    posX=450,
                                    posY=512
                                }
                            }
                        }
                    },
                    new
                    {
                        platformSign=false,
                        signOrder=2,
                        signerAccount=new
                        {
                            signerAccountId=accountId
                        },
                        signfields=new object[]{
                            new {
                                autoExecute=false,
                                fileId=fileId,
                                posBean=new
                                {
                                    posPage=2,
                                    posX=242,
                                    posY=423
                                }
                            }
                        }
                    }
                }
            });
            string api = ApiUrl + "/api/v2/signflows/createFlowOneStep";
            //Dictionary<string, string> headers = Http.SendHttp.buildHeaders(Config.AppKey, ESignAccessToken.GetAccessToken());
            //// HTTP请求内容类型
            //string contentType = "application/json";
            //string result = Http.SendHttp.Post(api, headers, json, contentType);
            string result = await api
            .WithHeader("X-Tsign-Open-App-Id", appKey)
            .WithHeader("X-Tsign-Open-Token", ESignAccessToken.GetAccessToken(appKey, AppSecret))
            .PostAsync().ReceiveString();
            var r = Newtonsoft.Json.Linq.JToken.Parse(result);
            if (r.Value<int>("code") == 0)
            {
                return r.Value<JToken>("data")!.Value<string>("flowId")!;
            }
            else
            {
                throw new Exception("请求错误");
            }
        }
        #region 获取后台动态数据
        /// <summary>
        /// 生成签署流程
        /// </summary>
        /// <param name="fileId">创建合同返回的id</param>
        /// <param name="businessScene"></param>
        /// <param name="orgId">组织机构账号</param>
        /// <param name="accountId">个人账号</param>
        /// <param name="noticeDeveloperUrl">通知开发者地址</param>
        /// <param name="redirectUrl">签署完成重定向地址</param>
        /// <param name="XYZ">动态坐标json字符串（合同模板表XYZ字段的值）</param>
        /// <returns></returns>
        public async Task<string> CreateFlows_1(string fileId, string businessScene, string orgId, string accountId, string noticeDeveloperUrl, string redirectUrl, string XYZ)
        {
            JArray array = Newtonsoft.Json.Linq.JArray.Parse(XYZ);

            //企业章
            var signers = array.Where(p => p["SealID"]!.ToString() != "0").Select(item => new
            {
                platformSign = false,
                signOrder = item["SignOrder"],
                signerAccount = new
                {
                    signerAccountId = orgId
                },
                signfields = new object[]{
                            new {
                                autoExecute=true,
                                //actorIndentityType=2,
                                sealId=item["SealID"]!.ToString(),
                                fileId=fileId,
                                posBean=new
                                {
                                    posPage=item["pagenumber"]!.ToString(),
                                    posX=item["zuobiaoX"]!.ToString(),
                                    posY=item["zuobiaoY1"]!.ToString()
                                }
                            }
                        }
            });
            //个人签名
            var signers_1 = array.Where(p => p["SealID"]!.ToString() == "0").Select(item => new
            {
                platformSign = false,
                signOrder = item["SignOrder"],
                signerAccount = new
                {
                    signerAccountId = accountId
                },
                signfields = new object[]{
                            new {
                                autoExecute=false,
                                fileId=fileId,
                                posBean=new
                                {
                                    posPage=item["pagenumber"]!.ToString(),
                                    posX=item["zuobiaoX"]!.ToString(),
                                    posY=item["zuobiaoY1"]!.ToString()
                                }
                            }
                        }
            });
            string signers_str = Newtonsoft.Json.JsonConvert.SerializeObject(signers);
            string signers_str_1 = Newtonsoft.Json.JsonConvert.SerializeObject(signers_1);
            signers_str = signers_str.Substring(0, signers_str.Length - 1);
            signers_str_1 = signers_str_1.Substring(1);
            signers_str = signers_str + "," + signers_str_1;
            JArray signersarray = Newtonsoft.Json.Linq.JArray.Parse(signers_str);
            string json = Newtonsoft.Json.JsonConvert.SerializeObject(new
            {
                docs = new object[]
                {
                    new {
                    fileId= fileId
                    }
                },
                flowInfo = new
                {
                    businessScene,
                    autoArchive = true,
                    autoInitiate = true,
                    flowConfigInfo = new
                    {
                        noticeDeveloperUrl = noticeDeveloperUrl,
                        redirectUrl = redirectUrl
                    }
                },
                signers = signersarray
            });
            string api = ApiUrl + "/api/v2/signflows/createFlowOneStep";
            //Dictionary<string, string> headers = Http.SendHttp.buildHeaders(Config.AppKey, ESignAccessToken.GetAccessToken());
            //// HTTP请求内容类型
            //string contentType = "application/json";
            //string result = Http.SendHttp.Post(api, headers, json, contentType);
            string result = await api
            .WithHeader("X-Tsign-Open-App-Id", appKey)
            .WithHeader("X-Tsign-Open-Token", ESignAccessToken.GetAccessToken(appKey, AppSecret))
            .PostAsync().ReceiveString();
            var r = Newtonsoft.Json.Linq.JToken.Parse(result);
            if (r.Value<int>("code") == 0)
            {
                return r.Value<JToken>("data")!.Value<string>("flowId")!;
            }
            else
            {
                throw new Exception("请求错误:" + result);
            }
        }
        /// <summary>
        /// 直接获取可签署的地址
        /// </summary>
        /// <param name="name"></param>
        /// <param name="templateId">模板编号</param>
        /// <param name="simpleFormFields"></param>
        /// <param name="businessScene">场景</param>
        /// <param name="orgId">组织机构账号</param>
        /// <param name="accountId">签署人的账号id</param>
        /// <param name="noticeDeveloperUrl">通知开发者地址</param>
        /// <param name="redirectUrl">签署完成重定向地址</param>
        /// <param name="XYZ">动态坐标json字符串（合同模板表XYZ字段的值）</param>
        /// <param name="flowId"></param>
        /// <returns></returns>
        public async Task<string> GetSignUrl_1(string name, string templateId, JToken simpleFormFields, string businessScene, string orgId, string accountId, string noticeDeveloperUrl, string redirectUrl, string XYZ)
        {
            string fileId = await CreateContract(templateId, name, simpleFormFields) ?? "";
            string flowId = await CreateFlows_1(fileId, businessScene, orgId, accountId, noticeDeveloperUrl, redirectUrl, XYZ);
            return await GetSignUrl(flowId, accountId);
        }
        #endregion
        /// <summary>
        /// 获取下载合同的链接
        /// </summary>
        /// <param name="flowId">文档id</param>
        /// <returns></returns>
        public async Task<string> GetDownloadUrl(string flowId,string appKey,string accessToken)
        {
            string api = ApiUrl + "/v1/signflows/" + flowId + "/documents";
            string result = await api
           .WithHeader("X-Tsign-Open-App-Id", appKey)
           .WithHeader("X-Tsign-Open-Token", ESignAccessToken.GetAccessToken(appKey, AppSecret))
           .GetStringAsync();
            //Dictionary<string, string> headers = Http.SendHttp.buildHeaders(appKey, ESignAccessToken.GetAccessToken());
            //// HTTP请求内容类型
            //string contentType = "application/json";
            //string result = Http.SendHttp.Get(api, headers, contentType);
            var r = JToken.Parse(result);
            if (r.Value<int>("code") == 0)
            {
                var docs = r!.Value<JToken>("data")!.Value<JArray>("docs")!;
                return docs[0]["fileUrl"]!.ToString() ?? "";
            }
            else
            {
                throw new Exception("请求错误");
            }
        }
    }
}
