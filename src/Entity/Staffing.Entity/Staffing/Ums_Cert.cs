using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

[Table("ums_cert")]
public class Ums_Cert
{
    [Key]
    [Column("Id", TypeName = "varchar(20)")]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    [Column("UmsRegId", TypeName = "varchar(20)")]
    public string? UmsRegId { get; set; }

    [Column("UserId", TypeName = "varchar(20)")]
    public string UserId { get; set; } = default!;

    [Column("AccountNo", TypeName = "varchar(20)")]
    public string AccountNo { get; set; } = default!;

    /// <summary>
    /// 商户ID
    /// </summary>
    [Required(ErrorMessage = "商户ID不能为空")]
    [Column("MerchantId")]
    [StringLength(20)]
    public string MerchantId { get; set; } = default!;

    [Column("Dn", TypeName = "varchar(255)")]
    public string? Dn { get; set; }

    [Column("PassWord", TypeName = "varchar(255)")]
    public string? PassWord { get; set; }

    [Column("PfxByte")]
    public byte[] PfxByte { get; set; } = default!;

    [Column("PublicKeyString", TypeName = "varchar(500)")]
    public string PublicKeyString { get; set; } = default!;

    [Column("EndTime")]
    public DateTime EndTime { get; set; } = default!;

    [Column("CreateTime")]
    public DateTime CreateTime { get; set; } = DateTime.Now;

    [Column("UpdateTime")]
    public DateTime UpdateTime { get; set; } = DateTime.Now;

}