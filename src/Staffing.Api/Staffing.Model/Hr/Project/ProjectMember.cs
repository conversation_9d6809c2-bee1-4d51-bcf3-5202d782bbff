﻿using Config.CommonModel;
using Config.Enums;

namespace Staffing.Model.Hr.Project;

public class GetProjectMembers : QueryRequest
{
    /// <summary>
    /// 项目Id（必填）
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 项目成员状态（必填）
    /// </summary>
    public ProjectMemberStatus? Status { get; set; }

    /// <summary>
    /// 姓名、身份证号、电话
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 是否实名
    /// </summary>
    public bool? HasIdCard { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public ProjectMemberSource? Source { get; set; }

    /// <summary>
    /// 离职状态
    /// </summary>
    public ProjectMemberQuitStatus? QuitStatus { get; set; }

    /// <summary>
    /// 入离职起始时间
    /// </summary>
    public DateOnly? EntryOrQuitBeginTime { get; set; }

    /// <summary>
    /// 入离职结束时间
    /// </summary>
    public DateOnly? EntryOrQuitEndTime { get; set; }

    /// <summary>
    /// 操作起始时间
    /// </summary>
    public DateTime? BeginTime { get; set; }

    /// <summary>
    /// 操作结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

public class GetProjectMembersResponse : QueryResponse
{
    public List<ProjectMemberInfo> Rows { get; set; } = default!;
}

public class ProjectMemberInfo : UpdateProjectMember
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public ProjectMemberStatus Status { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public ProjectMemberSource Source { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public string? SourceName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string? SexName { get; set; }

    /// <summary>
    /// 用工形式
    /// </summary>
    public string? EmploymentModeName { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 入职次数
    /// </summary>
    public int InductionTimes { get; set; }

    /// <summary>
    /// 合同数量
    /// </summary>
    public int ContractNum { get; set; }

    /// <summary>
    /// 离职时间
    /// </summary>
    public DateOnly? QuitTime { get; set; }

    /// <summary>
    /// 审批通过时间
    /// </summary>
    public DateTime? AuditTime { get; set; }

    /// <summary>
    /// 计划转正日期
    /// </summary>
    public DateOnly? RegularDate { get; set; }

    /// <summary>
    /// 离职状态
    /// </summary>
    public ProjectMemberQuitStatus QuitStatus { get; set; }

    public DateTime? UpdatedTime { get; set; }

    public DateTime? CreatedTime { get; set; }
}

public class UpdateProjectMember
{
    /// <summary>
    /// Id（添加不用传）
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 项目Id（更新不用传）
    /// </summary>
    public string? ProjectId { get; set; } = default!;

    /// <summary>
    /// 证件类型
    /// </summary>
    public IdCardType? IdentityCardType { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdentityCard { get; set; } = string.Empty;

    /// <summary>
    /// 身份证姓名
    /// </summary>
    public string? IdentityCardName { get; set; } = string.Empty;

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 用工形式
    /// </summary>
    public ProjectMemberEmploymentMode? EmploymentMode { get; set; }

    /// <summary>
    /// 试用期（月），0=无试用期
    /// </summary>
    public int ProbationMonth { get; set; }

    /// <summary>
    /// 应聘职位
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 入职部门
    /// </summary>
    public string? Department { get; set; }

    /// <summary>
    /// 入职时间
    /// </summary>
    public DateOnly? EntryTime { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? EMail { get; set; }

    /// <summary>
    /// 描述（预留字段）
    /// </summary>
    public string? Describe { get; set; }
}

public class UpdateProjectMemberResponse
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }
}

public class DeleteProjectMember
{
    /// <summary>
    /// Ids
    /// </summary>
    public List<string>? Ids { get; set; }
}

public class ProjectMemberEntry
{
    /// <summary>
    /// Ids
    /// </summary>
    public List<string>? Ids { get; set; }

    /// <summary>
    /// 用工形式
    /// </summary>
    public ProjectMemberEmploymentMode? EmploymentMode { get; set; }

    /// <summary>
    /// 试用期（月），0=无试用期
    /// </summary>
    public int ProbationMonth { get; set; }

    /// <summary>
    /// 应聘职位
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 入职部门
    /// </summary>
    public string? Department { get; set; }

    /// <summary>
    /// 入职时间
    /// </summary>
    public DateOnly? EntryTime { get; set; }

    /// <summary>
    /// 描述（预留字段）
    /// </summary>
    public string? Describe { get; set; }
}

public class ProjectMemberQuit
{
    /// <summary>
    /// Ids
    /// </summary>
    public List<string>? Ids { get; set; }

    /// <summary>
    /// 离职时间
    /// </summary>
    public DateOnly? QuitTime { get; set; }

    /// <summary>
    /// 离职状态
    /// </summary>
    public ProjectMemberQuitStatus? QuitStatus { get; set; }

    /// <summary>
    /// 离职原因
    /// </summary>
    public string? Describe { get; set; }
}

public class EntryImport
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 文件地址（Excel导入需要）
    /// </summary>
    public string? FileUrl { get; set; }

    /// <summary>
    /// 文件名称
    /// </summary>
    public string? FileName { get; set; }
}

public class EntryImportForNoah
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 项目编号（数字诺亚导入需要）
    /// </summary>
    public string? NoahProjectNo { get; set; }

    /// <summary>
    /// 数字诺亚项目名称
    /// </summary>
    public string? NoahProjectName { get; set; }
}

public class GetNoahProjectByNo
{
    /// <summary>
    /// 项目编号（数字诺亚导入需要）
    /// </summary>
    public string? NoahProjectNo { get; set; }
}

public class GetNoahProjectByNoResponse
{
    /// <summary>
    /// 数字诺亚项目名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 数字诺亚负责人姓名
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// 项目人数
    /// </summary>
    public int Members { get; set; }
}