﻿using Flurl;
using Flurl.Http;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using ServiceStack.Text;
using Noah.Aliyun.Storage;
using Config;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Hosting;
using Config.CommonModel.ESign;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Transient)]
public class ESignHelper
{
    private readonly IObjectStorage _objectStorage;
    private readonly ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private RequestContext _user;
    private readonly LogManager _log;
    public ESignHelper(IObjectStorage objectStorage, IOptionsSnapshot<ConfigManager> config,
    IHostEnvironment hostingEnvironment, RequestContext user, LogManager log)
    {
        _objectStorage = objectStorage;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _user = user;
        _log = log;
    }

    /// <summary>
    /// 创建个人签署账号
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<CreateUserAccountResponse> CreateUserAccount(CreateUserAccount model)
    {
        var result = await Post<CreateUserAccountResponse>("v1/accounts/createByThirdPartyUserId", model);
        return result;
    }

    /// <summary>
    /// 创建机构签署账号
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<CreateOrgAccountResponse> CreateOrgAccount(CreateOrgAccount model)
    {
        var result = await Post<CreateOrgAccountResponse>("v1/organizations/createByThirdPartyUserId", model);
        return result;
    }

    /// <summary>
    /// 创建机构签署账号
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<UpdateOrgAccountResponse> UpdateOrgAccount(UpdateOrgAccount model)
    {
        var result = await Put<UpdateOrgAccountResponse>($"v1/organizations/{model.orgId}", model);
        return result;
    }

    /// <summary>
    /// 查询个人签署账号（通过accountId查询）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetUserAccountResponse> GetUserAccount(GetUserAccount model)
    {
        var result = await Get<GetUserAccountResponse>($"v1/accounts/{model.accountId}", model);
        return result;
    }

    /// <summary>
    /// 查询机构签署账号（通过orgId查询）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetOrgAccountResponse> GetOrgAccount(GetOrgAccount model)
    {
        var result = await Get<GetOrgAccountResponse>($"v1/organizations/{model.orgId}", model);
        return result;
    }

    /// <summary>
    /// 查询机构签署账号（通过thirdPartyUserId查询）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetOrgAccountResponse> GetOrgAccountByThirdId(GetOrgAccount model)
    {
        var result = await Get<GetOrgAccountResponse>($"v1/organizations/getByThirdId", model);
        return result;
    }

    /// <summary>
    /// 注销个人签署账号（通过accountId注销）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<object> DeleteUserAccount(DeleteUserAccount model)
    {
        var result = await Delete<object>($"v1/accounts/{model.accountId}");
        return result;
    }

    /// <summary>
    /// 注销机构签署账号（通过orgId注销）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<object> DeleteOrgAccount(DeleteOrgAccount model)
    {
        var result = await Delete<object>($"v1/organizations/{model.orgId}");
        return result;
    }

    /// <summary>
    /// 发起运营商3要素核身认证
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<Telecom3FactorsResponse> Telecom3Factors(Telecom3Factors model)
    {
        var result = await Post<Telecom3FactorsResponse>("v2/identity/auth/api/individual/telecom3Factors", model);
        return result;
    }

    /// <summary>
    /// 运营商短信验证码校验
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<object> Telecom3FactorsSmsCheck(Telecom3FactorsSmsCheck model)
    {
        var result = await Post<object>($"v2/identity/auth/pub/individual/{model.flowId}/telecom3Factors", model);
        return result;
    }

    /// <summary>
    /// 发起个人刷脸核身认证
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<FaceIndividualResponse> FaceCheck(FaceIndividual model)
    {
        var result = await Post<FaceIndividualResponse>($"v2/identity/auth/api/individual/face", model);
        return result;
    }

    /// <summary>
    /// 个人实名刷脸结果核对
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<FaceCheckResponse> FaceCheck(FaceCheck model)
    {
        var result = await Post<FaceCheckResponse>($"v2/identity/auth/api/individual/{model.flowId}/face/check", model);
        return result;
    }

    /// <summary>
    /// 创建个人/机构图片印章
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<CreateSealsResponse> CreateSeals(CreateSeals model)
    {
        var result = await Post<CreateSealsResponse>($"v1/accounts/{model.accountId}/seals/image", model);
        return result;
    }

    /// <summary>
    /// 删除机构印章
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<object> DeleteSeals(DeleteSeals model)
    {
        var result = await Delete<object>($"v1/organizations/{model.orgId}/seals/{model.sealId}");
        return result;
    }

    /// <summary>
    /// 查询机构印章
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetSealsResponse> GetSeals(GetSeals model)
    {
        var result = await Get<GetSealsResponse>($"v1/organizations/{model.orgId}/seals", model);
        return result;
    }

    /// <summary>
    /// 设置静默签署授权
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<object> SignAuth(SignAuth model)
    {
        var result = await Post<object>($"v1/signAuth/{model.accountId}", model);
        return result;
    }

    /// <summary>
    /// 获取模板文件上传地址
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<CreateDocTemplatesResponse> CreateDocTemplates(CreateDocTemplates model)
    {
        var result = await Post<CreateDocTemplatesResponse>($"v1/docTemplates/createByUploadUrl", model);
        return result;
    }

    /// <summary>
    /// 查询模板文件上传状态
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetDocTemplatesBaseInfoResponse> GetDocTemplatesBaseInfo(GetDocTemplatesBaseInfo model)
    {
        var result = await Get<GetDocTemplatesBaseInfoResponse>($"v1/docTemplates/{model.templateId}/getBaseInfo", model);
        return result;
    }

    /// <summary>
    /// 查询模板文件详情
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetDocTemplateResponse> GetDocTemplate(GetDocTemplate model)
    {
        var result = await Get<GetDocTemplateResponse>($"v1/docTemplates/{model.templateId}", model);
        return result;
    }

    /// <summary>
    /// 获取模板设置页面地址
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetTemSettingUrlResponse> GetTemSettingUrl(GetTemSettingUrl model)
    {
        var result = await Get<GetTemSettingUrlResponse>($"v1/docTemplates/{model.templateId}/getSettingUrl", model);
        return result;
    }

    /// <summary>
    /// 填充内容生成PDF
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<CreateFileByTemplateResponse> CreateFileByTemplate(CreateFileByTemplate model)
    {
        var result = await Post<CreateFileByTemplateResponse>($"v1/files/createByTemplate", model);
        return result;
    }

    /// <summary>
    /// 查询PDF文件详情
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetFileResponse> GetFile(GetFile model)
    {
        var result = await Post<GetFileResponse>($"v1/files/{model.fileId}", model);
        return result;
    }

    /// <summary>
    /// 一步发起签署
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<CreateFlowOneStepResponse> CreateFlowOneStep(CreateFlowOneStep model)
    {
        var result = await Post<CreateFlowOneStepResponse>($"api/v2/signflows/createFlowOneStep", model);
        return result;
    }

    /// <summary>
    /// 签署流程查询
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetFlowOneStepResponse> GetFlowOneStep(GetFlowOneStep model)
    {
        var result = await Get<GetFlowOneStepResponse>($"v1/signflows/{model.flowId}", model);
        return result;
    }

    /// <summary>
    /// 签署流程开启
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<object> StartSignflows(StartSignflows model)
    {
        var result = await Put<object>($"v1/signflows/{model.flowId}/start", model);
        return result;
    }

    /// <summary>
    /// 获取签署地址
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetSignflowsExecuteUrlResponse> GetSignflowsExecuteUrl(GetSignflowsExecuteUrl model)
    {
        var result = await Get<GetSignflowsExecuteUrlResponse>($"v1/signflows/{model.flowId}/executeUrl", model);
        return result;
    }

    /// <summary>
    /// 签署流程撤销
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<object> CreateContractByTemplate(RevokeSignflows model)
    {
        var result = await Put<object>($"v1/signflows/{model.flowId}/revoke", model);
        return result;
    }

    /// <summary>
    /// 通用Get请求
    /// </summary>
    /// <param name="path"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<T> Get<T>(string path, object param)
    {
        var token = await GetAccessToken();

        // Console.WriteLine($"{_config.ESign!.Domain}/{path}");
        // Console.WriteLine(_config.ESign!.AppId);
        // Console.WriteLine(token);

        var restr = await $"{_config.ESign!.Domain}/{path}"
        .WithHeader("X-Tsign-Open-App-Id", _config.ESign!.AppId)
        .WithHeader("X-Tsign-Open-Token", token)
        .SetQueryParams(param)
        .GetStringAsync();

        var resp = JsonSerializer.DeserializeFromString<ESignResponse<T>>(restr);

        CheckESignCode(resp.code, resp.message);
        return resp.data!;
    }

    /// <summary>
    /// 通用Post请求
    /// </summary>
    /// <param name="path"></param>
    /// <param name="param"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<T> Post<T>(string path, object param)
    {
        var token = await GetAccessToken();
        var restr = await $"{_config.ESign!.Domain}/{path}"
        .WithHeader("X-Tsign-Open-App-Id", _config.ESign!.AppId)
        .WithHeader("X-Tsign-Open-Token", token)
        .PostJsonAsync(param).ReceiveString();

        var resp = JsonSerializer.DeserializeFromString<ESignResponse<T>>(restr);

        CheckESignCode(resp.code, resp.message);
        return resp.data!;
    }

    /// <summary>
    /// 通用Put请求
    /// </summary>
    /// <param name="path"></param>
    /// <param name="param"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<T> Put<T>(string path, object param)
    {
        var token = await GetAccessToken();
        var restr = await $"{_config.ESign!.Domain}/{path}"
        .WithHeader("X-Tsign-Open-App-Id", _config.ESign!.AppId)
        .WithHeader("X-Tsign-Open-Token", token)
        .PutJsonAsync(param).ReceiveString();

        var resp = JsonSerializer.DeserializeFromString<ESignResponse<T>>(restr);

        CheckESignCode(resp.code, resp.message);
        return resp.data!;
    }

    /// <summary>
    /// 通用Delete请求
    /// </summary>
    /// <param name="path"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<T> Delete<T>(string path)
    {
        var token = await GetAccessToken();
        var restr = await $"{_config.ESign!.Domain}/{path}"
        .WithHeader("X-Tsign-Open-App-Id", _config.ESign!.AppId)
        .WithHeader("X-Tsign-Open-Token", token)
        .DeleteAsync().ReceiveString();

        var resp = JsonSerializer.DeserializeFromString<ESignResponse<T>>(restr);

        CheckESignCode(resp.code, resp.message);
        return resp.data!;
    }

    /// <summary>
    /// 获取e签宝token
    /// </summary>
    /// <param name="url"></param>
    /// <param name="method"></param>
    /// <param name="param"></param>
    public async Task<string> GetAccessToken()
    {
        var cacheKey = $"esigntoken1";
        var token = MyRedis.Client.Get(cacheKey);

        if (!string.IsNullOrWhiteSpace(token))
            return token;

        //加锁获取token
        var lockerKey = $"esigntoken1Lk";
        using var locker = await MyRedis.Lock(lockerKey, 15, false);

        if (locker == null)
            throw new Exception("获取e签宝accesstoken锁失败");

        token = MyRedis.Client.Get(cacheKey);

        if (!string.IsNullOrWhiteSpace(token))
            return token;

        var url = $"{_config.ESign!.Domain}/v1/oauth2/access_token?appId={_config.ESign.AppId}&secret={_config.ESign.Secret}&grantType=client_credentials";

        //获取e签宝token
        var tokenResult = await url.GetJsonAsync<ESignResponse<ESignAccessToken>>();

        if (tokenResult.code != 0)
            throw new Exception($"获取e签宝token失败:{tokenResult.code}_{tokenResult.message}");

        token = tokenResult.data!.token;

        MyRedis.Client.Set(cacheKey, token, 3000);

        if (string.IsNullOrWhiteSpace(token))
            throw new Exception("获取e签宝accesstoken失败");

        return token;
    }

    private void CheckESignCode(int code, string message)
    {
        if (code == 0)
            return;

        switch (code)
        {
            case 30500000:
                throw new BadRequestException("请核实入参格式是否正确");
            case 30500101:
            case 30500116:
            case 30501002:
            case 30503056:
            case 30500002:
            case 30500003:
            case 30500004:
            case 30501005:
            case 30503002:
            case 30503004:
            case 30503006:
            case 1560102:
                throw new BadRequestException(message);
            default:
                throw new Exception($"E签宝接口失败：{code}_{message}");
        }
    }
}