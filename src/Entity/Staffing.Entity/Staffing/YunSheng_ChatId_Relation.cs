﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Staffing.Entity.Staffing
{
    [Table("yunsheng_chatid_relation")]
    public class YunSheng_ChatId_Relation
    {
        [Key]
        public string Id { get; set; } = EntityTools.SnowflakeId();
        public string? UserId {  get; set; }
        //协同职位id
        public string? TeamPostId {  get; set; }
        //HrId
        public string? AdviserImId {  get; set; }
        //云生会话ID
        public string? YunshengChatId {  get; set; }
        public DateTime CreatedTime { get; set; }
        public DateTime UpdatedTime { get; set; }
    }
}
