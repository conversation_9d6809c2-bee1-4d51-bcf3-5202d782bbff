﻿using Config;
using Config.CommonModel;
using Config.Enums;

namespace Staffing.Model.Seeker.User;

public class SeekerLogin
{
    /// <summary>
    /// 登录类型
    /// </summary>
    public ClientType Type { get; set; } = ClientType.SeekerApplet;

    /// <summary>
    /// 表示哪儿个小程序(零工市场需要)
    /// </summary>
    public string? AppId { get; set; }

    /// <summary>
    /// 微信code/快手Code
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 微信EncryptedData
    /// </summary>
    public string? EncryptedData { get; set; }

    /// <summary>
    /// 微信Iv
    /// </summary>
    public string? Iv { get; set; }

    /// <summary>
    /// 密钥
    /// </summary>
    public string? Key { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }
}

public class SeekerRefreshToken
{
    /// <summary>
    /// 刷新token
    /// </summary>
    public string? RefreshToken { get; set; }
}

public class SeekerLoginResponse : TokenInfo
{
    /// <summary>
    /// 顾问Id，若用户不是通过分享的方式进入小程序，服务器会取最近的顾问Id给前端
    /// </summary>
    public string AdviserId { get; set; } = Constants.PlatformHrId;
}

//临时，抖音审核
public class SeekerLoginForDy : SeekerLogin
{
}

// public class SeekerGetTokenInfo
// {
//     public string? AccessToken { get; set; }
//     public string? RefreshToken { get; set; }
//     public int? TokenExpiresTime { get; set; }
// }