﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Infrastructure.QYWechat.Model
{
    /// <summary>
    /// 企业微信回调参数
    /// </summary>
    public class QYWechatCallbackRequestQuery
    {
        /// <summary>
        /// 企业微信加密签名，msg_signature结合了企业填写的token、请求中的timestamp、nonce参数、加密的消息体
        /// </summary>
        [FromQuery]
        public string? Msg_Signature { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        [FromQuery]
        public string TimeStamp { get; set; } = default!;

        /// <summary>
        /// 随机数
        /// </summary>
        [FromQuery]
        public string? Nonce { get; set; }

        /// <summary>
        /// 加密的字符串。需要解密得到消息内容明文，解密后有random、msg_len、msg、CorpID四个字段，其中msg即为消息内容明文
        /// *仅在验证URL有效性时会传此参数
        /// </summary>
        [FromQuery]
        public string? Echostr { get; set; }
    }

    /// <summary>
    /// 企业微信回调POST参数
    /// </summary>
    [XmlRoot("xml")] // 对应XML根节点
    public class QYWechatCallbackRequestPost_Base
    {
        /// <summary>
        /// 企业微信的CorpID
        /// </summary>
        [XmlElement("ToUserName")]
        public string? ToUserName { get; set; }

        /// <summary>
        /// 此事件该值固定为sys，表示该消息由系统生成
        /// </summary>
        [XmlElement("FromUserName")]
        public string? FromUserName { get; set; }

        /// <summary>
        /// 消息创建时间 （整型）
        /// </summary>
        [XmlElement("CreateTime")]
        public long CreateTime { get; set; }

        /// <summary>
        /// 消息的类型，此时固定为event
        /// </summary>

        [XmlAttribute("MsgType")]
        public int MsgType { get; set; }

        /// <summary>
        /// 事件的类型，此时固定为change_external_contact
        /// </summary>
        [XmlElement("Event")]
        public string? Event { get; set; }

        /// <summary>
        /// 此时固定为add_external_contact
        /// </summary>
        [XmlElement("ChangeType")]
        public string? ChangeType { get; set; }
    }

    /// <summary>
    /// 企业微信回调POST参数(企业微信群变更回调Model)
    /// </summary>
    [XmlRoot("xml")] // 对应XML根节点
    public class QYWechatCallbackRequestPost_GroupChat : QYWechatCallbackRequestPost_Base
    {
        /// <summary>
        /// 添加此用户的「联系我」方式配置的state参数，或在获客链接中指定的customer_channel参数，可用于识别添加此用户的渠道
        /// </summary>
        [XmlElement("ChatId")]
        public string? ChatId { get; set; }

        #region 当【客户群变更事件】时才会有的字段
        /// <summary>
        /// 变更详情。目前有以下几种：
        /// add_member : 成员入群
        /// del_member : 成员退群
        /// change_owner : 群主变更
        /// change_name : 群名变更
        /// change_notice : 群公告变更
        /// </summary>
        [XmlElement("UpdateDetail")]
        public string? UpdateDetail { get; set; }

        /// <summary>
        /// 当是成员入群时有值。表示成员的入群方式
        /// 0 - 由成员邀请入群（包括直接邀请入群和通过邀请链接入群）
        /// 3 - 通过扫描群二维码入群
        /// </summary>
        [XmlElement("JoinScene")]
        public int JoinScene { get; set; }

        /// <summary>
        /// 当是成员退群时有值。表示成员的退群方式
        /// 0 - 自己退群
        /// 1 - 群主/群管理员移出
        /// </summary>
        [XmlElement("QuitScene")]
        public int QuitScene { get; set; }

        /// <summary>
        /// 当是成员入群或退群时有值。表示成员变更数量
        /// </summary>
        [XmlElement("MemChangeCnt")]
        public int MemChangeCnt { get; set; }

        /// <summary>
        /// 当是成员入群或退群时有值。变更的成员列表
        /// </summary>
        [XmlArray("MemChangeList")]
        [XmlArrayItem("Item")]
        public List<string>? MemChangeList { get; set; }

        /// <summary>
        /// 当是成员入群或退群时有值。 变更前的群成员版本号
        /// </summary>
        [XmlElement("LastMemVer")]
        public string? LastMemVer { get; set; }

        /// <summary>
        /// 当是成员入群或退群时有值。变更后的群成员版本号
        /// </summary>
        [XmlElement("CurMemVer")]
        public string? CurMemVer { get; set; }
        #endregion
    }
}
