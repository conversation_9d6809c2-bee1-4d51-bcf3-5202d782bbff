using System.Linq.Expressions;
using Config;
using Config.CommonModel;
using EntityFrameworkCore.AutoHistory.Extensions;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Model.Hr.Recruit;
using Staffing.Core.Interfaces.Hr;
using Config.Enums;
using Infrastructure.Aliyun;
using Microsoft.EntityFrameworkCore;
using ServiceStack.Text;
using Newtonsoft.Json.Linq;
using Noah.ImExportTools.Native;
using Infrastructure.NoahCommon;
using ServiceStack;
using Config.CommonModel.ThirdTalentInfo;
using Staffing.Model.Hr.Dashboard;
using Staffing.Core.Interfaces.Common;
using MiniExcelLibs;
using Flurl.Http;
using Config.CommonModel.Business;
using Staffing.Model.Hr.Post;
using Microsoft.Extensions.Hosting;
using Infrastructure.CommonService.ShareProfit;

namespace Staffing.Core.Services.Service.Hr;

/// <summary>
/// 招聘流程服务
/// </summary>
[Service(ServiceLifetime.Transient)]
public class RecruitService : IRecruitService
{
    private readonly StaffingContext _context;
    private readonly LogManager _log;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private readonly CommonDicService _commonDicService;
    private readonly WeChatHelper _weChatHelper;
    private readonly SmsHelper _smsHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly Noah.Aliyun.Storage.IObjectStorage _objectStorageService;
    private readonly ParseResume _parseResume;
    private readonly CommonUserService _commonUserService;
    private readonly CommonVirtualService _commonVirtualService;
    private readonly CommonPostOrder _commonPostOrder;
    private readonly ProfitService _profitService;
    private readonly PaymentStep _paymentStep;
    private readonly ICommonService _commonService;

    /// <summary>
    /// 注入
    /// </summary>
    public RecruitService(StaffingContext context, LogManager log, RequestContext user, ProfitService profitService,
        IOptionsSnapshot<ConfigManager> config, CacheHelper cacheHelper, CommonDicService commonDicService,
        WeChatHelper weChatHelper, SmsHelper smsHelper, Noah.Aliyun.Storage.IObjectStorage objectStorageService, IHostEnvironment hostingEnvironment,
        ParseResume parseResume, CommonUserService commonUserService, CommonVirtualService commonVirtualService,
        CommonPostOrder commonPostOrder, PaymentStep paymentStep, ICommonService commonService)
    {
        _context = context;
        _log = log;
        _user = user;
        _config = config.Value;
        _cacheHelper = cacheHelper;
        _commonDicService = commonDicService;
        _weChatHelper = weChatHelper;
        _smsHelper = smsHelper;
        _hostingEnvironment = hostingEnvironment;
        _objectStorageService = objectStorageService;
        _parseResume = parseResume;
        _commonUserService = commonUserService;
        _commonVirtualService = commonVirtualService;
        _commonPostOrder = commonPostOrder;
        _profitService = profitService;
        _paymentStep = paymentStep;
        _commonService = commonService;
    }

    #region 招聘流程列表相关方法

    /// <summary>
    /// 查询当前用户是否有权限
    /// </summary>
    /// <param name="projectId"></param>
    /// <returns></returns>
    public GetRecruitAuthorityResponse GetRecruitAuthority(string projectId)
    {
        GetRecruitAuthorityResponse retMdoel = new GetRecruitAuthorityResponse();
        if (string.IsNullOrEmpty(projectId))
        {
            retMdoel.IsOperable = false;
            return retMdoel;
        }

        var projectModel = _context.Project.Where(o => o.ProjectId == projectId && o.HrId == _user.Id)
            .FirstOrDefault();
        if (projectModel is null)
        {
            retMdoel.IsOperable = false;
        }
        else
        {
            retMdoel.IsOperable = true;
        }

        return retMdoel;
    }

    /// <summary>
    /// 获取项目职位列表
    /// </summary>
    /// <param name="projectId"></param>
    /// <returns></returns>
    public GetRecruitProjectPostListResponse GetRecruitProjectPostList(string projectId)
    {
        GetRecruitProjectPostListResponse retModel = new GetRecruitProjectPostListResponse();

        var mm = _context.Recruit.Where(o => o.Post_Delivery.Post.ProjectId == projectId).Count();

        retModel.Rows = _context.Recruit.Where(o => o.Post_Delivery.Post.ProjectId == projectId).Select(o =>
            new RecruitProjectPostList
            {
                Id = o.Post_Delivery.PostId,
                Name = o.Post_Delivery.Post.Name
            }).Distinct().ToList();

        return retModel;
    }

    /// <summary>
    /// 获取招聘流程列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public GetRecentRecruitsResponse GetRecruitListByProjectId(RecruitListRequest model)
    {
        if (string.IsNullOrEmpty(model.ProjectId) && string.IsNullOrEmpty(model.PostId))
            throw new BadRequestException("请求参数错误！");

        if (string.IsNullOrWhiteSpace(model.ProjectId))
            model.ProjectId = _context.Post.Where(o => o.PostId == model.PostId).Select(o => o.ProjectId).FirstOrDefault();

        bool IsOriginal = false;
        //判断当前用户是否原创hr
        var projectModel = _context.Project.Where(o => o.ProjectId == model.ProjectId).FirstOrDefault();
        if (projectModel is null)
        {
            throw new BadRequestException("项目不存在！");
        }

        if (projectModel.HrId == _user.Id)
        {
            IsOriginal = true;
        }

        GetRecentRecruitsResponse retModel = new GetRecentRecruitsResponse();

        var region = _commonDicService.GetRegion();

        var predicate = PredicateBuilder.New<Recruit>(o => o.Post_Delivery.Post.ProjectId == model.ProjectId);

        if (IsOriginal)
        {
            predicate.And(o => o.HrId == _user.Id);
        }
        else
        {
            predicate.And(o => o.Post_Delivery.Post_Team.Project_Team.HrId == _user.Id);
        }

        if (!string.IsNullOrEmpty(model.Search))
        {
            predicate = predicate.And(o => o.User_Seeker.NickName.Contains(model.Search) ||
                                           o.User_Seeker.User_Resume.School!.Contains(model.Search) ||
                                           o.User_Seeker.User.Mobile.Contains(model.Search) ||
                                           o.User_Seeker.User_Resume.User_Work.Any(
                                               m => m.Company.Contains(model.Search)));
        }

        if (!string.IsNullOrEmpty(model.PostId))
            predicate = predicate.And(o => o.Post_Delivery.PostId == model.PostId);

        if (!string.IsNullOrEmpty(model.LabelId) && (model.RecruitStatus == RecruitStatus.HrScreening ||
                                                     model.RecruitStatus == RecruitStatus.InterviewerScreening ||
                                                     model.RecruitStatus == RecruitStatus.Interview))
        {
            predicate = predicate.And(o => o.Recruit_Label.Any(m => m.Dic_Recruit_Label.Id == model.LabelId));
        }

        // switch (model.RecruitStatus)
        // {
        //     case RecruitStatus.HrScreening:
        //         predicate = predicate.And(o => o.Status == RecruitStatus.HrScreening);
        //         break;
        //     case RecruitStatus.InterviewerScreening:
        //         predicate = predicate.And(o => o.Status == RecruitStatus.InterviewerScreening);
        //         if (!string.IsNullOrEmpty(model.InterviewerId))
        //         {
        //             predicate = predicate.And(o => o.Recruit_Interviewer_Todo.OrderByDescending(s => s.CreatedTime).Take(1).Any(m => m.Type == RecruitInterviewerTodoType.InterviewScreen && m.InterviewerId == model.InterviewerId));
        //         }

        //         if (model.ScreenStatus is not null)
        //         {
        //             predicate = predicate.And(o => o.Recruit_Interviewer_Screen.OrderByDescending(s => s.CreatedTime).Take(1).Any(m => m.Status == model.ScreenStatus));
        //         }
        //         break;
        //     case RecruitStatus.Interview:
        //         predicate = predicate.And(o => o.Status == RecruitStatus.Interview);
        //         if (!string.IsNullOrEmpty(model.InterviewerId))
        //         {
        //             predicate = predicate.And(o => o.Recruit_Interviewer_Todo.OrderByDescending(s => s.CreatedTime).Take(1).Any(m => m.Type == RecruitInterviewerTodoType.Interview && m.InterviewerId == model.InterviewerId));
        //         }

        //         if (model.InterviewProcess is not null)
        //         {
        //             predicate = predicate.And(o => o.Recruit_Interview.OrderByDescending(s => s.CreatedTime).Take(1).Any(m => m.Process == model.InterviewProcess));
        //         }

        //         if (model.InterviewOutcome is not null)
        //         {
        //             predicate = predicate.And(o => o.Recruit_Interview.OrderByDescending(s => s.CreatedTime).Take(1).Any(m => m.Outcome == model.InterviewOutcome));
        //         }
        //         break;
        //     case RecruitStatus.Offer:
        //         predicate = predicate.And(o => o.Status == RecruitStatus.Offer);
        //         break;
        //     case RecruitStatus.Induction:
        //         predicate = predicate.And(o => o.Status == RecruitStatus.Induction);
        //         break;
        //     case RecruitStatus.Yaoyue:
        //         predicate = predicate.And(o => o.Status == RecruitStatus.InterviewerScreening
        //         && o.Recruit_Interviewer_Screen.OrderByDescending(o => o.CreatedTime).FirstOrDefault().Status == RecruitInterviewerScreenStatus.Adopt);
        //         break;
        //     case RecruitStatus.Jifei:
        //         predicate = predicate.And(o => !o.Project_Teambounty.Deleted && o.Project_Teambounty.Status == BountyStatus.交付中
        //         && o.Project_Teambounty.IfStockOut == 1);
        //         break;
        //     case RecruitStatus.Guobao:
        //         predicate = predicate.And(o => !o.Project_Teambounty.Deleted && o.Project_Teambounty.Status == BountyStatus.交付成功);
        //         break;
        //     case RecruitStatus.FileAway:
        //         predicate = predicate.And(o => o.Status == RecruitStatus.FileAway);
        //         if (model.FileAway is not null)
        //         {
        //             predicate = predicate.And(o => o.FileAway == model.FileAway);
        //         }
        //         break;
        //     default:
        //         break;
        // }
        if (!model.Status.HasValue)
            model.Status = model.RecruitStatus;

        model.IsOriginal = IsOriginal;

        _commonService.GetRecruitList(model, retModel, predicate);

        //if (retModel.Rows.Count > 0)
        //{
        //    var ids = retModel.Rows.Select(s => s.Id).ToList();
        //    var bounties = _context.Project_Teambounty.Where(w => ids.Contains(w.RecruitId) && w.Status != BountyStatus.交付中).ToList();
        //    foreach (var i in retModel.Rows)
        //    {
        //        i.IsRealName = string.IsNullOrEmpty(i.IdentityCard) ? false : true;
        //        i.SexName = i.Sex == null ? null : i.Sex.GetDescription();
        //        i.Age = i.Birthday == null ? null : (DateTime.Now.Year - i.Birthday.Value.Year);
        //        i.OccupationName = i.Occupation == null ? null : i.Occupation.GetDescription();
        //        i.EducationName = i.Education == null ? null : i.Education.GetDescription();
        //        i.Location = region.Where(o => o.Id == i.RegionId).FirstOrDefault()?.Name;
        //        i.SourceName = i.Source == null ? null : i.Source.GetDescription();
        //        i.StatusName = i.Status == null ? null : i.Status.GetDescription();
        //        i.ActiveName = i.Active.GetDescription();
        //        i.EduExp = !string.IsNullOrEmpty(i.EduExpSchool) ? (i.EduExpSchool + "-" + i.EduExpMajor + "-" + i.EduExpEducation?.GetDescription()) + " " + i.EduExpEndTime?.ToString("yyyy/MM") : null;
        //        i.WorkExp = i.WorkExpClass != null ? (i.WorkExpClass.Company + "-" + i.WorkExpClass.Post + " " + i.WorkExpClass.BeginDate?.ToString("yyyy/MM") + "-" + i.WorkExpClass.EndDate?.ToString("yyyy/MM")) : null;

        //        if (i.Status == RecruitStatus.InterviewerScreening)
        //        {
        //            i.InterviewScreenStatusName = i.InterviewScreenStatus?.GetDescription();
        //        }

        //        if (i.Status == RecruitStatus.Interview)
        //        {
        //            i.InterviewerFeedbackName = i.InterviewerFeedback?.GetDescription();
        //            i.CandidateFeedbackName = i.CandidateFeedback?.GetDescription();
        //            i.InterviewProcessName = i.InterviewProcess?.GetDescription();
        //        }

        //        if (i.Status == RecruitStatus.FileAway)
        //        {
        //            i.FileAwayName = i.FileAway?.GetDescription();
        //        }

        //        if (i.Adviser.Equals("交付后显示"))// 交付失败也显示
        //        {
        //            var bounty = bounties.FirstOrDefault(f => f.RecruitId == i.Id);
        //            if (bounty != null)
        //            {
        //                i.Adviser = bounty.TeamHrName;
        //            }
        //        }
        //    }
        //}

        return retModel;
    }

    /// <summary>
    /// 获取招聘流程标签集合(总标签)
    /// </summary>
    /// <returns></returns>
    public RecruitLabelListResponse GetRecruitLabelList()
    {
        RecruitLabelListResponse retModel = new RecruitLabelListResponse();

        retModel.Rows = _context.Dic_Recruit_Label.Where(o => o.HrId == _user.Id).Select(m => new RecruitLabelClass()
        {
            Id = m.Id,
            Name = m.LabelName
        }).ToList();

        return retModel;
    }

    /// <summary>
    /// 添加招聘流程标签(总标签)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse AddRecruitLabel(AddRecruitLabelRequest model)
    {
        Dic_Recruit_Label addModel = new Dic_Recruit_Label()
        {
            HrId = _user.Id,
            LabelName = model.RecruitLabelName
        };

        _context.Dic_Recruit_Label.Add(addModel);
        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 删除招聘流程标签(总标签)
    /// </summary>
    /// <param name="labelId"></param>
    /// <returns></returns>
    public EmptyResponse DeleteRecruitLabel(string labelId)
    {
        if (labelId.Length <= 2)
        {
            throw new BadRequestException("无法删除固定标签！");
        }

        var dicRecruitLabelModel = _context.Dic_Recruit_Label.Where(o => o.HrId == _user.Id && o.Id == labelId)
            .FirstOrDefault();

        if (dicRecruitLabelModel == null)
        {
            throw new BadRequestException("内容不存在！");
        }

        _context.Dic_Recruit_Label.Remove(dicRecruitLabelModel);

        var recruitLabelList = _context.Recruit_Label.Where(o => o.DicLabelId == labelId).ToList();
        _context.Recruit_Label.RemoveRange(recruitLabelList);

        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 获取招聘流程标签
    /// </summary>
    /// <param name="recruitId"></param>
    /// <returns></returns>
    public RecruitLabelResponse GetRecruitLabel(string recruitId)
    {
        var RecruitModel = _context.Recruit.Where(o => o.RecruitId == recruitId).FirstOrDefault();
        if (null == RecruitModel)
        {
            throw new BadRequestException("招聘流程不存在！");
        }

        RecruitLabelResponse retModel = new RecruitLabelResponse();

        retModel.Rows = _context.Recruit_Label.Where(o => o.RecruitId == recruitId).Select(m => new RecruitLabelClass()
        {
            Id = m.Dic_Recruit_Label.Id,
            Name = m.Dic_Recruit_Label.LabelName
        }).ToList();

        return retModel;
    }

    /// <summary>
    /// 变更招聘流程标签
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse EditRecruitLabel(EditRecruitLabelRequest model)
    {
        if (string.IsNullOrEmpty(model.RecruitId))
        {
            throw new BadRequestException("请求参数错误！");
        }

        bool IsRecruitIdExist = _context.Recruit.Any(o => o.RecruitId == model.RecruitId && o.HrId == _user.Id);
        if (!IsRecruitIdExist)
        {
            throw new BadRequestException("招聘流程不存在！");
        }

        foreach (var i in model.DicLabelId)
        {
            bool IsDicLabelIdExist = _context.Dic_Recruit_Label.Any(o => o.Id == i);
            if (!IsDicLabelIdExist)
            {
                throw new BadRequestException("标签不存在！");
            }
        }

        var labelRemoveList = _context.Recruit_Label.Where(o => o.RecruitId == model.RecruitId).ToList();
        if (labelRemoveList.Count > 0)
        {
            _context.Recruit_Label.RemoveRange(labelRemoveList);
        }

        List<Recruit_Label> addList = new List<Recruit_Label>();
        foreach (var i in model.DicLabelId)
        {
            addList.Add(new Recruit_Label()
            {
                RecruitId = model.RecruitId,
                DicLabelId = i
            });
        }

        _context.Recruit_Label.AddRange(addList);
        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 获取项目面试官列表
    /// </summary>
    /// <param name="projectId"></param>
    /// <returns></returns>
    public ProjectInterviewerListResponse GetProjectInterviewerList(string projectId)
    {
        ProjectInterviewerListResponse retModel = new ProjectInterviewerListResponse();

        retModel.Rows = _context.Project_Interviewer.Where(o => o.ProjectId == projectId).Select(o =>
            new ProjectInterviewerModel
            {
                Id = o.Id,
                Name = o.Name,
                IsMain = o.Phone == o.Project.User_Hr.User.Mobile
            }).ToList();

        return retModel;
    }

    /// <summary>
    /// 获取招聘流程简历详情
    /// </summary>
    /// <param name="recruitId"></param>
    /// <returns></returns>
    public RecruitResumeDetailsResponse GetRecruitResumeDetails(string recruitId)
    {
        RecruitResumeDetailsResponse? retModel;
        // 线索大厅需要这个接口，并且不接单也能看到简历信息，所以去掉了条件
        retModel = _context.Recruit.Where(o => o.RecruitId == recruitId).Select(o => new RecruitResumeDetailsResponse
        {
            AcceptOrder = o.AcceptOrder,
            Name = o.User_Seeker.NickName,
            ResumeExtend = !string.IsNullOrEmpty(o.Resume_Buffer.Id),
            _Birthday = o.User_Seeker.User_Resume.Birthday,
            AttachmentUrl = o.User_Seeker.User_Resume_Attach.Url,
            HeadPortrait = o.User_Seeker.Avatar,
            _IdentityCard = o.User_Seeker.User.IdentityCard,
            Mailbox = o.User_Seeker.User_Resume.EMail,
            Mobile = o.User_Seeker.User.Mobile,
            WeChat = o.User_Seeker.User_Resume.WeChatNo,
            QQ = o.User_Seeker.User_Resume.Qq,
            Occupation = o.User_Seeker.User_Resume.Occupation,
            Education = o.User_Seeker.User_Resume.Education,
            Perfection = o.User_Seeker.User_Resume.Score,
            UserId = o.SeekerId,
            RecruitId = o.RecruitId,
            ProjectId = o.Post_Delivery.Post.ProjectId!,
            Sex = o.User_Seeker.User_Resume.Sex,
            SelfEvaluation = o.User_Seeker.User_Resume.Describe,
            Nature = o.User_Seeker.User_Resume.Nature,
            Skill = o.User_Seeker.User_Resume.Skill,
            Appearance = o.User_Seeker.User_Resume.Appearance,
            SkillsCert = o.User_Seeker.User_Resume.Certificate,
            HrName = o.User_Hr.NickName,
            HrPost = o.User_Hr.Post,
            SeekerTencentImId = o.User_Seeker.TencentImId,
            Enterprise = o.Post_Delivery.Post.Project.Agent_Ent.Name,
            HrAppletQrCode = o.User_Seeker.HrAppletQrCode,
            Active = Tools.GetOnlineStatus(o.User_Seeker.User_Extend.LoginTime),
            IsResumeExempt = o.Post_Delivery.Post.IsResumeExempt,
            EduSub = string.IsNullOrEmpty(o.User_Seeker.User_Resume.School) ? null : new RecruitResumeEduSub
            {
                Education = o.User_Seeker.User_Resume.Education,
                SchoolName = o.User_Seeker.User_Resume.School,
                MajorName = o.User_Seeker.User_Resume.Major,
                EndTime = o.User_Seeker.User_Resume.GraduationDate,
            },
            WorkSub = o.User_Seeker.User_Resume.User_Work.Select(m => new RecruitResumeWorkSub
            {
                CompanyName = m.Company,
                CompanyRemarks = m.Describe,
                PostName = m.Post,
                EndTime = m.EndDate,
                StartTime = m.BeginDate
            }).OrderByDescending(o => o.StartTime).ToList(),
            PracticeSub = o.User_Seeker.User_Resume.User_Campus.Select(m => new RecruitResumePracticeSub
            {
                PracticeName = m.Name,
                Experience = m.Describe,
                PostPrize = m.Award,
                EndTime = m.EndDate,
                StartTime = m.BeginDate
            }).OrderByDescending(o => o.StartTime).ToList()
        }).FirstOrDefault();

        if (retModel is null)
        {
            throw new BadRequestException("内容不存在！");
        }

        //整理数据
        retModel.ActiveName = retModel.Active.GetDescription();
        retModel.Age = retModel._Birthday == null ? null : DateTime.Now.Year - retModel._Birthday.Value.Year;
        retModel.IsRealName = !string.IsNullOrEmpty(retModel._IdentityCard);
        retModel.OccupationName = retModel.Occupation?.GetDescription();
        retModel.EducationName = retModel.Education?.GetDescription();
        retModel.SexName = retModel.Sex?.GetDescription();
        if (retModel.EduSub != null)
        {
            retModel.EduSub.EducationName = retModel.EduSub.Education?.GetDescription();
        }

        //拼接标签
        if (retModel.Nature.Count > 0 || retModel.Skill.Count > 0 || retModel.Appearance.Count > 0)
        {
            retModel.Label = retModel.Nature.Union(retModel.Skill).Union(retModel.Appearance).ToList();
        }

        //申请岗位统计
        List<RecruitPostList>? postlist;
        postlist = _context.Recruit
            .Where(o => o.Post_Delivery.Post.ProjectId == retModel.ProjectId && o.SeekerId == retModel.UserId)
            .OrderByDescending(o => o.CreatedTime).Select(o => new RecruitPostList()
            {
                RecruitId = o.RecruitId,
                PostName = o.PostName,
                IsTrue = o.RecruitId == recruitId ? true : false
            }).ToList();
        retModel.PostList = postlist.OrderByDescending(o => o.IsTrue).ToList();
        retModel.DeliveryTimes = postlist?.Count;

        //查询是否自己项目（主创与协同）
        if (string.IsNullOrEmpty(retModel.ProjectId))
        {
            retModel.SelfProject = false;
        }

        var projectModel = _context.Project
            .FirstOrDefault(o => o.HrId == _user.Id && o.ProjectId == retModel.ProjectId);
        if (projectModel is null)
        {
            retModel.SelfProject = false;
        }
        else
        {
            retModel.SelfProject = true;
        }
        var projectTeamConfig = _context.Project_Team_Config.FirstOrDefault(o => o.Creator == _user.Id);
        if (projectTeamConfig != null)
        {
            retModel.IsCrowdsource = true;
        }
        //如果不在人才库，隐藏电话
        if (string.IsNullOrWhiteSpace(retModel.UserId) ||
            !_context.Talent_Platform.Any(x => x.HrId == _user.Id && x.SeekerId == retModel.UserId))
        {
            retModel.Mobile = Tools.MobilToX(retModel.Mobile);
            retModel.Mailbox = Tools.MailToX(retModel.Mailbox);
            retModel.WeChat = Tools.WeChatToX(retModel.WeChat);
            retModel.QQ = Tools.QQToX(retModel.QQ);
        }

        return retModel;
    }

    /// <summary>
    /// 获取招聘流程记录列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public RecruitmentProcessResponse GetRecruitmentProcessList(RecruitmentProcessRequest model)
    {
        RecruitmentProcessResponse retModel = new RecruitmentProcessResponse();

        retModel.Rows = _context.Recruit_Record.Where(o => o.RecruitId == model.RecruitId)
            .OrderByDescending(o => o.CreatedTime).Select(o => new RecruitmentProcessInfo
            {
                UserName = o.Recruit.User_Seeker.NickName,
                RecruitStatus = o.Status,
                RecruitStatusName = o.Status.GetDescription(),
                OperationTime = o.CreatedTime,
                OperationPeople = o.User_Hr.NickName,
                WorkPositionName = o.Recruit.PostName,
                RecruitInterviewModel = o.Status == RecruitStatus.Interview
                    ? new RecruitInterviewModel()
                    {
                        Interviewer = o.Recruit_Interview.Project_Interviewer.Name,
                        InterviewOutcome = o.Recruit_Interview.Outcome,
                        InterviewOutcomeName = o.Recruit_Interview.Outcome.GetDescription(),
                        InterviewTime = o.Recruit_Interview.InterviewTime,
                        InterviewProcess = o.Recruit_Interview.Process,
                        InterviewProcessName = o.Recruit_Interview.Process.GetDescription(),
                        Remarks = o.Recruit_Interview.Remarks,
                        UserFeedBack = o.Recruit_Interview.UserFeedBack,
                        UserFeedBackName = o.Recruit_Interview.UserFeedBack.GetDescription(),
                        TodoTime = o.Recruit_Interview.TodoTime,
                        CancelName = o.Recruit_Interview.CancelName
                    }
                    : null,
                RecruitScreenModel = o.Status == RecruitStatus.InterviewerScreening
                    ? new RecruitScreenModel()
                    {
                        Interviewer = o.Recruit_Interviewer_Screen.Project_Interviewer.Name,
                        InterviewerScreenStatus = o.Recruit_Interviewer_Screen.Status,
                        InterviewerScreenStatusName = o.Recruit_Interviewer_Screen.Status.GetDescription(),
                        Remarks = o.Recruit_Interviewer_Screen.Remarks,
                        TodoTime = o.Recruit_Interviewer_Screen.TodoTime
                    }
                    : null,
                RecruitOfferModel = o.Status == RecruitStatus.Offer
                    ? new RecruitOfferModel()
                    {
                        IsNotice = o.Recruit_Offer.IsMobileNotice,
                        ReleaseTime = o.CreatedTime,
                    }
                    : null,
                RecruitFileAwayModel = o.Status == RecruitStatus.FileAway
                    ? new RecruitFileAwayModel()
                    {
                        FileAwayReason = o.FileAwayRemarks,
                        RecruitFileAway = o.FileAway,
                        RecruitFileAwayName = o.FileAway.GetDescription()
                    }
                    : null,
                RecruitInductionModel = o.Status == RecruitStatus.Induction
                    ? new RecruitInductionModel()
                    {
                        InductionMemo = o.FileAwayRemarks,
                        InductionTime = o.Recruit.InductionTime
                    }
                    : null
            }).ToList();

        foreach (var item in retModel.Rows)
        {
            if (item?.RecruitInductionModel?.InductionTime.HasValue == true)
            {
                item.RecruitInductionModel.InductionMemo = $"{item.RecruitInductionModel.InductionMemo}（入职时间：{item.RecruitInductionModel.InductionTime.Value:yyyy-MM-dd}）";
            }
        }

        return retModel;
    }

    /// <summary>
    /// 获取招聘流程简历评论记录
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public GetRecruitResumeCommentResponse GetRecruitResumeComment(GetRecruitResumeCommentRequest model)
    {
        if (string.IsNullOrEmpty(model.RecruitId))
        {
            throw new BadRequestException("请求参数错误！");
        }

        GetRecruitResumeCommentResponse retModel = new GetRecruitResumeCommentResponse();

        var predicate = PredicateBuilder.New<Recruit_Comment>(o => o.RecruitId == model.RecruitId);

        var sql = _context.Recruit_Comment.Where(predicate);

        retModel.Total = sql.Count();

        retModel.Rows = sql
            .OrderByDescending(o => o.CreatedTime)
            .Skip((model.PageIndex - 1) * model.PageSize)
            .Take(model.PageSize)
            .Select(s => new GetRecruitResumeCommentModel
            {
                Id = s.Id,
                Content = s.Content,
                CreatedTime = s.CreatedTime,
                HeadPortrait = s.User_Hr.Avatar ?? "",
                HrName = s.HrName
            }).ToList();

        return retModel;
    }

    /// <summary>
    /// 添加招聘流程简历评论
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse AddRecruitResumeComment(AddRecruitResumeCommentRequest model)
    {
        bool isExist = _context.Recruit.Any(o => o.HrId == _user.Id && o.RecruitId == model.RecruitId);

        if (!isExist)
        {
            throw new BadRequestException("内容不存在！");
        }

        Recruit_Comment addModel = new Recruit_Comment()
        {
            CreatedTime = DateTime.Now,
            Content = model.Content,
            HrId = _user.Id,
            HrName = _user.Name ?? "",
            RecruitId = model.RecruitId
        };

        _context.Recruit_Comment.Add(addModel);
        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 删除招聘流程简历评论
    /// </summary>
    /// <param name="commentId"></param>
    /// <returns></returns>
    public EmptyResponse DeleteRecruitResumeComment(string commentId)
    {
        var commentModel = _context.Recruit_Comment.Where(o => o.HrId == _user.Id && o.Id == commentId)
            .FirstOrDefault();
        if (commentModel == null)
        {
            throw new BadRequestException("内容不存在！");
        }

        _context.Recruit_Comment.Remove(commentModel);
        _context.SaveChanges();

        return new EmptyResponse();
    }

    #endregion

    #region 主招聘流程状态修改

    /// <summary>
    /// 根据id获取招聘进程
    /// </summary>
    /// <param name="recruitId">招聘流程id</param>
    /// <returns></returns>
    public GetRecruitResponse GetRecruitById(string recruitId)
    {
        var Model = _context.Recruit.Where(o => o.RecruitId == recruitId).Select(o => new GetRecruitResponse()
        {
            FollowerId = o.FollowerId,
            TeamHrId = o.Post_Delivery.Post_Team.Project_Team.HrId,
            AcceptOrder = o.AcceptOrder,
            HrId = o.HrId,
            HrName = o.User_Hr.NickName,
            HrHeadPortrait = o.User_Hr.Avatar,
            UserName = o.User_Seeker.User.WeChatName,
            UserHeadPortrait = o.User_Seeker.Avatar,
            RecruitId = o.RecruitId,
            Status = o.Status,
            StatusName = o.Status.GetDescription(),
            StatusTime = o.StatusTime,
            InterviewerScreenSub = o.Status == RecruitStatus.InterviewerScreening
                ? new RecruitDetailsInterviewerScreenSub
                {
                    InterviewerName = o.Recruit_Interviewer_Screen.OrderByDescending(s => s.CreatedTime).First()
                        .Project_Interviewer.Name,
                    InterviewerScreenStatus = o.Recruit_Interviewer_Screen.OrderByDescending(s => s.CreatedTime).First()
                        .Status,
                    InterviewerScreenFeedBack = o.Recruit_Interviewer_Screen.OrderByDescending(s => s.CreatedTime)
                        .First().Remarks,
                    FeedBackTime = o.Recruit_Interviewer_Screen.OrderByDescending(s => s.CreatedTime).First().TodoTime,
                }
                : null,
            InterviewerSub = o.Status == RecruitStatus.Interview
                ? new RecruitDetailsInterviewerSub
                {
                    InterviewProcess = o.Recruit_Interview.OrderByDescending(s => s.CreatedTime).First().Process,
                    InterviewerName = o.Recruit_Interview.OrderByDescending(s => s.CreatedTime).First()
                        .Project_Interviewer.Name,
                    InterviewOutcome = o.Recruit_Interview.OrderByDescending(s => s.CreatedTime).First().Outcome,
                    InterviewerFeedBack = o.Recruit_Interview.OrderByDescending(s => s.CreatedTime).First().Remarks,
                    InterviewTime = o.Recruit_Interview.OrderByDescending(s => s.CreatedTime).First().InterviewTime,
                    FeedBackTime = o.Recruit_Interview.OrderByDescending(s => s.CreatedTime).First().TodoTime,
                    CancelName = o.Recruit_Interview.OrderByDescending(s => s.CreatedTime).First().CancelName,
                }
                : null,
            OfferSub = o.Status == RecruitStatus.Offer
                ? new RecruitDetailsOfferSub
                {
                    OfferTimet = o.Recruit_Offer.OrderByDescending(s => s.CreatedTime).First().CreatedTime,
                    IsNotice = o.Recruit_Offer.OrderByDescending(s => s.CreatedTime).First().IsMobileNotice
                }
                : null,
            FileAwaySub = o.Status == RecruitStatus.FileAway
                ? new RecruitDetailsFileAwaySub
                {
                    RecruitFileAway = o.FileAway,
                    FileAwayRemarks = o.FileRemarks,
                    FileAwayTime = o.UpdatedTime
                }
                : null
        }).FirstOrDefault();

        if (Model == null)
        {
            throw new BadRequestException("招聘流程不存在！");
        }
        //新增逻辑如果招聘流程状态为已经接单需要判断线索人是否等于实际接单人如果等于则设置接单状态为未接单
        if (Model.AcceptOrder == RecruitAcceptOrderEnum.已经接单)
        {
            if (string.Equals(Model.TeamHrId, Model.FollowerId))
            {
                Model.AcceptOrder = RecruitAcceptOrderEnum.未接单;
            }
        }
        switch (Model.Status)
        {
            case RecruitStatus.InterviewerScreening:
                if (Model.InterviewerScreenSub is not null)
                {
                    Model.InterviewerScreenSub.InterviewerScreenStatusName =
                        Model.InterviewerScreenSub.InterviewerScreenStatus?.GetDescription();
                }

                break;
            case RecruitStatus.Interview:
                if (Model.InterviewerSub is not null)
                {
                    Model.InterviewerSub.InterviewOutcomeName = Model.InterviewerSub.InterviewOutcome?.GetDescription();
                    Model.InterviewerSub.InterviewProcessName = Model.InterviewerSub.InterviewProcess?.GetDescription();
                }

                break;
            case RecruitStatus.FileAway:
                if (Model.FileAwaySub is not null)
                {
                    Model.FileAwaySub.RecruitFileAwayName = Model.FileAwaySub.RecruitFileAway?.GetDescription();
                }

                break;
        }

        return Model;
    }

    /// <summary>
    /// 修改招聘流程(面试官筛选)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<EmptyResponse> EditRecruitInterviewerScreening(RequestEditRecruitInterviewerScreening model)
    {
        //修改状态之前优先处理未完成的面试或面试官筛选
        EditInterviewOrInterviewScreen(model.RecruitId, "InterviewerScreening");

        var RecruitModel = _context.Recruit.Where(o =>
                o.RecruitId == model.RecruitId &&
                (o.HrId == _user.Id || o.Post_Delivery.Post_Team.Project_Team.HrId == _user.Id || o.FollowerId == _user.Id))
            .FirstOrDefault();
        if (null == RecruitModel)
        {
            throw new BadRequestException("招聘流程不存在！");
        }

        #region 修改招聘流程

        RecruitModel.Status = RecruitStatus.InterviewerScreening;
        RecruitModel.UpdatedTime = DateTime.Now;
        RecruitModel.StatusTime = DateTime.Now;
        //更新邀面人为自己，补全数据
        //RecruitModel.FollowerId = _user.Id;
        #endregion

        // 产品：这一步不减库存，库存校验放在下一步
        // // 库存校验 todo:主创渠道场景暂时未考虑

        // 查询项目是否设置了简历免审
        var recruitInfo = _context.Recruit.Where(x => x.RecruitId == model.RecruitId).Select(x => new
        {
            x.Post_Delivery.Post.PaymentNode,
            x.Post_Delivery.Post.IsResumeExempt,
            x.Post_Delivery.Post.ProjectId
        }).FirstOrDefault();

        // 如果model.InterviewerId不为空，验证是否存在，使用.any
        if (!string.IsNullOrEmpty(model.InterviewerId) &&
            !_context.Project_Interviewer.Any(x => x.ProjectId == recruitInfo.ProjectId && x.Id == model.InterviewerId))
            throw new BadRequestException("面试官不存在！");

        var isSkip = recruitInfo.IsResumeExempt || model.IsSkip;

        #region 插入招聘流程面试官筛选表

        Recruit_Interviewer_Screen AddInterviewerScreen = new Recruit_Interviewer_Screen()
        {
            CreatedTime = DateTime.Now,
            InterviewerId = model.IsSkip ? "" : model.InterviewerId,
            Recommend = isSkip ? "" : model.Recommend,
            Status = isSkip ? RecruitInterviewerScreenStatus.Adopt : RecruitInterviewerScreenStatus.NoFedBack,
            RecruitId = RecruitModel.RecruitId,
            SeekerId = RecruitModel.SeekerId,
            Remarks = "",
        };
        _context.Add(AddInterviewerScreen);

        #endregion

        #region 插入面试官待办表

        if (!isSkip)
        {
            Recruit_Interviewer_Todo AddInterviewerTodo = new Recruit_Interviewer_Todo()
            {
                CreatedTime = DateTime.Now,
                InterviewerId = model.InterviewerId!,
                Status = RecruitInterviewerTodoStatus.Untreated,
                Type = RecruitInterviewerTodoType.InterviewScreen,
                RecruitId = RecruitModel.RecruitId,
                RelationId = AddInterviewerScreen.Id
            };
            _context.Add(AddInterviewerTodo);
        }

        #endregion

        #region 添加招聘流程记录表记录

        Recruit_Record AddRecord = new Recruit_Record()
        {
            Creator = _user.Id,
            RecruitId = RecruitModel.RecruitId,
            Status = RecruitStatus.InterviewerScreening,
            Data = "",
            InterviewerScreenId = AddInterviewerScreen.Id,
        };
        _context.Add(AddRecord);

        #endregion

        // 尝试减库存，savechange之前，保持一个事务
        await _profitService.RecruitStatusChange(_context, new RecruitStatusChangeModel
        {
            Recruit = RecruitModel
        });

        _context.SaveChanges();

        return new EmptyResponse { };
    }

    /// <summary>
    /// 修改招聘流程(面试)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<EmptyResponse> EditRecruitInterview(RequestEditRecruitInterview model)
    {
        // 协同or渠道 要判断库存
        CheckPostStockLeft(model.RecruitId, "安排面试");

        //修改状态之前优先处理未完成的面试或面试官筛选
        EditInterviewOrInterviewScreen(model.RecruitId, "Interview");

        var RecruitModel = _context.Recruit.Where(o => o.RecruitId == model.RecruitId).FirstOrDefault();
        if (null == RecruitModel)
        {
            throw new BadRequestException("招聘流程不存在！");
        }

        #region 修改招聘流程

        RecruitModel.Status = RecruitStatus.Interview;
        RecruitModel.UpdatedTime = DateTime.Now;
        RecruitModel.StatusTime = DateTime.Now;
        //更新邀面人为自己，补全数据
        //RecruitModel.FollowerId = _user.Id;
        #endregion

        #region 添加面试前检查当前是否有未处理的面试

        var InterviewLast = _context.Recruit_Interview.Where(o => o.RecruitId == model.RecruitId)
            .OrderByDescending(o => o.CreatedTime).FirstOrDefault();
        if (InterviewLast is not null && InterviewLast.Outcome == RecruitInterviewOutcome.Waiting)
        {
            InterviewLast.Outcome = RecruitInterviewOutcome.Passed;
            var todoModel = _context.Recruit_Interviewer_Todo.Where(o => o.RelationId == InterviewLast.InterviewId)
                .FirstOrDefault();
            if (todoModel is not null)
            {
                todoModel.Status = RecruitInterviewerTodoStatus.Unknown;
            }
        }

        #endregion

        #region 插入招聘流程面试表

        Recruit_Interview AddInterview = new Recruit_Interview()
        {
            Address = model.IsSkip ? "" : model.Address,
            AddressDetail = model.IsSkip ? "" : model.AddressDetail,
            RegionId = model.IsSkip ? "" : model.RegionId,
            Forms = model.IsSkip ? RecruitInterviewForms.Scene : model.InterviewForms,
            UserFeedBack = model.IsSkip ? RecruitInterviewUserFeedBack.Confirmed : RecruitInterviewUserFeedBack.Waiting,
            HrId = _user.Id,
            InterviewerId = model.IsSkip ? "" : model.InterviewerId,
            InterviewTime = model.IsSkip ? Constants.DefaultTime : model.InterviewTime,
            Lat = model.IsSkip ? 0m : model.Lat,
            Lng = model.IsSkip ? 0m : model.Lng,
            Mail = model.IsSkip ? "" : model.Mail,
            MailContent = model.IsSkip ? "" : model.MailContent,
            Mobile = model.IsSkip ? "" : model.Mobile,
            MobileContent = model.IsSkip ? "" : model.MobileContent,
            Outcome = model.IsSkip ? RecruitInterviewOutcome.Passed : RecruitInterviewOutcome.Waiting,
            Process = model.IsSkip ? RecruitInterviewProcess.Preliminary : model.InterviewProcess,
            RecruitId = RecruitModel.RecruitId,
            SeekerId = RecruitModel.SeekerId,
            Remarks = "",
        };
        _context.Add(AddInterview);

        #endregion

        #region 插入面试官待办表

        if (!model.IsSkip)
        {
            Recruit_Interviewer_Todo AddInterviewerTodo = new Recruit_Interviewer_Todo()
            {
                CreatedTime = DateTime.Now,
                InterviewerId = AddInterview.InterviewerId!,
                Status = RecruitInterviewerTodoStatus.Untreated,
                Type = RecruitInterviewerTodoType.Interview,
                RecruitId = RecruitModel.RecruitId,
                RelationId = AddInterview.InterviewId
            };
            _context.Add(AddInterviewerTodo);
        }

        #endregion

        #region 添加招聘流程记录表记录

        Recruit_Record AddRecord = new Recruit_Record()
        {
            Creator = _user.Id,
            RecruitId = RecruitModel.RecruitId,
            Status = RecruitStatus.Interview,
            InterviewId = AddInterview.InterviewId,
        };
        _context.Add(AddRecord);

        #endregion

        var recruitInfo = _context.Recruit.Where(x => x.RecruitId == model.RecruitId).Select(x => new
        {
            x.Post_Delivery.Post.PaymentNode
        }).FirstOrDefault();

        // 尝试减库存，savechange之前，保持一个事务
        await _profitService.RecruitStatusChange(_context, new RecruitStatusChangeModel
        {
            Recruit = RecruitModel
        });

        _context.SaveChanges();

        //发送短信通知
        if (!model.IsSkip && !string.IsNullOrEmpty(model.Mobile))
        {
            var sendModel = _context.Recruit.Where(o => o.RecruitId == model.RecruitId && o.HrId == _user.Id).Select(
                o => new
                {
                    enterpriseName = o.Post_Delivery.Post.Project.Agent_Ent.Name,
                    userName = o.User_Seeker.NickName,
                    userSex = o.User_Seeker.User_Resume.Sex,
                    userPhone = o.User_Seeker.User.Mobile,
                    teamHrId = o.Post_Delivery.Post_Team.Project_Team.HrId
                }).First();

            //生成跳转链接
            string jumpDetails = "Mypackage/interview/interview";
            string query = "id={0}&adviser={1}";
            string deliveryId = RecruitModel.DeliveryId ?? "";
            string teamHrId = sendModel.teamHrId;
            string url = await _weChatHelper.GetWxShortLinkForJump(_config.WeChat!.SeekerApplet!.AppId!, jumpDetails,
                string.Format(query, deliveryId, teamHrId));
            string _jumpurl = url.Split('/')[^1];

            //发送短信
            _smsHelper.SendSMSAliyunForJumpApplet(sendModel.userPhone, _config.Aliyun!.AliSMS!.InterviewTemplateCode!,
                new
                {
                    name = sendModel.userName,
                    enterprise = sendModel.enterpriseName,
                    time = GetInterviewTimeRemarks(model.InterviewTime),
                    address = model.InterviewForms.GetDescription() + " " + model.Address,
                    jumpurl = _jumpurl
                });
            //if (model.InterviewForms is RecruitInterviewForms.Scene)
            //{
            //    _smsHelper.SendSMSAliyunForJumpApplet(sendModel.userPhone, _config.Aliyun!.AliSMS!.FacingInterviewTemplateCode!, new
            //    {
            //        name = sendModel.userName.FirstOrDefault().ToString() + (sendModel.userSex == Sex.男 ? "先生" : "女士"),
            //        enterprise = sendModel.enterpriseName,
            //        time = model.InterviewTime.ToString("yyyy-MM-dd"),
            //        address = model.Address,
            //        jumpurl = _jumpurl
            //    });
            //}
            //else
            //{
            //    _smsHelper.SendSMSAliyunForJumpApplet(sendModel.userPhone, _config.Aliyun!.AliSMS!.UnFacingInterviewTemplateCode!, new
            //    {
            //        name = sendModel.userName.FirstOrDefault().ToString() + (sendModel.userSex == Sex.男 ? "先生" : "女士"),
            //        enterprise = sendModel.enterpriseName,
            //        time = model.InterviewTime.ToString("yyyy-MM-dd"),
            //        address = model.InterviewForms.GetDescription(),
            //        jumpurl = _jumpurl
            //    });
            //}
        }

        return new EmptyResponse { };
    }

    /// <summary>
    /// 修改招聘流程(Offer)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<EmptyResponse> EditRecruitOffer(RequestEditRecruitOffer model)
    {
        // 协同or渠道 要判断库存
        CheckPostStockLeft(model.RecruitId, "发Offer");

        //修改状态之前优先处理未完成的面试或面试官筛选
        EditInterviewOrInterviewScreen(model.RecruitId, "Offer");

        var RecruitModel = _context.Recruit.Where(o =>
            o.RecruitId == model.RecruitId && o.HrId == _user.Id).FirstOrDefault();
        if (null == RecruitModel)
        {
            throw new BadRequestException("招聘流程不存在！");
        }

        #region 修改招聘流程

        RecruitModel.Status = RecruitStatus.Offer;
        RecruitModel.UpdatedTime = DateTime.Now;
        RecruitModel.StatusTime = DateTime.Now;
        RecruitModel.InductionTime = model.InductionTime;
        //更新邀面人为自己，补全数据  判断是否是线索方邀面
        //RecruitModel.FollowerId = _user.Id;
        #endregion

        #region 插入招聘流程offer表

        Recruit_Offer AddOffer = new Recruit_Offer()
        {
            Address = model.IsSkip ? string.Empty : (model.Address ?? string.Empty),
            RegionId = model.IsSkip ? string.Empty : (model.RegionId ?? string.Empty),
            InductionSalary = model.IsSkip ? 0 : model.InductionSalary,
            InductionTime = model.IsSkip ? Constants.DefaultTime : model.InductionTime,
            Lat = model.IsSkip ? 0m : model.Lat,
            Lng = model.IsSkip ? 0m : model.Lng,
            Mail = model.IsSkip ? string.Empty : (model.Mail ?? string.Empty),
            MailContent = model.IsSkip ? string.Empty : (model.MailContent ?? string.Empty),
            Mobile = model.IsSkip ? string.Empty : (model.Mobile ?? string.Empty),
            MobileContent = model.IsSkip ? string.Empty : (model.MobileContent ?? string.Empty),
            IsMobileNotice = !model.IsSkip && !string.IsNullOrEmpty(model.Mobile),
            RecruitId = RecruitModel.RecruitId,
            CarryInformation = model.IsSkip
                ? null
                : (model.IsNotice ? JsonSerializer.SerializeToString(model.CarryInformation) : null),
            NoticeContent = model.IsSkip ? null : (model.IsNotice ? model.NoticeContent : null),
            NoticeSubjectId = model.IsSkip ? null : (model.IsNotice ? model.NoticeSubjectId : null),
            NoticeSubjectName = model.IsSkip ? null : (model.IsNotice ? model.NoticeSubjectName : null)
        };
        _context.Add(AddOffer);

        #endregion

        #region 添加招聘流程记录表记录

        Recruit_Record AddRecord = new Recruit_Record()
        {
            Creator = _user.Id,
            RecruitId = RecruitModel.RecruitId,
            Status = RecruitStatus.Offer,
            OfferId = AddOffer.Id
        };
        _context.Add(AddRecord);

        #endregion

        var recruitInfo = _context.Recruit.Where(x => x.RecruitId == model.RecruitId).Select(x => new
        {
            x.Post_Delivery.Post.PaymentNode
        }).FirstOrDefault();

        // 尝试减库存，savechange之前，保持一个事务
        await _profitService.RecruitStatusChange(_context, new RecruitStatusChangeModel
        {
            Recruit = RecruitModel
        });

        _context.SaveChanges();

        //发送短信通知
        if (!model.IsSkip && !string.IsNullOrEmpty(model.Mobile))
        {
            var sendModel = _context.Recruit.Where(o =>
                o.RecruitId == model.RecruitId && o.HrId == _user.Id)
                .Select(o => new
                {
                    hrName = o.User_Hr.NickName,
                    hrPost = o.User_Hr.Post,
                    userName = o.User_Seeker.NickName,
                    userSex = o.User_Seeker.User_Resume.Sex,
                    userPhone = o.User_Seeker.User.Mobile,
                    entName = o.Post_Delivery.Post.Project.Agent_Ent.Name
                }).First();

            ////生成跳转链接
            string jumpDetails = "Mypackage/offer/offer";
            string query = "id={0}";
            string offerId = AddOffer.Id;
            string url = await _weChatHelper.GetWxShortLinkForJump(_config.WeChat!.SeekerApplet!.AppId!, jumpDetails,
                string.Format(query, offerId));
            string _jumpurl = url.Split('/')[^1];

            //发送短信
            _smsHelper.SendSMSAliyunForJumpApplet(sendModel.userPhone, _config.Aliyun!.AliSMS!.OfferTemplateCode!, new
            {
                name = sendModel.userName,
                entname = sendModel.entName,
                jumpurl = _jumpurl
            });
        }

        return new EmptyResponse { };
    }

    /// <summary>
    /// 发送offer获取主体集合
    /// </summary>
    /// <param name="recruitId"></param>
    /// <returns></returns>
    public SendRecruitOfferGetNoticeSubject GetNoticeSubjectForOffer(string recruitId)
    {
        SendRecruitOfferGetNoticeSubject retModel = new SendRecruitOfferGetNoticeSubject();

        List<SendRecruitOfferSub> subList = new List<SendRecruitOfferSub>();

        SendRecruitOfferSub? hrModel = new SendRecruitOfferSub();

        hrModel = _context.Recruit.Where(o => o.RecruitId == recruitId).Select(o => new SendRecruitOfferSub
        {
            Key = o.User_Hr.Enterprise.EntId,
            Value = o.User_Hr.Enterprise.Name
        }).FirstOrDefault();

        SendRecruitOfferSub? projectModel = new SendRecruitOfferSub();

        projectModel = _context.Recruit.Where(o => o.RecruitId == recruitId).Select(o => new SendRecruitOfferSub
        {
            Key = o.Post_Delivery.Post.Project.Agent_Ent.AgentEntId,
            Value = o.Post_Delivery.Post.Project.Agent_Ent.Name
        }).FirstOrDefault();

        if (hrModel is not null)
        {
            subList.Add(hrModel);
        }

        if (projectModel is not null)
        {
            subList.Add(projectModel);
        }

        retModel.Rows = subList;

        return retModel;
    }

    /// <summary>
    /// 面试，offer，入职操作，如果协同或主创渠道则验证库存是否大于0才能继续操作
    /// </summary>
    /// <param name="recruitId"></param>
    /// <param name="operateName"></param>
    /// <exception cref="BadRequestException"></exception>
    public void CheckPostStockLeft(string recruitId, string operateName = "")
    {
        var postInfo = (from a in _context.Recruit
                        join b in _context.Post_Delivery on a.DeliveryId equals b.DeliveryId
                        join c in _context.Post_Team on b.TeamPostId equals c.TeamPostId
                        join d in _context.Project_Team on c.TeamProjectId equals d.TeamProjectId
                        join e in _context.Post on c.PostId equals e.PostId
                        join f in _context.Project on e.ProjectId equals f.ProjectId
                        where a.RecruitId == recruitId
                        select new { b.PostId, e.LeftStock, e.PaymentNode }).FirstOrDefault();
        if (postInfo != null && postInfo.LeftStock <= 0 && (postInfo.PaymentNode == ProjectPaymentNode.入职过保))
            throw new BadRequestException($"交付数量剩余0，不能操作{operateName}");
    }

    /// <summary>
    /// 修改招聘流程(入职)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<EmptyResponse> EditRecruitInduction(RequestEditRecruitInduction model)
    {
        // 协同or渠道 要判断库存
        CheckPostStockLeft(model.RecruitId, "入职");

        //修改状态之前优先处理未完成的面试或面试官筛选
        EditInterviewOrInterviewScreen(model.RecruitId, "Induction");

        var RecruitModel = _context.Recruit.Where(o =>
            o.RecruitId == model.RecruitId && o.HrId == _user.Id).FirstOrDefault();
        if (null == RecruitModel)
        {
            throw new BadRequestException("招聘流程不存在！");
        }

        #region 修改招聘流程

        RecruitModel.Status = RecruitStatus.Induction;
        RecruitModel.InductionTime = model.InductionTime;
        RecruitModel.UpdatedTime = DateTime.Now;
        RecruitModel.StatusTime = DateTime.Now;
        //更新邀面人为自己，补全数据
        //RecruitModel.FollowerId = _user.Id;
        #endregion

        #region 添加招聘流程记录表记录

        Recruit_Record AddRecord = new Recruit_Record()
        {
            Creator = _user.Id,
            RecruitId = RecruitModel.RecruitId,
            Status = RecruitStatus.Induction,
        };
        _context.Add(AddRecord);

        #endregion

        #region 反查真实人才库表修改用户等级

        var platformModel = _context.Talent_Platform.IgnoreQueryFilters()
            .Where(o => o.SeekerId == RecruitModel.SeekerId && o.HrId == RecruitModel.HrId).FirstOrDefault();
        if (platformModel is not null)
        {
            if (platformModel.Level != TalentPlatformLevel.合同用户)
            {
                platformModel.Level = TalentPlatformLevel.入职用户;
            }
        }

        #endregion

        #region 自流转设置应用（加入正式成员）

        var formalRecruitModel = _context.Recruit.Where(o =>
                o.RecruitId == RecruitModel.RecruitId &&
                o.Post_Delivery.Post.Project.Project_Automatic!.OfferOrInduction == true)
            .Select(o => o.RecruitId).FirstOrDefault();
        if (formalRecruitModel is not null)
        {
        }

        #endregion

        var recruitInfo = _context.Recruit.Where(x => x.RecruitId == model.RecruitId).Select(x => new
        {
            x.Post_Delivery.Post.PaymentNode
        }).FirstOrDefault();

        // 尝试减库存，savechange之前，保持一个事务
        await _profitService.RecruitStatusChange(_context, new RecruitStatusChangeModel
        {
            Recruit = RecruitModel
        });

        _context.SaveChanges();

        return new EmptyResponse { };
    }

    /// <summary>
    /// 修改招聘流程(归档)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<EmptyResponse> EditRecruitFileAway(RequestEditRecruitFileAway model)
    {
        //修改状态之前优先处理未完成的面试或面试官筛选
        EditInterviewOrInterviewScreen(model.RecruitId, "FileAway");

        var RecruitModel = _context.Recruit.Where(o => o.RecruitId == model.RecruitId).FirstOrDefault();
        if (null == RecruitModel)
        {
            throw new BadRequestException("招聘流程不存在！");
        }

        #region 修改招聘流程

        RecruitModel.Status = RecruitStatus.FileAway;
        RecruitModel.FileAway = model.RecruitFileAway;
        RecruitModel.FileRemarks = model.FileAwayReason;
        RecruitModel.UpdatedTime = DateTime.Now;
        RecruitModel.StatusTime = DateTime.Now;

        #endregion

        #region 添加招聘流程记录表记录

        Recruit_Record AddRecord = new Recruit_Record()
        {
            Creator = _user.Id,
            RecruitId = RecruitModel.RecruitId,
            Status = RecruitStatus.FileAway,
            FileAway = model.RecruitFileAway,
            FileAwayRemarks = model.FileAwayReason
        };
        _context.Add(AddRecord);

        #endregion

        #region 反查真实人才库表修改用户等级

        var platformModel = _context.Talent_Platform.IgnoreQueryFilters()
            .Where(o => o.SeekerId == RecruitModel.SeekerId && o.HrId == RecruitModel.HrId).FirstOrDefault();
        if (platformModel is not null)
        {
            if (platformModel.Level != TalentPlatformLevel.入职用户 && platformModel.Level != TalentPlatformLevel.合同用户)
            {
                platformModel.Level = TalentPlatformLevel.归档用户;
            }
        }

        #endregion

        var recruitInfo = _context.Recruit.Where(x => x.RecruitId == model.RecruitId).Select(x => new
        {
            x.Post_Delivery.Post.PaymentNode
        }).FirstOrDefault();

        // 尝试减库存，savechange之前，保持一个事务
        await _profitService.RecruitStatusChange(_context, new RecruitStatusChangeModel
        {
            Recruit = RecruitModel
        });

        _context.SaveChanges();

        return new EmptyResponse { };
    }

    /// <summary>
    /// 取消面试邀请（hr主动取消）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse CancelInterview(CancelInterviewRequest model)
    {
        if (string.IsNullOrEmpty(model.RecruitId))
        {
            throw new BadRequestException("请求参数错误！");
        }

        var recruitIsExist = _context.Recruit.Any(o => o.RecruitId == model.RecruitId);
        if (!recruitIsExist)
        {
            throw new BadRequestException("招聘流程不存在！");
        }

        var interviewModel = _context.Recruit_Interview.Where(o => o.Recruit.RecruitId == model.RecruitId)
            .OrderByDescending(o => o.CreatedTime).FirstOrDefault();
        if (interviewModel is null)
        {
            throw new BadRequestException("未添加面试邀请！");
        }

        if (interviewModel.Outcome != RecruitInterviewOutcome.Waiting)
        {
            throw new BadRequestException("面试已反馈，无法取消！");
        }

        var hrName = _context.User_Hr.Where(o => o.UserId == _user.Id).First();

        //更新面试状态
        interviewModel.Outcome = RecruitInterviewOutcome.Cancelled;
        interviewModel.Remarks = model.CancelRemarks ?? "";
        interviewModel.TodoTime = DateTime.Now;
        interviewModel.CancelName = hrName.NickName;

        //尝试修改面试官待办状态
        var todoModel = _context.Recruit_Interviewer_Todo.Where(o => o.RelationId == interviewModel.InterviewId)
            .FirstOrDefault();
        if (todoModel is not null)
        {
            todoModel.Status = RecruitInterviewerTodoStatus.Unknown;
        }

        _context.SaveChanges();
        return new EmptyResponse();
    }

    /// <summary>
    /// 取消面试邀请（返回上一节点取消）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public void CancelInterviewForBack(CancelInterviewRequest model)
    {
        if (string.IsNullOrEmpty(model.RecruitId))
        {
            throw new BadRequestException("请求参数错误！");
        }

        var recruitIsExist = _context.Recruit.FirstOrDefault(o =>
            o.RecruitId == model.RecruitId && o.HrId == _user.Id);
        if (recruitIsExist == null)
        {
            throw new BadRequestException("招聘流程不存在！");
        }

        // 当前节点非“面试”节点，则不做反馈
        if (recruitIsExist.Status != RecruitStatus.Interview)
            return;

        var interviewModel = _context.Recruit_Interview.Where(o => o.Recruit.RecruitId == model.RecruitId)
            .OrderByDescending(o => o.CreatedTime).FirstOrDefault();
        if (interviewModel is null)
        {
            throw new BadRequestException("未添加面试邀请！");
        }

        if (interviewModel.Outcome != RecruitInterviewOutcome.Waiting)
        {
            throw new BadRequestException("面试已反馈，无法取消！");
        }

        var hrName = _context.User_Hr.Where(o => o.UserId == _user.Id).First();

        //更新面试状态
        interviewModel.Outcome = RecruitInterviewOutcome.Cancelled;
        interviewModel.Remarks = model.CancelRemarks ?? "";
        interviewModel.TodoTime = DateTime.Now;
        interviewModel.CancelName = hrName.NickName;

        //尝试修改面试官待办状态
        var todoModel = _context.Recruit_Interviewer_Todo.Where(o => o.RelationId == interviewModel.InterviewId)
            .FirstOrDefault();
        if (todoModel is not null)
        {
            todoModel.Status = RecruitInterviewerTodoStatus.Unknown;
        }

        _context.SaveChanges();
    }

    /// <summary>
    /// hr填写面试反馈
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse InterviewFeedback(InterviewFeedbackRequest model)
    {
        if (string.IsNullOrEmpty(model.RecruitId) || model.Outcome == RecruitInterviewOutcome.Waiting ||
            model.Outcome == RecruitInterviewOutcome.Cancelled)
        {
            throw new BadRequestException("请求参数错误！");
        }

        var recruitIsExist = _context.Recruit.Any(o => o.RecruitId == model.RecruitId);
        if (!recruitIsExist)
        {
            throw new BadRequestException("招聘流程不存在！");
        }

        var interviewModel = _context.Recruit_Interview.Where(o => o.Recruit.RecruitId == model.RecruitId)
            .OrderByDescending(o => o.CreatedTime).FirstOrDefault();
        if (interviewModel is null)
        {
            throw new BadRequestException("未添加面试邀请！");
        }

        if (interviewModel.Outcome != RecruitInterviewOutcome.Waiting)
        {
            throw new BadRequestException("面试已反馈，无法添加重复面试反馈！");
        }

        interviewModel.Outcome = model.Outcome;
        interviewModel.Remarks = model.Remarks;
        interviewModel.TodoTime = DateTime.Now;

        //尝试修改面试官待办状态
        var todoModel = _context.Recruit_Interviewer_Todo.Where(o => o.RelationId == interviewModel.InterviewId)
            .FirstOrDefault();
        if (todoModel is not null)
        {
            todoModel.Status = RecruitInterviewerTodoStatus.Processed;
        }

        #region 自流转设置应用

        // bool isNotice = false;
        Recruit_Record? AddRecord = null;
        if (model.Outcome == RecruitInterviewOutcome.Fail)
        {
            string projectId = _context.Recruit.Where(o => o.RecruitId == model.RecruitId)
                .Select(o => o.Post_Delivery.Post.ProjectId!).First();
            var automaticModel = _context.Project_Automatic.Where(o => o.ProjectId == projectId).FirstOrDefault();
            if (automaticModel is not null)
            {
                if (automaticModel.InterviewerOutCome)
                {
                    var recruitModel = _context.Recruit.Where(o => o.RecruitId == interviewModel.RecruitId)
                        .FirstOrDefault();
                    if (recruitModel is not null)
                    {
                        recruitModel.Status = RecruitStatus.FileAway;
                        recruitModel.FileAway = automaticModel.ScreenFileStatus;
                        recruitModel.FileRemarks = automaticModel.ScreenFileRemarks;
                        recruitModel.UpdatedTime = DateTime.Now;
                        recruitModel.StatusTime = DateTime.Now;

                        #region 添加招聘流程记录表记录

                        AddRecord = new Recruit_Record()
                        {
                            Creator = recruitModel.HrId,
                            RecruitId = recruitModel.RecruitId,
                            Status = RecruitStatus.FileAway,
                            FileAway = automaticModel.ScreenFileStatus,
                            FileAwayRemarks = automaticModel.ScreenFileRemarks
                        };
                        _context.Add(AddRecord);

                        #endregion

                        #region 反查真实人才库表修改用户等级

                        var platformModel = _context.Talent_Platform.IgnoreQueryFilters()
                            .Where(o => o.SeekerId == recruitModel.SeekerId && o.HrId == recruitModel.HrId)
                            .FirstOrDefault();
                        if (platformModel is not null)
                        {
                            if (platformModel.Level != TalentPlatformLevel.入职用户 &&
                                platformModel.Level != TalentPlatformLevel.合同用户)
                            {
                                platformModel.Level = TalentPlatformLevel.归档用户;
                            }
                        }

                        #endregion

                        // isNotice = true;
                    }
                }
            }
        }

        #endregion

        // #region 订单自流转

        // if (model.Outcome != RecruitInterviewOutcome.Cancelled && model.Outcome != RecruitInterviewOutcome.NotCome)
        //     if (_context.Project_Teambounty.Any(w =>
        //             w.RecruitId == model.RecruitId && w.PaymentNode == ProjectPaymentNode.到面交付))
        //         MyRedis.Client.RPush(SubscriptionKey.RecruitInterviewBack, model.RecruitId); // 反馈过，到面交付场景立即“交付成功”

        // #endregion

        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// hr填写面试官筛选反馈
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse InterviewerScreenFeedback(InterviewerScreenFeedbackRequest model)
    {
        if (string.IsNullOrEmpty(model.RecruitId) || model.Status == RecruitInterviewerScreenStatus.NoFedBack ||
            model.Status == RecruitInterviewerScreenStatus.Cancelled)
        {
            throw new BadRequestException("请求参数错误！");
        }

        var recruitIsExist = _context.Recruit.Any(o =>
            o.RecruitId == model.RecruitId && o.HrId == _user.Id);
        if (!recruitIsExist)
        {
            throw new BadRequestException("招聘流程不存在！");
        }

        var interviewScreenModel = _context.Recruit_Interviewer_Screen
            .Where(o => o.Recruit.RecruitId == model.RecruitId).OrderByDescending(o => o.CreatedTime).FirstOrDefault();
        if (interviewScreenModel is null)
        {
            throw new BadRequestException("未添加面试官筛选！");
        }

        if (interviewScreenModel.Status != RecruitInterviewerScreenStatus.NoFedBack)
        {
            throw new BadRequestException("面试官筛选已反馈，无法添加重复面试官筛选反馈！");
        }

        interviewScreenModel.Status = model.Status;
        interviewScreenModel.Remarks = model.Remarks;
        interviewScreenModel.TodoTime = DateTime.Now;

        //尝试修改面试官待办状态
        var todoModel = _context.Recruit_Interviewer_Todo.Where(o => o.RelationId == interviewScreenModel.Id)
            .FirstOrDefault();
        if (todoModel is not null)
        {
            todoModel.Status = RecruitInterviewerTodoStatus.Processed;
        }

        #region 自流转设置应用

        // bool isNotice = false;
        Recruit_Record? AddRecord = null;
        if (model.Status == RecruitInterviewerScreenStatus.NoAdopt)
        {
            string projectId = _context.Recruit.Where(o => o.RecruitId == model.RecruitId)
                .Select(o => o.Post_Delivery.Post.ProjectId!).First();
            var automaticModel = _context.Project_Automatic.Where(o => o.ProjectId == projectId).FirstOrDefault();
            if (automaticModel is not null)
            {
                if (automaticModel.InterviewerOutCome)
                {
                    var recruitModel = _context.Recruit.Where(o => o.RecruitId == interviewScreenModel.RecruitId)
                        .FirstOrDefault();
                    if (recruitModel is not null)
                    {
                        recruitModel.Status = RecruitStatus.FileAway;
                        recruitModel.FileAway = automaticModel.ScreenFileStatus;
                        recruitModel.FileRemarks = automaticModel.ScreenFileRemarks;
                        recruitModel.UpdatedTime = DateTime.Now;
                        recruitModel.StatusTime = DateTime.Now;

                        #region 添加招聘流程记录表记录

                        AddRecord = new Recruit_Record()
                        {
                            Creator = recruitModel.HrId,
                            RecruitId = recruitModel.RecruitId,
                            Status = RecruitStatus.FileAway,
                            FileAway = automaticModel.ScreenFileStatus,
                            FileAwayRemarks = automaticModel.ScreenFileRemarks
                        };
                        _context.Add(AddRecord);

                        #endregion

                        #region 反查真实人才库表修改用户等级

                        var platformModel = _context.Talent_Platform.IgnoreQueryFilters()
                            .Where(o => o.SeekerId == recruitModel.SeekerId && o.HrId == recruitModel.HrId)
                            .FirstOrDefault();
                        if (platformModel is not null)
                        {
                            if (platformModel.Level != TalentPlatformLevel.入职用户 &&
                                platformModel.Level != TalentPlatformLevel.合同用户)
                            {
                                platformModel.Level = TalentPlatformLevel.归档用户;
                            }
                        }

                        #endregion

                        // isNotice = true;
                    }
                }
            }
        }

        #endregion

        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 新增招聘流程面试
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse AddRecruitInterview(RequestEditRecruitInterview model)
    {
        var RecruitModel = _context.Recruit.Where(o =>
            o.RecruitId == model.RecruitId && o.Status == RecruitStatus.Interview && o.HrId == _user.Id).FirstOrDefault();
        if (null == RecruitModel)
        {
            throw new BadRequestException("招聘流程不存在！");
        }

        #region 添加面试前检查当前是否有未处理的面试

        var InterviewLast = _context.Recruit_Interview.Where(o => o.RecruitId == model.RecruitId)
            .OrderByDescending(o => o.CreatedTime).FirstOrDefault();
        if (InterviewLast is not null && InterviewLast.Outcome == RecruitInterviewOutcome.Waiting)
        {
            InterviewLast.Outcome = RecruitInterviewOutcome.Passed;
            var todoModel = _context.Recruit_Interviewer_Todo.Where(o => o.RelationId == InterviewLast.InterviewId)
                .FirstOrDefault();
            if (todoModel is not null)
            {
                todoModel.Status = RecruitInterviewerTodoStatus.Unknown;
            }
        }

        #endregion

        #region 插入招聘流程面试表

        Recruit_Interview AddInterview = new Recruit_Interview()
        {
            Address = model.IsSkip ? "" : model.Address,
            RegionId = model.IsSkip ? "" : model.RegionId,
            Forms = model.IsSkip ? RecruitInterviewForms.Scene : model.InterviewForms,
            UserFeedBack = model.IsSkip ? RecruitInterviewUserFeedBack.Confirmed : RecruitInterviewUserFeedBack.Waiting,
            HrId = _user.Id,
            InterviewerId = model.IsSkip ? "" : model.InterviewerId,
            InterviewTime = model.IsSkip ? Constants.DefaultTime : model.InterviewTime,
            Lat = model.IsSkip ? 0m : model.Lat,
            Lng = model.IsSkip ? 0m : model.Lng,
            Mail = model.IsSkip ? "" : model.Mail,
            MailContent = model.IsSkip ? "" : model.MailContent,
            Mobile = model.IsSkip ? "" : model.Mobile,
            MobileContent = model.IsSkip ? "" : model.MobileContent,
            Outcome = model.IsSkip ? RecruitInterviewOutcome.Passed : RecruitInterviewOutcome.Waiting,
            Process = model.IsSkip ? RecruitInterviewProcess.Retest : model.InterviewProcess,
            RecruitId = RecruitModel.RecruitId,
            SeekerId = RecruitModel.SeekerId,
            Remarks = "",
        };
        _context.Add(AddInterview);

        #endregion

        #region 插入面试官待办表

        if (!model.IsSkip)
        {
            Recruit_Interviewer_Todo AddInterviewerTodo = new Recruit_Interviewer_Todo()
            {
                CreatedTime = DateTime.Now,
                InterviewerId = AddInterview.InterviewerId!,
                Status = RecruitInterviewerTodoStatus.Untreated,
                Type = RecruitInterviewerTodoType.Interview,
                RecruitId = RecruitModel.RecruitId,
                RelationId = AddInterview.InterviewId
            };
            _context.Add(AddInterviewerTodo);
        }

        #endregion

        #region 添加招聘流程记录表记录

        Recruit_Record AddRecord = new Recruit_Record()
        {
            Creator = _user.Id,
            RecruitId = RecruitModel.RecruitId,
            Status = RecruitStatus.Interview,
            InterviewId = AddInterview.InterviewId,
        };
        _context.Add(AddRecord);

        #endregion

        _context.SaveChanges();

        return new EmptyResponse();
    }

    #endregion

    #region 面试官管理

    /// <summary>
    /// 获取项目面试官列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public GetProjectInterviewerListResponse GetProjectInterviewerList(GetProjectInterviewerListRequest model)
    {
        GetProjectInterviewerListResponse retModel = new GetProjectInterviewerListResponse();

        var predicate = PredicateBuilder.New<Project_Interviewer>(o =>
            o.ProjectId == model.ProjectId && o.Project.HrId == _user.Id);

        if (!string.IsNullOrEmpty(model.Name))
            predicate = predicate.And(o => o.Name == model.Name);

        var sql = _context.Project_Interviewer.Where(predicate);

        retModel.Total = sql.Count();

        retModel.Rows = sql
            .OrderBy(o => o.Id)
            .Skip((model.PageIndex - 1) * model.PageSize)
            .Take(model.PageSize)
            .Select(s => new GetProjectInterviewerSelect
            {
                Id = s.Id,
                Name = s.Name,
                Mail = s.Mail,
                OfficeAddress = s.OfficeAddress,
                Phone = s.Phone,
                Post = s.Post,
                ProjectId = model.ProjectId
            }).ToList();

        return retModel;
    }

    /// <summary>
    /// 变更项目面试官信息（添加、修改）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse ChangeProjectInterviewer(ChangeProjectInterviewerRequest model)
    {
        Project_Interviewer? projectInterviewerModel = null;

        if (_context.Project_Interviewer.Any(o =>
                o.ProjectId == model.ProjectId && o.Phone == model.Phone && o.Id != model.Id))
            throw new BadRequestException("电话已存在！");

        if (!_context.Project.Any(a => a.ProjectId == model.ProjectId && a.HrId == _user.Id))
            throw new BadRequestException("项目不存在！");

        if (!string.IsNullOrWhiteSpace(model.Id))
        {
            projectInterviewerModel = _context.Project_Interviewer
                .Where(o => o.Id == model.Id && o.Project.HrId == _user.Id)
                .FirstOrDefault();
        }
        else
        {
            projectInterviewerModel = new Project_Interviewer()
            {
                ProjectId = model.ProjectId!
            };
            _context.Add(projectInterviewerModel);
        }

        projectInterviewerModel.Mail = model.Mail;
        projectInterviewerModel.OfficeAddress = model.OfficeAddress;
        projectInterviewerModel.Phone = model.Phone;
        projectInterviewerModel.Post = model.Post;
        projectInterviewerModel.Name = model.Name;

        _context.SaveChanges();
        return new EmptyResponse();
    }

    /// <summary>
    /// 删除项目面试官信息
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public EmptyResponse DeleteProjectInterviewer(string id)
    {
        var InterviewerModel = _context.Project_Interviewer
            .FirstOrDefault(o => o.Project.HrId == _user.Id && o.Id == id);
        if (InterviewerModel == null)
        {
            throw new BadRequestException("面试官不存在！");
        }

        if (_context.Recruit_Interviewer_Todo.Any(o => o.InterviewerId == id))
        {
            throw new BadRequestException("面试官处于招聘流程中，无法删除！");
        }

        // 如果面试官手机号和主创手机号一样，不允许删除
        if (_context.Project.Any(o =>
                o.ProjectId == InterviewerModel.ProjectId && o.User_Hr.User.Mobile == InterviewerModel.Phone))
            throw new BadRequestException("默认面试官不允许删除！");

        _context.Remove(InterviewerModel);

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        return new EmptyResponse();
    }

    #endregion

    #region 内部私有方法

    /// <summary>
    /// 内部修改面试或面试官筛选状态
    /// 2023/10/7 郭思成：面试，面试官筛选，先去掉默认通过（节点往后跳）
    /// </summary>
    /// <param name="recruitId"></param>
    private void EditInterviewOrInterviewScreen(string recruitId, string? sectionName)
    {
        //面试
        var interviewList = _context.Recruit_Interview
            .Where(o => o.RecruitId == recruitId && o.Outcome == RecruitInterviewOutcome.Waiting).ToList();
        if (interviewList.Count > 0)
        {
            foreach (var i in interviewList)
            {
                if (sectionName == "InterviewerScreening")
                {
                    CancelInterviewForBack(new CancelInterviewRequest
                    { RecruitId = recruitId, CancelRemarks = "变更节点" });
                }
                //else
                //{
                //    i.Outcome = RecruitInterviewOutcome.Passed;
                //    // todo: 问题：点击归档后，面试反馈为"通过"，会走立即交付成功逻辑！！并没有倒计时
                //    if (sectionName != "InvalidResume")
                //        MyRedis.Client.RPush(SubscriptionKey.RecruitInterviewBack, recruitId);// 反馈通过，到面交付场景立即“交付成功”
                //}
            }
        }

        //面试官筛选
        var interviewScreenList = _context.Recruit_Interviewer_Screen
            .Where(o => o.RecruitId == recruitId && o.Status == RecruitInterviewerScreenStatus.NoFedBack).ToList();
        if (interviewScreenList.Count > 0)
        {
            //foreach (var i in interviewScreenList)
            //{
            //    i.Status = RecruitInterviewerScreenStatus.Adopt;
            //}
        }

        //待办
        var recruitToDoModel = _context.Recruit_Interviewer_Todo
            .Where(o => o.RecruitId == recruitId && o.Status == RecruitInterviewerTodoStatus.Untreated).ToList();
        if (recruitToDoModel.Count > 0)
        {
            foreach (var i in recruitToDoModel)
            {
                i.Status = RecruitInterviewerTodoStatus.Unknown;
            }
        }

        _context.SaveChanges();
    }

    /// <summary>
    /// 面试短信处理面试时间格式
    /// </summary>
    /// <param name="time"></param>
    /// <returns></returns>
    private string GetInterviewTimeRemarks(DateTime time)
    {
        string retTime = string.Empty;
        string day = time.ToString("yyyy-MM-dd");
        string week = string.Empty;
        switch (time.DayOfWeek)
        {
            case DayOfWeek.Sunday: week = "星期日"; break;
            case DayOfWeek.Monday: week = "星期一"; break;
            case DayOfWeek.Tuesday: week = "星期二"; break;
            case DayOfWeek.Wednesday: week = "星期三"; break;
            case DayOfWeek.Thursday: week = "星期四"; break;
            case DayOfWeek.Friday: week = "星期五"; break;
            case DayOfWeek.Saturday: week = "星期六"; break;
        }

        string timeSection = string.Empty;
        if (time >= Convert.ToDateTime(time.ToString("yyyy-MM-dd 00:00:00")) &&
            time <= Convert.ToDateTime(time.ToString("yyyy-MM-dd 11:59:59")))
        {
            timeSection = "上午";
        }

        if (time >= Convert.ToDateTime(time.ToString("yyyy-MM-dd 12:00:00")) &&
            time <= Convert.ToDateTime(time.ToString("yyyy-MM-dd 17:59:59")))
        {
            timeSection = "下午";
        }

        if (time >= Convert.ToDateTime(time.ToString("yyyy-MM-dd 18:00:00")) &&
            time <= Convert.ToDateTime(time.ToString("yyyy-MM-dd 23:59:59")))
        {
            timeSection = "晚上";
        }

        string hour = time.ToString("HH:mm");

        return day + " " + week + " " + timeSection + " " + hour;
    }

    public RecruitResumeExtend GetRecruitResumeExtend(string recruitId)
    {
        var result = new RecruitResumeExtend();

        var resumeBuffer = _context.Recruit.Where(x => x.RecruitId == recruitId)
            .Select(s => new
            {
                s.Resume_Buffer.Source,
                s.Resume_Buffer.ResumeId
            }).FirstOrDefault();

        if (resumeBuffer.Source == ThirdPlatType.快招工)
        {
            var kuaishouTalent = _context.Kuaishou_Talent_Infos.Where(x => x.ApplicationId == resumeBuffer.ResumeId)
                .Select(s => new
                {
                    s.DataDynamicResumeInfo.BasicSituation,
                    s.DataDynamicResumeInfo.Skill,
                    s.DataDynamicResumeInfo.Certificate,
                    s.DataDynamicResumeInfo.WorkWay,
                    s.DataDynamicResumeInfo.DriveExperience,
                    s.DataChannelSourceCode,
                    s.DataUserCity
                }).FirstOrDefault();

            if (kuaishouTalent == null)
                throw new BadRequestException("内容不存在");

            result.Rows.Add(new RecruitResumeExtendValue { Name = "来源渠道", Text = "快招工" });

            result.Rows.Add(new RecruitResumeExtendValue { Name = "基本情况", Text = kuaishouTalent.BasicSituation });
            result.Rows.Add(new RecruitResumeExtendValue { Name = "拥有技能", Text = kuaishouTalent.Skill });
            result.Rows.Add(new RecruitResumeExtendValue { Name = "拥有证书", Text = kuaishouTalent.Certificate });
            result.Rows.Add(new RecruitResumeExtendValue { Name = "工作方式", Text = kuaishouTalent.WorkWay });
            result.Rows.Add(new RecruitResumeExtendValue { Name = "驾驶经历", Text = kuaishouTalent.DriveExperience });
            result.Rows.Add(new RecruitResumeExtendValue { Name = "用户地区", Text = kuaishouTalent.DataUserCity });
        }

        result.Rows = result.Rows.Where(x => !string.IsNullOrWhiteSpace(x.Text)).ToList();

        return result;
    }

    public string DownLoadResume(string recruitid)
    {
        string type = "RecruitResume";
        if (!MyRedis.Client.SetNx($"resume:down:{type}", 1, 3))
            throw new BadRequestException("操作频繁，限3秒一次");

        var info = _commonDicService.GetExistFileFromOss(type, recruitid);
        if (info != null)
            return info.OssPath;
        // 获取用户简历信息
        var resumeInfo = GetRecruitResumeDetails(recruitid);
        // 处理数据
        // 处理生日
        string birthday = string.Empty;
        if (resumeInfo._Birthday != null)
            birthday = Convert.ToDateTime(resumeInfo._Birthday.ToString()).ToString("yyyy-MM-dd");
        // 处理头像
        //if (!string.IsNullOrWhiteSpace(resumeInfo.HeadPortrait))
        //    resumeInfo.HeadPortrait = resumeInfo.HeadPortrait.Split('?')[0];
        // 处理教育经历
        var eduList = new List<RecruitResumeEduSub>();
        if (resumeInfo.EduSub != null)
        {
            var endTime = resumeInfo.EduSub.EndTime;
            if (endTime != null)
                resumeInfo.EduSub.EndTimeYM = Convert.ToDateTime(endTime.ToString()).ToString("yyyy-MM");
            eduList.Add(resumeInfo.EduSub);
        }

        // 处理工作经历
        if (resumeInfo.WorkSub != null && resumeInfo.WorkSub.Count > 0)
        {
            resumeInfo.WorkSub.ForEach(f =>
            {
                f.StartTimeYM = f.StartTime != null
                    ? Convert.ToDateTime(f.StartTime.ToString()).ToString("yyyy-MM")
                    : null;
                f.EndTimeYM = f.EndTime != null ? Convert.ToDateTime(f.EndTime.ToString()).ToString("yyyy-MM") : "至今";
            });
        }

        // 处理项目经历
        if (resumeInfo.PracticeSub != null && resumeInfo.PracticeSub.Count > 0)
        {
            resumeInfo.PracticeSub.ForEach(f =>
            {
                f.StartTimeYM = f.StartTime != null
                    ? Convert.ToDateTime(f.StartTime.ToString()).ToString("yyyy-MM")
                    : null;
                f.EndTimeYM = f.EndTime != null ? Convert.ToDateTime(f.EndTime.ToString()).ToString("yyyy-MM") : "至今";
            });
        }

        // 快招工信息
        var kuaishouInfoList = new List<KuaishouResumeInfo>();
        if (resumeInfo.ResumeExtend)
        {
            var extend = GetRecruitResumeExtend(recruitid);
            var kuaishouInfo = new KuaishouResumeInfo();
            if (extend != null && extend.Rows.Count > 0)
            {
                extend.Rows.ForEach(f =>
                {
                    switch (f.Name)
                    {
                        case "来源渠道": kuaishouInfo.ChannelName = "快招工"; break;
                        case "基本情况": kuaishouInfo.BasicSituation = f.Text; break;
                        case "拥有技能": kuaishouInfo.Skill = f.Text; break;
                        case "拥有证书": kuaishouInfo.Certificate = f.Text; break;
                        case "工作方式": kuaishouInfo.WorkWay = f.Text; break;
                        case "驾驶经历": kuaishouInfo.DriveExperience = f.Text; break;
                        case "用户地区": kuaishouInfo.DataUserCity = f.Text; break;
                    }
                });
                kuaishouInfoList.Add(kuaishouInfo);
            }
        }

        Dictionary<string, object> dicResume = new Dictionary<string, object>();
        dicResume.Add("用户ID", resumeInfo.UserId);
        dicResume.Add("简历生成时间", DateTime.Now);
        dicResume.Add("姓名", resumeInfo.Name);
        dicResume.Add("性别", resumeInfo.SexName);
        dicResume.Add("生日", birthday);
        dicResume.Add("年龄", resumeInfo.Age);
        dicResume.Add("头像", resumeInfo.HeadPortrait);
        dicResume.Add("邮箱", resumeInfo.Mailbox);
        dicResume.Add("自我评价", resumeInfo.SelfEvaluation);
        dicResume.Add("联系电话", resumeInfo.Mobile);
        dicResume.Add("学历", resumeInfo.EducationName);
        dicResume.Add("工作经历", JArray.FromObject(resumeInfo.WorkSub));
        dicResume.Add("实践经历", JArray.FromObject(resumeInfo.PracticeSub));
        dicResume.Add("教育经历", JArray.FromObject(eduList));
        dicResume.Add("标签", resumeInfo.Label == null ? null : string.Join(' ', resumeInfo.Label));
        dicResume.Add("QQ", resumeInfo.QQ);
        dicResume.Add("微信", resumeInfo.WeChat);
        dicResume.Add("证书",
            (resumeInfo.SkillsCert == null || resumeInfo.SkillsCert.Count == 0)
                ? null
                : string.Join(' ', resumeInfo.SkillsCert));
        dicResume.Add("职业", resumeInfo.OccupationName);
        dicResume.Add("扩展信息", JArray.FromObject(kuaishouInfoList));

        // 获取模板路径及生成文件路径
        var rootPath = Path.Combine(Directory.GetCurrentDirectory(), "TemplateFiles", "resume/Recruit");
        if (!Directory.Exists(rootPath))
            Directory.CreateDirectory(rootPath);
        string tempPath = Path.Combine(rootPath, "ResumeTempRecruit.docx");
        string newName = DateTime.Now.ToString("yyyyMMddHHmmssfff") + (new Random()).Next(100) + ".docx";
        string outWordPath = rootPath + @"\" + newName;

        //Noah.ImExportTools.WordHelper.ExportWithArray(tempPath, outWordPath, dicResume).Wait();
        WordHelper.ExportWithArray(tempPath, outWordPath, dicResume).Wait();
        //将文件上传到OSS
        string ossPath = string.Empty;
        using (var stream = new FileStream(outWordPath, FileMode.Open))
        {
            ossPath = _objectStorageService.OrdinaryUploadFile(stream, Path.GetFileName(outWordPath),
                DateTime.Now.Ticks + Path.GetExtension(outWordPath)).Result;
        }

        if (File.Exists(outWordPath))
        {
            try
            {
                File.Delete(outWordPath);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        // oss地址存本地,用set不会重复
        MyRedis.Client.SAdd(SubscriptionKey.OssPathKey,
            new File_Path_Of_Oss { Type = type, PrimaryId = recruitid, OssPath = ossPath, UserId = resumeInfo.UserId });
        return ossPath;
    }

    #endregion

    public async Task<EmptyResponse> EditRecruitInvalidResume(RequestEditRecruitInvalidResume model)
    {
        //修改状态之前优先处理未完成的面试或面试官筛选
        EditInterviewOrInterviewScreen(model.RecruitId, "InvalidResume");

        var RecruitModel = _context.Recruit.Where(o =>
            o.RecruitId == model.RecruitId && o.HrId == _user.Id).FirstOrDefault();
        if (null == RecruitModel)
        {
            throw new BadRequestException("招聘流程不存在！");
        }

        #region 修改招聘流程

        RecruitModel.Status = RecruitStatus.FileAway;
        RecruitModel.FileAway = RecruitFileAway.Invalid;
        RecruitModel.FileRemarks = model.InvalidDescribe ?? string.Empty;
        RecruitModel.UpdatedTime = DateTime.Now;
        RecruitModel.StatusTime = DateTime.Now;
        RecruitModel.InvalidReason = model.InvalidReason;

        #endregion

        #region 添加招聘流程记录表记录

        Recruit_Record AddRecord = new Recruit_Record()
        {
            Creator = _user.Id,
            RecruitId = RecruitModel.RecruitId,
            Status = RecruitStatus.FileAway,
            FileAway = RecruitFileAway.Invalid,
            FileAwayRemarks = model.InvalidDescribe ?? string.Empty
        };
        _context.Add(AddRecord);

        #endregion

        #region 反查真实人才库表修改用户等级

        var platformModel = _context.Talent_Platform.IgnoreQueryFilters()
            .Where(o => o.SeekerId == RecruitModel.SeekerId && o.HrId == RecruitModel.HrId).FirstOrDefault();
        if (platformModel is not null)
        {
            if (platformModel.Level != TalentPlatformLevel.入职用户 && platformModel.Level != TalentPlatformLevel.合同用户)
            {
                platformModel.Level = TalentPlatformLevel.归档用户;
            }
        }

        #endregion

        var recruitInfo = _context.Recruit.Where(x => x.RecruitId == model.RecruitId).Select(x => new
        {
            x.Post_Delivery.Post.PaymentNode
        }).FirstOrDefault();

        // 尝试减库存，savechange之前，保持一个事务
        await _profitService.RecruitStatusChange(_context, new RecruitStatusChangeModel
        {
            Recruit = RecruitModel
        });

        _context.SaveChanges();

        return new EmptyResponse { };
    }

    /// <summary>
    /// 上传简历（调用小析服务解析简历）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public UpLoadResumeInfoResponse UpLoadResume(UpLoadResumeModel model)
    {
        string type = "RecruitResumeUpload";
        if (!MyRedis.Client.SetNx($"resume:upload:{type}", 1, 3))
            throw new BadRequestException("操作频繁，限3秒一次");

        var result = new UpLoadResumeInfoResponse();
        if (model != null)
        {
            // 添加上传记录
            var info = new Recruit_Upload_Record()
            {
                CreatedTime = DateTime.Now,
                FileExtension = model.FileExtension,
                FileName = model.FileName,
                FileUrl = model.FileUrl,
                HrId = _user.Id,
                UpLoadStatus = RecruitUpLoadStatus.待处理,
                SeekerName = string.Empty,
                SeekerPhone = string.Empty,
                SeekerEmail = string.Empty,
                TeamPostId = string.Empty
            };
            _context.Recruit_Upload_Record.Add(info);
            _context.SaveChanges();

            // 小析解析
            result = RecruitUpLoadResume(info).GetAwaiter().GetResult();
            result.RecordId = info.Id;
        }

        return result;
    }

    /// <summary>
    /// 小析解析
    /// </summary>
    /// <param name="recordId"></param>
    /// <returns></returns>
    private async Task<UpLoadResumeInfoResponse> RecruitUpLoadResume(Recruit_Upload_Record record)
    {
        var response = new UpLoadResumeInfoResponse();
        // 走小析解析
        (List<AnalysisResumeResponse> resumes, _, _) = await _parseResume.AnalysisResume(record.FileUrl, record.FileName);
        if (resumes == null || resumes.Count == 0)
            throw new BadRequestException("小析服务解析简历出错,解析简历数量0");
        var resume = resumes[0];
        record.ResumeInfo =
            Newtonsoft.Json.JsonConvert.DeserializeObject<AnalysisResume>(
                Newtonsoft.Json.JsonConvert.SerializeObject(resume)!) ?? new AnalysisResume();
        _context.Attach(record);
        //简历信息
        if (resume.parsing_result != null)
        {
            //基本信息（求职期望）
            if (resume.parsing_result.basic_info != null)
            {
                response.Name = resume.parsing_result!.basic_info!.name ?? string.Empty;
                response.Mobile = resume.parsing_result.contact_info?.phone_number ?? string.Empty;
                response.Email = resume.parsing_result.contact_info?.email ?? string.Empty;
                response.Age = resume.parsing_result.basic_info.age;
                response.Education = !string.IsNullOrEmpty(resume.parsing_result.basic_info.degree)
                    ? resume.parsing_result.basic_info.degree switch
                    {
                        "硕士" => EducationType.硕士,
                        "博士" => EducationType.博士,
                        "本科" => EducationType.本科,
                        "专科" => EducationType.大专,
                        "大专" => EducationType.大专,
                        "高中" => EducationType.高中,
                        "中专" => EducationType.其他,
                        "初中" => EducationType.其他,
                        "MBA" => EducationType.硕士,
                        _ => EducationType.其他
                    }
                    : EducationType.其他;
            }
        }

        _context.SaveChanges();
        return response;
    }

    public void ResumeDelivery(Recruit_Upload_Record record)
    {
        var resume = record.ResumeInfo;
        // 获取地区字典
        var regionModel = _commonDicService.GetRegion();
        List<string> industryList = new List<string>();
        List<string> postList = new List<string>();
        string? userId = string.Empty;
        Talent_Virtual? addTalent = null;

        var notifyNew = new Sub_TalentCount_Change
        {
            HrId = record.HrId
        };

        //简历信息
        if (resume.parsing_result != null)
        {
            //基本信息（求职期望）
            if (resume.parsing_result.basic_info != null)
            {
                // 简历信息转用户
                userId = ResumeToSeeker(_context, regionModel, record);
                if (userId == null)
                    throw new Exception("创建求职者异常");
                // 投递简历，重复投递会抛出异常，所以不会有重复入人才库的问题，只有成功投递才能进人才库
                try
                {
                    _commonUserService.DeliverResume(new CommonDeliverResume
                    {
                        TeamPostId = record.TeamPostId,
                        UserId = userId,
                        UserClient = ClientType.Import
                    });
                }
                catch (BadRequestException ex)
                {
                    // 判断是否有oppenid
                    if (ex.ErrorCode.Contains("Mismatch"))
                    {
                        if (IfBindUser(record.SeekerPhone))
                        {
                            string msg = ex.Message + "；已绑定真实用户";
                            string errCode = ex.ErrorCode;
                            throw new BadRequestException(msg, errCode);
                        }
                    }

                    throw;
                }

                addTalent = new Talent_Virtual()
                {
                    HeadPortrait = resume.avatar_url ?? string.Empty,
                    Name = resume.parsing_result.basic_info.name ?? string.Empty,
                    Mobile = resume.parsing_result.contact_info?.phone_number ?? string.Empty,
                    Sex = resume.parsing_result.basic_info.gender == "男" ? Sex.男 : Sex.女,
                    Mailbox = resume.parsing_result.contact_info?.email ?? string.Empty,
                    WeChat = resume.parsing_result.contact_info?.wechat ?? string.Empty,
                    QQ = resume.parsing_result.contact_info?.QQ ?? string.Empty,
                    WorkTime = DateTime.Now.AddYears(0 - resume.parsing_result.basic_info.num_work_experience),
                    Location = resume.parsing_result.basic_info.current_location ?? string.Empty,
                    DetailedLocation = resume.parsing_result.basic_info.detailed_location ?? string.Empty,
                    LocationNorm = resume.parsing_result.basic_info.current_location_norm ?? string.Empty,
                    Channel = TalentVirtualChannel.ResumeUpload,
                    CreatedTime = DateTime.Now,
                    HrId = record.HrId,
                    OriginalUrl = record.FileUrl,
                    Remark = "",
                    Status = TalentVirtualStatus.UnRegistered,
                    SeekerId = "",
                };
                _context.Talent_Virtual.Add(addTalent);

                if (!string.IsNullOrEmpty(resume.parsing_result.basic_info.detailed_location))
                {
                    addTalent.RegionId =
                        regionModel.Where(o => o.Name.Contains(resume.parsing_result.basic_info.detailed_location))
                            .FirstOrDefault()?.Id ?? string.Empty;
                }
                else
                {
                    addTalent.RegionId = string.Empty;
                }

                if (!string.IsNullOrEmpty(resume.parsing_result.basic_info.degree))
                {
                    switch (resume.parsing_result.basic_info.degree)
                    {
                        case "硕士": addTalent.Education = TalentVirtualEducation.Master; break;
                        case "博士": addTalent.Education = TalentVirtualEducation.Doctor; break;
                        case "本科": addTalent.Education = TalentVirtualEducation.Undergraduate; break;
                        case "专科": addTalent.Education = TalentVirtualEducation.Professional; break;
                        case "大专": addTalent.Education = TalentVirtualEducation.Professional; break;
                        case "高中": addTalent.Education = TalentVirtualEducation.Senior; break;
                        case "中专": addTalent.Education = TalentVirtualEducation.Specialized; break;
                        case "初中": addTalent.Education = TalentVirtualEducation.Junior; break;
                        default: addTalent.Education = TalentVirtualEducation.Junior; break;
                    }
                }

                if (!string.IsNullOrEmpty(resume.parsing_result.basic_info.date_of_birth))
                {
                    if (resume.parsing_result.basic_info.date_of_birth.Length == 4)
                    {
                        if (DateTime.TryParse(resume.parsing_result.basic_info.date_of_birth + "-01-01",
                                out DateTime _Birthday))
                        {
                            addTalent.Birthday = _Birthday;
                        }
                    }
                    else
                    {
                        if (DateTime.TryParse(resume.parsing_result.basic_info.date_of_birth, out DateTime _Birthday))
                        {
                            addTalent.Birthday = _Birthday;
                        }
                    }
                }

                if (resume.parsing_result.contact_info != null)
                {
                    if (!string.IsNullOrEmpty(resume.parsing_result.contact_info.phone_number))
                    {
                        var userModel = _context.User
                            .Where(o => o.Mobile == resume.parsing_result.contact_info.phone_number).FirstOrDefault();
                        if (userModel != null)
                        {
                            addTalent.SeekerId = userModel.UserId;
                            addTalent.Status = TalentVirtualStatus.Registered;

                            if (!string.IsNullOrEmpty(userModel.IdentityCard))
                            {
                                addTalent.Status = TalentVirtualStatus.RealName;
                            }
                        }
                    }
                }

                //补充信息
                if (resume.parsing_result.others != null)
                {
                    addTalent.SelfEvaluation = resume.parsing_result.others.self_evaluation ?? string.Empty;

                    TalentVirtaualResumeSkillSub SkillAnalysis = new TalentVirtaualResumeSkillSub()
                    {
                        Business = resume.parsing_result.others.business_skills,
                        IT = resume.parsing_result.others.it_skills,
                        Professional = resume.parsing_result.others.skills
                    };
                    addTalent.SkillAnalysis = SkillAnalysis;
                }
            }

            //求职期望
            if (addTalent != null && resume.parsing_result.basic_info != null)
            {
                Talent_Virtual_Hope addHopeModel = new Talent_Virtual_Hope();
                addHopeModel.HopeCity = resume.parsing_result.basic_info.expect_location ?? string.Empty;
                addHopeModel.IndustryName = resume.parsing_result.basic_info.desired_industry ?? string.Empty;
                addHopeModel.PostName = resume.parsing_result.basic_info.desired_position ?? string.Empty;
                addHopeModel.VirtualId = addTalent.Id;
                if (!string.IsNullOrEmpty(resume.parsing_result.basic_info.expect_location))
                {
                    addHopeModel.RegionId =
                        regionModel.Where(o => o.Name.Contains(resume.parsing_result.basic_info.expect_location))
                            .FirstOrDefault()?.Id ?? string.Empty;
                }
                else
                {
                    addHopeModel.RegionId = string.Empty;
                }

                if (!string.IsNullOrEmpty(resume.parsing_result.basic_info.desired_salary))
                {
                    string[] desired_salary = resume.parsing_result.basic_info.desired_salary.Split("-");
                    if (desired_salary.Length == 2)
                    {
                        string _MinSalary = desired_salary[0].Trim();
                        string _MaxSalary = desired_salary[1].Trim();
                        try
                        {
                            addHopeModel.MinSalary =
                                Convert.ToDecimal(
                                    System.Text.RegularExpressions.Regex.Replace(_MinSalary, @"[^\d]*", ""));
                            addHopeModel.MaxSalary =
                                Convert.ToDecimal(
                                    System.Text.RegularExpressions.Regex.Replace(_MaxSalary, @"[^\d]*", ""));
                            if (_MinSalary.Contains('K') || _MinSalary.Contains('k'))
                            {
                                addHopeModel.MinSalary *= 1000;
                            }

                            if (_MaxSalary.Contains('K') || _MaxSalary.Contains('k'))
                            {
                                addHopeModel.MaxSalary *= 1000;
                            }
                        }
                        catch
                        {
                            addHopeModel.MinSalary = 0;
                            addHopeModel.MaxSalary = 0;
                        }
                    }
                }

                _context.Add(addHopeModel);
            }

            //项目经历
            List<User_Campus> removeUserCampus = _context.User_Campus.Where(w => w.UserId == userId).ToList();
            if (addTalent != null && resume.parsing_result.project_experience != null &&
                resume.parsing_result.project_experience.Count > 0)
            {
                List<Talent_Virtual_Project> addProjectList = new List<Talent_Virtual_Project>();
                List<User_Campus> addUserCampus = new();

                resume.parsing_result.project_experience = resume.parsing_result.project_experience
                    .OrderBy(o => o.start_time_year).OrderBy(o => o.start_time_month).ToList();

                foreach (var m in resume.parsing_result.project_experience)
                {
                    addProjectList.Add(new Talent_Virtual_Project
                    {
                        CreatedTime = DateTime.Now,
                        PostName = m.job_title ?? string.Empty,
                        ProjectName = m.project_name ?? string.Empty,
                        ProjectRemarks = m.description ?? string.Empty,
                        StartTime =
                            (string.IsNullOrEmpty(m.start_time_year) || string.IsNullOrEmpty(m.start_time_month))
                                ? DateTime.Now
                                : Convert.ToDateTime(m.start_time_year + "-" + m.start_time_month + "-01"),
                        EndTime = (string.IsNullOrEmpty(m.end_time_year) || string.IsNullOrEmpty(m.end_time_month))
                            ? null
                            : Convert.ToDateTime(m.end_time_year + "-" + m.end_time_month + "-01"),
                        VirtualId = addTalent.Id
                    });

                    addUserCampus.Add(new User_Campus
                    {
                        Name = m.project_name ?? string.Empty,
                        Award = m.job_title ?? string.Empty,
                        BeginDate = DateOnly.FromDateTime(
                            (string.IsNullOrEmpty(m.start_time_year) || string.IsNullOrEmpty(m.start_time_month))
                                ? DateTime.Now
                                : Convert.ToDateTime(m.start_time_year + "-" + m.start_time_month + "-01")),
                        EndDate = DateOnly.FromDateTime(
                            (string.IsNullOrEmpty(m.end_time_year) || string.IsNullOrEmpty(m.end_time_month))
                                ? DateTime.MinValue
                                : Convert.ToDateTime(m.end_time_year + "-" + m.end_time_month + "-01")),
                        Describe = m.description ?? string.Empty,
                        UserId = userId
                    });
                }

                _context.Talent_Virtual_Project.AddRange(addProjectList);
                if (_context.User_OpenId.FirstOrDefault(f => f.UserId == userId) == null)
                {
                    // 先删除上一次上传
                    _context.RemoveRange(removeUserCampus);
                    _context.User_Campus.AddRange(addUserCampus);
                }
            }
            else
            {
                if (_context.User_OpenId.FirstOrDefault(f => f.UserId == userId) == null)
                {
                    // 先删除上一次上传
                    _context.RemoveRange(removeUserCampus);
                }
            }

            //工作经历
            List<User_Work> removeWorks = _context.User_Work.Where(w => w.UserId == userId).ToList();
            if (addTalent != null && resume.parsing_result.work_experience != null &&
                resume.parsing_result.work_experience.Count > 0)
            {
                List<Talent_Virtual_Work> addWorkList = new List<Talent_Virtual_Work>();
                List<User_Work> addWorks = new(); // 用户工作经历

                resume.parsing_result.work_experience = resume.parsing_result.work_experience
                    .OrderBy(o => o.start_time_year).OrderBy(o => o.start_time_month).ToList();

                foreach (var m in resume.parsing_result.work_experience)
                {
                    Talent_Virtual_Work addWorkModel = new Talent_Virtual_Work()
                    {
                        CompanyName = m.company_name ?? string.Empty,
                        CompanyRemarks = m.description ?? string.Empty,
                        CreatedTime = DateTime.Now,
                        Department = m.department ?? string.Empty,
                        IndustryName = m.industry ?? string.Empty,
                        PostName = m.job_title ?? string.Empty,
                        StartTime =
                            (string.IsNullOrEmpty(m.start_time_year) || string.IsNullOrEmpty(m.start_time_month))
                                ? DateTime.Now
                                : Convert.ToDateTime(m.start_time_year + "-" + m.start_time_month + "-01"),
                        EndTime = (string.IsNullOrEmpty(m.end_time_year) || string.IsNullOrEmpty(m.end_time_month))
                            ? null
                            : Convert.ToDateTime(m.end_time_year + "-" + m.end_time_month + "-01"),
                        VirtualId = addTalent.Id
                    };

                    User_Work addWord = new User_Work
                    {
                        Company = addWorkModel.CompanyName,
                        Post = addWorkModel.PostName,
                        BeginDate = DateOnly.FromDateTime(addWorkModel.StartTime),
                        EndDate = addWorkModel.EndTime is null
                            ? DateOnly.FromDateTime(DateTime.MinValue)
                            : DateOnly.FromDateTime(addWorkModel.EndTime.Value),
                        Describe = addWorkModel.CompanyRemarks,
                        UserId = userId
                    };

                    addWorks.Add(addWord);

                    if (!string.IsNullOrEmpty(m.industry))
                    {
                        industryList.Add(m.industry);
                    }

                    if (!string.IsNullOrEmpty(m.job_title))
                    {
                        postList.Add(m.job_title);
                    }

                    if (!string.IsNullOrEmpty(m.salary))
                    {
                        string[] salary = m.salary.Split("-");
                        if (salary.Length == 2)
                        {
                            string _MinSalary = salary[0].Trim();
                            string _MaxSalary = salary[1].Trim();
                            try
                            {
                                addWorkModel.MinSalary =
                                    Convert.ToDecimal(
                                        System.Text.RegularExpressions.Regex.Replace(_MinSalary, @"[^\d]*", ""));
                                addWorkModel.MaxSalary =
                                    Convert.ToDecimal(
                                        System.Text.RegularExpressions.Regex.Replace(_MaxSalary, @"[^\d]*", ""));
                                if (_MinSalary.Contains('K') || _MinSalary.Contains('k'))
                                {
                                    addWorkModel.MinSalary *= 1000;
                                }

                                if (_MaxSalary.Contains('K') || _MaxSalary.Contains('k'))
                                {
                                    addWorkModel.MaxSalary *= 1000;
                                }
                            }
                            catch
                            {
                                addWorkModel.MinSalary = 0;
                                addWorkModel.MaxSalary = 0;
                            }
                        }
                    }

                    addWorkList.Add(addWorkModel);
                }

                _context.Talent_Virtual_Work.AddRange(addWorkList);

                if (_context.User_OpenId.FirstOrDefault(f => f.UserId == userId) == null)
                {
                    // 先删除上一次上传
                    _context.RemoveRange(removeWorks);
                    _context.User_Work.AddRange(addWorks);
                }

                notifyNew.PostNew = addWorkList.Select(x => x.PostName).ToList();
                notifyNew.IndustryNew = addWorkList.Select(x => x.IndustryName).ToList();
            }
            else
            {
                if (_context.User_OpenId.FirstOrDefault(f => f.UserId == userId) == null)
                {
                    // 先删除上一次上传
                    _context.RemoveRange(removeWorks);
                }
            }

            //教育经历
            var userResume = _context.User_Resume.FirstOrDefault(f => f.UserId == userId);
            if (addTalent != null && resume.parsing_result.education_experience != null &&
                resume.parsing_result.education_experience.Count > 0)
            {
                List<Talent_Virtual_Edu> addEduList = new List<Talent_Virtual_Edu>();
                var topEducation = TalentVirtualEducation.Junior;
                resume.parsing_result.education_experience = resume.parsing_result.education_experience
                    .OrderBy(o => o.start_time_year).OrderBy(o => o.start_time_month).ToList();
                foreach (var m in resume.parsing_result.education_experience)
                {
                    Talent_Virtual_Edu addEduModel = new Talent_Virtual_Edu()
                    {
                        CreatedTime = DateTime.Now,
                        IsFullTime = m.study_model == "非统招" ? false : true,
                        MajorName = m.major ?? string.Empty,
                        SchoolName = m.school_name ?? string.Empty,
                        SchoolRemarks = string.Empty,
                        StartTime =
                            (string.IsNullOrEmpty(m.start_time_year) || string.IsNullOrEmpty(m.start_time_month))
                                ? DateTime.Now
                                : Convert.ToDateTime(m.start_time_year + "-" + m.start_time_month + "-01"),
                        EndTime = (string.IsNullOrEmpty(m.end_time_year) || string.IsNullOrEmpty(m.end_time_month))
                            ? null
                            : Convert.ToDateTime(m.end_time_year + "-" + m.end_time_month + "-01"),
                        VirtualId = addTalent.Id
                    };
                    switch (m.degree)
                    {
                        case "硕士": addEduModel.Education = TalentVirtualEducation.Master; break;
                        case "博士": addEduModel.Education = TalentVirtualEducation.Doctor; break;
                        case "本科": addEduModel.Education = TalentVirtualEducation.Undergraduate; break;
                        case "专科": addTalent.Education = TalentVirtualEducation.Professional; break;
                        case "大专": addEduModel.Education = TalentVirtualEducation.Professional; break;
                        case "高中": addEduModel.Education = TalentVirtualEducation.Senior; break;
                        case "中专": addEduModel.Education = TalentVirtualEducation.Specialized; break;
                        case "初中": addEduModel.Education = TalentVirtualEducation.Junior; break;
                        default: addEduModel.Education = TalentVirtualEducation.Junior; break;
                    }

                    if (topEducation < addEduModel.Education)
                        topEducation = addEduModel.Education;
                    addEduList.Add(addEduModel);
                }

                if (_context.User_OpenId.FirstOrDefault(f => f.UserId == userId) == null)
                {
                    var topSchoolInfo = addEduList.Where(w => w.Education == topEducation).FirstOrDefault();
                    // userResume!.Education = topEducation switch
                    // {
                    //     TalentVirtualEducation.Master => EducationType.硕士,
                    //     TalentVirtualEducation.Doctor => EducationType.博士,
                    //     TalentVirtualEducation.Undergraduate => EducationType.本科,
                    //     TalentVirtualEducation.Professional => EducationType.大专,
                    //     TalentVirtualEducation.Senior => EducationType.高中,
                    //     TalentVirtualEducation.Specialized => EducationType.高中,
                    //     TalentVirtualEducation.Junior => EducationType.其他,
                    //     _ => EducationType.其他
                    // };
                    userResume.School = topSchoolInfo!.SchoolName;
                    userResume.Major = topSchoolInfo!.MajorName;
                    userResume.GraduationDate = topSchoolInfo!.EndTime is null
                        ? null
                        : DateOnly.FromDateTime(topSchoolInfo!.EndTime.Value);
                }

                _context.Talent_Virtual_Edu.AddRange(addEduList);
            }
        }

        //画像信息
        if (addTalent != null && resume.predicted_result != null)
        {
            //标签
            if (resume.predicted_result.tags != null)
            {
                addTalent.IndustryLabel = resume.predicted_result.tags.skills == null
                    ? new List<string>()
                    : resume.predicted_result.tags.skills.Select(o => o.tag).ToList();
                addTalent.PostLabel = resume.predicted_result.tags.professional == null
                    ? new List<string>()
                    : resume.predicted_result.tags.professional.Select(o => o.tag).ToList();
                addTalent.OtherLabel = resume.predicted_result.tags.others == null
                    ? new List<string>()
                    : resume.predicted_result.tags.others.Select(o => o.tag).ToList();
            }

            //亮点
            if (resume.predicted_result.highlights != null)
            {
                TalentVirtaualResumeHighlights _Highlights = new TalentVirtaualResumeHighlights()
                {
                    education = resume.predicted_result.highlights.education,
                    occupation = resume.predicted_result.highlights.occupation,
                    others = resume.predicted_result.highlights.others,
                    project = resume.predicted_result.highlights.project,
                    tags = resume.predicted_result.highlights.tags
                };
                addTalent.Highlights = _Highlights;
            }

            //风险
            if (resume.predicted_result.risks != null)
            {
                TalentVirtaualResumeRisks _Risks = new TalentVirtaualResumeRisks()
                {
                    education = resume.predicted_result.risks.education,
                    occupation = resume.predicted_result.risks.occupation,
                    tags = resume.predicted_result.risks.tags
                };
                addTalent.Risks = _Risks;
            }
        }

        record.RepeatType = RecruitUpLoadRepeatType.投递正常;
        record.UpLoadStatus = RecruitUpLoadStatus.已完成;
        //保存与简历计算
        if (addTalent != null)
        {
            try
            {
                _context.SaveChanges();
                UpdateAttachFile(record, userId);
            }
            catch
            {
                throw new BadRequestException("简历投递成功，保存人才库失败");
            }

            //计算简历完整度
            int totalCount = _commonVirtualService.CountVirtualResumeScore(addTalent.Id);
        }

        if (postList.Count > 0)
        {
            var postDbList = _context.Dic_Talent_Post.Where(o => postList.Contains(o.PostName)).Select(o => o.PostName)
                .ToList();
            List<string> addPostList = new List<string>();
            addPostList = postList.Except(postDbList).Distinct().ToList();
            if (addPostList.Count > 0)
            {
                List<Dic_Talent_Post> addList = new List<Dic_Talent_Post>();
                foreach (var i in addPostList)
                {
                    addList.Add(new Dic_Talent_Post()
                    {
                        CreatedTime = DateTime.Now,
                        PostName = i,
                        ParentId = "28"
                    });
                }

                _context.Dic_Talent_Post.AddRange(addList);
            }
        }

        if (industryList.Count > 0)
        {
            var industryDbList = _context.Dic_Talent_Industry.Where(o => industryList.Contains(o.IndustryName))
                .Select(o => o.IndustryName).ToList();
            List<string> addIndustryList = new List<string>();
            addIndustryList = industryList.Except(industryDbList).Distinct().ToList();
            if (addIndustryList.Count > 0)
            {
                List<Dic_Talent_Industry> addList = new List<Dic_Talent_Industry>();
                foreach (var i in addIndustryList)
                {
                    addList.Add(new Dic_Talent_Industry()
                    {
                        CreatedTime = DateTime.Now,
                        IndustryName = i
                    });
                }

                _context.Dic_Talent_Industry.AddRange(addList);
            }
        }

        try
        {
            _context.SaveChanges();
        }
        catch
        {
            throw new BadRequestException("简历投递成功，保存人才库失败");
        }

        //新增人才库通知楷哥消息
        MyRedis.Client.SAdd(SubscriptionKey.VirtualTalentCountChange, notifyNew);
    }

    /// <summary>
    /// 更新附件简历
    /// </summary>
    /// <param name="record"></param>
    /// <param name="userId"></param>
    private void UpdateAttachFile(Recruit_Upload_Record record, string userId)
    {
        var attach = _context.User_Resume_Attach.FirstOrDefault(f => f.UserId == userId);
        if (attach == null)
        {
            attach = new User_Resume_Attach
            {
                UserId = userId,
                Title = Path.GetFileName(record.FileUrl),
                Url = record.FileUrl,
                Size = 0L
            };
            _context.Add(attach);
        }

        attach.Title = Path.GetFileName(record.FileUrl);
        attach.Url = record.FileUrl;
        _context.SaveChanges();
    }

    /// <summary>
    /// 简历直接转成求职者，执行save
    /// </summary>
    /// <param name="_context"></param>
    /// <param name="regionModel"></param>
    /// <param name="i"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private string? ResumeToSeeker(StaffingContext _context, List<RegionModel> regionModel,
        Recruit_Upload_Record record)
    {
        var i = record.ResumeInfo;
        var thirdTalentInfo = new Config.CommonModel.ThirdTalentInfo.ThirdTalentInfo()
        {
            Source = RegisterSource.Upload,
            Avatar = i.avatar_url ?? string.Empty,
            Name = record.SeekerName,
            Phone = record.SeekerPhone,
            GenderName = i.parsing_result.basic_info.gender,
            Email = record.SeekerEmail,
            WeChatNo = i.parsing_result.contact_info?.wechat ?? string.Empty,
            Qq = i.parsing_result.contact_info?.QQ ?? string.Empty,
            RegionId = !string.IsNullOrEmpty(i.parsing_result.basic_info.detailed_location)
                ? regionModel.Where(o => o.Name.Contains(i.parsing_result.basic_info.detailed_location))
                    .FirstOrDefault()?.Id
                : null,
            DataUserCity = i.parsing_result.basic_info.detailed_location,
            Education = record.SeekerEducation,
            //Education = !string.IsNullOrEmpty(i.parsing_result.basic_info.degree)
            //                                            ? i.parsing_result.basic_info.degree switch
            //                                            {
            //                                                "硕士" => EducationType.硕士,
            //                                                "博士" => EducationType.博士,
            //                                                "本科" => EducationType.本科,
            //                                                "专科" => EducationType.大专,
            //                                                "大专" => EducationType.大专,
            //                                                "高中" => EducationType.高中,
            //                                                "中专" => EducationType.其他,
            //                                                "初中" => EducationType.其他,
            //                                                "MBA" => EducationType.硕士,
            //                                                _ => throw new Exception("未知学历")
            //                                            }
            //                                            : null,
            Birthday = record?.Birthday != null
                ? record.Birthday.Value
                : GetBirthDay(i.parsing_result.basic_info.date_of_birth),
            Describe = i.parsing_result.others?.self_evaluation,
            Skill = new List<string>().Union(i.parsing_result.others?.business_skills ?? new List<string>())
                .Union(i.parsing_result.others?.it_skills ?? new List<string>())
                .Union(i.parsing_result.others?.skills ?? new List<string>()).ToList()
        };

        // 保存用户信息前先判断投递是否正常
        PostCheck(_context, record, thirdTalentInfo);

        _paymentStep.ResumeToSeeker(thirdTalentInfo, new Infrastructure.CommonInterface.PostOrderExceptionMessage());
        return thirdTalentInfo.SeekerId;
    }

    private void PostCheck(StaffingContext _context, Recruit_Upload_Record record, ThirdTalentInfo thirdTalentInfo)
    {
        //岗位信息
        var post = _context.Post_Team.Where(x => x.TeamPostId == record.TeamPostId)
            .Select(s => new
            {
                s.Post.LeftStock,
                s.TeamPostId,
                s.TeamProjectId,
                s.Post.ProjectId,
                s.Post.Project.HrId,
                s.Post.WorkNature,
                s.Post.Sex,
                s.Post.MaxAge,
                s.Post.MinAge,
                s.Post.Education,
                s.Post.GraduationYear,
                s.Post.PostId,
                s.Post.Name,
                s.Post.Status,
                TeamStatus = s.Status,
                TeamHrId = s.Project_Team.HrId,
                s.Project_Team.Source,
                ProjectStatus = s.Post.Project.Status,
                s.Show,
                s.Post.Money,
                ProjectName = s.Post.Project.Agent_Ent.Name,
                AgentEntName = s.Post.Project.Agent_Ent.Name,
                s.Post.PaymentNode,
                s.Post.PaymentDays,
                s.Post.Project.PaymentType,
                TeamProjectType = s.Project_Team.Type
            }).FirstOrDefault();

        if (post == null)
            throw new NotFoundException("该职位不存在");

        if (!post.Show)
            throw new BadRequestException("该职位已关闭");

        if (_context.Recruit.Any(x => x.Post_Delivery.TeamPostId == post.TeamPostId
                                      && x.User_Seeker.User.Mobile == record.SeekerPhone &&
                                      x.Status != RecruitStatus.FileAway))
            throw new BadRequestException("您已投递该职位，请勿重复操作");

        if (_context.Recruit.Any(x => x.Post_Delivery.PostId == post.PostId
                                      && x.User_Seeker.User.Mobile == record.SeekerPhone &&
                                      x.Status != RecruitStatus.FileAway))
            throw new BadRequestException("您已在其他顾问处投递该职位，请勿重复操作");

        // 库存校验 todo:主创渠道场景暂时未考虑
        if (post.LeftStock <= 0)
            throw new BadRequestException("人员已招满，无法投递");

        if (post.Sex.HasValue && thirdTalentInfo.GenderName != post.Sex.GetDescription())
            throw new BadRequestException("您的性别不符合要求", "SexMismatch");

        if (post.Education != EducationType.不限 && (thirdTalentInfo.Education < post.Education ||
                                                   thirdTalentInfo.Education is null ||
                                                   thirdTalentInfo.Education == EducationType.其他))
            throw new BadRequestException("您的学历不符合要求", "EducationMismatch");

        var userAge = Tools.GetAgeByBirthdate(thirdTalentInfo.Birthday);
        if (post.MinAge > 0 && (userAge == null || userAge < post.MinAge))
            throw new BadRequestException("您的年龄不符合要求", "AgeMismatch");
        if (post.MaxAge > 0 && (userAge == null || userAge > post.MaxAge))
            throw new BadRequestException("您的年龄不符合要求", "AgeMismatch");
    }

    private DateOnly? GetBirthDay(string date_of_birth)
    {
        DateOnly? birthday = null;
        if (string.IsNullOrEmpty(date_of_birth))
            return null;
        if (date_of_birth.Length == 4)
        {
            if (DateTime.TryParse(date_of_birth + "-01-01", out DateTime _Birthday))
                birthday = DateOnly.FromDateTime(_Birthday);
        }
        else
        {
            if (DateTime.TryParse(date_of_birth, out DateTime _Birthday))
                birthday = DateOnly.FromDateTime(_Birthday);
        }

        return birthday;
    }

    public List<PostTeamInfoEnum> GetPostsList(string TeamProjectId)
    {
        List<PostTeamInfoEnum> list = new();
        var posts = _context.Post_Team
            .Where(w => w.Status == PostStatus.发布中 && w.Show == true && w.TeamProjectId == TeamProjectId)
            .Select(s => new { s.TeamPostId, s.Post.Name }).ToList();
        foreach (var post in posts)
        {
            list.Add(new PostTeamInfoEnum { TeamPostId = post.TeamPostId, PostName = post.Name });
        }

        return list;
    }

    public EmptyResponse ResumeDelivery(UpLoadResumeInfoRequest request)
    {
        string type = "RecruitResumeDelivery";
        if (!MyRedis.Client.SetNx($"resume:delivery:{type}", 1, 3))
            throw new BadRequestException("操作频繁，限3秒一次");

        var record = _context.Recruit_Upload_Record.FirstOrDefault(f => f.Id == request.RecordId);
        record.SeekerName = request.Name;
        record.SeekerPhone = request.Mobile;
        record.SeekerEmail = request.Email;
        record.TeamPostId = request.TeamPostId;
        record.SeekerEducation = request.Education;
        if (request.Age > 0)
            record.Birthday = Tools.GetBirthdateByAge(request.Age);

        ResumeDelivery(record);

        return new EmptyResponse();
    }

    /// <summary>
    /// 是否绑定真实用户校验，是否有openid
    /// </summary>
    /// <param name="mobile"></param>
    /// <exception cref="NotImplementedException"></exception>
    private bool IfBindUser(string mobile)
    {
        bool binded = false;
        var openInfo = _context.User_OpenId.Where(w => w.User_Seeker.User.Mobile == mobile).FirstOrDefault();
        if (openInfo != null && !string.IsNullOrWhiteSpace(openInfo.Id))
            binded = true;
        return binded;
    }

    public EmptyResponse GetExists(UpLoadResumeInfoRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.Mobile))
            throw new BadRequestException("手机号必填");
        if (string.IsNullOrWhiteSpace(request.TeamPostId))
            throw new BadRequestException("未选择职位");
        var record = _context.Recruit_Upload_Record.FirstOrDefault(f =>
            f.SeekerPhone == request.Mobile && f.TeamPostId == request.TeamPostId);
        if (record != null)
            throw new BadRequestException("简历重复投递");

        return new EmptyResponse();
    }

    public EmptyResponse EditKuaiShouResume(EditKuaiShouResume request)
    {
        var resume = _context.Recruit.FirstOrDefault(x => x.RecruitId == request.RecruitId)
                     ?? throw new BadRequestException("内容不存在");

        var seekerId = resume.SeekerId;

        var openInfo = _context.User_OpenId.FirstOrDefault(f => f.UserId == seekerId);
        if (openInfo != null)
            throw new BadRequestException("该简历已绑定真实用户，禁止修改");

        var userSeeker = _context.User_Seeker.FirstOrDefault(f => f.UserId == seekerId);
        var userResume = _context.User_Resume.FirstOrDefault(f => f.UserId == seekerId);

        if (!string.IsNullOrWhiteSpace(request.Name))
            userSeeker.NickName = request.Name;

        if (request.Education.HasValue)
        {
            userResume.Education = request.Education;
        }

        if (request.Sex.HasValue)
        {
            userResume.Sex = request.Sex;
        }

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public EmptyResponse WaitToFeedback(string recruitId)
    {
        var waitings = _context.Recruit_Interview
            .Where(w => w.RecruitId == recruitId && w.Outcome == RecruitInterviewOutcome.Waiting).ToList();
        if (waitings.Count > 0)
        {
            var lastStatus = _context.Recruit_Record.Where(w => w.RecruitId == recruitId)
                .OrderByDescending(o => o.CreatedTime).FirstOrDefault();
            if (lastStatus != null && lastStatus.Status == RecruitStatus.Interview)
                throw new BadRequestException("存在未反馈的面试安排");
        }


        return new EmptyResponse();
    }

    public List<ExportRecruitData> ExportRecruit(ExportRecruitRequest model)
    {
        var predicate = PredicateBuilder.New<Recruit>(x => true);
        // var predicate2 = PredicateBuilder.New<Recruit_Record>(x => true);

        var proj = _context.Project_Team.Where(x => x.TeamProjectId == model.TeamProjectId)
            .Select(s => new
            {
                s.Type,
                s.ProjectId
            }).FirstOrDefault();

        //如果是自己项目，查询全部
        if (proj?.Type == HrProjectType.自己)
            predicate = predicate.And(x =>
                x.Post_Delivery.Post.ProjectId == proj.ProjectId && x.Post_Delivery.Post.Project.HrId == _user.Id);
        else
            predicate = predicate.And(x =>
                x.Post_Delivery.Post_Team.TeamProjectId == model.TeamProjectId &&
                x.Post_Delivery.Post_Team.Project_Team.HrId == _user.Id);

        var data = _context.Recruit.Where(predicate).OrderByDescending(o => o.CreatedTime)
            .Take(1000)
            .Select(s => new ExportRecruitData
            {
                RecruitId = s.RecruitId,
                求职者 = s.User_Seeker.NickName,
                求职者电话 = s.User_Seeker.User.Mobile,
                身份证号 = s.User_Seeker.User.IdentityCard,
                性别 = s.User_Seeker.User_Resume.Sex.GetDescription(),
                学历 = s.User_Seeker.User_Resume.Education.GetDescription(),
                投递时间 = s.CreatedTime,
                投递岗位 = s.Post_Delivery.Post.Name,
                投递公司 = s.Post_Delivery.Post.Agent_Ent.Name,
                招聘流程 = s.Status.GetDescription(),
                面试时间 = s.Recruit_Interview.OrderByDescending(o => o.CreatedTime).FirstOrDefault().InterviewTime,
                邀面人 = s.FollowerUser.NickName,
                邀面人电话 = s.FollowerUser.User.Mobile,
                // 面试是否到场 = string.Empty,
                入职时间 = string.Empty
            }).ToList();

        // var records = _context.Recruit_Record.Where(predicate2).OrderBy(o => o.RecruitId).ThenBy(o => o.CreatedTime)
        // .Select(s => new
        // {
        //     s.RecruitId,
        //     s.CreatedTime,
        //     s.Status
        // }).ToList();

        // var sortRecords = new SortedList<string, List<DateTime>>();

        // foreach (var item in records)
        // {
        //     if (!sortRecords.ContainsKey(item.RecruitId))
        //         sortRecords.Add(item.RecruitId, new List<DateTime> { item.CreatedTime });
        //     else
        //         sortRecords[item.RecruitId].Add(item.CreatedTime);
        // }

        // foreach (var item in data)
        // {
        //     var record = sortRecords[item.RecruitId!].OrderBy(o => o).Take(2).ToArray();
        //     if (record.Count() >= 2)
        //     {
        //         item.处理时常_分钟 = (int)(record[1] - record[0]).TotalMinutes;
        //     }
        // }

        return data;
    }

    public async Task<BatchInductionResponse> BatchInduction(BatchInductionRequest request)
    {
        string type = "BatchInduction";
        if (!MyRedis.Client.SetNx($"recruit:import:{type}", 1, 3))
            throw new BadRequestException("操作频繁，限3秒一次");

        if (string.IsNullOrWhiteSpace(request.Url))
            throw new BadRequestException("文件路径不存在");

        if (string.IsNullOrWhiteSpace(request.ProjectId))
            throw new BadRequestException("项目参数缺失");

        // 获取Excel内容
        var content = await request.Url.GetAsync().ReceiveBytes();
        var import = MiniExcel.Query<ExportRecruitData>(new MemoryStream(content)).ToList();
        if (import == null || import.Count == 0)
            throw new BadRequestException("缺少内容");

        // 数据校验
        var seekerPhones = new List<string>();
        foreach (var item in import)
        {
            if (!string.IsNullOrWhiteSpace(item.求职者电话))
                seekerPhones.Add(item.求职者电话);
        }

        if (seekerPhones.Count == 0)
            throw new BadRequestException("缺少需要入职的求职者");

        var seekers = _context.User_Seeker.Where(w => seekerPhones.Contains(w.User.Mobile))
            .Select(s => new
            {
                Mobile = s.User.Mobile,
                UserId = s.UserId
            }).ToList();

        var response = new BatchInductionResponse();

        // 1.未注册求职者校验
        var errorPhones = new List<string>();
        foreach (var p in seekerPhones)
        {
            if (!seekers.Any(a => a.Mobile == p))
                errorPhones.Add(p);
        }

        if (errorPhones.Count > 0)
            throw new BadRequestException($"{string.Join(",", errorPhones.Take(3))}等人未注册");

        // 补充入职日期格式检测
        var errorInduction = import.Where(w => !DateTime.TryParse(w.入职时间, out _)).ToList();

        if (errorInduction.Count > 0)
            throw new BadRequestException($"{string.Join(",", errorInduction.Take(3).Select(s => s.求职者电话))}等人缺少入职日期");

        // 查找招聘流程并入职
        // 想根据projectid + seekerid来确定唯一投递，来确定唯一招聘流程，但是存在同一个求职者同时投递同一个顾问的多个职位
        // 按实际场景的话，一个求职者只能入职一个职位，所以一个求职者如果有多个招聘流程，则只入职其中一个，其余的招聘流程，如果不归档的话，再次导入
        // 就又入职了，所以这里要求协同只能面试反馈通过一个招聘流程，如果有多个，则导入失败
        var successRecruit = new StringBuilderHelper();
        var failedRecruits = new StringBuilderHelper();
        try
        {
            // 获取所有求职者的 userId
            var userIds = import.Select(item => seekers.FirstOrDefault(f => f.Mobile == item.求职者电话)?.UserId).ToList();

            // 检查是否有多个入职流程的求职者
            var recruitGroups = userIds.Select((userId, index) => new { userId, item = import[index] })
            .GroupBy(x => x.userId)
            .Select(g => new
            {
                UserId = g.Key,
                Items = g.Select(x => x.item).ToList(),
                Recruits = _context.Recruit.Where(w =>
                    (w.Status == RecruitStatus.Interview || w.Status == RecruitStatus.Offer ||
                     w.Status == RecruitStatus.Induction)
                    && w.Post_Delivery.SeekerId == g.Key
                    && w.Post_Delivery.Post_Team.Project_Team.ProjectId == request.ProjectId).ToList()
            }).ToList();

            if (recruitGroups.Any(g => g.Recruits.Count == 0))
            {
                var fRecruits = recruitGroups.Where(g => g.Recruits.Count == 0)
                .SelectMany(g => g.Items)
                .Select(item => $"{item.求职者}-{item.求职者电话},不存在需要入职的招聘流程")
                .ToList();
                throw new BadRequestException(string.Join("\n", fRecruits));
            }

            if (recruitGroups.Any(g => g.Recruits.Count > 1))
            {
                var fRecruits = recruitGroups.Where(g => g.Recruits.Count > 1)
                .SelectMany(g => g.Items)
                .Select(item => $"{item.求职者}-{item.求职者电话},存在多个入职流程")
                .ToList();
                throw new BadRequestException(string.Join("\n", fRecruits));
            }

            foreach (var item in import)
            {
                string userId = seekers.FirstOrDefault(f => f.Mobile == item.求职者电话).UserId;
                var recruits = _context.Recruit.Where(w =>
                    (w.Status == RecruitStatus.Interview || w.Status == RecruitStatus.Offer ||
                     w.Status == RecruitStatus.Induction)
                    //&& w.Recruit_Interview.OrderByDescending(o => o.CreatedTime).FirstOrDefault().Outcome == RecruitInterviewOutcome.Passed
                    && w.Post_Delivery.SeekerId == userId
                    && w.Post_Delivery.Post_Team.Project_Team.ProjectId == request.ProjectId).ToList();

                if (recruits.Count == 0)
                {
                    failedRecruits.AppendLine($"{item.求职者}-{item.求职者电话},不存在需要入职的招聘流程");
                    continue;
                }

                if (recruits.Count > 1)
                {
                    failedRecruits.AppendLine($"{item.求职者}-{item.求职者电话},存在多个入职流程");
                    continue;
                }

                try
                {
                    var recruit = recruits[0];
                    // 已入职的，只修改入职时间
                    if (recruit.Status == RecruitStatus.Induction)
                    {
                        recruit.InductionTime = item.入职时间?.ToNullableDate();
                    }
                    else
                    {
                        await EditRecruitInduction(new RequestEditRecruitInduction
                        {
                            RecruitId = recruit.RecruitId,
                            InductionTime = item.入职时间?.ToNullableDate()
                        });
                    }

                    successRecruit.AppendLine($"{item.求职者}-{item.求职者电话},入职成功");
                }
                catch (Exception ex)
                {
                    throw new Exception($"{recruits[0].RecruitId}_{ex.Message}");
                }
            }

            // 添加上传记录
            var info = new Recruit_Upload_Record()
            {
                CreatedTime = DateTime.Now,
                FileExtension = TalentUpLoadFileExtension.Xlsx,
                FileName = "batchinduction.xlsx",
                FileUrl = request.Url,
                HrId = _user.Id,
                UpLoadStatus = RecruitUpLoadStatus.已完成,
                SeekerName = string.Empty,
                SeekerPhone = string.Empty,
                SeekerEmail = string.Empty,
                TeamPostId = request.ProjectId
            };
            _context.Recruit_Upload_Record.Add(info);
            _context.SaveChanges();
        }
        catch
        {
            throw;
        }

        response.SuccessStr = successRecruit.ToString();
        response.SuccessCount = successRecruit.AppendLineCount();
        response.FailedStr = failedRecruits.ToString();
        response.FailedCount = failedRecruits.AppendLineCount();
        return response;
    }

    public async Task<List<PostInterviewTimeModel>> GetInterviewTime(string recruitid)
    {
        if (string.IsNullOrWhiteSpace(recruitid))
            throw new BadRequestException("缺少招聘流程Id");

        var postId = _context.Recruit.Where(f => f.RecruitId == recruitid).Select(s => s.Post_Delivery.PostId)
            .FirstOrDefault();
        if (string.IsNullOrWhiteSpace(postId))
            throw new BadRequestException($"不存在的职位Id:{postId}");

        var result = await _context.Post_Interview_Date.Where(w => w.PostId == postId
                                                                   && w.Status == PostInterviewTimeStatus.可预约
                                                                   && w.Time >= DateTime.Now)
            .Select(s => new PostInterviewTimeModel
            {
                Time = s.Time,
                Status = s.Status
            })
            .OrderBy(o => o.Time)
            .ToListAsync();

        return result;
    }

    /// <summary>
    /// 线索大厅列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public CluesHallListResp GetCluesHallList(CluesHallReq model)
    {
        var result = new CluesHallListResp();
        var predicate = PredicateBuilder.New<Recruit>(r => r.IsClues == ProjectTeamIsClues.是)
            .And(r => !model.RecruitStatus.HasValue
                ? r.AcceptOrder == RecruitAcceptOrderEnum.未接单
                : r.AcceptOrder == RecruitAcceptOrderEnum.已经接单);


        // 处理 RecruitStatus 条件
        if (model.RecruitStatus.HasValue)
        {
            predicate = ApplyRecruitStatusFilter(predicate, model.RecruitStatus.Value);
            predicate = predicate.And(r => r.FollowerId == _user.Id);
        }
        else
        {
            predicate = predicate.And(r => r.ReceiverId == _user.Id || string.IsNullOrEmpty(r.ReceiverId));
        }
        // 处理搜索条件
        predicate = ApplySearchFilter(predicate, model.Search);

        // 处理城市条件
        predicate = ApplyCityFilter(predicate, model.City);

        // 处理 Category 条件
        predicate = ApplyCategoryFilter(predicate, model.Category);

        // 处理 PaymentNode 条件
        predicate = ApplyPaymentNodeFilter(predicate, model.PaymentNode);

        // 处理薪资条件
        predicate = ApplySalaryFilter(predicate, model.MinSalary, model.MaxSalary);

        // 处理教育条件
        predicate = ApplyEducationFilter(predicate, model.SeekerEducation);

        // 处理简历免审条件
        predicate = ApplyResumeExemptFilter(predicate, model.IsResumeExempt);

        // 处理面试时间条件
        predicate = ApplyInterviewTimeFilter(predicate, model.InterviewStartTime, model.InterviewEndTime);
        if (model.ClueLabel.HasValue)
        {
            if (model.ClueLabel == (int)ClueTagEnums.面试通过率高)
            {
                predicate = predicate.And(r => r.Post_Delivery.Post.Post_Extend.InductionNum > 5);
            }
            else if (model.ClueLabel == (int)ClueTagEnums.调研资料多)
            {
                predicate = predicate.And(r => r.Post_Delivery.Post.Project.Project_Survey.ContentLength > 2000);
            }
        }
        var sql = _context.Recruit.Where(predicate);
        sql = model.IsDescending ? sql.OrderByDescending(m => m.CreatedTime) : sql.OrderBy(m => m.CreatedTime);
        //Console.WriteLine(sql.ToQueryString());
        result.Total = sql.Count();
        result.Rows = sql
            .Skip((model.PageIndex - 1) * model.PageSize)
            .Take(model.PageSize)
            .Select(s => new CluesInfo
            {
                ModeType = s.ModeType,
                ReceiverId = s.ReceiverId,
                Id = s.RecruitId,
                ProjectId = s.Post_Delivery.Post.ProjectId,
                Name = s.User_Seeker.NickName,
                Avatar = s.User_Seeker.Avatar,
                Mobile = s.User_Seeker.User.Mobile,
                Sex = s.User_Seeker.User_Resume.Sex,
                Birthday = s.User_Seeker.User_Resume.Birthday,
                Occupation = s.User_Seeker.User_Resume.Occupation,
                Education = s.User_Seeker.User_Resume.Education,
                RegionId = s.User_Seeker.RegionId,
                UserSeekerTencentImId = s.User_Seeker.TencentImId,
                Source = s.Post_Delivery.Post_Team.Project_Team.Source,
                TeamHrId = s.Post_Delivery.Post_Team.Project_Team.HrId,
                Adviser = s.Post_Delivery.Post_Team.Project_Team.User_Hr.NickName,
                LoginTime = s.User_Seeker.User_Extend.LoginTime,
                EduExpSchool = s.User_Seeker.User_Resume.School,
                EduExpMajor = s.User_Seeker.User_Resume.Major,
                EduExpEducation = s.User_Seeker.User_Resume.Education,
                WorkExpClass = s.User_Seeker.User_Resume.User_Work.OrderByDescending(work => work.BeginDate).Select(userWork =>
                    new Model.Hr.Recruit.WorkExpClass
                    {
                        BeginDate = userWork.BeginDate,
                        Company = userWork.Company,
                        EndDate = userWork.EndDate,
                        Post = userWork.Post,
                    }).FirstOrDefault(),
                HopeIndustry = s.Post_Delivery.Post.Name,
                Active = Tools.GetOnlineStatus(s.User_Seeker.User_Extend.LoginTime),
                DeliveryTime = s.Post_Delivery.CreatedTime,
                EntName = s.Post_Delivery.Post.Project.Agent_Ent.DisplayName,
                Money = s.Post_Delivery.Post.Money ?? 0,
                InterviewDashboard = new PostRecentInterviews
                {
                    RecentDeliveryRate = s.Post_Delivery.Post.Post_Extend.RecentDeliveryRate,
                    HistoryBillingRate = s.Post_Delivery.Post.Post_Extend.HistoryBillingRate
                },
                PostId = s.Post_Delivery.PostId,
                ClueCode = $"{Constants.CluePre}{s.AutoId.ToString()!.PadLeft(9, '0')}",
                Hr = new HrModel
                {
                    AppletQrCode = s.Post_Delivery.Post.Project.User_Hr.AppletQrCode,
                    HrId = s.Post_Delivery.Post.Project.HrId,
                    NuoId = s.Post_Delivery.Post.Project.User_Hr.NuoId,
                    HrName = s.Post_Delivery.Post.Project.User_Hr.NickName,
                    Avatar = s.Post_Delivery.Post.Project.User_Hr.Avatar,
                    Post = s.Post_Delivery.Post.Project.User_Hr.Post,
                    LoginTime = s.Post_Delivery.Post.Project.User_Hr.User_Extend.LoginTime,
                    EntName = s.Post_Delivery.Post.Project.User_Hr.Enterprise.Name,
                    EntAbbr = s.Post_Delivery.Post.Project.User_Hr.Enterprise.Abbreviation,
                    Mobile = s.Post_Delivery.Post.Project.User_Hr.User.Mobile,
                    TencentImId = s.Post_Delivery.Post.Project.User_Hr.TencentImId
                },
                Welfare = s.Post_Delivery.Post.Welfare,
                IsResumeExempt = s.Post_Delivery.Post.IsResumeExempt,
                InductionNum = s.Post_Delivery.Post.Post_Extend.InductionNum,
                InvalidDisplay = s.HrId == _user.Id && s.Post_Bounty != null && s.Post_Bounty.Status == BountyStatus.交付中,
                BountyStatus = s.Post_Bounty.Status,
                IfStockOut = s.Post_Bounty.IfStockOut,
                InductionTime = s.InductionTime,
                PaymentDays = s.Post_Bounty.PaymentDays,
                PaymentNode = s.Post_Bounty.PaymentNode,
                RewardType = s.Post_Bounty.RewardType,
                PaymentCycleName = s.Post_Bounty.PaymentCycle.GetDescription(),
                AgentEntName = s.Post_Delivery.Post.Agent_Ent.Name,
                IsSalesCommission = s.Post_Delivery.Post.IsSalesCommission,

            }).ToList();
        var postIds = result.Rows.Select(s => s.PostId).ToList();
        var projectIds = result.Rows.Select(s => s.ProjectId).ToList();
        // 查询职位面试配置信息
        var postInterviews = _context.Post_Interview_Config.Where(x => postIds.Contains(x.PostId)).ToList();
        //最近7天
        var recent7 = DateTime.Now.Date.AddDays(-6);
        //本周面试人数
        var weekInterview = _context.Recruit_Record.Where(x =>
                x.CreatedTime >= recent7 && postIds.Contains(x.Recruit.Post_Delivery.PostId) &&
                Constants.InterviewStatus.Contains(x.Status))
            .GroupBy(g => g.RecruitId)
            .Select(s => new { s.Key, PostId = s.Max(m => m.Recruit.Post_Delivery.PostId) })
            .GroupBy(g => g.PostId)
            .Select(s => new { s.Key, Ct = s.Count() }).ToList();

        var bgt = DateTime.Now.Date.AddDays(-14);
        var interviewTimes = _context.Post_Interview_Date.Where(x => postIds.Contains(x.PostId) && x.Time >= bgt)
            .ToList();
        var projectSurveyMap = _context.ProjectSurvey
        .Where(x => projectIds.Contains(x.ProjectId) && string.IsNullOrEmpty(x.PostId))
        .GroupBy(x => x.ProjectId)
        .ToDictionary(g => g.Key, g => g.First());
        //线索大厅实时计算约面佣金
        var isNoah = _context.User_Hr.Include(userHr => userHr.Enterprise).First(s => s.UserId == _user.Id).Enterprise.Specific?.Contains(EntSpecific.Noah) == true;
        var settings = _context.Sys_Settings.FirstOrDefault();
        BountyConfig? bountyConfig;
        foreach (var item in result.Rows)
        {
            if (isNoah)
            {
                if ((bool)item.IsSalesCommission!)
                    bountyConfig = settings.NoahFull;
                else
                    bountyConfig = settings.NoahNoSale;
            }
            else
            {
                if ((bool)item.IsSalesCommission!)
                    bountyConfig = settings.PlatformFull;
                else
                    bountyConfig = settings.PlatformNoSale;
            }

            item.FollowerBounty = ((decimal)item.Money! * bountyConfig.Follower).ToFixed(2);
            item.SourceName = item.Source?.GetDescription();
            item.Age = Tools.GetAgeByBirthdate(item.Birthday);
            item.Hr!.OnlineStatus = Tools.GetOnlineStatus(item.Hr.LoginTime);
            item.EduExp = $"{item.EduExpSchool}-{item.EduExpMajor} {item.GraduationDate?.ToString("yyyy-MM")}";
            var workTimeStr = string.Empty;
            if (item.WorkExpClass?.BeginDate != null)
                workTimeStr =
                    $"{item.WorkExpClass?.BeginDate?.ToString("yyyy-MM")}~{item.WorkExpClass?.EndDate?.ToString("yyyy-MM")}";
            item.WorkExp = $"{item.WorkExpClass?.Company}-{item.WorkExpClass?.Post} {workTimeStr}";
            if (!string.IsNullOrWhiteSpace(item.RegionId))
                item.City = _commonDicService.GetCityById(item.RegionId);
            // 倒计时处理
            if (item.PaymentNode != null)
            {
                switch (item.PaymentNode)
                {
                    case ProjectPaymentNode.入职过保:
                        if (item.BountyStatus == BountyStatus.交付中 && item.IfStockOut == 1)
                        {
                            item.EndCountTime = item.InductionTime!.Value.AddDays(item.PaymentDays!.Value);
                        }
                        break;
                    case ProjectPaymentNode.简历交付:
                        if (item.BountyStatus == BountyStatus.交付中 && item.IfStockOut == 1)
                        {
                            var interviewerScreen = _context.Recruit_Interviewer_Screen.Where(w => w.RecruitId == item.Id)
                                .OrderBy(o => o.CreatedTime).Select(x => new { x.CreatedTime }).FirstOrDefault();
                            if (interviewerScreen?.CreatedTime != null)
                                item.EndCountTime = interviewerScreen.CreatedTime.AddDays(7);
                            // item.EndCountTime = item.CreatedTime!.Value.AddDays(7);
                        }
                        break;
                    case ProjectPaymentNode.到面交付:
                        if (item.BountyStatus == BountyStatus.交付中)
                        {
                            // todo：耗时操作
                            var recruit = _context.Recruit_Interview.Where(w => w.RecruitId == item.Id && w.Outcome == RecruitInterviewOutcome.Waiting).FirstOrDefault();
                            if (recruit != null)
                                item.EndCountTime = recruit.InterviewTime.AddDays(7);
                        }
                        break;
                }
            }
            //面试信息
            var interview = postInterviews.FirstOrDefault(x => x.PostId == item.PostId) ?? new Post_Interview_Config();
            if (interview != null)
            {
                //InterviewTime 只保留昨天至未来6天的面试时间
                var beginTime = DateTime.Now.Date.AddDays(-1);
                // var endTime = beginTime.AddDays(6);

                var interviewTime = interviewTimes.Where(x => x.PostId == item.PostId).ToList();
                foreach (var tm in Enumerable.Range(0, 7).Select(i => beginTime.AddDays(i)))
                {
                    PostInterviewTimeStatus? st = null;
                    if (interviewTime.Any(x =>
                            x.Time.Date == tm && x.Status == PostInterviewTimeStatus.可预约 &&
                            tm >= DateTime.Today.AddDays(interview.AdvanceDays)))
                        st = PostInterviewTimeStatus.可预约;
                    else
                        //if (interviewTime.Any(x =>
                        //             (x.Time.Date == tm && x.Status == PostInterviewTimeStatus.取消预约) ||
                        //             tm < DateTime.Now.AddDays(interview.AdvanceDays)))
                        st = PostInterviewTimeStatus.取消预约;

                    var its = new PostInterviewTimeModel
                    {
                        Time = tm,
                        Status = st,
                        SubTime = interviewTime.Where(x => x.Time.Date == tm).Select(s => TimeOnly.FromDateTime(s.Time))
                            .OrderBy(o => o).ToList()
                    };
                    item.InterviewDashboard.Recent.Add(its);
                }

                item.InterviewDashboard.RecentInterviewNum =
                    weekInterview.Where(x => x.Key == item.PostId)?.Sum(c => (int?)c.Ct) ?? 0;

                //产品：该数是未来可预约面试的场次，与时间无关
                item.InterviewDashboard.InterviewNum = interviewTime
                    ?.Where(x => x.Status == PostInterviewTimeStatus.可预约 && x.Time > DateTime.Now).Count() ?? 0;
            }
            //标签数据补充
            AddTags(item, projectSurveyMap!);

        }

        return result;
    }

    //处理 RecruitStatus 条件
    private Expression<Func<Recruit, bool>> ApplyRecruitStatusFilter(Expression<Func<Recruit, bool>> predicate,
        RecruitStatus recruitStatus)
    {
        var statusFilters = new Dictionary<RecruitStatus, Expression<Func<Recruit, bool>>>
        {
            {
                RecruitStatus.Yaoyue, o => o.Status == RecruitStatus.InterviewerScreening
                                           && o.Recruit_Interviewer_Screen.OrderByDescending(o => o.CreatedTime)
                                               .FirstOrDefault().Status == RecruitInterviewerScreenStatus.Adopt
            },
            {
                RecruitStatus.InterviewerScreening, o => o.Status == RecruitStatus.InterviewerScreening
                                                         && o.Recruit_Interviewer_Screen
                                                             .OrderByDescending(o => o.CreatedTime).FirstOrDefault()
                                                             .Status != RecruitInterviewerScreenStatus.Adopt
            },
            {
                RecruitStatus.Jifei, o => !o.Post_Bounty.Deleted
                                          && o.Post_Bounty.Post_Bounty_Stage.Any(x=>x.Status == BountyStageStatus.交付中 && x.GuaranteeStatus == GuaranteeStatus.过保)
            },
            {
                RecruitStatus.Guobao, o => !o.Post_Bounty.Deleted
                                           && o.Post_Bounty.Post_Bounty_Stage.Any(x=>x.Status == BountyStageStatus.交付中 && x.GuaranteeStatus == GuaranteeStatus.过保)
            }
        };

        return statusFilters.TryGetValue(recruitStatus, out var filter)
            ? predicate.And(filter)
            : predicate.And(o => o.Status == recruitStatus);
    }

    //处理搜索条件
    private Expression<Func<Recruit, bool>> ApplySearchFilter(Expression<Func<Recruit, bool>> predicate, string search)
    {
        return string.IsNullOrEmpty(search)
            ? predicate
            : predicate.And(r =>
                r.Post_Delivery.Post.Name.Contains(search) ||
                r.Post_Delivery.Post.Project.Agent_Ent.Abbr.Contains(search) ||
                r.Post_Delivery.Post.Project.User_Hr.NickName.Contains(search) ||
                r.Post_Delivery.Post.Project.Agent_Ent.DisplayName.Contains(search) ||
                r.User_Seeker.NickName.Contains(search) ||
                r.User_Seeker.User.Mobile.Contains(search));
    }

    //处理城市条件
    private Expression<Func<Recruit, bool>> ApplyCityFilter(Expression<Func<Recruit, bool>> predicate, string city)
    {
        return string.IsNullOrEmpty(city)
            ? predicate
            : predicate.And(r => r.Post_Delivery.Post.RegionId.StartsWith(city));
    }

    //处理 Category 条件
    private Expression<Func<Recruit, bool>> ApplyCategoryFilter(Expression<Func<Recruit, bool>> predicate,
        int? category)
    {
        if (!category.HasValue) return predicate;

        var categoryLevel = _commonDicService.GetPost()
            .FirstOrDefault(x => x.Id == category.ToString())?.Level;

        return string.IsNullOrWhiteSpace(categoryLevel)
            ? predicate
            : predicate.And(r => r.Post_Delivery.Post.Dic_Post.Level.StartsWith(categoryLevel));
    }

    //处理 PaymentNode 条件
    private Expression<Func<Recruit, bool>> ApplyPaymentNodeFilter(Expression<Func<Recruit, bool>> predicate,
        ProjectPaymentNode? paymentNode)
    {
        return paymentNode.HasValue
            ? predicate.And(r => r.Post_Bounty.PaymentNode == paymentNode)
            : predicate;
    }

    //处理薪资条件
    private Expression<Func<Recruit, bool>> ApplySalaryFilter(Expression<Func<Recruit, bool>> predicate,
        decimal? minSalary, decimal? maxSalary)
    {
        if (minSalary.HasValue && maxSalary.HasValue)
        {
            predicate = predicate.And(r => r.Post_Delivery.Post.MaxSalary >= minSalary &&
                                           r.Post_Delivery.Post.MinSalary <= maxSalary);
        }
        else if (minSalary.HasValue)
        {
            predicate = predicate.And(r => r.Post_Delivery.Post.MaxSalary >= minSalary);
        }
        else if (maxSalary.HasValue)
        {
            predicate = predicate.And(r => r.Post_Delivery.Post.MinSalary <= maxSalary);
        }
        return predicate;
    }

    //处理教育条件
    private Expression<Func<Recruit, bool>> ApplyEducationFilter(Expression<Func<Recruit, bool>> predicate,
        EducationType? seekerEducation)
    {
        if (seekerEducation == EducationType.不限)
        {
            seekerEducation = null;
        }
        return seekerEducation.HasValue
            ? predicate.And(r => r.Post_Delivery.User_Resume.Education == seekerEducation)
            : predicate;
    }

    //处理简历免审条件
    private Expression<Func<Recruit, bool>> ApplyResumeExemptFilter(Expression<Func<Recruit, bool>> predicate,
        bool? isResumeExempt)
    {
        return isResumeExempt.HasValue
            ? predicate.And(r => r.Post_Delivery.Post.IsResumeExempt == isResumeExempt)
            : predicate;
    }

    //处理面试时间条件
    private Expression<Func<Recruit, bool>> ApplyInterviewTimeFilter(Expression<Func<Recruit, bool>> predicate,
        DateTime? interviewStartTime, DateTime? interviewEndTime)
    {
        if (interviewStartTime.HasValue)
        {
            predicate = predicate.And(r => r.Recruit_Interview.Any(ri => ri.InterviewTime >= interviewStartTime));
        }

        if (interviewEndTime.HasValue)
        {
            var endOfDay = interviewEndTime.Value.Date.AddDays(1);
            predicate = predicate.And(r => r.Recruit_Interview.Any(ri => ri.InterviewTime < endOfDay));
        }

        return predicate;
    }
    private void AddTags(CluesInfo item, Dictionary<string, Project_Survey> projectSurveyMap)
    {
        if (item == null)
        {
            throw new ArgumentNullException(nameof(item));
        }
        // 添加标签的逻辑
        AddReceiverTag(item);
        AddNewLeadTag(item);
        AddResumeReviewTag(item);
        AddWelfareTags(item);
        AddHighInterviewPassRateTag(item);
        AddSurveyDataTag(item, projectSurveyMap);
    }

    private void AddReceiverTag(CluesInfo item)
    {
        if (item.ModeType == ProjectTeamConfigType.指定人)
        {
            ClueTagsResp clueTagsResp = new ClueTagsResp
            {
                Name = ClueTagEnums.指定线索.GetDescription(),
                Value = ClueTagEnums.指定线索
            };
            item.FirstTags.Add(clueTagsResp);
        }
    }

    private void AddNewLeadTag(CluesInfo item)
    {
        if (item.CreatedTime.HasValue && (DateTime.Now - item.CreatedTime.Value).TotalHours < 24)
        {
            ClueTagsResp clueTagsResp = new ClueTagsResp
            {
                Name = ClueTagEnums.新线索.GetDescription(),
                Value = ClueTagEnums.新线索
            };
            item.FirstTags.Add(clueTagsResp);
        }
    }

    private void AddResumeReviewTag(CluesInfo item)
    {
        if (!item.IsResumeExempt)
        {
            ClueTagsResp clueTagsResp = new ClueTagsResp
            {
                Name = ClueTagEnums.简历需审.GetDescription(),
                Value = ClueTagEnums.简历需审
            };
            item.FirstTags.Add(clueTagsResp);
        }
    }

    private void AddWelfareTags(CluesInfo item)
    {
        item.Welfare = item.Welfare?.Take(3).ToList() ?? new List<WelfareModel>();
        foreach (var welfare in item.Welfare)
        {
            item.SecondTags.Add(welfare.Name);
        }
    }

    private void AddHighInterviewPassRateTag(CluesInfo item)
    {
        if (item.InductionNum > 5)
        {
            ClueTagsResp clueTagsResp = new ClueTagsResp
            {
                Name = ClueTagEnums.面试通过率高.GetDescription(),
                Value = ClueTagEnums.面试通过率高
            };
            item.ThirdTags.Add(clueTagsResp);
        }
    }

    private void AddSurveyDataTag(CluesInfo item, Dictionary<string, Project_Survey> projectSurveyMap)
    {
        if (projectSurveyMap.Count == 0)
        {
            return;
        }
        if (projectSurveyMap.TryGetValue(item.ProjectId, out var value) && value.ContentLength > 2000)
        {
            ClueTagsResp clueTag = new ClueTagsResp
            {
                Name = ClueTagEnums.面试通过率高.GetDescription(),
                Value = ClueTagEnums.面试通过率高
            };
            ClueTagsResp clueTagsResp = new ClueTagsResp
            {
                Name = ClueTagEnums.调研资料多.GetDescription(),
                Value = ClueTagEnums.调研资料多
            };
            item.ThirdTags.Remove(clueTag);
            item.ThirdTags.Add(clueTagsResp);
        }
    }


    public EmptyResponse AcceptOrder(ClueAcceptOrderReq model)
    {
        var recruits = _context.Recruit.FirstOrDefault(r => r.RecruitId == model.Id);
        if (recruits is { AcceptOrder: RecruitAcceptOrderEnum.已经接单 })
        {
            throw new BadRequestException("该线索已经接单");
        }

        _profitService.AddFollower(_context, model.Id, _user.Id);

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public List<ClueTagsResp> GetClueTags()
    {
        var result = new List<ClueTagsResp>
        {
            new()
            {
                Name = ClueTagEnums.面试通过率高.GetDescription(),
                Value = ClueTagEnums.面试通过率高
            },
            new()
            {
                Name = ClueTagEnums.调研资料多.GetDescription(),
                Value = ClueTagEnums.调研资料多
            }
        };
        return result;
    }
}