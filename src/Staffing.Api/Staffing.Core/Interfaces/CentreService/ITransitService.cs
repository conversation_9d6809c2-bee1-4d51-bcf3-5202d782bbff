﻿using Config.CommonModel;
using Config.CommonModel.DataScreen;
using Config.CommonModel.TalentResume;
using Staffing.Model.CentreService.Report;
using Staffing.Model.CentreService.Transit;

namespace Staffing.Core.Interfaces.CentreService;

public interface ITransitService
{
    GetHrsResponse GetHrs(GetHrs model);
    GetPostsByIdsResponse GetPostsByIds(GetPostsByIds model);
    GetXqdpNkpResponse GetXqdpNkp(GetXqdpNkp model);
    EmptyResponse PostNotify(PostNotify model);
    EmptyResponse ResumeNotify(ResumeNotify model);
    RecommendEntResponse RecommendEnt(RecommendEnt model);
    Task<SubmitResumeResponse> SubmitResume(SubmitResume model);
    EmptyResponse UpdateRecommendEnt(UpdateRecommendEnt model);

    void SyncRecommendEntToSCRMForSH(string? OutletId);
    GetWeChatH5Response GetWeChatH5(GetWeChatH5 model);
    GetHrYscResponse GetHrYsc(GetHrYsc model);
    GetTopologyResponse GetTopology(TopologyDateRequest model);
    GetTopologyTalentResponse TopologyTalent(TopologyDateRequest model);
    EmptyResponse NykResumeNotify(List<NykTalentResume> model);
    EmptyResponse NykUserLoginNotify(List<NykTalentUpdateLoginTime> model);
    GetTopologyProjectResponse TopologyProject(TopologyDateRequest model);

    #region 数字诺亚查询经营损益明细表【为高松干--数据拓扑需求开发】
    /// <summary>
    /// 获取招聘会合同金额
    /// </summary>
    /// <param name="Sponsors">限制的招聘会主办/协办单位名称列表，限制企业名称列表最多1024条</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    decimal Noah_GetContractAmountBySponsors(List<GetContractAmountBySponsorsRequest> Sponsors);
    /// <summary>
    /// 获取合同数量
    /// </summary>
    /// <param name="EntName">限制的企业名称列表，限制企业名称列表最多1024条</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    long Noah_GetContractCountByEnt(List<string> EntName);
    /// <summary>
    /// 获取有合同的企业数量
    /// </summary>
    /// <param name="EntName">限制的企业名称列表，限制企业名称列表最多1024条</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    long Noah_GetHaveContractEntCount(List<string> EntName);
    /// <summary>
    /// 获取有新签合同的企业数量
    /// </summary>
    /// <param name="EntName">限制的企业名称列表，限制企业名称列表最多1024条</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    long Noah_GetHaveNewContractEntCount(List<string> EntName, DateTime BeginTime);
    /// <summary>
    /// 获取合同金额
    /// </summary>
    /// <param name="EntName">限制的企业名称列表，限制企业名称列表最多1024条</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    decimal Noah_GetContractAmountByEnt(List<string> EntName);

    /// <summary>
    /// 获取销售机会企业数量
    /// </summary>
    /// <param name="EntName">限制的企业名称列表，限制企业名称列表最多1024条</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    long Noah_GetOpportunityCount(List<string> EntName);
    
    /// <summary>
    /// 获取招聘需求线索
    /// </summary>
    /// <param name="EntName">限制的企业名称列表，限制企业名称列表最多1024条</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    List<DataScreenExpansion_ChartItem> Noah_GetRecruitOpportunity(List<string> EntName);
    
    /// <summary>
    /// 获取销售产品分类
    /// </summary>
    /// <param name="EntName">限制的企业名称列表，限制企业名称列表最多1024条</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    List<DataScreenExpansion_ChartItem> Noah_GetOperatingPro(List<string> EntName);
    #endregion

    #region 数字诺亚查询经营损益明细表【为高松干--数据大屏需求开发】
    /// <summary>
    /// 获取部门营收
    /// </summary>
    /// <param name="BeginTime"></param>
    /// <param name="EndTime"></param>
    /// <returns></returns>
    decimal Noah_GetContractAmountByDepart(string? DepartName, DateTime BeginTime, DateTime EndTime);

    /// <summary>
    /// 获取产品收入
    /// </summary>
    /// <param name="BeginTime"></param>
    /// <param name="EndTime"></param>
    /// <returns></returns>
    List<DataScreenExpansion_TimeDetail> Noah_GetContractAmountByPro(DateTime BeginTime, DateTime EndTime);

    /// <summary>
    /// 获取总收入收入
    /// </summary>
    /// <param name="BeginTime"></param>
    /// <param name="EndTime"></param>
    /// <returns></returns>
    decimal Noah_GetContractAmount(DateTime BeginTime, DateTime EndTime);

    /// <summary>
    /// 获取诺优考合同金额
    /// </summary>
    /// <param name="BeginTime"></param>
    /// <param name="EndTime"></param>
    /// <returns></returns>
    decimal Noah_GetNYKContractAmount(DateTime BeginTime, DateTime EndTime);
    #endregion
}