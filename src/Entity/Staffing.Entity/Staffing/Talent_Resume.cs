﻿using Config.CommonModel.TalentResume;
using Config.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 人才库融合
/// </summary>
[Table("talent_resume")]
public class Talent_Resume
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 用户来源
    /// </summary>
    public TalentResumeSource Source { get; set; } = TalentResumeSource.未知;

    /// <summary>
    /// 简历状态
    /// </summary>
    public TalentResumeStatus Status { get; set; } = TalentResumeStatus.UnRegistered;

    /// <summary>
    /// 求职者Id，建立关系后有值
    /// </summary>
    public string? SeekerId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 手机
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 头像
    /// </summary>
    public string? HeadPortrait { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 最高学历
    /// </summary>
    public TalentVirtualEducation? Education { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Mailbox { get; set; }

    /// <summary>
    /// 微信
    /// </summary>
    public string? WeChat { get; set; }

    /// <summary>
    /// QQ
    /// </summary>
    public string? QQ { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateTime? Birthday { get; set; }

    /// <summary>
    /// 开始工作日期
    /// </summary>
    public DateTime? WorkTime { get; set; }

    /// <summary>
    /// 简历完善度
    /// </summary>
    public int Perfection { get; set; } = 0;

    /// <summary>
    /// 自我评价
    /// </summary>
    public string? SelfEvaluation { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public List<Edus>? Edus { get; set; }

    /// <summary>
    /// 求职期望
    /// </summary>
    public List<NHopes>? Hopes { get; set; }

    /// <summary>
    /// 项目经历
    /// </summary>
    public List<Projects>? Projects { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    public List<Works>? Works { get; set; }

    /// <summary>
    /// 作品集
    /// </summary>
    public List<string>? Products { get; set; }

    /// <summary>
    /// 荣誉奖项
    /// </summary>
    public List<Honours>? Honours { get; set; }

    /// <summary>
    /// 证书
    /// </summary>
    public List<string>? Certificates { get; set; }

    /// <summary>
    /// 技能分析
    /// </summary>
    public TalentResumeSkillSub? SkillAnalysis { get; set; }

    /// <summary>
    /// 行业标签（来源小析职业标签字段）
    /// </summary>
    public List<string>? IndustryLabel { get; set; }

    /// <summary>
    /// 职位标签（来源小析职业标签字段）
    /// </summary>
    public List<string>? PostLabel { get; set; }

    /// <summary>
    /// 自定义标签
    /// </summary>
    public List<string>? OtherLabel { get; set; }

    /// <summary>
    /// 通用标签
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 亮点
    /// </summary>
    public TalentResumeHighlights? Highlights { get; set; }

    /// <summary>
    /// 风险
    /// </summary>
    public TalentResumeRisks? Risks { get; set; }

    /// <summary>
    /// 原始简历地址 - 附件地址
    /// </summary>
    public string? OriginalUrl { get; set; }

    /// <summary>
    /// 所在城市
    /// </summary>
    public string? City { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 最后一次上线时间
    /// </summary>
    public DateTime? LastLoginTime { get; set; }

    /// <summary>
    /// 首次注册时间
    /// </summary>
    public DateTime? RegistedTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 是否删除
    /// </summary>
    public bool Deleted { get; set; } = false;

    /// <summary>
    /// 扩展数据
    /// </summary>
    public string? ExtendedData { get; set; }

    /// <summary>
    /// 哪儿些顾问可以查看，为空则所有顾问可查看
    /// </summary>
    public List<string>? HrIds { get; set; }

    /// <summary>
    /// 哪儿些部门可以查看，为空则所有部门可查看
    /// </summary>
    public List<string>? DeptIds { get; set; }

    /// <summary>
    /// 隐藏简历失效时间
    /// </summary>
    public DateTime? HideInvalidTime { get; set; }

    public List<Talent_Resume_Connect>? Talent_Resume_Connects { get; set; }

    public User_Seeker? User_Seeker { get; set; }
}

