using Config.Enums;

namespace Staffing.Model.CentreService.Transit;

public class SubmitResume
{
    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 教育
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 毕业日期
    /// </summary>
    public DateOnly? GraduationDate { get; set; }

    /// <summary>
    /// 投递客户端
    /// </summary>
    public ClientType? UserClient { get; set; }

    /// <summary>
    /// 渠道Id
    /// </summary>
    public string? ChannelId { get; set; }

    /// <summary>
    /// 渠道来源
    /// </summary>
    public long? ChannelSource { get; set; }
}

public class SubmitResumeResponse
{
    /// <summary>
    /// 投递Id
    /// </summary>
    public string? DeliveryId { get; set; }
}
