using Config;
using Infrastructure.Common;
using Infrastructure.Extend;
using Infrastructure.YouLuZY;
using Staffing.Core.Interfaces.Certificate;
using Staffing.Entity;
using Staffing.Model.Certificate;
using Microsoft.EntityFrameworkCore;
using Staffing.Entity.Staffing;

namespace Staffing.Api.Apis.Seeker;

[Service(ServiceLifetime.Transient)]
public class Certificate : ISeekerRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("certificate").WithTags("证书大厅");

        group.MapPost("/register",
                async (CertificateRegistrationFormReq request, ICertificateService rep) => await rep.Register(request))
            .WithOpenApi(operation => new(operation) { Summary = "证书报名" });

        group.MapGet("/certificateDetail", (string id, string hrId, ICertificateService rep) => rep.GetCertificateDetail(id, hrId))
            .WithOpenApi(operation => new(operation) { Summary = "获取证书详情" });

        group.MapPost("/hall", (CertificateHallReq request, ICertificateService rep) => rep.GetCertificateHall(request))
            .WithOpenApi(operation => new(operation) { Summary = "证书分页列表" });

        group.MapGet("/getHrCertificateConfig", (string hrId, StaffingContext context, LogManager logger) =>
        {
            // 产品要求全部可见
            // var certificateUserConfig = context.CertificateUserConfig.FirstOrDefault(o => o.UserId == hrId);
            // if (certificateUserConfig == null)
            // {
            //     return new CertificateUserConfigResp();
            // }

            return new CertificateUserConfigResp()
            {
                IsEnabled = true,
            };
        }).WithOpenApi(operation => new(operation) { Summary = "获取当前hr的证书配置" });

        group.MapGet("/getCertificateRegistrationformList", (RequestContext user, StaffingContext context, LogManager logger) =>
        {
            var certificateRegistrationformList = context.CertificateRegistrationForm
                .Where(o => o.Creator == user.Id && o.Status != RegistrationformStatusEnum.无效报名)
                .OrderByDescending(o => o.RegistrationTime)
                .Select(o => new
                {
                    Id = o.Id,
                    CertificateId = o.CertificateTable.Id,
                    CertificateName = o.CertificateTable.CertificateName,
                    ProductImage = o.CertificateTable.ProductImage,
                    SpecName = o.CertificateSpec != null ? o.CertificateSpec.SpecName : "",
                    Price = o.Price,
                    PayStatus = o.PayStatus,
                    StudyList = o.CertificateSpec != null ? o.CertificateSpec.StudyList : null,

                })
                .IgnoreQueryFilters()
                .ToList();
            return certificateRegistrationformList;
        }).WithOpenApi(operation => new(operation) { Summary = "根据当前登录人获取报名的证书列表" });

        group.MapPost("/CertificateStudy", async (CertificateStudyReq request, RequestContext user, ICertificateService rep, StaffingContext context, LogManager logger, IHostEnvironment _hostingEnvironment) =>
        {
            var regModel = context.CertificateRegistrationForm
                //.Include(o => o.CertificateTable)
                .Where(o => o.Id == request.CerRegID)
                .Select(x => new
                {
                    StudyList = x.CertificateSpec != null ? x.CertificateSpec.StudyList : null,
                    x.PayStatus,
                    x.ContactNumber,
                    x.Creator,
                    x.Name,
                    x.CertificateId,
                    x.CertificateSpecsId,
                    SpecName= x.CertificateSpec != null ? x.CertificateSpec.SpecName : string.Empty,
                    CertName = x.CertificateTable != null ? x.CertificateTable.CertificateName : string.Empty,
                })
                .IgnoreQueryFilters()
                .FirstOrDefault();
            Staffing.Entity.Staffing.CertificateSpec.StudyModel? study = null;
            if (regModel == null)
                throw new Infrastructure.Exceptions.BadRequestException("尚未报名证书，请报名后重试");
            else if (regModel.StudyList == null || regModel.StudyList.Count == 0)
                throw new Infrastructure.Exceptions.BadRequestException("证书尚未配置学习地址，请联系管理员");
            else
            {
                study = regModel.StudyList.FirstOrDefault(x => x.Url == request.Url);
                if (study == null)
                    throw new Infrastructure.Exceptions.BadRequestException("学习链接已失效，请刷新页面后重试");
                //else if (regModel.ContactNumber != user.Account && regModel.Creator != user.Id)
                //    throw new Infrastructure.Exceptions.BadRequestException("报名信息与当前用户不一致");
                else if (regModel.PayStatus != Entity.Staffing.RegistrationformPayStatusEnum.已支付)
                    throw new Infrastructure.Exceptions.BadRequestException("经查报名尚未支付，请支付后重试，若已支付请联系负责人更新支付状态");
            }
            var toUrl = await YouLuZYHelper.GetToUrl(request.Url, user.Id, regModel.Name, _hostingEnvironment.IsProduction());
            if (toUrl.Result)
            {
                rep.SaveStudyRecord(study!.Url!, study!.Title ?? string.Empty,
                    regModel.CertificateId, regModel.CertName,
                    regModel.CertificateSpecsId, regModel.SpecName);
                return toUrl.Url;
            }
            else
            {
                logger.Error("优路云证书学习", $"报名记录ID={request.CerRegID}，请求获取学习链接失败：" + toUrl.ErrMsg);
                throw new Infrastructure.Exceptions.BadRequestException("获取学习地址错误，请联系管理员!");
            }
        }).WithOpenApi(operation => new(operation) { Summary = "去学习报名记录中的课程" });

        group.MapPost("/CertificatePromotion", async (CertificatePromotionReq request, ICertificateService rep, Microsoft.Extensions.Options.IOptions<ConfigManager> _config) =>
        {
            return await rep.GetPromotionImgUrl(request, _config.Value.Aliyun!.Oss!.Dir!);
        }).WithOpenApi(operation => new(operation) { Summary = "获取分享海报的图片URL" });
    }
}