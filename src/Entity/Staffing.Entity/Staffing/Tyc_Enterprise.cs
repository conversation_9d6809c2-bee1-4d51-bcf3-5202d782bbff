﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.CommonModel.Tianyancha;

namespace Staffing.Entity.Staffing;

/// <summary>
/// admin
/// </summary>
[Table("tyc_enterprise")]
public class Tyc_Enterprise
{
    [Key]
    public string Id { get; set; } = default!;

    /// <summary>
    /// 曾用名
    /// </summary>
    public string? HistoryNames { get; set; }

    /// <summary>
    /// 企业状态
    /// </summary>
    public string? RegStatus { get; set; }

    /// <summary>
    /// 注销日期
    /// </summary>
    public string? CancelDate { get; set; }

    /// <summary>
    /// 注册资本
    /// </summary>
    public string? RegCapital { get; set; }

    /// <summary>
    /// 市
    /// </summary>
    public string? City { get; set; }

    /// <summary>
    /// 人员规模
    /// </summary>
    public string? StaffNumRange { get; set; }

    /// <summary>
    /// 行业
    /// </summary>
    public string? Industry { get; set; }

    /// <summary>
    /// 股票号
    /// </summary>
    public string? BondNum { get; set; }

    /// <summary>
    /// 法人类型，1 人 2 公司
    /// </summary>
    public int? Type { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public string? UpdateTimes { get; set; }

    /// <summary>
    /// 吊销日期
    /// </summary>
    public string? RevokeDate { get; set; }

    /// <summary>
    /// 法人
    /// </summary>
    public string? LegalPersonName { get; set; }

    /// <summary>
    /// 吊销原因
    /// </summary>
    public string? RevokeReason { get; set; }

    /// <summary>
    /// 注册号
    /// </summary>
    public string? RegNumber { get; set; }

    /// <summary>
    /// 英文名
    /// </summary>
    public string? Property3 { get; set; }

    /// <summary>
    /// 统一社会信用代码
    /// </summary>
    public string? CreditCode { get; set; }

    /// <summary>
    /// 股票曾用名
    /// </summary>
    public string? UsedBondName { get; set; }

    /// <summary>
    /// 经营开始时间
    /// </summary>
    public string? FromTime { get; set; }

    /// <summary>
    /// 经营截止时间
    /// </summary>
    public string? ToTime { get; set; }

    /// <summary>
    /// 核准时间
    /// </summary>
    public string? ApprovedTime { get; set; }

    /// <summary>
    /// 参保人数
    /// </summary>
    public int? SocialStaffNum { get; set; }

    /// <summary>
    /// 简称
    /// </summary>
    public string? Alias { get; set; }

    /// <summary>
    /// 企业类型
    /// </summary>
    public string? CompanyOrgType { get; set; }

    /// <summary>
    /// 组织机构代码
    /// </summary>
    public string? OrgNumber { get; set; }

    /// <summary>
    /// 注销原因
    /// </summary>
    public string? CancelReason { get; set; }

    /// <summary>
    /// 实收注册资金
    /// </summary>
    public string? ActualCapital { get; set; }

    /// <summary>
    /// 成立日期
    /// </summary>
    public string? EstiblishTime { get; set; }

    /// <summary>
    /// 登记机关
    /// </summary>
    public string? RegInstitute { get; set; }

    /// <summary>
    /// 纳税人识别号
    /// </summary>
    public string? TaxNumber { get; set; }

    /// <summary>
    /// 经营范围
    /// </summary>
    public string? BusinessScope { get; set; }

    /// <summary>
    /// 注册地址
    /// </summary>
    public string? RegLocation { get; set; }

    /// <summary>
    /// 企业标签
    /// </summary>
    public string? Tags { get; set; }

    /// <summary>
    /// 区
    /// </summary>
    public string? District { get; set; }

    /// <summary>
    /// 企业名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 是否是小微企业 0不是 1是
    /// </summary>
    public bool? IsMicroEnt { get; set; }

    /// <summary>
    /// 省份简称
    /// </summary>
    public string? Base { get; set; }

    /// <summary>
    /// 接口编码
    /// </summary>
    public string? InterfaceId { get; set; }

    public DateTime? CreatedTime { get; set; } = DateTime.Now;

    public DateTime? UpdatedTime { get; set; } = DateTime.Now;

    public IndustryAll? IndustryAll { get; set; }
}
