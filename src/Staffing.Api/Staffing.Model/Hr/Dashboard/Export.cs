namespace Staffing.Model.Hr.Dashboard;

public class ExportZhaoPinJiaoFuKpi
{
    public int Month { get; set; }

    public string Today { get; set; } = null!;

    /// <summary>
    /// 顾问数
    /// </summary>
    public int HrNum { get; set; }

    /// <summary>
    /// 活跃顾问数
    /// </summary>
    public int HrActiveNum { get; set; }

    /// <summary>
    /// hr日活
    /// </summary>
    public int DailyActiveHr { get; set; }

    /// <summary>
    /// 上线岗位总量
    /// </summary>
    public int PostNum { get; set; }

    /// <summary>
    /// 报名人数
    /// </summary>
    public int RegistrationNum { get; set; }

    public List<ExportZhaoPinJiaoFuKpiDetail> Rows { get; set; } = new List<ExportZhaoPinJiaoFuKpiDetail>();
}

public class ExportZhaoPinJiaoFuKpiDetail
{
    public string HrId { get; set; } = null!;

    /// <summary>
    /// 姓名
    /// </summary>
    public string XingMing { get; set; } = null!;

    /// <summary>
    /// 收入目标
    /// </summary>
    public decimal ShouRuMuBiao { get; set; }

    /// <summary>
    /// 项目总数
    /// </summary>
    public int XiangMuZongShu { get; set; }

    /// <summary>
    /// 协同的项目数
    /// </summary>
    public int XieTongXiangMu { get; set; }

    /// <summary>
    /// 面试人次
    /// </summary>
    public int MianShiCiShu { get; set; }

    /// <summary>
    /// 人才数
    /// </summary>
    public int RenCaiShu { get; set; }
}

public class ExportHrXmSj : GetStaffDataResponse
{
}