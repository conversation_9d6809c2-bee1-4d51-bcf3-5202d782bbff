using System.Text.Json.Serialization;
using Config.Enums;

namespace Config.CommonModel.Business;

public class UpdatePost
{
    public string? PostId { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }
    /// <summary>
    /// 用户Id
    /// </summary>
    public string UserId { get; set; } = default!;
    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 项目调研表
    /// </summary>
    public ProjectSurveyContent? SurveyContent { get; set; }

    /// <summary>
    ///  当前角色是否是项目经理
    /// </summary>
    public bool? IsReceiver { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 职位类别
    /// </summary>
    public int Category { get; set; }

    /// <summary>
    /// 领带类型
    /// </summary>
    public TieType TieType { get; set; } = TieType.蓝领;

    /// <summary>
    /// 工作性质
    /// </summary>
    public PostWorkNature WorkNature { get; set; } = PostWorkNature.全职;

    /// <summary>
    /// 薪资类型
    /// </summary>
    public PostSalaryType SalaryType { get; set; } = PostSalaryType.面议;

    /// <summary>
    /// 招聘人数
    /// </summary>
    public int RecruitNumber { get; set; }

    /// <summary>
    /// 交付数量
    /// </summary>
    public int DeliveryNumber { get; set; }

    /// <summary>
    /// 剩余库存
    /// </summary>
    public int LeftStock { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public PostStatus Status { get; set; }

    // /// <summary>
    // /// 是否协同
    // /// </summary>
    // public bool Share { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string RegionId { get; set; } = default!;

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public double Lat { get; set; }

    /// <summary>
    /// 经度
    /// </summary>
    public double Lng { get; set; }

    /// <summary>
    /// 招聘企业Id
    /// </summary>
    public string? AgentEntId { get; set; }

    public GetAgentEntDetail? AgentEnt { get; set; }

    /// <summary>
    /// 销售是否需要分佣
    /// </summary>
    public bool? IsSalesCommission { get; set; }

    /// <summary>
    /// 签约方式
    /// </summary>
    public ContractType? ContractType { get; set; }

    /// <summary>
    /// 面试信息
    /// </summary>
    public PostInterviewInfo? Interview { get; set; }

    /// <summary>
    /// 部门
    /// </summary>
    public string Department { get; set; } = default!;

    /// <summary>
    /// 教育
    /// </summary>
    public EducationType Education { get; set; } = EducationType.不限;

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 最小年龄
    /// </summary>
    public int MinAge { get; set; }

    /// <summary>
    /// 最大年龄
    /// </summary>
    public int MaxAge { get; set; }

    /// <summary>
    /// 薪资要求(最低)
    /// </summary>
    public int MinSalary { get; set; }

    /// <summary>
    /// 薪资要求(最高)
    /// </summary>
    public int MaxSalary { get; set; }

    /// <summary>
    /// 几薪
    /// </summary>
    public int Salary { get; set; } = 12;

    #region 特殊字段

    //兼职

    /// <summary>
    /// 结算方式（日结、月结）
    /// </summary>
    public PostSettlementType? SettlementType { get; set; }

    /// <summary>
    /// 工作日(1-7)
    /// </summary>
    public List<int>? WorkingDays { get; set; } = new List<int>();

    /// <summary>
    /// 工作时间
    /// </summary>
    public List<PostWorkingHours>? WorkingHours { get; set; } = new List<PostWorkingHours>();

    /// <summary>
    /// 合作模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }

    // /// <summary>
    // /// 过保付费时间
    // /// </summary>
    // public int? PaymentDays { get; set; }

    /// <summary>
    /// 奖励时效类型（单次结算, 长期结算）
    /// </summary>
    public PostRewardType? RewardType { get; set; }

    /// <summary>
    /// 结算周期(按小时、天、月)
    /// </summary>
    public PostPaymentCycle? PaymentCycle { get; set; }

    /// <summary>
    /// 返费周期, null或0代表无限期
    /// </summary>
    public int? PaymentDuration { get; set; }
    /// <summary>
    /// 返费周期状态（0：持续返费，1：自定义返费）
    /// </summary>
    public int PaymentDurationStatus { get; set; }
    /// <summary>
    /// 付费阶段
    /// </summary>
    public List<PostProfitStage>? ProfitStage { get; set; }

    /// <summary>
    /// 简历免审
    /// </summary>
    public bool? IsResumeExempt { get; set; }

    //实习生

    /// <summary>
    /// 最少实习月数
    /// </summary>
    public int? MinMonths { get; set; }

    /// <summary>
    /// 最少周到岗天数
    /// </summary>
    public int? DaysPerWeek { get; set; }

    //应届

    /// <summary>
    /// 毕业年份
    /// </summary>
    public int? GraduationYear { get; set; }

    #endregion

    // /// <summary>
    // /// 金额/人
    // /// </summary>
    // public decimal? Money { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public List<string>? Tags { get; set; } = new List<string>();

    /// <summary>
    /// 职位亮点
    /// </summary>
    public List<string>? Highlights { get; set; } = new List<string>();

    /// <summary>
    /// 福利（更新只提交Id即可）
    /// </summary>
    public List<WelfareModel>? Welfare { get; set; } = new List<WelfareModel>();

    /// <summary>
    /// 福利自定义
    /// </summary>
    public List<string>? WelfareCustom { get; set; } = new List<string>();
    /// <summary>
    /// 面试场次状态
    /// </summary>
    public InterviewStatus? InterviewStatus { get; set; }
    /// <summary>
    /// 是否启用代聊功能
    /// </summary>
    public HrAgentStatus? HrAgentStatus { get; set; }
    public FanfeiStatus FanfeiStatus { get; set; }//0返费未变化 1返费有变化
    public PostSouce Source { get; set; }//岗位来源  0:诺快聘, 1:职多多， 2、58魔方
}

public class PostProfitStage
{
    /// <summary>
    /// 过保天数
    /// </summary>
    public int? GuaranteeDays { get; set; }

    /// <summary>
    /// 分润金额
    /// </summary>
    public decimal Amount { get; set; }
}

public class ProjectSurveyContent
{
    /// <summary>
    /// 重点必读
    /// </summary>
    public string? KeyRead { get; set; }

    /// <summary>
    /// 薪资待遇
    /// </summary>
    public List<ProjectSurveyContentValue>? SalaryBenefits { get; set; } = new List<ProjectSurveyContentValue>();

    /// <summary>
    /// 硬性条件
    /// </summary>
    public List<ProjectSurveyContentValue>? HardConditions { get; set; } = new List<ProjectSurveyContentValue>();

    /// <summary>
    /// 工作时间
    /// </summary>
    public List<ProjectSurveyContentValue>? WorkingHours { get; set; } = new List<ProjectSurveyContentValue>();

    /// <summary>
    /// 工作地点
    /// </summary>
    public List<ProjectSurveyContentValue>? WorkLocation { get; set; } = new List<ProjectSurveyContentValue>();

    /// <summary>
    /// 面试流程
    /// </summary>
    public List<ProjectSurveyContentValue>? InterviewProcess { get; set; } = new List<ProjectSurveyContentValue>();

    /// <summary>
    /// 入职流程
    /// </summary>
    public List<ProjectSurveyContentValue>? OnboardingProcess { get; set; } = new List<ProjectSurveyContentValue>();

    /// <summary>
    /// 工作情况
    /// </summary>
    public List<ProjectSurveyContentValue>? WorkCondition { get; set; } = new List<ProjectSurveyContentValue>();

    /// <summary>
    /// 其他问题
    /// </summary>
    public List<ProjectSurveyContentValue>? OtherQuestions { get; set; } = new List<ProjectSurveyContentValue>();

    /// <summary>
    /// 图片
    /// </summary>
    public List<ProjectSurveyContentMedia>? Images { get; set; } = new List<ProjectSurveyContentMedia>();

    /// <summary>
    /// 视频
    /// </summary>
    public List<ProjectSurveyContentMedia>? Videos { get; set; } = new List<ProjectSurveyContentMedia>();
}

public class ProjectSurveyContentValue
{
    /// <summary>
    /// 标题
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string? Content { get; set; }
}

public class ProjectSurveyContentMedia
{
    /// <summary>
    /// 地址
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }
}

public class PostInterviewInfo
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 面试官Id
    /// </summary>
    public string? InterviewerId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ActiveStatus? Status { get; set; }

    /// <summary>
    /// 面试方式
    /// </summary>
    public RecruitInterviewForms? InterviewMode { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string? AddressDetail { get; set; }

    /// <summary>
    /// 地区ID
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public double Lat { get; set; }

    /// <summary>
    /// 经度
    /// </summary>
    public double Lng { get; set; }

    /// <summary>
    /// 联络人姓名
    /// </summary>
    public string? ContactName { get; set; }

    /// <summary>
    /// 联络人电话
    /// </summary>
    public string? ContactPhone { get; set; }

    /// <summary>
    /// 可提前几天预约
    /// </summary>
    public int? AdvanceDays { get; set; }

    /// <summary>
    /// 面试时间
    /// </summary>
    public List<PostInterviewTimeModel>? InterviewTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}

public class PostInterviewTimeModel
{
    /// <summary>
    /// 时间
    /// </summary>
    public DateTime? Time { get; set; }

    /// <summary>
    /// 子时间，TimeOnly数组，Time的下级
    /// </summary>
    public List<TimeOnly>? SubTime { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public PostInterviewTimeStatus? Status { get; set; }
}

public class PostWorkingHours
{
    /// <summary>
    /// 工作时间段起始
    /// </summary>
    public string Begin { get; set; } = string.Empty;

    /// <summary>
    /// 工作时间段截止
    /// </summary>
    public string End { get; set; } = string.Empty;
}

public class GetPostDetail : UpdatePost
{
    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 协同顾问Id
    /// </summary>
    public string? TeamHrId { get; set; }

    /// <summary>
    /// 职位佣金总额
    /// </summary>
    public decimal CommissionTotal { get; set; }

    /// <summary>
    /// 奖励时效类型
    /// </summary>
    public string? RewardTypeName { get; set; }

    /// <summary>
    /// 结算周期
    /// </summary>
    public string? PaymentCycleName { get; set; }

    /// <summary>
    /// 协同项目Id
    /// </summary>
    public string? TeamProjectId { get; set; }

    public int? TeamPostAutoId { get; set; }

    /// <summary>
    /// 协同的职位编码
    /// </summary>
    public string? TeamPostNo { get; set; }

    /// <summary>
    /// 过保天数
    /// </summary>
    public int? PaymentDays { get; set; }

    /// <summary>
    /// 金额/人
    /// </summary>
    public decimal? Money { get; set; }

    /// <summary>
    /// 招聘企业名称
    /// </summary>
    public string? AgentEntName { get; set; }

    /// <summary>
    /// 结算方式（日结、月结）
    /// </summary>
    public string? SettlementTypeName { get; set; }

    /// <summary>
    /// 职位编号
    /// </summary>
    public string? PostNo { get; set; }

    public int? PostAutoId { get; set; }

    /// <summary>
    /// 职位类别名称
    /// </summary>
    public string? CategoryName { get; set; }

    /// <summary>
    /// 状态名称
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 教育名称
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 打款方式
    /// </summary>
    public ProjectPaymentType? PaymentType { get; set; }

    /// <summary>
    /// 项目类型
    /// </summary>
    public ProjectType? ProjectType { get; set; }

    /// <summary>
    /// 项目类型名称
    /// </summary>
    public string? ProjectTypeName { get; set; }

    /// <summary>
    /// 打款方式名称
    /// </summary>
    public string? PaymentTypeName { get; set; }

    /// <summary>
    /// 合作模式名称
    /// </summary>
    public string? PaymentNodeName { get; set; }

    /// <summary>
    /// 工作性质名称
    /// </summary>
    public string? WorkNatureName { get; set; }

    /// <summary>
    /// 薪资类型名称
    /// </summary>
    public string? SalaryTypeName { get; set; }

    /// <summary>
    /// 薪资文本显示
    /// </summary>
    public string? SalaryName { get; set; }

    /// <summary>
    /// 小程序二维码
    /// </summary>
    public string? AppletQrCode { get; set; }

    /// <summary>
    /// 地区
    /// </summary>
    public CityModel? Region { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    /// 报名人数
    /// </summary>
    public int? RegistrationNum { get; set; }

    /// <summary>
    /// 交付人数
    /// </summary>
    public int? DeliveriesNum { get; set; }

    /// <summary>
    /// 简历初筛人数
    /// </summary>
    public int? ScreeningNum { get; set; }

    /// <summary>
    /// 面试人数
    /// </summary>
    public int? InterviewNum { get; set; }

    /// <summary>
    /// 入职人数
    /// </summary>
    public int? InductionNum { get; set; }

    /// <summary>
    /// 协同报名人数
    /// </summary>
    [JsonIgnore]
    public int? TeamRegistrationNum { get; set; }

    /// <summary>
    /// 协同交付人数
    /// </summary>
    [JsonIgnore]
    public int? TeamDeliveriesNum { get; set; }

    /// <summary>
    /// 协同简历初筛人数
    /// </summary>
    [JsonIgnore]
    public int? TeamScreeningNum { get; set; }

    /// <summary>
    /// 协同面试人数
    /// </summary>
    [JsonIgnore]
    public int? TeamInterviewNum { get; set; }

    /// <summary>
    /// 协同入职人数
    /// </summary>
    [JsonIgnore]
    public int? TeamInductionNum { get; set; }

    /// <summary>
    /// 静态地图
    /// </summary>
    public string? LocationMap { get; set; }

    /// <summary>
    /// Hr信息
    /// </summary>
    public HrModel? Hr { get; set; }

    /// <summary>
    /// 顾问是否置顶
    /// </summary>
    public bool Top { get; set; }

    /// <summary>
    /// 渠道商是否置顶
    /// </summary>
    public bool ChannelTop { get; set; }
    /// <summary>
    /// 上架状态
    /// </summary>
    public bool Show { get; set; }
}

public class SeekerGetPosts : QueryRequest
{
    /// <summary>
    /// 推荐职位
    /// </summary>
    [JsonIgnore]
    public string? RecommendTeamPostId { get; set; }

    /// <summary>
    /// 按照渠道自己的排序展示
    /// </summary>
    public string? ChannelId { get; set; }

    // /// <summary>
    // /// 客户端Id
    // /// </summary>
    // [JsonIgnore]
    // public string? ClientId { get; set; }

    /// <summary>
    /// 是否优选
    /// </summary>
    public bool IsExcellent { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? AdviserId { get; set; }

    /// <summary>
    /// 检索
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 项目行业
    /// </summary>
    public ProjectIndustry? ProjectIndustry { get; set; }

    /// <summary>
    /// 职位类别
    /// </summary>
    public int? Category { get; set; }

    /// <summary>
    /// 工作性质
    /// </summary>
    public PostWorkNature? WorkNature { get; set; }

    /// <summary>
    /// 代招企业Id
    /// </summary>
    public string? AgentEntId { get; set; }

    /// <summary>
    /// 地区Id（最多10个）
    /// </summary>
    public List<string>? Citys { get; set; }

    /// <summary>
    /// 排序（1=距离近，2=最新，其他=默认）
    /// </summary>
    public int? Sort { get; set; }

    /// <summary>
    /// 当前用户纬度，若按距离排序必填
    /// </summary>
    public double Lat { get; set; }

    /// <summary>
    /// 当前用户经度，若按距离排序必填
    /// </summary>
    public double Lng { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 结算方式（日结、月结）
    /// </summary>
    public List<PostSettlementType?>? SettlementType { get; set; }

    /// <summary>
    /// 工作周期类型
    /// </summary>
    public List<ProjectWorkCycleType>? WorkCycleType { get; set; }

    /// <summary>
    /// 企业性质
    /// </summary>
    public EnterpriseNature? Nature { get; set; }

    /// <summary>
    /// 企业规模
    /// </summary>
    public EnterpriseScale? Scale { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 职位Id精准查找
    /// </summary>
    public List<string>? TeamPostIds { get; set; }
}

public class SeekerGetPostsResponse : QueryResponse
{
    public List<SeekerGetPostInfo> Rows { get; set; } = new List<SeekerGetPostInfo>();
}

public class SeekerGetPost
{
    /// <summary>
    /// 新区大厅临时用
    /// </summary>
    public bool? OnlyPostId { get; set; }

    /// <summary>
    /// 协同的职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? AdviserId { get; set; }

    /// <summary>
    /// 渠道Id（显示渠道置顶信息）
    /// </summary>
    public string? ChannelId { get; set; }

    // /// <summary>
    // /// 客户端Id
    // /// </summary>
    // [JsonIgnore]
    // public string? ClientId { get; set; }

    // /// <summary>
    // /// 求职者Id，用来判断是否收藏
    // /// </summary>
    // [JsonIgnore]
    // public string? SeekerId { get; set; }
}

public class PostRecentInterviews
{
    /// <summary>
    /// 最近面试日程
    /// </summary>
    public List<PostInterviewTimeModel> Recent { get; set; } = new List<PostInterviewTimeModel>();

    /// <summary>
    /// 最近7天面试人数
    /// </summary>
    public int RecentInterviewNum { get; set; }

    /// <summary>
    /// 剩余面试场次
    /// </summary>
    public int InterviewNum { get; set; }

    /// <summary>
    /// 近5场交付率
    /// </summary>
    public decimal RecentDeliveryRate { get; set; }

    /// <summary>
    /// 历史计费率
    /// </summary>
    public decimal HistoryBillingRate { get; set; }
}

public class SeekerGetPostInfo : GetPostDetail
{
    /// <summary>
    /// 72小时入职
    /// </summary>
    public bool EntryIn72hour { get; set; }

    /// <summary>
    /// 24小时面试
    /// </summary>
    public bool InterviewIn24Hour { get; set; }

    /// <summary>
    /// 是否已收藏
    /// </summary>
    public bool Collect { get; set; }

    ///// <summary>
    ///// 前端是否展示
    ///// </summary>
    //public bool Show { get; set; }

    /// <summary>
    /// 协同者状态
    /// </summary>
    [JsonIgnore]
    public PostStatus? TeamStatus { get; set; }

    /// <summary>
    /// 项目状态
    /// </summary>
    [JsonIgnore]
    public ProjectStatus? ProjectStatus { get; set; }

    /// <summary>
    /// 投递Id
    /// </summary>
    public string? DeliveryId { get; set; }

    /// <summary>
    /// 报名的人员头像
    /// </summary>
    public List<string> PostAvatars { get; set; } = new List<string>();

    /// <summary>
    /// 新标签(1=精选推荐，2=靠谱好岗)
    /// </summary>
    public int NewTagType { get; set; }
}

public class TeamPostHrAgentChat
{
    public string? ProjectId { get; set; }
    public string? PostId { get; set; }
    public string? TeamPostId { get; set; }
    public HrAgentStatus? HrAgentStatus { get; set; }
}