﻿using Nest;

namespace Infrastructure.Extend;

public static class EsExtend
{
    public static SearchDescriptor<T> Sort<T>(this SearchDescriptor<T> descriptor, EsSort sort) where T : class
    {
        if (sort.Dir == "desc")
            descriptor = descriptor.Sort(f => f.Field(sort.Field, SortOrder.Descending));
        if (sort.Dir == "asc")
            descriptor = descriptor.Sort(f => f.<PERSON>(sort.Field, SortOrder.Ascending));

        return descriptor;
    }
}

public class EsSort
{
    /// <summary>
    /// 默认降序
    /// </summary>
    public string Dir { get; set; } = "desc";

    /// <summary>
    /// 排序字段
    /// </summary>
    public Field? Field { get; set; }
}
