
-- ----------------------------
-- Table structure for log_warning
-- ----------------------------
DROP TABLE IF EXISTS `log_warning`;
CREATE TABLE `log_warning` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `Account` varchar(100) DEFAULT NULL,
  `Message` varchar(5000) NOT NULL,
  `CreatedTime` datetime(6) NOT NULL,
  PRIMARY KEY (`Id`),
  KEY `IX_log_warning_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- Table structure for log_error
-- ----------------------------
DROP TABLE IF EXISTS `log_error`;
CREATE TABLE `log_error` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `RequestId` char(50) NOT NULL,
  `Url` varchar(500) DEFAULT NULL,
  `Method` varchar(10) DEFAULT NULL,
  `Arguments` varchar(5000) DEFAULT NULL,
  `ErrorMessage` varchar(5000) DEFAULT NULL,
  `SendEMail` bit(1) NOT NULL,
  `UserId` varchar(500) DEFAULT NULL,
  `SystemId` int NOT NULL DEFAULT 0,
  `CreatedTime` datetime(6) NOT NULL,
  PRIMARY KEY (`Id`),
  KEY `IX_log_error_CreatedTime` (`CreatedTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



-- ----------------------------
-- Table structure for log_request
-- ----------------------------
DROP TABLE IF EXISTS `log_request`;
CREATE TABLE `log_request` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `RequestId` char(50) NOT NULL,
  `Url` varchar(500) DEFAULT NULL,
  `Method` varchar(10) DEFAULT NULL,
  `Spend` int(11) NOT NULL,
  `HttpStatus` varchar(10) NOT NULL,
  `Arguments` varchar(5000) DEFAULT NULL,
  `IpAddress` varchar(50) DEFAULT NULL,
  `UserAgent` varchar(1000) DEFAULT NULL,
  `UserId` varchar(500) DEFAULT NULL,
  `SystemId` int NOT NULL DEFAULT 0,
  `CreatedTime` datetime(6) NOT NULL,
  PRIMARY KEY (`Id`),
  KEY `IX_log_request_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- Table structure for log_hazardinfo
-- ----------------------------
DROP TABLE IF EXISTS `log_hazardinfo`;
CREATE TABLE `log_hazardinfo` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `Mobile` varchar(255) NOT NULL,
  `SystemId` int NOT NULL DEFAULT 0,
  `Message` varchar(1000) NOT NULL,
  `Url` varchar(500) DEFAULT NULL,
  `ErrorCode` varchar(10) DEFAULT NULL,
  `Arguments` varchar(2000) DEFAULT NULL,
  `UserAgent` varchar(1000) DEFAULT NULL,
  `HazardLevel` int(11) NOT NULL,
  `CreatedTime` datetime(6) NOT NULL,
  PRIMARY KEY (`Id`),
  KEY `IX_log_hazardinfo_Mobile` (`Mobile`),
  KEY `IX_log_hazardinfo_Url` (`Url`),
  KEY `IX_log_hazardinfo_SystemId` (`SystemId`),
  KEY `IX_log_hazardinfo_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;