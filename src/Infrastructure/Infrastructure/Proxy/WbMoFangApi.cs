﻿using Config;
using Config.CommonModel;
using Config.Enums;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure.Proxy
{
    [Service(ServiceLifetime.Transient)]
    public class WbMoFangApi
    {
        private readonly ConfigManager _config;
        private readonly CacheHelper _cacheHelper;
        private readonly LogManager _log;
        private readonly CommonDicService _commonDicService;
        private readonly CommonPostService _commonPostService;
        private readonly CommonProjectService _commonProjectService;
        private IDbContextFactory<StaffingContext> _staffingContextFactory;

        public WbMoFangApi(IOptionsSnapshot<ConfigManager> config, CacheHelper cacheHelper, LogManager log, CommonDicService commonDicService, CommonPostService commonPostService, CommonProjectService commonProjectService,IDbContextFactory<StaffingContext> staffingContextFactory)
        {
            _config = config.Value;
            _cacheHelper = cacheHelper;
            _log = log;
            _commonDicService = commonDicService;
            _commonPostService = commonPostService;
            _commonProjectService = commonProjectService;
            _staffingContextFactory = staffingContextFactory;
        }
        private string cookie = @"www58com=""UserID=80295779895568&UserName=gv2r4nkp0""; 58cooper=""userid=80295779895568&username=gv2r4nkp0""; token=c9cc33bba76a20500b67acd36839114f; crmvip=; dk_cookie=; new_session=0; PPU=""UID=80295779895568&UN=gv2r4nkp0&TT=2224795b404dd399795b0efe8dc115eb&PBODY=hM_NyeKMml_2BSh1-Ag8I0x8tiH-wgprjoJg1x5Yu2oMWRHpdmifwnZtcQ-gx_oubyAmEKZU4xOUarEkyCRcrx0M2E1KTy83pzGbO1JEaZOWedjIk9STiV98ZVLADZ_kmVdT8fPgvnaIRJzXEsimnCllQ314SbEDgXqDYZLfDKo&VER=1&CUID=vth2-KdZ3hpSFuP6OlOi6A""; xxzlbbid=pfmbM3wxMDMwN3wxLjEwLjF8MTc1MzA1ODg4MDE2MjAwMjg2OHxmaGJXNE5sMndiOU1peG94cmVRZjBRVkJqa2lUMEo5dmhyd3VRRzQ5NThvPXxmNWMzODk3NDE4YmFjNjliOWE2MjRiYWNmNzc0NjQ1ZF8xNzUzMDU4ODc3ODcwX2FhNWQ4ZjkyZjhlNjQ4ZmJiODI3NzZjZDg5YWI4YzdhXzQ2MTM3NTc5MHw0YzAxNGRlZDUwYTQyODg2YmQ3MDM2YzJhZTRiNThiMl8xNzUzMDU4ODc4MTEwXzI1NA==";
        //public static List<WelfareModel> welfareModels = new List<WelfareModel>();
        //public static List<Dic_Post> dic_Posts = new List<Dic_Post>();
        public async Task GetWbMoFangJobList(int page)
        {
            try
            {
                //int page = 1;
                int limit = 15;
                var ran = new Random(Guid.NewGuid().GetHashCode());
                using var _context = _staffingContextFactory.CreateDbContext();
                var wbjoblist = await GetWbMofangJobList(page, limit);
                if (wbjoblist != null && wbjoblist.Count > 0)
                {
                    //welfareModels = _commonDicService.GetWelfare();
                    //dic_Posts = _context.Dic_Post.ToList();
                    MyRedis.Client.SAdd(SubscriptionKey.WbJobList, wbjoblist);
                }

            }
            catch (Exception e)
            {
                _log.Error("获取58魔方岗位订单列表任务出错！", Tools.GetErrMsg(e));
            }
        }

        public async Task<List<WbMofangPostModel>> GetWbMofangJobList(int page, int pageSize, int tabType = 10, int sortType = 15)
        {
            try 
            {                
                var url = "https://mopinapi.58.com/project/pool/queryJobList";
                // 定义请求体的 JSON 数据
                var requestBody = new
                {
                    page,
                    pageSize,
                    tabType,
                    sortType,
                    keyWord = ""
                };
                var result = await url.WithHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36")
                    .WithHeader("Referer", "https://mopin.58.com/mopin/microapp/magiccube/invite/projectPoll")
                    .WithHeader("X-Requested-With", "XMLHttpRequest") // 表示请求从Ajax发出
                    .WithHeader("Origin", "https://mopin.58.com")
                    .WithHeader("Cookie", $"{cookie}")
                    .PostJsonAsync(requestBody)
                    .ReceiveString();
                string derestr = System.Text.RegularExpressions.Regex.Unescape(result);
                var res = JsonSerializer.DeserializeFromString<OrderReturn>(derestr);
                if (res.code != 0)
                {
                    throw new Exception($"58魔方岗位列表接口调用异常：{derestr}");
                }
                _log.Info($"58魔方岗位列表接口返回", $"参数：url:{url}", $"{derestr}");
                return res.data!.list!;
            }
            catch (FlurlHttpException ex)
            {
                _log.Error("58魔方列表接口调用异常！", ex.Message);
                var error = await ex.GetResponseStringAsync();
                throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
            }
        }

        public async Task<WbMofangPosDetailtModel> GetWbMofangDetailJob(string projectId)
        {
            try
            {
                var url = "https://mopinapi.58.com/project/pool/jobInfo";
                List<int> jobTab = new List<int>()// 岗位标签 默认4个
                {
                    1, 2, 3, 4
                };
                var requestBody = new
                {
                    projectId,
                    jobTab
                };
                var result = await url.WithHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36")
                    .WithHeader("Referer", $"https://mopin.58.com/mopin/microapp/magiccube/invite/jobDetail?projectId={projectId}")
                    .WithHeader("X-Requested-With", "XMLHttpRequest") // 表示请求从Ajax发出
                    .WithHeader("Origin", "https://mopin.58.com")
                    .WithHeader("Cookie", $"{cookie}")
                    .PostJsonAsync(requestBody)
                    .ReceiveString();
                result = result.Replace("\\\"", "");
                string derestr = System.Text.RegularExpressions.Regex.Unescape(result);
                var res = JsonSerializer.DeserializeFromString<OrderDetailReturn>(derestr);
                if (res.code != 0)
                {
                    throw new Exception($"58魔方岗位详情接口调用异常：{derestr}");
                }
                _log.Info($"58魔方岗位详情接口返回", $"参数：url:{url}", $"{derestr}");
                return res.data!;
            }
            catch(FlurlHttpException ex)
            {
                _log.Error("58魔方岗位详情接口调用异常！", ex.Message);
                var error = await ex.GetResponseStringAsync();
                throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
            }
        }

        public async Task DeleteWbMofangJobListTemp()
        {
            try
            {
                using var _context = _staffingContextFactory.CreateDbContext();
                // 每次存入临时表前，先删除岗位临时表数据
                var temp = _context.Thirdparty_Postid_Temp.Where(x => x.Source == PostSouce.魔方);
                _context.Thirdparty_Postid_Temp.RemoveRange(temp);
                _context.SaveChanges();
            }
            catch (FlurlHttpException ex)
            {
                _log.Error("58魔方岗位列表临时表删除任务出错！", Tools.GetErrMsg(ex));
                var error = await ex.GetResponseStringAsync();
                throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
            }
        }

        public async Task DeleteWbPostRelation()
        {
            try
            {
                using var _context = _staffingContextFactory.CreateDbContext();
                // 删除关联表数据
                var temp = _context.ThirdParty_Postid_Relation.Where(x => x.Source == PostSouce.魔方);
                _context.ThirdParty_Postid_Relation.RemoveRange(temp);
                _context.SaveChanges();
            }
            catch (FlurlHttpException ex)
            {
                _log.Error("58魔方岗位关联表删除任务出错！", Tools.GetErrMsg(ex));
                var error = await ex.GetResponseStringAsync();
                throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
            }
        }

        public async Task DownWbMoFangPost()
        {
            try
            {
                using var _context = _staffingContextFactory.CreateDbContext();
                List<string?> thirdpostidlist = new List<string?>();
                List<string?> wbjobidList = new List<string?>();
                var post = _context.ThirdParty_Postid_Relation.Where(x => x.Source == PostSouce.魔方).ToList();
                if (post != null)
                {
                    post.ForEach(x =>
                    {
                        if (x.ThirdPostId != null)
                        {
                            thirdpostidlist.Add(x.ThirdPostId);
                        }
                    });
                }
                //临时表数据，由于每次都是存入新的数据，认为跟58魔方岗位缓存数据一致
                _context.Thirdparty_Postid_Temp.Where(x => x.Source == PostSouce.魔方).ToList().ForEach(x =>
                {
                    wbjobidList.Add(x.ThirdPostId!);
                });

                if (wbjobidList.Count > 0 && 
                    thirdpostidlist.Count > 0 && 
                    ((thirdpostidlist.Count - wbjobidList.Count) < 200))
                {
                    //58魔方岗位在第三方岗位关系数据库中不在现查58魔方缓存中的数据
                    List<string?> except = thirdpostidlist.Except(wbjobidList).ToList();
                    MyRedis.Client.SAdd(SubscriptionKey.WbJobDownList, except);
                    await Task.Delay(500);
                }
            }
            catch (Exception ex)
            {
                _log.Error($"58魔方岗位下架出错", Tools.GetErrMsg(ex), "岗位下架出错");
            }

        }

        public class OrderReturn
        {
            public int code { get; set; }//错误代码
            public string? msg { get; set; }//成功 失败
            public ListData? data { get; set; }//返回全部列表
        }

        public class ListData
        {
            public int total { get; set;}//全部岗位总数
            public bool dayPermission {  get; set; }//是否有查看全部岗位权限
            public int totalPage {  get; set; }//总页数
            public bool costPermission { get; set;}//是否有查看费用权限
            public int pageSize {  get; set; }//每页显示数量
            public int page {  get; set; }//当前页码
            public List<WbMofangPostModel>? list { get; set; }//岗位列表

        }

        public class WbMofangPostModel
        {
            public required string jobId { get; set;}//职位id
            public required string projectId { get; set;}//项目id
            public string? jobTitle {  get; set; }//职位名称
            public decimal jobMinSalaryDesc {  get; set; }//工资下限
            public decimal jobMaxSalaryDesc {  get; set; }//工资上限
            public string? jobCity {  get; set; }//工作地点
            public string? jobCityName {  get; set; }//工作地点名称
            public string? interviewAddress {  get; set; }//面试地点
            public required string companyName {  get; set; }//企业名称
            public required string companyShortName {  get; set; }//企业简称
            public int chargeType {  get; set; }//入职结算类型 1 到面结算 2 入职结算 3 入职长返 4 到面结算+入职长返 5 入职结算+入职长返
            public int needInterview {  get; set; }//是否需要面试 0 不需要 1 需要
            public int ageMin {  get; set; }//年龄低值
            public int ageMax {  get; set; }//年龄高值
            public int degree {  get; set; }//学历要求 0 不限 1 初中 2 高中/中技/中专 3 大专 4 本科 5 硕士 6 博士
            public string? jobTypeName {  get; set; }//职位类型名称
            public List<Sessions>? sessions { get; set; }
            public List<ProjectExpenses>? projectExpenses { get; set; }
            public List<string>? companyIndustryDesc { get; set;}
            public int priceMode {  get; set; }//价格模式 0 不限 1 按天 2 按小时
            public int entryType {  get; set; }//
            public int weekDemandNum {  get; set;}//需求量
            public string? companyPicUrl {  get; set; }//公司Logo图片
            public int publishedTime {  get; set; }//职位发布时间
            public List<string>? jobAdvantageLabels { get; set;}//职位福利
            public double? chargeRatio { get; set;}//历史到访计费率
            public bool jobCharge {  get; set; }//是否已计费
            public bool goodComment {  get; set; }//是否好评
            public required string firstCateName {  get; set; }//一级类别名称
            public required string functionCrowd {  get; set; }//职位人群
            public bool isFocus {  get; set; }//是否聚焦
            public string? partnerName {  get; set; }//岗位发布单位名称
            public int proxyJob {  get; set; }//是否代理岗位 0-否 1-是
            public int todayResumeCount {  get; set; }//今日简历数
            public int expectedPaymentDays {  get; set; }//结算计费天数
        }

        public class Sessions 
        {
            public required string id {  get; set; }
            public int sessionState {  get; set; }//邀约状态 0 未开始 1 进行中 2 已结束
            public int sessionBeginTime {  get; set; }//邀约开始时间
            public int sessionEndTime {  get; set; }//邀约结束时间
            public string? totalDemand {  get; set; }//总需求
            public int receive {  get; set;}//收到的简历数
            public double completePersent { get; set;}//完成简历百分比
            public int forecastInvite {  get; set;}//可邀请数量
            public string? resumeAuditRequired {  get; set; }//简历筛选要求
        }

        public class ProjectExpenses
        {
            public int day {  get; set;}//质保期
            public string? bonusRatio {  get; set; }//佣金比例
            public decimal cost {  get; set;}//支付结算金额
            public decimal? bonus { get; set;}//佣金
            public decimal? exCost { get; set;}//额外佣金
            public string? exTime {  get; set;}//额外佣金结算时间
            public string? primaryCost { get; set;}//首次结算金额
        }


        public class OrderDetailReturn 
        {
            public int code { get; set; }//错误代码
            public string? msg { get; set; }//成功 失败
            public WbMofangPosDetailtModel? data { get; set; }//返回详细信息
        }

        public class WbMofangPosDetailtModel: WbMofangPostModel
        {
            public Job? job { get; set;}
            public JobSessionProcess? jobSessionProcess { get; set; }
        }
        public class Job
        {
            public JobMessage? jobMessage { get; set;}
            public SalaryMessage? salaryMessage { get; set;}
            public JobQaList? jobQaList { get; set; }
            public JobSessionAsk? jobSessionAsk { get; set; }
        }

        public class JobMessage
        {
            public string? jobAddress {  get; set; }//工作地点
            public string? jobContent {  get; set;}//职位描述
            public string? jobAdvantage {  get; set; }//职位福利
            public string? workTimeStart {  get; set;}//工作时间
            public string? workTimeEnd {  get; set;}//工作时间
            public string? workTimeFlexDesc {  get; set;}//工作时间
            public int workTimeDuration {  get; set;}//工作时长
            public string? weekend { get; set; }//周末
            public string? jobCityName {  get; set; }//工作城市名称
        }
        public class SalaryMessage
        {
            public int jobMinSalary {  get; set;}//最低工资
            public int jobMaxSalary {  get; set;}//最高工资
            public int basicMinSalary { get; set;}//基本工资下限
            public int basicMaxSalary { get; set;}//基本工资上限
            public List<string>? socialSecurity {  get; set;}//社保
            public List<string>? paidAnnualLeave {  get; set; }//带薪年假
            public string? salaryStructure {  get; set;}//工资结构
            public int tryoutSalary {  get; set;}//试用期工资
        }
        public class JobQaList
        {
            public List<JobQa>? jobQas { get; set;}
        }
        public class JobQa
        {
            public string? question { get; set;}
            public string? answer { get; set;}
        }
        public class JobSessionAsk
        {
            public int ageMin {  get; set;}
            public int ageMax {  get; set;}
            public int sex {  get; set;} // 0不限 1女 2男
        }

        public class TencentMapLocation
        {
            public long status {  get; set;}
            public List<object>? data { get; set;}
        }        

        public class LData
        { 
            public string? id {  get; set;}
            public object? location { get; set;}
        }

        public class tenlocation
        {
            public double lat {  get; set;}
            public double lng { get; set;}
        }
        public class JobSessionProcess
        {
            public int type { get; set; }
            public string? title { get; set; }
            public int count { get; set; }
            public JobSessionMessage? jobSessionMessage { get; set; }
        }
        public class JobSessionMessage
        {
            public string? interviewAddress { get; set; }//面试地址
            public string? interviewGps { get; set; }//面试地址经纬度
        }
    }
}
