namespace Config.CommonModel.ESign;

/// <summary>
/// 一步发起签署
/// </summary>
public class CreateFlowOneStep
{
    /// <summary>
    /// 流程基本信息
    /// </summary>
    public CreateFlowOneStepFlowInfo flowInfo { get; set; } = default!;

    /// <summary>
    /// 待签署文件信息
    /// </summary>
    public CreateFlowOneStepDocs[] docs { get; set; } = default!;

    /// <summary>
    /// 签署方信息
    /// </summary>
    public CreateFlowOneStepSigners[] signers { get; set; } = default!;

    /// <summary>
    /// 附件信息（仅用于查看，不需要签署的文档）
    /// </summary>
    public CreateFlowOneStepAttachments[] attachments { get; set; } = default!;
}

public class CreateFlowOneStepSigners
{
    /// <summary>
    /// 是否平台自动签署，默认false
    /// false-为对接平台的用户签署
    /// true-平台方自动签署
    /// </summary>
    public bool? platformSign { get; set; }

    /// <summary>
    /// 签署方签署顺序，默认1,且不小于1，顺序越小越先处理
    /// </summary>
    public int signOrder { get; set; } = 1;

    /// <summary>
    /// 签署方账号信息（平台方自动签署时，无需传入该参数）
    /// </summary>
    public CreateFlowOneStepSignerAccount? signerAccount { get; set; }

    /// <summary>
    /// 签署方的签署区列表数据
    /// </summary>
    public CreateFlowOneStepSignfields[] signfields { get; set; } = default!;
}

public class CreateFlowOneStepFlowInfo
{
    /// <summary>
    /// 全部签章后流程自动结束，默认false。
    /// true - 自动结束
    /// false - 非自动结束
    /// false时需要开发者调用【签署流程结束】接口结束流程
    /// </summary>
    public bool? autoArchive { get; set; }

    /// <summary>
    /// 是否自动开启，默认false。
    /// </summary>
    public bool? autoInitiate { get; set; }

    /// <summary>
    /// 本次签署流程的文件主题名称
    /// 注：名称不支持以下9个字符：/ \ : * " > | ？
    /// </summary>
    public string? businessScene { get; set; }

    /// <summary>
    /// 任务配置信息
    /// </summary>
    public CreateFlowOneStepFlowConfigInfo? flowConfigInfo { get; set; }
}

public class CreateFlowOneStepFlowConfigInfo
{
    /// <summary>
    /// 签署完成重定向地址
    /// </summary>
    public string? redirectUrl { get; set; }
}

public class CreateFlowOneStepDocs
{
    /// <summary>
    /// 待签署文件id
    /// </summary>
    public string? fileId { get; set; }

    /// <summary>
    /// 文件名称
    /// （签署界面展示的文件名称与下载时的文件名称）
    /// （1）名称不传扩展名时，例如传：xx，默认展示的文件名为 xx，下载时自动添加后缀 xx.pdf。
    /// （2）文件扩展名仅限传pdf格式，例如传 xx.pdf，展示的文件名即为所传值 xx.pdf，下载文件也为xx.pdf。
    /// （3）传入空值，取文件的原始名称，扩展名皆为.pdf。
    /// （4）文件名称不支持以下9个字符：/ \ : * " > | ？
    /// </summary>
    public string? fileName { get; set; }
}

public class CreateFlowOneStepAttachments
{
    /// <summary>
    /// 附件Id
    /// </summary>
    public string? fileId { get; set; }

    /// <summary>
    /// 附件名称
    /// 文件名称不支持以下9个字符：/ \ : * " > | ？
    /// </summary>
    public string? attachmentName { get; set; }
}

public class CreateFlowOneStepSignerAccount
{
    /// <summary>
    /// 签署操作人个人账号标识，即操作本次签署的个人
    /// 注：平台用户自动签署时，该参数需要传入签署主体账号id
    /// </summary>
    public string? signerAccountId { get; set; }
}

public class CreateFlowOneStepSignfields
{
    /// <summary>
    /// 是否自动执行，默认false（如果自动签署，必须设置为true）
    /// </summary>
    public bool? autoExecute { get; set; }

    /// <summary>
    /// 机构签约类别，当签约主体为机构时必传：
    /// 2-机构盖章
    /// 注：
    /// 1、签署主体是个人时，无需传入该参数，传入会导致无法正常签署
    /// 2、签署主体是企业时，该字段必传，传入2
    /// </summary>
    public int? actorIndentityType { get; set; }

    /// <summary>
    /// 文件fileID
    /// </summary>
    public string? fileId { get; set; }

    /// <summary>
    /// 印章ID
    /// 注：
    /// （1）当印章ID为空时，取appId对应企业的默认印章；
    /// （2）如果开通了实名签，企业手动签署这种场景不支持指定印章，个人签署场景是支持的；
    /// </summary>
    public string? sealId { get; set; }

    /// <summary>
    /// 签署类型，0-不限，1-单页签署，2-骑缝签署，默认1
    /// </summary>
    public int? signType { get; set; }

    /// <summary>
    /// 签署区位置信息 。
    /// signType为0时，本参数无效；
    /// signType为1时, 页码和XY坐标不能为空,；
    /// signType为2时, 页码和Y坐标不能为空
    /// </summary>
    public CreateFlowOneStepPosBean? posBean { get; set; }
}

public class CreateFlowOneStepPosBean
{
    /// <summary>
    /// 页码信息，
    /// 当签署区signType为2时, 页码可以'-'分割指定页码范围, 传all代表全部页码。
    /// 其他情况只能是数字
    /// </summary>
    public string? posPage { get; set; }

    /// <summary>
    /// x坐标，坐标为印章中心点
    /// </summary>
    public float? posX { get; set; }

    /// <summary>
    /// y坐标，坐标为印章中心点
    /// </summary>
    public float? posY { get; set; }
}

public class CreateFlowOneStepResponse
{
    /// <summary>
    /// 流程ID
    /// </summary>
    public string? flowId { get; set; }
}

/// <summary>
/// 获取签署地址
/// </summary>
public class GetSignflowsExecuteUrl
{
    /// <summary>
    /// 签字流程ID
    /// </summary>
    public string? flowId { get; set; }

    /// <summary>
    /// 个人签字账号ID
    /// </summary>
    public string? accountId { get; set; }

    /// <summary>
    /// 签字流程类型，默认值为空
    /// organizeId = "" 或不传值时表示查询签字人的个人签字流程。
    /// </summary>
    public string? organizeId { get; set; }

    /// <summary>
    /// 签字链接类型，固定值为2。
    /// 2 代表签字详情链接
    /// </summary>
    public string? urlType { get; set; }

    /// <summary>
    /// 设置签字完成后回调App中的页面地址
    /// 示例：appScheme=esign://demo/signBack
    /// </summary>
    public string? appScheme { get; set; }
}

public class GetSignflowsExecuteUrlResponse
{
    /// <summary>
    /// 签字页面短链接（30天有效）
    /// </summary>
    public string? shortUrl { get; set; }

    /// <summary>
    /// 签字页面长链接（永久有效）
    /// </summary>
    public string? url { get; set; }
}

/// <summary>
/// 签署流程查询
/// </summary>
public class GetFlowOneStep
{
    /// <summary>
    /// 流程ID
    /// </summary>
    public string? flowId { get; set; }
}

public class GetFlowOneStepResponse
{
    /// <summary>
    /// 流程ID
    /// </summary>
    public string? flowId { get; set; }

    /// <summary>
    /// 发起签署流程的应用ID
    /// </summary>
    public string? appId { get; set; }

    /// <summary>
    /// 签署主题
    /// </summary>
    public string? businessScene { get; set; }

    /// <summary>
    /// 全部签章后流程是否自动完结
    /// </summary>
    public bool? autoArchive { get; set; }

    /// <summary>
    /// 流程当前状态
    /// 0 - 草稿
    /// 1 - 签署中
    /// 2 - 已完成
    /// 3 - 已撤销
    /// 4 - 终止（签署流程设置了文件有效截至日期，到期后触发）
    /// 5 - 已过期（签署截至日期到期后触发）
    /// 7 - 已拒签
    /// </summary>
    public int? flowStatus { get; set; }

    /// <summary>
    /// 流程描述
    /// 如果流程已拒签或已撤回, 并且存在拒签或撤回原因,，
    /// 流程描述显示为原因, 否则默认为流程状态描述
    /// </summary>
    public string? flowDesc { get; set; }

    /// <summary>
    /// 流程开始时间（unix毫秒时间戳）
    /// </summary>
    public long? flowStartTime { get; set; }

    /// <summary>
    /// 流程结束时间（unix毫秒时间戳）
    /// </summary>
    public long? flowEndTime { get; set; }

    /// <summary>
    /// 发起人账号ID
    /// </summary>
    public string? initiatorAccountId { get; set; }

    /// <summary>
    /// 发起方主体账号ID
    /// </summary>
    public string? initiatorAuthorizedAccountId { get; set; }

    /// <summary>
    /// 签署有效截止日期
    /// </summary>
    public string? signValidity { get; set; }

    /// <summary>
    /// 流程配置信息
    /// </summary>
    public GetFlowOneStepConfigInfo? configInfo { get; set; }
}

public class GetFlowOneStepConfigInfo
{
    /// <summary>
    /// 签署平台，逗号分割，
    /// 1 - 开放服务H5
    /// 2 - 支付宝
    /// </summary>
    public string? signPlatform { get; set; }

    /// <summary>
    /// 签署完成重定向地址
    /// </summary>
    public string? redirectUrl { get; set; }
}

public class RevokeSignflows
{
    /// <summary>
    /// 流程id，该参数需放在请求地址里面，可以参考【请求示例】
    /// </summary>
    public string? flowId { get; set; }

    /// <summary>
    /// 发起人账户id，即发起本次签署的操作人个人账号id；如不传，默认由对接平台发起
    /// </summary>
    public string? operatorId { get; set; }

    /// <summary>
    /// 撤销原因,默认"撤销"（最长400字节）
    /// </summary>
    public string? revokeReason { get; set; }
}

/// <summary>
/// 签署流程开启
/// </summary>
public class StartSignflows
{
    /// <summary>
    /// 流程ID
    /// </summary>
    public string? flowId { get; set; }
}