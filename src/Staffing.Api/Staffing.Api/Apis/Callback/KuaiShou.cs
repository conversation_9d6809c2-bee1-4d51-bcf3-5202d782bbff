using System.Text;
using Config;
using Config.CommonModel.ThirdTalentInfo;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.Hr;

namespace Staffing.Api.Apis.Callback;

[Service(ServiceLifetime.Transient)]
public class KuaiShouRouter : ICallbackRouterBase
{
    public void AddRoutes(RouteGroupBuilder group)
    {
        group.MapGet("/thirdabut/kuaishouabut/tenantCallBack", (string openTenantId, string state, LogManager log, IDbContextFactory<StaffingContext> staffingContextFactory) =>
        {
            try
            {
                var st = MyRedis.Client.Get<KuaiShouEncryptModel?>($"{RedisKey.KuaiShou.KuaiShouCallbackKey}{state}");
                if (st == null)
                    throw new Exception($"state无效");

                if (st.Key != Constants.KuaiShouEncryptKey)
                    throw new Exception($"快手绑定租户回调内容不合法:{state}");

                using var _context = staffingContextFactory.CreateDbContext();

                var ksTenant = _context.Kuaishou_Tenant.FirstOrDefault(x => x.TenantOpenId == openTenantId);

                if (ksTenant == null)
                {
                    ksTenant = new Kuaishou_Tenant
                    {
                        Creator = st.UserId ?? string.Empty,
                        Name = string.Empty,
                        TenantOpenId = openTenantId
                    };
                    _context.Add(ksTenant);
                }

                _context.SaveChanges();
            }
            catch (Exception e)
            {
                log.Error("快手绑定租户回调失败", Tools.GetErrMsg(e), state);
            }
        }).WithOpenApi(operation => new(operation) { Summary = "快手绑定租户回调" });

        group.MapGet("/thirdabut/kuaishouabut/userCallBack", (string openId, string state, LogManager log, IDbContextFactory<StaffingContext> staffingContextFactory, IHostEnvironment hostingEnvironment) =>
        {
            var bkcode = "1";
            try
            {
                var st = MyRedis.Client.Get<KuaiShouEncryptModel?>($"{RedisKey.KuaiShou.KuaiShouCallbackKey}{state}");
                if (st == null)
                    throw new Exception($"state无效");

                if (st.Key != Constants.KuaiShouEncryptKey)
                    throw new Exception($"快手绑定用户回调内容不合法:{state}");

                using var _context = staffingContextFactory.CreateDbContext();

                var ksOpenId = _context.User_OpenId.FirstOrDefault(x => x.UserId == st.UserId && x.Type == ClientType.Kzg);

                if (ksOpenId == null)
                {
                    ksOpenId = new User_OpenId
                    {
                        UserId = st.UserId!,
                        AppId = Guid.NewGuid().ToString(),
                        Type = ClientType.Kzg,
                        OpenId = openId,
                        UnionId = openId
                    };
                    _context.Add(ksOpenId);
                }

                _context.SaveChanges();
            }
            catch (Exception e)
            {
                bkcode = "2";
                log.Error("快手绑定用户回调失败", Tools.GetErrMsg(e), state);
            }

            var callBackUrl = "https://unit.nuopin.cn/#/result?code=";
            if (!hostingEnvironment.IsProduction())
                callBackUrl = "https://test-unit.nuopin.cn/#/result?code=";

            return Results.Redirect($"{callBackUrl}{bkcode}");
        }).WithOpenApi(operation => new(operation) { Summary = "快手绑定用户回调" });

        group.MapPost("/thirdabut/kuaishouabut/receivePosition", async (HttpRequest request, LogManager log, IThirdTalentInfoService<KsTalentInfo> Service) =>
        {
            if (request.Body.CanSeek)
            {
                request.Body.Seek(0, SeekOrigin.Begin);
            }
            var body = await new StreamReader(request.Body, Encoding.UTF8).ReadToEndAsync();
            try
            {
                if (string.IsNullOrWhiteSpace(body))
                    throw new Exception("接收快手职位数据出错：内容为空");

                var receiveText = AesEncryptHelper.Decorypt(body);
                await Service.TryParseTalentInfoAsync(receiveText);
            }
            catch (Exception e)
            {
                log.Error("接收快手职位数据出错", Tools.GetErrMsg(e), body);
            }
        }).WithOpenApi(operation => new(operation) { Summary = "接收快手职位数据" });
    }
}