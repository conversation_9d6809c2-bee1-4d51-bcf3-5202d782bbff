﻿using Config.Enums;
using Config.CommonModel;
using EntityFrameworkCore.AutoHistory.Extensions;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Model.Seeker.User;
using Staffing.Model.Service;
using Staffing.Core.Interfaces;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces.Seeker;
using Config;
using Microsoft.EntityFrameworkCore;
using Infrastructure.CommonService;
using NetTopologySuite.Geometries;
using Config.CommonModel.Business;
using Infrastructure.Proxy;
using Config.CommonModel.Tencent;
using Staffing.Model.Seeker.Balance;
using Infrastructure.Aliyun;
using Senparc.Weixin.Exceptions;

namespace Staffing.Core.Services.Seeker;

[Service(ServiceLifetime.Transient)]
public class SeekerService : ISeekerService
{
    private readonly StaffingContext _context;
    private readonly RequestContext _user;
    private readonly ConfigManager _config;
    private readonly CacheHelper _cacheHelper;
    private readonly ISharedService _sharedService;
    private readonly ICommonService _commonService;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CommonUserService _commonUserService;
    private readonly CommonProjectService _commonProjectService;
    private readonly CommonCacheService _commonCacheService;
    private readonly CommonDicService _commonDicService;
    private readonly CommonNScoreService _commonNScoreService;
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly WeChatHelper _weChatHelper;
    private readonly KuaiShouHelper _kuaiShouHelper;
    private readonly NuoPinApi _nuoPinApi;
    private readonly TencentClient _tencentClient;
    private readonly TencentImHelper _tencentImHelper;
    private readonly NuoYouKaoApi _nuoYouKaoApi;
    private readonly CommonWithdraw _commonWithdraw;
    private readonly SmsHelper _smsHelper;
    private readonly DouYinHelper _douYinHelper;
    public SeekerService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, ICommonService commonService,
        LogManager log, WeChatHelper weChatHelper, CommonWithdraw commonWithdraw,
        ISharedService sharedService, IDbContextFactory<StaffingContext> contextFactory,
        CommonUserService commonUserService, CommonCacheService commonCacheService,
        CommonDicService commonDicService, NuoPinApi nuoPinApi, TencentClient tencentClient,
        TencentImHelper tencentImHelper, CommonProjectService commonProjectService,
        NuoYouKaoApi nuoYouKaoApi, CommonNScoreService commonNScoreService, SmsHelper smsHelper,
        KuaiShouHelper kuaiShouHelper, DouYinHelper douYinHelper)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _commonService = commonService;
        _log = log;
        _weChatHelper = weChatHelper;
        _kuaiShouHelper = kuaiShouHelper;
        _sharedService = sharedService;
        _contextFactory = contextFactory;
        _commonUserService = commonUserService;
        _commonCacheService = commonCacheService;
        _commonDicService = commonDicService;
        _nuoPinApi = nuoPinApi;
        _tencentClient = tencentClient;
        _tencentImHelper = tencentImHelper;
        _commonProjectService = commonProjectService;
        _nuoYouKaoApi = nuoYouKaoApi;
        _commonNScoreService = commonNScoreService;
        _commonWithdraw = commonWithdraw;
        _smsHelper = smsHelper;
        _douYinHelper = douYinHelper;
    }

    public SeekerInfo GetSeeker()
    {
        var result = _context.User_Seeker.Where(x => x.UserId == _user.Id)
        .Select(s => new SeekerInfo
        {
            UserId = s.UserId,
            Mobile = s.User.Mobile,
            Avatar = s.Avatar,
            CreatedTime = s.CreatedTime,
            Name = s.NickName,
            Status = s.Status,
            AdviserNum = s.User_Num.Adviser,
            ResumeScore = s.User_Resume.Score,
            Address = s.Address,
            Lat = s.Location.X,
            Lng = s.Location.Y,
            RegionId = s.RegionId,
            H5Notice = s.H5Notice,
            WeChatH5Subscribe = s.User.WeChatH5Subscribe,
            IsAdviser = s.User.User_Hr.Status == UserStatus.Active,
            IdCard = s.User.IdentityCard,
            IdName = s.User.IdentityCardName,
            IsChannel = s.Qd_Hr_Channel.Any(),
            AttachmentResume = new AttachmentResume
            {
                Title = s.User_Resume_Attach.Title,
                Url = s.User_Resume_Attach.Url,
                Language = s.User_Resume_Attach.Language,
                CreatedTime = s.User_Resume_Attach.CreatedTime,
                Size = s.User_Resume_Attach.Size
            },
            Adviser = new MyAdviserInfo
            {
                AdviserId = s.User_Extend.Adviser.UserId
            }
        }).FirstOrDefault();

        if (result == null)
            throw new ForbiddenException("用户不存在");

        //是否面试官
        result.IsInterview = _context.Project_Interviewer.Any(x => x.Phone == result.Mobile);

        result.Mobile = Tools.MobilToX(result.Mobile);

        result.Identity = !string.IsNullOrEmpty(result.IdCard);
        result.IdCard = Tools.IdCardToX(result.IdCard);

        result.DeliveryBehavior = _commonCacheService.GetSeekerDeliveryBehavior(_user.Id);

        if (!string.IsNullOrWhiteSpace(result.RegionId))
            result.Region = _commonDicService.GetCityById(result.RegionId);

        return result;
    }

    public GetHrBaseInfoResponse GetHrBaseInfo(GetHrBaseInfo model)
    {
        var result = _context.User_Hr.Where(x => x.UserId == model.HrId)
        .Select(s => new GetHrBaseInfoResponse
        {
            Avatar = s.Avatar,
            NickName = s.NickName
        }).FirstOrDefault();

        if (result == null)
            throw new BadRequestException("内容不存在");

        return result;
    }

    public CheckChannelResponse CheckChannel()
    {
        if (string.IsNullOrWhiteSpace(_user.ChannelId))
            throw new BadRequestException("缺少渠道Id");

        var result = _context.Qd_Hr_Channel.Where(x => x.Id == _user.ChannelId)
        .Select(s => new CheckChannelResponse
        {
            HrId = s.HrId,
            GroupQrCode = s.GroupQrCode
        }).FirstOrDefault();

        if (result == null)
            throw new BadRequestException("渠道商不存在");

        var talent = _context.Talent_Platform.Where(x => x.HrId == result.HrId && x.SeekerId == _user.Id)
        .Select(s => new { s.Id, s.ChannelId }).FirstOrDefault();

        if (talent != null && talent.ChannelId != _user.ChannelId)
            result.Status = 1;
        else
            _commonUserService.SetAdviser(_user.Id, result.HrId!, channelId: _user.ChannelId);

        return result;
    }

    public EmptyResponse UpdateSeeker(UpdateSeeker model)
    {
        var lockerKey = $"st:upseeker:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var resume = _context.User_Resume.Include(i => i.User_Seeker).Include(i => i.User)
        .FirstOrDefault(x => x.UserId == _user.Id);

        if (resume == null)
            throw new NotFoundException("内容不存在");

        //是否需改手机号
        var updateMobile = false;

        if (!string.IsNullOrWhiteSpace(model.NewMobile))
        {
            if (model.OldMobile?.Trim() != resume.User.Mobile.Trim())
                throw new BadRequestException("原手机号码验证失败");

            if (_context.User.Any(x => x.Mobile == model.NewMobile && x.UserId != _user.Id))
                throw new BadRequestException("该手机号已被使用");

            var checkSMSCode = _sharedService.CheckSMSCode(new CheckSMSCode
            {
                Phone = model.NewMobile,
                SMSCode = model.SmsCode,
                Type = SMSType.ChangePhone
            });

            if (!checkSMSCode.Result)
                throw new BadRequestException("验证码错误");

            resume.User.Mobile = model.NewMobile;

            updateMobile = true;
        }

        //如果已实名，不能修改姓名、性别、生日
        if (string.IsNullOrWhiteSpace(resume.User.IdentityCard))
        {
            if (model.Sex.HasValue)
                resume.Sex = model.Sex;

            if (!string.IsNullOrWhiteSpace(model.Name))
                resume.User_Seeker.NickName = model.Name;

            if (model.Birthday.HasValue)
                resume.Birthday = model.Birthday;
        }

        if (!string.IsNullOrWhiteSpace(model.Avatar))
            resume.User_Seeker.Avatar = model.Avatar;

        if (model.H5Notice.HasValue)
            resume.User_Seeker.H5Notice = model.H5Notice.Value;

        if (model.Occupation.HasValue)
            resume.Occupation = model.Occupation;

        if (model.Education.HasValue)
            resume.Education = model.Education;

        if (!string.IsNullOrWhiteSpace(model.School))
            resume.School = model.School;

        if (model.GraduationDate.HasValue)
            resume.GraduationDate = model.GraduationDate;

        if (!string.IsNullOrWhiteSpace(model.Major))
            resume.Major = model.Major;

        if (model.Describe != null)
            resume.Describe = model.Describe;

        if (model.EMail != null)
            resume.EMail = model.EMail;

        if (model.WeChatNo != null)
            resume.WeChatNo = model.WeChatNo;

        if (model.Qq != null)
            resume.Qq = model.Qq;

        if (model.Show.HasValue)
            resume.Show = model.Show.Value;

        if (model.Anonymous.HasValue)
            resume.Anonymous = model.Anonymous.Value;

        if (model.Nature != null)
            resume.Nature = model.Nature;

        if (model.Skill != null)
            resume.Skill = model.Skill;

        if (model.Appearance != null)
            resume.Appearance = model.Appearance;

        if (model.Certificate != null)
            resume.Certificate = model.Certificate;

        resume.UpdatedTime = DateTime.Now;

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        //更新im资料
        if (!string.IsNullOrWhiteSpace(resume.User_Seeker.TencentImId))
            MyRedis.Client.SAdd(SubscriptionKey.UpdateSkImAct, resume.UserId);

        if (updateMobile)
            MyRedis.Client.SAdd(SubscriptionKey.NewSeekerMobile, _user.Id);

        // 用户简历删除缓存
        MyRedis.Client.SAdd(SubscriptionKey.OssUserIdKey, _user.Id);

        return new EmptyResponse();
    }

    public UpdateCampusResponse UpdateCampus(UpdateCampus model)
    {
        var lockerKey = $"st:upcampus:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var result = new UpdateCampusResponse();
        if (string.IsNullOrWhiteSpace(model.Name))
            throw new BadRequestException("名称不能为空");

        if (string.IsNullOrWhiteSpace(model.Award))
            throw new BadRequestException("职位/奖项不能为空");

        if (!model.BeginDate.HasValue)
            throw new BadRequestException("开始时间不能为空");

        if (!model.EndDate.HasValue)
            throw new BadRequestException("结束时间不能为空");

        User_Campus? campus = null;
        if (!string.IsNullOrWhiteSpace(model.Id))
        {
            campus = _context.User_Campus.FirstOrDefault(x => x.UserId == _user.Id && x.Id == model.Id);
            if (campus == null)
                throw new NotFoundException("内容不存在");
        }

        if (campus == null)
        {
            //检测客户端重复提交
            if (_context.User_Campus.Any(x => x.UserId == _user.Id && x.Name == model.Name
            && x.BeginDate == x.BeginDate && x.EndDate == model.EndDate))
                throw new BadRequestException("重复添加");

            //添加校园经历
            campus = new User_Campus
            {
                UserId = _user.Id
            };
            _context.Add(campus);
        }

        campus.Name = model.Name;
        campus.Award = model.Award;
        campus.BeginDate = model.BeginDate.Value;
        campus.EndDate = model.EndDate.Value;
        campus.Describe = model.Describe;

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        UpdateResumeTime(_user.Id);

        result.Id = campus.Id;
        return result;
    }

    public UpdateWorksResponse UpdateWorks(UpdateWorks model)
    {
        var lockerKey = $"st:upworks:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var result = new UpdateWorksResponse();
        if (string.IsNullOrWhiteSpace(model.Company))
            throw new BadRequestException("公司不能为空");

        if (string.IsNullOrWhiteSpace(model.Post))
            throw new BadRequestException("职位不能为空");

        if (!model.BeginDate.HasValue)
            throw new BadRequestException("开始时间不能为空");

        if (!model.EndDate.HasValue)
            throw new BadRequestException("结束时间不能为空");

        User_Work? work = null;
        if (!string.IsNullOrWhiteSpace(model.Id))
        {
            work = _context.User_Work.FirstOrDefault(x => x.UserId == _user.Id && x.Id == model.Id);
            if (work == null)
                throw new NotFoundException("内容不存在");
        }

        if (work == null)
        {
            //检测客户端重复提交
            if (_context.User_Work.Any(x => x.UserId == _user.Id && x.Company == model.Company
            && x.Post == model.Post && x.BeginDate == x.BeginDate && x.EndDate == model.EndDate))
                throw new BadRequestException("重复添加");

            //添加工作经历
            work = new User_Work
            {
                UserId = _user.Id
            };
            _context.Add(work);
        }

        work.Company = model.Company;
        work.Post = model.Post;
        work.BeginDate = model.BeginDate.Value;
        work.EndDate = model.EndDate.Value;
        work.Describe = model.Describe;

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        UpdateResumeTime(_user.Id);

        result.Id = work.Id;

        return result;
    }

    public EmptyResponse DeleteCampus(string id)
    {
        var campus = _context.User_Campus.FirstOrDefault(x => x.UserId == _user.Id && x.Id == id);
        if (campus == null)
            throw new NotFoundException("内容不存在");

        _context.Remove(campus);

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        UpdateResumeTime(_user.Id);
        return new EmptyResponse();
    }

    public EmptyResponse DeleteWorks(string id)
    {
        var work = _context.User_Work.FirstOrDefault(x => x.UserId == _user.Id && x.Id == id);
        if (work == null)
            throw new NotFoundException("内容不存在");

        _context.Remove(work);

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        UpdateResumeTime(_user.Id);
        return new EmptyResponse();
    }

    private void UpdateResumeTime(string userId)
    {
        using var context = _contextFactory.CreateDbContext();
        var resume = context.User_Resume.First(x => x.UserId == _user.Id);
        resume.UpdatedTime = DateTime.Now;
        context.SaveChanges();

        // 用户简历删除缓存
        MyRedis.Client.SAdd(SubscriptionKey.OssUserIdKey, _user.Id);
    }

    public GetMyAdviserResponse GetMyAdviser(GetMyAdviser model)
    {
        var result = new GetMyAdviserResponse();

        //如果登录
        var predicate = PredicateBuilder.New<Talent_Platform>(x => x.SeekerId == _user.Id);

        if (!string.IsNullOrWhiteSpace(model.Name))
            predicate = predicate.And(x => x.User_Hr.NickName.Contains(model.Name));

        var sql = _context.Talent_Platform.IgnoreQueryFilters().Where(predicate);
        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(o => o.HrId == _user.AdviserId)
        .ThenByDescending(o => o.SeekerVisitTime)
        .ThenBy(x => x.Id)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new MyAdviserInfo
        {
            Avatar = s.User_Hr.Avatar,
            Describe = s.User_Hr.Describe,
            EntName = s.User_Hr.Enterprise.Name,
            Name = s.User_Hr.NickName,
            Talent = s.User_Hr.User_Num.Talent,
            VirtualTalent = s.User_Hr.User_Num.VirtualTalent,
            AdviserId = s.User_Hr.UserId,
            LoginTime = s.User_Hr.User_Extend.LoginTime,
            IsCurrent = s.User_Hr.UserId == _user.AdviserId,
            DataScore = s.User_Hr.Score
        }).ToList();

        foreach (var item in result.Rows)
        {
            item.OnlineStatus = Tools.GetOnlineStatus(item.LoginTime);
            item.Grade = Tools.GetHrGrade(item.DataScore, item.LoginTime);
        }

        return result;
    }

    public MyAdviserInfo GetCurrentAdviser()
    {
        var result = _context.User_Hr.Where(x => x.UserId == _user.AdviserId)
        .Select(s => new MyAdviserInfo
        {
            Avatar = s.Avatar,
            Describe = s.Describe,
            EntName = s.Enterprise.Name,
            Name = s.NickName,
            Talent = s.User_Num.Talent,
            VirtualTalent = s.User_Num.VirtualTalent,
            AdviserId = s.UserId,
            LoginTime = s.User_Extend.LoginTime,
            IsCurrent = s.UserId == _user.AdviserId,
            DataScore = s.Score,
            EntWeChatQrCode = s.EntWeChatQrCode,

            Mobile = s.User.Mobile//为获取企微二维码使用一下，之后需要置空
        }).FirstOrDefault();

        if (result == null)
            throw new NotFoundException("内容不存在");

        result.OnlineStatus = Tools.GetOnlineStatus(result.LoginTime);
        result.Grade = Tools.GetHrGrade(result.DataScore, result.LoginTime);

        // 企业微信二维码
        if (string.IsNullOrEmpty(result.EntWeChatQrCode))
        {
            var qw = _context.QYWechat_Employee.Where(x => x.Mobile == result.Mobile).FirstOrDefault();
            if (qw != null && !string.IsNullOrEmpty(qw.QRCodeURL))
                result.EntWeChatQrCode = qw.QRCodeURL;
        }
        result.Mobile = string.Empty;
        return result;
    }

    public EmptyResponse SetAdviser(SetAdviser model)
    {
        if (string.IsNullOrWhiteSpace(model.HrId))
            throw new BadRequestException("缺少顾问Id");

        var userId = _user.Id;
        Task.Run(() => _commonUserService.SetAdviser(userId, model.HrId, channelId: _user.ChannelId));

        return new EmptyResponse();
    }

    public EmptyResponse AddAdviser(AddAdviser model)
    {
        var lockerKey = $"st:upadviser:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var hrId = model.HrId;
        if (!string.IsNullOrWhiteSpace(model.InviteCode))
        {
            model.InviteCode = model.InviteCode?.TrimStart('0');
            hrId = _context.User_Hr.Where(x => x.InviteCode == model.InviteCode)
            .Select(s => s.UserId).FirstOrDefault();
        }

        if (string.IsNullOrWhiteSpace(hrId))
            throw new BadRequestException("顾问不存在");

        _commonUserService.SetAdviser(_user.Id, hrId, channelId: _user.ChannelId);

        // //更新用户当前顾问
        // var userExt = _context.User_Extend.First(x => x.UserId == _user.Id);
        // userExt.AdviserId = hrId;

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public EmptyResponse UpdateAttchResume(UpdateAttchResume model)
    {

        if (string.IsNullOrWhiteSpace(model.Url))
            throw new BadRequestException("缺少url");

        var userAtt = _context.User_Resume_Attach.FirstOrDefault(x => x.UserId == _user.Id);

        if (userAtt == null)
        {
            userAtt = new User_Resume_Attach
            {
                UserId = _user.Id
            };
            _context.Add(userAtt);
        }

        userAtt.Url = model.Url;
        userAtt.Language = model.Language;
        userAtt.Size = model.Size;

        var userName = _context.User_Seeker.Where(x => x.UserId == _user.Id)
        .Select(s => s.NickName).FirstOrDefault();

        //处理附件简历名字
        var ext = Path.GetExtension(userAtt.Url);
        userAtt.Title = !string.IsNullOrWhiteSpace(userName) ? $"{userName}的简历" : "我的简历";
        if (!string.IsNullOrWhiteSpace(ext))
            userAtt.Title = $"{userAtt.Title}{ext}";

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public EmptyResponse DeleteAttchResume()
    {

        var userAtt = _context.User_Resume_Attach.Where(x => x.UserId == _user.Id);

        _context.RemoveRange(userAtt);

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public GetSeekerBehaviorResponse GetSeekerBehavior()
    {
        var result = new GetSeekerBehaviorResponse();

        if (string.IsNullOrWhiteSpace(_user.Id))
            return result;

        // var pcKey = $"{RedisKey.PostCategoryVisited}{_user.Id}_{_user.AdviserId}";
        var pcKey = $"{RedisKey.PostCategoryVisited2}";

        // var rt = await MyRedisHelper.MyRedis.ZRevRangeWithScoresAsync<string>(pcKey, 0, 7);
        // var rt = MyRedis.Client.ZRevRangeWithScores(pcKey, 0, 7);
        var rt = MyRedis.Client.HGet<List<GeneralKeyValue<int>>?>(pcKey, $"{_user.Id}_{_user.AdviserId}");
        rt = rt ?? new List<GeneralKeyValue<int>>();
        rt = rt.OrderByDescending(o => o.Value).Take(8).ToList();

        var categoryIds = rt.Select(s => s.Key).ToList();
        var posts = _commonDicService.GetPost();
        var dicPost = posts.Where(x => categoryIds.Contains(x.Id))
        .Select(s => new { s.Id, s.Name }).ToList();

        foreach (var item in rt)
        {
            var ace = new PostCategoryVisitedInfo();

            ace.Category = Convert.ToInt32(item.Key);
            ace.Times = item.Value;
            ace.CategoryName = dicPost.FirstOrDefault(x => x.Id == item.Key)?.Name;

            ace.Logo = (ace.Category % 7) switch
            {
                0 => "https://resources.nuopin.cn/wechatpmr821/re1.png",
                1 => "https://resources.nuopin.cn/wechatpmr821/re2.png",
                2 => "https://resources.nuopin.cn/wechatpmr821/re3.png",
                3 => "https://resources.nuopin.cn/wechatpmr821/re4.png",
                4 => "https://resources.nuopin.cn/wechatpmr821/re5.png",
                5 => "https://resources.nuopin.cn/wechatpmr821/re6.png",
                6 => "https://resources.nuopin.cn/wechatpmr821/re7.png",
                _ => string.Empty
            };
            result.PostCategoryVisited.Add(ace);
        }

        return result;
    }

    public EmptyResponse UpdateSeekerLocation(UpdateSeekerLocation model)
    {

        if (string.IsNullOrWhiteSpace(model.RegionId))
            throw new BadRequestException("RegionId不能为空");

        model.RegionId = Tools.TrimRegionId(model.RegionId);

        var seeker = _context.User_Seeker.FirstOrDefault(x => x.UserId == _user.Id);

        if (seeker == null)
            throw new NotFoundException("内容不存在");

        seeker.Location = new Point(model.Lat, model.Lng);
        seeker.Address = model.Address;
        seeker.RegionId = Tools.TrimRegionId(model.RegionId);

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public GetCollectPostResponse GetCollectPost(GetCollectPost model)
    {
        var result = new GetCollectPostResponse();

        var predicate = PredicateBuilder.New<User_Collect_Post>(x => x.SeekerId == _user.Id);

        var sql = _context.User_Collect_Post.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql.OrderByDescending(o => o.HrId == _user.AdviserId)
        .ThenByDescending(o => o.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetCollectPostInfo
        {
            Id = s.Id,
            TeamPostId = s.TeamPostId,
            IsCurrent = s.HrId == _user.AdviserId,
            AgentEnt = new GetAgentEntDetail
            {
                DisplayName = s.Post_Team.Post.Agent_Ent.DisplayName,
                AgentEntId = s.Post_Team.Post.AgentEntId,
                Scale = s.Post_Team.Post.Agent_Ent.Scale,
                Abbr = s.Post_Team.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post_Team.Post.Agent_Ent.Abbr : s.Post_Team.Post.Agent_Ent.DisplayName,
                LogoUrl = s.Post_Team.Post.Agent_Ent.LogoUrl,
                ScaleName = s.Post_Team.Post.Agent_Ent.Scale.GetDescription(),
                Industry = s.Post_Team.Post.Agent_Ent.Industry.ToString(),
                IndustryName = s.Post_Team.Post.Agent_Ent.Dic_Industry.Name
            },
            Hr = new HrModel
            {
                HrId = s.HrId,
                HrName = s.User_Hr.NickName,
                Avatar = s.User_Hr.Avatar
            },
            PostAutoId = s.Post_Team.Post.AutoId,
            ProjectId = s.Post_Team.Post.ProjectId,
            Name = s.Post_Team.Post.Name,
            CreatedTime = s.Post_Team.Post.CreatedTime,
            UpdatedTime = s.Post_Team.Post.UpdatedTime,
            Address = s.Post_Team.Post.Address,
            Category = s.Post_Team.Post.Category,
            CategoryName = s.Post_Team.Post.Dic_Post.Name,
            Welfare = s.Post_Team.Post.Welfare,
            WelfareCustom = s.Post_Team.Post.WelfareCustom,
            Lat = s.Post_Team.Post.Location.X,
            Lng = s.Post_Team.Post.Location.Y,
            MaxSalary = s.Post_Team.Post.MaxSalary,
            MinSalary = s.Post_Team.Post.MinSalary,
            Money = s.Post_Team.Post.Money,
            PostId = s.Post_Team.Post.PostId,
            RecruitNumber = s.Post_Team.Post.DeliveryNumber,
            RegionId = s.Post_Team.Post.RegionId,
            Salary = s.Post_Team.Post.Salary,
            WorkNature = s.Post_Team.Post.WorkNature,
            SalaryType = s.Post_Team.Post.SalaryType,
            SalaryTypeName = s.Post_Team.Post.SalaryType.GetDescription(),
            WorkNatureName = s.Post_Team.Post.WorkNature.GetDescription()
        }).ToList();

        var welfare = _commonDicService.GetWelfare();
        foreach (var item in result.Rows)
        {
            item.PostNo = $"{Constants.PostIdPre}{item.PostAutoId.ToString()!.PadLeft(2, '0')}";

            item.SalaryName = Tools.FormattingSalary(item.MinSalary, item.MaxSalary, item.SalaryType, item.WorkNature, item.Salary);

            if (item.Welfare?.Count > 0)
                item.Welfare = welfare.Where(x => item.Welfare.Any(a => a.Id == x.Id)).ToList();

            if (!string.IsNullOrWhiteSpace(item.RegionId))
                item.Region = _commonDicService.GetCityById(item.RegionId);
        }

        return result;
    }

    public GetFollowIndustryResponse GetFollowIndustry(GetFollowIndustry model)
    {
        var result = new GetFollowIndustryResponse();

        var predicate = PredicateBuilder.New<User_Follow_Industry>(x => x.SeekerId == _user.Id);

        var sql = _context.User_Follow_Industry.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql.OrderByDescending(o => o.HrId == _user.AdviserId)
        .ThenByDescending(o => o.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetFollowIndustryInfo
        {
            Id = s.Id,
            IsCurrent = s.HrId == _user.AdviserId,
            HrId = s.HrId,
            HrName = s.User_Hr.NickName,
            HrAvatar = s.User_Hr.Avatar,
            Industry = s.Industry,
            IndustryName = s.Industry.GetDescription(),
            Post = _context.Post_Team.Count(c => c.Project_Team.HrId == _user.AdviserId
            && c.Post.Project.Industry == s.Industry && c.Show)
        }).ToList();

        return result;
    }

    public EmptyResponse UpdateCollectPost(UpdateCollectPost model)
    {
        var lockerKey = $"st:follow:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var result = new EmptyResponse();

        if (string.IsNullOrWhiteSpace(model.TeamPostId))
            throw new BadRequestException("缺少职位Id");

        var collects = _context.User_Collect_Post.Where(x => x.SeekerId == _user.Id
        && x.TeamPostId == model.TeamPostId).ToList();

        if (model.Collect)
        {
            if (collects.Count > 0)
                return result;

            var teamPost = _context.Post_Team.Where(x => x.TeamPostId == model.TeamPostId)
            .Select(s => new
            {
                s.Project_Team.HrId
            }).FirstOrDefault();

            if (teamPost == null)
                return result;

            var collect = new User_Collect_Post
            {
                HrId = teamPost.HrId,
                SeekerId = _user.Id,
                TeamPostId = model.TeamPostId
            };
            _context.Add(collect);
        }
        else
        {
            if (collects.Count == 0)
                return result;

            _context.RemoveRange(collects);
        }
        _context.SaveChanges();

        return result;
    }

    public EmptyResponse UpdateFollowIndustry(UpdateFollowIndustry model)
    {
        var lockerKey = $"st:follow:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var result = new EmptyResponse();

        if (!_context.User_Hr.Any(x => x.UserId == model.AdviserId))
            throw new BadRequestException("顾问Id无效");

        if (!model.Industry.HasValue)
            throw new BadRequestException("缺少行业Id");

        var follows = _context.User_Follow_Industry.Where(x => x.SeekerId == _user.Id
        && x.HrId == model.AdviserId && x.Industry == model.Industry).ToList();

        if (model.Follow)
        {
            if (follows.Count > 0)
                return result;

            var follow = new User_Follow_Industry
            {
                HrId = model.AdviserId!,
                SeekerId = _user.Id,
                Industry = model.Industry.Value
            };
            _context.Add(follow);
        }
        else
        {
            if (follows.Count == 0)
                return result;

            _context.RemoveRange(follows);
        }
        _context.SaveChanges();

        return result;
    }

    public SeekerGetAdviserInfo SeekerGetAdviser(string id)
    {
        if (id.StartsWith(Constants.ImHrIdPre) == true)
            id = id.TrimStart(Constants.ImHrIdPre.ToCharArray());

        var result = _context.User_Hr.Where(x => x.UserId == id)
        .Select(s => new SeekerGetAdviserInfo
        {
            Address = s.Address,
            Avatar = s.Avatar,
            Describe = s.Describe,
            EMail = s.EMail,
            EntWeChatQrCode = s.EntWeChatQrCode,
            Mobile = s.User.Mobile,
            Name = s.NickName,
            Post = s.Post,
            RegionId = s.RegionId,
            AdviserId = s.UserId,
            WeChatNo = s.WeChatNo,
            InviteCode = s.InviteCode,
            Sex = s.Sex,
            Lat = s.Location.X,
            Lng = s.Location.Y,
            EntAbbr = s.Enterprise.Abbreviation,
            EntName = s.Enterprise.Name,
            LoginTime = s.User_Extend.LoginTime,
            Talent = s.User_Num.Talent,
            VirtualTalent = s.User_Num.VirtualTalent,
            Authentication = true,//!string.IsNullOrEmpty(s.User.IdentityCard),
            IsCurrent = id == _user.AdviserId,
            DataScore = s.Score,
            Likes = s.User_Num.Likes,
            TencentImId = s.TencentImId
        }).FirstOrDefault();

        if (result == null)
            throw new NotFoundException("内容不存在");

        if (!string.IsNullOrWhiteSpace(result.RegionId))
            result.City = _commonDicService.GetCityById(result.RegionId);

        // result.Mobile = Tools.MobilToX(result.Mobile);

        result.OnlineStatus = Tools.GetOnlineStatus(result.LoginTime);

        result.DataGrade = Tools.CountGradeByScore(result.DataScore);

        result.ActivityScore = Tools.GetOnlineScore(result.LoginTime);
        result.ActivityGrade = Tools.CountGradeByScore(result.ActivityScore);

        result.Complaint = 0;
        result.ComplaintGrade = "S";

        // result.Review = 100;
        // result.ReviewGrade = "S";

        if (id == Constants.PlatformHrId)
            result.Mobile = Constants.PlatformMobile;

        result.Grade = Tools.GetHrGrade(result.DataScore, result.LoginTime);

        result.TalentAvatars = _commonCacheService.GetRandomTalentAvatars(id);

        var totalRank = MyRedis.Client.ZCard(RedisKey.HrRank);
        var rank = MyRedis.Client.ZRevRank(RedisKey.HrRank, id);
        rank += 1;

        if (rank > 0 && totalRank > 0)
        {
            rank = rank > totalRank ? totalRank : rank;
            result.RankingThen = (int)((1 - (rank / (decimal)totalRank)) * 100);
        }

        return result;
    }

    public EmptyResponse UpdateSeekerGuidance(UpdateSeekerGuidance model)
    {
        var lockerKey = $"st:upguidce:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var seekerGuidance = _context.User_Seeker_Guidance
        .FirstOrDefault(x => x.SeekerId == _user.Id && x.AdviserId == _user.AdviserId);
        if (seekerGuidance == null)
        {
            seekerGuidance = new User_Seeker_Guidance
            {
                AdviserId = _user.AdviserId,
                SeekerId = _user.Id
            };
            _context.Add(seekerGuidance);
        }

        if (model.FreeTimeType.HasValue)
            seekerGuidance.FreeTimeType = model.FreeTimeType;

        if (model.FreeTime.HasValue)
            seekerGuidance.FreeTime = model.FreeTime;

        if (model.CanPhone.HasValue)
            seekerGuidance.CanPhone = Convert.ToBoolean(model.CanPhone.Value);

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public GetSeekerGuidanceResponse GetSeekerGuidance()
    {
        var result = _context.User_Seeker_Guidance
        .Where(x => x.SeekerId == _user.Id && x.AdviserId == _user.AdviserId)
        .Select(s => new GetSeekerGuidanceResponse
        {
            CanPhone = s.CanPhone.HasValue ? Convert.ToInt32(s.CanPhone.Value) : null,
            FreeTime = s.FreeTime,
            FreeTimeType = s.FreeTimeType
        }).FirstOrDefault();

        result = result ?? new GetSeekerGuidanceResponse();

        return result;
    }

    public async Task<SeekerLoginResponse> SeekerLogin(SeekerLogin model)
    {
        var lkw = model.Type == ClientType.SeekerWeb ? model.Mobile : model.Code;
        lkw = lkw?.Trim();
        var lockerKey = $"sklogin:{Md5Helper.Md5((int)model.Type + "_" + lkw)}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey) ?? throw new BadRequestException("操作频繁");

        AppClientInfo? applet = null;

        if (model.Type != ClientType.SeekerWeb)
        {
            if (string.IsNullOrWhiteSpace(model.AppId))
            {
                applet = model.Type switch
                {
                    ClientType.SeekerApplet => ClientApps.SeekerApplet,
                    ClientType.SeekerKsApplet => ClientApps.KsAppletSeeker,
                    ClientType.InterviewerApplet => ClientApps.InterviewApplet,
                    _ => throw new BadRequestException("非法请求")
                };
            }
            else
            {
                var apps = _commonDicService.GetAppIds();

                applet = apps.Where(x => x.Type == model.Type && x.AppId == model.AppId)
                .Select(s => new AppClientInfo
                {
                    AppID = s.AppId,
                    AppSecret = s.AppSecret,
                    Type = s.Type
                }).FirstOrDefault();
            }

            if (applet == null)
                throw new BadRequestException("非法请求");
        }

        User? user = null;
        var mobile = string.Empty;
        var unionid = string.Empty;
        var openid = string.Empty;

        //微信小程序登录
        if (model.Type == ClientType.SeekerApplet || model.Type == ClientType.InterviewerApplet || model.Type == ClientType.JobMarketForSeeker)
        {
            if (string.IsNullOrEmpty(model.Code) || string.IsNullOrEmpty(model.EncryptedData) || string.IsNullOrEmpty(model.Iv))
                throw new BadRequestException("缺少小程序登录信息");

            GetAppletPhoneResponse? wechat = null;
            try
            {
                var wechatCkey = $"wechat:{Md5Helper.Md5(applet.AppID + model.Code!)}";
                var disable = 600;

                wechat = await _cacheHelper.GetRedisCacheAsync<GetAppletPhoneResponse>(async () =>
                {
                    var obj = await _weChatHelper.GetAppletPhone(applet.AppID, applet.AppSecret, model.Code, model.EncryptedData!, model.Iv!);
                    return obj;
                }, wechatCkey, disable);
            }
            catch (ErrorJsonResultException e)
            {
                if (e.JsonResult.errcode == Senparc.Weixin.ReturnCode.不合法的oauth_code || e.JsonResult.errcode == Senparc.Weixin.ReturnCode.oauth_code超时)
                    throw new BadRequestException("微信授权过期，请重新授权");
                else
                    throw;
            }
            catch (Exception e)
            {
                _log.Error("登录微信授权失败", e.Message, JsonSerializer.SerializeToString(model));
            }

            if (string.IsNullOrWhiteSpace(wechat?.WeId.unionid))
                throw new Exception("未能拿到微信unionid");

            mobile = wechat?.Phone.purePhoneNumber;
            unionid = wechat?.WeId.unionid;
            openid = wechat?.WeId.openid;

            if (wechat == null || string.IsNullOrEmpty(mobile))
                throw new BadRequestException("微信授权失败");
        }
        else if (model.Type == ClientType.SeekerKsApplet)
        {
            //快手小程序登录
            if (string.IsNullOrEmpty(model.Code) || string.IsNullOrEmpty(model.EncryptedData) || string.IsNullOrEmpty(model.Iv))
                throw new BadRequestException("缺少小程序登录信息");

            GetKsAppletPhoneResponse? ksInfo = null;
            try
            {
                var ksCkey = $"kuaishou:{Md5Helper.Md5(applet.AppID + model.Code!)}";
                var disable = 600;

                ksInfo = await _cacheHelper.GetRedisCacheAsync<GetKsAppletPhoneResponse>(async () =>
                {
                    var obj = await _kuaiShouHelper.GetAppletPhone(applet.AppID, applet.AppSecret, model.Code, model.EncryptedData!, model.Iv!);
                    return obj;
                }, ksCkey, disable);
            }
            catch (Exception e)
            {
                _log.Error("快手授权失败", e.Message, JsonSerializer.SerializeToString(model));
            }

            if (string.IsNullOrWhiteSpace(ksInfo?.open_id))
                throw new Exception("未能拿到快手open_id");

            mobile = ksInfo?.phoneNumber;
            openid = ksInfo?.open_id;
            unionid = ksInfo?.open_id;

            if (ksInfo == null || string.IsNullOrEmpty(mobile))
                throw new BadRequestException("快手授权失败");
        }
        else if (model.Type == ClientType.SeekerDyApplet)
        {
            //快手小程序登录
            if (string.IsNullOrEmpty(model.Code) || string.IsNullOrEmpty(model.EncryptedData) || string.IsNullOrEmpty(model.Iv))
                throw new BadRequestException("缺少小程序登录信息");

            GetDyAppletPhone? dyInfo = null;
            try
            {
                var ksCkey = $"douyin:{Md5Helper.Md5(applet.AppID + model.Code!)}";
                var disable = 600;

                dyInfo = await _cacheHelper.GetRedisCacheAsync<GetDyAppletPhone>(async () =>
                {
                    var obj = await _douYinHelper.GetAppletPhone(applet.AppID, applet.AppSecret, model.Code, null, model.EncryptedData!, model.Iv!);
                    return obj;
                }, ksCkey, disable);
            }
            catch (Exception e)
            {
                _log.Error("抖音授权失败", e.Message, JsonSerializer.SerializeToString(model));
            }

            if (string.IsNullOrWhiteSpace(dyInfo?.openid))
                throw new Exception("未能拿到抖音open_id");

            mobile = dyInfo?.purePhoneNumber;
            openid = dyInfo?.openid;
            unionid = dyInfo?.unionid;

            if (dyInfo == null || string.IsNullOrEmpty(mobile))
                throw new BadRequestException("抖音授权失败");
        }
        else if (model.Type == ClientType.SeekerWeb)
        {
            //快手小程序登录
            if (string.IsNullOrEmpty(model.Code) || string.IsNullOrEmpty(model.Mobile))
                throw new BadRequestException("缺少手机号或验证码");

            //验证短信验证码
            var checkResult = _sharedService.CheckSMSCode(new CheckSMSCode
            {
                Phone = model.Mobile,
                SMSCode = model.Code,
                Type = SMSType.LoginOrRegister
            });

            if (checkResult.Result)
            {
                mobile = model.Mobile;
                user = _context.User.Include(i => i.User_Extend).FirstOrDefault(x => x.Mobile == model.Mobile);
            }
            else
                throw new BadRequestException("验证码错误");
        }
        else
            throw new BadRequestException("不支持的登录类型");

        User_OpenId? userOpenId = null;
        if (model.Type != ClientType.SeekerWeb)
        {
            userOpenId = _context.User_OpenId.Where(x => x.AppId == applet.AppID && x.OpenId == openid)
            .Include(i => i.User).ThenInclude(i => i.User_Extend)
            .FirstOrDefault();

            user = userOpenId?.User;

            //如果用户不存在，手机号登录
            if (user == null)
            {
                user = _context.User.Include(i => i.User_Extend).Where(x => x.Mobile == mobile).FirstOrDefault();

                //如果手机号存在并且和原有wechatid不同
                if (user != null && _context.User_OpenId.Any(x => x.AppId == applet.AppID && x.UserId == user.UserId))
                    throw new BadRequestException("该手机号已绑定其他账号，请更换手机号或使用其他账号登录");
            }
        }

        var userExt = user?.User_Extend;

        // var newSeeker = false;

        //如果用户不存在，创建用户
        if (user == null)
        {
            user = new User
            {
                Mobile = mobile
            };
            userExt = user.User_Extend = new User_Extend
            {
                UserId = user.UserId,
                RegistrationIp = _user.RequestIpAddress ?? string.Empty
            };
            _context.Add(user);

            user.User_Num = new User_Num
            {
                UserId = user.UserId
            };
            _context.Add(user);

            var userBalance = new User_Balance
            {
                UserId = user.UserId
            };
            _context.Add(userBalance);
        }

        var seeker = _context.User_Seeker.FirstOrDefault(x => x.UserId == user.UserId);

        //如果求职者不存在，创建求职者
        if (seeker == null)
        {
            if (model.Type == ClientType.SeekerApplet || model.Type == ClientType.InterviewerApplet)
            {
                //新用户取一下公众号关注信息
                var wesub = _context.WeChat.FirstOrDefault(x => x.WeChatId == unionid);

                if (wesub != null)
                {
                    // 检查 WeChatH5OpenId 是否已存在
                    var existingUser = _context.User.Any(x => x.WeChatH5OpenId == wesub.WeChatH5OpenId);
                    if (!existingUser)
                    {
                        user.WeChatH5Subscribe = wesub.WeChatH5Subscribe == true;
                        user.WeChatH5OpenId = wesub.WeChatH5OpenId;
                    }
                }
            }

            seeker = new User_Seeker
            {
                Source = model.Type switch
                {
                    ClientType.SeekerApplet => RegisterSource.Applet,
                    ClientType.InterviewerApplet => RegisterSource.Interviewer,
                    ClientType.SeekerKsApplet => RegisterSource.KsApplet,
                    ClientType.JobMarketForSeeker => RegisterSource.JobMarket,
                    ClientType.SeekerWeb => RegisterSource.Web,
                    ClientType.SeekerDyApplet => RegisterSource.SeekerDyApplet,
                    _ => RegisterSource.Other
                },
                Status = UserStatus.Active,
                UserId = user.UserId,
                ChannelSource = _user.ChannelSource
            };

            if (seeker.Source == RegisterSource.Web && Tools.IsDyClient(_user.ClientId))
                seeker.Source = RegisterSource.SeekerDyApplet;

            _context.Add(seeker);

            //创seeker简历表
            var resume = new User_Resume
            {
                UserId = user.UserId
            };
            _context.Add(resume);

            // newSeeker = true;
        }

        if (userOpenId == null && model.Type != ClientType.SeekerWeb)
        {
            userOpenId = new User_OpenId
            {
                UserId = user.UserId,
                AppId = applet.AppID,
                Type = model.Type,
                OpenId = openid!,
                UnionId = unionid!
            };
            _context.Add(userOpenId);
        }

        userExt!.LoginTime = DateTime.Now;

        _context.SaveChanges();

        _commonUserService.UserActivity(seeker.UserId, model.Type, _user.RequestIpAddress);

        var result = new SeekerLoginResponse();

        // if (newSeeker)
        // {
        //     //求职者注册消息
        //     MyRedis.Client.RPush(SubscriptionKey.SeekerRegister, seeker.UserId);

        //     //计算简历完善度
        //     _commonUserService.CountResumeScore(seeker.UserId);

        //     //诺聘静默注册
        //     _ = Task.Run(() => _nuoPinApi.SilentRegistration(user.Mobile));
        // }

        var token = _commonService.CreateRefreshToken(user.UserId, TokenType.用户端, model.Type);

        result.AdviserId = _user.AdviserId;
        result.AccessToken = token.AccessToken;
        result.RefreshToken = token.RefreshToken;
        result.TokenExpiresTime = token.TokenExpiresTime;

        _ = Task.Run(() => _commonUserService.SetAdviser(seeker.UserId, _user.AdviserId, channelId: _user.ChannelId));

        return result;
    }

    public async Task<TokenInfo> SeekerRefreshToken(SeekerRefreshToken model)
    {
        var token = await _commonService.RefreshToken(model.RefreshToken!);

        if (token == null)
            throw new UnauthorizedException(string.Empty);

        var result = new TokenInfo
        {
            AccessToken = token.AccessToken,
            RefreshToken = token.RefreshToken,
            TokenExpiresTime = token.TokenExpiresTime
        };

        return result;
    }

    public async Task<GetImUserSigResponse> GetImUserSig()
    {
        var lockerKey = $"lk:imsig:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new NotAcceptableException(string.Empty);

        var result = await _commonUserService.GetSeekerImUserSig(_user.Id);

        return result;
    }

    public CheckSeekerTokenForNykResponse CheckSeekerTokenForNyk(string? token)
    {
        if (string.IsNullOrWhiteSpace(token))
            throw new UnauthorizedException(string.Empty);

        var exp = DateTime.Now.AddMinutes(-5);
        var at = _context.Token_Access
        .Where(x => x.Token == token && x.ExpirationTime > exp)
        .Select(s => new
        {
            s.Type,
            s.UserId
        }).FirstOrDefault();

        if (at == null || at.Type != TokenType.用户端)
            throw new UnauthorizedException(string.Empty);

        var result = _context.User_Seeker.Where(x => x.UserId == at.UserId)
        .Select(s => new CheckSeekerTokenForNykResponse
        {
            LoginName = s.User.Mobile,
            PK_UAID = s.UserId,
            PK_UID = s.UserId,
            User = new CheckSeekerTokenForNykUser
            {
                Birthday = s.User_Resume.Birthday,
                HeadPortrait = s.Avatar,
                Name = s.NickName,
                Sex = s.User_Resume.Sex
            }
        }).First();

        return result;
    }

    public async Task<GetNykRequestNoResponse> GetNykRequestNo(GetNykRequestNo model)
    {
        var resul = await _nuoYouKaoApi.GetNykRequestNo(model);
        return resul;
    }

    public GetBalanceResponse GetBalance(GetBalance model)
    {
        var balance = _context.User_Balance.First(x => x.UserId == _user.Id);

        var todayBalance = MyRedis.Client.HGet<decimal>(RedisKey.Balance.TodayBalance, $"{DateTime.Now.ToYYYY_MM_DD()}_{_user.Id}");

        var result = new GetBalanceResponse
        {
            Balance = balance.SeekerBalance,
            BalanceFull = balance.SeekerBalanceFull,
            TodayBalance = todayBalance
        };

        return result;
    }

    public GetBalanceRecordResponse GetBalanceRecord(GetBalanceRecord model)
    {
        var result = new GetBalanceRecordResponse();

        var predicate = PredicateBuilder.New<User_Balance_Record>(x => x.UserId == _user.Id && x.UserType == SeekerOrHr.Seeker);

        if (model.Month.HasValue)
        {
            var bg = new DateTime(model.Month.Value.Date.Year, model.Month.Value.Date.Month, 1);
            var ed = bg.AddMonths(1);
            predicate = predicate.And(x => x.EventTime >= bg && x.EventTime < ed);
        }

        result.Rows = _context.User_Balance_Record.Where(predicate)
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new BalanceRecordInfo
        {
            Amount = s.Amount,
            Content = s.Content,
            EventTime = s.EventTime,
            Increment = s.Increment,
            Type = s.Type,
            UserType = s.UserType
        }).ToList();

        if (result.Rows.Count >= model.PageSize)
            result.IsLast = false;

        return result;
    }

    public async Task<WithdrawResponse> Withdraw(Withdraw model)
    {
        await Task.FromResult(1);
        throw new BadRequestException("该功能暂未启用");

        // var result = new WithdrawResponse();

        // if (model.Amount < 1)
        //     throw new BadRequestException("提现最低1元");

        // if (model.Amount > 100)
        //     throw new BadRequestException("单笔最多100元");

        // if (_context.User_Seeker.Any(x => x.UserId == _user.Id && x.Status != UserStatus.Active))
        //     throw new BadRequestException("禁止提现，请联系客服人员");

        // //锁用户钱包
        // var lockerKey = $"{RedisKey.Balance.LockKey}{_user.Id}";
        // using var locker = await MyRedis.Lock(lockerKey, 15);

        // if (locker == null)
        //     throw new Exception("提现获取钱包锁失败");

        // //测试环境改为最低值
        // if (!_hostingEnvironment.IsProduction())
        //     model.Amount = 0.3M;

        // var now = DateTime.Today;
        // var todayWithraw = _context.User_Withdraw
        //     .Any(x => x.UserId == _user.Id && x.UserType == SeekerOrHr.Seeker && x.CreatedTime >= now
        //      && (x.Status == WithdrawStatus.提现中 || x.Status == WithdrawStatus.审核中 ||
        //      x.Status == WithdrawStatus.提现完成 || x.Status == WithdrawStatus.提现失败));
        // if (todayWithraw)
        //     throw new BadRequestException("今日已提现，明天再来试试吧！");

        // var user = _context.User_Seeker.Where(x => x.UserId == _user.Id).First();

        // if (string.IsNullOrWhiteSpace(user.WeChatAppletId))
        //     throw new BadRequestException("请前往小程序授权提现");

        // var balance = _context.User_Balance.Where(x => x.UserId == _user.Id).First();
        // if (balance.SeekerBalance < model.Amount)
        //     throw new BadRequestException("余额不足");

        // balance.SeekerBalance -= model.Amount;

        // var withdraw = new User_Withdraw
        // {
        //     Amount = model.Amount,
        //     UserId = _user.Id,
        //     AccountName = user.WeChatAppletId,
        //     Account = user.WeChatAppletId,
        //     ApprovalTime = DateTime.Now,
        //     Approver = string.Empty,
        //     Status = WithdrawStatus.提现中,
        //     Type = 0,
        //     UserType = SeekerOrHr.Seeker
        // };
        // _context.Add(withdraw);

        // var balanceRecord = new User_Balance_Record
        // {
        //     Amount = balance.SeekerBalance,
        //     Increment = model.Amount * -1,
        //     UserType = SeekerOrHr.Seeker,
        //     Type = BalanceRecordType.提现,
        //     Content = "提现",
        //     UserId = _user.Id
        // };
        // _context.Add(balanceRecord);

        // _context.SaveChanges();

        // var wdrt = false;

        // //去提现
        // try
        // {
        //     await _commonWithdraw.WechatWithdraw(WeChatApps.SeekerApplet.AppID, user.WeChatAppletId, model.Amount, withdraw.Id);
        //     wdrt = true;
        // }
        // catch (ThirdPartyServerException e)
        // {
        //     withdraw.Remark = e.Message;
        // }
        // catch (Exception e)
        // {
        //     _log.Error("提现失败", Tools.GetErrMsg(e), model);
        // }

        // //提现完成
        // if (wdrt)
        // {
        //     withdraw.Status = WithdrawStatus.提现完成;
        //     withdraw.WithdrawTime = DateTime.Now;
        //     balance.SeekerWithdrawTime = DateTime.Now;
        // }
        // else
        // {
        //     withdraw.Status = WithdrawStatus.提现失败;
        // }
        // _context.SaveChanges();

        // if (!wdrt)
        //     throw new BadRequestException("提现失败，请联系客服人员");

        // return result;
    }

    public EmptyResponse SeekerAppShow(SeekerAppShow model)
    {
        //如果有渠道Id
        if (!string.IsNullOrWhiteSpace(model.ChannelId))
        {
            var cacheKey = Md5Helper.Md5($"{model.ChannelId}_{_user.RequestIpAddress}_{_user.UserAgent}");
            //如果是首次
            if (MyRedis.Client.SetNx($"st:appshow:{cacheKey}", DateTime.Now.ToYYYY_MM_DD_HH(), TimeSpan.FromHours(1)))
            {
                //检测channelid
                var checkChannelId = _context.Qd_Hr_Channel.Any(x => x.Id == model.ChannelId);
                if (checkChannelId)
                    MyRedis.Client.HIncrBy($"{RedisKey.AppShow.ChannelAppShow}{model.ChannelId}", DateTime.Now.ToYYYY_MM_DD(), 1);
            }
        }

        return new EmptyResponse();
    }

    public async Task<SeekerLoginResponse> SeekerLogin2(SeekerLoginForDy key)
    {
        if (key.Code != "kachsalh123")
            throw new BadRequestException("非法操作");

        var result = new SeekerLoginResponse();

        await Task.FromResult(1);

        var token = _commonService.CreateRefreshToken("154370362724200965", TokenType.用户端, ClientType.SeekerDyApplet);

        result.AdviserId = _user.AdviserId;
        result.AccessToken = token.AccessToken;
        result.RefreshToken = token.RefreshToken;
        result.TokenExpiresTime = token.TokenExpiresTime;

        return result;
    }

    public GetKzgResumeResponse GetKzgResume(GetKzgResume model)
    {
        model = model ?? new GetKzgResume();
        var result = Tools.ModelConvert<GetKzgResumeResponse>(_commonService.GetResume(_user.Id, false));
        if (string.IsNullOrWhiteSpace(model.KsId))
            return result!;

        var ksInfo = _context.Kuaishou_Talent_Infos.Where(x => x.ApplicationId == model.KsId)
        .FirstOrDefault();
        result.KsName = ksInfo?.Name;

        if (!result.Age.HasValue)
            result.Age = ksInfo?.Age;

        if (!result.Sex.HasValue)
            result.Sex = ksInfo?.GenderCode switch { 2 => Sex.女, 1 => Sex.男, _ => null };

        if (!result.Education.HasValue && !string.IsNullOrWhiteSpace(ksInfo?.DataDynamicResumeInfo?.HighestDegree))
        {
            var baseInfo = _context.Dic_Base_Info.FirstOrDefault(f => f.Type == "kgz_nkp_education_enum_map" && f.Code == ksInfo.DataDynamicResumeInfo.HighestDegree);

            if (Enum.TryParse<EducationType>(baseInfo.Value, out var aoe))
                result.Education = aoe;
        }
        return result!;
    }
}