// using Config.CommonModel;

// namespace Staffing.Model.Common.NScore;

// public class AddScore
// {
//     /// <summary>
//     /// 积分类型
//     /// </summary>
//     public NScoreType? ScoreType { get; set; }

//     /// <summary>
//     /// c端分享人
//     /// </summary>
//     public string? ShareSeeker { get; set; }

//     /// <summary>
//     /// b端分享人
//     /// </summary>
//     public string? ShareHr { get; set; }
// }

// public class AddScoreResponse
// {
//     /// <summary>
//     /// 诺积分
//     /// </summary>
//     public int NScore { get; set; }

//     /// <summary>
//     /// 积分类型
//     /// </summary>
//     public NScoreType? ScoreType { get; set; }

//     /// <summary>
//     /// 今日次数
//     /// </summary>
//     public int TimesToday { get; set; }
// }