﻿// using System.Data.Entity;
// using Config.Enums;
// using Config.CommonModel;
// using Infrastructure.Common;
// using Infrastructure.Exceptions;
// using Infrastructure.Extend;
// using LinqKit;
// using Microsoft.Extensions.DependencyInjection;
// using Microsoft.Extensions.Hosting;
// using Microsoft.Extensions.Options;
// using Staffing.Entity;
// using Staffing.Entity.Staffing;
// using Staffing.Core.Interfaces.CentreService;
// using Config;
// using Staffing.Model.CentreService.NScore;

// namespace Staffing.Core.Services.CentreService;

// [Service(ServiceLifetime.Transient)]
// public class NScoreService : INScoreService
// {
//     private readonly StaffingContext _context;
//     private RequestContext _user;
//     private ConfigManager _config;
//     private readonly IHostEnvironment _hostingEnvironment;
//     private readonly LogManager _log;
//     public NScoreService(StaffingContext context, RequestContext user,
//         IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
//         LogManager log)
//     {
//         _user = user;
//         _context = context;
//         _config = config.Value;
//         _hostingEnvironment = hostingEnvironment;
//         _log = log;
//     }

//     public GetOrdersResponse GetOrders(GetOrders model)
//     {
//         var result = new GetOrdersResponse();

//         var predicate = PredicateBuilder.New<User_Nscore_Order>(x => x.User_Nscore_Goods.UserType == model.UserType);

//         if (model.Status.HasValue)
//             predicate = predicate.And(x => x.Status == model.Status);

//         if (!string.IsNullOrWhiteSpace(model.GoodsName))
//             predicate = predicate.And(x => x.User_Nscore_Goods.Name.Contains(model.GoodsName));

//         if (!string.IsNullOrWhiteSpace(model.UserMobile))
//             predicate = predicate.And(x => x.User_Seeker.User.Mobile == model.UserMobile);

//         if (model.BeginTime.HasValue)
//             predicate = predicate.And(x => x.CreatedTime >= model.BeginTime.Value);

//         if (model.EndTime.HasValue)
//         {
//             model.EndTime = model.EndTime.Value.Date.AddDays(1);
//             predicate = predicate.And(x => x.CreatedTime < model.EndTime.Value);
//         }

//         var sql = _context.User_Nscore_Order.Where(predicate);

//         result.Total = sql.Count();

//         result.Rows = sql
//         .OrderByDescending(x => x.CreatedTime)
//         .Skip((model.PageIndex - 1) * model.PageSize)
//         .Take(model.PageSize)
//         .Select(s => new OrderInfo
//         {
//             UserMobile = s.User_Seeker.User.Mobile,
//             UserName = s.User_Seeker.NickName,
//             Id = s.Id,
//             GoodsName = s.Name,
//             Score = s.Score,
//             Status = s.Status,
//             StatusName = s.Status.GetDescription(),
//             Thumbnail = s.User_Nscore_Goods.Content.Thumbnail,
//             CreatedTime = s.CreatedTime,
//             DeliveryTime = s.DeliveryTime,
//             Express = s.Express,
//             ExpressNo = s.ExpressNo,
//             Address = s.Address
//         }).ToList();

//         return result;
//     }

//     public SendOrdersResponse SendOrders(SendOrders model)
//     {
//         var result = new SendOrdersResponse();

//         var order = _context.User_Nscore_Order.FirstOrDefault(x => x.Id == model.OrderId);

//         if (order == null)
//             throw new BadRequestException("内容不存在");

//         order.Status = NScoreOrderStatus.已发货;
//         order.Express = model.Express;
//         order.ExpressNo = model.ExpressNo;

//         _context.SaveChanges();

//         return result;
//     }

//     public GetGoodsListResponse GetGoodsList(GetGoodsList model)
//     {
//         var predicate = PredicateBuilder.New<User_Nscore_Goods>(x => x.UserType == model.UserType);

//         if (model.Type.HasValue)
//             predicate = predicate.And(x => x.Type == model.Type);

//         if (model.Active.HasValue)
//             predicate = predicate.And(x => x.Active == Convert.ToBoolean(model.Active.Value));

//         if (model.MinStock.HasValue)
//             predicate = predicate.And(x => x.Stock >= model.MinStock);

//         if (model.MaxStock.HasValue)
//             predicate = predicate.And(x => x.Stock <= model.MaxStock);

//         if (!string.IsNullOrWhiteSpace(model.GoodsName))
//             predicate = predicate.And(x => x.Name.Contains(model.GoodsName));

//         var result = new GetGoodsListResponse();

//         var sql = _context.User_Nscore_Goods.Where(predicate);

//         result.Total = sql.Count();

//         result.Rows = sql
//         .OrderByDescending(x => x.Active)
//         .ThenByDescending(x => x.Type)
//         .ThenByDescending(x => x.CreatedTime)
//         .Skip((model.PageIndex - 1) * model.PageSize)
//         .Take(model.PageSize)
//         .Select(s => new GoodsInfo
//         {
//             Id = s.Id,
//             Money = s.Money,
//             Name = s.Name,
//             Describe = s.Describe,
//             Score = s.Score,
//             Thumbnail = s.Content.Thumbnail,
//             Type = s.Type,
//             TypeName = s.Type.GetDescription(),
//             Active = s.Active,
//             Stock = s.Stock,
//             UpdatedTime = s.UpdatedTime
//         }).ToList();

//         return result;
//     }

//     public UpdateGoodsResponse UpdateGoods(UpdateGoods model)
//     {
//         User_Nscore_Goods? goods = null;

//         if (!model.Type.HasValue)
//             throw new BadRequestException("商品类型不能为空");

//         if (!string.IsNullOrWhiteSpace(model.Id))
//         {
//             goods = _context.User_Nscore_Goods.FirstOrDefault(x => x.Id == model.Id);
//             if (goods == null)
//                 throw new BadRequestException("商品不存在");
//         }
//         else
//         {
//             goods = new User_Nscore_Goods
//             {
//                 UserType = model.UserType,
//                 Active = true,
//                 Type = model.Type.Value
//             };
//             _context.Add(goods);
//         }

//         if (string.IsNullOrWhiteSpace(model.Name))
//             throw new BadRequestException("商品名称不能为空");

//         if (model.Score <= 0)
//             throw new BadRequestException("积分无效");

//         if (string.IsNullOrWhiteSpace(model.Name))
//             throw new BadRequestException("商品名称不能为空");

//         if (model.Money < 0)
//             throw new BadRequestException("商品价值无效");

//         if (model.Stock < 0)
//             throw new BadRequestException("库存无效");

//         if (model.Type == NScoreGoodsType.实物 && string.IsNullOrWhiteSpace(model.Thumbnail))
//             throw new BadRequestException("商品图片不能为空");

//         if (model.Type == NScoreGoodsType.现金 && model.Money <= 0)
//             throw new BadRequestException("商品价值无效");

//         goods.UpdatedBy = _user.NuoPinId;
//         goods.UpdatedTime = DateTime.Now;
//         goods.Name = model.Name;
//         goods.Describe = model.Describe;
//         goods.Score = model.Score;
//         goods.Money = model.Money;
//         goods.Stock = model.Stock;
//         goods.Content.Thumbnail = model.Thumbnail;

//         _context.SaveChanges();

//         var result = new UpdateGoodsResponse();

//         result.Id = goods.Id;

//         return result;
//     }

//     public SetGoodsResponse SetGoods(SetGoods model)
//     {
//         var goods = _context.User_Nscore_Goods.FirstOrDefault(x => x.Id == model.Id);

//         if (goods == null)
//             throw new BadRequestException("商品不存在");

//         if (model.Active.HasValue)
//             goods.Active = model.Active.Value;

//         _context.SaveChanges();

//         return new SetGoodsResponse();
//     }

//     public GetNScoresResponse GetNScores(GetNScores model)
//     {
//         var result = new GetNScoresResponse();

//         var config = Tools.GetNScoreCfg(model.UserType);

//         if (config == null)
//             config = Tools.ModelConvert<List<NScoresStoreInfo>>(Constants.NScore.DefaultNScore)
//             .Where(x => x.UserType == model.UserType).ToList();

//         var curtType = config.Select(s => s.Type).ToList();

//         var ade = Constants.NScore.DefaultNScore.Where(x => x.UserType == model.UserType && !curtType.Contains(x.Type));
//         if (ade.Count() > 0)
//             config.AddRange(ade);

//         result.Rows = Tools.ModelConvert<List<NScoresInfo>>(config);

//         // result.Rows = result.Rows.Where(x => x.UserType == model.UserType).ToList();

//         result.Rows.ForEach(x =>
//         {
//             x.TypeName = x.Type.GetDescription();
//         });

//         if (!string.IsNullOrWhiteSpace(model.TypeName))
//             result.Rows = result.Rows.Where(x => x.TypeName?.Contains(model.TypeName) == true).ToList();

//         return result;
//     }

//     public SetNScoreResponse SetNScore(SetNScore model)
//     {

//         if (model.Score < 0)
//             throw new BadRequestException("积分无效");

//         var rdsk = $"{RedisKey.NScore.Key}{RedisKey.NScore.ScoreConfig}";
//         var config = MyRedis.Client.Get<List<NScoresStoreInfo>?>(rdsk);

//         if (config == null)
//             config = Tools.ModelConvert<List<NScoresStoreInfo>>(Constants.NScore.DefaultNScore);

//         var cur = config.FirstOrDefault(x => x.UserType == model.UserType && x.Type == model.Type);
//         if (cur == null)
//         {
//             cur = Constants.NScore.DefaultNScore.FirstOrDefault(x => x.Type == model.Type && x.UserType == model.UserType);
//             if (cur != null)
//                 config.Add(cur);
//         }

//         if (cur == null)
//             throw new BadRequestException("内容无效");

//         if (model.Score.HasValue)
//             cur.Score = model.Score.Value;

//         MyRedis.Client.Set(rdsk, config);

//         return new SetNScoreResponse();
//     }

//     public GetScoreRecordResponse GetScoreRecord(GetScoreRecord model)
//     {
//         var result = new GetScoreRecordResponse();

//         var predicate = PredicateBuilder.New<User_NScore_Record>(x => x.UserType == model.UserType);

//         if (model.Type.HasValue)
//             predicate = predicate.And(x => x.Type == model.Type);

//         if (!string.IsNullOrWhiteSpace(model.UserName))
//             predicate = predicate.And(x => x.User_Seeker.NickName.Contains(model.UserName));

//         if (!string.IsNullOrWhiteSpace(model.Mobile))
//             predicate = predicate.And(x => x.User_Seeker.User.Mobile == model.Mobile);

//         if (model.BeginTime.HasValue)
//             predicate = predicate.And(x => x.EventTime >= model.BeginTime.Value);

//         if (model.EndTime.HasValue)
//         {
//             model.EndTime = model.EndTime.Value.Date.AddDays(1);
//             predicate = predicate.And(x => x.EventTime < model.EndTime.Value);
//         }

//         var sql = _context.User_NScore_Record.Where(predicate);

//         result.Total = sql.Count();

//         result.Rows = sql
//         .OrderByDescending(x => x.CreatedTime)
//         .Skip((model.PageIndex - 1) * model.PageSize)
//         .Take(model.PageSize)
//         .Select(s => new ScoreRecordInfo
//         {
//             Amount = s.Amount,
//             Content = s.Content,
//             EventTime = s.EventTime,
//             Increment = s.Increment,
//             Type = s.Type,
//             TypeName = s.Type.GetDescription(),
//             UserType = s.UserType,
//             Mobile = s.User_Seeker.User.Mobile,
//             UserName = s.User_Seeker.NickName
//         }).ToList();

//         return result;
//     }

//     public GetScoreReportResponse GetScoreReport(GetScoreReport model)
//     {
//         var result = new GetScoreReportResponse();

//         var predicate = PredicateBuilder.New<User>(true);

//         if (model.UserType == SeekerOrHr.Seeker)
//             predicate = predicate.And(x => x.User_NScore.SeekerScoreTotal > 0);
//         else
//             predicate = predicate.And(x => x.User_NScore.HrScoreTotal > 0);

//         if (!string.IsNullOrWhiteSpace(model.UserName))
//             if (model.UserType == SeekerOrHr.Seeker)
//                 predicate = predicate.And(x => x.User_Seeker.NickName.Contains(model.UserName));
//             else
//                 predicate = predicate.And(x => x.User_Hr.NickName.Contains(model.UserName));

//         if (!string.IsNullOrWhiteSpace(model.UserMobile))
//             predicate = predicate.And(x => x.Mobile == model.UserMobile);

//         if (model.MinScore.HasValue)
//             if (model.UserType == SeekerOrHr.Seeker)
//                 predicate = predicate.And(x => x.User_NScore.SeekerScore >= model.MinScore);
//             else
//                 predicate = predicate.And(x => x.User_NScore.HrScore >= model.MinScore);

//         if (model.MaxScore.HasValue)
//             if (model.UserType == SeekerOrHr.Seeker)
//                 predicate = predicate.And(x => x.User_NScore.SeekerScore <= model.MaxScore);
//             else
//                 predicate = predicate.And(x => x.User_NScore.HrScore <= model.MaxScore);

//         if (model.BeginTime.HasValue)
//             if (model.UserType == SeekerOrHr.Seeker)
//                 predicate = predicate.And(x => x.User_Seeker.CreatedTime >= model.BeginTime.Value);
//             else
//                 predicate = predicate.And(x => x.User_Hr.CreatedTime >= model.BeginTime.Value);

//         if (model.EndTime.HasValue)
//         {
//             model.EndTime = model.EndTime.Value.Date.AddDays(1);
//             if (model.UserType == SeekerOrHr.Seeker)
//                 predicate = predicate.And(x => x.User_Seeker.CreatedTime < model.EndTime.Value);
//             else
//                 predicate = predicate.And(x => x.User_Hr.CreatedTime < model.EndTime.Value);
//         }

//         var sql = _context.User.Where(predicate);

//         result.Total = sql.Count();

//         var sum = sql.GroupBy(g => true)
//         .Select(s => new
//         {
//             TotalScore = s.Sum(ss => model.UserType == SeekerOrHr.Seeker ? ss.User_NScore.SeekerScoreTotal : ss.User_NScore.HrScoreTotal),
//             CurrentScore = s.Sum(ss => model.UserType == SeekerOrHr.Seeker ? ss.User_NScore.SeekerScore : ss.User_NScore.HrScore),
//             MoneyScore = s.Sum(ss => model.UserType == SeekerOrHr.Seeker ? ss.User_NScore.SeekerMoney : ss.User_NScore.HrMoney),
//             PrizeScore = s.Sum(ss => model.UserType == SeekerOrHr.Seeker ? ss.User_NScore.SeekerPrize : ss.User_NScore.HrPrize)
//         }).FirstOrDefault();

//         result.TotalScore = sum?.TotalScore ?? 0;
//         result.CurrentScore = sum?.CurrentScore ?? 0;
//         result.MoneyScore = sum?.MoneyScore ?? 0;
//         result.PrizeScore = sum?.PrizeScore ?? 0;
//         result.UsedScore = result.TotalScore - result.CurrentScore;

//         result.Withdraw = (from a in _context.User_Withdraw
//                            join b in sql on a.UserId equals b.UserId
//                            where a.Status == WithdrawStatus.提现完成 && a.UserType == model.UserType
//                            select a.Amount).Sum();

//         result.Rows = sql
//         .OrderByDescending(x => x.CreatedTime)
//         .Skip((model.PageIndex - 1) * model.PageSize)
//         .Take(model.PageSize)
//         .Select(s => new GetScoreReportInfo
//         {
//             CreatedTime = model.UserType == SeekerOrHr.Seeker ? s.User_Seeker.CreatedTime : s.User_Hr.CreatedTime,
//             UserMobile = s.Mobile,
//             UserName = model.UserType == SeekerOrHr.Seeker ? s.User_Seeker.NickName : s.User_Hr.NickName,
//             UserId = s.UserId,
//             CurrentScore = (model.UserType == SeekerOrHr.Seeker ? (int?)s.User_NScore.SeekerScore : (int?)s.User_NScore.HrScore) ?? 0,
//             TotalScore = (model.UserType == SeekerOrHr.Seeker ? (int?)s.User_NScore.SeekerScoreTotal : (int?)s.User_NScore.HrScoreTotal) ?? 0,
//             PrizeScore = (model.UserType == SeekerOrHr.Seeker ? (int?)s.User_NScore.SeekerPrize : (int?)s.User_NScore.HrPrize) ?? 0,
//             MoneyScore = (model.UserType == SeekerOrHr.Seeker ? (int?)s.User_NScore.SeekerMoney : (int?)s.User_NScore.HrMoney) ?? 0,
//             Withdraw = s.User_Withdraw.Where(x => x.Status == WithdrawStatus.提现完成 && x.UserType == model.UserType).Sum(sum => sum.Amount),
//             ScoreChangeTime = s.User_NScore_Record.OrderByDescending(o => o.CreatedTime).Select(s => s.CreatedTime).FirstOrDefault()
//         }).ToList();

//         foreach (var item in result.Rows)
//         {
//             item.UsedScore = item.TotalScore - item.CurrentScore;
//         }

//         return result;
//     }

//     public GetPushPostSettingsResponse GetPushPostSettings()
//     {
//         var result = new GetPushPostSettingsResponse();

//         result.Rows = MyRedis.Client.Get<List<GetPushPostSettingsInfo>?>($"{RedisKey.NScore.Key}{RedisKey.NScore.NewUserPushPosts}") ?? new List<GetPushPostSettingsInfo>();
//         result.Rows = result.Rows.OrderByDescending(o => o.Id).ToList();
//         return result;
//     }

//     public EmptyResponse UpdatePushPostSettings(UpdatePushPostSettings model)
//     {

//         if (string.IsNullOrWhiteSpace(model.Post))
//             throw new BadRequestException("职位不能为空");

//         if (string.IsNullOrWhiteSpace(model.Company))
//             throw new BadRequestException("公司不能为空");

//         var cs = MyRedis.Client.Get<List<GetPushPostSettingsInfo>?>($"{RedisKey.NScore.Key}{RedisKey.NScore.NewUserPushPosts}") ?? new List<GetPushPostSettingsInfo>();

//         if (!string.IsNullOrWhiteSpace(model.Id))
//             cs = cs.Where(x => x.Id != model.Id).ToList();

//         cs.Add(new GetPushPostSettingsInfo
//         {
//             Id = Tools.NextId(),
//             Post = model.Post,
//             Company = model.Company
//         });

//         MyRedis.Client.Set($"{RedisKey.NScore.Key}{RedisKey.NScore.NewUserPushPosts}", cs);

//         return new EmptyResponse();
//     }

//     public EmptyResponse DeletePushPostSettings(DeletePushPostSettings model)
//     {
//         var cs = MyRedis.Client.Get<List<GetPushPostSettingsInfo>?>($"{RedisKey.NScore.Key}{RedisKey.NScore.NewUserPushPosts}") ?? new List<GetPushPostSettingsInfo>();

//         if (!string.IsNullOrWhiteSpace(model.Id))
//             cs = cs.Where(x => x.Id != model.Id).ToList();

//         MyRedis.Client.Set($"{RedisKey.NScore.Key}{RedisKey.NScore.NewUserPushPosts}", cs);

//         return new EmptyResponse();
//     }
// }