CREATE TABLE `certificate_registrationform` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `CertificateId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '证书id',
  `CertificateSpecsId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '证书规则id',
  `Name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `IdentityCardNumber` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证号',
  `ContactNumber` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系方式（手机号码）',
  `WorkUnit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工作单位',
  `City` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所在城市',
  `CityCode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '城市代码',
  `Gender` enum('男','女') COLLATE utf8mb4_general_ci NOT NULL COMMENT '性别',
  `TrainingBatch` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '培训批次',
  `RegistrationTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '报名时间，默认当前时间',
  `RegistrationProject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '报考项目',
  `Recommender` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '推荐人',
  `RecommenderId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '推荐人id',
  `Fee` decimal(10,2) DEFAULT NULL COMMENT '收费',
  `Creator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `CreateTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，默认当前时间',
  `UpdateTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，默认当前时间，更新时自动更新为当前时间',
  `Price` decimal(10,2) DEFAULT NULL COMMENT '价格（元）',
  `RecommendCommission` decimal(10,2) DEFAULT NULL COMMENT '推荐佣金（元）',
  `ActualTrainingBatch` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '实际培训批次',
  `WeChatGroupName` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业微信群名称',
  `Status` int DEFAULT '0' COMMENT '状态',
  `SettlementStatus` int DEFAULT NULL COMMENT '结算状态',
  `CertificateGroupType` int DEFAULT NULL COMMENT '证书报名入群状态',
  `ToDo` int DEFAULT NULL COMMENT '等会再打状态',
  `PayStatus` int DEFAULT NULL COMMENT '支付状态',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='证书报名表';


CREATE TABLE `certificate_specs` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格ID',
  `CertificateId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的证书ID',
  `SpecName` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格名称（如：初级证书、中级证书）',
  `Price` decimal(10,2) NOT NULL COMMENT '价格（元）',
  `Stock` int DEFAULT NULL COMMENT '库存',
  `RecommendCommission` decimal(10,2) DEFAULT NULL COMMENT '推荐佣金（元）',
  `SortOrder` int DEFAULT '0' COMMENT '自定义排序',
  `CreatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `SalesVolume` int NOT NULL DEFAULT '0' COMMENT '销量',
  PRIMARY KEY (`Id` DESC) USING BTREE,
  UNIQUE KEY `idx_certificateId_specname` (`CertificateId`,`SpecName`) USING BTREE,
  KEY `idx_certificate` (`CertificateId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='证书规格表';


CREATE TABLE `certificate_table` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '证书记录唯一标识',
  `CertificateType` enum('国家职业资格证书','行业认证证书','国际通用证书','职业技能等级证书','专项能力证书','职称证书') COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品类型',
  `CertificateName` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '证书名称，20个字以内',
  `CertificateCategory` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '证书类目',
  `ProductImage` text COLLATE utf8mb4_general_ci COMMENT '商品图，存储图片路径等相关信息',
  `EnterpriseWechatQrCode` text COLLATE utf8mb4_general_ci COMMENT '企微群码，存储二维码图片路径等相关信息',
  `EnterpriseWechatGroupId` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企微群ID',
  `UserNotice` varchar(60) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户须知，60字以内',
  `RegistrationForm` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '报名表单',
  `JobPosition` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工种/职业方向',
  `RelevantMajors` text COLLATE utf8mb4_general_ci COMMENT '相关专业，建议800字以内',
  `IssuingAuthority` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发证机构',
  `QueryPlatform` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '查询平台',
  `EnterpriseGroupBuying` enum('开启','关闭') COLLATE utf8mb4_general_ci NOT NULL DEFAULT '关闭' COMMENT '企业团购状态',
  `SaleStartTime` datetime DEFAULT NULL COMMENT '开售时间，定时开售时记录时间，否则可为空',
  `TrainingTime` datetime DEFAULT NULL COMMENT '培训时间',
  `TrainingBatchType` enum('无线下培训','线下培训') COLLATE utf8mb4_general_ci NOT NULL DEFAULT '无线下培训' COMMENT '培训批次类型',
  `DetailDescription` text COLLATE utf8mb4_general_ci COMMENT '详情描述，记录证书详情相关文本内容',
  `Creator` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `CreateTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，默认当前时间',
  `UpdateTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，默认当前时间，更新时自动更新为当前时间',
  `Deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '删除',
  `EnterpriseWechatName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人企微姓名',
  `EnterpriseWechatGroupName` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '企业微信群名称',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='证书表';



CREATE TABLE `certificate_training_batch` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '批次ID',
  `BatchName` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '批次名称',
  `Address` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '培训地址',
  `StartTime` datetime NOT NULL COMMENT '开始时间',
  `EndTime` datetime NOT NULL COMMENT '结束时间',
  `ContactPerson` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人',
  `ContactPhone` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
  `CertificateId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的证书ID',
  `Description` text COLLATE utf8mb4_general_ci COMMENT '批次描述',
  `CreatedBy` varchar(18) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人ID',
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE KEY `Idx_CertificateId_BatchName` (`BatchName`,`CertificateId`) USING BTREE,
  KEY `Idx_Certificate` (`CertificateId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='证书培训批次表';


CREATE TABLE `certificate_user_config` (
  `Id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `UserId` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `IsEnabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用(0:禁用,1:启用)',
  `CreatedBy` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `CreatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UpdatedBy` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
  `UpdatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  KEY `IDX_UserId` (`UserId`) COMMENT '用户ID索引'
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户证书配置表';


CREATE TABLE `dict_data` (
  `DictCode` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `DictSort` int DEFAULT '0' COMMENT '字典排序',
  `DictLabel` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字典标签',
  `DictValue` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字典键值',
  `DictType` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典类型',
  `CssClass` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `ListClass` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '表格回显样式',
  `IsDefault` char(1) COLLATE utf8mb4_general_ci DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `Status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `CreateBy` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `CreateTime` datetime DEFAULT NULL COMMENT '创建时间',
  `UpdateBy` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `UpdateTime` datetime DEFAULT NULL COMMENT '更新时间',
  `Remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`DictCode`)
) ENGINE=InnoDB AUTO_INCREMENT=116 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字典数据表';