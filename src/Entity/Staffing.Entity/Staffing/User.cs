﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 用户主表
/// </summary>
[Table("user")]
public class User
{
    [Key]
    public string UserId { get; set; } = EntityTools.SnowflakeId();

    // /// <summary>
    // /// 诺聘邀约码，判断是否同一个人的标识(手机号两端可能修改，但邀约码不会变)
    // /// </summary>
    // public string NuoCode { get; set; } = default!;

    /// <summary>
    /// 手机号
    /// </summary>
    public string Mobile { get; set; } = string.Empty;

    // /// <summary>
    // /// 微信UnionId
    // /// </summary>
    // public string? WeChatId { get; set; }

    /// <summary>
    /// 微信昵称
    /// </summary>
    public string? WeChatName { get; set; } 

    /// <summary>
    /// 公众号OpenId
    /// </summary>
    public string? WeChatH5OpenId { get; set; }

    /// <summary>
    /// 用户是否关注公众号
    /// </summary>
    public bool WeChatH5Subscribe { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    public IdCardType IdentityCardType { get; set; } = IdCardType.身份证;

    /// <summary>
    /// 身份证号
    /// </summary>
    public string IdentityCard { get; set; } = string.Empty;

    /// <summary>
    /// 身份证姓名
    /// </summary>
    public string IdentityCardName { get; set; } = string.Empty;

    // /// <summary>
    // /// 快手OpenId
    // /// </summary>
    // public string? KsOpenId { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public User_Extend User_Extend { get; set; } = default!;
    public User_Resume User_Resume { get; set; } = default!;
    public User_Num User_Num { get; set; } = default!;
    public List<User_OpenId> User_OpenId { get; set; } = default!;
    public User_Seeker User_Seeker { get; set; } = default!;
    public User_Hr User_Hr { get; set; } = default!;
    // public User_NScore User_NScore { get; set; } = default!;
    public List<User_Withdraw> User_Withdraw { get; set; } = default!;
    // public List<User_NScore_Record> User_NScore_Record { get; set; } = default!;
    public Dd_User Dd_User { get; set; } = default!;
}