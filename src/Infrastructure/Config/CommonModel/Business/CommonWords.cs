﻿namespace Config.CommonModel.Business;

public class GetMsgWordsResponse
{
    public List<MsgWordsInfo> Rows { get; set; } = new List<MsgWordsInfo>();
}

public class MsgWordsInfo
{
    /// <summary>
    /// 空=添加常用语
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string? Content { get; set; }
}

public class GetMsgLastPost
{
    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 聊天的顾问Id
    /// </summary>
    public string? AdviserImId { get; set; }
}

public class GetMsgLastPostResponse
{
    /// <summary>
    /// 和上次沟通职位是否相同
    /// </summary>
    public bool SamePost { get; set; }
}