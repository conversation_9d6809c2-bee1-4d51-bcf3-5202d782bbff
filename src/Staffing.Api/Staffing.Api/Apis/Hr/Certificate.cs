using System.ComponentModel.DataAnnotations;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using MiniExcelLibs;
using Staffing.Core.Interfaces.Certificate;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Model.Certificate;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class Certificate : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("certificate").WithTags("证书大厅");

        group.MapPost("/register",
                async (CertificateRegistrationFormReq request, ICertificateService rep) => await rep.Register(request))
            .WithOpenApi(operation => new(operation) { Summary = "证书报名" });

        group.MapPost("/saveCertificate",
                (CertificateModel request, ICertificateService rep) => rep.SaveCertificate(request))
            .WithOpenApi(operation => new(operation) { Summary = "创建修改证书" });

        group.MapGet("/certificateDetail/{id}",
                (string id, ICertificateService rep) => rep.GetCertificateDetail(id, null))
            .WithOpenApi(operation => new(operation) { Summary = "获取证书详情" });

        group.MapPost("/deleteCertificate",
                (DeleteCertificateReq request, ICertificateService rep) => rep.DeleteCertificate(request))
            .WithOpenApi(operation => new(operation) { Summary = "删除证书" });

        group.MapPost("/hall",
                (CertificateHallReq request, RequestContext user, StaffingContext context, ICertificateService rep) =>
                {
                    var result = new CertificateHallResp();
                    var predicate = PredicateBuilder.New<CertificateTable>(true);
                    if (request.CertificateType != null)
                    {
                        predicate = predicate.And(x => x.CertificateType == request.CertificateType);
                    }

                    if (!string.IsNullOrWhiteSpace(request.CertificateName))
                    {
                        predicate = predicate.And(x => x.CertificateName.Contains(request.CertificateName));
                    }

                    var sql = context.CertificateTable.Where(predicate);
                    result.Total = sql.Select(s => new { a = 1 }).Count();
                    result.Rows = sql
                        .OrderByDescending(p => p.CreateTime)
                        .Skip((request.PageIndex - 1) * request.PageSize)
                        .Take(request.PageSize)
                        .Include(c => c.Specs)
                        .Include(c => c.TrainingBatch)
                        .Select(
                            s => new CertificateHallModel
                            {
                                Id = s.Id,
                                CertificateType = s.CertificateType,
                                CertificateName = s.CertificateName,
                                CertificateTypeName = s.CertificateType.GetDescription(),
                                JobPosition = s.JobPosition,
                                RelevantMajors = s.RelevantMajors,
                                IssuingAuthority = s.IssuingAuthority,
                                QueryPlatform = s.QueryPlatform,
                                RegistrationForm = s.RegistrationForm,
                                Specs = s.Specs == null
                                    ? new List<CertificateSpecModel>()
                                    : s.Specs.Select(a => new CertificateSpecModel
                                    {
                                        Id = a.Id,
                                        CertificateId = a.CertificateId,
                                        SpecName = a.SpecName,
                                        Price = a.Price,
                                        Stock = a.Stock,
                                        RecommendCommission = a.RecommendCommission,
                                        SortOrder = a.SortOrder
                                    }).ToList(),
                                TrainingBatch = s.TrainingBatch == null
                                    ? new List<CertificateTrainingBatchResp>()
                                    : s.TrainingBatch.Select(batch => new CertificateTrainingBatchResp
                                    {
                                        Id = batch.Id,
                                        BatchName = batch.BatchName,
                                        Address = batch.Address,
                                        StartTime = batch.StartTime,
                                        EndTime = batch.EndTime,
                                        Description = batch.Description,
                                        ContactPerson = batch.ContactPerson,
                                        ContactPhone = batch.ContactPhone,
                                    }).ToList(),
                                HrName = s.UserHr.NickName,
                                HrAvatar = s.UserHr.Avatar,
                                HrEntName = s.UserHr.Enterprise.Name,
                                HrEntWeChatQrCode = s.UserHr.EntWeChatQrCode,
                                TencentImId = s.UserHr.TencentImId,
                                ProductImage = s.ProductImage,
                                TrainingBatchType = s.TrainingBatchType,
                                Creator = s.Creator,
                            }
                        ).ToList();
                    var dictDataMap = context.DictData.Where(w => w.DictType == "CertificationAuthority").Select(s =>
                        new DictData()
                        {
                            DictValue = s.DictValue,
                            DictLabel = s.DictLabel,
                        }).ToDictionary(
                        k => k.DictValue,
                        v => v.DictLabel
                    ) ?? new Dictionary<string, string>();
                    foreach (var item in result.Rows)
                    {
                        item.IssuingAuthorityName = item.IssuingAuthority != null &&
                                                    dictDataMap.TryGetValue(item.IssuingAuthority, out var value)
                            ? value
                            : item.IssuingAuthority;
                        item.Specs = item.Specs?.OrderBy(o => o.Price).ToList();
                    }

                    return result;
                })
            .WithOpenApi(operation => new(operation) { Summary = "证书大厅列表" });

        group.MapPost("/generateQRcode",
                async (GenerateQRcodeReq request, ICertificateService rep) => await rep.GenerateQRcode(request))
            .WithOpenApi(operation => new(operation) { Summary = "生成小程序二维码" });

        group.MapPost("/deleteCertificateTrainingBatch", (string id, StaffingContext context, LogManager logger) =>
        {
            var commentModel = context.CertificateTrainingBatch.Where(o => o.Id == id).ExecuteDelete();
            return new EmptyResponse();
        }).WithOpenApi(operation => new(operation) { Summary = "删除证书培训批次" });

        group.MapPost("/deleteCertificateSpec", (string id, StaffingContext context, LogManager logger) =>
        {
            var commentModel = context.CertificateSpec.Where(o => o.Id == id).ExecuteDelete();
            return new EmptyResponse();
        }).WithOpenApi(operation => new(operation) { Summary = "删除证书规格" });

        group.MapPost("/configUserCertificate",
            ([Required] bool isEnabled, RequestContext user, StaffingContext context, LogManager logger) =>
            {
                var certificateUserConfig = context.CertificateUserConfig.FirstOrDefault(o => o.UserId == user.Id);
                if (certificateUserConfig == null)
                {
                    context.Add(new CertificateUserConfig()
                    {
                        UserId = user.Id,
                        IsEnabled = isEnabled,
                        CreatedBy = user.Id,
                        UpdatedBy = user.Id
                    });
                }
                else
                {
                    certificateUserConfig.IsEnabled = isEnabled;
                    certificateUserConfig.UpdatedAt = DateTime.Now;
                }

                context.SaveChanges();
                return new EmptyResponse();
            }).WithOpenApi(operation => new(operation) { Summary = "配置当前用户是否展示证书" });

        group.MapGet("/getUserCertificateConfig", (RequestContext user, StaffingContext context, LogManager logger) =>
        {
            var certificateUserConfig = context.CertificateUserConfig.FirstOrDefault(o => o.UserId == user.Id);
            if (certificateUserConfig == null)
            {
                return new CertificateUserConfigResp();
            }

            return new CertificateUserConfigResp()
            {
                IsEnabled = certificateUserConfig.IsEnabled
            };
        }).WithOpenApi(operation => new(operation) { Summary = "获取当前用户的证书配置" });

        group.MapGet("/export/certificate", ([AsParameters] CertificateRegistrationformListReq req,
            RequestContext user, StaffingContext context) =>
        {
            var predicate = PredicateBuilder.New<CertificateRegistrationForm>(true);
            predicate = predicate.And(r => r.CertificateTable.Creator == user.Id || r.RecommenderId == user.Id);
            if (req.CertificateType != null)
            {
                predicate = predicate.And(r => r.CertificateTable.CertificateType == req.CertificateType);
            }

            if (req.RegistrationStartTime.HasValue)
            {
                predicate = predicate.And(r => r.RegistrationTime >= req.RegistrationStartTime);
            }

            if (req.RegistrationEndTime.HasValue)
            {
                var endOfDay = req.RegistrationEndTime.Value.Date.AddDays(1);
                predicate = predicate.And(r => r.RegistrationTime < endOfDay);
            }

            if (req.CertificateGroupType != null)
            {
                predicate = predicate.And(r => r.CertificateGroupType == req.CertificateGroupType);
            }

            if (req.CertificateSettlement != null)
            {
                predicate = predicate.And(r => r.SettlementStatus == req.CertificateSettlement);
            }

            if (!string.IsNullOrWhiteSpace(req.Search))
            {
                predicate = predicate.And(r =>
                    r.Name.Contains(req.Search) || r.ContactNumber == req.Search ||
                    r.CertificateTable.CertificateName.Contains(req.Search));
            }

            var sql = context.CertificateRegistrationForm.IgnoreQueryFilters().Where(predicate);
            var data = sql
                .OrderByDescending(p => p.RegistrationTime)
                .Skip((req.PageIndex - 1) * req.PageSize)
                .Take(req.PageSize)
                .Select(s => new ExportDataForCertificateRegistrationForm()
                    {
                        报名来源 = s.Creator == null ? "后台报名" : "小程序",
                        姓名 = s.Name,
                        联系方式 = s.ContactNumber,
                        身份证号 = s.IdentityCardNumber,
                        性别 = s.Gender.GetDescription(),
                        所在城市 = s.City,
                        证书规格名称 = s.CertificateSpec != null ? s.CertificateSpec.SpecName : "",
                        证书名称 = s.CertificateTable.CertificateName,
                        培训批次 = s.TrainingBatch,
                        实际培训批次 = s.ActualTrainingBatch,
                        价格 = s.Price,
                        推荐佣金 = s.RecommendCommission,
                        工作单位 = s.WorkUnit,
                        报名时间 = s.RegistrationTime,
                        推荐人 = s.Recommender,
                        支付状态 = s.PayStatus.GetDescription(),
                    }
                ).ToList();
            var memoryStream = new MemoryStream();
            memoryStream.SaveAs(data);
            memoryStream.Seek(0, SeekOrigin.Begin);
            return Results.File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                $"证书报名线索.xlsx");
        }).WithOpenApi(operation => new(operation) { Summary = "证书报名导出接口" });


        group.MapGet("/getCertificateRegistrationformList", ([AsParameters] CertificateRegistrationformListReq req,
            RequestContext user, StaffingContext context) =>
        {
            var result = new CertificateRegistrationformListResp();
            var predicate = PredicateBuilder.New<CertificateRegistrationForm>(true);
            predicate = predicate.And(r => r.CertificateTable.Creator == user.Id || r.RecommenderId == user.Id);
            if (req.CertificateType != null)
            {
                predicate = predicate.And(r => r.CertificateTable.CertificateType == req.CertificateType);
            }

            if (req.RegistrationStartTime.HasValue)
            {
                predicate = predicate.And(r => r.RegistrationTime >= req.RegistrationStartTime);
            }

            if (req.RegistrationEndTime.HasValue)
            {
                var endOfDay = req.RegistrationEndTime.Value.Date.AddDays(1);
                predicate = predicate.And(r => r.RegistrationTime < endOfDay);
            }

            if (req.CertificateGroupType != null)
            {
                predicate = predicate.And(r => r.CertificateGroupType == req.CertificateGroupType);
            }

            if (req.CertificateSettlement != null)
            {
                predicate = predicate.And(r => r.SettlementStatus == req.CertificateSettlement);
            }
            if (req.PayStatus.HasValue)
            {
                predicate = predicate.And(r => r.PayStatus == req.PayStatus);
            }

            if (!string.IsNullOrWhiteSpace(req.Search))
            {
                predicate = predicate.And(r =>
                    r.Name.Contains(req.Search) || r.ContactNumber == req.Search ||
                    r.CertificateTable.CertificateName.Contains(req.Search));
            }

            var sql = context.CertificateRegistrationForm.IgnoreQueryFilters().Where(predicate);
            result.Total = sql.Select(s => new { a = 1 }).Count();
            result.Rows = sql
                .OrderByDescending(p => p.RegistrationTime)
                .Skip((req.PageIndex - 1) * req.PageSize)
                .Take(req.PageSize)
                .Select(s => new CertificateRegistrationformListModel
                    {
                        Id = s.Id,
                        CertificateId = s.CertificateId,
                        Source =  s.Creator == null ? "后台报名" : "小程序",
                        Name = s.Name,
                        ContactNumber = s.ContactNumber,
                        Gender = s.Gender,
                        GenderName = s.Gender.GetDescription(),
                        IdentityCardNumber = s.IdentityCardNumber,
                        City = s.City,
                        SpecName = s.CertificateSpec != null ? s.CertificateSpec.SpecName : null,
                        CertificateName = s.CertificateTable.CertificateName,
                        IssuingAuthority = s.CertificateTable.IssuingAuthority,
                        TrainingBatch = s.TrainingBatch,
                        ActualTrainingBatch = s.ActualTrainingBatch,
                        WeChatGroupName = s.WeChatGroupName,
                        StatusName = s.Status.GetDescription(),
                        SettlementStatusName = s.SettlementStatus.GetDescription(),
                        Price = s.Price,
                        RecommendCommission = s.RecommendCommission,
                        Recommender = s.Recommender,
                        WorkUnit = s.WorkUnit,
                        RegistrationTime = s.RegistrationTime,
                        Status = s.Status,
                        SettlementStatus = s.SettlementStatus,
                        ToDo = s.ToDo,
                        PayStatus = s.PayStatus,
                        CertificateCreator = s.CertificateTable.Creator,
                    }
                ).ToList();
            var dictDataMap = context.DictData.Where(w => w.DictType == "CertificationAuthority").Select(s =>
                new DictData()
                {
                    DictValue = s.DictValue,
                    DictLabel = s.DictLabel,
                }).ToDictionary(
                k => k.DictValue,
                v => v.DictLabel
            ) ?? new Dictionary<string, string>();
            foreach (var item in result.Rows)
            {
                item.IssuingAuthorityName = item.IssuingAuthority != null &&
                                            dictDataMap.TryGetValue(item.IssuingAuthority, out var value)
                    ? value
                    : item.IssuingAuthority;
            }
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "证书报名线索列表接口" });


        group.MapPost("/certificateRegistrationFormEdit", (CertificateRegistrationFormEditReq req, RequestContext user,
            StaffingContext context, LogManager logger) =>
        {
            var certificateRegistrationForm = context.CertificateRegistrationForm.IgnoreQueryFilters()
                .Include(certificateRegistrationForm => certificateRegistrationForm.CertificateTable)
                .FirstOrDefault(o => o.Id == req.Id);
            if (certificateRegistrationForm == null)
            {
                throw new BadRequestException("报名数据不存在");
            }

            if (!string.Equals(user.Id, certificateRegistrationForm.CertificateTable.Creator))
            {
                throw new BadRequestException("不是证书创建人无法修改报名数据");
            }

            certificateRegistrationForm.Status = req.Status;
            certificateRegistrationForm.SettlementStatus = req.SettlementStatus;
            certificateRegistrationForm.UpdateTime = DateTime.Now;  
            certificateRegistrationForm.ActualTrainingBatch = req.ActualTrainingBatch;
            certificateRegistrationForm.ToDo = req.Todo;
            certificateRegistrationForm.PayStatus = req.PayStatus;
            // 然后将空值属性标记为未修改
            var entry = context.Entry(certificateRegistrationForm);
            foreach (var property in entry.Properties)
            {
                if (property.CurrentValue == null)
                {
                    property.IsModified = false;
                }
            }
            context.SaveChanges();
            return new EmptyResponse();
        }).WithOpenApi(operation => new(operation) { Summary = "报名数据更新" });


        group.MapGet("/getTrainingBatchByCertificateId",
            (string id, RequestContext user, StaffingContext context, LogManager logger) =>
            {
                List<CertificateTrainingBatchResp> result = context.CertificateTrainingBatch
                    .Where(w => w.CertificateId == id)
                    .Select(s => new CertificateTrainingBatchResp
                    {
                        Id = s.Id,
                        BatchName = s.BatchName,
                        Address = s.Address,
                        StartTime = s.StartTime,
                        EndTime = s.EndTime,
                        Description = s.Description,
                        ContactPerson = s.ContactPerson,
                        ContactPhone = s.ContactPhone,
                    }).ToList();
                return result;
            }).WithOpenApi(operation => new(operation) { Summary = "根据证书id查询培训批次" });

        group.MapPost("/CertificatePromotion", async (CertificateMyPromotionReq request, RequestContext user, ICertificateService rep, Microsoft.Extensions.Options.IOptions<Config.ConfigManager> _config) =>
        {
            return await rep.GetPromotionImgUrl(new CertificatePromotionReq { AdviserID = user.Id, CertificateID = request.CertificateID }, _config.Value.Aliyun!.Oss!.Dir!);
        }).WithOpenApi(operation => new(operation) { Summary = "获取我的分享海报的图片URL" });
    }
}