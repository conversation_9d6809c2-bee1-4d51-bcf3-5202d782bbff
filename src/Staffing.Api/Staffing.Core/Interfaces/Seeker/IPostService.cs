﻿using Staffing.Model.Seeker.Post;
using Config.Enums;
using Config.CommonModel.Business;

namespace Staffing.Core.Interfaces.Seeker;

public interface IPostService
{
    SeekerGetPostsResponse SeekerGetPosts(SeekerGetPosts model);
    Task<SeekerGetPostInfo> SeekerGetPost(SeekerGetPost model);
    Task<SeekerGetPostInfo?> SeekerGetESPost(string TeamPostId);
    List<string> PostHotKeywords();
    List<ProjectIndustry> GetPostProjectIndustry();
    GetAdviserCityResponse GetAdviserCity(GetAdviserCity model);
    Task<SeekerGetEntInfo> SeekerGetEnt(SeekerGetEnt model);
    SeekerGetProjectsResponse SeekerGetProjects(SeekerGetProjects model);
    SeekerGetProjectIndustryInfo SeekerGetProjectIndustry(SeekerGetProjectIndustry model);
    HomePageThirdPartyDataResponse GetHomePageThirdPartyData();
    Task<GetSeekerShareQrCodeResponse> GetSeekerShareQrCode(GetSeekerShareQrCode model);
    GetAdviserCityResponse GetExcellentCity(GetAdviserCity model);
}