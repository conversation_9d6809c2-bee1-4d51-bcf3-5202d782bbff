﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Config.CommonModel.QYWechat
{
    public enum QYWechat_MemChangeType
    {
        入群 = 1,
        退群 = 2,
        群名变更 = 3
    }
    public class QYWechat_MemChangeCacheInfo
    {
        //public QYWechat_MemChangeType? ChangeType { get; set; }

        ///// <summary>
        ///// 微信群ID
        ///// </summary>
        //public string? GroupChatID { get; set; }
        /// <summary>
        /// 诺快聘小程序的用户ID
        /// *UserID和WeChatUnionId取其一即可
        /// </summary>
        public string? UserID { get; set; }
        /// <summary>
        /// 微信UnionId
        /// </summary>
        public string? WeChatUnionId { get; set; }
    }

    public class EnterpriseWechatGroupId
    {
        public string? GropChatId { get; set; }
    }
}
