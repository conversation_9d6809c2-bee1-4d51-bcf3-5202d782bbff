﻿using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using ServiceStack.Text;
using Noah.Aliyun.Storage;
using Config;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Hosting;
using Config.CommonModel.FigureNoah;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Transient)]
public class FigureNoahHelper
{
    private readonly IObjectStorage _objectStorage;
    private readonly ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    public FigureNoahHelper(IObjectStorage objectStorage, IOptionsSnapshot<ConfigManager> config,
    IHostEnvironment hostingEnvironment, LogManager log)
    {
        _objectStorage = objectStorage;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
    }

    /// <summary>
    /// 获取调用老数字诺亚凭证
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private FigureNoah.CredentialSoapHeader GetCredential()
    {
        return new FigureNoah.CredentialSoapHeader
        {
            UserID = "X9dZHwmHSZ06jfJCbo5PDg==",
            PassWord = "X9dZHwmHSZ06jfJCbo5PDg=="
        };
    }

    private void CheckFigureNoahResult<T>(FigureNoahModel<T> result)
    {
        if (result.code != 1 && result.code != -2)
            throw new Exception($"数字诺亚接口错误：{result.code}_{result.msg}");
    }

    /// <summary>
    /// 获取项目人员
    /// </summary>
    /// <param name="projectCode"></param>
    /// <param name="pageIndex"></param>
    /// <param name="pageCount"></param>
    /// <returns></returns>
    public async Task<NoahGetProjectMembersResponse> GetProjectMember(string projectCode, int pageIndex, int pageCount)
    {
        var client = new FigureNoah.HRServiceSoapClient(new FigureNoah.HRServiceSoapClient.EndpointConfiguration());
        var jsonReturn = (await client.FN_GetEmployeeByProjectCODEAsync(GetCredential(), projectCode, pageIndex, pageCount)).FN_GetEmployeeByProjectCODEResult;
        var result = JsonSerializer.DeserializeFromString<FigureNoahModel<NoahGetProjectMembersResponse>>(jsonReturn);

        CheckFigureNoahResult(result);

        return result?.result ?? new NoahGetProjectMembersResponse();
    }

    /// <summary>
    /// 获取人员销帮帮和数字诺亚数据
    /// </summary>
    /// <param name="proCode"></param>
    /// <returns></returns>
    public async Task<NoahGetEmployeeProjDataResponse> GetEmployeeProjData(string proCode)
    {
        var client = new FigureNoah.HRServiceSoapClient(new FigureNoah.HRServiceSoapClient.EndpointConfiguration());
        var jsonReturn = (await client.FN_GetHTANDXMByEmpCODEAsync(GetCredential(), proCode)).FN_GetHTANDXMByEmpCODEResult;
        var result = JsonSerializer.DeserializeFromString<FigureNoahModel<NoahGetEmployeeProjDataResponse>>(jsonReturn);

        CheckFigureNoahResult(result);

        return result?.result ?? new NoahGetEmployeeProjDataResponse();
    }

    /// <summary>
    /// 获取人员销帮帮和数字诺亚数据明细
    /// </summary>
    /// <param name="proCode"></param>
    /// <returns></returns>
    public async Task<NoahGetEmployeeProjDataDetailResponse> GetEmployeeProjDataDetail(string proCode)
    {
        var client = new FigureNoah.HRServiceSoapClient(new FigureNoah.HRServiceSoapClient.EndpointConfiguration());
        var jsonReturn = (await client.FN_GetHTANDXMByEmpCODEVIEWAsync(GetCredential(), proCode)).FN_GetHTANDXMByEmpCODEVIEWResult;
        var result = JsonSerializer.DeserializeFromString<FigureNoahModel<NoahGetEmployeeProjDataDetailResponse>>(jsonReturn);

        CheckFigureNoahResult(result);

        // Console.WriteLine(jsonReturn);

        return result?.result ?? new NoahGetEmployeeProjDataDetailResponse();
    }
}