-- ----------------------------
-- 钉钉部门
-- ----------------------------
DROP TABLE IF EXISTS `dd_dept`;
CREATE TABLE `dd_dept` (
  `DdDeptId` int(11) NOT NULL,
  `Name`  varchar(50) NOT NULL COMMENT '名称',
  `ParentId` int(11) NOT NULL COMMENT '父Id',
  `Level` varchar(1024) NOT NULL COMMENT '层级',
  `Sort` int(11) NOT NULL COMMENT '排序',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`DdDeptId`),
  KEY `IX_dd_dept_Level` (`Level`),
  KEY `IX_dd_dept_CreatedTime` (`CreatedTime` DESC),
  KEY `IX_dd_dept_UpdatedTime` (`UpdatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 钉钉用户
-- ----------------------------
DROP TABLE IF EXISTS `dd_user`;
CREATE TABLE `dd_user` (
  `DdUserId` varchar(50) NOT NULL COMMENT '钉钉用户Id',
  `Name`  varchar(50) NOT NULL COMMENT '姓名',
  `Mobile` varchar(20) NOT NULL COMMENT '手机号',
  `Title` varchar(100) NULL COMMENT '职位',
  `Avatar` varchar(1024) NULL COMMENT '头像',
  `JobNumber` varchar(50) NOT NULL COMMENT '员工工号',
  `WorkPlace` varchar(500) NULL COMMENT '办公地点',
  `Remark` varchar(5000) NULL COMMENT '备注',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`DdUserId`),
  UNIQUE KEY `IX_dd_user_JobNumber` (`JobNumber`),
  KEY `IX_dd_user_CreatedTime` (`CreatedTime` DESC),
  KEY `IX_dd_user_UpdatedTime` (`UpdatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 钉钉用户部门关系
-- ----------------------------
DROP TABLE IF EXISTS `dd_user_dept`;
CREATE TABLE `dd_user_dept` (
  `Id` varchar(18) NOT NULL,
  `DdUserId` varchar(50) NOT NULL COMMENT '钉钉用户Id',
  `DdDeptId` int(11) NOT NULL COMMENT '钉钉部门Id',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_dd_user_dept_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 钉钉推送记录
-- ----------------------------
DROP TABLE IF EXISTS `dd_push_record`;
CREATE TABLE `dd_push_record` (
  `Id` varchar(18) NOT NULL,
  `Type` int NOT NULL COMMENT '推送类型',
  `UserId` varchar(20) NOT NULL COMMENT '用户Id',
  `Content` text NOT NULL COMMENT '内容',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_dd_push_record_UserId` (`UserId`),
  KEY `IX_dd_push_record_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;