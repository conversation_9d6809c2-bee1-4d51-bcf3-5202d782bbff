using System.Text;
using Config;
using Infrastructure.Common;
using Microsoft.Extensions.Options;
using Org.Apache.Rocketmq;
using Staffing.Worker.Hosting;
using Staffing.Worker.Service;

namespace Staffing.Worker.Listener;

public class RocketMqListener(
    IOptionsSnapshot<ConfigManager> config,
    CanalConsumerService canalConsumerService,
    IHostApplicationLifetime lifetime,
    LogManager logger)
    : HostingBase(lifetime)
{
    private readonly ConfigManager _config = config.Value;
    private SimpleConsumer? _simpleConsumer;

    public override async Task StartAsync(CancellationToken cancel)
    {
        await ConnectAndListen(cancel);
    }

    private async Task ConnectAndListen(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var endpoints = _config.RocketMq!.RocketmqBrokerAddress;
                var clientConfig = new ClientConfig.Builder()
                    .SetEndpoints(endpoints)
                    .Build();

                var consumerGroup = _config.RocketMq.RocketConsumerGroup;
                var topic = _config.RocketMq.RocketmqTopic;
                var subscription = new Dictionary<string, FilterExpression>
                            { { topic!, new FilterExpression("*") } };

                _simpleConsumer = await new SimpleConsumer.Builder()
                    .SetClientConfig(clientConfig)
                    .SetConsumerGroup(consumerGroup)
                    .SetAwaitDuration(TimeSpan.FromSeconds(15))
                    .SetSubscriptionExpression(subscription)
                    .Build();

                await ReceiveMessagesAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                logger.Error("RocketMqListener", "Failed to connect or receive messages.", ex);
                await Task.Delay(TimeSpan.FromSeconds(2), cancellationToken); // 等待一段时间后重试
            }
        }
    }

    private async Task ReceiveMessagesAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var messageViews = await _simpleConsumer!.Receive(16, TimeSpan.FromSeconds(15));
                foreach (var message in messageViews)
                {
                    logger.Info($"Received a message, topic={message.Topic}, message-id={message.MessageId}, body-size={message.Body.Length}", "");

                    if (message.Body.Length > 0)
                    {
                        string messageBody = Encoding.UTF8.GetString(message.Body);
                        await Task.Run(() => canalConsumerService.CanalConsumer(messageBody), cancellationToken); // 异步处理消息
                    }

                    await _simpleConsumer.Ack(message);
                }
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                logger.Error("RocketMqListener", $"Error receiving messages: {ex.Message}", ex);
                await Task.Delay(1000, cancellationToken);
                throw;
            }
        }
    }

}