﻿using Config.CommonModel;
using Staffing.Model.Hr.TalentVirtual;

namespace Staffing.Core.Interfaces.Hr;

/// <summary>
/// 虚拟人才库服务接口
/// </summary>
public interface ITalentVirtualService
{
    /// <summary>
    /// 获取虚拟人才库列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    TalentVirtualResumeListResponse GetTalentVirtualResumeList(TalentVirtualResumeListRequest model);

    Task<TalentVirtualResumeListResponse> GetTalentVirtualResumeList_OLD(TalentVirtualResumeListRequest model);

    /// <summary>
    /// 删除人才库简历
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> DeleteTalentVirtualResume(DeleteTalentVirtualResumeRequest model);

    /// <summary>
    /// 获取虚拟人才库简历详情
    /// </summary>
    /// <param name="virtualId"></param>
    /// <returns></returns>
    TalentVirtualResumeDetailsResponse GetTalentVirtualResumeDetails(string virtualId);

    /// <summary>
    /// 修改虚拟人才库简历
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse EditTalentVirtaualResume(EditVirtualResumeDetailsRequest model);

    /// <summary>
    /// 修改虚拟人才库原始简历地址
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse EditTalentVirtaualOriginalUrl(EditTalentVirtaualOriginalUrlRequest model);

    /// <summary>
    /// 获取虚拟人才库简历评论
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetTalentVirtualResumeCommentResponse GetTalentVirtualResumeComment(GetTalentVirtualResumeCommentRequest model);

    /// <summary>
    /// 添加虚拟人才库简历评论
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse AddTalentVirtualResumeComment(AddTalentVirtualResumeCommentRequest model);

    /// <summary>
    /// 删除虚拟人才库简历评论
    /// </summary>
    /// <param name="commentId"></param>
    /// <returns></returns>
    EmptyResponse DeleteTalentVirtualResumeComment(string commentId);

    /// <summary>
    /// 获取虚拟人才库职位列表
    /// </summary>
    /// <returns></returns>
    TalentVirtualPostListResponse GetTalentVirtualPostList();

    /// <summary>
    /// 获取虚拟人才库行业列表
    /// </summary>
    /// <returns></returns>
    TalentVirtualIndustryListResponse GetTalentVirtualIndustryList();

    /// <summary>
    /// 上传简历（调用小析服务解析简历）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse UpLoadResume(UpLoadResumeRequest model);

    /// <summary>
    /// 解析原始简历
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> AnalyzeOriginalResume(AnalyzeOriginalResumeRequest model);

    /// <summary>
    /// 获取虚拟人才库上传简历记录
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetTalentUploadRecordListResponse GetTalentUploadRecordList(GetTalentUploadRecordListRequest model);

    /// <summary>
    /// 获取虚拟人才库上传简历记录（子表）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetTalentUploadRecordSubListResponse GetTalentUploadRecordSubList(GetTalentUploadRecordSubListRequest model);

    /// <summary>
    /// 获取人才库标签列表（真实虚拟通用）
    /// </summary>
    /// <returns></returns>
    TalentLabelListResponse GetTalentLabelList();

    /// <summary>
    /// 添加人才库标签（真实虚拟通用）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse AddTalentLabel(AddTalentLabelRequest model);

    /// <summary>
    /// 删除人才库标签（真实虚拟通用）
    /// </summary>
    /// <param name="labelId"></param>
    /// <returns></returns>
    EmptyResponse DeleteTalentLabel(string labelId);

    /// <summary>
    /// 获取虚拟人才库标签
    /// </summary>
    /// <param name="virtualId"></param>
    /// <returns></returns>
    TalentVirtualLabelResponse GetTalentVirtualLabel(string virtualId);

    /// <summary>
    /// 变更虚拟人才库标签
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> EditTalentVirtualLabel(EditTalentVirtualLabelRequest model);
}

