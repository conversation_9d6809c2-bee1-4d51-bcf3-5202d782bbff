namespace Config.CommonModel.ESign;

/// <summary>
/// e签宝通用返回格式
/// </summary>
/// <typeparam name="T"></typeparam>
public class ESignResponse<T>
{
    public int code { get; set; }
    public T? data { get; set; }
    public string message { get; set; } = string.Empty;
}

/// <summary>
/// e签宝token
/// </summary>
public class ESignAccessToken
{
    public string refreshToken { get; set; } = string.Empty;
    public string expiresIn { get; set; } = string.Empty;
    public string token { get; set; } = string.Empty;
}