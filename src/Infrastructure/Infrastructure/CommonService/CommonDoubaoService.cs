﻿using Flurl.Http;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using ServiceStack.Text;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure.CommonService
{
    [Service(ServiceLifetime.Transient)]
    public class CommonDoubaoService
    {
        string apiKey = "85ebffb2-6ec4-42ff-9d0f-1346b1b537ca";
        string apiEndpoint = "https://ark.cn-beijing.volces.com/api/v3/chat/completions";
        string model = "ep-20250331144724-qlcpd";

        public async Task<List<string>> DoubaoRes(string content)
        {
            // 构建请求体
            var requestBody = new
            {
                model,
                messages = new[] { new { role = "user", content = content + @"    对该段话进行关键字提炼该岗位福利标签，不要输出该段中不存在的词语或语句，多个岗位标签之间使用单个空格隔开" } }
            };
            string request = JsonSerializer.SerializeToString(requestBody);
            string receive = await apiEndpoint.WithHeader("Content-Type", "application/json").
                              WithHeader("Authorization", $"Bearer {apiKey}").
                              PostJsonAsync(request).
                              ReceiveString();
            if(receive.Contains("error"))
            {
                throw new Exception($"调用豆包API出错！{receive}");
            }
            var res = JsonSerializer.DeserializeFromString<DoubaoResponse>(receive);
            return res.choices![0].message!.content!.Split(' ').ToList();
        }

        public class DoubaoResponse
        {
            public string? id { get; set; }
            public List<Choices>? choices { get; set; }
        }
        public class Choices
        {
            public int index { get; set; }
            public Message? message { get; set; }
        }

        public class Message
        {
            public string? role { get; set; }
            public string? content { get; set;}
        }
    }
}
