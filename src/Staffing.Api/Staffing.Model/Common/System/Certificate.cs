﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Staffing.Model.Common.System
{
    public class Certificate_RecruitmentplanproReq
    {
        /// <summary>
        /// 顾问手机号
        /// </summary>
        [Required(ErrorMessage = "手机号不能为空")]
        public string AdviserMobile { get; set; } = default!;
        /// <summary>
        /// 模板图路径
        /// </summary>
        public string? TemplatePath { get; set; }
        /// <summary>
        /// 企微二维码X坐标
        /// </summary>
        public int QYWechatPosition_x { get; set; }
        /// <summary>
        /// 企微二维码Y坐标
        /// </summary>
        public int QYWechatPosition_y { get; set; }
        /// <summary>
        /// 企微二维码大小
        /// </summary>
        public int QYWechatSize { get; set; }
    }
    public class Certificate_GentoCerTificateReq
    {
        /// <summary>
        /// 顾问手机号
        /// </summary>
        [Required(ErrorMessage = "手机号不能为空")]
        public string AdviserMobile { get; set; } = default!;
        /// <summary>
        /// 模板图路径
        /// </summary>
        public string? TemplatePath { get; set; }
        /// <summary>
        /// 企微二维码X坐标
        /// </summary>
        public int QYWechatPosition_x { get; set; }
        /// <summary>
        /// 企微二维码Y坐标
        /// </summary>
        public int QYWechatPosition_y { get; set; }
        /// <summary>
        /// 企微二维码大小
        /// </summary>
        public int QYWechatSize { get; set; }

        public int Mobile_FontSize { get; set; }
        public int Mobile_PositionX { get; set; }
        public int Mobile_PositionY { get; set; }
    }
}
