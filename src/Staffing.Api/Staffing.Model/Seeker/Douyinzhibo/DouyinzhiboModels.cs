﻿using Config.CommonModel.Business;
using Config.CommonModel;
using Config.Enums;
using Staffing.Entity.Staffing;

namespace Staffing.Model.Seeker.Douyinzhibo;

public class DouyinzhiboModels
{

}

public class PostListForDouyinZhiboResponse : QueryResponse
{
    public List<DouyinzhiboPostInfo> Rows { get; set; } = new List<DouyinzhiboPostInfo>();
}

/// <summary>
/// 职位信息
/// </summary>
public class DouyinzhiboPostInfo
{
    /// <summary>
    /// 职位id
    /// </summary>
    public string? PostId { get; set; }

    public string TeamPostId { get; set; } = default!; 

    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 职位类别
    /// </summary>
    public int Category { get; set; }

    /// <summary>
    /// 职位类别名称
    /// </summary>
    public string? CategoryName { get; set; }

    /// <summary>
    /// 领带类型
    /// </summary>
    public TieType TieType { get; set; } = TieType.蓝领;

    /// <summary>
    /// 工作性质
    /// </summary>
    public PostWorkNature WorkNature { get; set; } = PostWorkNature.全职;

    /// <summary>
    /// 薪资类型
    /// </summary>
    public PostSalaryType SalaryType { get; set; } = PostSalaryType.面议;

    /// <summary>
    /// 薪资名称
    /// </summary>
    public string? SalaryName { get; set; }

    /// <summary>
    /// 招聘人数
    /// </summary>
    public int RecruitNumber { get; set; }

    /// <summary>
    /// 交付数量
    /// </summary>
    public int DeliveryNumber { get; set; }

    /// <summary>
    /// 剩余库存
    /// </summary>
    public int LeftStock { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string RegionId { get; set; } = default!;

    /// <summary>
    /// 地区
    /// </summary>
    public CityModel? Region { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public double Lat { get; set; }

    /// <summary>
    /// 经度
    /// </summary>
    public double Lng { get; set; }

    /// <summary>
    /// 教育
    /// </summary>
    public EducationType Education { get; set; } = EducationType.不限;

    public string? EducationName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 最小年龄
    /// </summary>
    public int MinAge { get; set; }

    /// <summary>
    /// 最大年龄
    /// </summary>
    public int MaxAge { get; set; }

    /// <summary>
    /// 薪资要求(最低)
    /// </summary>
    public int MinSalary { get; set; }

    /// <summary>
    /// 薪资要求(最高)
    /// </summary>
    public int MaxSalary { get; set; }

    /// <summary>
    /// 几薪
    /// </summary>
    public int Salary { get; set; } = 12;

    #region 特殊字段

    //兼职

    /// <summary>
    /// 结算方式（日结、月结）
    /// </summary>
    public PostSettlementType? SettlementType { get; set; }

    /// <summary>
    /// 工作日(1-7)
    /// </summary>
    public List<int>? WorkingDays { get; set; } = new List<int>();

    /// <summary>
    /// 工作时间
    /// </summary>
    public List<PostWorkingHours>? WorkingHours { get; set; } = new List<PostWorkingHours>();

    //实习生

    /// <summary>
    /// 最少实习月数
    /// </summary>
    public int? MinMonths { get; set; }

    /// <summary>
    /// 最少周到岗天数
    /// </summary>
    public int? DaysPerWeek { get; set; }

    //应届

    /// <summary>
    /// 毕业年份
    /// </summary>
    public int? GraduationYear { get; set; }

    #endregion

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Money { get; set; }

    /// <summary>
    /// 金额名称
    /// </summary>
    public string? MoneyName { get; set; }

    /// <summary>
    /// 交付场景名称
    /// </summary>
    public string? PaymentNodeName { get; set; }

    /// <summary>
    /// 交付场景
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }

    /// <summary>
    /// 打款天数
    /// </summary>
    public int? PaymentDays { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public List<string>? Tags { get; set; } = new List<string>();

    /// <summary>
    /// 职位亮点
    /// </summary>
    public List<string>? Highlights { get; set; } = new List<string>();

    /// <summary>
    /// 福利（更新只提交Id即可）
    /// </summary>
    public List<WelfareModel>? Welfare { get; set; } = new List<WelfareModel>();

    /// <summary>
    /// 福利自定义
    /// </summary>
    public List<string>? WelfareCustom { get; set; } = new List<string>();

    /// <summary>
    /// 是否下架
    /// </summary>
    public bool IsRemoved { get; set; } = false;

    /// <summary>
    /// 是否讲解
    /// </summary>
    public bool IsSpeek { get; set; } = false;

    /// <summary>
    /// 投递数
    /// </summary>
    public int DeliveryCount { get; set; }
}

public class PostsGroupsResponse
{
    //public List<PostsGroups> Rows { get; set; } = new List<PostsGroups>();
    public List<PostsGroups> CategoryGroups { get; set; } = new List<PostsGroups>();
    public List<PostsGroups> CityGroups { get; set; } = new List<PostsGroups>();
    public List<PostsGroups> CompanyGroups { get; set; } = new List<PostsGroups>();
}

public class PostsGroups
{
    /// <summary>
    /// 分组
    /// </summary>
    public PostGroupEnum GroupType { get; set; }

    /// <summary>
    /// 类别id
    /// </summary>
    public string TypeId { get; set; } = default!;

    /// <summary>
    /// 类别名称
    /// </summary>
    public string TypeName { get; set; } = default!;

    /// <summary>
    /// 岗位数量
    /// </summary>
    public int? Count { get; set; }

    /// <summary>
    /// 是否讲解
    /// </summary>
    public bool? IsSpeek { get; set; } = false;

    /// <summary>
    /// 是否加热
    /// </summary>
    public bool? IsHot { get; set; } = false;
}

public class PostsByGroupRequest
{
    /// <summary>
    /// 分组
    /// </summary>
    public PostGroupEnum GroupType { get; set; }

    public string TypeId { get; set; } = default!;
}

public class SpeekRequest
{
    /// <summary>
    /// 是否讲解：true-是，false-否
    /// </summary>
    public bool? IsSpeek { get; set; } = true;

    public string ZhiboId { get; set; } = default!;

    public string TeamPostId { get; set; } = default!;

    /// <summary>
    /// 渠道id
    /// </summary>
    public string? ChannelId { get; set; }
}

public class HotRequest
{
    public string ZhiboId { get; set; } = default!;

    public PostsByGroupRequest Group { get; set; } = default!;

    public bool IsHot { get; set; } = true;
    public string? ChannelId { get; set; }
}

public class DouyinzhiboPostRequestForCreate : DouyinzhiboPostRequest
{
    /// <summary>
    /// 渠道id，新建时必传
    /// </summary>
    public string ChannelId { get; set; } = default!;
}

public class DouyinzhiboPostRequestForEdit : DouyinzhiboPostRequest
{
    /// <summary>
    /// 直播id，编辑/去播/用户列表 必传
    /// </summary>
    public string ZhiboId { get; set; } = default!;

    public string ChannelId { get; set; } = default!;

    public UserTypeEnum? UserType { get; set; } = UserTypeEnum.直播端;
}

public enum UserTypeEnum
{
    直播端,用户端
}

public class DouyinzhiboPostRequest
{
    /// <summary>
    /// 大类
    /// </summary>
    public PostsByGroupRequest? Group { get; set; }

    /// <summary>
    /// 是否下架
    /// </summary>
    public bool IsRemoved { get; set; } = false;
}

public class GroupRequest
{
    public string? ZhiboId { get; set; }
    public string? ChannelId { get; set; }

    public UserTypeEnum? UserType { get; set; } = UserTypeEnum.直播端;
}

public class ZhiboInfo
{
    /// <summary>
    /// 直播id，新建时不传
    /// </summary>
    public string? ZhiboId { get; set; }

    /// <summary>
    /// 直播名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 直播开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 直播结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 直播封面地址
    /// </summary>
    public string? CoverUrl { get; set; }

    /// <summary>
    /// 渠道id
    /// </summary>
    public string? ChannelId { get; set; }

    /// <summary>
    /// 已选岗位数量
    /// </summary>
    public int PostCount { get; set; } = 0;
}

public class ZhiboEditResponse : ZhiboInfo
{
    
}

public class ZhiboListResponse
{
    public List<ZhiboInfo> Rows { get; set; } = new List<ZhiboInfo>();
}

public enum ZhiboTypeEnum
{
    未开始,
    进行中,
    已结束
}

public class ZhiboSaveRequest : ZhiboInfo
{
    /// <summary>
    /// 上/下架岗位列表
    /// </summary>
    public List<RemoveTeamposts>? RemoveTeamposts { get; set; }
}

public class ZhiboListRequest
{
    /// <summary>
    /// 渠道id
    /// </summary>
    public string ChannelId { get; set; } = default!;
    public ZhiboTypeEnum? ZhiboType { get; set; }
}

public class RemoveTeamposts
{
    public string TeampostId { get; set; } = default!;

    public bool IsRemove { get; set; } = true;
}

public class RemoveOrSpeekTeamposts
{
    public string TeampostId { get; set; } = default!;

    public bool IsRemove { get; set; } = true;

    public bool IsSpeek { get; set; } = false;
}

public class DouyinGroupType
{
    public string? TeamPostId { get; set; }
    public string? Category { get; set; }
    public string? CategoryName { get; set; }
    public string? RegionId { get; set; }
    public string? RegionName { get; set; }
    public string? AgentEntId { get; set; }
    public string? AgentEntName { get; set; }
    public bool? IsSpeek { get; set; } = false;
}
