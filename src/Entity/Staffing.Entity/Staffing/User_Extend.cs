﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// hr扩展表
/// </summary>
[Table("user_extend")]
public class User_Extend
{
    [Key]
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 当前顾问（暂不用）
    /// </summary>
    public string AdviserId { get; set; } = string.Empty;

    /// <summary>
    /// 登录时间
    /// </summary>
    public DateTime LoginTime { get; set; } = DateTime.Now;

    // /// <summary>
    // /// 最近使用app
    // /// </summary>
    // public string? LatelyApp { get; set; }

    /// <summary>
    /// 注册Ip
    /// </summary>
    public string RegistrationIp { get; set; } = string.Empty;

    /// <summary>
    /// 邀约人
    /// </summary>
    public string? Inviter { get; set; }

    public SeekerOrHr? InviterType { get; set; }

    /// <summary>
    /// 期望职位
    /// </summary>
    public string? DesiredPost { get; set; }

    /// <summary>
    /// 期望职位
    /// </summary>
    public string? DesiredPostName { get; set; }

    /// <summary>
    /// 顾问自动协同项目
    /// </summary>
    public int? HrAutoTeam { get; set; }


    public User User { get; set; } = default!;
    public User_Hr Adviser { get; set; } = default!;
}