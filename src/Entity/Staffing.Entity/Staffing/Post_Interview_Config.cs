﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 职位面试配置表
/// </summary>
[Table("post_interview_config")]
public class Post_Interview_Config
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 职位ID
    /// </summary>
    public string PostId { get; set; } = default!;

    // /// <summary>
    // /// 状态
    // /// </summary>
    // public ActiveStatus Status { get; set; } = ActiveStatus.Inactive;

    /// <summary>
    /// 面试方式
    /// </summary>
    public RecruitInterviewForms InterviewMode { get; set; } = RecruitInterviewForms.Scene;

    /// <summary>
    /// 面试官Id
    /// </summary>
    public string? InterviewerId { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 地区ID
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 坐标
    /// </summary>
    public NetTopologySuite.Geometries.Point Location { get; set; } = new NetTopologySuite.Geometries.Point(0, 0);

    // /// <summary>
    // /// 联络人姓名
    // /// </summary>
    // public string? ContactName { get; set; }

    // /// <summary>
    // /// 联络人电话
    // /// </summary>
    // public string? ContactPhone { get; set; }

    /// <summary>
    /// 可提前几天预约
    /// </summary>
    public int AdvanceDays { get; set; }

    // /// <summary>
    // /// 面试时间
    // /// </summary>
    // public List<PostInterviewTimeModel>? InterviewTime { get; set; } = new List<PostInterviewTimeModel>();

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 详细地址
    /// </summary>
    public string? AddressDetail { get; set; } 

    public Project_Interviewer Project_Interviewer { get; set; } = default!;
}