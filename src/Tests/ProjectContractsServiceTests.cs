using Microsoft.EntityFrameworkCore;
using Moq;
using Xunit;
using Infrastructure.Exceptions;
using Staffing.Core.Services.Hr;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Model.Hr.Project;
using Infrastructure.Common;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Config;
using Infrastructure.CommonService.Ndn;

namespace Staffing.Tests.Services.Hr;

/// <summary>
/// ProjectContractsService 单元测试
/// </summary>
public class ProjectContractsServiceTests
{
    private readonly Mock<StaffingContext> _mockContext;
    private readonly Mock<RequestContext> _mockUser;
    private readonly Mock<IDbContextFactory<NoahContext>> _mockNoahContextFactory;
    private readonly Mock<IOptionsSnapshot<ConfigManager>> _mockConfig;
    private readonly Mock<IHostEnvironment> _mockHostEnvironment;
    private readonly Mock<INdnService> _mockNdnService;
    private readonly Mock<LogManager> _mockLog;
    private readonly ProjectContractsService _service;

    public ProjectContractsServiceTests()
    {
        _mockContext = new Mock<StaffingContext>();
        _mockUser = new Mock<RequestContext>();
        _mockNoahContextFactory = new Mock<IDbContextFactory<NoahContext>>();
        _mockConfig = new Mock<IOptionsSnapshot<ConfigManager>>();
        _mockHostEnvironment = new Mock<IHostEnvironment>();
        _mockNdnService = new Mock<INdnService>();
        _mockLog = new Mock<LogManager>();

        _mockConfig.Setup(x => x.Value).Returns(new ConfigManager());

        _service = new ProjectContractsService(
            _mockContext.Object,
            _mockUser.Object,
            _mockNoahContextFactory.Object,
            _mockConfig.Object,
            _mockHostEnvironment.Object,
            _mockLog.Object,
            _mockNdnService.Object
        );
    }

    [Fact]
    public void HasDefaultContract_WithValidProjectIdAndDefaultContract_ReturnsTrue()
    {
        // Arrange
        var projectId = "test-project-id";
        var contracts = new List<Project_Contract>
        {
            new Project_Contract { ProjectId = projectId, IsDefault = 1 },
            new Project_Contract { ProjectId = projectId, IsDefault = 0 }
        }.AsQueryable();

        var mockSet = new Mock<DbSet<Project_Contract>>();
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.Provider).Returns(contracts.Provider);
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.Expression).Returns(contracts.Expression);
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.ElementType).Returns(contracts.ElementType);
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.GetEnumerator()).Returns(contracts.GetEnumerator());

        _mockContext.Setup(c => c.Project_Contract).Returns(mockSet.Object);

        // Act
        var result = _service.HasDefaultContract(projectId);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.HasDefault);
    }

    [Fact]
    public void HasDefaultContract_WithValidProjectIdAndNoDefaultContract_ReturnsFalse()
    {
        // Arrange
        var projectId = "test-project-id";
        var contracts = new List<Project_Contract>
        {
            new Project_Contract { ProjectId = projectId, IsDefault = 0 },
            new Project_Contract { ProjectId = projectId, IsDefault = 0 }
        }.AsQueryable();

        var mockSet = new Mock<DbSet<Project_Contract>>();
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.Provider).Returns(contracts.Provider);
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.Expression).Returns(contracts.Expression);
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.ElementType).Returns(contracts.ElementType);
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.GetEnumerator()).Returns(contracts.GetEnumerator());

        _mockContext.Setup(c => c.Project_Contract).Returns(mockSet.Object);

        // Act
        var result = _service.HasDefaultContract(projectId);

        // Assert
        Assert.NotNull(result);
        Assert.False(result.HasDefault);
    }

    [Fact]
    public void HasDefaultContract_WithValidProjectIdAndNoContracts_ReturnsFalse()
    {
        // Arrange
        var projectId = "test-project-id";
        var contracts = new List<Project_Contract>().AsQueryable();

        var mockSet = new Mock<DbSet<Project_Contract>>();
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.Provider).Returns(contracts.Provider);
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.Expression).Returns(contracts.Expression);
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.ElementType).Returns(contracts.ElementType);
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.GetEnumerator()).Returns(contracts.GetEnumerator());

        _mockContext.Setup(c => c.Project_Contract).Returns(mockSet.Object);

        // Act
        var result = _service.HasDefaultContract(projectId);

        // Assert
        Assert.NotNull(result);
        Assert.False(result.HasDefault);
    }

    [Fact]
    public void HasDefaultContract_WithNullProjectId_ThrowsBadRequestException()
    {
        // Act & Assert
        var exception = Assert.Throws<BadRequestException>(() => _service.HasDefaultContract(null));
        Assert.Equal("项目ID不能为空", exception.Message);
    }

    [Fact]
    public void HasDefaultContract_WithEmptyProjectId_ThrowsBadRequestException()
    {
        // Act & Assert
        var exception = Assert.Throws<BadRequestException>(() => _service.HasDefaultContract(""));
        Assert.Equal("项目ID不能为空", exception.Message);
    }

    [Fact]
    public void HasDefaultContract_WithWhitespaceProjectId_ThrowsBadRequestException()
    {
        // Act & Assert
        var exception = Assert.Throws<BadRequestException>(() => _service.HasDefaultContract("   "));
        Assert.Equal("项目ID不能为空", exception.Message);
    }

    [Fact]
    public void HasDefaultContract_WithNonExistentProjectId_ReturnsFalse()
    {
        // Arrange
        var projectId = "non-existent-project-id";
        var contracts = new List<Project_Contract>
        {
            new Project_Contract { ProjectId = "other-project-id", IsDefault = 1 }
        }.AsQueryable();

        var mockSet = new Mock<DbSet<Project_Contract>>();
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.Provider).Returns(contracts.Provider);
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.Expression).Returns(contracts.Expression);
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.ElementType).Returns(contracts.ElementType);
        mockSet.As<IQueryable<Project_Contract>>().Setup(m => m.GetEnumerator()).Returns(contracts.GetEnumerator());

        _mockContext.Setup(c => c.Project_Contract).Returns(mockSet.Object);

        // Act
        var result = _service.HasDefaultContract(projectId);

        // Assert
        Assert.NotNull(result);
        Assert.False(result.HasDefault);
    }
}