﻿using Staffing.Model.CentreService.Report;

namespace Staffing.Core.Interfaces.CentreService;

public interface IReportService
{
    object ExportKzgResume(ExportKzgResume model);
    object ExportKzgResumes(ExportKzgResume model);
    object ExportNkpRecruitSummary(ExportNkpRecruitSummary model);
    object ExportRecruit(ExportRecruit model);
    string GetChannelSeeker(GetChannelSeeker model);
    string GetDailyChannel(string channelId);
    GetDailyUserResponse GetDailyUser(GetDailyUser model);
    string GetNkpSummary(GetNkpSummary model);
    GetNkpWeekReportResponse GetNkpWeekReport(GetNkpWeekReport model);
}