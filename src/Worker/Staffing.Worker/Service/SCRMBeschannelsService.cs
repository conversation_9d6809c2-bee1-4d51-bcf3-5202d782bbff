﻿using System.Text.RegularExpressions;
using Config;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Infrastructure.Proxy;
using Microsoft.EntityFrameworkCore;
using Noah.Aliyun.Storage;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class SCRMBeschannelsService
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _logger;
    private readonly IObjectStorage _objectStorage;
    private readonly CommonDicService _commonDicService;
    private readonly CommonVirtualService _commonVirtualService;
    private readonly SCRMBeschannelsHelper _sCRMBeschannelsHelper;
    private readonly NuoPinApi _nuoPinApi;
    private readonly CacheHelper _cacheHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    public SCRMBeschannelsService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger,
    IObjectStorage objectStorage, CommonDicService commonDicService,
    CommonVirtualService commonVirtualService, SCRMBeschannelsHelper sCRMBeschannelsHelper,
    CacheHelper cacheHelper, IHostEnvironment hostingEnvironment, NuoPinApi nuoPinApi)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _objectStorage = objectStorage;
        _sCRMBeschannelsHelper = sCRMBeschannelsHelper;
        _commonDicService = commonDicService;
        _commonVirtualService = commonVirtualService;
        _cacheHelper = cacheHelper;
        _hostingEnvironment = hostingEnvironment;
        _nuoPinApi = nuoPinApi;
    }

    /// <summary>
    /// 同步数据至致趣百川
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubSyncToSCRMBeschannelTime(CancellationToken cancel)
    {
        //处理消息
        string[]? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            try
            {
                //晚上不执行
                var today = DateTime.Today;
                if (DateTime.Now <= today.AddHours(9) || DateTime.Now >= today.AddHours(22))
                {
                    await Task.Delay(60000);
                    continue;
                }

                msg = MyRedis.Client.SPop<string>(SubscriptionKey.SyncToSCRMBeschannel, 100);

                if (msg == null || msg.Length == 0)
                {
                    await Task.Delay(500);
                    continue;
                }

                using var _context = _contextFactory.CreateDbContext();
                var res = _context.User_Hr_RecommendEnt.Where(x => x.Source == HrRecommendEntSource.网点提交线索 && msg.Contains(x.Id))
                    .Select(x => new
                    {
                        Re = x,
                        AdviserName = x.User_Hr.NickName,
                        UserID = x.User_Hr.UserId,
                        AdviserMobile = x.User_Hr.User.Mobile
                    }).ToList();

                var UserID = res.Select(x => x.UserID).Distinct().ToList();
                var Dd_Depts = _context.User_DingDing.Where(x => UserID.Contains(x.UserId)).Select(x => new { UserID = x.UserId, Mobile = x.DingMobile, DingBranch = x.DingBranch }).ToList();

                //获取网点配置的【致趣百川会议ID】
                var SHs = await _nuoPinApi.GetOutlets();

                //同步数据
                foreach (var item in res)
                {
                    if (string.IsNullOrEmpty(item.Re.OutletId))
                        continue;
                    else
                    {
                        var SH = SHs.FirstOrDefault(x => x.ServiceHallID == item.Re.OutletId);
                        if (SH == null || string.IsNullOrEmpty(SH.BeschannelsMeetingId))
                            continue;

                        var DingBranch = Dd_Depts.FirstOrDefault(x => x.UserID == item.UserID)?.DingBranch ?? string.Empty;

                        await SyncToSCRMBeschannel(item.Re, item.AdviserName, item.AdviserMobile, DingBranch, SH.BeschannelsMeetingId, SH.CityName);
                    }
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.SyncToSCRMBeschannel}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(1000);
        }
    }

    /// <summary>
    /// 修改HR手机号的后续操作逻辑
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubUpdateHrMobileFollowUp(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            try
            {
                //晚上不执行
                var today = DateTime.Today;
                if (DateTime.Now <= today.AddHours(9) || DateTime.Now >= today.AddHours(22))
                {
                    await Task.Delay(60000);
                    continue;
                }

                msg = MyRedis.Client.SPop(SubscriptionKey.HrUpdateMobile);

                if (string.IsNullOrEmpty(msg))
                {
                    await Task.Delay(500);
                    continue;
                }

                int PageIndex = 1, PageSize = 1000, DataCount = 0;
                using var _context = _contextFactory.CreateDbContext();
                do
                {
                    var ids = _context.User_Hr_RecommendEnt.Where(x => x.Source == HrRecommendEntSource.网点提交线索 && x.AdviserId == msg)
                        .Select(x => x.Id).Skip((PageIndex - 1) * PageSize).Take(PageSize).ToArray();

                    if (ids.Count() > 0)
                        MyRedis.Client.SAdd(SubscriptionKey.SyncToSCRMBeschannel, ids);
                } while (DataCount == PageSize);
            }
            catch (Exception e)
            {
                _logger.Error($"SCRMBeschannelsService.SubUpdateHrMobileFollowUp{SubscriptionKey.HrUpdateMobile}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(1000);
        }
    }

    private async Task SyncToSCRMBeschannel(User_Hr_RecommendEnt item, string AdviserName, string AdviserMobile, string DingBranch, string BeschannelMeetingIDs, string? CityName)
    {
        if (string.IsNullOrEmpty(BeschannelMeetingIDs) || !long.TryParse(BeschannelMeetingIDs, out var BeschannelsMeetingId))
            return;

        var member = new Config.CommonModel.SCRMBeschannels.BesChannelsMemmberInput
        {
            Identity = "1",
            SvipSource = "11",//会员来源 1=注册，7=导入，11=API，4=未知
        };

        member.Mobile = item.Mobile;
        member.Name = item.HrName;
        member.Sex = "0";//性别 1=男，2=女，0=未知
        member.field_35039 = item.EntName;//企业名称
        member.field_36193 = AdviserName;// 顾问
        member.field_36194 = AdviserMobile; // 顾问电话
        member.field_36374 = DingBranch;// item.User_Hr.User_DingDing.DingBranch;//Adviser.Dd_User.Dd_User_Dept.OrderByDescending(x => x.Dd_Dept.Level.Length).FirstOrDefault()?.Dd_Dept?.Name;// 顾问部门
        member.field_36375 = item.Source.ToString();// 来源
        member.field_36376 = item.OutletName;// 网点名单
        member.field_36192 = item.OutletName;// 网点名称
        member.field_36191 = CityName ?? string.Empty;
        member.field_36377 = item.Status.ToString();// 状态
        member.field_36378 = item.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss");// 线索时间
        member.field_36379 = item.FeedBack;// 备注

        if (!string.IsNullOrEmpty(item.Remark))
        {
            var proMatch = Regex.Match(item.Remark, "企业所属行业：([^；]*?)；");
            member.field_34480 = proMatch.Success ? proMatch.Value.Replace("企业所属行业：", "").TrimEnd('；') : string.Empty;// 所属行业
            var natrueMatch = Regex.Match(item.Remark, "企业性质：([^；]*?)；");
            member.field_36195 = natrueMatch.Success ? natrueMatch.Value.Replace("企业性质：", "").TrimEnd('；') : string.Empty;// 企业性质
        }

        try
        {
            //判断会员/员工是否不是员工
            if (!(await _sCRMBeschannelsHelper.CheckMemberIsEmploye(member.Mobile)))
            {
                //推送HR信息
                var submitResult = await _sCRMBeschannelsHelper.MemberSubmit(member);
                if (!submitResult.Result)
                {
                    if (submitResult.ErrMsg != "重复调用接口")
                    {
                        _logger.Error($"[企业线索同步至致趣百川]消息处理出错", $"向致趣百川推送HR信息失败，推送参数:{member.ToJsonString()}，响应结果：{submitResult.ErrMsg}");
                        return;
                    }
                }
            }

            var meetingEntry = new Config.CommonModel.SCRMBeschannels.BesChannelsMeetingEntryInput
            {
                Mobile = member.Mobile,
                MeetingId = BeschannelsMeetingId,
                EntryTime = item.CreatedTime.ToUnixTimeSeconds(),
                Client = 2,//报名会议的客户端 1微信端 2pc端
                           //ChannelName = "诺聘招聘会",
                MeetingForm = 1, //报名方式 1线上 2线下
                RegisterChannel = "诺聘网点"
            };
            //报名会议
            var entryResult = await _sCRMBeschannelsHelper.MeetingEntry(meetingEntry);
            if (entryResult.Result == Config.CommonModel.SCRMBeschannels.OutputEnum.手机号未注册)
            {
                //推送HR信息
                var submitResult = await _sCRMBeschannelsHelper.MemberSubmit(member);
                if (!submitResult.Result
                    && submitResult.ErrMsg != "重复调用接口")
                {
                    _logger.Error($"[企业线索同步至致趣百川]消息处理出错", $"向致趣百川推送HR信息2失败，推送参数:{member.ToJsonString()}，响应结果：{submitResult.ErrMsg}");
                    return;
                }
                else
                    entryResult = await _sCRMBeschannelsHelper.MeetingEntry(meetingEntry);
            }

            if (entryResult.Result == Config.CommonModel.SCRMBeschannels.OutputEnum.失败)
            {
                _logger.Error($"[企业线索同步至致趣百川]消息处理出错", $"报名参会报错，推送参数:{meetingEntry.ToJsonString()}，响应结果：{entryResult.ErrMsg}");
                return;
            }
        }
        catch (Exception ee)
        {
            _logger.Error($"[企业线索同步至致趣百川]消息处理出错", $"报名参会报错，企业线索ID={item.Id},推送参数:{member.ToJsonString()}，异常信息：{ee.ToJsonString()}");
        }
    }
}
