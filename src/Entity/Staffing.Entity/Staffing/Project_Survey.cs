﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.CommonModel.Business;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 项目调研表
/// </summary>
[Table("project_survey")]
public class Project_Survey
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 项目ID
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 职位Id
    /// </summary>
    public string? PostId { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public ProjectSurveyContent? Content { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 内容长度
    /// </summary>
    public int? ContentLength { get; private set;}

    // 导航属性
    public Project Project { get; set; } = default!;
}
