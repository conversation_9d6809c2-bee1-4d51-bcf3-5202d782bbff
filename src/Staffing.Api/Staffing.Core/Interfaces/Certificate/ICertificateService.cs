using Config.CommonModel;
using Staffing.Model.Certificate;

namespace Staffing.Core.Interfaces.Certificate;

public interface ICertificateService
{
    /// <summary>
    /// 证书报名兼容pc报名-小程序报名
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    Task<EmptyResponse> Register(CertificateRegistrationFormReq request);
    /// <summary>
    /// 创建证书
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    EmptyResponse SaveCertificate(CertificateModel request);
    /// <summary>
    /// 获取证书详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    CertificateModel GetCertificateDetail(string id,string? hrId);
    
    /// <summary>
    /// 删除证书
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    EmptyResponse DeleteCertificate(DeleteCertificateReq req);
    /// <summary>
    /// 证书大厅列表
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    CertificateHallResp GetCertificateHall(CertificateHallReq req);
    
    /// <summary>
    /// 生成小程序二维码
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    Task<GenerateQRcodeResp> GenerateQRcode(GenerateQRcodeReq req);

    /// <summary>
    /// 获取推广图片URL
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    Task<GeneratePromotionResp> GetPromotionImgUrl(CertificatePromotionReq request, string ossDir);

    /// <summary>
    /// 保存学习记录
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    void SaveStudyRecord(string StudyUrl, string StudyName,
        string CertificateId, string CertificateName,
        string SpecID, string SpecName);
}