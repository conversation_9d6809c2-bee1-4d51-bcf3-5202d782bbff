﻿namespace Infrastructure.DataHandling;

// public class EnumSchemaFilter : ISchemaFilter
// {
//     public void Apply(OpenApiSchema model, SchemaFilterContext context)
//     {
//         if (context.Type.IsEnum)
//         {
//             model.Enum.Clear();

//             var array = Enum.GetValues(context.Type);
//             // var des = new List<string>();
//             foreach (var item in array)
//             {
//                 var desc = ((Enum)item).GetDescription();
//                 model.Enum.Add(new OpenApiString($"{desc}={(int)item}"));
//                 // des.Add($"{desc}={(int)item}");
//             }
//             // model.Description = string.Join(",", des);
//         }
//     }
// }

// public class SwaggerAuthFilter : Swashbuckle.AspNetCore.SwaggerGen.IOperationFilter
// {
//     public void Apply(OpenApiOperation operation, Swashbuckle.AspNetCore.SwaggerGen.OperationFilterContext context)
//     {
//         // NOTE: This adds the "Padlock" icon to the endpoint in swagger, 
//         //       we can also pass through the names of the policies in the string[]
//         //       which will indicate which permission you require.
//         operation.Security = new List<OpenApiSecurityRequirement>
//             {
//                 new OpenApiSecurityRequirement
//                 {
//                     {
//                         new OpenApiSecurityScheme
//                         {
//                             Name = "Authorization",
//                             Type = SecuritySchemeType.ApiKey,
//                             In = ParameterLocation.Header,
//                             Reference = new OpenApiReference
//                             {
//                                 Type = ReferenceType.SecurityScheme,
//                                 Id = "Authorization"
//                             }
//                          },
//                          new string[] { "global" }
//                     }
//                 }
//             };
//     }
// }