﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 销帮帮合同
/// </summary>
[Table("xbb_contract")]
public class Xbb_Contract
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 数字诺亚id
    /// </summary>
    public long NoahId { get; set; }

    /// <summary>
    /// 数字诺亚编码
    /// </summary>
    public string? NoahCode { get; set; }

    /// <summary>
    /// 合同名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 合同编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 合同分类：\r\naf009cfa-bce5-6ef8-b4c4-d3ff04382dc9：产品类合同；\r\n3cbefb3c-cd15-b48a-d141-de0c6519f889： 采购类合同；\r\nf9ccabd2-6842-4e39-7172-22c3db83b55f：虚拟合同\r\n510d3393-3bd5-17dc-a3ee-f9cb9b734ce8：框架子协议-通信；\r\n209512a9-5769-e66e-48c9-029ef36df57f：框架子协议-人力；\r\n461cb193-540a-3f54-7c2f-84917d91bb48：其它类\r\n08528b58-dd8a-f310-6745-4d84899d6214：合同变更(作废勿选)；\r\na6c9766d-67d2-037b-57fd-60b22f67e9ac：解除合同(作废勿选),新签（采购类合同选择）：f6eb0524-7a32-befc-9cd6-ce116fa6c9f7
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// 合同性质：\\r\\nd733f176-dc9f-657b-7047-8a4026b9b100：新签，\\r\\n7790fe46-2237-9669-c971-e8cb567f5cd8：续签，\\r\\nb70e8e90-e2c1-204e-1593-63e257ab2f99：变更，\\r\\n3c983fd8-901e-b60b-1ae6-b2dbae981b38：解除，\\r\\n3daa5d6e-c12d-1703-a2bc-45a27d25f49f：补充，\\r\\n09fbc921-53c5-74e0-4f0e-3cd2ebe28b0a：虚拟，\\r\\n60787a3b-4c33-533c-83d3-c72e422f9123：其它
    /// </summary>
    public string? Nature { get; set; }

    /// <summary>
    /// 合同状态:1签约，2执行中，3完毕，4终止，5意外终止，6过期，7待续签
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// 已发送消息
    /// </summary>
    public bool? SendNotify { get; set; }

    /// <summary>
    /// 我方签约人
    /// </summary>
    public string? Signatory { get; set; }

    /// <summary>
    /// 我方签约人工号
    /// </summary>
    public string? SignerEmployeeCode { get; set; }

    /// <summary>
    /// 客户名称
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// 产品Id
    /// </summary>
    public string? ProductId { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string? ProductName { get; set; }

    /// <summary>
    /// 合同地址
    /// </summary>
    public List<string>? ContractUrl { get; set; }

    /// <summary>
    /// 诺快聘状态
    /// </summary>
    public XbbNkpStatus? NkpStatus { get; set; }

    /// <summary>
    /// 合同开始时间
    /// </summary>
    public DateTime? BeginTime { get; set; }

    /// <summary>
    /// 合同结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 签订日期
    /// </summary>
    public DateTime? SignDate { get; set; }

    public DateTime NoahUpdatedTime { get; set; }

    public DateTime NoahCreatedTime { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    public string? UpdatedBy { get; set; }

    public DateTime UpdatedTime { get; set; } = DateTime.Now;
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}

public enum XbbContractStatus
{
    签约 = 1, 执行中, 完毕, 终止, 意外终止, 过期
}

public enum XbbNkpStatus
{
    已上诺快聘 = 1, 已上诺优考, 等待上线, 补充合同, 无招聘需求, 已上诺优考诺快聘 = 12
}