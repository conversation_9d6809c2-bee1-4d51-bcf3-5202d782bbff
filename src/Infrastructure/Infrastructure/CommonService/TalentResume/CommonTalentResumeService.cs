using Config.CommonModel;
using Config.CommonModel.TalentResume;
using Config.Enums;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Infrastructure.CommonService;

[Service(ServiceLifetime.Transient)]
public class CommonTalentResumeService
{
    private IDbContextFactory<StaffingContext> _staffingContextFactory;
    private readonly CommonDicService _commonDicService;

    public CommonTalentResumeService(IDbContextFactory<StaffingContext> staffingContextFactory, CommonDicService commonDicService)
    {
        _staffingContextFactory = staffingContextFactory;
        _commonDicService = commonDicService;
    }

    public EmptyResponse TalentResumeReceive(List<TalentResumeRequestModelExtends> model)
    {
        using var _context = _staffingContextFactory.CreateDbContext();
        if (model == null || model.Count == 0)
        {
            throw new BadRequestException("数据为空");
        }

        var checkedDatas = DataCheck(model);

        foreach (var item in checkedDatas)
        {
            var allDatas = _context.Talent_Resume.Where(f => f.Mobile == item.Mobile).ToList();
            if (allDatas != null && allDatas.Any(a => a.Source == item.Source))
            {
                var exist = allDatas.FirstOrDefault(f => f.Source == item.Source);
                exist!.UpdatedTime = DateTime.Now;
                if (item.Status == TalentResumeStatus.Deleted)
                {
                    exist.Status = TalentResumeStatus.Deleted;
                }
                else
                {
                    DataSwitch(item, exist);
                }
            }
            else
            {
                // 相同手机号不同来源，采取覆盖原则：诺优考 > 诺优考旧 > 诺聘 > 诺快聘 > 快招工
                // 由于覆盖规则存在，所以有且仅有一个未删除的
                var otherSource = allDatas?.Where(w => w.Source != item.Source && w.Status != TalentResumeStatus.Deleted).FirstOrDefault();
                var newData = new Talent_Resume();
                if (otherSource != null)
                {
                    if (GetSourcePriority(item.Source) > GetSourcePriority(otherSource.Source))
                    {
                        newData.Status = TalentResumeStatus.UnRegistered;
                        otherSource.Status = TalentResumeStatus.Deleted;
                    }
                    else
                    {
                        newData.Status = TalentResumeStatus.Deleted;
                    }
                }
                DataSwitch(item, newData);
                _context.Add(newData);
            }
        }

        // 批量处理
        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 获取优先级
    /// </summary>
    /// <param name="source"></param>
    /// <returns></returns>
    private int GetSourcePriority(TalentResumeSource source)
    {
        int nyk = 4, nykj = 3, np = 2, nkp = 1, others = 0;
        return source switch { TalentResumeSource.诺优考 => nyk, TalentResumeSource.诺优考旧 => nykj, TalentResumeSource.诺聘 => np, TalentResumeSource.诺快聘 => nkp, _ => others };
    }

    /// <summary>
    /// 数据校验
    /// </summary>
    /// <param name="model"></param>
    /// <exception cref="NotImplementedException"></exception>
    private List<TalentResumeRequestModelExtends> DataCheck(List<TalentResumeRequestModelExtends> model)
    {
        using var _context = _staffingContextFactory.CreateDbContext();
        // 是否注册
        var mobiles = model.Select(s => s.Mobile).ToList();
        var users = _context.User_Seeker.Where(w => mobiles.Contains(w.User.Mobile)).Select(s => new { s.User.Mobile, s.UserId }).ToList();

        var checkedData = new List<TalentResumeRequestModelExtends>();

        // 数据校验
        foreach (var item in model.OrderByDescending(o => o.LastLoginTime))
        {
            if (string.IsNullOrWhiteSpace(item.Mobile))
            {
                continue;
                //throw new BadRequestException("存在数据缺少手机号");
            }

            if (users.Any())
            {
                item.SeekerId = users.FirstOrDefault(f => f.Mobile == item.Mobile)?.UserId;
            }

            if(!checkedData.Any(c => c.Mobile == item.Mobile)) 
            {
                checkedData.Add(item);
            }
        }

        return checkedData;
    }

    /// <summary>
    /// 数据转换
    /// </summary>
    /// <param name="item"></param>
    /// <param name="destiny"></param>
    private void DataSwitch(TalentResumeRequestModelExtends item, Talent_Resume destiny)
    {
        destiny.Source = item.Source;
        destiny.Name = item.Name;
        destiny.Mobile = item.Mobile;
        destiny.HeadPortrait = item.HeadPortrait;
        destiny.Sex = item.Sex;
        destiny.Education = item.Education;
        destiny.Mailbox = item.Mailbox;
        destiny.WeChat = item.WeChat;
        destiny.QQ = item.QQ;
        destiny.Birthday = item.Birthday;
        destiny.WorkTime = item.WorkTime;
        if(destiny.WorkTime == null)
        {
            destiny.WorkTime = GetWorkTime(item.Works);
        }
        destiny.SelfEvaluation = item.SelfEvaluation;
        destiny.Edus = item.Edus;
        destiny.Projects = item.Projects;
        destiny.Works = item.Works;
        destiny.Products = item.Products;
        destiny.Honours = item.Honours;
        destiny.Certificates = item.Certificates;
        destiny.SkillAnalysis = item.SkillAnalysis;
        destiny.IndustryLabel = item.IndustryLabel;
        destiny.PostLabel = item.PostLabel;
        destiny.OtherLabel = item.OtherLabel;
        destiny.Highlights = item.Highlights;
        destiny.Risks = item.Risks;
        destiny.OriginalUrl = item.OriginalUrl;
        destiny.City = item.City;
        destiny.Address = item.Address;
        destiny.RegionId = item.RegionId;
        destiny.Remark = item.Remark;
        var hopes = new List<NHopes>();
        item.Hopes?.ForEach(f =>
        {
            CategoryInfo info = _commonDicService.GetCategory(f.Category);
            hopes.Add(new NHopes
            {
                HopeCity = f.HopeCity,
                RegionId = f.RegionId,
                CategoryLevel1 = info.CategoryLevel1,
                CategoryLevel2 = info.CategoryLevel2,
                CategoryLevel3 = info.CategoryLevel3,
                CategoryLevel1Name = info.CategoryLevel1Name,
                CategoryLevel2Name = info.CategoryLevel2Name,
                CategoryLevel3Name = info.CategoryLevel3Name,
                PositionLevel1Name = f.PositionLevel1Name,
                PositionLevel2Name = f.PositionLevel2Name,
                PositionLevel3Name = f.PositionLevel3Name,
                MinSalary = f.MinSalary,
                MaxSalary = f.MaxSalary,
            });
        });
        destiny.Hopes = hopes;
        destiny.LastLoginTime = item.LastLoginTime;
        destiny.RegistedTime = item.RegistedTime;
        // todo: 计算简历完整度
        destiny.Perfection = CountResumeScore(item);
        destiny.SeekerId = item.SeekerId;
        destiny.ExtendedData = item.ExtendedData;
        destiny.HrIds = item.HrIds;
        destiny.DeptIds = item.DeptIds;
    }

    private DateTime? GetWorkTime(List<Works>? works)
    {
        if(works == null || works?.Count == 0) 
            return null;
        var firstWork = works!.Where(w => w.StartTime != null).OrderBy(o => o.StartTime).FirstOrDefault();
        if (firstWork == null)
            return null;

        return firstWork.StartTime;
    }

    /// <summary>
    /// 计算简历完整度
    /// </summary>
    /// <param name="item"></param>
    /// <returns></returns>
    public int CountResumeScore(TalentResumeRequestModel item)
    {
        var resume = new
        {
            Name = !string.IsNullOrWhiteSpace(item.Name) ? 16 : 0,
            Sex = item.Sex.HasValue ? 6 : 0,
            Birthday = item.Birthday.HasValue ? 6 : 0,
            //Occupation = item..Occupation.HasValue ? 6 : 0, // todo:
            Mobile = true ? 3 : 0,
            WeChatNo = !string.IsNullOrWhiteSpace(item.WeChat) ? 6 : 0,
            Qq = !string.IsNullOrWhiteSpace(item.QQ) ? 1 : 0,
            EMail = !string.IsNullOrWhiteSpace(item.Mailbox) ? 1 : 0,
            Education = item.Education.HasValue ? 10 : 0, // todo:
            //School = !string.IsNullOrWhiteSpace(s.School) ? 8 : 0,//
            School = item.Edus?.Count > 0 ? 18 : 0,
            //GraduationDate = s.GraduationDate.HasValue ? 4 : 0,
            //Major = !string.IsNullOrWhiteSpace(s.Major) ? 6 : 0,
            Describe = !string.IsNullOrWhiteSpace(item.SelfEvaluation) ? 10 : 0,
            //Nature = s.Nature.Count > 0 ? 1 : 0,
            Skill = item.SkillAnalysis != null ? 1 : 0,
            //Appearance = s.Appearance.Count > 0 ? 1 : 0,
            User_Work = item.Works?.Count > 0 ? 22 : 0
        };

        int Score = resume.Name + resume.Sex + resume.Birthday + resume.Mobile
         + resume.WeChatNo + resume.Qq + resume.EMail + resume.Education + resume.School
         + resume.Describe + resume.Skill + resume.User_Work;

        return Score;
    }
}