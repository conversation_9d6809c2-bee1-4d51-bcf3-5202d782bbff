using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

[Table("ums_complex_upload")]
public class Ums_Complex_Upload
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Key]
    [Column("Id")]
    [StringLength(20)]

    public string Id { get; set; } = Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 自助签约流水号
    /// </summary>
    [Column("UmsRegId")]
    [StringLength(20)]
    public string? UmsRegId { get; set; }

    /// <summary>
    /// 用户ID（提交人）
    /// </summary>
    [Required(ErrorMessage = "用户ID不能为空")]
    [Column("UserId")]
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 商户ID
    /// </summary>
    [Required(ErrorMessage = "商户ID不能为空")]
    [Column("MerchantId")]
    [StringLength(20)]
    public string MerchantId { get; set; } = default!;

    /// <summary>
    /// 注册类型：
    /// 00-企业商户,01-个体工商户,
    /// 02-小微商户,03-机关事业单位或社会团体,
    /// 05-民办非企业
    /// </summary>
    [Required(ErrorMessage = "注册类型不能为空")]
    [Column("RegMerType")]
    [StringLength(2)]
    public string RegMerType { get; set; } = default!;

    /// <summary>
    /// 商户营业名称
    /// </summary>
    [Required(ErrorMessage = "商户营业名称不能为空")]
    [Column("ShopName")]
    [StringLength(200)]
    public string ShopName { get; set; } = default!;

    /// <summary>
    /// 商户对外名称
    /// </summary>
    [Column("ExternalName")]
    [StringLength(200)]
    public string? ExternalName { get; set; }

    /// <summary>
    /// 商户类型：
    /// 0-实体商户,1-网络商户,2-实体兼线上
    /// </summary>
    [Column("MchntType")]
    [StringLength(1)]
    public string? MchntType { get; set; }

    /// <summary>
    /// 是否连锁商户：
    /// 0-是,1-否
    /// </summary>
    [Required(ErrorMessage = "是否连锁商户不能为空")]
    [Column("IsChain")]
    [StringLength(1)]
    public string IsChain { get; set; } = default!;

    /// <summary>
    /// 行业类别编码
    /// </summary>
    [Required(ErrorMessage = "行业类别编码不能为空")]
    [Column("MccCode")]
    [StringLength(20)]
    public string MccCode { get; set; } = default!;

    /// <summary>
    /// 社会信用统一代码/营业执照号
    /// </summary>
    [Column("ShopLic")]
    [StringLength(50)]
    public string? ShopLic { get; set; }

    /// <summary>
    /// 法人身份证姓名
    /// </summary>
    [Required(ErrorMessage = "法人姓名不能为空")]
    [Column("LegalName")]
    [StringLength(30)]
    public string LegalName { get; set; } = default!;

    /// <summary>
    /// 法人身份证号
    /// </summary>
    [Required(ErrorMessage = "法人身份证号不能为空")]
    [Column("LegalIdcardNo")]
    [StringLength(36)]
    public string LegalIdcardNo { get; set; } = default!;

    /// <summary>
    /// 法人手机号
    /// </summary>
    [Required(ErrorMessage = "法人手机号不能为空")]
    [Column("LegalMobile")]
    [StringLength(30)]
    public string LegalMobile { get; set; } = default!;

    /// <summary>
    /// 法人邮箱
    /// </summary>
    [Column("LegalEmail")]
    [StringLength(30)]
    public string? LegalEmail { get; set; }

    /// <summary>
    /// 法人证件开始日期(yyyy-MM-dd)
    /// </summary>
    [Required(ErrorMessage = "法人证件开始日期不能为空")]
    [Column("LegalCardBeginDate")]
    [StringLength(10)]
    public DateOnly LegalCardBeginDate { get; set; } = default!;

    /// <summary>
    /// 法人证件截止日期(yyyy-MM-dd)
    /// </summary>
    [Required(ErrorMessage = "法人证件截止日期不能为空")]
    [Column("LegalCardDeadline")]
    [StringLength(10)]
    public DateOnly LegalCardDeadline { get; set; } = default!;

    /// <summary>
    /// 法人性别：
    /// 0-未知,1-男,2-女,
    /// 5-女改男,6-男改女,9-未说明
    /// </summary>
    [Column("LegalSex")]
    [StringLength(1)]
    public string? LegalSex { get; set; }

    /// <summary>
    /// 法人职业代码
    /// </summary>
    [Column("LegalOccupation")]
    [StringLength(2)]
    public string? LegalOccupation { get; set; }

    /// <summary>
    /// 法人职业详细描述(职业为7时必填)
    /// </summary>
    [Column("LegalmanCareerDesc")]
    [StringLength(200)]
    public string? LegalmanCareerDesc { get; set; }

    /// <summary>
    /// 法人家庭地址
    /// </summary>
    [Column("LegalmanHomeAddr")]
    [StringLength(200)]
    public string? LegalmanHomeAddr { get; set; }

    /// <summary>
    /// 开户行行号
    /// </summary>
    [Required(ErrorMessage = "开户行行号不能为空")]
    [Column("BankNo")]
    [StringLength(20)]
    public string BankNo { get; set; } = default!;

    /// <summary>
    /// 账户类型：
    /// 0-个人账户,1-公司账户
    /// </summary>
    [Required(ErrorMessage = "账户类型不能为空")]
    [Column("BankAcctType")]
    [StringLength(1)]
    public string BankAcctType { get; set; } = default!;

    /// <summary>
    /// 开户行账号
    /// </summary>
    [Required(ErrorMessage = "开户行账号不能为空")]
    [Column("BankAcctNo")]
    [StringLength(40)]
    public string BankAcctNo { get; set; } = default!;

    /// <summary>
    /// 开户账号名称
    /// </summary>
    [Required(ErrorMessage = "开户账号名称不能为空")]
    [Column("BankAcctName")]
    [StringLength(127)]
    public string BankAcctName { get; set; } = default!;

    /// <summary>
    /// 营业省份ID
    /// </summary>
    [Required(ErrorMessage = "营业省份不能为空")]
    [Column("ShopProvinceId")]
    [StringLength(10)]
    public string ShopProvinceId { get; set; } = default!;

    /// <summary>
    /// 营业城市ID
    /// </summary>
    [Required(ErrorMessage = "营业城市不能为空")]
    [Column("ShopCityId")]
    [StringLength(10)]
    public string ShopCityId { get; set; } = default!;

    /// <summary>
    /// 营业区县ID
    /// </summary>
    [Required(ErrorMessage = "营业区县不能为空")]
    [Column("ShopCountryId")]
    [StringLength(10)]
    public string ShopCountryId { get; set; } = default!;

    /// <summary>
    /// 营业地址补充信息
    /// </summary>
    [Column("ShopAddrExt")]
    [StringLength(200)]
    public string? ShopAddrExt { get; set; }

    /// <summary>
    /// 控股股东姓名 商户类型为小微或机关事业单位时不必传，其他商户类型必传
    /// </summary>
    [Column("ShareholderName")]
    [StringLength(20)]
    public string? ShareholderName { get; set; }

    /// <summary>
    /// 控股股东证件号
    /// </summary>
    [Column("ShareholderCertno")]
    [StringLength(20)]
    public string? ShareholderCertno { get; set; }

    /// <summary>
    /// 控股股东证件开始日期(yyyy-MM-dd)
    /// </summary>
    [Column("ShareholderCertBeginDate")]
    [StringLength(10)]
    public DateOnly? ShareholderCertBeginDate { get; set; }

    /// <summary>
    /// 控股股东证件有效期(yyyy-MM-dd)
    /// </summary>
    [Column("ShareholderCertExpire")]
    [StringLength(10)]
    public DateOnly? ShareholderCertExpire { get; set; }

    /// <summary>
    /// 控股股东证件类型：
    /// 1-身份证,2-护照等 3 军官证 4 警官证  5 士兵证 6 台湾居民来往大陆通行证 7 回乡证 8 港澳居民来往内地通行证 9 外国人永久居留身份证 10 港澳居民居住证 11 营业执照 12 组织机构代码证 13 税务登记证 14 商业登记证 15 民办非企业登记证书 16 批文证明 17 事业单位法人证书 18 台湾居民居住证
    /// 默认值：1
    /// </summary>
    [Column("ShareholderCertType")]
    [StringLength(2)]
    public string ShareholderCertType { get; set; } = "1";

    /// <summary>
    /// 控股股东家庭地址
    /// </summary>
    [Column("ShareholderHomeAddr")]
    [StringLength(60)]
    public string? ShareholderHomeAddr { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
    [Column("ContactName")]
    [StringLength(16)]
    public string? ContactName { get; set; }

    /// <summary>
    /// 联系人手机号
    /// </summary>
    [Column("ContactMobile")]
    [StringLength(11)]
    public string? ContactMobile { get; set; }

    /// <summary>
    /// 联系人证件类型
    /// </summary>
    [Column("ContactCardType")]
    [StringLength(2)]
    public string? ContactCardType { get; set; }

    /// <summary>
    /// 联系人证件号
    /// </summary>
    [Column("ContactCardNo")]
    [StringLength(36)]
    public string? ContactCardNo { get; set; }

    /// <summary>
    /// 平台名称
    /// </summary>
    [Column("AccesserName")]
    [StringLength(100)]
    public string? AccesserName { get; set; }

    /// <summary>
    /// 平台全称
    /// </summary>
    [Column("AccesserDesc")]
    [StringLength(100)]
    public string? AccesserDesc { get; set; }

    /// <summary>
    /// 商户传真
    /// </summary>
    [Column("Fax")]
    [StringLength(20)]
    public string? Fax { get; set; }

    /// <summary>
    /// 终端维护经理
    /// </summary>
    [Column("LastTerminalManager")]
    [StringLength(30)]
    public string? LastTerminalManager { get; set; }

    /// <summary>
    /// 客户维护经理
    /// </summary>
    [Column("LastClientManager")]
    [StringLength(30)]
    public string? LastClientManager { get; set; }

    /// <summary>
    /// 所属服务区域
    /// </summary>
    [Column("ServiceDistrict")]
    [StringLength(10)]
    public string? ServiceDistrict { get; set; }

    /// <summary>
    /// 细分服务区域
    /// </summary>
    [Column("DetailDistrict")]
    [StringLength(10)]
    public string? DetailDistrict { get; set; }

    /// <summary>
    /// 发展部门
    /// </summary>
    [Column("DevelopingDept")]
    [StringLength(10)]
    public string? DevelopingDept { get; set; }

    /// <summary>
    /// 发展人
    /// </summary>
    [Column("DevelopingPersonId")]
    [StringLength(20)]
    public string? DevelopingPersonId { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [Column("Remark")]
    [StringLength(200)]
    public string? Remark { get; set; }

    /// <summary>
    /// 二维码ID列表(最多7个,逗号分隔)
    /// </summary>
    [Column("UmsQrcodeList")]
    [StringLength(200)]
    public string? UmsQrcodeList { get; set; }

    /// <summary>
    /// 创建时间
    /// 默认值：当前时间
    /// </summary>
    [Required]
    [Column("CreateTime")]
    public DateTime CreateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// 默认值：当前时间
    /// 更新时自动设置为当前时间
    /// </summary>
    [Required]
    [Column("UpdateTime")]
    public DateTime UpdateTime { get; set; } = DateTime.Now;

    /// <summary>
    ///  上传状态
    /// </summary>
    public StatusEnum Status { get; set; } = StatusEnum.上传中;

    /// <summary>
    /// 进件申请状态。00：签约中 01：签约成功 02：入网审核中 03：入网成功 04：入网失败 05：对公账户待验证或异常 06：风控审核中 28：资料验证失败 31：冻结账户 99：其它错误
    /// </summary>
    public ApplyStatusEnum? ApplyStatus { get; set; } = ApplyStatusEnum.签约中;
    /// <summary>
    /// 申请状态对应的描述信息
    /// </summary>
    public string? ApplyStatusMsg { get; set; }
    /// <summary>
    /// 商户号
    /// </summary>
    public string? MerNo { get; set; }
    /// <summary>
    /// 失败原因
    /// </summary>
    public string? FailReason { get; set; }

    /// <summary>
    /// 账户号 转账支付使用
    /// </summary>
    public string? AcctNo { get; set; }

    /// <summary>
    /// 支付账户开户状态
    /// </summary>
    public SubAccountOpenStatus OpenStatus { get; set; } = SubAccountOpenStatus.审核中;
}

/// <summary>
/// 受益人信息类
/// </summary>
public class Beneficiary
{
    /// <summary>
    /// 受益人姓名
    /// </summary>
    public string? BnfName { get; set; }

    /// <summary>
    /// 受益人证件号
    /// </summary>
    public string? BnfCertno { get; set; }

    /// <summary>
    /// 受益人证件开始日期
    /// </summary>
    public DateOnly? BnfCertBeginDate { get; set; }

    /// <summary>
    /// 受益人证件有效期
    /// </summary>
    public DateOnly? BnfCertExpire { get; set; }

    /// <summary>
    /// 受益人证件类型
    /// </summary>
    public string? BnfCertType { get; set; }

    /// <summary>
    /// 受益人家庭地址
    /// </summary>
    public string? BnfHomeAddr { get; set; }

    /// <summary>
    /// 受益人电话
    /// </summary>
    public string? BnfMobile { get; set; }

    /// <summary>
    /// 受益人性别
    /// </summary>
    public string? BnfSex { get; set; }

    /// <summary>
    /// 受益人出生日期
    /// </summary>
    public DateOnly? BnfBirthday { get; set; }

    /// <summary>
    /// 是否为特定自然人
    /// </summary>
    public string? BnfNatrlPrsn { get; set; }

    /// <summary>
    /// 受益人图片列表
    /// </summary>
    public List<Document>? BnfPicList { get; set; }
}

/// <summary>
/// 证件材料信息类
/// </summary>
public class Document
{
    /// <summary>
    /// 上传图片类型
    /// </summary>
    public string? DocumentType { get; set; }

    /// <summary>
    /// 图片名称
    /// </summary>
    public string? DocumentName { get; set; }

    /// <summary>
    /// 图片路径
    /// </summary>
    public string? FilePath { get; set; }

    /// <summary>
    /// 图片大小
    /// </summary>
    public string? FileSize { get; set; }
}

/// <summary>
/// 分账关系信息类
/// </summary>
public class SettlementRelation
{
    /// <summary>
    /// 分账主商户ID
    /// </summary>
    public string? MasterMchntId { get; set; }
}