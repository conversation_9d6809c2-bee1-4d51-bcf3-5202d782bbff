namespace Config.CommonModel.JavaDataApi;


//职位浏览消息
public class KafkaViewPost
{
    public string? id { get; set; }

    public string? userId { get; set; }

    /// <summary>
    /// 协同人员
    /// </summary>
    public string? hrId { get; set; }

    /// <summary>
    /// 职位Id
    /// </summary>
    public string? postId { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? postName { get; set; }

    /// <summary>
    /// 职位类别
    /// </summary>
    public int? postTypeId { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public string? projectId { get; set; }

    /// <summary>
    /// 职位类别名称
    /// </summary>
    public string? postTypeName { get; set; }

    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? teamPostId { get; set; }

    /// <summary>
    /// 协同项目Id
    /// </summary>
    public string? teamProjectId { get; set; }

    /// <summary>
    /// 1=浏览
    /// </summary>
    public int? action { get; set; }

    public long actionTime { get; set; }
}