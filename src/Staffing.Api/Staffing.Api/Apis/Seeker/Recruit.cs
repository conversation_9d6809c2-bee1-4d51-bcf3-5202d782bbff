using Infrastructure.Extend;
using Staffing.Model.Seeker.Recruit;
using Staffing.Core.Interfaces.Seeker;

namespace Staffing.Api.Apis.Seeker;

[Service(ServiceLifetime.Transient)]
public class RecruitRouter : ISeekerRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("recruit").WithTags("招聘");

        group.MapPost("/jdinfo", (GetJDInfo model, IRecruitService rep) => rep.GetJDInfo(model))
        .WithMetadata(new Config.ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "京东家政信息" });

        group.MapPost("/jd/register/info", (IRecruitService rep) => rep.GetRegisterJD())
        .WithMetadata(new Config.ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "我的京东家政报名信息" });

        group.MapPost("/jd/register", async (RegisterJDReq model, IRecruitService rep) => await rep.RegisterJDAsync(model))
       .WithOpenApi(operation => new(operation) { Summary = "京东家政报名" });

        group.MapPost("/deliver", (DeliverResume model, IRecruitService rep) => rep.DeliverResume(model))
        .WithOpenApi(operation => new(operation) { Summary = "投递简历" });

        group.MapPost("/deliver/cancel", (CancelDeliver model, IRecruitService rep) => rep.CancelDeliver(model))
        .WithOpenApi(operation => new(operation) { Summary = "取消投递" });

        group.MapGet("/delivers", ([AsParameters] GetDelivers model, IRecruitService rep) => rep.GetDelivers(model))
        .WithOpenApi(operation => new(operation) { Summary = "投递记录" });

        group.MapGet("/deliver/{id}", (string id, IRecruitService rep) => rep.GetDeliver(id))
        .WithOpenApi(operation => new(operation) { Summary = "投递详情" });

        group.MapPost("/complaint", (ComplainRequest model, IRecruitService rep) => rep.Complain(model))
        .WithOpenApi(operation => new(operation) { Summary = "投诉" });

        group.MapPost("/interview", (SeekerSetInterview model, IRecruitService rep) => rep.SeekerSetInterview(model))
        .WithOpenApi(operation => new(operation) { Summary = "面试反馈" });

        group.MapGet("/interview", ([AsParameters] SeekerGetInterview model, IRecruitService rep) => rep.SeekerGetInterview(model))
        .WithOpenApi(operation => new(operation) { Summary = "面试详情" });

        group.MapGet("/offernotice/{offerId}", (string offerId, IRecruitService rep) => rep.GetOfferNotice(offerId))
        .WithOpenApi(operation => new(operation) { Summary = "用户端获取offer通知" });
    }
}