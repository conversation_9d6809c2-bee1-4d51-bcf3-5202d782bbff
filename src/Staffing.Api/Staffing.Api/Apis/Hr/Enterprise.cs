using Config;
using Config.Enums;
using Infrastructure.Extend;
using Staffing.Model.Hr.Enterprise;
using Staffing.Core.Interfaces.Hr;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class EnterpriseRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("ent").WithTags("企业");
        group.MapGet("/org", (IEntService rep) => rep.GetEntOrg())
            .WithOpenApi(operation => new(operation) { Summary = "获取组织架构" });

        group.MapPost("/org", (AddEntOrg model, IEntService rep) => rep.AddEntOrg(model))
            .WithMetadata(new Powers { Power = Power.Admin })
            .WithOpenApi(operation => new(operation) { Summary = "创建组织架构" });

        group.MapPut("/org", (UpdateEntOrg model, IEntService rep) => rep.UpdateEntOrg(model))
            .WithMetadata(new Powers { Power = Power.Admin })
            .WithOpenApi(operation => new(operation) { Summary = "更新组织架构" });

        group.MapDelete("/org/{OrgId}", ([AsParameters] DeleteEntOrg model, IEntService rep) => rep.DeleteEntOrg(model))
            .WithMetadata(new Powers { Power = Power.Admin })
            .WithOpenApi(operation => new(operation) { Summary = "删除组织架构" });

        group.MapGet("/role", ([AsParameters] GetEntRoles model, IEntService rep) => rep.GetEntRoles(model))
            .WithMetadata(new Powers { Power = Power.Admin })
            .WithOpenApi(operation => new(operation) { Summary = "查询公司角色" });

        group.MapPost("/role", (AddEntRole model, IEntService rep) => rep.AddEntRole(model))
            .WithMetadata(new Powers { Power = Power.Admin })
            .WithOpenApi(operation => new(operation) { Summary = "创建角色" });

        group.MapPut("/role", (UpdateEntRole model, IEntService rep) => rep.UpdateEntRole(model))
            .WithMetadata(new Powers { Power = Power.Admin })
            .WithOpenApi(operation => new(operation) { Summary = "更新角色" });

        group.MapPut("/role/hr", (UpdateUserRole model, IEntService rep) => rep.UpdateUserRole(model))
            .WithMetadata(new Powers { Power = Power.Admin })
            .WithOpenApi(operation => new(operation) { Summary = "更新角色人员" });

        group.MapDelete("/role/{RoleId}", ([AsParameters] DeleteEntRole model, IEntService rep) => rep.DeleteEntRole(model))
            .WithMetadata(new Powers { Power = Power.Admin })
            .WithOpenApi(operation => new(operation) { Summary = "删除角色" });

        group.MapGet("/users/less", ([AsParameters] GetEntHrLess model, IEntService rep) => rep.GetEntHrLess(model))
            .WithOpenApi(operation => new(operation) { Summary = "公司人员列表（全部，简版）" });

        group.MapGet("/users", ([AsParameters] GetEntUsers model, IEntService rep) => rep.GetEntUsers(model))
            .WithOpenApi(operation => new(operation) { Summary = "公司人员列表(分页、详细)" });

        group.MapPut("/hr/update", (UpdateHr model, IEntService rep) => rep.UpdateHr(model))
            .WithMetadata(new Powers { Power = Power.Admin })
            .WithOpenApi(operation => new(operation) { Summary = "更新hr" });

        group.MapPost("/merchants", (GetEnterpriseMerchants model, IEntService rep) => rep.GetEnterpriseMerchants(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取企业商户列表" });

        group.MapPost("/merchant/detail", (GetEnterpriseMerchantDetail model, IEntService rep) => rep.GetEnterpriseMerchantDetail(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取企业商户详情" });

        group.MapPost("/merchants/update", async (UpdateEnterpriseMerchantModel model, IEntService rep) => await rep.UpdateMerchants(model))
            .WithOpenApi(operation => new(operation) { Summary = "更新企业商户信息" });
    }
}