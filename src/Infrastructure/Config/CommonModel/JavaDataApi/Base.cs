namespace Config.CommonModel.JavaDataApi;

public class JavaApiModel<T>
{
    public T? data { get; set; }

    public int code { get; set; }
    public bool success { get; set; }
    public string? message { get; set; }
}

public class JavaQueryRequest
{
    private int _pageSize;
    private int _pageIndex;
    public JavaQueryRequest()
    {

        pageSize = 20;
        pageNum = 0;
    }

    /// <summary>
    /// 每次查询多少条(默认20)
    /// </summary>
    public int pageSize
    {
        get { return _pageSize; }
        set { _pageSize = (value > 100 ? 100 : value); }
    }

    /// <summary>
    /// 页
    /// </summary>
    public int pageNum
    {
        get { return _pageIndex; }
        set { _pageIndex = (value < 1 ? 1 : value); }
    }
}