﻿using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.Hr;
using Config;
using Staffing.Model.Hr.Post;
using Config.CommonModel;
using Infrastructure.CommonService;
using Infrastructure.Proxy;
using Staffing.Core.Interfaces.Common;
using Microsoft.EntityFrameworkCore;
using Config.CommonModel.Business;
using ServiceStack;

namespace Staffing.Core.Services.Hr;

[Service(ServiceLifetime.Transient)]
public class PostService : IPostService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CommonDicService _commonDicService;
    private readonly ICommonService _commonService;
    private readonly CommonCacheService _commonCacheService;
    private readonly NoahData _noahData;
    private readonly WeChatHelper _weChatHelper;
    private readonly JavaDataApi _javaDataApi;
    private readonly CommonProjectService _commonProjectService;
    private readonly MicroServiceApi _microServiceApi;
    public PostService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, LogManager log, CommonDicService commonDicService,
        NoahData noahData, ICommonService commonService,
        CommonCacheService commonCacheService, WeChatHelper weChatHelper, JavaDataApi javaDataApi,
        CommonProjectService commonProjectService, MicroServiceApi microServiceApi)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _log = log;
        _commonDicService = commonDicService;
        _noahData = noahData;
        _commonService = commonService;
        _commonCacheService = commonCacheService;
        _weChatHelper = weChatHelper;
        _javaDataApi = javaDataApi;
        _commonProjectService = commonProjectService;
        _microServiceApi = microServiceApi;
    }

    public GetHallPostResponse GetHallPost(GetHallPost model)
    {
        var result = new GetHallPostResponse();

        var predicate = GetHallPostPredicate(model);

        var sql = _context.Post.Where(predicate);

        result.Total = sql.Count();

        // 排序改为优先内部职位，优先佣金高的
        //_log.Info("", "");
        result.Rows = sql
        .OrderBy(x => x.Source)
        // .ThenByDescending(x => x.Money)
        .ThenByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetHallPostDetail
        {
            Hr = new HrModel
            {
                HrId = s.Project.HrId,
                HrName = s.Project.User_Hr.NickName,
                Avatar = s.Project.User_Hr.Avatar,
                Post = s.Project.User_Hr.Post,
                LoginTime = s.Project.User_Hr.User_Extend.LoginTime,
                EntName = s.Project.User_Hr.Enterprise.Name
            },
            AgentEnt = new GetAgentEntDetail
            {
                Name = s.Agent_Ent.Name,
                DisplayName = s.Agent_Ent.DisplayName,
                AgentEntId = s.AgentEntId,
                Scale = (EnterpriseScale?)s.Agent_Ent.Scale ?? EnterpriseScale.二十人以下,
                Abbr = s.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Agent_Ent.Abbr : s.Agent_Ent.DisplayName,
                LogoUrl = s.Agent_Ent.LogoUrl,
                ScaleName = s.Agent_Ent.Scale.GetDescription(),
                Industry = s.Agent_Ent.Industry.ToString(),
                IndustryName = s.Agent_Ent.Dic_Industry.Name
            },
            RewardType = s.RewardType,
            RewardTypeName = s.RewardType.GetDescription(),
            PaymentCycle = s.PaymentCycle,
            PaymentCycleName = s.PaymentCycle.GetDescription(),
            PaymentDuration = s.PaymentDuration,
            PostAutoId = s.AutoId,
            ProjectId = s.ProjectId,
            Name = s.Name,
            Status = s.Status,
            CreatedTime = s.CreatedTime,
            UpdatedTime = s.UpdatedTime,
            PaymentNode = s.PaymentNode,
            PaymentType = s.Project.PaymentType,
            PaymentDays = s.PaymentDays,
            StatusName = s.Status.GetDescription(),
            Address = s.Address,
            Category = s.Category,
            CategoryName = s.Dic_Post.Name,
            Department = s.Department,
            Describe = s.Describe,
            Education = s.Education,
            EducationName = s.Education.GetDescription(),
            Highlights = s.Highlights,
            Tags = s.Tags,
            Welfare = s.Welfare,
            WelfareCustom = s.WelfareCustom,
            Lat = s.Location.X,
            Lng = s.Location.Y,
            MaxSalary = s.MaxSalary,
            MinSalary = s.MinSalary,
            Money = s.Money ?? 0,
            PostId = s.PostId,
            RecruitNumber = s.DeliveryNumber,
            RegionId = s.RegionId,
            Salary = s.Salary,
            SettlementType = s.SettlementType,
            WorkingDays = s.WorkingDays,
            WorkingHours = s.WorkingHours,
            MinMonths = s.MinMonths,
            DaysPerWeek = s.DaysPerWeek,
            GraduationYear = s.GraduationYear,
            WorkNature = s.WorkNature,
            SalaryType = s.SalaryType,
            SalaryTypeName = s.SalaryType.GetDescription(),
            WorkNatureName = s.WorkNature.GetDescription(),
            Sex = s.Sex,
            MinAge = s.MinAge,
            MaxAge = s.MaxAge,
            RegistrationNum = s.Post_Extend.DeliveryNum,
            DeliveriesNum = s.Post_Extend.InductionNum,
            ScreeningNum = s.Post_Extend.HrScreeningNum,
            InterviewNum = s.Post_Extend.InterviewNum,
            InductionNum = s.Post_Extend.InductionNum,
            InterviewStatus = s.InterviewStatus,
            InterviewDashboard = new PostRecentInterviews
            {
                RecentDeliveryRate = s.Post_Extend.RecentDeliveryRate,
                HistoryBillingRate = s.Post_Extend.HistoryBillingRate
            },
            ProjectTypeName = s.Project.Type.GetDescription(),
            IsSalesCommission = s.IsSalesCommission,

        }).ToList();

        var postIds = result.Rows.Select(s => s.PostId).ToList();
        var projectIds = result.Rows.Select(s => s.ProjectId).ToList();

        var myPosts = _context.Post_Team.Where(x => x.Project_Team.HrId == _user.Id && postIds.Contains(x.PostId))
        .Select(s => s.PostId).ToList();

        var shareProjets = _context.Project_Team.Where(x => projectIds.Contains(x.ProjectId))
        .GroupBy(g => g.ProjectId)
        .Select(s => new
        {
            s.Key,
            Ct = s.Count()
        }).ToList();

        var sharePosts = _context.Post_Team.Where(x => postIds.Contains(x.PostId) && x.Project_Team.Type != HrProjectType.自己)
        .GroupBy(g => g.PostId)
        .Select(s => new
        {
            s.Key,
            Ct = s.Count()
        }).ToList();

        // 查询职位面试配置信息
        var postInterviews = _context.Post_Interview_Config.Where(x => postIds.Contains(x.PostId)).ToList();

        //最近7天
        var recent7 = DateTime.Now.Date.AddDays(-6);

        //本周面试人数
        var weekInterview = _context.Recruit_Record.Where(x => x.CreatedTime >= recent7 && postIds.Contains(x.Recruit.Post_Delivery.PostId) && Constants.InterviewStatus.Contains(x.Status))
        .GroupBy(g => g.RecruitId)
        .Select(s => new { s.Key, PostId = s.Max(m => m.Recruit.Post_Delivery.PostId) })
        .GroupBy(g => g.PostId)
        .Select(s => new { s.Key, Ct = s.Count() }).ToList();

        var bgt = DateTime.Now.Date.AddDays(-14);
        var interviewTimes = _context.Post_Interview_Date.Where(x => postIds.Contains(x.PostId) && x.Time >= bgt).ToList();

        var welfare = _commonDicService.GetWelfare();
        var isNoah = _context.User_Hr.Include(userHr => userHr.Enterprise).First(s => s.UserId == _user.Id).Enterprise.Specific?.Contains(EntSpecific.Noah) == true;
        var settings = _context.Sys_Settings.FirstOrDefault();
        BountyConfig? bountyConfig;

        foreach (var item in result.Rows)
        {

            if (isNoah)
            {
                if ((bool)item.IsSalesCommission!)
                    bountyConfig = settings.NoahFull;
                else
                    bountyConfig = settings.NoahNoSale;
            }
            else
            {
                if ((bool)item.IsSalesCommission!)
                    bountyConfig = settings.PlatformFull;
                else
                    bountyConfig = settings.PlatformNoSale;
            }

            item.DeliveryBounty = (((decimal)item.Money! * bountyConfig.Clue).ToFixed(2)) + (((decimal)item.Money! * bountyConfig.Follower).ToFixed(2));
            item.IsPlatform = item.Hr!.HrId == Constants.PlatformHrId;
            item.PostNo = $"{Constants.PostIdPre}{item.PostAutoId.ToString()!.PadLeft(2, '0')}";

            item.SalaryName = Tools.FormattingSalary(item.MinSalary, item.MaxSalary, item.SalaryType, item.WorkNature, item.Salary);

            if (item.Welfare?.Count > 0)
                item.Welfare = welfare.Where(x => item.Welfare.Any(a => a.Id == x.Id)).ToList();

            if (!string.IsNullOrWhiteSpace(item.RegionId))
                item.Region = _commonDicService.GetCityById(item.RegionId);

            item.PaymentNodeName = item.PaymentNode?.GetDescription();
            item.PaymentTypeName = item.PaymentType?.GetDescription();

            item.Hr!.OnlineStatus = Tools.GetOnlineStatus(item.Hr.LoginTime);

            item.HasShare = myPosts.Contains(item.PostId);
            item.ProjectShareNum = shareProjets.FirstOrDefault(x => x.Key == item.ProjectId)?.Ct ?? 0;
            item.PostShareNum = sharePosts.FirstOrDefault(x => x.Key == item.PostId)?.Ct ?? 0;

            //面试信息
            var interview = postInterviews.FirstOrDefault(x => x.PostId == item.PostId) ?? new Post_Interview_Config();
            if (interview != null)
            {
                //InterviewTime 只保留昨天至未来6天的面试时间
                var beginTime = DateTime.Now.Date.AddDays(-1);
                // var endTime = beginTime.AddDays(6);

                var interviewTime = interviewTimes.Where(x => x.PostId == item.PostId).ToList();
                foreach (var tm in Enumerable.Range(0, 7).Select(i => beginTime.AddDays(i)))
                {
                    PostInterviewTimeStatus? st = null;
                    if (interviewTime.Any(x => x.Time.Date == tm && x.Status == PostInterviewTimeStatus.可预约 && tm >= DateTime.Today.AddDays(interview.AdvanceDays)))
                        st = PostInterviewTimeStatus.可预约;
                    else
                        // if (interviewTime.Any(x => (x.Time.Date == tm && x.Status == PostInterviewTimeStatus.取消预约) || tm < DateTime.Today.AddDays(interview.AdvanceDays)))
                        st = PostInterviewTimeStatus.取消预约;

                    var its = new PostInterviewTimeModel
                    {
                        Time = tm,
                        Status = st,
                        SubTime = interviewTime.Where(x => x.Time.Date == tm).Select(s => TimeOnly.FromDateTime(s.Time)).OrderBy(o => o).ToList()
                    };
                    item.InterviewDashboard.Recent.Add(its);
                }

                item.InterviewDashboard.RecentInterviewNum = weekInterview.Where(x => x.Key == item.PostId)?.Sum(c => (int?)c.Ct) ?? 0;

                //产品：该数是未来可预约面试的场次，与时间无关
                item.InterviewDashboard.InterviewNum = interviewTime?.Where(x => x.Status == PostInterviewTimeStatus.可预约 && x.Time > DateTime.Now).Count() ?? 0;
            }

            // //面试信息
            // if (interview != null)
            // {
            //     // 上周一
            //     var today = DateTime.Now;
            //     var daysSinceMonday = (int)today.DayOfWeek - 1;
            //     if (daysSinceMonday < 0) daysSinceMonday = 6; // 如果今天是星期天
            //     var lastMonday = today.AddDays(-daysSinceMonday - 7).Date;

            //     foreach (var tm in Enumerable.Range(0, 14).Select(i => lastMonday.AddDays(i)))
            //     {
            //         PostInterviewTimeStatus? st = null;
            //         if (interviewTime.Any(x => x.Time.Date == tm && x.Status == PostInterviewTimeStatus.可预约))
            //             st = PostInterviewTimeStatus.可预约;
            //         else if (interviewTime.Any(x => x.Time.Date == tm && x.Status == PostInterviewTimeStatus.取消预约))
            //             st = PostInterviewTimeStatus.取消预约;

            //         var its = new PostInterviewTimeModel
            //         {
            //             Time = tm,
            //             Status = st
            //         };
            //         item.InterviewDashboard.Recent.Add(its);
            //     }
            // }
        }

        return result;
    }
    public GetDicTreeResponse GetHallPostCityTree(GetHallPost model)
    {
        model = new GetHallPost { IsOnlyUnAcceptOrder = model.IsOnlyUnAcceptOrder };
        var predicate = GetHallPostPredicate(model);

        var sql = _context.Post.Where(predicate);
        var RegionIds = sql.Select(s => s.RegionId).Distinct().ToList();
        var cityTree = _commonDicService.GetCityTreeById(RegionIds);
        return cityTree;
    }

    private ExpressionStarter<Post> GetHallPostPredicate(GetHallPost model)
    {
        var result = new GetHallPostResponse();

        var predicate = PredicateBuilder.New<Post>(x => x.Project.Status == ProjectStatus.已上线
        && x.Status == PostStatus.发布中 && x.LeftStock > 0);// 主创上线且协同

        var idPre = Constants.TeamPostIdPre;

        if (!string.IsNullOrWhiteSpace(model.Search))
        {
            if (model.Search.StartsWith(idPre) && int.TryParse(model.Search.Substring(1), out var id))
            {
                var postTeam = _context.Post_Team.FirstOrDefault(w => w.Project_Team.HrId == _user.Id && w.AutoId == id);
                if (postTeam != null)
                    predicate = predicate.And(x => x.PostId == postTeam.PostId || x.Name.Contains(model.Search) || x.Project.User_Hr.NickName.Contains(model.Search) || x.Project.Agent_Ent.Abbr.Contains(model.Search) || x.Project.Agent_Ent.DisplayName.Contains(model.Search));
                else
                    predicate = predicate.And(x => x.Name.Contains(model.Search) || x.Project.User_Hr.NickName.Contains(model.Search) || x.Project.Agent_Ent.Abbr.Contains(model.Search) || x.Project.Agent_Ent.DisplayName.Contains(model.Search));
            }
            else
                predicate = predicate.And(x => x.Name.Contains(model.Search) || x.Project.User_Hr.NickName.Contains(model.Search) || x.Project.Agent_Ent.Abbr.Contains(model.Search) || x.Project.Agent_Ent.DisplayName.Contains(model.Search));
        }

        if (model.Category.HasValue)
        {
            var categoryLevel = (_commonDicService.GetPost())
            .FirstOrDefault(x => x.Id == model.Category.ToString())?.Level;

            if (!string.IsNullOrWhiteSpace(categoryLevel))
                predicate = predicate.And(x => x.Dic_Post.Level.StartsWith(categoryLevel));
        }

        if (model.Citys?.Count > 0)
        {
            var subPredicate = PredicateBuilder.New<Post>(false);
            foreach (var item in model.Citys)
                subPredicate = subPredicate.Or(x => x.RegionId.StartsWith(item));

            predicate = predicate.And(subPredicate);
        }

        if (model.PaymentNode.HasValue)
        {
            predicate = predicate.And(x => x.PaymentNode == model.PaymentNode);
        }

        if (model.MinSalary.HasValue && model.MaxSalary.HasValue)
        {
            predicate = predicate.And(x => x.MaxSalary >= model.MinSalary &&
                                           x.MinSalary <= model.MaxSalary);
        }
        else if (model.MinSalary.HasValue)
        {
            predicate = predicate.And(x => x.MaxSalary >= model.MinSalary);
        }
        else if (model.MaxSalary.HasValue)
        {
            predicate = predicate.And(x => x.MinSalary <= model.MaxSalary);
        }

        if (model.SeekerEducation != EducationType.不限 && model.SeekerEducation.HasValue)
        {
            predicate = predicate.And(x => x.Education == model.SeekerEducation);
        }

        // 招聘流程优化补充筛选
        if (model.IsNew)
            predicate = predicate.And(x => x.CreatedTime >= DateTime.Now.Date.AddDays(-6));

        // // 如果model.IsSettled为true，则只查询Project_Teambounty表中存在的职位,不要先查出id再去查询
        // if (model.IsSettled)
        //     predicate = predicate.And(x => _context.Post_Bounty.Any(a => a.PostId == x.PostId && a.SettlementStatus == SettlementType.已结算));

        if (model.IsResumeExempt.HasValue)
            predicate = predicate.And(x => x.IsResumeExempt == model.IsResumeExempt);

        if (model.IsInterview)
            predicate = predicate.And(x => x.Post_Interview_Date.Any(a => a.Status == PostInterviewTimeStatus.可预约 && a.Time >= DateTime.Now));

        if (model.InterviewStartTime.HasValue)
        {
            predicate = predicate.And(x => x.Post_Interview_Date.Any(a => a.Time >= model.InterviewStartTime));
        }

        if (model.InterviewEndTime.HasValue)
        {
            var endOfDay = model.InterviewEndTime.Value.Date.AddDays(1);
            predicate = predicate.And(x => x.Post_Interview_Date.Any(a => a.Time < endOfDay));
        }
        if (model.IsOnlyUnAcceptOrder == IsOnlyAcceptOrder.OnlyUnAcceptOder)
        {
            predicate = predicate.And(x => !_context.Post_Team.Any(a => a.PostId == x.PostId && a.Project_Team.HrId == _user.Id));
        }
        return predicate;
    }

    public Task<EmptyResponse> PostSync(PostSync model)
    {
        var lockerKey = $"st:postsync:{_user.Id}";
        using var locker = MyRedis.TryLock(lockerKey);
        if (locker == null)
            throw new BadRequestException("操作频繁");

        // 判断是否自己的协同项目
        var shareProject = _context.Project.
            Include(i => i.Project_Team).
            Include(i => i.User_Hr).
            Include(i => i.Posts).
            Where(x => x.Posts.Any(a => a.PostId == model.PostId)).FirstOrDefault();
        if (shareProject == null)
            throw new NotFoundException("内容不存在");
        var post = shareProject.Posts.FirstOrDefault(f => f.PostId == model.PostId);
        if (post == null)
            throw new NotFoundException("内容不存在");
        //if (shareProject.HrId == _user.Id)
        //    throw new BadRequestException("无法接单自己发布的职位");

        string projectTeamId = string.Empty;
        var projectTeam = new Project_Team();
        projectTeam = shareProject.Project_Team.FirstOrDefault(f => f.HrId == _user.Id); projectTeam = shareProject.Project_Team.FirstOrDefault(f => f.HrId == _user.Id);

        // 项目创建人信息
        var projectHrInfo = _context.User_Hr.Include(i => i.Enterprise).Include(i => i.Enterprise_Org_User).Where(w => w.UserId == shareProject.HrId).FirstOrDefault();
        // 没有协同过该项目
        if (projectTeam == null)
        {
            var hrProject = new Project_Team
            {
                Type = HrProjectType.协同,
                HrId = _user.Id,
                ProjectId = shareProject.ProjectId,
                Status = ProjectStatus.已上线,
                UpdatedBy = _user.Id
            };
            projectTeamId = hrProject.TeamProjectId;
            _context.Add(hrProject);
        }
        else
            projectTeamId = projectTeam.TeamProjectId;

        // 职位协同（上线）
        var postTeam = _context.Post_Team.FirstOrDefault(w => w.PostId == model.PostId && w.TeamProjectId == projectTeamId);// todo:不管关闭状态都算进去
        // 如果是已协同职位
        if (postTeam != null)
            throw new BadRequestException("无法重复接单相同职位");

        //添加到协同
        postTeam = new Post_Team
        {
            PostId = post.PostId,
            UpdatedBy = _user.Id,
            Status = PostStatus.发布中,
            TeamProjectId = projectTeamId
        };
        var teamPostId = postTeam.TeamPostId;
        _context.Add(postTeam);
        _context.Add(new Post_Team_Extend { TeamPostId = teamPostId });
        _context.SaveChanges();// 放到更新状态之前执行 todo:会存在事务不一致的情况发生，

        // 职位状态变更处理
        _commonProjectService.UpdatePostShow(teamPostId, UpdatePostShowType.协同职位);// todo:这里边catch住了。。

        var result = new EmptyResponse();
        return Task.FromResult(result);
    }


    public GetHallPostResponse GetHasOrderPost(GetHallPost model)
    {
        var result = new GetHallPostResponse();

        var predicate = GetHasOrderPostPredicate(model);

        var sql = _context.Post_Team.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
            .OrderByDescending(x => x.Status == PostStatus.发布中 && x.Post.Status == PostStatus.发布中)
            //.OrderByDescending(x=>x.Post.Status == PostStatus.发布中)
            //.ThenByDescending(x => x.Status == PostStatus.关闭)
            .ThenByDescending(x => x.CreatedTime)
        //.OrderByDescending(x => x.CreatedTime)
        //.ThenByDescending(x =>
        //    x.Status == PostStatus.发布中 ? 3 :
        //    x.Status == PostStatus.待审核 ? 2 : 1)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetHallPostDetail
        {
            Hr = new HrModel
            {
                HrId = s.Post.Project.HrId,
                HrName = s.Post.Project.User_Hr.NickName,
                Avatar = s.Post.Project.User_Hr.Avatar,
                Post = s.Post.Project.User_Hr.Post,
                LoginTime = s.Post.Project.User_Hr.User_Extend.LoginTime,
                EntName = s.Post.Project.User_Hr.Enterprise.Name
            },
            AgentEnt = new GetAgentEntDetail
            {
                Name = s.Post.Agent_Ent.Name,
                DisplayName = s.Post.Agent_Ent.DisplayName,
                AgentEntId = s.Post.AgentEntId,
                Scale = s.Post.Agent_Ent.Scale,
                Abbr = s.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post.Agent_Ent.Abbr : s.Post.Agent_Ent.DisplayName,
                LogoUrl = s.Post.Agent_Ent.LogoUrl,
                ScaleName = s.Post.Agent_Ent.Scale.GetDescription(),
                Industry = s.Post.Agent_Ent.Industry.ToString(),
                IndustryName = s.Post.Agent_Ent.Dic_Industry.Name
            },
            RewardType = s.Post.RewardType,
            RewardTypeName = s.Post.RewardType.GetDescription(),
            PaymentCycle = s.Post.PaymentCycle,
            PaymentCycleName = s.Post.PaymentCycle.GetDescription(),
            PaymentDuration = s.Post.PaymentDuration,
            PostAutoId = s.Post.AutoId,
            ProjectId = s.Post.ProjectId,
            Name = s.Post.Name,
            Status = s.Post.Status,
            CreatedTime = s.Post.CreatedTime,
            UpdatedTime = s.Post.UpdatedTime,
            PaymentNode = s.Post.PaymentNode,
            PaymentType = s.Post.Project.PaymentType,
            PaymentDays = s.Post.PaymentDays,
            StatusName = s.Post.Status.GetDescription(),
            Address = s.Post.Address,
            Category = s.Post.Category,
            CategoryName = s.Post.Dic_Post.Name,
            Department = s.Post.Department,
            Describe = s.Post.Describe,
            Education = s.Post.Education,
            EducationName = s.Post.Education.GetDescription(),
            Highlights = s.Post.Highlights,
            Tags = s.Post.Tags,
            Welfare = s.Post.Welfare,
            WelfareCustom = s.Post.WelfareCustom,
            Lat = s.Post.Location.X,
            Lng = s.Post.Location.Y,
            MaxSalary = s.Post.MaxSalary,
            MinSalary = s.Post.MinSalary,
            Money = s.Post.Money ?? 0,
            PostId = s.Post.PostId,
            RecruitNumber = s.Post.DeliveryNumber,
            RegionId = s.Post.RegionId,
            Salary = s.Post.Salary,
            SettlementType = s.Post.SettlementType,
            WorkingDays = s.Post.WorkingDays,
            WorkingHours = s.Post.WorkingHours,
            MinMonths = s.Post.MinMonths,
            DaysPerWeek = s.Post.DaysPerWeek,
            GraduationYear = s.Post.GraduationYear,
            WorkNature = s.Post.WorkNature,
            SalaryType = s.Post.SalaryType,
            SalaryTypeName = s.Post.SalaryType.GetDescription(),
            WorkNatureName = s.Post.WorkNature.GetDescription(),
            Sex = s.Post.Sex,
            MinAge = s.Post.MinAge,
            MaxAge = s.Post.MaxAge,
            RegistrationNum = s.Post.Post_Extend.DeliveryNum,
            DeliveriesNum = s.Post.Post_Extend.InductionNum,
            ScreeningNum = s.Post.Post_Extend.HrScreeningNum,
            InterviewNum = s.Post.Post_Extend.InterviewNum,
            InductionNum = s.Post.Post_Extend.InductionNum,
            InterviewStatus = s.Post.InterviewStatus,
            InterviewDashboard = new PostRecentInterviews
            {
                RecentDeliveryRate = s.Post.Post_Extend.RecentDeliveryRate,
                HistoryBillingRate = s.Post.Post_Extend.HistoryBillingRate
            },
            ProjectTypeName = s.Post.Project.Type.GetDescription(),
            IsSalesCommission = s.Post.IsSalesCommission,
            Show = s.Show,
        }).ToList();

        var postIds = result.Rows.Select(s => s.PostId).ToList();
        var projectIds = result.Rows.Select(s => s.ProjectId).ToList();

        var myPosts = _context.Post_Team.Where(x => x.Project_Team.HrId == _user.Id && postIds.Contains(x.PostId))
        .Select(s => s.PostId).ToList();

        var shareProjets = _context.Project_Team.Where(x => projectIds.Contains(x.ProjectId))
        .GroupBy(g => g.ProjectId)
        .Select(s => new
        {
            s.Key,
            Ct = s.Count()
        }).ToList();

        var sharePosts = _context.Post_Team.Where(x => postIds.Contains(x.PostId) && x.Project_Team.Type != HrProjectType.自己)
        .GroupBy(g => g.PostId)
        .Select(s => new
        {
            s.Key,
            Ct = s.Count()
        }).ToList();

        // 查询职位面试配置信息
        var postInterviews = _context.Post_Interview_Config.Where(x => postIds.Contains(x.PostId)).ToList();

        //最近7天
        var recent7 = DateTime.Now.Date.AddDays(-6);

        //本周面试人数
        var weekInterview = _context.Recruit_Record.Where(x => x.CreatedTime >= recent7 && postIds.Contains(x.Recruit.Post_Delivery.PostId) && Constants.InterviewStatus.Contains(x.Status))
        .GroupBy(g => g.RecruitId)
        .Select(s => new { s.Key, PostId = s.Max(m => m.Recruit.Post_Delivery.PostId) })
        .GroupBy(g => g.PostId)
        .Select(s => new { s.Key, Ct = s.Count() }).ToList();

        var bgt = DateTime.Now.Date.AddDays(-14);
        var interviewTimes = _context.Post_Interview_Date.Where(x => postIds.Contains(x.PostId) && x.Time >= bgt).ToList();

        var welfare = _commonDicService.GetWelfare();
        var isNoah = _context.User_Hr.Include(userHr => userHr.Enterprise).First(s => s.UserId == _user.Id).Enterprise.Specific?.Contains(EntSpecific.Noah) == true;
        var settings = _context.Sys_Settings.FirstOrDefault();
        BountyConfig? bountyConfig;
        foreach (var item in result.Rows)
        {
            if (isNoah)
            {
                if ((bool)item.IsSalesCommission!)
                    bountyConfig = settings.NoahFull;
                else
                    bountyConfig = settings.NoahNoSale;
            }
            else
            {
                if ((bool)item.IsSalesCommission!)
                    bountyConfig = settings.PlatformFull;
                else
                    bountyConfig = settings.PlatformNoSale;
            }
            item.DeliveryBounty = (((decimal)item.Money! * bountyConfig.Clue).ToFixed(2)) + (((decimal)item.Money! * bountyConfig.Follower).ToFixed(2)); item.IsPlatform = item.Hr!.HrId == Constants.PlatformHrId;
            item.PostNo = $"{Constants.PostIdPre}{item.PostAutoId.ToString()!.PadLeft(2, '0')}";

            item.SalaryName = Tools.FormattingSalary(item.MinSalary, item.MaxSalary, item.SalaryType, item.WorkNature, item.Salary);

            if (item.Welfare?.Count > 0)
                item.Welfare = welfare.Where(x => item.Welfare.Any(a => a.Id == x.Id)).ToList();

            if (!string.IsNullOrWhiteSpace(item.RegionId))
                item.Region = _commonDicService.GetCityById(item.RegionId);

            item.PaymentNodeName = item.PaymentNode?.GetDescription();
            item.PaymentTypeName = item.PaymentType?.GetDescription();

            item.Hr!.OnlineStatus = Tools.GetOnlineStatus(item.Hr.LoginTime);

            item.HasShare = myPosts.Contains(item.PostId);
            item.ProjectShareNum = shareProjets.FirstOrDefault(x => x.Key == item.ProjectId)?.Ct ?? 0;
            item.PostShareNum = sharePosts.FirstOrDefault(x => x.Key == item.PostId)?.Ct ?? 0;

            //面试信息
            var interview = postInterviews.FirstOrDefault(x => x.PostId == item.PostId) ?? new Post_Interview_Config();
            if (interview != null)
            {
                //InterviewTime 只保留昨天至未来6天的面试时间
                var beginTime = DateTime.Now.Date.AddDays(-1);
                // var endTime = beginTime.AddDays(6);

                var interviewTime = interviewTimes.Where(x => x.PostId == item.PostId).ToList();
                foreach (var tm in Enumerable.Range(0, 7).Select(i => beginTime.AddDays(i)))
                {
                    PostInterviewTimeStatus? st = null;
                    if (interviewTime.Any(x => x.Time.Date == tm && x.Status == PostInterviewTimeStatus.可预约 && tm >= DateTime.Today.AddDays(interview.AdvanceDays)))
                        st = PostInterviewTimeStatus.可预约;
                    else
                        //if (interviewTime.Any(x => (x.Time.Date == tm && x.Status == PostInterviewTimeStatus.取消预约) || tm < DateTime.Now.AddDays(interview.AdvanceDays)))
                        st = PostInterviewTimeStatus.取消预约;

                    var its = new PostInterviewTimeModel
                    {
                        Time = tm,
                        Status = st,
                        SubTime = interviewTime.Where(x => x.Time.Date == tm).Select(s => TimeOnly.FromDateTime(s.Time)).OrderBy(o => o).ToList()
                    };
                    item.InterviewDashboard.Recent.Add(its);
                }

                item.InterviewDashboard.RecentInterviewNum = weekInterview.Where(x => x.Key == item.PostId)?.Sum(c => (int?)c.Ct) ?? 0;

                //产品：该数是未来可预约面试的场次，与时间无关
                item.InterviewDashboard.InterviewNum = interviewTime?.Where(x => x.Status == PostInterviewTimeStatus.可预约 && x.Time > DateTime.Now).Count() ?? 0;
            }
        }
        return result;
    }
    public GetDicTreeResponse GetHasOrderPostCityTree(GetHallPost model)
    {
        model = new GetHallPost { IsOnlyUnAcceptOrder = model.IsOnlyUnAcceptOrder };
        var predicate = GetHasOrderPostPredicate(model);

        var sql = _context.Post_Team.Where(predicate);
        var RegionIds = sql.Select(s => s.Post.RegionId).Distinct().ToList();
        var cityTree = _commonDicService.GetCityTreeById(RegionIds);
        return cityTree;
    }
    private ExpressionStarter<Post_Team> GetHasOrderPostPredicate(GetHallPost model)
    {
        var predicate = PredicateBuilder.New<Post_Team>(x => true);// 主创上线且协同

        var idPre = Constants.TeamPostIdPre;

        if (!string.IsNullOrWhiteSpace(model.Search))
        {
            if (model.Search.StartsWith(idPre) && int.TryParse(model.Search.Substring(1), out var id))
                predicate = predicate.And(x => x.AutoId == id);
            else
                predicate = predicate.And(x => x.Post.Name.Contains(model.Search) || x.Post.Project.User_Hr.NickName.Contains(model.Search) || x.Post.Project.Agent_Ent.Abbr.Contains(model.Search) || x.Post.Project.Agent_Ent.DisplayName.Contains(model.Search));
        }

        if (model.Category.HasValue)
        {
            var categoryLevel = (_commonDicService.GetPost())
            .FirstOrDefault(x => x.Id == model.Category.ToString())?.Level;

            if (!string.IsNullOrWhiteSpace(categoryLevel))
                predicate = predicate.And(x => x.Post.Dic_Post.Level.StartsWith(categoryLevel));
        }

        if (model.Citys?.Count > 0)
        {
            var subPredicate = PredicateBuilder.New<Post_Team>(false);
            foreach (var item in model.Citys)
                subPredicate = subPredicate.Or(x => x.Post.RegionId.StartsWith(item));

            predicate = predicate.And(subPredicate);
        }

        if (model.PaymentNode.HasValue)
        {
            predicate = predicate.And(x => x.Post.PaymentNode == model.PaymentNode);
        }

        if (model.MinSalary.HasValue && model.MaxSalary.HasValue)
        {
            predicate = predicate.And(x => x.Post.MaxSalary >= model.MinSalary &&
                                           x.Post.MinSalary <= model.MaxSalary);
        }
        else if (model.MinSalary.HasValue)
        {
            predicate = predicate.And(x => x.Post.MaxSalary >= model.MinSalary);
        }
        else if (model.MaxSalary.HasValue)
        {
            predicate = predicate.And(x => x.Post.MinSalary <= model.MaxSalary);
        }

        if (model.SeekerEducation != EducationType.不限 && model.SeekerEducation.HasValue)
        {
            predicate = predicate.And(x => x.Post.Education == model.SeekerEducation);
        }

        // 招聘流程优化补充筛选
        if (model.IsNew)
            predicate = predicate.And(x => x.CreatedTime >= DateTime.Now.Date.AddDays(-6));

        // // 如果model.IsSettled为true，则只查询Project_Teambounty表中存在的职位,不要先查出id再去查询
        // if (model.IsSettled)
        //     predicate = predicate.And(x => _context.Post_Bounty.Any(a => a.PostId == x.Post.PostId && a.SettlementStatus == SettlementType.已结算));

        if (model.IsResumeExempt.HasValue)
            predicate = predicate.And(x => x.Post.IsResumeExempt == model.IsResumeExempt);

        if (model.IsInterview)
            predicate = predicate.And(x => x.Post.Post_Interview_Date.Any(a => a.Status == PostInterviewTimeStatus.可预约 && a.Time >= DateTime.Now));

        if (model.InterviewStartTime.HasValue)
        {
            predicate = predicate.And(x => x.Post.Post_Interview_Date.Any(a => a.Time >= model.InterviewStartTime));
        }

        if (model.InterviewEndTime.HasValue)
        {
            var endOfDay = model.InterviewEndTime.Value.Date.AddDays(1);
            predicate = predicate.And(x => x.Post.Post_Interview_Date.Any(a => a.Time < endOfDay));
        }
        //predicate = predicate.And(x => _context.Post_Team
        //    .Any(a => a.Project_Team.HrId == _user.Id && 
        //    a.Project_Team.ProjectId == x.Post.ProjectId &&
        //    a.PostId == x.PostId));
        predicate = predicate.And(a => a.Project_Team.HrId == _user.Id);

        return predicate;
    }

    public GetAuditPostResponse GetAuditPost(GetAuditPost model)
    {
        var result = new GetAuditPostResponse();

        var isAdmin = _user.Powers?.Contains(Power.NkpAdmin) == true;
        if (!isAdmin)
            return new GetAuditPostResponse();

        var predicate = PredicateBuilder.New<Post>(true);

        var idPre = Constants.PostIdPre;

        if (!string.IsNullOrWhiteSpace(model.Search))
        {
            if (model.Search.StartsWith(idPre) && int.TryParse(model.Search.Substring(1), out var id))
            {
                var postTeam = _context.Post_Team.FirstOrDefault(w => w.Project_Team.HrId == _user.Id && w.AutoId == id);
                if (postTeam != null)
                    predicate = predicate.And(x => x.PostId == postTeam.PostId || x.Name.Contains(model.Search) || x.Project.User_Hr.NickName.Contains(model.Search) || x.Project.Agent_Ent.Abbr.Contains(model.Search) || x.Project.Agent_Ent.DisplayName.Contains(model.Search));
                else
                    predicate = predicate.And(x => x.Name.Contains(model.Search) || x.Project.User_Hr.NickName.Contains(model.Search) || x.Project.Agent_Ent.Abbr.Contains(model.Search) || x.Project.Agent_Ent.DisplayName.Contains(model.Search));
            }
            else
                predicate = predicate.And(x => x.Name.Contains(model.Search) || x.Project.User_Hr.NickName.Contains(model.Search) || x.Project.Agent_Ent.Abbr.Contains(model.Search) || x.Project.Agent_Ent.DisplayName.Contains(model.Search));
        }

        if (model.Category.HasValue)
        {
            var categoryLevel = (_commonDicService.GetPost())
            .FirstOrDefault(x => x.Id == model.Category.ToString())?.Level;

            if (!string.IsNullOrWhiteSpace(categoryLevel))
                predicate = predicate.And(x => x.Dic_Post.Level.StartsWith(categoryLevel));
        }

        if (model.Citys?.Count > 0)
        {
            var subPredicate = PredicateBuilder.New<Post>(false);
            foreach (var item in model.Citys)
                subPredicate = subPredicate.Or(x => x.RegionId.StartsWith(item));

            predicate = predicate.And(subPredicate);
        }

        if (model.Status.HasValue)
        {
            predicate = predicate.And(x => x.Status == model.Status);
        }

        var sql = _context.Post.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetAuditPostDetail
        {
            Hr = new HrModel
            {
                HrId = s.Project.HrId,
                HrName = s.Project.User_Hr.NickName,
                Avatar = s.Project.User_Hr.Avatar,
                Post = s.Project.User_Hr.Post,
                LoginTime = s.Project.User_Hr.User_Extend.LoginTime,
                EntName = s.Project.User_Hr.Enterprise.Name
            },
            AgentEnt = new GetAgentEntDetail
            {
                DisplayName = s.Agent_Ent.DisplayName,
                AgentEntId = s.AgentEntId,
                Scale = s.Agent_Ent.Scale,
                Abbr = s.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Agent_Ent.Abbr : s.Agent_Ent.DisplayName,
                LogoUrl = s.Agent_Ent.LogoUrl,
                ScaleName = s.Agent_Ent.Scale.GetDescription(),
                Industry = s.Agent_Ent.Industry.ToString(),
                IndustryName = s.Agent_Ent.Dic_Industry.Name
            },
            PostAutoId = s.AutoId,
            ProjectId = s.ProjectId,
            Name = s.Name,
            Status = s.Status,
            CreatedTime = s.CreatedTime,
            UpdatedTime = s.UpdatedTime,
            PaymentNode = s.PaymentNode,
            PaymentType = s.Project.PaymentType,
            PaymentDays = s.PaymentDays,
            StatusName = s.Status.GetDescription(),
            Address = s.Address,
            Category = s.Category,
            CategoryName = s.Dic_Post.Name,
            Department = s.Department,
            Describe = s.Describe,
            Education = s.Education,
            EducationName = s.Education.GetDescription(),
            Highlights = s.Highlights,
            Tags = s.Tags,
            Welfare = s.Welfare,
            WelfareCustom = s.WelfareCustom,
            Lat = s.Location.X,
            Lng = s.Location.Y,
            MaxSalary = s.MaxSalary,
            MinSalary = s.MinSalary,
            Money = s.Money,
            PostId = s.PostId,
            RecruitNumber = s.DeliveryNumber,
            RegionId = s.RegionId,
            Salary = s.Salary,
            SettlementType = s.SettlementType,
            WorkingDays = s.WorkingDays,
            WorkingHours = s.WorkingHours,
            MinMonths = s.MinMonths,
            DaysPerWeek = s.DaysPerWeek,
            GraduationYear = s.GraduationYear,
            WorkNature = s.WorkNature,
            SalaryType = s.SalaryType,
            SalaryTypeName = s.SalaryType.GetDescription(),
            WorkNatureName = s.WorkNature.GetDescription(),
            Sex = s.Sex,
            MinAge = s.MinAge,
            MaxAge = s.MaxAge,
            RegistrationNum = s.Post_Extend.DeliveryNum,
            DeliveriesNum = s.Post_Extend.InductionNum,
            ScreeningNum = s.Post_Extend.HrScreeningNum,
            InterviewNum = s.Post_Extend.InterviewNum,
            InductionNum = s.Post_Extend.InductionNum,
            ProjectTypeName = s.Project.Type.GetDescription(),
            AuditTime = s.AuditTime,
            AuditUserId = s.AuditUserId,
            AuditUserName = s.Audit_User.NickName,
            RejectReason = s.RejectReason,
            NuoId = s.Project.NuoId
        }).ToList();

        var postIds = result.Rows.Select(s => s.PostId).ToList();
        var projectIds = result.Rows.Select(s => s.ProjectId).ToList();

        var myPosts = _context.Post_Team.Where(x => x.Project_Team.HrId == _user.Id && postIds.Contains(x.PostId))
        .Select(s => s.PostId).ToList();

        var shareProjets = _context.Project_Team.Where(x => projectIds.Contains(x.ProjectId))
        .GroupBy(g => g.ProjectId)
        .Select(s => new
        {
            s.Key,
            Ct = s.Count()
        });

        var sharePosts = _context.Post_Team.Where(x => postIds.Contains(x.PostId))
        .GroupBy(g => g.PostId)
        .Select(s => new
        {
            s.Key,
            Ct = s.Count()
        });

        var welfare = _commonDicService.GetWelfare();
        foreach (var item in result.Rows)
        {
            item.PostNo = $"{Constants.PostIdPre}{item.PostAutoId.ToString()!.PadLeft(2, '0')}";

            item.SalaryName = Tools.FormattingSalary(item.MinSalary, item.MaxSalary, item.SalaryType, item.WorkNature, item.Salary);

            if (item.Welfare?.Count > 0)
                item.Welfare = welfare.Where(x => item.Welfare.Any(a => a.Id == x.Id)).ToList();

            if (!string.IsNullOrWhiteSpace(item.RegionId))
                item.Region = _commonDicService.GetCityById(item.RegionId);

            item.PaymentNodeName = item.PaymentNode?.GetDescription();
            item.PaymentTypeName = item.PaymentType?.GetDescription();

            item.Hr!.OnlineStatus = Tools.GetOnlineStatus(item.Hr.LoginTime);
        }
        result.AllNum = _context.Post.Count();
        result.OkNum = _context.Post.Count(c => c.Status == PostStatus.发布中);
        result.AuditNum = _context.Post.Count(c => c.Status == PostStatus.待审核);
        result.CloseNum = _context.Post.Count(c => c.Status == PostStatus.关闭);
        result.FailedNum = 0;

        return result;
    }

    public EmptyResponse AuditPost(AuditRequest request)
    {
        var isAdmin = _user.Powers?.Contains(Power.NkpAdmin) == true;
        if (!isAdmin)
            throw new ForbiddenException(string.Empty);

        if (request.Status != PostStatus.发布中 && request.Status != PostStatus.待审核)
            throw new BadRequestException("状态无效");

        var post = _context.Post.FirstOrDefault(f => f.PostId == request.PostId);
        if (post == null)
            throw new BadRequestException("内容不存在");
        post.AuditUserId = _user.Id;
        post.AuditTime = DateTime.Now;
        bool ifChange = post.Status != request.Status;
        if (request.Status == PostStatus.发布中)
        {
            if (post.LastStatus != PostStatus.关闭)
                post.Status = request.Status;
            else
                post.Status = PostStatus.关闭;
        }
        else
            post.Status = request.Status;

        if (!string.IsNullOrWhiteSpace(request.RejectReason))
            post.RejectReason = request.RejectReason;

        _context.SaveChanges();

        if (ifChange)
            _commonProjectService.UpdatePostShow(post.PostId, UpdatePostShowType.原始职位);

        // if (ifChange)
        // {
        //     _commonProjectService.UpdatePostShow(post.PostId, UpdatePostShowType.原始职位);
        //     //通知职位变更
        //     MyRedis.Client.SAdd(SubscriptionKey.PostStatusChange, new Sub_Post_StatusChange
        //     {
        //         PostId = post.PostId
        //     });

        //     //更新协同hr数据
        //     var teamHrs = _context.Post_Team.Where(x => x.PostId == post.PostId)
        //     .Select(s => new Sub_UpdateHrData
        //     {
        //         UserId = s.Project_Team.HrId,
        //         Type = UpdateHrDataType.职位
        //     }).ToArray();
        //     if (teamHrs.Count() > 0)
        //         _commonProjectService.UpdateHrProjectData(teamHrs);

        //     var hrId = _context.Post.Where(w => w.PostId == post.PostId).Select(p => p.Project.HrId).FirstOrDefault();
        //     //通知协同职位变更
        //     MyRedis.Client.SAdd(SubscriptionKey.TeamPostStatusChange, new Sub_TeamHrTj
        //     {
        //         TeamHrId = hrId!
        //     });
        // }

        return new EmptyResponse();
    }

    // public PostPaymentTypeResponse GetPostPaymentType(string postId)
    // {
    //     var post = _context.Post.FirstOrDefault(f => f.PostId == postId);
    //     if (post == null)
    //         throw new BadRequestException("内容不存在");

    //     var response = new PostPaymentTypeResponse();
    //     var PaymentNodeName = string.Empty;
    //     var UnitName = string.Empty;
    //     var PlaceHolder = string.Empty;

    //     switch (post.PaymentNode)
    //     {
    //         case ProjectPaymentNode.入职过保: PaymentNodeName = $"交付分润（入职过保{post.PaymentDays ?? 0}天）"; UnitName = "元/人"; PlaceHolder = "最低200元/人"; break;
    //         case ProjectPaymentNode.简历交付: PaymentNodeName = "交付分润（简历交付）"; UnitName = "元/份"; PlaceHolder = "最低3元/份"; break;
    //         case ProjectPaymentNode.到面交付: PaymentNodeName = "交付分润（到面交付）"; UnitName = "元/次"; PlaceHolder = "最低30元/次"; break;
    //         case ProjectPaymentNode.按天入职过保: PaymentNodeName = $"交付分润（按天入职过保{post.PaymentDays ?? 0}天）"; UnitName = "元/人/天"; PlaceHolder = "最低1元/人/天"; break;
    //     }

    //     response.PaymentNode = post.PaymentNode;
    //     response.PaymentNodeName = PaymentNodeName;
    //     response.UnitName = UnitName;
    //     response.PlaceHolder = PlaceHolder;

    //     return response;
    // }
}