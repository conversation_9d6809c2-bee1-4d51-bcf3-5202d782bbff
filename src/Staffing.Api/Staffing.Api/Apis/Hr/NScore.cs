// using Infrastructure.Extend;
// using Staffing.Core.Interfaces.Hr;

// namespace Staffing.Api.Apis.Hr;

// [Service(ServiceLifetime.Transient)]
// public class NScoreRouter : IHrRouterBase
// {
//     public void AddRoutes(RouteGroupBuilder parentGroup)
//     {
//         var group = parentGroup.MapGroup("nscore").WithTags("积分");
//         group.MapGet("/info", ([AsParameters] GetScore model, INScoreService rep) => rep.GetScore(model))
//             .WithOpenApi(operation => new(operation) { Summary = "当前用户积分" });

//         group.MapPut("/address", (UpdateKdAddress model, INScoreService rep) => rep.UpdateKdAddress(model))
//             .WithOpenApi(operation => new(operation) { Summary = "更新地址" });

//         group.MapPost("/add", async (AddScore model, INScoreService rep) => await rep.AddScore(model))
//             .WithOpenApi(operation => new(operation) { Summary = "加积分（支持顾问每日登录、顾问分享2种类型）" });

//         group.MapGet("/score/notify", ([AsParameters] GetScoreNotify model, INScoreService rep) => rep.GetScoreNotify(model))
//             .WithOpenApi(operation => new(operation) { Summary = "查询积分" });

//         group.MapGet("/goods", ([AsParameters] GetGoodsList model, INScoreService rep) => rep.GetGoodsList(model))
//             .WithOpenApi(operation => new(operation) { Summary = "商品列表" });

//         group.MapPost("/goods/buy", async (BuyGoods model, INScoreService rep) => await rep.BuyGoods(model))
//             .WithOpenApi(operation => new(operation) { Summary = "购买商品" });

//         group.MapGet("/orders", ([AsParameters] GetOrders model, INScoreService rep) => rep.GetOrders(model))
//             .WithOpenApi(operation => new(operation) { Summary = "订单列表" });

//         group.MapGet("/record", ([AsParameters] GetScoreRecord model, INScoreService rep) => rep.GetScoreRecord(model))
//             .WithOpenApi(operation => new(operation) { Summary = "积分记录" });
//     }
// }