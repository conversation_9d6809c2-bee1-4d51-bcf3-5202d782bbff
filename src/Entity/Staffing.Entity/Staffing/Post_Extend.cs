﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 职位扩展表
/// </summary>
[Table("post_extend")]
public class Post_Extend
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string PostId { get; set; } = default!;

    /// <summary>
    /// 协同次数
    /// </summary>
    public int SyncNum { get; set; }

    /// <summary>
    /// 投递人数累计去重
    /// </summary>
    public int DeliveryNum { get; set; }

    /// <summary>
    /// 简历初筛人数
    /// </summary>
    public int HrScreeningNum { get; set; }

    /// <summary>
    /// 面试官筛选人数
    /// </summary>
    public int InterviewerScreeningNum { get; set; }

    /// <summary>
    /// 面试人数
    /// </summary>
    public int InterviewNum { get; set; }

    /// <summary>
    /// Offer人数
    /// </summary>
    public int OfferNum { get; set; }

    /// <summary>
    /// 入职人数
    /// </summary>
    public int InductionNum { get; set; }

    /// <summary>
    /// 签约人数
    /// </summary>
    public int ContractNum { get; set; }

    /// <summary>
    /// 结算人数
    /// </summary>
    public int SettlementNum { get; set; }

    /// <summary>
    /// 最近交付率
    /// </summary>
    public decimal RecentDeliveryRate { get; set; }

    /// <summary>
    /// 历史计费率
    /// </summary>
    public decimal HistoryBillingRate { get; set; }
    
    /// <summary>
    /// 简历处理时长 计算当前岗位简历处理时长平均
    /// </summary>
    public int? ResumeProcessingDuration { get; set; }
}