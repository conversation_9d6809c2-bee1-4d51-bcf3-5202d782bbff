﻿using Config.Enums;

public class RequestContext
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 诺聘Id
    /// </summary>
    public string NuoPinId { get; set; } = default!;

    /// <summary>
    /// 诺聘EntId
    /// </summary>
    public string NuoEntId { get; set; } = default!;

    /// <summary>
    /// 企业Id
    /// </summary>
    public string? EntId { get; set; }

    /// <summary>
    /// 集团企业Id
    /// </summary>
    public string? GroupEntId { get; set; }

    /// <summary>
    /// 集团类型
    /// </summary>
    public EnterpriseGroupType GroupType { get; set; }

    /// <summary>
    /// 账号-手机号
    /// </summary>
    public string? Account { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// app类型
    /// </summary>
    public TokenType? AppType { get; set; }

    public ClientType? ClientType { get; set; }

    public string RequestId { get; set; } = string.Empty;
    public string ConnectionId { get; set; } = string.Empty;
    public string? RequestIpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? ClientId { get; set; }

    public string? AccessToken { get; set; }

    //顾问Id
    public string AdviserId { get; set; } = string.Empty;

    // //上级顾问Id
    // public string MainHrId { get; set; } = string.Empty;

    //渠道Id
    public string? ChannelId { get; set; }

    /// <summary>
    /// 渠道来源
    /// </summary>
    public long? ChannelSource { get; set; }

    // /// <summary>
    // /// 诺聘用户
    // /// </summary>
    // public AccessTokenUser? NuoUser { get; set; }

    /// <summary>
    /// 权限
    /// </summary>
    public List<string> Powers { get; set; } = new List<string>();
}

public class ShortUserInfo
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 企业Id
    /// </summary>
    public string? EntId { get; set; }

    /// <summary>
    /// 集团企业Id
    /// </summary>
    public string? GroupEntId { get; set; }

    /// <summary>
    /// 诺聘Id
    /// </summary>
    public string? NuoPinId { get; set; }

    /// <summary>
    /// 诺聘EntId
    /// </summary>
    public string? NuoEntId { get; set; }

    /// <summary>
    /// 客户端
    /// </summary>
    public ClientType? ClientType { get; set; }
    public UserStatus Status { get; set; }
    public EnterpriseGroupType GroupType { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string NickName { get; set; } = default!;

    /// <summary>
    /// 电话
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// token类型
    /// </summary>
    public TokenType? AppType { get; set; }
    public List<string> Powers { get; set; } = new List<string>();
}
