﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Config.CommonModel.Certificate
{
    /// <summary>
    /// 拼接模板推广图
    /// </summary>
    public class Certificate_ProImg_SplicingTemplate
    {
        /// <summary>
        /// 顶部模板URL
        /// </summary>
        public string? Top_TemplatePath { get; set; }
        /// <summary>
        /// 底部模板URL
        /// </summary>
        public string? Bottom_TemplatePath { get; set; }
        /// <summary>
        /// 列表单行模板URL
        /// </summary>
        public string? Table_TR_TemplatePath { get; set; }

        /// <summary>
        /// 证书表单字体路径
        /// </summary>
        public string? FontTTFPath { get; set;}

        /// <summary>
        /// 获取证书数量
        /// </summary>
        public int GetCerCount { get; set;}

        /// <summary>
        /// 证书表单的字体大小
        /// </summary>
        public int CerFontSize { get; set; }

        /// <summary>
        /// 证书表单的行高
        /// </summary>
        public int CerFont_HR_Height {  get; set; }
        /// <summary>
        /// 证书表单--起始位置Y
        /// </summary>
        public int CerFont_FirstPositionY { get; set; }

        /// <summary>
        /// 证书表单--编号的起始位置X
        /// </summary>
        public int CerFont_Num_PositionX { get; set; }

        /// <summary>
        /// 证书表单--证书名称的起始位置X
        /// </summary>
        public int CerFont_CertificateName_PositionX { get; set; }

        /// <summary>
        /// 证书表单--证书类别的起始位置X
        /// </summary>
        public int CerFont_CertificateType_PositionX { get; set; }

        /// <summary>
        /// 证书表单--证书规格的起始位置X
        /// </summary>
        public int CerFont_Specs_PositionX { get; set; }

        /// <summary>
        /// 证书表单--颁发机构的起始位置X
        /// </summary>
        public int CerFont_IssuingAuthority_PositionX { get; set; }
        /// <summary>
        /// 证书表单--报名费的起始位置X
        /// </summary>
        public int CerFont_Price_PositionX { get; set; }
    }
    /// <summary>
    /// 推广图上显示的证书列表
    /// </summary>
    public class Certificate_ProImg_Data
    {
        /// <summary>
        /// 证书名称，20个字以内
        /// </summary>
        public string CertificateName { get; set; } = default!;

        /// <summary>
        /// 商品类型
        /// </summary>
        public string? CertificateType { get; set; }
        /// <summary>
        /// 规格
        /// </summary>
        public List<string>? Specs { get; set; }
        /// <summary>
        /// 发证机构
        /// </summary>
        public string? IssuingAuthority { get; set; }

        /// <summary>
        /// 价格（元）
        /// </summary>
        public decimal Price { get; set; }
    }

    /// <summary>
    /// 为每个HR生成推广图
    /// </summary>
    public class Certificate_ProImg_ForHR
    {
        public string HRMobile { get; set; } = default!;
        ///// <summary>
        ///// 模板URL
        ///// </summary>
        //public string TemplatePath { get; set; }
        ///// <summary>
        ///// 小程序码路径
        ///// </summary>
        //public string QrCodePath { get; set; }
        /// <summary>
        /// 小程序码位置
        /// </summary>
        public int QrCodePosition_x { get; set; }
        /// <summary>
        /// 小程序码位置
        /// </summary>
        public int QrCodePosition_y { get; set; }
        /// <summary>
        /// 小程序宽高
        /// </summary>
        public int QRSize { get; set; }

        ///// <summary>
        ///// 企微二维码路径
        ///// </summary>
        //public string? QYWechatPath { get; set; }
        /// <summary>
        /// 企微二维码位置
        /// </summary>
        public int QYWechatPosition_x { get; set; }
        /// <summary>
        /// 企微二维码位置
        /// </summary>
        public int QYWechatPosition_y { get; set; }
        /// <summary>
        /// 企微二维码提示位置
        /// </summary>
        public int QYWechatTipPosition_x { get; set; }
        /// <summary>
        /// 企微二维码宽高
        /// </summary>
        public int QYWechatSize { get; set; }
    }

    public class Certificate_ProImg_ForHR_QW
    {
        /// <summary>
        /// 顾问手机号
        /// </summary>
        [Required(ErrorMessage = "手机号不能为空")]
        public string AdviserMobile { get; set; } = default!;
        /// <summary>
        /// 模板图路径
        /// </summary>
        public string? TemplatePath { get; set; }
        /// <summary>
        /// 企微二维码X坐标
        /// </summary>
        public int QYWechatPosition_x { get; set; }
        /// <summary>
        /// 企微二维码Y坐标
        /// </summary>
        public int QYWechatPosition_y { get; set; }
        /// <summary>
        /// 企微二维码大小
        /// </summary>
        public int QYWechatSize { get; set; }

        public int Mobile_FontSize { get; set; }
        public int Mobile_PositionX { get; set; }
        public int Mobile_PositionY { get; set; }
    }
}
