using Infrastructure.Extend;
using Staffing.Model.CentreService.Enterprise;
using Staffing.Core.Interfaces.CentreService;

namespace Staffing.Api.Apis.Admin;

[Service(ServiceLifetime.Transient)]
public class EnterpriseRouter : IAdminRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("enterprise").WithTags("企业");
        group.MapPut("/update", (UpdateEntName model, IEnterpriseService rep) => rep.UpdateEntName(model))
            .WithOpenApi(operation => new(operation) { Summary = "更新企业信息" });

        group.MapPost("/group/update", (UpdateEntGroup model, IEnterpriseService rep) => rep.UpdateEntGroup(model))
            .WithOpenApi(operation => new(operation) { Summary = "添加集团分公司" });

        group.MapPost("/group/delete", (DeleteEntGroup model, IEnterpriseService rep) => rep.DeleteEntGroup(model))
            .WithOpenApi(operation => new(operation) { Summary = "移除集团分公司" });
    }
}