﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 顾问推荐企业
/// </summary>
[Table("user_hr_recommendent")]
public class User_Hr_RecommendEnt
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string AdviserId { get; set; } = default!;

    /// <summary>
    /// 状态
    /// </summary>
    public HrRecommendEntStatus? Status { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public HrRecommendEntSource Source { get; set; }

    /// <summary>
    /// 级别
    /// </summary>
    public HrRecommendEntLevel? Level { get; set; }

    /// <summary>
    /// 网点Id
    /// </summary>
    public string? OutletId { get; set; }

    /// <summary>
    /// 网点名称
    /// </summary>
    public string? OutletName { get; set; }

    /// <summary>
    /// 三方企业Id
    /// </summary>
    public string TEntId { get; set; } = default!;

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// 公司名称
    /// </summary>
    public string EntName { get; set; } = default!;

    /// <summary>
    /// Hr名称
    /// </summary>
    public string HrName { get; set; } = default!;

    /// <summary>
    /// Hr电话
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 反馈
    /// </summary>
    public string? FeedBack { get; set; }

    /// <summary>
    /// 销帮帮销售机会名称
    /// </summary>
    public string? XbbXsjhName { get; set; }

    /// <summary>
    /// 销帮帮销售机会录入人Id
    /// </summary>
    public string? XbbXsjhUId { get; set; }

    /// <summary>
    /// 销帮帮销售机会录入人
    /// </summary>
    public string? XbbXsjhUName { get; set; }

    /// <summary>
    /// 最近发布职位时间
    /// </summary>
    public DateTime LastPostTime { get; set; } = Constants.DefaultTime;

    /// <summary>
    /// 最近投递时间
    /// </summary>
    public DateTime LastDeliverTime { get; set; } = Constants.DefaultTime;

    /// <summary>
    /// 最新跟进时间
    /// </summary>
    public DateTime? FollowUpTime { get; set; }

    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public User_Hr User_Hr { get; set; } = default!;
    // public Xbb_User? Xbb_Creator { get; set; }
}
