﻿using System.Text.Json.Serialization;
using Config.CommonModel;
using Config.Enums;

namespace Staffing.Model.Seeker.User;

public class GetMyAdviser : QueryRequest
{
    // /// <summary>
    // /// 合作时间（几年，null或不传=全部）
    // /// </summary>
    // public int? Cooperate { get; set; }

    // /// <summary>
    // /// 在线状态
    // /// </summary>
    // public OnlineStatus? OnlineStatus { get; set; }

    // /// <summary>
    // /// 合作地区（任意级，最多10个）
    // /// </summary>
    // public List<string>? RegionIds { get; set; }

    /// <summary>
    /// 顾问名字
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? AdviserId { get; set; }
}

public class GetMyAdviserResponse : QueryResponse
{
    public List<MyAdviserInfo> Rows { get; set; } = new List<MyAdviserInfo>();
}

public class MyAdviserInfo
{
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? AdviserId { get; set; }

    /// <summary>
    /// 是否是当前顾问
    /// </summary>
    public bool IsCurrent { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 在线状态
    /// </summary>
    public OnlineStatus? OnlineStatus { get; set; }

    /// <summary>
    /// 人才数量
    /// </summary>
    public int Talent { get; set; }

    /// <summary>
    /// 虚拟人才数量
    /// </summary>
    public int VirtualTalent { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 企业简称
    /// </summary>
    public string? EntAbbr { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 登录时间
    /// </summary>
    [JsonIgnore]
    public DateTime LoginTime { get; set; }

    /// <summary>
    /// 资料完整性
    /// </summary>
    public int DataScore { get; set; }

    /// <summary>
    /// 评分
    /// </summary>
    public string? Grade { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 企业微信二维码
    /// </summary>
    public string? EntWeChatQrCode { get; set; }
}

/// <summary>
/// 切换顾问
/// </summary>
public class SetAdviser
{
    /// <summary>
    /// HrId
    /// </summary>
    public string? HrId { get; set; }
}

/// <summary>
/// 添加顾问
/// </summary>
public class AddAdviser
{
    /// <summary>
    /// 邀请码
    /// </summary>
    public string? InviteCode { get; set; }

    /// <summary>
    /// HrId
    /// </summary>
    public string? HrId { get; set; }
}

public class SeekerGetAdviserInfo : MyAdviserInfo
{
    /// <summary>
    /// 点赞数
    /// </summary>
    public int Likes { get; set; }

    /// <summary>
    /// 人才头像数组
    /// </summary>
    public List<string>? TalentAvatars { get; set; }

    /// <summary>
    /// 超越百分比
    /// </summary>
    public int RankingThen { get; set; }

    /// <summary>
    /// 资料完整性评级
    /// </summary>
    public string? DataGrade { get; set; }

    /// <summary>
    /// 招聘活跃度
    /// </summary>
    public int ActivityScore { get; set; }

    /// <summary>
    /// 招聘活跃度评级
    /// </summary>
    public string? ActivityGrade { get; set; }

    /// <summary>
    /// 是否实名
    /// </summary>
    public bool Authentication { get; set; }

    /// <summary>
    /// 投诉次数
    /// </summary>
    public int Complaint { get; set; }

    /// <summary>
    /// 投诉评级
    /// </summary>
    public string? ComplaintGrade { get; set; }

    /// <summary>
    /// 人工复核
    /// </summary>
    public int? Review { get; set; }

    /// <summary>
    /// 人工复核评级
    /// </summary>
    public string? ReviewGrade { get; set; }

    /// <summary>
    /// 邀请码
    /// </summary>
    public string? InviteCode { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? EMail { get; set; }

    /// <summary>
    /// 微信
    /// </summary>
    public string? WeChatNo { get; set; }

    ///// <summary>
    ///// 企业微信
    ///// </summary>
    //public string? EntWeChatQrCode { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 腾讯ImId
    /// </summary>
    public string? TencentImId { get; set; }

    public double Lat { get; set; }
    public double Lng { get; set; }

    public CityModel? City { get; set; }
}

public class LikeAdviser
{
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? AdviserId { get; set; }
}

public class LikeAdviserResponse
{

}