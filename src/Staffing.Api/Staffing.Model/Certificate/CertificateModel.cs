using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;
using Config.CommonModel;
using Config.Enums;
using Staffing.Entity.Staffing;
namespace Staffing.Model.Certificate;

public class CertificateModel
{
    /// <summary>
    /// 证书记录唯一标识（更新时必填）
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 证书类型
    /// </summary>
    [Required(ErrorMessage = "证书类型不能为空")]
    public CertificateType CertificateType { get; set; }

    /// <summary>
    /// 证书名称（20字以内）
    /// </summary>
    [Required(ErrorMessage = "证书名称不能为空")]
    [StringLength(20, ErrorMessage = "证书名称不能超过20个字符")]
    public string CertificateName { get; set; } = default!;

    /// <summary>
    /// 证书类目
    /// </summary>
    [Required(ErrorMessage = "证书类目不能为空")]
    public string CertificateCategory { get; set; } = default!;
    /// <summary>
    /// 证书类目名称
    /// </summary>
    public string? CertificateCategoryName { get; set; }

    /// <summary>
    /// 商品图URL
    /// </summary>
    public string? ProductImage { get; set; }
    /// <summary>
    /// 企微群二维码URL
    /// </summary>
    public string? EnterpriseWechatQrCode { get; set; }
    /// <summary>
    /// 企微群ID
    /// </summary>
    public string? EnterpriseWechatGroupId { get; set; }

    /// <summary>
    /// 用户须知（60字以内）
    /// </summary>
    public string? UserNotice { get; set; }

    /// <summary>
    /// 报名表单配置
    /// </summary>
    public string? RegistrationForm { get; set; }

    /// <summary>
    /// 工种/职业方向
    /// </summary>
    public string? JobPosition { get; set; }

    /// <summary>
    /// 相关专业
    /// </summary>
    public string? RelevantMajors { get; set; }

    /// <summary>
    /// 发证机构
    /// </summary>
    public string? IssuingAuthority { get; set; }
    
    /// <summary>
    /// 发证机构名称
    /// </summary>
    public string? IssuingAuthorityName { get; set; }
    
    /// <summary>
    /// 查询平台URL
    /// </summary>
    public string? QueryPlatform { get; set; }
    /// <summary>
    /// 证书规格
    /// </summary>
    public List<CertificateSpecModel>? Specs { get; set; }

    /// <summary>
    /// 企业团购状态（默认：关闭）
    /// </summary>
    public EnterpriseGroupBuyingStatus EnterpriseGroupBuying { get; set; }

    /// <summary>
    /// 开售时间 为空则立即开售
    /// </summary>
    public DateTime? SaleStartTime { get; set; }

    /// <summary>
    /// 培训批次类型（默认：无线下培训）
    /// </summary>
    public TrainingBatchType TrainingBatchType { get; set; }
    
    /// <summary>
    /// 培训时间
    /// </summary>
    public DateTime? TrainingTime { get; set; }
    /// <summary>
    /// 详情描述HTML
    /// </summary>
    public string? DetailDescription { get; set; }
    /// <summary>
    /// 创建人
    /// </summary>
    public string? Creator { get; set; }
    
    /// <summary>
    /// 创建时间，默认当前时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    
    /// <summary>
    /// 证书创建人
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// 证书创建人头像
    /// </summary>
    public string? HrAvatar { get; set; }

    /// <summary>
    /// 证书创建人企业名称
    /// </summary>
    public string? HrEntName { get; set; }
    
    /// <summary>
    /// 腾讯IM账号Id
    /// </summary>
    public string? TencentImId { get; set; }
    
    /// <summary>
    /// 证书创建人电话
    /// </summary>
    public string? HrPhone { get; set; }
    
    /// <summary>
    /// 企业微信二维码
    /// </summary>
    public string? HrEntWeChatQrCode { get; set; }
    
    /// <summary>
    /// 证书类型名称
    /// </summary>
    public string? CertificateTypeName { get; set; }
    
    /// <summary>
    /// 培训批次
    /// </summary>
    public List<CertificateTrainingBatchReq>? TrainingBatch { get; set; } = new List<CertificateTrainingBatchReq>();
    /// <summary>
    /// 创建人企业wxid
    /// </summary>
    public string? EnterpriseWechatName { get; set; }
    
    /// <summary>
    /// 企业微信群名称
    /// </summary>
    public string? EnterpriseWechatGroupName{ get; set; }
    
    /// <summary>
    /// 在线状态
    /// </summary>
    public OnlineStatus? OnlineStatus { get; set; }
    
    
    // /// <summary>
    // /// 证书学习地址
    // /// </summary>
    // public List<CertificateSpec.StudyModel>? StudyList { get; set; }
    
    
}


// 修改模型类添加数据验证
public class CertificateRegistrationFormReq
{
    /// <summary>
    /// 证书规格id
    /// </summary>
    [Required(ErrorMessage = "证书规格ID不能为空")]
    public string CertificateSpecsId { get; set; } = default!;

    /// <summary>
    /// 姓名
    /// </summary>
    [Required(ErrorMessage = "姓名不能为空")]
    [MaxLength(50)]
    public string Name { get; set; } = default!;

    /// <summary>
    /// 身份证号
    /// </summary>
    [Required(ErrorMessage = "身份证号不能为空")]
    [RegularExpression(@"^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$", 
        ErrorMessage = "请输入有效的18位身份证号码")]
    public string IdentityCardNumber { get; set; } = default!;

    /// <summary>
    /// 联系方式
    /// </summary>
    [Required(ErrorMessage = "联系方式不能为空")]
    [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "手机号格式不正确")]
    public string ContactNumber { get; set; } = default!;
    /// <summary>
    /// 工作单位
    /// </summary>
    public string? WorkUnit { get; set; }
    public string? City { get; set; }
    public string? CityCode { get; set; }
    /// <summary>
    /// 性别
    /// </summary>
    public Gender Gender { get; set; }
    /// <summary>
    /// 培训批次
    /// </summary>
    public string? TrainingBatch { get; set; }
    public string? RegistrationProject { get; set; }
    public string? Recommender { get; set; }
    /// <summary>
    /// 推荐人id
    /// </summary>
    public string? RecommenderId { get; set; } 
    public decimal? Fee { get; set; }
    /// <summary>
    /// 来源用于区分后端报名或者是移动端报名
    /// </summary>
    public SourceEnum Source { get; set; }
    
    
    
}

// 添加性别枚举

// 添加来源枚举
public enum SourceEnum
{
    pc,
    wxapp
}

public class CertificateSpecModel
{
    /// <summary>
    /// 规格ID
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 关联的证书ID
    /// </summary>
    public string? CertificateId { get; set; }

    /// <summary>
    /// 规格名称（如：初级证书、中级证书）
    /// </summary>
    public string SpecName { get; set; } = default!;

    /// <summary>
    /// 价格（元）
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 库存
    /// </summary>
    public int? Stock { get; set; }

    /// <summary>
    /// 推荐佣金（元）
    /// </summary>
    public decimal? RecommendCommission { get; set; }

    /// <summary>
    /// 自定义排序
    /// </summary>
    public int SortOrder { get; set; }
    
    /// <summary>
    /// 销量
    /// </summary>
    public int SalesVolume { get; set; }
    
    /// <summary>
    /// 证书学习地址
    /// </summary>
    public List<CertificateSpec.StudyModel>? StudyList { get; set; }
    
}


public class DeleteCertificateReq
{
    public string Id { get; set; } = default!;
}


public class CertificateHallReq : QueryRequest
{
    /// <summary>
    /// 证书类型
    /// </summary>
    public CertificateType? CertificateType { get; set; }

    /// <summary>
    /// 证书名称（20字以内）
    /// </summary>
    public string? CertificateName { get; set; }
    
    
    
}
public class CertificateStudyReq
{
    /// <summary>
    /// 报名记录ID
    /// </summary>
    [Required(ErrorMessage = "报名记录标识不能为空")]
    public string CerRegID { get; set; } = default!;
    /// <summary>
    /// 学习Url
    /// </summary>
    [Required(ErrorMessage = "学习链接不能为空")]
    public string Url { get; set; } = default!;
}

public class CertificatePromotionReq
{
    /// <summary>
    /// 证书ID
    /// </summary>
    [Required(ErrorMessage = "证书标识不能为空")]
    public string CertificateID { get; set; } = default!;
    /// <summary>
    /// 顾问ID
    /// </summary>
    [Required(ErrorMessage = "顾问标识不能为空")]
    public string AdviserID { get; set; } = default!;
}
public class CertificateMyPromotionReq
{
    /// <summary>
    /// 证书ID
    /// </summary>
    [Required(ErrorMessage = "证书标识不能为空")]
    public string CertificateID { get; set; } = default!;
}
public class GeneratePromotionResp
{
    /// <summary>
    /// 推广图URL
    /// </summary>
    public string? url { get; set; }
    /// <summary>
    /// 缩略图URL
    /// </summary>
    public string? thumbnailUrl {  get; set; }
}

public class CertificateHallResp : QueryResponse
{
    
    /// <summary>
    /// 列表数据
    /// </summary>
    public List<CertificateHallModel> Rows { get; set; } = default!;
}

public class CertificateHallModel
{
    /// <summary>
    /// 证书ID
    /// </summary>
    public string Id { get; set; } = default!;

    /// <summary>
    /// 证书类型
    /// </summary>
    public CertificateType? CertificateType { get; set; }
    
    /// <summary>
    /// 证书类型名称
    /// </summary>
    public string? CertificateTypeName { get; set; }

    /// <summary>
    /// 证书名称（20字以内）
    /// </summary>
    public string? CertificateName { get; set; }
    
    /// <summary>
    /// 工种/职业方向
    /// </summary>
    public string? JobPosition { get; set; }

    /// <summary>
    /// 相关专业
    /// </summary>
    public string? RelevantMajors { get; set; }

    /// <summary>
    /// 发证机构
    /// </summary>
    public string? IssuingAuthority { get; set; }
    /// <summary>
    /// 发证机构名称
    /// </summary>
    public string? IssuingAuthorityName { get; set; }
    
    /// <summary>
    /// 查询平台URL
    /// </summary>
    public string? QueryPlatform { get; set; }
    
    /// <summary>
    /// 证书规格
    /// </summary>
    public List<CertificateSpecModel>? Specs { get; set; }
    
    
    /// <summary>
    /// 培训批次
    /// </summary>
    public List<CertificateTrainingBatchResp>? TrainingBatch { get; set; }
    
    /// <summary>
    /// 报名人数
    /// </summary>
    public int? RegistrationCount { get; set; }
    
    
    /// <summary>
    /// 证书创建人
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// 证书创建人头像
    /// </summary>
    public string? HrAvatar { get; set; }

    /// <summary>
    /// 证书创建人企业名称
    /// </summary>
    public string? HrEntName { get; set; }
    /// <summary>
    /// 企业微信二维码
    /// </summary>
    public string? HrEntWeChatQrCode { get; set; }
    
    /// <summary>
    /// 腾讯IM账号Id
    /// </summary>
    public string? TencentImId { get; set; }
    /// <summary>
    /// 商品图
    /// </summary>
    public string? ProductImage { get; set; }
    
    /// <summary>
    /// 培训批次类型枚举
    /// </summary>
    public TrainingBatchType TrainingBatchType { get; set; }
    
    /// <summary>
    /// 报名表单配置
    /// </summary>
    public string? RegistrationForm { get; set; }
    
    /// <summary>
    /// 创建人
    /// </summary>
    public string? Creator { get; set; }

    
}

public class GenerateQRcodeReq
{
    public string? CertificateId { get; set; }
}

public class GenerateQRcodeResp
{
    //二维码url
    public string? url { get; set; }
    //用户id
    public string? userId { get; set; }
    //用户名
    public string? userName { get; set; }
}
/// <summary>
/// 培训批次信息
/// </summary>
public class CertificateTrainingBatchReq
{
    /// <summary>
    /// 新增时为空
    /// </summary>
    public string? Id { get; set; } 
    /// <summary>
    /// 批次名称
    /// </summary>
    public string BatchName { get; set; } = default!;
    /// <summary>
    /// 培训地址
    /// </summary>
    public string? Address { get; set; }
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = default!;
    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; } = default!;
    /// <summary>
    /// 联系人
    /// </summary>
    public string ContactPerson { get; set; } = default!;
    /// <summary>
    /// 联系人电话
    /// </summary>
    public string ContactPhone { get; set; } = default!;
    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }
}

public class CertificateTrainingBatchResp
{
    public string? Id { get; set; } 
    /// <summary>
    /// 批次名称
    /// </summary>
    public string BatchName { get; set; } = default!;
    /// <summary>
    /// 培训地址
    /// </summary>
    public string? Address { get; set; }
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = default!;
    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; } = default!;
    /// <summary>
    /// 联系人
    /// </summary>
    public string ContactPerson { get; set; } = default!;
    /// <summary>
    /// 联系人电话
    /// </summary>
    public string ContactPhone { get; set; } = default!;
    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; } 
}


public class CertificateUserConfigResp
{
    public bool IsEnabled { get; set; } = false;
}


public class CertificateRegistrationformListReq : QueryRequest
{
    /// <summary>
    /// 报名开始时间
    /// </summary>
    public DateTime? RegistrationStartTime { get; set; }
    
    /// <summary>
    /// 报名结束时间
    /// </summary>
    public DateTime? RegistrationEndTime { get; set; }
    
    /// <summary>
    /// 证书类型
    /// </summary>
    public CertificateType? CertificateType { get; set; }
    
    /// <summary>
    /// 证书入群状态
    /// </summary>
    public CertificateGroupTypeEnum? CertificateGroupType { get; set; }
    
    /// <summary>
    /// 结算状态
    /// </summary>
    public CertificateSettlementEnum? CertificateSettlement { get; set; }
    
    /// <summary>
    /// 姓名/手机号/证书名称
    /// </summary>
    public string? Search { get; set; }
    /// <summary>
    /// 支付状态
    /// </summary>
    public RegistrationformPayStatusEnum? PayStatus{ get; set; }
    
}

public class CertificateRegistrationformListResp : QueryResponse
{
    public List<CertificateRegistrationformListModel> Rows { get; set; } = default!;
}
public class CertificateRegistrationformListModel
{
    /// <summary>
    /// 报名id
    /// </summary>
    public string Id { get; set; } = default!;
    
    /// <summary>
    /// 证书id
    /// </summary>
    public string? CertificateId { get; set; } = default!;

    /// <summary>
    /// 报名来源
    /// </summary>
    public string Source { get; set; } = "小程序";
    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 联系方式（手机号码）
    /// </summary>
    public string ContactNumber { get; set; } = default!;
    
    /// <summary>
    /// 性别
    /// </summary>
    public Gender Gender { get; set; }
    
    /// <summary>
    /// 性别名称
    /// </summary>
    public string? GenderName { get; set; }
    
    /// <summary>
    /// 年龄
    /// </summary>
    /// <summary>
    /// 根据身份证号计算出来的年龄，若身份证号为空或格式不对，则返回 null。
    /// </summary>
    public int? Age
    {
        get
        {
            if (string.IsNullOrEmpty(IdentityCardNumber))
                return null;

            string id = IdentityCardNumber;
            string birthString;

            // 18 位身份证
            if (id.Length == 18)
            {
                birthString = id.Substring(6, 8);       // yyyyMMdd
            }
            else
            {
                return null;
            }

            // 尝试解析出生日期
            if (!DateTime.TryParseExact(
                    birthString,
                    "yyyyMMdd",
                    CultureInfo.InvariantCulture,
                    DateTimeStyles.None,
                    out DateTime birthDate))
            {
                return null;
            }

            // 计算年龄
            DateTime today = DateTime.Today;
            int age = today.Year - birthDate.Year;
            // 如果今年还没过生日，则减一岁
            if (today < birthDate.AddYears(age))
                age--;

            return age;
        }
    }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    public string IdentityCardNumber { get; set; } = default!;
    
    /// <summary>
    /// 所在城市
    /// </summary>
    public string? City { get; set; }
    
    /// <summary>
    /// 证书规格名称
    /// </summary>
    public string? SpecName { get; set; }
    /// <summary>
    /// 证书名称
    /// </summary>
    public string? CertificateName { get; set; }
    /// <summary>
    /// 发证机构
    /// </summary>
    public string? IssuingAuthority { get; set; }
    /// <summary>
    /// 发证机构名称
    /// </summary>
    public string? IssuingAuthorityName { get; set; }

    
    /// <summary>
    /// 培训批次
    /// </summary>
    public string? TrainingBatch { get; set; }
    
    /// <summary>
    /// 实际培训批次
    /// </summary>
    public string? ActualTrainingBatch { get; set; }
    
    /// <summary>
    /// 企业微信群名称
    /// </summary>
    public string? WeChatGroupName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public string StatusName { get; set; } = default!;
    
        
    /// <summary>
    /// 结算状态
    /// </summary>
    public string SettlementStatusName { get; set; } = default!;
    
    /// <summary>
    /// 价格（元）
    /// </summary>
    public decimal? Price { get; set; }
    
    /// <summary>
    /// 推荐佣金（元）
    /// </summary>
    public decimal? RecommendCommission { get; set; }
    
    /// <summary>
    /// 工作单位
    /// </summary>
    public string? WorkUnit { get; set; } 
    

    /// <summary>
    /// 报名时间，默认当前时间
    /// </summary>
    public DateTime? RegistrationTime { get; set; }
    
    /// <summary>
    /// 推荐人
    /// </summary>
    public string? Recommender { get; set; }
    
    /// <summary>
    /// 创建人
    /// </summary>
    public string? Creator { get; set; }
    
    /// <summary>
    /// 证书创建人
    /// </summary>
    public string? CertificateCreator  { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public RegistrationformStatusEnum? Status { get; set; } 
    /// <summary>
    /// 结算状态
    /// </summary>
    public CertificateSettlementEnum? SettlementStatus { get; set; } 
    
    /// <summary>
    /// 等会再打
    /// </summary>
    public TodoEnum? ToDo { get; set; }
    
    /// <summary>
    /// 支付状态
    /// </summary>
    public RegistrationformPayStatusEnum? PayStatus { get; set; }

    
    
}

public class CertificateRegistrationFormEditReq
{
    public string Id { get; set; } = default!;
    /// <summary>
    /// 实际培训批次
    /// </summary>
    public string? ActualTrainingBatch { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public RegistrationformStatusEnum? Status { get; set; }
    
    /// <summary>
    /// 结算状态
    /// </summary>
    public CertificateSettlementEnum? SettlementStatus { get; set; }
    
    /// <summary>
    /// 等会待打
    /// </summary>
    public TodoEnum? Todo { get; set; }
    
    /// <summary>
    /// 支付状态
    /// </summary>
    public RegistrationformPayStatusEnum? PayStatus { get; set; } 
}

public class ExportDataForCertificateRegistrationForm
{
    

    /// <summary>
    /// 报名来源
    /// </summary>
    public string 报名来源 { get; set; } = default!;
    /// <summary>
    /// 姓名
    /// </summary>
    public string 姓名 { get; set; } = default!;

    /// <summary>
    /// 联系方式（手机号码）
    /// </summary>
    public string 联系方式 { get; set; } = default!;
    
    /// <summary>
    /// 性别
    /// </summary>
    public string 性别 { get; set; } = default!;
    
    /// <summary>
    /// 年龄
    /// </summary>
    /// <summary>
    /// 根据身份证号计算出来的年龄，若身份证号为空或格式不对，则返回 null。
    /// </summary>
    public int? 年龄
    {
        get
        {
            if (string.IsNullOrEmpty(身份证号))
                return null;

            string id = 身份证号;
            string birthString;

            // 18 位身份证
            if (id.Length == 18)
            {
                birthString = id.Substring(6, 8);       // yyyyMMdd
            }
            else
            {
                return null;
            }

            // 尝试解析出生日期
            if (!DateTime.TryParseExact(
                    birthString,
                    "yyyyMMdd",
                    CultureInfo.InvariantCulture,
                    DateTimeStyles.None,
                    out DateTime birthDate))
            {
                return null;
            }

            // 计算年龄
            DateTime today = DateTime.Today;
            int age = today.Year - birthDate.Year;
            // 如果今年还没过生日，则减一岁
            if (today < birthDate.AddYears(age))
                age--;

            return age;
        }
    }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    public string 身份证号 { get; set; } = default!;
    
    /// <summary>
    /// 所在城市
    /// </summary>
    public string? 所在城市 { get; set; }
    
    /// <summary>
    /// 证书规格名称
    /// </summary>
    public string? 证书规格名称 { get; set; }
    /// <summary>
    /// 证书名称
    /// </summary>
    public string? 证书名称 { get; set; }
    
    /// <summary>
    /// 培训批次
    /// </summary>
    public string? 培训批次 { get; set; }
    
    /// <summary>
    /// 实际培训批次
    /// </summary>
    public string? 实际培训批次 { get; set; }
    
    /// <summary>
    /// 价格（元）
    /// </summary>
    public decimal? 价格 { get; set; }
    
    /// <summary>
    /// 推荐佣金（元）
    /// </summary>
    public decimal? 推荐佣金 { get; set; }
    
    /// <summary>
    /// 工作单位
    /// </summary>
    public string? 工作单位 { get; set; } 
    

    /// <summary>
    /// 报名时间，默认当前时间
    /// </summary>
    public DateTime? 报名时间 { get; set; }
    
    /// <summary>
    /// 推荐人
    /// </summary>
    public string? 推荐人 { get; set; }
    /// <summary>
    /// 支付状态
    /// </summary>
    public string? 支付状态 { get; set; }
    
}

public class QrCodeData
{
    public string UserId { get; init; }
    public string CertificateId { get; init; }
    public string Url { get; init; }
    
    public QrCodeData(string userId, string certificateId, string url)
    {
        UserId = userId;
        CertificateId = certificateId;
        Url = url;
    }
}





