using Config;
using Config.CommonModel;
using Config.CommonModel.Business;
using Config.CommonModel.DingDingRoot;
using Config.CommonModel.Tencent;
using Config.CommonModel.ZhaoPinYun;
using Config.Enums;
using FreeRedis;
using Infrastructure.Aliyun;
using Infrastructure.Common;
using Infrastructure.CommonRepository.ZhaoPinYun;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Infrastructure.NoahCommon;
using Microsoft.EntityFrameworkCore;
using Noah.Aliyun.Storage;
using Senparc.Weixin.Entities.TemplateMessage;
using Senparc.Weixin.MP.AdvancedAPIs.TemplateMessage;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class MsgService
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _logger;
    private readonly IObjectStorage _objectStorage;
    private readonly ParseResume _parseResume;
    private readonly CommonDicService _commonDicService;
    private readonly CommonVirtualService _commonVirtualService;
    private readonly WeChatHelper _weChatHelper;
    private readonly TencentImHelper _tencentImHelper;
    private readonly ZPYunRepository _zpyunRepository;
    private readonly CacheHelper _cacheHelper;
    private readonly SmsHelper _smsHelper;
    private readonly DingDingRootHelper _dingDingRootHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    public MsgService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger,
    IObjectStorage objectStorage, ParseResume parseResume, CommonDicService commonDicService,
    CommonVirtualService commonVirtualService, WeChatHelper weChatHelper, TencentImHelper tencentImHelper,
    CacheHelper cacheHelper, SmsHelper smsHelper, DingDingRootHelper dingDingRootHelper, IHostEnvironment hostingEnvironment, ZPYunRepository zpyunRepository)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _objectStorage = objectStorage;
        _parseResume = parseResume;
        _commonDicService = commonDicService;
        _commonVirtualService = commonVirtualService;
        _weChatHelper = weChatHelper;
        _tencentImHelper = tencentImHelper;
        _cacheHelper = cacheHelper;
        _smsHelper = smsHelper;
        _dingDingRootHelper = dingDingRootHelper;
        _hostingEnvironment = hostingEnvironment;
        _zpyunRepository = zpyunRepository;
    }

    /// <summary>
    /// 消息通知
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubMsgNotify(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.LPop(SubscriptionKey.MsgNotify);

                if (string.IsNullOrWhiteSpace(msg))
                {
                    await Task.Delay(500);
                    continue;
                }

                // 处理消息
                using var _context = _contextFactory.CreateDbContext();

                var baseNotify = JsonSerializer.DeserializeFromString<MsgNotifyModel>(msg);

                if (baseNotify.Type == MsgNotifyType.职位报名)
                {
                    var detail = baseNotify.Data as MsgNotifyRecruit;
                    var title = $"【报名时间】{baseNotify.EventTime.ToYYYY_MM_DD_HH()}";
                    var content = $"候选人：{detail!.SeekerName}（岗位：{detail.PostName}）报名时间为：{baseNotify.EventTime.ToYYYY_MM_DD_HH()}";

                    var content2 = $"请及时处理面试事项";
                    if (!detail.IsSelfProj)
                        content2 = $"请及时跟进面试事项";

                    //通知
                    var notify = new Msg_Notify_Hr
                    {
                        UserId = baseNotify.UserId!,
                        Title = title,
                        Type = HrMsgType.职位报名,
                        Content = $"{content}，{content2}。",
                        Data = JsonSerializer.SerializeToString(detail)
                    };
                    _context.Add(notify);
                }
                // else if (baseNotify.Type == MsgNotifyType.协同交付)
                // {
                //     var detail = baseNotify.Data as MsgNotifyTeamDelivery;
                //     var title = $"【协同交付】{baseNotify.EventTime.ToYYYY_MM_DD_HH()}";
                //     var content = string.Empty;
                //     if (detail!.Status == BountyStatus.交付成功)
                //         content = $"候选人：{detail!.SeekerName}（岗位：{detail.PostName}）交付成功，正在等待过保结算，预计结算{detail.Money}元，结算时间{baseNotify.EventTime.ToYYYY_MM_DD()} 请您悉知。";
                //     else if (detail!.Status == BountyStatus.交付失败)
                //         content = $"候选人：{detail!.SeekerName}（岗位：{detail.PostName}）交付失败，请您悉知。";
                //     else
                //         throw new Exception("无效的订单状态");

                //     //通知协同
                //     if (notSelf)
                //     {
                //         var notify = new Msg_Notify_Hr
                //         {
                //             UserId = detail!.TeamHrId!,
                //             Title = title,
                //             Type = HrMsgType.协同交付,
                //             Content = $"{content}",
                //             Data = JsonSerializer.SerializeToString(detail)
                //         };
                //         _context.Add(notify);
                //     }
                // }

                _context.SaveChanges();

                //需要微信公众号通知
                if (baseNotify.Type == MsgNotifyType.职位报名)
                    MyRedis.Client.RPush(SubscriptionKey.MsgWeChatMpNotify, msg);

            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.MsgNotify}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 公众号模板通知
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubWeChatMsgNotify(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                //仅白天执行
                var today = DateTime.Today;
                if (DateTime.Now <= today.AddHours(9) || DateTime.Now >= today.AddHours(22))
                {
                    await Task.Delay(60000);
                    continue;
                }

                msg = MyRedis.Client.LPop(SubscriptionKey.MsgWeChatMpNotify);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                // 处理消息
                using var _context = _contextFactory.CreateDbContext();

                var baseNotify = JsonSerializer.DeserializeFromString<MsgNotifyModel>(msg);
                // var notSelf = baseNotify.HrId != baseNotify.TeamHrId;

                var uids = new List<string> { baseNotify.UserId! }.Distinct();
                var openIds = _context.User.Where(x => uids.Contains(x.UserId) && x.WeChatH5Subscribe).Select(s => new
                {
                    s.UserId,
                    s.WeChatH5OpenId
                }).ToList();

                if (openIds.Count == 0)
                    continue;

                var userOpenId = openIds.Where(x => x.UserId == baseNotify.UserId).Select(s => s.WeChatH5OpenId).FirstOrDefault();
                // var teamHrOpenId = openIds.Where(x => x.UserId == baseNotify.TeamHrId).Select(s => s.WeChatH5OpenId).FirstOrDefault();

                if (baseNotify.Type == MsgNotifyType.职位报名)
                {
                    var detail = baseNotify.Data as MsgNotifyRecruit;
                    var tmpId = "90EfU23OTQJtXzCZ7J2HTDt1vIhOF2dYGE87JhLjoHc";
                    var mpdata = new TemplateMessageData();
                    mpdata.Add("first", new TemplateMessageDataValue("您好,您收到一份简历。"));
                    mpdata.Add("keyword1", new TemplateMessageDataValue(detail!.AgentEntName));
                    mpdata.Add("keyword2", new TemplateMessageDataValue(detail.PostName));
                    mpdata.Add("keyword3", new TemplateMessageDataValue(baseNotify.EventTime.ToYYYY_MM_DD_HH()));
                    mpdata.Add("remark", new TemplateMessageDataValue($"候选人{detail.SeekerName}报名了{detail.ProjectName}，请及时{(detail.IsSelfProj ? "处理" : "跟进")}面试事项。"));

                    if (!string.IsNullOrWhiteSpace(userOpenId))
                    {
                        //通知用户
                        var rst = Senparc.Weixin.MP.AdvancedAPIs.TemplateApi.SendTemplateMessage(Constants.WeChatH5AppId,
                        userOpenId, tmpId, null,
                        mpdata, new TemplateModel_MiniProgram
                        {
                            appid = ClientApps.HrApplet.AppID
                        });

                        if (rst.errcode != Senparc.Weixin.ReturnCode.请求成功)
                            throw new Exception($"发送微信公众号消息失败:{JsonSerializer.SerializeToString(rst)}");
                    }

                    // if (!string.IsNullOrWhiteSpace(teamHrOpenId) && notSelf)
                    // {
                    //     //通知协同人
                    //     // mpdata["remark"] = new TemplateMessageDataValue($"候选人{detail.SeekerName}报名了{detail.ProjectName}，请及时跟进面试事项。");
                    //     // var rst = Senparc.Weixin.MP.AdvancedAPIs.TemplateApi.SendTemplateMessage(Constants.WeChatH5AppId,
                    //     // teamHrOpenId, tmpId, null,
                    //     // mpdata, new TemplateModel_MiniProgram
                    //     // {
                    //     //     appid = WeChatApps.HrApplet.AppID
                    //     // });

                    //     // if (rst.errcode != Senparc.Weixin.ReturnCode.请求成功)
                    //     //     throw new Exception($"发送微信公众号消息失败:{JsonSerializer.SerializeToString(rst)}");
                    // }
                }
                else if (baseNotify.Type == MsgNotifyType.归档)
                {
                    var detail = baseNotify.Data as MsgNotifyProjectEnd;

                    var hadEnd = baseNotify.EventTime.Date <= DateTime.Today;
                    var isSelf = detail!.IsSelfProj;

                    var tmpId = "RNl1mtSy40WqgFLR_xAAkoFF_gmfoNbIWQSM4fmeskY";
                    var mpdata = new TemplateMessageData();
                    mpdata.Add("first", new TemplateMessageDataValue($"您{(isSelf ? "" : "协同")}的项目{(hadEnd ? "已" : "即将")}归档，请知晓。"));
                    mpdata.Add("keyword1", new TemplateMessageDataValue(detail.ProjectName));
                    mpdata.Add("keyword2", new TemplateMessageDataValue(detail.HrName));
                    mpdata.Add("keyword3", new TemplateMessageDataValue(baseNotify.EventTime.ToYYYY_MM_DD()));
                    mpdata.Add("keyword4", new TemplateMessageDataValue(hadEnd ? "正常结束" : "即将结束"));

                    if (!string.IsNullOrWhiteSpace(userOpenId))
                    {
                        var rst = Senparc.Weixin.MP.AdvancedAPIs.TemplateApi.SendTemplateMessage(Constants.WeChatH5AppId,
                        userOpenId, tmpId, null,
                        mpdata, new TemplateModel_MiniProgram
                        {
                            appid = ClientApps.HrApplet.AppID
                        });

                        if (rst.errcode != Senparc.Weixin.ReturnCode.请求成功)
                            throw new Exception($"发送微信公众号消息失败:{JsonSerializer.SerializeToString(rst)}");
                    }
                }
                else if (baseNotify.Type == MsgNotifyType.招聘流程变更面试)
                {
                    var detail = baseNotify.Data as MsgNotifyRecruit;
                    var tmpId = "AD9-iQkNUpUbf7uYKSyRTc2fLFQUxahCsaSCoBLC44c";
                    var mpdata = new TemplateMessageData();
                    mpdata.Add("thing1", new TemplateMessageDataValue(detail!.PostName));
                    mpdata.Add("time3", new TemplateMessageDataValue(detail!.DeliveryTime?.ToYYYY_MM_DD()));
                    mpdata.Add("const4", new TemplateMessageDataValue("已通过简历初筛进入面试环节"));

                    if (!string.IsNullOrWhiteSpace(userOpenId))
                    {
                        var rst = Senparc.Weixin.MP.AdvancedAPIs.TemplateApi.SendTemplateMessage(Constants.WeChatH5AppId,
                        userOpenId, tmpId, null,
                        mpdata, new TemplateModel_MiniProgram
                        {
                            appid = ClientApps.SeekerApplet.AppID
                        });

                        if (rst.errcode != Senparc.Weixin.ReturnCode.请求成功)
                            throw new Exception($"发送微信公众号消息失败:{JsonSerializer.SerializeToString(rst)}");
                    }
                }
                else if (baseNotify.Type == MsgNotifyType.招聘流程变更归档)
                {
                    var detail = baseNotify.Data as MsgNotifyRecruit;
                    var tmpId = "AD9-iQkNUpUbf7uYKSyRTc2fLFQUxahCsaSCoBLC44c";
                    var mpdata = new TemplateMessageData();
                    mpdata.Add("thing1", new TemplateMessageDataValue(detail!.PostName));
                    mpdata.Add("time3", new TemplateMessageDataValue(detail!.DeliveryTime?.ToYYYY_MM_DD()));
                    mpdata.Add("const4", new TemplateMessageDataValue("HR已将您的简历标记为不合适"));

                    if (!string.IsNullOrWhiteSpace(userOpenId))
                    {
                        var rst = Senparc.Weixin.MP.AdvancedAPIs.TemplateApi.SendTemplateMessage(Constants.WeChatH5AppId,
                        userOpenId, tmpId, null,
                        mpdata, new TemplateModel_MiniProgram
                        {
                            appid = ClientApps.SeekerApplet.AppID
                        });

                        if (rst.errcode != Senparc.Weixin.ReturnCode.请求成功)
                            throw new Exception($"发送微信公众号消息失败:{JsonSerializer.SerializeToString(rst)}");
                    }
                }
            }
            catch (Senparc.Weixin.Exceptions.ErrorJsonResultException ex)
            {
                if (ex.JsonResult?.errcode != Senparc.Weixin.ReturnCode.需要接收者关注)
                {
                    _logger.Error($"{SubscriptionKey.MsgWeChatMpNotify}消息处理出错", $"{ex.JsonResult?.ErrorCodeValue}:{ex.JsonResult?.errmsg},{ex.Message}", JsonSerializer.SerializeToString(msg));
                    await Task.Delay(1000);
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.MsgWeChatMpNotify}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(20);
        }
    }

    /// <summary>
    /// 钉钉通知
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubDingDingMsg(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                //白天不执行
                var today = DateTime.Today;
                if (DateTime.Now <= today.AddHours(9) || DateTime.Now >= today.AddHours(22))
                {
                    await Task.Delay(60000);
                    continue;
                }

                msg = MyRedis.Client.LPop(SubscriptionKey.MsgDingDingNotify);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                var baseNotify = JsonSerializer.DeserializeFromString<MsgNotifyModel>(msg);
                await SendDingDingMsg(baseNotify);
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.MsgDingDingNotify}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(500);
        }
    }

    /// <summary>
    /// 钉钉通知（24小时发）
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubDingDingMsgFullTime(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.LPop(SubscriptionKey.MsgDingDingNotifyFullTime);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                var baseNotify = JsonSerializer.DeserializeFromString<MsgNotifyModel>(msg);
                await SendDingDingMsg(baseNotify);
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.MsgDingDingNotifyFullTime}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(500);
        }
    }

    private async Task SendDingDingMsg(MsgNotifyModel baseNotify)
    {
        using var _context = _contextFactory.CreateDbContext();

        //钉钉
        var dingId = _context.User_DingDing.Where(x => x.UserId == baseNotify.UserId && x.IsPush)
        .Select(s => s.DingUserid).FirstOrDefault();

        if (string.IsNullOrWhiteSpace(dingId))
        {
            var record = new Dd_Notify_Record
            {
                Data = JsonSerializer.SerializeToString(baseNotify),
                HrId = baseNotify.UserId ?? string.Empty,
                Type = baseNotify.Type!.Value,
                Status = DdNotifyRecordStatus.尚未绑定钉钉
            };
            _context.Add(record);
            _context.SaveChanges();
            return;
        }

        var dingMsgModel = new SendDingDingRootMessageRequest { userId = dingId };

        var content = string.Empty;
        string mdtitle = "消息提醒";
        if (baseNotify.Type == MsgNotifyType.归档)
        {
            var detail = baseNotify.Data as MsgNotifyProjectEnd;
            var hadEnd = baseNotify.EventTime.Date <= DateTime.Today;
            var isSelf = detail!.IsSelfProj;

            if (isSelf && !hadEnd)
                content = $"您的项目<{detail!.ProjectName}>将于{baseNotify.EventTime.ToYYYY_MM_DD()}归档，请知晓。";
            else
                content = $"您{(isSelf ? "" : "协同")}的项目<{detail!.ProjectName}>{(hadEnd ? "已" : "即将")}归档，{(isSelf ? "" : "所属业务顾问<" + detail.HrName + ">，")}请知晓。";
        }
        else if (baseNotify.Type == MsgNotifyType.职位优选面试)
        {
            var detail = baseNotify.Data as MsgExcellentPost;
            content = $"您发布的的“{detail!.PostName}”职位有求职者“{detail.SeekerName}”报名已超过24小时未办理面试，请登录PC端诺快聘后台进行处理！";
        }
        else if (baseNotify.Type == MsgNotifyType.职位优选入职)
        {
            var detail = baseNotify.Data as MsgExcellentPost;
            content = $"您发布的的“{detail!.PostName}”职位有求职者“{detail.SeekerName}”已超过72小时未办理入职，请登录PC端诺快聘后台进行处理！";
        }
        else if (baseNotify.Type == MsgNotifyType.诺聘发布职位)
        {
            var detail = baseNotify.Data as MsgNotifyNpCreatePost;
            content = $"{detail!.EntName}在{baseNotify.EventTime:yyyy-MM-dd HH:mm}发布{detail.PostName}岗位，请顾问及时跟进企业招聘进展！";
        }
        else if (baseNotify.Type == MsgNotifyType.诺聘投递简历)
        {
            var detail = baseNotify.Data as MsgNotifyNpDeliver;
            var userName = string.IsNullOrWhiteSpace(detail!.UserName) ? detail.UserMobile : detail.UserName;
            content = $"{userName}在{baseNotify.EventTime:yyyy-MM-dd HH:mm}投递{detail.EntName}的{detail.PostName}岗位，请及时联系企业HR和求职者，跟进求职进展，并进行持续转化！";
        }
        else if (baseNotify.Type == MsgNotifyType.自流转入职提醒)
        {
            var detail = baseNotify.Data as MsgAutomatic;
            var isSelf = detail!.IsSelfProj;

            if (isSelf)
                content = $"您发布的<{detail!.PostName}>职位，求职者“{detail!.SeekerName}”将于{baseNotify.EventTime.ToYYYY_MM_DD()}自动入职，请知晓。";
            else
                content = $"您协同的<{detail!.PostName}>职位，求职者“{detail!.SeekerName}”将于{baseNotify.EventTime.ToYYYY_MM_DD()}自动入职，所属业务顾问<" + detail.HrName + ">，请知晓。";
        }
        else if (baseNotify.Type == MsgNotifyType.自流转归档提醒)
        {
            var detail = baseNotify.Data as MsgAutomatic;
            var isSelf = detail!.IsSelfProj;

            if (isSelf)
                content = $"您发布的<{detail!.PostName}>职位，求职者“{detail!.SeekerName}”将于{baseNotify.EventTime.ToYYYY_MM_DD()}自动归档，请知晓。";
            else
                content = $"您协同的<{detail!.PostName}>职位，求职者“{detail!.SeekerName}”将于{baseNotify.EventTime.ToYYYY_MM_DD()}自动归档，所属业务顾问<" + detail.HrName + ">，请知晓。";
        }
        else if (baseNotify.Type == MsgNotifyType.企业线索池跟进提醒)
        {
            var detail = baseNotify.Data as MsgNotifyRecommendEnt;
            if (detail!.Hours == 24)
                content = $"当前企业线索池尚有{detail.Num}家企业暂未跟进，请及时跟进企业。";
            else if (detail.Hours == 72)
                content = $"Hi~ ，当前你这边还有{detail.Num}家企业在初步跟进中，请及时跟进企业招聘需求，如有销售机会，请及时录入销帮帮。";
        }
        else if (baseNotify.Type == MsgNotifyType.销售易企业线索池分配企业提醒)
        {
            var detail = baseNotify.Data as MsgNotifyRecommendEntAlloc;
            var hrName = string.IsNullOrWhiteSpace(detail?.HrName) ? string.Empty : $"({detail.HrName})";
            if (!string.IsNullOrWhiteSpace(detail!.OutletName))
                content = $"您好，从{detail!.OutletName}网点传递一个企业招聘需求，{detail!.EntName}，{detail!.HrMobile}{hrName}，请及时跟进，并协助进行诺聘注册.";
            else
                content = $"您好，当前诺聘平台已为您分配{detail!.EntName}，请及时添加客户微信{detail!.HrMobile}，并进行人才招聘协助.";
        }
        else if (baseNotify.Type == MsgNotifyType.企业线索池分配企业提醒)
        {
            var detail = baseNotify.Data as MsgNotifyRecommendEntAlloc;
            var hrName = string.IsNullOrWhiteSpace(detail?.HrName) ? string.Empty : $"({detail.HrName})";
            content = $"您好，企业线索：{detail!.EntName} 已分配给您，联系人：{detail!.HrMobile}{hrName}，请及时跟进。";
        }
        else if (baseNotify.Type == MsgNotifyType.销售易诺聘企业审核未通过)
        {
            var detail = baseNotify.Data as MsgNotifyRecommendEntAlloc;
            var hrName = string.IsNullOrWhiteSpace(detail?.HrName) ? string.Empty : $"({detail.HrName})";
            content = $"SCRM回传企业：{detail!.EntName}，在诺聘的审核状态为：{detail!.NpEnStatus}，请及时核实情况。";
        }
        else if (baseNotify.Type == MsgNotifyType.职位需审核通知)
        {
            var detail = baseNotify.Data as MsgNotifyPostNeedAudit;
            content = $"有新职位需要审核，发布人：{detail!.HrName}，职位名称：{detail!.PostName}，企业：{detail.EntName}。";
        }
        else if (baseNotify.Type == MsgNotifyType.企业线索池顾问未绑定钉钉)
        {
            var detail = baseNotify.Data as MsgNotifyRecommendEntAlloc;
            var hrName = string.IsNullOrWhiteSpace(detail?.HrName) ? string.Empty : $"({detail.HrName})";
            content = $"[顾问：{detail!.AdviserName}({detail.AdviserNo})尚未绑定钉钉，请督促他联系企业开通诺聘] 您好，企业线索：{detail!.EntName} 已分配给您，联系人：{detail!.HrMobile}{hrName}，请及时跟进。";
        }
        else if (baseNotify.Type == MsgNotifyType.钉钉机器人HR初筛定时提醒)
        {
            dingMsgModel.msgType = "sampleMarkdown";
            mdtitle = "HR初筛定时提醒";
            content = (string)baseNotify.Data;
        }
        else if (baseNotify.Type == MsgNotifyType.钉钉机器人投递简历提醒)
        {
            dingMsgModel.msgType = "sampleMarkdown";
            mdtitle = "求职者投递简历提醒";
            content = (string)baseNotify.Data;
        }
        else if (baseNotify.Type == MsgNotifyType.诺优考人才线索提醒)
        {
            content = (string)baseNotify.Data;
        }

        if (!string.IsNullOrWhiteSpace(content))
        {
            var testStr = _hostingEnvironment.IsProduction() ? string.Empty : "test:";
            content = $"{testStr}{content}";
            if (dingMsgModel.msgType == "sampleMarkdown")
            {
                dingMsgModel.msgParam = JsonSerializer.SerializeToString(new { title = mdtitle, text = content });
            }
            else if (dingMsgModel.msgType == "sampleText")
            {
                dingMsgModel.msgParam = JsonSerializer.SerializeToString(new { content = content });
            }

            var resp = await _dingDingRootHelper.SendDingDingRootMessage(dingMsgModel);

            var record = new Dd_Notify_Record
            {
                Data = JsonSerializer.SerializeToString(baseNotify),
                HrId = baseNotify.UserId ?? string.Empty,
                Type = baseNotify.Type!.Value,
                Status = DdNotifyRecordStatus.已发送,
                Content = content,
                ProcessQueryKey = resp?.processQueryKey
            };
            _context.Add(record);
            _context.SaveChanges();
        }
    }

    /// <summary>
    /// Im未读消息
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubImC2CMsgUnReadMsg(CancellationToken cancel)
    {
        //处理消息
        ZMember? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var today = DateTime.Today;
            if (DateTime.Now >= today.AddHours(20) || DateTime.Now <= today.AddHours(9))
            {
                await Task.Delay(300000);
                continue;
            }

            var workStartTime = DateTime.Now;
            try
            {
                var maxTime = DateTime.Now.AddMinutes(-10).ToUnixTimeMs();

                //查询到时间的消息
                msg = MyRedis.Client.ZRangeByScoreWithScores(SubscriptionKey.ImC2CMsgUnReadMsg, 0, maxTime, 0, 1).FirstOrDefault();

                if (msg == null || (msg is IDictionary<string, object> dict && dict.Count == 0))
                {
                    await Task.Delay(10000);
                    continue;
                }

                //取出之后移除
                MyRedis.Client.ZRem(SubscriptionKey.ImC2CMsgUnReadMsg, msg.member);

                //查询im账号未读消息数
                var timunreadmsg = await _tencentImHelper.GetC2CUnreadMsgNum(msg.member);
                if (timunreadmsg.AllC2CUnreadMsgNum <= 0)
                {
                    await Task.Delay(20);
                    continue;
                }

                //如果今天已发送过
                var rdsKey = $"im:urnt:{DateTime.Today.ToYYYY_MM_DD()}:{msg.member}";
                if (!MyRedis.Client.SetNx(rdsKey, 1, TimeSpan.FromDays(1)))
                {
                    await Task.Delay(20);
                    continue;
                }

                var appId = string.Empty;
                if (msg.member.StartsWith(Constants.ImHrIdPre))
                    appId = ClientApps.HrApplet.AppID;
                else if (msg.member.StartsWith(Constants.ImSeekerIdPre))
                    appId = ClientApps.SeekerApplet.AppID;
                else
                    throw new Exception("无法解析的Im账号前缀");

                var appletShortLink = await _weChatHelper.GetWxShortLinkForJump(appId);
                if (string.IsNullOrWhiteSpace(appletShortLink))
                {
                    await Task.Delay(20);
                    continue;
                }

                appletShortLink = appletShortLink.Split('/')[^1];

                using var _context = _contextFactory.CreateDbContext();

                var userId = Tools.GetUserIdByImId(msg.member);

                SendSmsUserInfo? usr = null;
                if (msg.member.StartsWith(Constants.ImHrIdPre))
                    usr = _context.User_Hr.Where(x => x.UserId == userId)
                    .Select(s => new SendSmsUserInfo
                    {
                        Mobile = s.User.Mobile,
                        Name = s.NickName,
                        Sex = s.Sex
                    }).FirstOrDefault();
                else if (msg.member.StartsWith(Constants.ImSeekerIdPre))
                    usr = _context.User_Seeker.Where(x => x.UserId == userId)
                   .Select(s => new SendSmsUserInfo
                   {
                       Mobile = s.User.Mobile,
                       Name = s.NickName,
                       Sex = s.User_Resume.Sex
                   }).FirstOrDefault();

                if (usr == null)
                {
                    await Task.Delay(20);
                    continue;
                }

                var userName = string.IsNullOrWhiteSpace(usr.Name) ? "你好" : usr.Name;
                if (msg.member.StartsWith(Constants.ImHrIdPre))
                    userName += "顾问";

                //发送短信
                _smsHelper.SendSMSAliyunForJumpApplet(usr.Mobile!, "SMS_246040278", new
                {
                    name = userName,
                    quantity = $"{timunreadmsg.AllC2CUnreadMsgNum}条",
                    jumpurl = appletShortLink
                });

            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.ImC2CMsgUnReadMsg}消息处理出错", Tools.GetErrMsg(e), new { member = msg?.member, score = msg?.score });
                await Task.Delay(1000);
            }

            await Task.Delay(20);
        }
    }

    /// <summary>
    /// 新用户通知
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubNewUserPush(CancellationToken cancel)
    {
        //处理消息
        Sub_NewUserMsgPush? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_NewUserMsgPush?>(SubscriptionKey.NewUserMsgPush);

                if (msg == null)
                {
                    await Task.Delay(30000);
                    continue;
                }

                var post = string.Empty;
                var company = string.Empty;

                //查询配置的职位
                var posts = MyRedis.Client.Get<List<GetPushPostSettingsInfo>?>($"{RedisKey.NScore.Key}{RedisKey.NScore.NewUserPushPosts}");

                if (posts != null && posts.Count > 0)
                {
                    var pc = posts.Count;
                    var ran = new Random(Guid.NewGuid().GetHashCode());
                    var ps = posts[ran.Next(0, pc)];
                    post = ps.Post;
                    company = ps.Company;
                }

                //公众号
                if (msg.WeChatH5Subscribe && !string.IsNullOrWhiteSpace(msg.H5OpenId))
                {
                    var tmpId = string.Empty;
                    var mpdata = new TemplateMessageData();

                    if (msg.Day == 2)
                    {
                        tmpId = "9VfgTLGSWz791g5xkGKXNuJ9QYd0nayBNSLT4JEpcYc";
                        mpdata.Add("first", new TemplateMessageDataValue("高薪急招，诺快聘顾问为您推荐了最新职位"));
                        mpdata.Add("keyword1", new TemplateMessageDataValue(string.IsNullOrWhiteSpace(post) ? "高薪职位" : post));
                        mpdata.Add("keyword2", new TemplateMessageDataValue(string.IsNullOrWhiteSpace(company) ? "高薪急招" : company));
                        mpdata.Add("keyword3", new TemplateMessageDataValue(DateTime.Now.ToYYYY_MM_DD_HH()));
                        mpdata.Add("remark", new TemplateMessageDataValue($"打开小程序了解职位详情"));
                    }
                    else if (msg.Day == 3)
                    {
                        tmpId = "9VfgTLGSWz791g5xkGKXNuJ9QYd0nayBNSLT4JEpcYc";
                        mpdata.Add("first", new TemplateMessageDataValue("您好，百强企业限时热招，名额有限"));
                        mpdata.Add("keyword1", new TemplateMessageDataValue(string.IsNullOrWhiteSpace(post) ? "百强企业限时热招" : post));
                        mpdata.Add("keyword2", new TemplateMessageDataValue(string.IsNullOrWhiteSpace(company) ? "名额有限" : company));
                        mpdata.Add("keyword3", new TemplateMessageDataValue(DateTime.Now.ToYYYY_MM_DD_HH()));
                        mpdata.Add("remark", new TemplateMessageDataValue($"打开小程序联系您的专属招聘顾问"));
                    }

                    if (!string.IsNullOrWhiteSpace(tmpId))
                    {
                        var rst = Senparc.Weixin.MP.AdvancedAPIs.TemplateApi.SendTemplateMessage(Constants.WeChatH5AppId,
                            msg.H5OpenId, tmpId, null,
                            mpdata, new TemplateModel_MiniProgram
                            {
                                appid = ClientApps.SeekerApplet.AppID,
                                pagepath = "pages/index/index?adviser=1"
                            });

                        if (rst.errcode != Senparc.Weixin.ReturnCode.请求成功)
                            throw new Exception($"发送微信公众号消息失败:{JsonSerializer.SerializeToString(rst)}");
                    }
                }

                //短信
                if (msg.Day == 2)
                {
                    var appletShortLink = await _weChatHelper.GetWxShortLinkForJump(ClientApps.SeekerApplet.AppID, "pages/index/index", "adviser=1");
                    appletShortLink = appletShortLink.Split('/')[^1];
                    if (string.IsNullOrWhiteSpace(appletShortLink))
                    {
                        await Task.Delay(20);
                        continue;
                    }
                    _smsHelper.SendSMSAliyunForJumpApplet(msg.Mobile, "SMS_267445015", new
                    {
                        zixun = string.IsNullOrWhiteSpace(post) ? "高薪职位" : post,
                        jumpurl = appletShortLink
                    });
                }
                else if (msg.Day == 3)
                {
                    var appletShortLink = await _weChatHelper.GetWxShortLinkForJump(ClientApps.SeekerApplet.AppID, "pages/index/index", "adviser=1");
                    appletShortLink = appletShortLink.Split('/')[^1];
                    if (string.IsNullOrWhiteSpace(appletShortLink))
                    {
                        await Task.Delay(20);
                        continue;
                    }
                    _smsHelper.SendSMSAliyunForJumpApplet(msg.Mobile, "SMS_265615336", new
                    {
                        seeker = string.Empty,
                        guwen = "诺快聘顾问",
                        jumpurl = appletShortLink
                    });
                }
                else if (msg.Day == 5)
                {
                    //第五日通知先不发，等运营通知。
                    // var appletShortLink = await _weChatHelper.GetWxShortLinkForJump(WeChatApps.SeekerApplet.AppID, "Mypackagedetail/myintegral/myintegral");
                    // appletShortLink = appletShortLink.Split('/')[^1];
                    // if (string.IsNullOrWhiteSpace(appletShortLink))
                    // {
                    //     await Task.Delay(20);
                    //     continue;
                    // }
                    // _smsHelper.SendSMSAliyunForJumpApplet(msg.Mobile, "SMS_265605376", new
                    // {
                    //     seeker = string.Empty,
                    //     jumpurl = appletShortLink
                    // });
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.NewUserMsgPush}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(20);
        }
    }

    /// <summary>
    /// Im接收求职者发送的全部消息并获取云生机器人回复
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubImC2CMsg(CancellationToken cancel)
    {
        //处理消息
        Sub_ImC2CMsg? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_ImC2CMsg>(SubscriptionKey.ImC2CMsg);
                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                using var _context = _contextFactory.CreateDbContext();
                _logger.Info($"求职者FromUserId:{msg.FromUserId}", "");
                _logger.Info($"招聘者ToUserId:{msg.ToUserId}", "");
                _logger.Info($"协同职位Id:{UserService.TeamPostId}", "");
                var postchat = _context.YunSheng_ChatId_Relation.
                    Where(x => x.UserId == msg.FromUserId!.Replace(Constants.ImSeekerIdPre, "") &&
                    x.AdviserImId == $"{msg.ToUserId}" &&
                    x.TeamPostId == UserService.TeamPostId).FirstOrDefault();
                _logger.Info($"云生会话Id:{postchat?.YunshengChatId}", "");
                _logger.Info($"消息数量:{msg.MsgBody?.Count}", "");
                if (postchat != null && msg.MsgBody != null)
                {
                    var hrAgentStatus = _context.Post_Team
                        .Where(t => t.TeamPostId == postchat.TeamPostId)
                        .Select(t => _context.Post
                            .Where(p => p.PostId == t.PostId)
                            .Select(p => p.HrAgentStatus)
                            .FirstOrDefault())
                        .FirstOrDefault();
                    _logger.Info($"hr代聊功能开启状态(TurnOff关闭,TurnOn开启)：{hrAgentStatus}", "");
                    if (postchat.YunshengChatId != null && hrAgentStatus == HrAgentStatus.TurnOn)//判断代聊天功能是否开启
                    {
                        string content = "";
                        switch (msg.MsgBody[0].MsgType)
                        {
                            case "TIMTextElem":
                                content = msg.MsgBody[0].MsgContent?.Text ?? string.Empty;
                                break;
                            case "TIMCustomElem":
                                content = msg.MsgBody[0].MsgContent?.Data ?? string.Empty;
                                break;
                            default:
                                _logger.Info("未知的消息类型：", $"{msg.MsgBody[0].MsgType}");
                                break;
                        }
                        _logger.Info($"求职者发送的消息：{content}", "");

                        ChatMessage chat = new ChatMessage
                        {
                            openId = "105060",
                            chatId = postchat.YunshengChatId,
                            messages = new List<chatInfo>()
                            {
                                new chatInfo
                                {
                                    role = "user",
                                    type = 1,
                                    content = content,
                                    messageTime = DateTime.UtcNow.ToUnixTimeMs()
                                }
                            }
                        };
                        Sub_ImC2CChatMsg imC2CChatMsg = new Sub_ImC2CChatMsg()
                        {
                            //招聘者与求职者ID对调，云生消息最终要发送给求职者
                            From_Account = msg.ToUserId,//招聘者
                            To_Account = msg.FromUserId,//求职者
                            chat = chat
                        };

                        //修改为将聊天保存到Redis中，以异步方式运行，避免进程等待
                        MyRedis.Client.SAdd(SubscriptionKey.ImC2CChatMsg, imC2CChatMsg);
                        //添加求职者聊天记录到数据库
                        _context.YunSheng_Chat_Messages.Add(new YunSheng_Chat_Messages
                        {
                            SeekerId = msg.FromUserId!,//求职者Id
                            HrId = msg.ToUserId!,//招聘者Id
                            ChatId = postchat.YunshengChatId,//聊天ID
                            Role = YunshengChatRole.Seeker,//角色为求职者
                            Content = msg.MsgBody[0].MsgContent!.Text,
                            Type = YunshengChatType.Message,//类型为消息
                            MessageTime = DateTime.Now
                        });
                        _context.SaveChanges();
                    }
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.ImC2CMsg}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString<List<SendImMsgBody>>(msg?.MsgBody!));
                await Task.Delay(1000);
            }

            await Task.Delay(20);
        }
    }
    public async Task SubImC2CChatMsg(CancellationToken cancel)
    {
        Sub_ImC2CChatMsg? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_ImC2CChatMsg>(SubscriptionKey.ImC2CChatMsg);
                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }
                //处理消息

                //调用云生接口获取机器人会话内容
                ChatMessageReceive? receivedChatContent = await _zpyunRepository.GetChatMessage(msg.chat);
                if (receivedChatContent != null)
                {
                    _logger.Info($"求职者收到的消息：{receivedChatContent.messages?[0].content}", "");
                    MyRedis.Client.SAdd(SubscriptionKey.ImB2CMsg, new Sub_ImB2CMsg
                    {
                        SyncOtherMachine = 1, // 1表示消息同步至发送方，2表示不同步
                        From_Account = msg.From_Account,
                        To_Account = msg.To_Account,
                        MsgBody = receivedChatContent.messages?.Select(x => new SendImMsgBody
                        {
                            MsgType = "TIMTextElem",
                            MsgContent = new MsgContentBody
                            {
                                Text = x.content
                            }
                        }).ToList()
                    });
                    using var _context = _contextFactory.CreateDbContext();
                    //添加机器人回复记录到数据库
                    DateTime messageTime = DateTime.Now;
                    //判断是否有动作，有则保存动作到数据库
                    if (receivedChatContent.messages?.Count > 1 &&
                        receivedChatContent.messages[1]?.commands?.Count > 0)
                    {
                        _context.YunSheng_Chat_Messages.Add(new YunSheng_Chat_Messages
                        {
                            SeekerId = msg.To_Account!,
                            HrId = msg.From_Account!,
                            ChatId = msg.chat?.chatId!,//聊天ID
                            Role = YunshengChatRole.Assistant,//机器人
                            Type = YunshengChatType.Command,//动作
                            Commands = string.Join("|", receivedChatContent.messages[1]?.commands ?? new List<string>()),//云生回复的动作
                            MessageId = receivedChatContent.messages[1]?.messageId,//消息Id
                            MessageTime = messageTime
                        });
                    }
                    //消息保存到数据库
                    _context.YunSheng_Chat_Messages.Add(new YunSheng_Chat_Messages
                    {
                        SeekerId = msg.To_Account!,
                        HrId = msg.From_Account!,
                        ChatId = msg.chat?.chatId!,//聊天ID
                        Role = YunshengChatRole.Assistant,//机器人
                        Content = receivedChatContent?.messages?[0].content,//云生回复的内容
                        Type = YunshengChatType.Message,//消息
                        MessageId = receivedChatContent?.messages?[0].messageId,//消息Id
                        MessageTime = messageTime
                    });

                    _context.SaveChanges();
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.ImC2CChatMsg}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(20);
        }
    }

    public async Task SubImB2CMsg(CancellationToken cancel)
    {
        //处理消息
        Sub_ImB2CMsg? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_ImB2CMsg>(SubscriptionKey.ImB2CMsg);
                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                ImSendMsgInfo imMsg = new ImSendMsgInfo()
                {
                    SyncOtherMachine = msg.SyncOtherMachine,
                    From_Account = msg.From_Account,
                    To_Account = msg.To_Account,
                    MsgRandom = DateTime.UtcNow.ToUnixTimeSeconds(),
                    MsgBody = new List<SendImMsgBody>()
                    {
                        new SendImMsgBody()
                        {
                            MsgType = "TIMTextElem",
                            MsgContent = new MsgContentBody
                            {
                                Text = msg?.MsgBody?[0].MsgContent?.Text
                            }
                        }
                    },
                    SupportMessageExtension = 0
                };

                _logger.Info($"向Im发送消息:{JsonSerializer.SerializeToString(imMsg)}", "");
                var result = await _tencentImHelper.ImSendMsg(imMsg);
                _logger.Info($"Im返回结果:{JsonSerializer.SerializeToString(result)}", "");
                if (result != null && result.ErrorCode == 0)
                {
                    _logger.Info($"result", "消息回复成功！", "");
                    //将双方消息置为已读
                    _logger.Info($"ImId：", $"{msg?.From_Account}", "");
                    _logger.Info($"ToImId：", $"{msg?.To_Account}", "");
                    ImMsgReadResponse respose
                    = _tencentImHelper.ImMsgRead(new ImMsgReadFull
                    {
                        ImId = msg?.From_Account,
                        ToImId = msg?.To_Account
                    });
                    //返回对方的消息置为已读时间戳
                    _logger.Info($"已读时间戳：", $"{respose.ReversalTime}", "");
                }
                else
                {
                    _logger.Error("result", $"消息回复失败！" + result?.ErrorInfo, "");
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.ImB2CMsg}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(20);
        }
    }

    // /// <summary>
    // /// Im未读消息
    // /// </summary>
    // /// <param name="cancel"></param>
    // /// <returns></returns>
    // public async Task SubImC2CMsgUnReadMsg(CancellationToken cancel)
    // {
    //     //处理消息
    //     string? msg = null;
    //     while (!cancel.IsCancellationRequested)
    //     {
    //         var workStartTime = DateTime.Now;
    //         try
    //         {
    //             msg = MyRedis.Client.LPop<string?>(SubscriptionKey.ImC2CMsgUnReadMsg);

    //             if (msg == null)
    //             {
    //                 await Task.Delay(500);
    //                 continue;
    //             }

    //             var jt = JToken.Parse(msg);
    //             var bodyObj = jt.ToObject<CallbackC2CAfterSendMsg>();

    //             var fromUser = GetImUserInfo(bodyObj!.From_Account!);
    //             var toUser = GetImUserInfo(bodyObj.To_Account!);

    //             // 处理消息
    //             using var _context = _contextFactory.CreateDbContext();

    //             var wechatOpenId = toUser?.WeChatOpenId;

    //             if (string.IsNullOrWhiteSpace(wechatOpenId))
    //                 continue;

    //             //redis实时查询订阅状态
    //             var subscript = MyRedis.Client.HGet<bool>(RedisKey.WeChatMpSub, wechatOpenId);
    //             if (!subscript)
    //                 continue;

    //             var msgContent = string.Empty;
    //             var firstMsg = bodyObj.MsgBody?.FirstOrDefault();

    //             if (firstMsg == null)
    //                 msgContent = "[未知类型消息]";
    //             else
    //             {
    //                 var msgBody = JToken.FromObject(firstMsg);

    //                 if (firstMsg?.MsgType == "TIMTextElem")
    //                     msgContent = msgBody.Value<JToken>("MsgContent")!.Value<string>("Text");
    //                 else if (firstMsg?.MsgType == "TIMLocationElem")
    //                     msgContent = "[位置信息]";
    //                 else if (firstMsg?.MsgType == "TIMFaceElem")
    //                     msgContent = "[表情]";
    //                 else if (firstMsg?.MsgType == "TIMCustomElem")
    //                     msgContent = "[系统消息]";
    //                 else if (firstMsg?.MsgType == "TIMImageElem")
    //                     msgContent = "[图像消息]";
    //                 else if (firstMsg?.MsgType == "TIMFileElem")
    //                     msgContent = "[文件消息]";
    //                 else if (firstMsg?.MsgType == "TIMVideoFileElem")
    //                     msgContent = "[视频消息]";
    //                 else if (firstMsg?.MsgType == "TIMSoundElem")
    //                     msgContent = "[语音消息]";
    //                 else
    //                     msgContent = "[未知类型消息]";
    //             }

    //             var tmpId = "yvKhNXkd4-3Br3gAxln1EaBUUnA3twC5w_P5DDzm-Vs";
    //             var mpdata = new TemplateMessageData();
    //             mpdata.Add("first", new TemplateMessageDataValue("您好，您收到一条消息。"));
    //             mpdata.Add("keyword1", new TemplateMessageDataValue(string.IsNullOrWhiteSpace(fromUser?.ImName) ? "微信用户" : fromUser?.ImName));
    //             mpdata.Add("keyword2", new TemplateMessageDataValue((bodyObj.MsgTime!.Value).FromUnixTime().ToYYYY_MM_DD_HH()));
    //             mpdata.Add("keyword3", new TemplateMessageDataValue(msgContent));
    //             mpdata.Add("remark", new TemplateMessageDataValue(string.Empty));

    //             TemplateModel_MiniProgram? miniProgram = null;

    //             if (bodyObj.To_Account?.StartsWith(Constants.ImHrIdPre) == true)
    //             {
    //                 miniProgram = new TemplateModel_MiniProgram
    //                 {
    //                     appid = WeChatApps.HrApplet.AppID
    //                 };
    //             }
    //             else if (bodyObj.To_Account?.StartsWith(Constants.ImSeekerIdPre) == true)
    //             {
    //                 miniProgram = new TemplateModel_MiniProgram
    //                 {
    //                     appid = WeChatApps.SeekerApplet.AppID
    //                 };
    //             }

    //             //通知主创
    //             var rst = Senparc.Weixin.MP.AdvancedAPIs.TemplateApi.SendTemplateMessage(Constants.WeChatH5AppId,
    //             wechatOpenId, tmpId, null,
    //             mpdata, miniProgram);

    //             if (rst.errcode != Senparc.Weixin.ReturnCode.请求成功)
    //                 throw new Exception($"发送微信公众号消息失败:{JsonSerializer.SerializeToString(rst)}");
    //         }
    //         catch (Exception e)
    //         {
    //             _logger.Error($"{SubscriptionKey.ImC2CMsgUnReadMsg}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
    //             await Task.Delay(1000);
    //         }

    //         await Task.Delay(10);
    //     }
    // }

    // private ImMsgUserInfo? GetImUserInfo(string imId)
    // {
    //     var imKeyPre = "imuserinfo:";
    //     var userInfo = _cacheHelper.GetMemoryCache<ImMsgUserInfo?>(() =>
    //     {
    //         using (var _context = _contextFactory.CreateDbContext())
    //         {
    //             if (imId.StartsWith(Constants.ImHrIdPre) == true)
    //             {
    //                 return _context.User_Hr.Where(x => x.TencentImId == imId)
    //                 .Select(s => new ImMsgUserInfo
    //                 {
    //                     ImId = imId,
    //                     ImName = s.NickName,
    //                     WeChatOpenId = s.User.WeChatH5OpenId
    //                 }).FirstOrDefault();
    //             }
    //             else if (imId?.StartsWith(Constants.ImSeekerIdPre) == true)
    //             {
    //                 return _context.User_Seeker.Where(x => x.TencentImId == imId)
    //                 .Select(s => new ImMsgUserInfo
    //                 {
    //                     ImId = imId,
    //                     ImName = s.NickName,
    //                     WeChatOpenId = s.User.WeChatH5OpenId
    //                 }).FirstOrDefault();
    //             }
    //             else
    //                 return null;
    //         }
    //     }, $"{imKeyPre}{imId}", new Random().Next(3600));

    //     return userInfo;
    // }
}

// public class ImMsgUserInfo
// {
//     public string? WeChatOpenId { get; set; }
//     public string? ImId { get; set; }
//     public string? ImName { get; set; }
// }

public class SendSmsUserInfo
{
    public string? Name { get; set; }

    public string? Mobile { get; set; }

    public Sex? Sex { get; set; }
}