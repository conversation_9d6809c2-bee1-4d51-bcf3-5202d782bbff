using Infrastructure.Common;
using Staffing.Worker.Service;

namespace Staffing.Worker.Hosting;

public class CanalWorker : HostingBase
{
    private readonly CanalService _canalService;
    private readonly LogManager _log;
    private readonly IHostEnvironment _hostingEnvironment;
    public CanalWorker(IHostApplicationLifetime lifetime, IHostEnvironment hostingEnvironment,
     LogManager log, CanalService canalService) : base(lifetime)
    {
        _log = log;
        _hostingEnvironment = hostingEnvironment;
        _canalService = canalService;
    }

    public override async Task StartAsync(CancellationToken cancel)
    {
        if (!_hostingEnvironment.IsDevelopment())
        {
            //_bgTasks.Add(await Task.Factory.StartNew(() => _canalService.SubCanalStaffing(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        }
        _bgTasks.Add(await Task.Factory.StartNew(() => _canalService.SubTableChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _canalService.SubTalentTableChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _canalService.SubVirtualTableChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _canalService.SubNkpPostChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _canalService.SubNkpHrChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _canalService.PostChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _canalService.TeamPostStatusChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _canalService.TalentResumeChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        //_bgTasks.Add(await Task.Factory.StartNew(() => _canalService.SubKuaishouTalentInfoTableChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
    }
}