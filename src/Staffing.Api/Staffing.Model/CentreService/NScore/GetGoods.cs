// using Config.CommonModel;
// using Config.Enums;
// using Staffing.Entity.Staffing;

// namespace Staffing.Model.CentreService.NScore;

// public class GetGoodsList : QueryRequest
// {
//     /// <summary>
//     /// hr还是求职者
//     /// </summary>
//     public SeekerOrHr UserType { get; set; } = SeekerOrHr.Seeker;

//     /// <summary>
//     /// 类型
//     /// </summary>
//     public NScoreGoodsType? Type { get; set; }

//     /// <summary>
//     /// 商品名称
//     /// </summary>
//     public string? GoodsName { get; set; }

//     /// <summary>
//     /// 是否上架
//     /// </summary>
//     public int? Active { get; set; }

//     /// <summary>
//     /// 库存小
//     /// </summary>
//     public int? MinStock { get; set; }

//     /// <summary>
//     /// 库存大
//     /// </summary>
//     public int? MaxStock { get; set; }
// }

// public class GetGoodsListResponse : QueryResponse
// {
//     /// <summary>
//     /// 商品列表
//     /// </summary>
//     public List<GoodsInfo>? Rows { get; set; }
// }

// public class GoodsInfo
// {
//     /// <summary>
//     /// 商品Id
//     /// </summary>
//     public string? Id { get; set; }

//     /// <summary>
//     /// 类型
//     /// </summary>
//     public NScoreGoodsType Type { get; set; }

//     /// <summary>
//     /// 类型名称
//     /// </summary>
//     public string? TypeName { get; set; }

//     /// <summary>
//     /// 商品名称
//     /// </summary>
//     public string? Name { get; set; }

//     /// <summary>
//     /// 商品描述
//     /// </summary>
//     public string? Describe { get; set; }

//     /// <summary>
//     /// 所需积分
//     /// </summary>
//     public int Score { get; set; }

//     /// <summary>
//     /// 商品价值
//     /// </summary>
//     public decimal Money { get; set; }

//     /// <summary>
//     /// 库存
//     /// </summary>
//     public int Stock { get; set; }

//     /// <summary>
//     /// 是否上架
//     /// </summary>
//     public bool? Active { get; set; }

//     /// <summary>
//     /// 缩略图
//     /// </summary>
//     public string? Thumbnail { get; set; }

//     /// <summary>
//     /// 更新时间
//     /// </summary>
//     public DateTime? UpdatedTime { get; set; }
// }

// public class UpdateGoods
// {
//     /// <summary>
//     /// hr还是求职者
//     /// </summary>
//     public SeekerOrHr UserType { get; set; } = SeekerOrHr.Seeker;

//     /// <summary>
//     /// 商品Id（不传=添加）
//     /// </summary>
//     public string? Id { get; set; }

//     /// <summary>
//     /// 类型
//     /// </summary>
//     public NScoreGoodsType? Type { get; set; }

//     /// <summary>
//     /// 商品名称
//     /// </summary>
//     public string? Name { get; set; }

//     /// <summary>
//     /// 商品描述
//     /// </summary>
//     public string? Describe { get; set; }

//     /// <summary>
//     /// 所需积分
//     /// </summary>
//     public int Score { get; set; }

//     /// <summary>
//     /// 商品价值
//     /// </summary>
//     public decimal Money { get; set; }

//     /// <summary>
//     /// 库存
//     /// </summary>
//     public int Stock { get; set; }

//     /// <summary>
//     /// 缩略图
//     /// </summary>
//     public string? Thumbnail { get; set; }
// }

// public class UpdateGoodsResponse
// {
//     /// <summary>
//     /// 商品Id
//     /// </summary>
//     public string? Id { get; set; }
// }

// public class SetGoods
// {
//     /// <summary>
//     /// 商品Id
//     /// </summary>
//     public string? Id { get; set; }

//     /// <summary>
//     /// 是否上架
//     /// </summary>
//     public bool? Active { get; set; }
// }

// public class SetGoodsResponse
// {

// }