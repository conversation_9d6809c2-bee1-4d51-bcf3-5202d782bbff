using Config;
using Config.CommonModel.Business;
using Config.CommonModel.Recruit;
using Config.CommonModel.Tencent;
using Config.CommonModel.ThirdTalentInfo;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.CommonService.ShareProfit;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Infrastructure.Proxy;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using NetTopologySuite.Geometries;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Infrastructure.CommonService;

[Service(ServiceLifetime.Transient)]
public class CommonUserService
{
    private IDbContextFactory<StaffingContext> _staffingContextFactory;
    private ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly TencentImHelper _tencentImHelper;
    private readonly NuoPinApi _nuoPinApi;
    private readonly ProfitService _profitService;
    private readonly CommonPostOrder _commonPostOrder;
    private RequestContext _user;

    public CommonUserService(IDbContextFactory<StaffingContext> staffingContextFactory, IOptionsSnapshot<ConfigManager> config,
    IHostEnvironment hostingEnvironment, LogManager log, TencentImHelper tencentImHelper, NuoPinApi nuoPinApi,
    CommonPostOrder commonPostOrder, RequestContext user, ProfitService profitService)
    {
        _staffingContextFactory = staffingContextFactory;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
        _tencentImHelper = tencentImHelper;
        _nuoPinApi = nuoPinApi;
        _profitService = profitService;
        _commonPostOrder = commonPostOrder;
        _user = user;
    }

    /// <summary>
    /// 计算简历完善度
    /// </summary>
    /// <param name="userId"></param>
    public void CountResumeScore(string userId)
    {
        using var context = _staffingContextFactory.CreateDbContext();

        var resume = context.User_Resume.Where(x => x.UserId == userId)
        .Select(s => new
        {
            Name = !string.IsNullOrWhiteSpace(s.User_Seeker.NickName) ? 10 : 0,
            Sex = s.Sex.HasValue ? 6 : 0,
            Birthday = s.Birthday.HasValue ? 6 : 0,
            Occupation = s.Occupation.HasValue ? 6 : 0,
            Mobile = true ? 3 : 0,
            WeChatNo = !string.IsNullOrWhiteSpace(s.WeChatNo) ? 6 : 0,
            Qq = !string.IsNullOrWhiteSpace(s.Qq) ? 1 : 0,
            EMail = !string.IsNullOrWhiteSpace(s.EMail) ? 1 : 0,
            Education = s.Education.HasValue ? 10 : 0,
            School = !string.IsNullOrWhiteSpace(s.School) ? 8 : 0,
            GraduationDate = s.GraduationDate.HasValue ? 4 : 0,
            Major = !string.IsNullOrWhiteSpace(s.Major) ? 6 : 0,
            Describe = !string.IsNullOrWhiteSpace(s.Describe) ? 8 : 0,
            Nature = s.Nature.Count > 0 ? 1 : 0,
            Skill = s.Skill.Count > 0 ? 1 : 0,
            Appearance = s.Appearance.Count > 0 ? 1 : 0,
            User_Work = s.User_Work.Any(a => true) ? 22 : 0
        }).FirstOrDefault();

        if (resume == null)
            return;

        var rm = new User_Resume { UserId = userId };
        context.Attach(rm);
        rm.Score = resume.Name + resume.Sex + resume.Birthday + resume.Occupation + resume.Mobile
         + resume.WeChatNo + resume.Qq + resume.EMail + resume.Education + resume.School + resume.GraduationDate
         + resume.Major + resume.Describe + resume.Nature + resume.Skill + resume.Appearance + resume.User_Work;

        context.SaveChanges();
    }

    /// <summary>
    /// 计算Hr资料完整度
    /// </summary>
    /// <param name="hrId"></param>
    public void CountHrScore(string hrId)
    {
        using var context = _staffingContextFactory.CreateDbContext();

        var resume = context.User_Hr.Where(x => x.UserId == hrId)
        .Select(s => new
        {
            Avatar = !string.IsNullOrWhiteSpace(s.Avatar) ? 10 : 0,
            Name = !string.IsNullOrWhiteSpace(s.NickName) ? 5 : 0,
            Sex = s.Sex.HasValue ? 10 : 0,
            Post = !string.IsNullOrWhiteSpace(s.Post) ? 10 : 0,
            Describe = !string.IsNullOrWhiteSpace(s.Describe) ? 10 : 0,
            Mobile = 10,
            EMail = !string.IsNullOrWhiteSpace(s.EMail) ? 10 : 0,
            Address = !string.IsNullOrWhiteSpace(s.Address) ? 10 : 0,
            WeChatNo = !string.IsNullOrWhiteSpace(s.WeChatNo) ? 10 : 0,
            EntWeChatQrCode = !string.IsNullOrWhiteSpace(s.EntWeChatQrCode) ? 10 : 0,
            Ent = 5
        }).FirstOrDefault();

        if (resume == null)
            return;

        var rm = new User_Hr { UserId = hrId };
        context.Attach(rm);
        rm.Score = resume.Avatar + resume.Name + resume.Sex + resume.Post + resume.Describe + resume.Mobile
         + resume.EMail + resume.Address + resume.WeChatNo + resume.EntWeChatQrCode + resume.Ent;

        context.SaveChanges();
    }

    /// <summary>
    /// 查询用户工号
    /// </summary>
    /// <param name="hrId"></param>
    public string? GetUserNo(string hrId)
    {
        var result = GetUserNos(new[] { hrId });
        return result.GetValueOrDefault(hrId);
    }

    /// <summary>
    /// 批量查询用户工号
    /// </summary>
    /// <param name="hrIds"></param>
    public Dictionary<string, string> GetUserNos(IEnumerable<string> hrIds)
    {
        var hrIdList = hrIds.ToList();
        if (!hrIdList.Any())
            return new Dictionary<string, string>();

        using var context = _staffingContextFactory.CreateDbContext();

        // 首先从 User_DingDing 表查询
        var dingDingJobNos = context.User_DingDing
            .Where(x => hrIdList.Contains(x.UserId) && !string.IsNullOrWhiteSpace(x.DingJobNo))
            .Select(s => new { s.UserId, JobNo = s.DingJobNo })
            .ToList();

        var result = dingDingJobNos.ToDictionary(x => x.UserId, x => x.JobNo);

        // 找出还没有工号的用户ID
        var missingHrIds = hrIdList.Except(result.Keys).ToList();

        if (missingHrIds.Any())
        {
            // 从 User 表的关联表查询剩余的工号
            var userJobNos = context.User
                .Where(x => missingHrIds.Contains(x.UserId) && !string.IsNullOrWhiteSpace(x.Dd_User.JobNumber))
                .Select(s => new { s.UserId, JobNo = s.Dd_User.JobNumber })
                .ToList();

            foreach (var item in userJobNos)
            {
                result[item.UserId] = item.JobNo;
            }
        }

        return result;
    }

    //更改用户活跃度
    public void UserActivity(string userId, ClientType type, string? ip = "")
    {
        var cacheKey = $"{RedisKey.UserLoginTime}{userId}";
        Task.Run(() =>
        {
            if (!(MyRedis.Client.SetNx(cacheKey, 1, 600)))
                return;

            using var _context = _staffingContextFactory.CreateDbContext();
            var userExtend = _context.User_Extend.FirstOrDefault(x => x.UserId == userId);
            if (userExtend != null)
            {
                userExtend.LoginTime = DateTime.Now;
            }

            //增加一个登录记录
            var loginRecord = new User_Login_Record
            {
                IpAddress = ip ?? string.Empty,
                Type = type,
                UserId = userId
            };
            _context.Add(loginRecord);

            _context.SaveChanges();

            //计算hr排行榜
            MyRedis.Client.SAdd(SubscriptionKey.HrRank, userId);
        });
    }

    /// <summary>
    /// 切换顾问
    /// </summary>
    /// <param name="seekerId"></param>
    /// <param name="hrId"></param>
    /// <param name="level"></param>
    /// <param name="channelId"></param>
    public void SetAdviser(string seekerId, string hrId, TalentPlatformLevel level = TalentPlatformLevel.访问用户, string? channelId = null)
    {
        try
        {
            using var _context = _staffingContextFactory.CreateDbContext();

            //人才库是否存在，不存在则补进人才库
            var talent = _context.Talent_Platform.IgnoreQueryFilters()
            .FirstOrDefault(x => x.SeekerId == seekerId && x.HrId == hrId);

            // //自动进顾问列表
            // var userAdviser = _context.User_Seeker_Adviser.IgnoreQueryFilters()
            // .FirstOrDefault(x => x.SeekerId == seekerId && x.AdviserId == hrId);

            if (talent == null) // || userAdviser == null)
            {
                //判断用户是否存在
                if (!_context.User_Seeker.Any(x => x.UserId == seekerId) || !_context.User_Hr.Any(x => x.UserId == hrId))
                    return;
            }

            var newTalent = false;
            // var newAdviser = false;
            if (talent == null)
            {
                newTalent = true;
                if (!string.IsNullOrWhiteSpace(channelId))
                    if (channelId != Constants.PlatformHrId)
                        if (!_context.Qd_Hr_Channel.Any(x => x.Id == channelId && x.HrId == hrId))
                            channelId = null;

                talent = new Talent_Platform
                {
                    HrId = hrId,
                    SeekerId = seekerId,
                    Source = HrProjectSource.自己项目,
                    Status = ActiveStatus.Active,
                    Remark = string.Empty,
                    Level = level,
                    ChannelId = channelId
                };
                _context.Add(talent);
            }
            talent.SeekerVisitTime = DateTime.Now;

            // if (userAdviser == null)
            // {
            //     userAdviser = new User_Seeker_Adviser
            //     {
            //         AdviserId = hrId,
            //         SeekerId = seekerId
            //     };
            //     _context.Add(userAdviser);
            //     newAdviser = true;
            // }
            // userAdviser.VisitTime = DateTime.Now;
            _context.SaveChanges();

            //更新hr人才数量
            if (newTalent)
            {
                MyRedis.Client.SAdd(SubscriptionKey.TalentCountChange, new Sub_TalentCount_Change
                {
                    HrId = hrId,
                    SeekerId = seekerId
                });
            }

            MyRedis.Client.SAdd(SubscriptionKey.VisitAdviser, new Sub_SeekerAndHr
            {
                SeekerId = seekerId,
                HrId = hrId
            });
        }
        catch (Exception e)
        {
            _log.Error("SetAdviser错误", Tools.GetErrMsg(e), $"{seekerId}_{hrId}");
            throw;
        }
    }

    /// <summary>
    /// 清理hr token
    /// </summary>
    /// <param name="nuoId"></param>
    public void CleanHrAccessToken(string nuoId)
    {
        var cacheKey = $"{RedisKey.UserTokenKey}{nuoId}";
        var tokenKey = MyRedis.Client.Get(cacheKey);
        if (!string.IsNullOrWhiteSpace(tokenKey))
            MyRedis.Client.Del(tokenKey);
    }

    /// <summary>
    /// 获取c端ImId
    /// </summary>
    /// <param name="hrId"></param>
    public async Task<GetImUserSigResponse> GetSeekerImUserSig(string userId)
    {
        var result = new GetImUserSigResponse();

        using var context = _staffingContextFactory.CreateDbContext();

        var seeker = context.User_Seeker.Where(x => x.UserId == userId)
        .Select(s => new
        {
            s.NickName,
            s.Avatar,
            s.TencentImId,
        }).First();

        var imId = seeker.TencentImId;

        //如果im不存在，创建im账号
        if (string.IsNullOrWhiteSpace(imId))
        {
            imId = $"{Constants.ImSeekerIdPre}{userId}";
            await _tencentImHelper.CreateImAccount(new CreateImAccount
            {
                Identifier = imId,
                FaceUrl = seeker.Avatar,
                Nick = seeker.NickName
            });

            //更新imid
            var updateSeeker = new User_Seeker
            {
                UserId = userId
            };
            context.Attach(updateSeeker);
            updateSeeker.TencentImId = imId;
            context.SaveChanges();

            //通知更新IM资料
            MyRedis.Client.SAdd(SubscriptionKey.UpdateSkImAct, userId);
        }

        result.Expire = 3600 * 24;
        result.UserSig = _tencentImHelper.GetUserSig(imId, result.Expire);
        result.ImId = imId;

        return result;
    }

    /// <summary>
    /// 获取b端ImId
    /// </summary>
    /// <param name="hrId"></param>
    public async Task<GetImUserSigResponse> GetHrImUserSig(string userId)
    {
        var result = new GetImUserSigResponse();

        using var context = _staffingContextFactory.CreateDbContext();

        var hr = context.User_Hr.Where(x => x.UserId == userId)
        .Select(s => new
        {
            s.NickName,
            s.Avatar,
            s.TencentImId,
        }).First();

        var imId = hr.TencentImId;

        //如果im不存在，创建im账号
        if (string.IsNullOrWhiteSpace(imId))
        {
            imId = $"{Constants.ImHrIdPre}{userId}";
            await _tencentImHelper.CreateImAccount(new CreateImAccount
            {
                Identifier = imId,
                FaceUrl = hr.Avatar,
                Nick = hr.NickName
            });

            //更新imid
            var updateHr = new User_Hr
            {
                UserId = userId
            };
            context.Attach(updateHr);
            updateHr.TencentImId = imId;
            context.SaveChanges();

            //通知更新IM资料
            MyRedis.Client.SAdd(SubscriptionKey.UpdateHrImAct, userId);
        }

        result.Expire = 3600 * 24;
        result.UserSig = _tencentImHelper.GetUserSig(imId, result.Expire);
        result.ImId = imId;

        return result;
    }

    /// <summary>
    /// 创建求职者用户
    /// </summary>
    public CreateSeekerResponse CreateSeeker(CreateSeekerModel model)
    {
        var result = new CreateSeekerResponse();

        if (!model.Source.HasValue)
            throw new BadRequestException("缺少来源");

        using var context = _staffingContextFactory.CreateDbContext();

        // 创建用户
        var user = context.User.FirstOrDefault(f => f.Mobile == model.Mobile);
        if (user == null)
        {
            user = new User { Mobile = model.Mobile };
            context.Add(user);
            var userExt = new User_Extend { UserId = user.UserId };
            context.Add(userExt);
            user.User_Num = new User_Num { UserId = user.UserId };
            context.Add(user);
            var userBalance = new User_Balance { UserId = user.UserId };
            context.Add(userBalance);
        }

        // 创建求职者
        var seeker = context.User_Seeker.FirstOrDefault(x => x.UserId == user.UserId);
        if (seeker == null)
        {
            seeker = new User_Seeker
            {
                Source = model.Source.Value,
                Status = UserStatus.Active,
                UserId = user.UserId,
                NickName = model.Name ?? string.Empty,
                RegionId = model.RegionId ?? string.Empty,
                Address = model.Address,
                Location = model.Location,
                ChannelSource = model.ChannelSource
            };
            context.Add(seeker);
            // 创seeker简历表
            var resume = new User_Resume
            {
                UserId = user.UserId,
                Sex = model.Sex,
                Birthday = model.Birthday,
                Education = model.Education
            };
            context.Add(resume);
            result.NewSeeker = true;
        }
        context.SaveChanges();

        // if (result.NewSeeker)
        // {
        //     //求职者注册消息
        //     MyRedis.Client.RPush(SubscriptionKey.SeekerRegister, seeker.UserId);

        //     //计算简历完善度
        //     CountResumeScore(seeker.UserId);

        //     //诺聘静默注册
        //     _ = Task.Run(() => _nuoPinApi.SilentRegistration(model.Mobile));
        // }

        //进入人才库
        if (!string.IsNullOrWhiteSpace(model.AdviserId))
            SetAdviser(seeker.UserId, model.AdviserId, channelId: model.ChannelId);

        result.UserId = seeker.UserId;
        return result;
    }

    public CommonDeliverResumeResponse DeliverResume(CommonDeliverResume model)
    {
        if (string.IsNullOrWhiteSpace(model.TeamPostId))
            throw new BadRequestException("缺少TeamPostId");

        if (string.IsNullOrWhiteSpace(model.UserId))
            throw new BadRequestException("缺少UserId");

        var userId = model.UserId;

        var result = new CommonDeliverResumeResponse();

        using var context = _staffingContextFactory.CreateDbContext();

        var seeker = context.User_Resume.Where(x => x.UserId == userId)
        .Select(s => new
        {
            s.User_Seeker.NickName,
            s.Sex,
            s.Birthday,
            s.Occupation
        }).FirstOrDefault();

        if (seeker == null)
            throw new BadRequestException("用户不存在");

        // 30天内不允许重复投递同一职位
        if (context.Recruit.Any(x => x.Post_Delivery.TeamPostId == model.TeamPostId
        && x.SeekerId == userId && x.CreatedTime > DateTime.Now.AddDays(-30)))
            throw new BadRequestException("您已投递该职位，请勿重复操作");

        //岗位信息
        var post = context.Post_Team.Where(x => x.TeamPostId == model.TeamPostId)
        .Select(s => new
        {
            s.Post.LeftStock,
            s.TeamProjectId,
            s.Post.ProjectId,
            s.Post.Project.HrId,
            s.Post.WorkNature,
            s.Post.Sex,
            s.Post.MaxAge,
            s.Post.MinAge,
            s.Post.Education,
            s.Post.GraduationYear,
            s.Post.PostId,
            s.Post.Name,
            s.Post.Status,
            TeamStatus = s.Status,
            TeamHrId = s.Project_Team.HrId,
            ProjectStatus = s.Post.Project.Status,
            s.Show,
            s.Post.Money,
            ProjectName = s.Post.Project.Agent_Ent.Name,
            AgentEntName = s.Post.Project.Agent_Ent.Name,
            s.Post.PaymentNode,
            s.Post.PaymentDays,
            s.Post.Project.PaymentType,
            TeamProjectType = s.Project_Team.Type,
            s.Post.IsResumeExempt
        }).FirstOrDefault();

        if (post == null)
            throw new NotFoundException("内容不存在");

        if (!post.Show)
            throw new BadRequestException("该职位已关闭");

        if (context.Recruit.Any(x => x.Post_Delivery.PostId == post.PostId
        && x.SeekerId == userId && x.Status != RecruitStatus.FileAway))
        {
            var msg = model.UserClient == ClientType.NuoPin ? "您已投递该职位，请勿重复操作" : "您已在其他顾问处投递该职位，请勿重复操作";
            throw new BadRequestException(msg);
        }

        // 产品：这一步不减库存，库存校验放在下一步
        // 库存校验 todo:主创渠道场景暂时未考虑
        if (post.LeftStock <= 0)
            throw new BadRequestException("人员已招满，无法投递");

        //简历
        var resume = context.User_Resume.Where(x => x.UserId == userId)
        .Select(s => new
        {
            s.Education,
            s.Sex,
            s.Birthday,
            s.GraduationDate
        }).First();

        //比较职位和简历
        if (post.WorkNature == PostWorkNature.实习 || post.WorkNature == PostWorkNature.应届毕业生)
        {
            if (post.GraduationYear > 0 && resume.GraduationDate?.Year != post.GraduationYear)
                throw new BadRequestException("您的毕业年限不符合要求", "GraduationDateMismatch");
        }

        if (post.Sex.HasValue && resume.Sex != post.Sex)
            throw new BadRequestException("您的性别不符合要求", "SexMismatch");

        if (post.Education != EducationType.不限 && (resume.Education < post.Education || resume.Education is null || resume.Education == EducationType.其他))
            throw new BadRequestException("您的学历不符合要求", "EducationMismatch");

        // var userAge = Tools.GetAgeByBirthdate(resume.Birthday);

        int? userAge = null;
        if (resume.Birthday.HasValue)
            userAge = DateTime.Now.Year - resume.Birthday.Value.Year;

        if (post.MinAge > 0 && (userAge == null || userAge < post.MinAge))
            throw new BadRequestException("您的年龄不符合要求", "AgeMismatch");
        if (post.MaxAge > 0 && (userAge == null || userAge > post.MaxAge))
            throw new BadRequestException("您的年龄不符合要求", "AgeMismatch");

        //人才库信息
        var talent = context.Talent_Platform.IgnoreQueryFilters()
        .Where(x => x.HrId == post.TeamHrId && x.SeekerId == userId)
        .FirstOrDefault();

        var channelId = talent == null ? model.ChannelId : talent?.ChannelId;

        if (!string.IsNullOrEmpty(channelId))
        {
            //检测渠道Id合法性
            if (!context.Qd_Hr_Channel.Any(x => x.Id == channelId && x.HrId == post.TeamHrId))
                channelId = null;
        }

        //投递表
        var postDelivery = new Post_Delivery
        {
            PostId = post.PostId,
            TeamPostId = model.TeamPostId,
            SeekerId = userId,
            Status = 0,
            Source = 0,
            // Money = post.Money,
            // PaymentNode = post.PaymentNode,
            // PaymentDays = post.PaymentDays,
            ChannelId = channelId,
            ChannelSource = model.ChannelSource
        };
        context.Add(postDelivery);
        //开启招聘流程
        //判断是否开启众包
        var projectTeamConfig = context.Project_Team_Config.FirstOrDefault(r => r.Creator == post.TeamHrId);
        var isClues = ProjectTeamIsClues.否;
        if (projectTeamConfig != null && projectTeamConfig.Status == 0)
        {
            isClues = ProjectTeamIsClues.是;
        }
        var recruit = new Recruit
        {
            DeliveryId = postDelivery.DeliveryId,
            HrId = post.HrId!,
            SeekerId = userId,
            Status = RecruitStatus.HrScreening,
            PostName = post.Name,
            Type = RecruitType.Driving,
            UserClient = model.UserClient,
            IsClues = isClues,
            ModeType = projectTeamConfig?.ModeType,
            ReceiverId = projectTeamConfig?.ReceiverId,

        };
        context.Add(recruit);
        //产品要求修改为协同hrid
        var recruitRecord = new Recruit_Record
        {
            RecruitId = recruit.RecruitId,
            Status = RecruitStatus.HrScreening,
            Creator = post.TeamHrId
        };
        context.Add(recruitRecord);

        // 产品：此处逻辑不对，去掉
        // // todo:如果是“简历免审”，则面试官筛选 反馈通过
        // // 20241031 如果是“简历免审”，则面试官筛选 反馈通过，且流程进入面试官筛选(企业筛选)
        // if (post.IsResumeExempt)
        // {
        //     recruit.Status = RecruitStatus.InterviewerScreening;

        //     var hrPhone = context.User.FirstOrDefault(f => f.UserId == post.HrId)?.Mobile;
        //     var interviewerInfo = context.Project_Interviewer.FirstOrDefault(f => f.Phone == hrPhone! && f.ProjectId == post.ProjectId);
        //     if (interviewerInfo == null)
        //     {
        //         // 打印错误日志
        //         _log.Error("简历投递错误", "未找到主创面试官", $"主创电话：{hrPhone}");
        //     }
        //     var interviewerId = interviewerInfo?.Id ?? $"{EntityTools.SnowflakeId()}-1";
        //     var nowTime = DateTime.Now;

        //     var ris = new Recruit_Interviewer_Screen
        //     {
        //         RecruitId = recruit.RecruitId,
        //         SeekerId = userId,
        //         InterviewerId = interviewerId,
        //         Recommend = "简历免审，自动通过",
        //         Status = RecruitInterviewerScreenStatus.Adopt,
        //         TodoTime = nowTime,
        //         CreatedTime = nowTime
        //     };
        //     context.Add(ris);

        //     var rr = new Recruit_Record
        //     {
        //         RecruitId = recruit.RecruitId,
        //         Status = RecruitStatus.InterviewerScreening,
        //         Creator = "admin",
        //         InterviewerScreenId = ris.Id,
        //         CreatedTime = nowTime
        //     };
        //     context.Add(rr);
        // }

        //如果是入职过保协同，进入分润
        //if (post.TeamProjectType == HrProjectType.协同 && post.PaymentNode == ProjectPaymentNode.入职过保)
        //{
        //    var teamBounty = new Project_Teambounty
        //    {
        //        TeamProjectId = post.TeamProjectId,
        //        PostId = post.PostId,
        //        TeamPostId = model.TeamPostId,
        //        SeekerId = userId,
        //        Status = BountyStatus.交付中,
        //        Source = 0,
        //        Money = post.Money,
        //        PaymentNode = post.PaymentNode,
        //        PaymentDays = post.PaymentDays,
        //        HrId = post.HrId,
        //        ProjectId = post.ProjectId!,
        //        TeamHrId = post.TeamHrId,
        //        PaymentType = post.PaymentType,
        //        RecruitId = recruit.RecruitId,
        //        SettlementMoney = post.Money
        //    };
        //    context.Add(teamBounty);
        //}

        // // 协同或者主创渠道进入分润，暂时不支持主创渠道
        // if (post.TeamProjectType == HrProjectType.协同)
        // {
        //     CreateOrder(model, channelId, recruit.RecruitId);
        // }

        //更新人才库用户等级
        if (talent != null)
        {
            if (talent.Level < TalentPlatformLevel.投递用户)
                talent.Level = TalentPlatformLevel.投递用户;
        }

        ///TODO:自动归档同项目的投递
        // var otherRecruits = context.Recruit.Where(x => x.SeekerId == userId
        // && x.Post_Delivery.Post.ProjectId == post.ProjectId && x.Status == RecruitStatus.HrScreening);

        // 创建订单
        _profitService.CreateOrder(context, new CreateOrderModel
        {
            ChannelId = channelId,
            Recruit = recruit,
            TeamPostId = model.TeamPostId
        });

        context.SaveChanges();

        //人才库是否存在，不存在则补进人才库

        if (talent == null)
            _ = Task.Run(() => SetAdviser(userId, post.TeamHrId, TalentPlatformLevel.投递用户, channelId));

        result.DeliveryId = postDelivery.DeliveryId;

        //通知消息
        // MsgHelper.SendMsgNotify(new MsgNotifyModel
        // {
        //     Type = MsgNotifyType.职位报名,
        //     EventTime = postDelivery.CreatedTime,
        //     // TeamHrId = post.TeamHrId,
        //     UserId = post.HrId,
        //     Data = new MsgNotifyRecruit
        //     {
        //         IsSelfProj = true,
        //         TeamPostId = model.TeamPostId,
        //         TeamProjectId = post.TeamProjectId,
        //         PostName = post.Name,
        //         SeekerName = seeker.NickName,
        //         AgentEntName = post.AgentEntName,
        //         ProjectName = post.ProjectName,
        //         RecruitId = recruit.RecruitId
        //     }
        // });
        // 因为招聘流程改版，修改消息通知规则只通知到当前操作人，不再通知主创或其他协同人员
        MsgHelper.SendMsgNotify(new MsgNotifyModel
        {
            Type = MsgNotifyType.职位报名,
            EventTime = postDelivery.CreatedTime,
            // TeamHrId = post.TeamHrId,
            UserId = post.TeamHrId,
            Data = new MsgNotifyRecruit
            {
                IsSelfProj = false,
                TeamPostId = model.TeamPostId,
                TeamProjectId = post.TeamProjectId,
                PostName = post.Name,
                SeekerName = seeker.NickName,
                AgentEntName = post.AgentEntName,
                ProjectName = post.ProjectName,
                RecruitId = recruit.RecruitId
            }
        });

        // //通知协同消息
        // if (post.TeamProjectType == HrProjectType.协同)
        // {
        //     MsgHelper.SendMsgNotify(new MsgNotifyModel
        //     {
        //         Type = MsgNotifyType.职位报名,
        //         EventTime = postDelivery.CreatedTime,
        //         // TeamHrId = post.TeamHrId,
        //         UserId = post.TeamHrId,
        //         Data = new MsgNotifyRecruit
        //         {
        //             IsSelfProj = false,
        //             TeamPostId = model.TeamPostId,
        //             TeamProjectId = post.TeamProjectId,
        //             PostName = post.Name,
        //             SeekerName = seeker.NickName,
        //             AgentEntName = post.AgentEntName,
        //             ProjectName = post.ProjectName,
        //             RecruitId = recruit.RecruitId
        //         }
        //     });
        // }

        return result;
    }

    /// <summary>
    /// 注册时更新人才融合库的注册状态
    /// </summary>
    /// <param name="msg"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task SeekerRegisterToTalentResume(string msg)
    {
        await Task.Run(() =>
        {
            using var _context = _staffingContextFactory.CreateDbContext();
            var mobile = _context.User.FirstOrDefault(f => f.UserId == msg)?.Mobile;
            var tr = _context.Talent_Resume.Where(f => f.Mobile == mobile).ToList();
            if (tr == null || tr.Count == 0)
                return;

            foreach (var item in tr)
            {
                item.SeekerId = msg;
            }

            _context.SaveChanges();
        });
    }
}

public class CreateSeekerModel
{
    /// <summary>
    /// 手机号
    /// </summary>
    public string Mobile { get; set; } = default!;

    public RegisterSource? Source { get; set; }

    public string? AdviserId { get; set; }
    public string? ChannelId { get; set; }
    public long? ChannelSource { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 教育
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 毕业日期
    /// </summary>
    public DateOnly? GraduationDate { get; set; }

    public string? RegionId { get; set; }
    public string? Address { get; set; }
    public Point Location { get; set; } = new Point(0, 0);
}

public class CreateSeekerResponse
{
    public string? UserId { get; set; }
    public bool NewSeeker { get; set; }
}

public class CommonDeliverResume
{
    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 渠道Id
    /// </summary>
    public string? ChannelId { get; set; }

    /// <summary>
    /// 渠道来源
    /// </summary>
    public long? ChannelSource { get; set; }

    /// <summary>
    /// 投递客户端
    /// </summary>
    public ClientType? UserClient { get; set; }
}

public class CommonDeliverResumeResponse
{
    /// <summary>
    /// 投递Id
    /// </summary>
    public string? DeliveryId { get; set; }
}