# 需求文档

## 介绍

本功能旨在为 ProjectRouter 类添加一个新的接口，用于根据项目ID查询 project_contract 表中是否存在默认的关联合同。这个接口将帮助前端或其他服务快速判断某个项目是否已经配置了默认合同，从而进行相应的业务逻辑处理。

## 需求

### 需求 1

**用户故事：** 作为一个系统开发者，我希望能够通过项目ID快速查询该项目是否有默认合同，以便在业务流程中做出相应的判断和处理。

#### 验收标准

1. WHEN 调用接口并传入有效的项目ID THEN 系统 SHALL 返回该项目是否存在默认合同的布尔值结果
2. WHEN 调用接口并传入的项目ID为空或null THEN 系统 SHALL 返回 BadRequestException 异常，提示"项目ID不能为空"
3. WHEN 调用接口并传入不存在的项目ID THEN 系统 SHALL 返回 false，表示该项目没有默认合同
4. WHEN 项目存在多个合同但只有一个设置为默认合同(IsDefault=1) THEN 系统 SHALL 返回 true
5. WHEN 项目存在合同但没有设置默认合同(所有合同的IsDefault=0) THEN 系统 SHALL 返回 false
6. WHEN 项目不存在任何合同 THEN 系统 SHALL 返回 false

### 需求 2

**用户故事：** 作为一个API使用者，我希望接口能够提供清晰的OpenAPI文档说明，以便我能够正确理解和使用这个接口。

#### 验收标准

1. WHEN 查看API文档 THEN 接口 SHALL 包含清晰的摘要描述"检查项目是否有默认合同"
2. WHEN 查看API文档 THEN 接口 SHALL 正确标记参数类型和是否必填
3. WHEN 查看API文档 THEN 接口 SHALL 包含正确的返回值类型说明

### 需求 3

**用户故事：** 作为一个系统维护者，我希望接口能够正确处理各种异常情况，确保系统的稳定性和可靠性。

#### 验收标准

1. WHEN 数据库连接异常 THEN 系统 SHALL 抛出相应的数据库异常
2. WHEN 传入的项目ID格式不正确 THEN 系统 SHALL 返回 BadRequestException 异常
3. WHEN 系统内部发生未预期错误 THEN 系统 SHALL 记录错误日志并返回适当的错误响应