using System.ComponentModel.DataAnnotations;
using Config;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Staffing.Model.Service;
using Staffing.Core.Interfaces;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Model.Common.System;


namespace Staffing.Api.Apis.Common;

[Service(ServiceLifetime.Transient)]
public class ServiceRouter : ICommonRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("service").WithTags("公共服务");
        group.MapPost("/sms/code", async (SMSCode model, ISharedService rep) => await rep.SMSCode(model))
            .WithOpenApi(operation => new(operation) { Summary = "发送短信验证码" });

        group.MapPost("/sms/code/dy", async (SMSCode model, ISharedService rep) => await rep.SMSCodeForDy(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "发送短信验证码（临时，抖音审核用）" });

        group.MapPost("/sms/code/nuoapi", async (SMSCode model, ISharedService rep) =>
        {
            try
            {
                var result = await rep.SMSCode(model);
                return new Infrastructure.Proxy.NuoApi<SMSCodeResponse>() { ErrCode = 200, Data = result };
            }
            catch (Exception ee)
            {
                return new Infrastructure.Proxy.NuoApi<SMSCodeResponse>() { ErrCode = 201, ErrMsg = ee.Message };
            }
        })
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "发送短信验证码（兼容诺聘API）" });

        group.MapPost("/aliyun/oss/token", (GetAliyunOssToken model, ISharedService rep) => rep.GetAliyunOssToken(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取OSS签名" });

        group.MapGet("/auth", (ISharedService rep, RequestContext user) =>
        {
            if (!string.IsNullOrEmpty(user.AccessToken))
            {
                var tkmd5 = Md5Helper.Md5(user.AccessToken);
                var usrCacheKey = $"ut:{tkmd5}";
                return MyRedis.Client.Get<ShortUserInfo?>(usrCacheKey) ?? new ShortUserInfo();
            }
            else
                return new ShortUserInfo();
        }).WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "获取鉴权信息" });

        group.MapGet("/dic/welfare", ([AsParameters] GetWelfare model, ISharedService rep) => rep.GetWelfare(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "福利" });

        group.MapGet("/dic/school", ([AsParameters] GetSchool model, ISharedService rep) => rep.GetSchool(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "学校" });

        group.MapGet("/dic/major", ([AsParameters] GetMajor model, ISharedService rep) => rep.GetMajor(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "专业" });

        group.MapGet("/dic/industry", ([AsParameters] GetIndustry model, ISharedService rep) => rep.GetIndustry(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "行业" });

        group.MapGet("/dic/post", ([AsParameters] GetPostCategory model, ISharedService rep) => rep.GetPostCategory(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "职位" });

        group.MapGet("/dic/cert", ([AsParameters] GetCert model, ISharedService rep) => rep.GetCert(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "证书" });

        group.MapGet("/dic/city", ([AsParameters] GetCity model, ISharedService rep) => rep.GetCity(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "城市" });

        group.MapGet("/dic/city/tree", ([AsParameters] GetCityTree model, ISharedService rep) => rep.GetCityTree(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "城市树" });

        group.MapGet("/platform", (CommonCacheService commonCacheResponse) =>
        {
            var platformCount = commonCacheResponse.GetPlatformCount();
            return new GetPlatformInfo
            {
                Adviser = platformCount.Adviser,
                Talent = platformCount.Talent,
                VirtualTalent = platformCount.VirtualTalent
            };
        })
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "平台信息" });

        group.MapGet("/applet/scene", ([AsParameters] GetAppletScene model, ISharedService rep) => rep.GetAppletScene(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "小程序分享码解析" });

        group.MapPost("/post/push", (CommonPostService commonPostService) => commonPostService.PostSync(new List<string> { }))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "职位推送" });

        group.MapGet("/warmapi", (ISharedService rep) => rep.WarmApi())
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "api预热" });


        group.MapPost("/dic/save", (DictDataSaveReq request, StaffingContext context, LogManager logger) =>
            {
                // 数据验证
                var validationResults = new List<ValidationResult>();
                var validationContext = new ValidationContext(request);
                bool isValid = Validator.TryValidateObject(request, validationContext, validationResults, true);
                if (!isValid)
                {
                    throw new BadRequestException(validationResults.FirstOrDefault()?.ErrorMessage);
                }
                // 检查DictLabel是否重复（相同DictType下）
                bool labelExists = context.DictData
                    .Any(x => x.DictType == request.DictType && x.DictLabel == request.DictLabel);

                if (labelExists)
                {
                    throw new BadRequestException($"字典标签'{request.DictLabel}'已存在");
                }
                // 保存数据
                context.Add(new DictData
                {
                    DictType = request.DictType,
                    DictLabel = request.DictLabel,
                    DictValue = request.DictValue,
                    DictSort = request.DictSort ?? 0
                });
                context.SaveChanges();
                return new EmptyResponse();
            })
            .WithOpenApi(operation => new(operation) { Summary = "保存字典" })
            .WithMetadata(new ApiMeta());

        group.MapPost("/dic/list", (DictDataListReq request, StaffingContext context, LogManager logger) =>
            {
                var result = new List<DictDataListResp>();
                if (!string.IsNullOrWhiteSpace(request.DictType))
                {
                    result = context.DictData.Where(s => s.DictType == request.DictType && s.Status == "0")
                        .OrderBy(s => s.DictSort)
                        .Select(s => new DictDataListResp
                        {
                            DictType = s.DictType,
                            DictLabel = s.DictLabel,
                            DictValue = s.DictValue,
                            DictSort = s.DictSort,
                        }).ToList();
                }
                return result;
            }).WithOpenApi(operation => new(operation) { Summary = "通用字段列表" })
            .WithMetadata(new ApiMeta());

        // get接口，重定向到baidu.com
        group.MapGet("/redirect", () =>
        {
            return Results.Redirect("https://www.baidu.com");
        }).WithOpenApi(operation => new(operation) { Summary = "重定向" })
        .WithMetadata(new ApiMeta());
    }
}