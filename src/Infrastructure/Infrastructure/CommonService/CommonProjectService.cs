using Config;
using Config.CommonModel;
using Config.Enums;
using DocumentFormat.OpenXml.Spreadsheet;
using FreeRedis;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Infrastructure.CommonService;

[Service(ServiceLifetime.Transient)]
public class CommonProjectService
{
    private IDbContextFactory<StaffingContext> _staffingContextFactory;
    private ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;

    public CommonProjectService(IDbContextFactory<StaffingContext> staffingContextFactory, IOptionsSnapshot<ConfigManager> config,
        IHostEnvironment hostingEnvironment, LogManager log)//, RequestContext user)
    {
        _staffingContextFactory = staffingContextFactory;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
    }

    /// <summary>
    /// 更新协同职位显示状态
    /// </summary>
    /// <param name="id"></param>
    /// <param name="type"></param>
    public void UpdatePostShow(string id, UpdatePostShowType type)
    {
        try
        {
            using var _context = _staffingContextFactory.CreateDbContext();

            if (type == UpdatePostShowType.协同职位)
            {
                _context.Database.ExecuteSqlInterpolated(@$"UPDATE post_team ptm
                JOIN post p ON ptm.PostId = p.PostId
                JOIN project_team projtm ON ptm.TeamProjectId = projtm.TeamProjectId
                JOIN project proj on proj.ProjectId = p.ProjectId
                SET ptm.`Show` = (ptm.`Status` = 1 AND p.`Status` = 1 AND proj.`Status` = 1)
                WHERE ptm.TeamPostId = {id}");
            }
            else if (type == UpdatePostShowType.原始职位)
            {
                _context.Database.ExecuteSqlInterpolated(@$"UPDATE post_team ptm
                JOIN post p ON ptm.PostId = p.PostId
                JOIN project_team projtm ON ptm.TeamProjectId = projtm.TeamProjectId
                JOIN project proj on proj.ProjectId = p.ProjectId
                SET ptm.`Show` = (ptm.`Status` = 1 AND p.`Status` = 1 AND proj.`Status` = 1)
                WHERE p.PostId = {id}");

                // //更新项目是否有协同的职位
                // _context.Database.ExecuteSqlInterpolated(@$"UPDATE project proj
                // JOIN post p ON proj.ProjectId = p.ProjectId
                // SET proj.SharePost = EXISTS(SELECT 1 FROM post WHERE ProjectId = proj.ProjectId AND `Share` AND `Status` = 1 LIMIT 1)
                // WHERE p.PostId = {id}");

                // //更新所有协同的hr数据
                // _context.Database.ExecuteSqlInterpolated(@$"UPDATE user_hr_data hrdata
                // JOIN project_team teamproj ON hrdata.UserId = teamproj.HrId
                // JOIN post_team teampt ON teamproj.TeamProjectId = teampt.TeamProjectId
                // SET MyPostOnline = (SELECT COUNT(1) FROM post_team x JOIN project_team y ON x.TeamProjectId = y.TeamProjectId WHERE y.HrId = hrdata.UserId AND y.`Type` = 0 AND x.`Show` = 1),
                // TeamPostOnline = (SELECT COUNT(1) FROM post_team x JOIN project_team y ON x.TeamProjectId = y.TeamProjectId WHERE y.HrId = hrdata.UserId AND y.`Type` = 1 AND x.`Show` = 1),
                // MyRecruitOnline = (SELECT IFNULL(SUM(z.RecruitNumber),0) FROM post_team x JOIN project_team y ON x.TeamProjectId = y.TeamProjectId JOIN post z ON x.PostId = z.PostId WHERE y.HrId = hrdata.UserId AND y.`Type` = 0 AND x.`Show` = 1),
                // TeamRecruitOnline = (SELECT IFNULL(SUM(z.RecruitNumber),0) FROM post_team x JOIN project_team y ON x.TeamProjectId = y.TeamProjectId JOIN post z ON x.PostId = z.PostId WHERE y.HrId = hrdata.UserId AND y.`Type` = 1 AND x.`Show` = 1)
                // WHERE teampt.PostId = {id}");
            }
            else if (type == UpdatePostShowType.项目)
            {
                _context.Database.ExecuteSqlInterpolated(@$"UPDATE post_team ptm
                JOIN post p ON ptm.PostId = p.PostId
                JOIN project_team projtm ON ptm.TeamProjectId = projtm.TeamProjectId
                JOIN project proj on proj.ProjectId = p.ProjectId
                SET ptm.`Show` = (ptm.`Status` = 1 AND p.`Status` = 1 AND proj.`Status` = 1)
                WHERE proj.ProjectId = {id}");

                //更新项目是否有协同的职位
                _context.Database.ExecuteSqlInterpolated(@$"UPDATE project proj
                SET proj.SharePost = EXISTS(SELECT 1 FROM post WHERE ProjectId = proj.ProjectId AND `Status` = 1 LIMIT 1)
                WHERE proj.ProjectId = {id}");

                // //更新所有协同的hr数据
                // _context.Database.ExecuteSqlInterpolated(@$"UPDATE user_hr_data hrdata
                // JOIN project_team teamproj ON hrdata.UserId = teamproj.HrId
                // SET MyProjectTotal = (SELECT COUNT(1) FROM project WHERE HrId = hrdata.UserId),
                // TeamProjectTotal = (SELECT COUNT(1) FROM Project_team WHERE HrId = hrdata.UserId AND `Type` = 1),
                // MyProjectOnline = (SELECT COUNT(1) FROM project WHERE HrId = hrdata.UserId AND `Status` = 1),
                // TeamProjectOnline = (SELECT COUNT(1) FROM Project_team x JOIN project y ON x.ProjectId = y.ProjectId WHERE x.HrId = hrdata.UserId AND x.`Type` = 1 AND y.`Status` = 1),
                // MyPostTotal = (SELECT COUNT(1) FROM post x JOIN project y ON x.ProjectId = y.ProjectId WHERE y.HrId = hrdata.UserId),
                // TeamPostTotal = (SELECT COUNT(1) FROM post_team x JOIN project_team y ON x.TeamProjectId = y.TeamProjectId WHERE y.HrId = hrdata.UserId AND y.`Type` = 1),
                // MyPostOnline = (SELECT COUNT(1) FROM post_team x JOIN project_team y ON x.TeamProjectId = y.TeamProjectId WHERE y.HrId = hrdata.UserId AND y.`Type` = 0 AND x.`Show` = 1),
                // TeamPostOnline = (SELECT COUNT(1) FROM post_team x JOIN project_team y ON x.TeamProjectId = y.TeamProjectId WHERE y.HrId = hrdata.UserId AND y.`Type` = 1 AND x.`Show` = 1),
                // MyRecruitOnline = (SELECT IFNULL(SUM(z.RecruitNumber),0) FROM post_team x JOIN project_team y ON x.TeamProjectId = y.TeamProjectId JOIN post z ON x.PostId = z.PostId WHERE y.HrId = hrdata.UserId AND y.`Type` = 0 AND x.`Show` = 1),
                // TeamRecruitOnline = (SELECT IFNULL(SUM(z.RecruitNumber),0) FROM post_team x JOIN project_team y ON x.TeamProjectId = y.TeamProjectId JOIN post z ON x.PostId = z.PostId WHERE y.HrId = hrdata.UserId AND y.`Type` = 1 AND x.`Show` = 1)
                // WHERE teamproj.ProjectId = {id}");
            }
        }
        catch (Exception e)
        {
            _log.Error("更新职位显示状态出错", Tools.GetErrMsg(e), $"{id}_{type}");
        }
    }
    public Task<EmptyResponse> PostSync(string userId, string projectId, string postId)
    {
        var lockerKey = $"st:postsync:{userId}";
        using var locker = MyRedis.TryLock(lockerKey);
        if (locker == null)
            throw new BadRequestException("操作频繁");
        using var _context = _staffingContextFactory.CreateDbContext();
        // 判断是否是自己协同过的项目
        // var shareProject = _context.Project
        //     .Where(x => x.ProjectId == projectId)
        //     .Select(p => new
        //     {
        //         p.HrId,
        //         p.Project_Team,
        //         Posts = p.Posts.Select(post => new { post.PostId }).ToList()
        //     }).FirstOrDefault();

        var teamProject = _context.Project_Team.Where(x => x.HrId == userId && x.ProjectId == projectId)
        .FirstOrDefault();

        // if (shareProject == null)
        //     throw new NotFoundException("内容不存在");
        // var post = shareProject.Posts.FirstOrDefault(f => f.PostId == postId);
        var post = _context.Post.Where(x => x.PostId == postId).Select(s => new { s.PostId }).FirstOrDefault();
        if (post == null)
            throw new NotFoundException("内容不存在");
        //if (shareProject.HrId == userId)
        //    throw new BadRequestException("无法接单自己发布的职位");

        // string projectTeamId = string.Empty;
        // var projectTeam = new Project_Team();
        // projectTeam = shareProject.Project_Team.FirstOrDefault(f => f.HrId == userId);

        // 项目创建人信息
        //var projectHrInfo = _context.User_Hr
        //    .Include(i => i.Enterprise)
        //    .Include(i => i.Enterprise_Org_User)
        //    .Where(w => w.UserId == shareProject.HrId).FirstOrDefault();
        // 没有协同过该项目
        if (teamProject == null)
        {
            teamProject = new Project_Team
            {
                Type = HrProjectType.协同,
                HrId = userId,
                ProjectId = projectId,
                Status = ProjectStatus.已上线,
                UpdatedBy = userId
            };
            // projectTeamId = hrProject.TeamProjectId;
            _context.Add(teamProject);
        }
        // else
        //     projectTeamId = projectTeam.TeamProjectId;

        // 职位协同（上线）
        var postTeam = _context
            .Post_Team.FirstOrDefault(w => w.PostId == postId && w.Project_Team.HrId == userId);
        // 如果是已协同职位
        if (postTeam != null)
            throw new BadRequestException("无法重复接单相同职位");

        //添加到协同
        postTeam = new Post_Team
        {
            PostId = postId,
            UpdatedBy = userId,
            Status = PostStatus.发布中,
            TeamProjectId = teamProject.TeamProjectId
        };
        var teamPostId = postTeam.TeamPostId;
        _context.Add(postTeam);
        _context.Add(new Post_Team_Extend { TeamPostId = teamPostId });
        _context.SaveChanges();// 放到更新状态之前执行 todo:会存在事务不一致的情况发生，

        // 职位状态变更处理
        UpdatePostShow(teamPostId, UpdatePostShowType.协同职位);// todo:这里边catch住了。。

        var result = new EmptyResponse();
        return Task.FromResult(result);
    }
    public async Task<string> ProjectSync(string projectId, string userId)
    {
        var lockerKey = $"st:prosync:{userId}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        using var _context = _staffingContextFactory.CreateDbContext();

        // var hallProject = _context.Project_Hall.Where(x => x.Id == hallProjectId)
        // .Select(s => new
        // {
        //     s.Project.HrId,
        //     s.Project.ProjectId
        // }).FirstOrDefault();
        // if (hallProject == null)
        //     throw new NotFoundException("内容不存在");
        //
        // if (hallProject.HrId == userId)
        //     throw new BadRequestException("无法协同自己的项目");

        var hr = _context.User_Hr
            .Where(x => x.UserId == userId)
            .Select(s => new
            {
                s.EntId,
                s.Enterprise.GroupEntId,
                s.Enterprise.GroupType
            }).FirstOrDefault();

        if (hr == null)
            throw new BadRequestException("无权限");

        if (_context.Project_Team.Any(x => x.HrId == userId && x.ProjectId == projectId))
            throw new BadRequestException("您已经协同过该项目了");

        var hrProject = new Project_Team
        {
            Type = HrProjectType.协同,
            HrId = userId,
            ProjectId = projectId,
            Status = ProjectStatus.待审核,
            UpdatedBy = userId
        };

        _context.Add(hrProject);

        _context.SaveChanges();

        await Task.FromResult(1);

        await SetProjectPostStatus(projectId, true, userId);

        return hrProject.TeamProjectId;
    }

    public async Task<EmptyResponse> SetProjectPostStatus(string projectId, bool on, string userId)
    {
        using var _context = _staffingContextFactory.CreateDbContext();

        var posts = _context.Post.Where(x => x.ProjectId == projectId)
        .Select(s => new
        {
            s.PostId
        }).ToList();

        foreach (var item in posts)
        {
            try { SetPostStatus(item.PostId, on ? PostStatus.发布中 : PostStatus.关闭, userId); }
            catch (BadRequestException) { }
            await Task.Delay(20);
        }

        return new EmptyResponse();
    }

    public EmptyResponse SetPostStatus(string? postId, PostStatus? status, string userId)
    {
        if (status != PostStatus.发布中 && status != PostStatus.关闭)
            throw new BadRequestException("状态无效");

        var lockerKey = $"st:uppost:{userId}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        using var _context = _staffingContextFactory.CreateDbContext();

        var tp = _context.Post
        .Where(x => x.PostId == postId)
        .Select(s => new
        {
            s.PostId,
            s.Status,
            s.Project.HrId,
            s.ProjectId,
            s.Project.SaleUserId,
            CategoryName = s.Dic_Post.Name,
            ProjectStatus = s.Project.Status,
        }).FirstOrDefault();

        if (tp == null)
            throw new NotFoundException("内容不存在");

        var isReceiver = tp.HrId == userId;

        var teamPostId = string.Empty;

        var predicate = PredicateBuilder.New<Post_Team>(x => x.PostId == postId);
        predicate = predicate.And(x => x.Project_Team.HrId == userId);
        var teamPost = _context.Post_Team.Where(predicate).FirstOrDefault();

        //如果没有协同过，同步职位
        if (teamPost == null)
        {
            //查询我的协同项目Id（必定有）
            var teamPj = _context.Project_Team.Where(x => x.ProjectId == tp.ProjectId && x.HrId == userId)
            .Select(s => new
            {
                s.TeamProjectId
            }).FirstOrDefault();

            if (teamPj == null)
                throw new NotFoundException("协同项目内容不存在");

            //添加到协同
            teamPost = new Post_Team
            {
                PostId = tp.PostId,
                UpdatedBy = userId,
                Status = PostStatus.发布中,
                TeamProjectId = teamPj.TeamProjectId
            };
            _context.Add(teamPost);
            _context.Add(new Post_Team_Extend { TeamPostId = teamPost.TeamPostId });
        }

        //如果是项目经理
        if (isReceiver)
        {
            if (tp.ProjectStatus != ProjectStatus.已上线)
                throw new BadRequestException("项目未上线");

            //自己项目
            var post = _context.Post.First(x => x.PostId == tp.PostId);

            // 项目经理的teamPost.Status永远是上架状态
            post.Status = status!.Value;

            //记录上次状态（大概不需要这个状态了，这是为以前平台审核用的）
            if (post.Status == PostStatus.关闭 || post.Status == PostStatus.发布中)
                post.LastStatus = post.Status;
        }
        else
        {
            //协同项目
            if (status == PostStatus.发布中 && tp.Status != PostStatus.发布中)
                throw new BadRequestException("该职位已关闭");

            teamPost.Status = status!.Value;

            teamPostId = teamPost.TeamPostId;
        }
        teamPost.HrAgentStatus = HrAgentStatus.TurnOff;
        _context.SaveChanges();

        //如果是自己职位
        if (isReceiver)
        {
            //职位状态变更处理
            UpdatePostShow(tp.PostId, UpdatePostShowType.原始职位);
        }
        else
        {
            //职位状态变更处理
            UpdatePostShow(teamPostId, UpdatePostShowType.协同职位);
        }

        return new EmptyResponse();
    }

    /// <summary>
    /// 更新hr统计-项目
    /// </summary>
    /// <param name="id"></param>
    /// <param name="type"></param>
    public void UpdateHrProjectData(params Sub_UpdateHrData[] model)
    {
        try
        {
            // if (model?.Count() > 0)
            //     MyRedis.Client.SAdd(SubscriptionKey.UpdateHrData, model);

            var ut = DateTime.Now.ToUnixTimeMs();
            if (model?.Count() > 0)
                MyRedis.Client.ZAdd(SubscriptionKey.UpdateHrData, model.Select(s => new ZMember(s.UserId, ut)).ToArray());

            // 全部更新
            // UPDATE user_hr_data hrdata
            // SET MyProjectTotal = (SELECT COUNT(1) FROM project WHERE HrId = hrdata.UserId),
            // TeamProjectTotal = (SELECT COUNT(1) FROM Project_team WHERE HrId = hrdata.UserId AND `Type` = 1),
            // MyProjectOnline = (SELECT COUNT(1) FROM project WHERE HrId = hrdata.UserId AND `Status` = 1),
            // TeamProjectOnline = (SELECT COUNT(1) FROM Project_team x JOIN project y ON x.ProjectId = y.ProjectId WHERE x.HrId = hrdata.UserId AND x.`Type` = 1 AND y.`Status` = 1),
            // MyPostTotal = (SELECT COUNT(1) FROM post x JOIN project y ON x.ProjectId = y.ProjectId WHERE y.HrId = hrdata.UserId AND x.Deleted = 0),
            // TeamPostTotal = (SELECT COUNT(1) FROM post_team x JOIN project_team y ON x.TeamProjectId = y.TeamProjectId WHERE y.HrId = hrdata.UserId AND y.`Type` = 1),
            // MyPostOnline = (SELECT COUNT(1) FROM post_team x JOIN project_team y ON x.TeamProjectId = y.TeamProjectId WHERE y.HrId = hrdata.UserId AND y.`Type` = 0 AND x.`Show` = 1),
            // TeamPostOnline = (SELECT COUNT(1) FROM post_team x JOIN project_team y ON x.TeamProjectId = y.TeamProjectId WHERE y.HrId = hrdata.UserId AND y.`Type` = 1 AND x.`Show` = 1),
            // MyRecruitOnline = (SELECT IFNULL(SUM(z.RecruitNumber),0) FROM post_team x JOIN project_team y ON x.TeamProjectId = y.TeamProjectId JOIN post z ON x.PostId = z.PostId WHERE y.HrId = hrdata.UserId AND y.`Type` = 0 AND x.`Show` = 1),
            // TeamRecruitOnline = (SELECT IFNULL(SUM(z.RecruitNumber),0) FROM post_team x JOIN project_team y ON x.TeamProjectId = y.TeamProjectId JOIN post z ON x.PostId = z.PostId WHERE y.HrId = hrdata.UserId AND y.`Type` = 1 AND x.`Show` = 1),
            // RecruitHrScreening = (SELECT COUNT(1) FROM recruit x JOIN post_delivery y ON x.DeliveryId = y.DeliveryId JOIN post z on y.PostId = z.PostId JOIN project a ON z.ProjectId = a.ProjectId WHERE x.HrId = hrdata.UserId AND x.`Status` = 1 AND a.`Status` = 1),
            // RecruitInterviewerScreening = (SELECT COUNT(1) FROM recruit x JOIN post_delivery y ON x.DeliveryId = y.DeliveryId JOIN post z on y.PostId = z.PostId JOIN project a ON z.ProjectId = a.ProjectId WHERE x.HrId = hrdata.UserId AND x.`Status` = 2 AND a.`Status` = 1),
            // RecruitInterview = (SELECT COUNT(1) FROM recruit x JOIN post_delivery y ON x.DeliveryId = y.DeliveryId JOIN post z on y.PostId = z.PostId JOIN project a ON z.ProjectId = a.ProjectId WHERE x.HrId = hrdata.UserId AND x.`Status` = 3 AND a.`Status` = 1),
            // RecruitOffer = (SELECT COUNT(1) FROM recruit x JOIN post_delivery y ON x.DeliveryId = y.DeliveryId JOIN post z on y.PostId = z.PostId JOIN project a ON z.ProjectId = a.ProjectId WHERE x.HrId = hrdata.UserId AND x.`Status` = 4 AND a.`Status` = 1),
            // RecruitInduction = (SELECT COUNT(1) FROM recruit x JOIN post_delivery y ON x.DeliveryId = y.DeliveryId JOIN post z on y.PostId = z.PostId JOIN project a ON z.ProjectId = a.ProjectId WHERE x.HrId = hrdata.UserId AND x.`Status` = 5 AND a.`Status` = 1),
            // RecruitContract = (SELECT COUNT(1) FROM recruit x JOIN post_delivery y ON x.DeliveryId = y.DeliveryId JOIN post z on y.PostId = z.PostId JOIN project a ON z.ProjectId = a.ProjectId WHERE x.HrId = hrdata.UserId AND x.`Status` = 6 AND a.`Status` = 1),
            // RecruitFileAway = (SELECT COUNT(1) FROM recruit x JOIN post_delivery y ON x.DeliveryId = y.DeliveryId JOIN post z on y.PostId = z.PostId JOIN project a ON z.ProjectId = a.ProjectId WHERE x.HrId = hrdata.UserId AND x.`Status` = 99 AND a.`Status` = 1),
            // RecruitDelivery = (SELECT COUNT(1) FROM recruit WHERE HrId = hrdata.UserId),
            // RecruitTotal = (SELECT IFNULL(SUM(x.RecruitNumber),0) FROM post x JOIN project y ON x.ProjectId = y.ProjectId WHERE y.HrId = hrdata.UserId AND x.Deleted = 0),
            // RecruitInterviewTotal = (SELECT COUNT(DISTINCT(RecruitId)) FROM recruit_interview WHERE HrId = hrdata.UserId),
            // RecruitInductionTotal = (SELECT COUNT(1) FROM recruit WHERE HrId = hrdata.UserId AND `Status` = 5)

        }
        catch (Exception e)
        {
            _log.Error("更新hr统计-项目出错", Tools.GetErrMsg(e), model);
        }
    }

    // /// <summary>
    // /// 用户更新项目成员（暂废弃）
    // /// </summary>
    // /// <param name="id"></param>
    // /// <param name="type"></param>
    // public void UpdateProjectMember(string userId)
    // {
    //     try
    //     {
    //         if (string.IsNullOrWhiteSpace(userId))
    //             return;

    //         using var _context = _staffingContextFactory.CreateDbContext();

    //         var needUpdate = _context.Project_Member.Where(x => x.UserId == userId
    //         && string.IsNullOrEmpty(x.IdentityCard) && x.Status == ProjectMemberStatus.正常)
    //         .Take(100).ToList();

    //         if (needUpdate.Count == 0)
    //             return;

    //         if (needUpdate.Count >= 100)
    //             _log.Error("用户更新项目成员出错", "匹配的人员数量超出100", $"{userId}");

    //         var user = _context.User_Seeker.Where(x => x.UserId == userId)
    //         .Select(s => new
    //         {
    //             s.User.IdentityCard,
    //             s.User.IdentityCardName,
    //             s.User_Resume.Birthday,
    //             s.User_Resume.Sex,
    //             s.User.Mobile
    //         }).First();

    //         if (string.IsNullOrEmpty(user.IdentityCard))
    //             throw new Exception("用户更新项目成员出错:缺少身份证");

    //         foreach (var item in needUpdate)
    //         {
    //             var userExists = _context.Project_Member
    //             .Where(x => x.ProjectId == item.ProjectId && x.Status == ProjectMemberStatus.正常 && x.IdentityCard == user.IdentityCard)
    //             .FirstOrDefault();

    //             if (userExists != null)
    //                 _context.Remove(userExists);

    //             item.IdentityCard = user.IdentityCard;
    //             item.Mobile = user.Mobile;
    //         }

    //         _context.EnsureAutoHistory();
    //         _context.SaveChanges();
    //     }
    //     catch (Exception e)
    //     {
    //         _log.Error("用户更新项目成员出错", Tools.GetErrMsg(e), $"{userId}");
    //     }
    // }
}