﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure.QYWechat.Model
{
    public class Response_DepartmentList: ResponseBase
    {
        public List<DepartmentInfo>? Department { get; set;}
    }
    public class DepartmentInfo
    {
        /// <summary>
        /// 部门id
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        public string? Name { get; set; }
        /// <summary>
        /// 父部门id。根部门为1
        /// </summary>
        public int? ParentId { get; set; }
        /// <summary>
        /// 在父部门中的次序值。order值大的排序靠前
        /// </summary>
        public int Order { get; set; }
    }
}
