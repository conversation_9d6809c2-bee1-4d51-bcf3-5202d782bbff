﻿using Config.CommonModel;
using Staffing.Model.CentreService.Report;

namespace Staffing.Core.Interfaces.CentreService;

public interface IPostService
{
    GetExcellentPostResponse GetExcellentPost(GetExcellentPost model);
    EmptyResponse SetExcellentPost(SetExcellentPost model);

    /// <summary>
    /// 获取超时记录(24小时未面试+72小时未入职)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetOvertimeExcellentPostResponse GetOvertimeExcellentPost(GetOvertimeExcellentPost model);
}