SET @currentId = '208016845865808005' COLLATE UTF8MB4_GENERAL_CI;
SET @toId = '101';

UPDATE `user` SET UserId = @toId WHERE UserId = @currentId;
UPDATE `user_hr` SET UserId = @toId WHERE UserId = @currentId;
UPDATE `user_resume` SET UserId = @toId WHERE UserId = @currentId;
UPDATE `user_seeker` SET UserId = @toId WHERE UserId = @currentId;
UPDATE `user_agent_ent` SET UserId = @toId WHERE UserId = @currentId;
UPDATE `user_hr_data` SET UserId = @toId WHERE UserId = @currentId;
UPDATE `user_extend` SET UserId = @toId WHERE UserId = @currentId;
UPDATE `user_num` SET UserId = @toId WHERE UserId = @currentId;
UPDATE `user_balance` SET UserId = @toId WHERE UserId = @currentId;
UPDATE `user_openid` SET UserId = @toId WHERE UserId = @currentId;
UPDATE `user_quick_job_audit` SET UserId = @toId WHERE UserId = @currentId;
UPDATE `project` SET AgentHrId = @toId WHERE AgentHrId = @currentId;
UPDATE `project_team` SET AgentHrId = @toId WHERE AgentHrId = @currentId;
UPDATE `talent_platform` SET HrId = @toId WHERE HrId = @currentId;
UPDATE `talent_virtual` SET HrId = @toId WHERE HrId = @currentId;