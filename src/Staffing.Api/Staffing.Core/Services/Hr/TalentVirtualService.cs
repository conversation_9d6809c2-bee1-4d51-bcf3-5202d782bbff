﻿using Config;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Model.Hr.TalentVirtual;
using Staffing.Core.Interfaces.Hr;
using ServiceStack.Text;
using Config.Enums;
using Infrastructure.Exceptions;
using LinqKit;
using Staffing.Entity.Staffing;
using Infrastructure.CommonService;
using Infrastructure.NoahCommon;
using Infrastructure.Proxy;
using Config.CommonModel.JavaDataApi;
using Staffing.Entity.Elasticsearch;
using Nest;
using Config.CommonModel.Business;

namespace Staffing.Core.Services.Service.Hr;

/// <summary>
/// 虚拟人才库服务
/// </summary>
[Service(ServiceLifetime.Transient)]
public class TalentVirtualService : ITalentVirtualService
{
    private readonly StaffingContext _context;
    private readonly LogManager _log;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private readonly ParseResume _parseResume;
    private readonly CommonVirtualService _commonVirtualService;
    private readonly CommonDicService _commonDicService;
    private readonly JavaDataApi _javaDataApi;
    private readonly EsHelper _esHelper;

    /// <summary>
    /// 注入,
    /// </summary>
    public TalentVirtualService(StaffingContext context, LogManager log, RequestContext user,
    IOptionsSnapshot<ConfigManager> config, CacheHelper cacheHelper, ParseResume parseResume,
    CommonVirtualService commonVirtualService, CommonDicService commonDicService,
    JavaDataApi javaDataApi, EsHelper esHelper)
    {
        _context = context;
        _log = log;
        _user = user;
        _config = config.Value;
        _cacheHelper = cacheHelper;
        _parseResume = parseResume;
        _commonVirtualService = commonVirtualService;
        _commonDicService = commonDicService;
        _javaDataApi = javaDataApi;
        _esHelper = esHelper;
    }

    /// <summary>
    /// 获取虚拟人才库列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public TalentVirtualResumeListResponse GetTalentVirtualResumeList(TalentVirtualResumeListRequest model)
    {
        var client = _esHelper.GetClient();
        var mustQuerys = new List<Func<QueryContainerDescriptor<EsTalentVirtual>, QueryContainer>>();

        //"149761703500854021"
        mustQuerys.Add(q => q.Term(t => t.Field(f => f.HrId).Value(_user.Id)));

        if (!string.IsNullOrEmpty(model.Search))
        {
            // model.Search = "*" + model.Search + "*";
            mustQuerys.Add(q => q.Wildcard(t => t.Field(f => f.TalentSearch).Value($"*{model.Search}*")));
        }
        // if (!string.IsNullOrEmpty(model.Search))
        // {
        //     model.Search = "*" + model.Search + "*";
        //     mustQuerys.Add(q => q.QueryString(qs => qs.Query(model.Search)
        //                  .Fields(fs => fs.Field(f => f.Name)
        //                      .Field(f => f.Mobile)
        //                      .Field(f => f.Mailbox)))
        //          || q.Nested(n => n.Path(p => p.Works).Query(qt => qt.QueryString(t => t.Query(model.Search).Fields(fs => fs.Field(f => f.Works.First().CompanyName)))))
        //          || q.Nested(n => n.Path(p => p.Edus).Query(qt => qt.QueryString(t => t.Query(model.Search).Fields(fs => fs.Field(f => f.Edus.First().SchoolName))))));
        // }
        if (model.Status != null)
            mustQuerys.Add(q => q.Term(t => t.Field(f => f.Status).Value(model.Status)));
        if (!string.IsNullOrWhiteSpace(model.HopePost))
            mustQuerys.Add(q => q.Nested(n => n.Path(p => p.Hopes).Query(qt => qt.QueryString(t => t.Query("*" + model.HopePost + "*").Fields(fs => fs.Field(f => f.Hopes.First().PostName))))));
        if (!string.IsNullOrWhiteSpace(model.HopeIndustry))
            mustQuerys.Add(q => q.Nested(n => n.Path(p => p.Hopes).Query(qt => qt.QueryString(t => t.Query("*" + model.HopeIndustry + "*").Fields(fs => fs.Field(f => f.Hopes.First().IndustryName))))));
        if (model.Education != null)
            mustQuerys.Add(q => q.Term(t => t.Field(f => f.Education).Value(model.Education)));
        if (model.Channel != null)
            mustQuerys.Add(q => q.Term(t => t.Field(f => f.Channel).Value(model.Channel)));

        var regionQuerys = new List<Func<QueryContainerDescriptor<EsTalentVirtual>, QueryContainer>>();
        if (model.RegionIdArray != null && model.RegionIdArray.Count > 0)
        {
            model.RegionIdArray = model.RegionIdArray.Take(10).ToList();
            foreach (var item in model.RegionIdArray)
                regionQuerys.Add(q => q.Prefix(t => t.Field(f => f.RegionId).Value(item)));
        }

        if (model.MinAge != null && model.MinAge > 0)
            mustQuerys.Add(q => q.DateRange(t => t.Field(f => f.Birthday).LessThanOrEquals(DateTime.Now.Date.AddYears(model.MinAge.Value * -1).ToYYYY_MM_DD())));
        if (model.MaxAge != null && model.MaxAge > 0)
            mustQuerys.Add(q => q.DateRange(t => t.Field(f => f.Birthday).GreaterThanOrEquals(DateTime.Now.Date.AddYears(model.MaxAge.Value * -1).ToYYYY_MM_DD())));
        if (!string.IsNullOrEmpty(model.StartTime) && DateTime.TryParse(model.StartTime, out var dtStartTime))
            mustQuerys.Add(q => q.DateRange(t => t.Field(f => f.CreatedTime).GreaterThanOrEquals(model.StartTime)));
        if (!string.IsNullOrEmpty(model.EndTime) && DateTime.TryParse(model.EndTime, out var dtEndTime))
            mustQuerys.Add(q => q.DateRange(t => t.Field(f => f.CreatedTime).LessThan(dtEndTime.Date.AddDays(1))));
        if (!string.IsNullOrWhiteSpace(model.ChannelId))
            mustQuerys.Add(q => q.Term(t => t.Field(f => f.ChannelId).Value(model.ChannelId)));

        var resultModel = client.Search<EsTalentVirtual>(s => s
        .Index(_esHelper.GetIndex().TalentVirtual)
        .TrackTotalHits()
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Query(q => q.Bool(b => b.Must(mustQuerys)) && q.Bool(b => b.Should(regionQuerys)))
        .Sort(s => s.Field(f =>
        {
            f.Order(SortOrder.Descending);
            f.Field(c => c.UpdatedTime);
            return f;
        })));

        var result = new TalentVirtualResumeListResponse { Rows = new List<TalentVirtualResumeListModel>() };
        result.Count = (int)resultModel.Total;

        // if (resultModel.Total < 0)
        //     result.Total = 0;

        // 获取当前页channelId集合,并从mysql查询对应name
        var ids = resultModel.Documents.Select(s => s.ChannelId);
        var channelInfos = _context.Qd_Hr_Channel.Where(w => ids.Contains(w.Id)).Select(s => new { ChannelId = s.Id, ChannelUserName = s.User_Seeker.NickName }).ToList();

        foreach (var i in resultModel.Documents)
        {
            var edu = i.Edus?.OrderByDescending(o => o.StartTime).FirstOrDefault();
            var wk = i.Works?.OrderByDescending(o => o.StartTime).FirstOrDefault();
            var addModel = new TalentVirtualResumeListModel()
            {
                Birthday = i.Birthday,
                ChannelName = i.Channel == null ? null : i.Channel.GetDescription(),
                EducationName = i.Education == null ? null : i.Education.GetDescription(),
                EduExp = $"{edu?.SchoolName}-{edu?.MajorName} {edu?.Education.GetDescription()} {edu?.EndTime:yyyy-MM}",
                HeadPortrait = i.HeadPortrait,
                HopeIndustry = i.Hopes == null ? null : string.Join('|', i.Hopes.Select(x => x.IndustryName)),
                HopePost = i.Hopes == null ? null : string.Join('|', i.Hopes.Select(x => x.PostName)),
                HopeSalary = i.Hopes == null ? null : string.Join('|', i.Hopes.Select(x => Tools.FormattingSalary((int)x.MinSalary, (int)x.MaxSalary, PostSalaryType.月薪, PostWorkNature.全职))),
                Id = i.Id,
                Location = i.Location,
                Name = i.Name,
                Sex = i.Sex,
                SexName = i.Sex == null ? null : i.Sex.ToString(),
                StatusName = i.Status == null ? null : i.Status.GetDescription(),
                UpdateTime = i.UpdatedTime,
                Channel = i.Channel,
                Education = i.Education,
                Status = i.Status,
                Label = i.OtherLabel,
                WorkExp = $"{wk?.CompanyName}-{wk?.PostName}/{wk?.StartTime:yyyy-MM}-{wk?.EndTime:yyyy-MM}",
                WorkExperience = i.WorkTime == null ? null : DateTime.Now.Year - i.WorkTime.Value.Year,
                TalentLabel = i.Tags?.Select(s => new TalentLabelClass
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = s
                }).ToList() ?? new List<TalentLabelClass>(),
                ChannelId = i.ChannelId,
                ChannelUserName = channelInfos.FirstOrDefault(f => f.ChannelId == i.ChannelId)?.ChannelUserName
            };
            if (i.Birthday is not null && i.Birthday.Value != Constants.DefaultTime)
                addModel.Age = Tools.GetAgeByBirthdate(DateOnly.FromDateTime(i.Birthday.Value));
            result.Rows.Add(addModel);
        }

        result.Total = new List<int> { (int)result.Count, 5000 }.Min();
        return result;
    }

    /// <summary>
    /// 获取虚拟人才库列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<TalentVirtualResumeListResponse> GetTalentVirtualResumeList_OLD(TalentVirtualResumeListRequest model)
    {
        TalentVirtualResumeListResponse retModel = new TalentVirtualResumeListResponse()
        {
            Rows = new List<TalentVirtualResumeListModel>(),
            Total = 0
        };


        ExternalRequest reqModel = new ExternalRequest()
        {
            city = model.RegionIdArray,
            education = model.Education == null ? null : (int)model.Education,
            endDate = model.EndTime,
            startDate = model.StartTime,
            expectedIndustry = model.HopeIndustry,
            expectedJob = model.HopePost,
            source = model.Channel == null ? null : (int)model.Channel,
            status = model.Status == null ? null : (int)model.Status,
            unionSearch = model.Search,
            pageNum = model.PageIndex,
            pageSize = model.PageSize,
            hrId = _user.Id,
            talentLabelId = model.TalentLabelId,
            maxAge = model.MaxAge,
            minAge = model.MinAge
        };

        var resultModel = await _javaDataApi.VirtualTalentSearch(reqModel);

        if (resultModel == null || resultModel.total == 0 || resultModel.list == null)
        {
            return retModel;
        }

        foreach (var i in resultModel.list)
        {
            var addModel = new TalentVirtualResumeListModel()
            {
                //Age = i.birthday == null ? null : Tools.GetAgeByBirthdate(i.birthday),
                Birthday = i.birthday,
                ChannelName = i.channel == null ? null : i.channel.GetDescription(),
                EducationName = i.education == null ? null : i.education.GetDescription(),
                EduExp = i.eduExp,
                HeadPortrait = i.headPortrait,
                HopeIndustry = i.industryName == null ? null : i.industryName,
                HopePost = i.postName == null ? null : i.postName,
                HopeSalary = i.salaryHope,
                Id = i.id,
                Location = i.location,
                Name = i.name,
                Sex = i.sex,
                SexName = i.sexName,
                StatusName = i.status == null ? null : i.status.GetDescription(),
                UpdateTime = i.updatedTime,
                Channel = i.channel,
                Education = i.education,
                Status = i.status,
                WorkExp = i.workExp,
                WorkExperience = i.workTime == null ? null : DateTime.Now.Year - i.workTime.Value.Year,
                TalentLabel = i.talentLabel,
            };
            try
            {
                addModel.Label = i.otherLabel == null ? null : JsonSerializer.DeserializeFromString<List<string>>(i.otherLabel);
            }
            catch
            {
            }
            retModel.Rows.Add(addModel);
        }

        if (retModel.Rows.Count > 0)
        {
            foreach (var i in retModel.Rows)
            {
                if (i.Birthday is not null)
                {
                    i.Age = Tools.GetAgeByBirthdate(DateOnly.FromDateTime(i.Birthday.Value));
                }
            }
        }

        retModel.Total = resultModel.total ?? 0;
        retModel.Count = retModel.Count;

        // var usernum = _context.User_Num.Where(x => x.UserId == _user.Id).Select(s => new
        // {
        //     s.Talent,
        //     s.VirtualTalent
        // }).First();

        // retModel.Talent = usernum.Talent;
        // retModel.VirtualTalent = new List<int> { usernum.Talent, retModel.Total }.Max();

        return retModel;
    }

    /// <summary>
    /// 删除人才库简历
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<EmptyResponse> DeleteTalentVirtualResume(DeleteTalentVirtualResumeRequest model)
    {
        //主表
        var resumelList = _context.Talent_Virtual.Where(o => o.HrId == _user.Id && model.VirtualIds.Contains(o.Id)).ToList();

        var selectVirtualIds = resumelList.Select(o => o.Id).ToList();

        //教育
        var eduList = _context.Talent_Virtual_Edu.Where(o => selectVirtualIds.Contains(o.VirtualId));

        //工作
        var workList = _context.Talent_Virtual_Work.Where(o => selectVirtualIds.Contains(o.VirtualId));

        var notifyModel = new Sub_TalentCount_Change
        {
            HrId = _user.Id,
            PostOld = workList.Select(x => x.PostName).ToList(),
            IndustryOld = workList.Select(x => x.IndustryName).ToList()
        };

        //项目
        var projectList = _context.Talent_Virtual_Project.Where(o => selectVirtualIds.Contains(o.VirtualId));

        //求职期望
        var hopeList = _context.Talent_Virtual_Hope.Where(o => selectVirtualIds.Contains(o.VirtualId));

        // //关系表
        // var relationList = _context.Talent_Relation.Where(o => selectVirtualIds.Contains(o.TalentId));

        //上传简历记录
        var uploadList = _context.Talent_Upload_Recordsub.Where(o => selectVirtualIds.Contains(o.VirtualId));

        _context.Talent_Virtual.RemoveRange(resumelList);
        _context.Talent_Virtual_Edu.RemoveRange(eduList);
        _context.Talent_Virtual_Work.RemoveRange(workList);
        _context.Talent_Virtual_Project.RemoveRange(projectList);
        _context.Talent_Virtual_Hope.RemoveRange(hopeList);
        // _context.Talent_Relation.RemoveRange(relationList);

        foreach (var i in uploadList)
        {
            i.VirtualId = string.Empty;
        }

        _context.SaveChanges();

        ////通知月斌删除虚拟人才库
        //string url = _config.DataRdsServer + "/datards/linggong/resume/virtual/remove";

        //var result = await url.PostJsonAsync(new { ids = model.VirtualIds }).ReceiveJson<JavaApiModel<object>>();

        //if (!result.success)
        //{
        //    throw new BadRequestException("删除虚拟人才库错误！");
        //}

        //删除ES上的人才库
        var client = _esHelper.GetClient();
        var delRes = await client.DeleteByQueryAsync<EsTalentVirtual>(dq =>
                    dq.MaximumDocuments(model.VirtualIds.Count)
                    .Query(q => q.Terms(tr => tr.Field(fd => fd.Id).Terms(model.VirtualIds)))
                    .Index(_esHelper.GetIndex().TalentVirtual).Refresh());


        //删除人才库通知楷哥消息
        MyRedis.Client.SAdd(SubscriptionKey.VirtualTalentCountChange, notifyModel);

        return new EmptyResponse();
    }

    /// <summary>
    /// 获取虚拟人才库简历详情
    /// </summary>
    /// <param name="virtualId"></param>
    /// <returns></returns>
    public TalentVirtualResumeDetailsResponse GetTalentVirtualResumeDetails(string virtualId)
    {
        var talentVirtualModel = _context.Talent_Virtual.Where(o => o.HrId == _user.Id && o.Id == virtualId).Select(o => new TalentVirtualResumeDetailsResponse
        {
            Name = o.Name,
            HeadPortrait = o.HeadPortrait,
            Mailbox = o.Mailbox,
            Mobile = o.Mobile,
            OriginalUrl = o.OriginalUrl,
            Perfection = o.Perfection,
            QQ = o.QQ,
            Sex = o.Sex,
            SexName = o.Sex.GetDescription(),
            Status = o.Status,
            StatusName = o.Status.GetDescription(),
            Education = o.Education,
            EducationName = o.Education.GetDescription(),
            SelfEvaluation = o.SelfEvaluation,
            VirtualId = o.Id,
            WeChat = o.WeChat,
            Age = DateTime.Now.Year - o.Birthday.Year,
            Birthday = o.Birthday,
            WorkExperience = DateTime.Now.Year - o.WorkTime.Year,
            WorkTime = o.WorkTime,
            Location = o.Location,
            RealUserSub = string.IsNullOrEmpty(o.SeekerId) ? null : new TalentVirtaualResumeRealUserSub
            {
                UserName = o.User_Seeker.NickName,
                HeadPortrait = o.User_Seeker.Avatar ?? "",
                Age = o.User_Seeker.User_Resume.Birthday == null ? null : (DateTime.Now.Year - o.User_Seeker.User_Resume.Birthday.Value.Year),
                Education = o.User_Seeker.User_Resume.Education,
                EducationName = o.User_Seeker.User_Resume.Education == null ? null : o.User_Seeker.User_Resume.Education.GetDescription(),
                IsRealName = !string.IsNullOrWhiteSpace(o.User_Seeker.User.IdentityCard),
                Occupation = o.User_Seeker.User_Resume.Occupation,
                OccupationName = o.User_Seeker.User_Resume.Occupation == null ? null : o.User_Seeker.User_Resume.Occupation.GetDescription(),
                SeekerId = o.SeekerId,
                Sex = o.User_Seeker.User_Resume.Sex,
                SexName = o.User_Seeker.User_Resume.Sex == null ? null : o.User_Seeker.User_Resume.Sex.GetDescription(),
                BrowseNumber = 0,
                Interested = 0,
                LoginTime = o.User_Seeker.User_Extend.LoginTime,
                //ActiveDay = (int)(DateTime.Now - o.User_Seeker.User_Extend.LoginTime).TotalDays,
            },
            Highlights = o.Highlights,
            Risks = o.Risks,
            OtherLabel = o.OtherLabel,
            SkillSub = o.SkillAnalysis,
            EduSub = o.Talent_Virtual_Edu.Select(s => new TalentVirtaualResumeEduSub
            {
                EduId = s.Id,
                Education = s.Education,
                EducationName = s.Education.GetDescription(),
                MajorName = s.MajorName,
                SchoolName = s.SchoolName,
                SchoolRemarks = s.SchoolRemarks,
                StartTime = s.StartTime,
                EndTime = s.EndTime,
                IsFullTime = s.IsFullTime,

            }).ToList(),
            ProjectSub = o.Talent_Virtual_Project.Select(s => new TalentVirtaualResumeProjectSub
            {
                PostName = s.PostName,
                ProjectId = s.Id,
                ProjectName = s.ProjectName,
                ProjectRemarks = s.ProjectRemarks,
                StartTime = s.StartTime,
                EndTime = s.EndTime
            }).ToList(),
            HopeSub = new TalentVirtaualResumeHopeSub
            {
                HopeCity = o.Talent_Virtual_Hope.HopeCity,
                HopeId = o.Talent_Virtual_Hope.Id,
                MaxSalary = o.Talent_Virtual_Hope.MaxSalary,
                MinSalary = o.Talent_Virtual_Hope.MinSalary,
                IndustryName = o.Talent_Virtual_Hope.IndustryName,
                PostName = o.Talent_Virtual_Hope.PostName,
            },
            WorkSub = o.Talent_Virtual_Work.Select(s => new TalentVirtaualResumeWorkSub
            {
                WorkId = s.Id,
                PostName = s.PostName,
                IndustryName = s.IndustryName,
                MinSalary = s.MinSalary,
                MaxSalary = s.MaxSalary,
                EndTime = s.EndTime,
                StartTime = s.StartTime,
                CompanyRemarks = s.CompanyRemarks,
                CompanyName = s.CompanyName,
                Department = s.Department,
            }).ToList(),
        }).FirstOrDefault();

        if (talentVirtualModel is null)
        {
            throw new NotFoundException("内容不存在！");
        }
        else
        {
            if (talentVirtualModel.RealUserSub is not null)
            {
                talentVirtualModel.RealUserSub.ActiveDay = (int)(DateTime.Now - talentVirtualModel.RealUserSub.LoginTime).TotalDays;
            }
        }

        return talentVirtualModel;
    }

    /// <summary>
    /// 修改虚拟人才库简历
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse EditTalentVirtaualResume(EditVirtualResumeDetailsRequest model)
    {
        var talentVirtualResumeModel = _context.Talent_Virtual.Where(o => o.HrId == _user.Id && o.Id == model.VirtualId).FirstOrDefault();
        if (talentVirtualResumeModel == null)
        {
            throw new NotFoundException("内容不存在！");
        }

        //基本信息
        talentVirtualResumeModel.Name = model.Name;
        talentVirtualResumeModel.Birthday = model.Birthday;
        talentVirtualResumeModel.Sex = model.Sex;
        talentVirtualResumeModel.WorkTime = model.WorkTime;
        talentVirtualResumeModel.WeChat = model.WeChat;
        talentVirtualResumeModel.QQ = model.QQ;
        talentVirtualResumeModel.Mobile = model.Mobile;
        talentVirtualResumeModel.Mailbox = model.Mailbox;
        talentVirtualResumeModel.SelfEvaluation = model.SelfEvaluation;

        //变更手机号后重新绑定真实用户
        var realUserModel = _context.User_Seeker.Where(o => o.User.Mobile == model.Mobile).FirstOrDefault();
        if (realUserModel is not null)
        {
            talentVirtualResumeModel.SeekerId = realUserModel.UserId;
        }

        var notify = new Sub_TalentCount_Change
        {
            HrId = _user.Id
        };

        //工作经历
        if (model.WorkSub?.Count > 0)
        {
            var deleteWorkList = _context.Talent_Virtual_Work.Where(o => o.VirtualId == talentVirtualResumeModel.Id).ToList();

            notify.PostOld = deleteWorkList.Select(x => x.PostName).ToList();
            notify.IndustryOld = deleteWorkList.Select(x => x.IndustryName).ToList();

            if (deleteWorkList.Count > 0)
            {
                _context.Talent_Virtual_Work.RemoveRange(deleteWorkList);
            }

            List<Talent_Virtual_Work> addWorkList = new List<Talent_Virtual_Work>();
            foreach (var i in model.WorkSub)
            {
                addWorkList.Add(new Talent_Virtual_Work
                {
                    CompanyName = i.CompanyName ?? "",
                    CompanyRemarks = i.CompanyRemarks ?? "",
                    CreatedTime = DateTime.Now,
                    Department = i.Department ?? "",
                    EndTime = i.EndTime,
                    IndustryName = i.IndustryName ?? "",
                    MaxSalary = i.MaxSalary ?? 0,
                    MinSalary = i.MinSalary ?? 0,
                    PostName = i.PostName ?? "",
                    StartTime = i.StartTime ?? DateTime.Now,
                    VirtualId = talentVirtualResumeModel.Id
                });
            }

            notify.PostNew = addWorkList.Select(x => x.PostName).ToList();
            notify.IndustryNew = addWorkList.Select(x => x.IndustryName).ToList();

            _context.Talent_Virtual_Work.AddRange(addWorkList);
        }

        //教育经历
        if (model.EduSub?.Count > 0)
        {
            var deleteEduList = _context.Talent_Virtual_Edu.Where(o => o.VirtualId == talentVirtualResumeModel.Id).ToList();
            if (deleteEduList.Count > 0)
            {
                _context.Talent_Virtual_Edu.RemoveRange(deleteEduList);
            }

            List<Talent_Virtual_Edu> addEduList = new List<Talent_Virtual_Edu>();
            foreach (var i in model.EduSub)
            {
                addEduList.Add(new Talent_Virtual_Edu
                {
                    CreatedTime = DateTime.Now,
                    Education = i.Education ?? TalentVirtualEducation.Junior,
                    EndTime = i.EndTime,
                    IsFullTime = i.IsFullTime ?? false,
                    MajorName = i.MajorName ?? "",
                    SchoolName = i.SchoolName ?? "",
                    SchoolRemarks = i.SchoolRemarks ?? "",
                    StartTime = i.StartTime ?? DateTime.Now,
                    VirtualId = talentVirtualResumeModel.Id
                });
            }

            _context.Talent_Virtual_Edu.AddRange(addEduList);
        }

        //项目经历
        if (model.ProjectSub?.Count > 0)
        {
            var deleteProjectList = _context.Talent_Virtual_Project.Where(o => o.VirtualId == talentVirtualResumeModel.Id).ToList();
            if (deleteProjectList.Count > 0)
            {
                _context.Talent_Virtual_Project.RemoveRange(deleteProjectList);
            }

            List<Talent_Virtual_Project> addProjectList = new List<Talent_Virtual_Project>();
            foreach (var i in model.ProjectSub)
            {
                addProjectList.Add(new Talent_Virtual_Project
                {
                    CreatedTime = DateTime.Now,
                    EndTime = i.EndTime,
                    PostName = i.PostName ?? "",
                    ProjectName = i.ProjectName ?? "",
                    ProjectRemarks = i.ProjectRemarks ?? "",
                    StartTime = i.StartTime ?? DateTime.Now,
                    VirtualId = talentVirtualResumeModel.Id
                });
            }

            _context.Talent_Virtual_Project.AddRange(addProjectList);
        }

        //求职期望
        if (model.HopeSub != null)
        {
            var hope = _context.Talent_Virtual_Hope.FirstOrDefault(x => x.Talent_Virtual.HrId == _user.Id && x.Id == model.HopeSub.HopeId);

            if (hope == null)
            {
                hope = new Talent_Virtual_Hope()
                {
                    VirtualId = talentVirtualResumeModel.Id
                };
                _context.Add(hope);
            }
            hope.HopeCity = model.HopeSub.HopeCity ?? "";
            hope.IndustryName = model.HopeSub.IndustryName ?? "";
            hope.PostName = model.HopeSub.PostName ?? "";
            hope.MaxSalary = model.HopeSub.MaxSalary ?? 0;
            hope.MinSalary = model.HopeSub.MinSalary ?? 0;
        }

        _context.SaveChanges();

        //计算简历完整度
        _commonVirtualService.CountVirtualResumeScore(model.VirtualId);

        MyRedis.Client.SAdd(SubscriptionKey.VirtualTalentCountChange, notify);

        return new EmptyResponse();
    }

    /// <summary>
    /// 修改虚拟人才库原始简历地址
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse EditTalentVirtaualOriginalUrl(EditTalentVirtaualOriginalUrlRequest model)
    {
        if (string.IsNullOrEmpty(model.VirtaualId) || string.IsNullOrEmpty(model.Url))
        {
            throw new BadRequestException("请求参数错误！");
        }

        var talentVirtaualModel = _context.Talent_Virtual.Where(o => o.HrId == _user.Id && o.Id == model.VirtaualId).FirstOrDefault();
        if (talentVirtaualModel == null)
        {
            throw new BadRequestException("内容不存在！");
        }

        talentVirtaualModel.OriginalUrl = model.Url;

        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 获取虚拟人才库简历评论
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public GetTalentVirtualResumeCommentResponse GetTalentVirtualResumeComment(GetTalentVirtualResumeCommentRequest model)
    {
        if (string.IsNullOrEmpty(model.VirtualId))
        {
            throw new BadRequestException("请求参数错误！");
        }

        GetTalentVirtualResumeCommentResponse retModel = new GetTalentVirtualResumeCommentResponse();

        var predicate = PredicateBuilder.New<Talent_Virtual_Comment>(o => o.HrId == _user.Id && o.VirtualId == model.VirtualId);

        var sql = _context.Talent_Virtual_Comment.Where(predicate);

        retModel.Total = sql.Count();

        retModel.Rows = sql
        .OrderByDescending(o => o.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetTalentVirtualResumeCommentModel
        {
            Id = s.Id,
            Content = s.Content,
            CreatedTime = s.CreatedTime,
            HeadPortrait = s.User_Hr.Avatar ?? "",
            HrName = s.HrName
        }).ToList();

        return retModel;
    }

    /// <summary>
    /// 添加虚拟人才库简历评论
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse AddTalentVirtualResumeComment(AddTalentVirtualResumeCommentRequest model)
    {
        bool isExist = _context.Talent_Virtual.Any(o => o.HrId == _user.Id && o.Id == model.VirtualId);

        if (!isExist)
        {
            throw new BadRequestException("内容不存在！");
        }

        Talent_Virtual_Comment addModel = new Talent_Virtual_Comment()
        {
            CreatedTime = DateTime.Now,
            Content = model.Content,
            HrId = _user.Id,
            HrName = _user.Name ?? "",
            VirtualId = model.VirtualId
        };

        _context.Talent_Virtual_Comment.Add(addModel);
        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 删除虚拟人才库简历评论
    /// </summary>
    /// <param name="commentId"></param>
    /// <returns></returns>
    public EmptyResponse DeleteTalentVirtualResumeComment(string commentId)
    {
        var commentModel = _context.Talent_Virtual_Comment.Where(o => o.HrId == _user.Id && o.Id == commentId).FirstOrDefault();
        if (commentModel == null)
        {
            throw new BadRequestException("内容不存在！");
        }

        _context.Talent_Virtual_Comment.Remove(commentModel);
        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 获取虚拟人才库职位列表
    /// </summary>
    /// <returns></returns>
    public TalentVirtualPostListResponse GetTalentVirtualPostList()
    {
        TalentVirtualPostListResponse retModel = new TalentVirtualPostListResponse();

        var postModel = _context.Dic_Talent_Post.Select(o => new TalentVirtualPostOneTree
        {
            Id = o.Id,
            Name = o.PostName,
            ParentId = o.ParentId,
        }).ToList();

        var treeNotes = Tools.GetChildrenTreeNode(postModel, x => x.Id, y => y.ParentId, string.Empty);

        retModel.Rows = Tools.ModelConvert<List<TalentVirtualPostOneTree>>(treeNotes);

        return retModel;
    }

    /// <summary>
    /// 获取虚拟人才库行业列表
    /// </summary>
    /// <returns></returns>
    public TalentVirtualIndustryListResponse GetTalentVirtualIndustryList()
    {
        TalentVirtualIndustryListResponse retModel = new TalentVirtualIndustryListResponse();

        var industryModel = _context.Dic_Talent_Industry.Select(o => new TalentVirtualIndustryListModel
        {
            Id = o.Id,
            Name = o.IndustryName
        }).ToList();

        retModel.Rows = industryModel;

        return retModel;
    }

    /// <summary>
    /// 上传简历（调用小析服务解析简历）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse UpLoadResume(UpLoadResumeRequest model)
    {
        if (model.resumes != null && model.resumes.Count > 0)
        {
            List<Talent_Upload_Record> addList = new List<Talent_Upload_Record>();
            foreach (var i in model.resumes)
            {
                addList.Add(new Talent_Upload_Record()
                {
                    CreatedTime = DateTime.Now,
                    FileExtension = i.FileExtension,
                    FileName = i.FileName,
                    FileUrl = i.FileUrl,
                    HrId = _user.Id,
                    RepeatType = i.RepeatType,
                    UpLoadStatus = TalentUpLoadStatus.Untreated,
                    FailNumber = 0,
                    QualifiedNumber = 0,
                    RepeatNumber = 0,
                    TotalNumber = 0,
                    WarehousingNumber = 0
                });

            }

            _context.Talent_Upload_Record.AddRange(addList);
            _context.SaveChanges();

            MyRedis.Client.RPush(SubscriptionKey.UpLoadResume, addList.Select(o => o.Id).ToArray());
        }

        return new EmptyResponse();
    }

    /// <summary>
    /// 解析原始简历
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<EmptyResponse> AnalyzeOriginalResume(AnalyzeOriginalResumeRequest model)
    {
        var lockerKey = $"st:analyzeresume:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        if (string.IsNullOrEmpty(model.VirtualId))
        {
            throw new BadRequestException("请求参数错误！");
        }

        var talentVirtualModel = _context.Talent_Virtual.Where(o => o.Id == model.VirtualId && o.HrId == _user.Id).FirstOrDefault();
        if (talentVirtualModel == null)
        {
            throw new BadRequestException("内容不存在！");
        }

        string fileName = Path.GetFileName(talentVirtualModel.OriginalUrl);
        string fileUrl = talentVirtualModel.OriginalUrl;

        var parseResult = await _parseResume.AnalysisResume(fileUrl, fileName);
        var resumeModel = parseResult.Data.FirstOrDefault();
        if (resumeModel != null)
        {
            try
            {
                var regionModel = _commonDicService.GetRegion();
                //简历信息
                if (resumeModel.parsing_result != null)
                {
                    //基本信息（求职期望）
                    if (resumeModel.parsing_result.basic_info != null)
                    {
                        talentVirtualModel.HeadPortrait = resumeModel.avatar_url ?? string.Empty;
                        talentVirtualModel.Name = resumeModel.parsing_result.basic_info.name ?? string.Empty;
                        talentVirtualModel.Mobile = resumeModel.parsing_result.contact_info?.phone_number ?? string.Empty;
                        talentVirtualModel.Sex = resumeModel.parsing_result.basic_info.gender == "男" ? Sex.男 : Sex.女;
                        talentVirtualModel.Mailbox = resumeModel.parsing_result.contact_info?.email ?? string.Empty;
                        talentVirtualModel.WeChat = resumeModel.parsing_result.contact_info?.wechat ?? string.Empty;
                        talentVirtualModel.QQ = resumeModel.parsing_result.contact_info?.QQ ?? string.Empty;
                        talentVirtualModel.WorkTime = DateTime.Now.AddYears(0 - resumeModel.parsing_result.basic_info.num_work_experience);
                        talentVirtualModel.Location = resumeModel.parsing_result.basic_info.current_location ?? string.Empty;
                        talentVirtualModel.DetailedLocation = resumeModel.parsing_result.basic_info.detailed_location ?? string.Empty;
                        talentVirtualModel.LocationNorm = resumeModel.parsing_result.basic_info.current_location_norm ?? string.Empty;
                        talentVirtualModel.Status = TalentVirtualStatus.UnRegistered;
                        talentVirtualModel.UpdatedTime = DateTime.Now;

                        if (resumeModel.parsing_result.contact_info != null)
                        {
                            if (!string.IsNullOrEmpty(resumeModel.parsing_result.contact_info.phone_number))
                            {
                                var userModel = _context.User.Where(o => o.Mobile == resumeModel.parsing_result.contact_info.phone_number).FirstOrDefault();
                                if (userModel != null)
                                {
                                    // Talent_Relation addRelation = new Talent_Relation()
                                    // {
                                    //     HrId = talentVirtualModel.HrId,
                                    //     SeekerId = userModel.UserId,
                                    //     TalentId = talentVirtualModel.Id
                                    // };
                                    // _context.Talent_Relation.Add(addRelation);

                                    talentVirtualModel.SeekerId = userModel.UserId;
                                    talentVirtualModel.Status = TalentVirtualStatus.Registered;

                                    if (!string.IsNullOrEmpty(userModel.IdentityCard))
                                    {
                                        talentVirtualModel.Status = TalentVirtualStatus.RealName;
                                    }
                                }
                            }
                        }

                        if (!string.IsNullOrEmpty(resumeModel.parsing_result.basic_info.detailed_location))
                        {
                            talentVirtualModel.RegionId = regionModel.Where(o => o.Name.Contains(resumeModel.parsing_result.basic_info.detailed_location)).FirstOrDefault()?.Id ?? string.Empty;
                        }

                        if (!string.IsNullOrEmpty(resumeModel.parsing_result.basic_info.degree))
                        {
                            switch (resumeModel.parsing_result.basic_info.degree)
                            {
                                case "硕士": talentVirtualModel.Education = TalentVirtualEducation.Master; break;
                                case "博士": talentVirtualModel.Education = TalentVirtualEducation.Doctor; break;
                                case "本科": talentVirtualModel.Education = TalentVirtualEducation.Undergraduate; break;
                                case "大专": talentVirtualModel.Education = TalentVirtualEducation.Professional; break;
                                case "高中": talentVirtualModel.Education = TalentVirtualEducation.Senior; break;
                                case "中专": talentVirtualModel.Education = TalentVirtualEducation.Specialized; break;
                                case "初中": talentVirtualModel.Education = TalentVirtualEducation.Junior; break;
                                default: talentVirtualModel.Education = TalentVirtualEducation.Junior; break;
                            }
                        }

                        if (resumeModel.parsing_result.basic_info.age.HasValue)
                        {
                            var bd = Tools.GetBirthdateByAge(resumeModel.parsing_result.basic_info.age);
                            if (bd.HasValue)
                                talentVirtualModel.Birthday = bd.Value.ToDateTime(TimeOnly.MinValue);
                        }

                        if (!string.IsNullOrEmpty(resumeModel.parsing_result.basic_info.date_of_birth))
                        {
                            if (resumeModel.parsing_result.basic_info.date_of_birth.Length == 4)
                            {
                                if (DateTime.TryParse(resumeModel.parsing_result.basic_info.date_of_birth + "-01-01", out DateTime _Birthday))
                                {
                                    talentVirtualModel.Birthday = _Birthday;
                                }
                            }
                            else
                            {
                                if (DateTime.TryParse(resumeModel.parsing_result.basic_info.date_of_birth, out DateTime _Birthday))
                                {
                                    talentVirtualModel.Birthday = _Birthday;
                                }
                            }
                        }

                        //求职期望
                        var hope = _context.Talent_Virtual_Hope.FirstOrDefault(o => o.VirtualId == talentVirtualModel.Id);
                        if (hope != null)
                        {
                            _context.Talent_Virtual_Hope.Remove(hope);
                        }
                        Talent_Virtual_Hope addHopeModel = new Talent_Virtual_Hope();
                        addHopeModel.HopeCity = resumeModel.parsing_result.basic_info.expect_location ?? string.Empty;
                        addHopeModel.IndustryName = resumeModel.parsing_result.basic_info.desired_industry ?? string.Empty;
                        addHopeModel.PostName = resumeModel.parsing_result.basic_info.desired_position ?? string.Empty;
                        addHopeModel.VirtualId = talentVirtualModel.Id;
                        if (!string.IsNullOrEmpty(resumeModel.parsing_result.basic_info.expect_location))
                        {
                            addHopeModel.RegionId = regionModel.Where(o => o.Name.Contains(resumeModel.parsing_result.basic_info.expect_location)).FirstOrDefault()?.Id ?? string.Empty;
                        }
                        if (!string.IsNullOrEmpty(resumeModel.parsing_result.basic_info.desired_salary))
                        {
                            string[] desired_salary = resumeModel.parsing_result.basic_info.desired_salary.Split("-");
                            if (desired_salary.Length == 2)
                            {
                                string _MinSalary = desired_salary[0].Trim();
                                string _MaxSalary = desired_salary[1].Trim();
                                try
                                {
                                    addHopeModel.MinSalary = Convert.ToDecimal(System.Text.RegularExpressions.Regex.Replace(_MinSalary, @"[^\d]*", ""));
                                    addHopeModel.MaxSalary = Convert.ToDecimal(System.Text.RegularExpressions.Regex.Replace(_MaxSalary, @"[^\d]*", ""));
                                    if (_MinSalary.Contains('K') || _MinSalary.Contains('k'))
                                    {
                                        addHopeModel.MinSalary *= 1000;
                                    }
                                    if (_MaxSalary.Contains('K') || _MaxSalary.Contains('k'))
                                    {
                                        addHopeModel.MaxSalary *= 1000;
                                    }
                                }
                                catch
                                {
                                    addHopeModel.MinSalary = 0;
                                    addHopeModel.MaxSalary = 0;
                                }
                            }
                        }
                        _context.Talent_Virtual_Hope.Add(addHopeModel);
                    }

                    //补充信息
                    if (resumeModel.parsing_result.others != null)
                    {
                        talentVirtualModel.SelfEvaluation = resumeModel.parsing_result.others.self_evaluation ?? string.Empty;

                        TalentVirtaualResumeSkillSub SkillAnalysis = new TalentVirtaualResumeSkillSub()
                        {
                            Business = resumeModel.parsing_result.others.business_skills,
                            IT = resumeModel.parsing_result.others.it_skills,
                            Professional = resumeModel.parsing_result.others.skills
                        };
                        talentVirtualModel.SkillAnalysis = SkillAnalysis;
                    }

                    //项目经历
                    if (resumeModel.parsing_result.project_experience != null && resumeModel.parsing_result.project_experience.Count > 0)
                    {
                        var deleteProjectList = _context.Talent_Virtual_Project.Where(o => o.VirtualId == talentVirtualModel.Id).ToList();
                        if (deleteProjectList.Count > 0)
                        {
                            _context.Talent_Virtual_Project.RemoveRange(deleteProjectList);
                        }

                        List<Talent_Virtual_Project> addProjectList = new List<Talent_Virtual_Project>();
                        foreach (var i in resumeModel.parsing_result.project_experience)
                        {
                            addProjectList.Add(new Talent_Virtual_Project
                            {
                                CreatedTime = DateTime.Now,
                                PostName = i.job_title ?? string.Empty,
                                ProjectName = i.project_name ?? string.Empty,
                                ProjectRemarks = i.description ?? string.Empty,
                                StartTime = (string.IsNullOrEmpty(i.start_time_year) || string.IsNullOrEmpty(i.start_time_month)) ? DateTime.Now : Convert.ToDateTime(i.start_time_year + "-" + i.start_time_month + "-01"),
                                EndTime = (string.IsNullOrEmpty(i.end_time_year) || string.IsNullOrEmpty(i.end_time_month)) ? null : Convert.ToDateTime(i.end_time_year + "-" + i.end_time_month + "-01"),
                                VirtualId = talentVirtualModel.Id
                            });
                        }

                        _context.Talent_Virtual_Project.AddRange(addProjectList);
                    }

                    //工作经历
                    if (resumeModel.parsing_result.work_experience != null && resumeModel.parsing_result.work_experience.Count > 0)
                    {
                        var deleteWorkList = _context.Talent_Virtual_Work.Where(o => o.VirtualId == talentVirtualModel.Id).ToList();
                        if (deleteWorkList.Count > 0)
                        {
                            _context.Talent_Virtual_Work.RemoveRange(deleteWorkList);
                        }

                        List<Talent_Virtual_Work> addWorkList = new List<Talent_Virtual_Work>();
                        foreach (var i in resumeModel.parsing_result.work_experience)
                        {
                            Talent_Virtual_Work addWorkModel = new Talent_Virtual_Work()
                            {
                                CompanyName = i.company_name ?? string.Empty,
                                CompanyRemarks = i.description ?? string.Empty,
                                CreatedTime = DateTime.Now,
                                Department = i.department ?? string.Empty,
                                IndustryName = i.industry ?? string.Empty,
                                PostName = i.job_title ?? string.Empty,
                                StartTime = (string.IsNullOrEmpty(i.start_time_year) || string.IsNullOrEmpty(i.start_time_month)) ? DateTime.Now : Convert.ToDateTime(i.start_time_year + "-" + i.start_time_month + "-01"),
                                EndTime = (string.IsNullOrEmpty(i.end_time_year) || string.IsNullOrEmpty(i.end_time_month)) ? null : Convert.ToDateTime(i.end_time_year + "-" + i.end_time_month + "-01"),
                                VirtualId = talentVirtualModel.Id
                            };

                            if (!string.IsNullOrEmpty(i.salary))
                            {
                                string[] salary = i.salary.Split("-");
                                if (salary.Length == 2)
                                {
                                    string _MinSalary = salary[0].Trim();
                                    string _MaxSalary = salary[1].Trim();
                                    try
                                    {
                                        addWorkModel.MinSalary = Convert.ToDecimal(System.Text.RegularExpressions.Regex.Replace(_MinSalary, @"[^\d]*", ""));
                                        addWorkModel.MaxSalary = Convert.ToDecimal(System.Text.RegularExpressions.Regex.Replace(_MaxSalary, @"[^\d]*", ""));
                                        if (_MinSalary.Contains('K') || _MinSalary.Contains('k'))
                                        {
                                            addWorkModel.MinSalary *= 1000;
                                        }
                                        if (_MaxSalary.Contains('K') || _MaxSalary.Contains('k'))
                                        {
                                            addWorkModel.MaxSalary *= 1000;
                                        }
                                    }
                                    catch
                                    {
                                        addWorkModel.MinSalary = 0;
                                        addWorkModel.MaxSalary = 0;
                                    }
                                }
                            }

                            addWorkList.Add(addWorkModel);
                        }

                        _context.Talent_Virtual_Work.AddRange(addWorkList);
                    }

                    //教育经历
                    if (resumeModel.parsing_result.education_experience != null && resumeModel.parsing_result.education_experience.Count > 0)
                    {
                        var deleteEduList = _context.Talent_Virtual_Edu.Where(o => o.VirtualId == talentVirtualModel.Id).ToList();
                        if (deleteEduList.Count > 0)
                        {
                            _context.Talent_Virtual_Edu.RemoveRange(deleteEduList);
                        }

                        List<Talent_Virtual_Edu> addEduList = new List<Talent_Virtual_Edu>();
                        foreach (var i in resumeModel.parsing_result.education_experience)
                        {
                            Talent_Virtual_Edu addEduModel = new Talent_Virtual_Edu()
                            {
                                CreatedTime = DateTime.Now,
                                IsFullTime = i.study_model == "全职" ? true : false,
                                MajorName = i.major ?? string.Empty,
                                SchoolName = i.school_name ?? string.Empty,
                                SchoolRemarks = string.Empty,
                                StartTime = (string.IsNullOrEmpty(i.start_time_year) || string.IsNullOrEmpty(i.start_time_month)) ? DateTime.Now : Convert.ToDateTime(i.start_time_year + "-" + i.start_time_month + "-01"),
                                EndTime = (string.IsNullOrEmpty(i.end_time_year) || string.IsNullOrEmpty(i.end_time_month)) ? null : Convert.ToDateTime(i.end_time_year + "-" + i.end_time_month + "-01"),
                                VirtualId = talentVirtualModel.Id
                            };

                            switch (i.degree)
                            {
                                case "硕士": addEduModel.Education = TalentVirtualEducation.Master; break;
                                case "博士": addEduModel.Education = TalentVirtualEducation.Doctor; break;
                                case "本科": addEduModel.Education = TalentVirtualEducation.Undergraduate; break;
                                case "大专": addEduModel.Education = TalentVirtualEducation.Professional; break;
                                case "高中": addEduModel.Education = TalentVirtualEducation.Senior; break;
                                case "中专": addEduModel.Education = TalentVirtualEducation.Specialized; break;
                                case "初中": addEduModel.Education = TalentVirtualEducation.Junior; break;
                                default: addEduModel.Education = TalentVirtualEducation.Junior; break;
                            }

                            addEduList.Add(addEduModel);
                        }

                        _context.Talent_Virtual_Edu.AddRange(addEduList);
                    }
                }

                //画像信息
                if (resumeModel.predicted_result != null)
                {
                    //标签
                    if (resumeModel.predicted_result.tags != null)
                    {
                        talentVirtualModel.IndustryLabel = resumeModel.predicted_result.tags.skills == null ? new List<string>() : resumeModel.predicted_result.tags.skills.Select(o => o.tag).ToList();
                        talentVirtualModel.PostLabel = resumeModel.predicted_result.tags.professional == null ? new List<string>() : resumeModel.predicted_result.tags.professional.Select(o => o.tag).ToList();
                        talentVirtualModel.OtherLabel = resumeModel.predicted_result.tags.others == null ? new List<string>() : resumeModel.predicted_result.tags.others.Select(o => o.tag).ToList();
                    }

                    //亮点
                    if (resumeModel.predicted_result.highlights != null)
                    {
                        TalentVirtaualResumeHighlights _Highlights = new TalentVirtaualResumeHighlights()
                        {
                            education = resumeModel.predicted_result.highlights.education,
                            occupation = resumeModel.predicted_result.highlights.occupation,
                            others = resumeModel.predicted_result.highlights.others,
                            project = resumeModel.predicted_result.highlights.project,
                            tags = resumeModel.predicted_result.highlights.tags
                        };

                        talentVirtualModel.Highlights = _Highlights;
                    }

                    //风险
                    if (resumeModel.predicted_result.risks != null)
                    {
                        TalentVirtaualResumeRisks _Risks = new TalentVirtaualResumeRisks()
                        {
                            education = resumeModel.predicted_result.risks.education,
                            occupation = resumeModel.predicted_result.risks.occupation,
                            tags = resumeModel.predicted_result.risks.tags
                        };

                        talentVirtualModel.Risks = _Risks;
                    }
                }

                _context.SaveChanges();

                //计算简历完整度
                _commonVirtualService.CountVirtualResumeScore(talentVirtualModel.Id);
            }
            catch (Exception ex)
            {
                _log.Error("解析原始简历错误，用户id：" + model.VirtualId, Tools.GetErrMsg(ex));
                throw new BadRequestException("解析原始简历错误!");
            }
        }

        return new EmptyResponse();
    }

    /// <summary>
    /// 获取虚拟人才库上传简历记录
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public GetTalentUploadRecordListResponse GetTalentUploadRecordList(GetTalentUploadRecordListRequest model)
    {
        GetTalentUploadRecordListResponse retModel = new GetTalentUploadRecordListResponse();

        var predicate = PredicateBuilder.New<Talent_Upload_Record>(o => o.HrId == _user.Id);

        switch (model.FileType)
        {
            case 0: predicate = predicate.And(o => o.FileExtension == TalentUpLoadFileExtension.Docx || o.FileExtension == TalentUpLoadFileExtension.PDF || o.FileExtension == TalentUpLoadFileExtension.TXT || o.FileExtension == TalentUpLoadFileExtension.JPG); break;
            case 10: predicate = predicate.And(o => o.FileExtension == TalentUpLoadFileExtension.Xlsx); break;
        }

        var sql = _context.Talent_Upload_Record.Where(predicate);

        retModel.Total = sql.Count();

        retModel.Rows = sql
        .OrderByDescending(o => o.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(o => new GetTalentUploadRecordListModel
        {
            Id = o.Id,
            CreatedTime = o.CreatedTime,
            FailNumber = o.FailNumber,
            FileName = o.FileName,
            HrName = o.User_Hr.NickName,
            QualifiedNumber = o.QualifiedNumber,
            RepeatNumber = o.RepeatNumber,
            TotalNumber = o.TotalNumber,
            WarehousingNumber = o.WarehousingNumber
        }).ToList();

        return retModel;
    }

    /// <summary>
    /// 获取虚拟人才库上传简历记录（子表）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public GetTalentUploadRecordSubListResponse GetTalentUploadRecordSubList(GetTalentUploadRecordSubListRequest model)
    {
        GetTalentUploadRecordSubListResponse retModel = new GetTalentUploadRecordSubListResponse();

        var predicate = PredicateBuilder.New<Talent_Upload_Recordsub>(o => o.RecordId == model.RecordId);
        if (model.IsSuccess != null)
            predicate = predicate.And(o => o.IsSuccess == model.IsSuccess);
        var sql = _context.Talent_Upload_Recordsub.Where(predicate);

        retModel.Total = sql.Count();

        retModel.Rows = sql
        .OrderByDescending(o => o.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(o => new GetTalentUploadRecordSubListModel
        {
            CreatedTime = o.CreatedTime,
            IsRepeat = o.IsRepeat,
            IsSuccess = o.IsSuccess,
            IsWarehousing = o.IsWarehousing,
            Remarks = o.Remarks,
            VirtualId = o.VirtualId,
            VirtualName = o.Talent_Virtual == null ? "测试" : o.Talent_Virtual.Name,
            FileName = o.Talent_Upload_Record.FileName,
            Perfection = o.Talent_Virtual == null ? -1 : o.Talent_Virtual.Perfection
        }).ToList();

        return retModel;
    }

    /// <summary>
    /// 获取人才库标签列表（真实虚拟通用）
    /// </summary>
    /// <returns></returns>
    public TalentLabelListResponse GetTalentLabelList()
    {
        TalentLabelListResponse retModel = new TalentLabelListResponse();

        var predicate = PredicateBuilder.New<Dic_Talent_Label>(o => o.HrId == _user.Id);

        var sql = _context.Dic_Talent_Label.Where(predicate);

        retModel.Rows = sql
        .Select(s => new TalentLabelModel
        {
            Id = s.Id,
            Name = s.LabelName
        }).ToList();

        return retModel;
    }

    /// <summary>
    /// 添加人才库标签（真实虚拟通用）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse AddTalentLabel(AddTalentLabelRequest model)
    {
        if (string.IsNullOrEmpty(model.LabelName))
        {
            throw new BadRequestException("标签内容不能为空！");
        }

        Dic_Talent_Label addModel = new Dic_Talent_Label()
        {
            HrId = _user.Id,
            LabelName = model.LabelName
        };

        _context.Dic_Talent_Label.Add(addModel);
        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 删除人才库标签（真实虚拟通用）
    /// </summary>
    /// <param name="labelId"></param>
    /// <returns></returns>
    public EmptyResponse DeleteTalentLabel(string labelId)
    {
        if (labelId.Length <= 2)
        {
            throw new BadRequestException("无法删除固定标签！");
        }

        var dicTalentLabelModel = _context.Dic_Talent_Label.Where(o => o.HrId == _user.Id && o.Id == labelId).FirstOrDefault();

        if (dicTalentLabelModel == null)
        {
            throw new BadRequestException("内容不存在！");
        }

        _context.Dic_Talent_Label.Remove(dicTalentLabelModel);

        var talentLabelList = _context.Talent_Label.Where(o => o.DicLabelId == labelId).ToList();
        _context.Talent_Label.RemoveRange(talentLabelList);

        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 获取虚拟人才库标签
    /// </summary>
    /// <param name="virtualId"></param>
    /// <returns></returns>
    public TalentVirtualLabelResponse GetTalentVirtualLabel(string virtualId)
    {
        TalentVirtualLabelResponse retModel = new TalentVirtualLabelResponse();

        var predicate = PredicateBuilder.New<Talent_Label>(o => o.VirtualId == virtualId);

        var sql = _context.Talent_Label.Where(predicate);

        retModel.Rows = sql
        .Select(s => new TalentVirtualLabelModel
        {
            Id = s.Dic_Talent_Label.Id,
            Name = s.Dic_Talent_Label.LabelName
        }).ToList();

        return retModel;
    }

    /// <summary>
    /// 变更虚拟人才库标签
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<EmptyResponse> EditTalentVirtualLabel(EditTalentVirtualLabelRequest model)
    {
        await Task.FromResult(1);
        if (string.IsNullOrEmpty(model.VirtualId))
        {
            throw new BadRequestException("请求参数错误！");
        }

        bool IsVirtualIdExist = _context.Talent_Virtual.Any(o => o.Id == model.VirtualId);
        if (!IsVirtualIdExist)
        {
            throw new BadRequestException("人才库不存在！");
        }

        foreach (var i in model.DicLabelId)
        {
            bool IsDicLabelIdExist = _context.Dic_Talent_Label.Any(o => o.Id == i);
            if (!IsDicLabelIdExist)
            {
                throw new BadRequestException("标签不存在！");
            }
        }

        var labelRemoveList = _context.Talent_Label.Where(o => o.VirtualId == model.VirtualId).ToList();
        if (labelRemoveList.Count > 0)
        {
            _context.Talent_Label.RemoveRange(labelRemoveList);
        }

        List<TalentVirtualLabelModel> postList = new List<TalentVirtualLabelModel>();
        if (model.DicLabelId.Count > 0)
        {
            List<Talent_Label> addList = new List<Talent_Label>();
            foreach (var i in model.DicLabelId)
            {
                addList.Add(new Talent_Label()
                {
                    DicLabelId = i,
                    VirtualId = model.VirtualId,
                    PlatformId = ""
                });
            }

            _context.Talent_Label.AddRange(addList);

            postList = _context.Dic_Talent_Label.Where(o => addList.Select(m => m.DicLabelId).Contains(o.Id)).Select(n => new TalentVirtualLabelModel
            {
                Id = n.Id,
                Name = n.LabelName
            }).ToList();
        }

        _context.SaveChanges();

        // //TODO：虚拟人才库筛选：人才库标签变更是否需要更新ES
        // //通知月斌变更平台人才库标签
        // string url = _config.DataRdsServer + "/datards/linggong/resume/virtual/tag/update";
        // var result = await url.PostJsonAsync(new { id = model.VirtualId, labels = postList }).ReceiveString();

        return new EmptyResponse();
    }
}

