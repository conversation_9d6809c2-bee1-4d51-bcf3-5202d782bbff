﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 幂等性检测表
/// </summary>
[Table("sys_idempotency_check")]
public class Sys_Idempotency_Check
{
    /// <summary>
    /// 标识
    /// </summary>
    public string Key { get; set; } = default!;

    /// <summary>
    /// 检测类型
    /// </summary>
    public IdempotencyCheckType Type { get; set; }

    /// <summary>
    /// 已检测次数
    /// </summary>
    [ConcurrencyCheck]
    public int CheckTimes { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}

public enum IdempotencyCheckType
{
    Sms, Password, Withdraw, WithdrawReturn
}