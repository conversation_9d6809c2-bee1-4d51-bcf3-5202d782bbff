/*
 Navicat Premium Data Transfer

 Source Server         : 诺聘**************（测试环境）
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : **************:3306
 Source Schema         : staffing

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 24/03/2025 14:46:43
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for thirdparty_postid_temp
-- ----------------------------
DROP TABLE IF EXISTS `thirdparty_postid_temp`;
CREATE TABLE `thirdparty_postid_temp`  (
  `Id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `ThirdPostId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '第三方职位ID',
  `Source` int NOT NULL COMMENT '岗位来源 1：职多多 2：58魔方 3：后续',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '临时存储临时第三方岗位Id' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
