﻿using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces.Hr;
using Config;
using Staffing.Model.Hr.DataScreen;
using Config.CommonModel.DataScreen;
using Newtonsoft.Json;
using Config.CommonModel;
using Infrastructure.Aop;

namespace Staffing.Core.Services.Service.Hr;

[Service(ServiceLifetime.Transient)]
public class DataScreenService : IDataScreenService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private ICommonService _commonService;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    public DataScreenService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, ICommonService commonService, LogManager log)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _commonService = commonService;
        _log = log;
    }

    /// <summary>
    /// 获取平台级别统计数据
    /// </summary>
    [RedisCache(Expire = 30)]
    public DSPlatDataResponse GetPlatformData()
    {
        var data = MyRedis.Client.HGetAll<string>(RedisKey.DataScreen.Key);

        if (data == null)
            return new DSPlatDataResponse(true);

        DSPlatDataResponse result = new DSPlatDataResponse(false);
        if (data.TryGetValue(RedisKey.DataScreen.InfoCount, out var Info))
            result.Info = string.IsNullOrEmpty(Info) ? new DataScreenInfoResponse() : JsonConvert.DeserializeObject<DataScreenInfoResponse>(Info)!;
        result.Info.TalentAverage = (int)Math.Ceiling(result.Info.TalentCount * 1.0 / result.Info.AdvCount);
        //＜100=较差；100＜1000=良好；＞1001=优秀
        if (result.Info.TalentAverage < 100)
            result.Info.TalentAverageText = "较差";
        else if (result.Info.TalentAverage <= 1000)
            result.Info.TalentAverageText = "良好";
        else
            result.Info.TalentAverageText = "优秀";

        if (data.TryGetValue(RedisKey.DataScreen.AdvTalRanking, out var AdvTalRanking))
            result.AdvTalRanking = JsonConvert.DeserializeObject<List<AdvTalRankingInfo>>(AdvTalRanking);

        if (data.TryGetValue(RedisKey.DataScreen.EntAdvRanking, out var EntAdvRanking))
            result.EntAdvRanking = JsonConvert.DeserializeObject<List<EntAdvRankingInfo>>(EntAdvRanking);

        if (data.TryGetValue(RedisKey.DataScreen.EntTalRanking, out var EntTalRanking))
            result.EntTalRanking = JsonConvert.DeserializeObject<List<EntTalRankingInfo>>(EntTalRanking);

        if (data.TryGetValue(RedisKey.DataScreen.RegionEntList, out var RegionEntList))
            result.RegionEnt = JsonConvert.DeserializeObject<List<string>>(RegionEntList);

        if (data.TryGetValue(RedisKey.DataScreen.CityProjectList, out var CityProjectList))
            result.CityProjectList = JsonConvert.DeserializeObject<List<RegionProjectInfo>?>(CityProjectList) ?? new List<RegionProjectInfo>();

        return result;
    }

    [RedisCache(Expire = 30)]
    public DSPlatRecruitDataResponse GetPlatformOfRecruitData()
    {
        // 从db直接拿数据
        var recruit_data_result = _context.User_Hr_Data.GroupBy(g => 1).Select(g => new DSPlatRecruitDataResponse
        {
            RecruitDelivery = g.Sum(s => s.RecruitDelivery),
            RecruitInterviewTotal = g.Sum(s => s.RecruitInterviewTotal),
            RecruitInductionTotal = g.Sum(s => s.RecruitInductionTotal),
            RecruitHrScreening = g.Sum(s => s.RecruitHrScreening),
            RecruitInterviewerScreening = g.Sum(s => s.RecruitInterviewerScreening),
            RecruitInterview = g.Sum(s => s.RecruitInterview),
            RecruitOffer = g.Sum(s => s.RecruitOffer),
            RecruitInduction = g.Sum(s => s.RecruitInduction),
            RecruitFileAway = g.Sum(s => s.RecruitFileAway)
        }).FirstOrDefault();

        if (recruit_data_result == null)
            recruit_data_result = new DSPlatRecruitDataResponse();

        // 从redis拿数据
        var data = MyRedis.Client.HGetAll<string>(RedisKey.DataScreen.ScreenRecruitInfo);

        if (data != null)
        {
            // 面试官概况
            if(data.TryGetValue(RedisKey.DataScreen.RecruitInterviewerInfo, out var interviewerInfo))
            {
                recruit_data_result.DSPlatInterviewerOfRecruitInfo = JsonConvert.DeserializeObject<DSPlatInterviewerOfRecruitInfo>(interviewerInfo);
            }

            // 累计联系 todo:第三方采集


            // 联系概况 todo:第三方采集


            // 投递概况
            if (data.TryGetValue(RedisKey.DataScreen.RecruitDeliveryInfo, out var deliveryInfo))
            {
                var deliveryList = JsonConvert.DeserializeObject<List<DSPlatDeliveryInfo>>(deliveryInfo);
                if(deliveryList != null && deliveryList.Count > 0)
                {
                    // 去年月份跟今年月份对齐，没有数据则补0
                    var CurrentYear = deliveryList.Where(w => Convert.ToDateTime(w.YearMonth).CompareTo(Convert.ToDateTime(DateTime.Now.Year + "-01")) >= 0).OrderBy(o => Convert.ToDateTime(o.YearMonth)).ToList();
                    var lastYear = deliveryList.Where(w => Convert.ToDateTime(w.YearMonth).CompareTo(Convert.ToDateTime(DateTime.Now.AddYears(-1).ToString("yyyy-MM"))) <= 0).OrderBy(o => Convert.ToDateTime(o.YearMonth)).ToList();
                    if(lastYear == null || lastYear.Count == 0)
                    {
                        lastYear = CurrentYear.Select(s => new DSPlatDeliveryInfo
                        {
                            YearMonth = Convert.ToDateTime(s.YearMonth).AddYears(-1).ToString("yyyy-MM"),
                            Count = 0
                        }).ToList();
                    }

                    foreach(var item in CurrentYear)
                    {
                        var lastYM = Convert.ToDateTime(item.YearMonth).AddYears(-1).ToString("yyyy-MM");
                        var lastInfo = lastYear.Where(w => Convert.ToDateTime(w.YearMonth).ToString("yyyy-MM") == lastYM).FirstOrDefault();
                        if (lastInfo == null)
                        {
                            lastYear.Add(new DSPlatDeliveryInfo { YearMonth = lastYM, Count = 0});
                        }
                    }

                    var sumInfo = new DSPlatDeliverySumInfo
                    {
                        CurrentYear = CurrentYear,
                        LastYear = lastYear.OrderBy(o => Convert.ToDateTime(o.YearMonth)).ToList()
                    };
                    recruit_data_result.DSPlatDeliverySumInfo = sumInfo;
                }
            }

            // 投递实时播报
            if (data.TryGetValue(RedisKey.DataScreen.RecruitDeliveryTop30, out var deliveryTop30Info))
            {
                var list = JsonConvert.DeserializeObject<List<DSPlatDeliveryTop30Info>>(deliveryTop30Info);
                if (list != null && list.Count > 0)
                {
                    recruit_data_result.DSPlatDeliveryTop30Infos = list;
                }
            }

            // 处理效率
            if (data.TryGetValue(RedisKey.DataScreen.RecruitXiaoLvInfo, out var recruitXiaoLvInfo))
            {
                var handleInfo = JsonConvert.DeserializeObject<ChuLiXiaoLvInfo>(recruitXiaoLvInfo);
                recruit_data_result.RegisterPercent = handleInfo?.ZhuCe ?? 0;
                recruit_data_result.DeliveryPercent = handleInfo?.TouDi ?? 0;
                recruit_data_result.ReturnPercent = handleInfo?.FanKui ?? 0;
                recruit_data_result.EntryPercent = handleInfo?.RuZhi ?? 0;
                recruit_data_result.ContractPercent = handleInfo?.QianYue ?? 0;
            }

            // 合同概况

        }

        return recruit_data_result;
    }

    [RedisCache(Expire = 30)]
    public DSPlatSeekerDataResponse GetPlatformOfSeekerData()
    {
        DSPlatSeekerDataResponse response = new();
        // 从redis拿数据
        var data = MyRedis.Client.HGetAll<string>(RedisKey.DataScreen.ScreenSeekerInfo);
        if(data is not null && data.Count > 0)
        {
            if(data.TryGetValue(RedisKey.DataScreen.ScreenSeekerInfo, out var seekerInfo))
            {
                var info = JsonConvert.DeserializeObject<DSPlatSeekerDataInfo>(seekerInfo);
                if (info == null)
                    return response;

                // 数据处理
                // 性别概况
                List<DSPlatSeekerSexResponse>? sexInfo = info.SexInfo?.Select(x => new DSPlatSeekerSexResponse
                {
                    Sex = x.Sex?.GetDescription() ?? "未填写",
                    SexCount = x.SexCount,
                    SexPercent = Math.Round(x.SexPercent, 8, MidpointRounding.AwayFromZero)
                }).ToList();
                // 年龄概况
                List<DSPlatSeekerAgeResponse>? ageInfo = info.AgeInfo?.Select(x => new DSPlatSeekerAgeResponse
                {
                    AgeGroup = x.AgeGroup?.GetDescription() ?? "未填写",
                    AgeCount = x.AgeCount,
                    AgePercent = Math.Round(x.AgePercent, 8, MidpointRounding.AwayFromZero)
                }).ToList();
                // 学历概况
                List<DSPlatSeekerEducationResponse>? eduactionInfo = info.EducationInfo?.Select(x => new DSPlatSeekerEducationResponse
                {
                    Education = x.Education?.GetDescription() ?? "未填写",
                    EduCount = x.EduCount,
                    EduPercent = Math.Round(x.EduPercent, 8, MidpointRounding.AwayFromZero)
                }).ToList();
                // 用户注册来源
                List<UserSourceResponse>? registerResource = info.RegisterResource?
                    .Where(w => w.SourceName != RegisterSource.Web && w.SourceName != RegisterSource.Interviewer)// 产品说拿掉web跟面试官
                    .Select(s => new UserSourceResponse
                    {
                        SourceName = s.SourceName?.GetDescription(),
                        Quantity = s.Quantity
                    }).ToList();
                // 地区储备人才
                if (data.TryGetValue(RedisKey.DataScreen.ScreenUserRegionInfo, out var areaseekerInfo))
                {
                    var userAreaList = JsonConvert.DeserializeObject<List<DSPlatSeekerAreaInfo>>(areaseekerInfo);
                    if (userAreaList != null)
                    {
                        info.AreaInfos = userAreaList.Where(w => !string.IsNullOrWhiteSpace(w.City) && w.Province == "河北省")// 默认展示河北省
                        .GroupBy(g => g.City)
                        .Select(s => new DSPlatSeekerAreaInfo { City = s.Key!, Quantity = s.Sum(x => x.Quantity), OrderIndex = s.Sum(x => x.Quantity) })
                        .OrderByDescending(o => o.OrderIndex)
                        .ToList();
                    }
                }

                response = new DSPlatSeekerDataResponse
                {
                    LoginedNum = info.LoginedNum,
                    NewRegisterNum = info.NewRegisterNum,
                    RegisterResource = registerResource,
                    ResumeNum = info.ResumeNum,
                    TalentNum = info.TalentNum,
                    VisitNum = info.VisitNum,
                    VisiterNum = info.VisiterNum,
                    SexInfo = sexInfo,
                    EducationInfo = eduactionInfo,
                    AgeInfo = ageInfo,
                    IndustryTop5 = info.IndustryTop5,
                    HopePostTop5 = info.HopePostTop5,
                    AreaInfos = info.AreaInfos
                };
            }
        }
        return response;
    }

    [RedisCache(Expire = 30)]
    public DSPlatProjectDataResponse GetPlatformOfProjectData()
    {
        DSPlatProjectDataResponse response = new();
        // 从redis拿数据
        var data = MyRedis.Client.HGetAll<string>(RedisKey.DataScreen.ScreenProjectInfo);
        if (data is not null && data.Count > 0)
        {
            if (data.TryGetValue(RedisKey.DataScreen.ScreenProjectInfo, out var projectInfo))
            {
                var info = JsonConvert.DeserializeObject<DSPlatProjectDataInfo>(projectInfo);
                if (info == null)
                    return response;

                // 数据处理
                // 诺亚项目占比
                var noahProjectSumPercent = Math.Round(info.NoahProjectSumPercent, 4, MidpointRounding.AwayFromZero);
                // 平台项目占比
                var platProjectSumPercent = Math.Round(info.PlatProjectSumPercent, 4, MidpointRounding.AwayFromZero);
                // 第三方平台占比
                var otherProjectSumPercent = Math.Round(1 - noahProjectSumPercent - platProjectSumPercent, 4, MidpointRounding.AwayFromZero);
                // 项目概况处理
                var projectInfos = info.ProjectInfos?.Select(s => new ProjectResponse
                {
                    ProjectType = s.ProjectType?.ToString(),
                    ExecNum = s.ExecNum,
                    ExecNumPercent = Math.Round(s.ExecNumPercent, 4, MidpointRounding.AwayFromZero)
                }).ToList();
                // 项目行业概况
                var projectIndustryInfos = info.ProjectIndustryInfos?.Select(s => new ProjectIndustryResponse
                {
                    ProjectIndustry = s.ProjectIndustry?.ToString(),
                    IndustryExecNum = s.IndustryExecNum,
                    IndustryExecNumPercent = Math.Round(s.IndustryExecNumPercent, 4, MidpointRounding.AwayFromZero)
                }).ToList();
                // 项目地区概况
                info.ProjectAreaInfos?.ForEach(f =>
                {
                    f.AreaExecNumPercent = Math.Round(f.AreaExecNumPercent, 4, MidpointRounding.AwayFromZero);
                });

                response = new DSPlatProjectDataResponse
                {
                    ProjectSum = info.ProjectSum,
                    ProjectExecNum = info.ProjectExecNum,
                    ProjectArchivedNum = info.ProjectArchivedNum,
                    PlatProjectExecNum = info.PlatProjectExecNum,
                    PlatProjectArchivedNum = info.PlatProjectArchivedNum,
                    NoahProjectSum = info.NoahProjectSum,
                    NoahProjectSumPercent = noahProjectSumPercent,
                    PlatProjectSum = info.PlatProjectSum,
                    PlatProjectSumPercent = platProjectSumPercent,
                    OtherProjectSum = info.OtherProjectSum,
                    OtherProjectSumPercent = otherProjectSumPercent,
                    ProjectInfos = projectInfos,
                    ProjectIndustryInfos = projectIndustryInfos,
                    ProjectAreaInfos = info.ProjectAreaInfos
                };
            }
        }
        return response;
    }

    [RedisCache(Expire = 30)]
    public List<DSPlatSeekerAreaInfo> GetSeekerDataForAreaByAreaName(string? cityName, string? provinceName, int area = 0)
    {
        List<DSPlatSeekerAreaInfo> infos = new();
        // 从redis拿数据
        var userListStr = MyRedis.Client.HGet(RedisKey.DataScreen.ScreenSeekerInfo, RedisKey.DataScreen.ScreenUserRegionInfo);
        if (!string.IsNullOrWhiteSpace(userListStr))
        {
            var userinfos = JsonConvert.DeserializeObject<List<DSPlatSeekerAreaInfo>>(userListStr);
            if(userinfos != null && userinfos.Count > 0)
            {
                // 查全国，以省分组
                if (area == 1)
                {
                    infos = userinfos
                    .GroupBy(g => g.Province)
                    .OrderByDescending(o => o.Sum(s => s.Quantity))
                    .Select(s => new DSPlatSeekerAreaInfo { Province = provinceName, County = s.Key, Quantity = s.Sum(x => x.Quantity) })
                    .ToList();
                }
                // 查省，以地市分组
                else if (string.IsNullOrWhiteSpace(cityName) && !string.IsNullOrWhiteSpace(provinceName))
                {
                    infos = userinfos.Where(w => !string.IsNullOrWhiteSpace(w.City) && w.Province == provinceName)
                    .GroupBy(g => g.City)
                    .OrderByDescending(o => o.Sum(s => s.Quantity))
                    .Select(s => new DSPlatSeekerAreaInfo { Province = provinceName, City = s.Key!, Quantity = s.Sum(x => x.Quantity) })
                    .ToList();
                }
                // 查地市，以县区分组
                else if (!string.IsNullOrWhiteSpace(cityName))
                {
                    provinceName ??= "河北省";
                    infos = userinfos.Where(w => w.City == cityName && w.Province == provinceName )
                    .GroupBy(g => g.County)
                    .OrderByDescending(o => o.Sum(s => s.Quantity))
                    .Select(s => new DSPlatSeekerAreaInfo { Province = provinceName, City = cityName, County = s.Key, Quantity = s.Sum(x => x.Quantity) })
                    .ToList();
                }
            }
        }
        return infos;
    }

    /// <summary>
    /// 获取指定顾问的统计数据
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    [RedisCache(Expire = 30)]
    public DSHRDataResponse GetHRData(GetHRDataQuery model)
    {
        if (string.IsNullOrEmpty(model.UserId))
            throw new BadRequestException("顾问标识为空");

        var result = _context.User_Hr_Data.Where(x => x.UserId == model.UserId)
        .Select(s => new DSHRDataResponse
        {
            NickName = s.User_Hr.NickName,
            Avatar = s.User_Hr.Avatar,
            Post = s.User_Hr.Post,
            MyProjectTotal = s.MyProjectTotal,
            TeamProjectTotal = s.TeamProjectTotal,
            MyProjectOnline = s.MyProjectOnline,
            TeamProjectOnline = s.TeamProjectOnline,
            MyPostTotal = s.MyPostTotal,
            TeamPostTotal = s.TeamPostTotal,
            MyPostOnline = s.MyPostOnline,
            TeamPostOnline = s.TeamPostOnline,
            MyRecruitOnline = s.MyRecruitOnline,
            TeamRecruitOnline = s.TeamRecruitOnline,
            RecruitDelivery = s.RecruitDelivery,
            RecruitHrScreening = s.RecruitHrScreening,
            RecruitInterviewerScreening = s.RecruitInterviewerScreening,
            RecruitInterview = s.RecruitInterview,
            RecruitOffer = s.RecruitOffer,
            RecruitInduction = s.RecruitInduction,
            RecruitContract = s.RecruitContract,
            RecruitFileAway = s.RecruitFileAway,
            RealTalent = s.User_Hr.User_Num.Talent,
            Talent = s.User_Hr.User_Num.VirtualTalent
        }).FirstOrDefault();

        if (result == null)
            return new DSHRDataResponse() { DayVisits = new List<DayVisitsInfo>(), IndustryDistribute = new List<DistributeInfo>(), PostDistribute = new List<DistributeInfo>() };

        var hour24 = DateTime.Now.AddHours(-24);

        //之前统计的是协同报名，现在是主创报名，不能用之前redis的
        //有需要的其他key可以继续加，一起查
        var fullVistKey = $"{RedisKey.HrStatBehavior.Visits}{model.UserId}";
        var fullDeliveryKey = $"{RedisKey.HrStatBehavior.Delivery}{model.UserId}";
        var flNum = MyRedis.Client.HMGet<int>(RedisKey.HrStatBehavior.FullKey, fullVistKey, fullDeliveryKey);
        result.FullVisits = flNum[0];
        result.FullDelivery = flNum[1];

        //hr行为数据hashkey
        var hrCacheKey = $"{RedisKey.HrBehavior.Key}{model.UserId}";
        var hrbehavior = MyRedis.Client.HGet<List<PostDistribute>?>(hrCacheKey, RedisKey.HrBehavior.PostDistribute);
        if (hrbehavior == null)
            result.PostDistribute = new List<DistributeInfo>();
        else
            result.PostDistribute = hrbehavior.Select(x => new DistributeInfo { Name = x.Name, Value = x.Num }).ToList();

        //人才行业储备
        //  获取行业储备前9个排行
        var Industry = MyRedis.Client.ZRevRangeWithScores($"{RedisKey.HrBehavior.HrTalentIndustry}{model.UserId}", 0, 9);
        //  获取行业储备总量
        int IndustryTotal = MyRedis.Client.HGet<int>($"{RedisKey.HrBehavior.Key}{model.UserId}", RedisKey.HrBehavior.HrTalentIndustryNum);
        result.IndustryDistribute = Industry.Select(x => new DistributeInfo { Name = x.member, Value = x.score }).ToList();
        //  补充其他行业数量
        decimal IndustryOther = IndustryTotal - Industry.Sum(x => x.score);
        if (IndustryOther > 0)
            result.IndustryDistribute.Add(new DistributeInfo { Name = "其他", Value = IndustryOther });
        result.IndustryDistribute = result.IndustryDistribute.OrderBy(x => x.Value).ToList();
        ////今日报名（最近24小时），只查当天数据问题不大，后期优化
        //var cacheKey = $"hrdelivery:{model.UserId}";
        //result.TodayDelivery = _cacheHelper.GetRedisCache<int>(() => _context.Recruit.Count(x => x.HrId == model.UserId && x.CreatedTime > hour24), cacheKey, 10);

        //访问量
        var vistKey = $"{RedisKey.HrStatBehavior.Visits}{model.UserId}";
        var todayDeliveryKey = $"{RedisKey.HrStatBehavior.Delivery}{model.UserId}";
        string todayKey = $"{RedisKey.HrStatBehavior.DailyKey}{DateTime.Today.ToString("yyyyMMdd")}";
        var gzNum = MyRedis.Client.HMGet<int>(todayKey, vistKey, todayDeliveryKey);
        result.TodayVisits = gzNum[0];
        result.TodayDelivery = gzNum[1];
        //近7日访问量
        string DayFormat = "d日";
        result.DayVisits = _cacheHelper.GetRedisCache<List<DayVisitsInfo>>(() =>
        {
            DateTime day = DateTime.Today.AddDays(-1);
            using (var tran = MyRedis.Client.Multi())
            {
                tran.HGet<int>($"{RedisKey.HrStatBehavior.DailyKey}{day.ToString("yyyyMMdd")}", vistKey);
                tran.HGet<int>($"{RedisKey.HrStatBehavior.DailyKey}{day.AddDays(-1).ToString("yyyyMMdd")}", vistKey);
                tran.HGet<int>($"{RedisKey.HrStatBehavior.DailyKey}{day.AddDays(-2).ToString("yyyyMMdd")}", vistKey);
                tran.HGet<int>($"{RedisKey.HrStatBehavior.DailyKey}{day.AddDays(-3).ToString("yyyyMMdd")}", vistKey);
                tran.HGet<int>($"{RedisKey.HrStatBehavior.DailyKey}{day.AddDays(-4).ToString("yyyyMMdd")}", vistKey);
                tran.HGet<int>($"{RedisKey.HrStatBehavior.DailyKey}{day.AddDays(-5).ToString("yyyyMMdd")}", vistKey);

                object[] ret = tran.Exec();
                return new List<DayVisitsInfo>() {
                    new DayVisitsInfo{ Day=day.AddDays(-5).ToString(DayFormat),Visits=(int)ret[5]},
                    new DayVisitsInfo{ Day=day.AddDays(-4).ToString(DayFormat),Visits= (int)ret[4]},
                    new DayVisitsInfo{ Day=day.AddDays(-3).ToString(DayFormat),Visits= (int)ret[3]},
                    new DayVisitsInfo{ Day=day.AddDays(-2).ToString(DayFormat),Visits= (int)ret[2]},
                    new DayVisitsInfo{ Day=day.AddDays(-1).ToString(DayFormat),Visits= (int)ret[1]},
                    new DayVisitsInfo{ Day=day.ToString(DayFormat),Visits= (int)ret[0]}
                };
            }
        }, $"hrhisVisits:{model.UserId}", 10);
        result.DayVisits.Add(new DayVisitsInfo { Day = DateTime.Today.ToString(DayFormat), Visits = result.TodayVisits });

        return result;
    }

    /// <summary>
    /// 获取指定公司/部门统计数据
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    [RedisCache(Expire = 30)]
    public DSEntOrgDataResponse GetEntOrgData(GetEntOrgDataQuery model)
    {
        if (!(model.OrgId > 0))
            model.OrgId = 1;

        var predicate = PredicateBuilder.New<User_Hr_Data>(x => x.User_Hr.Status == UserStatus.Active);

        var ddOrgLevel = _context.Dd_Dept.Where(x => x.DdDeptId == model.OrgId)
        .Select(s => s.Level).FirstOrDefault() ?? string.Empty;

        predicate = predicate.And(x => x.User_Hr.User.Dd_User.Dd_User_Dept.Any(o => o.Dd_Dept.Level.StartsWith(ddOrgLevel)));

        IQueryable<User_Hr_Data> sql = _context.User_Hr_Data.Where(predicate);

        var result = sql.GroupBy(g => 1).Select(g => new DSEntOrgDataResponse
        {
            RecruitDelivery = g.Sum(s => s.RecruitDelivery),
            RecruitInterviewTotal = g.Sum(s => s.RecruitInterviewTotal),
            RecruitInductionTotal = g.Sum(s => s.RecruitInductionTotal),
            RealTalent = g.Sum(s => s.User_Hr.User_Num.Talent),
            VirtualTalent = g.Sum(s => s.User_Hr.User_Num.VirtualTalent),
            ProjectTotal = g.Sum(s => s.MyProjectTotal),
            ProjectOnline = g.Sum(s => s.MyProjectOnline),
            PostOnline = g.Sum(s => s.MyPostOnline),
            PostTotal = g.Sum(s => s.MyPostTotal),
            RecruitTotal = g.Sum(s => s.RecruitTotal),
            RecruitOnline = g.Sum(s => s.MyRecruitOnline),
            RecruitHrScreening = g.Sum(s => s.RecruitHrScreening),
            RecruitInterviewerScreening = g.Sum(s => s.RecruitInterviewerScreening),
            RecruitInterview = g.Sum(s => s.RecruitInterview),
            RecruitOffer = g.Sum(s => s.RecruitOffer),
            RecruitInduction = g.Sum(s => s.RecruitInduction),
            RecruitFileAway = g.Sum(s => s.RecruitFileAway)
        }).FirstOrDefault();

        if (result == null)
            result = new DSEntOrgDataResponse();

        result.TotalTalent = result.RealTalent + result.VirtualTalent;
        result.ProjectAway = result.ProjectTotal - result.ProjectOnline;
        result.Enrolling = result.RecruitOnline - result.RecruitInduction;
        result.Applicant = result.RecruitHrScreening + result.RecruitInterviewerScreening
            + result.RecruitInterview + result.RecruitInduction + result.RecruitOffer
            + result.RecruitFileAway;

        DateTime LastLoginTime = DateTime.Now.AddHours(-24);

        var baseCount = sql.Select(x => new
        {
            Online = x.User_Hr.User_Extend.LoginTime >= LastLoginTime,
            BindDingDing = !string.IsNullOrEmpty(x.User_Hr.User_DingDing.DingUserid),
            BindWechart = (!string.IsNullOrEmpty(x.User_Hr.WeChatNo) || !string.IsNullOrEmpty(x.User_Hr.EntWeChatQrCode))
        }).ToList();

        result.HRCount = baseCount.Count;
        result.HRDingDing = baseCount.Where(x => x.BindDingDing).Count();
        result.HRWechat = baseCount.Where(x => x.BindWechart).Count();
        result.HROnlineCount = baseCount.Where(x => x.Online).Count();

        result.HROnlineRatio = result.HRCount == 0 ? 0 : Math.Round(result.HROnlineCount * 1.0 / result.HRCount * 100, 0, MidpointRounding.AwayFromZero);

        return result;
    }

    /// <summary>
    /// 获取指定公司/部门协同统计数据
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    [RedisCache(Expire = 60)]
    public DSEntOrgTeamDataResponse GetEntOrgTeamData(GetEntOrgDataQuery model)
    {
        return new DSEntOrgTeamDataResponse();
    }
}
