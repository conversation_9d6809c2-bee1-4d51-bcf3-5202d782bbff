﻿using Newtonsoft.Json.Linq;

namespace Config.CommonModel.Tencent;

/// <summary>
/// IM回调参数
/// </summary>
public class ImCallbackRequestQuery
{
    /// <summary>
    /// App 在即时通信 IM 分配的应用标识
    /// </summary>
    public string? SdkAppid { get; set; }

    /// <summary>
    /// 回调命令字
    /// </summary>
    public string? CallbackCommand { get; set; }

    /// <summary>
    /// 可选，通常值为 JSON
    /// </summary>
    public string? contenttype { get; set; }

    /// <summary>
    /// 客户端 IP 地址
    /// </summary>
    public string? ClientIP { get; set; }

    /// <summary>
    /// 客户端平台，对应不同的平台类型，可能的取值有：RESTAPI（使用 REST API 发送请求）、Web（使用 Web SDK 发送请求）、Android、iOS、Windows、Mac、IPad、Unknown（使用未知类型的设备发送请求）
    /// </summary>
    public string? OptPlatform { get; set; }
}

/// <summary>
/// IM回调Body
/// </summary>
public class ImCallbackRequestBody
{
    /// <summary>
    /// 回调命令字
    /// </summary>
    public string? CallbackCommand { get; set; }
}

/// <summary>
/// 发单聊消息之后回调
/// </summary>
public class CallbackC2CAfterSendMsg : ImCallbackRequestBody
{
    /// <summary>
    /// 消息发送者 UserID
    /// </summary>
    public string? From_Account { get; set; }

    /// <summary>
    /// 消息接收者 UserID
    /// </summary>
    public string? To_Account { get; set; }

    /// <summary>
    /// 消息序列号，用于标记该条消息（32位无符号整数）
    /// </summary>
    public long? MsgSeq { get; set; }

    /// <summary>
    /// 消息随机数，用于标记该条消息（32位无符号整数）
    /// </summary>
    public long? MsgRandom { get; set; }

    /// <summary>
    /// 消息的发送时间戳，单位为秒，单聊消息优先使用 MsgTime 进行排序，同一秒发送的消息则按 MsgSeq 排序，MsgSeq 值越大消息越靠后
    /// </summary>
    public long? MsgTime { get; set; }

    /// <summary>
    /// 该条消息的唯一标识，可根据该标识进行 REST API 撤回单聊消息
    /// </summary>
    public string? MsgKey { get; set; }

    /// <summary>
    /// 在线消息，为1，否则为0
    /// </summary>
    public int? OnlineOnlyFlag { get; set; }

    /// <summary>
    /// 该条消息的发送结果，0表示发送成功，非0表示发送失败，具体可参见 错误码
    /// </summary>
    public int? SendMsgResult { get; set; }

    /// <summary>
    /// To_Account 未读的单聊消息总数量（包含所有的单聊会话）。若该条消息下发失败（例如被脏字过滤），该字段值为-1
    /// </summary>
    public int? UnreadMsgNum { get; set; }

    /// <summary>
    /// 消息体，具体参见 消息格式描述
    /// </summary>
    public List<SendImMsgBody>? MsgBody { get; set; }

    /// <summary>
    /// 消息自定义数据（云端保存，会发送到对端，程序卸载重装后还能拉取到）
    /// </summary>
    public string? CloudCustomData { get; set; }
}
public class CallbackC2CAfterMsgReadReport : ImCallbackRequestBody
{
    /// <summary>
    /// 已读上报方 UserID
    /// </summary>
    public string? Report_Account { get; set; }
    /// <summary>
    /// 会话对端 UserID
    /// </summary>
    public string? Peer_Account { get; set; }
    /// <summary>
    /// 已读时间(秒级别时间戳)
    /// </summary>
    public int? LastReadTime { get; set; }
    /// <summary>
    /// 事件触发的毫秒级别时间戳
    /// </summary>
    public int? EventTime { get; set; }
}
public class SendImMsgBody
{
    /// <summary>
    /// 消息元素类别；目前支持的消息对象包括：TIMTextElem(文本消息)，TIMLocationElem(位置消息)，TIMFaceElem(表情消息)，TIMCustomElem(自定义消息)，TIMSoundElem(语音消息)，TIMImageElem(图像消息)，TIMFileElem(文件消息)，TIMVideoFileElem(视频消息)。
    /// </summary>
    public string? MsgType { get; set; }

    /// <summary>
    /// 消息元素的内容，不同的 MsgType 有不同的 MsgContent 格式。
    /// </summary>
    public MsgContentBody? MsgContent { get; set; }
}

public class MsgContentBody
{
    public string? Text { get; set; }//文本消息内容
    public string? Desc { get; set; }//自定义消息描述信息
    public string? Data { get; set; }//自定义消息数据
    public string? Ext { get; set; }//扩展字段
    //public JToken? content { get; set; }
}