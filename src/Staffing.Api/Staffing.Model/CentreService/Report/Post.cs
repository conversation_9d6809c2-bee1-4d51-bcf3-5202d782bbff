using MiniExcelLibs.Attributes;

namespace Staffing.Model.CentreService.Report;

public class ExportNkpRecruitSummaryData
{
    [ExcelIgnore]
    public string? 职位Id { get; set; }
    public string? 项目名称 { get; set; }
    public string? 职位名称 { get; set; }
    public int 报名次数 { get; set; }
    public int Hr初筛 { get; set; }
    public int 面试官筛选 { get; set; }
    public int 面试 { get; set; }
    public int Offer { get; set; }
    public int 入职 { get; set; }
    public int 签约 { get; set; }
    public int 归档 { get; set; }
    public string? 客户名称 { get; set; }
    public string? 顾问 { get; set; }
    public string? 顾问电话 { get; set; }
    public string? 职位创建时间 { get; set; }
    public string? 项目状态 { get; set; }
    public string? 职位状态 { get; set; }
}