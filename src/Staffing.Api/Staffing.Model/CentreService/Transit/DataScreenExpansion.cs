﻿namespace Staffing.Model.CentreService.Transit
{
    /// <summary>
    /// 诺聘平台数据拓扑_从数字诺亚获取合同金额
    /// </summary>
    public class DataScreenExpansion_ContractAmount
    {
        /// <summary>
        /// 合同金额(单位：元)
        /// </summary>
        public decimal ContractAmount { get; set; }
    }

    /// <summary>
    /// 诺聘平台数据拓扑_从数字诺亚获取合同数量
    /// </summary>
    public class DataScreenExpansion_ContractCount
    {
        /// <summary>
        /// 合同数量
        /// </summary>
        public long ContractCount { get; set; }
    }

    /// <summary>
    /// 诺聘平台数据拓扑_从数字诺亚获取销售机会数量
    /// </summary>
    public class DataScreenExpansion_OpportunityCount
    {
        /// <summary>
        /// 销售机会数量
        /// </summary>
        public decimal OpportunityCount { get; set; }
    }

    /// <summary>
    /// 诺聘平台数据拓扑_从数字诺亚获取销售机会数量
    /// </summary>
    public class DataScreenExpansion_RecruitOpportunity
    {
        /// <summary>
        /// 销售机会数量
        /// </summary>
        public List<DataScreenExpansion_ChartItem> ChartData { get; set; } = new ();
    }
    /// <summary>
    /// 诺聘平台数据拓扑_统计图显示列表使用
    /// </summary>
    public class DataScreenExpansion_ChartItem
    {
        public string name { get; set; } = string.Empty;
        public int value { get; set; }
    }

    public class GetContractAmountBySponsorsRequest
    {
        public string Sponsors { get; set; } = string.Empty;
        public DateTime FirstTime { get; set; }
    }

    public class DataScreenExpansion_DDUser
    {
        public string Name { get; set; } = default!;
        public string JobNumber { get; set; } = default!;
    }
    public class DataScreenExpansion_TimeDetail
    {
        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }

        public string Name { get; set; } = default!;
        public decimal Value { get; set; }
    }
    public class DataScreenExpansion_TimeParam
    {
        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }
    }
    public class DataScreenExpansion_TimeParam_Depart: DataScreenExpansion_TimeParam
    {
        public string? DepartName { get; set; }
    }
}
