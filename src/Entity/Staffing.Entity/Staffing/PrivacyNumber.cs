﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 隐私号码
/// </summary>
[Table("privacy_number")]
public class PrivacyNumber
{
    /// <summary>
    ///  ID
    /// </summary>
    [Key]
    public long? id { get; set; }

    public string? fromId { get; set; }

    public PrivacyNumberFromType? fromType { get; set; }

    /// <summary>
    /// 电话号码
    /// </summary>
    public string? phone_no { get; set; }

    /// <summary>
    /// 城市
    /// </summary>
    public string? city { get; set; }

    /// <summary>
    /// 呼出时间
    /// </summary>
    public DateTime? call_out_time { get; set; }

    /// <summary>
    /// 响铃时间
    /// </summary>
    public DateTime? ring_time { get; set; }

    /// <summary>
    /// 录音 URL
    /// </summary>
    public string? record_url { get; set; }

    /// <summary>
    /// 响铃录音 URL
    /// </summary>
    public string? ring_record_url { get; set; }

    /// <summary>
    /// 自由响铃时间
    /// </summary>
    public DateTime? free_ring_time { get; set; }

    /// <summary>
    /// 控制消息
    /// </summary>
    public string? control_msg { get; set; }

    /// <summary>
    /// 密号
    /// </summary>
    public string? secret_no { get; set; }

    /// <summary>
    /// 呼叫类型
    /// </summary>
    public PrivacyNumberCallType? call_type { get; set; }

    /// <summary>
    /// 控制类型
    /// </summary>
    public string? control_type { get; set; }

    /// <summary>
    /// 释放时间
    /// </summary>
    public DateTime? release_time { get; set; }

    /// <summary>
    /// 池键
    /// </summary>
    public string? pool_key { get; set; }

    /// <summary>
    /// 子 ID
    /// </summary>
    public long? sub_id { get; set; }

    /// <summary>
    /// 未连接原因
    /// </summary>
    public PrivacyNumberUnconnectedCause? unconnected_cause { get; set; }

    /// <summary>
    /// 呼叫时间
    /// </summary>
    public DateTime? call_time { get; set; }

    /// <summary>
    /// 对方号码
    /// </summary>
    public string? peer_no { get; set; }

    /// <summary>
    /// 被叫显示号码
    /// </summary>
    public string? called_display_no { get; set; }

    /// <summary>
    /// 释放方向
    /// </summary>
    public PrivacyNumberReleaseDir? release_dir { get; set; }

    /// <summary>
    /// 呼叫 ID
    /// </summary>
    public string? call_id { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? start_time { get; set; }

    /// <summary>
    /// 合作伙伴键
    /// </summary>
    public string? partner_key { get; set; }

    /// <summary>
    /// 外部 ID
    /// </summary>
    public string? out_id { get; set; }

    /// <summary>
    /// 释放原因
    /// </summary>
    public int? release_cause { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}
