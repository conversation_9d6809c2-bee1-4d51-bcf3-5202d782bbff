using Config.Enums;

namespace Config.CommonModel.Business;

public class GetAppList
{
    /// <summary>
    /// 类别
    /// </summary>
    public List<GetAppsInfo> Category { get; set; } = default!;

    /// <summary>
    /// App
    /// </summary>
    public List<GetAppsInfo> Apps { get; set; } = default!;

    /// <summary>
    /// 最近使用
    /// </summary>
    public List<GetAppsInfo> Used { get; set; } = default!;
}

public class GetAppsInfo
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 父Id
    /// </summary>
    public string? ParentId { get; set; }

    /// <summary>
    /// 应用Id
    /// </summary>
    public string? AppId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 短概要
    /// </summary>
    public string? ShortAbstract { get; set; } = string.Empty;

    /// <summary>
    /// 图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// Url
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ActiveStatus Status { get; set; }

    /// <summary>
    /// 申请状态
    /// </summary>
    public AppApplyStatus ApplyStatus { get; set; } = AppApplyStatus.未申请;

    /// <summary>
    /// 权限,英文逗号分割的字符串，如果为空则是所有人都有权限
    /// 注意区分大小写
    /// </summary>
    public string? Powers { get; set; }
}

public class GetAppIdInfo
{
    public int Id { get; set; }
    public string AppId { get; set; } = default!;
    public string AppSecret { get; set; } = default!;
    public ClientType Type { get; set; } = default!;
    public string Describe { get; set; } = default!;
}

public class QuickJobData
{
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// 地区
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 小程序二维码
    /// </summary>
    public string? AppletQrCode { get; set; }

    /// <summary>
    /// B端小程序二维码
    /// </summary>
    public string? HrAppletQrCode { get; set; }

    /// <summary>
    /// C端小程序Id
    /// </summary>
    public string? AppletAppId { get; set; }

    /// <summary>
    /// app名称
    /// </summary>
    public string? AppName { get; set; }
}

public class GetQuickJobAppsInfo
{
    /// <summary>
    /// 零工市场数据
    /// </summary>
    public QuickJobData? QuickJobData { get; set; }

    public string? Name { get; set; }
}

public enum AppApplyStatus
{
    未申请, 审核中, 审核通过, 无法申请, 审核拒绝
}

public class AppApplyStatusTools
{
    public static AppApplyStatus UserStatusToAppApplyStatus(UserStatus? st)
    {
        return st switch
        {
            UserStatus.Active => AppApplyStatus.审核通过,
            UserStatus.Rejected => AppApplyStatus.审核拒绝,
            UserStatus.UnReviewed => AppApplyStatus.审核中,
            // UserStatus.Disabled => AppApplyStatus.无法申请,
            _ => AppApplyStatus.未申请
        };
    }
}