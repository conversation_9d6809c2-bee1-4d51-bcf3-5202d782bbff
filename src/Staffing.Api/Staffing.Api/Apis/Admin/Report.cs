using Config;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Staffing.Model.CentreService.Report;
using Staffing.Core.Interfaces.CentreService;
using MiniExcelLibs;
using Infrastructure.Extend;

namespace Staffing.Api.Apis.Admin;

[Service(ServiceLifetime.Transient)]
public class ReportRouter : IAdminRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("report").WithTags("报表");
        group.MapGet("/daily/user", ([AsParameters] GetDailyUser model, IReportService rep) => rep.GetDailyUser(model))
        .WithOpenApi(operation => new(operation) { Summary = "获取每日用户统计" });

        // group.MapGet("/daily/score", ([AsParameters] GetDailyScore model, IReportService rep) => rep.GetDailyScore(model))
        // .WithOpenApi(operation => new(operation) { Summary = "获取每日积分统计" });

        group.MapGet("/daily/channel", (string key, string channelId, IReportService rep) =>
        {
            if (key != "ovnajv1dimzpgs")
                return string.Empty;

            return rep.GetDailyChannel(channelId);
        })
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "获取渠道统计" });

        group.MapGet("/kzg/resume", ([AsParameters] ExportKzgResume model, IReportService rep) =>
        {
            if (!MyRedis.Client.SetNx("report/kzg/resume", 1, 3))
                throw new BadRequestException("操作频繁，限3秒一次");

            var result = rep.ExportKzgResume(model);

            var memoryStream = new MemoryStream();
            memoryStream.SaveAs(result);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return Results.File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"快招工简历.xlsx");
        })
        .WithOpenApi(operation => new(operation) { Summary = "快招工简历数据导出" });

        group.MapGet("/kzg/resumes", ([AsParameters] ExportKzgResume model, IReportService rep) =>
        {
            if (!MyRedis.Client.SetNx("report/kzg/resumes", 1, 3))
                throw new BadRequestException("操作频繁，限3秒一次");

            var result = rep.ExportKzgResumes(model);

            var memoryStream = new MemoryStream();
            memoryStream.SaveAs(result);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return Results.File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"快招工简历列表_{model.BeginTime}至{model.EndTime}.xlsx");
        })
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "快招工简历数据导出 - 无模板" });

        group.MapGet("/nkp/recruit", ([AsParameters] ExportRecruit model, IReportService rep) =>
        {
            if (model.Key != "ovnajv1dimzpgs")
                throw new BadRequestException("非法请求");

            if (!MyRedis.Client.SetNx("report/nkp/recruit", 1, 3))
                throw new BadRequestException("操作频繁，限3秒一次");

            var result = rep.ExportRecruit(model);

            var memoryStream = new MemoryStream();
            memoryStream.SaveAs(result);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return Results.File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"报名.xlsx");
        })
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "导出职位报名数据" });

        group.MapGet("/channel/seeker", ([AsParameters] GetChannelSeeker model, IReportService rep) => rep.GetChannelSeeker(model))
        .WithOpenApi(operation => new(operation) { Summary = "渠道看板" });

        group.MapGet("/nkp/summary/step", ([AsParameters] GetNkpSummary model, IReportService rep) => rep.GetNkpSummary(model))
        .WithOpenApi(operation => new(operation) { Summary = "诺快聘阶段转化率" });

        group.MapGet("/nkp/recruit/summary", ([AsParameters] ExportNkpRecruitSummary model, IReportService rep) =>
        {
            if (!MyRedis.Client.SetNx("report/nkp/recruit/summary", 1, 3))
                throw new BadRequestException("操作频繁，限3秒一次");

            var result = rep.ExportNkpRecruitSummary(model);

            var memoryStream = new MemoryStream();
            memoryStream.SaveAs(result);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return Results.File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"职位招聘概况.xlsx");
        })
        .WithOpenApi(operation => new(operation) { Summary = "职位招聘概况" });

        group.MapGet("/nkp/week/report", ([AsParameters] GetNkpWeekReport model, IReportService rep) =>
        {
            if (model.Key != "ovnajv1dimzpgs")
                throw new BadRequestException("非法请求");

            if (!MyRedis.Client.SetNx("report/week/report", 1, 3))
                throw new BadRequestException("操作频繁，限3秒一次");

            var result = rep.GetNkpWeekReport(model);

            var tplPath = Path.Combine(Directory.GetCurrentDirectory(), "TemplateFiles", "ProjectBoard", "诺快聘运营数据周报.xlsx");

            var memoryStream = new MemoryStream();
            memoryStream.SaveAsByTemplate(tplPath, result);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return Results.File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"诺快聘运营数据周报.xlsx");
        })
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "诺快聘运营数据周报" });
    }
}