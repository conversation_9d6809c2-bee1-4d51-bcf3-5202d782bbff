// using Config.CommonModel;
// using Staffing.Entity.Staffing;

// namespace Staffing.Model.Common.NScore;

// public class GetGoodsList : QueryRequest
// {
//     /// <summary>
//     /// 类型
//     /// </summary>
//     public NScoreGoodsType? Type { get; set; }
// }

// public class GetGoodsListResponse : QueryResponse
// {
//     /// <summary>
//     /// 商品列表
//     /// </summary>
//     public List<GoodsInfo>? Rows { get; set; }
// }

// public class GoodsInfo
// {
//     /// <summary>
//     /// 商品Id
//     /// </summary>
//     public string? Id { get; set; }

//     /// <summary>
//     /// 类型
//     /// </summary>
//     public NScoreGoodsType Type { get; set; }

//     /// <summary>
//     /// 商品名称
//     /// </summary>
//     public string Name { get; set; } = null!;

//     /// <summary>
//     /// 商品描述
//     /// </summary>
//     public string? Describe { get; set; }

//     /// <summary>
//     /// 所需积分
//     /// </summary>
//     public int Score { get; set; }

//     /// <summary>
//     /// 商品价值
//     /// </summary>
//     public decimal Money { get; set; }

//     /// <summary>
//     /// 缩略图
//     /// </summary>
//     public string? Thumbnail { get; set; }
// }