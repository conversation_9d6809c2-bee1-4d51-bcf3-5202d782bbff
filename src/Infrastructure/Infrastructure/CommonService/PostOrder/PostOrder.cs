﻿using Config;
using Config.CommonModel.ThirdTalentInfo;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.CommonInterface;
using Infrastructure.CommonService.Ndn;
using Infrastructure.CommonService.ShareProfit;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Infrastructure.CommonService;


public abstract class PostOrder<T, P> : IPostOrder<T, P> where T : IThirdTalentInfo where P : IProjectPostInfo
{
    // public abstract void PayFailed(string orderId, string description = "");

    // public abstract void PaySuccess(string orderId);

    // public abstract void PostStockIn(string orderId);
}

/// <summary>
/// 下单公共服务
/// </summary>
[TypeService(ServiceLifetime.Transient)]
public class CommonPostOrder : PostOrder<ThirdTalentInfo, ProjectPostInfo>
{
    protected IDbContextFactory<StaffingContext> _contextFactory;
    private readonly INdnService _ndnService;
    private readonly LogManager _log;

    public CommonPostOrder(IDbContextFactory<StaffingContext> contextFactory, INdnService ndnService,
    LogManager log)
    {
        _contextFactory = contextFactory;
        _ndnService = ndnService;
        _log = log;
    }

    // public override void PostStockIn(string OrderId)
    // {
    //     using var _context = _contextFactory.CreateDbContext();
    //     var order = _context.Project_Teambounty.FirstOrDefault(f => f.Id == OrderId);
    //     if (order == null)
    //         throw new BadRequestException($"订单不存在");

    //     // 加全局锁
    //     var lockerKey = $"{KuaishouConst.POST_ORDER_STOCK_CONST_LOCK_KEY_STR}:{order.PostId}";
    //     using var locker = MyRedis.Lock(lockerKey, 15).GetAwaiter().GetResult();
    //     if (locker == null)
    //         throw new BadRequestException("获取库存锁失败");

    //     var post = _context.Post.FirstOrDefault(f => f.PostId == order.PostId);
    //     if (post == null)
    //         throw new BadRequestException("职位不存在");
    //     if (order.IfStockOut == 1)
    //     {
    //         post.LeftStock += 1;

    //         // 创建入库明细
    //         _context.Post_Stock.Add(new Post_Stock
    //         {
    //             OrderId = OrderId,
    //             PostId = order.PostId,
    //             StockType = StockType.订单入库,
    //             StockNum = 1,
    //             LeftStock = post.LeftStock,
    //             CreatedTime = DateTime.Now
    //         });
    //     }
    //     _context.SaveChanges();
    //     _log.Info("TranStep_PostStockInCommit_2", $"step2", $"交付失败完成");
    // }

    // public override void PayFailed(string orderId, string description = "")
    // {
    //     using var _context = _contextFactory.CreateDbContext();
    //     var order = _context.Project_Teambounty.FirstOrDefault(f => f.Id == orderId);
    //     order!.Status = BountyStatus.交付失败;
    //     order!.Description = description;
    //     order!.UpdatedTime = DateTime.Now;
    //     _context.SaveChanges();
    // }

    // public override void PaySuccess(string orderId)
    // {
    //     using var _context = _contextFactory.CreateDbContext();
    //     var order = _context.Project_Teambounty.FirstOrDefault(f => f.Id == orderId);
    //     //交付成功修改为计费待确认，等待项目经理确认交付
    //     order!.Status = BountyStatus.计费待确认;
    //     order!.UpdatedTime = DateTime.Now;
    //     //CreateSettle(order);
    //     _log.Info("TranStep_PaySuccess_3", $"step1", $"交付成功-订单生成完成");

    //     // 大改版，暂不走数字诺亚
    //     // try
    //     // {
    //     //     // todo:交付成功，解冻金额(不走数字诺亚)
    //     //     if (order.SettlementActualMoney != null && order.SettlementActualMoney > 0)
    //     //     {
    //     //         var nuoId = _context.Project.Where(w => w.ProjectId == order.ProjectId).Select(s => s.NuoId).FirstOrDefault();
    //     //         // todo:判断NuoId
    //     //         if (!string.IsNullOrWhiteSpace(nuoId))
    //     //         {
    //     //             _ = _ndnService.FreezeBudget(new Config.CommonModel.Ndn.FreezeBudgetByPost
    //     //             {
    //     //                 projectCode = nuoId,
    //     //                 type = Config.CommonModel.Ndn.BudgetFreezeType.消费,
    //     //                 amount = order.SettlementActualMoney.Value,
    //     //                 postId = order.PostId,
    //     //                 HrId = order.HrId,
    //     //                 ProjectId = order.ProjectId
    //     //             }).GetAwaiter().GetResult();
    //     //         }
    //     //         _log.Info("TranStep_PaySuccess_3", $"step2", $"交付成功-消费完成");
    //     //     }
    //     // }
    //     // catch (Exception ex)
    //     // {
    //     //     order!.Status = BountyStatus.交付失败;
    //     //     order!.Description = ex.Message;
    //     // }

    //     _context.SaveChanges();
    //     _log.Info("TranStep_PaySuccess_3", $"step3", $"交付成功-全部完成");
    // }

    /// <summary>
    /// 获取协同佣金比例 - 百分比
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    public decimal GetTeamUserLevelRate(string userId)
    {
        return 0m;// todo:先固定0个点
    }

    /// <summary>
    /// 获取渠道商佣金比例 - 百分比
    /// </summary>
    /// <param name="channelHrId"></param>
    /// <returns></returns>
    public decimal GetChannelUserLevelRate(string? channelHrId)
    {
        return 0m;// todo:不知道渠道商比例怎么来
    }

    // /// <summary>
    // /// 订单金额计算
    // /// </summary>
    // /// <param name="orderMoney"></param>
    // /// <param name="teamHrId"></param>
    // /// <returns></returns>
    // public CalculateMoney CalculateSettleMoney(decimal orderMoney, string recruitId)
    // {
    //     // 规则大改
    //     // var platformSettlementRate = GetTeamUserLevelRate(teamHrid);
    //     // var platformSettlementMoney = Math.Round(orderMoney * (decimal)platformSettlementRate, 2, MidpointRounding.AwayFromZero);// 平台抽佣金额
    //     // var settlementMoney = orderMoney - platformSettlementMoney;// 协同应收金额 = 职位佣金 - 平台抽佣
    //     // var channelSettlementRate = GetChannelUserLevelRate(ChannelHrId);
    //     // var channelSettlementMoney = Math.Round(settlementMoney * (decimal)channelSettlementRate, 2, MidpointRounding.AwayFromZero);// 渠道商应收金额 = 协同应收金额 * 渠道佣金比例
    //     // var settlementActualMoney = settlementMoney - channelSettlementMoney;// 协同实收金额 = 协同应收金额 - 渠道商应收金额

    //     // 计算分佣
    //     using var _context = _contextFactory.CreateDbContext();

    //     var settings = _context.Sys_Settings.FirstOrDefault();

    //     if (settings == null)
    //         throw new BadRequestException("系统配置缺失");

    //     // 判断用户是否诺亚公司，采用不同的分佣规则
    //     var recruit = _context.Recruit.Where(f => f.RecruitId == recruitId)
    //     .Select(s => new
    //     {
    //         s.Post_Delivery.Post.IsSalesCommission,
    //         ClueEntSpecific = s.Post_Delivery.Post_Team.Project_Team.User_Hr.Enterprise.Specific,
    //         ManagerEntSpecific = s.User_Hr.Enterprise.Specific,
    //         SalesEntSpecific = s.Post_Delivery.Post.Project.SaleUser.Enterprise.Specific
    //     }).First();

    //     var managerIsNoah = recruit.ManagerEntSpecific?.Contains(EntSpecific.Noah) == true;
    //     var clueIsNoah = recruit.ClueEntSpecific?.Contains(EntSpecific.Noah) == true;
    //     var salesBounty = recruit.SalesEntSpecific?.Contains(EntSpecific.Noah) == true;

    //     BountyConfig? managerConfig;
    //     BountyConfig? clueConfig;
    //     BountyConfig? salesConfig;

    //     if (recruit.IsSalesCommission)
    //     {
    //         salesConfig = salesBounty ? settings.NoahFull : settings.PlatformFull;
    //         managerConfig = managerIsNoah ? settings.NoahFull : settings.PlatformFull;
    //         clueConfig = clueIsNoah ? settings.NoahFull : settings.PlatformFull;
    //     }
    //     else
    //     {
    //         salesConfig = salesBounty ? settings.NoahNoSale : settings.PlatformNoSale;
    //         managerConfig = managerIsNoah ? settings.NoahNoSale : settings.PlatformNoSale;
    //         clueConfig = clueIsNoah ? settings.NoahNoSale : settings.PlatformNoSale;
    //     }

    //     return new CalculateMoney
    //     {
    //         SalesBounty = (orderMoney * salesConfig!.Sales).ToFixed(2),
    //         ManagerBounty = (orderMoney * managerConfig!.Manager).ToFixed(2),
    //         ClueBounty = (orderMoney * clueConfig!.Clue).ToFixed(2),
    //         // FollowerBounty = (orderMoney * bountyConfig.Follower).ToFixed(2),
    //         // PlatformBounty = (orderMoney * bountyConfig.Platform).ToFixed(2),
    //         SalesRate = salesConfig.Sales,
    //         ManagerRate = managerConfig.Manager,
    //         ClueRate = clueConfig.Clue,
    //         // FollowerRate = bountyConfig.Follower,
    //         // PlatformRate = bountyConfig.Platform
    //     };
    // }
}

/// <summary>
/// 交付流程实现类
/// </summary>
[TypeService(ServiceLifetime.Scoped)]
public class PaymentStep : IPaymentStep<ThirdTalentInfo, ProjectPostInfo>
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly ProfitService _profitService;

    public PaymentStep(IDbContextFactory<StaffingContext> contextFactory, ProfitService profitService)
    {
        _contextFactory = contextFactory;
        _profitService = profitService;
    }

    public void ResumeCheck()
    {
        throw new NotImplementedException();
    }

    public void ResumeToSeeker(ThirdTalentInfo thirdTalentInfo, PostOrderExceptionMessage ex)
    {
        using var _context = _contextFactory.CreateDbContext();
        // 创建用户
        var user = _context.User.FirstOrDefault(f => f.Mobile == thirdTalentInfo.Phone);
        if (user == null)
        {
            user = new User { Mobile = thirdTalentInfo.Phone! };
            _context.Add(user);
            var userExt = new User_Extend { UserId = user.UserId };
            _context.Add(userExt);
            user.User_Num = new User_Num { UserId = user.UserId };
            _context.Add(user);
            var userBalance = new User_Balance { UserId = user.UserId };
            _context.Add(userBalance);
        }
        // 创建求职者
        var seeker = _context.User_Seeker.FirstOrDefault(x => x.UserId == user.UserId);
        if (seeker == null)
        {
            seeker = new User_Seeker
            {
                Source = thirdTalentInfo.Source,
                Status = UserStatus.Active,
                UserId = user.UserId,
                NickName = thirdTalentInfo.Name ?? string.Empty,
                RegionId = thirdTalentInfo.RegionId is null ? thirdTalentInfo.DataLocationCitys?.adCode ?? string.Empty : thirdTalentInfo.RegionId,
                Address = thirdTalentInfo.DataUserCity is null ? thirdTalentInfo.DataLocationCitys?.address : thirdTalentInfo.DataUserCity,
                Location = new NetTopologySuite.Geometries.Point(Convert.ToDouble(thirdTalentInfo.DataLocationCitys!.longitude), Convert.ToDouble(thirdTalentInfo.DataLocationCitys.latitude)),
                Avatar = thirdTalentInfo.Avatar ?? string.Empty
            };
            _context.Add(seeker);
            // 创seeker简历表
            var resume = new User_Resume
            {
                UserId = user.UserId,
                Sex = string.IsNullOrWhiteSpace(thirdTalentInfo.GenderName) ? null : (thirdTalentInfo.GenderName.Equals("男") ? Sex.男 : Sex.女),
                Show = true,// 是否开启
                Birthday = thirdTalentInfo.Birthday is null ? DateOnly.FromDateTime(Convert.ToDateTime($"{DateTime.Now.Year - thirdTalentInfo.Age}-01-01")) : thirdTalentInfo.Birthday,// 没有生日久倒推生日，默认月日为"-01-01"
                // todo: 以下后期要抽象出来，不同平台有不同平台的获取方式
                Occupation = null,
                Education = thirdTalentInfo.Education,
                School = thirdTalentInfo.TopSchool,
                GraduationDate = thirdTalentInfo.GraduationDate,
                Major = thirdTalentInfo.Major,
                Describe = thirdTalentInfo.Describe,
                EMail = thirdTalentInfo.Email,
                WeChatNo = thirdTalentInfo.WeChatNo,
                Qq = thirdTalentInfo.Qq,
                Skill = thirdTalentInfo.Skill
            };
            _context.Add(resume);
            thirdTalentInfo.newSeeker = true;
        }
        else// 更新求职者信息，以最新的为准，新的会覆盖旧的
        {
            if (_context.User_OpenId.FirstOrDefault(f => f.UserId == seeker.UserId) == null)// 如果有openid，则表示用户绑定微信，则不能修改
            {
                if (!string.IsNullOrWhiteSpace(thirdTalentInfo.Name))
                    seeker.NickName = thirdTalentInfo.Name;
                var resume = _context.User_Resume.FirstOrDefault(f => f.UserId == seeker.UserId);
                if (resume != null)
                {
                    resume.Sex = string.IsNullOrWhiteSpace(thirdTalentInfo.GenderName) ? null : (thirdTalentInfo.GenderName.Equals("男") ? Sex.男 : Sex.女);
                    resume.Birthday = thirdTalentInfo.Birthday is null ? DateOnly.FromDateTime(Convert.ToDateTime($"{DateTime.Now.Year - thirdTalentInfo.Age}-01-01")) : thirdTalentInfo.Birthday;// 倒推生日，默认月日为"-01-01"
                    resume.Education = thirdTalentInfo.Education;
                    resume.School = thirdTalentInfo.TopSchool;
                    resume.GraduationDate = thirdTalentInfo.GraduationDate;
                    resume.Major = thirdTalentInfo.Major;
                    resume.Describe = thirdTalentInfo.Describe;
                    resume.EMail = thirdTalentInfo.Email;
                    resume.WeChatNo = thirdTalentInfo.WeChatNo;
                    resume.Qq = thirdTalentInfo.Qq;
                    resume.Skill = thirdTalentInfo.Skill;
                    // 用户简历删除缓存
                    MyRedis.Client.SAdd(SubscriptionKey.OssUserIdKey, seeker.UserId);
                }
            }
        }
        thirdTalentInfo.SeekerId = seeker.UserId;
        _context.SaveChanges();
    }

    public void ToRecruit(ThirdTalentInfo seekerInfo, ProjectPostInfo postInfo, PostOrderExceptionMessage ex)
    {
        using var _context = _contextFactory.CreateDbContext();
        // 1.职位匹配校验
        // 重复投递校验
        if (_context.Recruit.Any(x => x.Post_Delivery.TeamPostId == postInfo.TeamPostId
        && x.SeekerId == seekerInfo.SeekerId && x.Status != RecruitStatus.FileAway))
            ex.ExceptionMessage!.Add("已投递该职位，请勿重复操作");
        // 该职位其他顾问是否投递
        else if (_context.Recruit.Any(x => x.Post_Delivery.PostId == postInfo.PostId
        && x.SeekerId == seekerInfo.SeekerId && x.Status != RecruitStatus.FileAway))
            ex.ExceptionMessage!.Add("已在其他顾问处投递该职位，请勿重复操作");

        // 2.基本信息校验
        if (seekerInfo.NotCheck == false)
        {
            // 性别校验
            if (postInfo.Sex.HasValue)
            {
                if (string.IsNullOrWhiteSpace(seekerInfo.GenderName))
                    ex.ExceptionMessage!.Add("性别不符合要求");
                else
                {
                    var sex = new Sex();
                    sex = seekerInfo.GenderName == "男" ? Sex.男 : Sex.女;
                    if (sex != postInfo.Sex)
                        ex.ExceptionMessage!.Add("性别不符合要求");
                }
            }
            // 学历校验
            if (postInfo.Education.HasValue)
            {
                if (!seekerInfo.Education.HasValue)
                    ex.ExceptionMessage!.Add("学历不符合要求");
                else if (postInfo.Education != EducationType.不限 && (seekerInfo.Education < postInfo.Education || seekerInfo.Education == EducationType.其他))
                    ex.ExceptionMessage!.Add("学历不符合要求");
            }
            // 毕业年限校验
            if (seekerInfo.GraduationDate != null)// todo: 二期把这个判断去掉
            {
                if (postInfo.WorkNature == PostWorkNature.实习 || postInfo.WorkNature == PostWorkNature.应届毕业生)
                {
                    if (postInfo.GraduationYear > 0 && seekerInfo.GraduationDate?.Year != postInfo.GraduationYear)
                        ex.ExceptionMessage!.Add("毕业年限不符合要求");
                }
            }
            // 年龄校验
            if (seekerInfo.Age < postInfo.MinAge || (postInfo.MaxAge > 0 && seekerInfo.Age > postInfo.MaxAge))
                ex.ExceptionMessage!.Add("年龄不符合要求");
        }

        if (ex.ExceptionMessage != null && ex.ExceptionMessage.Count > 0)
            return;

        // 投递表
        var postDelivery = new Post_Delivery
        {
            PostId = postInfo.PostId,
            TeamPostId = postInfo.TeamPostId,
            SeekerId = seekerInfo.SeekerId!,
            Status = 0,
            Source = 0,
            // Money = postInfo.Money,
            // PaymentNode = postInfo.PaymentNode,
            // PaymentDays = postInfo.PaymentDays
        };
        _context.Add(postDelivery);
        // 开启招聘流程
        //判断是否开启众包
        var projectTeamConfig = _context.Project_Team_Config.FirstOrDefault(r => r.Creator == postInfo.TeamHrId);
        var isClues = ProjectTeamIsClues.否;
        if (projectTeamConfig != null && projectTeamConfig.Status == 0)
        {
            isClues = ProjectTeamIsClues.是;
        }
        var recruit = new Recruit
        {
            DeliveryId = postDelivery.DeliveryId,
            ResumeBufferId = seekerInfo.ResumeBufferId,
            HrId = postInfo.HrId!,
            SeekerId = seekerInfo.SeekerId!,
            Status = RecruitStatus.HrScreening,
            PostName = postInfo.PostName ?? string.Empty,
            Type = RecruitType.Driving,
            UserClient = ClientType.Kzg,
            IsClues = isClues,
            ModeType = projectTeamConfig?.ModeType,
            ReceiverId = projectTeamConfig?.ReceiverId,

        };
        _context.Add(recruit);
        //产品要求修改为协同hrid
        var recruitRecord = new Recruit_Record
        {
            RecruitId = recruit.RecruitId,
            Status = RecruitStatus.HrScreening,
            Creator = postInfo.TeamHrId
        };
        seekerInfo.RecruitId = recruit.RecruitId;
        seekerInfo.RecruitRecordId = recruitRecord.Id;
        postInfo.RecruitId = recruit.RecruitId;
        postInfo.SeekerId = seekerInfo.SeekerId ?? string.Empty;
        _context.Add(recruitRecord);

        _profitService.CreateOrder(_context, new CreateOrderModel
        {
            Recruit = recruit,
            TeamPostId = postInfo.TeamPostId
        });

        _context.SaveChanges();
    }
}

/// <summary>
/// 交付流程
/// </summary>
public interface IPaymentStep<T1, T2> where T1 : IThirdTalentInfo where T2 : IProjectPostInfo
{
    /// <summary>
    /// 第三方简历筛选
    /// </summary>
    void ResumeCheck();

    /// <summary>
    /// 简历转诺快聘用户
    /// </summary>
    void ResumeToSeeker(T1 thirdTalentInfo, PostOrderExceptionMessage ex);

    /// <summary>
    /// 进入招聘流程
    /// </summary>
    void ToRecruit(T1 seekerInfo, T2 postInfo, PostOrderExceptionMessage ex);
}

/// <summary>
/// 金额计算类
/// </summary>
public class CalculateMoney
{
    /// <summary>
    /// 销售佣金
    /// </summary>
    public decimal SalesBounty { get; set; }

    /// <summary>
    /// 项目管理佣金
    /// </summary>
    public decimal ManagerBounty { get; set; }

    /// <summary>
    /// 线索佣金
    /// </summary>
    public decimal ClueBounty { get; set; }

    /// <summary>
    /// 邀面佣金
    /// </summary>
    public decimal FollowerBounty { get; set; }

    /// <summary>
    /// 平台佣金
    /// </summary>
    public decimal PlatformBounty { get; set; }

    /// <summary>
    /// 销售佣金比例
    /// </summary>
    public decimal SalesRate { get; set; }

    /// <summary>
    /// 项目管理佣金比例
    /// </summary>
    public decimal ManagerRate { get; set; }

    /// <summary>
    /// 线索佣金比例
    /// </summary>
    public decimal ClueRate { get; set; }

    /// <summary>
    /// 邀面佣金比例
    /// </summary>
    public decimal FollowerRate { get; set; }

    /// <summary>
    /// 平台佣金比例
    /// </summary>
    public decimal PlatformRate { get; set; }
}

