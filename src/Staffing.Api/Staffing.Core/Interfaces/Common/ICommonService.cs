﻿using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;
using LinqKit;
using Staffing.Entity.Staffing;
using Staffing.Model.Common.System;
using Staffing.Model.Hr.Dashboard;
using Staffing.Model.Service;

namespace Staffing.Core.Interfaces.Common;

public interface ICommonService
{
    /// <summary>
    /// 检测失败次数
    /// </summary>
    /// <param name="pwdType"></param>
    /// <param name="date"></param>
    /// <param name="key"></param>
    /// <returns></returns>
    bool CheckIdempotencyTimes(IdempotencyCheckType pwdType, string key);

    /// <summary>
    /// 失败增加次数
    /// </summary>
    /// <param name="pwdType"></param>
    /// <param name="key"></param>
    void CheckIdempotencyFaild(IdempotencyCheckType pwdType, string key);

    TokenInfo CreateRefreshToken(string userId, TokenType type, ClientType? client = null);
    Task<TokenInfo> RefreshToken(string refreshToken);

    GetIdVerificationResponse GetIdVerification(GetIdVerification model);

    /// <summary>
    /// 实名认证
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<IdVerificationResponse> IdVerification(IdVerificationModel model);

    /// <summary>
    /// 简历详情
    /// </summary>
    /// <param name="id"></param>
    /// <param name="checkShow"></param>
    /// <returns></returns>
    ResumeInfo GetResume(string id, bool checkShow);

    /// <summary>
    /// 常用语列表
    /// </summary>
    /// <returns></returns>
    GetMsgWordsResponse GetMsgWords(MsgWordsType type);

    /// <summary>
    /// 更新常用语
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse UpdateMsgWords(MsgWordsInfo model, MsgWordsType type);

    /// <summary>
    /// 删除常用语
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    EmptyResponse DeleteMsgWords(string id);

    /// <summary>
    /// 验证用户手机号
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse UserCheckMobile(UserCheckMobile model);

    /// <summary>
    /// 获取用户报名的所有职位
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetDeliveryPostsResponse GetDeliveryPosts(GetDeliveryPosts model);

    /// <summary>
    /// 获取已上架的广告列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    AdvertListResponse AdvertGetListByUse();

    /// <summary>
    /// 获取招聘流程列表
    /// </summary>
    /// <param name="model"></param>
    /// <param name="result"></param>
    /// <param name="predicate"></param>
    void GetRecruitList(GetRecentRecruits model, GetRecentRecruitsResponse result, ExpressionStarter<Recruit> predicate);
}