﻿using System.Data.Entity;
using Config.Enums;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.CentreService;
using Config;
using Staffing.Model.CentreService.Report;
using System.Text;
using Infrastructure.Aop;

namespace Staffing.Core.Services.CentreService;

[Service(ServiceLifetime.Transient)]
public class ReportService : IReportService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    public ReportService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, LogManager log)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _log = log;
    }

    public GetDailyUserResponse GetDailyUser(GetDailyUser model)
    {
        var result = new GetDailyUserResponse();

        var predicate = PredicateBuilder.New<Rpt_Daily_User>(true);

        var sql = _context.Rpt_Daily_User
        .Where(predicate);

        result.Total = sql.Select(s => new { a = 1 }).Count();

        result.Rows = sql
        .OrderByDescending(o => o.EventDate)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new DailyUserInfo
        {
            CreatedTime = s.CreatedTime,
            ActiveHr = s.ActiveHr,
            ActiveInterviewer = s.ActiveInterviewer,
            ActiveSeeker = s.ActiveSeeker,
            ActiveUser = s.ActiveUser,
            DeliverResume = s.DeliverResume,
            EventDate = s.EventDate,
            Id = s.Id,
            NewHr = s.NewHr,
            NewSeeker = s.NewSeeker,
            NewUser = s.NewUser
        }).ToList();

        return result;
    }

    public string GetDailyChannel(string channelId)
    {
        var sb = new StringBuilder();

        var chalAll = MyRedis.Client.HGetAll<int>($"{RedisKey.AppShow.ChannelAppShow}{channelId}");

        var visits = new List<ChannelReportInfo>();

        foreach (var item in chalAll)
        {
            visits.Add(new ChannelReportInfo
            {
                Date = item.Key,
                Visits = item.Value
            });
        }

        var regs = _context.Talent_Platform.Where(x => x.ChannelId == "11")
        .Select(s => new { s.CreatedTime, SeekerId = s.SeekerId ?? string.Empty })
        .GroupBy(g => g.SeekerId)
        .Select(s => new { s.Key, CreatedTime = s.Min(m => m.CreatedTime) })
        .GroupBy(g => g.CreatedTime.Date)
        .Select(s => new ChannelReportInfo { Date = s.Key.ToYYYY_MM_DD(), RegNum = s.Count() }).ToList();

        var fullDate = visits.Select(s => s.Date).Union(regs.Select(s => s.Date)).ToList();

        var fullData = new List<ChannelReportInfo>();
        foreach (var item in fullDate ?? new List<string?>())
        {
            fullData.Add(new ChannelReportInfo
            {
                Date = item,
                Visits = visits.FirstOrDefault(x => x.Date == item)?.Visits ?? 0,
                RegNum = regs.FirstOrDefault(x => x.Date == item)?.RegNum ?? 0
            });
        }

        fullData = fullData.OrderByDescending(o => o.Date).ToList();
        sb.AppendLine($"{"日期",-20}{"访问",-20}{"注册"}");

        foreach (var item in fullData)
            sb.AppendLine($"{item.Date + "　　",-20}{item.Visits + "　　",-20}{item.RegNum}");

        sb.AppendLine();
        sb.AppendLine($"{"合计",-20}{fullData.Sum(x => x.Visits) + "　　",-20}{((byte)fullData.Sum(x => x.RegNum)),-20}");

        return sb.ToString();
    }

    public object ExportKzgResume(ExportKzgResume model)
    {
        // var result = new ExportKzgResumeResponse();
        var predicate = PredicateBuilder.New<Kuaishou_Talent_Infos>(x => true);

        if (model.BeginTime.HasValue)
            predicate = predicate.And(x => x.CreatedTime >= model.BeginTime.Value);

        if (model.EndTime.HasValue)
        {
            model.EndTime = model.EndTime.Value.Date.AddDays(1);
            predicate = predicate.And(x => x.CreatedTime < model.EndTime.Value);
        }

        var linq = from ks in _context.Kuaishou_Talent_Infos.Where(predicate)
                   join ksre2 in _context.Kuaishou_Hr_Talent_Relations on ks.ApplicationId equals ksre2.ApplicationId into ksreD
                   from ksre in ksreD.DefaultIfEmpty()
                   join hr2 in _context.User_Hr on ksre.CreatedUser equals hr2.UserId into hrD
                   from hr in hrD.DefaultIfEmpty()
                   join rb2 in _context.Resume_Buffer on ksre.ApplicationId equals rb2.ResumeId into rbD
                   from rb in rbD.DefaultIfEmpty()
                   join recruit2 in _context.Recruit on rb.Id equals recruit2.ResumeBufferId into recruitD
                   from recruit in recruitD.DefaultIfEmpty()
                   where ks.PlatType == 0 && ks.RecommenderId == "2847149541"
                   orderby ks.CreatedTime descending
                   select new
                   {
                       姓名 = ks.Name,
                       手机号 = ks.Phone,
                       职位名称 = ks.JobName,
                       推送职位名称 = ks.SendTeamPostName,
                       推送职位时间 = ((DateTime?)rb.CreatedTime).ToYYYY_MM_DD_HH(),
                       来源主播昵称 = ks.Recommender,
                       申请时间 = ((DateTime?)ks.ApplyTime).ToYYYY_MM_DD_HH(),
                       企业名称 = ks.CompanyBusinessName,
                       回访情况 = ksre.ReturnVisit == ReturnVisit.已回访 ? "已回访" : "未回访",
                       回访描述 = ksre.VisitMemo,
                       回访人 = hr.NickName,
                       有效简历 = ksre.Result.GetDescription(),
                       招聘流程状态 = recruit.Status.GetDescription()
                   };

        // result.Rows = linq.ToList();
        return linq.ToList();
    }

    public object ExportRecruit(ExportRecruit model)
    {
        var predicate = PredicateBuilder.New<Recruit>(x => true);

        var postIds = model.PostIds?.Split(',').ToList();
        predicate = predicate.And(x => postIds.Contains(x.Post_Delivery.PostId));

        var result = _context.Recruit.Where(predicate)
        .OrderBy(o => o.Post_Delivery.PostId)
        .ThenByDescending(o => o.CreatedTime)
        .Select(s => new
        {
            职位 = s.Post_Delivery.Post.Name,
            姓名 = s.Post_Delivery.User_Seeker.NickName,
            电话 = s.Post_Delivery.User_Seeker.User.Mobile,
            报名时间 = s.CreatedTime.ToYYYY_MM_DD_HH(),
            招聘状态 = s.Status.GetDescription(),
            性别 = s.Post_Delivery.User_Seeker.User_Resume.Sex,
            年龄 = Tools.GetAgeByBirthdate(s.Post_Delivery.User_Seeker.User_Resume.Birthday),
            学历 = s.Post_Delivery.User_Seeker.User_Resume.Education.GetDescription(),
            学校 = s.Post_Delivery.User_Seeker.User_Resume.School,
            业务顾问 = s.Post_Delivery.Post_Team.Project_Team.User_Hr.NickName,
            职业 = s.Post_Delivery.User_Seeker.User_Resume.Occupation.GetDescription()
        });

        // result.Rows = linq.ToList();
        return result.ToList();
    }

    public string GetChannelSeeker(GetChannelSeeker model)
    {
        //腾讯投流运营数据字段：C端注册数量，C端报名数量（有效线索），C端转化数据（筛选、面试、入职、归档），价值统计（单个项目入职人数*项目分润  ---求和）

        var channel = _context.Qd_Hr_Channel.Where(x => x.User_Hr.User.Mobile == model.HrMobile
        && x.User_Seeker.User.Mobile == model.ChannelMobile).Select(s => new
        {
            ChannelId = s.Id,
            ChannelName = string.IsNullOrEmpty(s.User_Seeker.NickName) ? model.ChannelMobile : s.User_Seeker.NickName,
            HrName = string.IsNullOrEmpty(s.User_Hr.NickName) ? model.HrMobile : s.User_Hr.NickName
        }).FirstOrDefault();

        if (channel == null)
            return "渠道关系不存在";

        var C端注册 = _context.Talent_Platform.Where(x => x.ChannelId == channel.ChannelId).Count();
        var 报名次数 = _context.Post_Delivery.Where(x => x.ChannelId == channel.ChannelId).Count();
        var 报名人数 = _context.Post_Delivery.Where(x => x.ChannelId == channel.ChannelId).Select(s => s.SeekerId).Distinct().Count();

        var 招聘状态 = _context.Recruit.Where(x => x.Post_Delivery.ChannelId == channel.ChannelId)
        .GroupBy(s => s.Status).Select(s => new { s.Key, Ct = s.Count() });

        var 入职人数 = _context.Recruit.Where(x => x.Post_Delivery.ChannelId == channel.ChannelId
        && x.Status == RecruitStatus.Induction).Count();

        var 交付成功 = _context.Post_Bounty.Where(x => x.Recruit.Post_Delivery.ChannelId == channel.ChannelId
        && x.Post_Bounty_Stage.Any(a => a.GuaranteeStatus == GuaranteeStatus.过保)).GroupBy(g => 1)
        .Select(s => new
        {
            协同结算金额 = s.Sum(c => c.Money * c.ClueRate),
            渠道商结算金额 = 0
        }).FirstOrDefault();

        var sb = new StringBuilder();
        sb.AppendLine($"顾问:<{channel.HrName},渠道:<{channel.ChannelName}>>");

        sb.AppendLine($"C端注册:{C端注册}");
        sb.AppendLine($"报名人数:{报名人数}");
        sb.AppendLine($"报名次数:{报名次数}");
        sb.AppendLine($"入职人数:{入职人数}");
        sb.AppendLine($"协同结算金额:{交付成功?.协同结算金额}");
        sb.AppendLine($"渠道商结算金额:{交付成功?.渠道商结算金额}");
        sb.AppendLine($"招聘流程状态:");
        foreach (var item in 招聘状态)
        {
            sb.AppendLine($"{item.Key.GetDescription()}:{item.Ct}");
        }

        return sb.ToString();
    }

    public object ExportKzgResumes(ExportKzgResume model)
    {
        var predicate = PredicateBuilder.New<Kuaishou_Talent_Infos>(x => true);

        if (model.BeginTime.HasValue)
            predicate = predicate.And(x => x.CreatedTime >= model.BeginTime.Value);

        if (model.EndTime.HasValue)
        {
            model.EndTime = model.EndTime.Value.Date.AddDays(1);
            predicate = predicate.And(x => x.CreatedTime < model.EndTime.Value);
        }

        var linq = from ks in _context.Kuaishou_Talent_Infos.Where(predicate)
                   join ksre2 in _context.Kuaishou_Hr_Talent_Relations on ks.ApplicationId equals ksre2.ApplicationId into ksreD
                   from ksre in ksreD.DefaultIfEmpty()
                   join hr2 in _context.User_Hr on ksre.CreatedUser equals hr2.UserId into hrD
                   from hr in hrD.DefaultIfEmpty()
                   join rb2 in _context.Resume_Buffer on ksre.ApplicationId equals rb2.ResumeId into rbD
                   from rb in rbD.DefaultIfEmpty()
                   join recruit2 in _context.Recruit on rb.Id equals recruit2.ResumeBufferId into recruitD
                   from recruit in recruitD.DefaultIfEmpty()
                   where ks.PlatType == 0 && ks.RecommenderId == "2847149541"
                   orderby ks.CreatedTime descending
                   select new
                   {
                       姓名 = ks.Name,
                       电话号 = ks.Phone,
                       投递职位名称 = ks.JobName,
                       推送职位名称 = ks.SendTeamPostName,
                       推送时间 = ((DateTime?)rb.CreatedTime).ToYYYY_MM_DD_HH(),
                       渠道来源 = ks.Recommender,
                       投递时间 = ((DateTime?)ks.ApplyTime).ToYYYY_MM_DD_HH(),
                       投递企业 = ks.CompanyBusinessName,
                       回访状态 = ksre.ReturnVisit == ReturnVisit.已回访 ? "已回访" : "未回访",
                       回访描述 = ksre.VisitMemo,
                       回访人 = hr.NickName,
                       有效状态 = ksre.Result.GetDescription(),
                       招聘流程状态 = recruit.Status.GetDescription()
                   };

        // result.Rows = linq.ToList();
        return linq.ToList();
    }

    public string GetNkpSummary(GetNkpSummary model)
    {
        var predicate = PredicateBuilder.New<User_Seeker>(x => true);
        var predicate2 = PredicateBuilder.New<Post_Delivery>(x => true);

        if (model.BeginTime.HasValue)
        {
            predicate = predicate.And(x => x.CreatedTime >= model.BeginTime.Value);
            predicate2 = predicate2.And(x => x.User_Seeker.CreatedTime >= model.BeginTime.Value);
        }

        if (model.EndTime.HasValue)
        {
            model.EndTime = model.EndTime.Value.Date.AddDays(1);
            predicate = predicate.And(x => x.CreatedTime < model.EndTime.Value);
            predicate2 = predicate2.And(x => x.User_Seeker.CreatedTime < model.EndTime.Value);
        }

        var seeker = _context.User_Seeker.Count(predicate);
        var deliver = _context.Post_Delivery.Where(predicate2).Select(s => s.SeekerId).Distinct().Count();

        var sb = new StringBuilder();
        sb.AppendLine($"c端注册人数:{seeker}");
        sb.AppendLine($"投递人数（以上注册人数的投递）:{deliver}");
        sb.AppendLine($"转化率:{(seeker > 0 ? ((decimal)deliver / seeker).ToFixed(3) : 0)}");

        return sb.ToString();
    }

    public object ExportNkpRecruitSummary(ExportNkpRecruitSummary model)
    {
        var recruit = _context.Recruit.GroupBy(g => new { g.Post_Delivery.PostId, g.Status })
        .Select(s => new
        {
            s.Key.PostId,
            s.Key.Status,
            Ct = s.Count()
        }).ToList();

        var posts = _context.Post
        .Select(s => new ExportNkpRecruitSummaryData
        {
            职位Id = s.PostId,
            项目名称 = "",
            职位名称 = s.Name,
            客户名称 = s.Project.Agent_Ent.Name,
            顾问 = s.Project.User_Hr.NickName,
            顾问电话 = s.Project.User_Hr.User.Mobile,
            职位创建时间 = s.CreatedTime.ToYYYY_MM_DD_HH(),
            项目状态 = s.Project.Status.GetDescription(),
            职位状态 = s.Status.GetDescription()
        }).ToList();

        foreach (var item in posts)
        {
            item.报名次数 = recruit.Where(x => x.PostId == item.职位Id).Sum(s => s.Ct);
            item.Hr初筛 = recruit.Where(x => x.PostId == item.职位Id && x.Status == RecruitStatus.HrScreening).Sum(s => s.Ct);
            item.面试官筛选 = recruit.Where(x => x.PostId == item.职位Id && x.Status == RecruitStatus.InterviewerScreening).Sum(s => s.Ct);
            item.面试 = recruit.Where(x => x.PostId == item.职位Id && x.Status == RecruitStatus.Interview).Sum(s => s.Ct);
            item.Offer = recruit.Where(x => x.PostId == item.职位Id && x.Status == RecruitStatus.Offer).Sum(s => s.Ct);
            item.入职 = recruit.Where(x => x.PostId == item.职位Id && x.Status == RecruitStatus.Induction).Sum(s => s.Ct);
            item.签约 = recruit.Where(x => x.PostId == item.职位Id && x.Status == RecruitStatus.Contract).Sum(s => s.Ct);
            item.归档 = recruit.Where(x => x.PostId == item.职位Id && x.Status == RecruitStatus.FileAway).Sum(s => s.Ct);
        }

        return posts;
    }

    public GetNkpWeekReportResponse GetNkpWeekReport(GetNkpWeekReport model)
    {
        // var now = DateTime.Today;
        // var week = Convert.ToInt32(now.DayOfWeek);
        // week = week == 0 ? 7 : week;

        // //星期一
        // var beginTime = now.AddDays(1 - week);

        // if (model.LastWeek)
        //     beginTime = beginTime.AddDays(-7);

        var beginTime = DateTime.Today.AddDays(-7);
        var endTime = Constants.DefaultFutureTime;

        if (model.BeginTime.HasValue)
            beginTime = model.BeginTime.Value.Date;

        if (model.EndTime.HasValue)
            endTime = model.EndTime.Value.Date.AddDays(1);

        if (endTime > DateTime.Now)
            endTime = DateTime.Today.AddDays(1);

        var result = new GetNkpWeekReportResponse();

        var doBegin = DateOnly.FromDateTime(beginTime);
        var doEnd = DateOnly.FromDateTime(endTime);
        var weekSeeker = _context.Rpt_Daily_User.Where(x => x.EventDate >= doBegin && x.EventDate < doEnd).ToList();

        var 报名 = _context.Post_Delivery.Where(x => x.CreatedTime >= beginTime && x.CreatedTime < endTime)
        .GroupBy(g => g.CreatedTime.Date)
        .Select(s => new
        {
            s.Key,
            Ct = s.Count()
        }).ToList();

        var 面试 = _context.Recruit_Record.Where(x => x.CreatedTime >= beginTime && x.CreatedTime < endTime && x.Status == RecruitStatus.Interview)
        .GroupBy(g => g.Recruit)
        .Select(s => new
        {
            s.Key,
            Dt = s.Max(m => m.CreatedTime)
        }).GroupBy(g => g.Dt.Date)
        .Select(s => new
        {
            s.Key,
            Ct = s.Count()
        }).ToList();

        var 入职 = _context.Recruit_Record.Where(x => x.CreatedTime >= beginTime && x.CreatedTime < endTime && x.Status == RecruitStatus.Induction)
        .GroupBy(g => g.Recruit)
        .Select(s => new
        {
            s.Key,
            Dt = s.Max(m => m.CreatedTime)
        }).GroupBy(g => g.Dt.Date)
        .Select(s => new
        {
            s.Key,
            Ct = s.Count()
        }).ToList();

        var bt = beginTime;
        while (bt < endTime)
        {
            var weekSeeker1 = weekSeeker.FirstOrDefault(x => x.EventDate == DateOnly.FromDateTime(bt));
            var week1 = new GetNkpWeekReportWeek1
            {
                Day = bt.ToYYYY_MM_DD(),
                Week = string.Empty,
                注册量 = weekSeeker1?.NewSeeker ?? 0,
                报名量 = 报名.FirstOrDefault(x => x.Key == bt)?.Ct ?? 0,
                面试量 = 面试.FirstOrDefault(x => x.Key == bt)?.Ct ?? 0,
                入职量 = 入职.FirstOrDefault(x => x.Key == bt)?.Ct ?? 0
            };

            var week2 = new GetNkpWeekReportWeek2
            {
                Day = bt.ToYYYY_MM_DD(),
                Week = string.Empty,
                用户登录量 = weekSeeker1?.ActiveSeeker ?? 0,
                顾问登录量 = weekSeeker1?.ActiveHr ?? 0
            };
            result.Week.Add(week1);
            result.Week2.Add(week2);

            bt = bt.AddDays(1);
        }

        result.项目运行数 = _context.Post.Where(x => x.Status == PostStatus.发布中 && x.Project.Status == ProjectStatus.已上线).Select(s => s.ProjectId).Distinct().Count();
        result.在招岗位 = _context.Post.Count(x => x.Status == PostStatus.发布中 && x.Project.Status == ProjectStatus.已上线);
        result.需求人数 = _context.Post.Where(x => x.Status == PostStatus.发布中 && x.Project.Status == ProjectStatus.已上线).Sum(s => s.DeliveryNumber);
        result.入职人数 = _context.Recruit.Count(x => x.Post_Delivery.Post.Status == PostStatus.发布中 && x.Post_Delivery.Post.Project.Status == ProjectStatus.已上线 && x.Status == RecruitStatus.Induction);
        result.实缺人数 = result.需求人数 - result.入职人数;

        result.平台用户储备 = _context.User_Seeker.Count();
        result.平台简历储备 = _context.Talent_Virtual.Count();

        var reg = _context.User_Seeker.GroupBy(g => g.Source)
        .Select(s => new
        {
            s.Key,
            Ct = s.Count()
        }).ToList();

        result.微信注册人数 = reg.FirstOrDefault(x => x.Key == RegisterSource.Applet)?.Ct ?? 0;
        result.快招工注册人数 = reg.FirstOrDefault(x => x.Key == RegisterSource.KsTalent)?.Ct ?? 0;
        result.诺聘注册人数 = reg.FirstOrDefault(x => x.Key == RegisterSource.NuoPin)?.Ct ?? 0;
        result.快手注册人数 = reg.FirstOrDefault(x => x.Key == RegisterSource.KsApplet)?.Ct ?? 0;
        result.零工市场注册 = reg.FirstOrDefault(x => x.Key == RegisterSource.JobMarket)?.Ct ?? 0;

        return result;
    }
}