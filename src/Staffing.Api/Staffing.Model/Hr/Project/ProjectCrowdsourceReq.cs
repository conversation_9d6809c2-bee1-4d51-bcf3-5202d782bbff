using Config.Enums;
using Staffing.Entity.Staffing;

namespace Staffing.Model.Hr.Project;
/// <summary>
/// 项目众包req
/// </summary>
public class ProjectCrowdsourceReq
{
    // /// <summary>
    // /// 协调项目表id
    // /// </summary>
    // public string TeamProjectId { get; set; } = default!;
    
    /// <summary>
    /// 类别
    /// </summary>
    public ProjectTeamConfigType ModeType { get; set; }
    
    /// <summary>
    /// 接收人id
    /// </summary>
    public string? ReceiverId { get; set; }
    /// <summary>
    /// 0开启 1关闭
    /// </summary>
    public int? Status { get; set; }


}



public class ProjectCrowdsourceResp
{
    /// <summary>
    /// 协调项目表id
    /// </summary>
    public string TeamProjectId { get; set; } = default!;
    
    /// <summary>
    /// 类别
    /// </summary>
    public ProjectTeamConfigType ModeType { get; set; }
    
    /// <summary>
    /// 接收人id
    /// </summary>
    public string? ReceiverId { get; set; }
    /// <summary>
    /// 0开启 1关闭
    /// </summary>
    public int? Status { get; set; }
    
}

public class NoahBookResp
{
    /// <summary>
    /// id
    /// </summary>
    public string Id { get; set; } = default!;
    /// <summary>
    /// 账套ID
    /// </summary>
    public string BookCode { get; set; } = default!;

    /// <summary>
    /// 账套名称
    /// </summary>
    public string BookName { get; set; } = default!;
    
    
}



