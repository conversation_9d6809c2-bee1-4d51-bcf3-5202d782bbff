﻿using Config.CommonModel.DataScreen;
using Staffing.Model.Hr.DataScreen;

namespace Staffing.Core.Interfaces.Hr;

public interface IDataScreenService
{
    /// <summary>
    /// 获取平台级别统计数据
    /// </summary>
    DSPlatDataResponse GetPlatformData();

    // /// <summary>
    // /// 平台按钉钉组织架构获取统计数据
    // /// </summary>
    // DSDingDingDataInfoResponse GetPlatDingDingData();

    /// <summary>
    /// 获取指定顾问的统计数据
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    DSHRDataResponse GetHRData(GetHRDataQuery model);

    /// <summary>
    /// 获取指定公司/部门统计数据
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    DSEntOrgDataResponse GetEntOrgData(GetEntOrgDataQuery model);

    /// <summary>
    /// 获取指定公司/部门协同统计数据
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    DSEntOrgTeamDataResponse GetEntOrgTeamData(GetEntOrgDataQuery model);

    /// <summary>
    /// 获取 平台级-招聘流程 统计数据
    /// </summary>
    /// <returns></returns>
    DSPlatRecruitDataResponse GetPlatformOfRecruitData();

    /// <summary>
    /// 获取 平台级-用户概况 统计数据
    /// </summary>
    /// <returns></returns>
    DSPlatSeekerDataResponse GetPlatformOfSeekerData();

    /// <summary>
    /// 获取 平台级-项目看板 统计数据
    /// </summary>
    /// <returns></returns>
    DSPlatProjectDataResponse GetPlatformOfProjectData();

    /// <summary>
    /// 根据地区名称（国标）获取相应下级地区人才储备数据
    /// </summary>
    /// <param name="areaname"></param>
    /// <returns></returns>
    List<DSPlatSeekerAreaInfo> GetSeekerDataForAreaByAreaName(string? areaname, string? province, int area = 0);
}
