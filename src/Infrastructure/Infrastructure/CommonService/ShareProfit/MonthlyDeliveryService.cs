﻿// using Config.CommonModel.Recruit;
// using Config.CommonModel.ThirdTalentInfo;
// using Config.Enums;

// namespace Infrastructure.CommonService.ShareProfit;

// /// <summary>
// /// 按月交付场景
// /// </summary>
// public class MonthlyDeliveryService : CommonDeliveryService
// {
//     public MonthlyDeliveryService(ProjectPostInfo projectPostInfo, RecruitInfos recruit, CommonPostOrder orderService)
//         : base(projectPostInfo, recruit, orderService)
//     {
//     }

//     public override void Induction(string orderId)
//     {
//         // 减库存
//         _orderService.PostStockOut(orderId);
//     }

//     public override void FileAway(string orderId)
//     {
//         if(_projectPostInfo.Status == BountyStatus.交付中)
//         {
//             // 交付失败
//             _orderService.PayFailed(orderId, _recruit.InvalidReason ?? "交付中归档");
//             // 加库存
//             _orderService.PostStockIn(orderId);
//         }
//     }
// }
