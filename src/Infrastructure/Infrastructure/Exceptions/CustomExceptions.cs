﻿namespace Infrastructure.Exceptions;

public class NotFoundException : CustomException
{
    public NotFoundException(string? message, string errorCode = "") : base(message, errorCode) { }
    public NotFoundException(string? message, Exception inner) : base(message, inner) { }
}

public class BadRequestException : CustomException
{
    public BadRequestException(string? message, string errorCode = "") : base(message, errorCode)
    {
    }
    public BadRequestException(string? message, Exception inner) : base(message, inner) { }
}

public class ForbiddenException : CustomException
{
    public ForbiddenException(string? message, string errorCode = "") : base(message, errorCode) { }
    public ForbiddenException(string? message, Exception inner) : base(message, inner) { }
}

public class UnauthorizedException : CustomException
{
    public UnauthorizedException(string? message, string errorCode = "") : base(message, errorCode) { }
    public UnauthorizedException(string? message, Exception inner) : base(message, inner) { }
}

public class ThirdPartyServerException : CustomException
{
    public ThirdPartyServerException(string? message, string errorCode = "") : base(message, errorCode) { }
    public ThirdPartyServerException(string? message, Exception inner) : base(message, inner) { }
}

public class InternalServerException : CustomException
{
    public InternalServerException(string? message, string errorCode = "") : base(message, errorCode) { }
    public InternalServerException(string? message, Exception inner) : base(message, inner) { }
}

public class NotAcceptableException : CustomException
{
    public NotAcceptableException(string? message, string errorCode = "") : base(message, errorCode) { }
    public NotAcceptableException(string? message, Exception inner) : base(message, inner) { }
}

public class CustomException : Exception
{
    public string ErrorCode { get; set; }
    public CustomException(string? message, string errorCode = "") : base(message)
    {
        ErrorCode = errorCode;
    }
    public CustomException(string? message, Exception inner) : base(message, inner)
    {
        ErrorCode = string.Empty;
    }
}