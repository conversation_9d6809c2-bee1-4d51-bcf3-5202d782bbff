using Staffing.Core.Interfaces.Hr;
using Staffing.Model.Hr.Recruit;

namespace Staffing.Api.Apis.Hr;
using Infrastructure.Extend;
[Service(ServiceLifetime.Transient)]
public class Clue: IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("clue").WithTags("线索");
        group.MapPost("/hall", (CluesHallReq request, IRecruitService rep) => rep.GetCluesHallList(request))
            .WithOpenApi(operation => new(operation) { Summary = "线索大厅列表" });
        group.MapPost("/acceptOrder", (ClueAcceptOrderReq request, IRecruitService rep) => rep.AcceptOrder(request))
            .WithOpenApi(operation => new(operation) { Summary = "线索大厅接单" });
        group.MapGet("/getClueTags", (IRecruitService rep) => rep.GetClueTags())
            .WithOpenApi(operation => new(operation) { Summary = "线索大厅获取历史项目评价字典" });
    }

}