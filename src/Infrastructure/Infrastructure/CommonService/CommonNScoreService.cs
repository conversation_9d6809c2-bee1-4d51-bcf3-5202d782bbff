using Config;
using Config.CommonModel;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Infrastructure.CommonService;

[Service(ServiceLifetime.Transient)]
public class CommonNScoreService
{
    private IDbContextFactory<StaffingContext> _staffingContextFactory;
    private ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly TencentImHelper _tencentImHelper;

    public CommonNScoreService(IDbContextFactory<StaffingContext> staffingContextFactory, IOptionsSnapshot<ConfigManager> config,
        IHostEnvironment hostingEnvironment, LogManager log, TencentImHelper tencentImHelper)
    {
        _staffingContextFactory = staffingContextFactory;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
        _tencentImHelper = tencentImHelper;
    }

    // /// <summary>
    // /// 加诺积分
    // /// </summary>
    // public async Task<int> AddNScore(NScoreModel model)
    // {
    //     if (model.Increment == 0)
    //         return 0;

    //     var score = 0;
    //     try
    //     {
    //         var lockerKey = $"{RedisKey.NScore.Key}{model.UserId}";
    //         using var locker = await MyRedis.Lock(lockerKey, 15);

    //         if (locker == null)
    //             throw new Exception("加诺积分获取锁失败");

    //         using var context = _staffingContextFactory.CreateDbContext();

    //         var nscore = context.User_NScore.FirstOrDefault(x => x.UserId == model.UserId);
    //         if (nscore == null)
    //         {
    //             nscore = new User_NScore
    //             {
    //                 UserId = model.UserId
    //             };
    //             context.Add(nscore);
    //         }

    //         if (model.UserType == SeekerOrHr.Seeker)
    //             nscore.SeekerScore += model.Increment;
    //         else if (model.UserType == SeekerOrHr.Hr)
    //             nscore.HrScore += model.Increment;

    //         if (model.Increment > 0)
    //             if (model.UserType == SeekerOrHr.Seeker)
    //                 nscore.SeekerScoreTotal += model.Increment;
    //             else if (model.UserType == SeekerOrHr.Hr)
    //                 nscore.HrScoreTotal += model.Increment;

    //         var scoreRecord = new User_NScore_Record
    //         {
    //             Amount = nscore.SeekerScore,
    //             Increment = model.Increment,
    //             UserType = model.UserType,
    //             Type = model.Type,
    //             Content = model.Content,
    //             Data = model.Data,
    //             EventTime = model.EventTime,
    //             UserId = model.UserId
    //         };
    //         context.Add(scoreRecord);

    //         context.SaveChanges();

    //         score = scoreRecord.Increment;
    //     }
    //     catch (Exception e)
    //     {
    //         _log.Error("加积分出错", Tools.GetErrMsg(e), model);
    //     }

    //     return score;
    // }
}

// public class NScoreModel
// {
//     public string UserId { get; set; } = null!;

//     /// <summary>
//     /// 增量
//     /// </summary>
//     public int Increment { get; set; }

//     /// <summary>
//     /// hr还是求职者
//     /// </summary>
//     public SeekerOrHr UserType { get; set; }

//     /// <summary>
//     /// 类型
//     /// </summary>
//     public NScoreType Type { get; set; }

//     /// <summary>
//     /// 记录内容
//     /// </summary>
//     public string? Content { get; set; }

//     /// <summary>
//     /// 数据json
//     /// </summary>
//     public string? Data { get; set; }

//     /// <summary>
//     /// 发生时间
//     /// </summary>
//     public DateTime EventTime { get; set; } = DateTime.Now;
// }