﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 合同表
/// </summary>
[Table("contract")]
public class Contract
{
    [Key]
    public string ContractId { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// E签宝签署流程Id
    /// </summary>
    public string EFlowId { get; set; } = null!;

    /// <summary>
    /// 合同名称
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// 合同类型
    /// </summary>
    public int Type { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    public int IdentityCardType { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    public string IdentityCard { get; set; } = null!;

    /// <summary>
    /// 身份证名字
    /// </summary>
    public string IdentityCardName { get; set; } = null!;

    /// <summary>
    /// 项目Id
    /// </summary>
    public string ProjectId { get; set; } = null!;

    /// <summary>
    /// 职位
    /// </summary>
    public string Post { get; set; } = null!;

    /// <summary>
    /// 用户手机号
    /// </summary>
    public string UserMobile { get; set; } = null!;

    /// <summary>
    /// 开始签署时间
    /// </summary>
    public DateTime? SigningBeginTime { get; set; }

    /// <summary>
    /// 完成签署时间
    /// </summary>
    public DateTime? SigningEndTime { get; set; }

    /// <summary>
    /// 合同开始时间
    /// </summary>
    public DateTime? ContractBeginTime { get; set; }

    /// <summary>
    /// 合同截止时间
    /// </summary>
    public DateTime? ContractEndTime { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string CreatedBy { get; set; } = null!;

    /// <summary>
    /// 更新人
    /// </summary>
    public string UpdatedBy { get; set; } = null!;

    public DateTime UpdatedTime { get; set; }
    public DateTime CreatedTime { get; set; }
}