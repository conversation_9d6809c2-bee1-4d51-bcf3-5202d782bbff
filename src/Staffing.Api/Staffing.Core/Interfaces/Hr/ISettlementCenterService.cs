using Config.CommonModel;
using Staffing.Model.Hr.SettlementCenter;

namespace Staffing.Core.Interfaces.Hr;

/// <summary>
/// 结算中心服务接口
/// </summary>
public interface ISettlementCenterService
{
    /// <summary>
    /// 获取订单收入列表
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <returns>收入列表</returns>
    OrderIncomeListResponse GetOrderIncomeList(OrderIncomeRequest request);

    /// <summary>
    /// 获取订单消费列表
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <returns>消费列表</returns>
    OrderExpenseListResponse GetOrderExpenseList(OrderExpenseRequest request);
}
