﻿using Config;
using Config.CommonModel.Business;
using Config.CommonModel.DingDing;
using Config.CommonModel.DingDingRoot;
using Config.CommonModel.FigureNoah;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Staffing.Worker.Service;

/// <summary>
/// 钉钉服务
/// </summary>
[Service(ServiceLifetime.Transient)]
public class DingService
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _logger;
    private ConfigManager _config;
    private readonly DingDingRootHelper _dingDingRootHelper;
    private readonly DingDingHelper _dingDingHelper;
    private readonly FigureNoahHelper _figureNoahHelper;
    private readonly CommonDicService _commonDicService;

    //排除的组织ID
    private readonly long[] ExcludeDdDeptId = new long[] {
            143342460,//外部人员
            387365207,//外包员工（仅管理员可见）
            128054618//历史组织架构（仅管理员可见）
        };

    // 钉钉机器人不发送的主创userid
    private readonly List<string> NotNoteUserIds = new List<string>
    {
        "101","102"
    };

    // 钉钉机器人发送测试用户userid
    private readonly List<string> testHrIds = new List<string>
    {
        "154370362724200965",
        "212581386807644421"
    };

    /// <summary>
    /// 注入
    /// </summary>
    public DingService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger,
    IOptionsSnapshot<ConfigManager> config, DingDingRootHelper dingDingRootHelper,
    DingDingHelper dingDingHelper, FigureNoahHelper figureNoahHelper, CommonDicService commonDicService)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _config = config.Value;
        _dingDingRootHelper = dingDingRootHelper;
        _dingDingHelper = dingDingHelper;
        _figureNoahHelper = figureNoahHelper;
        _commonDicService = commonDicService;
    }

    /// <summary>
    /// 定时推送钉钉消息
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task PushDingDingMessage(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            var today = DateTime.Today;
            if (DateTime.Now <= today.AddHours(9) || DateTime.Now >= today.AddHours(11))
                return;

            //redis缓存
            string redisKey = "dingding:push" + DateTime.Now.ToString("yyyyMMdd");
            var redisExit = MyRedis.Client.SetNx(redisKey, "1", 86400);
            if (!redisExit)
            {
                return;
            }

            using var _context = _contextFactory.CreateDbContext();

            try
            {
                //企业线索池消息通知
                var hours24 = DateTime.Now.AddDays(-1);
                var hours72 = DateTime.Now.AddDays(-3);
                var notify24hour = _context.User_Hr_RecommendEnt.Where(g => g.Status == null
                 && g.CreatedTime <= hours24)
                 .GroupBy(g => g.AdviserId)
                 .Select(s => new
                 {
                     s.Key,
                     Ct = s.Count()
                 }).ToList().Where(x => x.Ct > 0).ToList();

                var notify72hour = _context.User_Hr_RecommendEnt.Where(g => g.Status == HrRecommendEntStatus.初步跟进
                && g.FollowUpTime <= hours72)
                .GroupBy(g => g.AdviserId)
                .Select(s => new
                {
                    s.Key,
                    Ct = s.Count()
                }).ToList().Where(x => x.Ct > 0).ToList();

                if (notify24hour.Count > 0)
                    MsgHelper.SendDingdingNotify(notify24hour.Select(s => new MsgNotifyModel
                    {
                        Type = MsgNotifyType.企业线索池跟进提醒,
                        EventTime = DateTime.Now,
                        UserId = s.Key,
                        Data = new MsgNotifyRecommendEnt { Num = s.Ct, Hours = 24 }
                    }).ToArray());

                if (notify72hour.Count > 0)
                    MsgHelper.SendDingdingNotify(notify72hour.Select(s => new MsgNotifyModel
                    {
                        Type = MsgNotifyType.企业线索池跟进提醒,
                        EventTime = DateTime.Now,
                        UserId = s.Key,
                        Data = new MsgNotifyRecommendEnt { Num = s.Ct, Hours = 72 }
                    }).ToArray());
            }
            catch (Exception e)
            {
                _logger.Error("企业线索池消息通知错误", Tools.GetErrMsg(e));
            }

            DateTime startTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd 00:00:00"));
            DateTime endTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd 23:59:59"));
            var pushModel = _context.Msg_DingDingPush.Where(o => o.CreatedTime >= startTime && o.CreatedTime <= endTime && o.PushTime == null).ToList();

            foreach (var i in pushModel)
            {
                try
                {
                    string pushTemplate = "诺快聘数据概况 截止{12}\n \n您创建并运行的项目有 {0} 个，其中 {2} 个项目大家正在帮您招聘，需要集团帮忙的项目有 {1} 个。\n作为项目负责人您又收到 {4} 位求职者投递的简历、还有 {5} 人正在面试进程中，需要及时处理哦！\n您当前的人才库储备有 {10} 人，他们可以预览并投递的岗位有 {3} 个。\n您已经储备了 {11} 份简历 ，多囤点简历对您的招聘很有帮助哦！\n作为项目协同者，您正在帮助 {7} 人寻找工作，其中 {8} 个人已经成功入职，因为您的参与，让更多人实现了自己的价值。\n诺快聘：用户工作有所“求”，我们必所“应”。";

                    if (i.MessageNoReply != 0)
                    {
                        pushTemplate += "\n \n还有个 {6} 个未读消息，记得上诺快聘业务端回复下哦！";
                    }

                    var dingId = _context.User_DingDing.Where(x => x.UserId == i.UserId && x.IsPush)
                    .Select(s => s.DingUserid).FirstOrDefault();

                    if (string.IsNullOrWhiteSpace(dingId))
                        continue;

                    SendDingDingRootMessageRequest model = new SendDingDingRootMessageRequest()
                    {
                        userId = dingId,
                        msgParam = JsonSerializer.SerializeToString(new { content = string.Format(pushTemplate, i.CreateProject, i.EnableCollaboration, i.CollaborativeProject, i.RegistrablePositions, i.ScreeningNum, i.InterviewNum, i.MessageNoReply, i.CoordinationDelivery, i.CoordinationSuccess, i.CoordinationFail, i.ReservePlatform, i.ReserveVirtual, i.CreatedTime.ToString("yyyy-MM-dd HH:mm")) })
                    };
                    await _dingDingRootHelper.SendDingDingRootMessage(model);

                    var dingdingModel = _context.DingDing.Where(o => o.DingUserid == i.DingUserid).FirstOrDefault();
                    if (dingdingModel is not null)
                    {
                        dingdingModel.PushTime = DateTime.Now;
                    }

                    i.PushTime = DateTime.Now;
                    _context.SaveChanges();

                    await Task.Delay(50);
                }
                catch (Exception ex)
                {
                    _logger.Error("钉钉统计数据推送", Tools.GetErrMsg(ex), i);
                }
            }

            //已绑定的集合
            List<string> messageIded = _context.User_DingDing.Select(o => o.DingUserid).ToList();

            //全集合
            List<string> messageIdAll = _context.DingDing.Select(o => o.DingUserid).ToList();

            //取全集合和已绑定的集合的差集=未绑定的集合
            // List<string> messageId = messageIdAll.Union(messageIded).ToList();
            var messageId = messageIdAll.Except(messageIded).ToList();

            foreach (var i in messageId)
            {
                try
                {
                    string messageTemplate = "亲爱的同事您好，我是诺快聘小助手，现邀请您入驻诺快聘用工管理平台！\n诺快聘是公司旗下的一站式用工管理平台，可以依托平台积累自己私域用户；招聘困难可调动全公司人员帮你交付；多余人脉储备可通过协同项目赚取交付提成。\n目前诺快聘用户转换率79 %（高于目前的任何招聘渠道），支持多场景下的推广使用，我们会始终围绕：联系、跟进、成交、管理四个环节进行推动。\n相关操作手册可在人才赋能中心查看。\n开通诺快聘步骤如下，1、打开nuopin.cn；2、登录账号进入HR端；3、点击云市场（左侧工具栏），随便一个应用点击开通；4、诺聘平台会及时审核开通；\n\n如您已开通，请打开云市场 → 项目执行（右下侧） → 绑定钉钉，会解除邀约推送。";
                    SendDingDingRootMessageRequest model = new SendDingDingRootMessageRequest()
                    {
                        userId = i,
                        // msgParam = messageTemplate
                        msgParam = JsonSerializer.SerializeToString(new { content = messageTemplate })
                    };
                    await _dingDingRootHelper.SendDingDingRootMessage(model);

                    var dingdingModel = _context.DingDing.Where(o => o.DingUserid == i).FirstOrDefault();
                    if (dingdingModel is not null)
                    {
                        dingdingModel.PushTime = DateTime.Now;
                    }
                    _context.SaveChanges();

                    await Task.Delay(50);
                }
                catch (Exception ex)
                {
                    _logger.Error("钉钉提示消息推送", Tools.GetErrMsg(ex), i);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.Error("定时推送钉钉消息", Tools.GetErrMsg(ex));
        }
    }

    /// <summary>
    /// 钉钉数据同步
    /// </summary>
    /// <param name="cancel"></param>
    public async Task DdAsync(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            try
            {
                // if (!MyRedis.Client.SetNx($"dd:ddasynccheck", 3500))
                //     return;

                //同步部门信息
                await DdDeptAsync();

                //同步部门人员
                await DdUserAsync();

                //同步数字诺亚合同和项目
                await SZNYAsync();
            }
            catch (Exception e)
            {
                _logger.Error("钉钉数据同步失败", Tools.GetErrMsg(e));
            }
        }
        catch (Exception e)
        {
            _logger.Error("钉钉数据同步失败", Tools.GetErrMsg(e));
        }
    }

    private async Task DdDeptAsync()
    {
        //获取钉钉部门
        var ddDepts_New = await DdDept(1, "1.");

        if (ddDepts_New == null || ddDepts_New.Count == 0)
            throw new Exception("钉钉数据同步失败：钉钉部门数量为0");

        //同步钉钉部门
        using var _context = _contextFactory.CreateDbContext();
        var depts = _context.Dd_Dept.ToList();

        var delDepts = depts.ExceptBy(ddDepts_New.Select(s => s.DdDeptId), x => x.DdDeptId);

        if (delDepts.Count() > 0)
        {
            var ids = delDepts.Select(s => s.DdDeptId).ToList();
            _context.Dd_Dept.RemoveRange(delDepts.ToList());
            _context.Dd_User_Dept.RemoveRange(_context.Dd_User_Dept.Where(x => ids.Contains(x.DdDeptId ?? 0)));
        }

        foreach (var item in ddDepts_New)
        {
            var temp = depts.FirstOrDefault(x => x.DdDeptId == item.DdDeptId);
            if (temp == null)
                _context.Add(item);
            else
            {
                temp.Name = item.Name;
                temp.Level = item.Level;
                temp.ParentId = item.ParentId;
                temp.UpdatedTime = DateTime.Now;
            }
        }

        _context.SaveChanges();

        var currentDepts = _context.Dd_Dept.ToList();
        //更新缓存
        MyRedis.Client.Set(RedisKey.DdDept, currentDepts);
    }

    async Task<List<Dd_Dept>?> DdDept(long deptId, string? level = null)
    {
        level = level ?? deptId.ToString();

        var resp = await _dingDingHelper.GetDepartmentListSub(deptId);
        if (resp == null || resp.Count() == 0)
            return null;

        await Task.Delay(30);

        List<Dd_Dept> detpList = new List<Dd_Dept>();
        foreach (var item in resp)
        {
            if (ExcludeDdDeptId.Contains(item.dept_id))
                continue;
            detpList.Add(new Dd_Dept
            {
                DdDeptId = item.dept_id,
                Name = item.name ?? string.Empty,
                ParentId = deptId,
                Level = $"{level}{item.dept_id}.",
                UpdatedTime = DateTime.Now,
                CreatedTime = DateTime.Now
            });
            var child = await DdDept(item.dept_id, $"{level}{item.dept_id}.");
            if (child != null && child.Count > 0)
                detpList.AddRange(child);
        }
        return detpList;
    }

    private async Task DdUserAsync()
    {
        var depts = MyRedis.Client.Get<List<Dd_Dept>>(RedisKey.DdDept);

        if (depts.Count == 0)
            return;

        var newDdIds = new List<string>();

        //同步部门用户
        foreach (var item in depts)
        {
            var request = new GetDeptUsers
            {
                dept_id = item.DdDeptId,
                cursor = 0,
                size = 100
            };

            while (true)
            {
                var resp = await _dingDingHelper.GetDeptUsers(request);
                request.cursor += request.size;

                if (resp.list == null)
                    break;

                newDdIds.AddRange(resp.list.Select(s => s.userid!));

                using var _context = _contextFactory.CreateDbContext();
                foreach (var duser in resp.list)
                {
                    //有人退出企业重新加入，导致id变化，有人更换手机号导致手机号变化，主键不可修改，为了便于查询，ef根据手机号设置了一对一关系，因此手机号无法修改，若修改手机号只能删除重新添加
                    var users = _context.Dd_User.Where(x => x.Mobile == duser.mobile || x.DdUserId == duser.userid)
                    .ToList();

                    foreach (var usr in users)
                    {
                        if (usr.Mobile == duser.mobile && usr.DdUserId != duser.userid)
                            _context.Remove(usr);
                        if (usr.Mobile != duser.mobile && usr.DdUserId == duser.userid)
                            _context.Remove(usr);
                    }

                    var user = users.FirstOrDefault(x => x.Mobile == duser.mobile && x.DdUserId == duser.userid);
                    if (user == null)
                    {
                        var newUser = new Dd_User
                        {
                            DdUserId = duser.userid!,
                            Mobile = duser.mobile ?? string.Empty,
                            Name = duser.name ?? string.Empty,
                            Title = duser.title,
                            Avatar = duser.avatar,
                            JobNumber = duser.job_number ?? string.Empty,
                            Remark = duser.remark
                        };
                        _context.Add(newUser);
                    }
                    else
                    {
                        user.Name = duser.name ?? string.Empty;
                        user.Title = duser.title;
                        user.Avatar = duser.avatar;
                        user.JobNumber = duser.job_number ?? string.Empty;
                        user.Remark = duser.remark;

                        user.UpdatedTime = DateTime.Now;
                    }

                    //更新所在部门
                    var deptIds = duser.dept_id_list?.Select(s => (long?)s).ToList() ?? new List<long?>();
                    var oldDeptIds = _context.Dd_User_Dept.Where(x => x.DdUserId == duser.userid).Select(s => s.DdDeptId).ToList();
                    var removeDeptIds = oldDeptIds.Except(deptIds).ToList();
                    var addDeptIds = deptIds.Except(oldDeptIds).ToList();

                    _context.Dd_User_Dept.RemoveRange(_context.Dd_User_Dept.Where(x => x.DdUserId == duser.userid && removeDeptIds.Contains(x.DdDeptId)));
                    if (addDeptIds.Count > 0)
                    {
                        _context.AddRange(addDeptIds.Select(s => new Dd_User_Dept
                        {
                            DdUserId = duser.userid!,
                            DdDeptId = s
                        }));
                    }
                }
                _context.SaveChanges();

                if (resp.has_more != true || resp.list.Count() == 0)
                    break;

                await Task.Delay(30);
            }
        }

        //移除离职员工
        if (newDdIds.Count > 0)
        {
            await Task.Delay(50);
            using var _context = _contextFactory.CreateDbContext();

            using (var tran = _context.Database.BeginTransaction())
            {
                var currentDdIds = _context.Dd_User.Select(s => s.DdUserId).ToList();

                var reomveIds = currentDdIds.Except(newDdIds).ToList();

                _context.Dd_User.Where(x => reomveIds.Contains(x.DdUserId))
                .ExecuteDelete();

                _context.Dd_User_Dept.Where(x => reomveIds.Contains(x.DdUserId))
                .ExecuteDelete();

                tran.Commit();
            }

            _context.SaveChanges();
        }
    }

    private async Task SZNYAsync()
    {
        using var _context2 = _contextFactory.CreateDbContext();

        var users = _context2.Rpt_Ny_User.ToList();

        if (users.Count == 0)
            return;

        var i = 1;
        //同步数字诺亚数据
        foreach (var user in users)
        {
            try
            {
                var szny = await _figureNoahHelper.GetEmployeeProjDataDetail(user.工号!);

                using var _context = _contextFactory.CreateDbContext();

                var distinctht = new List<NoahGetEmployeeProjHtDetail>();
                foreach (var ht in szny.htdata.rows)
                {
                    if (!distinctht.Any(x => x.SJHT_CODE!.ToLower().Trim() == ht.SJHT_CODE!.ToLower().Trim()))
                        distinctht.Add(ht);
                }

                foreach (var ht in distinctht)
                {
                    var updateht = _context.Rpt_Szny_Contract.FirstOrDefault(x => x.SJHT_CODE.Trim() == ht.SJHT_CODE!.Trim());
                    if (updateht == null)
                    {
                        updateht = new Rpt_Szny_Contract
                        {
                            SJHT_CODE = ht.SJHT_CODE!
                        };
                        _context.Add(updateht);
                    }

                    updateht.SJHT_ENDDATE = ht.SJHT_ENDDATE!.Value;
                    updateht.JobNumber = user.工号!;
                    updateht.SJHT_BEGINDATE = ht.SJHT_BEGINDATE!.Value;
                    updateht.SJHT_CPID = ht.SJHT_CPID ?? string.Empty;
                    updateht.SJHT_CPMC = ht.SJHT_CPMC ?? string.Empty;
                    updateht.SJHT_NAME = ht.SJHT_NAME ?? string.Empty;
                    updateht.SJHT_SY_RYCODE = ht.SJHT_SY_RYCODE ?? string.Empty;
                    updateht.SJHT_XBBBM = ht.SJHT_XBBBM ?? string.Empty;
                    updateht.SJHT_SETDATE = ht.SJHT_SETDATE!.Value;

                    updateht.UpdatedTime = DateTime.Now;
                }

                var distinctxm = new List<NoahGetEmployeeProjXmDetail>();
                foreach (var xm in szny.xmdata.rows)
                {
                    if (!distinctxm.Any(x => x.XM_CODE!.ToLower().Trim() == xm.XM_CODE!.ToLower().Trim()))
                        distinctxm.Add(xm);
                }

                foreach (var xm in distinctxm)
                {
                    var updatexm = _context.Rpt_Szny_Project.FirstOrDefault(x => x.XM_CODE == xm.XM_CODE);
                    if (updatexm == null)
                    {
                        updatexm = new Rpt_Szny_Project
                        {
                            XM_CODE = xm.XM_CODE!
                        };
                        _context.Add(updatexm);
                    }

                    updatexm.JobNumber = user.工号!;
                    updatexm.XM_NAME = xm.XM_NAME ?? string.Empty;
                    updatexm.XM_PRODUCTID = xm.XM_PRODUCTID ?? string.Empty;
                    updatexm.XM_PRODUCTNAME = xm.XM_PRODUCTNAME ?? string.Empty;
                    updatexm.XM_SETDATE = xm.XM_SETDATE!.Value;
                    updatexm.XM_BEGTIME = xm.XM_BEGTIME!.Value;
                    updatexm.XM_ENDTIME = xm.XM_ENDTIME!.Value;

                    updatexm.UpdatedTime = DateTime.Now;
                }

                _context.SaveChanges();
            }
            catch
            {
                //数字诺亚经常报错，不再提示了。
                // _logger.Error("数字诺亚数据同步失败", Tools.GetErrMsg(e), user.工号 ?? string.Empty);
            }
            Console.WriteLine(i++);

            await Task.Delay(30);
        }
    }

    /// <summary>
    /// 给主管推送部门统计（开发中）
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task PushStatForLeader(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            var today = DateTime.Today;
            if (DateTime.Now <= today.AddHours(9) || DateTime.Now >= today.AddHours(11))
                return;

            //redis缓存
            string redisKey = "dingding:PushStatForLeader";

            if (!MyRedis.Client.SetNx(redisKey, "1", 86400))
                return;

            using var _context = _contextFactory.CreateDbContext();

            //查询所有主管
            var leaders = _context.Enterprise_Org_User.Where(x => x.User_Hr.RoleId == Power.LeaderRoleId)
            .Select(s => s.User_Hr).Distinct().ToList();

            var userDd = _context.User_DingDing.Select(s => new
            {
                s.UserId,
                s.DingUserid
            }).ToList();

            foreach (var leader in leaders)
            {
                try
                {
                    // string pushTemplate = "诺快聘数据概况 截止{12}\n \n您创建并运行的项目有 {0} 个，其中 {2} 个项目大家正在帮您招聘，需要集团帮忙的项目有 {1} 个。\n作为项目负责人您又收到 {4} 位求职者投递的简历、还有 {5} 人正在面试进程中，需要及时处理哦！\n您当前的人才库储备有 {10} 人，他们可以预览并投递的岗位有 {3} 个。\n您已经储备了 {11} 份简历 ，多囤点简历对您的招聘很有帮助哦！\n作为项目协同者，您正在帮助 {7} 人寻找工作，其中 {8} 个人已经成功入职，因为您的参与，让更多人实现了自己的价值。\n诺快聘：用户工作有所“求”，我们必所“应”。";

                    // if (i.MessageNoReply != 0)
                    // {
                    //     pushTemplate += "\n \n还有个 {6} 个未读消息，记得上诺快聘业务端回复下哦！";
                    // }
                    var message = $"hello";

                    var dingId = userDd.FirstOrDefault(x => x.UserId == leader.UserId)?.DingUserid;

                    if (string.IsNullOrWhiteSpace(dingId))
                        continue;

                    var ddrequest = new SendDingDingRootMessageRequest()
                    {
                        userId = dingId,
                        msgParam = JsonSerializer.SerializeToString(new { content = message })
                    };
                    await _dingDingRootHelper.SendDingDingRootMessage(ddrequest);

                    // var ddPushRecord = new Dd_Push_Record
                    // {
                    //     Content = message,
                    //     Type = DdPushRecordType.部门统计,
                    //     UserId = leader.UserId
                    // };
                    // _context.Add(ddPushRecord);

                    // _context.SaveChanges();

                    await Task.Delay(50);
                }
                catch (Exception ex)
                {
                    _logger.Error("钉钉给主管推送部门统计出错", Tools.GetErrMsg(ex), leader);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.Error("钉钉给主管推送部门统计出错", Tools.GetErrMsg(ex));
        }
    }

    /// <summary>
    /// 新用户推送
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public void NewUserPush(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            var today = DateTime.Today;
            if (DateTime.Now <= today.AddHours(9) || DateTime.Now >= today.AddHours(11))
                return;

            //redis缓存
            var redisKey = "st:push:newusermsg" + DateTime.Now.ToString("yyyyMMdd");
            var exits = MyRedis.Client.SetNx(redisKey, "1", 86400);
            if (!exits)
                return;

            using var _context = _contextFactory.CreateDbContext();

            //郭思诚提出暂时不通知，后期针对不同顾问进行推送
            // //第注册2天的人
            // var day2 = DateTime.Today.AddDays(-1);
            // var user2 = _context.User_Seeker.Where(x => x.CreatedTime.Date == day2 && x.Status == UserStatus.Active)
            // .Select(s => new Sub_NewUserMsgPush
            // {
            //     Day = 2,
            //     UserId = s.UserId,
            //     Mobile = s.User.Mobile,
            //     H5OpenId = s.User.WeChatH5OpenId,
            //     WeChatH5Subscribe = s.User.WeChatH5Subscribe,
            //     UserName = s.NickName,
            //     Score = (int?)s.User_NScore.SeekerScore ?? 0
            // }).ToArray();

            // //第注册3天的人
            // var day3 = DateTime.Today.AddDays(-2);
            // var user3 = _context.User_Seeker.Where(x => x.CreatedTime.Date == day3 && x.Status == UserStatus.Active)
            // .Select(s => new Sub_NewUserMsgPush
            // {
            //     Day = 3,
            //     UserId = s.UserId,
            //     Mobile = s.User.Mobile,
            //     H5OpenId = s.User.WeChatH5OpenId,
            //     WeChatH5Subscribe = s.User.WeChatH5Subscribe,
            //     UserName = s.NickName,
            //     Score = (int?)s.User_NScore.SeekerScore ?? 0
            // }).ToArray();

            // //第注册5天的人
            // var day5 = DateTime.Today.AddDays(-4);
            // var user5 = _context.User_Seeker.Where(x => x.CreatedTime.Date == day5 && x.Status == UserStatus.Active)
            // .Select(s => new Sub_NewUserMsgPush
            // {
            //     Day = 5,
            //     UserId = s.UserId,
            //     Mobile = s.User.Mobile,
            //     H5OpenId = s.User.WeChatH5OpenId,
            //     WeChatH5Subscribe = s.User.WeChatH5Subscribe,
            //     UserName = s.NickName,
            //     Score = (int?)s.User_NScore.SeekerScore ?? 0
            // }).ToArray();

            // if (user2?.Count() > 0)
            //     MyRedis.Client.SAdd(SubscriptionKey.NewUserMsgPush, user2);

            // if (user3?.Count() > 0)
            //     MyRedis.Client.SAdd(SubscriptionKey.NewUserMsgPush, user3);

            // if (user5?.Count() > 0)
            //     MyRedis.Client.SAdd(SubscriptionKey.NewUserMsgPush, user5);

            //项目到期提醒
            var edt = DateTime.Today.AddDays(2);
            var projectEndMsgs = _context.Project.Where(x => x.Status == ProjectStatus.已上线 && x.EndTime.Date == edt)
            .Select(s => new MsgNotifyModel
            {
                Type = MsgNotifyType.归档,
                EventTime = s.EndTime,
                // TeamHrId = s.HrId,
                UserId = s.HrId,
                Data = new MsgNotifyProjectEnd
                {
                    // TeamProjectId = string.Empty,
                    IsSelfProj = true,
                    ProjectId = s.ProjectId,
                    ProjectName = s.Agent_Ent.Name,
                    HrName = s.User_Hr.NickName
                }
            }).ToArray();

            //钉钉通知、公众号通知
            if (projectEndMsgs?.Count() > 0)
            {
                MyRedis.Client.RPush(SubscriptionKey.MsgDingDingNotify, projectEndMsgs);
                MyRedis.Client.RPush(SubscriptionKey.MsgWeChatMpNotify, projectEndMsgs);
            }
        }
        catch (Exception ex)
        {
            _logger.Error("新用户推送", Tools.GetErrMsg(ex));
        }
    }

    /// <summary>
    /// 职位优选报名提醒
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public void ExcellentPostMsg(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            using var _context = _contextFactory.CreateDbContext();

            //每次检测多少分钟内的数据
            var invMins = 15;

            //职位优选面试提醒
            var excellentInterviewTime = DateTime.Now.AddHours(-24);
            var excellentInterviewTimeMin = excellentInterviewTime.AddMinutes(invMins * -1);

            var etInterviewStatus = new List<RecruitStatus> { RecruitStatus.HrScreening, RecruitStatus.InterviewerScreening };
            var excellentInterviewMsgs = _context.Recruit.Where(x => etInterviewStatus.Contains(x.Status)
            && x.CreatedTime < excellentInterviewTime && x.CreatedTime > excellentInterviewTimeMin
            && x.Post_Delivery.Post_Team.Post_Excellent.Status == ExcellentPostStatus.Active
            && x.Post_Delivery.Post_Team.Post_Excellent.InterviewIn24Hour)
            .Select(s => new MsgNotifyModel
            {
                Type = MsgNotifyType.职位优选面试,
                EventTime = DateTime.Now,
                // TeamHrId = s.HrId,
                UserId = s.HrId,
                Data = new MsgExcellentPost
                {
                    RecruitId = s.RecruitId,
                    PostName = s.Post_Delivery.Post.Name,
                    SeekerName = s.User_Seeker.NickName
                }
            }).ToArray();

            var lastInterviewIds = MyRedis.Client.Get<List<string>?>(RedisKey.ExcellentPost.InterviewIn24Hour) ?? new List<string>();

            MyRedis.Client.Set(RedisKey.ExcellentPost.InterviewIn24Hour,
            excellentInterviewMsgs.Select(s => (s.Data as MsgExcellentPost)!.RecruitId).ToList(), invMins * 60);

            excellentInterviewMsgs = excellentInterviewMsgs.Where(x => !lastInterviewIds.Contains((x.Data as MsgExcellentPost)!.RecruitId!)).ToArray();

            //钉钉通知
            if (excellentInterviewMsgs?.Count() > 0)
                MyRedis.Client.RPush(SubscriptionKey.MsgDingDingNotifyFullTime, excellentInterviewMsgs);

            //职位优选入职提醒
            var excellentEntryTime = DateTime.Now.AddHours(-72);
            var excellentEntryTimeMin = excellentEntryTime.AddMinutes(invMins * -1);

            var etEntryStatus = new List<RecruitStatus> { RecruitStatus.Contract, RecruitStatus.Induction, RecruitStatus.Offer, RecruitStatus.FileAway };
            var excellentEntryMsgs = _context.Recruit.Where(x => !etEntryStatus.Contains(x.Status)
            && x.CreatedTime < excellentEntryTime && x.CreatedTime > excellentEntryTimeMin
            && x.Post_Delivery.Post_Team.Post_Excellent.Status == ExcellentPostStatus.Active
            && x.Post_Delivery.Post_Team.Post_Excellent.EntryIn72hour)
            .Select(s => new MsgNotifyModel
            {
                Type = MsgNotifyType.职位优选入职,
                EventTime = DateTime.Now,
                // TeamHrId = s.HrId,
                UserId = s.HrId,
                Data = new MsgExcellentPost
                {
                    RecruitId = s.RecruitId,
                    PostName = s.Post_Delivery.Post.Name,
                    SeekerName = s.User_Seeker.NickName
                }
            }).ToArray();

            var lastEntryIds = MyRedis.Client.Get<List<string>?>(RedisKey.ExcellentPost.EntryIn72Hour) ?? new List<string>();

            MyRedis.Client.Set(RedisKey.ExcellentPost.EntryIn72Hour,
            excellentEntryMsgs.Select(s => (s.Data as MsgExcellentPost)!.RecruitId).ToList(), invMins * 60);

            excellentEntryMsgs = excellentEntryMsgs.Where(x => !lastEntryIds.Contains((x.Data as MsgExcellentPost)!.RecruitId!)).ToArray();

            //钉钉通知
            if (excellentEntryMsgs?.Count() > 0)
                MyRedis.Client.RPush(SubscriptionKey.MsgDingDingNotifyFullTime, excellentEntryMsgs);
        }
        catch (Exception ex)
        {
            _logger.Error("职位优选面试入职提醒", Tools.GetErrMsg(ex));
        }
    }

    /// <summary>
    /// Hr初筛提醒，定时推送钉钉消息（9:00-9:30）
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task PushDingDingMessageForRecruit9(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            var today = DateTime.Today;
            if (DateTime.Now <= today.AddHours(9) || DateTime.Now >= today.AddHours(9).AddMinutes(30))
                return;

            await SendDingRobotMessageForSum();
        }
        catch (Exception ex)
        {
            _logger.Error("Hr初筛提醒，定时推送钉钉消息（9:00-9:30）出错", Tools.GetErrMsg(ex));
        }
    }

    /// <summary>
    /// Hr初筛提醒，定时推送钉钉消息（11:00-11:30）
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task PushDingDingMessageForRecruit11(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            var today = DateTime.Today;
            if (DateTime.Now <= today.AddHours(11) || DateTime.Now >= today.AddHours(11).AddMinutes(30))
                return;

            await SendDingRobotMessageForSum();
        }
        catch (Exception ex)
        {
            _logger.Error("Hr初筛提醒，定时推送钉钉消息（11:00-11:30）出错", Tools.GetErrMsg(ex));
        }
    }

    /// <summary>
    /// Hr初筛提醒，定时推送钉钉消息（16:00-16:30）
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task PushDingDingMessageForRecruit16(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            var today = DateTime.Today;
            if (DateTime.Now <= today.AddHours(16) || DateTime.Now >= today.AddHours(16).AddMinutes(30))
                return;

            await SendDingRobotMessageForSum();
        }
        catch (Exception ex)
        {
            _logger.Error("Hr初筛提醒，定时推送钉钉消息（16:00-16:30）出错", Tools.GetErrMsg(ex));
        }
    }

    private async Task SendDingRobotMessageForSum()
    {
        var _context = _contextFactory.CreateDbContext();
        // 对测试人员开放  && testHrIds.Contains(g.HrId)
        var hrScreeningList = _context.Recruit.Where(g => g.Status == RecruitStatus.HrScreening && !NotNoteUserIds.Contains(g.HrId)).ToList();
        if (hrScreeningList.Count == 0)
        {
            return;
        }
        // 给hr发消息
        var hrs = hrScreeningList.Select(s => s.HrId).Distinct().ToList();
        foreach (var hrId in hrs)
        {
            // 同一个hr半小时内只发送一次消息提醒，发送失败也不会重试
            string redisKey = $"dingding:recruit_push_{hrId}";
            var redisExit = MyRedis.Client.SetNx(redisKey, "1", 2000);// 大于1800s，防止极端情况发生
            if (!redisExit)
            {
                return;
            }

            // 判断是否绑定钉钉，未绑定不发送
            //var dingUserId = _context.User_DingDing.FirstOrDefault(f => f.UserId == hrId)?.DingUserid;
            //if (dingUserId == null)
            //{
            //    _logger.Error("发送钉钉机器人消息出错-未找到钉钉用户id", hrId, "PushDingDingMessageForRecruit9_10");
            //    continue;
            //}

            int ProjectNum = 0, PostNum = 0, ResumeNum = 0;
            var recruitInfo = _context.Recruit.Where(w => w.Status == RecruitStatus.HrScreening && w.HrId == hrId)
                .Select(s => new
                {
                    s.RecruitId,
                    s.SeekerId,
                    s.Post_Delivery.PostId,
                    s.Post_Delivery.Post_Team.Project_Team.ProjectId
                }).ToList();

            if (recruitInfo.Count == 0)
            {
                return;
            }

            ProjectNum = recruitInfo.GroupBy(g => g.ProjectId).Count();
            PostNum = recruitInfo.GroupBy(g => g.PostId).Count();
            ResumeNum = recruitInfo.GroupBy(g => new { g.ProjectId, g.PostId, g.SeekerId }).Count();

            var messageJson = new
            {
                title = "HR初筛汇总消息",
                text = $"您涉及 **{ProjectNum}** 个项目 **{PostNum}** 个岗位 **{ResumeNum}** 个求职者报名数据未处理\n " +
                $"*** \n" +
                $"#### [点击处理]({GetUrl()}) \n"
            };

            //var request = new SendDingDingRootMessageRequest
            //{
            //    userId = dingUserId,
            //    msgParam = JsonSerializer.SerializeToString(messageJson)
            //};

            // 发送钉钉机器人消息
            //await _dingDingRootHelper.SendDingDingRootMDMessage(request);

            // 钉钉机器人发消息统一接口 - 白天发（9点以后）
            await MsgHelper.SendDingdingNotifyAsync(new MsgNotifyModel[]
            {
                new MsgNotifyModel
                {
                    Type = MsgNotifyType.钉钉机器人HR初筛定时提醒,
                    EventTime = DateTime.Now,
                    UserId = hrId,
                    Data = messageJson.text
                }
            });
        }
    }

    /// <summary>
    /// 简历投递给主创发送钉钉机器人消息
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task RecruitDingTalkRobot(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<string?>(SubscriptionKey.RecruitDingTalkRobot);

                if (string.IsNullOrWhiteSpace(msg))
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息
                var recruitId = msg;
                using var _context = _contextFactory.CreateDbContext();
                var recruitInfo = _context.Recruit.Where(x => x.RecruitId == recruitId)
                .Select(s => new
                {
                    s.HrId,
                    s.CreatedTime, // 投递时间
                    ProjectName = s.Post_Delivery.Post_Team.Project_Team.Project.Agent_Ent.Name,  // 统一返回为企业名称 项目名称
                    CompanyName = s.Post_Delivery.Post_Team.Post.Agent_Ent.Name,// 修改为岗位中的代招企业名称
                    s.Post_Delivery.User_Seeker.NickName,                                            // 求职者名称
                    RegionId = s.Post_Delivery.User_Seeker.RegionId,                                 // 求职者地区id
                    Education = s.Post_Delivery.User_Seeker.User_Resume.Education.GetDescription(),  // 求职者学历
                    Sex = s.Post_Delivery.User_Seeker.User_Resume.Sex.GetDescription(),              // 求职者性别
                    Age = Tools.GetAgeByBirthdate(s.Post_Delivery.User_Seeker.User_Resume.Birthday), // 求职者年龄
                    Avatar = s.Post_Delivery.User_Seeker.Avatar,                                     // 求职者头像
                    Source = s.Post_Delivery.User_Seeker.Source.GetDescription(),                    // 求职者来源
                    PostName = s.Post_Delivery.Post.Name,          // 职位名称
                    Category = s.Post_Delivery.Post.Category,       // 职位类别编码
                    TeamHrId = s.Post_Delivery.Post_Team.Project_Team.HrId, // 简历接收人id
                }).First();

                if (recruitInfo == null)
                    continue;
                // todo:先对测试人员开放
                //if (!testHrIds.Contains(recruitInfo.HrId))
                //    continue;

                var categoryInfo = _commonDicService.GetCategory(recruitInfo.Category + "");
                var regionInfo = _commonDicService.GetCityById(recruitInfo.RegionId + "");
                var msgParam = new DingRobotMDRecruitMsgParam
                {
                    ApplyTime = recruitInfo.CreatedTime,
                    ProjectName = recruitInfo.ProjectName,
                    CompanyName = recruitInfo.CompanyName,
                    SeekerName = recruitInfo.NickName,
                    City = regionInfo.CityName,
                    Education = recruitInfo.Education,
                    Sex = recruitInfo.Sex,
                    Age = recruitInfo.Age,
                    Avatar = recruitInfo.Avatar,
                    Source = Exchange(recruitInfo.Source),
                    PostName = recruitInfo.PostName,
                    Category = categoryInfo.CategoryLevel3Name
                };

                //var dingUserId = _context.User_DingDing.FirstOrDefault(f => f.UserId == recruitInfo.HrId)?.DingUserid;
                //if (dingUserId == null)
                //{
                //    _logger.Error("投递简历发送钉钉机器人消息出错-未找到钉钉用户id", recruitInfo.HrId, (msg ?? string.Empty).ToString());
                //    continue;
                //}

                // 生成消息内容
                string projectName = string.IsNullOrWhiteSpace(msgParam.ProjectName) ? "项目名称无" : msgParam.ProjectName;
                string companyName = string.IsNullOrWhiteSpace(msgParam.CompanyName) ? "代招公司无" : msgParam.CompanyName;
                string postName = string.IsNullOrWhiteSpace(msgParam.PostName) ? "岗位名称无" : msgParam.PostName;
                string category = string.IsNullOrWhiteSpace(msgParam.Category) ? "岗位类别无" : msgParam.Category;
                string seekerName = string.IsNullOrWhiteSpace(msgParam.SeekerName) ? "求职者名称无" : msgParam.SeekerName;
                string seekerAge = msgParam.Age == null ? "年龄无" : (msgParam.Age + "岁");
                string seekerSex = string.IsNullOrWhiteSpace(msgParam.Sex) ? "性别无" : msgParam.Sex;
                string seekerCity = string.IsNullOrWhiteSpace(msgParam.City) ? "地区无" : msgParam.City;
                string seekerEducation = string.IsNullOrWhiteSpace(msgParam.Education) ? "学历无" : msgParam.Education;

                var messageJson = new
                {
                    title = "简历投递消息",
                    text = $"{msgParam.ApplyTime?.ToString("M月d日 HH:mm")} **求职者通过“{msgParam.Source}”发起了报名** \n " +
                    $"*** \n" +
                    $"> ##### 项目名称：{projectName}\n " +
                    $"> 代招企业名称：{companyName}\n >" +
                    $"*** \n" +
                    $"> ##### 岗位名称：{postName}\n " +
                    $"> 岗位类别：{category}\n >" +
                    $"*** \n" +
                    $"> ##### 求职者姓名：{seekerName}\n " +
                    $"> 年龄：{seekerAge}\n" +
                    $"> 性别：{seekerSex}\n" +
                    $"> 地区：{seekerCity}\n" +
                    $"> 学历：{seekerEducation}\n " +
                    $"*** \n" +
                    $"#### [点击处理]({GetUrl()}) \n"
                };

                //var request = new SendDingDingRootMessageRequest
                //{
                //    userId = dingUserId,
                //    msgParam = JsonSerializer.SerializeToString(messageJson)
                //};

                // 发送钉钉机器人消息
                //await _dingDingRootHelper.SendDingDingRootMDMessage(request);

                // 钉钉机器人发消息统一接口 - 全天发
                await MsgHelper.SendDingdingNotifyFullTimeAsync(new MsgNotifyModel[]
                {
                    new MsgNotifyModel
                    {
                        Type = MsgNotifyType.钉钉机器人投递简历提醒,
                        EventTime = DateTime.Now,
                        UserId = recruitInfo.TeamHrId,
                        Data = messageJson.text
                    }
                });
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.RecruitDingTalkRobot}消息处理出错", Tools.GetErrMsg(e), (msg ?? string.Empty).ToString());
                await Task.Delay(500);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 获取消息跳转链接
    /// </summary>
    /// <returns></returns>
    private static string GetUrl()
    {
        string redirectUrl = $"https://linggong.nuopin.cn/ddlogin.html?corpId%3D1";// source=1是自定义变量，表示来源钉钉机器人，用来做跳转
        string url = $"dingtalk://dingtalkclient/action/openapp?corpid={Constants.CorpId}&container_type=work_platform&app_id=0_{Constants.DingDingAgent}&redirect_type=jump&redirect_url={redirectUrl}";
        return url;
    }

    /// <summary>
    /// 来源信息调整
    /// </summary>
    /// <param name="source"></param>
    /// <returns></returns>
    private string Exchange(string source)
    {
        return source switch
        {
            "微信小程序" => "微信小程序",
            "Web" => "诺快聘web端",
            "快手小程序" => "快手小程序",
            "快招工简历" => "快招工",
            "抖音小程序" => "抖音小程序",
            "简历导入" => "简历导入",
            "诺聘" => "诺聘平台",
            "其他" => "其他平台",
            _ => ""
        };
    }

    /// <summary>
    /// 钉钉消息已读状态更新
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task DingDingMessageReadStatus(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            // 等待执行完
            using var locker = MyRedis.TryLock("st:dingding:readstatus");
            if (locker == null)
                return;

            var _context = _contextFactory.CreateDbContext();
            // 查询未读消息，只支持24h之内
            var unReadList = _context.Dd_Notify_Record.Where(w => w.CreatedTime >= DateTime.Now.AddHours(-24)
            && !string.IsNullOrWhiteSpace(w.ProcessQueryKey)
            && (w.Type == MsgNotifyType.钉钉机器人投递简历提醒 || w.Type == MsgNotifyType.钉钉机器人HR初筛定时提醒)
            && w.ReadStatus != "READ").ToList();// && w.HrId == "212581386807644421"
            if (unReadList.Count == 0)
            {
                return;
            }

            // todo:感觉有隐患，一秒发200次请求会有问题不
            foreach (var item in unReadList)
            {
                item.ReadStatus = await _dingDingRootHelper.DingDingMessageReadStatus(item.ProcessQueryKey!);
            }

            _context.SaveChanges();
        }
        catch (Exception ex)
        {
            _logger.Error("更新钉钉消息已读状态出错", Tools.GetErrMsg(ex));
        }
    }
}