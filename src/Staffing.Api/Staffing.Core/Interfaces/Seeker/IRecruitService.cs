﻿using Config.CommonModel;
using Staffing.Model.Seeker.Recruit;

namespace Staffing.Core.Interfaces.Seeker;

public interface IRecruitService
{
    GetJDInfoResponse GetJDInfo(GetJDInfo model);
    Task<RegisterJDReqResponse> RegisterJDAsync(RegisterJDReq model);
    RegisterJDReq GetRegisterJD();
    DeliverResumeResponse DeliverResume(DeliverResume model);
    GetDeliverInfo GetDeliver(string id);
    EmptyResponse Complain(ComplainRequest model);
    GetDeliversResponse GetDelivers(GetDelivers model);
    EmptyResponse SeekerSetInterview(SeekerSetInterview model);
    SeekerGetInterviewInfo SeekerGetInterview(SeekerGetInterview model);
    EmptyResponse CancelDeliver(CancelDeliver model);

    /// <summary>
    /// 用户端获取offer通知
    /// </summary>
    /// <param name="offerId"></param>
    /// <returns></returns>
    GetOfferNoticeResponse GetOfferNotice(string offerId);
}