﻿using System.Text.Json.Serialization;
using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;

namespace Staffing.Model.Seeker.Recruit;

public class DeliverResume
{
    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }
}

public class GetJDInfo
{
    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }
}

public class GetJDInfoResponse
{
    /// <summary>
    /// 是否是京东职位
    /// </summary>
    public bool IsJdPost { get; set; }

    /// <summary>
    /// 顾问电话
    /// </summary>
    public string? HrMobile { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? HrId { get; set; }
}

public class RegisterJDReq
{
    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    // /// <summary>
    // /// 电话
    // /// </summary>
    // public string? Phone { get; set; }

    /// <summary>
    /// 身份证
    /// </summary>
    public string? IDCard { get; set; }

    /// <summary>
    /// 是否知晓健康证
    /// </summary>
    public bool? IsHealthCard { get; set; }

    /// <summary>
    /// 是否知晓购买工具箱
    /// </summary>
    public bool? IsToolbox { get; set; }

    /// <summary>
    /// 培训时间
    /// </summary>
    public DateTime? TrainingTime { get; set; }
}

public class RegisterJDReqResponse
{
    /// <summary>
    /// 1=成功，2=性别不符，3=年龄不符，4=身份证认证失败
    /// </summary>
    public int Result { get; set; } = 1;

    /// <summary>
    /// 投递Id
    /// </summary>
    public string? DeliveryId { get; set; }
}

public class DeliverResumeResponse
{
    /// <summary>
    /// 投递Id
    /// </summary>
    public string? DeliveryId { get; set; }

    /// <summary>
    /// 1=成功，2=信息不全
    /// </summary>
    public int Result { get; set; }
}

public class GetDelivers : QueryRequest
{
    /// <summary>
    /// 投递状态
    /// </summary>
    public DeliverStatus? DeliverStatus { get; set; }
}

public class GetDeliversResponse : QueryResponse
{
    public List<GetDeliverInfo> Rows { get; set; } = new List<GetDeliverInfo>();
}

public class GetDeliverInfo
{
    /// <summary>
    /// 投递Id
    /// </summary>
    public string? DeliveryId { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public GetPostDetail? Post { get; set; }

    /// <summary>
    /// 顾问
    /// </summary>
    public HrModel? Hr { get; set; }

    /// <summary>
    /// 协同顾问
    /// </summary>
    [JsonIgnore]
    public HrModel? TeamHr { get; set; }

    /// <summary>
    /// 代招企业
    /// </summary>
    public GetAgentEntDetail? AgentEnt { get; set; }

    [JsonIgnore]
    public RecruitStatus? RecruitStatus { get; set; }

    /// <summary>
    /// 投递状态
    /// </summary>
    public DeliverStatus? DeliverStatus { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? DeliverStatusName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    /// 职位Id
    /// </summary>
    public string? PostId { get; set; }

    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 顾问电话
    /// </summary>
    public string? HrMobile { get; set; }

    /// <summary>
    /// 是否可以取消
    /// </summary>
    public bool CanCancel { get; set; }

    /// <summary>
    /// 记录
    /// </summary>
    public List<GetDeliverRecord> Record { get; set; } = new List<GetDeliverRecord>();
}

public class GetDeliverRecord
{
    public string? Id { get; set; }

    /// <summary>
    /// 招聘id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 状态
    /// </summary>
    public Config.Enums.RecruitStatus Status { get; set; }

    /// <summary>
    /// 归档状态
    /// </summary>
    public Config.Enums.RecruitFileAway FileAway { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }
}

public class ComplainRequest
{
    /// <summary>
    /// 投递Id
    /// </summary>
    public string? DeliveryId { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Describe { get; set; } = string.Empty;
}

public class CancelDeliver
{
    /// <summary>
    /// 投递Id
    /// </summary>
    public string? DeliveryId { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }
}