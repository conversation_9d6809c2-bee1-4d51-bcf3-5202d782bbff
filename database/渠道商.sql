DROP TABLE IF EXISTS `qd_hr_channel`;
CREATE TABLE `qd_hr_channel` (
  `Id` varchar(18) NOT NULL,
  `ChannelUserId` varchar(18) NOT NULL COMMENT '渠道用户Id',
  `HrId` varchar(18) NOT NULL COMMENT '顾问Id',
  `Type` int NOT NULL COMMENT '类型',
  `Status` int NOT NULL COMMENT '状态',
  `IsDefaultHr` bit NOT NULL DEFAULT 0 COMMENT '是否默认顾问',
  `GroupQrCode` varchar(1024) NULL COMMENT '群二维码',
  `ChannelQrCode` varchar(1024) NULL COMMENT '渠道二维码',
  `EndTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '项目截止日期',
  `Describe` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `Talent` int NOT NULL DEFAULT 0 COMMENT '人才数量',
  `VirtualTalent` int NOT NULL DEFAULT 0 COMMENT '虚拟人才数量',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_qd_hr_channel_HrId_ChannelUserId` (`HrId`,`ChannelUserId`),
  KEY `IX_qd_hr_channel_HrId_Type_Status` (`HrId`,`Type`,`Status`,`IsDefaultHr`),
  KEY `IX_qd_hr_channel_ChannelUserId_Type_Status` (`ChannelUserId`,`Type`,`Status`),
  KEY `IX_qd_hr_channel_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='顾问渠道关系表';

ALTER TABLE talent_virtual ADD ChannelId varchar(18) NULL COMMENT '渠道Id';
ALTER TABLE user_hr ADD ChannelInvQrCode varchar(1024) NULL COMMENT '顾问邀约渠道二维码';

UPDATE talent_virtual a
JOIN `user` b on a.Mobile = b.Mobile
JOIN user_seeker c ON b.UserId = c.UserId
AND a.`Status` = 0
SET a.STATUS = 1,a.SeekerId = b.UserId;
