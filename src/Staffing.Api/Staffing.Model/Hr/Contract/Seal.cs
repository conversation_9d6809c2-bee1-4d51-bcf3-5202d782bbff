using Config.CommonModel;

namespace Staffing.Model.Hr.Contract;

public class UpdateSeal
{
    /// <summary>
    /// 机构名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 机构证件号
    /// </summary>
    public string? IdNumber { get; set; }
}

public class UpdateSealResponse
{
    /// <summary>
    /// E签宝机构Id
    /// </summary>
    public string? EOrgId { get; set; }
}

public class DeleteSeal
{
    /// <summary>
    /// 标识
    /// </summary>
    public string? Id { get; set; }
}


public class GetSeal : QueryRequest
{
    /// <summary>
    /// 机构名称
    /// </summary>
    public string? Name { get; set; }
}

public class GetSealResponse : QueryResponse
{
    public List<SealInfo> Rows { get; set; } = default!;
}

public class SealInfo
{
    /// <summary>
    /// 标识
    /// </summary>
    public string? SealId { get; set; }
    
    /// <summary>
    /// 机构Id
    /// </summary>
    public string? OrgId { get; set; }

    /// <summary>
    /// 印章名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 印章类型
    /// </summary>
    public int Type { get; set; }

    /// <summary>
    /// 印章宽度
    /// </summary>
    public int Width { get; set; }

    /// <summary>
    /// 印章高度
    /// </summary>
    public int Height { get; set; }

    /// <summary>
    /// 印章地址
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// 是否默认印章
    /// </summary>
    public ulong Default { get; set; }

    /// <summary>
    /// E签宝印章ID
    /// </summary>
    public string? EsealId { get; set; }

    /// <summary>
    /// E签宝印章fileKey
    /// </summary>
    public string? EfileKey { get; set; }

    /// <summary>
    /// 删除
    /// </summary>
    public bool Deleted { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    public string? UpdatedBy { get; set; }

    public DateTime? UpdatedTime { get; set; }
    public DateTime? CreatedTime { get; set; }
}