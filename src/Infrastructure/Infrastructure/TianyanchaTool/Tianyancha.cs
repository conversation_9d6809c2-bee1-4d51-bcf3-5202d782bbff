﻿using Flurl.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Infrastructure.TianyanchaTool.Tianyancha;

public class Tianyancha
{
    public static readonly string token = "8960384d-0bec-41c8-abff-e333e6feceaf";

    // 天眼查企业基本信息接口地址 1116
    public static readonly string _baseInfoUrl = $"http://open.api.tianyancha.com/services/open/ic/baseinfo/normal?keyword=";

    // 天眼查特殊企业基本信息接口地址 1117
    public static readonly string _specialInfoUrl = $"http://open.api.tianyancha.com/services/open/ic/baseinfo/special?keyword=";

    public static string _interfaceId = "1116";

    // 天眼查工商信息接口地址 - 没用这个
    public static readonly string _industryInfoUrl = $"http://open.api.tianyancha.com/services/open/cb/ic/2.0?keyword=";

    private readonly StaffingContext _context;
    public Tianyancha(StaffingContext context)
    {
        _context = context;
    }

    public void GetIndustryInfo(string keyWords)
    {
        var url = $"{_baseInfoUrl}{keyWords}";
        // 请求处理
        var responseStr = httpGet(url, token);
        Console.WriteLine(responseStr);
    }

    public Tyc_Enterprise? GetCompanyBaseInfo(string keyWords)
    {
        var url = $"{_baseInfoUrl}{keyWords}";
        // 请求处理
        var responseStr = httpGet(url, token).GetAwaiter().GetResult();
        if (!string.IsNullOrEmpty(responseStr) && (responseStr.Contains("无数据") || responseStr.Contains("无结果")))
        {
            url = $"{_specialInfoUrl}{keyWords}";
            _interfaceId = "1117";
            responseStr = httpGet(url, token).GetAwaiter().GetResult();
        }

        if (!string.IsNullOrEmpty(responseStr) && responseStr.Contains("余额不足"))
        {
            throw new Exception($"余额不足，查询公司：{keyWords}");
        }
        // 写入数据库
        return SaveData(responseStr);
    }

    /// <summary>
    /// 请求数据
    /// </summary>
    /// <param name="url"></param>
    /// <param name="token"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private async Task<string> httpGet(string url, string token)
    {
        try
        {
            var result = await url.WithHeader("Authorization", token)
            .GetAsync()
            .ReceiveString();

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 数据入库
    /// </summary>
    /// <param name="responseStr"></param>
    /// <exception cref="NotImplementedException"></exception>
    private Tyc_Enterprise? SaveData(string responseStr)
    {
        JObject obj = (JObject)JsonConvert.DeserializeObject(responseStr)!;
        var infos = JsonConvert.DeserializeObject<Tyc_Enterprise>(obj["result"]!.ToString());
        if (infos != null)
        {
            infos.InterfaceId = _interfaceId;
            var existsInfo = _context.Tyc_Enterprise.FirstOrDefault(f => f.Id == infos.Id);
            if (existsInfo == null)
            {
                _context.Add(infos);
            }
            else // todo:这里缺少更新其他字段
            {
                //existsInfo = infos;
                existsInfo.InterfaceId = _interfaceId;
                existsInfo.ToTime = infos.ToTime;
                existsInfo.FromTime = infos.FromTime;
                existsInfo.UpdatedTime = DateTime.Now;
            }

            _context.SaveChanges();
        }
        return infos;
    }
}
