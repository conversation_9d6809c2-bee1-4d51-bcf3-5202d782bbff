﻿using System.Text.Json.Serialization;
using Config.CommonModel.Business;
using Config.Enums;
using Staffing.Entity.Staffing;

namespace Staffing.Model.Hr.User;

public class GetAppInfo
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 应用Id
    /// </summary>
    public string? AppId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 别名
    /// </summary>
    public string Alias { get; set; } = string.Empty;

    /// <summary>
    /// 图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// Url
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public SystemAppType Type { get; set; }

    /// <summary>
    /// 概要
    /// </summary>
    public string? Abstract { get; set; }

    /// <summary>
    /// 短概要
    /// </summary>
    public string? ShortAbstract { get; set; }

    /// <summary>
    /// 应用详情
    /// </summary>
    public string? AppInfo { get; set; }

    /// <summary>
    /// 使用说明
    /// </summary>
    public string? Manual { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ActiveStatus Status { get; set; }

    /// <summary>
    /// 用户状态
    /// </summary>
    [JsonIgnore]
    public UserStatus UserStatus { get; set; }

    /// <summary>
    /// 审核状态
    /// </summary>
    public AppApplyStatus ApplyStatus { get; set; } = AppApplyStatus.未申请;

    // /// <summary>
    // /// 零工市场数据
    // /// </summary>
    // public QuickJobData? QuickJobData { get; set; }

    // /// <summary>
    // /// 零工市场顾问Id
    // /// </summary>
    // public string? QuickJobHrId { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 协同的项目Id
    /// </summary>
    public string? TeamProjectId { get; set; }

}

public class UseApp
{
    /// <summary>
    /// AppId
    /// </summary>
    public string? AppId { get; set; }
}