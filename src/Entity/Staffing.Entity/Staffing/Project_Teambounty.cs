﻿// using System.ComponentModel.DataAnnotations;
// using System.ComponentModel.DataAnnotations.Schema;
// using Config.Enums;

// namespace Staffing.Entity.Staffing;

// /// <summary>
// /// 项目协同分润表
// /// </summary>
// [Table("project_teambounty")]
// public class Project_Teambounty
// {
//     /// <summary>
//     /// 主键id
//     /// </summary>
//     [Key]
//     public string Id { get; set; } = EntityTools.SnowflakeId();

//     /// <summary>
//     /// 项目Id
//     /// </summary>
//     public string ProjectId { get; set; } = default!;

//     /// <summary>
//     /// 项目名称
//     /// </summary>
//     public string? ProjectName { get; set; }

//     /// <summary>
//     /// 项目编码
//     /// </summary>
//     public string? ProjectCode { get; set; }

//     /// <summary>
//     /// 协同项目Id
//     /// </summary>
//     public string TeamProjectId { get; set; } = default!;

//     /// <summary>
//     /// 招聘流程Id
//     /// </summary>
//     public string? RecruitId { get; set; } = default!;

//     /// <summary>
//     /// 第三方简历池主键id
//     /// </summary>
//     public string? ResumeBufferId { get; set; }

//     /// <summary>
//     /// Hr职位Id
//     /// </summary>
//     public string TeamPostId { get; set; } = default!;

//     /// <summary>
//     /// 线索跟进者Id
//     /// </summary>
//     public string? FollowerId { get; set; }

//     /// <summary>
//     /// 原始职位Id
//     /// </summary>
//     public string PostId { get; set; } = default!;

//     /// <summary>
//     /// 职位名称
//     /// </summary>
//     public string? PostName { get; set; }

//     /// <summary>
//     /// 求职者Id
//     /// </summary>
//     public string SeekerId { get; set; } = default!;

//     /// <summary>
//     /// 求职者名称
//     /// </summary>
//     public string? SeekerName { get; set; }

//     /// <summary>
//     /// 求职者电话
//     /// </summary>
//     public string? SeekerMobile { get; set; }

//     /// <summary>
//     /// 项目经理Id
//     /// </summary>
//     public string HrId { get; set; } = default!;

//     /// <summary>
//     /// 主创名称
//     /// </summary>
//     public string? HrName { get; set; }

//     /// <summary>
//     /// 渠道hrId
//     /// </summary>
//     public string? ChannelHrId { get; set; }

//     /// <summary>
//     /// 渠道商名称
//     /// </summary>
//     public string? ChannelHrName { get; set; }

//     /// <summary>
//     /// 协同HrId
//     /// </summary>
//     public string TeamHrId { get; set; } = default!;

//     /// <summary>
//     /// 协同名称
//     /// </summary>
//     public string? TeamHrName { get; set; }

//     /// <summary>
//     /// 合作模式
//     /// </summary>
//     public ProjectPaymentNode? PaymentNode { get; set; }

//     /// <summary>
//     /// 打款天数
//     /// </summary>
//     public int? PaymentDays { get; set; }

//     /// <summary>
//     /// 打款方式
//     /// </summary>
//     public ProjectPaymentType? PaymentType { get; set; }

//     /// <summary>
//     /// 金额
//     /// </summary>
//     public decimal? Money { get; set; }

//     /// <summary>
//     /// 状态
//     /// </summary>
//     [ConcurrencyCheck]
//     public ProjectTeambountyStatus Status { get; set; }

//     /// <summary>
//     /// 来源
//     /// </summary>
//     public TeamBountySource Source { get; set; }

//     /// <summary>
//     /// 是否无效简历，默认0-否，1-是
//     /// </summary>
//     public int IfInValid { get; set; }

//     /// <summary>
//     /// 是否减库存，默认0-否，1-是
//     /// </summary>
//     [ConcurrencyCheck]
//     public int IfStockOut { get; set; } = 0;

//     /// <summary>
//     /// 交付失败原因描述
//     /// </summary>
//     public string? Description { get; set; }

//     /// <summary>
//     /// 结算时间
//     /// </summary>
//     public DateTime? SettlementTime { get; set; }

//     /// <summary>
//     /// 奖励时效类型（单次结算, 长期结算）
//     /// </summary>
//     public PostRewardType? RewardType { get; set; }

//     /// <summary>
//     /// 结算周期(按小时、天、月)
//     /// </summary>
//     public PostPaymentCycle? PaymentCycle { get; set; }

//     /// <summary>
//     /// 返费周期, null或0代表无限期
//     /// </summary>
//     public int? PaymentDuration { get; set; }

//     /// <summary>
//     /// 计算过保开始时间
//     /// </summary>
//     public DateTime? GuaranteeStartDate { get; set; }

//     // /// <summary>
//     // /// 结算金额
//     // /// </summary>
//     // public decimal? SettlementMoney { get; set; }

//     // /// <summary>
//     // /// 协同实收佣金
//     // /// </summary>
//     // public decimal? SettlementActualMoney { get; set; }

//     // /// <summary>
//     // /// 协同平台抽佣比例
//     // /// </summary>
//     // public decimal? PlatformSettlementRate { get; set; }

//     // /// <summary>
//     // /// 协同平台抽佣金额
//     // /// </summary>
//     // public decimal? PlatformSettlementMoney { get; set; }

//     // /// <summary>
//     // /// 渠道商结算金额
//     // /// </summary>
//     // public decimal? ChannelSettlementMoney { get; set; }

//     // /// <summary>
//     // /// 渠道商结算比例
//     // /// </summary>
//     // public decimal? ChannelSettlementRate { get; set; }

//     /// <summary>
//     /// 结算表主键id
//     /// </summary>
//     public string? SettlementId { get; set; }

//     /// <summary>
//     /// 渠道商合同关联id
//     /// </summary>
//     public string? ChannelContractId { get; set; }

//     /// <summary>
//     /// 结算状态
//     /// </summary>
//     public SettlementType SettlementStatus { get; set; } = SettlementType.待结算;

//     /// <summary>
//     /// 更新时间
//     /// </summary>
//     public DateTime UpdatedTime { get; set; } = DateTime.Now;

//     /// <summary>
//     /// 创建时间
//     /// </summary>
//     public DateTime CreatedTime { get; set; } = DateTime.Now;

//     /// <summary>
//     /// 渠道关系Id
//     /// </summary>
//     public string? ChannelId { get; set; }

//     public bool Deleted { get; set; } = false;

//     /// <summary>
//     /// 销售佣金
//     /// </summary>
//     public decimal SalesBounty { get; set; }

//     /// <summary>
//     /// 项目管理佣金
//     /// </summary>
//     public decimal ManagerBounty { get; set; }

//     /// <summary>
//     /// 线索佣金
//     /// </summary>
//     public decimal ClueBounty { get; set; }

//     /// <summary>
//     /// 邀面佣金(邀面)
//     /// </summary>
//     public decimal FollowerBounty { get; set; }

//     /// <summary>
//     /// 平台佣金
//     /// </summary>
//     public decimal PlatformBounty { get; set; }

//     /// <summary>
//     /// 销售佣金比例
//     /// </summary>
//     public decimal SalesRate { get; set; }

//     /// <summary>
//     /// 项目管理佣金比例
//     /// </summary>
//     public decimal ManagerRate { get; set; }

//     /// <summary>
//     /// 线索佣金比例
//     /// </summary>
//     public decimal ClueRate { get; set; }

//     /// <summary>
//     /// 跟进人佣金比例(邀面)
//     /// </summary>
//     public decimal FollowerRate { get; set; }

//     /// <summary>
//     /// 平台佣金比例
//     /// </summary>
//     public decimal PlatformRate { get; set; }

//     /// <summary>
//     /// 销售Id
//     /// </summary>
//     public string? SaleUserId { get; set; }

//     // public Project Project { get; set; } = default!;
    
//     [ForeignKey("RecruitId")]
//     public Recruit Recruit { get; set; } = default!;
//     // public Project_Team Project_Team { get; set; } = default!;
//     // public User_Seeker User_Seeker { get; set; } = default!;
//     // /// <summary>
//     // /// 项目经理
//     // /// </summary>
//     // public User_Hr User_Hr { get; set; } = default!;
//     // /// <summary>
//     // /// 销售经理
//     // /// </summary>
//     // public User_Hr SaleUser { get; set; } = default!;
//     // /// <summary>
//     // /// 线索方
//     // /// </summary>
//     // public User_Hr Team_User_Hr { get; set; } = default!;
//     // public Post Post { get; set; } = default!;
//     // public Post_Team Post_Team { get; set; } = default!;
//     // public List<Project_Teambounty_Stage> Project_Teambounty_Stage { get; set; } = default!;
//     // public List<Project_Settlement> project_Settlements { get; set; } = default!;
// }