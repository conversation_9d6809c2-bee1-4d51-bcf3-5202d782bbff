﻿using Config.CommonModel.SCRMBeschannels;
using Flurl.Http;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace Infrastructure.Common
{
    [Service(ServiceLifetime.Transient)]
    public class SCRMBeschannelsHelper
    {
        private readonly BeschannelsOptions _options;
        private const string _SyncToBeschannelMemberMobilesCacheKey = "SyncToBeschannelMemberMobile";
        private const string _BeschannelEmployeMobilesCacheKey = "BeschannelEmployeMobiles";
        private readonly string OpenAiUrl = "https://openapi.beschannels.com";
        public SCRMBeschannelsHelper(IOptionsSnapshot<BeschannelsOptions> config)
        {
            _options = config.Value;
        }
        
        /// <summary>
        /// 获取分类列表
        /// https://apifox.com/apidoc/shared-0f5e5d40-1266-4d0d-8e48-9f269fae6acf/api-********
        /// </summary>
        /// <returns></returns>
        public async Task<(bool Result, string? ErrMsg, List<BesChannelsCateOutput>? Data)> GetCateList()
        {
            var api = "/ma-open-api/category/list";
            var input = new
            {
                appid = _options.WeChatOffiaccountID,//公众号的appid
                cate_type = 2 //模块 1=资料下载（老内容管理），2=会议管理，默认值2
            };
            var obj = await Post<List<BesChannelsCateOutput>>(api, input);
            if (obj.code == 0)
                return (true, string.Empty, obj.data);
            else
                return (false, obj.msg, null);
        }
        /// <summary>
        /// 获取会议列表
        /// https://apifox.com/apidoc/shared-0f5e5d40-1266-4d0d-8e48-9f269fae6acf/api-********
        /// </summary>
        /// <param name="cateId"></param>
        /// <returns></returns>
        public async Task<(bool Result, string? ErrMsg, List<BesChannelsMeetingOutput>? Data)> GetMeettingList(int? cateId)
        {
            var api = "/ma-open-api/meeting/list";
            var input = new
            {
                appid = _options.WeChatOffiaccountID,//公众号的appid
                                                     //meeting_name ="",//会议名称（需要进行urlencode）,可选
                                                     //meetingIdStr="",//会议id,可选
                category_id = cateId,//分类id
                                     //page_no = 1,//当前请求页码,可选
                                     //page_size = 20 //每页返回数量，默认100，最大500,可选
            };
            var obj = await Post<List<BesChannelsMeetingOutput>>(api, input);
            if (obj.code == 0)
                return (true, string.Empty, obj.data);
            else
                return (false, obj.msg, null);
        }

        /// <summary>
        /// 会员管理-获取基础信息
        /// https://apifox.com/apidoc/shared-0f5e5d40-1266-4d0d-8e48-9f269fae6acf/api-*********
        /// </summary>
        /// <returns></returns>
        public async Task<(bool Result, string? ErrMsg, MemberBasicOutput? Data)> MemberBasicGet(string Mobile)
        {
            var api = "/ma-open-api/member/basic-get";
            ///ma-open-api/member/basic-get
            var obj = await Post<MemberBasicOutput>(api, new { mobile = Mobile });
            if (obj.code == 0)
                return (true, string.Empty, obj.data);
            else
                return (false, obj.msg, null);
        }

        /// <summary>
        /// 会员管理-根据会员id或会员手机号获取会员详细信息
        /// https://apifox.com/apidoc/shared-0f5e5d40-1266-4d0d-8e48-9f269fae6acf/api-*********
        /// </summary>
        /// <returns></returns>
        public async Task<(bool IsHaveUser, string? ErrMsg, MemberInfoOutputData? Data)> MemberGetInfo(string Mobile)
        {
            var api = "/ma-open-api/app-api/member/getmemberInfo";

            var obj = await Post<MemberInfoOutput>(api, new { mobile = Mobile });
            if (obj.code == 0 && obj.data != null)
            {
                MyRedis.Client.SAdd(_SyncToBeschannelMemberMobilesCacheKey, Mobile);
                return (true, string.Empty, obj.data?.Data);
            }
            else if (obj.code == 80022)//暂无相关数据
                return (false, obj.msg, null);
            else
                return (false, obj.msg, null);
        }

        /// <summary>
        /// 会员/员工(注册/更新)
        /// https://apifox.com/apidoc/shared-0f5e5d40-1266-4d0d-8e48-9f269fae6acf/api-*********
        /// </summary>
        /// <returns></returns>
        public async Task<(bool Result, string? ErrMsg, string? Data)> MemberSubmit(BesChannelsMemmberInput input)
        {
            var api = "/ma-open-api/app-api/member/addFiled";

            var obj = await Post<string>(api, input.AddProperty("appid", _options.WeChatOffiaccountID));
            if (obj.code == 0)
            {
                MyRedis.Client.SAdd(_SyncToBeschannelMemberMobilesCacheKey, input.Mobile);
                return (true, string.Empty, obj.data);
            }
            else
                return (false, obj.msg, null);
        }

        /// <summary>
        /// 验证会员/员工是否已同步至致趣百川
        /// </summary>
        /// <param name="Mobile"></param>
        /// <returns></returns>
        public async Task<bool> CheckMemberIsExists(string? Mobile)
        {
            if (string.IsNullOrEmpty(Mobile))
                return false;
            if (MyRedis.Client.SIsMember(_SyncToBeschannelMemberMobilesCacheKey, Mobile))
                return true;

            var member = await MemberGetInfo(Mobile);
            if (member.IsHaveUser)
                MyRedis.Client.SAdd(_SyncToBeschannelMemberMobilesCacheKey, Mobile);

            return member.IsHaveUser;
        }

        /// <summary>
        /// 验证用户是否为员工
        /// </summary>
        /// <param name="Mobile"></param>
        /// <returns>true=员工，false=未知或会员</returns>
        public async Task<bool> CheckMemberIsEmploye(string? Mobile)
        {
            if (string.IsNullOrEmpty(Mobile))
                return false;
            if (MyRedis.Client.SIsMember(_BeschannelEmployeMobilesCacheKey, Mobile))
                return true;

            var member = await MemberGetInfo(Mobile);
            if (member.IsHaveUser && member.Data != null && !string.IsNullOrEmpty(member.Data.Level) && member.Data.Level.Contains("员工"))
            {
                MyRedis.Client.SAdd(_BeschannelEmployeMobilesCacheKey, Mobile);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 会议报名【验证用户是否已提交至致趣百川】
        /// https://apifox.com/apidoc/shared-0f5e5d40-1266-4d0d-8e48-9f269fae6acf/api-*********
        /// </summary>
        /// <returns>Data=首次报名成功返回data-id(报名失败或重复调用时返回空字符)</returns>
        public async Task<(bool Result, string? ErrMsg, string? Data)> MeetingEntryWithCheckUser(BesChannelsMeetingEntryInput input)
        {
            var temp = new BesChannelsMemmberInput
            {
                Mobile = input.Mobile,
                Identity = "1",
                SvipSource = "11"
            };
            //尚未同步会员/员工信息时，调用同步接口
            if (!(await CheckMemberIsExists(input.Mobile)))
                await MemberSubmit(temp);

            var result = await MeetingEntry(input);
            if (result.Result == OutputEnum.手机号未注册)
            {
                await MemberSubmit(temp);
                result = await MeetingEntry(input);
            }
            return (result.Result == OutputEnum.成功, result.ErrMsg, result.Data);
        }

        /// <summary>
        /// 会议报名
        /// https://apifox.com/apidoc/shared-0f5e5d40-1266-4d0d-8e48-9f269fae6acf/api-*********
        /// </summary>
        /// <returns>Data=首次报名成功返回data-id(报名失败或重复调用时返回空字符)</returns>
        public async Task<(OutputEnum Result, string? ErrMsg, string? Data)> MeetingEntry(BesChannelsMeetingEntryInput input)
        {
            var api = "/ma-open-api/app-api/meetingSap/meetingEntry";

            var obj = await Post<MemberSubmitOutput>(api, input.AddProperty("appid", _options.WeChatOffiaccountID));
            if (obj.code == 0)
                return (OutputEnum.成功, string.Empty, obj.data?.DataID);
            else if (obj.code == 91015)
                return (OutputEnum.手机号未注册, string.Empty, obj.data?.DataID);
            else
                return (OutputEnum.失败, obj.msg, null);
        }

        /// <summary>
        /// 获取token
        /// https://apifox.com/apidoc/shared-0f5e5d40-1266-4d0d-8e48-9f269fae6acf/api-********
        /// </summary>
        /// <returns></returns>
        private async Task<string?> GetToken()
        {
            var tokenKey = $"TokenBesChannels";
            var token = MyRedis.Client.Get(tokenKey);
            if (string.IsNullOrWhiteSpace(token))
            {
                var api = "/api/get-access-token";
                var input = new
                {
                    appId = _options.AppId,
                    appSecret = _options.AppSecret,
                };
                var obj = await Post<BesChannelsTokenOutput>(api, input);
                if (obj != null && obj.code == 0)
                {
                    token = obj.data.access_token;
                    //诺聘token有效期为7200秒（2小时）
                    MyRedis.Client.Set(tokenKey, token, TimeSpan.FromSeconds(obj.data.expires));
                }
            }
            return token;
        }

        /// <summary>
        /// 智趣百川API POST请求方法
        /// </summary>
        /// <param name="method">方法名称</param>
        /// <param name="param">请求参数</param>
        /// <returns></returns>
        private async Task<BesChannelsBaseOutput<T>> Post<T>(string method, object param)
        {
            // 设置要请求的 URL  
            string url = $"{OpenAiUrl}{method}";
            if (!method.Contains("get-access-token"))
            {
                var token = await GetToken();
                url += "?access_token=" + token;
            }

            string ret = string.Empty;
            try
            {
                // 读取响应内容  
                ret = await url.PostJsonAsync(param).ReceiveString();
                BesChannelsBaseOutput<T>? result = Newtonsoft.Json.JsonConvert.DeserializeObject<BesChannelsBaseOutput<T>>(ret);

                return result ?? new BesChannelsBaseOutput<T> { code = -99, msg = ret };
            }
            catch //(Exception ex)
            {
                return new BesChannelsBaseOutput<T> { code = -999, msg = "解析返回结果失败:" + ret };
            }
        }
    }
}
