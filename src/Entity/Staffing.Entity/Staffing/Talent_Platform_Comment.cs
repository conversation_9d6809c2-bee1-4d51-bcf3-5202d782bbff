﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 平台人才库简历评论表
/// </summary>
[Table("talent_platform_comment")]
public class Talent_Platform_Comment
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 平台人才库id
    /// </summary>
    public string PlatformId { get; set; } = default!;

    /// <summary>
    /// 评论内容
    /// </summary>
    public string Content { get; set; } = default!;

    /// <summary>
    /// HrId
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// HrName
    /// </summary>
    public string HrName { get; set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Talent_Platform Talent_Platform { get; set; } = default!;

    public User_Hr User_Hr { get; set; } = default!;
}

