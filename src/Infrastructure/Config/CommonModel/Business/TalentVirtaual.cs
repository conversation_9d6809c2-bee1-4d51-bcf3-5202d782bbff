
using Config.Enums;

namespace Config.CommonModel.Business;

/// <summary>
/// 工作经历（详情子表）
/// </summary>
public class TalentVirtaualResumeWorkSub
{
    /// <summary>
    /// 工作经历id
    /// </summary>
    public string? WorkId { get; set; }

    /// <summary>
    /// 公司名称
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 公司部门
    /// </summary>
    public string? Department { get; set; }

    /// <summary>
    /// 公司Logo
    /// </summary>
    public string? CompanyLogo { get; set; }

    /// <summary>
    /// 行业名称
    /// </summary>
    public string? IndustryName { get; set; }

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 最低薪资
    /// </summary>
    public decimal? MinSalary { get; set; }

    /// <summary>
    /// 最高薪资
    /// </summary>
    public decimal? MaxSalary { get; set; }

    /// <summary>
    /// 公司描述
    /// </summary>
    public string? CompanyRemarks { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 教育经历（详情子表）
/// </summary>
public class TalentVirtaualResumeEduSub
{
    /// <summary>
    /// 教育经历id
    /// </summary>
    public string? EduId { get; set; }

    /// <summary>
    /// 学校名称
    /// </summary>
    public string? SchoolName { get; set; }

    /// <summary>
    /// 是否全日制
    /// </summary>
    public bool? IsFullTime { get; set; }

    /// <summary>
    /// 学校logo
    /// </summary>
    public string? SchoolLogo { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public TalentVirtualEducation? Education { get; set; }

    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 专业
    /// </summary>
    public string? MajorName { get; set; }

    /// <summary>
    /// 学校描述
    /// </summary>
    public string? SchoolRemarks { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 项目经历（详情子表）
/// </summary>
public class TalentVirtaualResumeProjectSub
{
    /// <summary>
    /// 项目经历id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 项目logo
    /// </summary>
    public string? ProjectLogo { get; set; }

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 项目描述
    /// </summary>
    public string? ProjectRemarks { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 求职期望（详情子表）
/// </summary>
public class TalentVirtaualResumeHopeSub
{
    /// <summary>
    /// 求职期望id
    /// </summary>
    public string? HopeId { get; set; }

    /// <summary>
    /// 期望城市
    /// </summary>
    public string? HopeCity { get; set; }

    /// <summary>
    /// 期望行业名称集合
    /// </summary>
    public string? IndustryName { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 最低薪资
    /// </summary>
    public decimal? MinSalary { get; set; }

    /// <summary>
    /// 最高薪资
    /// </summary>
    public decimal? MaxSalary { get; set; }
}