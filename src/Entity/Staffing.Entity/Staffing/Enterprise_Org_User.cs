﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 组织架构人员
/// </summary>
[Table("enterprise_org_user")]
public class Enterprise_Org_User
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 组织架构Id
    /// </summary>
    public string OrgId { get; set; } = default!;

    /// <summary>
    /// 用户Id
    /// </summary>
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 创建人
    /// </summary>
    public string? Creator { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Enterprise_Org Enterprise_Org { get; set; } = default!;
    public User_Hr User_Hr { get; set; } = default!;
}
