-- log_info表
CREATE TABLE staffing_log.`log_info` (
	`Id` INT(10) NOT NULL AUTO_INCREMENT,
	`Type` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`Title` VARCHAR(500) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`Data` TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`CreatedTime` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`CreatedUser` VARCHAR(50) NOT NULL COLLATE 'utf8mb4_general_ci',
	PRIMARY KEY (`Id`) USING BTREE
)
COLLATE='utf8mb4_general_ci'
ENGINE=INNODB;

-- post 职位表新增字段
alter table post 
add column DeliveryNumber INT(10) COMMENT '交付数量' AFTER RecruitNumber,
add column LeftStock INT(10) COMMENT '剩余实时库存,简历交付为简历剩余份数，否则是剩余招聘人数' AFTER DeliveryNumber;

-- recruit 表新增字段
alter table recruit
add column InvalidReason VARCHAR(300) COMMENT '无效原因';