﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 钉钉推送消息记录表
/// </summary>
[Table("msg_dingdingpush")]
public class Msg_DingDingPush
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// userid
    /// </summary>
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 用户钉钉userid
    /// </summary>
    public string DingUserid { get; set; } = default!;

    /// <summary>
    /// 创建项目(进行中）
    /// </summary>
    public int CreateProject { get; set; } = 0;

    /// <summary>
    /// 开启协同（集团级）
    /// </summary>
    public int EnableCollaboration { get; set; } = 0;

    /// <summary>
    /// 协同项目
    /// </summary>
    public int CollaborativeProject { get; set; } = 0;

    /// <summary>
    /// 可报名职位
    /// </summary>
    public int RegistrablePositions { get; set; } = 0;

    /// <summary>
    /// 初筛
    /// </summary>
    public int ScreeningNum { get; set; } = 0;

    /// <summary>
    /// 面试安排
    /// </summary>
    public int InterviewNum { get; set; } = 0;

    /// <summary>
    /// 即时通讯未回复
    /// </summary>
    public int MessageNoReply { get; set; } = 0;

    /// <summary>
    /// 协同交付中
    /// </summary>
    public int CoordinationDelivery { get; set; } = 0;

    /// <summary>
    /// 协同交付成功
    /// </summary>
    public int CoordinationSuccess { get; set; } = 0;

    /// <summary>
    /// 协同交付失败
    /// </summary>
    public int CoordinationFail { get; set; } = 0;

    /// <summary>
    /// 简历储备
    /// </summary>
    public int ReserveVirtual { get; set; } = 0;

    /// <summary>
    /// 真实用户
    /// </summary>
    public int ReservePlatform { get; set; } = 0;

    /// <summary>
    /// 消息推送时间
    /// </summary>
    public DateTime? PushTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}

