using Config;
using Config.Enums;
using Infrastructure.Common;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using ServiceStack.Text;
using Staffing.Entity;
using Infrastructure.CommonService;
using Infrastructure.Proxy;
using Config.CommonModel.Business;
using Microsoft.AspNetCore.Http;
using System.Net;
using Config.CommonModel;

namespace Infrastructure.Middleware;

public class AuthorizationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly CacheHelper _cacheHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly CommonUserService _commonUserService;
    private readonly NuoPinApi _nuoPinApi;
    private readonly ConfigManager _config;

    public AuthorizationMiddleware(RequestDelegate next, CacheHelper cacheHelper,
    IHostEnvironment hostingEnvironment,
    CommonUserService commonUserService, NuoPinApi nuoPinApi,
    IOptionsSnapshot<ConfigManager> config)
    {
        _next = next;
        _cacheHelper = cacheHelper;
        _hostingEnvironment = hostingEnvironment;
        _commonUserService = commonUserService;
        _nuoPinApi = nuoPinApi;
        _config = config.Value;
    }

    public async Task Invoke(HttpContext context, RequestContext _user, StaffingContext _context)
    {
        var metadatas = context.GetEndpoint()?.Metadata;
        var apiMeta = metadatas?.GetMetadata<ApiMeta>();
        var authType = apiMeta?.AuthType;

        _user.RequestId = context.TraceIdentifier;
        _user.ConnectionId = context.Connection.Id;

        var token = string.Empty;

        //拿到token
        if (context.Request!.Headers.TryGetValue("Authorization", out var headerAuth))
        {
            var headerAuthStr = headerAuth.FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(headerAuthStr) && headerAuthStr.StartsWith("Bearer"))
            {
                token = headerAuthStr.Substring("Bearer".Length).Trim();
                _user.AccessToken = token;
            }
        }

        //clientid
        if (context.Request.Headers.TryGetValue("x-requested-with", out var clientId))
            _user.ClientId = clientId.ToString().Trim();

        //拿到顾问Id
        if (context.Request.Headers.TryGetValue("AdviserId", out var adviserId))
            _user.AdviserId = adviserId.ToString().Trim();

        //拿到渠道Id
        if (context.Request.Headers.TryGetValue("ChannelId", out var channelId))
            _user.ChannelId = channelId.ToString().Trim();

        //拿到渠道来源
        if (context.Request.Headers.TryGetValue("ChannelSource", out var channelSource))
            if (long.TryParse(channelSource.ToString().Trim(), out var csi))
                _user.ChannelSource = csi;

        //如果没有顾问，设置为顾问为平台
        if (string.IsNullOrWhiteSpace(_user.AdviserId))
            _user.AdviserId = Constants.PlatformHrId;
        else
        {
            //顾问id是否存在
            var advExists = _cacheHelper.GetMemoryCache<bool?>(() =>
            {
                var e = _context.User_Hr.Any(x => x.UserId == _user.AdviserId && x.Status == UserStatus.Active);
                return e;
            }, $"AdviserIdExists:{_user.AdviserId}", 300) ?? false;

            if (!advExists)
                _user.AdviserId = Constants.PlatformHrId;
        }

        if (authType == ApiAuthType.内部服务)
        {
            if (string.IsNullOrWhiteSpace(token) || !_config.ServiceKeys!.Contains(token))
            {
                context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                return;
            }
            _user.Id = token;
        }

        ShortUserInfo? usr = null;

        //如果有token，解析
        if (!string.IsNullOrWhiteSpace(token) && authType != ApiAuthType.内部服务)
        {
            var tkmd5 = Md5Helper.Md5(token);
            var usrCacheKey = $"ut:{tkmd5}";
            var disable = _hostingEnvironment.IsProduction() ? 600 : 60;

            usr = MyRedis.Client.Get<ShortUserInfo?>(usrCacheKey);

            //如果缓存不存在
            if (usr == null)
            {
                //加锁获取token
                using (var locker = await MyRedis.Lock($"tklk:{tkmd5}", 10))
                {
                    if (locker == null)
                        throw new Exception("请求获取锁失败");

                    usr = MyRedis.Client.Get<ShortUserInfo?>(usrCacheKey);
                    if (usr == null)
                    {
                        //判断是否自己token
                        var isMyToken = token.StartsWith(Constants.MyTokenPrefix);

                        ClientType? client = null;

                        if (isMyToken)
                        {
                            //走自己认证
                            var at = _context.Token_Access
                            .Where(x => x.Token == token && x.ExpirationTime > DateTime.Now)
                            .Select(s => new
                            {
                                s.Type,
                                s.UserId,
                                s.Client
                            }).FirstOrDefault();

                            if (at != null)
                            {
                                var tokenType = at.Type;
                                client = at.Client ?? ClientType.Other;

                                //查询对应用户信息
                                var id = at?.UserId ?? string.Empty;

                                //目前只支持用户端
                                if (tokenType == TokenType.用户端)
                                {
                                    usr = _context.User_Seeker.Where(x => x.UserId == id)
                                    .Select(s => new ShortUserInfo
                                    {
                                        UserId = s.UserId,
                                        NickName = s.NickName,
                                        Mobile = s.User.Mobile,
                                        Status = s.Status,
                                        AppType = at!.Type,
                                        ClientType = client
                                    }).FirstOrDefault();

                                    //更新用户活跃时间
                                    if (!string.IsNullOrWhiteSpace(usr?.UserId))
                                        _commonUserService.UserActivity(usr.UserId, client ?? ClientType.SeekerApplet, _user.RequestIpAddress);
                                }
                                else if (tokenType == TokenType.企业端)
                                {
                                    var hr = _context.User_Hr
                                        .Where(x => x.UserId == id)
                                        .Select(s => new
                                        {
                                            UserId = s.UserId,
                                            NickName = s.NickName,
                                            Mobile = s.User.Mobile,
                                            Power = s.Enterprise_Role.Powers,
                                            CustomPower = s.Powers,
                                            s.Status,
                                            EntId = s.EntId,
                                            GroupEntId = s.Enterprise.GroupEntId,
                                            GroupType = s.Enterprise.GroupType,
                                            s.NuoId,
                                            EntNuoId = s.Enterprise.NuoId,
                                        }).FirstOrDefault();

                                    if (hr != null)
                                        usr = new ShortUserInfo
                                        {
                                            UserId = hr.UserId,
                                            NickName = hr.NickName,
                                            Mobile = hr.Mobile,
                                            Powers = hr.Power.Union(hr.CustomPower ?? new List<string>()).ToList(),
                                            EntId = hr.EntId,
                                            Status = hr.Status,
                                            GroupEntId = hr.GroupEntId,
                                            GroupType = hr.GroupType
                                        };

                                    //更新用户活跃时间
                                    if (!string.IsNullOrWhiteSpace(usr?.UserId))
                                        _commonUserService.UserActivity(usr.UserId, client ?? ClientType.HrWeb, _user.RequestIpAddress);

                                    usr = usr ?? new ShortUserInfo();
                                    usr.NuoPinId = hr?.NuoId;
                                    usr.NuoEntId = hr?.EntNuoId ?? string.Empty;
                                    usr.AppType = TokenType.企业端;
                                    usr.ClientType = client;

                                    //企业缓存key存下来，清理token用
                                    if (!string.IsNullOrWhiteSpace(usr.NuoPinId))
                                        MyRedis.Client.Set($"{RedisKey.UserTokenKey}{usr.NuoPinId}", usrCacheKey, 3600 * 12);
                                }
                                else if (tokenType == TokenType.平台端)
                                {
                                    var admin = _context.Admin
                                        .Where(x => x.Id == id)
                                        .Select(s => new
                                        {
                                            s.Id,
                                            s.Name,
                                            s.Powers,
                                            s.Account,
                                            s.Status
                                        }).FirstOrDefault();

                                    if (admin != null)
                                        usr = new ShortUserInfo
                                        {
                                            UserId = admin.Id,
                                            NickName = admin.Name ?? string.Empty,
                                            Mobile = admin.Account,
                                            Powers = admin.Powers,
                                            Status = admin.Status == ActiveStatus.Active ? UserStatus.Active : UserStatus.Rejected
                                        };

                                    usr = usr ?? new ShortUserInfo();
                                    usr.AppType = TokenType.平台端;
                                    usr.ClientType = client;
                                    usr.NuoPinId = id;
                                }
                            }
                        }
                        else
                        {
                            //走诺聘认证
                            if (authType == ApiAuthType.平台端)
                            {
                                var nuoUser = await _nuoPinApi.GetPlatformUserByToken(token);
                                if (!string.IsNullOrWhiteSpace(nuoUser?.Id))
                                {
                                    if (nuoUser != null)
                                        usr = new ShortUserInfo
                                        {
                                            UserId = nuoUser.Id,
                                            Mobile = nuoUser.Mobile ?? string.Empty
                                        };

                                    usr = usr ?? new ShortUserInfo();
                                    usr.NuoPinId = nuoUser!.Id;
                                    usr.AppType = TokenType.平台端;
                                    usr.ClientType = ClientType.Other;
                                }
                            }
                            else
                            {
                                var nuoUser = await _nuoPinApi.GetUserByToken(token, TokenType.企业端);
                                if (!string.IsNullOrWhiteSpace(nuoUser?.Id))
                                {
                                    var hr = _context.User_Hr
                                        .Where(x => x.NuoId == nuoUser.Id)
                                        .Select(s => new
                                        {
                                            UserId = s.UserId,
                                            NickName = s.NickName,
                                            Mobile = s.User.Mobile,
                                            Power = s.Enterprise_Role.Powers,
                                            CustomPower = s.Powers,
                                            s.Status,
                                            EntId = s.EntId,
                                            GroupEntId = s.Enterprise.GroupEntId,
                                            GroupType = s.Enterprise.GroupType
                                        }).FirstOrDefault();

                                    if (hr != null)
                                        usr = new ShortUserInfo
                                        {
                                            UserId = hr.UserId,
                                            NickName = hr.NickName,
                                            Mobile = hr.Mobile,
                                            Powers = hr.Power.Union(hr.CustomPower ?? new List<string>()).ToList(),
                                            EntId = hr.EntId,
                                            Status = hr.Status,
                                            GroupEntId = hr.GroupEntId,
                                            GroupType = hr.GroupType
                                        };

                                    //更新用户活跃时间
                                    if (!string.IsNullOrWhiteSpace(usr?.UserId))
                                        _commonUserService.UserActivity(usr.UserId, ClientType.HrWeb, _user.RequestIpAddress);

                                    usr = usr ?? new ShortUserInfo();
                                    usr.NuoPinId = nuoUser.Id;
                                    usr.NuoEntId = nuoUser.LoginPrincipalNo ?? string.Empty;
                                    usr.AppType = TokenType.企业端;
                                    usr.ClientType = ClientType.Other;

                                    //企业缓存key存下来，清理token用
                                    if (!string.IsNullOrWhiteSpace(usr.NuoPinId))
                                        MyRedis.Client.Set($"{RedisKey.UserTokenKey}{usr.NuoPinId}", usrCacheKey, 3600 * 12);

                                    // //如果审核未通过，仅缓存3秒做个样子
                                    // if (string.IsNullOrEmpty(usr.UserId) || usr.Status != UserStatus.Active)
                                    //     disable = 3;
                                }
                            }
                        }

                        MyRedis.Client.Set(usrCacheKey, usr ?? new ShortUserInfo(), disable);
                    }
                }
            }

            _user.Id = usr?.UserId ?? string.Empty;
            _user.Account = usr?.Mobile;
            _user.Name = usr?.NickName;
            _user.NuoPinId = usr?.NuoPinId ?? string.Empty;
            _user.NuoEntId = usr?.NuoEntId ?? string.Empty;
            _user.EntId = usr?.EntId ?? string.Empty;
            _user.GroupEntId = usr?.GroupEntId ?? string.Empty;
            _user.GroupType = usr?.GroupType ?? EnterpriseGroupType.Ordinary;
            _user.Powers = usr?.Powers ?? new List<string>();
            _user.ClientType = usr?.ClientType;
        }

        //如果需要身份验证
        if (authType != null && authType != ApiAuthType.内部服务)
        {
            var isRightToken = (authType == ApiAuthType.平台端 && usr?.AppType == TokenType.平台端)
            || ((authType == ApiAuthType.企业端 || authType == ApiAuthType.诺聘验证) && usr?.AppType == TokenType.企业端)
            || (authType == ApiAuthType.用户端 && usr?.AppType == TokenType.用户端)
            || (authType == ApiAuthType.任意登录 && (usr?.AppType == TokenType.用户端 || usr?.AppType == TokenType.企业端 || usr?.AppType == TokenType.平台端));

            if (!isRightToken)
            {
                context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                return;
            }

            if (authType == ApiAuthType.用户端)
            {
                if (string.IsNullOrWhiteSpace(_user.Id))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                    return;
                }

                if (usr?.Status != UserStatus.Active)
                {
                    context.Response.StatusCode = (int)HttpStatusCode.Forbidden;
                    context.Response.ContentType = "application/json";
                    var jm = new ErrorType();
                    jm.code = ErrorCodes.NotAudit;
                    jm.msg = "禁止访问，请联系客服人员";
                    await context.Response.WriteAsync(ServiceStack.Text.JsonSerializer.SerializeToString(jm)).ConfigureAwait(false);
                    return;
                }
            }
            else if (authType == ApiAuthType.企业端)
            {
                if (string.IsNullOrWhiteSpace(_user.Id))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                    return;
                }

                if (string.IsNullOrWhiteSpace(usr?.EntId))
                {
                    //throw new ForbiddenException("尚未加入企业", ErrorCodes.NoEnt);
                    context.Response.StatusCode = (int)HttpStatusCode.Forbidden;
                    context.Response.ContentType = "application/json";
                    var jm = new ErrorType();
                    jm.code = ErrorCodes.NoEnt;
                    jm.msg = "尚未加入企业";
                    await context.Response.WriteAsync(ServiceStack.Text.JsonSerializer.SerializeToString(jm)).ConfigureAwait(false);
                    return;
                }

                if (usr?.Status != UserStatus.Active)
                {
                    //throw new ForbiddenException("用户尚未审核通过", ErrorCodes.NotAudit);
                    context.Response.StatusCode = (int)HttpStatusCode.Forbidden;
                    context.Response.ContentType = "application/json";
                    var jm = new ErrorType();
                    jm.code = ErrorCodes.NotAudit;
                    jm.msg = "用户尚未审核通过";
                    await context.Response.WriteAsync(ServiceStack.Text.JsonSerializer.SerializeToString(jm)).ConfigureAwait(false);
                    return;
                }

                //检测权限
                var powerObj = metadatas?.GetMetadata<Powers>();

                if (powerObj != null)
                {
                    var power = powerObj as Powers;
                    if (_user.Powers == null || !Power.CheckPower(_user.Powers, power!.Power))
                    {
                        context.Response.StatusCode = (int)HttpStatusCode.Forbidden;
                        return;
                    }
                }
            }
            else if (authType == ApiAuthType.诺聘验证)
            {
                if (string.IsNullOrWhiteSpace(_user.NuoPinId))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                    return;
                }
            }
            else if (authType == ApiAuthType.平台端)
            {
                if (string.IsNullOrWhiteSpace(_user.NuoPinId))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                    return;
                }
            }
            else
            {
                if (string.IsNullOrWhiteSpace(_user.Id) && string.IsNullOrWhiteSpace(_user.NuoPinId))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                    return;
                }
            }
        }

        // 继续处理请求
        await _next(context);
    }
}