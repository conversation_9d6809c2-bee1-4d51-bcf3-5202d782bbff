﻿using Config.CommonModel;

namespace Staffing.Core.Interfaces.CentreService;

public interface INScoreService
{
    // GetGoodsListResponse GetGoodsList(GetGoodsList model);
    // GetNScoresResponse GetNScores(GetNScores model);
    // GetOrdersResponse GetOrders(GetOrders model);
    // GetScoreRecordResponse GetScoreRecord(GetScoreRecord model);
    // GetScoreReportResponse GetScoreReport(GetScoreReport model);
    // SendOrdersResponse SendOrders(SendOrders model);
    // SetGoodsResponse SetGoods(SetGoods model);
    // SetNScoreResponse SetNScore(SetNScore model);
    // UpdateGoodsResponse UpdateGoods(UpdateGoods model);
    // GetPushPostSettingsResponse GetPushPostSettings();
    // EmptyResponse UpdatePushPostSettings(UpdatePushPostSettings model);
    // EmptyResponse DeletePushPostSettings(DeletePushPostSettings model);
}