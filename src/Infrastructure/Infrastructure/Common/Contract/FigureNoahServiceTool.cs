﻿using System.Data;
using System.Text;
using System.Xml;
using FigureNoah;
using Newtonsoft.Json.Linq;

namespace Infrastructure.Common.Contract;

public class FigureNoahServiceTool
{
    /// <summary>
    /// 调用服务的Header
    /// </summary>
    /// <returns></returns>
    private FigureNoah.CredentialSoapHeader GetHeader()
    {
        FigureNoah.CredentialSoapHeader credentialSoapHeader = new FigureNoah.CredentialSoapHeader();
        credentialSoapHeader.UserID = "X9dZHwmHSZ06jfJCbo5PDg==";
        credentialSoapHeader.PassWord = "X9dZHwmHSZ06jfJCbo5PDg==";
        return credentialSoapHeader;
    }

    public bool IsSyncSucess(string result)
    {
        #region 入职的接口返回结果
        //1、数据集不能为空!
        //2、项目不存在!
        //3、员工：" + ygName + " 身份证号：" + ygIdCard + " 入职类型为空或者输入错误，添加失败!
        //4、员工：" + ygName + " 身份证号：" + ygIdCard + " 已经在【" + xmName + "】项目下存在，不能重复入职 SUCCESS
        //5、员工：" + ygName + " 身份证号：" + ygIdCard + " 已经存在 且未办理离职不能入职                    SUCCESS
        //6、员工：" + ygName + " 身份证号：" + ygIdCard + " 入职类型为空或者输入错误，员工添加失败!
        #endregion
        #region 离职的接口返回结果
        //1、未找到对应的项目!
        //2、员工：" + ygName + " 身份证号：" + ygIdCard + " 不存在!
        //3、员工：" + ygName + " 身份证号：" + ygIdCard + " 入职状态错误!"
        #endregion
        if (result.Equals("SUCCESS", StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }
        if (result.Contains("不能重复入职") || result.Contains("未办理离职不能入职"))
        {
            return false;
        }
        return false;
    }

    #region 获取项目
    /// <summary>
    /// 从数字诺亚获取服务专员的项目
    /// </summary>
    /// <param name="employeeNumber">数字诺亚员工工号</param>
    /// <returns>dynamic: 诺亚项目编号 ProjCode, 客户名称 CustomerName</returns>
    public List<object> GetProjectListByNoahEmployeeNumber(string employeeNumber)
    {
        List<object> projectInfoList = new List<object>(); //存储该服务专员的项目
        if (string.IsNullOrWhiteSpace(employeeNumber))
        {
            return projectInfoList;
        }
        #region 接口返回数据格式
        //{
        //    "code": 1,        //1 成功; -1 验证出错; -2 未获取到相关数据
        //    "msg": "操作成功", //信息
        //    "result": {
        //        "count":1,    //数量
        //        "data": {
        //            "rows": [
        //                {
        //                    "XM_CODE": "U00466",                                                     //项目编码
        //                    "XM_NAME": "石家庄市财政会计服务中心劳务派遣-1502.03.00013财政会计服务中心", //项目名称
        //                    "XM_KH_CODE": "01096",                                                   //客户编码
        //                    "XM_KH_NAME": "石家庄市财政会计服务中心",                                  //客户名称
        //                    "XM_HT_CODE": "NY-PQ-2016-0202",                                         //合同编码
        //                    "XM_HT_NAME": "财政会计 劳务派遣 续签",                                   //合同名称
        //                    "XM_XMPRODUCTNAME": "劳务派遣现金流老业务-1902.07.003"                    //产品信息
        //                } 
        //            ]
        //        }
        //    }
        //}
        #endregion
        FigureNoah.HRServiceSoapClient hRServiceSoapClient = new FigureNoah.HRServiceSoapClient(new FigureNoah.HRServiceSoapClient.EndpointConfiguration());
        string jsonResponse=string.Empty;
        var jsonAsyncResponse = hRServiceSoapClient.FN_GetProjectByEmpCODEAsync(GetHeader(), employeeNumber);
        if(jsonAsyncResponse!=null)
        {
            jsonResponse = jsonAsyncResponse.Result.FN_GetProjectByEmpCODEResult;
        }
        var objectReturn = Newtonsoft.Json.Linq.JObject.Parse(jsonResponse);
        if (objectReturn["code"]?.ToString() != "1" || objectReturn["msg"]?.ToString() != "操作成功")
        {
            return projectInfoList; //接口调用异常
        }
        if (int.TryParse(objectReturn!["result"]!["count"]?.ToString(), out int projectCount) && projectCount > 0)
        {
            var jarrayReturn = objectReturn["result"]!["data"]!["rows"] as JArray;
            if (jarrayReturn != null)
            {
                foreach (var item in jarrayReturn)
                {
                    if (item["XM_CODE"] == null || string.IsNullOrWhiteSpace(item["XM_CODE"]?.ToString()) ||
                        item["XM_KH_NAME"] == null || string.IsNullOrWhiteSpace(item["XM_KH_NAME"]?.ToString()) ||
                        item["XM_HT_NAME"] == null || string.IsNullOrWhiteSpace(item["XM_HT_NAME"]?.ToString()))
                    {
                        continue;
                    }
                    projectInfoList.Add(item);
                }
            } 
        }
        return projectInfoList;
    }
    #endregion

    #region 获取项目组员工
    /// <summary>
    /// 从接口获取json数据
    /// </summary>
    /// <param name="projectCode"></param>
    /// <param name="pageIndex"></param>
    /// <param name="pageCount"></param>
    /// <returns></returns>
    public List<NoahEmployeeInfo> GetStaffFromFigureNoah(string projectCode, int pageIndex, int pageCount)
    {
        FigureNoah.HRServiceSoapClient hRServiceSoapClient = new FigureNoah.HRServiceSoapClient(new FigureNoah.HRServiceSoapClient.EndpointConfiguration());
        var jsonReturn = hRServiceSoapClient.FN_GetEmployeeByProjectCODEAsync(GetHeader(), projectCode, pageIndex, pageCount).Result.FN_GetEmployeeByProjectCODEResult;
       
        var objectReturn = Newtonsoft.Json.Linq.JObject.Parse(jsonReturn);
        if (objectReturn["code"]?.ToString() != "1" || objectReturn["msg"]?.ToString() != "操作成功")
        {
            return new List<NoahEmployeeInfo>();
        }
        int returnCount = Convert.ToInt32(objectReturn!["result"]!["count"]);
        var staffList = (JArray?)objectReturn!["result"]!["data"]!["rows"];
        if (staffList == null || staffList.Count < 1 || staffList.Count != returnCount)
        {
            return new List<NoahEmployeeInfo>();
        }
        List<NoahEmployeeInfo> resultList = new List<NoahEmployeeInfo>();
        foreach (var staff in staffList)
        {
            var model = new NoahEmployeeInfo
            {
                UserName = staff["LDHT_EMPLOYEENAME"]?.ToString().Trim(),
                TelePhone = staff["LDHT_LXDH"]?.ToString().Trim(),
                IDCard = staff["LDHT_IDNUMBER"]?.ToString().Trim(),
                Email = staff["YG_EMAIL"]?.ToString().Trim(),
                Sex = staff["YG_SEX"]?.ToString().Trim(),
                HomeAddress = staff["YG_HOMEADDRESS"]?.ToString().Trim(),
                ZipCode = staff["YG_POSTAL"]?.ToString().Trim(),
                Duty = staff["LDHT_GW"]?.ToString().Trim(),
                ContractCode = staff["LDHT_CODE"]?.ToString().Trim(),
                WorkSite = staff["LDHT_GZDD"]?.ToString().Replace("市", "").Replace("地区", "").Trim(),
                ContractSignTime = null,
                ContractExpireTime = null,
                TrialPeriod = null,
            };
            if (string.IsNullOrWhiteSpace(model.UserName))
            {
                model.UserName = staff["YG_NAME"]?.ToString().Trim();
            }
            if (string.IsNullOrWhiteSpace(model.TelePhone))
            {
                model.TelePhone = staff["YG_MOBILEPHONE"]?.ToString().Trim();
            }
            if (string.IsNullOrWhiteSpace(model.IDCard))
            {
                model.IDCard = staff["YG_IDNUMBER"]?.ToString().Trim();
            }
            if (string.IsNullOrWhiteSpace(model.HomeAddress))
            {
                model.HomeAddress = staff["YG_DOMICILEPLACE"]?.ToString().Trim();
            }
            if (string.IsNullOrWhiteSpace(model.HomeAddress))
            {
                model.HomeAddress = staff["YG_NATIVEPLACE"]?.ToString().Trim();
            }
            if (DateTime.TryParse(staff["LDHT_BEGINDATE"]?.ToString().Trim(), out var beginDate))
            {
                model.ContractSignTime = beginDate;
            }
            if (DateTime.TryParse(staff["LDHT_ENDDATE"]?.ToString().Trim(), out var endDate))
            {
                model.ContractExpireTime = endDate;
            }
            if (int.TryParse(staff["LDHT_PROBATION"]?.ToString().Trim(), out var trialPeriod))
            {
                model.TrialPeriod = trialPeriod;
            }
            if (DateTime.TryParse(staff["LDHT_SENDTIME"]?.ToString().Trim(), out var signTime))
            {
                model.SignTime = signTime;
            }
            if (!model.SignTime.HasValue && DateTime.TryParse(staff["LDHT_SUBMITDATE"]?.ToString().Trim(), out signTime))
            {
                model.SignTime = signTime;
            }
            resultList.Add(model);
        }
        return resultList;
    }
    public class NoahEmployeeInfo
    {
        public string? UserName { get; set; }
        public string? TelePhone { get; set; }
        public string? IDCard { get; set; }
        public string? Email { get; set; }
        public string? Sex { get; set; }
        public string? HomeAddress { get; set; }
        public string? ZipCode { get; set; }
        public string? Duty { get; set; }
        /// <summary>
        /// 合同编号
        /// </summary>
        public string? ContractCode { get; set; }
        /// <summary>
        /// 合同开始日期
        /// </summary>
        public DateTime? ContractSignTime { get; set; }
        /// <summary>
        /// 合同结束日期
        /// </summary>
        public DateTime? ContractExpireTime { get; set; }
        /// <summary>
        /// 合同试用期
        /// </summary>
        public int? TrialPeriod { get; set; }
        /// <summary>
        /// 合同签订时间
        /// </summary>
        public DateTime? SignTime { get; set; }
        /// <summary>
        /// 工作地点
        /// </summary>
        public string? WorkSite { get; set; }
    }
    #endregion

    #region 入职
    /// <summary>
    /// 入职信息同步到数字诺亚-2.调用接口
    /// </summary>
    /// <param name="model">
    ///     model.WorkGroup (View_WorkGroup)
    ///     model.Staff     (WorkGroupUser)
    ///     model.Entry     (WorkGroupEntry)
    ///     model.Archive   (WorkGroupArchive)
    ///     model.Contract  (WorkGroupEmployeeContract)
    /// </param>
    /// <returns></returns>
    public async Task<FN_Insert_Update_T_ENTRYAPPLICATION_RZSQResponse> PutEntryToFigureNoah(dynamic model)
    {
        DataSet dataSet = GetDataSet_T_ENTRYAPPLICATION_RZSQ();
        DataRow dr = dataSet.Tables[0].NewRow();
        string xm_code = model.WorkGroup.RelateNumber; //数字诺亚项目编号 U00466 //U03444 / U03702 / U05449
        #region 必填参数
        dr["LOGIN_NAME"] = model.WorkGroup.ManagerName.Replace(" ", ""); //数字诺亚登录账号=姓名+工号=工作组管理员的姓名(无空格) //薛立杰000259
        dr["YG_NAME"] = model.Staff.UserName;  //员工姓名
        dr["YG_IDNUMBER"] = model.Staff.IDCard;//身份证号码
        dr["LDHT_YGGXK_NAME"] = "诺亚库";               //员工关系库名称//"ENTRYAPPLICATION"
        dr["LDHT_TYPE"] = "劳动合同";                   //合同类型: 入职合同
        dr["EPLINK_EMPLOYEETYPENAME"] = "劳务派遣";     //入职类型名称: 离职退费/劳务用工/人事外包/代发工资/劳务派遣/外包入大库
        #endregion

        #region 可选参数
        //员工基本信息
        dr["YG_MOBILEPHONE"] = model.Staff.TelePhone;   //移动电话
                                                        //员工个人信息
        if (model.Entry.Sex != null)
        {
            try
            {
                dr["YG_SEX"] = System.Enum.GetName(typeof(Config.Enums.Sex), model.Entry.Sex); //性别
            }
            catch
            {
                dr["YG_SEX"] = "";
            }
        }
        dr["YG_HIGHEDUCTION"] = model.Entry.PK_EID; //已转换为最高学历的显示名称
        dr["YG_GRADUATESCHOOL"] = model.Entry.SchoolName;   //毕业院校
        dr["YG_MAJOR"] = model.Entry.SchoolSpecialty;       //主修专业
        dr["YG_GRADUATEDATE"] = model.Entry.GraduationDate; //毕业日期
        dr["YG_NATIONAL"] = model.Entry.Nation;  //民族
        if (model.Entry.Politic != null)
        {
            try
            {
                dr["YG_POLITYVISAGE"] = System.Enum.GetName(typeof(Config.Enums.PoliticsStatus), model.Entry.Politic); //政治面貌
            }
            catch
            {
                dr["YG_POLITYVISAGE"] = "";
            }
        }
        if (model.Entry.HouseholdType != null)
        {
            try
            {
                dr["YG_ACCOUNTTYPE"] = System.Enum.GetName(typeof(Config.Enums.HouseholdType), model.Entry.HouseholdType); //户口类型
            }
            catch
            {
                dr["YG_ACCOUNTTYPE"] = "";
            }
        }
        dr["YG_BIRTHDAY"] = model.Entry.Birthday;  //员工生日
        dr["YG_HOMEADDRESS"] = string.Join(" ", model.Entry.ProvinceName, model.Entry.CityName, model.Entry.DistrictName, model.Entry.Address);//地址
                                                                                                                                               //入职合同信息
        dr["KH_NAME"] = model.Contract.Organization;  //用人单位
        dr["YG_SUBUNIT"] = model.Contract.Department; //用人部门
        dr["LDHT_BEGINDATE"] = model.Contract.ContractSignTime; //合同开始日期
        dr["LDHT_ENDDATE"] = model.Contract.ContractExpireTime; //合同结束日期
        dr["LDHT_GW"] = model.Contract.Duty; //岗位
        dr["LDHT_GZDD"] = model.Contract.WorkSiteCityName; //工作地点
        dr["LDHT_PROBATION"] = model.Contract.TrialPeriod; //试用期
                                                           //档案保险信息
        if (model.Archive != null)
        {
            dr["YG_BANKNAME"] = model.Archive.SalaryAccountBankName ?? ""; //开户行
            dr["YG_BANKACCOUNT"] = model.Archive.SalaryAccount ?? "";      //银行卡号
            if (model.Archive.IsDisability != null)
            {
                try
                {
                    dr["YG_SFCJ"] = System.Enum.GetName(typeof(Config.Enums.ArchivesIsDisability), model.Archive.IsDisability); //是否残疾
                }
                catch
                {
                    dr["YG_SFCJ"] = "";
                }
            }
        }
        #endregion
        dataSet.Tables[0].Rows.Add(dr);

        FigureNoah.HRServiceSoapClient hRServiceSoapClient = new FigureNoah.HRServiceSoapClient(new HRServiceSoapClient.EndpointConfiguration());
        try
        {
            var arrayOfXElement = ToArrayOfXElement(dataSet);
            var result = await hRServiceSoapClient.FN_Insert_Update_T_ENTRYAPPLICATION_RZSQAsync(GetHeader(), xm_code, arrayOfXElement);
            //记录日志
            //Newtonsoft.Json.JsonConvert.SerializeObject(new
            //{
            //    Name = "入职合同信息同步至数字诺亚",
            //    ProjectCode = xm_code,
            //    Result = result,
            //    Content = dr.Table,
            //    ContractId = model.Contract.PK_WGECID
            //});
            return result;
        }
        catch
        {
            return new FN_Insert_Update_T_ENTRYAPPLICATION_RZSQResponse();
        }
    }
    /// <summary>
    /// 入职信息同步到数字诺亚-1.初始化数据集
    /// </summary>
    /// <returns></returns>
    private DataSet GetDataSet_T_ENTRYAPPLICATION_RZSQ()
    {
        DataSet ds = new DataSet();
        DataTable dt = new DataTable();
        #region 数据表的字段
        string columnStr = "LOGIN_NAME,XM_NAME,EPLINK_EMPLOYEETYPENAME,EPLINK_DEPTNAME,KH_NAME,YG_NAME,YG_CODE,YG_SEX,YG_BIRTHDAY,YG_HIGHEDUCTION,YG_IDNUMBER,YG_GRADUATESCHOOL,YG_GRADUATEDATE,"
            + "YG_PHONE,YG_MOBILEPHONE,YG_EMAIL,YG_HOMEADDRESS,YG_MAJOR,YG_DRIVLICENSETYPE,YG_JOBSENIORITYCARD,YG_WORKHISTORY,YG_HEIGHT,YG_WEIGHT,YG_NATIONAL,YG_NATIVEPLACE,YG_POLITYVISAGE,YG_ACCOUNTTYPE,"
            + "YG_DOMICILEPLACE,YG_TECHNICALTIT,YG_FORLANGUAGELEV,YG_COMPUTERLEVEL,YG_BANKNAME,YG_BANKACCOUNT,YG_BANKCARDHOUSEHOLD,YG_MARITALSTATE,YG_BOOLAGED,YG_BOOLMEDICAL,YG_BOOLHURT,YG_BOOLUNEMPLOYMENT,"
            + "YG_BOOLGIVEBIRTH,YG_BOOLFUND,YG_UNITAGEDCODE,YG_UNITMEDCODE,YG_UNITHURTCODE,YG_UNITUNEMPCODE,YG_UNITGIVBIRCODE,YG_UNITFUNDCODE,YG_INAGEDDATE,YG_INMEDICALDATE,YG_INHURDATE,YG_INUNEMPLOYDATE,"
            + "YG_INGIVEBIRTHDATE,YG_INFUNDDATE,YG_LABORFORMNAME,YG_PERSONSTATUS,YG_SUBUNIT,YG_WORKDATE,YG_AGEDBASIC,YG_MEDBASIC,YG_HURTBASIC,YG_UNEMPLOYBASIC,YG_FUNDBASIC,YG_UAGEDEXPESCA,YG_PAGEDEXPESCA,"
            + "YG_UNEMEXPESCA,YG_PUNEMEXPESCA,YG_UHURTEXPESCA,YG_UFUNDEXPESCA,YG_PFUNDEXPESCA,YG_UMEDEXPESCA,YG_PMEDEXPESCA,YG_UGIVBIREXPESCA,YG_DBAAGEID,YG_DBAAGENAME,YG_DBAMEDID,YG_DBAMEDNAME,YG_DBAHURTID,"
            + "YG_DBAHURTNAME,YG_DBAUNEMPID,YG_DBAUNEMPNAME,YG_DBAGIVBIRID,YG_DBAGIVBIRNAME,YG_DBAFUNDID,YG_DBAFUNDNAME,YG_AGEDTYPE,YG_MEDICALTYPE,YG_HURTTYPE,YG_UNEMPLOYTYPE,YG_GIVEBIRTHTYPE,YG_FUNDTYPE,"
            + "YG_WORKTYPE,YG_SFCJ,YG_SFLS,YG_SFGL,YG_CJZH,YG_LSZH,YG_RZSGRQ,"
        + "YG_LZRQ,YG_ZY,YG_SFTDHY,YG_SFGDTZZ,YG_HJSF,YG_HJCS,YG_HJQX,YG_HJXXDZ,YG_JZSF,YG_JZCS,YG_JZQX,YG_JZXXDZ,YG_SFJWRY,YG_JWZWM,YG_SFJNZS,YG_JWNSRSBH,YG_JWCSD,YG_JWSCRJSJ,YG_JWLHSJ,"
        + "YG_JWRZQX,YG_JWYJLJSJ,YG_JWYJLJDD,YG_JWJNZW,YG_JWJWZW,YG_JWZFD,YG_JWJWZFD,YG_SFGUY,YG_GJDQ,YG_RYSTATE,YG_GRGBTZ,"
        + "LDHT_YGGXK_NAME,LDHT_TYPE,LDHT_FL,LDHT_BEGINDATE,LDHT_ENDDATE,LDHT_GW,LDHT_GZDD,LDHT_SENDTIME,LDHT_LABORCERT,LDHT_LXDH,LDHT_ADD,LDHT_PROBATION,"
        + "SQYG_NUMBER,SQYG_LZ_TYPE,SQYG_LEAVEDATE,"
            + "SQYG_IFCERTIFY,SQYG_REASON,SQYG_IFPROCEDURES,SQYG_THEMPLOYEESTATE,Cont_Code,Fin_Code";
        #endregion
        string[] column = columnStr.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
        foreach (string col in column)
        {
            dt.Columns.Add(col);
        }
        ds.Tables.Add(dt);
        return ds;
    }
    #endregion

    #region 离职
    /// <summary>
    /// 离职信息同步到数字诺亚-1.初始化数据集
    /// </summary>
    /// <returns></returns>
    private DataSet GetDataSet_T_ENTRYAPPLICATION_LZSQ()
    {
        DataSet ds = new DataSet();
        DataTable dt = new DataTable();
        #region 数据表的字段
        string columnStr = "LOGIN_NAME,XM_NAME,EPLINK_EMPLOYEETYPENAME,EPLINK_DEPTNAME,KH_NAME,YG_NAME,YG_CODE,YG_SEX,YG_BIRTHDAY,YG_HIGHEDUCTION,YG_IDNUMBER,YG_GRADUATESCHOOL,YG_GRADUATEDATE,"
            + "YG_PHONE,YG_MOBILEPHONE,YG_EMAIL,YG_HOMEADDRESS,YG_MAJOR,YG_DRIVLICENSETYPE,YG_JOBSENIORITYCARD,YG_WORKHISTORY,YG_HEIGHT,YG_WEIGHT,YG_NATIONAL,YG_NATIVEPLACE,YG_POLITYVISAGE,YG_ACCOUNTTYPE,"
            + "YG_DOMICILEPLACE,YG_TECHNICALTIT,YG_FORLANGUAGELEV,YG_COMPUTERLEVEL,YG_BANKNAME,YG_BANKACCOUNT,YG_BANKCARDHOUSEHOLD,YG_MARITALSTATE,YG_BOOLAGED,YG_BOOLMEDICAL,YG_BOOLHURT,YG_BOOLUNEMPLOYMENT,"
            + "YG_BOOLGIVEBIRTH,YG_BOOLFUND,YG_UNITAGEDCODE,YG_UNITMEDCODE,YG_UNITHURTCODE,YG_UNITUNEMPCODE,YG_UNITGIVBIRCODE,YG_UNITFUNDCODE,YG_INAGEDDATE,YG_INMEDICALDATE,YG_INHURDATE,YG_INUNEMPLOYDATE,"
            + "YG_INGIVEBIRTHDATE,YG_INFUNDDATE,YG_LABORFORMNAME,YG_PERSONSTATUS,YG_SUBUNIT,YG_WORKDATE,YG_AGEDBASIC,YG_MEDBASIC,YG_HURTBASIC,YG_UNEMPLOYBASIC,YG_FUNDBASIC,YG_UAGEDEXPESCA,YG_PAGEDEXPESCA,"
            + "YG_UNEMEXPESCA,YG_PUNEMEXPESCA,YG_UHURTEXPESCA,YG_UFUNDEXPESCA,YG_PFUNDEXPESCA,YG_UMEDEXPESCA,YG_PMEDEXPESCA,YG_UGIVBIREXPESCA,YG_DBAAGEID,YG_DBAAGENAME,YG_DBAMEDID,YG_DBAMEDNAME,YG_DBAHURTID,"
            + "YG_DBAHURTNAME,YG_DBAUNEMPID,YG_DBAUNEMPNAME,YG_DBAGIVBIRID,YG_DBAGIVBIRNAME,YG_DBAFUNDID,YG_DBAFUNDNAME,YG_AGEDTYPE,YG_MEDICALTYPE,YG_HURTTYPE,YG_UNEMPLOYTYPE,YG_GIVEBIRTHTYPE,YG_FUNDTYPE,"
            + "YG_WORKTYPE,YG_SFCJ,YG_SFLS,YG_SFGL,YG_CJZH,YG_LSZH,YG_RZSGRQ,"
        + "YG_LZRQ,YG_ZY,YG_SFTDHY,YG_SFGDTZZ,YG_HJSF,YG_HJCS,YG_HJQX,YG_HJXXDZ,YG_JZSF,YG_JZCS,YG_JZQX,YG_JZXXDZ,YG_SFJWRY,YG_JWZWM,YG_SFJNZS,YG_JWNSRSBH,YG_JWCSD,YG_JWSCRJSJ,YG_JWLHSJ,"
        + "YG_JWRZQX,YG_JWYJLJSJ,YG_JWYJLJDD,YG_JWJNZW,YG_JWJWZW,YG_JWZFD,YG_JWJWZFD,YG_SFGUY,YG_GJDQ,YG_RYSTATE,YG_GRGBTZ,"
        + "LDHT_YGGXK_NAME,LDHT_TYPE,LDHT_FL,LDHT_BEGINDATE,LDHT_ENDDATE,LDHT_GW,LDHT_GZDD,LDHT_SENDTIME,LDHT_LABORCERT,LDHT_LXDH,LDHT_ADD,LDHT_PROBATION,"
        + "SQYG_NUMBER,SQYG_LZ_TYPE,SQYG_LEAVEDATE,"
            + "SQYG_IFCERTIFY,SQYG_REASON,SQYG_IFPROCEDURES,SQYG_THEMPLOYEESTATE,Cont_Code,Fin_Code";
        #endregion
        string[] column = columnStr.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
        foreach (string col in column)
        {
            dt.Columns.Add(col);
        }
        ds.Tables.Add(dt);
        return ds;
    }
    /// <summary>
    /// 离职信息同步到数字诺亚-2.调用接口
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<FigureNoah.FN_Insert_Update_T_ENTRYAPPLICATION_LZSQResponse> PutDimissionToFigureNoah(dynamic model)
    {
        //model.WorkGroup (View_WorkGroup)
        //model.Staff     (WorkGroupUser)
        //model.Entry     (WorkGroupEntry)
        //model.Archive   (WorkGroupArchive)
        //model.Dimission (WorkGroupDimission)
        DataSet dataSet = GetDataSet_T_ENTRYAPPLICATION_LZSQ();
        DataRow dr = dataSet.Tables[0].NewRow();
        string xm_code = model.WorkGroup.RelateNumber;  //数字诺亚项目编号 U00466 //U03444 / U03702 / U05449
        #region 必填参数
        dr["LOGIN_NAME"] = model.WorkGroup.ManagerName.Replace(" ", ""); //数字诺亚登录账号=姓名+工号=工作组管理员的姓名(无空格) //薛立杰000259
        dr["YG_NAME"] = model.Staff.UserName;  //员工姓名
        dr["YG_IDNUMBER"] = model.Staff.IDCard;//身份证号码
        dr["LDHT_YGGXK_NAME"] = "诺亚库";               //员工关系库名称//"ENTRYAPPLICATION"
        dr["LDHT_TYPE"] = "劳动合同";                   //合同类型: 劳动合同
        dr["EPLINK_EMPLOYEETYPENAME"] = "劳务派遣";     //类型名称: 离职退费/劳务用工/人事外包/代发工资/劳务派遣/外包入大库
        #endregion

        #region 可选参数
        //员工基本信息
        dr["YG_MOBILEPHONE"] = model.Staff.TelePhone;   //移动电话
                                                        //员工个人信息
        if (model.Entry.Sex != null)
        {
            try
            {
                dr["YG_SEX"] = System.Enum.GetName(typeof(Config.Enums.Sex), model.Entry.Sex); //性别
            }
            catch
            {
                dr["YG_SEX"] = "";
            }
        }
        dr["YG_HIGHEDUCTION"] = model.Entry.PK_EID; //已转换为最高学历的显示名称
        dr["YG_GRADUATESCHOOL"] = model.Entry.SchoolName;   //毕业院校
        dr["YG_MAJOR"] = model.Entry.SchoolSpecialty;       //主修专业
        dr["YG_GRADUATEDATE"] = model.Entry.GraduationDate; //毕业日期
        dr["YG_NATIONAL"] = model.Entry.Nation;  //民族
        if (model.Entry.Politic != null)
        {
            try
            {
                dr["YG_POLITYVISAGE"] = System.Enum.GetName(typeof(Config.Enums.PoliticsStatus), model.Entry.Politic); //政治面貌
            }
            catch
            {
                dr["YG_POLITYVISAGE"] = "";
            }
        }
        if (model.Entry.HouseholdType != null)
        {
            try
            {
                dr["YG_ACCOUNTTYPE"] = System.Enum.GetName(typeof(Config.Enums.HouseholdType), model.Entry.HouseholdType); //户口类型
            }
            catch
            {
                dr["YG_ACCOUNTTYPE"] = "";
            }
        }
        dr["YG_BIRTHDAY"] = model.Entry.Birthday;  //员工生日
        dr["YG_HOMEADDRESS"] = string.Join(" ", model.Entry.ProvinceName, model.Entry.CityName, model.Entry.DistrictName, model.Entry.Address);//地址
                                                                                                                                               //档案保险部分
        if (model.Archive != null)
        {
            dr["YG_BANKNAME"] = model.Archive.SalaryAccountBankName ?? ""; //开户行
            dr["YG_BANKACCOUNT"] = model.Archive.SalaryAccount ?? "";      //银行卡号
            if (model.Archive.IsDisability != null)
            {
                try
                {
                    dr["YG_SFCJ"] = System.Enum.GetName(typeof(Config.Enums.ArchivesIsDisability), model.Archive.IsDisability); //是否残疾
                }
                catch
                {
                    dr["YG_SFCJ"] = "";
                }
            }
        }
        //离职部分
        dr["KH_NAME"] = model.WorkGroup.WGName;  //用人单位
        dr["YG_SUBUNIT"] = model.WorkGroup.Department ?? ""; //用人部门
        dr["YG_WORKDATE"] = model.Dimission.WorkTime; //参加工作日期
        dr["YG_WORKTYPE"] = model.Dimission.Production ?? ""; //工种
        dr["LDHT_BEGINDATE"] = model.Dimission.ContractSignTime; //合同开始日期
        dr["LDHT_ENDDATE"] = model.Dimission.ContractExpireTime; //合同结束日期
        //dr["SQYG_LZ_TYPE"] = model.Dimission.; //离职类型
        //dr["SQYG_LEAVEDATE"] = model.Dimission.; //离职日期
        //dr["SQYG_IFCERTIFY"] = model.Dimission.; //是否有用工单位退回证明：是；否
        dr["SQYG_REASON"] = model.Dimission.DimissionReason ?? ""; //离职原因
                                                                   //dr["SQYG_IFPROCEDURES"] = model.Dimission.; //无手续离职：是；否
        #endregion
        dataSet.Tables[0].Rows.Add(dr);

        FigureNoah.HRServiceSoapClient hRServiceSoapClient = new FigureNoah.HRServiceSoapClient(new HRServiceSoapClient.EndpointConfiguration());
        try
        {
            var arrayOfXElement = ToArrayOfXElement(dataSet);
            var result = await hRServiceSoapClient.FN_Insert_Update_T_ENTRYAPPLICATION_LZSQAsync(GetHeader(), xm_code, arrayOfXElement);

            //记录日志
            //Newtonsoft.Json.JsonConvert.SerializeObject(new
            //{
            //    Name = "离职合同信息同步至数字诺亚",
            //    ProjectCode = xm_code,
            //    Result = result,
            //    Content = dr.Table,
            //    ContractId = model.Contract.PK_WGDID
            //});
            return result;
        }
        catch (Exception ex)
        {
            //return ex.Message;
            return new FN_Insert_Update_T_ENTRYAPPLICATION_LZSQResponse(ex.Message);
        }
    }

    #endregion

    #region 续签
    /// <summary>
    /// 续签合同同步到数字诺亚-1.初始化数据集
    /// </summary>
    /// <returns></returns>
    private DataSet GetDataSet_FN_Update_T_ENTRYAPPLICATION()
    {
        DataSet ds = new DataSet();
        DataTable dt = new DataTable();
        #region 数据表的字段
        string columnStr = "LDHT_EMPLOYEENAME,LDHT_IDNUMBER,LDHT_LXDH,LDHT_ADD,LDHT_SENDUNITNAME,LDHT_EMPLOYUNITNAME,LDHT_BEGINDATE,LDHT_ENDDATE,LDHT_GW,LDHT_TYPE,LDHT_GZDD,LDHT_SENDTIME,LDHT_REMARK";
        #endregion
        string[] column = columnStr.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
        foreach (string col in column)
        {
            dt.Columns.Add(col);
        }
        ds.Tables.Add(dt);
        return ds;
    }
    /// <summary>
    /// 入职信息同步到数字诺亚-2.调用接口
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<FN_Update_T_ENTRYAPPLICATIONResponse> PutRenewContractToFigureNoah(dynamic model)
    {
        //model.WorkGroup (View_WorkGroup)
        //model.Staff     (WorkGroupUser)
        //model.Entry     (WorkGroupEntry)
        //model.Contract  (WorkGroupEmployeeContract)
        DataSet dataSet = GetDataSet_FN_Update_T_ENTRYAPPLICATION();
        DataRow dr = dataSet.Tables[0].NewRow();
        string xm_code = model.WorkGroup.RelateNumber; //数字诺亚项目编号 U00466 //U03444 / U03702 / U05449
        #region 必填参数
        dr["LDHT_EMPLOYEENAME"] = model.Staff.UserName; //员工姓名
        dr["LDHT_IDNUMBER"] = model.Staff.IDCard; //身份证号
        dr["LDHT_LXDH"] = model.Staff.TelePhone;  //联系电话
        dr["LDHT_ADD"] = string.Join(" ", model.Entry.ProvinceName, model.Entry.CityName, model.Entry.DistrictName, model.Entry.Address);
        dr["LDHT_SENDUNITNAME"] = model.WorkGroup.SubCompanyName; //用人单位：诺亚及分公司名称 (项目组关联的子公司名-默认是总公司名)
        dr["LDHT_EMPLOYUNITNAME"] = model.Contract.Organization;//用工单位：企业
        dr["LDHT_BEGINDATE"] = model.Contract.ContractSignTime.ToString("yyyy-MM-dd"); //合同开始日期
        dr["LDHT_ENDDATE"] = model.Contract.ContractExpireTime != null ? model.Contract.ContractExpireTime.ToString("yyyy-MM-dd") : ""; //合同结束日期
        dr["LDHT_GW"] = model.Contract.Duty; //岗位
        dr["LDHT_TYPE"] = "劳动合同"; //类型：外包合同 劳务合同 劳务协议 实习协议
        dr["LDHT_GZDD"] = string.Join(" ", model.Contract.WorkSiteProvinceName, model.Contract.WorkSiteCityName);
        dr["LDHT_REMARK"] = "";
        #endregion
        dataSet.Tables[0].Rows.Add(dr);

        FigureNoah.HRServiceSoapClient hRServiceSoapClient = new FigureNoah.HRServiceSoapClient(new HRServiceSoapClient.EndpointConfiguration());
        try
        {
            var arrayOfXElement = ToArrayOfXElement(dataSet);
            var result = await hRServiceSoapClient.FN_Update_T_ENTRYAPPLICATIONAsync(GetHeader(), xm_code, arrayOfXElement);
            //Tools.LogTool.Debug(Newtonsoft.Json.JsonConvert.SerializeObject(new
            //{
            //    Name = "续签合同信息同步至数字诺亚",
            //    ProjectCode = xm_code,
            //    Result = result,
            //    Content = dr.Table,
            //    ContractId = model.Contract.PK_WGECID
            //}));
            //return result;
            //记录日志
            //Newtonsoft.Json.JsonConvert.SerializeObject(new
            //{
            //    Name = "续签合同信息同步至数字诺亚",
            //    ProjectCode = xm_code,
            //    Result = result,
            //    Content = dr.Table,
            //    ContractId = model.Contract.PK_WGECID
            //});
            return result;
        }
        catch (Exception ex)
        {
            //return ex.Message.ToString();
            return new FN_Update_T_ENTRYAPPLICATIONResponse(ex.Message.ToString());
        }
    }
    #endregion

    #region 获取需续签的员工  旧版云办公未用到
    /// <summary>
    /// 获取需续签的员工 (根据身份证号读取最后一份合同)
    /// </summary>
    /// <param name="IDNumber"></param>
    /// <returns></returns>
    public async Task<NoahEmployeeContractInfo> GetStaffLastContractFromFigureNoah(string IDNumber)
    {
        var result = new NoahEmployeeContractInfo();
        // "XM_CODE": "",//项目编码
        // "XM_NAME": "",//项目名称
        // "XM_KH_CODE": "",//客户编码
        // "XM_KH_NAME": "",//客户名称
        // "LDHT_CODE": "2015-07-8940",//员工合同编码
        // "LDHT_BEGINDATE": "2015-07-29 0:00:00",开始日期
        // "LDHT_ENDDATE": "2017-07-28 0:00:00",//结束日期
        // "LDHT_SENDTIME": "",//派遣时间
        // "EPLINK_EMPLOYEETYPENAME": "",//入职类型（劳务派遣、劳务用工、外包入大库、人事外包、代发工资）
        // "LDHT_TYPE": "",//合同类型 (劳务协议、劳动合同、实习协议)
        // "LDHT_STATE1": "已审核",//合同状态（保存、审核中、已审核）
        // "LDHT_EMPLOYUNITNAME": "",//用工单位
        // "LDHT_PROBATION": "",//试用期
        // "LDHT_FL": "",//合同分类  新签、续签、变更、补充
        // "LDHT_GW": "",//岗位
        // "LDHT_GZDD": "河北省",//工作地点
        // "LDHT_XM_NAME": ""//参与项目
        FigureNoah.HRServiceSoapClient hRServiceSoapClient = new FigureNoah.HRServiceSoapClient(new HRServiceSoapClient.EndpointConfiguration());
        var jsonReturn = await hRServiceSoapClient.FN_GetContactByIDNumberAsync(GetHeader(), IDNumber);
        #region 接口返回数据格式
        //{
        //	"code": 1,
        //	"msg": "操作成功",
        //	"result": {
        //		"count": 1, //多条代表此员在数字诺亚存在于多个项目中（兼职员工）
        //		"data": {
        //			"rows": [{}]
        //		}
        //	}
        //}
        #endregion
        var objectReturn = Newtonsoft.Json.Linq.JObject.Parse(jsonReturn.FN_GetContactByIDNumberResult);
        if (objectReturn?["code"]?.ToString() != "1" || objectReturn?["msg"]?.ToString() != "操作成功")
        {
            return result; //接口调用异常 -> 停止导入
        }
        int returnCount = Convert.ToInt32(objectReturn!["result"]!["count"]);
        var contractList = (JArray)objectReturn["result"]!["data"]!["rows"]!;
        if (contractList == null || contractList.Count < 1 || contractList.Count != returnCount)
        {
            return result;
        }
        List<NoahEmployeeContractInfo> resultList = new List<NoahEmployeeContractInfo>();
        foreach (var contract in contractList)
        {
            DateTime? dtBegin = null;
            if (DateTime.TryParse(contract["LDHT_BEGINDATE"]!.ToString(), out DateTime beginTime))
            {
                dtBegin = beginTime;
            }
            DateTime? dtEnd = null;
            if (DateTime.TryParse(contract["LDHT_ENDDATE"]!.ToString(), out DateTime endTime))
            {
                dtEnd = endTime;
            }
            var model = new NoahEmployeeContractInfo
            {
                UserName = contract["YG_NAME"]!.ToString(),
                //TelePhone = staff[""].ToString(),
                IDCard = IDNumber,
                ContractSignTime = dtBegin,
                ContractExpireTime = dtEnd,
                Duty = contract["LDHT_GW"]!.ToString()
            };
            resultList.Add(model);
        }
        return resultList.OrderByDescending(p => p.ContractExpireTime).First();
    }
    public class NoahEmployeeContractInfo
    {
        public string UserName { get; set; } = String.Empty;
        public string TelePhone { get; set; } = String.Empty;
        public string IDCard { get; set; } = String.Empty;
        public string Duty { get; set; } = String.Empty;
        public DateTime? ContractSignTime { get; set; }
        public DateTime? ContractExpireTime { get; set; }
    }
    #endregion


    #region DataSet ArrayOfXElement 转换
    //将DataSet结构转换为xml字符串
    private string ConvertDataSetSchemaToXML(DataSet xmlDS, Encoding encoding)
    {
        MemoryStream stream = new MemoryStream();
        XmlTextWriter? writer = null;
        string result = "<result>-3</result>";
        try
        {
            stream = new MemoryStream();
            //从stream装载到XmlTextReader
            writer = new XmlTextWriter(stream, encoding);
            //用WriteXml方法写入文件.
            xmlDS.WriteXmlSchema(writer);
            int count = (int)stream.Length;
            byte[] arr = new byte[count];
            stream.Seek(0, SeekOrigin.Begin);
            stream.Read(arr, 0, count);
            result = encoding.GetString(arr).Trim();
        }
        catch { }
        finally
        {
            if (writer != null) writer.Close();
        }
        return result;
    }
    //将DataSet数据转换为xml字符串
    private string ConvertDataSetToXML(DataSet xmlDS, Encoding encoding)
    {
        MemoryStream? stream = null;
        XmlTextWriter? writer = null;
        string result = "<result>-3</result>";
        try
        {
            stream = new MemoryStream();
            //从stream装载到XmlTextReader
            writer = new XmlTextWriter(stream, encoding);
            //用WriteXml方法写入文件.
            xmlDS.WriteXml(writer);
            int count = (int)stream.Length;
            byte[] arr = new byte[count];
            stream.Seek(0, SeekOrigin.Begin);
            stream.Read(arr, 0, count);
            result = encoding.GetString(arr).Trim();
        }
        catch { }
        finally
        {
            if (writer != null) writer.Close();
        }
        return result;
    }
    /// <summary>
    /// ArrayOfXElement转换为DataSet     
    /// </summary>     
    /// <param name="ds">DataSet</param>    
    /// <returns>ArrayOfXElement</returns>  
    private ArrayOfXElement ToArrayOfXElement(DataSet ds)
    {
        string strSchema = ConvertDataSetSchemaToXML(ds, Encoding.UTF8);
        string strData = ConvertDataSetToXML(ds, Encoding.UTF8);
        string strTitle = "<diffgr:diffgram xmlns:msdata=\"urn:schemas-microsoft-com:xml-msdata\" xmlns:diffgr=\"urn:schemas-microsoft-com:xml-diffgram-v1\">";
        string strDataSet = strTitle + strData.Substring(1) + "</diffgr:diffgram>";
        string strXML = strSchema.Substring(39) + strDataSet;
        ArrayOfXElement result = new ArrayOfXElement();
        using (StringReader strRdr = new StringReader("<root>" + strXML + "</root>"))
        {
            //通过XmlReader.Create静态方法创建XmlReader实例
            using (XmlReader rdr = XmlReader.Create(strRdr))
            {
                rdr.MoveToContent();
                rdr.Read();
                result.ReadXml(rdr);
            }
        }

        return result;
    }
    #endregion

}

