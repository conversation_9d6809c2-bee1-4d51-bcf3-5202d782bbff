
-- ----------------------------
-- sys_banner表
-- ----------------------------
DROP TABLE IF EXISTS `sys_banner`;
CREATE TABLE `sys_banner` (
  `Id` varchar(18) NOT NULL,
  `Sort` int(11) NOT NULL COMMENT '排序',
  `Type` int(3) NOT NULL COMMENT '类型',
  `Status` int(3) NOT NULL COMMENT '状态',
  `TargetType` int(3) NOT NULL COMMENT '操作类型',
  `Title` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '标题',
  `ImageUrl` varchar(1024) DEFAULT NULL COMMENT '图片',
  `Content` text CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '内容',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMAR<PERSON>Y (`Id`),
  <PERSON><PERSON>Y `IX_sys_banner_Type` (`Type`),
  KEY `IX_sys_banner_Status` (`Status`),
  KEY `IX_sys_banner_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 幂等性检测表
-- ----------------------------
DROP TABLE IF EXISTS `sys_idempotency_check`;
CREATE TABLE `sys_idempotency_check` (
  `Key` varchar(50) NOT NULL COMMENT '唯一键',
  `Type` int(2) NOT NULL COMMENT '类型，强制区分key',
  `CheckTimes` int(11) NOT NULL COMMENT '次数',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Key`, `Type`),
  KEY `IX_sys_idempotency_check_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 系统消息
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice_system`;
CREATE TABLE `sys_notice_system` (
  `Id` varchar(18) NOT NULL,
  `Title` varchar(50) CHARACTER SET utf8mb4 NOT NULL COMMENT '标题',
  `Describe` varchar(255) CHARACTER SET utf8mb4 NOT NULL COMMENT '描述',
  `Content` text CHARACTER SET utf8mb4 NOT NULL COMMENT '内容',
  `Type` int(3) NOT NULL COMMENT '类型',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_notice_systeCreatedTime` (`CreatedTime` DESC),
  KEY `IX_notice_systeType` (`Type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 短信表
-- ----------------------------
DROP TABLE IF EXISTS `sys_sms`;
CREATE TABLE `sys_sms` (
  `Id` varchar(18) NOT NULL,
  `Type` int(4) NOT NULL COMMENT '短信类型',
  `Active` bit(1) NOT NULL COMMENT '有效',
  `Mobile` varchar(20) NOT NULL COMMENT '手机',
  `UserId` varchar(18) NOT NULL DEFAULT '' COMMENT '用户',
  `Content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '内容',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_sys_sms_Type` (`Type`),
  KEY `IX_sys_sms_Mobile` (`Mobile`),
  KEY `IX_sys_sms_UserId` (`UserId` DESC),
  KEY `IX_sys_sms_Active` (`Active` DESC),
  KEY `IX_sys_sms_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- 设置表
-- ----------------------------
DROP TABLE IF EXISTS `sys_settings`;
CREATE TABLE `sys_settings` (
  `Id` varchar(18) NOT NULL,
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 云市场应用
-- ----------------------------
DROP TABLE IF EXISTS `sys_app`;
CREATE TABLE `sys_app` (
  `Id` varchar(18) NOT NULL,
  `Name` varchar(50) NOT NULL COMMENT '名称',
  `Alias` varchar(50) NOT NULL COMMENT '别名',
  `Icon` varchar(1024) NULL COMMENT '图标',
  `Type` int(2) NOT NULL COMMENT '类型',
  `Url` varchar(1024) NULL COMMENT 'Url',
  `Powers` varchar(50) NULL COMMENT '所需权限',
  `Abstract` text NULL COMMENT '概要',
  `ShortAbstract` text NULL COMMENT '短概要',
  `AppInfo` text NULL COMMENT '应用详情',
  `Manual` text NULL COMMENT '操作指南',
  `Status` int(2) NOT NULL COMMENT '状态',
  `Show` bit NOT NULL DEFAULT 1 COMMENT '显示',
  `ParentId` varchar(18) NOT NULL COMMENT '父Id',
  `Level` varchar(1024) NOT NULL COMMENT '层级',
  `Sort` int(11) NOT NULL COMMENT '排序',
  `AdminId` varchar(64) NOT NULL,
  `AdminName` varchar(50) CHARACTER SET utf8mb4 NOT NULL,
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_sys_app_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 每日用户统计
-- ----------------------------
DROP TABLE IF EXISTS `rpt_daily_user`;
CREATE TABLE `rpt_daily_user` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `NewUser` int(11) NOT NULL,
  `NewSeeker` int(11) NOT NULL,
  `NewHr` int(11) NOT NULL,
  `ActiveUser` int(11) NOT NULL,
  `ActiveSeeker` int(11) NOT NULL,
  `ActiveHr` int(11) NOT NULL,
  `ActiveInterviewer` int(11) NOT NULL,
  `DeliverResume` int(11) NOT NULL DEFAULT 0 COMMENT '投递简历',
  `EventDate` date NOT NULL,
  `CreatedTime` datetime(6) NOT NULL,
  PRIMARY KEY (`Id`),
  KEY `IX_rpt_daily_user_EventDate` (`EventDate` DESC),
  KEY `IX_rpt_daily_user_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


INSERT INTO `sys_settings` VALUES (1, NOW());

BEGIN;
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('140992238579613829', '发布项目', '', 'https://resources.nuopin.cn/4ee717cd40464fb08a7448af9f19352f.png', 0, '/cloudMarket/createProject', '', '', '', '', '', 1, b'0', '140992617142112801', '', 2, '202007011348010880000000000000000001', '系统管理员', '2022-04-08 13:25:40.393343', '2022-04-08 13:25:40.393343');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('140992462270234757', '组织管理', '', 'https://resources.nuopin.cn/edc55806256947d680d47da29dcf8041.png', 0, '/cloudMarket/orgManage', '', '', '', '', '', 1, b'1', '140992617142112804', '', 1, '202007011348010880000000000000000001', '系统管理员', '2022-04-08 13:27:27.057898', '2022-04-08 13:27:27.057899');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('140992526143193221', '模块详情', '', 'https://resources.nuopin.cn/04b256d2cd38494c9f44c2b0dca83f6d.png', 0, '/cloudMarket/modularInfo', '', '', '', '', '', 1, b'0', '140992617142112801', '', 3, '202007011348010880000000000000000001', '系统管理员', '2022-04-08 13:27:57.514315', '2022-04-08 13:27:57.514317');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('140992571655585925', '人事管理', '', 'https://resources.nuopin.cn/211c3a8c7a734539abb0e8eed5b8da20.png', 0, '', '', '', '', '', '', 1, b'1', '', '', 3, '202007011348010880000000000000000001', '系统管理员', '2022-04-08 13:28:19.216048', '2022-04-08 13:28:19.216049');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('140992617142112801', '项目执行', '', 'https://resources.nuopin.cn/19f2977f61fb44fab78eee587cd9b63e.png', 0, '/cloudMarket', '', '', '', '', '', 1, b'1', '', '', 2, '202007011348010880000000000000000001', '系统管理员', '2022-03-11 10:15:21.994955', '2022-03-11 10:15:21.994956');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('140992617142112804', '组织管理', '', 'https://resources.nuopin.cn/823daecddbb44418a006feca70d1a0db.png', 0, '/cloudMarket/moudalurInfo', '', NULL, '角色管理短描述', NULL, NULL, 1, b'1', '', '', 4, '202007011348010880000000000000000001', '系统管理员', '2022-03-11 10:31:26.559174', '2022-03-11 10:31:26.559175');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('140992617142812805', '人事管理', '', 'https://resources.nuopin.cn/2b9dce1d7a63487bbdf8c03d091eba95.png', 0, '/cloudMarket/humanManage', '', '', '', '', '', 1, b'1', '140992571655585925', '', 1, '202007011348010880000000000000000001', '系统管理员', '2022-04-08 13:28:40.906033', '2022-04-08 13:28:40.906035');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('141732570871103621', '项目执行', '', 'https://resources.nuopin.cn/e527d1253fd8428eab8838f5b65353f3.png', 0, '/cloudMarket/myProject', '', '', '', '', '', 1, b'1', '140992617142112801', '', 1, '202007011348010880000000000000000001', '系统管理员', '2022-04-12 15:29:18.339789', '2022-04-12 15:29:18.339791');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('141739813056282757', '项目执行详情', '', 'https://resources.nuopin.cn/c14889bd1c0b41fea52b190f5f49c4d1.png', 0, '/cloudMarket/myProjectDetail', '', '', '', '', '', 1, b'0', '140992617142112801', '', 4, '202007011348010880000000000000000001', '系统管理员', '2022-04-12 16:26:51.682370', '2022-04-12 16:26:51.682372');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('141922906769719429', '简历库-简历详情', '', 'https://resources.nuopin.cn/c15903b249f74e94a112b860dfc61332.png', 0, '/cloudMarket/resumeDetailFalse', '', '', '', '', '', 1, b'0', '140992571655585925', '', 5, '202007011348010880000000000000000001', '系统管理员', '2022-04-13 16:41:57.568875', '2022-04-13 16:41:57.568876');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('142808974339604613', '发布职位', '', 'https://resources.nuopin.cn/a0e4c36917b7491d8d34e514bbf2790c.png', 0, '/cloudMarket/createPosition', '', '', '', '', '', 1, b'0', '140992617142112801', '', 5, '202007011348010880000000000000000001', '系统管理员', '2022-04-18 14:03:47.510009', '2022-04-18 14:03:47.510011');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('143363548079194245', '导入简历', '', 'https://resources.nuopin.cn/0426253802484621a9d3ee29c84bd128.png', 0, '/cloudMarket/importResume', '', '', '', '', '', 1, b'0', '140992571655585925', '', 2, '202007011348010880000000000000000001', '系统管理员', '2022-04-21 15:31:08.876374', '2022-04-21 15:31:08.876375');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('143533539988603013', '简历导入记录', '', 'https://resources.nuopin.cn/04717bbb030648a68d6cb66b68932d2b.png', 0, '/cloudMarket/importResumeRecord', '', '', '', '', '', 1, b'0', '140992571655585925', '', 3, '202007011348010880000000000000000001', '系统管理员', '2022-04-22 14:02:07.335219', '2022-04-22 14:02:07.335220');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('144057255543701637', '真实用户-简历详情', '', 'https://resources.nuopin.cn/789c93af9c7745d6955f0948560d45b5.png', 0, '/cloudMarket/resumeDetailTrue', '', '', '', '', '', 1, b'0', '140992571655585925', '', 4, '202007011348010880000000000000000001', '系统管理员', '2022-04-25 11:24:14.372417', '2022-04-25 11:24:14.372418');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('145909812270465157', '招聘进程-简历详情', '', 'https://resources.nuopin.cn/a94c2bc948b34f74af697347f8cb339f.png', 0, '/cloudMarket/recruitResumeDetail', '', '', '', '', '', 1, b'0', '140992571655585925', '', 6, '202007011348010880000000000000000001', '系统管理员', '2022-05-05 16:47:02.257786', '2022-05-05 16:47:02.257788');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('149122081571785605', '项目大厅', '', 'https://resources.nuopin.cn/349c8b22cb4541aab07e1c1d1535de3b.png', 0, '/cloudMarket/projectHall', '', '', '', '', '', 1, b'1', '', '', 1, '202007011348010880000000000000000001', '系统管理员', '2022-05-23 10:15:51.622025', '2022-05-23 10:15:51.622026');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('149122194576821125', '项目大厅详情', '', 'https://resources.nuopin.cn/349bb0b1e3714d4fa28a3384a7789398.png', 0, '/cloudMarket/projectHallDetail', '', '', '', '', '', 1, b'0', '149122081571785605', '', 0, '202007011348010880000000000000000001', '系统管理员', '2022-05-23 10:16:45.507797', '2022-05-23 10:16:45.507798');
INSERT INTO `sys_app` (`Id`, `Name`, `Alias`, `Icon`, `Type`, `Url`, `Powers`, `Abstract`, `ShortAbstract`, `AppInfo`, `Manual`, `Status`, `Show`, `ParentId`, `Level`, `Sort`, `AdminId`, `AdminName`, `UpdatedTime`, `CreatedTime`) VALUES ('149125627950189445', '角色管理', '', 'https://resources.nuopin.cn/6f4594ae387c4440971574a497aa2c92.png', 0, '/cloudMarket/roleManage', '', '', '', '', '', 1, b'1', '140992617142112804', '', 2, '202007011348010880000000000000000001', '系统管理员', '2022-05-23 10:44:02.667697', '2022-05-23 10:44:02.667698');
COMMIT;