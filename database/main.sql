-- ----------------------------
-- 用户主表
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `UserId` varchar(18) NOT NULL,
  `Mobile` varchar(20) NOT NULL COMMENT '手机号',
  `WeChatId` varchar(50) DEFAULT NULL COMMENT '微信UnionId',
  `WeChatName` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '微信昵称',
  `WeChatH5OpenId` varchar(50) DEFAULT NULL COMMENT '公众号OpenId',
  `WeChatH5Subscribe` bit NOT NULL DEFAULT 0 COMMENT '是否订阅公众号',
  `IdentityCardType` int(2) NOT NULL DEFAULT 0 COMMENT '证件类型',
  `IdentityCard` varchar(20) NOT NULL DEFAULT '' COMMENT '身份证号',
  `IdentityCardName` varchar(50) NOT NULL DEFAULT '' COMMENT '身份证名字',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`UserId`),
  UNIQUE KEY `IX_user_Mobile` (`Mobile`),
  UNIQUE KEY `IX_user_WeChatId` (`WeChatId` DESC),
  UNIQUE KEY `IX_user_WeChatH5OpenId` (`WeChatH5OpenId` DESC),
  KEY `IX_user_IdentityCard` (`IdentityCard` DESC),
  KEY `IX_user_WeChatH5Subscribe` (`WeChatH5Subscribe` DESC),
  KEY `IX_user_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 求职者主表
-- ----------------------------
DROP TABLE IF EXISTS `user_seeker`;
CREATE TABLE `user_seeker` (
  `UserId` varchar(18) NOT NULL,
  `NuoId` varchar(64) NOT NULL COMMENT '诺聘PK_UAID',
  `WeChatAppletId` varchar(50) DEFAULT NULL COMMENT '微信小程序OpenId',
  `WeChatInterviewerAppletId` varchar(50) DEFAULT NULL COMMENT '面试官小程序OpenId',
  `Avatar` varchar(1024) DEFAULT NULL COMMENT '头像',
  `NickName` varchar(50) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '昵称',
  `H5Notice` bit NOT NULL DEFAULT 0 COMMENT '公众号开关通知',
  `InterviewerEnt` varchar(100) NULL COMMENT '面试官所在企业',
  `RegionId` varchar(15) NOT NULL DEFAULT '' COMMENT '地区Id',
  `Address` varchar(1024) NULL COMMENT '地址',
  `Location` point NOT NULL DEFAULT (point(0,0)),
  `Source` int(3) NOT NULL DEFAULT 0 COMMENT '来源',
  `Status` int(2) NOT NULL DEFAULT 1 COMMENT '状态',
  `TencentImId` varchar(32) DEFAULT NULL COMMENT 'IM账号Id',
  `HrAppletQrCode` varchar(1024) NULL COMMENT '简历hr小程序二维码',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`UserId`),
  KEY `IX_user_seeker_NuoId` (`NuoId`),
  KEY `IX_user_seeker_Status` (`Status`),
  KEY `IX_user_seeker_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- hr主表
-- ----------------------------
DROP TABLE IF EXISTS `user_hr`;
CREATE TABLE `user_hr` (
  `UserId` varchar(18) NOT NULL,
  `NuoId` varchar(64) NOT NULL COMMENT '诺聘PK_EAID',
  `InviteCode` varchar(9) NOT NULL COMMENT '邀请码',
  `EntId` varchar(18) NOT NULL COMMENT '企业Id',
  `RoleId` varchar(18) NOT NULL DEFAULT 0 COMMENT '角色Id',
  `WeChatAppletId` varchar(50) DEFAULT NULL COMMENT '微信小程序OpenId',
  `Powers` varchar(1024) NOT NULL DEFAULT '' COMMENT '权限列表',
  `Post` varchar(50) DEFAULT NULL COMMENT '职位',
  `EMail` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `Avatar` varchar(1024) DEFAULT NULL COMMENT '头像',
  `NickName` varchar(50) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '昵称',
  `WeChatNo` varchar(50) DEFAULT NULL COMMENT '微信号',
  `EntWeChatQrCode` varchar(1024) DEFAULT NULL COMMENT '企业微信二维码',
  `AppletQrCode` varchar(1024) DEFAULT NULL COMMENT '小程序二维码',
  `Sex` int(2) NULL COMMENT '性别',
  `Describe` varchar(5000) NULL COMMENT '描述',
  `RegionId` varchar(15) NULL COMMENT '地区Id',
  `Address` varchar(1024) NULL COMMENT '地址',
  `Location` point NOT NULL DEFAULT (point(0,0)),
  `Source` int(3) NOT NULL DEFAULT 0 COMMENT '来源',
  `Status` int(2) NOT NULL DEFAULT 1 COMMENT '状态',
  `Score` int(3) NOT NULL COMMENT '资料完整度',
  `ApplicationTime` datetime(6) NOT NULL DEFAULT NOW(6) COMMENT '申请开通零工时间',
  `TencentImId` varchar(32) DEFAULT NULL COMMENT 'IM账号Id',
  `H5Notice` bit NOT NULL DEFAULT 1 COMMENT '公众号开关通知',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`UserId`),
  UNIQUE KEY `IX_user_hr_NuoId` (`NuoId`),
  UNIQUE KEY `IX_user_hr_InviteCode` (`InviteCode`),
  KEY `IX_user_hr_RoleId` (`RoleId`),
  KEY `IX_user_hr_Status` (`Status`),
  KEY `IX_user_hr_EntId` (`EntId` DESC),
  KEY `IX_user_hr_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 用户简历
-- ----------------------------
DROP TABLE IF EXISTS `user_resume`;
CREATE TABLE `user_resume` (
  `UserId` varchar(18) NOT NULL,
  `Occupation` int(2) NULL COMMENT '职业',
  `Sex` int(2) NULL COMMENT '性别',
  `Education` int(2) NULL COMMENT '学历',
  `School` varchar(50) NULL COMMENT '学校',
  `GraduationDate` date NULL COMMENT '毕业日期',
  `Major` varchar(50) NULL COMMENT '专业名称',
  `Score` int(3) NOT NULL COMMENT '简历积分',
  `Describe` varchar(500) CHARACTER SET utf8mb4 COMMENT '描述',
  `Nature` varchar(1024) NULL COMMENT '性格',
  `Skill` varchar(1024) NULL COMMENT '技能',
  `Appearance` varchar(1024) NULL COMMENT '外貌',
  `Birthday` date NULL COMMENT '生日',
  `EMail` varchar(100) NULL COMMENT '邮箱',
  `WeChatNo` varchar(50) NULL COMMENT '微信号',
  `Qq` varchar(50) NULL COMMENT 'qq号',
  `Show` bit NOT NULL DEFAULT 0 COMMENT '简历开放',
  `Anonymous` bit NOT NULL DEFAULT 0 COMMENT '是否匿名',
  `Certificate` varchar(1024) NULL COMMENT '证书',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`UserId`),
  KEY `IX_user_resume_UpdatedTime` (`UpdatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 附件简历
-- ----------------------------
DROP TABLE IF EXISTS `user_resume_attach`;
CREATE TABLE `user_resume_attach` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL,
  `Title` varchar(50) NOT NULL COMMENT '附件标题',
  `Url` varchar(1024) NULL COMMENT '附件地址',
  `Size` int(11) NOT NULL COMMENT '大小',
  `Language` int(2) NOT NULL COMMENT '语言',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_user_resume_attach_UserId` (`UserId`),
  KEY `IX_user_resume_attach_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 校园经历
-- ----------------------------
DROP TABLE IF EXISTS `user_campus`;
CREATE TABLE `user_campus` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL,
  `Name` varchar(50) NOT NULL COMMENT '名称',
  `Award` varchar(50) NOT NULL COMMENT '职位/奖项',
  `BeginDate` date NOT NULL COMMENT '开始时间',
  `EndDate` date NOT NULL COMMENT '结束时间',
  `Describe` varchar(500) CHARACTER SET utf8mb4 COMMENT '描述',
  PRIMARY KEY (`Id`),
  KEY `IX_user_campus_UserId` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 工作经历
-- ----------------------------
DROP TABLE IF EXISTS `user_work`;
CREATE TABLE `user_work` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL,
  `Company` varchar(100) NOT NULL COMMENT '公司',
  `Post` varchar(50) NOT NULL COMMENT '职位',
  `BeginDate` date NOT NULL COMMENT '开始时间',
  `EndDate` date NOT NULL COMMENT '结束时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `Describe` varchar(500) CHARACTER SET utf8mb4 COMMENT '描述',
  PRIMARY KEY (`Id`),
  KEY `IX_user_work_UserId` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 用户的顾问列表
-- ----------------------------
DROP TABLE IF EXISTS `user_seeker_adviser`;
CREATE TABLE `user_seeker_adviser` (
  `Id` varchar(18) NOT NULL,
  `SeekerId` varchar(18) NOT NULL COMMENT '求职者Id',
  `AdviserId` varchar(18) NOT NULL DEFAULT '1' COMMENT '顾问Id',
  `VisitTime` datetime(6) NOT NULL DEFAULT NOW(6) COMMENT '访问时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_user_seeker_adviser_SeekerId_AdviserId` (`SeekerId`, `AdviserId`),
  KEY `IX_user_seeker_adviser_SeekerId_Full` (`SeekerId`, `AdviserId`, `VisitTime` DESC, `CreatedTime` DESC),
  KEY `IX_user_seeker_adviser_AdviserId_Full` (`AdviserId`, `SeekerId`, `VisitTime` DESC, `CreatedTime` DESC),
  KEY `IX_user_seeker_adviser_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 就业指导
-- ----------------------------
DROP TABLE IF EXISTS `user_seeker_guidance`;
CREATE TABLE `user_seeker_guidance` (
  `Id` varchar(18) NOT NULL,
  `SeekerId` varchar(18) NOT NULL COMMENT '求职者Id',
  `AdviserId` varchar(18) NOT NULL DEFAULT '1' COMMENT '顾问Id',
  `FreeTimeType` int(2) NULL COMMENT '可沟通时间类型',
  `FreeTime` datetime(6) NULL COMMENT '可沟通时间',
  `CanPhone` bit NULL COMMENT '可电话沟通',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_user_seeker_guidance_SeekerId_AdviserId` (`SeekerId`, `AdviserId`),
  KEY `IX_user_seeker_guidance_SeekerId` (`SeekerId`),
  KEY `IX_user_seeker_guidance_AdviserId` (`AdviserId`),
  KEY `IX_user_seeker_guidance_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- hr审批记录表
-- ----------------------------
DROP TABLE IF EXISTS `user_hr_audit`;
CREATE TABLE `user_hr_audit` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL,
  `EntId` varchar(18) NOT NULL,
  `Status` int(2) NOT NULL COMMENT '状态',
  `Describe` varchar(1025) CHARACTER SET utf8mb4 NULL COMMENT '描述',
  `AdminId` varchar(64) NOT NULL,
  `AdminName` varchar(50) CHARACTER SET utf8mb4 NOT NULL,
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_hr_UserId` (`UserId`),
  KEY `IX_user_hr_EntId` (`EntId`),
  KEY `IX_user_hr_audit_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 企业
-- ----------------------------
DROP TABLE IF EXISTS `enterprise`;
CREATE TABLE `enterprise` (
  `EntId` varchar(18) NOT NULL,
  `NuoId` varchar(64) NOT NULL COMMENT '诺聘PK_EID',
  `Name` varchar(50) NOT NULL COMMENT '企业名称',
  `LogoUrl` varchar(1024) NULL COMMENT '企业Logo',
  `Abbreviation` varchar(50) NOT NULL COMMENT '简称',
  `Type` int(3) NOT NULL DEFAULT 0 COMMENT '类型',
  `Status` int(4) NOT NULL DEFAULT 0 COMMENT '状态',
  `GroupType` int(3) NOT NULL DEFAULT 0 COMMENT '集团类型',
  `GroupEntId` varchar(18) NOT NULL DEFAULT 0 COMMENT '集团企业Id',
  `RegionId` varchar(15) NOT NULL COMMENT '地区Id',
  `Address` varchar(1024) NULL COMMENT '地址',
  `Location` point NOT NULL DEFAULT (point(0,0)),
  `EsignOrgId` varchar(50) NOT NULL DEFAULT '' COMMENT 'E签宝机构Id',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`EntId`),
  UNIQUE KEY `IX_enterprise_NuoId` (`NuoId`),
  KEY `IX_enterprise_Name` (`Name`),
  KEY `IX_enterprise_GroupEntId` (`GroupEntId`),
  KEY `IX_enterprise_Status` (`Status`),
  KEY `IX_enterprise_EsignOrgId` (`EsignOrgId` DESC),
  KEY `IX_enterprise_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 企业工商数据
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_business`;
CREATE TABLE `enterprise_business` (
  `Id` varchar(18) NOT NULL,
  `Name` varchar(50) NOT NULL COMMENT '企业名称',
  `BaseData` text NULL COMMENT '基本数据',
  `FullData` text NULL COMMENT '全量数据',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_enterprise_business_Name` (`Name`),
  KEY `IX_enterprise_business_UpdatedTime` (`UpdatedTime` DESC),
  KEY `IX_enterprise_business_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 企业集团关系
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_relation`;
CREATE TABLE `enterprise_relation` (
  `Id` varchar(18) NOT NULL,
  `EntId` varchar(18) NOT NULL COMMENT '公司Id',
  `GroupEntId` varchar(18) NOT NULL COMMENT '所属集团公司Id',
  `Creator` varchar(18) NOT NULL,
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_enterprise_relation_EntId` (`EntId`),
  KEY `IX_enterprise_relation_GroupEntId` (`GroupEntId`),
  KEY `IX_enterprise_relation_Creator` (`Creator` DESC),
  KEY `IX_enterprise_relation_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 组织架构
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_org`;
CREATE TABLE `enterprise_org` (
  `OrgId` varchar(18) NOT NULL,
  `EntId` varchar(18) NOT NULL COMMENT '企业Id',
  `Name`  varchar(50) NOT NULL COMMENT '名称',
  `ParentId` varchar(18) NOT NULL COMMENT '父Id',
  `Level` varchar(1024) NOT NULL COMMENT '层级',
  `Sort` int(11) NOT NULL COMMENT '排序',
  `Creator` varchar(18) NOT NULL,
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`OrgId`),
  KEY `IX_enterprise_org_EntId` (`EntId`),
  KEY `IX_enterprise_org_Level` (`Level`),
  KEY `IX_enterprise_org_Creator` (`Creator` DESC),
  KEY `IX_enterprise_org_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 组织架构人员
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_org_user`;
CREATE TABLE `enterprise_org_user` (
  `Id` varchar(18) NOT NULL,
  `OrgId` varchar(18) NOT NULL COMMENT '组织架构Id',
  `UserId` varchar(18) NOT NULL COMMENT '用户Id',
  `Creator` varchar(18) NOT NULL,
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_enterprise_org_user_OrgId_UserId` (`OrgId`, `UserId`),
  KEY `IX_enterprise_org_user_OrgId` (`OrgId`),
  KEY `IX_enterprise_org_user_UserId` (`UserId`),
  KEY `IX_enterprise_org_user_Creator` (`Creator` DESC),
  KEY `IX_enterprise_org_user_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 企业角色
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_role`;
CREATE TABLE `enterprise_role` (
  `RoleId` varchar(18) NOT NULL,
  `EntId` varchar(18) NOT NULL COMMENT '企业Id',
  `Type` int(3) NOT NULL COMMENT '0=系统，1=自定义',
  `Name`  varchar(50) NOT NULL COMMENT '名称',
  `Describe` varchar(1024) CHARACTER SET utf8mb4 NULL COMMENT '描述',
  `Scope` varchar(50) NULL COMMENT '管辖范围',
  `Icon`  varchar(1024) NULL COMMENT '图标',
  `Powers` varchar(1024) NOT NULL COMMENT '权限列表',
  `Creator` varchar(18) NOT NULL,
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`RoleId`),
  KEY `IX_enterprise_role_EntId` (`EntId`),
  KEY `IX_enterprise_role_Type` (`Type`),
  KEY `IX_enterprise_role_Creator` (`Creator` DESC),
  KEY `IX_enterprise_role_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 企业白名单
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_whitelist`;
CREATE TABLE `enterprise_whitelist` (
  `Id` varchar(18) NOT NULL,
  `NuoId` varchar(64) NOT NULL COMMENT '诺聘PK_EID',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_enterprise_whitelist_NuoId` (`NuoId`),
  KEY `IX_user_enterprise_whitelist_audit_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 用户扩展表
-- ----------------------------
DROP TABLE IF EXISTS `user_extend`;
CREATE TABLE `user_extend` (
  `UserId` varchar(18) NOT NULL,
  `AdviserId` varchar(18) NOT NULL DEFAULT '1' COMMENT '当前顾问Id',
  `LoginTime` datetime(6) NOT NULL DEFAULT NOW(6) COMMENT '登录时间',
  `RegistrationIp` varchar(50) DEFAULT NULL COMMENT '说明',
  PRIMARY KEY (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 用户各种数量
-- ----------------------------
DROP TABLE IF EXISTS `user_num`;
CREATE TABLE `user_num` (
  `UserId` varchar(18) NOT NULL,
  `Talent` int(11) NOT NULL COMMENT '人才数量',
  `VirtualTalent` int(11) NOT NULL COMMENT '虚拟人才库数量',
  `Adviser` int(11) NOT NULL COMMENT '顾问数量',
  `Likes` int(11) NOT NULL COMMENT '点赞数量',
  PRIMARY KEY (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 用户登录记录
-- ----------------------------
DROP TABLE IF EXISTS `user_login_record`;
CREATE TABLE `user_login_record` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL,
  `Type` int(2) NOT NULL COMMENT '类型',
  `Client` varchar(20) NOT NULL COMMENT 'ClientId',
  `IpAddress` varchar(50) NOT NULL COMMENT 'Ip',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_login_record_UserId` (`UserId`),
  KEY `IX_user_login_record_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 用户余额表
-- ----------------------------
DROP TABLE IF EXISTS `user_balance`;
CREATE TABLE `user_balance` (
  `UserId` varchar(18) NOT NULL,
  `HrBalance` decimal(18,6) NOT NULL DEFAULT 0 COMMENT 'hr余额',
  `HrBalanceFull` decimal(18,6) NOT NULL DEFAULT 0 COMMENT 'Hr历史余额',
  `HrWithdrawTime` datetime(6) DEFAULT NULL COMMENT 'hr提现时间',
  `SeekerBalance` decimal(18,6) NOT NULL DEFAULT 0 COMMENT '求职者余额',
  `SeekerBalanceFull` decimal(18,6) NOT NULL DEFAULT 0 COMMENT '求职者历史余额',
  `SeekerWithdrawTime` datetime(6) DEFAULT NULL COMMENT '求职者提现时间',
  PRIMARY KEY (`UserId`),
  KEY `IX_user_balance_HrBalance` (`HrBalance` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 用户余额记录表
-- ----------------------------
DROP TABLE IF EXISTS `user_balance_record`;
CREATE TABLE `user_balance_record` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL,
  `Amount` decimal(18,6) NOT NULL COMMENT '当前值',
  `Increment` decimal(18,6) NOT NULL COMMENT '增量',
  `UserType` int(2) NOT NULL COMMENT 'hr还是求职者',
  `Type` int(4) NOT NULL COMMENT '类型',
  `Content` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '记录内容',
  `Data` varchar(1024) DEFAULT NULL COMMENT '数据json',
  `EventTime` datetime(6) NOT NULL COMMENT '发生时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_balance_record_UserId` (`UserId`),
  KEY `IX_user_balance_record_Type` (`Type` DESC),
  KEY `IX_user_balance_record_UserType` (`UserType` DESC),
  KEY `IX_user_balance_record_EventTime` (`EventTime` DESC),
  KEY `IX_user_balance_record_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 职位收藏
-- ----------------------------
DROP TABLE IF EXISTS `user_collect_post`;
CREATE TABLE `user_collect_post` (
  `Id` varchar(18) NOT NULL,
  `HrId` varchar(18) NOT NULL COMMENT '顾问Id',
  `SeekerId` varchar(18) NOT NULL COMMENT '求职者Id',
  `TeamPostId` varchar(18) NOT NULL COMMENT '协同职位Id',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_collect_post_TeamPostId` (`TeamPostId` DESC),
  KEY `IX_user_collect_post_SeekerId_HrId_TeamPostId_CreatedTime` (`SeekerId`, `HrId`, `TeamPostId`, `CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 关注行业
-- ----------------------------
DROP TABLE IF EXISTS `user_follow_industry`;
CREATE TABLE `user_follow_industry` (
  `Id` varchar(18) NOT NULL,
  `HrId` varchar(18) NOT NULL COMMENT '顾问Id',
  `SeekerId` varchar(18) NOT NULL COMMENT '求职者Id',
  `Industry` int(3) NOT NULL COMMENT '行业Id',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_follow_industry_SeekerId_HrId_CreatedTime_Industry` (`SeekerId`, `HrId`, `Industry`, `CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 用户业务短信
-- ----------------------------
DROP TABLE IF EXISTS `user_sms`;
CREATE TABLE `user_sms` (
  `UserId` varchar(18) NOT NULL,
  `BusinessSms` int(11) NOT NULL COMMENT '业务短信数量',
  PRIMARY KEY (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 渠道顾问关系
-- ----------------------------
DROP TABLE IF EXISTS `user_channel_relation`;
CREATE TABLE `user_channel_relation` (
  `Id` varchar(18) NOT NULL,
  `ChannelId` varchar(18) NOT NULL COMMENT '渠道Id',
  `HrId` varchar(18) NOT NULL COMMENT '顾问Id',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_user_channel_relation_ChannelId_HrId` (`ChannelId`, `HrId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 用户短信记录表
-- ----------------------------
DROP TABLE IF EXISTS `user_sms_record`;
CREATE TABLE `user_sms_record` (
  `Id` varchar(18) NOT NULL,
  `Type` tinyint NOT NULL DEFAULT 0 COMMENT '类型',
  `UserId` varchar(18) NOT NULL,
  `Amount` int(11) NOT NULL COMMENT '数量',
  `Balance` int(11) NOT NULL COMMENT '余额',
  `Content` varchar(500) NULL COMMENT '内容',
  `TaskId` varchar(18) NOT NULL COMMENT '通用任务Id',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_sms_record_UserId` (`UserId`),
  KEY `IX_user_sms_record_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 微信Id
-- ----------------------------
DROP TABLE IF EXISTS `wechat`;
CREATE TABLE `wechat` (
  `WeChatId` varchar(50) NOT NULL COMMENT '微信UnionId',
  `WeChatH5OpenId` varchar(50) DEFAULT NULL COMMENT '公众号OpenId',
  `WeChatH5Subscribe` bit NOT NULL DEFAULT 0 COMMENT '是否订阅公众号',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`WeChatId`),
  UNIQUE KEY `IX_wechat_WeChatH5OpenId` (`WeChatH5OpenId` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 用户钉钉信息扩展表
-- ----------------------------
DROP TABLE IF EXISTS `user_dingding`;
CREATE TABLE `user_dingding` (
  `UserId` varchar(18) NOT NULL,
  `DingAvatar` varchar(500) NOT NULL DEFAULT '' COMMENT '用户钉钉头像',
  `DingName` varchar(100) NOT NULL DEFAULT '' COMMENT '用户钉钉姓名',
  `DingJobNo` varchar(10) NOT NULL DEFAULT '' COMMENT '用户钉钉工号',
  `DingMobile` varchar(20) NOT NULL DEFAULT '' COMMENT '用户钉钉手机号',
  `DingTitle` varchar(100) NOT NULL DEFAULT '' COMMENT '用户钉钉职位',
  `DingBranch` varchar(100) NOT NULL DEFAULT '' COMMENT '用户钉钉部门',
  `DingUserid` varchar(100) NOT NULL DEFAULT '' COMMENT '用户钉钉userid',
  `DingUnionid` varchar(100) NOT NULL DEFAULT '' COMMENT '用户钉钉unionid',
  `IsPush` bit NOT NULL DEFAULT 1 COMMENT '是否开启推送',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- 钉钉信息表
-- ----------------------------
DROP TABLE IF EXISTS `dingding`;
CREATE TABLE `dingding` (
  `DingUserid` varchar(100) NOT NULL COMMENT '用户钉钉userid',
  `DingUnionid` varchar(100) NOT NULL DEFAULT '' COMMENT '用户钉钉unionid',
  `DingAvatar` varchar(500) NOT NULL DEFAULT '' COMMENT '用户钉钉头像',
  `DingName` varchar(100) NOT NULL DEFAULT '' COMMENT '用户钉钉姓名',
  `DingJobNo` varchar(10) NOT NULL DEFAULT '' COMMENT '用户钉钉工号',
  `DingMobile` varchar(20) NOT NULL DEFAULT '' COMMENT '用户钉钉手机号',
  `DingTitle` varchar(100) NOT NULL DEFAULT '' COMMENT '用户钉钉职位',
  `DingBranch` varchar(100) NOT NULL DEFAULT '' COMMENT '用户钉钉部门',
  `PushTime` datetime(6) COMMENT '消息推送时间',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`DingUserid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



INSERT INTO `user` (`UserId`, `Mobile`, `IdentityCard`, `IdentityCardName`, `CreatedTime`) VALUES ('1', '18888888888', '', '', '2022-04-08 13:02:00.958773');
INSERT INTO `user_hr` (`UserId`, `NuoId`, `EntId`, `RoleId`, `WeChatAppletId`, `Powers`, `Post`, `EMail`, `Avatar`, `NickName`, `WeChatNo`, `Source`, `Status`, `ApplicationTime`, `CreatedTime`, `EntWeChatQrCode`, `Sex`, `RegionId`, `Describe`, `Address`, `InviteCode`, `Location`, `Score`) VALUES ('1', '202012071701154250000000000058598316', '100989262137917573', '1', NULL, '', '人事专员', '', '', '诺快聘', '110', 1, 1, '2022-04-08 13:02:01.155225', '2022-04-08 13:02:01.155227', '', 1, '', '', '', '', ST_GeomFromText('POINT(1.1 2.2)'), 0);
INSERT INTO `enterprise` (`EntId`, `NuoId`, `Name`, `LogoUrl`, `Abbreviation`, `Type`, `Status`, `GroupType`, `GroupEntId`, `RegionId`, `Address`, `UpdatedTime`, `CreatedTime`, `Location`) VALUES ('100989262137917573', '201710110744320530000000000000000002', '河北诺亚人力资源开发有限公司', '', '诺亚人力', 1, 1, 0, '', '', '', '2022-04-08 13:02:01.115714', '2022-04-08 13:02:01.115716', ST_GeomFromText('POINT(0 0)'));
INSERT INTO `user_num` (`UserId`, `Talent`, `VirtualTalent`, `Adviser`, `Likes`) VALUES ('1', 0, 0, 0, 0);
INSERT INTO `user_extend` (`UserId`, `LoginTime`, `RegistrationIp`, `AdviserId`) VALUES ('1', '2022-05-06 13:43:54.448058', '::1', '140992762181845125');
INSERT INTO `user_balance` (`UserId`, `HrBalance`, `HrBalanceFull`, `HrWithdrawTime`) VALUES ('1', 0.000000, 0.000000, NULL);

-- INSERT INTO `user_seeker`(`UserId`,`NuoId`) VALUES (1, '202108110915056420000000000000000010');
-- INSERT INTO `user_resume`(`UserId`,`Name`) VALUES (1, '王大力');

INSERT INTO `enterprise_role`(`RoleId`,`EntId`,`Type`,`Name`,`Describe`,`Scope`,`Icon`,`Powers`,`Creator`,`CreatedTime`) VALUE(1, 0, 0, '负责人', '拥有所有权限', '全公司', 'https://resources.nuopin.cn/staf/res/entrole/admin.png', 'Admin,Manager', 0, NOW());
INSERT INTO `enterprise_role`(`RoleId`,`EntId`,`Type`,`Name`,`Describe`,`Scope`,`Icon`,`Powers`,`Creator`,`CreatedTime`) VALUE(2, 0, 0, '主管', '可管理本部门及子部门项目', '本部门', 'https://resources.nuopin.cn/staf/res/entrole/manager2.png', 'Manager', 0, NOW());
INSERT INTO `enterprise_role`(`RoleId`,`EntId`,`Type`,`Name`,`Describe`,`Scope`,`Icon`,`Powers`,`Creator`,`CreatedTime`) VALUE(99, 0, 0, '项目经理', '可管理平台项目', '本人', 'https://resources.nuopin.cn/staf/res/entrole/employee.png', 'Employee', 0, NOW());