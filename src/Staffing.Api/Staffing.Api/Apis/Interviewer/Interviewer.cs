using Infrastructure.Extend;
using Staffing.Model.Interviewer;
using Staffing.Core.Interfaces.Interviewer;

namespace Staffing.Api.Apis.Interviewer;

[Service(ServiceLifetime.Transient)]
public class InterviewerRouter : IInterviewerRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("interviewer").WithTags("面试官");
        group.MapGet("/info", (IInterviewerService rep) => rep.GetIneterviewerInfo())
            .WithOpenApi(operation => new(operation) { Summary = "获取面试官基本信息" });

        group.MapPut("", (EditInterviewerInfoRequest model, IInterviewerService rep) => rep.EditInterviewerInfo(model))
            .WithOpenApi(operation => new(operation) { Summary = "修改面试官基本信息" });

        group.MapGet("/todolist", ([AsParameters] TodoListRequest model, IInterviewerService rep) => rep.GetTodoList(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取面试官待办列表" });

        group.MapGet("/screenlist", ([AsParameters] ScreenListRequest model, IInterviewerService rep) => rep.GetScreenList(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取面试官筛选列表" });

        group.MapGet("/interviewlist", ([AsParameters] InterviewListRequest model, IInterviewerService rep) => rep.GetInterviewList(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取面试官面试列表" });

        group.MapPut("/interviewoutcome", (EditInterviewOutcomeRequest model, IInterviewerService rep) => rep.EditInterviewOutcome(model))
            .WithOpenApi(operation => new(operation) { Summary = "面试官修改面试结果" });

        group.MapPut("/screenstatus", (EditScreenStatusRequest model, IInterviewerService rep) => rep.EditScreenStatus(model))
            .WithOpenApi(operation => new(operation) { Summary = "面试官修改筛简历状态" });

        group.MapGet("/user/{todoId}", (string todoId, IInterviewerService rep) => rep.GetUserInformation(todoId))
            .WithOpenApi(operation => new(operation) { Summary = "获取用户基本信息" });

        group.MapGet("/recruit/{todoId}", (string todoId, IInterviewerService rep) => rep.GetRecruitmentProcessList(todoId))
            .WithOpenApi(operation => new(operation) { Summary = "获取招聘流程列表" });

        group.MapPost("/todoplan", (GetTodoPlanRequest model, IInterviewerService rep) => rep.GetTodoPlan(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取面试待办计划（日历模式）" });

        group.MapPost("/todoplanlist", (GetTodoPlanListRequest model, IInterviewerService rep) => rep.GetTodoPlanList(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取面试待办计划列表（日历模式）" });

        group.MapGet("/project", ([AsParameters] ProjectCooperationList model, IInterviewerService rep) => rep.GetProjectCooperation(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取合作项目" });
    }
}