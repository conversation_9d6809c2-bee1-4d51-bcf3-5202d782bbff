using Config.CommonModel;

namespace Staffing.Model.CentreService.Report;

public class GetDailyUser : QueryRequest
{

}

public class GetDailyUserResponse : QueryResponse
{
    public List<DailyUserInfo> Rows { get; set; } = default!;
}

public class DailyUserInfo
{
    public int Id { get; set; }

    /// <summary>
    /// 新用户
    /// </summary>
    public int NewUser { get; set; }

    /// <summary>
    /// c端注册
    /// </summary>
    public int NewSeeker { get; set; }

    /// <summary>
    /// b端注册
    /// </summary>
    public int NewHr { get; set; }

    /// <summary>
    /// 用户日活
    /// </summary>
    public int ActiveUser { get; set; }

    /// <summary>
    /// c端日活
    /// </summary>
    public int ActiveSeeker { get; set; }

    /// <summary>
    /// b端日活
    /// </summary>
    public int ActiveHr { get; set; }

    /// <summary>
    /// 面试官日活
    /// </summary>
    public int ActiveInterviewer { get; set; }

    /// <summary>
    /// 投递简历
    /// </summary>
    public int DeliverResume { get; set; }

    /// <summary>
    /// 日期
    /// </summary>
    public DateOnly? EventDate { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}