﻿using Config.CommonModel;
using Config.Enums;
using MiniExcelLibs.Attributes;

namespace Staffing.Model.Hr.Recruit;

public class RecruitResumeExtend
{
    public List<RecruitResumeExtendValue> Rows { get; set; } = new List<RecruitResumeExtendValue>();
}

public class RecruitResumeExtendValue
{
    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 文本
    /// </summary>
    public string? Text { get; set; }
}

#region 上传简历解析简历相关模型
/// <summary>
/// 解析完成返回模型
/// </summary>
public class UpLoadResumeInfoResponse
{
    /// <summary>
    /// 上传记录Id
    /// </summary>
    public string RecordId { get; set; } = default!;

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 求职者学历
    /// </summary>
    public EducationType? Education { get; set; }
}

/// <summary>
/// 推荐简历请求模型
/// </summary>
public class UpLoadResumeInfoRequest
{
    /// <summary>
    /// 上传记录Id
    /// </summary>
    public string RecordId { get; set; } = default!;

    /// <summary>
    /// 职位Id
    /// </summary>
    public string TeamPostId { get; set; } = default!;

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email { get; set; } = default!;

    /// <summary>
    /// 电话
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType Education { get; set; } = default!;
}

/// <summary>
/// 上传简历模型
/// </summary>
public class UpLoadResumeModel
{
    /// <summary>
    /// 文件地址
    /// </summary>
    public string FileUrl { get; set; } = string.Empty;

    /// <summary>
    /// 文件名称
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 文件扩展名
    /// </summary>
    public TalentUpLoadFileExtension FileExtension { get; set; }

}

/// <summary>
/// 获取HR初筛上传简历记录接收模型
/// </summary>
public class GetRecruitUploadRecordListRequest : QueryRequest
{
    public string TeamPostId { get; set; } = default!;
}

/// <summary>
/// 获取HR初筛上传简历记录返回模型
/// </summary>
public class GetRecruitUploadRecordListResponse : QueryResponse
{
    public List<GetRecruitUploadRecordListModel> Rows { get; set; } = default!;
}

/// <summary>
/// 获取HR初筛上传简历记录模型
/// </summary>
public class GetRecruitUploadRecordListModel
{
    /// <summary>
    /// 记录id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// hr名称
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 文件名称
    /// </summary>
    public string? FileName { get; set; }

    /// <summary>
    /// 文件地址
    /// </summary>
    public string? FileUrl { get; set; }

    /// <summary>
    /// 重复类型
    /// </summary>
    public RecruitUpLoadRepeatType RepeatType { get; set; }

    /// <summary>
    /// 重复类型名称
    /// </summary>
    public string? RepeatTypeName { get; set; }

    /// <summary>
    /// 导入状态
    /// </summary>
    public RecruitUpLoadStatus UpLoadStatus { get; set; }

    /// <summary>
    /// 导入状态名称
    /// </summary>
    public string? UpLoadStatusName { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }
}

/// <summary>
/// 职位选择框查询
/// </summary>
public class PostTeamPageListRequest : QueryRequest
{
    public string TeamProjectId { get; set; } = default!;
    public string? Search { get; set; }
}

public class PostTeamInfoEnum
{
    /// <summary>
    /// 职位id
    /// </summary>
    public string TeamPostId { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    public string PostName { get; set; } = default!;
}

/// <summary>
/// 职位选择框返回信息
/// </summary>
public class PostTeamInfoPageListResponse : QueryResponse
{
    public List<PostTeamInfo> Rows { get; set; } = default!;
}

public class PostTeamInfo
{
    /// <summary>
    /// 主创名称 - 项目经理
    /// </summary>
    public string HrName { get; set; } = default!;

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ProjectName { get; set; } = default!;

    /// <summary>
    /// 项目编码
    /// </summary>
    public string ProjectCode { get; set; } = default!;

    /// <summary>
    /// 项目创建时间
    /// </summary>
    public DateTime? ProjectCreateTime { get; set; }

    /// <summary>
    /// 职位Id
    /// </summary>
    public string TeamPostId { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 招聘人数
    /// </summary>
    public int RecruitNumber { get; set; } = default!;

    /// <summary>
    /// 工作性质 - 全职/兼职
    /// </summary>
    public string? WorkNature { get; set; }
    public PostWorkNature WorkNatureType { get; set; }

    /// <summary>
    /// 毕业年份
    /// </summary>
    public int? GraduationYear { get; set; }

    /// <summary>
    /// 职位描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 职位类别
    /// </summary>
    public string? CategoryName { get; set; }

    /// <summary>
    /// 薪资要求(最低)
    /// </summary>
    public int MinSalary { get; set; }

    /// <summary>
    /// 薪资要求(最高)
    /// </summary>
    public int MaxSalary { get; set; }

    /// <summary>
    /// 几薪
    /// </summary>
    public int Salary { get; set; }
    public PostSalaryType SalaryType { get; set; }

    /// <summary>
    /// 薪酬描述
    /// </summary>
    public string SalaryName { get; set; } = string.Empty;

    /// <summary>
    /// 学历
    /// </summary>
    public string? Education { get; set; }

    /// <summary>
    /// 地区id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 城市
    /// </summary>
    public string? City { get; set; }

    // /// <summary>
    // /// 坐标
    // /// </summary>
    // public Point? Location { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 福利
    /// </summary>
    public List<WelfareModel>? Welfare { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string? Sex { get; set; }

    /// <summary>
    /// 最小年龄
    /// </summary>
    public int? MinAge { get; set; }

    /// <summary>
    /// 最大年龄
    /// </summary>
    public int? MaxAge { get; set; }

    public bool IsFirst { get; set; } = false;
}

#endregion

#region 批量入职导入

public class BatchInductionResponse
{
    /// <summary>
    /// 成功描述
    /// </summary>
    public string SuccessStr { get; set; } = default!;

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; } = 0;

    /// <summary>
    /// 失败描述
    /// </summary>
    public string FailedStr { get; set; } = default!;

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; } = 0;
}

public class BatchInductionRequest
{
    /// <summary>
    /// 协同项目Id
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 文件路径
    /// </summary>
    public string Url { get; set; } = default!;
}

// public class BatchInduction
// {
//     [ExcelColumnName("求职者姓名")]
//     public string? Name { get; set; }

//     /// <summary>
//     /// 手机号
//     /// </summary>
//     [ExcelColumnName("手机号")]
//     public string? Phone { get; set; }

//     /// <summary>
//     /// 身份证号
//     /// </summary>
//     [ExcelColumnName("身份证号")]
//     public string? IDNumber { get; set; }

//     /// <summary>
//     /// 性别
//     /// </summary>
//     [ExcelColumnName("性别")]
//     public string? Sex { get; set; }

//     /// <summary>
//     /// 学历
//     /// </summary>
//     [ExcelColumnName("学历")]
//     public string? Education { get; set; }

//     /// <summary>
//     /// 面试时间
//     /// </summary>
//     [ExcelColumnName("面试时间")]
//     public DateTime? InterviewTime { get; set; }

//     /// <summary>
//     /// 入职时间
//     /// </summary>
//     [ExcelColumnName("入职时间")]
//     public DateTime? InductionTime { get; set; }

//     /// <summary>
//     /// 面试是否到场
//     /// </summary>
//     [ExcelColumnName("面试是否到场")]
//     public string? IsArrival { get; set; }
// }
#endregion