﻿using Config.Enums;
using System.Drawing;

namespace Config.CommonModel.ThirdTalentInfo;

/// <summary>
/// 第三方简历池模型
/// </summary>
public class ResumeBufferModels
{
    /// <summary>
    /// 主键id
    /// </summary>
    public string? Id { get; set; }
    /// <summary>
    /// 第三方表主键
    /// </summary>
    public string ResumeId { get; set; } = default!;
    /// <summary>
    /// 来源：0-快招工，1-导入
    /// </summary>
    public int Source { get; set; } = 0;
    /// <summary>
    /// 0-待筛选，1-有效线索，2-无效线索
    /// </summary>
    public int Valid { get; set; } = 0;
    /// <summary>
    /// 诺快聘职位名称
    /// </summary>
    public string PostName { get; set; } = default!;
    /// <summary>
    /// 诺快聘职位Id-TeamPostId
    /// </summary>
    public string PostId { get; set; } = default!;
    /// <summary>
    /// 报名时间/导入时间
    /// </summary>
    public DateTime ApplyTime { get; set; }
    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; } = default!;
    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }
    /// <summary>
    /// 性别
    /// </summary>
    public string? Sex { get; set; }
    /// <summary>
    /// 生日
    /// </summary>
    public DateTime Birthday { get; set; }
    /// <summary>
    /// 年龄
    /// </summary>
    public int Age { get; set; }
    /// <summary>
    /// 电话
    /// </summary>
    public string Mobile { get; set; } = default!;
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? HrId { get; set; }
    /// <summary>
    /// 顾问名称
    /// </summary>
    public string? HrName { get; set; }
    /// <summary>
    /// 渠道商Id
    /// </summary>
    public string? ChannelId { get; set; }
    /// <summary>
    /// 渠道商名称
    /// </summary>
    public string? ChannelName { get; set; }
    /// <summary>
    /// 微信号码
    /// </summary>
    public string? WeChat { get; set; }
    /// <summary>
    /// QQ号码
    /// </summary>
    public string? QQ { get; set; }
    /// <summary>
    /// 邮箱号
    /// </summary>
    public string? Mailbox { get; set; }
    /// <summary>
    /// 所在地
    /// </summary>
    public string? Location { get; set; }
    /// <summary>
    /// 所在地经纬度
    /// </summary>
    public Point LocationPoint { get; set; } = new Point(0, 0);
    /// <summary>
    /// 最高学历
    /// </summary>
    public string? TopEducation { get; set; }
    /// <summary>
    /// 自我评价
    /// </summary>
    public string? SelfEvaluation { get; set; }
    /// <summary>
    /// 技能分析
    /// </summary>
    public ResumeBufferSkill? Skills { get; set; }
    /// <summary>
    /// 行业标签（来源小析职业标签字段）
    /// </summary>
    public List<string> IndustryLabel { get; set; } = new List<string>();
    /// <summary>
    /// 职位标签（来源小析职业标签字段）
    /// </summary>
    public List<string> PostLabel { get; set; } = new List<string>();
    /// <summary>
    /// 其他标签（来源小析职业标签字段）
    /// </summary>
    public List<string> OtherLabel { get; set; } = new List<string>();
    /// <summary>
    /// 求职期望
    /// </summary>
    public ResumeBufferHopes? Hopes { get; set; }
    /// <summary>
    /// 教育经历
    /// </summary>
    public ResumeBufferEducations? Educations { get; set; }
    /// <summary>
    /// 项目经历
    /// </summary>
    public ResumeBufferProjects? Projects { get; set; }
    /// <summary>
    /// 工作经历
    /// </summary>
    public ResumeBufferWorks? Works { get; set; }

    public DateTime CreatedTime { get; set; } = DateTime.Now;
    public DateTime UpdatedTime { get; set; } = DateTime.Now;
    public string? CreatedUser { get; set; }
    public string? UpdatedUser { get; set; }
}

/// <summary>
/// 技能分析
/// </summary>
public class ResumeBufferSkill
{
    /// <summary>
    /// 专业技能集合
    /// </summary>
    public List<string>? Professional { get; set; }

    /// <summary>
    /// IT技能集合
    /// </summary>
    public List<string>? IT { get; set; }

    /// <summary>
    /// 商业技能集合
    /// </summary>
    public List<string>? Business { get; set; }
}

/// <summary>
/// 求职期望
/// </summary>
public class ResumeBufferHopes
{
    /// <summary>
    /// 期望城市
    /// </summary>
    public string? HopeCity { get; set; }

    /// <summary>
    /// 期望行业名称集合
    /// </summary>
    public string? IndustryName { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 最低薪资
    /// </summary>
    public decimal? MinSalary { get; set; }

    /// <summary>
    /// 最高薪资
    /// </summary>
    public decimal? MaxSalary { get; set; }
}

/// <summary>
/// 教育经历
/// </summary>
public class ResumeBufferEducations
{
    /// <summary>
    /// 教育经历id
    /// </summary>
    public string? EduId { get; set; }

    /// <summary>
    /// 学校名称
    /// </summary>
    public string? SchoolName { get; set; }

    /// <summary>
    /// 是否全日制
    /// </summary>
    public bool? IsFullTime { get; set; }

    /// <summary>
    /// 学校logo
    /// </summary>
    public string? SchoolLogo { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public TalentVirtualEducation? Education { get; set; }

    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 专业
    /// </summary>
    public string? MajorName { get; set; }

    /// <summary>
    /// 学校描述
    /// </summary>
    public string? SchoolRemarks { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 项目经历
/// </summary>
public class ResumeBufferProjects
{
    /// <summary>
    /// 项目经历id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 项目logo
    /// </summary>
    public string? ProjectLogo { get; set; }

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 项目描述
    /// </summary>
    public string? ProjectRemarks { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 工作经历
/// </summary>
public class ResumeBufferWorks
{
    /// <summary>
    /// 工作经历id
    /// </summary>
    public string? WorkId { get; set; }

    /// <summary>
    /// 公司名称
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 公司部门
    /// </summary>
    public string? Department { get; set; }

    /// <summary>
    /// 公司Logo
    /// </summary>
    public string? CompanyLogo { get; set; }

    /// <summary>
    /// 行业名称
    /// </summary>
    public string? IndustryName { get; set; }

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 最低薪资
    /// </summary>
    public decimal? MinSalary { get; set; }

    /// <summary>
    /// 最高薪资
    /// </summary>
    public decimal? MaxSalary { get; set; }

    /// <summary>
    /// 公司描述
    /// </summary>
    public string? CompanyRemarks { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}
