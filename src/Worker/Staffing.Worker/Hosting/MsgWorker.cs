using Staffing.Worker.Service;

namespace Staffing.Worker.Hosting;

public class MsgWorker : HostingBase
{
    private readonly MsgService _msgService;

    public MsgWorker(IHostApplicationLifetime lifetime, MsgService msgService) : base(lifetime)
    {
        _msgService = msgService;
    }

    public override async Task StartAsync(CancellationToken cancel)
    {
        _bgTasks.Add(await Task.Factory.StartNew(() => _msgService.SubMsgNotify(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _msgService.SubWeChatMsgNotify(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _msgService.SubDingDingMsg(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _msgService.SubDingDingMsgFullTime(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _msgService.SubImC2CMsgUnReadMsg(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _msgService.SubNewUserPush(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _msgService.SubImC2CMsg(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _msgService.SubImC2CChatMsg(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _msgService.SubImB2CMsg(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        // return Task.CompletedTask;
    }
}