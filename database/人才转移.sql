DROP TABLE IF EXISTS `talent_transfer`;
CREATE TABLE `talent_transfer` (
  `Id` varchar(18) NOT NULL,
  `HrId` varchar(18) NOT NULL COMMENT '被转移顾问',
  `ToHrId` varchar(18) NOT NULL COMMENT '转移顾问',
  `Creator` varchar(100) NOT NULL COMMENT '创建人',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_transfer_HrId` (`HrId`),
  KEY `IX_talent_transfer_ToHrId` (`ToHrId`),
  KEY `IX_talent_transfer_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='人才转移';


DROP TABLE IF EXISTS `talent_transfer_record`;
CREATE TABLE `talent_transfer_record` (
  `Id` varchar(18) NOT NULL,
  `TransferId` varchar(18) NOT NULL COMMENT '转换Id',
  `SeekerId` varchar(18) NOT NULL COMMENT '人才库Id',
  PRIMARY KEY (`Id`),
  KEY `IX_talent_transfer_record_SeekerId` (`SeekerId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='人才转移记录';

ALTER TABLE project ADD `XbbContractNo` varchar(50) NULL COMMENT '销帮帮合同编号';


DROP TABLE IF EXISTS `xbb_contract`;
CREATE TABLE `xbb_contract` (
  `Id` varchar(18) NOT NULL,
  `NoahId` bigint NOT NULL COMMENT '数字诺亚Id',
  `NoahCode` varchar(50) NULL COMMENT '数字诺亚项目编码',
  `Name` varchar(200) NULL COMMENT '合同名称',
  `Code` varchar(50) NULL COMMENT '合同编码',
  `Category` varchar(50) NULL COMMENT '合同分类',
  `Nature` varchar(50) NULL COMMENT '合同性质',
  `BeginTime` datetime(6) NULL DEFAULT NOW(6),
  `EndTime` datetime(6) NULL DEFAULT NOW(6),
  `SignDate` datetime(6) NULL DEFAULT NOW(6) COMMENT '签订日期',
  `Status` varchar(50) NULL COMMENT '合同状态',
  `Signatory` varchar(200) NULL COMMENT '我方签约人',
  `SignerEmployeeCode` varchar(200) NULL COMMENT '我方签约人工号',
  `CustomerName` varchar(200) NULL COMMENT '客户名称',
  `ContractUrl` varchar(5000) NULL COMMENT '合同地址',
  `ProductId` varchar(50) NULL COMMENT '产品Id',
  `ProductName` varchar(500) NULL COMMENT '产品名称',
  `NkpStatus` int NULL COMMENT '诺快聘状态',
  `SendNotify` bit NULL COMMENT '是否已发送消息',
  `UpdatedBy` varchar(18) NULL COMMENT '更新人',
  `NoahUpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `NoahCreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_xbb_contract_NoahId` (`NoahId`),
  KEY `IX_xbb_contract_UpdatedTime` (`UpdatedTime` DESC),
  KEY `IX_xbb_contract_NoahUpdatedTime` (`NoahUpdatedTime` DESC),
  KEY `IX_xbb_contract_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='销帮帮合同';

ALTER TABLE wechat ADD `WeChatH5OpenId2` varchar(50) NULL COMMENT '诺快聘公众号OpenId';
ALTER TABLE wechat ADD `WeChatH5Subscribe2` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否订阅诺快聘公众号';


DROP TABLE IF EXISTS `dd_notify_record`;
CREATE TABLE `dd_notify_record` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `Type` int NOT NULL COMMENT '通知类型',
  `Status` int NOT NULL COMMENT '通知状态',
  `HrId` varchar(18) NOT NULL COMMENT '顾问Id',
  `Content` varchar(5000) NOT NULL COMMENT '通知内容',
  `Data` varchar(5000) NOT NULL COMMENT '详细数据',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_dd_notify_record_HrId` (`HrId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='钉钉通知记录';

ALTER TABLE recruit ADD `UserClient` int NULL COMMENT '用户端';


ALTER TABLE post ADD `TieType` int NOT NULL DEFAULT 0 COMMENT '领带类型';