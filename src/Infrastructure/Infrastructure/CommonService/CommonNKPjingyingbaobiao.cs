﻿using Config.CommonModel.DataScreen;
using Infrastructure.Common;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Infrastructure.CommonService;

[Service(ServiceLifetime.Transient)]
public class CommonNKPjingyingbaobiao
{
    private IDbContextFactory<StaffingContext> _staffingContextFactory;

    public CommonNKPjingyingbaobiao(IDbContextFactory<StaffingContext> staffingContextFactory)
    {
        _staffingContextFactory = staffingContextFactory;
    }

    // 未发布职位的项目，也计入未审核通过，标签为其他
    public ProjectSummaryResponse GetProjectSummary(DateTime? beginTime, DateTime? endTime)
    {
        (beginTime, endTime) = Tools.AdjustTimeRange(beginTime, endTime, new DateTime(2022, 1, 1));

        var data = new ProjectSummaryResponse();
        using var _context = _staffingContextFactory.CreateDbContext();
        var projectAll = _context.Project.Where(w => w.CreatedTime >= beginTime && w.CreatedTime < endTime)
            .Select(s => new
            {
                s.ProjectId,
                s.Status,
                AuditReject = false,
                NoPosts = !s.Posts.Any()
            })
            .ToList();
        data.ProjectNum = projectAll.Count;
        data.UnPassedProjectNum = projectAll.Count(c => c.AuditReject || c.NoPosts);
        data.PassedProjectNum = data.ProjectNum - data.UnPassedProjectNum;
        if (data.ProjectNum == 0)
        {
            data.PassedRate = 0;
        }
        else
        {
            data.PassedRate = Math.Round((decimal)(data.ProjectNum - data.UnPassedProjectNum) / data.ProjectNum, 4, MidpointRounding.AwayFromZero);
        }
        data.TeamPProjectNum = projectAll.Count(c => !c.AuditReject && !c.NoPosts);
        data.UnTeamPProjectNum = projectAll.Count(c => !c.AuditReject && !c.NoPosts);

        data.UnPassedReasonList = new List<UnPassedReason>();
        if (data.UnPassedProjectNum > 0)
        {
            var projectIds = projectAll.Where(w => w.AuditReject || w.NoPosts).Select(s => s.ProjectId).ToList();
            var rejectReason = new List<string>();

            data.UnPassedReasonList = rejectReason.GroupBy(g => g).Select(s => new UnPassedReason
            {
                Reason = s.Key!,
                Count = s.Count()
            }).ToList();
        }

        // 没有发布职位，置为《其他》标签
        if (projectAll.Count(c => c.NoPosts) > 0)
        {
            data.UnPassedReasonList.Add(new UnPassedReason
            {
                Reason = "其他",
                Count = projectAll.Count(c => c.NoPosts)
            });
        }

        return data;
    }

    // 人才线索池包括诺优考，诺聘，诺快聘，c端只是诺快聘
    // 蓝领：只有这几个？
    // c端/蓝领 总量，是开始日期之前汇总，然后从开始日期开始加上增加量
    public UserSummaryResponse GetUserSummary(DateTime? beginTime, DateTime? endTime)
    {
        (beginTime, endTime) = Tools.AdjustTimeRange(beginTime, endTime, new DateTime(2022, 1, 1));

        var data = new UserSummaryResponse();
        using var _context = _staffingContextFactory.CreateDbContext();

        // 汇总数据获取
        var userInfo = _context.User_Seeker.Where(w => w.CreatedTime < endTime).Select(s => new { s.UserId, s.CreatedTime }).ToList();// 缓存，空间换时间
        data.UserTotal = userInfo.Count();
        data.UserAddTotal = userInfo.Where(w => w.CreatedTime >= beginTime).Count();

        var llIn = new List<string> { "操作工", "客服", "电焊工", "搬运工", "缝纫工", "保安", "司机" };
        var blPredicate = PredicateBuilder.New<Post_Delivery>(w => w.CreatedTime < endTime);
        var blPredicate_or = PredicateBuilder.New<Post_Delivery>(true);
        foreach (var item in llIn)
            blPredicate_or = blPredicate_or.Or(w => w.Post.Name.Contains(item));
        var blueUserInfo = _context.Post_Delivery.Where(blPredicate.And(blPredicate_or))
            .Select(s => new { s.SeekerId, s.CreatedTime }).ToList();// 缓存，空间换时间,todo:这个数据量不知道有多少
        data.BlueUserTotal = blueUserInfo.Select(s => s.SeekerId).Distinct().Count();
        var existIds = blueUserInfo.Where(w => w.CreatedTime < beginTime).Select(s => s.SeekerId).Distinct().ToList() ?? new List<string>();// 这个要缓存起始时间之前所有数据
        var BlueUserAdds = blueUserInfo.Where(w => w.CreatedTime >= beginTime).Select(s => s.SeekerId).Distinct().ToList();
        data.BlueUserAddTotal = BlueUserAdds.Count();

        if (existIds.Count > 0)
        {
            var sect = existIds.Intersect(BlueUserAdds).ToList();
            data.BlueUserAddTotal -= sect?.Count;
        }


        // 明细数据获取
        // 总数的起始数量
        int userTotalBeginNum = userInfo.Where(w => w.CreatedTime < beginTime).Count();
        int blueTotalBeginNum = blueUserInfo.Where(w => w.CreatedTime < beginTime).Select(s => s.SeekerId).Distinct().Count();
        var details = new List<DateWithCount>();
        var dates = new List<Infrastructure.Common.DateRange>();

        // 月内
        endTime = endTime.Value.AddDays(-1);
        if (endTime.Value.Year == beginTime.Value.Year && endTime.Value.Month == beginTime.Value.Month)
        {
            dates = DateHelper.GetWeekRanges(beginTime.Value, endTime.Value, WeekBegin.周日);
        }
        else// 月外
        {
            dates = DateHelper.GetMonthRanges(beginTime.Value, endTime.Value);
        }

        int userAddTotalCount = 0, blueUserAddTotalCount = 0;

        foreach (var date in dates)
        {
            var userATC = userInfo.Count(c => c.CreatedTime >= date.StartDate && c.CreatedTime < date.EndDate.AddDays(1));
            userAddTotalCount += userATC;
            var blueUAT = blueUserInfo.Where(c => c.CreatedTime >= date.StartDate && c.CreatedTime <= date.EndDate.AddDays(1)).Select(s => s.SeekerId).Distinct().ToList();
            var intersection = blueUAT.Intersect(existIds).ToList();
            var blueUATCount = blueUAT.Count() - intersection.Count();// 排除已存在seekerid的数量
            existIds.AddRange(blueUAT);
            blueUserAddTotalCount += blueUATCount;
            details.Add(new DateWithCount
            {
                BeginDate = date.StartDate,
                EndDate = date.EndDate,
                UserTotalCount = userTotalBeginNum + userAddTotalCount,
                UserAddTotalCount = userATC,
                BlueUserTotalCount = blueTotalBeginNum + blueUserAddTotalCount,
                BlueUserAddTotalCount = blueUATCount
            });
        }
        data.Details = details;

        return data;
    }

    // todo:这里的投递简历总量，是截止日期有关（总量），还是跟日期区间有关（变动量）
    // 20240718跟产品确定，是增量（除了c端总量是截止日期，别的都是增量）
    public DeliverySummaryResponse GetDeliverySummary(DateTime? beginTime, DateTime? endTime)
    {
        (beginTime, endTime) = Tools.AdjustTimeRange(beginTime, endTime, new DateTime(2022, 1, 1));
        var data = new DeliverySummaryResponse();
        using var _context = _staffingContextFactory.CreateDbContext();
        var recruitInfo = _context.Post.Where(w => w.Project.CreatedTime >= beginTime && w.Project.CreatedTime < endTime).Select(s => new { s.DeliveryNumber, s.Project.CreatedTime }).ToList();
        var deliveryInfo = _context.Post_Delivery.Where(w => w.Post.Project.CreatedTime >= beginTime && w.Post.Project.CreatedTime < endTime).Select(s => new { s.Post.Project.CreatedTime, s.User_Resume.Sex }).ToList();

        // todo:这里都是时间区间的增量总量
        data.DeliveryNum = deliveryInfo.Count;
        data.RecruitNum = recruitInfo.Sum(s => s.DeliveryNumber);
        data.DeliveryMaleNum = deliveryInfo.Count(c => c.Sex == Config.Enums.Sex.男);
        data.DeliveryFemaleNum = deliveryInfo.Count(c => c.Sex == Config.Enums.Sex.女);
        data.DeliveryUnknownSexNum = deliveryInfo.Count(c => c.Sex == null);

        var details = new List<DeliveryDateWithCount>();
        var dates = new List<Infrastructure.Common.DateRange>();

        // 月内
        endTime = endTime.Value.AddDays(-1);
        if (endTime.Value.Year == beginTime.Value.Year && endTime.Value.Month == beginTime.Value.Month)
        {
            dates = DateHelper.GetWeekRanges(beginTime.Value, endTime.Value, WeekBegin.周日);
        }
        else// 月外
        {
            dates = DateHelper.GetMonthRanges(beginTime.Value, endTime.Value);
        }

        foreach (var date in dates)
        {
            details.Add(new DeliveryDateWithCount
            {
                BeginDate = date.StartDate,
                EndDate = date.EndDate,
                RecruitNum = recruitInfo.Where(w => w.CreatedTime >= date.StartDate && w.CreatedTime < date.EndDate.AddDays(1)).Sum(s => s.DeliveryNumber),
                DeliveryNum = deliveryInfo.Where(w => w.CreatedTime >= date.StartDate && w.CreatedTime < date.EndDate.AddDays(1)).Count()
            });
        }
        data.Details = details;
        return data;
    }

    // todo:面试人数，是面试过的人数，因为自流转会入职或归档 可能跳过面试环节，直接发offer或者入职
    // 入职人数，是入职过的人数，因为入职了，也会归档（离职）
    // 简历完整度 筛选跟左边报表一致,按人去重？
    public ZhiboDeliverySummaryResponse GetZhiboDeliverySummary(DateTime? beginTime, DateTime? endTime)
    {
        (beginTime, endTime) = Tools.AdjustTimeRange(beginTime, endTime, new DateTime(2022, 1, 1));
        var data = new ZhiboDeliverySummaryResponse();
        using var _context = _staffingContextFactory.CreateDbContext();
        var recruitInfo = _context.Recruit.Where(w => w.CreatedTime >= beginTime && w.CreatedTime < endTime && w.User_Seeker.Source == Config.Enums.RegisterSource.KsTalent)
            .Select(s => new
            {
                s.SeekerId,
                Interviewed = s.Recruit_Record.Any(a => a.Status == Config.Enums.RecruitStatus.Interview),
                Inductioned = s.Recruit_Record.Any(a => a.Status == Config.Enums.RecruitStatus.Induction),
                //s.User_Seeker.User_Resume.Score,
                s.CreatedTime
            }).ToList();

        //data.Percent0_50Num = recruitInfo.Where(w => w.Score < 50).Count();
        //data.Percent80_50Num = recruitInfo.Where(w => w.Score >= 50 && w.Score < 80).Count();
        //data.Percent80Num = recruitInfo.Where(w => w.Score >= 80).Count();

        var details = new List<ZhiboDeliveryDateWithCount>();
        var dates = new List<Infrastructure.Common.DateRange>();

        // 月内
        endTime = endTime.Value.AddDays(-1);
        if (endTime.Value.Year == beginTime.Value.Year && endTime.Value.Month == beginTime.Value.Month)
        {
            dates = DateHelper.GetWeekRanges(beginTime.Value, endTime.Value, WeekBegin.周日);
        }
        else// 月外
        {
            dates = DateHelper.GetMonthRanges(beginTime.Value, endTime.Value);
        }

        foreach (var date in dates)
        {
            details.Add(new ZhiboDeliveryDateWithCount
            {
                BeginDate = date.StartDate,
                EndDate = date.EndDate,
                SentNum = recruitInfo.Where(w => w.CreatedTime >= date.StartDate && w.CreatedTime < date.EndDate.AddDays(1)).Count(),
                InterviewNum = recruitInfo.Where(w => w.CreatedTime >= date.StartDate && w.CreatedTime < date.EndDate.AddDays(1) && w.Interviewed).Count(),
                InductionNum = recruitInfo.Where(w => w.CreatedTime >= date.StartDate && w.CreatedTime < date.EndDate.AddDays(1) && w.Inductioned).Count()
            });
        }
        data.Details = details;

        return data;
    }

    public HrTalentSummaryResponse GetHrTalentSummary(HrTalentRequest request)
    {
        if (request.HrIds.Count() == 0)
            throw new Exception("缺少顾问id");
        (var beginTime, var endTime) = Tools.AdjustTimeRange(request.StartDate, request.EndDate, new DateTime(2022, 1, 1));

        var data = new HrTalentSummaryResponse();
        using var _context = _staffingContextFactory.CreateDbContext();

        var totalInfo = _context.Talent_Platform.IgnoreQueryFilters().Where(w => request.HrIds.Contains(w.HrId) && w.CreatedTime < endTime).Select(s => new { s.CreatedTime }).ToList(); // s.HrId, s.SeekerId,
        var beginInfo = totalInfo.Where(w => w.CreatedTime < beginTime).Select(s => new { s.CreatedTime }).ToList(); // s.HrId, s.SeekerId, 

        data.TalentNum = totalInfo.Where(w => w.CreatedTime >= beginTime && w.CreatedTime < endTime).Count();

        var details = new List<HrTalentDetail>();
        var dates = new List<Infrastructure.Common.DateRange>();

        // 月内
        endTime = endTime.AddDays(-1);
        if (endTime.Year == beginTime.Year && endTime.Month == beginTime.Month)
        {
            dates = DateHelper.GetWeekRanges(beginTime, endTime, WeekBegin.周日);
        }
        else// 月外
        {
            dates = DateHelper.GetMonthRanges(beginTime, endTime);
        }

        int beginCount = beginInfo.Count();
        int addCount = 0;
        foreach (var date in dates)
        {
            int addC = totalInfo.Where(w => w.CreatedTime >= date.StartDate && w.CreatedTime < date.EndDate.AddDays(1)).Count();
            addCount += addC;
            details.Add(new HrTalentDetail
            {
                BeginDate = date.StartDate,
                EndDate = date.EndDate,
                SumNum = beginCount + addCount,
                AddNum = addC,
            });
        }
        data.Details = details;

        return data;
    }

    public ZhiboDeliverySummaryResponse GetZhiboDeliveryPercentSummary(DateTime? beginTime, DateTime? endTime)
    {
        (beginTime, endTime) = Tools.AdjustTimeRange(beginTime, endTime, new DateTime(2022, 1, 1));
        var data = new ZhiboDeliverySummaryResponse();
        using var _context = _staffingContextFactory.CreateDbContext();
        var recruitInfo = _context.Recruit.Where(w => w.CreatedTime >= beginTime && w.CreatedTime < endTime && w.User_Seeker.Source == Config.Enums.RegisterSource.KsTalent)
            .Select(s => new
            {
                s.User_Seeker.User_Resume.Score,
            }).ToList();

        data.Percent0_50Num = recruitInfo.Where(w => w.Score < 50).Count();
        data.Percent80_50Num = recruitInfo.Where(w => w.Score >= 50 && w.Score < 80).Count();
        data.Percent80Num = recruitInfo.Where(w => w.Score >= 80).Count();
        return data;
    }
}


