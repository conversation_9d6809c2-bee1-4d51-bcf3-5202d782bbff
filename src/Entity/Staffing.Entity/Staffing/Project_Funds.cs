﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config;
using Config.CommonModel;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 项目资金
/// </summary>
[Table("project_funds")]
public class Project_Funds
{
    /// <summary>
    /// 项目ID
    /// </summary>
    [Key]
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount { get; set; }
}