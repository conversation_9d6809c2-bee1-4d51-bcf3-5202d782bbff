using System.Text.Json.Serialization;

namespace Config.CommonModel.Business;

public class GetNykRequestNo
{
    /// <summary>
    /// 来源1001诺快聘
    /// </summary>
    [JsonIgnore]
    public int Origin { get; set; } = 1001;

    /// <summary>
    /// 诺聘的token
    /// </summary>
    [JsonIgnore]
    public string? AccessToken { get; set; }

    /// <summary>
    /// 页面的地址
    /// </summary>
    public string? PageUrl { get; set; }
}

public class GetNykRequestNoResponse
{
    /// <summary>
    /// 请求编码(1分钟有效)
    /// </summary>
    public string? RequestNo { get; set; }
}