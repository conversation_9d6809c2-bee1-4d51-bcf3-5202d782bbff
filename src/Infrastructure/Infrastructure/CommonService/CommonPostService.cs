using Config;
using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;
using DocumentFormat.OpenXml.Spreadsheet;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Infrastructure.Proxy;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using NetTopologySuite.Geometries;
using ServiceStack;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using System.Linq.Dynamic.Core;

namespace Infrastructure.CommonService;

[Service(ServiceLifetime.Transient)]
public class CommonPostService
{
    private IDbContextFactory<StaffingContext> _staffingContextFactory;
    private ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private RequestContext _user;
    private readonly CommonDicService _commonDicService;
    private readonly CommonProjectService _commonProjectService;
    private readonly NoahData _noahData;
    private readonly CommonCacheService _commonCacheService;

    public CommonPostService(IDbContextFactory<StaffingContext> staffingContextFactory, IOptionsSnapshot<ConfigManager> config,
        IHostEnvironment hostingEnvironment, LogManager log, RequestContext user, CommonDicService commonDicService, CommonProjectService commonProjectService,
        NoahData noahData, CommonCacheService commonCacheService)
    {
        _staffingContextFactory = staffingContextFactory;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
        _user = user;
        _commonDicService = commonDicService;
        _commonProjectService = commonProjectService;
        _noahData = noahData;
        _commonCacheService = commonCacheService;
    }

    public SeekerGetPostsResponse SeekerGetPosts(SeekerGetPosts model)
    {
        using var _context = _staffingContextFactory.CreateDbContext();
        var result = new SeekerGetPostsResponse();

        var idPre = Constants.PostIdPre;

        var predicate = PredicateBuilder.New<Post_Team>(x => x.Show);

        if (!string.IsNullOrWhiteSpace(model.AdviserId))
            _user.AdviserId = model.AdviserId;

        if (model.IsExcellent)
            predicate = predicate.And(x => x.Post_Excellent.Status == ExcellentPostStatus.Active);
        else
            predicate = predicate.And(x => x.Project_Team.HrId == _user.AdviserId);

        if (model.ProjectIndustry.HasValue)
            predicate = predicate.And(x => x.Project_Team.Project.Industry == model.ProjectIndustry);

        if (!string.IsNullOrWhiteSpace(model.RecommendTeamPostId))
        {
            var postCategoryLevel = _context.Post_Team.Where(x => x.TeamPostId == model.RecommendTeamPostId)
            .Select(s => s.Post.Dic_Post.Level).FirstOrDefault();
            postCategoryLevel = $"{postCategoryLevel?.Split('.')[0]}.";
            if (!string.IsNullOrWhiteSpace(postCategoryLevel))
                predicate = predicate.And(x => x.Post.Dic_Post.Level.StartsWith(postCategoryLevel));
        }

        if (!string.IsNullOrWhiteSpace(model.Search))
        {
            if (model.Search.StartsWith(idPre) && int.TryParse(model.Search.Substring(1), out var id))
                predicate = predicate.And(x => x.Post.AutoId == id || x.Post.Name.Contains(model.Search));
            else
                predicate = predicate.And(x => x.Post.Name.Contains(model.Search)
                || x.Post.Dic_Post.Name.Contains(model.Search) || x.Post.Project.Agent_Ent.DisplayName.Contains(model.Search));
        }

        if (model.Category.HasValue)
        {
            var categoryLevel = (_commonDicService.GetPost())
            .FirstOrDefault(x => x.Id == model.Category.ToString())?.Level;

            if (!string.IsNullOrWhiteSpace(categoryLevel))
                predicate = predicate.And(x => x.Post.Dic_Post.Level.StartsWith(categoryLevel));
        }

        if (!string.IsNullOrWhiteSpace(model.AgentEntId))
            predicate = predicate.And(x => x.Post.Agent_Ent.AgentEntId == model.AgentEntId);

        if (model.Sex.HasValue)
            predicate = predicate.And(x => x.Post.Sex == model.Sex);

        if (model.SettlementType?.Count > 0)
            predicate = predicate.And(x => model.SettlementType.Contains(x.Post.SettlementType));

        if (model.WorkCycleType?.Count > 0)
            predicate = predicate.And(x => model.WorkCycleType.Contains(x.Post.Project.WorkCycleType));

        if (model.WorkNature.HasValue)
            predicate = predicate.And(x => x.Post.WorkNature == model.WorkNature);

        if (model.Nature.HasValue)
            predicate = predicate.And(x => x.Post.Project.Agent_Ent.Nature == model.Nature);

        if (model.Scale.HasValue)
            predicate = predicate.And(x => x.Post.Project.Agent_Ent.Scale == model.Scale);

        if (model.Education.HasValue)
            predicate = predicate.And(x => model.Education == EducationType.不限 || x.Post.Education <= model.Education);

        if (model.TeamPostIds?.Count > 0)
            predicate = predicate.And(x => model.TeamPostIds.Contains(x.TeamPostId));

        if (model.Citys?.Count > 0)
        {
            var subPredicate = PredicateBuilder.New<Post_Team>(false);
            foreach (var item in model.Citys)
                subPredicate = subPredicate.Or(x => x.Post.RegionId.StartsWith(item));

            predicate = predicate.And(subPredicate);
        }

        //抖音小程序审核
        if (Tools.IsDyClient(_user.ClientId))
        {
            predicate = GetFilterPostCondition(predicate);
        }

        var sql = _context.Post_Team.Where(predicate);

        result.Total = sql.Count();

        // 排序改为优先内部职位
        var sortSql = sql.OrderByDescending(x => x.TopTime).ThenBy(x => x.Post.Source).ThenByDescending(x => x.CreatedTime);
        if (!string.IsNullOrWhiteSpace(model.ChannelId))
        {
            sortSql = sql.OrderByDescending(o => o.Post_Team_Channel.Where(x => x.ChannelId == model.ChannelId).Max(m => m.TopTime))
            .ThenByDescending(x => x.TopTime)
            .ThenByDescending(x => x.Post.Score)
            .ThenByDescending(o => o.Post.CreatedTime);
        }
        else if (model.IsExcellent)
        {
            sortSql = sql.OrderByDescending(o => o.Post_Excellent.RefreshTime);
        }
        else if (model.Sort == 1)
        {
            // sortSql = sql.OrderBy(o => EF.Functions.SpatialDistanceSphere(curentLocation, o.Post.Location, SpatialDistanceAlgorithm.Native))
            var curentLocation = new Point(model.Lat, model.Lng);
            sortSql = sql.OrderBy(o => o.Post.Location.Distance(curentLocation))
            .ThenByDescending(x => x.Post.Score)
            .ThenByDescending(o => o.Post.CreatedTime);
        }
        else if (model.Sort == 2)
            sortSql = sql.OrderByDescending(x => x.Post.Score).ThenByDescending(o => o.Post.CreatedTime);

        //是否诺聘
        var isNuoPin = _user.AdviserId == Constants.PlatformHrId;

        result.Rows = sortSql
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new SeekerGetPostInfo
        {
            AgentEnt = new GetAgentEntDetail
            {
                DisplayName = s.Post.Agent_Ent.DisplayName,
                AgentEntId = s.Post.AgentEntId,
                Scale = s.Post.Agent_Ent.Scale,
                Abbr = s.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post.Agent_Ent.Abbr : s.Post.Agent_Ent.DisplayName,
                LogoUrl = s.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post.Agent_Ent.LogoUrl : string.Empty,
                ScaleName = s.Post.Agent_Ent.Scale.GetDescription(),
                Industry = s.Post.Agent_Ent.Industry.ToString(),
                IndustryName = s.Post.Agent_Ent.Dic_Industry.Name ?? string.Empty
            },
            Hr = new HrModel
            {
                HrId = isNuoPin ? s.Post.Project.HrId : s.Project_Team.HrId,
                NuoId = isNuoPin ? s.Post.Project.User_Hr.NuoId : s.Project_Team.User_Hr.NuoId,
                HrName = isNuoPin ? s.Post.Project.User_Hr.NickName : s.Project_Team.User_Hr.NickName,
                Avatar = isNuoPin ? s.Post.Project.User_Hr.Avatar : s.Project_Team.User_Hr.Avatar,
                Post = isNuoPin ? s.Post.Project.User_Hr.Post : s.Project_Team.User_Hr.Post,
            },
            ProjectType = s.Post.Project.Type,
            PostAutoId = s.Post.AutoId,
            ProjectId = s.Post.ProjectId,
            Name = s.Post.Name,
            CreatedTime = s.Post.CreatedTime,
            UpdatedTime = s.Post.UpdatedTime,
            Address = s.Post.Address,
            Category = s.Post.Category,
            CategoryName = s.Post.Dic_Post.Name,
            Education = s.Post.Education,
            EducationName = s.Post.Education.GetDescription(),
            Welfare = s.Post.Welfare,
            WelfareCustom = s.Post.WelfareCustom,
            Lat = s.Post.Location.X,
            Lng = s.Post.Location.Y,
            MaxSalary = s.Post.MaxSalary,
            MinSalary = s.Post.MinSalary,
            Money = s.Post.Money,
            PostId = s.Post.PostId,
            RecruitNumber = s.Post.DeliveryNumber,
            RegionId = s.Post.RegionId,
            Salary = s.Post.Salary,
            WorkNature = s.Post.WorkNature,
            SalaryType = s.Post.SalaryType,
            SalaryTypeName = s.Post.SalaryType.GetDescription(),
            WorkNatureName = s.Post.WorkNature.GetDescription(),
            TeamPostId = s.TeamPostId,
            InterviewIn24Hour = model.IsExcellent ? s.Post_Excellent.InterviewIn24Hour : false,
            EntryIn72hour = model.IsExcellent ? s.Post_Excellent.EntryIn72hour : false
        }).ToList();

        var welfare = _commonDicService.GetWelfare();
        foreach (var item in result.Rows)
        {
            item.PostNo = $"{Constants.PostIdPre}{item.PostAutoId.ToString()!.PadLeft(2, '0')}";

            item.SalaryName = Tools.FormattingSalary(item.MinSalary, item.MaxSalary, item.SalaryType, item.WorkNature, item.Salary);

            if (item.Welfare?.Count > 0)
                item.Welfare = welfare.Where(x => item.Welfare.Any(a => a.Id == x.Id)).ToList();

            // item.FullWelfare = (item.Welfare ?? new List<WelfareModel>())
            // .Union(item.WelfareCustom ?? new List<WelfareModel>()).ToList();

            item.ProjectTypeName = item.ProjectType?.GetDescription();

            if (!string.IsNullOrWhiteSpace(item.RegionId))
                item.Region = _commonDicService.GetCityById(item.RegionId);

            //Highlight
            if (item.Welfare?.Any(x => new List<int> { 15, 22, 44, 49, 50 }.Contains(x.Id)) == true)
                item.NewTagType = 1;
            else if (item.Welfare?.Any(x => new List<int> { 1, 4, 6, 14, 58 }.Contains(x.Id)) == true)
                item.NewTagType = 2;
        }

        return result;
    }

    /// <summary>
    /// post_team 表公共筛选条件，用于筛选出符合要求的岗位
    /// </summary>
    /// <param name="predicate"></param>
    /// <returns></returns>
    public ExpressionStarter<Post_Team> GetFilterPostCondition(ExpressionStarter<Post_Team> predicate)
    {
        var entNature = new List<EnterpriseNature> { EnterpriseNature.国企, EnterpriseNature.基金会, EnterpriseNature.政府事业单位 };
        predicate = predicate.And(x => !entNature.Contains(x.Post.Project.Agent_Ent.Nature)
                    && x.Post.WorkNature != PostWorkNature.兼职 && !x.Post.Dic_Post.Level.StartsWith("384.412.415.")
                    && !x.Post.Describe!.Contains("兼职")
                    && x.Post.Project.Agent_Ent.Display == EntDisplayType.企业名称
                    && !x.Post.Dic_Post.Level.StartsWith("510.553."));


        var pnList = new List<string>
            {
                "面点生产操作工",
                "包装工",
                "配货员",
                "长白班操作工",
                "两班倒操作工",
                "缝纫机工",
                "化验员",
                "数控操作工",
                "工业润滑油销售员",
                "四药生产操作工",
                "分建工",
                "课程顾问",
                "骑手",
                "咨询顾问",
                "骑手",
                "帮厨",
                "洗碗工",
                "服务员",
                "厨师长",
                "厨师",
                "快递员",
                "保洁员",
                "快递员",
                "行政助理（偏设计）",
                "城市经理",
                "客房卫生员（包食宿）",
                "坐席客服",
                "河北电信客服代表",
                "汽车邀约客服",
                "财税接线",
                "联通接听客服",
                "嘀嗒投诉专员",
                "嘀嗒接线客服",
                "嘀嗒在线打字客服",
                "电话客服",
                "四药集团生产操作工",
                "面点生产操作工",
                "板框车间操作工",
                "带电作业操作工",
                "早8晚5-汽车配件厂招聘操作工",
            }.Distinct().ToList();

        // // x.Post.Name包含操作工、普工、技工的不显示，但是如果pnList包含x.Post.Name，或者x.Post.Name包含"客服"则显示
        // predicate = predicate.And(x => (!x.Post.Name.Contains("操作工") && !x.Post.Name.Contains("普工") && !x.Post.Name.Contains("技工"))
        // || pnList.Contains(x.Post.Name) || x.Post.Name.Contains("客服"));

        //只显示客服和品牌同事提供的职位
        predicate = predicate.And(x => pnList.Contains(x.Post.Name) || x.Post.Name.Contains("客服"));

        var dyPb = MyRedis.Client.HGetAll("dypostpb");
        if (dyPb != null)
        {
            if (dyPb.TryGetValue("post", out var poststr))
            {
                try
                {
                    var post = (poststr ?? string.Empty).Split(',').ToList();
                    foreach (var item in post)
                        predicate = predicate.And(x => !x.Post.Name.Contains(item) && !x.Post.Describe!.Contains(item));
                }
                catch { }
            }

            if (dyPb.TryGetValue("ent", out var entstr))
            {
                try
                {
                    var ent = (entstr ?? string.Empty).Split(',').ToList();
                    foreach (var item in ent)
                        predicate = predicate.And(x => !x.Post.Project.Agent_Ent.DisplayName.Contains(item));

                }
                catch { }
            }
        }

        // var entNature = new List<EnterpriseNature> { EnterpriseNature.国企, EnterpriseNature.基金会, EnterpriseNature.政府事业单位 };
        // predicate = predicate.And(x => !entNature.Contains(x.Post.Project.User_Agent_Ent.Nature)
        // && x.Post.WorkNature != PostWorkNature.兼职 && !x.Post.Name.Contains("主播")
        // && !x.Post.Name.Contains("铁路") && !x.Post.Name.Contains("机场") && !x.Post.Name.Contains("公安") && !x.Post.Name.Contains("国企")
        // && !x.Post.Name.Contains("央企") && !x.Post.Name.Contains("兼职") && !x.Post.Dic_Post.Level.StartsWith("384.412.415.")
        // && !x.Post.Name.Contains("保险") && !x.Post.Name.Contains("中国") && !x.Post.Describe!.Contains("兼职")
        // && !x.Post.Name.Contains("地铁") && !x.Post.Project.User_Agent_Ent.DisplayName.Contains("地铁")
        // && !x.Post.Name.Contains("邮政") && !x.Post.Project.User_Agent_Ent.DisplayName.Contains("邮政")
        // && !x.Post.Name.Contains("联通") && !x.Post.Name.Contains("电信") && !x.Post.Name.Contains("移动")
        // && x.Post.Project.User_Agent_Ent.Display == EntDisplayType.企业名称
        // && !x.Post.Name.Contains("普工") && !x.Post.Name.Contains("操作工") && !x.Post.Name.Contains("技工")
        // && !x.Post.Name.Contains("银行")
        // && !x.Post.Dic_Post.Level.StartsWith("928.1006.") && !x.Post.Dic_Post.Level.StartsWith("928.1006.1007.")
        // && !x.Post.Project.User_Agent_Ent.DisplayName.Contains("铁路") && !x.Post.Dic_Post.Level.StartsWith("510.553.")
        // && !x.Post.Project.User_Agent_Ent.DisplayName.Contains("机场") && !x.Post.Project.User_Agent_Ent.DisplayName.Contains("公安")
        // && !x.Post.Project.User_Agent_Ent.DisplayName.Contains("国企") && !x.Post.Project.User_Agent_Ent.DisplayName.Contains("央企")
        // && !x.Post.Project.User_Agent_Ent.DisplayName.StartsWith("中国"));
        return predicate;
    }

    public async Task<SeekerGetPostInfo> SeekerGetPost(SeekerGetPost model)
    {
        using var _context = _staffingContextFactory.CreateDbContext();

        var predicate = PredicateBuilder.New<Post_Team>(x => x.TeamPostId == model.TeamPostId);

        var sql = _context.Post_Team.Where(predicate);

        if (!string.IsNullOrWhiteSpace(model.AdviserId))
            _user.AdviserId = model.AdviserId;

        //是否诺聘
        var isNuoPin = _user.AdviserId == Constants.PlatformHrId && !(model.OnlyPostId == true);

        var result = sql
        .Select(s => new SeekerGetPostInfo
        {
            AgentEnt = new GetAgentEntDetail
            {
                Display = s.Post.Agent_Ent.Display,
                AgentEntId = s.Post.AgentEntId,
                Scale = s.Post.Agent_Ent.Scale,
                ScaleName = s.Post.Agent_Ent.Scale.GetDescription(),
                LogoUrl = s.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post.Agent_Ent.LogoUrl : string.Empty,
                DisplayName = s.Post.Agent_Ent.DisplayName,
                Name = s.Post.Agent_Ent.Name,
                Abbr = s.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post.Agent_Ent.Abbr : s.Post.Agent_Ent.DisplayName,
                Nature = s.Post.Agent_Ent.Nature,
                NatureName = s.Post.Agent_Ent.Nature.GetDescription(),
                Industry = s.Post.Agent_Ent.Industry.ToString(),
                Capital = s.Post.Agent_Ent.Capital,
                RegionId = s.Post.Agent_Ent.RegionId,
                Address = s.Post.Agent_Ent.Address,
                Lat = s.Post.Agent_Ent.Location.X,
                Lng = s.Post.Agent_Ent.Location.Y,
                IndustryName = s.Post.Agent_Ent.Dic_Industry.Name ?? string.Empty,
                UpdatedTime = s.UpdatedTime,
                CapitalName = s.Post.Agent_Ent.Capital.GetDescription(),
                Posts = _context.Post_Team.Count(x => x.Project_Team.HrId == _user.AdviserId &&
                x.Post.Agent_Ent.AgentEntId == s.Post.Agent_Ent.AgentEntId && x.Show)
            },
            Hr = new HrModel
            {
                AppletQrCode = isNuoPin ? s.Post.Project.User_Hr.AppletQrCode : s.Project_Team.User_Hr.AppletQrCode,
                HrId = isNuoPin ? s.Post.Project.HrId : s.Project_Team.HrId,
                NuoId = isNuoPin ? s.Post.Project.User_Hr.NuoId : s.Project_Team.User_Hr.NuoId,
                HrName = isNuoPin ? s.Post.Project.User_Hr.NickName : s.Project_Team.User_Hr.NickName,
                Avatar = isNuoPin ? s.Post.Project.User_Hr.Avatar : s.Project_Team.User_Hr.Avatar,
                Post = isNuoPin ? s.Post.Project.User_Hr.Post : s.Project_Team.User_Hr.Post,
                LoginTime = isNuoPin ? s.Post.Project.User_Hr.User_Extend.LoginTime : s.Project_Team.User_Hr.User_Extend.LoginTime,
                EntName = isNuoPin ? s.Post.Project.User_Hr.Enterprise.Name : s.Project_Team.User_Hr.Enterprise.Name,
                EntAbbr = isNuoPin ? s.Post.Project.User_Hr.Enterprise.Abbreviation : s.Project_Team.User_Hr.Enterprise.Abbreviation,
                Mobile = isNuoPin ? s.Post.Project.User_Hr.User.Mobile : s.Project_Team.User_Hr.User.Mobile,
                TencentImId = isNuoPin ? s.Post.Project.User_Hr.TencentImId : s.Project_Team.User_Hr.TencentImId,
                Identity = !string.IsNullOrEmpty(s.Project_Team.Project.User_Hr.User.IdentityCard),
            },
            Show = s.Show,
            TeamPostId = s.TeamPostId,
            TeamHrId = s.Project_Team.HrId,
            ProjectId = s.Post.ProjectId,
            Name = s.Post.Name,
            TieType = s.Post.TieType,
            Status = s.Post.Status,
            TeamStatus = s.Status,
            ProjectStatus = s.Post.Project.Status,
            Describe = s.Post.Describe,
            PaymentNode = s.Post.PaymentNode,
            PaymentDays = s.Post.PaymentDays,
            ProjectType = s.Post.Project.Type,
            Address = s.Post.Address,
            Category = s.Post.Category,
            CategoryName = s.Post.Dic_Post.Name,
            CreatedTime = s.CreatedTime,
            UpdatedTime = s.UpdatedTime,
            Department = s.Post.Department,
            Education = s.Post.Education,
            EducationName = s.Post.Education.GetDescription(),
            Highlights = s.Post.Highlights,
            Lat = s.Post.Location.X,
            Lng = s.Post.Location.Y,
            MaxSalary = s.Post.MaxSalary,
            MinSalary = s.Post.MinSalary,
            Money = s.Post.Money,
            PaymentType = s.Post.Project.PaymentType,
            PostId = s.PostId,
            RecruitNumber = s.Post.DeliveryNumber,
            RegionId = s.Post.RegionId,
            Salary = s.Post.Salary,
            SettlementType = s.Post.SettlementType,
            WorkingDays = s.Post.WorkingDays,
            WorkingHours = s.Post.WorkingHours,
            MinMonths = s.Post.MinMonths,
            DaysPerWeek = s.Post.DaysPerWeek,
            GraduationYear = s.Post.GraduationYear,
            Tags = s.Post.Tags,
            Welfare = s.Post.Welfare,
            WelfareCustom = s.Post.WelfareCustom,
            WorkNature = s.Post.WorkNature,
            SalaryType = s.Post.SalaryType,
            SalaryTypeName = s.Post.SalaryType.GetDescription(),
            WorkNatureName = s.Post.WorkNature.GetDescription(),
            Sex = s.Post.Sex,
            MinAge = s.Post.MinAge,
            MaxAge = s.Post.MaxAge,
            AppletQrCode = s.AppletQrCode,
            RegistrationNum = s.Post.Post_Extend.DeliveryNum,
            LocationMap = s.Post.LocationMap,
            TeamProjectId = s.TeamProjectId,
            Top = s.TopTime > Config.Constants.DefaultTime,
            Collect = string.IsNullOrEmpty(_user.Id) ? false : _context.User_Collect_Post.Any(x => x.SeekerId == _user.Id && x.TeamPostId == model.TeamPostId),
            DeliveryId = string.IsNullOrEmpty(_user.Id) ? null : _context.Recruit
            .Where(x => x.Post_Delivery.TeamPostId == model.TeamPostId && x.SeekerId == _user.Id)
            .OrderByDescending(o => o.CreatedTime)
            .Select(s => s.DeliveryId).FirstOrDefault()
        }).FirstOrDefault();

        if (result == null)
            throw new NotFoundException("内容不存在");

        result.PostNo = $"{Constants.PostIdPre}{result.PostAutoId.ToString()!.PadLeft(2, '0')}";

        result.PaymentNodeName = result.PaymentNode?.GetDescription();
        result.PaymentTypeName = result.PaymentType?.GetDescription();
        result.ProjectTypeName = result.ProjectType?.GetDescription();

        result.SalaryName = Tools.FormattingSalary(result.MinSalary, result.MaxSalary, result.SalaryType, result.WorkNature, result.Salary);

        if (result.Welfare?.Count > 0)
        {
            var welfare = _commonDicService.GetWelfare();
            result.Welfare = welfare.Where(x => result.Welfare.Any(a => a.Id == x.Id)).ToList();
        }

        if (!string.IsNullOrWhiteSpace(result.RegionId))
            result.Region = _commonDicService.GetCityById(result.RegionId);

        if (result.AgentEnt != null)
        {
            var entBus = await _noahData.GetEnterpriseCache(result.AgentEnt.Name);
            result.AgentEnt.Age = entBus.Age;
            result.AgentEnt.Tags = entBus.Tags;
            result.AgentEnt.RegisterDate = entBus.RegisterDate;
            result.AgentEnt.Name = string.Empty;
        }

        result.PostAvatars = _commonCacheService.GetRandomPostAvatars(result.PostId!);

        if (!result.Show)
            result.Status = PostStatus.关闭;

        result.StatusName = result.Status.GetDescription();

        result.Hr!.OnlineStatus = Tools.GetOnlineStatus(result.Hr.LoginTime!.Value);

        //浏览职位消息
        var vid = string.IsNullOrEmpty(_user.Id) ? $"{_user.ConnectionId}" : _user.Id;
        vid = Md5Helper.Md5($"{result.TeamPostId}_{vid}");
        MyRedis.Client.RPush(SubscriptionKey.PostView, new Sub_Post_View
        {
            HrId = result.Hr!.HrId,
            SeekerId = _user.Id,
            PostCategory = result.Category,
            PostId = result.PostId,
            TeamPostId = result.TeamPostId,
            Id = vid
        });

        if (!string.IsNullOrWhiteSpace(_user.Id))
        {
            var userId = _user.Id;
            var msg = new Sub_UserPostVisit
            {
                Post = result.Category,
                PostId = result.PostId,
                UserId = userId,
                HrId = result.TeamHrId
            };
            MyRedis.Client.LPush(SubscriptionKey.UserPostVisit, msg);

            //通知月宾数据分析
            // _ = Task.Run(async () =>
            // {
            //     await _queueHelper.SendKafkaMsg(_config.DataKafkaTopic, new KafkaViewPost
            //     {
            //         id = Guid.NewGuid().ToString(),
            //         postId = result.PostId,
            //         hrId = result.Hr.HrId,
            //         action = 1,
            //         actionTime = DateTimeOffset.Now.ToUnixTimeMilliseconds(),
            //         postName = result.Name,
            //         postTypeId = result.Category,
            //         postTypeName = result.CategoryName,
            //         userId = userId,
            //         projectId = result.ProjectId,
            //         teamPostId = result.TeamPostId,
            //         teamProjectId = result.TeamProjectId
            //     });
            // });
        }

        return result;
    }

    public async Task<SeekerGetPostInfo?> SeekerGetESPost(string TeamPostId)
    {
        using var _context = _staffingContextFactory.CreateDbContext();
        var sql = _context.Post_Team.Where(x => x.TeamPostId == TeamPostId);
        var TeamPost = sql.Select(x => new { x.PostId, x.Project_Team.HrId }).FirstOrDefault();
        if (TeamPost == null || string.IsNullOrEmpty(TeamPost.PostId))
            return null;

        if (TeamPost.HrId != Constants.PlatformHrId)
            sql = _context.Post_Team.Where(x => x.Project_Team.HrId == Constants.PlatformHrId && x.PostId == TeamPost.PostId);

        //是否诺聘
        var isNuoPin = true;

        var result = sql
        .Select(s => new SeekerGetPostInfo
        {
            AgentEnt = new GetAgentEntDetail
            {
                Display = s.Post.Agent_Ent.Display,
                AgentEntId = s.Post.Project.AgentEntId,
                Scale = s.Post.Agent_Ent.Scale,
                ScaleName = s.Post.Agent_Ent.Scale.GetDescription(),
                LogoUrl = s.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post.Agent_Ent.LogoUrl : string.Empty,
                DisplayName = s.Post.Agent_Ent.DisplayName,
                Name = s.Post.Agent_Ent.Name,
                Abbr = s.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post.Agent_Ent.Abbr : s.Post.Agent_Ent.DisplayName,
                Nature = s.Post.Agent_Ent.Nature,
                NatureName = s.Post.Agent_Ent.Nature.GetDescription(),
                Industry = s.Post.Agent_Ent.Industry.ToString(),
                Capital = s.Post.Agent_Ent.Capital,
                RegionId = s.Post.Agent_Ent.RegionId,
                Address = s.Post.Agent_Ent.Address,
                Lat = s.Post.Agent_Ent.Location.X,
                Lng = s.Post.Agent_Ent.Location.Y,
                IndustryName = s.Post.Agent_Ent.Dic_Industry.Name ?? string.Empty,
                UpdatedTime = s.UpdatedTime,
                CapitalName = s.Post.Agent_Ent.Capital.GetDescription(),
                Posts = _context.Post_Team.Count(x => x.Project_Team.HrId == _user.AdviserId &&
                x.Post.Project.AgentEntId == s.Post.Project.AgentEntId && x.Show)
            },
            Hr = new HrModel
            {
                AppletQrCode = isNuoPin ? s.Post.Project.User_Hr.AppletQrCode : s.Project_Team.User_Hr.AppletQrCode,
                HrId = isNuoPin ? s.Post.Project.HrId : s.Project_Team.HrId,
                NuoId = isNuoPin ? s.Post.Project.User_Hr.NuoId : s.Project_Team.User_Hr.NuoId,
                HrName = isNuoPin ? s.Post.Project.User_Hr.NickName : s.Project_Team.User_Hr.NickName,
                Avatar = isNuoPin ? s.Post.Project.User_Hr.Avatar : s.Project_Team.User_Hr.Avatar,
                Post = isNuoPin ? s.Post.Project.User_Hr.Post : s.Project_Team.User_Hr.Post,
                LoginTime = isNuoPin ? s.Post.Project.User_Hr.User_Extend.LoginTime : s.Project_Team.User_Hr.User_Extend.LoginTime,
                EntName = isNuoPin ? s.Post.Project.User_Hr.Enterprise.Name : s.Project_Team.User_Hr.Enterprise.Name,
                EntAbbr = isNuoPin ? s.Post.Project.User_Hr.Enterprise.Abbreviation : s.Project_Team.User_Hr.Enterprise.Abbreviation,
                Mobile = isNuoPin ? s.Post.Project.User_Hr.User.Mobile : s.Project_Team.User_Hr.User.Mobile,
                TencentImId = isNuoPin ? s.Post.Project.User_Hr.TencentImId : s.Project_Team.User_Hr.TencentImId,
                Identity = !string.IsNullOrEmpty(s.Project_Team.Project.User_Hr.User.IdentityCard),
            },
            Show = s.Show,
            TeamPostId = s.TeamPostId,
            TeamHrId = s.Project_Team.HrId,
            ProjectId = s.Post.ProjectId,
            Name = s.Post.Name,
            TieType = s.Post.TieType,
            Status = s.Post.Status,
            TeamStatus = s.Status,
            ProjectStatus = s.Post.Project.Status,
            Describe = s.Post.Describe,
            PaymentNode = s.Post.PaymentNode,
            PaymentDays = s.Post.PaymentDays,
            ProjectType = s.Post.Project.Type,
            Address = s.Post.Address,
            Category = s.Post.Category,
            CategoryName = s.Post.Dic_Post.Name,
            CreatedTime = s.CreatedTime,
            UpdatedTime = s.UpdatedTime,
            Department = s.Post.Department,
            Education = s.Post.Education,
            EducationName = s.Post.Education.GetDescription(),
            Highlights = s.Post.Highlights,
            Lat = s.Post.Location.X,
            Lng = s.Post.Location.Y,
            MaxSalary = s.Post.MaxSalary,
            MinSalary = s.Post.MinSalary,
            Money = s.Post.Money,
            PaymentType = s.Post.Project.PaymentType,
            PostId = s.PostId,
            RecruitNumber = s.Post.DeliveryNumber,
            RegionId = s.Post.RegionId,
            Salary = s.Post.Salary,
            SettlementType = s.Post.SettlementType,
            WorkingDays = s.Post.WorkingDays,
            WorkingHours = s.Post.WorkingHours,
            MinMonths = s.Post.MinMonths,
            DaysPerWeek = s.Post.DaysPerWeek,
            GraduationYear = s.Post.GraduationYear,
            Tags = s.Post.Tags,
            Welfare = s.Post.Welfare,
            WelfareCustom = s.Post.WelfareCustom,
            WorkNature = s.Post.WorkNature,
            SalaryType = s.Post.SalaryType,
            SalaryTypeName = s.Post.SalaryType.GetDescription(),
            WorkNatureName = s.Post.WorkNature.GetDescription(),
            Sex = s.Post.Sex,
            MinAge = s.Post.MinAge,
            MaxAge = s.Post.MaxAge,
            AppletQrCode = s.AppletQrCode,
            RegistrationNum = s.Post.Post_Extend.DeliveryNum,
            LocationMap = s.Post.LocationMap,
            TeamProjectId = s.TeamProjectId,
            Top = s.TopTime > Config.Constants.DefaultTime,
            Collect = string.IsNullOrEmpty(_user.Id) ? false : _context.User_Collect_Post.Any(x => x.SeekerId == _user.Id && x.TeamPostId == TeamPostId),
            DeliveryId = string.IsNullOrEmpty(_user.Id) ? null : _context.Recruit
            .Where(x => x.Post_Delivery.TeamPostId == TeamPostId && x.SeekerId == _user.Id)
            .OrderByDescending(o => o.CreatedTime)
            .Select(s => s.DeliveryId).FirstOrDefault()
        }).FirstOrDefault();

        if (result == null)
            throw new NotFoundException("内容不存在");

        result.PostNo = $"{Constants.PostIdPre}{result.PostAutoId.ToString()!.PadLeft(2, '0')}";

        result.PaymentNodeName = result.PaymentNode?.GetDescription();
        result.PaymentTypeName = result.PaymentType?.GetDescription();
        result.ProjectTypeName = result.ProjectType?.GetDescription();

        result.SalaryName = Tools.FormattingSalary(result.MinSalary, result.MaxSalary, result.SalaryType, result.WorkNature, result.Salary);

        if (result.Welfare?.Count > 0)
        {
            var welfare = _commonDicService.GetWelfare();
            result.Welfare = welfare.Where(x => result.Welfare.Any(a => a.Id == x.Id)).ToList();
        }

        if (!string.IsNullOrWhiteSpace(result.RegionId))
            result.Region = _commonDicService.GetCityById(result.RegionId);

        if (result.AgentEnt != null)
        {
            var entBus = await _noahData.GetEnterpriseCache(result.AgentEnt.Name);
            result.AgentEnt.Age = entBus.Age;
            result.AgentEnt.Tags = entBus.Tags;
            result.AgentEnt.RegisterDate = entBus.RegisterDate;
            result.AgentEnt.Name = string.Empty;
        }

        result.PostAvatars = _commonCacheService.GetRandomPostAvatars(result.PostId!);

        if (!result.Show)
            result.Status = PostStatus.关闭;

        result.StatusName = result.Status.GetDescription();

        result.Hr!.OnlineStatus = Tools.GetOnlineStatus(result.Hr.LoginTime!.Value);

        //浏览职位消息
        var vid = string.IsNullOrEmpty(_user.Id) ? $"{_user.ConnectionId}" : _user.Id;
        vid = Md5Helper.Md5($"{result.TeamPostId}_{vid}");
        MyRedis.Client.RPush(SubscriptionKey.PostView, new Sub_Post_View
        {
            HrId = result.Hr!.HrId,
            SeekerId = _user.Id,
            PostCategory = result.Category,
            PostId = result.PostId,
            TeamPostId = result.TeamPostId,
            Id = vid
        });

        if (!string.IsNullOrWhiteSpace(_user.Id))
        {
            var userId = _user.Id;
            var msg = new Sub_UserPostVisit
            {
                Post = result.Category,
                PostId = result.PostId,
                UserId = userId,
                HrId = result.TeamHrId
            };
            MyRedis.Client.LPush(SubscriptionKey.UserPostVisit, msg);
        }

        return result;
    }


    public List<PostSyncResponse> PostSync(List<string> teamPostIds)
    {
        using var _context = _staffingContextFactory.CreateDbContext();

        var result = _context.Post_Team.Where(x => teamPostIds.Contains(x.TeamPostId)).OrderByDescending(o => o.CreatedTime)
        .Select(s => new PostSyncResponse
        {
            Type = PostSyncType.职位变更,
            Ent = new PostSyncEntInfo
            {
                Display = s.Post.Agent_Ent.Display,
                AgentEntId = s.Post.Project.AgentEntId,
                Scale = s.Post.Agent_Ent.Scale,
                ScaleName = s.Post.Agent_Ent.Scale.GetDescription(),
                LogoUrl = s.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post.Agent_Ent.LogoUrl : string.Empty,
                DisplayName = s.Post.Agent_Ent.DisplayName,
                Name = s.Post.Agent_Ent.Name,
                Abbr = s.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post.Agent_Ent.Abbr : s.Post.Agent_Ent.DisplayName,
                Nature = s.Post.Agent_Ent.Nature,
                NatureName = s.Post.Agent_Ent.Nature.GetDescription(),
                Industry = s.Post.Agent_Ent.Industry.ToString(),
                Capital = s.Post.Agent_Ent.Capital,
                RegionId = s.Post.Agent_Ent.RegionId,
                Address = s.Post.Agent_Ent.Address,
                Lat = s.Post.Agent_Ent.Location.X,
                Lng = s.Post.Agent_Ent.Location.Y,
                IndustryName = s.Post.Agent_Ent.Dic_Industry.Name ?? string.Empty,
                UpdatedTime = s.Post.Agent_Ent.UpdatedTime,
                Describe = s.Post.Agent_Ent.Describe,
                Status = s.Post.Agent_Ent.Status
            },
            Hr = new PostSyncHrInfo
            {
                HrId = s.Post.Project.HrId,
                NuoId = s.Post.Project.User_Hr.NuoId,
                HrName = s.Post.Project.User_Hr.NickName,
                Avatar = s.Post.Project.User_Hr.Avatar,
                Post = s.Post.Project.User_Hr.Post,
                LoginTime = s.Post.Project.User_Hr.User_Extend.LoginTime,
                Mobile = s.Post.Project.User_Hr.User.Mobile,
                CreatedTime = s.Post.Project.User_Hr.CreatedTime,
                Describe = s.Post.Project.User_Hr.Describe,
                Sex = s.Post.Project.User_Hr.Sex
            },
            Post = new PostSyncInfo
            {
                TeamPostId = s.TeamPostId,
                ProjectId = s.Post.ProjectId,
                Name = s.Post.Name,
                Show = s.Show,
                Describe = s.Post.Describe,
                Address = s.Post.Address,
                Category = s.Post.Category,
                Department = s.Post.Department,
                Education = s.Post.Education,
                Highlights = s.Post.Highlights,
                Lat = s.Post.Location.X,
                Lng = s.Post.Location.Y,
                MaxSalary = s.Post.MaxSalary,
                MinSalary = s.Post.MinSalary,
                Money = s.Post.Money,
                PostId = s.Post.PostId,
                RecruitNumber = s.Post.DeliveryNumber,
                RegionId = s.Post.RegionId,
                Salary = s.Post.Salary,
                SettlementType = s.Post.SettlementType,
                WorkingDays = s.Post.WorkingDays,
                WorkingHours = s.Post.WorkingHours,
                MinMonths = s.Post.MinMonths,
                DaysPerWeek = s.Post.DaysPerWeek,
                GraduationYear = s.Post.GraduationYear,
                Tags = s.Post.Tags,
                Welfare = s.Post.Welfare,
                WelfareCustom = s.Post.WelfareCustom,
                WorkNature = s.Post.WorkNature,
                SalaryType = s.Post.SalaryType,
                Sex = s.Post.Sex,
                MinAge = s.Post.MinAge,
                MaxAge = s.Post.MaxAge,
                DeliveryNumber = s.Post.DeliveryNumber,
                LeftStock = s.Post.LeftStock
            }
        }).ToList();

        foreach (var item in result)
        {
            if (!string.IsNullOrWhiteSpace(item.Post!.RegionId))
                item.Post.Region = _commonDicService.GetCityById(item.Post.RegionId);

            if (!string.IsNullOrWhiteSpace(item.Ent!.RegionId))
                item.Ent.Region = _commonDicService.GetCityById(item.Ent.RegionId);
        }

        return result;
    }

    public async Task<UpdatePostResponseNew> UpdatePost(UpdatePost model)
    {
        var lockerKey = $"st:uppost:{model.UserId}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);
        using var _context = _staffingContextFactory.CreateDbContext();
        if (locker == null)
            throw new BadRequestException("操作频繁");

        var result = new UpdatePostResponseNew();
        if (string.IsNullOrWhiteSpace(model.Name))
            throw new BadRequestException("职位名称不能为空");

        if (string.IsNullOrWhiteSpace(model.PostId))
            if (!_context.Dic_Post.Any(x => x.Id == model.Category))
                throw new BadRequestException("缺少职位类别");

        if (model.MaxAge < model.MinAge && model.MaxAge != 0)
            throw new BadRequestException("年龄要求有误");

        if (model.Welfare?.Count == 0 && model.WelfareCustom?.Count == 0)
            throw new BadRequestException("缺少职位福利");

        if (!model.IsResumeExempt.HasValue)
            throw new BadRequestException("缺少是否免简历选项");

        if (string.IsNullOrWhiteSpace(model.AgentEntId))
            throw new BadRequestException("缺少招聘企业");

        if (model.AgentEnt == null)
            throw new BadRequestException("缺少招聘企业信息");

        if (string.IsNullOrWhiteSpace(model.AgentEnt.Abbr))
            throw new BadRequestException("缺少企业简称");

        if (model.AgentEnt.Display == EntDisplayType.匿名展示 && string.IsNullOrWhiteSpace(model.AgentEnt.DisplayName))
            throw new BadRequestException("缺少企业匿名展示名称");

        // AgentEntId是否存在
        if (!_context.Agent_Ent.Any(x => x.AgentEntId == model.AgentEntId))
            throw new BadRequestException("招聘企业不存在");

        if (!model.ContractType.HasValue)
            throw new BadRequestException("缺少签约方式");


        Post? post = null;

        // 是否新建职位
        var isNewPost = false;

        // // 是否有项目经理接单
        // var hasReceiver = false;

        // 是否是已上线的职位修改
        var isOnlinePost = false;

        if (!string.IsNullOrWhiteSpace(model.PostId))
        {
            post = _context.Post.FirstOrDefault(x => (x.Project.HrId == model.UserId || x.Project.SaleUserId == model.UserId) && x.PostId == model.PostId);
            if (post == null)
                throw new BadRequestException("内容不存在");

            if (post.Status != PostStatus.待审核)
                isOnlinePost = true;
        }
        else
        {
            isNewPost = true;
            // model.Status = PostStatus.待审核;
            //添加职位
            post = new Post
            {
                ProjectId = model.ProjectId,
                WorkNature = model.WorkNature,
                Category = model.Category,
                LeftStock = model.DeliveryNumber,
                CreatorId = model.UserId,
                HrAgentStatus = HrAgentStatus.TurnOff //新发布职位默认关闭小职代聊
            };
            _context.Add(post);
        }

        var project = _context.Project.Where(x => x.ProjectId == model.ProjectId)
        .Select(s => new
        {
            s.ProjectId,
            s.NuoId,
            s.PaymentType,
            s.ClearingChannel,
            s.Industry,
            s.HrId,
            s.SaleUserId,
            s.Status,
            ReceiverName = s.User_Hr.NickName
        }).FirstOrDefault();

        if (project == null)
            throw new NotFoundException("项目不存在");

        // 当前用户是否项目经理角色（注意，只看角色，因为他们可能是同一个人，但是前端界面不一样，所以需要前端传递IsReceiver来判断角色）
        var isReceiver = model.IsReceiver == true && !string.IsNullOrWhiteSpace(project.HrId) && project.HrId == model.UserId;

        // if (!string.IsNullOrWhiteSpace(project.ReceiverId))
        //     hasReceiver = true;

        if (project.HrId != model.UserId && project.SaleUserId != model.UserId)
            throw new BadRequestException("无权限操作");

        // 编辑职位
        if (!isNewPost)
        {
            // 如果是项目发布者修改职位，那么职位只能是待审核状态
            if (!isReceiver && isOnlinePost)
                throw new BadRequestException($"无权限操作，请联系项目经理({project.ReceiverName})修改");
        }

        // 如果前端传发布中，说明是以项目经理身份在操作(因为项目经理和销售可能是同一个人，但是他们操作界面不一样)
        if (isReceiver)
        {
            if (!model.IsSalesCommission.HasValue)
                throw new BadRequestException("缺少是否提供销售佣金选项");

            if (!model.PaymentNode.HasValue)
                throw new BadRequestException("缺少合作模式");

            if (!model.RewardType.HasValue)
                throw new BadRequestException("缺少佣金类型");

            if (!model.PaymentCycle.HasValue && model.RewardType == PostRewardType.长期结算)
                throw new BadRequestException("缺少佣金结算周期");

            if (model.ProfitStage == null || model.ProfitStage.Count == 0)
                throw new BadRequestException("缺少佣金设置");

            if (model.ProfitStage.Any(x => x.Amount <= 0
                || model.ProfitStage.Any(a => a.GuaranteeDays < 0)))
                throw new BadRequestException("佣金设置有误");

            if (model.ProfitStage.GroupBy(x => x.GuaranteeDays).Count() != model.ProfitStage.Count)
                throw new BadRequestException("佣金设置重复");

            if (model.RewardType == PostRewardType.长期结算)
                if (model.ProfitStage.Count > 1)
                    throw new BadRequestException("佣金设置有误");

            // if (model.PaymentDays < 0)
            //     throw new BadRequestException("入职过保天数不合法");

            // if (model.Money <= 0)
            //     throw new BadRequestException("请设置签约金额");

            if (model.DeliveryNumber <= 0)
                throw new BadRequestException("请设置招聘人数");

            // 如果上线过的职位，不允许修改以下内容
            if (!isOnlinePost)
            {
                post.IsSalesCommission = model.IsSalesCommission ?? false;
                post.PaymentNode = model.PaymentNode;
            }

            post.RewardType = model.RewardType.Value;
            if (model.PaymentCycle != null)
            {
                post.PaymentCycle = model.PaymentCycle.Value;
            }
            if (model.RewardType == PostRewardType.单次结算)
            {
                post.PaymentCycle = null;
            }
            post.PaymentDuration = model.PaymentDuration;
            // 编辑岗位时先删除Post_Profit_Stage表中对应PostId的数据再添加，避免重复数据和前后数据不一致
            if (!isNewPost)
            {
                _context.RemoveRange(_context.Post_Profit_Stage.Where(x => x.PostId == model.PostId));
            }
            _context.AddRange(model.ProfitStage.Select(s => new Post_Profit_Stage
            {
                PostId = post.PostId,
                Amount = s.Amount,
                GuaranteeDays = s.GuaranteeDays ?? 0
            }));

        }

        await Task.FromResult(1);

        //增加面试配置信息
        var interview = _context.Post_Interview_Config.FirstOrDefault(x => x.PostId == post.PostId);
        if (interview == null)
        {
            interview = new Post_Interview_Config
            {
                PostId = post.PostId
            };
            _context.Add(interview);
        }
        //项目经理要求判断面试信息包括面试时间只要存在，就发布岗位，不按照状态判断是否发布
        //经与项目经理确认，改成之前的逻辑，继续按照状态判断，上述逻辑作废
        if (model.InterviewStatus == InterviewStatus.Open)
        //if (model.Interview != null)
        {
            Project_Interviewer? interviewer = null;
            //判断InterviewerId是否存在
            interviewer = _context.Project_Interviewer.AsNoTracking().FirstOrDefault(x => x.ProjectId == model.ProjectId && x.Id == model.Interview!.InterviewerId);
            if (interviewer == null)
                throw new BadRequestException("面试场次内容缺失，面试官不存在");

            if (string.IsNullOrWhiteSpace(model.Interview!.RegionId))
                throw new BadRequestException("面试场次内容缺失，面试地址地区不能为空！");

            if (model.Interview.Lat == 0 || model.Interview.Lng == 0)
                throw new BadRequestException("面试场次内容缺失，面试地址经纬度不能为空！");

            interview.UpdatedTime = DateTime.Now;
            interview.RegionId = Tools.TrimRegionId(model.Interview.RegionId)!;
            interview.Address = model.Interview.Address;
            interview.AddressDetail = model.Interview.AddressDetail;
            interview.Location = new Point(model.Interview.Lat, model.Interview.Lng);
            interview.InterviewerId = model.Interview.InterviewerId;
            interview.InterviewMode = model.Interview.InterviewMode ?? RecruitInterviewForms.Scene;
            interview.AdvanceDays = model.Interview.AdvanceDays ?? 0;

            model.Interview.InterviewTime ??= new List<PostInterviewTimeModel>();
            model.Interview.InterviewTime = model.Interview.InterviewTime.Where(w => w.Time != null).ToList();

            var oldInterviewTime = _context.Post_Interview_Date.Where(w => w.PostId == post.PostId).ToList();

            // 按照提交的内容修改已存在数据
            foreach (var item in oldInterviewTime)
            {
                var old = model.Interview.InterviewTime.FirstOrDefault(f => f.Time == item.Time);
                if (old != null)
                    item.Status = old.Status!.Value;
            }

            // 查找oldInterviewTime中不存在而model.Interview.InterviewTime终存在的数组
            var newInterviewTime = model.Interview.InterviewTime.Where(w => !oldInterviewTime.Any(a => a.Time == w.Time) && w.Status == PostInterviewTimeStatus.可预约).ToList();
            _context.AddRange(newInterviewTime.Select(s => new Post_Interview_Date
            {
                PostId = post.PostId,
                Time = s.Time!.Value,
                Status = PostInterviewTimeStatus.可预约
            }));
        }

        if (isNewPost)
        {
            // // 职位发布时创建库存(开协同才有库存，预算操作)
            // if (post.LeftStock > 0)
            // {
            //     _context.Post_Stock.Add(new Post_Stock
            //     {
            //         PostId = post.PostId,
            //         StockType = StockType.创建职位入库,
            //         StockNum = post.LeftStock,
            //         LeftStock = post.LeftStock,
            //         CreatedTime = DateTime.Now
            //     });
            // }

            //职位扩展
            var postExt = new Post_Extend { PostId = post.PostId };
            _context.Add(postExt);
        }
        else
        {

            // 协同 => 不协同，清空库存
            // todo:如果点不协同的同时，改变了交付数量，往小了改，会保存不成功，后续逻辑会报错，只能往大了改，这里是个隐藏逻辑，用户会困惑
            // 不协同时，交付数量保持不变（事实证明往小了改不会报错）
            int shareChangeStock = 0;

            int diffNumber = model.DeliveryNumber - post.DeliveryNumber;
            if (diffNumber > 0)
            {
                post.LeftStock += diffNumber;
                shareChangeStock = post.LeftStock;
                // // 改交付数量时入库
                // _context.Post_Stock.Add(new Post_Stock
                // {
                //     PostId = post.PostId,
                //     StockType = StockType.改交付数量入库,
                //     StockNum = diffNumber,
                //     LeftStock = post.LeftStock,
                //     CreatedTime = DateTime.Now
                // });
            }
            else if (diffNumber < 0)
            {
                if (diffNumber + post.LeftStock >= 0)// 够减
                {
                    post.LeftStock += diffNumber;
                    shareChangeStock = post.LeftStock;
                }
                else// 不够减
                {
                    throw new BadRequestException("交付人数不能低于下限_" + post.LeftStock);
                }

                // // 改交付数量时出库
                // _context.Post_Stock.Add(new Post_Stock
                // {
                //     PostId = post.PostId,
                //     StockType = StockType.改交付数量出库,
                //     StockNum = diffNumber,
                //     LeftStock = post.LeftStock,
                //     CreatedTime = DateTime.Now
                // });
            }
        }

        // 新增逻辑，如果职位类别id是1041，则可以修改职位类别
        if ((post.Category == 1041 || post.Category == 0) && model.Category > 0)
            post.Category = model.Category;


        post.ProjectId = model.ProjectId;
        post.Name = model.Name;
        post.Describe = model.Describe;
        post.IsResumeExempt = model.IsResumeExempt ?? false;
        post.SalaryType = model.SalaryType;
        post.DeliveryNumber = model.DeliveryNumber;
        post.RegionId = Tools.TrimRegionId(model.RegionId)!;
        post.Address = model.Address;
        post.Location = new Point(model.Lat, model.Lng);
        post.Department = model.Department;
        post.Education = model.Education;
        post.Sex = model.Sex;
        post.MinAge = model.MinAge;
        post.MaxAge = model.MaxAge;
        post.MinSalary = model.MinSalary;
        post.MaxSalary = model.MaxSalary;
        post.Salary = model.Salary;
        post.Money = model.ProfitStage?.Sum(x => x.Amount);
        post.Tags = model.Tags ?? new List<string>();
        post.Highlights = model.Highlights ?? new List<string>();
        post.Welfare = model.Welfare ?? new List<WelfareModel>();
        post.WelfareCustom = model.WelfareCustom ?? new List<string>();
        post.UpdatedTime = DateTime.Now;
        post.InterviewStatus = model.InterviewStatus;
        post.SettlementType = model.SettlementType;
        post.WorkingDays = model.WorkingDays ?? new List<int>();
        post.WorkingHours = model.WorkingHours ?? new List<PostWorkingHours>();
        post.MinMonths = model.MinMonths;
        post.DaysPerWeek = model.DaysPerWeek;
        post.GraduationYear = model.GraduationYear;
        post.TieType = model.TieType;
        post.PaymentDays = model.ProfitStage?.Min(x => x.GuaranteeDays);
        post.ContractType = model.ContractType;
        post.AgentEntId = model.AgentEntId;
        //post.Source = model.Source;
        if (isNewPost)
        {
            post.Status = PostStatus.待审核;
            post.Source = model.Source;
        }
            

        // 补充企业信息
        var agentEnt = _context.Agent_Ent.FirstOrDefault(x => x.AgentEntId == model.AgentEntId);
        if (agentEnt != null)
        {
            agentEnt.Abbr = model.AgentEnt.Abbr;
            agentEnt.Display = model.AgentEnt.Display;
            agentEnt.DisplayName = model.AgentEnt.Display == EntDisplayType.匿名展示 ? model.AgentEnt.DisplayName : agentEnt.Name;
        }

        // 岗位调研表
        if (model.SurveyContent != null)
        {
            Project_Survey? survey = null;
            if (!string.IsNullOrWhiteSpace(model.PostId))
                survey = _context.Project_Survey.FirstOrDefault(x => x.PostId == model.PostId);

            if (survey == null)
            {
                survey = new Project_Survey
                {
                    ProjectId = model.ProjectId!,
                    PostId = post.PostId
                };
                _context.Add(survey);
            }
            survey.Content = model.SurveyContent;
        }

        _context.SaveChanges();

        result.PostId = post.PostId;

        // 创建职位后，根据情况更新职位状态，编辑职位只修改内容，不修改状态
        if (isNewPost && isReceiver && project.Status == ProjectStatus.已上线 && model.Source == PostSouce.诺快聘)
        {
            locker.Dispose();
            _commonProjectService.SetPostStatus(post.PostId, PostStatus.发布中, model.UserId);
        }

        return result;
    }

    public List<PostSyncResponse> HrSync(List<string> hrIds)
    {
        using var _context = _staffingContextFactory.CreateDbContext();

        var result = _context.User_Hr.Where(x => hrIds.Contains(x.UserId))
        .Select(s => new PostSyncResponse
        {
            Type = PostSyncType.顾问变更,
            Hr = new PostSyncHrInfo
            {
                HrId = s.UserId,
                HrName = s.NickName,
                Avatar = s.Avatar,
                Post = s.Post,
                LoginTime = s.User_Extend.LoginTime,
                Mobile = s.User.Mobile,
                NuoId = s.NuoId,
                CreatedTime = s.CreatedTime,
                Describe = s.Describe,
                Sex = s.Sex
            }
        }).ToList();

        return result;
    }
}