﻿namespace Config.CommonModel.Tianyancha;

/// <summary>
/// 天眼查通用字段
/// </summary>
public class TycEntResponse
{
    /// <summary>
    /// 天眼查企业Id
    /// </summary>
    public string? TycEntId { get; set; }

    /// <summary>
    /// 企业名字
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 曾用名
    /// </summary>
    public string? HistoryNames { get; set; }

    /// <summary>
    /// 企业状态
    /// </summary>
    public string? RegStatus { get; set; }

    /// <summary>
    /// 注册资本
    /// </summary>
    public string? RegCapital { get; set; }

    /// <summary>
    /// 市
    /// </summary>
    public string? City { get; set; }

    /// <summary>
    /// 人员规模
    /// </summary>
    public string? StaffNumRange { get; set; }

    /// <summary>
    /// 公司行业
    /// </summary>
    public string? Industry { get; set; }

    /// <summary>
    /// 股票号
    /// </summary>
    public string? BondNum { get; set; }

    /// <summary>
    /// 法人类型
    /// </summary>
    public int? Type { get; set; }

    /// <summary>
    /// 法人
    /// </summary>
    public string? LegalPersonName { get; set; }

    /// <summary>
    /// 注册号
    /// </summary>
    public string? RegNumber { get; set; }

    /// <summary>
    /// 英文名
    /// </summary>
    public string? Property3 { get; set; }

    /// <summary>
    /// 统一社会信用代码
    /// </summary>
    public string? CreditCode { get; set; }

    /// <summary>
    /// 经营开始时间
    /// </summary>
    public string? FromTime { get; set; }

    /// <summary>
    /// 经营截止时间
    /// </summary>
    public string? ToTime { get; set; }

    /// <summary>
    /// 参保人数
    /// </summary>
    public int? SocialStaffNum { get; set; }

    /// <summary>
    /// 简称
    /// </summary>
    public string? Alias { get; set; }

    /// <summary>
    /// 企业类型
    /// </summary>
    public string? CompanyOrgType { get; set; }

    /// <summary>
    /// 组织机构代码
    /// </summary>
    public string? OrgNumber { get; set; }

    /// <summary>
    /// 成立日期
    /// </summary>
    public string? EstiblishTime { get; set; }

    /// <summary>
    /// 登记机关
    /// </summary>
    public string? RegInstitute { get; set; }

    /// <summary>
    /// 纳税人识别号
    /// </summary>
    public string? TaxNumber { get; set; }

    /// <summary>
    /// 经营范围
    /// </summary>
    public string? BusinessScope { get; set; }

    /// <summary>
    /// 注册地址
    /// </summary>
    public string? RegLocation { get; set; }

    /// <summary>
    /// 国⺠经济行业分类
    /// </summary>
    public IndustryAll? IndustryAll { get; set; }

    /// <summary>
    /// 是否是小微企业 0不是 1是
    /// </summary>
    public bool? IsMicroEnt { get; set; }

    /// <summary>
    /// 省份简称
    /// </summary>
    public string? Base { get; set; }

    /// <summary>
    /// 企业标签
    /// </summary>
    public string? Tags { get; set; }
}

/// <summary>
/// 国⺠经济行业分类
/// </summary>
public class IndustryAll
{
    /// <summary>
    /// 国⺠经济行业分类中类
    /// </summary>
    public string? categoryMiddle { get; set; }

    /// <summary>
    /// 国⺠经济行业分类大类
    /// </summary>
    public string? categoryBig { get; set; }

    /// <summary>
    /// 国⺠经济行业分类⻔类
    /// </summary>
    public string? category { get; set; }

    /// <summary>
    /// 国⺠经济行业分类小类
    /// </summary>
    public string? categorySmall { get; set; }
}

public class TycRequestModel
{
    /// <summary>
    /// 公司名称
    /// </summary>
    public string CompanyName { get; set; } = default!;

    /// <summary>
    /// 是否限制查询次数 - 默认不限制
    /// </summary>
    public bool CheckTimes { get; set; } = false;
}
