﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 凭证明细表
/// </summary>
[Table("voucher_detail")]
public class Voucher_Detail
{
    /// <summary>
    /// 凭证明细ID
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();
    /// <summary>
    /// 凭证ID
    /// </summary>
    public string VoucherId { get; set; } = default!;
    /// <summary>
    /// 项目ID
    /// </summary>
    public string ProjectId { get; set; } = default!;
    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }
    /// <summary>
    /// 结算单Id
    /// </summary>
    public string? SettlementId { get; set; }
    /// <summary>
    /// 服务方式
    /// </summary>
    public string? ServiceType { get; set; }

    /// <summary>
    /// 确认成果
    /// </summary>
    public string? ConfirmResult { get; set; }

    /// <summary>
    /// 确认时间
    /// </summary>
    public DateTime? ConfirmTime { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    [ForeignKey(nameof(VoucherId))]
    public Voucher Voucher { get; set; } = default!;
}
