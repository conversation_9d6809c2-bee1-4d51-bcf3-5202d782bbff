-- ----------------------------
-- 平台人才库
-- ----------------------------
DROP TABLE IF EXISTS `talent_platform`;
CREATE TABLE `talent_platform` (
  `Id` varchar(18) NOT NULL,
  `HrId` varchar(18) NOT NULL COMMENT 'HrId',
  `SeekerId` varchar(18) NOT NULL COMMENT '求职者Id',
  `Source` int(3) NOT NULL COMMENT '来源',
  `ChannelId` varchar(18) NULL COMMENT '渠道Id',
  `Level` int(3) NOT NULL COMMENT '等级',
  `Status` int(3) NOT NULL COMMENT '状态',
  `Remark` varchar(1024) NOT NULL  COMMENT '备注',
  `Deleted` bit NOT NULL DEFAULT 0 COMMENT '删除',
  `SeekerVisitTime` datetime(6) NOT NULL DEFAULT NOW(6) COMMENT '用户访问时间',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_talent_platform_HrId_SeekerId` (`HrId`, `SeekerId`),
  KEY `IX_talent_platform_SeekerId` (`SeekerId`),
  KEY `IX_talent_platform_Deleted` (`Deleted`),
  KEY `IX_talent_platform_ChannelId` (`ChannelId` DESC),
  KEY `IX_talent_platform_Status` (`Status` DESC),
  KEY `IX_talent_platform_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 虚拟人才库
-- ----------------------------
DROP TABLE IF EXISTS `talent_virtual`;
CREATE TABLE `talent_virtual` (
  `Id` varchar(18) NOT NULL,
  `HrId` varchar(18) NOT NULL COMMENT 'HrId',
  `Channel` int(2) NOT NULL DEFAULT 0 COMMENT '简历渠道',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '简历状态',
  `SeekerId` varchar(18) NOT NULL COMMENT '求职者Id，建立关系后有值',
  `Name` varchar(50) NOT NULL COMMENT '姓名',
  `Mobile` varchar(20) NOT NULL COMMENT '手机',
  `HeadPortrait` varchar(100) NOT NULL COMMENT '头像',
  `Sex` int(2) NOT NULL DEFAULT 0 COMMENT '性别',
  `Education` int(2) NOT NULL DEFAULT 0 COMMENT '学历',
  `Mailbox` varchar(50) NOT NULL COMMENT '邮箱',
  `WeChat` varchar(50) NOT NULL COMMENT '微信',
  `QQ` varchar(50) NOT NULL COMMENT 'qq',
  `Birthday` date NOT NULL DEFAULT '1970-01-01' COMMENT '生日',
  `WorkTime` date NOT NULL DEFAULT '1970-01-01' COMMENT '开始工作日期',
  `Perfection` int(3) NOT NULL DEFAULT 0 COMMENT '简历完善度',
  `SelfEvaluation` varchar(1000) NOT NULL COMMENT '自我评价',
  `SkillAnalysis` varchar(5000) NOT NULL COMMENT '技能分析（小析补充信息字段）',
  `IndustryLabel` varchar(1000) NOT NULL COMMENT '行业标签（来源小析职业标签字段）',
  `PostLabel` varchar(1000) NOT NULL COMMENT '职位标签（来源小析职业标签字段）',
  `OtherLabel` varchar(1000) NOT NULL COMMENT '其他标签（来源小析职业标签字段）',
  `Highlights` varchar(1000) NOT NULL COMMENT '亮点',
  `Risks` varchar(1000) NOT NULL COMMENT '风险',
  `OriginalUrl` varchar(100) NOT NULL COMMENT '原始简历地址',
  `Location` varchar(100) NOT NULL COMMENT '所在地',
  `LocationNorm` varchar(100) NOT NULL COMMENT '所在地（标准地址）',
  `DetailedLocation` varchar(500) NOT NULL COMMENT '详细位置',
  `RegionId` varchar(15) NOT NULL COMMENT '地区Id',
  `Remark` varchar(500) NOT NULL  COMMENT '备注',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_virtual_HrId` (`HrId`),
  KEY `IX_talent_virtual_Mobile` (`Mobile`),
  KEY `IX_talent_virtual_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 虚拟人才库行业(字典)
-- ----------------------------
DROP TABLE IF EXISTS `dic_talent_industry`;
CREATE TABLE `dic_talent_industry` (
  `Id` varchar(18) NOT NULL,
  `IndustryName` varchar(50) NOT NULL COMMENT '行业名称',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_dic_talent_industry_IndustryName` (`IndustryName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 虚拟人才库职位(字典)
-- ----------------------------
DROP TABLE IF EXISTS `dic_talent_post`;
CREATE TABLE `dic_talent_post` (
  `Id` varchar(18) NOT NULL,
  `ParentId` varchar(18) NOT NULL COMMENT '父级id',
  `PostName` varchar(50) NOT NULL COMMENT '职位名称',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_dic_talent_post_PostName` (`PostName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 虚拟人才库教育经历
-- ----------------------------
DROP TABLE IF EXISTS `talent_virtual_edu`;
CREATE TABLE `talent_virtual_edu` (
  `Id` varchar(18) NOT NULL,
  `VirtualId` varchar(18) NOT NULL COMMENT '虚拟人才库id',
  `SchoolName` varchar(50) NOT NULL COMMENT '学校名称',
  `IsFullTime` bit NOT NULL DEFAULT 0 COMMENT '是否全日制',
  `Education` int(2) NOT NULL DEFAULT 0 COMMENT '学历',
  `MajorName` varchar(50) NOT NULL COMMENT '专业名称',
  `StartTime` date COMMENT '教育开始时间',
  `EndTime` date COMMENT '教育结束时间',
  `SchoolRemarks` text COMMENT '学校注释',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_virtual_edu_VirtualId` (`VirtualId`),
  KEY `IX_talent_virtual_edu_SchoolName` (`SchoolName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 虚拟人才库工作经历
-- ----------------------------
DROP TABLE IF EXISTS `talent_virtual_work`;
CREATE TABLE `talent_virtual_work` (
  `Id` varchar(18) NOT NULL,
  `VirtualId` varchar(18) NOT NULL COMMENT '虚拟人才库id',
  `CompanyName` varchar(50) NOT NULL COMMENT '公司名称',
  `Department` varchar(50) NOT NULL COMMENT '公司部门',
  `IndustryName` varchar(50) NOT NULL COMMENT '行业名称',
  `PostName` varchar(50) NOT NULL COMMENT '岗位名称',
  `MinSalary` decimal(18,2) NOT NULL DEFAULT 0 COMMENT '最低薪资',
  `MaxSalary` decimal(18,2) NOT NULL DEFAULT 0 COMMENT '最高薪资',
  `StartTime` date COMMENT '开始时间',
  `EndTime` date COMMENT '结束时间',
  `CompanyRemarks` text COMMENT '公司描述',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_virtual_work_VirtualId` (`VirtualId`),
  KEY `IX_talent_virtual_work_CompanyName` (`CompanyName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 虚拟人才库项目经历
-- ----------------------------
DROP TABLE IF EXISTS `talent_virtual_project`;
CREATE TABLE `talent_virtual_project` (
  `Id` varchar(18) NOT NULL,
  `VirtualId` varchar(18) NOT NULL COMMENT '虚拟人才库id',
  `ProjectName` varchar(50) NOT NULL COMMENT '项目名称',
  `PostName` varchar(50) NOT NULL COMMENT '岗位名称',
  `StartTime` date COMMENT '开始时间',
  `EndTime` date COMMENT '结束时间',
  `ProjectRemarks` text COMMENT '项目描述',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_virtual_project_VirtualId` (`VirtualId`),
  KEY `IX_talent_virtual_project_ProjectName` (`ProjectName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 虚拟人才库求职期望
-- ----------------------------
DROP TABLE IF EXISTS `talent_virtual_hope`;
CREATE TABLE `talent_virtual_hope` (
  `Id` varchar(18) NOT NULL,
  `VirtualId` varchar(18) NOT NULL COMMENT '虚拟人才库id',
  `HopeCity` varchar(50) NOT NULL COMMENT '期望城市',
  `RegionId` varchar(15) NOT NULL COMMENT '地区Id',
  `IndustryName` varchar(500) NOT NULL COMMENT '行业名称（多个）',
  `PostName` varchar(500) NOT NULL COMMENT '职位名称（多个）',
  `MinSalary` decimal(18,2) NOT NULL DEFAULT 0 COMMENT '最低薪资',
  `MaxSalary` decimal(18,2) NOT NULL DEFAULT 0 COMMENT '最高薪资',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_talent_virtual_hope_VirtualId` (`VirtualId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- 虚拟人才库简历评论
-- ----------------------------
DROP TABLE IF EXISTS `talent_virtual_comment`;
CREATE TABLE `talent_virtual_comment` (
  `Id` varchar(18) NOT NULL,
  `VirtualId` varchar(18) NOT NULL COMMENT '虚拟人才库id',
  `Content` varchar(5000) NOT NULL COMMENT '评论内容',
  `HrId` varchar(18) NOT NULL COMMENT 'HrId',
  `HrName` varchar(50) NOT NULL COMMENT 'Hr姓名',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_virtual_comment_VirtualId` (`VirtualId`),
  KEY `IX_talent_virtual_comment_HrId` (`HrId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- 平台人才库简历评论
-- ----------------------------
DROP TABLE IF EXISTS `talent_platform_comment`;
CREATE TABLE `talent_platform_comment` (
  `Id` varchar(18) NOT NULL,
  `PlatformId` varchar(18) NOT NULL COMMENT '虚拟人才库id',
  `Content` varchar(5000) NOT NULL COMMENT '评论内容',
  `HrId` varchar(18) NOT NULL COMMENT 'HrId',
  `HrName` varchar(50) NOT NULL COMMENT 'Hr姓名',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_platform_comment_VirtualId` (`PlatformId`),
  KEY `IX_talent_platform_comment_HrId` (`HrId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- 虚拟人才库导入记录
-- ----------------------------
DROP TABLE IF EXISTS `talent_upload_record`;
CREATE TABLE `talent_upload_record` (
  `Id` varchar(18) NOT NULL,
  `HrId` varchar(18) NOT NULL COMMENT 'HrId',
  `FileUrl` varchar(500) NOT NULL COMMENT '文件地址',
  `FileName` varchar(500) NOT NULL COMMENT '文件名称',
  `FileExtension` int(2) NOT NULL DEFAULT 0 COMMENT '文件扩展名',
  `RepeatType` int(2) NOT NULL DEFAULT 0 COMMENT '重复类型',
  `UpLoadStatus` int(2) NOT NULL DEFAULT 0 COMMENT '导入状态',
  `TotalNumber` int(2) NOT NULL DEFAULT 0 COMMENT '简历总数量',
  `FailNumber` int(2) NOT NULL DEFAULT 0 COMMENT '失败数量',
  `QualifiedNumber` int(2) NOT NULL DEFAULT 0 COMMENT '合格数量',
  `RepeatNumber` int(2) NOT NULL DEFAULT 0 COMMENT '重复数量',
  `WarehousingNumber` int(2) NOT NULL DEFAULT 0 COMMENT '入库数量',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_upload_record_HrId` (`HrId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- 虚拟人才库导入记录子表
-- ----------------------------
DROP TABLE IF EXISTS `talent_upload_recordsub`;
CREATE TABLE `talent_upload_recordsub` (
  `Id` varchar(18) NOT NULL,
  `RecordId` varchar(18) NOT NULL COMMENT '导入记录id',
  `VirtualId` varchar(18) NOT NULL COMMENT '虚拟人才库id',
  `IsWarehousing` bit NOT NULL DEFAULT 0 COMMENT '是否入库',
  `IsRepeat` bit NOT NULL DEFAULT 0 COMMENT '是否重复',
  `IsSuccess` bit NOT NULL DEFAULT 0 COMMENT '是否成功',
  `Remarks` varchar(1000) NOT NULL COMMENT '备注',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_upload_recordsub_RecordId` (`RecordId`),
  KEY `IX_talent_upload_recordsub_VirtualId` (`VirtualId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- 人才库标签字典表（真实虚拟通用表）
-- ----------------------------
DROP TABLE IF EXISTS `dic_talent_label`;
CREATE TABLE `dic_talent_label` (
  `Id` varchar(18) NOT NULL,
  `HrId` varchar(18) NOT NULL COMMENT 'HrId',
  `LabelName` varchar(20) NOT NULL COMMENT '标签名称',
  PRIMARY KEY (`Id`),
  KEY `IX_dic_talent_label_HrId` (`HrId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- 人才库对应标签表
-- ----------------------------
DROP TABLE IF EXISTS `talent_label`;
CREATE TABLE `talent_label` (
  `Id` varchar(18) NOT NULL,
  `VirtualId` varchar(18) NOT NULL COMMENT '虚拟人才库id',
  `PlatformId` varchar(18) NOT NULL COMMENT '真实人才库id',
  `DicLabelId` varchar(18) NOT NULL COMMENT '标签id',
  PRIMARY KEY (`Id`),
  KEY `IX_talent_label_DicLabelId` (`DicLabelId`),
  KEY `IX_talent_label_VirtualId` (`VirtualId`),
  KEY `IX_talent_label_PlatformId` (`PlatformId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 人才库（平台用户和虚拟用户关系）
-- ----------------------------
DROP TABLE IF EXISTS `talent_relation`;
CREATE TABLE `talent_relation` (
  `Id` varchar(18) NOT NULL,
  `HrId` varchar(18) NOT NULL COMMENT 'HrId',
  `SeekerId` varchar(18) NOT NULL COMMENT '求职者Id',
  `TalentId` varchar(18) NOT NULL COMMENT '虚拟人才Id',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_talent_relation_HrId_TalentId` (`HrId`, `TalentId`),
  KEY `IX_talent_relation_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

