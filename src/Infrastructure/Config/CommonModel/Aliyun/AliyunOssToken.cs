﻿namespace Config.CommonModel.Aliyun;

public class <PERSON>yunOssToken
{
    /// <summary>
    /// 令牌id
    /// </summary>
    public string AccessId { get; set; } = string.Empty;

    /// <summary>
    /// 上传的目录
    /// </summary>
    public string? Path { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public long Expire { get; set; }

    /// <summary>
    /// 阿里云OSS上传地址
    /// </summary>
    public string Host { get; set; } = string.Empty;

    /// <summary>
    /// 客户端直传的授权
    /// </summary>
    public string Policy { get; set; } = string.Empty;

    /// <summary>
    /// 客户端直传的签名
    /// </summary>
    public string Signature { get; set; } = string.Empty;
}