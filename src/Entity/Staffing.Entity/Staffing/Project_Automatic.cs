﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 项目自流转表
/// </summary>
[Table("project_automatic")]
public class Project_Automatic
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 项目id
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 招聘周期
    /// </summary>
    public bool RecruitmentCycle { get; set; }

    /// <summary>
    /// 招聘人数
    /// </summary>
    public bool RecruitmentNumber { get; set; }

    /// <summary>
    /// 面试官筛选结果
    /// </summary>
    public bool InterviewerOutCome { get; set; }

    /// <summary>
    /// 筛选归档类型
    /// </summary>
    public Config.Enums.RecruitFileAway ScreenFileStatus { get; set; }

    /// <summary>
    /// 筛选归档描述
    /// </summary>
    public string ScreenFileRemarks { get; set; } = string.Empty;

    /// <summary>
    /// offer状态
    /// </summary>
    public bool OfferStatus { get; set; }

    /// <summary>
    /// offer或入职
    /// </summary>
    public bool OfferOrInduction { get; set; }

    /// <summary>
    /// 离职操作
    /// </summary>
    public bool QuitJob { get; set; }

    /// <summary>
    /// 离职归档类型
    /// </summary>
    public Config.Enums.RecruitFileAway QuitFileStatus { get; set; }

    /// <summary>
    /// 离职归档描述
    /// </summary>
    public string QuitFileRemarks { get; set; } = string.Empty;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Project Project { get; set; } = default!;
}

