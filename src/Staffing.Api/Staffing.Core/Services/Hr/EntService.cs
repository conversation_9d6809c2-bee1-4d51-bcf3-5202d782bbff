﻿using Config.Enums;
using Config.CommonModel;
using EntityFrameworkCore.AutoHistory.Extensions;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Model.Hr.Enterprise;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces.Hr;
using Config;
using Infrastructure.Aop;
using Infrastructure.CommonService.Ndn;

namespace Staffing.Core.Services.Hr;

[Service(ServiceLifetime.Transient)]
public class EntService : IEntService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private ICommonService _commonService;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly Noah.Aliyun.Storage.IObjectStorage _objectStorageService;
    private readonly LogManager _log;
    private readonly INdnService _ndnService;
    private WeChatHelper _weChatHelper;
    public EntService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, ICommonService commonService, INdnService ndnService,
        LogManager log, WeChatHelper weChatHelper, Noah.Aliyun.Storage.IObjectStorage objectStorageService)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _commonService = commonService;
        _log = log;
        _ndnService = ndnService;
        _weChatHelper = weChatHelper;
        _objectStorageService = objectStorageService;
    }

    public GetEntOrgResponse GetEntOrg()
    {
        var result = new GetEntOrgResponse();

        var entIds = new List<string?>();
        entIds.Add(_user.EntId);
        if (_user.GroupType == EnterpriseGroupType.MainGroup)
        {
            var subEntIds = _context.Enterprise_Relation.Where(x => x.GroupEntId == _user.GroupEntId)
            .Select(s => s.EntId).ToList();

            if (subEntIds.Count > 0)
                entIds.AddRange(subEntIds);
        }

        entIds = entIds.Distinct().ToList();

        var ents = _context.Enterprise.Where(x => entIds.Contains(x.EntId))
        .Select(s => new
        {
            s.EntId,
            s.Name
        }).ToList();

        var orgs = _context.Enterprise_Org.Where(x => entIds.Contains(x.EntId))
        .Select(s => new GetEntOrgDetail
        {
            Id = s.OrgId,
            ParentId = s.ParentId ?? string.Empty,
            Name = s.Name,
            EntId = s.EntId,
            Sort = s.Sort,
            CurrentEnt = s.EntId == _user.EntId
        }).ToList();

        result.Rows = new List<GetEntOrgDetail>();
        List<GetEntOrgDetail> mainTree = result.Rows;

        //如果是集团公司
        if (_user.GroupType == EnterpriseGroupType.MainGroup)
        {
            var groupName = ents.Where(x => x.EntId == _user.EntId).Select(s => s.Name).First();

            var node = new GetEntOrgDetail
            {
                Children = new List<GetEntOrgDetail>(),
                EntId = _user.EntId,
                Id = "-1",
                Name = groupName,
                CurrentEnt = true
            };
            mainTree.Add(node);

            mainTree = node.Children;
        }

        foreach (var item in entIds)
        {
            var entName = ents.Where(x => x.EntId == item).Select(s => s.Name).First();
            var treeData = orgs.Where(x => x.EntId == item).OrderBy(o => o.Sort).ThenBy(o => o.Id).ToList();
            var treeNotes = Tools.GetChildrenTreeNode(treeData, x => x.Id, y => y.ParentId, string.Empty);
            var tree = Tools.ModelConvert<List<GetEntOrgDetail>>(treeNotes);

            var node = new GetEntOrgDetail
            {
                Children = tree,
                EntId = item,
                Id = "0",
                Name = entName,
                CurrentEnt = item == _user.EntId
            };
            mainTree.Add(node);
        }

        return result;
    }

    [RedisCache(Expire = 60)]
    public GetDdOrgResponse GetDdOrg(GetDdOrg model)
    {
        var result = new GetDdOrgResponse();

        var orgs = _context.Dd_Dept.Where(x => x.DdDeptId > 1)
        .OrderBy(o => o.Name)
        .Select(s => new GetDdOrgDetail
        {
            Id = s.DdDeptId,
            ParentId = s.ParentId,
            Name = s.Name,
            Sort = s.Sort
        }).ToList();

        result.Rows = new List<GetDdOrgDetail>();
        List<GetDdOrgDetail> mainTree = result.Rows;

        var treeNotes = Tools.GetChildrenTreeNode(orgs, x => x.Id, y => y.ParentId, 1);
        var tree = Tools.ModelConvert<List<GetDdOrgDetail>>(treeNotes);

        mainTree.AddRange(tree);

        // if (string.IsNullOrWhiteSpace(model.EntId))
        //     throw new BadRequestException("缺少企业Id");


        // var entInfo = _context.Enterprise.Where(x => x.EntId == model.EntId)
        // .Select(s => new
        // {
        //     s.GroupType,
        //     s.GroupEntId
        // }).FirstOrDefault();

        // if (entInfo == null)
        //     throw new BadRequestException("企业不存在");

        // var entIds = new List<string?>();
        // entIds.Add(model.EntId);
        // if (entInfo.GroupType == EnterpriseGroupType.MainGroup)
        // {
        //     var subEntIds = _context.Enterprise_Relation.Where(x => x.GroupEntId == entInfo.GroupEntId)
        //     .Select(s => s.EntId).ToList();

        //     if (subEntIds.Count > 0)
        //         entIds.AddRange(subEntIds);
        // }

        // entIds = entIds.Distinct().ToList();

        // var ents = _context.Enterprise.Where(x => entIds.Contains(x.EntId))
        // .Select(s => new
        // {
        //     s.EntId,
        //     s.Name
        // }).ToList();

        // var orgs = _context.Enterprise_Org.Where(x => entIds.Contains(x.EntId))
        // .Select(s => new GetEntOrgDetail
        // {
        //     Id = s.OrgId,
        //     ParentId = s.ParentId ?? string.Empty,
        //     Name = s.Name,
        //     EntId = s.EntId,
        //     Sort = s.Sort,
        //     CurrentEnt = s.EntId == model.EntId
        // }).ToList();

        // result.Rows = new List<GetEntOrgDetail>();
        // List<GetEntOrgDetail> mainTree = result.Rows;

        // //如果是集团公司
        // if (entInfo.GroupType == EnterpriseGroupType.MainGroup)
        // {
        //     var groupName = ents.Where(x => x.EntId == model.EntId).Select(s => s.Name).First();

        //     var node = new GetEntOrgDetail
        //     {
        //         Children = new List<GetEntOrgDetail>(),
        //         EntId = model.EntId,
        //         Id = "-1",
        //         Name = groupName,
        //         CurrentEnt = true
        //     };
        //     mainTree.Add(node);

        //     mainTree = node.Children;
        // }

        // foreach (var item in entIds)
        // {
        //     var entName = ents.Where(x => x.EntId == item).Select(s => s.Name).First();
        //     var treeData = orgs.Where(x => x.EntId == item).OrderBy(o => o.Sort).ThenBy(o => o.Id).ToList();
        //     var treeNotes = Tools.GetChildrenTreeNode(treeData, x => x.Id, y => y.ParentId, string.Empty);
        //     var tree = Tools.ModelConvert<List<GetEntOrgDetail>>(treeNotes);

        //     var node = new GetEntOrgDetail
        //     {
        //         Children = tree,
        //         EntId = item,
        //         Id = "0",
        //         Name = entName,
        //         CurrentEnt = item == model.EntId
        //     };
        //     mainTree.Add(node);
        // }

        // foreach (var item in entIds)
        // {
        //     var entName = ents.Where(x => x.EntId == item).Select(s => s.Name).First();
        //     var treeData = orgs.Where(x => x.EntId == item).OrderBy(o => o.Sort).ThenBy(o => o.Id).ToList();
        //     var treeNotes = Tools.GetChildrenTreeNode(treeData, x => x.Id, y => y.ParentId, string.Empty);
        //     var tree = Tools.ModelConvert<List<GetEntOrgDetail>>(treeNotes);

        //     var node = new GetEntOrgDetail
        //     {
        //         Children = tree,
        //         EntId = item,
        //         Id = "0",
        //         Name = entName,
        //         CurrentEnt = item == model.EntId
        //     };
        //     mainTree.Add(node);
        // }

        return result;
    }

    public GetEntResponse GetEnt(QuerySearch model)
    {
        var result = new GetEntResponse();
        var predicate = PredicateBuilder.New<Enterprise>(x => x.Status == EnterpriseStatus.Active);
        if (!string.IsNullOrWhiteSpace(model.Search))
            predicate.And(x => x.Name.Contains(model.Search));

        result.Rows = _context.Enterprise.Where(predicate)
        .Select(s => new GetEntDetail
        {
            EntId = s.EntId,
            EntName = s.Name
        }).ToList();

        return result;
    }

    public AddEntOrgResponse AddEntOrg(AddEntOrg model)
    {
        var result = new AddEntOrgResponse();
        if (string.IsNullOrWhiteSpace(model.Name))
            throw new BadRequestException("部门名称不能为空");

        model.ParentId = model.ParentId != "0" ? model.ParentId : string.Empty;

        var level = string.Empty;

        if (!string.IsNullOrWhiteSpace(model.ParentId))
        {
            var parent = _context.Enterprise_Org
            .Where(x => x.EntId == _user.EntId && x.OrgId == model.ParentId)
            .Select(s => new
            {
                s.OrgId,
                s.Level
            }).FirstOrDefault();

            if (parent == null)
                throw new BadRequestException("上级部门无效");

            // if (parent.Level.Count(c => c == '.') >= 2)
            //     throw new BadRequestException("部门最多3级");

            level = parent.Level;
        }

        var org = new Enterprise_Org
        {
            EntId = _user.EntId,
            Name = model.Name,
            ParentId = model.ParentId,
            Level = level,
            Creator = _user.Id
        };
        _context.Add(org);

        org.Level = $"{level}{org.OrgId}.";
        _context.SaveChanges();

        result.OrgId = org.OrgId;

        return result;
    }

    public EmptyResponse UpdateEntOrg(UpdateEntOrg model)
    {
        var org = _context.Enterprise_Org
        .FirstOrDefault(x => x.EntId == _user.EntId && x.OrgId == model.OrgId);

        if (org == null)
            throw new BadRequestException("部门不存在");

        //如果修改名称
        if (!string.IsNullOrWhiteSpace(model.Name))
            org.Name = model.Name;

        //如果移动部门排序
        if (model.Move.HasValue && model.Move != 0)
        {
            var pid = _context.Enterprise_Org.Where(x => x.EntId == _user.EntId && x.OrgId == model.OrgId)
            .Select(s => new { s.OrgId, s.ParentId }).FirstOrDefault();

            if (pid == null)
                throw new BadRequestException("部门不存在");

            var depts = _context.Enterprise_Org.Where(x => x.ParentId == pid.ParentId)
            .OrderBy(o => o.Sort).ThenBy(o => o.OrgId)
            .ToList();

            var i = 0;
            depts.ForEach(item =>
            {
                item.Sort = i++;
            });

            var nextI = model.Move > 0 ? -1 : 1;
            var dept = depts.First(x => x.OrgId == model.OrgId);
            var nextDept = depts.FirstOrDefault(x => x.Sort == (dept.Sort + nextI));

            if (nextDept != null)
            {
                dept.Sort += nextI;
                nextDept.Sort -= nextI;
            }
        }

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        return new EmptyResponse();
    }

    public EmptyResponse DeleteEntOrg(DeleteEntOrg model)
    {
        var org = _context.Enterprise_Org
        .FirstOrDefault(x => x.EntId == _user.EntId && x.OrgId == model.OrgId);

        if (org == null)
            throw new BadRequestException("部门不存在");

        if (_context.Enterprise_Org.Any(x => x.EntId == _user.EntId && x.ParentId == model.OrgId))
            throw new BadRequestException("请先删除子部门");

        if (_context.Enterprise_Org_User.Any(x => x.OrgId == model.OrgId))
            throw new BadRequestException("请先转移部门中的人员再进行删除");

        _context.Remove(org);

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        return new EmptyResponse();
    }

    public GetEntRolesResponse GetEntRoles(GetEntRoles model)
    {
        var result = new GetEntRolesResponse();

        var predicate = PredicateBuilder.New<Enterprise_Role>(x => x.EntId == _user.EntId || x.Type == RoleType.System);

        if (!string.IsNullOrWhiteSpace(model.Name))
            predicate = predicate.And(x => x.Name.Contains(model.Name));

        var sql = _context.Enterprise_Role.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
        .OrderBy(o => o.Type)
        .ThenBy(o => o.EntId)
        .ThenByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetEntRolesInfo
        {
            CreatedTime = s.CreatedTime,
            RoleId = s.RoleId,
            Creator = s.Creator_User.NickName,
            Describe = s.Describe,
            Name = s.Name,
            Powers = s.Powers,
            Type = s.Type,
            Scope = s.Scope,
            Icon = s.Icon
        }).ToList();

        var roleIds = result.Rows.Select(s => s.RoleId).ToList();

        var group = _context.User_Hr.Where(x => x.Status == UserStatus.Active && x.EntId == _user.EntId && roleIds.Contains(x.RoleId))
        .GroupBy(g => g.RoleId)
        .Select(s => new { RoleId = s.Key, Ct = s.Count() })
        .ToList();

        result.Rows.ForEach(item =>
        {
            // item.Powers = item.PowersStr?.Split(",").ToList();
            item.Numbers = group.FirstOrDefault(x => x.RoleId == item.RoleId)?.Ct ?? 0;
            item.TypeName = item.Type.GetDescription();
        });

        return result;
    }

    public AddEntRoleResponse AddEntRole(AddEntRole model)
    {
        var result = new AddEntRoleResponse();
        if (string.IsNullOrWhiteSpace(model.Name))
            throw new BadRequestException("角色名称不能为空");

        if (model.Powers == null || model.Powers.Count < 1)
            throw new BadRequestException("权限不能为空");

        // var power = string.Join(",", model.Powers);
        var org = new Enterprise_Role
        {
            EntId = _user.EntId,
            Name = model.Name,
            Describe = model.Describe ?? string.Empty,
            Creator = _user.Id,
            Type = RoleType.Custom,
            Powers = model.Powers
        };
        _context.Add(org);

        _context.SaveChanges();

        result.RoleId = org.RoleId;

        return result;
    }

    public EmptyResponse UpdateEntRole(UpdateEntRole model)
    {
        if (string.IsNullOrWhiteSpace(model.Name))
            throw new BadRequestException("角色名称不能为空");

        var role = _context.Enterprise_Role
        .FirstOrDefault(x => x.EntId == _user.EntId && x.RoleId == model.RoleId);

        if (role == null)
            throw new BadRequestException("角色不存在");

        if (model.Powers == null || model.Powers.Count < 1)
            throw new BadRequestException("权限不能为空");

        // var power = string.Join(",", model.Powers);
        role.Name = model.Name;
        role.Powers = model.Powers;

        if (model.Describe != null)
            role.Describe = model.Describe;

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        return new EmptyResponse();
    }

    public EmptyResponse DeleteEntRole(DeleteEntRole model)
    {
        var role = _context.Enterprise_Role
        .FirstOrDefault(x => x.EntId == _user.EntId && x.RoleId == model.RoleId);

        if (role == null)
            throw new BadRequestException("角色不存在");

        if (role.Type != RoleType.Custom)
            throw new BadRequestException("非自定义角色不允许删除");

        _context.Remove(role);

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        return new EmptyResponse();
    }

    public GetEntUsersResponse GetEntUsers(GetEntUsers model)
    {
        var result = new GetEntUsersResponse();

        IQueryable<User_Hr> sql = default!;

        long.TryParse(model.OrgId, out long orgId);

        if (orgId > 0)
        {
            var predicate = PredicateBuilder.New<Enterprise_Org_User>(x => x.User_Hr.Status == UserStatus.Active);

            if (_user.GroupType == EnterpriseGroupType.MainGroup)
                predicate = predicate.And(x => x.User_Hr.Enterprise.GroupEntId == _user.EntId);
            else
                predicate = predicate.And(x => x.User_Hr.EntId == _user.EntId);


            if (!string.IsNullOrWhiteSpace(model.Search))
                predicate = predicate.And(x => x.User_Hr.NickName.Contains(model.Search) || x.User_Hr.User.Mobile.Contains(model.Search));

            if (!string.IsNullOrWhiteSpace(model.OrgId))
                predicate = predicate.And(x => x.OrgId == model.OrgId);

            sql = _context.Enterprise_Org_User.Where(predicate).Select(s => s.User_Hr);
        }
        else
        {
            var predicate = PredicateBuilder.New<User_Hr>(x => x.Status == UserStatus.Active);

            //如果是集团公司管理员，查看所有用户
            if (orgId == -1)
            {
                if (_user.GroupType != EnterpriseGroupType.MainGroup)
                    throw new BadRequestException("无权限查看");

                predicate = predicate.And(x => x.Enterprise.GroupEntId == _user.EntId);
            }
            else
            {
                //如果不是自己公司
                if (_user.EntId != model.EntId)
                    if (_user.GroupType != EnterpriseGroupType.MainGroup)
                        throw new BadRequestException("无权限查看");

                //只能查看自己公司或集团公司
                predicate = predicate.And(x => x.EntId == model.EntId && (x.EntId == _user.EntId || x.Enterprise.GroupEntId == _user.EntId));
            }

            if (!string.IsNullOrWhiteSpace(model.Search))
                predicate = predicate.And(x => x.NickName.Contains(model.Search) || x.User.Mobile.Contains(model.Search));

            sql = _context.User_Hr.Where(predicate);
        }

        result.Total = sql.Select(s => new { a = 1 }).Count();

        //排序，没有部门的排在上边
        result.Rows = sql
        .OrderByDescending(o => o.Enterprise_Org_User.Any())
        .ThenByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetEntUsersInfo
        {
            CreatedTime = s.CreatedTime,
            RoleId = s.RoleId,
            Name = s.NickName,
            Avatar = s.Avatar,
            Post = s.Post,
            EMail = s.EMail,
            Mobile = s.User.Mobile,
            RoleName = s.Enterprise_Role.Name,
            Status = s.Status,
            StatusName = s.Status.GetDescription(),
            UserId = s.UserId,
            CurrentEnt = s.EntId == model.EntId
        }).ToList();

        var hrIds = result.Rows.Select(s => s.UserId).ToList();

        var orgs = _context.Enterprise_Org_User.Where(x => hrIds.Contains(x.UserId))
        .Select(s => new { s.UserId, s.Enterprise_Org.Name }).ToList();

        result.Rows.ForEach(item =>
        {
            item.OrgName = orgs.Where(x => x.UserId == item.UserId).Select(s => s.Name).ToList();
        });

        return result;
    }

    /// <summary>
    /// 获取顾问列表(平台级)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public GetDdUsersResponse GetDdUsers(GetDdUsers model)
    {
        var result = new GetDdUsersResponse();

        IQueryable<Dd_User> sql = default!;

        if (model.OrgId > 0)
        {
            var predicate = PredicateBuilder.New<Dd_User>(x => x.User.User_Hr.Status == UserStatus.Active);

            if (!string.IsNullOrWhiteSpace(model.Search))
                predicate = predicate.And(x => x.User.User_Hr.NickName.Contains(model.Search) || x.User.Mobile.Contains(model.Search));

            predicate = predicate.And(x => x.Dd_User_Dept.Any(c => c.DdDeptId == model.OrgId));

            sql = _context.Dd_User.Where(predicate);
        }
        else
        {
            var predicate = PredicateBuilder.New<Dd_User>(x => x.User.User_Hr.Status == UserStatus.Active);

            if (!string.IsNullOrWhiteSpace(model.Search))
                predicate = predicate.And(x => x.User.User_Hr.NickName.Contains(model.Search) || x.User.Mobile.Contains(model.Search));

            sql = _context.Dd_User.Where(predicate);
        }

        result.Total = sql.Select(s => new { a = 1 }).Count();

        //排序，没有部门的排在上边
        result.Rows = sql
        .OrderBy(x => x.Name)
        .ThenBy(o => o.DdUserId)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetDdUsersInfo
        {
            CreatedTime = s.CreatedTime,
            Name = s.Name,
            Avatar = s.User.User_Hr.Avatar,
            Mobile = s.User.Mobile,
            UserId = s.User.UserId
        }).ToList();

        return result;
    }

    public EmptyResponse UpdateHr(UpdateHr model)
    {
        if (model.HrIds == null)
            throw new BadRequestException("缺少HrId");

        var predicate = PredicateBuilder.New<User_Hr>(x => model.HrIds.Contains(x.UserId));

        if (_user.GroupType == EnterpriseGroupType.MainGroup)
            predicate = predicate.And(x => x.Enterprise.GroupEntId == _user.EntId);
        else
            predicate = predicate.And(x => x.EntId == _user.EntId);

        var hrs = _context.User_Hr
        .Where(predicate)
        .ToList();

        //根据实际权限过滤hrid
        model.HrIds = hrs.Select(s => s.UserId).ToList();

        //更新角色
        if (!string.IsNullOrWhiteSpace(model.RoleId))
        {
            if (!_context.Enterprise_Role.Any(x => (x.EntId == _user.EntId || x.Type == RoleType.System) && x.RoleId == model.RoleId))
                throw new BadRequestException("角色不存在");

            //如果更新后缺少管理员
            if (model.RoleId != Power.AdminRoleId)
                if (!_context.User_Hr.Any(x => x.EntId == _user.EntId && x.RoleId == Power.AdminRoleId && !model.HrIds.Contains(x.UserId)))
                    throw new BadRequestException("当前人员为负责人身份，负责人不能为空");

            hrs.ForEach(item =>
            {
                item.RoleId = model.RoleId;
            });
        }

        //更新组织架构
        if (model.OrgIds != null && model.OrgIds.Count > 0)
        {
            var predicate2 = PredicateBuilder.New<Enterprise_Org>(x => model.OrgIds.Contains(x.OrgId));

            if (_user.GroupType == EnterpriseGroupType.MainGroup)
                predicate2 = predicate2.And(x => x.Enterprise.GroupEntId == _user.EntId);
            else
                predicate2 = predicate2.And(x => x.EntId == _user.EntId);

            if (model.OrgIds.Count > 0)
                if (!_context.Enterprise_Org.Any(predicate2))
                    throw new BadRequestException("组织架构不存在");

            //查询出当前人员的组织架构
            var userOrgs = _context.Enterprise_Org_User.Where(x => model.HrIds.Contains(x.UserId))
            .Select(x => new
            {
                x.UserId,
                x.OrgId
            }).ToList();

            //删除原部门
            if (model.RemoveFromDept)
            {
                var rms = _context.Enterprise_Org_User.Where(x => model.HrIds.Contains(x.UserId));
                _context.RemoveRange(rms);
            }

            hrs.ForEach(item =>
            {
                model.OrgIds.ForEach(org =>
                {
                    //如果当前部门没有
                    if (model.RemoveFromDept || !userOrgs.Any(x => x.UserId == item.UserId && x.OrgId == org))
                        _context.Add(new Enterprise_Org_User
                        {
                            Creator = _user.Id,
                            OrgId = org,
                            UserId = item.UserId
                        });
                });
            });
        }

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        // //清除缓存
        // MyRedis.Client.Del(hrs.Select(s => $"usr:{(int)TokenType.企业端}_{s.NuoId}").ToArray());

        return new EmptyResponse();
    }

    // public EmptyResponse RemoveHrFromOrg(RemoveHrFromOrg model)
    // {
    //     var hr = _context.User_Hr.FirstOrDefault(x => x.EntId == _user.EntId && x.UserId == model.HrId);

    //     if (hr == null)
    //         throw new BadRequestException("用户不存在");

    //     if (!_context.Enterprise_Org.Any(x => x.EntId == _user.EntId && x.OrgId == model.OrgId))
    //         throw new BadRequestException("组织架构不存在");

    //     var orgUser = _context.Enterprise_Org_User.FirstOrDefault(x => x.OrgId == model.OrgId && x.UserId == model.HrId);

    //     if (orgUser != null)
    //         _context.Remove(orgUser);

    //     _context.EnsureAutoHistory();
    //     _context.SaveChanges();

    //     return new EmptyResponse();
    // }

    public EmptyResponse UpdateUserRole(UpdateUserRole model)
    {
        var result = new EmptyResponse();

        if (!_context.Enterprise_Role.Any(x => (x.EntId == _user.EntId || x.Type == RoleType.System) && x.RoleId == model.RoleId))
            throw new BadRequestException("角色不存在");

        model.HrIds = model.HrIds ?? new List<string>();

        //如果更新后缺少管理员
        if (model.RoleId == Power.AdminRoleId && !(model.HrIds?.Count > 0))
            throw new BadRequestException("负责人不能为空");

        if (model.RoleId != Power.AdminRoleId)
            if (!_context.User_Hr.Any(x => x.EntId == _user.EntId && x.RoleId == Power.AdminRoleId && !model.HrIds.Contains(x.UserId)))
                throw new BadRequestException("当前人员为负责人身份，负责人不能为空");

        using (var tran = _context.Database.BeginTransaction())
        {
            //移除其他人
            var hr1 = _context.User_Hr
            .Where(x => x.EntId == _user.EntId && x.RoleId == model.RoleId && !model.HrIds.Contains(x.UserId))
            .ToList();
            hr1.ForEach(item => item.RoleId = Power.EmployeeRoleId);

            //更新当前人
            var hr2 = _context.User_Hr
            .Where(x => x.EntId == _user.EntId && model.HrIds.Contains(x.UserId))
            .ToList();
            hr2.ForEach(item => item.RoleId = model.RoleId);

            _context.EnsureAutoHistory();
            _context.SaveChanges();
            tran.Commit();

            // //清除缓存
            // MyRedis.Client.Del(hr1.Union(hr2).Select(s => $"usr:{(int)TokenType.企业端}_{s.NuoId}").ToArray());
        }

        return new EmptyResponse();
    }

    public GetEntHrLessResponse GetEntHrLess(GetEntHrLess model)
    {
        var result = new GetEntHrLessResponse();

        var predicate = PredicateBuilder.New<User_Hr>(x => x.Status == UserStatus.Active);

        //如果查询集团人员
        if (model.ShowGroup && _user.GroupType == EnterpriseGroupType.MainGroup)
            predicate = predicate.And(x => x.Enterprise.GroupEntId == _user.EntId);
        else
            predicate = predicate.And(x => x.EntId == _user.EntId);

        if (!string.IsNullOrWhiteSpace(model.Search))
            predicate = predicate.And(x => x.NickName.Contains(model.Search) || x.User.Mobile.Contains(model.Search));

        result.Rows = _context.User_Hr.Where(predicate)
        .OrderBy(o => o.NickName)
        .ThenBy(o => o.UserId)
        .Select(s => new GetEntHrLessInfo
        {
            Avatar = s.Avatar,
            Mobile = s.User.Mobile,
            Name = s.NickName,
            RoleId = s.RoleId ?? string.Empty,
            UserId = s.UserId
        }).ToList();

        return result;
    }

    public GetEnterpriseMerchantsResponse GetEnterpriseMerchants(GetEnterpriseMerchants model)
    {
        var result = new GetEnterpriseMerchantsResponse();

        var predicate = PredicateBuilder.New<Enterprise_Merchant>(x => true);
        if (!string.IsNullOrWhiteSpace(model.MerchantName))
            predicate = predicate.And(x => x.MerchantName.Contains(model.MerchantName));

        if (model.UnionPayStatus.HasValue)
            predicate = predicate.And(x => _context.Ums_Complex_Upload.Any(u => u.MerchantId == x.Id && u.Status != StatusEnum.上传失败 && u.ApplyStatus == model.UnionPayStatus));

        var sql = _context.Enterprise_Merchant
        .Where(predicate);

        result.Total = sql.Count();

        result.Rows = GetEnterpriseMerchantResp(predicate, model.PageIndex, model.PageSize);

        return result;
    }

    /// <summary>
    /// 根据ID获取商户详情
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EnterpriseMerchantModel? GetEnterpriseMerchantDetail(GetEnterpriseMerchantDetail model)
    {
        var predicate = PredicateBuilder.New<Enterprise_Merchant>(x => x.Id == model.MerchantId);
        var result = GetEnterpriseMerchantResp(predicate, 1, 1).FirstOrDefault();

        return result;
    }

    private List<EnterpriseMerchantModel> GetEnterpriseMerchantResp(ExpressionStarter<Enterprise_Merchant> predicate, int PageIndex, int PageSize)
    {
        List<EnterpriseMerchantModel> Rows = _context.Enterprise_Merchant.Where(predicate)
        .OrderByDescending(x => x.Id)
        .Skip((PageIndex - 1) * PageSize)
        .Take(PageSize)
        .Select(s => new EnterpriseMerchantModel
        {
            Id = s.Id,
            PartyAContractNo = s.PartyAContractNo,
            PartyBContractNo = s.PartyBContractNo,
            MerchantName = s.MerchantName,
            CreatedTime = s.CreatedTime,
            ContractSeal = s.ContractSeal,
            CreatedName = s.CreatedUser.NickName,
            CertificateStatus = _context.Ums_Cert.Any(x => x.MerchantId == s.Id) ? 1 : 0,
            LegalSeal = s.LegalSeal,
            MerchantType = s.MerchantType,
            MerchantTypeText = s.MerchantType.GetDescription(),
            OfficeSeal = s.OfficeSeal,
            UpdatedName = s.UpdatedUser.NickName,
            UpdatedTime = s.UpdatedTime,
            InvoiceInfo = s.InvoiceInfo
        }).ToList();

        var ids = Rows.Select(s => s.Id).ToList();
        var unionPays = _context.Ums_Complex_Upload.Where(x => ids.Contains(x.MerchantId) && x.Status != StatusEnum.上传失败)
        .GroupBy(g => g.MerchantId)
        .Select(s => new
        {
            MerchantId = s.Key,
            ApplyStatus = s.Max(m => m.ApplyStatus),
            ApplyStatusMsg = s.Max(m => m.ApplyStatusMsg)
        }).ToList();

        Rows.ForEach(item =>
        {
            var unionPay = unionPays.FirstOrDefault(x => x.MerchantId == item.Id);
            item.UnionPayStatus = unionPay?.ApplyStatus;
            item.UnionPayStatusText = unionPay?.ApplyStatusMsg;
            item.CertificateStatusText = item.CertificateStatus == 1 ? "已上传" : "未上传";
        });
        return Rows;
    }

    public async Task<EmptyResponse> UpdateMerchants(UpdateEnterpriseMerchantModel model)
    {
        var merchant = _context.Enterprise_Merchant
        .FirstOrDefault(x => x.Id == model.Id);

        if (merchant == null)
            throw new BadRequestException("商户信息不存在");

        if (!string.IsNullOrWhiteSpace(model.ContractSeal))
        {
            var ossPath = await ParseBase64AndUploadToOssAsync(model.ContractSeal);
            if (!string.IsNullOrEmpty(ossPath))
                merchant.ContractSeal = ossPath;
        }

        if (!string.IsNullOrWhiteSpace(model.LegalSeal))
        {
            var ossPath = await ParseBase64AndUploadToOssAsync(model.LegalSeal);
            if (!string.IsNullOrEmpty(ossPath))
                merchant.LegalSeal = ossPath;
        }

        if (!string.IsNullOrWhiteSpace(model.OfficeSeal))
        {
            var ossPath = await ParseBase64AndUploadToOssAsync(model.OfficeSeal);
            if (!string.IsNullOrEmpty(ossPath))
                merchant.OfficeSeal = ossPath;
        }

        var outMerchant = _context.Enterprise_Merchant
        .Where(x => x.MerchantType == MerchantType.平台)
        .Select(s => new
        {
            s.Id,
            s.MerchantName
        }).FirstOrDefault();

        if (outMerchant == null)
            throw new BadRequestException("平台商户信息不存在");

        if (!string.IsNullOrWhiteSpace(model.PartyAContractNo))
        {
            var noahContract = _ndnService.GetNoahContractByCode(model.PartyAContractNo);
            if (noahContract == null)
                throw new BadRequestException("合同编号不存在");

            // 比较项目的甲乙双方名称和合同的甲乙双方名称是否一致
            if (!string.Equals(merchant.MerchantName?.Trim(), noahContract.Initiator?.Trim()))
                throw new BadRequestException("甲方名称不一致");

            if (!string.Equals(outMerchant.MerchantName?.Trim(), noahContract.Participant?.Trim()))
                throw new BadRequestException("乙方名称不一致");

            merchant.PartyAContractNo = model.PartyAContractNo;
        }

        if (!string.IsNullOrWhiteSpace(model.PartyBContractNo))
        {
            var noahContract = _ndnService.GetNoahContractByCode(model.PartyBContractNo);
            if (noahContract == null)
                throw new BadRequestException("合同编号不存在");

            // 比较项目的甲乙双方名称和合同的甲乙双方名称是否一致
            if (!string.Equals(outMerchant.MerchantName?.Trim(), noahContract.Initiator?.Trim()))
                throw new BadRequestException("甲方名称不一致");

            if (!string.Equals(merchant.MerchantName?.Trim(), noahContract.Participant?.Trim()))
                throw new BadRequestException("乙方名称不一致");

            merchant.PartyBContractNo = model.PartyBContractNo;
        }

        if (model.InvoiceInfo != null)
        {
            if (string.IsNullOrWhiteSpace(model.InvoiceInfo.TaxNumber))
                throw new BadRequestException("税号不能为空");
            if (string.IsNullOrWhiteSpace(model.InvoiceInfo.Bank))
                throw new BadRequestException("开户银行不能为空");
            if (string.IsNullOrWhiteSpace(model.InvoiceInfo.BankAccount))
                throw new BadRequestException("银行账号不能为空");
            if (string.IsNullOrWhiteSpace(model.InvoiceInfo.Address))
                throw new BadRequestException("注册地址不能为空");
            if (string.IsNullOrWhiteSpace(model.InvoiceInfo.Phone))
                throw new BadRequestException("开票电话不能为空");
            if (!model.InvoiceInfo.InvoiceType.HasValue)
                throw new BadRequestException("发票类型不能为空");
            if (string.IsNullOrWhiteSpace(model.InvoiceInfo.InvoiceOperatorName))
                throw new BadRequestException("开票人姓名不能为空");
            if (string.IsNullOrWhiteSpace(model.InvoiceInfo.InvoiceOperatorNo))
                throw new BadRequestException("开票人工号不能为空");
            if (string.IsNullOrWhiteSpace(model.InvoiceInfo.ContractOperatorName))
                throw new BadRequestException("合同经办人姓名不能为空");
            if (string.IsNullOrWhiteSpace(model.InvoiceInfo.ContractOperatorNo))
                throw new BadRequestException("合同经办人工号不能为空");

            merchant.InvoiceInfo = model.InvoiceInfo;
            merchant.InvoiceInfo.InvoiceTitle = merchant.MerchantName;
        }
        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 解析base64并上传oss
    /// </summary>
    private async Task<string?> ParseBase64AndUploadToOssAsync(string base64)
    {
        if (string.IsNullOrWhiteSpace(base64))
            return null;

        if (!base64.StartsWith("data:image/"))
            return null;

        try
        {
            // base64解析文件后缀
            var fileType = base64.Split(';')[0].Split(':')[1].Split('/')[1];
            // 转换byte[]并上传oss
            byte[] fileBytes = Convert.FromBase64String(base64.Split(',')[1]);
            var ossResult = await _objectStorageService.OrdinaryUploadFile(
                fileBytes,
                $"{_config.Aliyun!.Oss!.Dir!}/seals/{Guid.NewGuid()}.{fileType}");
            if (ossResult == null)
                throw new BadRequestException("上传印章失败");
            return ossResult;
        }
        catch
        {
            throw new BadRequestException("上传印章失败，请检查印章格式是否正确");
        }
    }
}