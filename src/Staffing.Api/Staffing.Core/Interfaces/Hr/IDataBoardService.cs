﻿using Staffing.Model.Hr.Dashboard;
using Staffing.Model.Hr.Project;

namespace Staffing.Core.Interfaces.Hr;

public interface IDataBoardService
{
    Task<object> GetPostBoard(GetProjectBoard model);
    Task<object> GetTeamBoardAnalysis(GetProjectBoard model);
    Task<object> GetTeamBoardStat(GetProjectBoard model);
    Task<object> GetInterviewerStat(GetProjectBoard model);
    Task<object> GetRecruitStat(GetProjectBoard model);
    object GetChannelSeeker(GetChannelSeeker model);
    SummaryDataResponse GetSummaryData();
}