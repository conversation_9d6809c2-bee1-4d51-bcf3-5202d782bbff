using Config;
using Config.CommonModel;
using Config.CommonModel.Cache;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Infrastructure.TianyanchaTool.Tianyancha;
using Config.CommonModel.Tianyancha;

namespace Infrastructure.CommonService;

[Service(ServiceLifetime.Singleton)]
public class CommonCacheService
{
    private IDbContextFactory<StaffingContext> _staffingContextFactory;
    private ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CacheHelper _cacheHelper;
    private readonly RequestContext _user;

    public CommonCacheService(IDbContextFactory<StaffingContext> staffingContextFactory,
        IOptionsSnapshot<ConfigManager> config,
        IHostEnvironment hostingEnvironment, LogManager log,
        CacheHelper cacheHelper,
        RequestContext user)
    {
        _staffingContextFactory = staffingContextFactory;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
        _cacheHelper = cacheHelper;
        _user = user;
    }

    // /// <summary>
    // /// hr人才库数量
    // /// </summary>
    // /// <returns></returns>
    // public async Task<HrTalentCount> GetHrTalentCount(string hrId)
    // {
    //     var cacheKey = $"st:ca:hrtc_{hrId}";
    //     var disable = 60;
    //     var result = _cacheHelper.GetRedisCache<HrTalentCount>(() =>
    //     {
    //         using (var _context = _staffingContextFactory.CreateDbContext())
    //         {
    //             var re = new HrTalentCount();

    //             re.Talent = _context.Talent_Platform.Count(x => x.HrId == hrId);

    //             return re;
    //         }
    //     }, cacheKey, disable);
    //     return result;
    // // }

    /// <summary>
    /// 平台端各种数量
    /// </summary>
    /// <returns></returns>
    public PlatformCount GetPlatformCount()
    {
        var platformCount = new PlatformCount();

        var platformCountCache = MyRedis.Client.HGetAll<int>(RedisKey.PlatformCount.Key);

        if (platformCountCache == null)
            return platformCount;

        if (platformCountCache.TryGetValue(RedisKey.PlatformCount.Talent, out var talent))
            platformCount.Talent = talent;

        if (platformCountCache.TryGetValue(RedisKey.PlatformCount.VirtualTalent, out var virtualTalent))
            platformCount.VirtualTalent = virtualTalent;

        if (platformCountCache.TryGetValue(RedisKey.PlatformCount.Adviser, out var adviser))
            platformCount.Adviser = adviser;


        return platformCount;
    }

    /// <summary>
    /// 求职者投递行为数据
    /// </summary>
    /// <param name="seekerId"></param>
    public SeekerDeliveryNum GetSeekerDeliveryBehavior(string seekerId)
    {
        var cacheKey = $"{RedisKey.SeekerBehavior.Key}{seekerId}";

        var seekerBehavior = MyRedis.Client.HGet<SeekerDeliveryBehavior>(cacheKey, RedisKey.SeekerBehavior.Delivery);
        if (seekerBehavior == null)
            seekerBehavior = new SeekerDeliveryBehavior();

        var result = new SeekerDeliveryNum
        {
            ContractNum = seekerBehavior.ContractNum,
            InductionNum = seekerBehavior.InductionNum + seekerBehavior.OfferNum,
            InterviewNum = seekerBehavior.InterviewNum,
            RegistrationNum = seekerBehavior.HrScreeningNum + seekerBehavior.InterviewerScreeningNum
        };

        return result;
    }

    /// <summary>
    /// 职位行为数据
    /// </summary>
    /// <param name="seekerId"></param>
    public PostBehavior GetPostData(string postId)
    {
        var cacheKey = $"{RedisKey.PostData.Key}{postId}";
        var postBehavior = new PostBehavior();
        var data = MyRedis.Client.HGetAll<int>(cacheKey);

        if (data == null)
            return postBehavior;

        if (data.TryGetValue(RedisKey.PostData.Visit, out var visit))
            postBehavior.VisitorNum = visit;

        if (data.TryGetValue(((int)RecruitStatus.Contract).ToString(), out var contract))
            postBehavior.ContractNum = contract;

        if (data.TryGetValue(((int)RecruitStatus.Induction).ToString(), out var induction))
            postBehavior.InductionNum = induction;

        if (data.TryGetValue(((int)RecruitStatus.Interview).ToString(), out var interview))
            postBehavior.InterviewNum = interview;

        if (data.TryGetValue(((int)RecruitStatus.HrScreening).ToString(), out var registration))
            postBehavior.RegistrationNum = registration;

        return postBehavior;
    }

    /// <summary>
    /// 协同职位行为数据
    /// </summary>
    /// <param name="seekerId"></param>
    public PostBehavior GetTeamPostData(string teamPostId)
    {
        var cacheKey = $"{RedisKey.PostData.TeamKey}{teamPostId}";

        var postBehavior = new PostBehavior();
        var data = MyRedis.Client.HGetAll<int>(cacheKey);

        if (data == null)
            return postBehavior;

        if (data.TryGetValue(RedisKey.PostData.Visit, out var visit))
            postBehavior.VisitorNum = visit;

        if (data.TryGetValue(((int)RecruitStatus.Contract).ToString(), out var contract))
            postBehavior.ContractNum = contract;

        if (data.TryGetValue(((int)RecruitStatus.Induction).ToString(), out var induction))
            postBehavior.InductionNum = induction;

        if (data.TryGetValue(((int)RecruitStatus.Interview).ToString(), out var interview))
            postBehavior.InterviewNum = interview;

        if (data.TryGetValue(((int)RecruitStatus.HrScreening).ToString(), out var registration))
            postBehavior.RegistrationNum = registration;

        return postBehavior;
    }

    /// <summary>
    /// 判断是否在企业白名单
    /// </summary>
    /// <param name="nuoId"></param>
    /// <returns></returns>
    public bool IsEntWhiteList(string nuoId)
    {
        var cacheKey = RedisKey.EntWhiteList;
        var disable = -1;
        var obj = _cacheHelper.GetRedisCache<List<string>>(() =>
        {
            using (var _context = _staffingContextFactory.CreateDbContext())
            {
                return _context.Enterprise_WhiteList.Select(s => s.NuoId).ToList();
            }
        }, cacheKey, disable);
        return obj.Contains(nuoId);
    }

    /// <summary>
    /// 得到随机人才头像
    /// </summary>
    /// <param name="hrId"></param>
    /// <returns></returns>
    public List<string> GetRandomTalentAvatars(string hrId)
    {
        var hrCacheKey = $"{RedisKey.HrBehavior.Key}{hrId}";
        var result = MyRedis.Client.HGet<List<string>>(hrCacheKey, RedisKey.HrBehavior.TalentAvatars);
        result = result?.Select(s => string.IsNullOrEmpty(s) ? Constants.DefaultAvatar : s).ToList();
        return result ?? new List<string>();
        // var cacheKey = $"{RedisKey.TalentRandomAvatars}{hrId}";
        // var disable = 300;
        // var result = _cacheHelper.GetRedisCache<List<string>>(() =>
        // {
        //     using (var _context = _staffingContextFactory.CreateDbContext())
        //     {
        //         return _context.Talent_Platform.Where(x => x.HrId == hrId && !string.IsNullOrEmpty(x.User_Seeker.Avatar))
        //         .OrderBy(o => EF.Functions.Random())
        //         .Take(3)
        //         .Select(s => s.User_Seeker.Avatar!).ToList();
        //     }
        // }, cacheKey, disable) ?? new List<string>();
        // return result;
    }

    /// <summary>
    /// 得到随机职位报名的头像
    /// </summary>
    /// <param name="hrId"></param>
    /// <returns></returns>
    public List<string> GetRandomPostAvatars(string postId)
    {
        var postCacheKey = $"{RedisKey.PostData.Key}{postId}";
        var result = MyRedis.Client.HGet<List<string>>(postCacheKey, RedisKey.PostData.DeliveryAvatars);
        result = result?.Select(s => string.IsNullOrEmpty(s) ? Constants.DefaultAvatar : s).ToList();
        return result ?? new List<string>();
        // var cacheKey = $"{RedisKey.PostRandomAvatars}{postId}";
        // var disable = 300;
        // var result = _cacheHelper.GetRedisCache<List<string>>(() =>
        // {
        //     using (var _context = _staffingContextFactory.CreateDbContext())
        //     {
        //         return _context.Post_Delivery.Where(x => x.PostId == postId)
        //         .GroupBy(g => g.SeekerId)
        //         .Where(x => !string.IsNullOrEmpty(x.Max(m => m.User_Seeker.Avatar)))
        //         .OrderBy(o => EF.Functions.Random())
        //         .Take(15)
        //         .Select(s => s.Max(m => m.User_Seeker.Avatar)).ToList()!;
        //     }
        // }, cacheKey, disable) ?? new List<string>();
        // return result;//.OrderBy(o => Guid.NewGuid()).ToList();
    }

    /// <summary>
    /// 根据名称获取天眼查企业信息 - 诺快聘内部调用
    /// </summary>
    /// <param name="name">企业名称</param>
    /// <param name="checkTimes">是否校验天眼查查询次数</param>
    /// <returns></returns>
    public TycUpdateAgentEntResponse GetAgentEntFromTyc(string name, bool checkTimes = true)
    {
        name = (name ?? string.Empty).Trim()
               .Replace("(", "（").Replace(")", "）")
               .Replace("[", "【").Replace("]", "】")
               .Replace("<", "《").Replace(">", "》");
        using var _context = _staffingContextFactory.CreateDbContext();

        // 优化检索
        var date = new DateTime(2025, 6, 1); // 因为新增了字段，所以需要重新获取数据
        var TycInfo = _context.Tyc_Enterprise.FirstOrDefault(f => f.Name == name && f.UpdatedTime > date);
        var ent = new TycUpdateAgentEntResponse();
        if (TycInfo == null)
        {
            try
            {
                if (checkTimes)
                {
                    TimesCount();
                }
                TycInfo = new Tianyancha(_context).GetCompanyBaseInfo(name);
            }
            catch (Exception ex)
            {
                _log.Error("调用天眼查接口错误", ex.Message);
            }
        }

        if (TycInfo != null)
        {
            Agent_Ent? agentEnt = null;
            //判断agent_ent是否存在，不存在则保存
            agentEnt = _context.Agent_Ent.FirstOrDefault(s => s.Name.Contains(TycInfo.Name!));
            if (agentEnt == null)
            {
                agentEnt = new Agent_Ent()
                {
                    // 不需要的代码
                    // Name = TycInfo.Name!,
                    // Abbr = TycInfo.Alias ?? string.Empty,
                    // Nature = (EnterpriseNature)GetNature(_context, TycInfo.CompanyOrgType)!,
                    // RegionId = GetRegionId(_context, TycInfo.City),
                    // Describe = TycInfo.BusinessScope,
                    // Address = TycInfo.RegLocation,
                };
                _context.Add(agentEnt);
            }
            agentEnt.Name = TycInfo.Name!;
            if (string.IsNullOrWhiteSpace(agentEnt.Abbr))
                agentEnt.Abbr = TycInfo.Alias ?? string.Empty;
            if (string.IsNullOrWhiteSpace(agentEnt.DisplayName))
                agentEnt.DisplayName = TycInfo.Name ?? string.Empty;
            agentEnt.Nature = (EnterpriseNature)GetNature(_context, TycInfo.CompanyOrgType)!;
            agentEnt.RegionId = GetRegionId(_context, TycInfo.City);
            agentEnt.Describe = TycInfo.BusinessScope;
            agentEnt.Address = TycInfo.RegLocation;
            _context.SaveChanges();
            ent = new TycUpdateAgentEntResponse
            {
                AgentEntId = agentEnt!.AgentEntId,
                TycEntId = TycInfo.Id,
                Name = name,
                Abbr = agentEnt.Abbr ?? string.Empty,
                Display = agentEnt.Display,
                DisplayName = agentEnt.DisplayName,
                Nature = GetNature(_context, TycInfo.CompanyOrgType),
                RegionId = GetRegionId(_context, TycInfo.City),
                TycIndustry = TycInfo.Industry,
                Industry = GetIndustryId(TycInfo.IndustryAll!),
                Scale = GetSacle(TycInfo.StaffNumRange),
                Capital = GetCapital(TycInfo),
                Describe = TycInfo.BusinessScope,
                CreditCode = TycInfo.CreditCode,
                Address = TycInfo.RegLocation,
                RegCapital = TycInfo.RegCapital,
                LegalPersonName = TycInfo.LegalPersonName,
                EstiblishTime = TycInfo.EstiblishTime != null ? DateTimeOffset.FromUnixTimeMilliseconds(long.Parse(TycInfo.EstiblishTime)).ToLocalTime().ToString("yyyy-MM-dd") : null,
                RegStatus = TycInfo.RegStatus,
                FromTime = TycInfo.FromTime,
                ToTime = TycInfo.ToTime,
                Tags = TycInfo.Tags != null ? TycInfo.Tags.Split(";").ToList() : null
            };
        }

        return ent;
    }


    /// <summary>
    /// 根据名称获取天眼查企业信息
    /// </summary>
    /// <param name="name">企业名称</param>
    /// <param name="checkTimes">是否校验天眼查查询次数</param>
    /// <returns></returns>
    public TycEntResponse GetEntFromTyc(string name, bool checkTimes = true)
    {
        name = (name ?? string.Empty).Trim()
               .Replace("(", "（").Replace(")", "）")
               .Replace("[", "【").Replace("]", "】")
               .Replace("<", "《").Replace(">", "》");
        using var _context = _staffingContextFactory.CreateDbContext();

        // 优化检索
        var TycInfo = _context.Tyc_Enterprise.FirstOrDefault(f => f.Name == name);
        var ent = new TycEntResponse();
        if (TycInfo == null)
        {
            try
            {
                if (checkTimes)
                {
                    TimesCount();
                }
                TycInfo = new Tianyancha(_context).GetCompanyBaseInfo(name);
            }
            catch (Exception ex)
            {
                _log.Error("调用天眼查接口错误", ex.Message);
            }
        }

        if (TycInfo != null)
        {
            ent = new TycEntResponse
            {
                TycEntId = TycInfo.Id,
                Name = TycInfo.Name,
                HistoryNames = TycInfo.HistoryNames,
                RegStatus = TycInfo.RegStatus,
                RegCapital = TycInfo.RegCapital,
                City = TycInfo.City,
                StaffNumRange = TycInfo.StaffNumRange,
                Industry = TycInfo.Industry,
                BondNum = TycInfo.BondNum,
                Type = TycInfo.Type,
                LegalPersonName = TycInfo.LegalPersonName,
                RegNumber = TycInfo.RegNumber,
                Property3 = TycInfo.Property3,
                CreditCode = TycInfo.CreditCode,
                FromTime = TycInfo.FromTime,
                ToTime = TycInfo.ToTime,
                SocialStaffNum = TycInfo.SocialStaffNum,
                Alias = TycInfo.Alias,
                CompanyOrgType = TycInfo.CompanyOrgType,
                OrgNumber = TycInfo.OrgNumber,
                EstiblishTime = TycInfo.EstiblishTime != null ? DateTimeOffset.FromUnixTimeMilliseconds(long.Parse(TycInfo.EstiblishTime)).ToLocalTime().ToString("yyyy-MM-dd") : null,
                RegInstitute = TycInfo.RegInstitute,
                TaxNumber = TycInfo.TaxNumber,
                BusinessScope = TycInfo.BusinessScope,
                RegLocation = TycInfo.RegLocation,
                IndustryAll = TycInfo.IndustryAll,
                IsMicroEnt = TycInfo.IsMicroEnt,
                Base = TycInfo.Base,
                Tags = TycInfo.Tags
            };
        }

        return ent;
    }

    /// <summary>
    /// 天眼查接口调用计数
    /// </summary>
    /// <exception cref="BadRequestException"></exception>
    private void TimesCount()
    {
        string defaultIp = "1::1::1::1";
        string dayStr = DateTime.Now.ToString("yyyy-MM-dd");
        string ip = _user.RequestIpAddress ?? defaultIp;

        // 没有公网ip说明走的是服务器内网
        if (ip.Equals(defaultIp))
            return;

        string rdsKey_5000 = $"tyc:total:{dayStr}";
        string rdsKey_50 = $"tyc:ip:{ip}:{dayStr}";
        string item_name = "times";
        // action 调用总数每日限制 - 5000次
        if (Convert.ToInt32(MyRedis.Client.HGet(rdsKey_5000, item_name)) >= 5000)
        {
            throw new BadRequestException("天眼查接口调用,超出每日5000次");
        }
        var times5000 = MyRedis.Client.HIncrBy(rdsKey_5000, item_name, 1);
        if (times5000 == 1)
            MyRedis.Client.Expire(rdsKey_5000, TimeSpan.FromDays(1));

        // action 单个ip每日限制次数 - 100次
        if (Convert.ToInt32(MyRedis.Client.HGet(rdsKey_50, item_name)) >= 100)
        {
            throw new BadRequestException($"天眼查接口调用,单个ip:{ip} 超出每日100次");
        }
        var times50 = MyRedis.Client.HIncrBy(rdsKey_50, item_name, 1);
        if (times50 == 1)
            MyRedis.Client.Expire(rdsKey_50, TimeSpan.FromDays(1));
    }

    #region 天眼查信息映射诺快聘企业信息

    /// <summary>
    /// 融资阶段映射
    /// </summary>
    /// <param name="tycInfo"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    private EnterpriseCapital? GetCapital(Tyc_Enterprise tycInfo)
    {
        return null;
    }

    /// <summary>
    /// 企业规模映射
    /// </summary>
    /// <param name="staffNumRange"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    private EnterpriseScale? GetSacle(string? staffNumRange)
    {
        return null;
    }

    /// <summary>
    /// 公司行业映射
    /// </summary>
    /// <param name="industryAll"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    private string? GetIndustryId(IndustryAll? industryAll)
    {
        return null;
    }

    /// <summary>
    /// 企业性质映射
    /// </summary>
    /// <param name="companyOrgType"></param>
    /// <returns></returns>
    private EnterpriseNature? GetNature(StaffingContext _context, string? companyOrgType)
    {
        var result = _context.Dic_Base_Info.FirstOrDefault(f => f.Code == companyOrgType);
        if (result == null)
            return EnterpriseNature.私营;
        int value = Convert.ToInt32(result.Value);
        return (EnterpriseNature)value;
    }

    /// <summary>
    /// 根据名称获取regionid
    /// </summary>
    /// <param name="_context"></param>
    /// <param name="city"></param>
    /// <returns></returns>
    private string? GetRegionId(StaffingContext _context, string? city)
    {
        var result = _context.Dic_Region.FirstOrDefault(f => f.Name == city && !string.IsNullOrWhiteSpace(f.ParentId));
        if (result == null)
            return null;
        return result.Id;
    }

    #endregion

}