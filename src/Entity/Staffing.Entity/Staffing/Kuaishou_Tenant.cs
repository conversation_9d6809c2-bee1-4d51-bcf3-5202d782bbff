﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 快手租户表
/// </summary>
[Table("kuaishou_tenant")]
public class Ku<PERSON>hou_Tenant
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 租户openid
    /// </summary>
    public string TenantOpenId { get; set; } = default!;

    /// <summary>
    /// 租户名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 创建人
    /// </summary>
    public string Creator { get; set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}
