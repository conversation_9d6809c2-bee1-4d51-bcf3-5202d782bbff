﻿using Config.Enums;

namespace Staffing.Model.Hr.Project;

public class SetPostStatus
{
    /// <summary>
    /// 职位Id
    /// </summary>
    public string? PostId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public PostStatus? Status { get; set; }
}

public class TopPost
{
    /// <summary>
    /// 职位Id
    /// </summary>
    public string? PostId { get; set; }

    /// <summary>
    /// 是否置顶
    /// </summary>
    public bool Top { get; set; } = true;
}

public class ExcellentPost
{
    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ExcellentPostStatus? Status { get; set; }

    /// <summary>
    /// 72小时入职
    /// </summary>
    public bool EntryIn72hour { get; set; }

    /// <summary>
    /// 24小时面试
    /// </summary>
    public bool InterviewIn24Hour { get; set; }
}