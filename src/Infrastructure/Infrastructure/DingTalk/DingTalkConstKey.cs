﻿namespace Infrastructure.DingTalk;

/// <summary>
/// 钉钉应用静态配置
/// </summary>
public class DingTalkConstKey
{
    #region 审批流应用(测试)

    public static readonly string WorkFlowAppKey = "dingtd73p4rlltm2pndu";

    public static readonly string WorkFlowAppSecret = "4pfGfRnt4z7KwvbEfLau6Qjv7ApoM6vtG1jRdjwDKCWEdDXG1qzA5XX2XJXI52HE";

    public static readonly string WorkFlowAppID = "a1d4a694-9d92-4fcd-ad5b-e73299f370c7";

    public static readonly long WorkFlowAgentId = 2832645684;

    /// <summary>
    /// 审批流程ID
    /// </summary>
    public static readonly string ProcessCode = "PROC-B524F6D0-22EA-428F-B36A-A430660ED6DB";

    #endregion

    #region 诺快聘应用

    public static readonly string NkpAppKey = "dingisrgpurarbzjgh2a";

    public static readonly string NkpAppSecret = "783Zc96mBFKAMWxR7rpqsnIXAh2L4u_IzBdYb0yfeVG6IrpTaDlaoAuuaPyQwyfD";

    public static readonly string NkpAppID = "fe29a6f5-df3c-41bc-8a03-913d0334aa19";

    public static readonly long NkpAgentId = 2876531899;

    #endregion
}
