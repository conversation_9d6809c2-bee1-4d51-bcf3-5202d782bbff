﻿using Config.CommonModel;
using Staffing.Model.CentreService.User;

namespace Staffing.Core.Interfaces.CentreService;

public interface IHrService
{
    GetHrListResponse GetHrList(GetHrList model);
    EmptyResponse HrAudit(HrAudit model);
    SearchEntNameResponse SearchEntName(SearchEntName model);
    EmptyResponse HrChangeEnt(HrChangeEnt model);

    /// <summary>
    /// 平台端绑定钉钉用户（协助hr绑定）批量
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<BindDingDingModelResponse> BindDingDing(BindDingDingModelRequest model);

    /// <summary>
    /// 获取钉钉用户列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetDingDingListResponse GetDingDingList(GetDingDingListRequest model);

    /// <summary>
    /// 添加钉钉用户
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> AddDingDingUser(AddDingDingUserRequest model);

    /// <summary>
    /// 绑定钉钉用户
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse BindDingDingByDingUserId(BindDingDingByDingUserIdRequest model);

    /// <summary>
    /// 解除绑定钉钉
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse UnBindDingDingByDingUserId(UnBindDingDingByDingUserIdRequest model);

    /// <summary>
    /// 删除钉钉用户
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse DeleteDingDing(DeleteDingDingRequest model);
    Task<EmptyResponse> DdBind(DdBindRequest model);
    EmptyResponse DdUnBind(DdUnBindRequest model);
}