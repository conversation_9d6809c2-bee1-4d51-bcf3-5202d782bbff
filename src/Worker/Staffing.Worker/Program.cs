﻿using System.Web;
using AspectCore.Extensions.DependencyInjection;
using Config;
using Flurl.Http;
using Flurl.Http.Configuration;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Newtonsoft.Json;
using Noah.Aliyun;
using QuestPDF;
using QuestPDF.Infrastructure;
using Senparc.CO2NET;
using Senparc.CO2NET.Cache;
using Senparc.CO2NET.Cache.Redis;
using Senparc.Weixin;
using Senparc.Weixin.RegisterServices;
using Senparc.Weixin.WxOpen.Containers;
using Serilog;
using Serilog.Events;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Noah;

var builder = WebApplication.CreateBuilder(args);

//aop支持
builder.Host.UseServiceProviderFactory(new DynamicProxyServiceProviderFactory());

//Senparc
builder.Host.UseServiceProviderFactory(new SenparcServiceProviderFactory());

builder.Services.AddSenparcWeixinServices(builder.Configuration);//Senparc.Weixin 注册（必须）

builder.Services.AddMemoryCache();

//序列化枚举用int
JsConfig.TreatEnumAsInteger = true;
//允许任何类型序列化
JsConfig.AllowRuntimeType = _ => true;


// #region 添加配置文件
// var svcEnvAppSettingsFile = $"appsettings.{builder.Environment.EnvironmentName}.json";

// if (builder.Configuration.GetValue<bool>("DOTNET_RUNNING_IN_CONTAINER")) //环境主要配置
//     builder.Configuration.AddJsonFile(ConfigMapFileProvider.FromRelativePath("config"), svcEnvAppSettingsFile, false, true);
// else //非容器≈本地开发环境
//     builder.Configuration.AddJsonFile(Path.Join(AppContext.BaseDirectory, "config", svcEnvAppSettingsFile), false, true);
// #endregion

builder.Services.Configure<ConfigManager>(builder.Configuration.GetSection("Settings"));

//雪花算法worker注册
var snWkId = builder.Environment.IsProduction() ? 21 : 11;
Tools.SnowflakeWorkerInit((ushort)snWkId);

Tools.NextId();

//阿里云相关组件(人机检测、短信、内容检查、对象存储)
builder.Services.AddAliyun();

var connectionString = builder.Configuration.GetConnectionString("sqlCon");

builder.Services.AddDbContextFactory<StaffingContext>(
    options =>
    options.UseMySql(connectionString, new MySqlServerVersion(Constants.MySqlVersion), x => x.UseNetTopologySuite())
    .ConfigureWarnings(builder => builder.Ignore(CoreEventId.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning)));

builder.Services.AddDbContextFactory<NoahContext>(
    options => options
    .UseLoggerFactory(LoggerFactory.Create(builder => builder.ClearProviders()))
    .UseSnakeCaseNamingConvention()
    .UseMySql(builder.Configuration.GetConnectionString("noahCon"), new MySqlServerVersion(Constants.MySqlVersion)));

var cfg = builder.Configuration.GetSection("Settings").Get<ConfigManager>();

// //redis
// var csredis = new CSRedis.CSRedisClient(cfg.RedisAddress);
// MyRedisHelper.MyRedis.Initialization(csredis);

MyRedis.Client = new FreeRedis.RedisClient(cfg!.RedisAddress);
MyRedis.Client.Serialize = obj => ServiceStack.Text.JsonSerializer.SerializeToString(obj);
MyRedis.Client.Deserialize = (json, type) => ServiceStack.Text.JsonSerializer.DeserializeFromString(json, type);

var esUrl = cfg.ElasticSearch!.Address!;
var esUrlWithCredentials = $"http://{cfg.ElasticSearch.UserName}:{HttpUtility.UrlEncode(cfg.ElasticSearch.Password)}@{new Uri(esUrl).Host}:{new Uri(esUrl).Port}";

builder.Logging.ClearProviders();
Log.Logger = new LoggerConfiguration()
    .Enrich.FromLogContext() // 增加上下文信息
    .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
    .WriteTo.Console() // 控制台输出
    .WriteTo.Elasticsearch(new Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions(new Uri(esUrlWithCredentials))
    {
        AutoRegisterTemplate = true, // 自动注册索引模板
        IndexFormat = "nkplogs-{0:yyyy.MM.dd}", // 日志索引格式
    }).CreateLogger();

builder.Host.UseSerilog();

ProgramConfig.Init(builder);

builder.Services.AddSingleton<RequestContext>(new RequestContext());

builder.Services.AddHostedService<Staffing.Worker.Hosting.DailyWorker>();
builder.Services.AddHostedService<Staffing.Worker.Hosting.UserWorker>();
builder.Services.AddHostedService<Staffing.Worker.Hosting.PostWorker>();
builder.Services.AddHostedService<Staffing.Worker.Hosting.TalentWorker>();
builder.Services.AddHostedService<Staffing.Worker.Hosting.MsgWorker>();
//builder.Services.AddHostedService<Staffing.Worker.Hosting.TeamBountyWorker>();// 分润交付结算
builder.Services.AddHostedService<Staffing.Worker.Hosting.CanalWorker>();
builder.Services.AddHostedService<Staffing.Worker.Hosting.ProjectAutomaticWorker>();

if (!builder.Environment.IsDevelopment())
    builder.Services.AddHostedService<Staffing.Worker.Listener.RocketMqListener>();

var app = builder.Build();

app.MapGet("/Health/Check", () => Results.Ok());

FlurlHttp.Configure(settings =>
{
    var jsonSettings = new JsonSerializerSettings
    {
        NullValueHandling = NullValueHandling.Ignore,
        ReferenceLoopHandling = ReferenceLoopHandling.Ignore
    };
    settings.JsonSerializer = new NewtonsoftJsonSerializer(jsonSettings);
    // settings.OnErrorAsync = HandleFlurlErrorAsync;
});

//微信Senparc注册
Senparc.CO2NET.RegisterServices.RegisterService.Start(new SenparcSetting
{
    Cache_Redis_Configuration = cfg.PublicRedisAddress
}).UseSenparcGlobal(false, null)
.UseSenparcWeixin(null, null);

//使用redis分布式缓存
CacheStrategyFactory.RegisterObjectCacheStrategy(() => RedisObjectCacheStrategy.Instance);

await AccessTokenContainer.RegisterAsync(cfg.WeChat!.SeekerApplet!.AppId, cfg.WeChat.SeekerApplet.Secret);
await AccessTokenContainer.RegisterAsync(cfg.WeChat.HrApplet!.AppId, cfg.WeChat.HrApplet.Secret);
await AccessTokenContainer.RegisterAsync(cfg.WeChat.InterviewApplet!.AppId, cfg.WeChat.InterviewApplet.Secret);
await Senparc.Weixin.MP.Containers.AccessTokenContainer.RegisterAsync(Constants.WeChatH5AppId, Constants.WeChatH5Secret);

app.Lifetime.ApplicationStopped.Register(OnAppExit);

Console.WriteLine($"服务启动：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");

#region questPDF 设置
// 不设置的话会报异常。
Settings.License = LicenseType.Community;
// 禁用QuestPDF库中文本字符可用性的检查
Settings.CheckIfAllTextGlyphsAreAvailable = false;
#endregion

//var service = app.Services.GetService<ProjectBountyService>();
//service!.MonthlyBountyCheckByEveryDay(CancellationToken.None);


await app.RunAsync();

// async Task HandleFlurlErrorAsync(FlurlCall call)
// {
//     throw new Exception($"HttpStatus:{call.Response.StatusCode}。{await call.Response.GetStringAsync()}");
// }

void OnAppExit()
{
    try
    {
        MyRedis.Client.Dispose();
    }
    catch { }
}

public class ProgramConfig
{
    public static void Init(WebApplicationBuilder builder)
    {
        //自动注入
        builder.Services.AutoDependencyInjection();
        builder.Services.AutoDependencyInjectionForOnlyType();
    }
}