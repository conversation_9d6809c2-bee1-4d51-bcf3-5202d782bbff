﻿using Config.CommonModel;
using Staffing.Model.Interviewer;

namespace Staffing.Core.Interfaces.Interviewer;

public interface IInterviewerService
{
    /// <summary>
    /// 获取面试官基本信息
    /// </summary>
    /// <returns></returns>
    InterviewerInfoResponse GetIneterviewerInfo();

    /// <summary>
    /// 修改面试官基本信息
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse EditInterviewerInfo(EditInterviewerInfoRequest model);

    /// <summary>
    /// 获取面试官待办列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    TodoListResponse GetTodoList(TodoListRequest model);

    /// <summary>
    /// 获取面试官筛选列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    ScreenListResponse GetScreenList(ScreenListRequest model);

    /// <summary>
    /// 获取面试官面试列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    InterviewListResponse GetInterviewList(InterviewListRequest model);

    /// <summary>
    /// 面试官修改面试结果
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse EditInterviewOutcome(EditInterviewOutcomeRequest model);

    /// <summary>
    /// 面试官修改筛简历状态
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse EditScreenStatus(EditScreenStatusRequest model);

    /// <summary>
    /// 获取用户基本信息
    /// </summary>
    /// <param name="todoId"></param>
    /// <returns></returns>
    UserInformationResponse GetUserInformation(string todoId);

    /// <summary>
    /// 获取招聘流程列表
    /// </summary>
    /// <param name="todoId"></param>
    /// <returns></returns>
    RecruitmentProcessResponse GetRecruitmentProcessList(string todoId);

    /// <summary>
    /// 获取面试待办计划（日历模式）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetTodoPlanResponse GetTodoPlan(GetTodoPlanRequest model);

    /// <summary>
    /// 获取面试待办计划列表（日历模式）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetTodoPlanListResponse GetTodoPlanList(GetTodoPlanListRequest model);

    /// <summary>
    /// 获取合作项目
    /// </summary>
    /// <returns></returns>
    ProjectCooperationListResponse GetProjectCooperation(ProjectCooperationList model);
}

