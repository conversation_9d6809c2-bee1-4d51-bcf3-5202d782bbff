using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using System.Net;
using System.Text.Json;
using Xunit;
using Staffing.Model.Hr.Project;

namespace Staffing.Tests.Integration;

/// <summary>
/// 项目合同API集成测试
/// </summary>
public class ProjectContractsApiIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public ProjectContractsApiIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task HasDefaultContract_WithValidProjectId_ReturnsOkResult()
    {
        // Arrange
        var projectId = "test-project-id";

        // Act
        var response = await _client.GetAsync($"/project/contract/default/{projectId}");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<HasDefaultContractResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        
        Assert.NotNull(result);
        Assert.IsType<bool>(result.HasDefault);
    }

    [Fact]
    public async Task HasDefaultContract_WithEmptyProjectId_ReturnsBadRequest()
    {
        // Act
        var response = await _client.GetAsync("/project/contract/default/");

        // Assert
        // 这个测试可能返回 404 (Not Found) 因为路由不匹配，而不是 400 (Bad Request)
        // 实际的验证逻辑在服务层进行
        Assert.True(response.StatusCode == HttpStatusCode.NotFound || response.StatusCode == HttpStatusCode.BadRequest);
    }

    [Theory]
    [InlineData("project-1")]
    [InlineData("project-2")]
    [InlineData("non-existent-project")]
    public async Task HasDefaultContract_WithDifferentProjectIds_ReturnsValidResponse(string projectId)
    {
        // Act
        var response = await _client.GetAsync($"/project/contract/default/{projectId}");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<HasDefaultContractResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        
        Assert.NotNull(result);
        Assert.IsType<bool>(result.HasDefault);
    }

    [Fact]
    public async Task HasDefaultContract_ResponseContentType_IsApplicationJson()
    {
        // Arrange
        var projectId = "test-project-id";

        // Act
        var response = await _client.GetAsync($"/project/contract/default/{projectId}");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        Assert.Contains("application/json", response.Content.Headers.ContentType?.ToString());
    }

    [Fact]
    public async Task HasDefaultContract_ApiDocumentation_IsAccessible()
    {
        // Act - 访问 Swagger/OpenAPI 文档
        var response = await _client.GetAsync("/swagger/v1/swagger.json");

        // Assert
        if (response.StatusCode == HttpStatusCode.OK)
        {
            var content = await response.Content.ReadAsStringAsync();
            
            // 验证我们的新接口是否在 API 文档中
            Assert.Contains("/project/contract/default/{projectId}", content);
            Assert.Contains("检查项目是否有默认合同", content);
        }
        else
        {
            // 如果 Swagger 未启用，跳过此测试
            Assert.True(true, "Swagger documentation not available in this environment");
        }
    }
}

/// <summary>
/// 测试用的简化响应模型（用于反序列化）
/// </summary>
public class TestHasDefaultContractResponse
{
    public bool HasDefault { get; set; }
}