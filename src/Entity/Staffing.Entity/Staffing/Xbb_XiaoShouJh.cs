﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 销帮帮销售机会
/// </summary>
[Table("xbb_xiaoshoujh")]
public class Xbb_XiaoShouJh
{
    [Key]
    public long Id { get; set; }

    /// <summary>
    /// 销售机会名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 客户名称
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string? Creator { get; set; }

    /// <summary>
    /// 创建人Id
    /// </summary>
    public string? CreatorId { get; set; }

    /// <summary>
    /// 创建人工号
    /// </summary>
    public string? CreatorNumber { get; set; }

    public DateTime XbbUpdatedTime { get; set; } = DateTime.Now;
    public DateTime XbbCreatedTime { get; set; } = DateTime.Now;
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}
