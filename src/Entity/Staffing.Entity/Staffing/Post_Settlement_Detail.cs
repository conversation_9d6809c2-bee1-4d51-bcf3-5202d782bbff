﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 职位结算单
/// </summary>
[Table("post_settlement_detail")]
public class Post_Settlement_Detail
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// post_settlement表主键id
    /// </summary>
    public string SettlementId { get; set; } = default!;

    /// <summary>
    /// 结算人id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 用户企业Id
    /// </summary>
    public string? EntId { get; set; }

    /// <summary>
    /// 结算人工号
    /// </summary>
    public string? UserNo { get; set; }

    /// <summary>
    /// 支付单号-调用结算接口返回唯一标识
    /// </summary>
    public string? SettlementNo { get; set; }

    /// <summary>
    /// 结算类型，内部、外部、个人
    /// </summary>
    public PostSettleBountyType? SettleBountyType { get; set; }

    /// <summary>
    /// 用户类型
    /// </summary>
    public PostSettleUserType UserType { get; set; } = default!;

    /// <summary>
    /// 结算状态
    /// </summary>
    [ConcurrencyCheck]
    public PaymentStatus PaymentStatus { get; set; } = PaymentStatus.未打款;

    /// <summary>
    /// 入金商户Id
    /// </summary>
    public string? InMerchantId { get; set; }

    /// <summary>
    /// 出金商户Id
    /// </summary>
    public string? OutMerchantId { get; set; }

    /// <summary>
    /// 合同号（数字诺亚转账需要）
    /// </summary>
    public string? ContractNo { get; set; }

    /// <summary>
    /// 经办人工号
    /// </summary>
    public string? AgentHrNo { get; set; }

    /// <summary>
    /// 是否生成凭证
    /// </summary>
    public int IsVoucher { get; set; }

    /// <summary>
    /// 打款时间
    /// </summary>
    public DateTime? PaymentTime { get; set; }

    /// <summary>
    /// 结算金额
    /// </summary>
    public decimal SettlementMoney { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }


    [ForeignKey("UserId")]
    public User_Hr User_Hr { get; set; } = default!;

    [ForeignKey("EntId")]
    public Enterprise Enterprise { get; set; } = default!;

    [ForeignKey(nameof(InMerchantId))]
    public Enterprise_Merchant InMerchant { get; set; } = default!;

    [ForeignKey(nameof(OutMerchantId))]
    public Enterprise_Merchant OutMerchant { get; set; } = default!;

    [ForeignKey("SettlementId")]
    public Post_Settlement Post_Settlement { get; set; } = default!;
}
