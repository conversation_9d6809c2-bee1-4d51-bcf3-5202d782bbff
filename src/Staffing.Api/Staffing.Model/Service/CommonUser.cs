namespace Staffing.Model.Service;

public class IdVerificationModel
{
    /// <summary>
    /// 身份证
    /// </summary>
    public string? IdCard { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }
}

public class IdVerificationResponse
{
    // /// <summary>
    // /// 结果
    // /// </summary>
    // public bool Result { get; set; }

    // /// <summary>
    // /// 错误描述
    // /// </summary>
    // public string? Msg { get; set; }
}

public class GetIdVerification
{
    /// <summary>
    /// 身份证
    /// </summary>
    public string? IdCard { get; set; }
}

public class GetIdVerificationResponse
{
    /// <summary>
    /// 使用该身份证号的手机号
    /// </summary>
    public string? Mobile { get; set; }
}