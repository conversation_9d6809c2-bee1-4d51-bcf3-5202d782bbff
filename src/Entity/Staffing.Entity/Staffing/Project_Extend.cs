﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 项目扩展表
/// </summary>
[Table("project_extend")]
public class Project_Extend
{
    /// <summary>
    /// 项目Id
    /// </summary>
    [Key]
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 协同次数
    /// </summary>
    public int SyncNum { get; set; }

    /// <summary>
    /// 报名人数
    /// </summary>
    public int RegistrationNum { get; set; }

    /// <summary>
    /// 交付人数
    /// </summary>
    public int DeliveriesNum { get; set; }
    
    /// <summary>
    /// 面试轮次
    /// </summary>
    public int InterviewRound { get; set; }
    /// <summary>
    /// 是否支持视频面试
    /// </summary>
    public bool? IsVideoInterviewSupported { get; set; }
    /// <summary>
    /// 面试流程阶段
    /// </summary>
    public string? InterviewProcess { get; set; }
    /// <summary>
    /// 职位人数
    /// </summary>
    public int? PositionCount { get; set; }
    /// <summary>
    /// 招聘人数
    /// </summary>
    public int? RecruitmentCount { get; set; }
    /// <summary>
    /// 合同金额
    /// </summary>
    public decimal? Price { get; set; }
    /// <summary>
    /// 其他
    /// </summary>
    public string? Remark { get; set; }
    /// <summary>
    /// 必备条件字段
    /// </summary>
    public string? RequiredCondition { get; set; }
    /// <summary>
    /// 项目手册
    /// </summary>
    public string? ProjectManualUrl { get; set; } 
    /// <summary>
    /// 合同签约方式
    /// </summary>
    public SignContractType? SignContractType { get; set; }
    

}