using Infrastructure.Common;
using Staffing.Worker.Service;

namespace Staffing.Worker.Hosting;

public class TeamBountyWorker : HostingBase
{
    private readonly SettlementService _settlementService;
    private readonly ProjectAutomaticService _projectAutomaticService;
    private readonly DingService _dingService;
    private readonly DataBoardService _dataBoardService;
    private readonly LogManager _log;
    private readonly IHostEnvironment _hostingEnvironment;
    public TeamBountyWorker(IHostApplicationLifetime lifetime, ProjectAutomaticService projectAutomaticService, IHostEnvironment hostingEnvironment,
    LogManager log, DataBoardService dataBoardService,
    SettlementService settlementService, DingService dingService) : base(lifetime)
    {
        _projectAutomaticService = projectAutomaticService;
        _log = log;
        _settlementService = settlementService;
        _dingService = dingService;
        _dataBoardService = dataBoardService;
        _hostingEnvironment = hostingEnvironment;
    }

    public override async Task StartAsync(CancellationToken cancel)
    {
        _bgTasks.Add(await Task.Factory.StartNew(() => SettleMentWorkerBy30Seconds(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
    }


    private async Task SettleMentWorkerBy30Seconds(CancellationToken cancel)
    {
        using var timer = new PeriodicTimer(TimeSpan.FromSeconds(30));
        try
        {
            while (await timer.WaitForNextTickAsync(cancel))
            {
                _settlementService.SettlementTeamBounty(cancel);
            }
        }
        catch (OperationCanceledException) { }
    }
}