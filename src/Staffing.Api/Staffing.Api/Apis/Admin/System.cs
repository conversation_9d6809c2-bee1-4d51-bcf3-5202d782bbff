using Config;
using Config.CommonModel;
using Staffing.Model.CentreService.System;
using Staffing.Core.Interfaces.CentreService;
using Staffing.Core.Interfaces;
using Infrastructure.Extend;
using Staffing.Model.Service;

namespace Staffing.Api.Apis.Admin;

[Service(ServiceLifetime.Transient)]
public class SystemRouter : IAdminRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("system").WithTags("系统");

        group.MapPost("/login", (AdminLogin model, ISystemService rep) => rep.AdminLogin(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "登录" });

        group.MapPost("/sms/send", (SendSms model, ISystemService rep) => rep.SendSms(model))
            .WithOpenApi(operation => new(operation) { Summary = "发短信" });

        group.MapPost("/sms/sendcustom", async (SendSmsCustom model, ISystemService rep) => await rep.SendSmsCustomContent(model))
            .WithOpenApi(operation => new(operation) { Summary = "发自定义内容短信(无模板,自定义内容)" });

        group.MapPost("/sms/getcustomsendlist", (GetSmsCustom model, ISystemService rep) => rep.GetSendSmsCustomList(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取自定义内容短信发送列表" });

        group.MapGet("/app", ([AsParameters] GetSystemAppTree model, ISystemService rep) => rep.GetSystemAppTree(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取app树" });

        group.MapGet("/app/{id}", (string? id, ISystemService rep) => rep.GetSystemApp(id))
            .WithOpenApi(operation => new(operation) { Summary = "获取app详情" });

        group.MapPost("/app", (AddSystemApp model, ISystemService rep) => rep.AddSystemApp(model))
            .WithOpenApi(operation => new(operation) { Summary = "添加App" });

        group.MapPut("/app", (UpdateSystemApp model, ISystemService rep) => rep.UpdateSystemApp(model))
            .WithOpenApi(operation => new(operation) { Summary = "更新App" });

        group.MapPut("/app/status", (UpdateSystemAppStatus model, ISystemService rep) => rep.UpdateSystemAppStatus(model))
            .WithOpenApi(operation => new(operation) { Summary = "更新App状态" });

        group.MapDelete("/app/{Id}", ([AsParameters] DeleteSystemApp model, ISystemService rep) => rep.DeleteSystemApp(model))
            .WithOpenApi(operation => new(operation) { Summary = "删除app" });

        group.MapPost("/aliyun/oss/token", (GetAliyunOssToken model, ISharedService rep) => rep.GetAliyunOssToken(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取OSS签名" });

        group.MapPost("/sms/add", async (AddUserSms model, ISystemService rep) => await rep.AddUserSms(model))
            .WithOpenApi(operation => new(operation) { Summary = "短信充值" });

        group.MapGet("/withdraw/record", ([AsParameters] GetWithdrawRecord model, ISystemService rep) => rep.GetWithdrawRecord(model))
            .WithOpenApi(operation => new(operation) { Summary = "提现记录" });

        group.MapPost("/withdraw/set", async (WithdrawChangeStatus model, ISystemService rep) => await rep.WithdrawChangeStatus(model))
            .WithOpenApi(operation => new(operation) { Summary = "提现状态变更" });

        group.MapPost("/config/rcignore/list", (GetRenCaiConfig model, ISystemService rep) => rep.GetRenCaiConfig(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取人才线索池手机号忽略配置" });

        group.MapPost("/config/rcignore/add", (AddRenCaiConfig model, ISystemService rep) => rep.AddRenCaiConfig(model))
            .WithOpenApi(operation => new(operation) { Summary = "添加人才线索池手机号忽略配置" });

        group.MapPost("/config/rcignore/delete", (DeleteRenCaiConfig model, ISystemService rep) => rep.DeleteRenCaiConfig(model))
            .WithOpenApi(operation => new(operation) { Summary = "删除人才线索池手机号忽略配置" });

        group.MapPost("/advert/save", (AdvertSave model, ISystemService rep) => rep.AdvertSave(model))
            .WithOpenApi(operation => new(operation) { Summary = "保存广告" });

        group.MapPost("/advert/getpagelist", (QueryRequest model, ISystemService rep) => rep.AdvertGetPageList(model))
            .WithOpenApi(operation => new(operation) { Summary = "分页获取广告列表" });

        group.MapPost("/advert/updatestate", (AdvertUpdateState model, ISystemService rep) => rep.AdvertUpdateState(model))
            .WithOpenApi(operation => new(operation) { Summary = "修改广告上下架状态" });
    }
}