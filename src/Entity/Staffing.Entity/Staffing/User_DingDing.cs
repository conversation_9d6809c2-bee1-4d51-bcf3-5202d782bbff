﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 用户钉钉信息扩展表
/// </summary>
[Table("user_dingding")]
public class User_DingDing
{
    /// <summary>
    /// 用户id
    /// </summary>
    [Key]
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 用户钉钉头像
    /// </summary>
    public string DingAvatar { get; set; } = string.Empty;

    /// <summary>
    /// 用户钉钉姓名
    /// </summary>
    public string DingName { get; set; } = string.Empty;

    /// <summary>
    /// 用户钉钉工号
    /// </summary>
    public string DingJobNo { get; set; } = string.Empty;

    /// <summary>
    /// 用户钉钉手机号
    /// </summary>
    public string DingMobile { get; set; } = string.Empty;

    /// <summary>
    /// 用户钉钉职位
    /// </summary>
    public string DingTitle { get; set; } = string.Empty;

    /// <summary>
    /// 用户钉钉部门
    /// </summary>
    public string DingBranch { get; set; }=string.Empty;

    /// <summary>
    /// 用户钉钉userid
    /// </summary>
    public string DingUserid { get; set; } = string.Empty;

    /// <summary>
    /// 用户钉钉unionid
    /// </summary>
    public string DingUnionid { get; set; } = string.Empty;

    /// <summary>
    /// 是否开启推送
    /// </summary>
    public bool IsPush { get; set; } = true;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public User_Hr User_Hr { get; set; } = default!;
}

