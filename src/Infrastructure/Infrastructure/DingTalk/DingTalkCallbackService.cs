﻿// using Config.CommonModel.DingDing;
// using Infrastructure.Common;
// using Infrastructure.CommonService.OrderSettlement;
// using Infrastructure.Extend;
// using Microsoft.AspNetCore.Http;
// using Microsoft.Extensions.DependencyInjection;
// using Newtonsoft.Json;
// using Newtonsoft.Json.Linq;
// using System.Collections;

// namespace Infrastructure.DingTalk;

// [Service(ServiceLifetime.Transient)]
// public class DingTalkCallbackService
// {
//     private static readonly string aes_key = "exmQRv5RE6TeKwMs7LxPj7apHDkEyPKRsDzizQln6rw";
//     private static readonly string token = "4G3kn7dFAtORHaeuGPIRIBePZYF73iZ28";

//     private readonly CacheHelper _cacheHelper;
//     private readonly IHttpContextAccessor _httpContextAccessor;
//     private readonly IDingTalkCallBackExcute _dingTalkCallBackExcute;

//     public DingTalkCallbackService(CacheHelper cacheHelper, 
//         IHttpContextAccessor httpContextAccessor, 
//         SettlementDingTalkService dingTalkCallBackExcute
//         )
//     {
//         _cacheHelper = cacheHelper;
//         _httpContextAccessor = httpContextAccessor;
//         _dingTalkCallBackExcute = dingTalkCallBackExcute;
//     }

//     /// <summary>
//     /// 1500ms内返回
//     /// </summary>
//     /// <param name="msgSignature">url中的签名</param>
//     /// <param name="msgTimeStamp">url中的时间戳</param>
//     /// <param name="mstNonce">url中的随机字符串</param>
//     public object GetCallBack(string msgSignature, string msgTimeStamp, string mstNonce)
//     {
//         string encryptStr = string.Empty;
//         if (_httpContextAccessor.HttpContext!.Request.Body.CanSeek)
//         {
//             _httpContextAccessor.HttpContext!.Request.Body.Seek(0, SeekOrigin.Begin);
//         }
//         using (var reader = new StreamReader(_httpContextAccessor.HttpContext!.Request.Body))
//         {
//             encryptStr = reader.ReadToEnd();
//         }

//         var dingtalkcrypt = new DingTalkCrypt(token, aes_key, DingTalkConstKey.WorkFlowAppKey);
//         var obj = (JObject)JsonConvert.DeserializeObject(encryptStr)!;
//         if(obj == null)
//         {
//             // todo:记录错误日志，但是返回成功，否则钉钉会重试
//             return EncryptReturnMsg("success", mstNonce, dingtalkcrypt);
//         }
            
//         encryptStr = obj["encrypt"]!.ToString();// 加密消息体
//         string cryptStr = string.Empty;// 解密消息体

//         try
//         {
//             // 解密
//             int sign = dingtalkcrypt.VerifyURL(msgSignature, msgTimeStamp, mstNonce, encryptStr, ref cryptStr);
//             if (sign != 0)
//             {
//                 // 验证回调url是否合法
//                 // todo: 记录错误日志，不阻止钉钉重试
//                 return EncryptReturnMsg("failed", mstNonce, dingtalkcrypt);
//             }
//             var dingtalkcallback = JsonConvert.DeserializeObject<DingTalkApprovalCallback>(cryptStr)!;
//             switch (dingtalkcallback.EventType)
//             {
//                 case "check_url": // 测试回调
//                     break;
//                 case "suite_ticket"://定时推送Ticket
//                     break;
//                 case "tmp_auth_code"://钉钉推送过来的临时授权码
//                     break;
//                 case "change_auth":
//                     break;
//                 case "check_update_suite_url": 
//                     break;
//                 case "bpms_task_change"://审批任务开始，结束，转交
//                     break;
//                 case "bpms_instance_change"://审批实例开始，结束
//                     _dingTalkCallBackExcute.ProcessInstanceExcute(dingtalkcallback);
//                     break;
//             }
//         }
//         catch
//         {
//             // todo:记录日志
//         }

//         return EncryptReturnMsg("success", mstNonce, dingtalkcrypt);
//     }

//     public static long DateTimeToUnixTimestamp(DateTime localTime)
//     {
//         var dateTime = localTime.ToUniversalTime();
//         var start = new DateTime(1970, 1, 1, 0, 0, 0, dateTime.Kind);
//         return Convert.ToInt64((dateTime - start).TotalSeconds);
//     }

//     private object EncryptReturnMsg(string res, string nonce, DingTalkCrypt dingTalk)
//     {
//         string encrypt = string.Empty;
//         string signaturert = string.Empty;
//         string timeStamprt = DateTimeToUnixTimestamp(DateTime.Now).ToString();
//         dingTalk.EncryptMsg(res, timeStamprt, nonce, ref encrypt, ref signaturert);
//         var jsonMap = new Hashtable
//                        {
//                            {"msg_signature", signaturert},
//                            {"encrypt", encrypt},
//                            {"timeStamp", timeStamprt},
//                            {"nonce", nonce}
//                        };
//         return jsonMap;
//     }
// }