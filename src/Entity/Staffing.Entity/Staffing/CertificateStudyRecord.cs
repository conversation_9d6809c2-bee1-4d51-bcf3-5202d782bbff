﻿using Nest;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 用户学习课程记录
/// *一个用户+学习URL+证书ID+规格ID 作为一条记录的唯一标识
/// </summary>
[Table("certificate_studyrecord")]
public class CertificateStudyRecord
{
    /// <summary>
    /// 记录ID
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 学习用户的ID
    /// </summary>
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 学习用户的手机号
    /// *为防止用户被删除后无法关联，因此记录
    /// </summary>
    public string UserMobile { get; set; } = default!;
    /// <summary>
    /// 课程链接
    /// </summary>
    public string StudyUrl { get; set; } = default!;

    /// <summary>
    /// 课程名称
    /// </summary>
    public string StudyName { get; set; } = default!;

    /// <summary>
    /// 证书ID
    /// </summary>
    public string CertificateId { get; set; } = default!;

    /// <summary>
    /// 规格ID
    /// </summary>
    public string SpecID { get; set; } = default!;

    /// <summary>
    /// 证书名称
    /// *为防止证书被删除后无法关联，因此记录
    /// </summary>
    public string CertificateName { get; set; } = default!;
    /// <summary>
    /// 证书规格名称
    /// *为防止规格被删除后无法关联，因此记录
    /// </summary>
    public string SpecName { get; set; } = default!;
    /// <summary>
    /// 记录第一次学习时间
    /// </summary>
    public DateTime CreateTime { get; set; } 
}
