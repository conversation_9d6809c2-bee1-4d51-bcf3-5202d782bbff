﻿using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Model.Hr.User;
using Staffing.Core.Interfaces;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces.Hr;
using Config;
using Config.CommonModel.Business;
using Infrastructure.CommonService;
using Config.CommonModel;
using Microsoft.EntityFrameworkCore;
using ServiceStack.Text;
using EntityFrameworkCore.AutoHistory.Extensions;
using NetTopologySuite.Geometries;
using Infrastructure.Proxy;
using Config.CommonModel.Tencent;
using Staffing.Model.Service;
using Config.CommonModel.Aliyun;
using LinqKit;
using Config.CommonModel.Tasks;
using Staffing.Model.Hr.Balance;
using Senparc.Weixin.Exceptions;

namespace Staffing.Core.Services.Hr;

[Service(ServiceLifetime.Transient)]
public class HrService : IHrService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private ISharedService _sharedService;
    private ICommonService _commonService;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CommonDicService _commonDicService;
    private readonly CommonCacheService _commonCacheService;
    private readonly CommonUserService _commonUserService;
    private readonly WeChatHelper _weChatHelper;
    private readonly NuoPinApi _nuoPinApi;
    private readonly TencentImHelper _tencentImHelper;
    private readonly AliyunDyplsHelper _aliyunDyplsHelper;
    private readonly DingDingHelper _dingDingHelper;
    private readonly CommonWithdraw _commonWithdraw;
    public HrService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, ICommonService commonService,
        LogManager log, WeChatHelper weChatHelper, NuoPinApi nuoPinApi,
        DingDingHelper dingDingHelper, CommonWithdraw commonWithdraw,
        ISharedService sharedService, CommonDicService commonDicService,
        CommonCacheService commonCacheService, CommonUserService commonUserService,
        TencentImHelper tencentImHelper, AliyunDyplsHelper aliyunDyplsHelper)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _commonService = commonService;
        _log = log;
        _weChatHelper = weChatHelper;
        _sharedService = sharedService;
        _commonDicService = commonDicService;
        _commonCacheService = commonCacheService;
        _commonUserService = commonUserService;
        _nuoPinApi = nuoPinApi;
        _tencentImHelper = tencentImHelper;
        _aliyunDyplsHelper = aliyunDyplsHelper;
        _dingDingHelper = dingDingHelper;
        _commonWithdraw = commonWithdraw;
    }

    public async Task<HrLoginResponse> HrLogin(HrLogin model)
    {
        var lockerKey = $"hrlogin:{Md5Helper.Md5((int)model.Type + "_" + model.Code!)}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        if (model.Type != HrLoginType.Applet)
            throw new BadRequestException("目前仅支持小程序登录");

        AppClientInfo? applet = null;
        if (string.IsNullOrWhiteSpace(model.AppId))
        {
            applet = ClientApps.HrApplet;
        }
        else
        {
            var apps = _commonDicService.GetAppIds();

            applet = apps.Where(x => x.AppId == model.AppId && (x.Type == ClientType.JobMarketForHr || x.Type == ClientType.HrApplet))
            .Select(s => new AppClientInfo
            {
                AppID = s.AppId,
                AppSecret = s.AppSecret,
                Type = s.Type
            }).FirstOrDefault();
        }

        if (applet == null)
            throw new BadRequestException("非法请求");

        if (string.IsNullOrEmpty(model.Code) || string.IsNullOrEmpty(model.EncryptedData) || string.IsNullOrEmpty(model.Iv))
            throw new BadRequestException("缺少小程序登录信息");

        GetAppletPhoneResponse? wechat = null;
        try
        {
            var wechatCkey = $"wechat:{Md5Helper.Md5(applet.AppID + model.Code!)}";
            var disable = 600;

            wechat = await _cacheHelper.GetRedisCacheAsync<GetAppletPhoneResponse>(async () =>
            {
                var obj = await _weChatHelper.GetAppletPhone(applet.AppID, applet.AppSecret, model.Code, model.EncryptedData!, model.Iv!);
                return obj;
            }, wechatCkey, disable);
        }
        catch (ErrorJsonResultException e)
        {
            if (e.JsonResult.errcode == Senparc.Weixin.ReturnCode.不合法的oauth_code || e.JsonResult.errcode == Senparc.Weixin.ReturnCode.oauth_code超时)
                throw new BadRequestException("微信授权过期，请重新授权");
            else
                throw;
        }
        catch (Exception e)
        {
            _log.Error("登录微信授权失败", e.Message, JsonSerializer.SerializeToString(model));
        }

        if (string.IsNullOrWhiteSpace(wechat?.WeId.unionid))
            throw new Exception("未能拿到微信unionid");

        // wechat = new GetAppletPhoneResponse
        // {
        //     Phone = new DecodedPhoneNumber { purePhoneNumber = "15532181990" },
        //     WeId = new JsCode2JsonResult { unionid = "ovpVR0SfraVJbHCD9RstQKtE5AQs", openid = "" }
        // };

        var mobile = wechat?.Phone.purePhoneNumber;

        if (wechat == null || string.IsNullOrEmpty(mobile))
            throw new BadRequestException("微信授权失败");

        //优先微信登录
        var userOpenId = _context.User_OpenId.Where(x => x.AppId == applet.AppID && x.OpenId == wechat.WeId.openid)
        .Include(i => i.User)
        .FirstOrDefault();

        var user = userOpenId?.User;
        // var user = _context.User.Where(x => x.WeChatId == wechat.WeId.unionid).FirstOrDefault();

        //如果微信不存在，手机号登录
        if (user == null)
        {
            user = _context.User.Include(i => i.User_Extend).Where(x => x.Mobile == mobile).FirstOrDefault();

            if (user != null && _context.User_OpenId.Any(x => x.AppId == applet.AppID && x.UserId == user.UserId))
                throw new BadRequestException("该手机号已绑定其他微信");

            // //如果手机号存在并且和原有wechatid不同
            // if (user != null && !string.IsNullOrWhiteSpace(user.WeChatId) && user.WeChatId.ToLower().Trim() != wechat.WeId.unionid.ToLower().Trim())
            //     throw new BadRequestException("该手机号已绑定其他微信");
        }

        if (user == null)
            throw new BadRequestException("该手机号尚未开通诺快聘");

        // if (string.IsNullOrWhiteSpace(user.WeChatId))
        //     user.WeChatId = wechat.WeId.unionid;

        var hr = _context.User_Hr.FirstOrDefault(x => x.UserId == user.UserId);
        if (hr == null || hr.Status != UserStatus.Active)
            throw new BadRequestException("该手机号尚未开通诺快聘");

        // if (string.IsNullOrWhiteSpace(hr.WeChatAppletId))
        //     hr.WeChatAppletId = wechat.WeId.openid;

        if (userOpenId == null)
        {
            userOpenId = new User_OpenId
            {
                UserId = user.UserId,
                AppId = applet.AppID,
                Type = ClientType.HrApplet,
                OpenId = wechat.WeId.openid!,
                UnionId = wechat.WeId.unionid!
            };
            _context.Add(userOpenId);
        }

        _context.SaveChanges();

        ClientType? client = model.Type switch
        {
            HrLoginType.Applet => ClientType.HrApplet,
            HrLoginType.Web => ClientType.HrWeb,
            _ => null
        };

        var token = _commonService.CreateRefreshToken(user.UserId, TokenType.企业端, client);

        var result = new HrLoginResponse
        {
            AccessToken = token.AccessToken,
            RefreshToken = token.RefreshToken,
            TokenExpiresTime = token.TokenExpiresTime
        };

        return result;
    }

    public async Task<TokenInfo> HrRefreshToken(HrRefreshToken model)
    {
        var token = await _commonService.RefreshToken(model.RefreshToken!);

        if (token == null)
            throw new UnauthorizedException(string.Empty);

        var result = new TokenInfo
        {
            AccessToken = token.AccessToken,
            RefreshToken = token.RefreshToken,
            TokenExpiresTime = token.TokenExpiresTime
        };

        return result;
    }

    public async Task<HrApplyAppResponse> HrApplyApp(HrApplyApp model)
    {
        var result = new HrApplyAppResponse();

        var hr = _context.User_Hr.FirstOrDefault(x => x.NuoId == _user.NuoPinId);

        var newHr = false;
        if (hr == null)
        {
            //获取诺聘信息
            var nuoHr = await _nuoPinApi.GetEnterpriseAccount(_user.NuoPinId);

            if (nuoHr == null)
                throw new Exception("获取诺聘hr信息失败");

            //企业白名单
            var entIsWhiteList = _commonCacheService.IsEntWhiteList(nuoHr.PK_EID);

            //注册开始
            if (string.IsNullOrWhiteSpace(nuoHr!.PK_EID))
                throw new BadRequestException("您尚未加入企业");

            // var nuoCode = _user.NuoUser!.Expand.InviteCode;

            var user = _context.User.FirstOrDefault(x => x.Mobile == nuoHr.LoginName);

            //创建用户主表
            if (user == null)
            {
                // if (_context.User.Any(x => x.NuoCode == nuoCode || x.Mobile == _user.NuoUser.LoginName))
                // {
                //     _log.Error("Hr开通应用出错", "该账号关键信息已被占用", JsonSerializer.SerializeToString(_user.NuoUser));
                //     throw new BadRequestException("该账号关键信息已被占用，请联系客服人员进行处理");
                // }

                user = new User
                {
                    Mobile = nuoHr.LoginName
                    // NuoCode = nuoCode,
                    // WeChatId = nuoHr.UnionID
                };
                user.User_Extend = new User_Extend
                {
                    UserId = user.UserId,
                    RegistrationIp = _user.RequestIpAddress ?? string.Empty
                };
                user.User_Num = new User_Num
                {
                    UserId = user.UserId
                };
                _context.Add(user);

                var userBalance = new User_Balance
                {
                    UserId = user.UserId
                };
                _context.Add(userBalance);
            }

            if (_context.User_Hr.Any(x => x.UserId == user.UserId))
            {
                _log.Error("Hr开通应用出错", "该账号手机号已被使用", JsonSerializer.SerializeToString(nuoHr));
                throw new BadRequestException("该账号手机号已被使用，请联系客服人员进行处理");
            }

            var enterprise = _context.Enterprise.FirstOrDefault(x => x.NuoId == nuoHr.PK_EID);

            var isFirstHr = false;
            //创建企业
            if (enterprise == null)
            {
                enterprise = new Enterprise
                {
                    NuoId = nuoHr.PK_EID,
                    Name = nuoHr.EnterpriseName,
                    LogoUrl = nuoHr.LogoUrl,
                    Abbreviation = nuoHr.Abbreviation ?? string.Empty,
                    Type = 1,
                    Status = EnterpriseStatus.Active,
                    RegionId = string.Empty,
                    Address = nuoHr.Address
                };
                _context.Add(enterprise);
                isFirstHr = true;
            }

            //加锁防止邀约码重复
            using var locker = await MyRedis.Lock("st:hrreg", 10);

            if (locker == null)
                _log.Error("注册hr出错", "邀约码获取锁失败");

            //生成邀约码
            var ivcode = string.Empty;
            var random = new Random();
            ivcode = random.Next(100000, 999999).ToString();
            var times = 0;
            while (_context.User_Hr.Any(a => a.InviteCode == ivcode))
            {
                ivcode = random.Next(100000, 999999).ToString();
                times++;
                if (times > 10)
                {
                    _log.Error("邀请码快不够啦", "邀请码快不够啦");
                }
                else if (times > 100)
                {
                    throw new Exception("生成邀约码失败");
                }
            }

            //初始化默认头像
            var avatarnan = new List<string> {
                "https://resources.nuopin.cn/staffing/res/avatarnan1.png",
                "https://resources.nuopin.cn/staffing/res/avatarnan2.png",
                "https://resources.nuopin.cn/staffing/res/avatarnan3.png",
                "https://resources.nuopin.cn/staffing/res/avatarnan4.png"
            };
            var avatarnv = new List<string> {
                "https://resources.nuopin.cn/staffing/res/avatarnv1.png",
                "https://resources.nuopin.cn/staffing/res/avatarnv2.png",
                "https://resources.nuopin.cn/staffing/res/avatarnv3.png",
                "https://resources.nuopin.cn/staffing/res/avatarnv4.png"
            };

            var ran = new Random(Guid.NewGuid().GetHashCode());
            var avatarIndex = ran.Next(0, 4);
            var avatar = string.Empty;
            if (nuoHr.Sex == 1)
                avatar = avatarnan[avatarIndex];
            else
                avatar = avatarnv[avatarIndex];

            //创hr表
            hr = new User_Hr
            {
                NuoId = nuoHr.PK_EAID,
                NickName = nuoHr.EnterpriseAccountName ?? string.Empty,
                Avatar = avatar,
                Source = RegisterSource.Web,
                Enterprise = enterprise,
                Status = !model.IsQuickJob ? (entIsWhiteList ? UserStatus.Active : UserStatus.UnReviewed) : UserStatus.Ignore,
                User = user,
                RoleId = isFirstHr ? Power.AdminRoleId : Power.EmployeeRoleId,
                Post = "招聘顾问",//nuoHr.PositionName,
                EMail = nuoHr.ReceiveMailbox,
                // WeChatAppletId = nuoHr.AppletID,
                InviteCode = ivcode
            };
            _context.Add(hr);

            //添加顾问统计表
            var userData = new User_Hr_Data
            {
                UserId = hr.UserId
            };
            _context.Add(userData);

            newHr = true;
        }
        else
        {
            if (!model.IsQuickJob)
                if (hr.Status != UserStatus.Active)
                    hr.Status = UserStatus.UnReviewed;
            // hr.Status = entIsWhiteList ? UserStatus.Active : UserStatus.UnReviewed;
        }

        // //从诺聘更新数据
        // if (string.IsNullOrWhiteSpace(hr.WeChatAppletId) && !string.IsNullOrWhiteSpace(nuoHr.AppletID))
        // {
        //     hr.WeChatAppletId = nuoHr.AppletID;
        // }

        _context.SaveChanges();

        result.Status = hr.Status;
        result.ApplyStatus = AppApplyStatusTools.UserStatusToAppApplyStatus(hr.Status);

        if (newHr)
        {
            //计算资料完整度
            _commonUserService.CountHrScore(hr.UserId);

            //hr注册消息
            MyRedis.Client.RPush(SubscriptionKey.HrRegister, hr.UserId);

            //新用户清理token缓存，重新读取内容
            _commonUserService.CleanHrAccessToken(_user.NuoPinId);
        }

        //计算hr排行榜
        MyRedis.Client.SAdd(SubscriptionKey.HrRank, hr.UserId);

        return result;
    }

    /// <summary>
    /// 诺快聘入驻 - 人力资源机构
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    /// <exception cref="BadRequestException"></exception>
    public async Task<HrApplyAppResponse> HrApplyAppNew(HrApplyApp model)
    {
        var result = new HrApplyAppResponse();

        var hr = _context.User_Hr.FirstOrDefault(x => x.NuoId == _user.NuoPinId);

        var newHr = false;
        if (hr == null)
        {
            //获取诺聘信息
            var nuoHr = await _nuoPinApi.GetEnterpriseAccount(_user.NuoPinId);

            if (nuoHr == null)
                throw new Exception("获取诺聘hr信息失败");

            //企业白名单
            var entIsWhiteList = _commonCacheService.IsEntWhiteList(nuoHr.PK_EID);

            //注册开始
            //if (string.IsNullOrWhiteSpace(nuoHr!.PK_EID))
            //    throw new BadRequestException("您尚未加入企业");

            // var nuoCode = _user.NuoUser!.Expand.InviteCode;

            var user = _context.User.FirstOrDefault(x => x.Mobile == nuoHr.LoginName);

            //创建用户主表
            if (user == null)
            {
                user = new User
                {
                    Mobile = nuoHr.LoginName
                    // NuoCode = nuoCode,
                    // WeChatId = nuoHr.UnionID
                };
                user.User_Extend = new User_Extend
                {
                    UserId = user.UserId,
                    RegistrationIp = _user.RequestIpAddress ?? string.Empty
                };
                user.User_Num = new User_Num
                {
                    UserId = user.UserId
                };
                _context.Add(user);

                var userBalance = new User_Balance
                {
                    UserId = user.UserId
                };
                _context.Add(userBalance);
            }

            if (_context.User_Hr.Any(x => x.UserId == user.UserId))
            {
                _log.Error("Hr开通应用出错", "该账号手机号已被使用", JsonSerializer.SerializeToString(nuoHr));
                throw new BadRequestException("该账号手机号已被使用，请联系客服人员进行处理");
            }

            var enterprise = _context.Enterprise.FirstOrDefault(x => x.Name == model.CompanyInfo.CompanyName);

            var isFirstHr = false;
            //创建企业
            if (enterprise == null)
            {
                enterprise = new Enterprise
                {
                    NuoId = nuoHr.PK_EID ?? string.Empty,// 可为空
                    Name = model.CompanyInfo.CompanyName ?? "空企业名称_" + DateTimeOffset.Now.ToUnixTimeSeconds().ToString(),
                    LogoUrl = nuoHr.LogoUrl,
                    Abbreviation = nuoHr.Abbreviation ?? string.Empty,
                    Type = 1,
                    Status = EnterpriseStatus.Active,
                    RegionId = string.Empty,
                    Address = nuoHr.Address ?? model.CompanyInfo.CompanyAddress
                };
                _context.Add(enterprise);
                isFirstHr = true;
            }

            //加锁防止邀约码重复
            using var locker = await MyRedis.Lock("st:hrreg", 10);

            if (locker == null)
                _log.Error("注册hr出错", "邀约码获取锁失败");

            //生成邀约码
            var ivcode = string.Empty;
            var random = new Random();
            ivcode = random.Next(100000, 999999).ToString();
            var times = 0;
            while (_context.User_Hr.Any(a => a.InviteCode == ivcode))
            {
                ivcode = random.Next(100000, 999999).ToString();
                times++;
                if (times > 10)
                {
                    _log.Error("邀请码快不够啦", "邀请码快不够啦");
                }
                else if (times > 100)
                {
                    throw new Exception("生成邀约码失败");
                }
            }

            //初始化默认头像
            var avatarnan = new List<string> {
                "https://resources.nuopin.cn/staffing/res/avatarnan1.png",
                "https://resources.nuopin.cn/staffing/res/avatarnan2.png",
                "https://resources.nuopin.cn/staffing/res/avatarnan3.png",
                "https://resources.nuopin.cn/staffing/res/avatarnan4.png"
            };
            var avatarnv = new List<string> {
                "https://resources.nuopin.cn/staffing/res/avatarnv1.png",
                "https://resources.nuopin.cn/staffing/res/avatarnv2.png",
                "https://resources.nuopin.cn/staffing/res/avatarnv3.png",
                "https://resources.nuopin.cn/staffing/res/avatarnv4.png"
            };

            var ran = new Random(Guid.NewGuid().GetHashCode());
            var avatarIndex = ran.Next(0, 4);
            var avatar = string.Empty;
            if (nuoHr.Sex == 1)
                avatar = avatarnan[avatarIndex];
            else
                avatar = avatarnv[avatarIndex];

            //创hr表
            hr = new User_Hr
            {
                NuoId = nuoHr.PK_EAID,
                NickName = (nuoHr.EnterpriseAccountName ?? model.NickName) ?? string.Empty,
                Avatar = avatar,
                Source = RegisterSource.Web,
                Enterprise = enterprise,
                Status = !model.IsQuickJob ? (entIsWhiteList ? UserStatus.Active : UserStatus.UnReviewed) : UserStatus.Ignore,
                User = user,
                RoleId = isFirstHr ? Power.AdminRoleId : Power.EmployeeRoleId,
                Post = "招聘顾问", //nuoHr.PositionName ?? model.CompanyInfo?.PositionName,
                EMail = nuoHr.ReceiveMailbox,
                // WeChatAppletId = nuoHr.AppletID,
                InviteCode = ivcode
            };
            _context.Add(hr);

            //添加顾问统计表
            var userData = new User_Hr_Data
            {
                UserId = hr.UserId
            };
            _context.Add(userData);

            newHr = true;
        }
        else
        {
            if (!model.IsQuickJob)
                if (hr.Status != UserStatus.Active)
                    hr.Status = UserStatus.UnReviewed;
            // hr.Status = entIsWhiteList ? UserStatus.Active : UserStatus.UnReviewed;
        }

        // //从诺聘更新数据
        // if (string.IsNullOrWhiteSpace(hr.WeChatAppletId) && !string.IsNullOrWhiteSpace(nuoHr.AppletID))
        // {
        //     hr.WeChatAppletId = nuoHr.AppletID;
        // }

        _context.SaveChanges();

        result.Status = hr.Status;
        result.ApplyStatus = AppApplyStatusTools.UserStatusToAppApplyStatus(hr.Status);

        if (newHr)
        {
            //计算资料完整度
            _commonUserService.CountHrScore(hr.UserId);

            //hr注册消息
            MyRedis.Client.RPush(SubscriptionKey.HrRegister, hr.UserId);

            //新用户清理token缓存，重新读取内容
            _commonUserService.CleanHrAccessToken(_user.NuoPinId);
        }

        //计算hr排行榜
        MyRedis.Client.SAdd(SubscriptionKey.HrRank, hr.UserId);

        return result;
    }

    public HrInfo GetHr()
    {
        var result = _context.User_Hr.Where(x => x.UserId == _user.Id)
        .Select(s => new HrInfo
        {
            UserId = s.UserId,
            Mobile = s.User.Mobile,
            Avatar = s.Avatar,
            CreatedTime = s.CreatedTime,
            Name = s.NickName,
            Status = s.Status,
            // Powers = s.Enterprise_Role.Powers,
            RoleId = s.RoleId,
            RoleName = s.Enterprise_Role.Name,
            Post = s.Post,
            InviteCode = s.InviteCode,
            Talent = s.User_Num.Talent,
            VirtualTalent = s.User_Num.VirtualTalent,
            DingMobile = s.User_DingDing.DingMobile,
            Sex = s.Sex,
            H5Notice = s.H5Notice,
            WeChatH5Subscribe = s.User.WeChatH5Subscribe,
            EntName = s.Enterprise.Name,
            EntAbbr = string.IsNullOrEmpty(s.Enterprise.Abbreviation) ? s.Enterprise.Name : s.Enterprise.Abbreviation,
            WeChatNo = s.WeChatNo,
            EntSpecific = s.Enterprise.Specific,
            BusinessSms = (int?)s.User_Sms.BusinessSms ?? 0
        }).FirstOrDefault();

        if (result == null)
            return new HrInfo();

        // if (!string.IsNullOrWhiteSpace(result?.PowerStr))
        //     result.Powers = result.PowerStr.Split(",").ToList();

        // var talentCount = await _commonCacheService.GetHrTalentCount(_user.Id);

        result.IsNoah = result.EntSpecific?.Contains(EntSpecific.Noah) == true;

        result.Powers = _user.Powers;

        result!.Insiders = _commonCacheService.IsEntWhiteList(_user.NuoEntId);

        result.IsNuoKuaiPin = _user.Id == Constants.PlatformHrId;

        result.NeedBindDing = result.IsNoah && result.Status == UserStatus.Active && string.IsNullOrWhiteSpace(result.DingMobile);

        return result!;
    }

    public GetHrSimpleResponse GetHrSimpleSearch(QuerySearch model)
    {
        var result = new GetHrSimpleResponse();
        var predicate = PredicateBuilder.New<User_Hr>(x => x.Status == UserStatus.Active);
        if (!string.IsNullOrWhiteSpace(model.Search))
            predicate.And(x => x.NickName.Contains(model.Search) || x.User.Mobile.Contains(model.Search));
        var sql = _context.User_Hr.Where(predicate)
            .Select(s => new HrSimpleInfo
            {
                UserId = s.UserId,
                Name = s.NickName,
                Avatar = s.Avatar,
                Post = s.Post,
                Mobile = s.User.Mobile,
                EntName = s.Enterprise.Name,
                EntAbbr = string.IsNullOrEmpty(s.Enterprise.Abbreviation) ? s.Enterprise.Name : s.Enterprise.Abbreviation,
                OrgName = string.Join('|', s.Enterprise_Org_User.Select(x => x.Enterprise_Org.Name).ToList())
            });
        if (string.IsNullOrWhiteSpace(model.Search))
            result.Rows = sql.Take(20).ToList();//防止未传参时读取全库
        else
            result.Rows = sql.ToList();

        if (result.Rows == null)
            result.Rows = new List<HrSimpleInfo>();
        return result;
    }

    public GetAppList GetApps()
    {
        var result = _commonDicService.GetApps();

        if (!string.IsNullOrWhiteSpace(_user.Id))
        {
            var hr = _context.User_Hr.Where(x => x.UserId == _user.Id)
                .Select(s => new
                {
                    s.Status
                    // s.User_Extend.LatelyApp
                }).FirstOrDefault();

            //应用状态
            result.Apps.ForEach(item =>
            {
                if (item.Status == ActiveStatus.Inactive)
                    item.ApplyStatus = AppApplyStatus.无法申请;
                else
                {
                    item.ApplyStatus = AppApplyStatusTools.UserStatusToAppApplyStatus(hr?.Status);
                }
            });

            var cacheKey = $"{RedisKey.HrBehavior.Key}{_user.Id}";
            var latelyApps = MyRedis.Client.HGet<List<string>>(cacheKey, RedisKey.HrBehavior.LatelyApps);

            //最近使用
            if (latelyApps?.Count > 0)
            {
                result.Used = new List<GetAppsInfo>();
                foreach (var item in latelyApps)
                {
                    var app = result.Apps.Where(x => x.Id == item).FirstOrDefault();
                    if (app != null)
                        result.Used.Add(app);
                }
            }
        }

        // 权限处理
        AppsPowerCheck(result);

        // 如果某个result.Category下没有任何result.Category，则隐藏，排序按照之前逻辑
        result.Category = result.Category.Where(x => result.Apps.Any(y => y.ParentId == x.Id)).ToList();

        return result;
    }

    /// <summary>
    /// 零工市场 - 企业审核权限处理
    /// sys_app: powers字段:string
    /// user_hr: power字段：list<string>
    /// </summary>
    /// <param name="list"></param>
    private void AppsPowerCheck(GetAppList list)
    {
        List<string> userPowers = _user.Powers ?? new List<string>();

        for (int i = list.Apps.Count - 1; i >= 0; i--)
        {
            // 这里Powers如果为空，则不进行校验，说明所有人都有权限看到
            if (!string.IsNullOrWhiteSpace(list.Apps[i].Powers))
            {
                var powers = list.Apps[i].Powers.Split(',');
                bool check = false;
                foreach (var item in userPowers)
                {
                    if (powers.Contains(item))
                    {
                        check = true;
                        break;
                    }
                }
                if (!check)
                {
                    list.Apps.RemoveAt(i);
                }
            }
        }
    }

    public GetAppInfo GetAppInfo(string id)
    {
        var result = _context.Sys_App.Where(x => x.Id == id)
        .Select(s => new GetAppInfo
        {
            Id = s.Id,
            Name = s.Name,
            AppId = s.Id,
            Icon = s.Icon,
            Abstract = s.Abstract,
            Alias = s.Alias,
            AppInfo = s.AppInfo,
            Manual = s.Manual,
            ShortAbstract = s.ShortAbstract,
            Status = s.Status,
            Type = s.Type,
            Url = s.Url
        }).FirstOrDefault();

        if (result == null)
            throw new NotFoundException("内容不存在");

        var hr = _context.User_Hr.Where(x => x.UserId == _user.Id)
        .Select(s => new
        {
            s.Status,
        }).FirstOrDefault();

        if (result.Status == ActiveStatus.Inactive)
            result.ApplyStatus = AppApplyStatus.无法申请;
        else
        {
            result.ApplyStatus = AppApplyStatusTools.UserStatusToAppApplyStatus(hr?.Status);
        }

        return result;
    }

    public HrDataInfo GetHrData()
    {
        return GetHrData(_user.Id);
    }

    public HrDataInfo GetHrData(string UserId)
    {
        var result = new HrDataInfo();

        var allTalent = _context.User_Num.Where(x => x.UserId == UserId)
        .Select(s => new { s.Talent, s.VirtualTalent }).FirstOrDefault();

        result.Talent = (allTalent?.Talent ?? 0) + (allTalent?.VirtualTalent ?? 0);
        result.RealTalent = allTalent?.Talent ?? 0;

        var todayKey = $"{RedisKey.HrStatBehavior.DailyKey}{DateTime.Today.ToString("yyyyMMdd")}";
        var todayDeliveryKey = $"{RedisKey.HrStatBehavior.Delivery}{UserId}";
        var newTalKey = $"{RedisKey.HrStatBehavior.NewTalent}{UserId}";
        var todayVistKey = $"{RedisKey.HrStatBehavior.Visits}{UserId}";
        var todayDeliveryBountyKey = $"{RedisKey.HrStatBehavior.DeliveryBounty}{UserId}";

        var gzNum = MyRedis.Client.HMGet<decimal>(todayKey, todayDeliveryKey, newTalKey, todayVistKey, todayDeliveryBountyKey);
        result.TodayDelivery = (int)gzNum[0];
        result.TodayNewTalent = (int)gzNum[1];
        result.TodayVisits = (int)gzNum[2];
        result.TodayDeliveryBounty = gzNum[3];

        var fullDeliveryKey = $"{RedisKey.HrStatBehavior.Delivery}{UserId}";
        var fullVistKey = $"{RedisKey.HrStatBehavior.Visits}{UserId}";
        var fullDeliveryBountyKey = $"{RedisKey.HrStatBehavior.DeliveryBounty}{UserId}";

        var flNum = MyRedis.Client.HMGet<decimal>(RedisKey.HrStatBehavior.FullKey, fullDeliveryKey, fullVistKey, fullDeliveryBountyKey);
        result.FullDelivery = (int)flNum[0];
        result.FullVisits = (int)flNum[1];
        result.FullDeliveryBounty = flNum[2];

        if (result.FullDeliveryBounty < result.TodayDeliveryBounty)
            result.FullDeliveryBounty = result.TodayDeliveryBounty;

        if (result.FullDelivery < result.TodayDelivery)
            result.FullDelivery = result.TodayDelivery;

        if (result.FullVisits < result.TodayVisits)
            result.FullVisits = result.TodayVisits;

        if (result.Talent < result.TodayNewTalent)
            result.Talent = result.TodayNewTalent;

        return result;
    }

    public EmptyResponse UseApp(UseApp model)
    {
        var result = new EmptyResponse();

        if (string.IsNullOrWhiteSpace(_user.Id) || string.IsNullOrWhiteSpace(model.AppId))
            return result;

        var cacheKey = $"{RedisKey.HrBehavior.Key}{_user.Id}";

        // var userExt = _context.User_Extend.FirstOrDefault(x => x.UserId == _user.Id);
        var latelyApps = MyRedis.Client.HGet<List<string>>(cacheKey, RedisKey.HrBehavior.LatelyApps);

        latelyApps = latelyApps ?? new List<string>();

        latelyApps = latelyApps.Where(x => x != model.AppId).ToList();

        if (latelyApps.Count >= 10)
            latelyApps.RemoveAt(latelyApps.Count - 1);

        latelyApps.Insert(0, model.AppId!);

        MyRedis.Client.HSet(cacheKey, RedisKey.HrBehavior.LatelyApps, latelyApps);


        // _context.SaveChanges();

        return result;
    }

    public HrCardInfo GetHrCard(string id)
    {
        if (string.IsNullOrWhiteSpace(id))
            id = _user.Id;

        if (string.IsNullOrWhiteSpace(id))
            throw new NotFoundException("内容不存在");

        var result = _context.User_Hr.Where(x => x.UserId == id)
        .Select(s => new HrCardInfo
        {
            Address = s.Address,
            Avatar = s.Avatar,
            CreatedTime = s.CreatedTime,
            Describe = s.Describe,
            EMail = s.EMail,
            EntWeChatQrCode = s.EntWeChatQrCode,
            Mobile = s.User.Mobile,
            Name = s.NickName,
            Post = s.Post,
            RegionId = s.RegionId,
            UserId = s.UserId,
            WeChatNo = s.WeChatNo,
            InviteCode = s.InviteCode,
            Sex = s.Sex,
            Lat = s.Location.X,
            Lng = s.Location.Y,
            AppletQrCode = s.AppletQrCode,
            Score = s.Score,
            Tags = s.Tags,
            Identity = !string.IsNullOrEmpty(s.User.IdentityCard),
            EntName = string.IsNullOrEmpty(s.Enterprise.Abbreviation) ? s.Enterprise.Name : s.Enterprise.Abbreviation,
            H5Notice = s.H5Notice,
            EntAbbr = string.IsNullOrEmpty(s.Enterprise.Abbreviation) ? s.Enterprise.Name : s.Enterprise.Abbreviation
        }).FirstOrDefault();

        if (result == null)
            throw new NotFoundException("内容不存在");

        if (!string.IsNullOrWhiteSpace(result.RegionId))
            result.City = _commonDicService.GetCityById(result.RegionId);

        // result.Mobile = Tools.MobilToX(result.Mobile);

        result.RegistrationDays = (int)(DateTime.Now - result.CreatedTime!.Value).TotalDays + 1;

        result.HrAppletQrCode = "https://resources.nuopin.cn/teststaffing/user/146579773418648837/agentent/1657519662530.jpg";

        if (result.Tags == null)
            result.Tags = new List<string>();

        return result;
    }

    public EmptyResponse UpdateHrCard(UpdateHrCard model)
    {
        if (string.IsNullOrWhiteSpace(model.Name))
            throw new NotFoundException("姓名不能为空");

        var hr = _context.User_Hr.Include(i => i.User).ThenInclude(i => i.User_Extend)
        .FirstOrDefault(x => x.UserId == _user.Id);

        if (hr == null)
            throw new NotFoundException("内容不存在");

        if (!string.IsNullOrWhiteSpace(model.Name))
            hr.NickName = model.Name;

        if (model.Avatar != null)
            hr.Avatar = model.Avatar;

        //产品经理要求固定返回"招聘顾问"
        // if (model.Post != null)
        //     hr.Post = model.Post;

        if (model.EMail != null)
            hr.EMail = model.EMail;

        if (model.Address != null)
            hr.Address = model.Address;

        var needAutoTeam = false;
        if (model.HrAutoTeam != null)
        {
            hr.User_Extend.HrAutoTeam = model.HrAutoTeam == 1 ? 1 : 0;

            if (hr.User_Extend.HrAutoTeam == 1)
                needAutoTeam = true;
        }

        if (model.WeChatNo != null)
            hr.WeChatNo = model.WeChatNo;

        bool isUpdateEntWeChatQrCode = false;
        if (model.EntWeChatQrCode != null)
        {
            if (hr.EntWeChatQrCode != model.EntWeChatQrCode)
            {
                hr.EntWeChatQrCode = model.EntWeChatQrCode;
                isUpdateEntWeChatQrCode = true;
            }
        }

        if (model.Describe != null)
            hr.Describe = model.Describe;

        if (model.RegionId != null)
            hr.RegionId = Tools.TrimRegionId(model.RegionId);

        if (model.Sex.HasValue)
            hr.Sex = model.Sex;

        if (model.Lat != 0 && model.Lng != 0)
            hr.Location = new Point(model.Lat, model.Lng);

        if (model.H5Notice.HasValue)
            hr.H5Notice = model.H5Notice.Value;

        if (model.Tags != null)
            hr.Tags = model.Tags;

        if (!string.IsNullOrWhiteSpace(model.NewMobile))
        {
            if (model.OldMobile?.Trim() != hr.User.Mobile.Trim())
                throw new BadRequestException("原手机号码验证失败");

            if (_context.User.Any(x => x.Mobile == model.NewMobile && x.UserId != _user.Id))
                throw new BadRequestException("该手机号已被使用");

            var checkSMSCode = _sharedService.CheckSMSCode(new CheckSMSCode
            {
                Phone = model.NewMobile,
                SMSCode = model.SmsCode,
                Type = SMSType.ChangePhone
            });

            if (!checkSMSCode.Result)
                throw new BadRequestException("验证码错误");

            hr.User.Mobile = model.NewMobile;

            MyRedis.Client.SAdd(SubscriptionKey.HrUpdateMobile, hr.UserId);
        }

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        //计算资料完整度
        _commonUserService.CountHrScore(_user.Id);

        //计算hr排行榜
        MyRedis.Client.SAdd(SubscriptionKey.HrRank, hr.UserId);

        //更新im资料
        if (!string.IsNullOrWhiteSpace(hr.TencentImId))
            MyRedis.Client.SAdd(SubscriptionKey.UpdateHrImAct, hr.UserId);

        //通知自动协同项目
        if (needAutoTeam)
        {
            var ut = DateTime.Now.AddHours(1).ToUnixTime();
            MyRedis.Client.ZAdd(SubscriptionKey.HrAutoTeam, ut, hr.UserId);
        }

        //更新企微后，更新蓝领证书推广图
        if (isUpdateEntWeChatQrCode)
            MyRedis.Client.SAdd(SubscriptionKey.AdviserShareCertificate, hr.UserId);

        return new EmptyResponse();
    }

    public async Task<GetImUserSigResponse> GetImUserSig()
    {
        var lockerKey = $"lk:imsig:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new NotAcceptableException(string.Empty);

        var result = await _commonUserService.GetHrImUserSig(_user.Id);

        return result;
    }

    public GetHrMessageSumResponse GetHrMessageSum()
    {
        var result = new GetHrMessageSumResponse();

        result.ReadTime = MyRedis.Client.HGet<long>(RedisKey.Message.HrMsgReadKey, _user.Id);

        var lastMsgTime = _context.Msg_Notify_Hr.Where(x => x.UserId == _user.Id)
        .Select(s => (DateTime?)s.CreatedTime).FirstOrDefault();

        long tms = 0;
        if (lastMsgTime.HasValue)
            tms = lastMsgTime.Value.ToUnixTimeMs();

        result.NewMsg = tms > result.ReadTime;

        return result;
    }

    public GetHrMessageResponse GetHrMessage(GetHrMessage model)
    {
        var result = new GetHrMessageResponse();

        var predicate = PredicateBuilder.New<Msg_Notify_Hr>(x => x.UserId == _user.Id);

        if (model.MsgType.HasValue)
            predicate = predicate.And(x => x.Type == model.MsgType);

        var sql = _context.Msg_Notify_Hr.Where(predicate);

        result.Rows = sql
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetHrMessageInfo
        {
            CreatedTime = s.CreatedTime,
            Type = s.Type,
            Content = s.Content,
            DataStr = s.Data,
            Id = s.Id,
            Title = s.Title,
            UserId = s.UserId
        }).ToList();

        if (result.Rows.Count < model.PageSize)
            result.IsLast = true;
        else
            result.IsLast = false;

        //如果设置已读
        if (model.SetRead)
            MyRedis.Client.HSet(RedisKey.Message.HrMsgReadKey, _user.Id, DateTime.Now.ToUnixTimeMs());

        foreach (var item in result.Rows)
        {
            item.TimeStamp = item.CreatedTime!.Value.ToUnixTimeMs();

            if (!string.IsNullOrWhiteSpace(item.DataStr))
                item.Data = JsonObject.Parse(item.DataStr);

            item.DataStr = null;
        }

        return result;
    }

    public GetRecentDeliveryResponse GetRecentDelivery(GetRecentDelivery model)
    {
        var result = new GetRecentDeliveryResponse();

        var predicate = PredicateBuilder.New<Recruit>(x => x.Post_Delivery.Post_Team.Project_Team.HrId == _user.Id);

        if (model.LastTimeStamp > 0)
        {
            var lastTime = model.LastTimeStamp?.FromUnixTimeMs();
            predicate = predicate.And(x => x.CreatedTime < lastTime);
        }

        var sql = _context.Recruit.Where(predicate);

        result.Rows = sql
        .OrderByDescending(x => x.CreatedTime)
        .Take(model.PageSize)
        .Select(s => new GetRecentDeliveryInfo
        {
            EventTime = s.CreatedTime,
            Avatar = s.User_Seeker.Avatar,
            Birthday = s.User_Seeker.User_Resume.Birthday,
            Education = s.User_Seeker.User_Resume.Education,
            UserName = s.User_Seeker.NickName,
            PostName = s.Post_Delivery.Post.Name,
            Identity = !string.IsNullOrEmpty(s.User_Seeker.User.IdentityCard),
            Occupation = s.User_Seeker.User_Resume.Occupation,
            RecruitId = s.RecruitId,
            Sex = s.User_Seeker.User_Resume.Sex,
            Status = s.Status,
            FileAwayStatus = s.FileAway,
            UserId = s.SeekerId
        }).ToList();

        if (result.Rows.Count < model.PageSize)
            result.IsLast = true;
        else
            result.IsLast = false;

        result.LastTimeStamp = result.Rows.LastOrDefault()?.EventTime.ToUnixTimeMs() ?? 0;

        foreach (var item in result.Rows)
        {
            item.EducationName = item.Education?.GetDescription();
            item.OccupationName = item.Occupation?.GetDescription();
            item.Age = Tools.GetAgeByBirthdate(item.Birthday);
            item.SeekerDeliveryStatus = DeliverStatusTools.RecruitStatusToSeekerDeliveryStatus(item.Status, item.FileAwayStatus);
            item.SeekerDeliveryStatusName = item.SeekerDeliveryStatus?.GetDescription();
        }

        return result;
    }

    public GetNewTalentResponse GetNewTalent(GetNewTalent model)
    {
        var result = new GetNewTalentResponse();

        var predicate = PredicateBuilder.New<Talent_Platform>(x => x.HrId == _user.Id);

        if (model.LastTimeStamp > 0)
        {
            var lastTime = model.LastTimeStamp?.FromUnixTimeMs();
            predicate = predicate.And(x => x.CreatedTime < lastTime);
        }

        var sql = _context.Talent_Platform.Where(predicate);

        result.Rows = sql
        .OrderByDescending(x => x.CreatedTime)
        .Take(model.PageSize)
        .Select(s => new GetNewTalentInfo
        {
            EventTime = s.CreatedTime,
            Avatar = s.User_Seeker.Avatar,
            Birthday = s.User_Seeker.User_Resume.Birthday,
            Education = s.User_Seeker.User_Resume.Education,
            UserName = s.User_Seeker.NickName,
            Identity = !string.IsNullOrEmpty(s.User_Seeker.User.IdentityCard),
            Occupation = s.User_Seeker.User_Resume.Occupation,
            UserId = s.SeekerId,
            Sex = s.User_Seeker.User_Resume.Sex,
            TalentLevel = s.Level
        }).ToList();

        if (result.Rows.Count < model.PageSize)
            result.IsLast = true;
        else
            result.IsLast = false;

        result.LastTimeStamp = result.Rows.LastOrDefault()?.EventTime.ToUnixTimeMs() ?? 0;

        foreach (var item in result.Rows)
        {
            item.EducationName = item.Education?.GetDescription();
            item.OccupationName = item.Occupation?.GetDescription();
            item.TalentLevelName = item.TalentLevel.GetDescription();
            item.Age = Tools.GetAgeByBirthdate(item.Birthday);
        }

        return result;
    }

    public GetRecentVisitsResponse GetRecentVisits(GetRecentVisits model)
    {
        var result = new GetRecentVisitsResponse();

        var predicate = PredicateBuilder.New<Talent_Platform>(x => x.HrId == _user.Id);

        if (model.LastTimeStamp > 0)
        {
            var lastTime = model.LastTimeStamp?.FromUnixTimeMs();
            predicate = predicate.And(x => x.SeekerVisitTime < lastTime);
        }

        var sql = _context.Talent_Platform.Where(predicate);

        result.Rows = sql
        .OrderByDescending(x => x.SeekerVisitTime)
        .Take(model.PageSize)
        .Select(s => new GetRecentVisitsInfo
        {
            EventTime = s.SeekerVisitTime,
            Avatar = s.User_Seeker.Avatar,
            Birthday = s.User_Seeker.User_Resume.Birthday,
            Education = s.User_Seeker.User_Resume.Education,
            UserName = s.User_Seeker.NickName,
            Identity = !string.IsNullOrEmpty(s.User_Seeker.User.IdentityCard),
            Occupation = s.User_Seeker.User_Resume.Occupation,
            UserId = s.SeekerId,
            Sex = s.User_Seeker.User_Resume.Sex,
            TalentLevel = s.Level
        }).ToList();

        if (result.Rows.Count < model.PageSize)
            result.IsLast = true;
        else
            result.IsLast = false;

        result.LastTimeStamp = result.Rows.LastOrDefault()?.EventTime.ToUnixTimeMs() ?? 0;

        foreach (var item in result.Rows)
        {
            item.EducationName = item.Education?.GetDescription();
            item.OccupationName = item.Occupation?.GetDescription();
            item.TalentLevelName = item.TalentLevel.GetDescription();
            item.Age = Tools.GetAgeByBirthdate(item.Birthday);
        }

        return result;
    }

    public async Task<GetPrivacyPhoneNumberResponse> GetPrivacyPhoneNumber(GetPrivacyPhoneNumber model)
    {
        var result = new GetPrivacyPhoneNumberResponse();

        model.SeekerId = Tools.GetUserIdByImId(model.SeekerId);

        if (string.IsNullOrWhiteSpace(model.SeekerId) && string.IsNullOrWhiteSpace(model.ResumeId))
            throw new BadRequestException("缺少Id");

        var seekerMobile = string.Empty;

        if (!string.IsNullOrWhiteSpace(model.SeekerId))
            seekerMobile = _context.User.Where(x => x.UserId == model.SeekerId)
            .Select(s => s.Mobile).FirstOrDefault();
        else if (!string.IsNullOrWhiteSpace(model.ResumeId))
            seekerMobile = _context.Talent_Resume.Where(x => x.Id == model.ResumeId)
            .Select(s => s.Mobile).FirstOrDefault();

        if (seekerMobile == null || Tools.IsMobile(seekerMobile) == false)
            throw new BadRequestException("电话不存在");

        var hrMobile = _context.User.Where(x => x.UserId == _user.Id)
        .Select(s => s.Mobile).First();

        await Task.FromResult(1);

        result.Mobile = seekerMobile;
        result.IsVirtual = false;
        return result;

        // //如果双方电话相同
        // if (seekerMobile.Trim() == hrMobile.Trim())
        // {
        //     result.Mobile = seekerMobile;
        //     result.IsVirtual = false;
        //     return result;
        // }

        // //如果投递过，直接返回真实电话（郭思诚2024.9.5要求，招聘流程显示真实号码）
        // if (!string.IsNullOrEmpty(model.SeekerId) && _context.Post_Delivery.Any(x => x.SeekerId == model.SeekerId && x.Post.Project.HrId == _user.Id))
        // {
        //     result.Mobile = seekerMobile;
        //     result.IsVirtual = false;
        //     return result;
        // }

        // //如果在人才库，直接返回真实电话
        // if (_context.Talent_Platform.Any(x => x.HrId == _user.Id && x.User_Seeker.User.Mobile == seekerMobile))
        // {
        //     result.Mobile = seekerMobile;
        //     result.IsVirtual = false;
        //     return result;
        // }



        // //取隐私号码
        // var bindAxb = new BindAxb
        // {
        //     PhoneNoA = hrMobile,
        //     PhoneNoB = seekerMobile
        // };
        // var secretNoResult = await _aliyunDyplsHelper.BindAxb(bindAxb);

        // result.ExpireSeconds = bindAxb.ExpiredSeconds;
        // result.Mobile = secretNoResult.SecretNo;

        // return result;
    }

    /// <summary>
    /// 获取钉钉绑定状态
    /// </summary>
    /// <returns></returns>
    public GetDingDingBindingStatsuRsponse GetDingDingBindingStatsu()
    {
        GetDingDingBindingStatsuRsponse retModel = new GetDingDingBindingStatsuRsponse() { BindingStatus = false };

        var dingdingModel = _context.User_DingDing.Where(o => o.UserId == _user.Id).FirstOrDefault();
        if (dingdingModel is not null)
        {
            retModel.BindingStatus = true;
            retModel.DingName = dingdingModel.DingName;
            retModel.DingMobile = dingdingModel.DingMobile;
            retModel.DingJobNo = dingdingModel.DingJobNo;
            retModel.DingAvatar = dingdingModel.DingAvatar;
        }

        return retModel;
    }

    /// <summary>
    /// 绑定钉钉，获取钉钉用户信息
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<BindingDingDingRsponse> BindingDingDing(BindingDingDingRequest model)
    {
        BindingDingDingRsponse retModel = new BindingDingDingRsponse();

        bool IsBinding = _context.User_DingDing.Any(o => o.UserId == _user.Id);

        if (IsBinding)
        {
            throw new BadRequestException("用户已绑定钉钉，无法重复绑定！");
        }

        var dingAuthorization = await _dingDingHelper.GetDingDingUserIdByPhone(new Config.CommonModel.DingDing.GetDingDingUserIdByPhoneRequest { mobile = model.DingPhone });

        if (dingAuthorization.userid is not null)
        {
            var dingUserDetails = await _dingDingHelper.GetDingDingUserDetailsByUserId(new Config.CommonModel.DingDing.GetDingDingUserDetailsByUserIdRequest { userid = dingAuthorization.userid });
            if (dingUserDetails is not null)
            {
                bool IsBinding_dingding = _context.User_DingDing.Any(o => o.DingUserid == dingUserDetails.userid);
                if (IsBinding_dingding)
                {
                    throw new BadRequestException("钉钉用户已存在！");
                }

                string branchName = string.Empty;
                if (dingUserDetails.dept_id_list is not null && dingUserDetails.dept_id_list.Count > 0)
                {
                    var branchModel = await _dingDingHelper.GetDingBranchByBranchId(dingUserDetails.dept_id_list.Last());
                    if (branchModel is not null)
                    {
                        branchName = branchModel.name ?? string.Empty;
                    }
                }

                var dingdingModel = _context.DingDing.Where(o => o.DingUserid == dingUserDetails.userid!).FirstOrDefault();
                if (dingdingModel is null)
                {
                    DingDing addDingDingModel = new DingDing()
                    {
                        DingUserid = dingUserDetails.userid!,
                        CreatedTime = DateTime.Now,
                        DingAvatar = dingUserDetails.avatar ?? string.Empty,
                        DingBranch = branchName,
                        DingJobNo = dingUserDetails.job_number ?? string.Empty,
                        DingMobile = dingUserDetails.mobile ?? string.Empty,
                        DingName = dingUserDetails.name ?? string.Empty,
                        DingTitle = dingUserDetails.title ?? string.Empty,
                        DingUnionid = dingUserDetails.unionid ?? string.Empty,
                        PushTime = null,
                        UpdatedTime = DateTime.Now,
                    };
                    _context.Add(addDingDingModel);
                }

                User_DingDing user_DingDing = new User_DingDing()
                {
                    UserId = _user.Id,
                    CreatedTime = DateTime.Now,
                    IsPush = true,
                    UpdatedTime = DateTime.Now,
                    DingUserid = dingUserDetails.userid!,
                    DingAvatar = dingUserDetails.avatar ?? string.Empty,
                    DingJobNo = dingUserDetails.job_number ?? string.Empty,
                    DingMobile = dingUserDetails.mobile ?? string.Empty,
                    DingName = dingUserDetails.name ?? string.Empty,
                    DingUnionid = dingUserDetails.unionid ?? string.Empty,
                    DingBranch = branchName,
                    DingTitle = dingUserDetails.title ?? string.Empty,
                };

                _context.Add(user_DingDing);
                _context.SaveChanges();

                retModel.DingMobile = dingUserDetails.mobile ?? string.Empty;
                retModel.DingJobNo = dingUserDetails.job_number ?? string.Empty;
                retModel.DingName = dingUserDetails.name ?? string.Empty;
                retModel.DingAvatar = dingUserDetails.avatar ?? string.Empty;

                MyRedis.Client.SAdd(SubscriptionKey.HrUpdateMobile, user_DingDing.UserId);
            }
            else
            {
                throw new BadRequestException("获取钉钉用户信息错误！");
            }
        }
        else
        {
            throw new BadRequestException("钉钉授权失败，手机号不匹配！");
        }

        return retModel;
    }

    /// <summary>
    /// 移除钉钉绑定
    /// </summary>
    /// <returns></returns>
    public EmptyResponse RelieveDingDingBinding()
    {
        var bindingModel = _context.User_DingDing.Where(o => o.UserId == _user.Id).FirstOrDefault();

        if (bindingModel is null)
        {
            throw new BadRequestException("用户未绑定钉钉，无法解除绑定！");
        }

        _context.Remove(bindingModel);
        _context.SaveChanges();
        MyRedis.Client.SAdd(SubscriptionKey.HrUpdateMobile, _user.Id);
        return new EmptyResponse { };
    }

    public async Task<SendInviteSmsResponse> SendInviteSms(SendInviteSms model)
    {
        var lockerKey = $"updateusersms_{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = await MyRedis.Lock(lockerKey, 10);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var result = new SendInviteSmsResponse();

        model.ToUsers = model.ToUsers.GroupBy(g => g.Mobile!.Trim())
        .Select(s => new SendInviteSmsUserInfo
        {
            Mobile = s.Key,
            Name = s.Max(m => m.Name)
        }).ToList();

        if (model.ToUsers.Count == 0)
            return result;

        if (!(model.TeamPostNo?.StartsWith(Constants.TeamPostIdPre) == true && int.TryParse(model.TeamPostNo.Substring(1), out var teamPostAutoId)))
            throw new BadRequestException("职位编号格式错误");

        var teamPost = _context.Post_Team.Where(x => x.AutoId == teamPostAutoId && x.Show)
        .Select(s => new
        {
            s.TeamPostId,
            s.PostId,
            EntName = s.Project_Team.User_Hr.Enterprise.Name,
            PostName = s.Post.Name,
            HrName = s.Project_Team.User_Hr.NickName,
            HrId = s.Project_Team.HrId
        }).FirstOrDefault();

        if (teamPost == null)
            throw new BadRequestException("职位不存在");

        var userSms = _context.User_Sms.FirstOrDefault(x => x.UserId == _user.Id);

        if (userSms == null || userSms.BusinessSms < model.ToUsers.Count)
            throw new BadRequestException("可用额度不足");

        var appletShortLink = await _weChatHelper.GetWxShortLinkForJump(ClientApps.SeekerApplet.AppID, $"pages/positiondetail/positiondetail", $"id={teamPost.TeamPostId}&adviser={teamPost.HrId}&channelid=1");
        if (string.IsNullOrWhiteSpace(appletShortLink))
            throw new Exception("生成职位短链接出错");

        appletShortLink = appletShortLink.Split('/')[^1];

        userSms.BusinessSms -= model.ToUsers.Count;

        var task = new Tasks
        {
            TargetId = Tools.NextId(),
            UserId = _user.Id,
            Type = TaskHandlingType.职位邀约短信,
            Name = $"职位邀约{model.ToUsers.Count}个",
            Content = JsonSerializer.SerializeToString(new SendBusinessSms
            {
                ToUsers = model.ToUsers,
                SmsTempCode = "SMS_248430302",
                SmsTempJson = new SendBusinessSmsTempCode
                {
                    hr = $"{teamPost.HrName}（{teamPost.EntName}）",
                    position = $"（{teamPost.PostName}）",
                    jumpurl = appletShortLink
                }
            })
        };
        _context.Add(task);

        var userSmsRecord = new User_Sms_Record
        {
            Type = UserSmsRecordType.职位邀约,
            Amount = model.ToUsers.Count * -1,
            UserId = _user.Id,
            Content = "职位邀约",
            TaskId = task.TaskId,
            Balance = userSms.BusinessSms
        };
        _context.Add(userSmsRecord);

        _context.SaveChanges();

        return result;
    }

    public GetBalanceResponse GetBalance(GetBalance model)
    {
        var balance = _context.User_Balance.First(x => x.UserId == _user.Id);

        var todayBalance = MyRedis.Client.HGet<decimal>(RedisKey.Balance.HrTodayBalance, $"{DateTime.Now.ToYYYY_MM_DD()}_{_user.Id}");

        var result = new GetBalanceResponse
        {
            Balance = balance.HrBalance,
            BalanceFull = balance.HrBalanceFull,
            TodayBalance = todayBalance
        };

        return result;
    }

    public GetBalanceRecordResponse GetBalanceRecord(GetBalanceRecord model)
    {
        var result = new GetBalanceRecordResponse();

        var predicate = PredicateBuilder.New<User_Balance_Record>(x => x.UserId == _user.Id && x.UserType == SeekerOrHr.Hr);

        if (model.Month.HasValue)
        {
            var bg = new DateTime(model.Month.Value.Date.Year, model.Month.Value.Date.Month, 1);
            var ed = bg.AddMonths(1);
            predicate = predicate.And(x => x.EventTime >= bg && x.EventTime < ed);
        }

        result.Rows = _context.User_Balance_Record.Where(predicate)
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new BalanceRecordInfo
        {
            Amount = s.Amount,
            Content = s.Content,
            EventTime = s.EventTime,
            Increment = s.Increment,
            Type = s.Type,
            UserType = s.UserType
        }).ToList();

        if (result.Rows.Count >= model.PageSize)
            result.IsLast = false;

        return result;
    }

    public async Task<WithdrawResponse> Withdraw(Withdraw model)
    {
        await Task.FromResult(1);
        throw new BadRequestException("该功能暂未启用");

        // var result = new WithdrawResponse();

        // if (model.Amount < 1)
        //     throw new BadRequestException("提现最低1元");

        // if (model.Amount > 100)
        //     throw new BadRequestException("单笔最多100元");

        // if (_context.User_Hr.Any(x => x.UserId == _user.Id && x.Status != UserStatus.Active))
        //     throw new BadRequestException("禁止提现，请联系客服人员");

        // //锁用户钱包
        // var lockerKey = $"{RedisKey.Balance.LockKey}{_user.Id}";
        // using var locker = await MyRedis.Lock(lockerKey, 15);

        // if (locker == null)
        //     throw new Exception("提现获取钱包锁失败");

        // //测试环境改为最低值
        // if (!_hostingEnvironment.IsProduction())
        //     model.Amount = 0.3M;

        // var now = DateTime.Today;
        // var todayWithraw = _context.User_Withdraw
        //     .Any(x => x.UserId == _user.Id && x.UserType == SeekerOrHr.Hr && x.CreatedTime >= now
        //      && (x.Status == WithdrawStatus.提现中 || x.Status == WithdrawStatus.审核中 ||
        //      x.Status == WithdrawStatus.提现完成 || x.Status == WithdrawStatus.提现失败));
        // if (todayWithraw)
        //     throw new BadRequestException("今日已提现，明天再来试试吧！");

        // var user = _context.User_Hr.Where(x => x.UserId == _user.Id).First();

        // if (string.IsNullOrWhiteSpace(user.WeChatAppletId))
        //     throw new BadRequestException("请前往小程序授权提现");

        // var balance = _context.User_Balance.Where(x => x.UserId == _user.Id).First();
        // if (balance.HrBalance < model.Amount)
        //     throw new BadRequestException("余额不足");

        // balance.HrBalance -= model.Amount;

        // var withdraw = new User_Withdraw
        // {
        //     Amount = model.Amount,
        //     UserId = _user.Id,
        //     AccountName = user.WeChatAppletId,
        //     Account = user.WeChatAppletId,
        //     ApprovalTime = DateTime.Now,
        //     Approver = string.Empty,
        //     Status = WithdrawStatus.提现中,
        //     Type = 0,
        //     UserType = SeekerOrHr.Hr
        // };
        // _context.Add(withdraw);

        // var balanceRecord = new User_Balance_Record
        // {
        //     Amount = balance.HrBalance,
        //     Increment = model.Amount * -1,
        //     UserType = SeekerOrHr.Hr,
        //     Type = BalanceRecordType.提现,
        //     Content = "提现",
        //     UserId = _user.Id
        // };
        // _context.Add(balanceRecord);

        // _context.SaveChanges();

        // var wdrt = false;

        // //去提现
        // try
        // {
        //     await _commonWithdraw.WechatWithdraw(WeChatApps.HrApplet.AppID, user.WeChatAppletId, model.Amount, withdraw.Id);
        //     wdrt = true;
        // }
        // catch (ThirdPartyServerException e)
        // {
        //     withdraw.Remark = e.Message;
        // }
        // catch (Exception e)
        // {
        //     _log.Error("提现失败", Tools.GetErrMsg(e), model);
        // }

        // //提现完成
        // if (wdrt)
        // {
        //     withdraw.Status = WithdrawStatus.提现完成;
        //     withdraw.WithdrawTime = DateTime.Now;
        //     balance.HrWithdrawTime = DateTime.Now;
        // }
        // else
        // {
        //     withdraw.Status = WithdrawStatus.提现失败;
        // }
        // _context.SaveChanges();

        // if (!wdrt)
        //     throw new BadRequestException("提现失败，请联系客服人员");

        // return result;
    }

    public EmptyResponse SetHrCardShow(HrCardShow model)
    {
        MyRedis.Client.HSet(RedisKey.HrCardShow, _user.Id, model);

        return new EmptyResponse();
    }

    public HrCardShow GetHrCardShow()
    {
        var result = MyRedis.Client.HGet<HrCardShow>(RedisKey.HrCardShow, _user.Id);
        result = result ?? new HrCardShow();
        return result;
    }

    public HrSettingInfo GetHrSetting()
    {
        var result = _context.User_Extend.Where(x => x.UserId == _user.Id)
        .Select(s => new HrSettingInfo
        {
            HrAutoTeam = s.HrAutoTeam ?? 0
        }).FirstOrDefault();

        return result ?? new HrSettingInfo();
    }

    public EmptyResponse UpdateHrSetting(HrSettingInfo model)
    {
        _context.User_Extend.Where(x => x.UserId == _user.Id)
        .ExecuteUpdate(s => s.SetProperty(b => b.HrAutoTeam, model.HrAutoTeam));

        return new EmptyResponse();
    }
}