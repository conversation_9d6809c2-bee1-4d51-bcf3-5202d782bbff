﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Noah;

/// <summary>
/// 客户联系人信息
/// </summary>
[Table("customer_contact")]
public partial class CustomerContact
{
    [Key]
    public long ContactId { get; set; }
    /// <summary>
    /// 编号
    /// </summary>
    public string? ContactCode { get; set; }
    /// <summary>
    /// 客户id
    /// </summary>
    public long? CustomerId { get; set; }
    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }
    /// <summary>
    /// 电话
    /// </summary>
    public string? Tel { get; set; }
    /// <summary>
    /// 手机
    /// </summary>
    public string? Phone { get; set; }
    /// <summary>
    /// 传真
    /// </summary>
    public string? Fax { get; set; }
    /// <summary>
    /// 1男，2女
    /// </summary>
    public string? Gender { get; set; }
    /// <summary>
    /// 职务
    /// </summary>
    public string? Position { get; set; }
    /// <summary>
    /// 部门
    /// </summary>
    public string? Dept { get; set; }
    /// <summary>
    /// 1高层，2中层，3基层
    /// </summary>
    public string? Grade { get; set; }
    /// <summary>
    /// 1身份证；2军官证；3护照；4其他
    /// </summary>
    public string? IdType { get; set; }
    /// <summary>
    /// 证件号码
    /// </summary>
    public string? IdNumber { get; set; }
    /// <summary>
    /// 重要程度1星，2星，3星，4星，5星（对应1、2、3、4、5）
    /// </summary>
    public string? Importance { get; set; }
    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set; }
    /// <summary>
    /// QQ
    /// </summary>
    public string? Qq { get; set; }
    /// <summary>
    /// 生日
    /// </summary>
    public string? Birthday { get; set; }
    /// <summary>
    /// 1阳历，2阴历
    /// </summary>
    public string? BirthdayType { get; set; }
    /// <summary>
    /// 爱好
    /// </summary>
    public string? Hobby { get; set; }
    /// <summary>
    /// 1关键决策人，2分项决策人，3商务决策人，4技术决策人，5财务决策人，6使用人，7意见影响人，8普通人
    /// </summary>
    public string? DecisionRole { get; set; }
    /// <summary>
    /// 亲密程度1星，2星，3星，4星，5星（对应1、2、3、4、5）
    /// </summary>
    public string? Intimacy { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    /// <summary>
    /// 省
    /// </summary>
    public int? Province { get; set; }
    /// <summary>
    /// 市
    /// </summary>
    public int? City { get; set; }
    /// <summary>
    /// 区
    /// </summary>
    public int? District { get; set; }
    /// <summary>
    /// 街道(销帮帮使用高德地图)
    /// </summary>
    public string? Street { get; set; }
    /// <summary>
    /// 邮编
    /// </summary>
    public string? Postcode { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 删除标志：0正常；1：删除
    /// </summary>
    public string? DelFlag { get; set; }
    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreateBy { get; set; }
    /// <summary>
    /// 关联生日隐藏
    /// </summary>
    public string? BirthdayHide { get; set; }
    /// <summary>
    /// 名片
    /// </summary>
    public string? Card { get; set; }
    /// <summary>
    /// 钉钉联系人ID
    /// </summary>
    public string? DingdingId { get; set; }
    /// <summary>
    /// 关联客户隐藏
    /// </summary>
    public string? CustomerHide { get; set; }
    /// <summary>
    /// 月日生日
    /// </summary>
    public string? BirthdayAnother { get; set; }
    /// <summary>
    /// 联系人的dataId
    /// </summary>
    public string? DataId { get; set; }
    /// <summary>
    /// 第二个电话
    /// </summary>
    public string? Tel2 { get; set; }
    /// <summary>
    /// 负责人
    /// </summary>
    public string? Principal { get; set; }
    /// <summary>
    /// 协同人
    /// </summary>
    public string? CollaborativePeople { get; set; }
    /// <summary>
    /// 详细地址
    /// </summary>
    public string? Address { get; set; }
}
