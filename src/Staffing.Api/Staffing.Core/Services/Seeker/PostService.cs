﻿using Config.Enums;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Model.Seeker.Post;
using Staffing.Core.Interfaces;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces.Seeker;
using Config;
using Microsoft.EntityFrameworkCore;
using Infrastructure.CommonService;
using Config.CommonModel.Business;
using Infrastructure.Proxy;

namespace Staffing.Core.Services.Seeker;

[Service(ServiceLifetime.Transient)]
public class PostService : IPostService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private ISharedService _sharedService;
    private ICommonService _commonService;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private CommonUserService _commonUserService;
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly CommonDicService _commonDicService;
    private readonly CommonCacheService _commonCacheService;
    private WeChatHelper _weChatHelper;
    private TencentMapHelper _tencentMapHelper;
    private readonly NoahData _noahData;
    private readonly QueueHelper _queueHelper;
    private readonly CommonPostService _commonPostService;
    public PostService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, ICommonService commonService,
        LogManager log, WeChatHelper weChatHelper, ISharedService sharedService,
        IDbContextFactory<StaffingContext> contextFactory, CommonUserService commonUserService,
        CommonDicService commonDicService, NoahData noahData, CommonCacheService commonCacheService,
        TencentMapHelper tencentMapHelper, QueueHelper queueHelper, CommonPostService commonPostService)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _commonService = commonService;
        _log = log;
        _weChatHelper = weChatHelper;
        _sharedService = sharedService;
        _contextFactory = contextFactory;
        _commonUserService = commonUserService;
        _commonDicService = commonDicService;
        _noahData = noahData;
        _commonCacheService = commonCacheService;
        _tencentMapHelper = tencentMapHelper;
        _queueHelper = queueHelper;
        _commonPostService = commonPostService;
    }

    public SeekerGetPostsResponse SeekerGetPosts(SeekerGetPosts model)
    {
        var result = _commonPostService.SeekerGetPosts(model);

        return result;
    }

    public async Task<SeekerGetPostInfo> SeekerGetPost(SeekerGetPost model)
    {
        var result = await _commonPostService.SeekerGetPost(model);

        return result;
    }
    public async Task<SeekerGetPostInfo?> SeekerGetESPost(string TeamPostId)
    {
        var result = await _commonPostService.SeekerGetESPost(TeamPostId);

        return result;
    }

    public List<string> PostHotKeywords()
    {
        // var aaa = await _tencentMapHelper.GetStaticMap("abc", 38.039672, 114.655839);
        // Console.Write(aaa);

        var cacheKey = $"{RedisKey.HrBehavior.Key}{_user.AdviserId}";
        // var result = MyRedisHelper.MyRedis.HGet<List<string>>(cacheKey, RedisKey.HrBehavior.PostHotKeywords);
        var result = MyRedis.Client.HGet<List<string>>(cacheKey, RedisKey.HrBehavior.PostHotKeywords);
        // MyRedisHelper.cli.Dispose();
        return result ?? new List<string>();
    }

    public List<ProjectIndustry> GetPostProjectIndustry()
    {
        var cacheKey = $"{RedisKey.HrBehavior.Key}{_user.AdviserId}";
        var result = MyRedis.Client.HGet<List<ProjectIndustry>>(cacheKey, RedisKey.HrBehavior.ProjectIndustry);
        return result ?? new List<ProjectIndustry>();
    }

    public GetAdviserCityResponse GetAdviserCity(GetAdviserCity model)
    {
        var result = new GetAdviserCityResponse();
        var cacheKey = $"{RedisKey.HrBehavior.Key}{_user.AdviserId}";
        var regionIds = MyRedis.Client.HGet<List<string>>(cacheKey, RedisKey.HrBehavior.PostCity);
        regionIds = regionIds ?? new List<string>();

        if (string.IsNullOrWhiteSpace(model.ParentId))
            regionIds = regionIds.Where(x => x.Count() >= 4).Select(s => s.Substring(0, 4)).Distinct().ToList();
        else
            regionIds = regionIds.Where(x => x.Count() > 4 && x.StartsWith(model.ParentId))
            .Distinct().ToList();

        var region = _commonDicService.GetRegion();
        result.Rows = region.Where(x => regionIds.Contains(x.Id))
        .OrderBy(o => o.Name)
        .Select(s => new GeneralDic
        {
            Id = s.Id,
            Name = s.Name
        }).ToList();

        return result;
    }

    public async Task<SeekerGetEntInfo> SeekerGetEnt(SeekerGetEnt model)
    {
        var result = _context.Agent_Ent.Where(x => x.AgentEntId == model.AgentEntId)
        .Select(s => new SeekerGetEntInfo
        {
            AgentEntId = s.AgentEntId,
            LogoUrl = s.Display == EntDisplayType.企业名称 ? s.LogoUrl : string.Empty,
            Name = s.Name,
            DisplayName = s.DisplayName,
            Abbr = s.Display == EntDisplayType.企业名称 ? s.Abbr : s.DisplayName,
            Display = s.Display,
            Nature = s.Nature,
            Industry = s.Industry.ToString(),
            Scale = s.Scale,
            Capital = s.Capital,
            Status = EnterpriseStatus.Active,
            Describe = s.Describe,
            RegionId = s.RegionId,
            Address = s.Address,
            Lat = s.Location.X,
            Lng = s.Location.Y,
            IndustryName = s.Dic_Industry.Name,
            UpdatedTime = s.UpdatedTime,
            Posts = _context.Post_Team.Count(x => x.Project_Team.HrId == _user.AdviserId
            && x.Post.Project.AgentEntId == model.AgentEntId && x.Show),
            CapitalName = s.Capital.GetDescription(),
            NatureName = s.Nature.GetDescription(),
            ScaleName = s.Scale.GetDescription()
        }).FirstOrDefault();

        if (result == null)
            throw new NotFoundException("内容不存在");

        result.NatureName = result.Nature.GetDescription();
        result.ScaleName = result.Scale.GetDescription();
        result.CapitalName = result.Capital.GetDescription();

        if (!string.IsNullOrWhiteSpace(result.RegionId))
            result.Region = _commonDicService.GetCityById(result.RegionId);

        var entBus = await _noahData.GetEnterpriseCache(result.Name);
        if (result.Display != EntDisplayType.匿名展示)
        {
            result.LegalPerson = entBus.LegalPerson;
        }
        result.Age = entBus.Age;
        result.Tags = entBus.Tags;
        result.RegisteredCapital = entBus.RegisteredCapital;
        result.RegisterDate = entBus.RegisterDate;

        return result;
    }

    public SeekerGetProjectsResponse SeekerGetProjects(SeekerGetProjects model)
    {
        var result = new SeekerGetProjectsResponse();

        var predicate = PredicateBuilder.New<Project_Team>(x => x.HrId == _user.AdviserId
        && x.Status == ProjectStatus.已上线 && x.Project.Status == ProjectStatus.已上线);

        predicate = predicate.And(x => x.Post_Team.Any(x => x.Show));

        if (model.ProjectIndustry.HasValue)
            predicate = predicate.And(x => x.Project.Industry == model.ProjectIndustry);

        if (model.Citys?.Count > 0)
        {
            var subPredicate = PredicateBuilder.New<Project_Team>(false);
            foreach (var item in model.Citys)
                subPredicate = subPredicate.Or(x => x.Project.Agent_Ent.RegionId.StartsWith(item));

            predicate = predicate.And(subPredicate);
        }

        var sql = _context.Project_Team.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new SeekerGetProjectInfo
        {
            TeamProjectId = s.TeamProjectId,
            Display = s.Project.Agent_Ent.Display,
            AgentEntId = s.Project.AgentEntId,
            Scale = s.Project.Agent_Ent.Scale,
            ScaleName = s.Project.Agent_Ent.Scale.GetDescription(),
            LogoUrl = s.Project.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Project.Agent_Ent.LogoUrl : string.Empty,
            DisplayName = s.Project.Agent_Ent.DisplayName,
            Abbr = s.Project.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Project.Agent_Ent.Abbr : s.Project.Agent_Ent.DisplayName,
            Nature = s.Project.Agent_Ent.Nature,
            NatureName = s.Project.Agent_Ent.Nature.GetDescription(),
            Industry = s.Project.Agent_Ent.Industry.ToString(),
            Capital = s.Project.Agent_Ent.Capital,
            RegionId = s.Project.Agent_Ent.RegionId,
            Address = s.Project.Agent_Ent.Address,
            Lat = s.Project.Agent_Ent.Location.X,
            Lng = s.Project.Agent_Ent.Location.Y,
            IndustryName = s.Project.Agent_Ent.Dic_Industry.Name,
            UpdatedTime = s.UpdatedTime,
            CapitalName = s.Project.Agent_Ent.Capital.GetDescription(),
            Posts = s.Post_Team.Count(c => c.Show),
            HotPost = s.Post_Team.Where(c => c.Show)
            .OrderByDescending(o => o.UpdatedTime).Select(s => s.Post.Dic_Post.Name).FirstOrDefault()
        }).ToList();

        foreach (var item in result.Rows)
        {
            if (!string.IsNullOrWhiteSpace(item.RegionId))
                item.Region = _commonDicService.GetCityById(item.RegionId);
        }

        return result;
    }

    public SeekerGetProjectIndustryInfo SeekerGetProjectIndustry(SeekerGetProjectIndustry model)
    {
        var result = new SeekerGetProjectIndustryInfo();

        if (!model.ProjectIndustry.HasValue)
            throw new BadRequestException("缺少行业");

        result.ProjectIndustry = model.ProjectIndustry.Value;
        result.ProjectIndustryName = result.ProjectIndustry.GetDescription();

        result.TalentAvatars = _commonCacheService.GetRandomTalentAvatars(_user.AdviserId);

        var cacheKey = $"{RedisKey.AdviserProjectIndustryCount}{_user.AdviserId}_{(int)model.ProjectIndustry}";
        var disable = 300;

        result.Post = _cacheHelper.GetRedisCache<int?>(() =>
        {
            return _context.Post_Team.Where(x => x.Project_Team.HrId == _user.AdviserId
            && x.Project_Team.Project.Industry == model.ProjectIndustry && x.Show)
            .Count();
        }, cacheKey, disable) ?? 0;

        if (string.IsNullOrWhiteSpace(_user.Id))
            result.Follow = false;
        else
            result.Follow = _context.User_Follow_Industry.Any(x => x.SeekerId == _user.Id
            && x.HrId == _user.AdviserId && x.Industry == model.ProjectIndustry);

        return result;
    }

    public HomePageThirdPartyDataResponse GetHomePageThirdPartyData()
    {
        var thirdPartyData = new HomePageThirdPartyDataResponse();

        var pthirdPartyDataCache = MyRedis.Client.HGetAll(RedisKey.HomePageTPData.Key);

        if (pthirdPartyDataCache == null)
            return thirdPartyData;

        if (pthirdPartyDataCache.TryGetValue(RedisKey.HomePageTPData.NuoPinPost, out var nuoPinPost))
            thirdPartyData.NuopinPostPanel = JsonSerializer.DeserializeFromString<NuopinPostPanel>(nuoPinPost);

        if (pthirdPartyDataCache.TryGetValue(RedisKey.HomePageTPData.NuoyoukaoProject, out var nuoyoukaoProject))
            thirdPartyData.NuoyoukaoProjectPanel = JsonSerializer.DeserializeFromString<NuoyoukaoProjectPanel>(nuoyoukaoProject);

        if (pthirdPartyDataCache.TryGetValue(RedisKey.HomePageTPData.NuopinAdvisers, out var nuopinAdvisers))
        {
            thirdPartyData.NuopinAdvisers = JsonSerializer.DeserializeFromString<List<NuopinAdvisersModel>>(nuopinAdvisers);
            foreach (var item in thirdPartyData.NuopinAdvisers ?? new List<NuopinAdvisersModel>())
            {
                item.Avatar = string.IsNullOrEmpty(item.Avatar) ? Constants.DefaultAvatar : item.Avatar;
            }
        }


        return thirdPartyData;
    }

    public async Task<GetSeekerShareQrCodeResponse> GetSeekerShareQrCode(GetSeekerShareQrCode model)
    {
        var result = new GetSeekerShareQrCodeResponse();

        var teamPost = _context.Post_Team.Where(x => x.TeamPostId == model.TeamPostId)
        .Select(s => new
        {
            s.Project_Team.HrId,
            AppletQrCode = s.AppletQrCode
        }).FirstOrDefault();

        if (teamPost == null)
            throw new BadRequestException("内容不存在");

        result.QrCode = teamPost.AppletQrCode;

        var appId = _config.WeChat!.SeekerApplet!.AppId!;

        //如果需要渠道二维码
        if (!string.IsNullOrWhiteSpace(model.ChannelId))
        {
            var cacheKey = Md5Helper.Md5($"{(int)AppletShareType.渠道商职位二维码}_{model.ChannelId}_{model.TeamPostId}_{appId}");
            var qrKey = MyRedis.Client.HGet<string?>(RedisKey.SeekerShareQrCodeId, cacheKey);
            if (string.IsNullOrWhiteSpace(qrKey))
            {
                qrKey = Tools.NextId();
                MyRedis.Client.HSet<string?>(RedisKey.SeekerShareQrCodeId, cacheKey, qrKey);
            }

            var qrInfo = MyRedis.Client.HGet<GetSeekerShareQrCodeInfo?>(RedisKey.SeekerShareQrCode, qrKey);

            if (qrInfo == null)
            {
                using var locker = await MyRedis.Lock($"tmjobqr:{qrKey}", 10, false, 100);
                if (locker == null)
                    throw new Exception("请求获取锁失败");

                var qrCode = await RetryHelper.Do(async () => await _weChatHelper.GetWxaCodeUnlimitForSharePosition(qrKey, AppletShareType.渠道商职位二维码, appId));
                qrInfo = new GetSeekerShareQrCodeInfo
                {
                    QrCode = qrCode,
                    ChannelId = model.ChannelId,
                    TeamPostId = model.TeamPostId,
                    HrId = teamPost.HrId
                };
                MyRedis.Client.HSet<GetSeekerShareQrCodeInfo?>(RedisKey.SeekerShareQrCode, qrKey, qrInfo);
            }

            result.QrCode = qrInfo.QrCode;

            if (result.QrCode == null)
                throw new BadRequestException("服务器繁忙，请稍后再试");
        }

        return result;
    }

    public GetAdviserCityResponse GetExcellentCity(GetAdviserCity model)
    {
        var result = new GetAdviserCityResponse();
        var regionIds = _cacheHelper.GetRedisCache<List<string>>(() =>
            {
                return _context.Post_Excellent.Where(x => x.Post_Team.Show)
                .Select(s => s.Post_Team.Post.RegionId).Distinct().ToList();
            }, RedisKey.ExcellentPost.Citys, 300);

        if (string.IsNullOrWhiteSpace(model.ParentId))
            regionIds = regionIds.Where(x => x.Count() >= 4).Select(s => s.Substring(0, 4)).Distinct().ToList();
        else
            regionIds = regionIds.Where(x => x.Count() > 4 && x.StartsWith(model.ParentId))
            .Distinct().ToList();

        var region = _commonDicService.GetRegion();
        result.Rows = region.Where(x => regionIds.Contains(x.Id))
        .OrderBy(o => o.Name)
        .Select(s => new GeneralDic
        {
            Id = s.Id,
            Name = s.Name
        }).ToList();

        return result;
    }
}