﻿using Config.CommonModel;
using Google.Protobuf.WellKnownTypes;
using Staffing.Entity.Staffing;
using Staffing.Model.Hr.Enterprise;

namespace Staffing.Core.Interfaces.Hr;

public interface IEntService
{
    /// <summary>
    /// 获取组织架构
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetEntOrgResponse GetEntOrg();

    /// <summary>
    /// 获取所有组织架构
    /// </summary>
    /// <returns></returns>
    GetDdOrgResponse GetDdOrg(GetDdOrg model);

    /// <summary>
    /// 创建组织架构
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    AddEntOrgResponse AddEntOrg(AddEntOrg model);

    /// <summary>
    /// 更新组织架构
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse UpdateEntOrg(UpdateEntOrg model);

    /// <summary>
    /// 删除组织架构
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse DeleteEntOrg(DeleteEntOrg model);

    /// <summary>
    /// 查询公司角色
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetEntRolesResponse GetEntRoles(GetEntRoles model);

    /// <summary>
    /// 创建角色
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    AddEntRoleResponse AddEntRole(AddEntRole model);

    /// <summary>
    /// 更新角色
    /// </summary>
    /// <param name="model"></param>
    EmptyResponse UpdateEntRole(UpdateEntRole model);

    /// <summary>
    /// 删除角色
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse DeleteEntRole(DeleteEntRole model);

    /// <summary>
    /// 公司人员列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetEntUsersResponse GetEntUsers(GetEntUsers model);

    /// <summary>
    /// 获取顾问列表(平台级)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetDdUsersResponse GetDdUsers(GetDdUsers model);

    /// <summary>
    /// 更新Hr
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse UpdateHr(UpdateHr model);

    /// <summary>
    /// 更新角色人员
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse UpdateUserRole(UpdateUserRole model);

    // /// <summary>
    // /// 从组织架构移除Hr
    // /// </summary>
    // /// <param name="model"></param>
    // /// <returns></returns>
    // EmptyResponse RemoveHrFromOrg(RemoveHrFromOrg model);

    /// <summary>
    /// 公司人员列表（全部，简版）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetEntHrLessResponse GetEntHrLess(GetEntHrLess model);

    GetEntResponse GetEnt(QuerySearch model);

    /// <summary>
    /// 获取企业商户信息
    /// </summary>
    /// <returns></returns>
    GetEnterpriseMerchantsResponse GetEnterpriseMerchants(GetEnterpriseMerchants model);
    /// <summary>
    /// 根据ID获取商户详情
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EnterpriseMerchantModel? GetEnterpriseMerchantDetail(GetEnterpriseMerchantDetail model);
    Task<EmptyResponse> UpdateMerchants(UpdateEnterpriseMerchantModel model);
}