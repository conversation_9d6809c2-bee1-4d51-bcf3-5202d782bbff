namespace Config.CommonModel.ESign;

/// <summary>
/// 创建个人签署账号
/// </summary>
public class CreateUserAccount
{
    /// <summary>
    /// 创建个人账号的唯一标识。可将个人证件号、手机号、邮箱地址等作为此账号的唯一标识。
    /// 注：
    /// （1）创建个人账号和机构账号时，个人账号的唯一标识和机构账号的唯一标识不可重复；
    /// （2）开发者需保证字段thirdPartyUserId在平台方业务系统不可重复。
    /// </summary>
    public string thirdPartyUserId { get; set; } = default!;

    /// <summary>
    /// 姓名
    /// </summary>
    public string name { get; set; } = default!;

    /// <summary>
    /// 证件类型，默认CRED_PSN_CH_IDCARD
    /// （1）CRED_PSN_CH_IDCARD 大陆身份证，默认值
    /// （2）CRED_PSN_CH_TWCARD 台湾来往大陆通行证
    /// （3）CRED_PSN_CH_MACAO 澳门来往大陆通行证
    /// （4）CRED_PSN_CH_HONGKONG 香港来往大陆通行证
    /// （5）CRED_PSN_PASSPORT 护照
    /// </summary>
    public string idType { get; set; } = "CRED_PSN_CH_IDCARD";

    /// <summary>
    /// 证件号，需传入真实存在的证件信息
    /// 注：
    /// 身份证中有X字母的，需要传入大写的X。
    /// 后续业务中e签宝会根据 idNumber 字段进行签署文件关联。
    /// </summary>
    public string idNumber { get; set; } = default!;

    /// <summary>
    /// 手机号码，可空。
    /// 注：手机号为空时，无法通过短信接收签署相关通知，
    /// 且签署过程无法使用短信验证码做意愿认证
    /// </summary>
    public string? mobile { get; set; }

    /// <summary>
    /// 邮箱地址，默认空
    /// 注：邮箱为空时，无法通过邮箱接收签署相关邮件通知
    /// </summary>
    public string? email { get; set; }
}

public class CreateUserAccountResponse
{
    /// <summary>
    /// 个人账号id
    /// </summary>
    public string? accountId { get; set; }
}

/// <summary>
/// 创建机构签署账号
/// </summary>
public class CreateOrgAccount
{
    /// <summary>
    /// 创建机构账号的唯一标识。可将企业证件号、企业邮箱地址等作为此账号的唯一标识。
    /// 注：
    /// （1）创建机构账号和个人账号时，机构账号的唯一标识和个人账号的唯一标识不可重复；
    /// （2）开发者需保证字段thirdPartyUserId在平台方业务系统不可重复。
    /// </summary>
    public string thirdPartyUserId { get; set; } = default!;

    /// <summary>
    /// 机构名称
    /// </summary>
    public string name { get; set; } = default!;

    /// <summary>
    /// 证件类型，默认CRED_ORG_USCC
    /// （1）CRED_ORG_USCC 统一社会信用代码
    /// （2）CRED_ORG_REGCODE 工商注册号
    /// </summary>
    public string idType { get; set; } = "CRED_ORG_USCC";

    /// <summary>
    /// 企业证件号，需传入真实存在的证件信息
    /// </summary>
    public string idNumber { get; set; } = default!;

    /// <summary>
    /// 可空 法定代表人证件号
    /// 注：法人证件号支持中国大陆身份证，台湾来往大陆通行证，港澳来往大陆通行证，护照这几种证件类型。但接口不做法人证件号格式校验。
    /// </summary>
    public string? orgLegalIdNumber { get; set; }

    /// <summary>
    /// 法定代表人名称
    /// </summary>
    public string? orgLegalName { get; set; }

    /// <summary>
    /// 自2021年11月15日起，此字段调整为非必填，建议不传此值。
    /// 创建人的个人账号id（调用个人账号创建获取到的accountId）
    /// </summary>
    public string? creator { get; set; }
}

public class CreateOrgAccountResponse
{
    /// <summary>
    /// 机构账号id
    /// </summary>
    public string? orgId { get; set; }
}

/// <summary>
/// 修改机构签署账号
/// </summary>
public class UpdateOrgAccount
{
    /// <summary>
    /// 机构签署账号ID，通过创建机构签署账号API获取
    /// </summary>
    public string orgId { get; set; } = default!;

    /// <summary>
    /// 机构名称，默认不变
    /// </summary>
    public string? name { get; set; }
}

public class UpdateOrgAccountResponse
{
    /// <summary>
    /// 机构签署账号ID
    /// </summary>
    public string? orgId { get; set; }
}

/// <summary>
/// 查询个人签署账号（通过accountId查询）
/// </summary>
public class GetUserAccount
{
    /// <summary>
    /// 个人账号id
    /// </summary>
    public string? accountId { get; set; }
}

public class GetUserAccountResponse : CreateUserAccount
{
    /// <summary>
    /// 个人账号id
    /// </summary>
    public string? accountId { get; set; }
}

/// <summary>
/// 查询机构签署账号（通过orgId查询）
/// </summary>
public class GetOrgAccount
{
    /// <summary>
    /// 机构账号id
    /// </summary>
    public string? orgId { get; set; }

    /// <summary>
    /// 创建机构账号的唯一标识
    /// </summary>
    public string? thirdPartyUserId { get; set; }
}

public class GetOrgAccountResponse : CreateOrgAccount
{
    /// <summary>
    /// 机构账号id
    /// </summary>
    public string? orgId { get; set; }
}

/// <summary>
/// 注销个人签署账号（通过accountId注销）
/// </summary>
public class DeleteUserAccount
{
    /// <summary>
    /// 个人账号id
    /// </summary>
    public string? accountId { get; set; }
}

/// <summary>
/// 注销机构签署账号（通过orgId注销）
/// </summary>
public class DeleteOrgAccount
{
    /// <summary>
    /// 机构账号id
    /// </summary>
    public string? orgId { get; set; }
}