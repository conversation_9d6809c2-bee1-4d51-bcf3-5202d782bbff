using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using Config;
using Config.CommonModel;
using Config.CommonModel.QYWechat;
using Config.Enums;
using EntityFrameworkCore.AutoHistory.Extensions;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.CommonService.Ndn;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Infrastructure.Proxy;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NPOI.HPSF;
using NPOI.SS.Formula.Functions;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using ServiceStack;
using Staffing.Core.Interfaces.Certificate;
using Staffing.Core.Interfaces.Common;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Model.Certificate;
using TencentCloud.Faceid.V20180301.Models;

namespace Staffing.Core.Services.Certificate;

[Service(ServiceLifetime.Transient)]
public class CertificateService : ICertificateService
{
    private readonly StaffingContext _context;
    private readonly RequestContext _user;
    private readonly CacheHelper _cacheHelper;
    private readonly ICommonService _commonService;
    private readonly LogManager _log;
    private readonly CommonDicService _commonDicService;
    private readonly CommonUserService _commonUserService;
    private readonly NdnApi _ndnApi;
    private readonly INdnService _ndnService;
    private readonly CommonCacheService _commonCacheService;
    private readonly TencentClient _tencentClient;
    private readonly WeChatHelper _weChatHelper;
    Noah.Aliyun.Storage.IObjectStorage _objectStorageService;

    public CertificateService(StaffingContext context,
        RequestContext user,
        CacheHelper cacheHelper,
        ICommonService commonService,
        LogManager log,
        CommonDicService commonDicService,
        CommonUserService commonUserService,
        NdnApi ndnApi,
        CommonCacheService commonCacheService,
        INdnService ndnService,
        TencentClient tencentClient,
        WeChatHelper weChatHelper,
        Noah.Aliyun.Storage.IObjectStorage objectStorageService)
    {
        _user = user;
        _context = context;
        _cacheHelper = cacheHelper;
        _commonService = commonService;
        _log = log;
        _commonDicService = commonDicService;
        _commonUserService = commonUserService;
        _ndnApi = ndnApi;
        _commonCacheService = commonCacheService;
        _ndnService = ndnService;
        _weChatHelper = weChatHelper;
        _tencentClient = tencentClient;
        _objectStorageService = objectStorageService;
    }

    public async Task<EmptyResponse> Register(CertificateRegistrationFormReq request)
    {
        var validationResults = new List<ValidationResult>();
        var validationContext = new ValidationContext(request);
        bool isValid = Validator.TryValidateObject(request, validationContext, validationResults, true);
        if (!isValid)
        {
            throw new BadRequestException(validationResults.FirstOrDefault().ErrorMessage);
        }

       var certificateRegistrationForm = _context.CertificateRegistrationForm.FirstOrDefault(w =>
            w.IdentityCardNumber == request.IdentityCardNumber && w.CertificateSpecsId == request.CertificateSpecsId &&
            w.Status == RegistrationformStatusEnum.待审核);
       if (certificateRegistrationForm!=null)
       {
           throw new BadRequestException("请勿重复报名！");
       }
        //实名认证，先查询本地库是否存在身份证，但是库里的身份证数据不更新，可能会有错误
        var userIdCard = _context.User
            .Where(x => x.IdentityCard == request.IdentityCardNumber)
            .Select(s => new
            {
                s.IdentityCard,
                s.IdentityCardName
            }).FirstOrDefault();
        //如果不存在，调用三方接口验证
        if (userIdCard == null)
        {
            var req = new IdCardVerificationRequest
            {
                IdCard = request.IdentityCardNumber,
                Name = request.Name
            };
            var rep = await _tencentClient.GetFaceidClient().IdCardVerification(req);
            if (!rep.Result.Equals("0"))
            {
                var msg = string.Empty;
                switch (rep.Result)
                {
                    case "-1":
                        msg = "姓名和身份证号不一致";
                        break;
                    case "-2":
                        msg = "非法身份证号";
                        break;
                    case "-3":
                        msg = "非法姓名";
                        break;
                    case "-5":
                        msg = "证件库中无此身份证记录";
                        break;
                    default:
                        msg = "实名服务暂不可用，请稍后再进行认证";
                        break;
                }

                throw new BadRequestException(msg);
            }
        }
        //如果存在读取本库验证
        else
        {
            if (!string.Equals(userIdCard.IdentityCardName, request.Name))
            {
                throw new BadRequestException("身份证姓名不正确");
            }
        }

        //如果pc端报名，设置推荐人为当前登录人
        if (request.Source == SourceEnum.pc)
        {
            request.RecommenderId = _user.Id;
            request.Recommender = _user.Name;
        }
        var certificateSpecs = _context.CertificateSpec.Include(certificateSpec => certificateSpec.Certificate).FirstOrDefault(x => x.Id == request.CertificateSpecsId);
        if (certificateSpecs == null)
        {
            throw new BadRequestException("证书规格不存在");
        }

        if (!string.IsNullOrWhiteSpace(request.TrainingBatch))
        {
            var certificateTrainingBatch = _context.CertificateTrainingBatch.FirstOrDefault(x => x.BatchName == request.TrainingBatch && x.CertificateId == certificateSpecs.CertificateId);
            if (certificateTrainingBatch == null)
            {
                throw new BadRequestException("培训批次不存在");
            }
        }
        CertificateRegistrationForm certificateRegistrationFormDTO = new()
        {
            CertificateId = certificateSpecs.CertificateId,
            CertificateSpecsId = certificateSpecs.Id,
            Name = request.Name,
            IdentityCardNumber = request.IdentityCardNumber,
            ContactNumber = request.ContactNumber,
            WorkUnit = request.WorkUnit,
            City = request.City,
            CityCode = request.CityCode,
            Gender = request.Gender,
            TrainingBatch = request.TrainingBatch,
            RegistrationProject = request.RegistrationProject,
            Recommender = request.Recommender,
            RecommenderId = request.RecommenderId!,
            Price = certificateSpecs.Price,
            RecommendCommission = certificateSpecs.RecommendCommission,
            Creator = request.Source == SourceEnum.wxapp ? _user.Id : null,
        };
        _context.Add(certificateRegistrationFormDTO);
        _context.SaveChanges();

        //为防止用户已进入企微群,不再触发入群事件，此处先增加验证是否入群的判断
        if (!string.IsNullOrEmpty(certificateSpecs.Certificate.EnterpriseWechatGroupId) && certificateSpecs.Certificate.EnterpriseWechatGroupId != "[]")
            MyRedis.Client.LPush(SubscriptionKey.QYWechatMemUpToCerRegs,
                    certificateSpecs.Certificate.EnterpriseWechatGroupId.FromJson<List<Config.CommonModel.QYWechat.EnterpriseWechatGroupId>>().Select(x =>
                           new QYWechat_MemChangeCacheInfo
                           {
                               //ChangeType = QYWechat_MemChangeType.入群,
                               //GroupChatID = x.GropChatId!,
                               UserID = _user.Id
                           }).ToArray());

        return new EmptyResponse();
    }


    public EmptyResponse SaveCertificate(CertificateModel request)
    {
        // 校验请求参数
        ValidateRequest(request);
        string? OldWeChatGroup = null, NewWeChatGroup = null;
        // 根据是否有ID决定是更新还是新增
        if (!string.IsNullOrWhiteSpace(request.Id))
        {
            (OldWeChatGroup, NewWeChatGroup) = UpdateExistingCertificate(request);
        }
        else
        {
            CreateNewCertificate(request);
        }

        // 保存变更
        _context.EnsureAutoHistory();
        _context.SaveChanges();

        UpdateCerWechatGroup(request.Id, OldWeChatGroup, NewWeChatGroup);
        return new EmptyResponse();
    }

    // 校验请求参数
    private void ValidateRequest(CertificateModel request)
    {
        var validationResults = new List<ValidationResult>();
        var validationContext = new ValidationContext(request);
        bool isValid = Validator.TryValidateObject(request, validationContext, validationResults, true);

        if (!isValid)
        {
            throw new BadRequestException(validationResults.FirstOrDefault()?.ErrorMessage);
        }
    }

    // 更新已有证书
    private (string? OldWeChatGroup, string? NewWeChatGroup) UpdateExistingCertificate(CertificateModel request)
    {
        var certificate = _context.CertificateTable.FirstOrDefault(x => x.Id == request.Id)
                          ?? throw new BadRequestException("证书不存在");

        if (certificate.Creator != _user.Id)
        {
            throw new BadRequestException("证书创建人和当前登录人不一致");
        }

        // 更新证书基本信息
        var upWeChatGroup = UpdateCertificateProperties(certificate, request);

        // 处理规格
        ProcessSpecs(request, certificate.Id);

        // 处理培训批次
        ProcessTrainingBatches(request, certificate.Id);
        return upWeChatGroup;
    }

    // 创建新证书
    private void CreateNewCertificate(CertificateModel request)
    {
        var certificateTable = new CertificateTable
        {
            CertificateType = request.CertificateType,
            CertificateName = request.CertificateName,
            CertificateCategory = request.CertificateCategory,
            ProductImage = request.ProductImage,
            EnterpriseWechatQrCode = request.EnterpriseWechatQrCode,
            EnterpriseWechatGroupId = request.EnterpriseWechatGroupId,
            UserNotice = request.UserNotice,
            RegistrationForm = request.RegistrationForm,
            JobPosition = request.JobPosition,
            RelevantMajors = request.RelevantMajors,
            IssuingAuthority = request.IssuingAuthority,
            QueryPlatform = request.QueryPlatform,
            EnterpriseGroupBuying = request.EnterpriseGroupBuying,
            SaleStartTime = request.SaleStartTime,
            TrainingBatchType = request.TrainingBatchType,
            TrainingTime = request.TrainingTime,
            DetailDescription = request.DetailDescription,
            Creator = _user.Id,
            EnterpriseWechatName = request.EnterpriseWechatName,
            EnterpriseWechatGroupName = request.EnterpriseWechatGroupName,
        };

        // 处理规格
        ProcessSpecs(request, certificateTable.Id);

        // 处理培训批次
        ProcessTrainingBatches(request, certificateTable.Id);

        _context.Add(certificateTable);
    }

    // 更新证书属性
    private (string? OldWeChatGroup, string? NewWeChatGroup) UpdateCertificateProperties(CertificateTable certificate, CertificateModel request)
    {
        string? OldWeChatGroup = certificate.EnterpriseWechatGroupName;

        certificate.CertificateType = request.CertificateType;
        certificate.CertificateName = request.CertificateName;
        certificate.CertificateCategory = request.CertificateCategory;
        certificate.ProductImage = request.ProductImage;
        certificate.EnterpriseWechatQrCode = request.EnterpriseWechatQrCode;
        certificate.EnterpriseWechatGroupId = request.EnterpriseWechatGroupId;
        certificate.UserNotice = request.UserNotice;
        certificate.RegistrationForm = request.RegistrationForm;
        certificate.JobPosition = request.JobPosition;
        certificate.RelevantMajors = request.RelevantMajors;
        certificate.IssuingAuthority = request.IssuingAuthority;
        certificate.QueryPlatform = request.QueryPlatform;
        certificate.EnterpriseGroupBuying = request.EnterpriseGroupBuying;
        certificate.SaleStartTime = request.SaleStartTime;
        certificate.TrainingBatchType = request.TrainingBatchType;
        certificate.TrainingTime = request.TrainingTime;
        certificate.DetailDescription = request.DetailDescription;
        certificate.UpdateTime = DateTime.Now;
        certificate.EnterpriseWechatName = request.EnterpriseWechatName;
        certificate.EnterpriseWechatGroupName = request.EnterpriseWechatGroupName;
        return (OldWeChatGroup, request.EnterpriseWechatGroupName);
    }

    // 处理证书规格
    private void ProcessSpecs(CertificateModel request, string certificateId)
    {
        // 校验规格名称是否重复
        ValidateUniqueNames(
            request.Specs.Select(x => x.SpecName),
            "证书规格名称不能重复"
        );
       
        foreach (var specModel in request.Specs)
        {
            specModel.StudyList = specModel.StudyList
                ?.Where(x => !string.IsNullOrWhiteSpace(x.Title) || !string.IsNullOrWhiteSpace(x.Url)).ToList();
            
            if (string.IsNullOrWhiteSpace(specModel.Id))
            {
                // 新增规格
                _context.CertificateSpec.Add(new CertificateSpec
                {
                    CertificateId = certificateId,
                    SpecName = specModel.SpecName,
                    Price = specModel.Price,
                    Stock = specModel.Stock,
                    SalesVolume = specModel.SalesVolume,
                    RecommendCommission = specModel.RecommendCommission,
                    SortOrder = specModel.SortOrder,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now,
                    StudyList = (specModel.StudyList == null || specModel.StudyList.Count == 0) ? null : specModel.StudyList,
                });
            }
            else
            {
                // 更新规格
                var spec = _context.CertificateSpec.FirstOrDefault(x => x.Id == specModel.Id)
                           ?? throw new BadRequestException("证书规格不存在");

                spec.SpecName = specModel.SpecName;
                spec.Price = specModel.Price;
                spec.Stock = specModel.Stock;
                spec.RecommendCommission = specModel.RecommendCommission;
                spec.SalesVolume = specModel.SalesVolume;
                spec.SortOrder = specModel.SortOrder;
                spec.UpdatedAt = DateTime.Now;
                spec.StudyList = (specModel.StudyList == null || specModel.StudyList.Count == 0) ? null : specModel.StudyList;
            }
        }
    }

    // 处理培训批次
    private void ProcessTrainingBatches(CertificateModel request, string certificateId)
    {
        // 如果培训类型是"无线下培训"且没有提供批次数据，则删除所有相关批次
        if (request.TrainingBatchType == TrainingBatchType.无线下培训 && 
            (request.TrainingBatch == null || request.TrainingBatch.Count == 0))
        {
            var batchesToDelete = _context.CertificateTrainingBatch
                .Where(x => x.CertificateId == certificateId)
                .ToList();
        
            _context.CertificateTrainingBatch.RemoveRange(batchesToDelete);
            return;
        }
        // 校验批次名称是否重复
        ValidateUniqueNames(
            request.TrainingBatch.Select(x => x.BatchName),
            "证书批次名称不能重复"
        );
        
        foreach (var batchReq in request.TrainingBatch)
        {
            if (string.IsNullOrWhiteSpace(batchReq.Id))
            {
                // 新增批次
                _context.Add(new CertificateTrainingBatch
                {
                    CertificateId = certificateId,
                    BatchName = batchReq.BatchName,
                    StartTime = batchReq.StartTime,
                    EndTime = batchReq.EndTime,
                    Description = batchReq.Description,
                    Address = batchReq.Address,
                    ContactPerson = batchReq.ContactPerson,
                    ContactPhone = batchReq.ContactPhone,
                });
            }
            else
            {
                // 更新批次
                var batch = _context.CertificateTrainingBatch.FirstOrDefault(x => x.Id == batchReq.Id)
                            ?? throw new BadRequestException("证书批次不存在");
                batch.BatchName = batchReq.BatchName;
                batch.StartTime = batchReq.StartTime;
                batch.EndTime = batchReq.EndTime;
                batch.Description = batchReq.Description;
                batch.Address = batchReq.Address;
                batch.ContactPerson = batchReq.ContactPerson;
                batch.ContactPhone = batchReq.ContactPhone;
                batch.UpdatedAt = DateTime.Now;
            }
        }
    }

    // 校验名称是否重复
    private void ValidateUniqueNames(IEnumerable<string> names, string errorMessage)
    {
        var nameSet = new HashSet<string>();
        foreach (var name in names)
        {
            if (!nameSet.Add(name))
            {
                throw new BadRequestException(errorMessage);
            }
        }
    }

    public CertificateModel GetCertificateDetail(string id,string? hrId)
    {
        CertificateModel result = new CertificateModel();
        var certificate = _context.CertificateTable.FirstOrDefault(x => x.Id == id);
        if (certificate == null)
        {
            throw new BadRequestException("证书不存在");
        }
 
        var userHr = _context.User_Hr.Include(userHr => userHr.Enterprise).Include(userHr => userHr.User)
            .FirstOrDefault(x => x.UserId == certificate.Creator);
        if (userHr == null)
        {
            throw new BadRequestException("证书创建人不存在");
        }

        result.CertificateType = certificate.CertificateType;
        result.CertificateName = certificate.CertificateName;
        result.CertificateCategory = certificate.CertificateCategory!;
        result.CertificateCategoryName = _context.DictData.FirstOrDefault(x =>
            x.DictValue == certificate.CertificateCategory && x.DictType == "CertificateCategory")?.DictLabel;
        result.ProductImage = certificate.ProductImage;
        result.EnterpriseWechatQrCode = certificate.EnterpriseWechatQrCode;
        result.EnterpriseWechatGroupId = certificate.EnterpriseWechatGroupId?.ToJsonString();
        result.UserNotice = certificate.UserNotice;
        result.RegistrationForm = certificate.RegistrationForm;
        result.JobPosition = certificate.JobPosition;
        result.RelevantMajors = certificate.RelevantMajors;
        result.IssuingAuthority = certificate.IssuingAuthority;
        result.QueryPlatform = certificate.QueryPlatform;
        result.EnterpriseGroupBuying = certificate.EnterpriseGroupBuying;
        result.SaleStartTime = certificate.SaleStartTime;
        result.TrainingBatchType = certificate.TrainingBatchType;
        result.TrainingTime = certificate.TrainingTime;
        result.DetailDescription = certificate.DetailDescription;
        result.Specs = _context.CertificateSpec.OrderBy(o=>o.Price).Where(x => x.CertificateId == id).Select(s => new CertificateSpecModel
        {
            Id = s.Id,
            CertificateId = s.CertificateId,
            SpecName = s.SpecName,
            Price = s.Price,
            Stock = s.Stock,
            RecommendCommission = s.RecommendCommission,
            SalesVolume = s.SalesVolume,
            SortOrder = s.SortOrder,
            StudyList = s.StudyList,
            
        }).ToList();
        result.Creator = certificate.Creator;
        result.CreateTime = certificate.CreateTime;
        result.HrName = userHr.NickName;
        result.HrAvatar = userHr.Avatar;
        result.HrEntName = userHr.Enterprise.Name;
        result.TencentImId = userHr.TencentImId;
        result.HrEntWeChatQrCode = userHr.EntWeChatQrCode;
        result.HrPhone = userHr.User.Mobile;
        result.CertificateTypeName = certificate.CertificateType.GetDescription();
        result.TrainingBatch = _context.CertificateTrainingBatch.Where(x => x.CertificateId == id).Select(s =>
            new CertificateTrainingBatchReq
            {
                Id = s.Id,
                BatchName = s.BatchName,
                Address = s.Address,
                StartTime = s.StartTime,
                EndTime = s.EndTime,
                Description = s.Description,
                ContactPerson = s.ContactPerson,
                ContactPhone = s.ContactPhone,
            }).ToList();
        result.EnterpriseWechatName = certificate.EnterpriseWechatName;
        result.EnterpriseWechatGroupId = certificate.EnterpriseWechatGroupId?.ToJsonString();
        result.EnterpriseWechatGroupName = certificate.EnterpriseWechatGroupName;
        result.IssuingAuthorityName = _context.DictData.FirstOrDefault(x =>
            x.DictValue == certificate.IssuingAuthority && x.DictType == "CertificationAuthority")?.DictLabel;

        if (!string.IsNullOrWhiteSpace(hrId))
        {
            var hrModel = _context.User_Hr.Include(hrModel => hrModel.Enterprise).Include(hrModel => hrModel.User)
                .Include(hrModel => hrModel.User_Extend)
                .FirstOrDefault(x => x.UserId == hrId);
            if (hrModel == null)
            {
                throw new BadRequestException("证书顾问不存在");
            }
            result.HrName = hrModel.NickName;
            result.HrAvatar = hrModel.Avatar;
            result.HrEntName = hrModel.Enterprise.Name;
            result.HrEntWeChatQrCode = hrModel.EntWeChatQrCode;
            result.OnlineStatus = Tools.GetOnlineStatus(hrModel.User_Extend.LoginTime);
        }
        return result;
    }

    public EmptyResponse DeleteCertificate(DeleteCertificateReq req)
    {
        var certificate = _context.CertificateTable.FirstOrDefault(x => x.Id == req.Id);
        if (certificate == null)
        {
            throw new BadRequestException("证书不存在");
        }

        if (certificate.Creator != _user.Id)
        {
            throw new BadRequestException("证书创建人和当前登录人不一致");
        }

        certificate.Deleted = true;
        _context.SaveChanges();
        return new EmptyResponse();
    }

    public CertificateHallResp GetCertificateHall(CertificateHallReq req)
    {
        var result = new CertificateHallResp();
        var predicate = PredicateBuilder.New<CertificateTable>(true);
        predicate = predicate.And(x => x.SaleStartTime == null || x.SaleStartTime.Value <= DateTime.Now);
        if (req.CertificateType != null)
        {
            predicate = predicate.And(x => x.CertificateType == req.CertificateType);
        }

        if (!string.IsNullOrWhiteSpace(req.CertificateName))
        {
            predicate = predicate.And(x => x.CertificateName.Contains(req.CertificateName));
        }

        var sql = _context.CertificateTable.Where(predicate);
        result.Total = sql.Select(s => new { a = 1 }).Count();
        result.Rows = sql
            .OrderByDescending(p => p.CreateTime)
            .Skip((req.PageIndex - 1) * req.PageSize)
            .Take(req.PageSize)
            .Include(c => c.Specs)
            .Include(c => c.TrainingBatch)
            .Select(    
                s => new CertificateHallModel
                {
                    Id = s.Id,
                    CertificateType = s.CertificateType,
                    CertificateName = s.CertificateName,
                    CertificateTypeName = s.CertificateType.GetDescription(),
                    JobPosition = s.JobPosition,
                    RelevantMajors = s.RelevantMajors,
                    IssuingAuthority = s.IssuingAuthority,
                    QueryPlatform = s.QueryPlatform,
                    RegistrationForm = s.RegistrationForm,
                    Specs = s.Specs.Select(a => new CertificateSpecModel
                    {
                        Id = a.Id,
                        CertificateId = a.CertificateId,
                        SpecName = a.SpecName,
                        Price = a.Price,
                        Stock = a.Stock,
                        RecommendCommission = a.RecommendCommission,
                        SortOrder = a.SortOrder
                    }).ToList(),
                    TrainingBatch = s.TrainingBatch.Select(batch => new CertificateTrainingBatchResp
                    {
                        Id = s.Id,
                        BatchName = batch.BatchName,
                        Address = batch.Address,
                        StartTime = batch.StartTime,
                        EndTime = batch.EndTime,
                        Description = batch.Description,
                        ContactPerson = batch.ContactPerson,
                        ContactPhone = batch.ContactPhone,
                    }).ToList(),
                    HrName = s.UserHr.NickName,
                    HrAvatar = s.UserHr.Avatar,
                    HrEntName = s.UserHr.Enterprise.Name,
                    HrEntWeChatQrCode = s.UserHr.EntWeChatQrCode,
                    TencentImId = s.UserHr.TencentImId,
                    ProductImage = s.ProductImage,
                    TrainingBatchType = s.TrainingBatchType,
                    Creator = s.Creator,
                }
            )
            .ToList();
        var dictDataMap = _context.DictData.Where(w => w.DictType == "CertificationAuthority").Select(s => new DictData()
        {
            DictValue = s.DictValue,
            DictLabel = s.DictLabel,
        }).ToDictionary(
            k => k.DictValue,
            v => v.DictLabel
        ) ?? new Dictionary<string, string>();
        foreach (var item in result.Rows)
        {
            item.IssuingAuthorityName = dictDataMap.TryGetValue(item.IssuingAuthority, out var value) ? value : item.IssuingAuthority;
            item.Specs = item.Specs?.OrderBy(o => o.Price).ToList();
        }
        return result;
    }

    public async Task<GenerateQRcodeResp> GenerateQRcode(GenerateQRcodeReq req)
    {
        return await GenerateQRcode(req.CertificateId, _user.Id);
    }
    private async Task<GenerateQRcodeResp> GenerateQRcode(string? CertificateId,string AdviserID)
    {

        var safeSign = Tools.MakeSign(AdviserID, CertificateId);
        const string redisKey = "CertificateQrcode";

        // 尝试从Redis获取现有QR码 
        var qrcodeJson = await MyRedis.Client.HGetAsync<string>(redisKey, $"{(int)AppletShareType.证书PC}_{safeSign}");

        QrCodeData qrcodeData;
        if (string.IsNullOrWhiteSpace(qrcodeJson))
        {
            // 生成新的QR码
            var url = await RetryHelper.Do(async () =>
                await _weChatHelper.GetWxaCodeUnlimit(
                    ClientApps.SeekerApplet.AppID,
                    $"{(int)AppletShareType.证书PC}_{safeSign}",
                    "Mypackagedetail/certificate/certificatedetail", true));

            qrcodeData = new QrCodeData(AdviserID, CertificateId, url);

            // 存储到Redis
            await MyRedis.Client.HSetAsync(
                redisKey,
                $"{(int)AppletShareType.证书PC}_{safeSign}",
                JsonSerializer.Serialize(qrcodeData));
        }
        else
        {
            // 使用现有的QR码
            qrcodeData = JsonSerializer.Deserialize<QrCodeData>(qrcodeJson)
                         ?? throw new InvalidOperationException("Invalid QR code data in Redis");
        }

        // 返回结果
        return new GenerateQRcodeResp
        {
            url = qrcodeData.Url,
            userName = _user.Name
        };
    }

    /// <summary>
    /// 证书关联企微群变更时，更新报名用户最新加入的群聊
    /// </summary>
    /// <param name="CertificateId"></param>
    /// <param name="WeChatGroup"></param>
    /// <param name="NewWeChatGroup"></param>
    private void UpdateCerWechatGroup(string? CertificateId, string? WeChatGroup, string? NewWeChatGroup)
    {
        if (string.IsNullOrEmpty(CertificateId))
            return;
        //判断证书是否有变化
        if (WeChatGroup == NewWeChatGroup)
            return;

        var userIds = _context.CertificateRegistrationForm.AsNoTracking()
            .Where(x => x.CertificateId == CertificateId && !string.IsNullOrEmpty(x.Creator))
            .Select(x => x.Creator)
            .ToList();
        if (userIds.Count == 0)
            return;
        //证书群变更时，需重新筛选当前证书报名记录中 用户最新进入的企微群(由于查看用户变更记录时仅与用户ID有关，不关注群ID，因此仅将UserID写入缓存即可)
        MyRedis.Client.LPush(SubscriptionKey.QYWechatMemUpToCerRegs,
                userIds.Select(x =>
                       new QYWechat_MemChangeCacheInfo
                       {
                           //ChangeType = QYWechat_MemChangeType.入群,
                           //GroupChatID = string.Empty,
                           UserID = x
                       }).ToArray());
    }

    /// <summary>
    /// 获取推广图片URL
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<GeneratePromotionResp> GetPromotionImgUrl(CertificatePromotionReq request, string ossDir)
    {
        var safeSign = Tools.MakeSign(request.AdviserID, request.CertificateID);
        string redisKey = $"CertificatePromotion:{DateTime.Now:yyyyMMdd}";

        // 尝试从Redis获取现有推广图
        var proJson = await MyRedis.Client.HGetAsync<string>(redisKey, safeSign);

        string qrcodeData;
        if (string.IsNullOrWhiteSpace(proJson))
        {
            // 生成新的推广图
            var url = await RetryHelper.Do(async () =>
                              await GetPromotionImgUrlFun(request, ossDir));

            qrcodeData = url;

            // 存储到Redis
            await MyRedis.Client.HSetAsync(redisKey, safeSign, qrcodeData);
        }
        else // 使用现有推广图
            qrcodeData = proJson;

        // 返回结果
        return new GeneratePromotionResp { url = qrcodeData, thumbnailUrl = qrcodeData + (qrcodeData.IndexOf("?") == -1 ? "?" : "&") + "x-oss-process=style/501xn" };
    }
    private async Task<string> GetPromotionImgUrlFun(CertificatePromotionReq request,string ossDir)
    {
        //获取推广的证书详情页小程序二维码
        var qrResult =await GenerateQRcode(request.CertificateID, request.AdviserID);
        //获取顾问手机号
        var hr = _context.User.Where(x => x.UserId == request.AdviserID).FirstOrDefault();
        var AdviserMobile = hr?.Mobile ?? string.Empty;

        var imgConfig = ImgHelper._Certificate_QWImgConfigConfig;

        //组装拼接图片URL及位置
        var Overlay = new List<ImgHelper_ImageOverlay2>
                    {
                        new(qrResult.url, imgConfig.QYWechatPosition_x, imgConfig.QYWechatPosition_y, imgConfig.QYWechatSize)
                    };
        //组装文本
        string FontPath = @".\wwwroot\Certificate\msyh.ttf";
        var color = SixLabors.ImageSharp.Color.White;
        SixLabors.Fonts.FontStyle fontStyle = SixLabors.Fonts.FontStyle.Bold;
        List<ImgHelper_TextLayer> textLayers = [
            new ImgHelper_TextLayer
                {
                    Text = $"*咨询电话 {AdviserMobile.FormatPhoneNumber()}*",
                    FontPath = FontPath,
                    FontSize = imgConfig.Mobile_FontSize,
                    Color = color,
                    FontStyle = fontStyle,
                    Position = new SixLabors.ImageSharp.PointF(imgConfig.Mobile_PositionX, imgConfig.Mobile_PositionY)
                }
        ];

        //合并图片
        var stream = await ImgHelper.MergeImagesAsync(imgConfig.TemplatePath ?? @".\wwwroot\Certificate\qw_Certificate2.jpg", Overlay, textLayers);

        // 上传到OSS，并返回生成的图片URL
        string newName = $"{request.AdviserID}.jpg";
        string ossUrl = await _objectStorageService.OrdinaryUploadFile(stream, newName, newName, $"{ossDir}/Certificate/promotionimg/{request.CertificateID}");
        stream.Close();
        stream.Dispose();
        return ossUrl + "?timestamp=" + DateTime.Now.Ticks;
    }

    /// <summary>
    /// 保存学习记录
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public void SaveStudyRecord(string StudyUrl, string StudyName,
        string CertificateId, string CertificateName,
        string SpecID, string SpecName)
    {
        var isExists = _context.CertificateStudyRecord.Any(w =>
             w.UserId == _user.Id && w.StudyUrl == StudyUrl &&
             w.CertificateId == CertificateId && w.SpecID == SpecID);
        if (isExists)
            return;

        CertificateStudyRecord model = new()
        {
            UserId = _user.Id,
            UserMobile = _user.Account ?? string.Empty,
            StudyUrl = StudyUrl,
            StudyName = StudyName ?? string.Empty,
            CertificateId = CertificateId,
            SpecID = SpecID,
            CertificateName = CertificateName ?? string.Empty,
            SpecName = SpecName ?? string.Empty,
            CreateTime = DateTime.Now
        };
        _context.Add(model);
        _context.SaveChanges();
    }
}