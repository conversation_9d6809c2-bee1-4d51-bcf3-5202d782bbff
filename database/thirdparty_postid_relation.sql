/*
 Navicat Premium Data Transfer

 Source Server         : 诺聘**************（测试环境）
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : **************:3306
 Source Schema         : staffing

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 07/03/2025 18:32:47
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for thirdparty_postid_relation
-- ----------------------------
DROP TABLE IF EXISTS `thirdparty_postid_relation`;
CREATE TABLE `thirdparty_postid_relation`  (
  `id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `PostId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '诺聘职位ID',
  `ThirdPostId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '第三方职位ID',
  `Status` int NULL DEFAULT NULL COMMENT '状态  1：上架 2：更新 3：下架',
  `UpdatedTime` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `CreatedTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Source` int NOT NULL COMMENT '岗位来源 1：职多多 2：58魔方 3：后续',
  `FanfeiStatus` int NOT NULL COMMENT '返费是否发生过变化 0：不变 1：变化',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `zddid_pk`(`id` ASC) USING BTREE COMMENT '唯一索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '第三方岗位id与诺快聘岗位id关联表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
