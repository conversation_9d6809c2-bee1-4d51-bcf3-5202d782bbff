﻿using Config.CommonModel;
using Config.Enums;
using Staffing.Entity.Staffing;
using Staffing.Model.Hr.Recruit;
using System.ComponentModel.DataAnnotations;

namespace Staffing.Model.Hr.Bounty;

public class GetTeamBountys : QueryRequest
{
    /// <summary>
    /// 协同项目Id
    /// </summary>
    public string? TeamProjectId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public BountyStatus? Status { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public HrProjectSource? Source { get; set; }

    /// <summary>
    /// 求职者手机
    /// </summary>
    public string? SeekerMobile { get; set; }

    /// <summary>
    /// 求职者名字
    /// </summary>
    public string? SeekerName { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 协同Hr名字
    /// </summary>
    public string? TeamHrName { get; set; }

    /// <summary>
    /// 协同Hr手机
    /// </summary>
    public string? TeamHrMobile { get; set; }

    /// <summary>
    /// 协同Hr公司
    /// </summary>
    public string? TeamHrEntName { get; set; }
}

public class GetTeamBountysResponse : QueryResponse
{
    public List<GetTeamBountyDetail> Rows { get; set; } = default!;
}

public class GetTeamBountyDetail
{
    /// <summary>
    /// 招聘流程Id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// 投递Id
    /// </summary>
    public string? DeliveryId { get; set; }

    /// <summary>
    /// 主创HrId
    /// </summary>
    public string? HrId { get; set; } = default!;

    /// <summary>
    /// 协同HrId
    /// </summary>
    public string? TeamHrId { get; set; } = default!;

    /// <summary>
    /// 协同Hr名字
    /// </summary>
    public string? TeamHrName { get; set; }

    /// <summary>
    /// 协同Hr手机
    /// </summary>
    public string? TeamHrMobile { get; set; }

    /// <summary>
    /// 协同Hr公司名字
    /// </summary>
    public string? TeamHrEntName { get; set; }

    /// <summary>
    /// 求职者Id
    /// </summary>
    public string? SeekerId { get; set; }

    /// <summary>
    /// 求职者手机
    /// </summary>
    public string? SeekerMobile { get; set; }

    /// <summary>
    /// 求职者名字
    /// </summary>
    public string? SeekerName { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 职位类别名称
    /// </summary>
    public string? PostCategoryName { get; set; }

    /// <summary>
    /// 投递时间
    /// </summary>
    public DateTime? DeliveryTime { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// Hr职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 原始职位Id
    /// </summary>
    public string? PostId { get; set; }

    /// <summary>
    /// 合作模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }

    /// <summary>
    /// 合作模式
    /// </summary>
    public string? PaymentNodeName { get; set; }

    /// <summary>
    /// 打款天数
    /// </summary>
    public int? PaymentDays { get; set; }

    /// <summary>
    /// 打款方式
    /// </summary>
    public ProjectPaymentType? PaymentType { get; set; }

    /// <summary>
    /// 打款方式
    /// </summary>
    public string? PaymentTypeName { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Money { get; set; }

    // /// <summary>
    // /// 结算金额
    // /// </summary>
    // public decimal? SettlementMoney { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public BountyStatus Status { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public HrProjectSource? Source { get; set; }

    /// <summary>
    /// 来源名称
    /// </summary>
    public string? SourceName { get; set; }

    /// <summary>
    /// 入职时间
    /// </summary>
    public DateTime? InductionTime { get; set; }

    /// <summary>
    /// 招聘流程状态
    /// </summary>
    public RecruitStatus RecruitStatus { get; set; }

    /// <summary>
    /// 预估结算时间
    /// </summary>
    public DateTime? EstimatedSettlementTime { get; set; }

    /// <summary>
    /// 实际结算时间
    /// </summary>
    public DateTime? SettlementTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; } = DateTime.Now;
}

public class ExportTeamBountys
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }
}

public class ExportTeamBountysResponse
{
    public List<ExportTeamBountyDetail> Rows { get; set; } = default!;

    public string? ProjectName { get; set; }
}

public class ExportTeamBountyDetail : GetTeamBountyDetail
{
    /// <summary>
    /// 招聘流程状态
    /// </summary>
    public string? RecruitStatusName { get; set; }

    public new string? DeliveryTime { get; set; }

    public new string? EstimatedSettlementTime { get; set; }
}

#region 新结算单模型

public class NewTeamBountyRequest : QueryRequest
{
    /// <summary>
    /// 项目id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 职位Id
    /// </summary>
    public string? PostId { get; set; }

    /// <summary>
    /// 候选人姓名
    /// </summary>
    public string? SeekerName { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string? SeekerMobile { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public BountyStatus? Status { get; set; }

    /// <summary>
    /// 交付人
    /// </summary>
    public string? ShareName { get; set; }

    /// <summary>
    /// 投递职位
    /// </summary>
    public string? DeliveryPostName { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public string? BeginDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public string? EndDate { get; set; }

    /// <summary>
    /// 结算状态
    /// </summary>
    public SettlementType? SettlementType { get; set; }

    /// <summary>
    /// 交付来源
    /// </summary>
    public TeamBountySource? TeamBountySource { get; set; }
}

public class NewTeamBounryResponse : QueryResponse
{
    /// <summary>
    /// 列表数据
    /// </summary>
    public List<NewTeamBountyPageList> Rows { get; set; } = default!;

    /// <summary>
    /// 角色类型 0-主创，1-协同
    /// </summary>
    public int UserType { get; set; } = 0;
}

public class NewTeamBountyPageList
{
    /// <summary>
    /// 候选人
    /// </summary>
    public string? SeekerName { get; set; } = string.Empty;

    /// <summary>
    /// 联系电话
    /// </summary>
    public string? SeekerMobile { get; set; } = string.Empty;

    /// <summary>
    /// 交付项目
    /// </summary>
    public string ProjectHame { get; set; } = string.Empty;

    /// <summary>
    /// 项目编码
    /// </summary>
    public string ProjectNo { get; set; } = string.Empty;

    /// <summary>
    /// 候选人来源 - 交付来源
    /// </summary>
    public TeamBountySource? TeamBountySource { get; set; }

    /// <summary>
    /// 候选人来源名称
    /// </summary>
    public string? TeamBountySourceName { get; set; }

    /// <summary>
    /// 投递职位
    /// </summary>
    public string PostName { get; set; } = string.Empty;

    /// <summary>
    /// 交付佣金
    /// </summary>
    public decimal? Money { get; set; }

    /// <summary>
    /// 单位 /份 /人 /人/天
    /// </summary>
    public string? UnitName { get; set; }

    /// <summary>
    /// 渠道佣金
    /// </summary>
    public decimal? ChannelMoney { get; set; }

    /// <summary>
    /// 投递时间
    /// </summary>
    public DateTime DeliveryTime { get; set; }

    /// <summary>
    /// 平台服务费
    /// </summary>
    public decimal? PlatformSettlementMoney { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public BountyStatus Status { get; set; }

    /// <summary>
    /// 订单状态名称
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 交付人
    /// </summary>
    public string? ShareName { get; set; } = string.Empty;

    /// <summary>
    /// 交付关系
    /// </summary>

    public string? ShareRelation { get; set; } = string.Empty;

    /// <summary>
    /// 倒计时结束时间，格式：时:分:秒，为空则显示：未开始；9999-12-31，则显示已结束
    /// 倒计时 = 倒计时结束时间 - 当前时间
    /// </summary>
    public string? EndCountTime { get; set; }

    /// <summary>
    /// 结算状态
    /// </summary>
    public SettlementType SettlementStatus { get; set; }

    /// <summary>
    /// 结算状态名称
    /// </summary>
    public string? SettlementStatusName { get; set; }

    /// <summary>
    /// 合作模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// 是否减库存，默认0-否，1-是
    /// </summary>
    public int IfStockOut { get; set; } = 0;

    /// <summary>
    /// 打款天数
    /// </summary>
    public int? PaymentDays { get; set; }
}

#endregion

#region 结算中心模型

/// <summary>
/// 交付订单请求模型
/// </summary>
public class CenterOrderListRequest : QueryRequest
{
    /// <summary>
    /// 候选人姓名
    /// </summary>
    public string? SeekerName { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string? SeekerMobile { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public BountyStatus? Status { get; set; }

    /// <summary>
    /// 交付人
    /// </summary>
    public string? ShareName { get; set; }

    /// <summary>
    /// 投递职位
    /// </summary>
    public string? DeliveryPostName { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public string? BeginDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public string? EndDate { get; set; }

    /// <summary>
    /// 结算状态
    /// </summary>
    public SettleStatus? SettleStatus { get; set; }

    /// <summary>
    /// 简历来源
    /// </summary>
    public TeamBountySource? TeamBountySource { get; set; }

    /// <summary>
    /// 交付来源
    /// </summary>
    public BountySource? BountySource { get; set; }

    /// <summary>
    /// 交付项目：项目名称或编码
    /// </summary>
    public string? ProjectSearch { get; set; }
}

/// <summary>
/// 交付订单响应模型
/// </summary>
public class CenterOrderDetailResponse : QueryResponse
{
    /// <summary>
    /// 列表数据
    /// </summary>
    public List<OrderDetailPageList> Rows { get; set; } = default!;
}

public class OrderDetailPageListResponse : QueryResponse
{
    public List<OrderDetailPageList> Rows { get; set; } = default!;
}

/// <summary>
/// 结算单明细列表
/// </summary>
public class OrderDetailPageList
{
    /// <summary>
    /// 交付人userid
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 交付人
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// 工号
    /// </summary>
    public string? UserNo { get; set; }

    /// <summary>
    /// 发放形式
    /// </summary>
    public string? SettlementTypeName { get; set; }

    /// <summary>
    /// 应结算金额
    /// </summary>
    public decimal? PayableSettlementMoney { get; set; }

    /// <summary>
    /// 已结算金额
    /// </summary>
    public decimal? ActualSettlementMoney { get; set; }

    /// <summary>
    /// 结算状态
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 交付订单号
    /// </summary>
    public string OrderId { get; set; } = default!;
}

/// <summary>
/// 明细请求模型
/// </summary>
public class OrderDetailRequst : QueryRequest
{
    public string ProjectId { get; set; } = default!;

    public RequestType Type { get; set; } = default;
}

public class BountyAccountingRequst : QueryRequest
{
    /// <summary>
    /// 招聘流程Id
    /// </summary>
    public string? RecruitId { get; set; } = default!;
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 岗位Id
    /// </summary>
    public string? PostId { get; set; }

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 根据项目、签约主体、求职者、销售人员、线索提供人、约面人、项目经理搜索
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 求职者姓名
    /// </summary>
    public string? SeekerName { get; set; }

    /// <summary>
    /// 销售人员
    /// </summary>
    public string? SaleName { get; set; }

    /// <summary>
    /// 项目经理
    /// </summary>
    public string? ManagerName { get; set; }

    /// <summary>
    /// 合作模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }
    /// <summary>
    /// 奖励时效类型（单次结算, 长期结算）
    /// </summary>
    public PostRewardType? RewardType { get; set; }

    /// <summary>
    /// 审核的角色
    /// </summary>
    public PostSettleApprovalRole? Role { get; set; }
    /// <summary>
    /// 是否线索大厅(0否  1是)
    /// </summary>
    public int? IsClueHall { get; set; }

    /// <summary>
    /// 审核状态
    /// </summary>
    public PostSettleApprovalStatus? ApprovalStatus { get; set; }
}

public class BountyAccounting
{
    /// <summary>
    /// 结算订单Id
    /// </summary>
    public required string SettlementId { get; set; }
    /// <summary>
    /// 项目Id
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 签约主体Id
    /// </summary>
    public string? BookCode { get; set; } = default!;

    /// <summary>
    /// 签约主体名称
    /// </summary>
    public string? BookName { get; set; }

    /// <summary>
    /// 岗位Id
    /// </summary>
    public string PostId { get; set; } = default!;

    /// <summary>
    /// 订单状态
    /// </summary>
    public BountyStatus Status { get; set; }

    /// <summary>
    /// 订单状态名称
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 销售经理Id
    /// </summary>
    public string SaleId { get; set; } = default!;

    /// <summary>
    /// 销售经理Id
    /// </summary>
    public string? SaleName { get; set; }

    /// <summary>
    /// 求职者Id
    /// </summary>
    public string? SeekerId { get; set; }

    /// <summary>
    /// 求职者名称
    /// </summary>
    public string? SeekerName { get; set; }

    /// <summary>
    /// 邀面方Id
    /// </summary>
    public string? FollowerId { get; set; }

    /// <summary>
    /// 邀面方名称
    /// </summary>
    public string? FollowerName { get; set; }

    /// <summary>
    /// 销售佣金
    /// </summary>
    public decimal SalesBounty { get; set; }

    /// <summary>
    /// 项目经理Id
    /// </summary>
    public string PmId { get; set; } = default!;

    ///<summary>
    /// 项目经理Id
    /// </summary>
    public string? PmName { get; set; }

    /// <summary>
    /// 项目管理佣金
    /// </summary>
    public decimal ManagerBounty { get; set; }

    /// <summary>
    ///  求职者电话
    /// </summary>
    public string? SeekerMobile { get; set; }

    /// <summary>
    /// 投递职位
    /// </summary>
    public string PostName { get; set; } = string.Empty;

    /// <summary>
    /// 投递时间
    /// </summary>
    public DateTime DeliveryTime { get; set; }

    /// <summary>
    /// 线索方Id
    /// </summary>
    public string? ClueId { get; set; }

    /// <summary>
    /// 线索方名称
    /// </summary>
    public string? ClueName { get; set; }

    /// <summary>
    /// 线索佣金
    /// </summary>
    public decimal ClueBounty { get; set; }

    /// <summary>
    /// 邀面佣金(邀面)
    /// </summary>
    public decimal FollowerBounty { get; set; }

    /// <summary>
    ///  平台方
    /// </summary>
    public string? PlatformName { get; set; } = string.Empty;

    /// <summary>
    /// 平台佣金
    /// </summary>
    public decimal PlatformBounty { get; set; }

    /// <summary>
    /// 交付金额（线索+邀面）
    /// </summary>
    public decimal? DeliveryBounty { get; set; }
    /// <summary>
    /// 计算金额
    /// </summary>
    public decimal SettlementMoney { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }
    /// <summary>
    /// 结算开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结算截止时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    #region  RecruitInfo实体部分信息
    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }
    /// <summary>
    /// 性别解释
    /// </summary>
    public string? SexName { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public OccupationType? Occupation { get; set; }

    /// <summary>
    /// 职业解释
    /// </summary>
    public string? OccupationName { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 地区id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 地区
    /// </summary>
    public CityModel? City { get; set; }

    /// <summary>
    /// 用户来源
    /// </summary>
    public HrProjectSource? Source { get; set; }
    /// <summary>
    /// 投递公司
    /// </summary>
    public string? AgentEntName { get; set; }
    /// <summary>
    /// 用户来源解释
    /// </summary>
    public string? SourceName { get; set; }

    /// <summary>
    /// 协同人
    /// </summary>
    public string? TeamHrId { get; set; }

    /// <summary>
    /// 用户头像
    /// </summary>
    public string? Avatar { get; set; }
    /// <summary>
    /// 顾问名称
    /// </summary>
    public string? Adviser { get; set; }

    /// <summary>
    /// 活跃度
    /// </summary>
    public OnlineStatus Active { get; set; }
    /// <summary>
    /// 毕业时间
    /// </summary>
    public DateOnly? GraduationDate { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 合作模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }

    /// <summary>
    /// 合作模式名称
    /// </summary>
    public string? PaymentNodeName { get; set; }

    /// <summary>
    /// 结算方式（单次结算, 长期结算）
    /// </summary>
    public PostRewardType? RewardType { get; set; }

    /// <summary>
    /// 结算方式名称（单次结算, 长期结算）
    /// </summary>
    public string? RewardTypeName { get; set; }

    /// <summary>
    /// 分润金额
    /// </summary>
    public decimal Money { get; set; }

    /// <summary>
    /// 入职过保时间
    /// </summary>
    public int? PaymentDays { get; set; }

    /// <summary>
    /// 岗位福利
    /// </summary>
    public List<WelfareModel>? Welfare { get; set; }

    /// <summary>
    /// 销售是否需要分佣
    /// </summary>
    public bool? IsSalesCommission { get; set; }

    /// <summary>
    /// 招聘流程列表工作经验数据库查询模型（不返回前端）
    /// </summary>
    public WorkExpClass? WorkExpClass { get; set; }
    /// <summary>
    /// 教育经历
    /// </summary>
    public string? EduExp { get; set; }
    /// <summary>
    /// 工作经历
    /// </summary>
    public string? WorkExp { get; set; }

    /// <summary>
    /// 教育经历学校（不返回前端）
    /// </summary>
    public string? EduExpSchool { get; set; }

    /// <summary>
    /// 教育经历专业（不返回前端）
    /// </summary>
    public string? EduExpMajor { get; set; }

    /// <summary>
    /// 教育经历学历（不返回前端）
    /// </summary>
    public EducationType? EduExpEducation { get; set; }

    /// <summary>
    /// 教育经历学历解释（不返回前端）
    /// </summary>
    public string? EduExpEducationName { get; set; }
    /// <summary>
    /// 审批状态名称
    /// </summary>
    public string? ApprovalStatusName { get; set; }
    /// <summary>
    /// 审批状态
    /// </summary>
    [ConcurrencyCheck]
    public PostSettleApprovalStatus ApprovalStatus { get; set; } = PostSettleApprovalStatus.项目经理审核;
    #endregion

}
public class BountyAccountingResponse : QueryResponse
{
    public List<BountyAccounting> Rows { get; set; } = default!;
}
/// <summary>
/// 分佣比例
/// </summary>
public class BountyRatio : SettlementHistory
{
    /// <summary>
    /// 结算单详情Id
    /// </summary>
    public string? SettlementDetailId { get; set; }//结算单详情Id
    /// <summary>
    /// 分佣人
    /// </summary>
    public string? BountyReceiver { get; set; }
    /// <summary>
    /// 分佣人类型
    /// </summary>
    public string? BountyReceiverType { get; set; }
    /// <summary>
    /// 分佣比例
    /// </summary>
    public decimal Ratio { get; set; }
    /// <summary>
    /// 投递公司Id
    /// </summary>
    public string? AgentEntId { get; set; }
    /// <summary>
    /// 投递公司
    /// </summary>
    public string? AgentEntName { get; set; }
    public string? NdnBookName { get; set; }

    /// <summary>
    /// 结算类型，内部、外部、个人
    /// </summary>
    public PostSettleBountyType? SettleBountyType { get; set; }
    /// <summary>
    /// 结算类型名称
    /// </summary>
    public string? SettleBountyTypeName { get; set; }
    /// <summary>
    /// 支付时间
    /// </summary>
    public DateTime? PaymentTime { get; set; }
    /// <summary>
    /// 结算状态
    /// </summary>
    public PaymentStatus PaymentStatus { get; set; } = default!;
}

public class UserMobile
{
    public string? UserId { get; set; }
    public string? Mobile { get; set; }
}

public class BountyRatioResponse : QueryResponse
{
    public List<BountyRatio> Rows { get; set; } = default!;
}

public class ContractInfo
{
    public string? ContractId { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 签署方式
    /// </summary>
    public int? SignType { get; set; }

    /// <summary>
    /// 合同状态
    /// </summary>
    public ProjectContractStatus? Status { get; set; }

    /// <summary>
    /// 合同状态文本
    /// </summary>
    public string? StatusText { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public ProjectContractSource? Source { get; set; }

    /// <summary>
    /// 合同名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 签署日期
    /// </summary>
    public DateTime? SignDate { get; set; }

    /// <summary>
    /// 发起日期
    /// </summary>
    public DateTime? InitiationDate { get; set; }

    /// <summary>
    /// 合同开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 合同结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 发起方
    /// </summary>
    public string? Initiator { get; set; }

    /// <summary>
    /// 参与方
    /// </summary>
    public string? Participant { get; set; }

    /// <summary>
    /// 发起方电话
    /// </summary>
    public string? InitiatorPhone { get; set; }

    /// <summary>
    /// 参与方电话
    /// </summary>
    public string? ParticipantPhone { get; set; }

    /// <summary>
    /// 发起方联系人
    /// </summary>
    public string? InitiatorContact { get; set; }

    /// <summary>
    /// 参与方联系人
    /// </summary>
    public string? ParticipantContact { get; set; }

    /// <summary>
    /// 合同金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 合同编号
    /// </summary>
    public string? ContractNo { get; set; }

    /// <summary>
    /// 合同附件
    /// </summary>
    public List<string>? Attachment { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 项目主题
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreatedById { get; set; }

    /// <summary>
    /// 创建人名字
    /// </summary>
    public string? CreatedByName { get; set; }
}

public class ContractListResponse : QueryResponse
{
    /// <summary>
    /// 合同列表
    /// </summary>
    public List<ContractInfo>? Rows { get; set; }
}

public class ReqModel() : QueryRequest
{
    /// <summary>
    /// 结算单Id
    /// </summary>
    public string? SettlementId { get; set; }

    /// <summary>
    /// 订单Id
    /// </summary>
    public string? OrderId { get; set; }
}
public class SettlementInfo
{
    /// <summary>
    /// 求职者Id
    /// </summary>
    public string? SeekerId { get; set; }
    /// <summary>
    /// 求职者名称
    /// </summary>
    public string? SeekerName { get; set; }
    /// <summary>
    /// 求职者电话
    /// </summary>
    public string? Mobile { get; set; }
    /// <summary>
    /// 求职者邮箱
    /// </summary>
    public string? Email { get; set; }
    /// <summary>
    /// 求职者性别
    /// </summary>
    public Sex? Sex { get; set; }
    /// <summary>
    /// 求职者城市
    /// </summary>
    public string? City { get; set; }
    /// <summary>
    /// 求职者身份证
    /// </summary>
    public string? IdCard { get; set; }
    /// <summary>
    /// 求职者学历
    /// </summary>
    public EducationType? Education { get; set; }
    /// <summary>
    /// 求职者学历名称
    /// </summary>
    public string? EducationName { get; set; }
    /// <summary>
    /// 职业
    /// </summary>
    public OccupationType? Occupation { get; set; }
    /// <summary>
    /// 入职时间
    /// </summary>
    public DateTime? InductionTime { get; set; }
    /// <summary>
    /// 招聘Id
    /// </summary>
    public string? RecruitId { get; set; }
    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 返费周期, null或0代表无限期
    /// </summary>
    public int? PaymentDuration { get; set; }

    /// <summary>
    /// 返费周期结束日期
    /// </summary>
    public DateTime? PaymentDurationEndTime { get; set; }

    /// <summary>
    /// 结算方式（单次结算, 长期结算）
    /// </summary>
    public PostRewardType? RewardType { get; set; }
    /// <summary>
    /// 合作模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }
    /// <summary>
    /// 合作模式名称
    /// </summary>
    public string? PaymentNodeName { get; set; }
    /// <summary>
    /// 过保天数
    /// </summary>
    public int GuaranteeDays { get; set; }
    /// <summary>
    /// 分润金额
    /// </summary>
    public decimal Money { get; set; }
    /// <summary>
    /// 过保开始时间
    /// </summary>
    public DateTime? GuaranteeStartDate { get; set; }
    /// <summary>
    /// 结算状态
    /// </summary>
    public PostSettleStatus Status { get; set; }
    /// <summary>
    /// 结算审批状态
    /// </summary>
    public PostSettleApprovalStatus ApprovalStatus { get; set; }
    /// <summary>
    /// 结算开始时间
    /// </summary>
    public string? StartTime { get; set; }
    /// <summary>
    /// 结算结束时间
    /// </summary>
    public string? EndTime { get; set; }
    /// <summary>
    /// 结算审批状态名称
    /// </summary>
    public string? ApprovalStatusName { get; set; }
    /// <summary>
    /// 岗位Id
    /// </summary>
    public string? PostId { get; set; }
    /// <summary>
    /// 分润Id
    /// </summary>
    public string? BountyId { get; set; }
    /// <summary>
    /// 结算量
    /// </summary>
    public decimal SettlementAmount { get; set; }

    /// <summary>
    /// 结算金额
    /// </summary>
    public decimal SettlementMoney { get; set; }
    /// <summary>
    /// 结算阶段
    /// </summary>
    public List<PostProfitPayment>? BountyStages { get; set; }
    /// <summary>
    /// 结算周期(按小时、天、月)
    /// </summary>
    public PostPaymentCycle? PaymentCycle { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}

public class DetailRequestModel
{
    /// <summary>
    /// 结算单Id
    /// </summary>
    public string? SettlementId { get; set; }
    /// <summary>
    /// 结算单详情Id
    /// </summary>
    public string? SettlementDetailId { get; set; }
}

public class DeliveryReceiptResonse()
{
    /// <summary>
    /// 结算单Id
    /// </summary>
    public string? SettlementId { get; set; }
    /// <summary>
    /// 订单Id
    /// </summary>
    public string? BountyId { get; set; }
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }
    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }
    /// <summary>
    /// 项目编号
    /// </summary>
    public int AutoNo { get; set; }
    /// <summary>
    /// 项目经理名称
    /// </summary>
    public string? ManagerName { get; set; }
    /// <summary>
    /// 求职者名称
    /// </summary>
    public string? SeekerName { get; set; }
    /// <summary>
    /// 销售名称
    /// </summary>
    public string? SaleName { get; set; }
    /// <summary>
    /// 邀面人
    /// </summary>
    public string? FollowName { get; set; }
    /// <summary>
    /// 线索名称
    /// </summary>
    public string? ClueName { get; set; }
    /// <summary>
    /// 手机号
    /// </summary>
    public string? TelNo { get; set; }
    /// <summary>
    /// 交付方式
    /// </summary>
    public PostRewardType? RewardType { get; set; }
    /// <summary>
    /// 交付方式名称
    /// </summary>
    public string? RewardTypeName { get; set; }
    /// <summary>
    /// 合作模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }
    /// <summary>
    /// 合作模式名称
    /// </summary>
    public string? PaymentNodeName { get; set; }
    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }
    /// <summary>
    /// 交付时间
    /// </summary>
    public DateTime? DeliveryTime { get; set; }
    /// <summary>
    /// 确认成果
    /// </summary>
    public string? ConfirmResult { get; set; }
    /// <summary>
    /// 确认时间
    /// </summary>
    public DateTime? ConfirmTime { get; set; }
    /// <summary>
    /// 交付明细
    /// </summary>
    public List<DeliveryDetail>? DeliveryDetails { get; set; }
    /// <summary>
    /// 原始总金额
    /// </summary>
    public decimal? TotalOrigin { get; set; }
    /// <summary>
    /// 实付总金额
    /// </summary>
    public decimal? Total { get; set; }
}

public class DeliveryDetail()
{
    /// <summary>
    /// 结算单Id
    /// </summary>
    public string? SettlementId { get; set; }
    /// <summary>
    /// 参与交付角色
    /// </summary>
    public string? DeliveryType { get; set; }
    /// <summary>
    /// 交付成员
    /// </summary>
    public string? DeliveryName { get; set; }
    /// <summary>
    /// 联系方式
    /// </summary>
    public string? ContactNo { get; set; }
    /// <summary>
    /// 用户ID
    /// </summary>
    public string? UserId { get; set; }
    /// <summary>
    /// 账套编号
    /// </summary>
    public string? NdnBookCode { get; set; }
    /// <summary>
    /// 账套公司
    /// </summary>
    public string? NdnBookName { get; set; }
    /// <summary>
    /// 分佣金额
    /// </summary>
    public decimal? BountyMoney { get; set; }
    /// <summary>
    /// 支付方式
    /// </summary>
    public string? PaymentType { get; set; }
    /// <summary>
    /// 实付金额
    /// </summary>
    public decimal? PaymentMoney { get; set; }
}

public class PostProfitPayment
{
    /// <summary>
    /// 结算方式（单次结算, 长期结算）
    /// </summary>
    public PostRewardType? RewardType { get; set; }
    /// <summary>
    /// 合作模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }
    /// <summary>
    /// 合作模式名称
    /// </summary>
    public string? PaymentNodeName { get; set; }
    /// <summary>
    /// 结算周期(按小时、天、月)
    /// </summary>
    public PostPaymentCycle? PaymentCycle { get; set; }
    /// <summary>
    /// 返费周期, null或0代表无限期
    /// </summary>
    public int? PaymentDuration { get; set; }
    /// <summary>
    /// 过保天数
    /// </summary>
    public int GuaranteeDays { get; set; }

    /// <summary>
    /// 分润金额
    /// </summary>
    public decimal Money { get; set; }
    /// <summary>
    /// 返费周期结束日期
    /// </summary>
    public DateTime? PaymentDurationEndTime { get; set; }
}

public class SettlementHistory
{
    /// <summary>
    /// 结算单Id
    /// </summary>
    public string? SettlementId { get; set; }
    /// <summary>
    /// 结算人Id
    /// </summary>
    public string? UserId { get; set; }
    /// <summary>
    /// 结算单截止时间
    /// </summary>
    public DateTime? SettlementEndTime { get; set; }
    /// <summary>
    /// 结算单标题
    /// </summary>
    public string? SettlementTitle { get; set; }
    /// <summary>
    /// 结算量
    /// </summary>
    public decimal SettlementAmount { get; set; }
    /// <summary>
    /// 支付金额
    /// </summary>
    public decimal SettlementMoney { get; set; }
    /// <summary>
    /// 结算金额
    /// </summary>
    public decimal Money { get; set; }
    /// <summary>
    /// 项目经理确认结算时间
    /// </summary>
    public DateTime? ManagerConfirmTime { get; set; }
    /// <summary>
    /// 账单生成时间
    /// </summary>
    public DateTime? SettlementTime { get; set; }
    /// <summary>
    /// 结算状态
    /// </summary>
    public PostSettleStatus Status { get; set; }
    /// <summary>
    /// 审批状态
    /// </summary>
    public PostSettleApprovalStatus? ApprovalStatus { get; set; }
    /// <summary>
    /// 结算审批状态名称
    /// </summary>
    public string? ApprovalStatusName { get; set; }
    /// <summary>
    /// 合作模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }
    /// <summary>
    /// 结算周期(按小时、天、月)
    /// </summary>
    public PostPaymentCycle? PaymentCycle { get; set; }
    /// <summary>
    /// 结算方式（单次结算, 长期结算）
    /// </summary>
    public PostRewardType? RewardType { get; set; }
    /// <summary>
    /// 分佣比例
    /// </summary>
    public List<BountyRatioMoney>? BountysRatioMoney { get; set; }
    /// <summary>
    /// 是否无效结算单 0 否 1 是
    /// </summary>
    public int IsInvalidSettlement { get; set; }
}

public class BountyRatioMoney
{
    /// <summary>
    /// 分佣人
    /// </summary>
    public string? BountyReceiver { get; set; }
    /// <summary>
    /// 分佣人类型
    /// </summary>
    public string? BountyReceiverType { get; set; }
    /// <summary>
    /// 支付金额
    /// </summary>
    public decimal SettlementMoney { get; set; }
}

public class SettlementHistoryResponse : QueryResponse
{
    public List<SettlementHistory> Rows { get; set; } = default!;
}

public class SettlementHistoryDetail : BountyRatio
{
    /// <summary>
    /// 公司名称
    /// </summary>
    public string? AgentName { get; set; }
    /// <summary>
    /// 对账开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }
    /// <summary>
    /// 对账结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
    ///// <summary>
    ///// 结算周期(按小时、天、月)
    ///// </summary>
    //public PostPaymentCycle? PaymentCycle { get; set; }
    /// <summary>
    /// 职位结算单Id
    /// </summary>
    public string? BountyStageId { get; set; }

    ///// <summary>
    ///// 审批状态
    ///// </summary>
    ////public PostSettleApprovalStatus? ApprovalStatus { get; set; }

    /// <summary>
    /// 对账时间（长期结算返回时间段，精确到日期  单次结算与简历交付返回具体的日期时间点）
    /// </summary>
    public string? SettlementDateTime { get; set; }
}
public enum RequestType
{
    公司, 个人
}

/// <summary>
/// 公共汇总数据模型
/// </summary>
public class OrderSumData
{
    /// <summary>
    /// 待结算项目
    /// </summary>
    public int? WaitToSettleProjectNum { get; set; }

    /// <summary>
    /// 待结算金额
    /// </summary>
    public decimal? WaitToSettleMoney { get; set; }

    /// <summary>
    /// 已结算金额
    /// </summary>
    public decimal? SettledMoney { get; set; }

    /// <summary>
    /// 交付中订单
    /// </summary>
    public int? ExecOrderNum { get; set; }

    /// <summary>
    /// 交付成功订单
    /// </summary>
    public int? SucceedOrderNum { get; set; }

    /// <summary>
    /// 交付失败订单
    /// </summary>
    public int? FailedOrderNum { get; set; }

    /// <summary>
    /// 预估交付中金额
    /// </summary>
    public decimal? ExecOrderMoney { get; set; }

    /// <summary>
    /// 交付失败金额
    /// </summary>
    public decimal? FailedOrderMoney { get; set; }

    /// <summary>
    /// 入驻项目
    /// </summary>
    public int? ProjectNum { get; set; }

    /// <summary>
    /// 运行项目
    /// </summary>
    public int? ExecProjectNum { get; set; }

    /// <summary>
    /// 开启分润项目
    /// </summary>
    public int? SharedProjectNum { get; set; }

    /// <summary>
    /// 需求人数：库存数量
    /// </summary>
    public int? LeftStock { get; set; }

    /// <summary>
    /// 投放佣金：冻结金额
    /// </summary>
    public decimal? FreezedMoney { get; set; }
}

/// <summary>
/// 公司资产汇总模型
/// </summary>
public class CompanyOrderSumData : OrderSumData
{
    /// <summary>
    /// 公司入驻人员
    /// </summary>
    public int? HrNum { get; set; }

    /// <summary>
    /// 待结算人员
    /// </summary>
    public int? WaitToSettleHrNum { get; set; }
}

/// <summary>
/// 个人收益汇总模型
/// </summary>
public class PersonalOrderSumData : OrderSumData
{
    /// <summary>
    /// 本月投递量
    /// </summary>
    public int? DeliveryNum { get; set; }

    /// <summary>
    /// 本月交付率
    /// </summary>
    public decimal? DeliveryRate { get; set; }
}

/// <summary>
/// 个人收益列表
/// </summary>
public class PersonalOrderListResponse : QueryResponse
{
    public List<PersonalOrderList> Rows { get; set; } = default!;
}

/// <summary>
/// 个人收益列表模型
/// </summary>
public class PersonalOrderList
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// 项目经理
    /// </summary>
    public string? ProjectHrName { get; set; }

    /// <summary>
    /// 订单数量
    /// </summary>
    public int? OrderNum { get; set; }

    /// <summary>
    /// 交付中
    /// </summary>
    public int? ExecOrderNum { get; set; }

    /// <summary>
    /// 交付成功
    /// </summary>
    public int? SucceedOrderNum { get; set; }

    /// <summary>
    /// 预估结算金额 - 交付中 + 交付成功
    /// </summary>
    public decimal? SettledMoney { get; set; }

    /// <summary>
    /// 应结算金额 - 交付成功
    /// </summary>
    public decimal? PayableSettleMoney { get; set; }

    /// <summary>
    /// 已结算金额 - 实际打钱
    /// </summary>
    public decimal? ActualSettleMoney { get; set; }

    /// <summary>
    /// 交付失败
    /// </summary>
    public int? FailedOrderNum { get; set; }

    /// <summary>
    /// 结算状态：预算用完就是已结算，否则待结算，todo:能获取预算前，先空着
    /// </summary>
    public string? SettleStatus { get; set; }
}

/// <summary>
/// 公司资产列表
/// </summary>
public class CompanyOrderListResponse : QueryResponse
{
    public List<CompanyOrderList> Rows { get; set; } = default!;
}

/// <summary>
/// 公司资产列表模型
/// </summary>
public class CompanyOrderList : PersonalOrderList
{
    /// <summary>
    /// 交付人员
    /// </summary>
    public int? DeliveryUserNum { get; set; }
}

/// <summary>
/// 列表请求模型
/// </summary>
public class OrderListReqeust : QueryRequest
{
    /// <summary>
    /// 平台项目
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// 结算状态
    /// </summary>
    public SStatus? Status { get; set; }

    /// <summary>
    /// 开始金额
    /// </summary>
    public decimal? MinMoney { get; set; }

    /// <summary>
    /// 结束金额
    /// </summary>
    public decimal? MaxMoney { get; set; }

    /// <summary>
    /// 交付人员
    /// </summary>
    public string? PostUserId { get; set; }
}

public enum SStatus
{
    待结算, 已结算
}

public class UserInfo
{
    public string UserId { get; set; } = default!;

    public string UserName { get; set; } = default!;
}
#endregion

public class ApprovalSettlementReq
{
    /// <summary>
    /// 结算单Id
    /// </summary>
    public string? SettlementId { get; set; }

    /// <summary>
    /// 审核的角色
    /// </summary>
    public PostSettleApprovalRole? Role { get; set; }

    /// <summary>
    /// 审批状态
    /// </summary>
    public AuditStatus? AuditStatus { get; set; }

    /// <summary>
    /// 审批意见
    /// </summary>
    public string? Remark { get; set; }
}

public class ConfirmSettlementReq
{
    /// <summary>
    /// 结算单Id
    /// </summary>
    public string? SettlementId { get; set; }

    /// <summary>
    /// 交付成果
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 是否归档
    /// </summary>
    public bool? FileAway { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    // /// <summary>
    // /// 招聘流程Id
    // /// </summary>
    // public string? RecruitId { get; set; }

    // /// <summary>
    // /// 结算单
    // /// </summary>
    // public List<ConfirmSettlementDetail>? Settlements { get; set; } = default!;
}

// public class ConfirmSettlementDetail
// {
//     /// <summary>
//     /// 结算单Id
//     /// </summary>
//     public string? SettlementId { get; set; }

//     /// <summary>
//     /// 交付成果
//     /// </summary>
//     public int Amount { get; set; }

//     /// <summary>
//     /// 是否归档
//     /// </summary>
//     public bool FileAway { get; set; } = false;
// }