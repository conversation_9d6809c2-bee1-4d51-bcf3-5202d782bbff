﻿using System.Text;
using Microsoft.AspNetCore.Http;

namespace Infrastructure.Extend;

public class RequestParams
{
    /// <summary>
    /// 获取参数值
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    public static string GetParams(HttpContext context)
    {
        var para = string.Empty;
        var method = context.Request.Method.ToUpper();
        if (method == "GET" || method == "DELETE")
        {
            para = context.Request.QueryString.Value;
        }
        else if (method == "POST" || method == "PUT")
        {
            if (context.Request.HasFormContentType && context.Request.Form.Files.Count > 0)
            {
                // 如果请求包含文件，则排除文件
                var form = context.Request.Form;
                var formData = form.Where(kv => kv.Value.Count > 0)
                                   .ToDictionary(kv => kv.Key, kv => kv.Value.ToString());
                para = string.Join("&", formData.Select(kv => $"{kv.Key}={kv.Value}"));
            }
            else
            {
                if (context.Request.Body.CanSeek)
                {
                    context.Request.Body.Seek(0, SeekOrigin.Begin);
                }
                using (var stream = new StreamReader(context.Request.Body, Encoding.UTF8, true, 1024, leaveOpen: true))
                {
                    para = stream.ReadToEnd();
                }

                if (context.Request.Body.CanSeek)
                {
                    context.Request.Body.Seek(0, SeekOrigin.Begin);
                }
            }
        }
        return para ?? string.Empty;
    }
}