﻿using Config.CommonModel.Tasks;

namespace Staffing.Model.Hr.User;

public class SendInviteSms
{
    /// <summary>
    /// 协同职位编号
    /// </summary>
    public string? TeamPostNo { get; set; }

    /// <summary>
    /// 发送对象
    /// </summary>
    public List<SendInviteSmsUserInfo> ToUsers { get; set; } = new List<SendInviteSmsUserInfo>();
}

public class SendInviteSmsResponse
{
    /// <summary>
    /// 任务Id
    /// </summary>
    public string? TaskId { get; set; }
}