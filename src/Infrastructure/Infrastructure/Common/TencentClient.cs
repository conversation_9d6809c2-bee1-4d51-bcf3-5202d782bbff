using Config;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using TencentCloud.Common;
using TencentCloud.Faceid.V20180301;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Singleton)]
public class TencentClient
{
    private readonly LogManager _log;
    private readonly ConfigManager _config;
    public TencentClient(LogManager log, IOptionsSnapshot<ConfigManager> config)
    {
        _log = log;
        _config = config.Value;
    }

    public FaceidClient GetFaceidClient()
    {
        var region = "ap-beijing";

        // 必要步骤：
        // 实例化一个认证对象，入参需要传入腾讯云账户密钥对secretId，secretKey。
        // 这里采用的是从环境变量读取的方式，需要在环境变量中先设置这两个值。
        // 你也可以直接在代码中写死密钥对，但是小心不要将代码复制、上传或者分享给他人，
        // 以免泄露密钥对危及你的财产安全。
        Credential cred = new Credential
        {
            SecretId = _config.TencentOptions!.FaceIdSecretId,
            SecretKey = _config.TencentOptions.FaceIdSecretKey
        };

        var client = new FaceidClient(cred, region);

        return client;
    }
}