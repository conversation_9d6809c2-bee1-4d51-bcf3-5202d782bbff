﻿// using Config.CommonModel.Recruit;
// using Config.CommonModel.ThirdTalentInfo;
// using Config.Enums;

// namespace Infrastructure.CommonService.ShareProfit;

// /// <summary>
// /// 简单工厂 - 创建交付场景对象
// /// </summary>
// public class DeliverySimpleFactory
// {
//     protected readonly ProjectPostInfo _projectPostInfo;
//     protected readonly RecruitInfos _recruit;
//     protected readonly CommonPostOrder _orderService;
//     public DeliverySimpleFactory(ProjectPostInfo projectPostInfo, RecruitInfos recruit, CommonPostOrder orderService)
//     {
//         _projectPostInfo = projectPostInfo;
//         _recruit = recruit;
//         _orderService = orderService;
//     }

//     public CommonDeliveryService CreateDelivery(ProjectPaymentNode node)
//     {
//         CommonDeliveryService Service;
//         switch (node)
//         {
//             case ProjectPaymentNode.入职过保:
//                 Service = new InductionDeliveryService(_projectPostInfo, _recruit, _orderService);
//                 break;
//             case ProjectPaymentNode.简历交付:
//                 Service = new ResumeDeliveryService(_projectPostInfo, _recruit, _orderService);
//                 break;
//             case ProjectPaymentNode.到面交付:
//                 Service = new InterviewDeliveryService(_projectPostInfo, _recruit, _orderService);
//                 break;
//             case ProjectPaymentNode.按天入职过保:
//                 Service = new MonthlyDeliveryService(_projectPostInfo, _recruit, _orderService);
//                 break;
//             default:
//                 Service = new CommonDeliveryService(_projectPostInfo, _recruit, _orderService);
//                 break;
//         }
//         return Service;
//     }
// }
