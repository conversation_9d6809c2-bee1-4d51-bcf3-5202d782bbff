﻿using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Core.Interfaces.Hr;
using Config;
using Staffing.Model.Hr.Project;
using Staffing.Entity.Staffing;
using Config.Enums;
using Google.Rpc;
using Infrastructure.Exceptions;
using ServiceStack;
using Config.CommonModel;
using Config.CommonModel.Xbb;
using Microsoft.EntityFrameworkCore;
using Staffing.Entity.Noah;
using LinqKit;
using Infrastructure.CommonService.Ndn;
using Config.CommonModel.Ndn;

namespace Staffing.Core.Services.Hr;

[Service(ServiceLifetime.Transient)]
public class ProjectContractsService : IProjectContractsService
{
    private readonly RequestContext _user;
    private readonly StaffingContext _context;
    private readonly IDbContextFactory<NoahContext> _noahContextFactory;
    private readonly ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly INdnService _ndnService;
    private readonly LogManager _log;
    public ProjectContractsService(StaffingContext context, RequestContext user, IDbContextFactory<NoahContext> noahContextFactory,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment, LogManager log, INdnService ndnService)
    {
        _user = user;
        _context = context;
        _noahContextFactory = noahContextFactory;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _ndnService = ndnService;
        _log = log;
    }

    public async Task<GetProjectContractResponse> GetNoahContract(GetNoahContract model)
    {
        await Task.CompletedTask;

        var result = _ndnService.GetNoahContractByCode(model.ContractCode);

        if (result == null)
            throw new BadRequestException("合同编号不存在");

        return Tools.ModelConvert<GetProjectContractResponse>(result);
    }

    public async Task<GetProjectContractResponse> GetProjectContract(GetProjectContract model)
    {
        await Task.CompletedTask;

        if (string.IsNullOrWhiteSpace(model.ContractId))
            throw new BadRequestException("合同Id不能为空");

        var result = _context.Project_Contract.Where(x => x.ContractId == model.ContractId)
        .Select(s => new GetProjectContractResponse
        {
            IsDefault = s.IsDefault,
            ContractId = s.ContractId,
            ProjectId = s.ProjectId,
            Name = s.Project.Agent_Ent.Name,
            InitiationDate = s.InitiationDate,
            SignDate = s.SignDate,
            StartTime = s.StartTime,
            EndTime = s.EndTime,
            Initiator = s.Initiator,
            Participant = s.Participant,
            InitiatorPhone = s.InitiatorPhone,
            ParticipantPhone = s.ParticipantPhone,
            InitiatorContact = s.InitiatorContact,
            ParticipantContact = s.ParticipantContact,
            Amount = s.Amount,
            Status = s.Status,
            StatusText = s.Status.GetDescription(),
            ContractNo = s.ContractNo,
            Attachment = s.Attachment,
            SignType = s.SignType,
            Source = s.Source,
            Remark = s.Remark,
            CreatedById = s.CreatedBy,
            CreatedByName = s.user_Hr.NickName
        }).FirstOrDefault();

        result ??= new GetProjectContractResponse();

        return result;
    }

    public GetProjectContractListResponse GetProjectContractList(GetProjectContractList model)
    {
        var result = new GetProjectContractListResponse();

        var predicate = PredicateBuilder.New<Project_Contract>(x => x.ProjectId == model.ProjectId);

        if (model.Status.HasValue)
            predicate = predicate.And(x => x.Status == model.Status);

        if (!string.IsNullOrWhiteSpace(model.ContractNo))
            predicate = predicate.And(x => x.ContractNo.Contains(model.ContractNo));

        var sql = _context.Project_Contract.Where(predicate);
        result.Total = sql.Count();

        result.Rows = sql.OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new ProjectContractInfo
        {
            IsDefault = s.IsDefault,
            ContractId = s.ContractId,
            ProjectId = s.ProjectId,
            Name = s.Project.Agent_Ent.Name,
            SignDate = s.SignDate,
            InitiationDate = s.InitiationDate,
            StartTime = s.StartTime,
            EndTime = s.EndTime,
            Initiator = s.Initiator,
            Participant = s.Participant,
            InitiatorPhone = s.InitiatorPhone,
            ParticipantPhone = s.ParticipantPhone,
            InitiatorContact = s.InitiatorContact,
            ParticipantContact = s.ParticipantContact,
            Amount = s.Amount,
            Status = s.Status,
            StatusText = s.Status.GetDescription(),
            ContractNo = s.ContractNo,
            Attachment = s.Attachment,
            SignType = s.SignType,
            Source = s.Source,
            Remark = s.Remark,
            CreatedById = s.CreatedBy,
            CreatedByName = s.user_Hr.NickName
        }).ToList();

        return result;
    }

    public EmptyResponse SetDefaultProjectContract(SetDefaultProjectContract model)
    {
        var projectContract = _context.Project_Contract
            .Where(x => x.ContractId == model.ContractId)
            .FirstOrDefault();

        if (projectContract == null)
            throw new BadRequestException("合同不存在");

        // 将同项目下的所有合同设置为非默认
        var projectContracts = _context.Project_Contract
            .Where(x => x.ProjectId == projectContract.ProjectId)
            .ToList();

        foreach (var contract in projectContracts)
        {
            contract.IsDefault = 0;
        }

        // 设置指定合同为默认
        projectContract.IsDefault = 1;

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public UpdateProjectContractResponse UpdateProjectContract(UpdateProjectContract model)
    {
        if (string.IsNullOrEmpty(model.ProjectId))
            throw new BadRequestException("项目Id不能为空");

        var noahContract = _ndnService.GetNoahContractByCode(model.ContractCode);

        if (noahContract == null)
            throw new BadRequestException("合同编号不存在");

        // project是否存在
        var project = _context.Project.Where(x => x.ProjectId == model.ProjectId)
        .Select(s => new
        {
            s.ProjectId,
            Initiator = s.Agent_Ent.Name,
            Participant = _context.Ndn_Books.Where(x => x.BookCode == s.BookCode).Select(s => s.BookName).FirstOrDefault()
        }).FirstOrDefault();

        if (project == null)
            throw new BadRequestException("项目不存在");

        // 检测合同编号是否被其他项目使用
        var existingContract = _context.Project_Contract
            .Where(x => x.ContractNo == noahContract.ContractNo)
            .Select(x => new { x.ProjectId, x.Project.SaleUser.NickName, x.Project.Agent_Ent.Name })
            .FirstOrDefault();

        if (existingContract != null)
            throw new BadRequestException($"合同编号已经被使用，销售人员：{existingContract.NickName}，项目名称：{existingContract.Name}");

        // 检测这些是否填写：Name,SignDate StartTime  EndTime  Initiator  Participant  InitiatorPhone  ParticipantPhone  InitiatorContact  ParticipantContact   ContractNo

        if (string.IsNullOrWhiteSpace(noahContract.Name))
            throw new BadRequestException("合同名称不能为空");

        if (!noahContract.SignDate.HasValue)
            throw new BadRequestException("签约日期不能为空");

        if (string.IsNullOrWhiteSpace(noahContract.Initiator))
            throw new BadRequestException("甲方不能为空");

        if (string.IsNullOrWhiteSpace(noahContract.Participant))
            throw new BadRequestException("已方不能为空");

        if (string.IsNullOrWhiteSpace(noahContract.ContractNo))
            throw new BadRequestException("合同编号不能为空");

        if (!model.StartTime.HasValue)
            throw new BadRequestException("开始日期不能为空");

        if (!model.EndTime.HasValue)
            throw new BadRequestException("结束日期不能为空");

        // 比较项目的甲乙双方名称和合同的甲乙双方名称是否一致
        if (!string.Equals(project.Initiator?.Trim(), noahContract.Initiator?.Trim()))
            throw new BadRequestException("甲方名称不一致");

        if (!string.Equals(project.Participant?.Trim(), noahContract.Participant?.Trim()))
            throw new BadRequestException("乙方名称不一致");

        model.Source ??= ProjectContractSource.销售易;

        var contract = new Project_Contract
        {
            ProjectId = model.ProjectId,
            Name = noahContract.Name,
            SignDate = noahContract.SignDate.Value,
            InitiationDate = noahContract.InitiationDate!.Value,
            StartTime = model.StartTime.Value,
            EndTime = model.EndTime.Value,
            Initiator = noahContract.Initiator ?? string.Empty,
            Participant = noahContract.Participant ?? string.Empty,
            InitiatorPhone = noahContract.InitiatorPhone ?? string.Empty,
            ParticipantPhone = noahContract.ParticipantPhone ?? string.Empty,
            InitiatorContact = noahContract.InitiatorContact ?? string.Empty,
            ParticipantContact = noahContract.ParticipantContact ?? string.Empty,
            Amount = noahContract.Amount,
            ContractNo = noahContract.ContractNo,
            Attachment = noahContract.Attachment,
            Status = ProjectContractStatus.已完成,
            CreatedBy = _user.Id,
            SignType = 0,
            Source = model.Source.Value,
            Remark = noahContract.Remark
        };

        _context.Add(contract);

        if (!_context.Project_Contract.Any(x => x.ProjectId == model.ProjectId))
        {
            contract.IsDefault = 1;
        }

        _context.SaveChanges();

        var result = new UpdateProjectContractResponse
        {
            ContractId = contract.ContractId
        };

        return result;
    }

    public HasDefaultContractResponse HasDefaultContract(string projectId)
    {
        if (string.IsNullOrWhiteSpace(projectId))
            throw new BadRequestException("项目ID不能为空");

        var hasDefaultContract = _context.Project_Contract
            .Any(x => x.ProjectId == projectId);

        return new HasDefaultContractResponse { HasDefault = hasDefaultContract };
    }
}