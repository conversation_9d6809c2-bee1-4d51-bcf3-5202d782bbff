
ALTER TABLE post ADD `IsResumeExempt` BIT(1) NOT NULL DEFAULT b'0' COMMENT '简历是否免审';
ALTER TABLE post ADD `PaymentNode` int DEFAULT '0' COMMENT '合作模式';
ALTER TABLE post ADD `PaymentDays` int DEFAULT '0' COMMENT '入职过保天数';
ALTER TABLE post ADD `IsSalesCommission` bit(1) NOT NULL DEFAULT b'0' COMMENT '销售是否需要分佣';
ALTER TABLE post ADD `AgentEntId` varchar(18) NULL COMMENT '招聘企业id';
ALTER TABLE post ADD `ContractType` int NULL COMMENT '签约方式';
ALTER TABLE post ADD `CreatorId`  varchar(18) NULL COMMENT '创建人Id';

ALTER TABLE project_survey ADD `PostId` varchar(18) NULL COMMENT '职位Id';
ALTER TABLE project_survey ADD UNIQUE KEY `IX_project_survey_PostId` (`PostId`);
ALTER TABLE project_survey DROP INDEX `IX_project_survey_ProjectId`;
ALTER TABLE project_survey ADD KEY `IX_project_survey_ProjectId` (`ProjectId`);

ALTER TABLE sys_settings ADD `PlatformFull` text NULL COMMENT '平台-全部佣金';
ALTER TABLE sys_settings ADD `PlatformNoSale` text NULL COMMENT '平台-不带销售佣金';
ALTER TABLE sys_settings ADD `NoahFull` text NULL COMMENT '诺亚-全部佣金';
ALTER TABLE sys_settings ADD `NoahNoSale` text NULL COMMENT '诺亚-不带销售佣金';

ALTER TABLE project_teambounty ADD `SaleUserId` varchar(18) NULL COMMENT '销售Id';

ALTER TABLE project_teambounty ADD `SalesBounty` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '销售佣金';
ALTER TABLE project_teambounty ADD `ManagerBounty` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '项目管理佣金';
ALTER TABLE project_teambounty ADD `ClueBounty` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '线索佣金';
ALTER TABLE project_teambounty ADD `FollowerBounty` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '邀面佣金';
ALTER TABLE project_teambounty ADD `PlatformBounty` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '平台佣金';
ALTER TABLE project_teambounty ADD `SalesRate` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '销售佣金比例';
ALTER TABLE project_teambounty ADD `ManagerRate` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '项目管理佣金比例';
ALTER TABLE project_teambounty ADD `ClueRate` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '线索佣金比例';
ALTER TABLE project_teambounty ADD `FollowerRate` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '邀面佣金比例';
ALTER TABLE project_teambounty ADD `PlatformRate` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '平台佣金比例';

ALTER TABLE recruit ADD `ResumeProcessTime` INT NULL COMMENT '简历处理时长';

-- 增加项目合同表，主要字段：签署方式、合同名称、签署日期、合同开始时间、合同结束时间、发起方、参与方、发起方电话、参与方电话、发起方联系人、参与方联系人、合同金额、合同编号、合同附件、备注
DROP TABLE IF EXISTS `project_contract`;
CREATE TABLE `project_contract` (
  `ContractId` varchar(18) NOT NULL COMMENT '合同Id',
  `ProjectId` varchar(18) NOT NULL COMMENT '项目Id',
  `SignType` int NOT NULL DEFAULT 0 COMMENT '签署方式',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态',
  `Source` int NOT NULL DEFAULT 0 COMMENT '来源',
  `Name` varchar(255) NOT NULL COMMENT '合同名称',
  `InitiationDate` datetime(6) NOT NULL COMMENT '发起日期',
  `SignDate` datetime(6) NOT NULL COMMENT '签署日期',
  `StartTime` datetime(6) NOT NULL COMMENT '合同开始时间',
  `EndTime` datetime(6) NOT NULL COMMENT '合同结束时间',
  `Initiator` varchar(18) NOT NULL COMMENT '发起方',
  `Participant` varchar(18) NOT NULL COMMENT '参与方',
  `InitiatorPhone` varchar(20) NOT NULL COMMENT '发起方电话',
  `ParticipantPhone` varchar(20) NOT NULL COMMENT '参与方电话',
  `InitiatorContact` varchar(255) NOT NULL COMMENT '发起方联系人',
  `ParticipantContact` varchar(255) NOT NULL COMMENT '参与方联系人',
  `Amount` decimal(18,2) NOT NULL COMMENT '合同金额',
  `ContractNo` varchar(50) NOT NULL COMMENT '合同编号',
  `Attachment` varchar(5000) NULL COMMENT '合同附件',
  `Remark` varchar(5000) NULL COMMENT '备注',
  `CreatedBy` varchar(18) NOT NULL COMMENT '创建人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    PRIMARY KEY (`ContractId`),
    KEY `IX_project_contract_ProjectId` (`ProjectId`),
    KEY `IX_project_contract_Initiator` (`Initiator`),
    KEY `IX_project_contract_Participant` (`Participant`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目合同表';

ALTER TABLE project_teambounty 
DROP COLUMN ChannelSettlementMoney,
DROP COLUMN ChannelSettlementRate,
DROP COLUMN PlatformSettlementRate,
DROP COLUMN PlatformSettlementMoney,
DROP COLUMN SettlementActualMoney,
DROP COLUMN SettlementMoney;

-- 暂时不用的字段：ChannelSettlementMoney,ChannelSettlementRate,PlatformSettlementRate,PlatformSettlementMoney,SettlementActualMoney,SettlementMoney


UPDATE post
INNER JOIN project ON post.projectid = project.projectid
SET 
    post.IsResumeExempt = project.IsResumeExempt,
    post.PaymentNode = project.PaymentNode,
    post.PaymentDays = project.PaymentDays,
    post.ContractType = project.ContractType;

UPDATE post
INNER JOIN project ON post.projectid = project.projectid
SET 
    post.CreatorId = CASE 
                        WHEN project.HrId IS NOT NULL AND project.HrId != '' THEN project.HrId 
                        ELSE project.SaleUserId 
                     END;

-- TODO: 职位的企业Id需要处理老数据
-- agent_ent表去重
DELETE FROM agent_ent
WHERE AgentEntId NOT IN (
    SELECT * FROM (
        SELECT MIN(AgentEntId)
        FROM agent_ent
        GROUP BY Name
    ) AS temp
);

-- 更新project表
UPDATE project p
JOIN user_agent_ent uae ON p.AgentEntId = uae.AgentEntId
JOIN agent_ent ae ON uae.Name = ae.Name
SET p.AgentEntId = ae.AgentEntId;

-- 更新post表
UPDATE post p
JOIN project pr ON p.ProjectId = pr.ProjectId
SET p.AgentEntId = pr.AgentEntId;









-- 更新project_team_config表 结构

ALTER TABLE `staffing`.`project_team_config` DROP COLUMN `TeamProjectId`;

ALTER TABLE `staffing`.`project_team_config` DROP COLUMN `HrId`;

ALTER TABLE `staffing`.`project_team_config` DROP COLUMN `ProjectId`;

ALTER TABLE `staffing`.`project_team_config` DROP INDEX `idx_team_project_id`;

ALTER TABLE `staffing`.`project_team_config` MODIFY COLUMN `Creator` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人' AFTER `CreatedTime`;




--更新项目表结构project

ALTER TABLE `staffing`.`project` MODIFY COLUMN `ProjectId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL FIRST;

ALTER TABLE `staffing`.`project` MODIFY COLUMN `AgentEntId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代招企业Id';

ALTER TABLE `staffing`.`project` MODIFY COLUMN `HrId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人-修改为项目经理id';

ALTER TABLE `staffing`.`project` MODIFY COLUMN `AgentHrId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '代填企业hrId';

ALTER TABLE `staffing`.`project` MODIFY COLUMN `Name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目名称';

ALTER TABLE `staffing`.`project` MODIFY COLUMN `Level` int NULL DEFAULT 0 COMMENT '项目级别';

ALTER TABLE `staffing`.`project` MODIFY COLUMN `Type` int NULL DEFAULT 0 COMMENT '项目类型';

ALTER TABLE `staffing`.`project` MODIFY COLUMN `Industry` int NULL DEFAULT 0 COMMENT '项目行业';

ALTER TABLE `staffing`.`project` MODIFY COLUMN `NuoId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '诺亚项目编码' ;

ALTER TABLE `staffing`.`project` MODIFY COLUMN `Describe` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述';

ALTER TABLE `staffing`.`project` MODIFY COLUMN `RegionData` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '地区Json' ;

ALTER TABLE `staffing`.`project` ADD COLUMN `StartTime` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '项目开始时间';

ALTER TABLE `staffing`.`project` MODIFY COLUMN `EndTime` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '项目截止日期';

ALTER TABLE `staffing`.`project` MODIFY COLUMN `XbbContractNo` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '销帮帮合同编号';

ALTER TABLE `staffing`.`project` MODIFY COLUMN `IsResumeExempt` bit(1) NULL DEFAULT b'0' COMMENT '简历是否免审';

ALTER TABLE `staffing`.`project` ADD COLUMN `IsAssignedPerson` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否指定人员';

ALTER TABLE `staffing`.`project` ADD COLUMN `SettlementPersonId` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结算财务人员id';

ALTER TABLE `staffing`.`project` ADD COLUMN `BookCode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账套code';

ALTER TABLE `staffing`.`project` ADD COLUMN `AcceptOrder` bit(1) NULL DEFAULT b'0' COMMENT '是否接单' ;

ALTER TABLE `staffing`.`project` ADD COLUMN `Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '合同金额';

ALTER TABLE `staffing`.`project` ADD COLUMN `SaleUserId` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '销售人员id';
ALTER TABLE `staffing`.`project` 
DROP COLUMN `ReceiverId`;



--更新项目表数据

UPDATE staffing.project 
SET SaleUserId = CAST(HrId AS VARCHAR)
WHERE SaleUserId = '' OR SaleUserId IS NULL;



--项目扩展表project_extend

ALTER TABLE `staffing`.`project_extend` MODIFY COLUMN `ProjectId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL FIRST;

ALTER TABLE `staffing`.`project_extend` ADD COLUMN `InterviewRound` int NOT NULL COMMENT '面试轮次' AFTER `DeliveriesNum`;

ALTER TABLE `staffing`.`project_extend` ADD COLUMN `IsVideoInterviewSupported` bit(1) NULL DEFAULT b'0' COMMENT '是否支持视频面试' AFTER `InterviewRound`;

ALTER TABLE `staffing`.`project_extend` ADD COLUMN `InterviewProcess` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '面试流程' AFTER `IsVideoInterviewSupported`;

ALTER TABLE `staffing`.`project_extend` ADD COLUMN `PositionCount` int NULL DEFAULT NULL COMMENT '职位数量' AFTER `InterviewProcess`;

ALTER TABLE `staffing`.`project_extend` ADD COLUMN `RecruitmentCount` int NULL DEFAULT NULL COMMENT '招聘人数' AFTER `PositionCount`;

ALTER TABLE `staffing`.`project_extend` ADD COLUMN `Price` decimal(10, 2) NULL DEFAULT NULL COMMENT '合同金额' AFTER `RecruitmentCount`;

ALTER TABLE `staffing`.`project_extend` ADD COLUMN `Remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他' AFTER `Price`;

ALTER TABLE `staffing`.`project_extend` ADD COLUMN `RequiredCondition` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '必备条件' AFTER `Remark`;

ALTER TABLE `staffing`.`project_extend` ADD COLUMN `ProjectManualUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目手册url' AFTER `RequiredCondition`;

ALTER TABLE `staffing`.`project_extend` ADD COLUMN `SignContractType` int NULL DEFAULT NULL COMMENT '合同签约方式' AFTER `ProjectManualUrl`;


--企业表
CREATE TABLE `agent_ent` (
  `AgentEntId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `Name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业名称',
  `Abbr` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业简称',
  `Display` int DEFAULT NULL COMMENT '展示项',
  `DisplayName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '展示名称',
  `LogoUrl` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业Logo',
  `AuthorizationUrl` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业授权证明',
  `Industry` int NOT NULL COMMENT '公司行业',
  `Nature` int NOT NULL COMMENT '企业性质',
  `Scale` int NOT NULL COMMENT '企业规模',
  `Capital` int NOT NULL COMMENT '融资阶段',
  `Describe` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `Status` int NOT NULL COMMENT '状态',
  `RegionId` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地区Id',
  `Address` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地址',
  `Location` point DEFAULT NULL,
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`AgentEntId`),
  KEY `IX_user_agent_ent_Name` (`Name`) USING BTREE,
  KEY `IX_user_agent_ent_RegionId` (`RegionId`) USING BTREE,
  KEY `IX_user_agent_ent_Status` (`Status`) USING BTREE,
  KEY `IX_user_agent_ent_Industry` (`Industry`) USING BTREE,
  KEY `IX_user_agent_ent_CreatedTime` (`CreatedTime` DESC) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='代招企业表\n';





-- 岗位扩展表


ALTER TABLE `staffing`.`post_extend` ADD COLUMN `ResumeProcessingDuration` int NULL COMMENT '简历处理时长';



-- 招聘流程

ALTER TABLE `staffing`.`recruit` ADD COLUMN `FollowerId` varchar(255)  NULL DEFAULT NULL COMMENT '邀面人id';
	
