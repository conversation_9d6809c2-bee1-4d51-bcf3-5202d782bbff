﻿using Config.CommonModel;
using Config.CommonModel.NkpConfig;
using Infrastructure.CommonInterface;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Infrastructure.CommonService;

/// <summary>
/// 诺快聘配置后台
/// </summary>
[Service(ServiceLifetime.Transient)]
public class NkpConfigService: INkpConfigService
{
    private IDbContextFactory<StaffingContext> _staffingContextFactory;

    /// <summary>
    /// 注入
    /// </summary>
    public NkpConfigService(IDbContextFactory<StaffingContext> staffingContextFactory)
    {
        _staffingContextFactory = staffingContextFactory;
    }

    public List<ConfigModel> GetConfigTreeList()
    {
        using var _context = _staffingContextFactory.CreateDbContext();

        var datas = _context.Nkp_Config_Info.OrderBy(o => o.Sort).ToList();
        
        var headDatas = new List<ConfigModel>(); // 创建头节点
        var heads = datas.Where(w => string.IsNullOrWhiteSpace(w.ParentId) && w.Deleted == 0).OrderBy(o => o.Sort).ToList();
        foreach(var head in heads)
        {
            var headData = new ConfigModel
            {
                Id = head.Id,
                Name = head.Name,
                Alias = head.Alias,
                Sort = head.Sort,
                Image = head.Image,
                Status = head.Status,
                Tags = head.Tags
            };

            // 添加子节点
            GetChildrens(datas, headData);

            headDatas.Add(headData);
        }

        return headDatas;
    }

    /// <summary>
    /// 递归获取子节点，子节点的子节点
    /// </summary>
    /// <param name="datas">所有节点</param>
    /// <param name="headData"></param>
    private void GetChildrens(List<Nkp_Config_Info> datas, ConfigModel headData)
    {
        // 如果是叶节点(没有子节点)
        if(!datas.Any(w => w.ParentId == headData.Id))
        {
            return;
        }

        var childrens = datas.Where(w => w.ParentId == headData.Id && w.Deleted == 0).OrderBy(o => o.Sort).ToList();
        var childs = new List<ConfigModel>();

        foreach(var child in childrens)
        {
            var ch = new ConfigModel
            {
                Id = child.Id,
                Name = child.Name,
                Alias = child.Alias,
                Sort = child.Sort,
                Image = child.Image,
                Status = child.Status,
                Tags = child.Tags
            };
            
            GetChildrens(datas, ch);
            childs.Add(ch);
        }

        headData.Children = childs;
    }

    public EmptyResponse SaveConfig(ConfigSaveModel model)
    {
        if (model == null)
            throw new Exception("无数据");

        using var _context = _staffingContextFactory.CreateDbContext();

        if (!string.IsNullOrWhiteSpace(model.Id))
        {
            //if(model.Deleted != 1 && (model.Names.Count > 1 || model.Names.Count == 0))
            //{
            //    throw new Exception("修改时不能传多个名称");
            //}

            var exits = _context.Nkp_Config_Info.Where(w => w.Id == model.Id).FirstOrDefault() ?? throw new Exception("获取数据失败");

            exits.Name = model.Names != null && model.Names.Count > 0 ? model.Names[0] : exits.Name;
            exits.Alias = model.Alias ?? exits.Alias;
            exits.Sort = model.Sort ?? exits.Sort;
            exits.VerifyValue = model.VerifyValue ?? exits.VerifyValue;
            exits.Image = model.Image ?? exits.Image;
            exits.Module = model.Module ?? exits.Module;
            exits.Status = model.Status ?? exits.Status;
            exits.UpdatedTime = DateTime.Now;
            exits.Deleted = model.Deleted;
            exits.Tags = model.Tags ?? exits.Tags;
        }
        else
        {
            foreach(var name in model.Names.Distinct())
            {
                var data = new Nkp_Config_Info
                {
                    Name = name,
                    Alias = model.Alias,
                    ParentId = model.ParentId,
                    Sort = model.Sort ?? 0,
                    VerifyValue = model.VerifyValue,
                    Image = model.Image,
                    Module = model.Module,
                    Status = model.Status ?? 0,
                    CreatedTime = DateTime.Now,
                    Tags = model.Tags
                };
                data.Level = GetLevel(_context, data);
                _context.Add(data);
            }
        }

        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 获取level - 全路径
    /// </summary>
    /// <param name="_context"></param>
    /// <param name="data"></param>
    /// <returns></returns>
    private static string GetLevel(StaffingContext _context, Nkp_Config_Info data)
    {
        if (string.IsNullOrWhiteSpace(data.ParentId))
        {
            // 头节点返回自己的Id
            return data.Id + ".";
        }
        else
        {
            var parentInfo = _context.Nkp_Config_Info.FirstOrDefault(f => f.Id == data.ParentId);
            return GetLevel(_context, parentInfo!) + data!.Id + ".";
        }
    }
}

