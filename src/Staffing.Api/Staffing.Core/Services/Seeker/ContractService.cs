﻿using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces.Seeker;
using Config;
using Microsoft.EntityFrameworkCore;
using Infrastructure.CommonService;
using System.Text;
using Infrastructure.Aliyun;
using Staffing.Model.Seeker.Contract;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using Config.CommonModel.FigureNoah;

namespace Staffing.Core.Services.Seeker;

[Service(ServiceLifetime.Transient)]
public class ContractService : IContractService
{
    private readonly StaffingContext _context;
    private readonly RequestContext _user;
    private readonly ConfigManager _config;
    private readonly CacheHelper _cacheHelper;
    private readonly ICommonService _commonService;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CommonUserService _commonUserService;
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly CommonDicService _commonDicService;
    private readonly CommonCacheService _commonCacheService;
    private readonly WeChatHelper _weChatHelper;
    private readonly ESignHelper _eSignHelper;
    private readonly FigureNoahHelper _figureNoahHelper;
    private readonly AliyunDyplsHelper _aliyunDyplsHelper;
    private readonly TencentImHelper _tencentImHelper;
    private readonly DingDingHelper _dingDingHelper;
    private readonly SmsHelper _smsHelper;
    private readonly Interfaces.CentreService.IHrService _hrService;
    public ContractService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, ICommonService commonService,
        LogManager log, WeChatHelper weChatHelper, IDbContextFactory<StaffingContext> contextFactory,
        CommonUserService commonUserService, CommonDicService commonDicService,
        CommonCacheService commonCacheService, ESignHelper eSignHelper, FigureNoahHelper figureNoahHelper,
        AliyunDyplsHelper aliyunDyplsHelper, TencentImHelper tencentImHelper, DingDingHelper dingDingHelper,
        Interfaces.CentreService.IHrService hrService, SmsHelper smsHelper)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _commonService = commonService;
        _log = log;
        _weChatHelper = weChatHelper;
        _contextFactory = contextFactory;
        _commonUserService = commonUserService;
        _commonDicService = commonDicService;
        _commonCacheService = commonCacheService;
        _eSignHelper = eSignHelper;
        _figureNoahHelper = figureNoahHelper;
        _aliyunDyplsHelper = aliyunDyplsHelper;
        _tencentImHelper = tencentImHelper;
        _dingDingHelper = dingDingHelper;
        _hrService = hrService;
        _smsHelper = smsHelper;
    }

    public async Task<object> Test(string[]? abc)
    {
        // var aa = await _tencentImHelper.GetC2CUnreadMsgNum("imb149173898083096709");
        // Console.WriteLine("xxx:" + aa.AllC2CUnreadMsgNum);

        // var resp = await _eSignHelper.CreateUserAccount(new CreateUserAccount
        // {
        //     thirdPartyUserId = "test_144082261659091077",
        //     name = "王金超",
        //     idNumber = "130726199601014718"
        // });

        // var resp = await _eSignHelper.CreateDocTemplates(new CreateDocTemplates
        // {
        //     contentType = "application/octet-stream",
        //     fileName = "劳务派遣劳动合同-德汇（2022.6以后）（加安全生产培训）.doc",
        //     contentMd5 = "1TpjVTz+BpcVcP+QaCmuwQ==",
        //     convert2Pdf = true
        // });

        //a649b56a0ccc4174b463bca754f44db9
        //f4631a7592774e428314027a7d98738f

        // var resp = await _eSignHelper.GetTemSettingUrl(new GetTemSettingUrl
        // {
        //     templateId = "9caf3fc530fe4d9b87713bb0b7ca54b7"
        // });

        // var resp = await _figureNoahHelper.GetEmployeeProjData("001224");

        // var resp = await _eSignHelper.CreateFileByTemplate(new CreateFileByTemplate
        // {
        //     templateId = "a649b56a0ccc4174b463bca754f44db9",
        //     simpleFormFields = new { },
        //     name = "文件2"
        // });

        //047011b696c54e1b892ca9be18e19735
        //文件1

        //db1a6c8dd8014abea8d6da30decae7ba
        //文件2

        //88f6216e0df943518f741dd55e3eca2e
        //文件3

        //fa46f1fde397485c9f74218f5f763eb1
        //王金超
        //130726199601014718

        //b742359ad62f4e619029d59e657f2974
        //郭思成
        //13040619901214271X

        //工作流
        //4ea287d084e141eba6be75efdcb0faf5

        // var resp = await _eSignHelper.CreateFlowOneStep(new CreateFlowOneStep
        // {
        //     flowInfo = new CreateFlowOneStepFlowInfo
        //     {
        //         businessScene = "老郭的合同"
        //     },
        //     signers = new CreateFlowOneStepSigners[] {
        //         new CreateFlowOneStepSigners
        //         {
        //             signerAccount = new CreateFlowOneStepSignerAccount
        //             {
        //                 signerAccountId = "b742359ad62f4e619029d59e657f2974"
        //             },
        //             signfields = new CreateFlowOneStepSignfields[]{
        //                 new CreateFlowOneStepSignfields{
        //                     signType = 0,
        //                     fileId = "db1a6c8dd8014abea8d6da30decae7ba",
        //                     // posBean = new CreateFlowOneStepPosBean
        //                     // {
        //                     //     posPage = 0
        //                     // }
        //                 }
        //             }
        //         }
        //     },
        //     docs = new CreateFlowOneStepDocs[] {
        //         new CreateFlowOneStepDocs
        //         {
        //             fileId = "db1a6c8dd8014abea8d6da30decae7ba",
        //             fileName = "需签字合同"
        //         }
        //     },
        //     attachments = new CreateFlowOneStepAttachments[] {
        //         new CreateFlowOneStepAttachments
        //         {
        //             fileId = "047011b696c54e1b892ca9be18e19735",
        //             attachmentName = "附件1"
        //         },
        //         new CreateFlowOneStepAttachments
        //         {
        //             fileId = "88f6216e0df943518f741dd55e3eca2e",
        //             attachmentName = "附件2"
        //         }
        //     }
        // });

        // var resp = await _eSignHelper.StartSignflows(new StartSignflows
        // {
        //     flowId = "4e87992d242348e292336e051a22a0be"
        // });

        // var resp = await _eSignHelper.GetSignflowsExecuteUrl(new GetSignflowsExecuteUrl
        // {
        //     flowId = "4e87992d242348e292336e051a22a0be",
        //     accountId = "b742359ad62f4e619029d59e657f2974"
        // });


        // var resp = await _eSignHelper.GetSignflowsExecuteUrl(new GetSignflowsExecuteUrl
        // {
        //     flowId = "4e87992d242348e292336e051a22a0be",
        //     accountId = "b742359ad62f4e619029d59e657f2974"
        // });

        // var resp = await _dingDingHelper.GetDingDingUserDetailsByUserId(new Config.CommonModel.DingDing.GetDingDingUserDetailsByUserIdRequest
        // {
        //     userid = "*****************"
        // });

        // var talents = _context.User_Hr
        // .Select(s => new
        // {
        //     s.UserId,
        //     s.User_Num.Talent
        // }).ToList();

        // var recruits = _context.Recruit.GroupBy(g => g.Post_Delivery.Post_Team.Project_Team.HrId)
        // .Select(s => new
        // {
        //     s.Key,
        //     Ct = s.Count()
        // }).ToList();

        // var bounty = _context.Project_Teambounty.Where(x => x.Status == BountyStatus.交付成功).GroupBy(g => g.TeamHrId)
        // .Select(s => new
        // {
        //     s.Key,
        //     Money = s.Sum(c => c.SettlementMoney)
        // }).ToList();

        // var talentKeys = new Dictionary<string, decimal>();
        // foreach (var item in talents)
        // {
        //     var fullVisitsKey = $"{RedisKey.HrStatBehavior.HyperLogKey}{RedisKey.HrStatBehavior.Visits}{item.UserId}";
        //     if (item.Talent <= 0)
        //         continue;

        //     var aaa = Enumerable.Range(0, item.Talent).Select(s => s.ToString()).ToArray();
        //     MyRedis.Client.PfAdd(fullVisitsKey, aaa);
        //     var totalVst = MyRedis.Client.PfCount(fullVisitsKey);
        //     MyRedis.Client.HSet(RedisKey.HrStatBehavior.FullKey, $"{RedisKey.HrStatBehavior.Visits}{item.UserId}", totalVst);

        //     var fullVistKey = $"{RedisKey.HrStatBehavior.Visits}{item.UserId}";
        //     talentKeys.Add(fullVistKey, item.Talent);
        // }

        // foreach (var item in recruits)
        // {
        //     var fullDeliveryKey = $"{RedisKey.HrStatBehavior.Delivery}{item.Key}";
        //     talentKeys.Add(fullDeliveryKey, item.Ct);
        // }

        // foreach (var item in bounty)
        // {
        //     var fullDeliveryBountyKey = $"{RedisKey.HrStatBehavior.DeliveryBounty}{item.Key}";
        //     talentKeys.Add(fullDeliveryBountyKey, item.Money ?? 0);
        // }

        // MyRedis.Client.HMSet(RedisKey.HrStatBehavior.FullKey, talentKeys);

        // var aaa = _context.Tasks.First(x => x.Type == TaskHandlingType.职位邀约短信);
        // var taskContent = JsonSerializer.DeserializeFromString<Config.CommonModel.Tasks.SendBusinessSms>(aaa.Content);

        // var aaa = _smsHelper.SendSMSAliyunForJumpApplet("***********", "SMS_248540121", new Config.CommonModel.Tasks.SendBusinessSmsTempCode
        // {

        // });

        // var str = $"SELECT COUNT(1) as c FROM [User] usr WHERE usr.IsRemove = 0 AND usr.FromType != 7 AND ( 1 = 2";

        // foreach (var item in abc!)
        // {
        //     str += $"OR usr.ProfessionalTags LIKE '%{item}%' OR usr.Tags LIKE '%{item}%'";
        // }

        // str += ")";

        // var ss = await _tencentImHelper.GetImAccount(new GetImAccount
        // {
        //     To_Account = abc?.ToList()
        // });

        // //注册im
        // try
        // {
        //     await _commonUserService.GetSeekerImUserSig(abc!.First());
        // }
        // catch (Exception e)
        // {
        //     var cc = e;
        // }

        // await _tencentImHelper.SetImAccount(new SetImAccount
        // {
        //     NickName = "",
        //     From_Account = "abcdefg"
        // });

        // var appletShortLink = await _weChatHelper.GetWxShortLinkForJump(WeChatApps.SeekerApplet.AppID);
        // Console.WriteLine(appletShortLink);

        // try
        // {
        //     var aaa = await "http://localhost:6001/centreservice/v1/Hr/audit".PostJsonAsync(null).ReceiveString();
        //     Console.WriteLine($"111:{await aaa.GetStringAsync()}");
        // }
        // catch (FlurlHttpException ex)
        // {
        //     Console.WriteLine($"333:{ex.StatusCode}_{await ex.GetResponseStringAsync()}");
        // }
        // catch (Exception e)
        // {
        //     Console.WriteLine($"222:{e.StackTrace}");
        // }

        // MyRedis.Client.RPush(SubscriptionKey.SeekerRegister, "164713007633835781");
        // MyRedis.Client.RPush(SubscriptionKey.SeekerRegister, "164715957557045253");

        // var resp = await _figureNoahHelper.GetEmployeeProjData("item");


        // await dg(1, "1.");

        // var resp = await _dingDingHelper.GetDepartmentListSub(1);

        // await dg(*********);
        // await dg(*********);
        // await dg(*********);
        // await dg(*********);

        var resp = await _figureNoahHelper.GetEmployeeProjDataDetail("000219");


        //SetQueryParams
        //http://localhost:6001/common/v1/Service/dic/welfare
        // var url = "https://www.tianyancha.com/company/2351667404";
        // var resp = await url.WithHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_4) AppleWebKit/537.31 (KHTML like Gecko) Chrome/26.0.1410.63 Safari/537.31")
        // .SetQueryParams(new Object()).GetAsync();


        await Task.CompletedTask;
        return resp;
    }

    private async Task dg(long deptId, string? level = null)
    {
        level = level ?? deptId.ToString();

        var resp = await _dingDingHelper.GetDepartmentListSub(deptId);

        if (resp == null || resp.Count() == 0)
            return;

        foreach (var item in resp)
        {
            var dept = new Dd_Dept
            {
                DdDeptId = item.dept_id,
                Name = item.name ?? string.Empty,
                ParentId = deptId,
                Level = $"{level}{item.dept_id}."
            };
            _context.Add(dept);
            _context.SaveChanges();
            await Task.Delay(10);
            await dg(item.dept_id, dept.Level);
        }
    }

    public async Task<object> Test2(string str)
    {
        using var sr = new StringReader(str);
        var line = string.Empty;

        var gonghao = new List<string>();
        while ((line = sr.ReadLine()) != null)
        {
            if (!string.IsNullOrWhiteSpace(line))
                gonghao.Add(line);
        }

        var sb = new StringBuilder();
        var i = 0;
        // foreach (var item in gonghao)
        // {
        //     try
        //     {
        //         await _hrService.AddDingDingUser(new Model.CentreService.User.AddDingDingUserRequest
        //         {
        //             Mobile = item
        //         });
        //     }
        //     catch (Exception e)
        //     {
        //         sb.AppendLine($"{item}:{e.Message}");
        //     }
        //     Console.WriteLine($"已处理{i++}个");
        //     await Task.Delay(10);
        // }

        // return sb.ToString();

        // var result = new StringBuilder();
        // result.AppendLine("工号,项目,劳务派遣,委托招聘,业务外包,招聘流程外包,诺优考SaaS平台租赁,合同,劳务派遣,委托招聘,业务外包,招聘流程外包,诺优考SaaS平台租赁");

        // foreach (var item in gonghao)
        // {
        //     var resp = await _figureNoahHelper.GetEmployeeProjData(item);
        //     var lstr = $"{item}";
        //     lstr += $",{resp.xmdata.rows.Sum(s => Convert.ToInt32(s.SL))}";
        //     lstr += $",{resp.xmdata.rows.Where(x => x.XM_PRODUCTNAME == "劳务派遣").Sum(s => Convert.ToInt32(s.SL))}";
        //     lstr += $",{resp.xmdata.rows.Where(x => x.XM_PRODUCTNAME == "委托招聘").Sum(s => Convert.ToInt32(s.SL))}";
        //     lstr += $",{resp.xmdata.rows.Where(x => x.XM_PRODUCTNAME == "业务外包").Sum(s => Convert.ToInt32(s.SL))}";
        //     lstr += $",{resp.xmdata.rows.Where(x => x.XM_PRODUCTNAME == "招聘流程外包").Sum(s => Convert.ToInt32(s.SL))}";
        //     lstr += $",{resp.xmdata.rows.Where(x => x.XM_PRODUCTNAME == "诺优考SaaS平台租赁").Sum(s => Convert.ToInt32(s.SL))}";

        //     lstr += $",{resp.htdata.rows.Sum(s => Convert.ToInt32(s.SL))}";
        //     lstr += $",{resp.htdata.rows.Where(x => x.SJHT_CPMC == "劳务派遣").Sum(s => Convert.ToInt32(s.SL))}";
        //     lstr += $",{resp.htdata.rows.Where(x => x.SJHT_CPMC == "委托招聘").Sum(s => Convert.ToInt32(s.SL))}";
        //     lstr += $",{resp.htdata.rows.Where(x => x.SJHT_CPMC == "业务外包").Sum(s => Convert.ToInt32(s.SL))}";
        //     lstr += $",{resp.htdata.rows.Where(x => x.SJHT_CPMC == "招聘流程外包").Sum(s => Convert.ToInt32(s.SL))}";
        //     lstr += $",{resp.htdata.rows.Where(x => x.SJHT_CPMC == "诺优考SaaS平台租赁").Sum(s => Convert.ToInt32(s.SL))}";

        //     result.AppendLine(lstr);
        //     Console.WriteLine($"已处理{i++}个");
        // }

        var result = new StringBuilder();
        // result.AppendLine("工号,项目编码,产品编码,产品名称,创建时间,开始时间,结束时间");
        result.AppendLine("工号,合同编码,合同名称,受益人编码,销帮帮合同编码,产品编码,产品名称,创建时间,开始时间,结束时间");

        foreach (var item in gonghao)
        {
            var resp = await _figureNoahHelper.GetEmployeeProjDataDetail(item);

            // foreach (var item2 in resp.xmdata.rows)
            // {
            //     var lstr = $"{item}";
            //     lstr += $",{item2.XM_CODE}";
            //     lstr += $",{item2.XM_PRODUCTID}";
            //     lstr += $",{item2.XM_PRODUCTNAME}";
            //     lstr += $",{item2.XM_SETDATE}";
            //     lstr += $",{item2.XM_BEGTIME}";
            //     lstr += $",{item2.XM_ENDTIME}";
            //     result.AppendLine(lstr);
            // }

            foreach (var item2 in resp.htdata.rows)
            {
                var lstr = $"{item}";
                lstr += $",{item2.SJHT_CODE}";
                lstr += $",{item2.SJHT_NAME}";
                lstr += $",{item2.SJHT_SY_RYCODE}";
                lstr += $",{item2.SJHT_XBBBM}";
                lstr += $",{item2.SJHT_CPID}";
                lstr += $",{item2.SJHT_CPMC}";
                lstr += $",{item2.SJHT_SETDATE}";
                lstr += $",{item2.SJHT_BEGINDATE}";
                lstr += $",{item2.SJHT_ENDDATE}";
                result.AppendLine(lstr);
            }

            Console.WriteLine($"已处理{i++}个");
        }

        await Task.CompletedTask;
        return result.ToString();
    }

    public async Task<byte[]> ExportSznyData(GetExportSznyData model)
    {
        // var asdfasf = ServiceStack.Text.JsonSerializer.SerializeToString(data);

        // var asdfasf2 = Newtonsoft.Json.JsonConvert.SerializeObject(data);

        // var cxx = Newtonsoft.Json.JsonConvert.DeserializeObject<List<dynamic>>(asdfasf2);

        var data = new SznyProjectExoprt { Rows = new List<SznyProjectExoprtDetail>() };

        var nyusers = _context.Rpt_Ny_User
        .OrderBy(o => o.部门1).ThenBy(o => o.部门2).ThenBy(o => o.部门3)
        .ThenBy(o => o.部门4).ThenBy(o => o.部门5).ThenBy(o => o.姓名)
        .ToList();

        var projects = _context.Rpt_Szny_Project.ToList();

        foreach (var usr in nyusers)
        {
            var projs = projects.Where(x => x.JobNumber.Trim() == usr.工号!.Trim())
            .OrderByDescending(o => o.CreatedTime).ToList();

            foreach (var proj in projs)
            {
                var oc = Tools.ModelConvert<SznyProjectExoprtDetail>(usr);
                oc.项目名称 = proj.XM_NAME;
                oc.项目编码 = proj.XM_CODE;
                oc.产品编码 = proj.XM_PRODUCTID;
                oc.产品名称 = proj.XM_PRODUCTNAME;
                oc.创建时间 = proj.XM_SETDATE.ToYYYY_MM_DD();
                oc.开始时间 = proj.XM_BEGTIME.ToYYYY_MM_DD();
                oc.结束时间 = proj.XM_ENDTIME.ToYYYY_MM_DD();
                data.Rows.Add(oc);
            }
        }

        //模板路径
        var tplPath = Path.Combine(Directory.GetCurrentDirectory(), "TemplateFiles", "Szny", "project.xlsx");

        //创建Excel导出对象
        IExportFileByTemplate exporter = new ExcelExporter();

        //根据模板导出
        var fileByte = await exporter.ExportBytesByTemplate(data, tplPath);

        return fileByte;
    }
}