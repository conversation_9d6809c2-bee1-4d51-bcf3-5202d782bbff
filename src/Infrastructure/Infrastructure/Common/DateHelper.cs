﻿namespace Infrastructure.Common;

public class DateHelper
{
    /// <summary>
    /// 获取自然周起始日期区间
    /// </summary>
    /// <param name="startDate"></param>
    /// <param name="endDate"></param>
    /// <param name="weekBegin">周一或者周日做为周起始</param>
    /// <returns></returns>
    public static List<DateRange> GetWeekRanges(DateTime startDate, DateTime endDate, WeekBegin weekBegin = WeekBegin.周一)
    {
        var weekRanges = new List<DateRange>();

        // 确保起始日期是周一（每周开始，可以是周日）
        var MondayDate = startDate.AddDays(-(int)startDate.DayOfWeek + (int)weekBegin);

        while (MondayDate <= endDate)
        {
            DateTime endOfWeek = MondayDate.AddDays(6); // 周日
            if (endOfWeek > endDate)
            {
                // 如果结束周超过了endDate，则只包含到endDate的部分  
                endOfWeek = endDate;
            }

            if(MondayDate < startDate)
            {
                weekRanges.Add(new DateRange(startDate, endOfWeek));
            }
            else
            {
                weekRanges.Add(new DateRange(MondayDate, endOfWeek));
            }

            // 移动到下一周的开始  
            MondayDate = MondayDate.AddDays(7);
        }

        return weekRanges;
    }

    /// <summary>
    /// 获取自然月的起始日期区间，
    /// </summary>
    /// <param name="startDate"></param>
    /// <param name="endDate"></param>
    /// <returns></returns>
    public static List<DateRange> GetMonthRanges(DateTime startDate, DateTime endDate)
    {
        var monthRanges = new List<DateRange>();
        var monthBegin = startDate.AddDays(-startDate.Day + 1);

        while(monthBegin < endDate)
        {
            var monthEnd = monthBegin.AddMonths(1).AddDays(-1);
            if(monthEnd > endDate)
            {
                monthEnd = endDate;
            }

            if(monthBegin < startDate)
            {
                monthRanges.Add(new DateRange(startDate, monthEnd));
            }
            else
            {
                monthRanges.Add(new DateRange(monthBegin, monthEnd));
            }

            monthBegin = monthBegin.AddMonths(1);
        }

        return monthRanges;
    }
}

/// <summary>
/// 每周开始
/// </summary>
public enum WeekBegin
{
    周日, 周一, 周二, 周三, 周四, 周五, 周六
}

public class DateRange
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }

    public DateRange(DateTime start, DateTime end)
    {
        StartDate = start;
        EndDate = end;
    }
}