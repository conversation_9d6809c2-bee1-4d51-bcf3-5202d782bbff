﻿using System.Text;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using ServiceStack.Text;
using Noah.Aliyun.Storage;
using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp;
using Config;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Hosting;
using Config.Enums;
using Senparc.Weixin.WxOpen.Entities;
using Senparc.Weixin.WxOpen.AdvancedAPIs.Sns;
using Senparc.Weixin.MP.AdvancedAPIs.User;
using Config.CommonModel;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Transient)]
public class WeChatHelper
{
    private readonly IObjectStorage _objectStorage;
    private readonly ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    public WeChatHelper(IObjectStorage objectStorage, IOptionsSnapshot<ConfigManager> config,
    IHostEnvironment hostingEnvironment, LogManager log)
    {
        _objectStorage = objectStorage;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
    }

    /// <summary>
    /// 生成小程序二维码
    /// </summary>
    /// <param name="appId"></param>
    /// <param name="scene"></param>
    /// <param name="page"></param>
    /// <param name="isHyaline"></param>
    /// <returns></returns>
    public async Task<string> GetWxaCodeUnlimit(string appId, string scene, string page, bool isHyaline = false)
    {

        using var stream = new MemoryStream();
        var env_version = "release";
        if (!_hostingEnvironment.IsProduction())
            env_version = "develop";

        var rst = await WxAppApi.GetWxaCodeUnlimitAsync(appId, stream, scene, page, false, env_version, isHyaline: isHyaline);

        if (rst.errcode != Senparc.Weixin.ReturnCode.请求成功)
        {
            throw new Exception($"生成小程序二维码出错：{JsonSerializer.SerializeToString(rst)}_{page}");
        }

        if (stream.Length < 1024)
        {
            throw new Exception($"生成小程序二维码_图片小于1K：{Encoding.UTF8.GetString(stream.GetBuffer())}_{JsonSerializer.SerializeToString(rst)}");
        }

        var key = $"{Guid.NewGuid().ToString()}.png";
        var url = await _objectStorage.OrdinaryUploadFile(stream, key, key, $"{_config.Aliyun!.Oss!.Dir!}/appletqrcode");

        if (string.IsNullOrWhiteSpace(url))
            _log.Error("oss上传顾问二维码出错", page);

        return url;
    }

    /// <summary>
    /// 生成职位小程序二维码
    /// </summary>
    /// <param name="teamPostId"></param>
    /// <returns></returns>
    public async Task<string> GetWxaCodeUnlimitForPosition(string teamPostId, string? appId = null)
    {
        using var stream = new MemoryStream();
        var env_version = "release";
        if (!_hostingEnvironment.IsProduction())
            env_version = "develop";

        if (string.IsNullOrEmpty(appId))
            appId = _config.WeChat!.SeekerApplet!.AppId!;

        var rst = await WxAppApi.GetWxaCodeUnlimitAsync(appId, stream, $"{(int)AppletShareType.职位}_{teamPostId}", "pages/middlepage/middlepage", false, env_version, isHyaline: true);

        if (rst.errcode != Senparc.Weixin.ReturnCode.请求成功)
        {
            throw new Exception($"生成职位二维码出错：{JsonSerializer.SerializeToString(rst)}_{teamPostId}");
        }

        if (stream.Length < 1024)
        {
            throw new Exception($"生成职位二维码出错_图片小于1K：{Encoding.UTF8.GetString(stream.GetBuffer())}_{JsonSerializer.SerializeToString(rst)}");
        }

        var key = $"{teamPostId}.png";
        var url = await _objectStorage.OrdinaryUploadFile(stream, key, key, $"{_config.Aliyun!.Oss!.Dir!}/post/qrcode");

        if (string.IsNullOrWhiteSpace(url))
            _log.Error("oss上传职位二维码出错", teamPostId);

        return url;
    }

    /// <summary>
    /// 生成职位小程序分享二维码
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public async Task<string> GetWxaCodeUnlimitForSharePosition(string stKey, AppletShareType jumpType, string? appId = null)
    {
        using var stream = new MemoryStream();
        var env_version = "release";
        if (!_hostingEnvironment.IsProduction())
            env_version = "develop";

        if (string.IsNullOrEmpty(appId))
            appId = _config.WeChat!.SeekerApplet!.AppId!;

        var rst = await WxAppApi.GetWxaCodeUnlimitAsync(appId, stream, $"{(int)jumpType}_{stKey}", "pages/middlepage/middlepage", false, env_version, isHyaline: true);

        if (rst.errcode != Senparc.Weixin.ReturnCode.请求成功)
        {
            throw new Exception($"生成职位二维码出错：{JsonSerializer.SerializeToString(stKey)}");
        }

        if (stream.Length < 1024)
        {
            throw new Exception($"生成分享职位二维码出错_图片小于1K：{Encoding.UTF8.GetString(stream.GetBuffer())}_{JsonSerializer.SerializeToString(rst)}");
        }

        var key = $"{stKey}.png";
        var url = await _objectStorage.OrdinaryUploadFile(stream, key, key, $"{_config.Aliyun!.Oss!.Dir!}/sharepost/qrcode");

        if (string.IsNullOrWhiteSpace(url))
            _log.Error("oss上传享职职位二维码出错", stKey);

        return url;
    }

    /// <summary>
    /// 生成顾问的小程序二维码
    /// </summary>
    /// <param name="teamPostId"></param>
    /// <returns></returns>
    public async Task<string> GetWxaCodeUnlimitForHr(string hrId, string? appId = null)
    {

        using var stream = new MemoryStream();
        var env_version = "release";
        if (!_hostingEnvironment.IsProduction())
            env_version = "develop";

        if (string.IsNullOrEmpty(appId))
            appId = _config.WeChat!.SeekerApplet!.AppId!;

        var rst = await WxAppApi.GetWxaCodeUnlimitAsync(appId, stream, $"{(int)AppletShareType.首页}_{hrId}", "pages/middlepage/middlepage", false, env_version, isHyaline: true);

        if (rst.errcode != Senparc.Weixin.ReturnCode.请求成功)
        {
            throw new Exception($"生成顾问二维码出错：{JsonSerializer.SerializeToString(rst)}_{hrId}");
        }

        if (stream.Length < 1024)
        {
            throw new Exception($"生成顾问二维码出错_图片小于1K：{Encoding.UTF8.GetString(stream.GetBuffer())}_{JsonSerializer.SerializeToString(rst)}");
        }

        var key = $"{Md5Helper.Md5(appId)}.png";
        var url = await _objectStorage.OrdinaryUploadFile(stream, key, key, $"{_config.Aliyun!.Oss!.Dir!}/user/{hrId}/applet");

        if (string.IsNullOrWhiteSpace(url))
            _log.Error("oss上传顾问二维码出错", hrId);

        return url;
    }

    /// <summary>
    /// 生成求职者的hr端小程序简历二维码
    /// </summary>
    /// <param name="teamPostId"></param>
    /// <returns></returns>
    public async Task<string> GetWxaCodeUnlimitForSeeker(string seekerId)
    {
        using var stream = new MemoryStream();
        var env_version = "release";
        if (!_hostingEnvironment.IsProduction())
            env_version = "develop";

        var rst = await WxAppApi.GetWxaCodeUnlimitAsync(_config.WeChat!.HrApplet!.AppId, stream, $"{seekerId}", $"pages/candidate/candidate", false, env_version, isHyaline: true);

        if (rst.errcode != Senparc.Weixin.ReturnCode.请求成功)
        {
            throw new Exception($"生成求职者二维码出错：{JsonSerializer.SerializeToString(rst)}_{seekerId}");
        }

        if (stream.Length < 1024)
        {
            throw new Exception($"生成求职者二维码出错_图片小于1K：{Encoding.UTF8.GetString(stream.GetBuffer())}_{JsonSerializer.SerializeToString(rst)}");
        }

        var key = $"{seekerId}.png";
        var url = await _objectStorage.OrdinaryUploadFile(stream, key, key, $"{_config.Aliyun!.Oss!.Dir!}/resumeqrcode");

        if (string.IsNullOrWhiteSpace(url))
            _log.Error("oss上传求职者二维码出错", seekerId);

        return url;
    }

    /// <summary>
    /// 生成证书小程序二维码
    /// </summary>
    /// <param name="adviserId">顾问Id</param>
    /// <returns></returns>
    public async Task<string> GetWxaCodeUnlimitForCertificateList(string adviserId, string? appId = null)
    {
        var cacheKey = Md5Helper.Md5($"{(int)AppletShareType.证书}_{adviserId}");
        var qrKey = MyRedis.Client.HGet<string?>(RedisKey.AdviserShareCertificateQrCodeId, cacheKey);
        if (string.IsNullOrWhiteSpace(qrKey))
        {
            qrKey = Tools.NextId();
            MyRedis.Client.HSet<string?>(RedisKey.AdviserShareCertificateQrCodeId, cacheKey, qrKey);
        }

        var qrInfo = MyRedis.Client.HGet<GetAdviserShareCerQrCodeInfo?>(RedisKey.AdviserShareCertificateQrCodeId, qrKey);

        if (qrInfo == null)
        {
            using var locker = await MyRedis.Lock($"tmcerqr:{qrKey}", 10, false, 100);
            if (locker == null)
                throw new Exception("请求获取锁失败");

            var qrCode = await RetryHelper.Do(async () => await GetWxaCodeUnlimit(ClientApps.SeekerApplet.AppID, adviserId, "Mypackagedetail/certificate/certificate", true));
            qrInfo = new GetAdviserShareCerQrCodeInfo
            {
                QrCode = qrCode,
                AdviserId = adviserId
            };
            MyRedis.Client.HSet<GetAdviserShareCerQrCodeInfo?>(RedisKey.SeekerShareQrCode, qrKey, qrInfo);
        }

        return qrInfo.QrCode ?? string.Empty;
    }
    
    /// <summary>
    /// 生成职位小程序短链接
    /// </summary>
    /// <param name="teamPostId"></param>
    /// <returns></returns>
    public async Task<string> GetWxShortLinkForPosition(string teamPostId, string adviserId, string title)
    {
        try
        {
            var rst = await Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp.ShortLinkApi.GenerateAsync(_config.WeChat!.SeekerApplet!.AppId, $"pages/positiondetail/positiondetail?id={teamPostId}&adviser={adviserId}", title);

            if (rst.errcode != Senparc.Weixin.ReturnCode.请求成功)
            {
                _log.Error("生成职位小程序短链接出错", JsonSerializer.SerializeToString(rst), teamPostId);
                return string.Empty;
            }

            return rst.link;
        }
        catch (Exception e)
        {
            throw new Exception($"Senparc.Weixin出错：{Tools.GetErrMsg(e)}");
        }
    }

    /// <summary>
    /// 生成跳转小程序短链接
    /// </summary>
    /// <param name="appId"></param>
    /// <returns></returns>
    public async Task<string> GetWxShortLinkForJump(string appId, string urladdress = "", string query = "")
    {
        var env_version = "release";
        if (!_hostingEnvironment.IsProduction())
            env_version = "develop";

        //$"{(int)AppletShareType.职位}_{teamPostId}", "pages/middlepage/middlepage",

        try
        {
            var rst = await Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp.UrlLinkApi.GenerateAsync(appId, urladdress, query, envVersion: env_version);

            if (rst.errcode != Senparc.Weixin.ReturnCode.请求成功)
            {
                _log.Error("生成跳转小程序短链接出错", JsonSerializer.SerializeToString(rst));
                return string.Empty;
            }
            return rst.url_link;
        }
        catch (Exception e)
        {
            throw new Exception($"Senparc.Weixin出错：{Tools.GetErrMsg(e)}");
        }
    }

    /// <summary>
    /// 生成跳转小程序Scheme链接
    /// </summary>
    /// <param name="appId"></param>
    /// <returns></returns>
    public async Task<string> GetWxSchemeLinkForJump(string appId, string urladdress = "", string query = "", bool? isExpire = null)
    {
        //var env_version = "release";
        //if (!_hostingEnvironment.IsProduction())
        //    env_version = "develop";

        try
        {
            var rst = await Senparc.Weixin.WxOpen.AdvancedAPIs.UrlSchemeApi.GenerateSchemeAsync(appId,
                new Senparc.Weixin.WxOpen.AdvancedAPIs.UrlScheme.GenerateSchemeJumpWxa(urladdress, query), isExpire);

            if (rst.errcode != Senparc.Weixin.ReturnCode.请求成功)
            {
                _log.Error("生成跳转小程序Scheme链接出错", JsonSerializer.SerializeToString(rst));
                return string.Empty;
            }
            return rst.openlink;
        }
        catch (Exception e)
        {
            throw new Exception($"Senparc.Weixin出错：{Tools.GetErrMsg(e)}");
        }
    }


    /// <summary>
    /// 获取用户微信小程序手机
    /// </summary>
    /// <param name="appID"></param>
    /// <param name="appSecret"></param>
    /// <param name="code"></param>
    /// <param name="encryptedData"></param>
    /// <param name="iv"></param>
    /// <returns></returns>
    public async Task<GetAppletPhoneResponse> GetAppletPhone(string appID, string appSecret, string code, string encryptedData, string iv)
    {
        var tokenResult = await Senparc.Weixin.WxOpen.AdvancedAPIs.Sns.SnsApi.JsCode2JsonAsync(appID, appSecret, code);
        var model = Senparc.Weixin.WxOpen.Helpers.EncryptHelper.DecryptPhoneNumberBySessionKey(tokenResult.session_key, encryptedData, iv);
        return new GetAppletPhoneResponse
        {
            Phone = model,
            WeId = tokenResult
        };
    }

    /// <summary>
    /// 获取用户微信小程序用户
    /// </summary>
    /// <param name="appID"></param>
    /// <param name="appSecret"></param>
    /// <param name="code"></param>
    /// <param name="encryptedData"></param>
    /// <param name="iv"></param>
    /// <returns></returns>
    public async Task<DecodedUserInfo> GetAppletUser(string appID, string appSecret, string code, string encryptedData, string iv)
    {
        var tokenResult = await Senparc.Weixin.WxOpen.AdvancedAPIs.Sns.SnsApi.JsCode2JsonAsync(appID, appSecret, code);
        var model = Senparc.Weixin.WxOpen.Helpers.EncryptHelper.DecodeEncryptedDataToEntityEasy<DecodedUserInfo>(tokenResult.session_key, encryptedData, iv);
        return model;
    }

    // /// <summary>
    // /// 获取公众号accesstoken
    // /// </summary>
    // /// <param name="appID"></param>
    // /// <returns></returns>
    // public async Task<string> GetMpAccessToken(string appId)
    // {
    //     if (!_hostingEnvironment.IsProduction())
    //         throw new Exception("非产品环境触发获取公众号token");

    //     var cacheKey = $"wechatmptoken1";
    //     var token = MyRedis.Client.Get(cacheKey);

    //     if (!string.IsNullOrWhiteSpace(token))
    //         return token;

    //     //加锁获取token
    //     var lockerKey = $"wechatmptoken1Lk";
    //     using var locker = await MyRedis.Lock(lockerKey, 15);

    //     if (locker == null)
    //         throw new Exception("获取公众号accesstoken锁失败");

    //     token = MyRedis.Client.Get(cacheKey);

    //     if (!string.IsNullOrWhiteSpace(token))
    //         return token;

    //     //强制从微信获取新的token
    //     // var tokenResult = await Senparc.Weixin.MP.Containers.AccessTokenContainer.GetAccessTokenResultAsync(appId, true);
    //     var tokenResult = await Senparc.Weixin.MP.CommonAPIs.CommonApi.GetTokenAsync(Constants.WeChatH5AppId, Constants.WeChatH5Secret);
    //     token = tokenResult.access_token;

    //     MyRedis.Client.Set(cacheKey, token, tokenResult.expires_in / 2);
    //     MyRedis.Client.LPush($"{cacheKey}record", token);

    //     if (string.IsNullOrWhiteSpace(token))
    //         throw new Exception("获取公众号accesstoken失败");

    //     return token;
    // }

    /// <summary>
    /// 获取公众号用户信息
    /// </summary>
    /// <param name="openId"></param>
    /// <returns></returns>
    public async Task<UserInfoJson> GetMpUserInfo(string appId, string openId)
    {
        // var accessToken = await GetMpAccessToken(appId);
        try
        {
            // var userInfo = await Senparc.Weixin.MP.AdvancedAPIs.UserApi.InfoAsync(accessToken, openId);
            var userInfo = await Senparc.Weixin.MP.AdvancedAPIs.UserApi.InfoAsync(appId, openId);
            return userInfo;
        }
        catch (Exception e)
        {
            _log.Error($"获取公众号用户信息失败", $"appid:{appId}", e.Message);
            throw;
        }
    }
}

public class GetAppletPhoneResponse
{
    public DecodedPhoneNumber Phone { get; set; } = default!;
    public JsCode2JsonResult WeId { get; set; } = default!;
}