﻿using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces.Hr;
using Config;
using Infrastructure.Proxy;
using Staffing.Model.Hr.Project;
using System.Text;
using Staffing.Model.Hr.Dashboard;

namespace Staffing.Core.Services.Hr;

[Service(ServiceLifetime.Transient)]
public class DataBoardService : IDataBoardService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private ICommonService _commonService;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly JavaDataApi _javaDataApi;
    public DataBoardService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, ICommonService commonService,
        LogManager log, JavaDataApi javaDataApi)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _commonService = commonService;
        _log = log;
        _javaDataApi = javaDataApi;
    }

    public async Task<object> GetPostBoard(GetProjectBoard model)
    {
        await Task.FromResult(1);
        var teamProject = _context.Project_Team.Where(x => x.TeamProjectId == model.TeamProjectId
         && x.HrId == _user.Id)
         .Select(s => new
         {
             s.ProjectId,
             s.Project.HrId,
             s.Type
         }).FirstOrDefault();

        if (teamProject == null)
            throw new NotFoundException("内容不存在");

        var result = new object();
        // if (teamProject.Type == HrProjectType.自己)
        //     result = await _javaDataApi.GetPostBoard(new ProjectBoardReq
        //     {
        //         pageNum = model.pageNum,
        //         pageSize = model.pageSize,
        //         projectId = teamProject.ProjectId,
        //         type = model.type
        //     });
        // else
        //     result = await _javaDataApi.GetTeamPostBoard(new ProjectBoardReq
        //     {
        //         pageNum = model.pageNum,
        //         pageSize = model.pageSize,
        //         teamProjectId = model.TeamProjectId,
        //         type = model.type
        //     });

        return result;
    }

    public async Task<object> GetTeamBoardAnalysis(GetProjectBoard model)
    {
        var teamProject = _context.Project_Team.Where(x => x.TeamProjectId == model.TeamProjectId
         && x.Project.HrId == _user.Id)
         .Select(s => new
         {
             s.ProjectId,
             s.Project.HrId,
             s.Type
         }).FirstOrDefault();

        if (teamProject == null)
            throw new NotFoundException("内容不存在");

        // var result = _javaDataApi.GetTeamBoardAnalysis(new ProjectBoardReq
        // {
        //     pageNum = model.pageNum,
        //     pageSize = model.pageSize,
        //     projectId = teamProject.ProjectId,
        //     type = model.type
        // });
        await Task.FromResult(1);
        return new object();
    }

    public async Task<object> GetTeamBoardStat(GetProjectBoard model)
    {
        var teamProject = _context.Project_Team.Where(x => x.TeamProjectId == model.TeamProjectId
         && x.Project.HrId == _user.Id)
         .Select(s => new
         {
             s.ProjectId,
             s.Project.HrId,
             s.Type
         }).FirstOrDefault();

        if (teamProject == null)
            throw new NotFoundException("内容不存在");

        // var result = _javaDataApi.GetTeamBoardStat(new ProjectBoardReq
        // {
        //     pageNum = model.pageNum,
        //     pageSize = model.pageSize,
        //     projectId = teamProject.ProjectId,
        //     type = model.type
        // });

        await Task.FromResult(1);

        return new object();
    }

    public async Task<object> GetInterviewerStat(GetProjectBoard model)
    {
        var teamProject = _context.Project_Team.Where(x => x.TeamProjectId == model.TeamProjectId
         && x.Project.HrId == _user.Id)
         .Select(s => new
         {
             s.ProjectId,
             s.Project.HrId,
             s.Type
         }).FirstOrDefault();

        if (teamProject == null)
            throw new NotFoundException("内容不存在");

        // var result = _javaDataApi.GetInterviewerStat(new ProjectBoardReq
        // {
        //     pageNum = model.pageNum,
        //     pageSize = model.pageSize,
        //     projectId = teamProject.ProjectId,
        //     type = model.type
        // });

        await Task.FromResult(1);
        return new object();
    }

    public async Task<object> GetRecruitStat(GetProjectBoard model)
    {
        var teamProject = _context.Project_Team.Where(x => x.TeamProjectId == model.TeamProjectId
        && x.Project.HrId == _user.Id)
        .Select(s => new
        {
            s.ProjectId,
            s.Project.HrId,
            s.Type
        }).FirstOrDefault();

        if (teamProject == null)
            throw new NotFoundException("内容不存在");

        // var result = _javaDataApi.GetRecruitStat(new ProjectBoardReq
        // {
        //     pageNum = model.pageNum,
        //     pageSize = model.pageSize,
        //     projectId = teamProject.ProjectId
        // });

        await Task.FromResult(1);

        return new object();
    }

    public object GetChannelSeeker(GetChannelSeeker model)
    {
        //腾讯投流运营数据字段：C端注册数量，C端报名数量（有效线索），C端转化数据（筛选、面试、入职、归档），价值统计（单个项目入职人数*项目分润  ---求和）

        var C端注册 = _context.Talent_Platform.Where(x => x.ChannelId == model.ChannelId).Count();
        var 报名次数 = _context.Post_Delivery.Where(x => x.ChannelId == model.ChannelId).Count();
        var 报名人数 = _context.Post_Delivery.Where(x => x.ChannelId == model.ChannelId).Select(s => s.SeekerId).Distinct().Count();

        var 招聘状态 = _context.Recruit.Where(x => x.Post_Delivery.ChannelId == model.ChannelId)
        .GroupBy(s => s.Status).Select(s => new { s.Key, Ct = s.Count() });

        var 入职人数 = _context.Recruit.Where(x => x.Post_Delivery.ChannelId == model.ChannelId
        && x.Status == RecruitStatus.Induction).Count();

        var 交付成功 = _context.Post_Bounty.Where(x => x.Recruit.Post_Delivery.ChannelId == model.ChannelId
        && x.Status == BountyStatus.交付中 && x.GuaranteeStatus == GuaranteeStatus.过保).GroupBy(g => 1)
        .Select(s => new
        {
            //协同结算金额 = s.Sum(c => c.ClueBounty),
            协同结算金额 = s.Sum(c => c.Money * c.ClueRate),
            渠道商结算金额 = 0
        }).FirstOrDefault();

        var sb = new StringBuilder();

        sb.AppendLine($"C端注册:{C端注册}");
        sb.AppendLine($"报名人数:{报名人数}");
        sb.AppendLine($"报名次数:{报名次数}");
        sb.AppendLine($"入职人数:{入职人数}");
        sb.AppendLine($"协同结算金额:{交付成功?.协同结算金额}");
        sb.AppendLine($"渠道商结算金额:{交付成功?.渠道商结算金额}");
        sb.AppendLine($"招聘流程状态:");
        foreach (var item in 招聘状态)
        {
            sb.AppendLine($"{item.Key.GetDescription()}:{item.Ct}");
        }

        return sb.ToString();
    }

    public SummaryDataResponse GetSummaryData()
    {
        var response = new SummaryDataResponse();
        var now = DateTime.Now;
        var currentMonth = new DateTime(now.Year, now.Month, 1);
        response.ThirtyDaysVisits = _context.Talent_Platform.Where(w => w.HrId == _user.Id && w.SeekerVisitTime >= now.AddMonths(-1)).Count();
        response.MonthTalentAdded = _context.Talent_Platform.Where(w => w.HrId == _user.Id && w.CreatedTime >= currentMonth).Count();
        // 主投简历：做为主创，协同直接收到的简历 type=0或者type=1
        response.Zhutou = _context.Post_Delivery.Where(w => w.Post_Team.Project_Team.HrId == _user.Id).Count();
        // 内推简历：做为主创，协同收到的简历 type=1
        response.Neitui = _context.Post_Delivery.Where(w => w.Post_Team.Project_Team.Project.HrId == _user.Id
        && w.Post_Team.Project_Team.Type == HrProjectType.协同).Count();

        var predicate = PredicateBuilder.New<Recruit>();
        predicate = predicate.And(o => (o.Post_Delivery.Post_Team.Project_Team.HrId == _user.Id && o.Post_Delivery.Post_Team.Project_Team.Type == HrProjectType.协同)
        || (o.HrId == _user.Id && o.Status == RecruitStatus.InterviewerScreening && !o.Post_Delivery.Post.IsResumeExempt)
        || (o.HrId == _user.Id && o.Status == RecruitStatus.Interview && o.Recruit_Interview.OrderByDescending(o => o.CreatedTime).FirstOrDefault().Outcome == RecruitInterviewOutcome.Passed)
        || (o.HrId == _user.Id && (o.Status == RecruitStatus.Offer || o.Status == RecruitStatus.Induction || o.Status == RecruitStatus.FileAway))
        || (o.HrId == _user.Id && o.Post_Delivery.Post_Team.Project_Team.Type == HrProjectType.自己));

        var chushaiWhere = PredicateBuilder.New<Recruit>(p => p.Status == RecruitStatus.HrScreening);
        response.Chushai = _context.Recruit.Where(chushaiWhere.And(predicate)).Count();
        // 面试官筛选 - 待反馈
        var mianshiguanWhere = PredicateBuilder.New<Recruit>(p => p.Status == RecruitStatus.InterviewerScreening
        && p.Recruit_Interviewer_Screen.OrderByDescending(o => o.CreatedTime).FirstOrDefault().Status == RecruitInterviewerScreenStatus.NoFedBack);
        response.Mianshiguan = _context.Recruit.Where(mianshiguanWhere.And(predicate)).Count();
        // 面试待安排 - 面试官筛选已通过
        var mianshiWhere = PredicateBuilder.New<Recruit>(p => p.Status == RecruitStatus.InterviewerScreening
        && p.Recruit_Interviewer_Screen.OrderByDescending(o => o.CreatedTime).FirstOrDefault().Status == RecruitInterviewerScreenStatus.Adopt);
        response.Mianshi = _context.Recruit.Where(mianshiWhere.And(predicate)).Count();
        // 入职待确认 - 面试通过 + offer
        var ruzhiWhere = PredicateBuilder.New<Recruit>(p => (p.Status == RecruitStatus.Interview
        && p.Recruit_Interview.OrderByDescending(o => o.CreatedTime).FirstOrDefault().Outcome == RecruitInterviewOutcome.Passed)
        || p.Status == RecruitStatus.Offer);
        response.Ruzhi = _context.Recruit.Where(ruzhiWhere.And(predicate)).Count();
        // 计费待确认 todo:按天入职过保订单表的recruitid字段存在'-'，但是应该能关联到一条，所以这里没问题，但是结算中心个人订单可能有问题
        var jifeiWhere = PredicateBuilder.New<Recruit>(o => !o.Post_Bounty.Deleted && o.Post_Bounty.Status == BountyStatus.交付中);
        response.Jifei = _context.Recruit.Where(jifeiWhere.And(predicate)).Count();
        // 佣金未结算 todo:按天入职过保订单表的recruitid字段存在'-'，这里应该关联不到，因为交付成功都会打上'-'
        // var yongjinWhere = PredicateBuilder.New<Recruit>(o => !o.Post_Bounty.Deleted && o.Post_Bounty.Status == BountyStatus.结算中
        // && o.Post_Bounty.SettlementStatus == SettlementType.待结算);
        // response.Yongjin = _context.Recruit.Where(yongjinWhere.And(predicate)).Count();
        // 逾期强制扣款
        // var yuqiWhere = PredicateBuilder.New<Recruit>(o => !o.Post_Bounty.Deleted && o.Post_Bounty.SettlementStatus == SettlementType.已结算
        // && o.FileRemarks.Contains("自动入职"));
        // response.Yuqi = _context.Recruit.Where(yuqiWhere.And(predicate)).Count();

        response.PostFreezeAmount = _context.Ndn_Hr_Wallet.Where(w => w.UserId == _user.Id).Sum(s => s.Amount);
        return response;
    }
}