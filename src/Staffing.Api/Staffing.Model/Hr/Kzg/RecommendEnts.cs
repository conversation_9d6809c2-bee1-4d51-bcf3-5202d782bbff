using Config.CommonModel;
using Config.CommonModel.Noah;
using Config.Enums;

namespace Staffing.Model.Hr.Kzg;

public class GetRecommendEnts : QueryRequest
{
    /// <summary>
    /// 状态
    /// </summary>
    public HrRecommendEntStatus? Status { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public List<HrRecommendEntSource>? Source { get; set; }

    /// <summary>
    /// 级别
    /// </summary>
    public HrRecommendEntLevel? Level { get; set; }

    /// <summary>
    /// 0=全部,1=今日新职位,2=今日新投递
    /// </summary>
    public int NoahCategory { get; set; }

    /// <summary>
    /// 搜索
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 网点Id
    /// </summary>
    public string? OutletId { get; set; }

    /// <summary>
    /// 最新跟进时间（开始）
    /// </summary>
    public DateTime? BeginFollowUpTime { get; set; }

    /// <summary>
    /// 最新跟进时间（截止）
    /// </summary>
    public DateTime? EndFollowUpTime { get; set; }

    /// <summary>
    /// 创建时间（开始）
    /// </summary>
    public DateTime? BeginCreatedTime { get; set; }

    /// <summary>
    /// 创建时间（截止）
    /// </summary>
    public DateTime? EndCreatedTime { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public List<string>? AdviserIds { get; set; }
}

public class GetNPEntJobPositions
{
    /// <summary>
    /// 诺聘的企业ID
    /// </summary>
    public string? EnterpriseID { get; set; }
    /// <summary>
    /// 按岗位名称模糊检索
    /// </summary>
    public string? Search { get; set; }
    public int PageIndex { get; set; }
    public int PageSize { get; set; }
}

public class GetNPDeliver
{
    /// <summary>
    /// 诺聘的企业ID
    /// </summary>
    public string? EnterpriseID { get; set; }
    /// <summary>
    /// 诺聘的岗位ID
    /// </summary>
    public string? PK_WPID { get; set; }
    public int PageIndex { get; set; }
    public int PageSize { get; set; }
}

public class SaveNPUserContact
{
    /// <summary>
    /// 求职者ID
    /// </summary>
    public string? UserID { get; set; }
    /// <summary>
    /// 目的
    /// </summary>
    public string? Design { get; set; }
    /// <summary>
    /// 结果
    /// </summary>
    public string? Result { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}

public class GetNPUserContact : QueryRequest
{
    /// <summary>
    /// 求职者ID
    /// </summary>
    public string? UserID { get; set; }
}

public class GetRecommendEntsResponse : QueryResponse
{
    public List<GetRecommendEntInfo> Rows { get; set; } = new List<GetRecommendEntInfo>();
}

public class GetNPEntJobPositionsResponse : QueryResponse
{
    public List<GetNPJobPositionsInfo> Rows { get; set; } = new List<GetNPJobPositionsInfo>();
}

public class GetNPDeliverResponse : QueryResponse
{
    public List<GetNPDeliverInfo> Rows { get; set; } = new List<GetNPDeliverInfo>();
}

public class GetRecommendEntInfo
{
    public string? Id { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? AdviserId { get; set; }

    /// <summary>
    /// 顾问姓名
    /// </summary>
    public string? AdviserName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public HrRecommendEntStatus? Status { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public HrRecommendEntSource Source { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public string? SourceName { get; set; }

    /// <summary>
    /// 级别
    /// </summary>
    public HrRecommendEntLevel? Level { get; set; }

    /// <summary>
    /// 三方企业Id
    /// </summary>
    public string? TEntId { get; set; }

    /// <summary>
    /// 公司名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 职务
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// Hr名称
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// Hr电话
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 反馈
    /// </summary>
    public string? FeedBack { get; set; }

    /// <summary>
    /// 网点Id
    /// </summary>
    public string? OutletId { get; set; }

    /// <summary>
    /// 网点名称
    /// </summary>
    public string? OutletName { get; set; }

    /// <summary>
    /// 销帮帮销售机会名称
    /// </summary>
    public string? XbbXsjhName { get; set; }

    /// <summary>
    /// 销帮帮销售机会录入人
    /// </summary>
    public string? XbbXsjhUName { get; set; }

    /// <summary>
    /// 今日投递数量
    /// </summary>
    public int TodayDeliverCount { get; set; }

    /// <summary>
    /// 最新跟进时间
    /// </summary>
    public DateTime? FollowUpTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}

public class GetNPUserContactResponse : QueryResponse
{
    public List<GetNPUserContactInfo> Rows { get; set; } = new List<GetNPUserContactInfo>();
}

public class UpdateRecommendEnts
{
    public string? Id { get; set; }

    // /// <summary>
    // /// 状态
    // /// </summary>
    // public HrRecommendEntStatus? Status { get; set; }

    // /// <summary>
    // /// 级别
    // /// </summary>
    // public HrRecommendEntLevel? Level { get; set; }

    /// <summary>
    /// 反馈
    /// </summary>
    public string? FeedBack { get; set; }
}

public class GetRemarkDicResponse
{
    public List<GeneralDic> Rows { get; set; } = default!;
}

public class GetOutletsResponse
{
    /// <summary>
    /// 网点
    /// </summary>
    public List<GetOutletsInfo> Rows { get; set; } = new List<GetOutletsInfo>();
}

public class GetOutletsInfo
{
    /// <summary>
    /// 网点Id
    /// </summary>
    public string? OutletId { get; set; }

    /// <summary>
    /// 网点名称
    /// </summary>
    public string? OutletName { get; set; }
}

public class GetRecommendHrsResponse
{
    public List<GetRecommendHrsInfo> Rows { get; set; } = new List<GetRecommendHrsInfo>();
}

public class GetRecommendHrsInfo
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 名字
    /// </summary>
    public string? Name { get; set; }
}