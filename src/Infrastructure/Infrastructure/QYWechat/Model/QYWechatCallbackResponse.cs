﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Infrastructure.QYWechat.Model
{
    /// <summary>
    /// 企业微信回调POST响应
    /// </summary>
    [Serializable]
    [XmlRoot("xml")]
    public class QYWechatCallbackResponsePost
    {
        /// <summary>
        /// 经过加密的消息结构体
        /// </summary>
        [XmlElement("Encrypt")]
        public required string Encrypt { get; set; }

        /// <summary>
        /// 消息签名
        /// </summary>
        [XmlAttribute("MsgSignature")]
        public required string MsgSignature { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        [XmlAttribute("TimeStamp")]
        public long TimeStamp { get; set; }

        /// <summary>
        /// 随机数，由企业自行生成
        /// </summary>
        [XmlAttribute("Nonce")]
        public required string Nonce { get; set; }
    }
}
