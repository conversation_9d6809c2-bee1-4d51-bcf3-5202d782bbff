﻿using System.Data.Entity;
using Config.Enums;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Model.CentreService.User;
using Staffing.Core.Interfaces;
using Staffing.Core.Interfaces.CentreService;
using Staffing.Core.Interfaces.Common;
using Config;
using Infrastructure.CommonService;
using EntityFrameworkCore.AutoHistory.Extensions;

namespace Staffing.Core.Services.CentreService;

[Service(ServiceLifetime.Transient)]
public class HrService : IHrService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private ISharedService _sharedService;
    private ICommonService _commonService;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CommonUserService _commonUserService;
    private readonly DingDingHelper _dingDingHelper;
    public HrService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, ICommonService commonService, DingDingHelper dingDingHelper,
        LogManager log, ISharedService sharedService, CommonUserService commonUserService)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _commonService = commonService;
        _log = log;
        _sharedService = sharedService;
        _commonUserService = commonUserService;
        _dingDingHelper = dingDingHelper;
    }

    public GetHrListResponse GetHrList(GetHrList model)
    {
        var result = new GetHrListResponse();

        var predicate = PredicateBuilder.New<User_Hr>(x => x.Status != UserStatus.Ignore);

        if (!string.IsNullOrWhiteSpace(model.Mobile))
            predicate = predicate.And(x => x.User.Mobile == model.Mobile);

        if (!string.IsNullOrWhiteSpace(model.EntName))
            predicate = predicate.And(x => x.Enterprise.Name.Contains(model.EntName));

        if (!string.IsNullOrWhiteSpace(model.NickName))
            predicate = predicate.And(x => x.NickName.Contains(model.NickName));

        if (model.Status.HasValue)
            predicate = predicate.And(x => x.Status == model.Status);

        var sql = _context.User_Hr
        .Where(predicate);

        result.Total = sql.Select(s => new { a = 1 }).Count();

        result.Rows = sql
        .OrderByDescending(o => o.Status == UserStatus.UnReviewed)
        .ThenByDescending(o => o.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetHrInfo
        {
            CreatedTime = s.CreatedTime,
            Mobile = s.User.Mobile,
            Avatar = s.Avatar,
            EntId = s.EntId,
            EntLogo = s.Enterprise.LogoUrl,
            EntName = s.Enterprise.Name,
            EntAbbr = s.Enterprise.Abbreviation,
            EntNuoId = s.Enterprise.NuoId,
            NickName = s.NickName,
            NuoId = s.NuoId,
            Source = s.Source,
            Status = s.Status,
            UserId = s.UserId,
            Post = s.Post,
            DingMobile = s.User_DingDing.DingMobile,
            ApplicationTime = s.ApplicationTime,
            BusinessSms = (int?)s.User_Sms.BusinessSms ?? 0
        }).ToList();

        return result;
    }

    public EmptyResponse HrAudit(HrAudit model)
    {
        var status = new List<UserStatus> { UserStatus.Active, UserStatus.UnReviewed, UserStatus.Rejected };
        if (model.Status == null || !status.Contains(model.Status.Value))
            throw new BadRequestException("Status参数错误");

        var hr = _context.User_Hr.FirstOrDefault(x => x.UserId == model.UserId);

        if (hr == null)
            throw new BadRequestException("用户不存在");

        hr.Status = model.Status.Value;

        //加入记录
        var hrAudit = new User_Hr_Audit
        {
            UserId = model.UserId!,
            AdminId = _user.NuoPinId ?? string.Empty,
            AdminName = _user.Account ?? string.Empty,
            Describe = model.Describe,
            EntId = hr.EntId ?? string.Empty,
            Status = hr.Status
        };
        _context.Add(hrAudit);

        _context.SaveChanges();

        //清理token缓存，重新读取内容
        _commonUserService.CleanHrAccessToken(hr.NuoId);

        return new EmptyResponse();
    }

    public SearchEntNameResponse SearchEntName(SearchEntName model)
    {
        var result = new SearchEntNameResponse();

        var predicate = PredicateBuilder.New<Enterprise>(x => x.Status == EnterpriseStatus.Active);

        if (!string.IsNullOrWhiteSpace(model.EntName))
            predicate = predicate.And(x => x.Name.Contains(model.EntName));

        var sql = _context.Enterprise
        .Where(predicate);

        result.Rows = sql
        .OrderBy(o => o.Name)
        .Select(s => new GeneralDic
        {
            Id = s.EntId,
            Name = s.Name
        }).Take(100).ToList();

        return result;
    }

    public EmptyResponse HrChangeEnt(HrChangeEnt model)
    {
        if (string.IsNullOrWhiteSpace(model.HrId) || string.IsNullOrWhiteSpace(model.EntId))
            throw new BadRequestException("缺少参数");

        var hr = _context.User_Hr.FirstOrDefault(x => x.UserId == model.HrId);

        if (hr == null)
            throw new BadRequestException("hr不存在");

        var ent = _context.Enterprise.FirstOrDefault(x => x.EntId == model.EntId);

        if (ent == null)
            throw new BadRequestException("企业不存在");

        hr.EntId = model.EntId;

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 平台端绑定钉钉用户（协助hr绑定）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<BindDingDingRsponse> BindDingDing(BindDingDingRequest model)
    {
        BindDingDingRsponse retModel = new BindDingDingRsponse();

        var userHr = _context.User_Hr.FirstOrDefault(o => o.User.Mobile == model.HrPhone);
        if (userHr is null)
        {
            throw new BadRequestException("用户不存在！");
        }

        bool IsBinding = _context.User_DingDing.Any(o => o.UserId == userHr.UserId);

        if (IsBinding)
        {
            throw new BadRequestException("用户已绑定钉钉，无法重复绑定！");
        }

        var dingAuthorization = await _dingDingHelper.GetDingDingUserIdByPhone(new Config.CommonModel.DingDing.GetDingDingUserIdByPhoneRequest { mobile = model.DingPhone });

        if (dingAuthorization.userid is not null)
        {
            var dingUserDetails = await _dingDingHelper.GetDingDingUserDetailsByUserId(new Config.CommonModel.DingDing.GetDingDingUserDetailsByUserIdRequest { userid = dingAuthorization.userid });
            if (dingUserDetails is not null)
            {
                bool IsBinding_dingding = _context.User_DingDing.Any(o => o.DingUserid == dingUserDetails.userid);
                if (IsBinding_dingding)
                {
                    throw new BadRequestException("钉钉用户已存在！");
                }

                User_DingDing user_DingDing = new User_DingDing()
                {
                    UserId = userHr.UserId,
                    CreatedTime = DateTime.Now,
                    IsPush = true,
                    UpdatedTime = DateTime.Now,
                    DingUserid = dingUserDetails.userid!,
                    DingAvatar = dingUserDetails.avatar ?? string.Empty,
                    DingJobNo = dingUserDetails.job_number ?? string.Empty,
                    DingMobile = dingUserDetails.mobile ?? string.Empty,
                    DingName = dingUserDetails.name ?? string.Empty,
                    DingUnionid = dingUserDetails.unionid ?? string.Empty
                };

                _context.Add(user_DingDing);
                _context.SaveChanges();

                retModel.DingMobile = dingUserDetails.mobile ?? string.Empty;
                retModel.DingJobNo = dingUserDetails.job_number ?? string.Empty;
                retModel.DingName = dingUserDetails.name ?? string.Empty;
                retModel.DingAvatar = dingUserDetails.avatar ?? string.Empty;

                MyRedis.Client.SAdd(SubscriptionKey.HrUpdateMobile, user_DingDing.UserId);
            }
            else
            {
                throw new BadRequestException("获取钉钉用户信息错误！");
            }
        }
        else
        {
            throw new BadRequestException("钉钉授权失败，手机号不匹配！");
        }

        return retModel;
    }

    /// <summary>
    /// 平台端绑定钉钉用户（协助hr绑定）批量
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<BindDingDingModelResponse> BindDingDing(BindDingDingModelRequest model)
    {
        BindDingDingModelResponse retModel = new BindDingDingModelResponse();
        Dictionary<string, string> dic = new Dictionary<string, string>();
        foreach (var i in model.Phones)
        {
            try
            {
                var userHr = _context.User_Hr.FirstOrDefault(o => o.User.Mobile == i);
                if (userHr is null)
                {
                    throw new BadRequestException("用户不存在！");
                }

                bool IsBinding = _context.User_DingDing.Any(o => o.UserId == userHr.UserId);

                if (IsBinding)
                {
                    throw new BadRequestException("用户已绑定钉钉，无法重复绑定！");
                }

                var dingAuthorization = await _dingDingHelper.GetDingDingUserIdByPhone(new Config.CommonModel.DingDing.GetDingDingUserIdByPhoneRequest { mobile = i });

                if (dingAuthorization.userid is not null)
                {
                    var dingUserDetails = await _dingDingHelper.GetDingDingUserDetailsByUserId(new Config.CommonModel.DingDing.GetDingDingUserDetailsByUserIdRequest { userid = dingAuthorization.userid });
                    if (dingUserDetails is not null)
                    {
                        bool IsBinding_dingding = _context.User_DingDing.Any(o => o.DingUserid == dingUserDetails.userid);
                        if (IsBinding_dingding)
                        {
                            throw new BadRequestException("钉钉用户已存在！");
                        }

                        User_DingDing user_DingDing = new User_DingDing()
                        {
                            UserId = userHr.UserId,
                            CreatedTime = DateTime.Now,
                            IsPush = true,
                            UpdatedTime = DateTime.Now,
                            DingUserid = dingUserDetails.userid!,
                            DingAvatar = dingUserDetails.avatar ?? string.Empty,
                            DingJobNo = dingUserDetails.job_number ?? string.Empty,
                            DingMobile = dingUserDetails.mobile ?? string.Empty,
                            DingName = dingUserDetails.name ?? string.Empty,
                            DingUnionid = dingUserDetails.unionid ?? string.Empty
                        };

                        _context.Add(user_DingDing);
                        _context.SaveChanges();
                        MyRedis.Client.SAdd(SubscriptionKey.HrUpdateMobile, user_DingDing.UserId);
                    }
                    else
                    {
                        throw new BadRequestException("获取钉钉用户信息错误！");
                    }
                }
                else
                {
                    throw new BadRequestException("钉钉授权失败，手机号不匹配！");
                }
            }
            catch (Exception ex)
            {
                dic.Add(i, ex.Message);
            }
        }

        retModel.ErrorPhone = dic;

        return retModel;
    }

    /// <summary>
    /// 获取钉钉用户列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public GetDingDingListResponse GetDingDingList(GetDingDingListRequest model)
    {
        GetDingDingListResponse retModel = new GetDingDingListResponse();

        var predicate = PredicateBuilder.New<DingDing>(o => o.DingMobile != string.Empty);

        if (model.Type is not null)
        {
            switch (model.Type)
            {
                case 0:
                    if (!string.IsNullOrEmpty(model.Search))
                    {
                        predicate.And(o => o.DingName == model.Search);
                    }
                    break;
                case 1:
                    if (!string.IsNullOrEmpty(model.Search))
                    {
                        predicate.And(o => o.DingMobile == model.Search);
                    }
                    break;
                case 2:
                    if (!string.IsNullOrEmpty(model.Search))
                    {
                        predicate.And(o => o.DingJobNo == model.Search);
                    }
                    break;
                case 3:
                    if (!string.IsNullOrEmpty(model.Search))
                    {
                        var dingUserId = _context.User_DingDing.Where(o => o.User_Hr.User.Mobile == model.Search).Select(o => o.DingUserid).ToList();
                        if (dingUserId.Count > 0)
                        {
                            predicate.And(o => dingUserId.Contains(o.DingUserid));
                        }
                        else
                        {
                            predicate.And(o => false);
                        }
                    }
                    break;
                case 4:
                    if (!string.IsNullOrEmpty(model.Search))
                    {
                        predicate.And(o => o.DingBranch == model.Search);
                    }
                    break;
                case 5:
                    if (!string.IsNullOrEmpty(model.Search))
                    {
                        predicate.And(o => o.DingTitle == model.Search);
                    }
                    break;
            }
        }

        var dingdingModel = _context.DingDing.Where(predicate);

        retModel.Total = dingdingModel.Count();

        retModel.Rows = dingdingModel
        .OrderByDescending(m => m.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(m => new GetDingDingListModel
        {
            DingUserid = m.DingUserid,
            DingMobile = m.DingMobile,
            DingAvatar = m.DingAvatar,
            DingBranch = m.DingBranch,
            DingJobNo = m.DingJobNo,
            DingName = m.DingName,
            DingTitle = m.DingTitle,
            PushTime = m.PushTime,

        }).ToList();

        if (retModel.Rows.Count > 0)
        {
            List<string> dingUserids = retModel.Rows.Select(o => o.DingUserid).ToList();

            var dingUserModel = _context.User_DingDing.Where(o => dingUserids.Contains(o.DingUserid)).Select(o => new { DingUserid = o.DingUserid, Mobile = o.User_Hr.User.Mobile }).ToList();

            foreach (var i in dingUserModel)
            {
                foreach (var m in retModel.Rows)
                {
                    if (i.DingUserid == m.DingUserid)
                    {
                        m.IsBind = i.Mobile == null ? false : true;
                        m.NuoPinMobile = i.Mobile ?? string.Empty;
                    }
                }
            }
        }

        return retModel;
    }

    /// <summary>
    /// 添加钉钉用户
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<EmptyResponse> AddDingDingUser(AddDingDingUserRequest model)
    {
        var dingdingModel = _context.DingDing.Where(o => o.DingMobile == model.Mobile).FirstOrDefault();
        if (dingdingModel is not null)
        {
            throw new BadRequestException("钉钉用户已存在！");
        }

        var dingAuthorization = await _dingDingHelper.GetDingDingUserIdByPhone(new Config.CommonModel.DingDing.GetDingDingUserIdByPhoneRequest { mobile = model.Mobile });
        if (dingAuthorization.userid is not null)
        {
            var dingUserDetails = await _dingDingHelper.GetDingDingUserDetailsByUserId(new Config.CommonModel.DingDing.GetDingDingUserDetailsByUserIdRequest { userid = dingAuthorization.userid });
            if (dingUserDetails is not null)
            {
                bool IsBinding_dingding = _context.DingDing.Any(o => o.DingUserid == dingUserDetails.userid);
                if (IsBinding_dingding)
                {
                    throw new BadRequestException("钉钉用户已存在！");
                }

                string branchName = string.Empty;
                if (dingUserDetails.dept_id_list is not null && dingUserDetails.dept_id_list.Count > 0)
                {
                    var branchModel = await _dingDingHelper.GetDingBranchByBranchId(dingUserDetails.dept_id_list.Last());
                    if (branchModel is not null)
                    {
                        branchName = branchModel.name ?? string.Empty;
                    }
                }

                DingDing addModel = new DingDing()
                {
                    CreatedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now,
                    DingUserid = dingUserDetails.userid!,
                    DingAvatar = dingUserDetails.avatar ?? string.Empty,
                    DingJobNo = dingUserDetails.job_number ?? string.Empty,
                    DingMobile = dingUserDetails.mobile ?? string.Empty,
                    DingName = dingUserDetails.name ?? string.Empty,
                    DingUnionid = dingUserDetails.unionid ?? string.Empty,
                    DingBranch = branchName,
                    DingTitle = dingUserDetails.title ?? string.Empty,
                    PushTime = null
                };

                _context.Add(addModel);
                _context.SaveChanges();

                return new EmptyResponse();
            }
            else
            {
                throw new BadRequestException("获取钉钉用户信息错误！");
            }
        }
        else
        {
            throw new BadRequestException("钉钉授权失败，手机号不匹配！");
        }
    }

    /// <summary>
    /// 绑定钉钉用户
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse BindDingDingByDingUserId(BindDingDingByDingUserIdRequest model)
    {
        var dingdingModel = _context.DingDing.Where(o => o.DingUserid == model.DingUserid).FirstOrDefault();
        if (dingdingModel is null)
        {
            throw new BadRequestException("钉钉用户不存在！");
        }

        var userModel = _context.User.Where(o => o.Mobile == model.NuoPinMobile).FirstOrDefault();
        if (userModel is null)
        {
            throw new BadRequestException("诺聘用户不存在！");
        }

        var userHrModel = _context.User_Hr.Where(o => o.UserId == userModel.UserId).FirstOrDefault();
        if (userHrModel is null)
        {
            throw new BadRequestException("用户角色不是hr！");
        }

        var userdingdingModel = _context.User_DingDing.Where(o => o.UserId == userModel.UserId).FirstOrDefault();
        if (userdingdingModel is not null)
        {
            throw new BadRequestException("用户已绑定钉钉！");
        }

        User_DingDing addModel = new User_DingDing()
        {
            CreatedTime = DateTime.Now,
            DingAvatar = dingdingModel.DingAvatar,
            DingBranch = dingdingModel.DingBranch,
            DingJobNo = dingdingModel.DingJobNo,
            DingMobile = dingdingModel.DingMobile,
            DingName = dingdingModel.DingName,
            DingTitle = dingdingModel.DingTitle,
            DingUnionid = dingdingModel.DingUnionid,
            DingUserid = dingdingModel.DingUserid,
            IsPush = true,
            UpdatedTime = DateTime.Now,
            UserId = userModel.UserId,
        };

        _context.Add(addModel);
        _context.SaveChanges();
        MyRedis.Client.SAdd(SubscriptionKey.HrUpdateMobile, addModel.UserId);
        return new EmptyResponse();
    }

    /// <summary>
    /// 解除绑定钉钉
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse UnBindDingDingByDingUserId(UnBindDingDingByDingUserIdRequest model)
    {
        var dingdingModel = _context.DingDing.Where(o => o.DingUserid == model.DingUserid).FirstOrDefault();
        if (dingdingModel is null)
        {
            throw new BadRequestException("钉钉用户不存在！");
        }

        var userdingdingModel = _context.User_DingDing.Where(o => o.DingUserid == model.DingUserid).FirstOrDefault();
        if (userdingdingModel is null)
        {
            throw new BadRequestException("用户未绑定钉钉！");
        }

        _context.Remove(userdingdingModel);
        _context.SaveChanges();

        MyRedis.Client.SAdd(SubscriptionKey.HrUpdateMobile, userdingdingModel.UserId);
        return new EmptyResponse();
    }

    /// <summary>
    /// 删除钉钉用户
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse DeleteDingDing(DeleteDingDingRequest model)
    {
        if (model.DingUserid.Count == 0)
        {
            throw new BadRequestException("请求参数不可以为空！");
        }

        foreach (var i in model.DingUserid)
        {
            var dingdingModel = _context.DingDing.Where(o => o.DingUserid == i).FirstOrDefault();
            if (dingdingModel is null)
            {
                throw new BadRequestException("钉钉用户不存在！");
            }

            var userDingDingModel = _context.User_DingDing.Where(o => o.DingUserid == i).FirstOrDefault();
            if (userDingDingModel != null)
            {
                _context.Remove(dingdingModel);
                MyRedis.Client.SAdd(SubscriptionKey.HrUpdateMobile, userDingDingModel.UserId);
            }

            //尝试删除钉钉绑定关系表
            if (userDingDingModel is not null)
            {
                _context.Remove(userDingDingModel);
            }

            _context.SaveChanges();
        }
        return new EmptyResponse();
    }

    public async Task<EmptyResponse> DdBind(DdBindRequest model)
    {
        var userHr = _context.User_Hr.FirstOrDefault(o => o.UserId == model.UserId);
        if (userHr is null)
            throw new BadRequestException("用户不存在！");

        if (string.IsNullOrEmpty(model.DingPhone))
            throw new BadRequestException("钉钉手机号不能为空！");

        var dingAuthorization = await _dingDingHelper.GetDingDingUserIdByPhone(new Config.CommonModel.DingDing.GetDingDingUserIdByPhoneRequest { mobile = model.DingPhone });
        if (dingAuthorization is null)
            throw new BadRequestException("获取钉钉用户信息错误！");

        var dingUserDetails = await _dingDingHelper.GetDingDingUserDetailsByUserId(new Config.CommonModel.DingDing.GetDingDingUserDetailsByUserIdRequest { userid = dingAuthorization.userid });
        if (dingUserDetails is null)
            throw new BadRequestException("获取钉钉信息失败");

        if (_context.User_DingDing.Any(o => o.DingUserid == dingUserDetails.userid && o.UserId != model.UserId))
            throw new BadRequestException("该钉钉账号已绑定其他顾问");

        var userDd = _context.User_DingDing.FirstOrDefault(o => o.UserId == model.UserId);

        if (userDd == null)
        {
            userDd = new User_DingDing()
            {
                UserId = userHr.UserId,
                IsPush = true
            };
            _context.Add(userDd);
        }

        userDd.DingUserid = dingUserDetails.userid!;
        userDd.DingAvatar = dingUserDetails.avatar ?? string.Empty;
        userDd.DingJobNo = dingUserDetails.job_number ?? string.Empty;
        userDd.DingMobile = dingUserDetails.mobile ?? string.Empty;
        userDd.DingName = dingUserDetails.name ?? string.Empty;
        userDd.DingUnionid = dingUserDetails.unionid ?? string.Empty;
        userDd.UpdatedTime = DateTime.Now;

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public EmptyResponse DdUnBind(DdUnBindRequest model)
    {
        var userDd = _context.User_DingDing.FirstOrDefault(o => o.UserId == model.UserId);
        if (userDd is null)
            throw new BadRequestException("未绑定钉钉账号");

        _context.Remove(userDd);
        _context.SaveChanges();

        return new EmptyResponse();
    }
}