using Config;
using Flurl;
using Flurl.Http;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using ServiceStack.Text;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Singleton)]
public class TencentAdHelper
{
    private readonly ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly string adUrl;
    // private const int TencentAdAccount = ********;
    public TencentAdHelper(IOptionsSnapshot<ConfigManager> config,
    IHostEnvironment hostingEnvironment, LogManager log)
    {
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
        adUrl = _hostingEnvironment.IsProduction() ? $"https://api.e.qq.com/v1.1" : "https://api.e.qq.com/v1.1";//"https://sandbox-api.e.qq.com";
    }

    /// <summary>
    /// 行为上报
    /// </summary>
    /// <param name="clickId"></param>
    /// <returns></returns>
    public async Task<string> Report(string clickId, long setId, int accountId, string at)
    {
        var url = $"{adUrl}/user_actions/add";
        var rstStr = await url.SetQueryParams(GetGlobalPara(at))
        .PostJsonAsync(new
        {
            account_id = accountId,
            user_action_set_id = setId,
            actions = new List<dynamic>
            {
                new
                {
                    action_time = 0,
                    // action_type = "RESERVATION",
                    action_type = "REGISTER",
                    external_action_id = clickId,
                    trace = new
                    {
                        click_id = clickId
                    }
                }
            }
        }).ReceiveString();

        TencentAdBaseResponse<object?>? rst;
        try
        {
            rst = JsonSerializer.DeserializeFromString<TencentAdBaseResponse<object?>>(rstStr);
        }
        catch (Exception e)
        {
            throw new Exception($"腾讯Ad接口解析出错：{Tools.GetErrMsg(e)}", new Exception(rstStr));
        }

        if (rst?.Code != 0)
            throw new Exception($"腾讯Ad接口出错：{rst?.Code}##{rst?.Message}", new Exception($"{clickId}_{setId}_{accountId}"));


        return string.Empty;
    }

    // /// <summary>
    // /// 创建用户行为源
    // /// </summary>
    // /// <returns></returns>
    // public async Task<string> AddUserActionSets(string type, string wechat_app_id, string name)
    // {
    //     var url = $"{adUrl}/user_action_sets/add";
    //     var retstr = await url.SetQueryParams(GetGlobalPara())
    //     .PostJsonAsync(new
    //     {
    //         account_id = TencentAdAccount,
    //         type = type, //"WECHAT_MINI_PROGRAM", //WEB
    //         wechat_app_id = wechat_app_id, //ClientApps.SeekerApplet.AppID
    //         name = name
    //     }).ReceiveString();

    //     return retstr;
    // }

    // /// <summary>
    // /// 获取用户行为源
    // /// </summary>
    // /// <returns></returns>
    // public async Task<string> GetUserActionSets()
    // {
    //     var url = $"{adUrl}/user_action_sets/get?account_id=********";
    //     var retstr = await url.SetQueryParams(GetGlobalPara()).GetAsync().ReceiveString();

    //     return retstr;
    // }

    // /// <summary>
    // /// 获取用户行为源数据报表
    // /// </summary>
    // /// <returns></returns>
    // public async Task<string> GetUserActionSetReport(long setId)
    // {
    //     var url = $"{adUrl}/user_action_set_reports/get?account_id=********&user_action_set_id={setId}";
    //     var retstr = await url.SetQueryParams(GetGlobalPara()).GetAsync().ReceiveString();

    //     return retstr;
    // }

    // /// <summary>
    // /// 刷新Token
    // /// </summary>
    // /// <typeparam name="TReturn"></typeparam>
    // /// <returns></returns>
    // public async Task<TencentAdToken> RefreshToken()
    // {
    //     var cacheKey = $"tencentadtoken";
    //     var token = MyRedis.Client.Get<TencentAdToken>(cacheKey);

    //     if (!string.IsNullOrWhiteSpace(token?.Access_token))
    //         return token;

    //     //加锁获取token
    //     var lockerKey = $"tencentadtokenlk";

    //     using var locker = await MyRedis.Lock(lockerKey, 15, false) ?? throw new Exception("获取腾讯广告accesstoken锁失败");

    //     token = MyRedis.Client.Get<TencentAdToken>(cacheKey);

    //     if (!string.IsNullOrWhiteSpace(token?.Access_token))
    //         return token;

    //     var url = $"{adUrl}/oauth/token";

    //     var rstStr = await url
    //     .PostUrlEncodedAsync(new
    //     {
    //         client_id = "1112017223",
    //         client_secret = "",
    //         grant_type = "refresh_token",
    //         refresh_token = "aff168484a0eb8b513c2e98d2d6e5ea5"
    //     }).ReceiveString();

    //     TencentAdBaseResponse<TencentAdToken>? result;
    //     try
    //     {
    //         result = JsonSerializer.DeserializeFromString<TencentAdBaseResponse<TencentAdToken>>(rstStr);
    //     }
    //     catch (Exception e)
    //     {
    //         throw new Exception($"腾讯Ad接口解析出错：{Tools.GetErrMsg(e)}", new Exception(rstStr));
    //     }

    //     if (result?.Code != 0)
    //         throw new Exception($"腾讯Ad接口出错：{result?.Code}##{result?.Message}");

    //     if (string.IsNullOrWhiteSpace(result?.Data?.Access_token))
    //         throw new Exception("获取腾讯广告accesstoken失败");

    //     MyRedis.Client.Set(cacheKey, token, result.Data.Access_token_expires_in / 2);

    //     return result.Data;
    // }

    private TencentAdGlobalPara GetGlobalPara(string at)
    {
        return new TencentAdGlobalPara
        {
            access_token = at, //"e4c6dfb766d6548e24184ecdf8039fff", //(await RefreshToken()).Access_token,
            nonce = Guid.NewGuid().ToString().Replace("-", ""),
            timestamp = DateTime.Now.ToUnixTime()
        };
    }
}

public class TencentAdToken
{
    public string? Access_token { get; set; }
    public string? Refresh_token { get; set; }
    public int Access_token_expires_in { get; set; }
    public int Refresh_token_expires_in { get; set; }
}

public class TencentAdBaseResponse<T>
{
    public T? Data { get; set; }
    public int Code { get; set; }
    public string? Message { get; set; }
}

public class TencentAdGlobalPara
{
    public string? access_token { get; set; }
    public long timestamp { get; set; }
    public string? nonce { get; set; }
    public string? fields { get; set; }
}