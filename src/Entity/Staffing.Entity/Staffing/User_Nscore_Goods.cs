﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.CommonModel.Business;
using Config.Enums;

namespace Staffing.Entity.Staffing;

// /// <summary>
// /// 诺积分
// /// </summary>
// [Table("user_nscore_goods")]
// public class User_Nscore_Goods
// {
//     [Key]
//     public string Id { get; set; } = EntityTools.SnowflakeId();

//     /// <summary>
//     /// hr还是求职者
//     /// </summary>
//     public SeekerOrHr UserType { get; set; }

//     /// <summary>
//     /// 类型
//     /// </summary>
//     public NScoreGoodsType Type { get; set; }

//     /// <summary>
//     /// 商品名称
//     /// </summary>
//     public string Name { get; set; } = null!;

//     /// <summary>
//     /// 描述
//     /// </summary>
//     public string? Describe { get; set; }


//     /// <summary>
//     /// 所需积分
//     /// </summary>
//     public int Score { get; set; }

//     /// <summary>
//     /// 商品价值
//     /// </summary>
//     public decimal Money { get; set; }

//     /// <summary>
//     /// 内容
//     /// </summary>
//     public NScoreGoodsContent Content { get; set; } = new NScoreGoodsContent();

//     /// <summary>
//     /// 库存
//     /// </summary>
//     public int Stock { get; set; }

//     /// <summary>
//     /// 更新人
//     /// </summary>
//     public string UpdatedBy { get; set; } = null!;

//     public DateTime UpdatedTime { get; set; } = DateTime.Now;
//     public DateTime CreatedTime { get; set; } = DateTime.Now;

//     public bool Active { get; set; } = true;
// }

public enum NScoreGoodsType
{
    实物, 现金
}