﻿using System.Runtime.InteropServices;
using System.Xml.Serialization;

namespace Infrastructure.Extend;

public static class StringExtend
{
    public static DateTime? ToNullableDate(this string dateString)
    {
        if (string.IsNullOrWhiteSpace(dateString))
            return null;

        if (dateString.Length <= 4)
            dateString = $"{dateString}-01-01";

        if (DateTime.TryParse(dateString, out DateTime resultDate))
            return resultDate;

        return null;
    }

    public static decimal? ToNullableDecimal(this string decimalString)
    {
        if (string.IsNullOrWhiteSpace(decimalString))
        {
            return null;
        }

        if (decimal.TryParse(decimalString, out decimal resultDate))
        {
            return resultDate;
        }

        return null;
    }

    public static int? ToNullableInt(this string intString)
    {
        if (string.IsNullOrWhiteSpace(intString))
        {
            return null;
        }

        if (int.TryParse(intString, out int resultDate))
        {
            return resultDate;
        }

        return null;
    }

    public static string ToYYYY_MM_DD(this DateTime date)
    {
        return date.ToString("yyyy-MM-dd");
    }

    public static string ToYYYY_MM_DD_HH(this DateTime date)
    {
        return date.ToString("yyyy-MM-dd HH:mm:ss");
    }

    public static string ToYYYY_MM_DD(this DateTime? date)
    {
        return date?.ToString("yyyy-MM-dd") ?? string.Empty;
    }

    public static string ToYYYY_MM_DD_HH(this DateTime? date)
    {
        return date?.ToString("yyyy-MM-dd HH:mm:ss") ?? string.Empty;
    }

    public static DateTime? ToDateTime(this long timeStamp)
    {
        if (timeStamp <= 0)
        {
            return null;
        }
        var start = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        return start.AddSeconds(timeStamp).AddHours(8);
    }

    public static double ToTimeDiffHour(this DateTime date, DateTime diffTime)
    {
        return (diffTime - date).TotalHours;
    }

    // public static string ReplaceToChineseBracket(this string str)
    // {
    //     return str.Replace('(', '（').Replace(')', '）');
    // }

    // public static string ReplaceToEnglishBracket(this string str)
    // {
    //     return str.Replace('（', '(').Replace('）', ')');
    // }

    /// <summary>
    /// xml字符串转对象
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="xmlString"></param>
    /// <returns></returns>
    public static T? ParseXmlToModel<T>(this string xmlString) where T : class
    {
        var serializer = new System.Xml.Serialization.XmlSerializer(typeof(T));
        using var reader = new StringReader(xmlString);
        return (T?)serializer.Deserialize(reader);
    }

    /// <summary>
    /// 将对象转为XML字符串
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="xmlString"></param>
    /// <returns></returns>
    public static string ParseModelToXml<T>(this T model, bool IsAddNamespaces = false) where T : class
    {
        var serializer = new System.Xml.Serialization.XmlSerializer(typeof(T));

        //输出为字符串
        var xmlString = string.Empty;
        using (var sw = new StringWriter())
        {
            if (IsAddNamespaces)
            {
                var ns = new XmlSerializerNamespaces();
                ns.Add("ns", "http://example.com/schema");
                serializer.Serialize(sw, model, ns);
            }
            else
                serializer.Serialize(sw, model);
            xmlString = sw.ToString();
        }
        return xmlString;
    }

    /// <summary>
    /// 路径分隔符适配扩展方法
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    public static string ReplaceFilePath(this string path)
    {
        bool isWindows = RuntimeInformation.IsOSPlatform(OSPlatform.Windows);
        return isWindows ? path.Replace("/", "\\") : path.Replace("\\", "/");
    }
}
