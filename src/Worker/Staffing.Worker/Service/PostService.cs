using Config;
using Config.CommonModel;
using Config.CommonModel.Business;
using Config.CommonModel.Cache;
using Config.CommonModel.Recruit;
using Config.CommonModel.ThirdTalentInfo;
using Config.Enums;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Drawing;
using DocumentFormat.OpenXml.Math;
using DocumentFormat.OpenXml.Office.CustomUI;
using DocumentFormat.OpenXml.Spreadsheet;
using Essensoft.Paylink.WeChatPay.V3.Domain;
using Flurl.Http;
using FreeRedis;
using Google.Protobuf.WellKnownTypes;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.CommonService.ShareProfit;
using Infrastructure.Extend;
using Infrastructure.NoahCommon;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.FileSystemGlobbing.Internal;
using Noah.Aliyun.Storage;
using NetTopologySuite.Geometries;
using NPOI.Util;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using static Infrastructure.Proxy.WbMoFangApi;
using static Infrastructure.Proxy.ZhiXiaoErApi;
using System.Text;

namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class PostService
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _logger;
    private readonly IObjectStorage _objectStorage;
    private readonly ParseResume _parseResume;
    private readonly CommonDicService _commonDicService;
    private readonly CommonVirtualService _commonVirtualService;
    private readonly TencentMapHelper _tencentMapHelper;
    private readonly CommonProjectService _commonProjectService;
    private readonly CommonNScoreService _commonNScoreService;
    private readonly CommonPostService _commonPostService;
    private readonly CommonCacheService _commonCacheService;
    private readonly CommonDoubaoService _commonDoubaoService;
    private readonly WeChatHelper _weChatHelper;
    private readonly CommonPostOrder _orderService;
    public PostService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger,
    IObjectStorage objectStorage, ParseResume parseResume, CommonDicService commonDicService,
    CommonVirtualService commonVirtualService, TencentMapHelper tencentMapHelper,
    CommonProjectService commonProjectService, WeChatHelper weChatHelper, CommonDoubaoService commonDoubaoService,
    CommonNScoreService commonNScoreService, CommonPostOrder orderService, CommonCacheService commonCacheService,
    CommonPostService commonPostService)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _objectStorage = objectStorage;
        _parseResume = parseResume;
        _commonDicService = commonDicService;
        _commonVirtualService = commonVirtualService;
        _tencentMapHelper = tencentMapHelper;
        _commonProjectService = commonProjectService;
        _weChatHelper = weChatHelper;
        _commonDoubaoService = commonDoubaoService;
        _commonNScoreService = commonNScoreService;
        _orderService = orderService;
        _commonPostService = commonPostService;
        _commonCacheService = commonCacheService;
    }

    /// <summary>
    /// 处理归档项目
    /// </summary>
    public async void ProjectFileAway(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            var today = DateTime.Today;
            if (DateTime.Now >= today.AddHours(2))
                return;

            using var _context = _contextFactory.CreateDbContext();

            var expProj = _context.Project
            .Where(x => x.EndTime < today && x.Status == ProjectStatus.已上线)
            .OrderByDescending(o => o.CreatedTime)
            .Take(20).ToList();

            foreach (var item in expProj)
            {
                item.Status = ProjectStatus.归档;
            }

            _context.SaveChanges();

            //项目状态变更消息
            var msg = expProj.Select(s => new Sub_Project_StatusChange
            {
                ProjectId = s.ProjectId
            }).ToArray();
            if (msg.Count() > 0)
                MyRedis.Client.SAdd(SubscriptionKey.ProjectStatusChange, msg);

            await Task.FromResult(1);
        }
        catch (Exception e)
        {
            _logger.Error("处理归档项目出错", Tools.GetErrMsg(e));
        }
    }

    /// <summary>
    /// 劳动力采集 顾问下渠道 状态归档
    /// </summary>
    public void CounselorChannelStatusArchive(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            var today = DateTime.Today;
            if (DateTime.Now >= today.AddHours(2))
                return;

            using var _context = _contextFactory.CreateDbContext();

            var expProj = _context.Qd_Hr_Channel
                .Where(x => x.EndTime < today && x.Status == ActiveStatus.Active).OrderByDescending(o => o.EndTime)
                .Take(20).ToList();

            foreach (var item in expProj)
            {
                item.Status = ActiveStatus.Inactive;
            }

            _context.SaveChanges();
        }
        catch (Exception e)
        {
            _logger.Error("劳动力采集顾问渠道状态归档出错", Tools.GetErrMsg(e));
        }
    }

    public async Task SubTeamBountyChange(CancellationToken cancel)
    {
        //处理消息
        Sub_TeamBountyChange? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_TeamBountyChange>(SubscriptionKey.TeamBountyChange);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息
                using var _context = _contextFactory.CreateDbContext();
                var teamBounty = _context.Post_Bounty.Where(x => x.Id == msg.Id)
                .Select(s => new
                {
                    s.PostId,
                    s.Post_Team.Project_Team.HrId,
                    s.Status,
                    s.IfStockOut
                }).FirstOrDefault();

                if (teamBounty == null)
                    return;

                //更新职位分润状态
                var post = _context.Post_Extend.First(x => x.PostId == teamBounty.PostId);

                var postId = teamBounty.PostId;

                //最近5场面试交付率，产品说修改规则为：近5场交付率：最近5个订单的减库存的比例，1个减库存的订单/5个订单=20%。
                var recre = _context.Post_Bounty.Where(x => x.PostId == postId).OrderByDescending(o => o.CreatedTime).Take(5)
                .Select(s => new { s.IfStockOut, s.Status }).ToList();

                if (recre.Count > 0)
                    post.RecentDeliveryRate = recre.Where(x => x.IfStockOut == 1).Count() / (decimal)recre.Count;
                else
                    post.RecentDeliveryRate = 0;

                post.RecentDeliveryRate = post.RecentDeliveryRate.ToFixed(2);

                //交付成功数量
                var deliverySuccessNum = _context.Post_Bounty.Where(x => x.PostId == postId && x.GuaranteeStatus == GuaranteeStatus.过保).Count();
                //减库存订单数量
                var orderNum = _context.Post_Bounty.Where(x => x.PostId == postId && x.IfStockOut == 1).Count();
                //历史计费率
                post.HistoryBillingRate = orderNum == 0 ? 0 : (decimal)deliverySuccessNum / orderNum;
                post.HistoryBillingRate = post.HistoryBillingRate.ToFixed(2);

                _context.SaveChanges();
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.TeamBountyChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(500);
            }

            await Task.Delay(20);
        }
    }

    public async Task SubZddPost(CancellationToken cancel)
    {
        //处理消息
        ZhiXiaoErDetailModel? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<ZhiXiaoErDetailModel>(SubscriptionKey.ZddJobDetail);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息                
                if (msg.jobname != null && msg.zhaopinid != null)
                {
                    #region 福利待遇
                    List<string> welfare = new List<string>();
                    if (msg.ext!.xz_welfare! != "无")
                    {
                        welfare.Add(msg.ext!.xz_welfare!);
                        //welfare = await _commonDoubaoService.DoubaoRes(msg.ext!.xz_welfare!);
                    }
                    else
                    {
                        welfare.Add("无");
                    }
                    #endregion
                    #region 企业规模
                    EnterpriseScale es = EnterpriseScale.二十人以下;
                    switch (msg.ext?.hy_scale)
                    {
                        case "0-1000人":
                            es = EnterpriseScale.五百至一千;
                            break;
                        case "5000人以上":
                            es = EnterpriseScale.一千至一万;
                            break;
                        case "1000-5000人":
                            es = EnterpriseScale.一千至一万;
                            break;
                        case "50-500人":
                            es = EnterpriseScale.一百至五百;
                            break;
                        case "500人以上":
                            es = EnterpriseScale.五百至一千;
                            break;
                        case "10000人以上":
                            es = EnterpriseScale.一万以上;
                            break;
                        case "500-1000人":
                            es = EnterpriseScale.五百至一千;
                            break;
                        default:
                            es = EnterpriseScale.一千至一万;
                            break;
                    }
                    #endregion
                    #region 学历要求
                    EducationType education = EducationType.不限;
                    switch (msg.ext?.yq_edu)
                    {
                        case string s when s.Contains("初中"):
                            education = EducationType.不限;
                            break;
                        case string s when s.Contains("本科"):
                            education = EducationType.本科;
                            break;
                        case string s when s.Contains("硕士"):
                            education = EducationType.硕士;
                            break;
                        case string s when s.Contains("博士"):
                            education = EducationType.博士;
                            break;
                        case string s when s.Contains("大专"):
                            education = EducationType.大专;
                            break;
                        default:
                            education = EducationType.不限;
                            break;
                    }
                    #endregion
                    #region 岗位类别
                    int category = 0;
                    switch (msg.jobname)
                    {
                        case string s when s.Contains("操作工"):
                            category = 1007;
                            break;
                        case string s when s.Contains("叉车工"):
                            category = 1008;
                            break;
                        case string s when s.Contains("焊工"):
                            category = 1010;
                            break;
                        case string s when s.Contains("车险"):
                            category = 555;
                            break;
                        case string s when s.Contains("打包工"):
                            category = 1007;
                            break;
                        case string s when s.Contains("安检员"):
                            category = 917;
                            break;
                        case string s when s.Contains("服务员"):
                            category = 858;
                            break;
                        case string s when s.Contains("营业员"):
                            category = 881;
                            break;
                        case string s when s.Contains("分拣员"):
                            category = 728;
                            break;
                        case string s when s.Contains("销售"):
                            category = 384;
                            break;
                        case string s when s.Contains("洗碗工"):
                            category = 851;
                            break;
                        case string s when s.Contains("店员"):
                            category = 881;
                            break;
                        case string s when s.Contains("客服"):
                            category = 263;
                            break;
                        case string s when s.Contains("课程顾问"):
                            category = 434;
                            break;
                        case string s when s.Contains("分餐"):
                            category = 263;
                            break;
                        case string s when s.Contains("保安"):
                            category = 911;
                            break;
                        case string s when s.Contains("司机"):
                            category = 735;
                            break;
                        case string s when s.Contains("客房保洁"):
                            category = 912;
                            break;
                        case string s when s.Contains("厨师"):
                            category = 859;
                            break;
                        case string s when s.Contains("面点师"):
                            category = 847;
                            break;
                        case string s when s.Contains("导购"):
                            category = 880;
                            break;
                        case string s when s.Contains("寿险"):
                            category = 554;
                            break;
                        case string s when s.Contains("保洁"):
                            category = 912;
                            break;
                        case string s when s.Contains("缝纫工"):
                            category = 1034;
                            break;
                        case string s when s.Contains("其他"):
                            category = 1041;
                            break;
                        case string s when s.Contains("厨工"):
                            category = 859;
                            break;
                        case string s when s.Contains("油漆工"):
                            category = 1014;
                            break;
                        default:
                            category = 1041;
                            break;
                    }
                    #endregion
                    #region 返费类型
                    PostRewardType rewardType = PostRewardType.长期结算;
                    switch (msg.fanfeitype1)
                    {
                        case 1 or 2 or 4:
                            rewardType = PostRewardType.长期结算;
                            break;
                        case 3:
                            rewardType = PostRewardType.单次结算;
                            break;
                    }
                    #endregion
                    #region 结算周期
                    PostPaymentCycle paymentCycle = PostPaymentCycle.小时;
                    switch (msg.fanfeiunit1)
                    {
                        case string s when s.Contains("小时"):
                            paymentCycle = PostPaymentCycle.小时;
                            break;
                        case string s when s.Contains("天"):
                            paymentCycle = PostPaymentCycle.天;
                            break;
                        case string s when s.Contains("月"):
                            paymentCycle = PostPaymentCycle.月;
                            break;
                    }
                    #endregion
                    var agent = _commonCacheService.GetAgentEntFromTyc(msg.companyname!.Replace(".", ""));//获取企业信息，过滤录入的部分企业信息中带着.的问题，造成无法正常验证企业信息问题
                    if (agent == null)//企业信息不为空，否则无法创建岗位
                    {
                        _logger.Error($"天眼查未获取到职多多企业信息", $"职多多招聘id：{msg.zhaopinid}，企业信息：{msg.companyname}，职多多岗位名称：{msg.jobname}，获取企业信息失败！", JsonSerializer.SerializeToString(msg));
                        continue;
                    }
                    // 正则表达式模式，匹配 '-' 之前的连续数字
                    string pattern = @"^\d+|(?<=-)\d+|\d+";
                    // 判断是否已经创建过该岗位
                    using var _context = _contextFactory.CreateDbContext();
                    var post = _context.ThirdParty_Postid_Relation.Where(x => x.ThirdPostId == msg.zhaopinid).FirstOrDefault();
                    #region 解决经纬度对调问题
                    if (msg.v6_lat > 90 || msg.v6_lat < -90)
                    {
                        //职多多很多招聘岗位的经纬度反了
                        //进行调转
                        double? lat = msg.v6_lng;//纬度
                        double? lng = msg.v6_lat;//经度
                        msg.v6_lat = lat;
                        msg.v6_lng = lng;
                        _logger.Error($"纬度异常", $"纬度异常：{lat}，职多多招聘id：{msg.zhaopinid}，企业信息：{msg.companyname}，职多多岗位名称：{msg.jobname}", JsonSerializer.SerializeToString(msg));
                    }
                    if (msg.v6_lng > 180 || msg.v6_lng < -180)
                    {
                        _logger.Error($"经度异常", $"经度异常：{msg.v6_lng}，职多多招聘id：{msg.zhaopinid}，企业信息：{msg.companyname}，职多多岗位名称：{msg.jobname}", JsonSerializer.SerializeToString(msg));
                        continue;
                    }
                    if (msg.v6_lng == msg.v6_lat)
                    {
                        //经纬度相同，可能是数据错误
                        _logger.Error($"经纬度相同", $"经纬度相同：{msg.v6_lng}，职多多招聘id：{msg.zhaopinid}，企业信息：{msg.companyname}，职多多岗位名称：{msg.jobname}", JsonSerializer.SerializeToString(msg));
                        continue;
                    }
                    #endregion
                    string projectId = "351015028313006598";//正式
                    //string projectId = "331988481213591302";//测试
                    string? userId = _context.Project.Where(pro => pro.ProjectId == projectId).Select(u => u.HrId).FirstOrDefault();

                    //没有创建过该岗位则直接创建
                    if (post == null)
                    {
                        try
                        {
                            UpdatePost model = new UpdatePost()
                            {
                                //PostId = PostId!.ToString(),
                                Name = msg.title ?? string.Empty,
                                ProjectId = projectId,
                                UserId = userId ?? "237054204150576261",
                                IsReceiver = true,
                                Describe = msg.wk_maincontent + "\n\r" + msg.xz_detail + "\n\r" + msg.ext!.gk_overage ?? string.Empty,
                                Category = category,
                                TieType = TieType.蓝领,
                                WorkNature = PostWorkNature.全职,
                                SalaryType = PostSalaryType.月薪,
                                RecruitNumber = int.Parse(string.Join("", Regex.Matches(msg.ext_number!, pattern).Cast<Match>().Select(m => m.Value))),
                                DeliveryNumber = int.Parse(string.Join("", Regex.Matches(msg.ext_number!, pattern).Cast<Match>().Select(m => m.Value))),
                                LeftStock = 0,
                                Status = PostStatus.待审核,
                                RegionId = msg.cityid ?? string.Empty,
                                AgentEntId = agent.AgentEntId,
                                AgentEnt = new GetAgentEntDetail
                                {
                                    Display = EntDisplayType.企业名称,
                                    AgentEntId = agent.AgentEntId,
                                    Name = agent.Name!,
                                    DisplayName = agent.Name!,
                                    Scale = es,
                                    ScaleName = es.GetDescription(),
                                    Address = agent.Address,
                                    Abbr = agent.Abbr == string.Empty ? msg.companyname! : agent.Abbr!,
                                    Describe = agent.Describe,
                                    RegionId = agent.RegionId,
                                },
                                Address = msg.address,
                                Lat = msg.v6_lat ?? 0,
                                Lng = msg.v6_lng ?? 0,
                                IsSalesCommission = true,
                                ContractType = ContractType.与第三方劳务签订合同,
                                Interview = new PostInterviewInfo
                                {
                                    InterviewMode = RecruitInterviewForms.Scene,
                                    Address = msg.gatherplace,
                                },
                                Education = education,
                                Sex = (msg.sex == Infrastructure.Proxy.ZhiXiaoErApi.Sex.Man ? Config.Enums.Sex.男 : Config.Enums.Sex.女),
                                MinAge = (msg.sex1start == null ? msg.sex2start : msg.sex1start) ?? 0,
                                MaxAge = (msg.sex1end == null ? msg.sex2start : msg.sex2end) ?? 0,
                                Salary = 12,
                                MinSalary = msg.pay1,
                                MaxSalary = msg.pay2,
                                // Money = msg.fanfeimoney1,

                                /// TODO:需要补充
                                RewardType = rewardType,
                                PaymentCycle = paymentCycle,
                                // PaymentDuration = 
                                ProfitStage = new List<PostProfitStage>
                                {
                                    new PostProfitStage {
                                        Amount = msg.fanfeimoney1 ?? 0,
                                        GuaranteeDays = 0
                                    }
                                },
                                Tags = msg.label,
                                Highlights = msg.zhaopinlight,
                                WelfareCustom = welfare,
                                IsResumeExempt = false,
                                InterviewStatus = InterviewStatus.Off,
                                PaymentNode = ProjectPaymentNode.入职过保,
                                // PaymentDays = 3,
                                Department = msg.sy_contract ?? string.Empty,
                                Source = PostSouce.职多多,
                                #region 岗位调研表
                                SurveyContent = new ProjectSurveyContent
                                {
                                    KeyRead = msg.hy_content_source,
                                    SalaryBenefits = new List<ProjectSurveyContentValue>
                                {
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "综合薪资",
                                        Content = msg.real_pay
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "薪资结构",
                                        Content = msg.address
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "试用期工资",
                                        Content = msg.xz_probation
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "五险一金",
                                        Content = msg.ext.xz_issocialsecurity
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "其他福利",
                                        Content = msg.ext.xz_welfare
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "月休天数",
                                        Content = msg.ext.wk_monthrestday
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "商业保险",
                                        Content = msg.ext.xz_iscommercialinsurance
                                    },
                                },
                                    HardConditions = new List<ProjectSurveyContentValue>
                                {
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "纹身烟疤",
                                        Content = msg.ext.yq_tattoo
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "钢板钢钉",
                                        Content = msg.ext.yq_plate
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "超龄协调",
                                        Content = msg.ext.yq_overage
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "公安案底",
                                        Content = msg.ext.ms_iscriminalrecord
                                    },
                                },
                                    WorkingHours = new List<ProjectSurveyContentValue>
                                {
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "工作方式",
                                        Content = msg.ext.wk_mode
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "上班时间(是否双休)",
                                        Content = msg.ext.wk_manhour
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "发薪时间(具体时间)",
                                        Content = msg.ext.xz_salaryday
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "上下班次(是否打卡)",
                                        Content = msg.ext.wk_type
                                    },
                                },
                                    WorkLocation = new List<ProjectSurveyContentValue>
                                {
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "集合地点",
                                        Content = msg.gatherplace
                                    },
                                },
                                    InterviewProcess = new List<ProjectSurveyContentValue>
                                {
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "接站安排",
                                        Content = msg.ext.bk_isstation
                                    },
                                },
                                    OnboardingProcess = new List<ProjectSurveyContentValue>
                                {
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "报道材料",
                                        Content = msg.ext.ms_material
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "是否体检",
                                        Content = msg.ext.tj_yesorno
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "体检安排",
                                        Content = msg.ext.ms_inspect
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "体检费用",
                                        Content = msg.ext.tj_isself
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "体检项目",
                                        Content = msg.ext.tj_project
                                    }
                                },
                                    WorkCondition = new List<ProjectSurveyContentValue>
                                {
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "宿舍情况(是否提供)",
                                        Content = msg.ext.ss_stay
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "宿舍标准",
                                        Content = msg.ext.ss_room
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "夫妻宿舍(是否提供)",
                                        Content = msg.ext.ss_spouseroom
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "能否外宿",
                                        Content = msg.ext.ss_sleepover
                                    },
                                    new ProjectSurveyContentValue
                                    {
                                        Title = "在外住宿(是否补助)",
                                        Content = msg.ext.ss_sleepoversubsidy
                                    },
                                }
                                }
                                #endregion
                            };

                            var result = await _commonPostService.UpdatePost(model);
                            if (result != null)
                            {
                                var zhiduoduo_postid_relation = new ThirdParty_Postid_Relation()
                                {
                                    PostId = result.PostId,
                                    ThirdPostId = msg.zhaopinid,
                                    Status = PostStatus.待审核,
                                    UpdatedTime = DateTime.Now,
                                    Source = PostSouce.职多多,
                                    FanfeiStatus = FanfeiStatus.NoChange,
                                };
                                _context.ThirdParty_Postid_Relation.Add(zhiduoduo_postid_relation);
                                _context.SaveChanges();
                            }
                            else
                            {
                                _logger.Error($"岗位发布错误", $"职多多招聘id：{msg.zhaopinid}，职多多岗位名称：{msg.jobname}，诺快聘岗位发布失败，postid为空！", JsonSerializer.SerializeToString(msg));
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.Error($"职多多岗位发布至诺快聘错误,职多多招聘id：{msg.zhaopinid}，职多多岗位名称：{msg.jobname}", Tools.GetErrMsg(ex), JsonSerializer.SerializeToString(msg));
                        }
                    }
                    else//已创建过该岗位，则判断岗位返费状态，若岗位返费有变化则更新到表中
                    {
                        try
                        {
                            var updatePost = _context.Post.
                                Where(p => p.PostId == post.PostId).FirstOrDefault();
                            if (updatePost != null)
                            {
                                updatePost.Source = PostSouce.职多多;//预留，后续产品经理要求更新直接在后面添加，目前为预留
                                updatePost.UpdatedTime = DateTime.Now;
                                updatePost.IsSalesCommission = true;
                                if (updatePost.Status == PostStatus.关闭)
                                    updatePost.Status = PostStatus.待审核;//重新上架审核
                                updatePost.WelfareCustom = welfare;

                                var agentent = _context.Agent_Ent.Where(x => x.AgentEntId == agent.AgentEntId).FirstOrDefault();
                                if (agentent != null)
                                {
                                    agentent.DisplayName = agent.Name!;
                                }
                                var third = _context.ThirdParty_Postid_Relation.
                                    Where(r => r.PostId == post.PostId).FirstOrDefault();
                                if (third != null)
                                {
                                    third.UpdatedTime = DateTime.Now;
                                    if (third.Status == PostStatus.关闭)
                                        third.Status = PostStatus.待审核;
                                    if (msg.fanfeimoney1 != updatePost.Money)
                                        third.FanfeiStatus = FanfeiStatus.Change;
                                }
                                _context.SaveChanges();
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.Error("更新返费状态失败", Tools.GetErrMsg(ex), $"岗位Id：{post.PostId}");
                        }
                    }
                }
            }
            catch (Exception e)
            {
                _logger.Error($"职多多岗位同步至诺快聘消息处理出错 招聘Id：{msg!.zhaopinid}", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(500);
            }

            await Task.Delay(500);
        }
    }

    public async Task DownZddPost(CancellationToken cancel)
    {
        List<string?> zddmsg = new List<string?>();
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                zddmsg = MyRedis.Client.SPop<List<string?>>(SubscriptionKey.ZddJobDownList);
                //处理消息
                if (zddmsg == null)
                {
                    await Task.Delay(500);
                    continue;
                }
                if (zddmsg.Count == 0)
                {
                    await Task.Delay(500);
                    continue;
                }
                using var _context = _contextFactory.CreateDbContext();
                List<string> postidList = new List<string>();
                foreach (var item in zddmsg)
                {
                    if (item != null && string.Empty != item)
                    {
                        var post = _context.ThirdParty_Postid_Relation.Where(x => x.ThirdPostId == item &&
                        x.Source == PostSouce.职多多).FirstOrDefault();
                        if (post != null)
                        {
                            postidList.Add(post.PostId!);
                        }
                    }
                }
                string projectId = "351015028313006598";//正式
                //string? projectId = "331988481213591302";//测试
                string? userId = _context.Project.Where(pro => pro.ProjectId == projectId).Select(u => u.HrId).FirstOrDefault();
                foreach (var post in postidList)
                {
                    //下架诺快聘岗位
                    _commonProjectService.SetPostStatus(post, PostStatus.关闭, userId ?? "237054204150576261");
                    //下架职多多关联表岗位
                    var existingRelation = _context.ThirdParty_Postid_Relation.FirstOrDefault(r => r.PostId == post);
                    if (existingRelation != null)
                    {
                        existingRelation.Status = PostStatus.关闭;
                        existingRelation.UpdatedTime = DateTime.Now;
                        _context.SaveChanges();
                    }
                    await Task.Delay(1000);
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"职多多岗位下架出错", Tools.GetErrMsg(ex), JsonSerializer.SerializeToString(zddmsg));
                await Task.Delay(500);
            }
            await Task.Delay(500);
        }
    }

    public async Task SubWbMoFangPost(CancellationToken cancel)
    {
        //处理消息
        WbMofangPosDetailtModel? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<WbMofangPosDetailtModel>(SubscriptionKey.WbJobDetail);
                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }
                if (msg.jobId != null && msg.jobTitle != null && msg.job != null && msg.jobSessionProcess != null)
                {
                    var agent = _commonCacheService.GetAgentEntFromTyc(msg.companyName!);//获取企业信息，过滤录入的部分企业信息中带着.的问题，造成无法正常验证企业信息问题
                    if (agent == null)//企业信息不为空，否则无法创建岗位
                    {
                        _logger.Error($"天眼查未获取到58魔方企业信息", $"58魔方岗位id：{msg.jobId}，企业信息：{msg.companyName}，第三方岗位名称：{msg.jobTitle}，获取企业信息失败！", JsonSerializer.SerializeToString(msg));
                        continue;
                    }

                    using var _context = _contextFactory.CreateDbContext();
                    var post = _context.ThirdParty_Postid_Relation.Where(x => x.ThirdPostId == msg.jobId).FirstOrDefault();
                    if (msg.job.jobMessage != null && msg.jobSessionProcess.jobSessionMessage != null)
                    {
                        if (msg.job.jobMessage.jobCityName != null && msg.job.jobMessage.jobAddress != null)
                        {
                            #region 公共方法调用腾讯地图接口

                            //string url = "https://apis.map.qq.com/ws/place/v1/suggestion/?" + "region=&keyword=" + msg.job.jobMessage.jobAddress  + "&key=" + Constants.TencentMapKey;
                            //double lat = 0;
                            //double lng = 0;
                            //var loca =  await url.GetJsonAsync();
                            //if (loca != null)
                            //{
                            //    TencentMapLocation tencentLocation = new TencentMapLocation
                            //    {
                            //        status = loca.status,
                            //        data = loca.data,
                            //    };
                            //    if (tencentLocation == null || tencentLocation.status != 0 || tencentLocation.data == null || tencentLocation.data.Count == 0)
                            //    {
                            //        _logger.Error($"腾讯地图接口出错", $"58招聘id：{msg.jobId}，58岗位名称：{msg.jobTitle}", JsonSerializer.SerializeToString(msg));
                            //        throw new Exception("腾讯地图获取58岗位地址信息失败");
                            //    }
                            //    dynamic dynamicLocation = tencentLocation.data[0];
                            //    LData lData = new LData()
                            //    {
                            //        id = dynamicLocation.id,
                            //        location = dynamicLocation.location,
                            //    };
                            //    if (lData.location != null)
                            //    {
                            //        dynamic location = lData.location;
                            //        tenlocation tenlocation = new tenlocation
                            //        {
                            //            lat = location.lat,
                            //            lng = location.lng
                            //        };
                            //        lat = tenlocation.lat;
                            //        lng = tenlocation.lng;
                            //    }
                            //}
                            #endregion

                            #region 计算结算金额
                            decimal sum = 0;
                            int count = 0;
                            int day = 0;
                            if (msg.projectExpenses != null && msg.projectExpenses.Count > 0)
                            {
                                msg.projectExpenses.ForEach(x =>
                                {
                                    sum = sum + x.cost;
                                    count++;
                                    if (count == msg.projectExpenses.Count)
                                    {
                                        day = x.day;
                                    }
                                });
                            }
                            #endregion

                            #region 福利待遇
                            List<WelfareModel> welfareList = new List<WelfareModel>();
                            List<string> listCustom = new List<string>();
                            if (msg.jobAdvantageLabels != null && msg.jobAdvantageLabels.Count > 0)
                            {
                                var welfares = _commonDicService.GetWelfare();
                                foreach (var welfare in welfares)
                                {
                                    foreach (var label in msg.jobAdvantageLabels)
                                    {
                                        if (welfare.Name == label)
                                        {
                                            welfareList.Add(welfare);
                                        }
                                    }
                                }
                                if (welfareList.Count == 0 && msg.jobAdvantageLabels.Count > 0)
                                {
                                    msg.jobAdvantageLabels.ForEach(x =>
                                    {
                                        listCustom.Add(x);
                                    });
                                }
                            }
                            else
                            {
                                WelfareModel welfareModel = new WelfareModel()
                                {
                                    Id = 58,
                                    Name = "不加班",
                                    Logo = "https://resources.nuopin.cn/welfare/不加班.png",
                                    Describe = "倡导高效工作，不强制或不提倡员工加班。"
                                };
                                welfareList.Add(welfareModel);
                            }
                            #endregion
                            string projectId = "351013634522718725";//正式
                            //string projectId = "335071964460783494";//测试
                            string? userId = "244603393120190981";//正式
                            //string? userId = "101";//测试
                            #region 合并岗位描述
                            string describe = "";
                            string weekend = "";
                            weekend = msg.job.jobMessage.weekend!.TrimStart('[').TrimEnd(']');
                            StringBuilder describeBuilder = new StringBuilder();
                            if (msg.job.jobMessage.workTimeStart != null && msg.job.jobMessage.workTimeEnd != null)
                            {
                                describeBuilder.AppendLine($"工作时间：{msg.job.jobMessage.workTimeStart}-{msg.job.jobMessage.workTimeEnd}");
                            }
                            else
                            {
                                describeBuilder.AppendLine($"工作时间：{msg.job.jobMessage.workTimeFlexDesc}");
                            }
                            describeBuilder.AppendLine($"工作内容：{msg.job.jobMessage.jobContent}")
                                           .AppendLine($"职位亮点：{msg.job.jobMessage.jobAdvantage}")
                                           .AppendLine($"工作地址：{msg.job.jobMessage.jobAddress}")
                                           .AppendLine($"工作时长：{msg.job.jobMessage.workTimeDuration}")
                                           .Append($"是否双休：{weekend}");
                            describe = describeBuilder.ToString();
                            #endregion

                            #region 面试邀约时间
                            List<PostInterviewTimeModel> interviewTimeList = new List<PostInterviewTimeModel>();
                            msg.sessions!.ForEach(x =>
                            {
                                TimeSpan interval = TimeSpan.FromMinutes(30);
                                // 把时间戳转换为 DateTime 对象
                                // 时区转换为上海时区
                                TimeZoneInfo shanghaiTimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");
                                DateTime startTime = TimeZoneInfo.ConvertTimeFromUtc(DateTimeOffset.FromUnixTimeSeconds(x.sessionBeginTime).UtcDateTime, shanghaiTimeZone);
                                DateTime endTime = TimeZoneInfo.ConvertTimeFromUtc(DateTimeOffset.FromUnixTimeSeconds(x.sessionEndTime).UtcDateTime, shanghaiTimeZone);
                                DateTime currentTime = startTime;
                                // 存储符合条件的时间段
                                List<string> subtimeList = new List<string>();
                                while (currentTime.Hour <= endTime.Hour)
                                {
                                    if (currentTime >= startTime && currentTime <= endTime)
                                    {
                                        subtimeList.Add(currentTime.ToString("HH:mm"));
                                    }
                                    currentTime = currentTime.Add(interval);
                                }
                                subtimeList.ForEach(a =>
                                {
                                    interviewTimeList.Add(new PostInterviewTimeModel
                                    {
                                        Time = Convert.ToDateTime(DateTimeOffset.FromUnixTimeSeconds(x.sessionBeginTime).DateTime.Date.ToString("yyyy-MM-dd") + " " + a.ToString()),
                                        Status = PostInterviewTimeStatus.可预约
                                    });
                                });
                            });
                            #endregion

                            #region 岗位类型
                            int category = 0;
                            if (msg.jobTypeName != null)
                            {
                                string[] typenames = msg.jobTypeName.Split('/');
                                string typename = typenames[typenames.Length - 1];

                                var categoryRelation = _context.ThirdParty_Category_Dic_Relation.Where(x => x.ThirdCategoryName!.Contains(typename)).FirstOrDefault();
                                if (categoryRelation != null)
                                {
                                    category = categoryRelation.NkpCategoryId;
                                }
                                else
                                {
                                    category = 1041;
                                }
                            }
                            #endregion

                            #region 存入临时表，用来筛选下架岗位
                            var thirdtemp = _context.Thirdparty_Postid_Temp.Where(x => x.ThirdPostId == msg.jobId).FirstOrDefault(); ;
                            if (thirdtemp == null)
                            {
                                var zhiduoduo_postid_temp = new Thirdparty_Postid_Temp()
                                {
                                    ThirdPostId = msg.jobId,
                                    Source = PostSouce.魔方,
                                };
                                _context.Thirdparty_Postid_Temp.Add(zhiduoduo_postid_temp);
                            }
                            #endregion

                            //没有创建过该岗位则直接创建
                            if (post == null)
                            {
                                try
                                {
                                    #region 学历要求
                                    EducationType education = EducationType.不限;
                                    switch (msg.degree)
                                    {
                                        case 0:
                                            education = EducationType.不限;
                                            break;
                                        case 1:
                                            education = EducationType.其他;
                                            break;
                                        case 2:
                                            education = EducationType.高中;
                                            break;
                                        case 3:
                                            education = EducationType.大专;
                                            break;
                                        case 4:
                                            education = EducationType.本科;
                                            break;
                                        case 5:
                                            education = EducationType.硕士;
                                            break;
                                        case 6:
                                            education = EducationType.博士;
                                            break;
                                        default:
                                            education = EducationType.其他;
                                            break;
                                    }
                                    #endregion

                                    #region 性别
                                    Config.Enums.Sex? sex = null;
                                    if (msg.job.jobSessionAsk != null)
                                    {
                                        switch (msg.job.jobSessionAsk.sex)
                                        {
                                            case 0:
                                                sex = null;
                                                break;
                                            case 1:
                                                sex = Config.Enums.Sex.女;
                                                break;
                                            case 2:
                                                sex = Config.Enums.Sex.男;
                                                break;
                                        }
                                    }
                                    #endregion

                                    #region 结算方式
                                    ProjectPaymentNode projectPaymentNode = ProjectPaymentNode.入职过保;
                                    // PostRewardType postRewardType = PostRewardType.单次结算;
                                    // PostPaymentCycle postPaymentCycle = PostPaymentCycle.天;
                                    // List<PostProfitStage> ProfitStage = new();

                                    /// TODO:需要兼容新模式

                                    switch (msg.chargeType)
                                    {
                                        case 1:
                                            projectPaymentNode = ProjectPaymentNode.到面交付;
                                            break;
                                        case 2:
                                            projectPaymentNode = ProjectPaymentNode.入职过保;
                                            break;
                                            // case 3:
                                            //     projectPaymentNode = ProjectPaymentNode.按天入职过保;
                                            //     break;
                                    }
                                    #endregion

                                    UpdatePost model = new UpdatePost()
                                    {
                                        Name = msg.jobTitle ?? string.Empty,
                                        ProjectId = projectId,
                                        UserId = userId ?? "244603393120190981",
                                        IsReceiver = true,
                                        Describe = describe,
                                        Category = category,//1041 其他 该状态下允许项目经理修改岗位类别
                                        //TieType = TieType.蓝领,
                                        WorkNature = PostWorkNature.全职,
                                        SalaryType = PostSalaryType.月薪,
                                        RecruitNumber = msg.weekDemandNum,
                                        DeliveryNumber = msg.weekDemandNum,
                                        LeftStock = 0,
                                        Status = PostStatus.待审核,
                                        RegionId = msg.jobCity ?? string.Empty,
                                        AgentEntId = agent.AgentEntId,
                                        AgentEnt = new GetAgentEntDetail
                                        {
                                            Display = EntDisplayType.企业名称,
                                            AgentEntId = agent.AgentEntId,
                                            Name = agent.Name!,
                                            DisplayName = agent.Name!,
                                            //Scale = agent.Scale ?? EnterpriseScale.一万以上,
                                            //ScaleName = agent.Scale.GetDescription(),
                                            Address = agent.Address,
                                            Abbr = agent.Abbr == string.Empty ? msg.companyName! : agent.Abbr!,
                                            Describe = agent.Describe,
                                            RegionId = agent.RegionId,
                                        },
                                        Address = msg.job.jobMessage.jobAddress,
                                        Lat = double.Parse(msg.jobSessionProcess.jobSessionMessage.interviewGps!.Split(',')[1]),
                                        Lng = double.Parse(msg.jobSessionProcess.jobSessionMessage.interviewGps.Split(',')[0]),
                                        IsSalesCommission = true,
                                        ContractType = ContractType.与第三方劳务签订合同,
                                        Interview = new PostInterviewInfo
                                        {
                                            InterviewMode = RecruitInterviewForms.Scene,
                                            Address = msg.jobSessionProcess.jobSessionMessage.interviewAddress == null ? msg.job.jobMessage.jobAddress : msg.jobSessionProcess.jobSessionMessage.interviewAddress,
                                            RegionId = msg.jobCity,
                                            Lat = double.Parse(msg.jobSessionProcess.jobSessionMessage.interviewGps.Split(',')[1]),
                                            Lng = double.Parse(msg.jobSessionProcess.jobSessionMessage.interviewGps.Split(',')[0]),
                                            ContactName = "马迪",
                                            //ContactName = "张红",
                                            ContactPhone = "15832157993",
                                            //ContactPhone = "13111565052",
                                            AdvanceDays = 1,
                                            InterviewerId = "351013650417033734",//"335072018833643397",//"334903738349837061",
                                            AddressDetail = msg.jobSessionProcess.jobSessionMessage.interviewAddress == null ? msg.job.jobMessage.jobAddress : msg.jobSessionProcess.jobSessionMessage.interviewAddress,
                                            InterviewTime = interviewTimeList,
                                            Remark = ""
                                        },
                                        Education = education,
                                        Sex = sex,
                                        MinAge = msg.ageMin,
                                        MaxAge = msg.ageMax,
                                        Salary = 12,
                                        MinSalary = msg.job.salaryMessage!.jobMinSalary,
                                        MaxSalary = msg.job.salaryMessage!.jobMaxSalary,
                                        // Money = sum,
                                        /// TODO:需要补充
                                        RewardType = PostRewardType.单次结算,
                                        PaymentCycle = PostPaymentCycle.天,
                                        // PaymentDuration = 
                                        ProfitStage = new List<PostProfitStage>
                                        {
                                            new PostProfitStage {
                                                Amount = sum,
                                                GuaranteeDays = day
                                            }
                                        },
                                        //Tags = msg.label,
                                        Highlights = msg.jobAdvantageLabels,
                                        Welfare = welfareList,
                                        WelfareCustom = listCustom,
                                        IsResumeExempt = false,
                                        InterviewStatus = InterviewStatus.Open,
                                        //PaymentNode = ProjectPaymentNode.按天入职过保,
                                        PaymentNode = projectPaymentNode,
                                        // PaymentDays = day,
                                        Department = msg.partnerName ?? string.Empty,
                                        Source = PostSouce.魔方,
                                        #region 岗位调研表
                                        SurveyContent = new ProjectSurveyContent
                                        {

                                            SalaryBenefits = new List<ProjectSurveyContentValue>
                                            {
                                                new ProjectSurveyContentValue
                                                {
                                                    Title = "综合薪资",
                                                    Content = msg.job.salaryMessage.jobMinSalary + "-" + msg.job.salaryMessage.jobMaxSalary + "元"
                                                },
                                                new ProjectSurveyContentValue
                                                {
                                                    Title = "薪资结构",
                                                    Content = msg.job.salaryMessage.salaryStructure
                                                },
                                                new ProjectSurveyContentValue
                                                {
                                                    Title = "试用期工资",
                                                    Content = msg.job.salaryMessage.tryoutSalary == -1 ? "面议" : msg.job.salaryMessage.tryoutSalary + "元"
                                                },
                                                new ProjectSurveyContentValue
                                                {
                                                    Title = "五险一金",
                                                    Content = string.Join(",",msg.job.salaryMessage.socialSecurity!)
                                                },
                                                new ProjectSurveyContentValue
                                                {
                                                    Title = "其他福利",
                                                    Content = msg.job.jobMessage.jobAdvantage
                                                },
                                                //new ProjectSurveyContentValue
                                                //{
                                                //    Title = "月休天数",
                                                //    Content = msg.job.jobMessage.weekend!
                                                //},
                                                new ProjectSurveyContentValue
                                                {
                                                    Title = "商业保险",
                                                    Content = msg.job.jobMessage.jobAdvantage
                                                },
                                            },
                                            WorkingHours = new List<ProjectSurveyContentValue>
                                            {
                                                new ProjectSurveyContentValue
                                                {
                                                    Title = "工作方式",
                                                    Content = msg.jobTypeName
                                                },
                                                new ProjectSurveyContentValue
                                                {
                                                    Title = "上班时间(是否双休)",
                                                    Content = msg.job.jobMessage.workTimeStart + "-" + msg.job.jobMessage.workTimeEnd
                                                },
                                             },
                                        }
                                        #endregion
                                    };

                                    var result = await _commonPostService.UpdatePost(model);
                                    if (result != null)
                                    {
                                        // 存入第三方岗位关联表
                                        var zhiduoduo_postid_relation = new ThirdParty_Postid_Relation()
                                        {
                                            PostId = result.PostId,
                                            ThirdPostId = msg.jobId,
                                            Status = PostStatus.待审核,
                                            UpdatedTime = DateTime.Now,
                                            Source = PostSouce.魔方,
                                            FanfeiStatus = FanfeiStatus.NoChange,
                                        };
                                        _context.ThirdParty_Postid_Relation.Add(zhiduoduo_postid_relation);

                                        _context.SaveChanges();
                                    }
                                    else
                                    {
                                        _logger.Error($"岗位发布错误", $"58招聘id：{msg.jobId}，58岗位名称：{msg.jobTitle}，诺快聘岗位发布失败，postid为空！", JsonSerializer.SerializeToString(msg));
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error($"58岗位发布至诺快聘错误,58招聘id：{msg.jobId}，58岗位名称：{msg.jobTitle}", Tools.GetErrMsg(ex), JsonSerializer.SerializeToString(msg));
                                }
                            }
                            else//已创建过该岗位，则判断岗位返费状态，若岗位返费有变化则更新到表中
                            {
                                try
                                {
                                    var updatePost = _context.Post.
                                        Where(p => p.PostId == post.PostId).FirstOrDefault();
                                    if (updatePost != null)
                                    {
                                        updatePost.Source = PostSouce.魔方;//预留，后续产品经理要求更新直接在后面添加，目前为预留
                                        updatePost.UpdatedTime = DateTime.Now;
                                        updatePost.Category = category;
                                        if (updatePost.Status == PostStatus.关闭)
                                            updatePost.Status = PostStatus.待审核;//重新上架审核
                                        updatePost.Describe = describe;
                                        var agentent = _context.Agent_Ent.Where(x => x.AgentEntId == agent.AgentEntId).FirstOrDefault();
                                        if (agentent != null)
                                        {
                                            agentent.DisplayName = agent.Name!;
                                        }
                                        updatePost.IsSalesCommission = true;
                                        updatePost.InterviewStatus = InterviewStatus.Open;
                                        _context.Post_Interview_Date.RemoveRange(_context.Post_Interview_Date.Where(w => w.PostId == post.PostId));
                                        _context.AddRange(interviewTimeList.Select(s => new Post_Interview_Date
                                        {
                                            PostId = post.PostId!,
                                            Time = s.Time!.Value,
                                            Status = PostInterviewTimeStatus.可预约
                                        }));
                                        var config = _context.Post_Interview_Config.Where(i => i.PostId == post.PostId).FirstOrDefault();
                                        if (config != null)
                                        {
                                            config.PostId = post.PostId!;
                                            double Lat = double.Parse(msg.jobSessionProcess.jobSessionMessage.interviewGps!.Split(',')[1]);
                                            double Lng = double.Parse(msg.jobSessionProcess.jobSessionMessage.interviewGps.Split(',')[0]);
                                            config.Address = msg.jobSessionProcess.jobSessionMessage.interviewAddress == null ? msg.job.jobMessage.jobAddress : msg.jobSessionProcess.jobSessionMessage.interviewAddress;
                                            config.Location = new NetTopologySuite.Geometries.Point(Lat, Lng);
                                            config.AddressDetail = msg.job.jobMessage.jobAddress;
                                            config.AdvanceDays = 1;
                                            config.InterviewerId = "351013650417033734";//"335072018833643397",//"334903738349837061",
                                            config.RegionId = msg.jobCity;
                                        }


                                        var third = _context.ThirdParty_Postid_Relation.
                                            Where(r => r.PostId == post.PostId).FirstOrDefault();
                                        if (third != null)
                                        {
                                            third.UpdatedTime = DateTime.Now;
                                            if (third.Status == PostStatus.关闭)
                                                third.Status = PostStatus.待审核;
                                            if (sum != updatePost.Money)
                                                third.FanfeiStatus = FanfeiStatus.Change;
                                        }
                                        _context.SaveChanges();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("更新返费状态失败", Tools.GetErrMsg(ex), $"岗位Id：{post.PostId}");
                                }
                            }

                        }
                        else
                        {
                            _logger.Error($"58魔方岗位处理出错", $"招聘Id：{msg!.jobId}", JsonSerializer.SerializeToString(msg));
                            await Task.Delay(500);
                        }
                    }
                    else
                    {
                        _logger.Error($"58魔方岗位未审核，无法发布，缺少必要信息", $"招聘Id：{msg!.jobId}", JsonSerializer.SerializeToString(msg));
                        await Task.Delay(500);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"58魔方岗位同步至诺快聘消息处理出错 招聘Id：{msg!.jobId}", Tools.GetErrMsg(ex), JsonSerializer.SerializeToString(msg));
                await Task.Delay(500);
            }
            await Task.Delay(500);
        }
    }

    public async Task DownWbMoFangPost(CancellationToken cancel)
    {
        List<string?> wbmsg = new List<string?>();
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                wbmsg = MyRedis.Client.SPop<List<string?>>(SubscriptionKey.WbJobDownList);
                //处理消息
                if (wbmsg == null)
                {
                    await Task.Delay(500);
                    continue;
                }
                if (wbmsg.Count == 0)
                {
                    await Task.Delay(500);
                    continue;
                }
                using var _context = _contextFactory.CreateDbContext();
                List<string> postidList = new List<string>();
                foreach (var item in wbmsg)
                {
                    if (item != null && string.Empty != item)
                    {
                        var post = _context.ThirdParty_Postid_Relation.Where(x => x.ThirdPostId == item &&
                        x.Source == PostSouce.魔方).FirstOrDefault();
                        if (post != null)
                        {
                            postidList.Add(post.PostId!);
                        }
                    }
                }
                string projectId = "351013634522718725";//正式  马迪
                //string projectId = "335071964460783494";//测试
                string? userId = _context.Project.Where(pro => pro.ProjectId == projectId).
                    Select(u => u.HrId).FirstOrDefault();
                foreach (var post in postidList)
                {
                    //下架诺快聘岗位
                    _commonProjectService.SetPostStatus(post, PostStatus.关闭, userId ?? "244603393120190981");
                    //下架58魔方关联表岗位
                    var existingRelation = _context.ThirdParty_Postid_Relation.FirstOrDefault(r => r.PostId == post &&
                    r.Source == PostSouce.魔方);
                    if (existingRelation != null)
                    {
                        existingRelation.Status = PostStatus.关闭;
                        existingRelation.UpdatedTime = DateTime.Now;
                        _context.SaveChanges();
                    }
                    await Task.Delay(1000);
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"58魔方岗位下架出错", Tools.GetErrMsg(ex), JsonSerializer.SerializeToString(wbmsg));
                await Task.Delay(500);
            }
            await Task.Delay(500);
        }
    }

    public async Task SubThirdPartyPost<T>(CancellationToken cancel)
    {
        T msg;
        while (!cancel.IsCancellationRequested)
        {
            try
            {
                msg = MyRedis.Client.SPop<T>(SubscriptionKey.ThirdPartyJobDetail);
                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"第三方岗位发布出错", Tools.GetErrMsg(ex));
                await Task.Delay(500);
            }
        }
    }

    /// <summary>
    /// 职位统计
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubPostStatis(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<string?>(SubscriptionKey.PostStatis);

                if (string.IsNullOrWhiteSpace(msg))
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息
                var recruitId = msg;
                using var _context = _contextFactory.CreateDbContext();
                var delivery = _context.Recruit.Where(x => x.RecruitId == recruitId)
                .Select(s => new { s.Post_Delivery.PostId, s.Post_Delivery.TeamPostId, s.SeekerId })
                .FirstOrDefault();
                if (delivery == null)
                    throw new Exception("投递Id不存在");

                var teamPostId = delivery.TeamPostId;
                var postId = delivery.PostId;

                //统计职位信息
                var postSt = _context.Recruit.Where(x => x.Post_Delivery.PostId == postId
                && x.Status != RecruitStatus.FileAway)
                .GroupBy(g => g.Status)
                .Select(s => new
                {
                    s.Key,
                    Ct = s.Count()
                }).ToList();

                var post = _context.Post_Extend.First(x => x.PostId == postId);
                post.HrScreeningNum = postSt.FirstOrDefault(x => x.Key == RecruitStatus.HrScreening)?.Ct ?? 0;
                post.InterviewerScreeningNum = postSt.FirstOrDefault(x => x.Key == RecruitStatus.InterviewerScreening)?.Ct ?? 0;
                post.InterviewNum = postSt.FirstOrDefault(x => x.Key == RecruitStatus.Interview)?.Ct ?? 0;
                post.OfferNum = postSt.FirstOrDefault(x => x.Key == RecruitStatus.Offer)?.Ct ?? 0;
                post.InductionNum = postSt.FirstOrDefault(x => x.Key == RecruitStatus.Induction)?.Ct ?? 0;
                post.ContractNum = postSt.FirstOrDefault(x => x.Key == RecruitStatus.Contract)?.Ct ?? 0;
                post.DeliveryNum = _context.Post_Delivery.Where(x => x.PostId == postId).Select(s => s.SeekerId)
                .Distinct().Count();

                if (!string.IsNullOrWhiteSpace(teamPostId))
                {
                    //统计代招职位信息
                    var teamSt = _context.Recruit.Where(x => x.Post_Delivery.TeamPostId == teamPostId
                    && x.Status != RecruitStatus.FileAway)
                    .GroupBy(g => g.Status)
                    .Select(s => new
                    {
                        s.Key,
                        Ct = s.Count()
                    }).ToList();
                    var teamPost = _context.Post_Team_Extend.First(x => x.TeamPostId == teamPostId);

                    teamPost.HrScreeningNum = teamSt.FirstOrDefault(x => x.Key == RecruitStatus.HrScreening)?.Ct ?? 0;
                    teamPost.InterviewerScreeningNum = teamSt.FirstOrDefault(x => x.Key == RecruitStatus.InterviewerScreening)?.Ct ?? 0;
                    teamPost.InterviewNum = teamSt.FirstOrDefault(x => x.Key == RecruitStatus.Interview)?.Ct ?? 0;
                    teamPost.OfferNum = teamSt.FirstOrDefault(x => x.Key == RecruitStatus.Offer)?.Ct ?? 0;
                    teamPost.InductionNum = teamSt.FirstOrDefault(x => x.Key == RecruitStatus.Induction)?.Ct ?? 0;
                    teamPost.ContractNum = teamSt.FirstOrDefault(x => x.Key == RecruitStatus.Contract)?.Ct ?? 0;
                    teamPost.DeliveryNum = _context.Post_Delivery.Where(x => x.TeamPostId == teamPostId).Select(s => s.SeekerId)
                    .Distinct().Count();
                }

                _context.SaveChanges();

                //用户行为数据
                var usb = _context.Recruit.Where(x => x.SeekerId == delivery.SeekerId)
                .GroupBy(g => g.Status)
                .Select(s => new
                {
                    s.Key,
                    Ct = s.Count()
                }).ToList();
                var seekerBehavior = new SeekerDeliveryBehavior();
                seekerBehavior.ContractNum = 0;
                seekerBehavior.HrScreeningNum = usb.FirstOrDefault(x => x.Key == RecruitStatus.HrScreening)?.Ct ?? 0;
                seekerBehavior.InterviewerScreeningNum = usb.FirstOrDefault(x => x.Key == RecruitStatus.InterviewerScreening)?.Ct ?? 0;
                seekerBehavior.InterviewNum = usb.FirstOrDefault(x => x.Key == RecruitStatus.Interview)?.Ct ?? 0;
                seekerBehavior.OfferNum = usb.FirstOrDefault(x => x.Key == RecruitStatus.Offer)?.Ct ?? 0;
                seekerBehavior.InductionNum = usb.FirstOrDefault(x => x.Key == RecruitStatus.Induction)?.Ct ?? 0;

                var cacheKey = $"{RedisKey.SeekerBehavior.Key}{delivery.SeekerId}";
                MyRedis.Client.HSet(cacheKey, RedisKey.SeekerBehavior.Delivery, seekerBehavior);
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.PostStatis}消息处理出错", Tools.GetErrMsg(e), (msg ?? string.Empty).ToString());
                await Task.Delay(500);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 招聘状态变更
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubRecruitStatusChange(CancellationToken cancel)
    {
        //处理消息
        Sub_Recruit_StatusChange? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.LPop<Sub_Recruit_StatusChange>(SubscriptionKey.RecruitStatusChange);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                // 招聘状态变更-计算简历处理时长
                MyRedis.Client.SAdd(SubscriptionKey.ResumeProcessTime, msg);

                // 入职进入职员工表 - 暂时不需要
                //if (msg.Status == RecruitStatus.Induction)
                //{
                //    MyRedis.Client.SAdd(SubscriptionKey.RecruitInduction, msg.RecruitId);
                //}

                //统计职位中的招聘数量
                MyRedis.Client.SAdd(SubscriptionKey.PostStatis, msg.RecruitId);

                using var _context = _contextFactory.CreateDbContext();
                var delivery = _context.Recruit.Where(x => x.RecruitId == msg.RecruitId)
                .Select(s => new
                {
                    s.User_Seeker.User.Mobile,
                    s.Post_Delivery.PostId,
                    s.Post_Delivery.TeamPostId,
                    s.SeekerId,
                    s.HrId,
                    s.DeliveryId,
                    s.RecruitId,
                    s.CreatedTime,
                    TeamHrId = s.Post_Delivery.Post_Team.Project_Team.HrId,
                    TeamProjectId = s.Post_Delivery.Post_Team.TeamProjectId,
                    PostName = s.Post_Delivery.Post.Name,
                    SeekerName = s.User_Seeker.NickName,
                    OrderId = s.Post_Bounty.Id
                }).FirstOrDefault();

                if (delivery == null)
                    throw new Exception("投递Id不存在");

                if (!string.IsNullOrWhiteSpace(delivery.OrderId))// 有订单才进交付分润
                {
                    // 招聘状态(所有状态)变更即进入计算分润
                    MyRedis.Client.SAdd(SubscriptionKey.ProjectTeamBounty, msg.RecruitId);
                }

                // 投递发钉钉机器人消息
                // 投递时更新人才线索池隐藏逻辑
                if (msg.IsNew == true)
                {
                    MyRedis.Client.SAdd(SubscriptionKey.RecruitDingTalkRobot, msg.RecruitId);
                    MyRedis.Client.SAdd(SubscriptionKey.RecruitChangeToTalentResume, msg.RecruitId);
                }

                var postCacheKey = $"{RedisKey.PostData.Key}{delivery.PostId}";
                var teamPostCacheKey = $"{RedisKey.PostData.TeamKey}{delivery.TeamPostId}";

                //如果是投递，记录投递者头像
                if (msg.Status == RecruitStatus.HrScreening)
                {
                    // var tdKey = $"{RedisKey.PostRandomAvatars}{delivery.PostId}";
                    var tdAvatars = _context.Post_Delivery.Where(x => x.PostId == delivery.PostId)
                    .GroupBy(g => g.SeekerId)
                    .OrderBy(o => EF.Functions.Random())
                    .Take(15)
                    .Select(s => s.Max(m => m.User_Seeker.Avatar)).ToList()!;
                    MyRedis.Client.HSet(postCacheKey, RedisKey.PostData.DeliveryAvatars, tdAvatars);

                    //今日投递
                    var today = DateTime.Today;
                    var todayDelivery = _context.Post_Delivery.Count(x => x.Post_Team.Project_Team.HrId == delivery.TeamHrId && x.CreatedTime >= today);
                    var todayKey = $"{RedisKey.HrStatBehavior.DailyKey}{DateTime.Today.ToString("yyyyMMdd")}";
                    var todayDeliveryKey = $"{RedisKey.HrStatBehavior.Delivery}{delivery.TeamHrId}";
                    MyRedis.Client.HSet(todayKey, todayDeliveryKey, todayDelivery);

                    //历史投递
                    if (msg.IsNew == true)
                        MyRedis.Client.HIncrBy(RedisKey.HrStatBehavior.FullKey, $"{RedisKey.HrStatBehavior.Delivery}{delivery.TeamHrId}", 1);
                }

                var columnKey = ((int)msg.Status).ToString();
                if (!string.IsNullOrWhiteSpace(columnKey))
                {
                    //只记录一次
                    if (MyRedis.Client.HSetNx($"{RedisKey.Idempotency.RecruitStatus}", $"{delivery.PostId}_{delivery.SeekerId}_{(int)msg.Status}", DateTime.Now.ToYYYY_MM_DD_HH()))
                    {
                        MyRedis.Client.HIncrBy(postCacheKey, columnKey, 1);
                        MyRedis.Client.HIncrBy(teamPostCacheKey, columnKey, 1);
                    }
                    _context.SaveChanges();
                }

                //更新hr数据
                _commonProjectService.UpdateHrProjectData(new Sub_UpdateHrData { UserId = delivery.HrId, Type = UpdateHrDataType.招聘 });

                // var recruitRecord = _context.Recruit_Record.Where(x => x.Id == msg.RecruitRecordId)
                // .Select(s => new
                // {
                //     s.CreatedTime
                // }).FirstOrDefault();

                // if (recruitRecord == null)
                //     throw new Exception($"招聘流程记录不存在:{msg.RecruitRecordId}");

                //通知消息
                if (msg.Status == RecruitStatus.Interview)
                {
                    var aoe = new MsgNotifyModel
                    {
                        Type = MsgNotifyType.招聘流程变更面试,
                        EventTime = DateTime.Now,
                        UserId = delivery.SeekerId,
                        Data = new MsgNotifyRecruit
                        {
                            TeamPostId = delivery.TeamPostId,
                            TeamProjectId = delivery.TeamProjectId,
                            PostName = delivery.PostName,
                            SeekerName = delivery.SeekerName,
                            RecruitId = delivery.RecruitId,
                            DeliveryTime = delivery.CreatedTime
                        }
                    };

                    MyRedis.Client.RPush(SubscriptionKey.MsgWeChatMpNotify, aoe);
                }
                else if (msg.Status == RecruitStatus.FileAway)
                {
                    var aoe = new MsgNotifyModel
                    {
                        Type = MsgNotifyType.招聘流程变更归档,
                        EventTime = DateTime.Now,
                        UserId = delivery.SeekerId,
                        Data = new MsgNotifyRecruit
                        {
                            TeamPostId = delivery.TeamPostId,
                            TeamProjectId = delivery.TeamProjectId,
                            PostName = delivery.PostName,
                            SeekerName = delivery.SeekerName,
                            RecruitId = delivery.RecruitId,
                            DeliveryTime = delivery.CreatedTime
                        }
                    };

                    MyRedis.Client.RPush(SubscriptionKey.MsgWeChatMpNotify, aoe);
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.RecruitStatusChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(500);
            }

            await Task.Delay(20);
        }
    }

    public async Task SubRecruitStatusInvalidChange(CancellationToken cancel)
    {
        //处理消息
        Sub_Recruit_StatusChange? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.LPop<Sub_Recruit_StatusChange>(SubscriptionKey.RecruitStatusChange2);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                using var _context = _contextFactory.CreateDbContext();
                var delivery = _context.Recruit.Where(x => x.RecruitId == msg.RecruitId)
                .Select(s => new
                {
                    s.User_Seeker.User.Mobile,
                    s.Post_Delivery.PostId,
                    s.Post_Delivery.TeamPostId,
                    s.SeekerId,
                    s.HrId,
                    TeamHrId = s.Post_Delivery.Post_Team.Project_Team.HrId,
                    TeamProjectId = s.Post_Delivery.Post_Team.TeamProjectId,
                    PostName = s.Post_Delivery.Post.Name,
                    SeekerName = s.User_Seeker.NickName,
                    OrderId = s.Post_Bounty.Id
                }).FirstOrDefault();

                if (delivery == null)
                    throw new Exception("投递Id不存在");

                if (!string.IsNullOrWhiteSpace(delivery.OrderId))// 有订单才进交付分润
                {
                    // 招聘状态(所有状态)变更即进入计算分润
                    MyRedis.Client.SAdd(SubscriptionKey.ProjectTeamBounty, msg.RecruitId);
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.RecruitStatusChange2}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(500);
            }

            await Task.Delay(20);
        }
    }

    // /// <summary>
    // /// 职位变更
    // /// </summary>
    // /// <param name="cancel"></param>
    // /// <returns></returns>
    // public async Task SubPostChange(CancellationToken cancel)
    // {
    //     //处理消息
    //     string? msg = null;
    //     while (!cancel.IsCancellationRequested)
    //     {
    //         try
    //         {
    //             msg = MyRedis.Client.SPop<string?>(SubscriptionKey.PostChange);

    //             if (string.IsNullOrWhiteSpace(msg))
    //             {
    //                 await Task.Delay(500);
    //                 continue;
    //             }

    //             //处理消息
    //             var postId = msg;
    //             using var _context = _contextFactory.CreateDbContext();

    //         }
    //         catch (Exception e)
    //         {
    //             _logger.Error($"{SubscriptionKey.PostChange}消息处理出错", Tools.GetErrMsg(e), (msg ?? string.Empty).ToString());
    //             await Task.Delay(500);
    //         }

    //         await Task.Delay(20);
    //     }
    // }

    /// <summary>
    /// 职位顾问职位统计
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubTeamHrTj(CancellationToken cancel)
    {
        //处理消息
        ZMember? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                // msg = MyRedis.Client.SPop<Sub_TeamHrTj>(SubscriptionKey.TeamHrTj);

                // if (msg == null)
                // {
                //     await Task.Delay(500);
                //     continue;
                // }

                var maxTime = DateTime.Now.AddSeconds(-10).ToUnixTimeMs();

                //查询到时间的消息
                msg = MyRedis.Client.ZRangeByScoreWithScores(SubscriptionKey.TeamHrTj, 0, maxTime, 0, 1).FirstOrDefault();

                if (msg == null || (msg is IDictionary<string, object> dict && dict.Count == 0))
                {
                    await Task.Delay(10000);
                    continue;
                }

                //取出之后移除
                MyRedis.Client.ZRem(SubscriptionKey.TeamHrTj, msg.member);

                //处理消息
                using var _context = _contextFactory.CreateDbContext();

                //hr行为数据hashkey
                var hrCacheKey = $"{RedisKey.HrBehavior.Key}{msg.member}";

                //更新热搜
                var postCategorys = _context.Post_Team.Where(x => x.Project_Team.HrId == msg.member && x.Show)
                .Select(s => s.Post.Dic_Post.Name).Distinct().OrderBy(o => EF.Functions.Random()).Take(8).ToList();
                postCategorys = postCategorys.Where(x => !string.IsNullOrWhiteSpace(x)).ToList();
                MyRedis.Client.HSet(hrCacheKey, RedisKey.HrBehavior.PostHotKeywords, postCategorys);

                //更新hr拥有的项目
                var projectCategorys = _context.Post_Team.Where(x => x.Project_Team.HrId == msg.member && x.Show)
                .Select(s => s.Post.Project.Industry).Distinct().Take(8).ToList();
                MyRedis.Client.HSet(hrCacheKey, RedisKey.HrBehavior.ProjectIndustry, projectCategorys);

                //更新hr职位区域
                var postCitys = _context.Post_Team.Where(x => x.Project_Team.HrId == msg.member && x.Show)
                .GroupBy(g => g.Post.RegionId).Select(s => s.Key).ToList();
                MyRedis.Client.HSet(hrCacheKey, RedisKey.HrBehavior.PostCity, postCitys);

                //职位分布
                var postDistribute = _context.Post_Team.Where(x => x.Project_Team.HrId == msg.member && x.Show)
                .GroupBy(g => g.Post.Dic_Post.Parent!.Parent!.Name).OrderByDescending(o => o.Count())
                .Select(s => new PostDistribute
                {
                    Name = s.Key,
                    Num = s.Count()
                }).ToList();
                var postDistributeTop10 = postDistribute.Take(9).ToList();
                var postDistributeOtherNum = postDistribute.Skip(9).Sum(s => s.Num);
                if (postDistributeOtherNum > 0)
                    postDistributeTop10.Add(new PostDistribute { Name = "其他", Num = postDistributeOtherNum });
                MyRedis.Client.HSet(hrCacheKey, RedisKey.HrBehavior.PostDistribute, postDistributeTop10);
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.TeamHrTj}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(500);
            }

            await Task.Delay(20);
        }
    }

    // /// <summary>
    // /// 职位状态变更
    // /// </summary>
    // /// <param name="cancel"></param>
    // /// <returns></returns>
    // public async Task SubPostStatusChange(CancellationToken cancel)
    // {
    //     //处理消息
    //     Sub_Post_StatusChange? msg = null;
    //     while (!cancel.IsCancellationRequested)
    //     {
    //         var workStartTime = DateTime.Now;
    //         try
    //         {
    //             msg = MyRedis.Client.SPop<Sub_Post_StatusChange>(SubscriptionKey.PostStatusChange);

    //             if (msg == null)
    //             {
    //                 await Task.Delay(500);
    //                 continue;
    //             }

    //             //处理消息
    //             using var _context = _contextFactory.CreateDbContext();
    //             var teamHrIds = _context.Post_Team.Where(x => x.PostId == msg.PostId)
    //             .Select(s => s.Project_Team.HrId).Distinct().ToArray();

    //             var his = teamHrIds.Select(s => new Sub_TeamPost_StatusChange
    //             {
    //                 TeamHrId = s
    //             }).ToArray();

    //             if (his.Count() > 0)
    //                 MyRedis.Client.SAdd(SubscriptionKey.TeamPostStatusChange, his);
    //         }
    //         catch (Exception e)
    //         {
    //             _logger.Error($"{SubscriptionKey.PostStatusChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
    //             await Task.Delay(500);
    //         }

    //         await Task.Delay(20);
    //     }
    // }

    /// <summary>
    /// 项目状态变更
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubProjectStatusChange(CancellationToken cancel)
    {
        //处理消息
        Sub_Project_StatusChange? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_Project_StatusChange>(SubscriptionKey.ProjectStatusChange);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息
                using var _context = _contextFactory.CreateDbContext();

                var project = _context.Project.Where(x => x.ProjectId == msg.ProjectId)
                .Select(s => new
                {
                    s.Status
                }).First();

                //如果项目归档，那么所有职位归档
                if (project.Status == ProjectStatus.归档)
                    _context.Database.ExecuteSqlInterpolated($"UPDATE post SET `Status` = 0 WHERE ProjectId = {msg.ProjectId}");

                //项目状态变更处理
                _commonProjectService.UpdatePostShow(msg.ProjectId, UpdatePostShowType.项目);

                if (project.Status == ProjectStatus.归档)
                {
                    //通知相关人员
                    var notifyMsgs = _context.Project_Team.Where(x => x.ProjectId == msg.ProjectId)
                    .Select(s => new MsgNotifyModel
                    {
                        Type = MsgNotifyType.归档,
                        EventTime = s.Project.EndTime < DateTime.Now ? s.Project.EndTime : DateTime.Now,
                        // TeamHrId = s.HrId,
                        UserId = s.HrId,
                        Data = new MsgNotifyProjectEnd
                        {
                            // TeamProjectId = s.TeamProjectId,
                            IsSelfProj = s.Type == HrProjectType.自己,
                            ProjectId = s.ProjectId,
                            ProjectName = s.Project.Agent_Ent.Name,
                            HrName = s.Project.User_Hr.NickName
                        }
                    }).ToArray();

                    //钉钉通知、公众号通知
                    if (notifyMsgs?.Count() > 0)
                    {
                        MyRedis.Client.RPush(SubscriptionKey.MsgDingDingNotify, notifyMsgs);
                        MyRedis.Client.RPush(SubscriptionKey.MsgWeChatMpNotify, notifyMsgs);
                    }
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.ProjectStatusChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(500);
            }

            await Task.Delay(20);
        }
    }

    /// <summary>
    /// 职位经纬度更新
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubPostLocationChange(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<string?>(SubscriptionKey.PostLocationChange);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息
                using var _context = _contextFactory.CreateDbContext();
                var post = _context.Post.Where(x => x.PostId == msg)
                .First();

                var locationMap = string.Empty;
                if (post.Location.X > 0 && post.Location.Y > 0)
                    locationMap = await _tencentMapHelper.GetStaticMap(post.PostId, post.Location.X, post.Location.Y);

                post.LocationMap = locationMap;
                _context.SaveChanges();
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.PostLocationChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(500);
            }

            await Task.Delay(500);
        }
    }

    /// <summary>
    /// 浏览职位消息
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubViewPost(CancellationToken cancel)
    {
        //处理消息
        Sub_Post_View? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.LPop<Sub_Post_View>(SubscriptionKey.PostView);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息
                var checkKey = $"tmvis:{msg.Id}";
                if (MyRedis.Client.SetNx(checkKey, DateTime.Now, 3600))
                {
                    //如果当前用户登录，记录访问行为
                    if (!string.IsNullOrWhiteSpace(msg.SeekerId))
                    {
                        // var pcKey = $"{RedisKey.PostCategoryVisited}{msg.SeekerId}_{msg.HrId}";
                        // _ = Task.Run(() => MyRedis.Client.ZIncrBy(pcKey, 1, msg.PostCategory.ToString()));

                        //记录方式改为hash
                        var pcKey = $"{RedisKey.PostCategoryVisited2}";
                        var ace = MyRedis.Client.HGet<List<GeneralKeyValue<int>>?>(pcKey, $"{msg.SeekerId}_{msg.HrId}");
                        ace = ace ?? new List<GeneralKeyValue<int>>();
                        var fir = ace.FirstOrDefault(x => x.Key == msg.PostCategory.ToString());
                        if (fir == null)
                            ace.Add(new GeneralKeyValue<int> { Key = msg.PostCategory.ToString(), Value = 1 });
                        else
                            fir.Value += 1;
                        MyRedis.Client.HSet(pcKey, $"{msg.SeekerId}_{msg.HrId}", ace);
                    }

                    //计算访问量
                    _ = Task.Run(() => MyRedis.Client.HIncrBy($"{RedisKey.PostData.Key}{msg.PostId}", RedisKey.PostData.Visit, 1));
                    _ = Task.Run(() => MyRedis.Client.HIncrBy($"{RedisKey.PostData.TeamKey}{msg.TeamPostId}", RedisKey.PostData.Visit, 1));
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.PostView}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(500);
            }

            await Task.Delay(20);
        }
    }

    /// <summary>
    /// 协同交付
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubProjectTeamBounty(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<string?>(SubscriptionKey.ProjectTeamBounty);

                if (string.IsNullOrWhiteSpace(msg))
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息
                var recruitId = msg;
                using var _context = _contextFactory.CreateDbContext();
                var recruit = _context.Recruit.Where(x => x.RecruitId == recruitId)
                .Select(s => new
                {
                    s.Post_Delivery.Post.ProjectId,
                    s.Post_Delivery.Post.PaymentNode,
                    TeamProjectType = s.Post_Delivery.Post_Team.Project_Team.Type,
                    s.Status,
                    s.Post_Delivery.Post_Team.TeamPostId,
                    s.Post_Delivery.Post_Team.TeamProjectId,
                    SeekerName = s.Post_Delivery.User_Seeker.NickName,
                    HrId = s.HrId,
                    TeamHrId = s.Post_Delivery.Post_Team.Project_Team.HrId,
                    PostName = s.Post_Delivery.Post.Name,
                    OrderStatus = s.Post_Bounty.Status,
                    OrderId = s.Post_Bounty.Id,
                    OrderPaymentNode = s.Post_Bounty.PaymentNode,
                    s.Post_Bounty.IfStockOut,
                    LastInterViewTime = s.Recruit_Interview != null && s.Recruit_Interview.Count > 0 ? s.Recruit_Interview.Max(m => m.InterviewTime) : DateTime.MinValue,
                    FileAwayType = s.FileAway,
                    InvalidReason = s.InvalidReason,
                    LastStatus = s.Recruit_Record.Where(w => w.Status != s.Status).OrderByDescending(o => o.CreatedTime).Select(s => s.Status).FirstOrDefault()
                }).First();

                // // 不同招聘流程状态，不同场景有不同交付方式
                // var delivery = new DeliverySimpleFactory(
                //     new ProjectPostInfo
                //     {
                //         Status = recruit.OrderStatus
                //     },
                //     new RecruitInfos
                //     {
                //         LastInterviewTime = recruit.LastInterViewTime,
                //         FileAwayType = recruit.FileAwayType,
                //         InvalidReason = recruit.InvalidReason,
                //         LastStatus = recruit.LastStatus
                //     },
                //     _orderService)
                //     .CreateDelivery(recruit.PaymentNode == null ? recruit.OrderPaymentNode!.Value : recruit.PaymentNode.Value);
                // _logger.Info("SubProjectTeamBounty", $"订单交付-{recruit.Status.GetDescription()}", $"订单号：{recruit.OrderId},开始处理");
                // switch (recruit.Status)
                // {
                //     case RecruitStatus.HrScreening:
                //         delivery.HrScreening(recruit.OrderId);
                //         break;
                //     case RecruitStatus.InterviewerScreening:
                //         delivery.InterviewerScreening(recruit.OrderId);
                //         break;
                //     case RecruitStatus.Interview:
                //         delivery.Interview(recruit.OrderId);
                //         break;
                //     case RecruitStatus.Offer:
                //         delivery.Offer(recruit.OrderId);
                //         break;
                //     case RecruitStatus.Induction:
                //         delivery.Induction(recruit.OrderId);
                //         break;
                //     case RecruitStatus.Contract: break;
                //     case RecruitStatus.FileAway:
                //         delivery.FileAway(recruit.OrderId);
                //         break;
                // }
                _logger.Info("SubProjectTeamBounty", $"订单交付-{recruit.Status.GetDescription()}", $"订单号：{recruit.OrderId},处理完成");
                //通知消息
                //MsgHelper.SendMsgNotify(new MsgNotifyModel
                //{
                //    Type = MsgNotifyType.协同交付,
                //    EventTime = DateTime.Now,
                //    TeamHrId = recruit.TeamHrId,
                //    HrId = recruit.HrId,
                //    Data = new MsgNotifyTeamDelivery
                //    {
                //        TeamPostId = recruit.TeamPostId,
                //        TeamProjectId = recruit.TeamProjectId,
                //        TeamHrId = recruit.TeamHrId,
                //        HrId = recruit.HrId,
                //        PostName = recruit.PostName,
                //        SeekerName = recruit.SeekerName,
                //        Money = teamBounty.SettlementMoney,
                //        Status = teamBounty.Status,
                //        RecruitId = recruitId
                //    }
                //});
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.ProjectTeamBounty}消息处理出错", Tools.GetErrMsg(e), (msg ?? string.Empty).ToString());
                await Task.Delay(500);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 生成职位小程序二维码
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubPostAppletQrCode(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<string?>(SubscriptionKey.PostAppletQrCode);

                if (string.IsNullOrWhiteSpace(msg))
                {
                    await Task.Delay(1000);
                    continue;
                }

                //处理消息
                using var _context = _contextFactory.CreateDbContext();
                var teamPost = _context.Post_Team.Include(i => i.Project_Team).First(x => x.TeamPostId == msg);

                var url = await RetryHelper.Do(async () => await _weChatHelper.GetWxaCodeUnlimitForPosition(teamPost.TeamPostId));
                teamPost.AppletQrCode = url;

                _context.SaveChanges();
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.PostAppletQrCode}消息处理出错", Tools.GetErrMsg(e), (msg ?? string.Empty).ToString());
                await Task.Delay(500);
            }

            await Task.Delay(10);
        }
    }

    // /// <summary>
    // /// 诺积分
    // /// </summary>
    // /// <param name="cancel"></param>
    // /// <returns></returns>
    // public async Task AddNScore(CancellationToken cancel)
    // {
    //     //处理消息
    //     NScoreModel? msg = null;
    //     while (!cancel.IsCancellationRequested)
    //     {
    //         var workStartTime = DateTime.Now;
    //         try
    //         {
    //             msg = MyRedis.Client.RPop<NScoreModel?>(SubscriptionKey.AddNScore);

    //             if (msg == null)
    //             {
    //                 await Task.Delay(500);
    //                 continue;
    //             }

    //             //处理消息
    //             await _commonNScoreService.AddNScore(msg);
    //         }
    //         catch (Exception e)
    //         {
    //             _logger.Error($"{SubscriptionKey.AddNScore}消息处理出错", Tools.GetErrMsg(e), msg);
    //             await Task.Delay(500);
    //         }

    //         await Task.Delay(10);
    //     }
    // }

    /// <summary>
    /// 简历投递更新人才线索池隐藏逻辑
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task RecruitChangeToTalentResume(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<string?>(SubscriptionKey.RecruitChangeToTalentResume);

                if (string.IsNullOrWhiteSpace(msg))
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息
                var recruitId = msg;
                using var _context = _contextFactory.CreateDbContext();
                var user_mobile = _context.Recruit.Where(x => x.RecruitId == recruitId).Select(s => s.User_Seeker.User.Mobile).FirstOrDefault();

                if (user_mobile == null)
                    continue;

                var talentResume = _context.Talent_Resume.Where(w => w.Status != TalentResumeStatus.Deleted
                && w.Mobile == user_mobile
                && w.HideInvalidTime != null).FirstOrDefault();

                if (talentResume == null)
                    continue;

                // 同一种来源，一天只加一次30天
                string hideKey = $"{SubscriptionKey.RecruitChangeToTalentResume}Num_{talentResume.Mobile}_{talentResume.Source}_{DateTime.Now:d}";
                if (Convert.ToInt32(MyRedis.Client.HGet(hideKey, "count")) == 0)
                {
                    talentResume.HideInvalidTime = talentResume.HideInvalidTime!.Value.AddDays(30);
                    _context.SaveChanges();
                    MyRedis.Client.HSet(hideKey, "count", 1);
                    MyRedis.Client.Expire(hideKey, TimeSpan.FromDays(1));
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.RecruitChangeToTalentResume}消息处理出错", Tools.GetErrMsg(e), (msg ?? string.Empty).ToString());
                await Task.Delay(500);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 招聘状态变更-计算简历处理时长
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubRecruitProcessTime(CancellationToken cancel)
    {
        //处理消息
        Sub_Recruit_StatusChange? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_Recruit_StatusChange>(SubscriptionKey.ResumeProcessTime);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                // 查询该招聘流程最先的2个变更记录，计算处理时长(秒 )
                using var _context = _contextFactory.CreateDbContext();

                var recruit = _context.Recruit.Where(x => x.RecruitId == msg.RecruitId).First();

                if (recruit.ResumeProcessTime.HasValue)
                {
                    await Task.Delay(20);
                    continue;
                }

                var recruitRecord = _context.Recruit_Record.Where(x => x.RecruitId == msg.RecruitId)
                .OrderBy(o => o.CreatedTime).Take(2).ToList();

                if (recruitRecord.Count < 2)
                {
                    await Task.Delay(20);
                    continue;
                }

                var processTime = recruitRecord[1].CreatedTime - recruitRecord[0].CreatedTime;

                recruit.ResumeProcessTime = (int)processTime.TotalSeconds;

                // 再计算职位的平均处理时长
                var post = _context.Recruit.Where(x => x.RecruitId == msg.RecruitId).Select(s => s.Post_Delivery.Post.Post_Extend).First();

                post.ResumeProcessingDuration = (int?)_context.Recruit.Where(x => x.Post_Delivery.PostId == post.PostId && x.ResumeProcessTime.HasValue)
                .Average(a => a.ResumeProcessTime);

                _context.SaveChanges();
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.ResumeProcessTime}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(500);
            }

            await Task.Delay(20);
        }
    }
}