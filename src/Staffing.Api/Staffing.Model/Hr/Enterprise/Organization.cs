﻿namespace Staffing.Model.Hr.Enterprise;

public class GetDdOrg
{

}

public class GetEntOrgResponse
{
    public List<GetEntOrgDetail> Rows { get; set; } = default!;
}

public class GetEntOrgDetail
{
    /// <summary>
    /// 企业Id
    /// </summary>
    public string? EntId { get; set; }

    /// <summary>
    /// 编号（大于0才可以编辑和删除）
    /// </summary>
    public string? Id { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 父节点Id
    /// </summary>
    public string? ParentId { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; } = 0;

    /// <summary>
    /// 是否是本公司（集团架构）
    /// </summary>
    public bool CurrentEnt { get; set; } = true;

    /// <summary>
    /// 子节点
    /// </summary>
    public List<GetEntOrgDetail>? Children { get; set; }
}

public class GetDdOrgResponse
{
    public List<GetDdOrgDetail> Rows { get; set; } = default!;
}

public class GetDdOrgDetail
{
    /// <summary>
    /// 编号（大于0才可以编辑和删除）
    /// </summary>
    public long? Id { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 父节点Id
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; } = 0;

    /// <summary>
    /// 企业Id
    /// </summary>
    public string EntId { get; set; } = "1";

    /// <summary>
    /// 子节点
    /// </summary>
    public List<GetDdOrgDetail>? Children { get; set; }
}

public class AddEntOrg
{
    /// <summary>
    /// 部门名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 父Id
    /// </summary>
    public string? ParentId { get; set; }
}

public class AddEntOrgResponse
{
    public string? OrgId { get; set; }
}

public class UpdateEntOrg
{
    /// <summary>
    /// 部门名称(null不处理)
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 部门Id
    /// </summary>
    public string? OrgId { get; set; }

    /// <summary>
    /// 整数上移，负数下移，null或0=不处理
    /// </summary>
    public int? Move { get; set; }
}

public class DeleteEntOrg
{
    /// <summary>
    /// 部门Id
    /// </summary>
    public string? OrgId { get; set; }
}


public class GetEntResponse
{
    public List<GetEntDetail> Rows { get; set; } = default!;
}
public class GetEntDetail
{
    /// <summary>
    /// 企业Id
    /// </summary>
    public string? EntId { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }
}