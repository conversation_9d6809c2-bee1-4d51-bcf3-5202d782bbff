using Config.CommonModel;
using Config.Enums;
using Infrastructure.Common;
using Staffing.Core.Interfaces.CentreService;
using Staffing.Model.Hr.Activity;
using Staffing.Core.Interfaces;
using Config;
using Infrastructure.Extend;

namespace Staffing.Api.Apis.Seeker;

[Service(ServiceLifetime.Transient)]
public class ActivityRouter : ISeekerRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("act").WithTags("活动");
        group.MapPost("/tx/recommend", (TQRecommendEnt model, ITransitService rep, RequestContext user, CacheHelper cacheHelper, IHttpContextAccessor httpContextAccessor) =>
        {
            var actAdviserId = cacheHelper.GetRedisCache<string>(() => "158056310704067973", Config.RedisKey.Activity.RecommendAdviserId, -1);
            var result = rep.RecommendEnt(new Model.CentreService.Transit.RecommendEnt
            {
                IsCover = true,
                AdviserId = string.IsNullOrEmpty(model.AdviserId) ? actAdviserId : model.AdviserId,
                EntName = model.EntName,
                HrName = string.IsNullOrEmpty(model.HrName) ? user.Name : model.HrName,
                Mobile = user.Account,
                Source = HrRecommendEntSource.Act腾讯活动,
                Remark = ((string.IsNullOrEmpty(model.NeedService) ? string.Empty : $"是否需要猎头服务或批量用工交付：{model.NeedService}；")
                + (string.IsNullOrEmpty(model.JobRecruitment) ? string.Empty : $"招聘岗位：{model.JobRecruitment}；")
                + (string.IsNullOrEmpty(model.RecruitingNumber) ? string.Empty : $"招聘人数：{model.RecruitingNumber}")
                ).TrimEnd('；')
            });

            if (httpContextAccessor.HttpContext?.Request.Headers.TryGetValue("gdt_vid", out var clickId) == true)
            {
                if (!string.IsNullOrWhiteSpace(clickId))
                    MyRedis.Client.LPush(SubscriptionKey.TencentAdReport, new Sub_TencentAdReport
                    {
                        ClickId = clickId,
                        SetId = **********,
                        AccountId = ********,
                        At = "25c7c1e630d0fa4c3db34a4f9b08475d"
                    });
            }
            return new EmptyResponse();
        }).WithOpenApi(operation => new(operation) { Summary = "【腾讯活动】提交企业信息" });

        group.MapPost("/nyk/recommend", (NYKRecommendEnt model, ITransitService rep, ISharedService serviceRep, CacheHelper cacheHelper) =>
        {
            if (!model.Mobile.IsMobile())
                return new Infrastructure.Proxy.NuoApi<EmptyResponse>() { ErrCode = 201, ErrMsg = "请输入正确的手机号" };

            try
            {
                var checkResult = serviceRep.CheckSMSCode(new Model.Service.CheckSMSCode
                {
                    Phone = model.Mobile,
                    SMSCode = model.VerificationCode,
                    Type = SMSType.Activity
                });

                if (checkResult.Result)
                {
                    var actAdviserId = cacheHelper.GetRedisCache<string>(() => "158056310704067973", Config.RedisKey.Activity.RecommendAdviserId, -1);
                    var result = rep.RecommendEnt(new Model.CentreService.Transit.RecommendEnt
                    {
                        IsCover = true,
                        AdviserId = string.IsNullOrEmpty(model.AdviserId) ? actAdviserId : model.AdviserId,
                        EntName = model.EntName,
                        HrName = model.HrName,
                        Mobile = model.Mobile,
                        Source = HrRecommendEntSource.Act诺优考落地页
                    });

                    if (!string.IsNullOrWhiteSpace(model.Gdt_vid))
                    {
                        MyRedis.Client.LPush(SubscriptionKey.TencentAdReport, new Sub_TencentAdReport
                        {
                            ClickId = model.Gdt_vid,
                            SetId = **********,
                            AccountId = ********,
                            At = "e4c6dfb766d6548e24184ecdf8039fff"
                        });
                    }

                    return new Infrastructure.Proxy.NuoApi<EmptyResponse>() { ErrCode = 200, ErrMsg = "操作成功" };
                }
                else
                    return new Infrastructure.Proxy.NuoApi<EmptyResponse>() { ErrCode = 201, ErrMsg = "验证码错误" };
            }
            catch (Exception ee)
            {
                return new Infrastructure.Proxy.NuoApi<EmptyResponse>() { ErrCode = 201, ErrMsg = ee.Message };
            }
        })
        .WithOpenApi(operation => new(operation) { Summary = "【诺优考落地页】提交企业信息" });
    }
}