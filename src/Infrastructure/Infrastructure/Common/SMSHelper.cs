﻿using System.Text.RegularExpressions;
using Config;
using Config.CommonModel;
using Flurl.Http;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using ServiceStack.Text;

namespace Infrastructure.Common
{
    [Service(ServiceLifetime.Transient)]
    public class SMSHelper
    {
        private readonly ConfigManager _config;

        public SMSHelper(IOptionsSnapshot<ConfigManager> config)
        {
            _config = config.Value;
        }
        /// <summary>
        /// 庄点科技发送短信
        /// </summary>
        /// <param name="phone">手机号</param>
        /// <param name="content">内容</param>
        /// <param name="subcode">扩展码，值为数字或字母</param>
        /// <returns></returns>
        public async Task<(bool Success, ZhuangDianSendSMSResponse resp)> ZhuangDianSendSMS(string phone, string content, string? subcode = null)
        {
            return await ZhuangDianSendSMS(new string[] { phone }, content, subcode);
        }
        /// <summary>
        /// 庄点科技发送短信
        /// </summary>
        /// <param name="phones">手机号列表（每次最多可发送500条）</param>
        /// <param name="content">内容</param>
        /// <param name="subcode">扩展码，值为数字或字母</param>
        /// <returns></returns>
        public async Task<(bool Success, ZhuangDianSendSMSResponse resp)> ZhuangDianSendSMS(string[] phones, string content, string? subcode = null)
        {
            try
            {
                string url = $"http://{_config.ZhuangDian!.Api_IP}/json/submit";

                string timestamp = DateTime.Now.ToString("yyyyMMddHHmmssSSS"),
                    sendtime = DateTime.Now.ToString("yyyyMMddHHmm");

                var restr = await url.PostJsonAsync(new
                {
                    enterprise_no = _config.ZhuangDian.Enterprise_No,
                    account = _config.ZhuangDian.Account,
                    phones = string.Join(',', phones),
                    content = content,
                    timestamp = timestamp,
                    subcode = subcode ?? string.Empty,
                    sendtime = sendtime,
                    sign = Md5Helper.Md5(_config.ZhuangDian.Enterprise_No + _config.ZhuangDian.Account + timestamp + _config.ZhuangDian.Http_Sign_Key).ToUpper()
                })
                    .ReceiveString();

                var resp = JsonSerializer.DeserializeFromString<ZhuangDianSendSMSResponse>(restr);
                return (resp.result! == "0", resp);
            }
            catch (Exception ex)
            {
                return (false, new ZhuangDianSendSMSResponse()
                {
                    result = "999999",
                    desc = ex.Message
                });
            }
        }

        /// <summary>
        /// 验证发送内容中是否包含短信签名
        /// *签名需内容的最前面或最后面(中间不行，使用【】包围的就算签名，无需验证内部字符是否正确)
        /// </summary>
        /// <param name="Content">短信内容</param>
        /// <returns>true包含签名，false不含签名</returns>
        public bool CheckSMSSignature(string Content, out string Msg)
        {
            bool result = false;
            Msg = "未含签名";

            if (!string.IsNullOrEmpty(Content))
            {
                MatchCollection matches = Regex.Matches(Content, "【.+?】");
                if (matches.Count == 1)
                {
                    if ((Content.StartsWith(matches[0].Value) || Content.EndsWith(matches[0].Value)))
                    {
                        result = true;
                        Msg = "OK";
                    }
                    else
                        Msg = "签名需放在内容最前或最后";
                }
                else if (matches.Count > 1)
                {
                    if ((Content.StartsWith(matches[0].Value) && Content.EndsWith(matches[matches.Count - 1].Value)))
                    {
                        result = false;
                        Msg = "签名仅能添加一个";
                    }
                    else if ((Content.StartsWith(matches[0].Value) || Content.EndsWith(matches[matches.Count - 1].Value)))
                    {
                        result = true;
                        Msg = "OK";
                    }
                    else
                        Msg = "签名需放在内容最前或最后";
                }
            }
            return result;
        }

        ///// <summary>
        ///// 验证发送内容中是否包含短信签名
        ///// *签名需内容的最前面或最后面(中间不行)
        ///// </summary>
        ///// <param name="Content">短信内容</param>
        ///// <returns>true包含签名，false不含签名</returns>
        //public bool CheckSMSSignature(string Content,out string Msg)
        //{
        //    bool result = false;
        //    Msg = "未含签名";
        //    if (!string.IsNullOrEmpty(Content))
        //    {
        //        foreach (string s in _config.ZhuangDian!.SMSSignature)
        //        {
        //            if (Content.Contains(s))
        //            {
        //                if (Content.StartsWith(s) || Content.EndsWith(s))
        //                {
        //                    if (result || (Content != s && Content.StartsWith(s) && Content.EndsWith(s)))
        //                    {
        //                        result = false;
        //                        Msg = "签名仅能添加一个";
        //                    }
        //                    else
        //                    {
        //                        result = true;
        //                        Msg = "OK";
        //                    }
        //                }
        //                else
        //                    Msg = "签名需放在内容最前或最后";
        //            }
        //        }
        //    }
        //    return result;
        //}
    }
}
