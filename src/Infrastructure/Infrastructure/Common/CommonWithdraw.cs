﻿using Config;
using Essensoft.Paylink.WeChatPay;
using Essensoft.Paylink.WeChatPay.V3;
using Microsoft.Extensions.Options;
using Infrastructure.Exceptions;
using Microsoft.Extensions.DependencyInjection;
using Infrastructure.Extend;
using System.Text.Json.Serialization;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Transient)]
public class CommonWithdraw
{
    private readonly IWeChatPayClient _weichatclient;
    private readonly LogManager _log;
    private readonly IOptionsSnapshot<WeChatPayOptions> _wechatOptionsSnapshotAccessor;
    private ConfigManager _config;
    public CommonWithdraw(IOptionsSnapshot<WeChatPayOptions> wechatOptionsSnapshotAccessor,
    IWeChatPayClient weichatclient, IOptionsSnapshot<ConfigManager> config, LogManager log)
    {
        _config = config.Value;
        _wechatOptionsSnapshotAccessor = wechatOptionsSnapshotAccessor;
        _weichatclient = weichatclient;
        _log = log;
    }

    public async Task WechatWithdraw(string appId, string openId, decimal payMoney, string tradeNo)
    {
        await Task.FromResult(1);
        throw new BadRequestException("该功能暂未启用");

        // var opt = Tools.ModelConvert<WeChatPayOptions>(_wechatOptionsSnapshotAccessor.Value);
        // opt.AppId = appId;

        // var amount = Convert.ToInt32(payMoney * 100);
        // var model = new WeChatPayTransferBatchesBodyModel
        // {
        //     AppId = opt.AppId,
        //     OutBatchNo = tradeNo,
        //     BatchName = "诺快聘提现",
        //     BatchRemark = "诺快聘提现",
        //     TotalAmount = amount,
        //     TotalNum = 1,
        //     TransferDetailList = new List<TransferDetail>
        //     {
        //         new TransferDetail{
        //             OutDetailNo = tradeNo,
        //             TransferAmount = amount,
        //             TransferRemark = "诺快聘提现",
        //             OpenId = openId
        //         }
        //     }
        // };

        // var request = new WeChatPayTransferBatchesRequest();
        // request.SetBodyModel(model);

        // var response = await _weichatclient.ExecuteAsync(request, opt);

        // if (response.IsError)
        // {
        //     _log.Error("微信提现出错", response.Message, response);
        //     throw new ThirdPartyServerException(response.Message ?? string.Empty);
        // }
    }
}

/// <summary>
/// 发起商家转账API
/// </summary>
/// <remarks>
/// 商户可以通过该接口同时向多个用户微信零钱进行转账操作。
/// </remarks>
public class WeChatPayTransferBatchesRequest : IWeChatPayPrivacyPostRequest<WeChatPayTransferBatchesResponse>
{
    private WeChatPayObject _bodyModel = default!;

    public string GetRequestUrl()
    {
        return "https://api.mch.weixin.qq.com/v3/transfer/batches";
    }

    public WeChatPayObject GetBodyModel()
    {
        return _bodyModel;
    }

    public void SetBodyModel(WeChatPayObject bodyModel)
    {
        _bodyModel = bodyModel;
    }
}

/// <summary>
/// 发起商家转账
/// </summary>
public class WeChatPayTransferBatchesBodyModel : WeChatPayObject
{
    /// <summary>
    /// 直连商户的appid
    /// </summary>
    /// <remarks>
    /// 申请商户号的appid或商户号绑定的appid（企业号corpid即为此appid）
    /// <para>示例值：wxf636efh567hg4356</para>
    /// </remarks>
    [JsonPropertyName("appid")]
    public string? AppId { get; set; }

    /// <summary>
    /// 商家批次单号
    /// </summary>
    /// <remarks>
    /// 商户系统内部的商家批次单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一
    /// <para>示例值：plfk2020042013</para>
    /// </remarks>
    [JsonPropertyName("out_batch_no")]
    public string? OutBatchNo { get; set; }

    /// <summary>
    /// 批次名称
    /// </summary>
    /// <remarks>
    /// 该笔批量转账的名称
    /// <para>示例值：2019年1月深圳分部报销单</para>
    /// </remarks>
    [JsonPropertyName("batch_name")]
    public string? BatchName { get; set; }

    /// <summary>
    /// 批次备注
    /// </summary>
    /// <remarks>
    /// 转账说明，UTF8编码，最多允许32个字符
    /// <para>示例值：2019年1月深圳分部报销单</para>
    /// </remarks>
    [JsonPropertyName("batch_remark")]
    public string? BatchRemark { get; set; }

    /// <summary>
    /// 转账总金额
    /// </summary>
    /// <remarks>
    /// 转账金额单位为“分”。转账总金额必须与批次内所有明细转账金额之和保持一致，否则无法发起转账操作
    /// <para>示例值：4000000</para>
    /// </remarks>
    [JsonPropertyName("total_amount")]
    public int TotalAmount { get; set; }

    /// <summary>
    /// 转账总笔数
    /// </summary>
    /// <remarks>
    /// 个转账批次单最多发起三千笔转账。转账总笔数必须与批次内所有明细之和保持一致，否则无法发起转账操作
    /// <para>示例值：200</para>
    /// </remarks>
    [JsonPropertyName("total_num")]
    public int TotalNum { get; set; }

    /// <summary>
    /// 转账明细列表
    /// </summary>
    /// <remarks>
    /// 发起批量转账的明细列表，最多三千笔
    /// </remarks>
    [JsonPropertyName("transfer_detail_list")]
    public List<TransferDetail> TransferDetailList { get; set; } = new List<TransferDetail>();
}

/// <summary>
/// 转账明细
/// </summary>
public class TransferDetail : WeChatPayObject
{
    /// <summary>
    /// 商家明细单号
    /// </summary>
    /// <remarks>
    /// 商户系统内部区分转账批次单下不同转账明细单的唯一标识，要求此参数只能由数字、大小写字母组成
    /// <para>示例值：x23zy545Bd5436</para>
    /// </remarks>
    [JsonPropertyName("out_detail_no")]
    public string? OutDetailNo { get; set; }

    /// <summary>
    /// 转账金额
    /// </summary>
    /// <remarks>
    /// 转账金额单位为分
    /// <para>示例值：200000</para>
    /// </remarks>
    [JsonPropertyName("transfer_amount")]
    public int TransferAmount { get; set; }

    /// <summary>
    /// 转账备注
    /// </summary>
    /// <remarks>
    /// 单条转账备注（微信用户会收到该备注），UTF8编码，最多允许32个字符
    /// <para>示例值：2020年4月报销</para>
    /// </remarks>
    [JsonPropertyName("transfer_remark")]
    public string? TransferRemark { get; set; }

    /// <summary>
    /// 用户在直连商户应用下的用户标示
    /// </summary>
    /// <remarks>
    /// openid是微信用户在公众号appid下的唯一用户标识（appid不同，则获取到的openid就不同），可用于永久标记一个用户
    /// <a href="https://pay.weixin.qq.com/wiki/doc/apiv3/terms_definition/chapter1_1_3.shtml">获取openid</a>
    /// <para>示例值：o-MYE42l80oelYMDE34nYD456Xoy</para>
    /// </remarks>
    [JsonPropertyName("openid")]
    public string? OpenId { get; set; }

    /// <summary>
    /// 收款用户姓名
    /// </summary>
    /// <remarks>
    /// 1、明细转账金额 >= 2,000，收款用户姓名必填；
    /// 2、同一批次转账明细中，收款用户姓名字段需全部填写、或全部不填写；
    /// 3、 若传入收款用户姓名，微信支付会校验用户openID与姓名是否一致，并提供电子回单；
    /// 4、收款方姓名。采用标准RSA算法，公钥由微信侧提供
    /// 5、该字段需进行加密处理，加密方法详见敏感信息加密说明。(提醒：必须在HTTP头中上送Wechatpay-Serial)
    /// <para>示例值：757b340b45ebef5467rter35gf464344v3542sdf4t6re4tb4f54ty45t4yyry45</para>
    /// </remarks>
    [WeChatPayPrivacyProperty]
    [JsonPropertyName("user_name")]
    public string? UserName { get; set; }
}

public class WeChatPayTransferBatchesResponse : WeChatPayResponse
{
    /// <summary>
    /// 商家批次单号
    /// </summary>
    /// <remarks>
    /// 商户系统内部的商家批次单号
    /// <para>示例值：plfk2020042013</para>
    /// </remarks>
    [JsonPropertyName("out_batch_no")]
    public string? OutBatchNo { get; set; }

    /// <summary>
    /// 微信批次单号
    /// </summary>
    /// <remarks>
    /// 微信批次单号，微信商家转账系统返回的唯一标识
    /// <para>示例值：1030000071100999991182020050700019480001</para>
    /// </remarks>
    [JsonPropertyName("batch_id")]
    public string? BatchId { get; set; }

    /// <summary>
    /// 批次创建时间
    /// </summary>
    /// <remarks>
    /// 批次受理成功时返回，遵循rfc3339标准格式，格式为yyyy-MM-DDTHH:mm:ss.sss+TIMEZONE，yyyy-MM-DD表示年月日，T出现在字符串中，表示time元素的开头，HH:mm:ss.sss表示时分秒毫秒，TIMEZONE表示时区（+08:00表示东八区时间，领先UTC 8小时，即北京时间）。例如：2015-05-20T13:29:35.120+08:00表示北京时间2015年05月20日13点29分35秒
    /// <para>示例值：2015-05-20T13:29:35.120+08:00</para>
    /// </remarks>
    [JsonPropertyName("create_time")]
    public string? CreateTime { get; set; }
}