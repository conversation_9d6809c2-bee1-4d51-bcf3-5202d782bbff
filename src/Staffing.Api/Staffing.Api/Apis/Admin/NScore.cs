using Staffing.Core.Interfaces.CentreService;
using Infrastructure.Extend;

namespace Staffing.Api.Apis.Admin;

[Service(ServiceLifetime.Transient)]
public class NScoreRouter : IAdminRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("nscore").WithTags("积分");
        // group.MapGet("/list", ([AsParameters] GetNScores model, INScoreService rep) => rep.GetNScores(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "积分列表" });

        // group.MapPut("/set", (SetNScore model, INScoreService rep) => rep.SetNScore(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "更新积分" });

        // group.MapGet("/goods", ([AsParameters] GetGoodsList model, INScoreService rep) => rep.GetGoodsList(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "商品列表" });

        // group.MapPost("/goods", (UpdateGoods model, INScoreService rep) => rep.UpdateGoods(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "更新商品" });

        // group.MapPut("/goods/set", (SetGoods model, INScoreService rep) => rep.SetGoods(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "设置商品状态" });

        // group.MapGet("/orders", ([AsParameters] GetOrders model, INScoreService rep) => rep.GetOrders(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "订单列表" });

        // group.MapPost("/orders/send", (SendOrders model, INScoreService rep) => rep.SendOrders(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "发货" });

        // group.MapGet("/record", ([AsParameters] GetScoreRecord model, INScoreService rep) => rep.GetScoreRecord(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "积分记录" });

        // group.MapGet("/report", ([AsParameters] GetScoreReport model, INScoreService rep) => rep.GetScoreReport(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "积分报表" });

        // group.MapGet("/pushposts", (INScoreService rep) => rep.GetPushPostSettings())
        //     .WithOpenApi(operation => new(operation) { Summary = "推送职位配置" });

        // group.MapPost("/pushposts", (UpdatePushPostSettings model, INScoreService rep) => rep.UpdatePushPostSettings(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "更新/添加职位" });

        // group.MapDelete("/pushposts", ([AsParameters] DeletePushPostSettings model, INScoreService rep) => rep.DeletePushPostSettings(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "删除职位" });
    }
}