// using Config.CommonModel;
// using Staffing.Entity.Staffing;

// namespace Staffing.Model.Common.NScore;

// public class GetOrders : QueryRequest
// {
//     /// <summary>
//     /// 订单状态
//     /// </summary>
//     public NScoreOrderStatus? Status { get; set; }
// }

// public class GetOrdersResponse : QueryResponse
// {
//     /// <summary>
//     /// 订单列表
//     /// </summary>
//     public List<OrderInfo>? Rows { get; set; }
// }

// public class OrderInfo
// {
//     /// <summary>
//     /// 订单Id
//     /// </summary>
//     public string? Id { get; set; }

//     /// <summary>
//     /// 商品名称
//     /// </summary>
//     public string Name { get; set; } = null!;

//     /// <summary>
//     /// 商品描述
//     /// </summary>
//     public string? Describe { get; set; }

//     /// <summary>
//     /// 所需积分
//     /// </summary>
//     public int Score { get; set; }

//     /// <summary>
//     /// 订单状态
//     /// </summary>
//     public NScoreOrderStatus Status { get; set; }

//     /// <summary>
//     /// 订单状态文本
//     /// </summary>
//     public string? StatusName { get; set; }

//     /// <summary>
//     /// 发货时间
//     /// </summary>
//     public DateTime? DeliveryTime { get; set; }

//     /// <summary>
//     /// 快递
//     /// </summary>
//     public string Express { get; set; } = null!;

//     /// <summary>
//     /// 快递单号
//     /// </summary>
//     public string ExpressNo { get; set; } = null!;

//     /// <summary>
//     /// 商品价值
//     /// </summary>
//     public decimal Money { get; set; }

//     /// <summary>
//     /// 缩略图
//     /// </summary>
//     public string? Thumbnail { get; set; }

//     /// <summary>
//     /// 兑换时间
//     /// </summary>
//     public DateTime CreatedTime { get; set; } = DateTime.Now;
// }