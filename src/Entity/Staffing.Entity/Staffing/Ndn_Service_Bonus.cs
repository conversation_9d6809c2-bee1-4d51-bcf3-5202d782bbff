﻿// using System.ComponentModel.DataAnnotations;
// using System.ComponentModel.DataAnnotations.Schema;
// using Config.CommonModel.Ndn;

// namespace Staffing.Entity.Staffing;

// /// <summary>
// /// 数字诺亚服务奖金
// /// </summary>
// [Table("ndn_service_bonus")]
// public class Ndn_Service_Bonus
// {
//     /// <summary>
//     /// 主键
//     /// </summary>
//     [Key]
//     public string Id { get; set; } = EntityTools.SnowflakeId();

//     /// <summary>
//     /// 结算单Id
//     /// </summary>
//     public string SettlementId { get; set; } = default!;

//     /// <summary>
//     /// 顾问Id
//     /// </summary>
//     public string HrId { get; set; } = default!;

//     /// <summary>
//     /// 项目转账流水Id
//     /// </summary>
//     public string? TransferId { get; set; }

//     /// <summary>
//     /// 发放帐套
//     /// </summary>
//     public string? BookCode { get; set; }

//     /// <summary>
//     /// 发放项目
//     /// </summary>
//     public string? ProjectCode { get; set; }

//     /// <summary>
//     /// 发放项目名称
//     /// </summary>
//     public string? ProjectName { get; set; }

//     /// <summary>
//     /// 状态(弃用)
//     /// </summary>
//     [ConcurrencyCheck]
//     public NdnAuditStatus Status { get; set; } = NdnAuditStatus.未开始;

//     /// <summary>
//     /// 描述
//     /// </summary>
//     public string? Description { get; set; }

//     /// <summary>
//     /// 操作人Id
//     /// </summary>
//     public string? OperatorId { get; set; }

//     /// <summary>
//     /// 操作人姓名
//     /// </summary>
//     public string? OperatorName { get; set; }

//     /// <summary>
//     /// 更新时间
//     /// </summary>
//     public DateTime UpdatedTime { get; set; } = DateTime.Now;

//     /// <summary>
//     /// 创建时间
//     /// </summary>
//     public DateTime CreatedTime { get; set; } = DateTime.Now;


//     public Project_Settlement Project_Settlement { get; set; } = default!;
//     public Ndn_Project_Transfers Ndn_Project_Transfers { get; set; } = default!;
// }
