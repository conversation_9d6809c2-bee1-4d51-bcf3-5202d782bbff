﻿namespace Config.CommonModel.DingDingRoot;

public class SendDingDingRootMessage
{
}

/// <summary>
/// 钉钉机器人发送消息响应模型
/// </summary>
public class SendDingDingRootMessageResponse
{
    public List<string>? flowControlledStaffIdList { get; set; }

    public List<string>? invalidStaffIdList { get; set; }

    public string? processQueryKey { get; set; }
}

/// <summary>
/// 钉钉机器人发送消息请求模型
/// </summary>
public class SendDingDingRootMessageRequest
{
    /// <summary>
    /// 发送用户钉钉userid
    /// </summary>
    public string userId { get; set; } = default!;

    /// <summary>
    /// 发送参数，例子："{\"content\": \"啥时候对接钉钉授权的接口啊 \n 234234234234234\"}"
    /// </summary>
    public string? msgParam { get; set; }

    /// <summary>
    /// 消息类型，默认sampleText
    /// </summary>
    public string msgType { get; set; } = "sampleText";
}

/// <summary>
/// 投递简历钉钉机器人发送消息模型
/// </summary>
public class DingRobotMDRecruitMsgParam
{
    /// <summary>
    /// 投递时间
    /// </summary>
    public DateTime? ApplyTime { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName {  get; set; }

    /// <summary>
    /// 代招企业名称
    /// </summary>
    public string? CompanyName { get; set;}

    /// <summary>
    /// 求职者名称
    /// </summary>
    public string? SeekerName { get; set;}

    /// <summary>
    /// 求职者地区
    /// </summary>
    public string? City { get; set;}

    /// <summary>
    /// 求职者学历
    /// </summary>
    public string? Education { get; set;}

    /// <summary>
    /// 求职者性别
    /// </summary>
    public string? Sex { get; set;}

    /// <summary>
    /// 求职者年龄
    /// </summary>
    public int? Age { get; set;}

    /// <summary>
    /// 求职者头像
    /// </summary>
    public string? Avatar { get; set;}

    /// <summary>
    /// 求职者来源
    /// </summary>
    public string? Source { get; set;}

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set;}

    /// <summary>
    /// 职位类别
    /// </summary>
    public string? Category { get; set;}

    /// <summary>
    /// 项目数
    /// </summary>
    public int ProjectNum { get; set; }

    /// <summary>
    /// 岗位数
    /// </summary>
    public int PostNum { get; set; }

    /// <summary>
    /// 简历数
    /// </summary>
    public int ResumeNum { get; set; }
}

