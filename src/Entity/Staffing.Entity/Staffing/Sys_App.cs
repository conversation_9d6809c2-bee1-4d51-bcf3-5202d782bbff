﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.CommonModel.Business;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 应用表
/// </summary>
[Table("sys_app")]
public class Sys_App
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 别名
    /// </summary>
    public string Alias { get; set; } = string.Empty;

    /// <summary>
    /// 图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// Url
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// 所需权限
    /// </summary>
    public string? Powers { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public SystemAppType Type { get; set; }

    /// <summary>
    /// 概要
    /// </summary>
    public string? Abstract { get; set; }

    /// <summary>
    /// 短概要
    /// </summary>
    public string? ShortAbstract { get; set; }

    /// <summary>
    /// 应用详情
    /// </summary>
    public string? AppInfo { get; set; }

    /// <summary>
    /// Manual
    /// </summary>
    public string? Manual { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ActiveStatus Status { get; set; }

    /// <summary>
    /// 显示
    /// </summary>
    public bool Show { get; set; }

    /// <summary>
    /// 父Id
    /// </summary>
    public string? ParentId { get; set; }

    /// <summary>
    /// 层级
    /// </summary>
    public string Level { get; set; } = string.Empty;

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    // /// <summary>
    // /// 零工市场hrId
    // /// </summary>
    // public string? QuickJobHrId { get; set; }

    // /// <summary>
    // /// 零工市场数据
    // /// </summary>
    // public QuickJobData? QuickJobData { get; set; }

    /// <summary>
    /// 操作者Id
    /// </summary>
    public string AdminId { get; set; } = string.Empty;

    /// <summary>
    /// 操作者名称
    /// </summary>
    public string AdminName { get; set; } = string.Empty;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}

public enum SystemAppType
{
    模块
}