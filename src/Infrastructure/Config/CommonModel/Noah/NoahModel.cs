namespace Config.CommonModel.Noah;

// public class AccessTokenUser
// {
//     /// <summary>
//     /// app类型
//     /// </summary>
//     public TokenType AppType { get; set; }

//     /// <summary>
//     /// 登录者的昵称/姓名
//     /// </summary>
//     public string? LoginUserName { get; set; }

//     /// <summary>
//     /// 登录的账号
//     /// </summary>
//     public string LoginName { get; set; } = default!;

//     /// <summary>
//     /// 登录的账号ID
//     /// </summary>
//     public string LoginAccountNo { get; set; } = default!;

//     /// <summary>
//     /// 登录的(用户/企业)ID
//     /// </summary>
//     public string LoginPrincipalNo { get; set; } = default!;

//     public AccessTokenUserExpand Expand { get; set; } = default!;
// }

public class AccessTokenUserExpand
{
    public string InviteCode { get; set; } = default!;
}

public class GetPositionIncr
{
    public string EntId { get; set; } = default!;
    public DateTime LastTime { get; set; }
}

public class GetDeliverIncr
{
    public string EntId { get; set; } = default!;
    public DateTime LastTime { get; set; }
}

public class GetOutletsInfo
{
    /// <summary>
    /// 网点Id
    /// </summary>
    public string? ServiceHallID { get; set; }

    /// <summary>
    /// 网点名称
    /// </summary>
    public string? Name { get; set; }
    /// <summary>
    /// 致趣百川会议ID
    /// </summary>
    public string? BeschannelsMeetingId { get; set; }
    /// <summary>
    /// 所在市Name
    /// </summary>
    public string? CityName { get; set; }
}

public class GetEnterpriseAccount
{
    public string PK_EAID { get; set; } = default!;
    public string PK_EID { get; set; } = default!;
    public string UnionID { get; set; } = default!;
    public string LoginName { get; set; } = default!;
    public string AppletID { get; set; } = default!;
    public string HeadPortrait { get; set; } = default!;
    public string EnterpriseAccountName { get; set; } = default!;
    public string EnterpriseName { get; set; } = default!;
    public string? LogoUrl { get; set; }
    public string? Abbreviation { get; set; }
    public string? Address { get; set; }
    public string? ProvinceName { get; set; }
    public string? CityName { get; set; }
    public string? DistrictName { get; set; }
    public string? PositionName { get; set; }
    public string? ReceiveMailbox { get; set; }
    public int? Sex { get; set; }
}

public class GetUserAccount
{
    public string PK_UAID { get; set; } = default!;
    public string PK_UID { get; set; } = default!;
    public string LoginName { get; set; } = default!;
    public string? Name { get; set; }
    public string? HeadPortrait { get; set; }
    public int Status { get; set; }
    public int UserStatus { get; set; }
    public string? UnionID { get; set; }
    public string? AppletID { get; set; }
    public string? InterviewerUnionID { get; set; }
    public string? InterviewerAppletID { get; set; }
}

public class GetUserByToken
{
    public string? Id { get; set; }
    public string? LoginPrincipalNo { get; set; }
    public string? Mobile { get; set; }
}

public class GetNuoPinTokenInfo
{
    public string? AccessToken { get; set; }
    public int TokenExpiresTime { get; set; }
    public string? TokenType { get; set; }
    public string? RefreshToken { get; set; }
    public int RefreshTokenExpiresTime { get; set; }
    public string? Scope { get; set; }
    public string? PK_UAID { get; set; }
    public string? PK_UID { get; set; }
    public string? PK_EAID { get; set; }
    public string? PK_EID { get; set; }
    public string? Name { get; set; }
    public string? Avatar { get; set; }
}

public class GetEntDeliverCountResponse
{
    public string? EnterpriseID { get; set; }
    public int TodayDeliverCount { get; set; }
}

public class GetNPJobPositionsInfo
{
    /// <summary>
    /// 职位Id
    /// </summary>
    public string? PK_WPID { get; set; }

    /// <summary>
    /// 岗位类别ID
    /// </summary>
    public string? PositionID { get; set; }

    /// <summary>
    /// 岗位类型
    /// </summary>
    public string? PositionName { get; set; }

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? WorkPositionName { get; set; }


    /// <summary>
    /// 置顶
    /// </summary>
    public bool IsTop { get; set; }

    /// <summary>
    /// 加急
    /// </summary>
    public bool IsUrgent { get; set; }

    /// <summary>
    /// 刷新时间
    /// </summary>
    public DateTime RefreshTime { get; set; }

    /// <summary>
    /// 薪资要求(最低)
    /// </summary>
    public int MinSalary { get; set; }

    /// <summary>
    /// 薪资要求(最高)
    /// </summary>
    public int MaxSalary { get; set; }

    /// <summary>
    /// 多少薪
    /// </summary>
    public int Salary { get; set; }

    /// <summary>
    /// 薪资字符串
    /// </summary>
    public string? SalaryName { get; set; }


    /// <summary>
    /// 工作地点市
    /// </summary>
    public string? CityNo { get; set; }

    /// <summary>
    /// 工作地点市
    /// </summary>
    public string? CityName { get; set; }

    /// <summary>
    /// 学历要求（名称）
    /// </summary>
    public string? DegreeName { get; set; }

    /// <summary>
    /// 招聘人数
    /// </summary>
    public int RecruitNumber { get; set; }

    /// <summary>
    /// 工作性质名字
    /// </summary>
    public string? WorkNatureName { get; set; }


    /// <summary>
    /// 工作经验名字
    /// </summary>
    public string? WorkTimeName { get; set; }

    /// <summary>
    /// 发布日期
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 招聘截止
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 招聘过期
    /// </summary>
    public bool Expiration { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>
    /// 初筛简历
    /// </summary>
    public int ResumeScreening { get; set; }

    /// <summary>
    /// 预约面试简历
    /// </summary>
    public int ResumeInterview { get; set; }

    /// <summary>
    /// 发放offer
    /// </summary>
    public int ResumeOffer { get; set; }

    /// <summary>
    /// 发布者账号Id
    /// </summary>
    public string? EnterpriseAccountID { get; set; }

    /// <summary>
    /// 发布者名字
    /// </summary>
    public string? EnterpriseAccountName { get; set; }

    /// <summary>
    /// 工作地址
    /// </summary>
    public string? Address { get; set; }
    /// <summary>
    /// 福利
    /// </summary>
    public string? Benefit { get; set; }

    /// <summary>
    /// 投递数量
    /// </summary>
    public int DeliverCount { get; set; }
}

public class GetNPDeliverInfo
{
    /// <summary>
    /// 投递记录的ID
    /// </summary>

    public string? PK_WPDID { get; set; }

    /// <summary>
    /// 投递来源
    /// </summary>
    public string? DeliverFromText { get; set; }

    /// <summary>
    /// 投递时间
    /// </summary>
    public string? CreateTime { get; set; }
    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? WorkPositionName { get; set; }
    /// <summary>
    /// 求职者ID
    /// </summary>
    public string? UserID { get; set; }
    /// <summary>
    /// 求职者姓名
    /// </summary>
    public string? Name { get; set; }
    /// <summary>
    /// 联系方式
    /// </summary>
    public string? UserPhone { get; set; }
    /// <summary>
    /// 性别
    /// </summary>
    public string? SexText { get; set; }
    /// <summary>
    /// 年龄
    /// </summary>
    public string? Age { get; set; }
    /// <summary>
    /// 工作经验
    /// </summary>
    public string? WorkEmpirical { get; set; }
    /// <summary>
    /// 开始工作时间
    /// </summary>
    public DateTime? WorkTime { get; set; }
    /// <summary>
    /// 学历
    /// </summary>
    public string? DegreeName { get; set; }
    /// <summary>
    /// 期望薪资
    /// </summary>
    public string? Salary { get; set; }
    /// <summary>
    /// 求职期望职位
    /// </summary>
    public string? PurposeExperience { get; set; }
}

public class GetNPUserContactInfo
{
    /// <summary>
    /// 记录ID
    /// </summary>

    public string? PK_UCRID { get; set; }

    /// <summary>
    /// 操作人姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 沟通目的
    /// </summary>
    public string? Design { get; set; }
    /// <summary>
    /// 沟通结果
    /// </summary>
    public string? Result { get; set; }
    /// <summary>
    /// 沟通备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 创建记录时间
    /// </summary>
    public string? CreateTime { get; set; }
}

public class GetEntByNameResponse
{
    /// <summary>
    /// 企业 Id
    /// </summary>
    public string? PK_EID { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }
}

/// <summary>
/// 销售易提交过来的HR和企业信息
/// </summary>
public class SyncSalesEasyHRRequest
{
    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// HR手机号
    /// </summary>
    public string? HRMobile { get; set; }
    /// <summary>
    /// HR姓名
    /// </summary>
    public string? HRName { get; set; }

    /// <summary>
    /// 已分配顾问的ID
    /// </summary>
    public string? AdviserId { get; set; }
    /// <summary>
    /// 已分配顾问的手机号
    /// </summary>
    public string? AdviserMobile { get; set; }
    /// <summary>
    /// 已分配顾问的顾问姓名
    /// </summary>
    public string? AdviserName { get; set; }

    /// <summary>
    /// 企业微信二维码
    /// </summary>
    public string? EntWeChatQrCode { get; set; }
}

public class SyncSalesEasyHRInfo
{
    /// <summary>
    /// 企业Id
    /// </summary>
    public string? PK_EID { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
}