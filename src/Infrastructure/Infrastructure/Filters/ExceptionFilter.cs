﻿using System.Net;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;

namespace Infrastructure.Filters;

/// <summary>
/// ExceptionFilter
/// </summary>
public class ExceptionFilter : IExceptionFilter
{
    private LogManager _logger;
    private readonly IHostEnvironment _hostEnvironment;
    private RequestContext _request;

    /// <summary>
    /// 
    /// </summary>
    public ExceptionFilter(LogManager logger, IHostEnvironment hostingEnvironment, RequestContext request)
    {
        _logger = logger;
        _hostEnvironment = hostingEnvironment;
        _request = request;
    }

    /// <summary>
    /// OnException
    /// </summary>
    /// <param name="context"></param>
    public void OnException(ExceptionContext context)
    {
        var url = context.HttpContext.Request.Path;
        var message = string.Empty;

        HttpStatusCode status = HttpStatusCode.InternalServerError;

        string errorCode = string.Empty;

        var exceptionType = context.Exception.GetType();

        if (exceptionType == typeof(NotFoundException))
        {
            status = HttpStatusCode.NotFound;
            errorCode = ((CustomException)context.Exception).ErrorCode;
        }
        else if (exceptionType == typeof(BadRequestException))
        {
            status = HttpStatusCode.BadRequest;
            errorCode = ((CustomException)context.Exception).ErrorCode;
        }
        else if (exceptionType == typeof(ForbiddenException))
        {
            status = HttpStatusCode.Forbidden;
            errorCode = ((CustomException)context.Exception).ErrorCode;
        }
        else if (exceptionType == typeof(UnauthorizedException))
        {
            status = HttpStatusCode.Unauthorized;
            errorCode = ((CustomException)context.Exception).ErrorCode;
        }
        else if (exceptionType == typeof(NotAcceptableException))
        {
            status = HttpStatusCode.NotAcceptable;
            errorCode = ((CustomException)context.Exception).ErrorCode;
        }
        var para = RequestParams.GetParams(context.HttpContext);
        var method = context.HttpContext!.Request.Method.ToUpper();

        var detailMessage = string.Empty;
        if (context.Exception.InnerException != null)
        {
            detailMessage = ". detail: " + (context.Exception.InnerException?.Message ?? string.Empty) + ". " + (context.Exception.InnerException?.InnerException?.Message ?? string.Empty);
        }
        if (status == HttpStatusCode.InternalServerError && exceptionType != typeof(InternalServerException))
        {
            _logger.Error(context.HttpContext.Request.Path, context.Exception.Message + detailMessage, para);
        }

        var showErr = context.Exception.Message;
        var realErr = context.Exception.Message ?? string.Empty;

        if (exceptionType == typeof(DbUpdateConcurrencyException))
        {
            showErr = "操作太快了，休息一下再试";
        }

        if (status == HttpStatusCode.InternalServerError && _hostEnvironment.IsProduction())
        {
            showErr = "出错啦，等会再试试看吧";
        }

        var userAgent = _request.UserAgent;
        var mobile = _request.Account ?? string.Empty;

        context.Result = new Microsoft.AspNetCore.Mvc.ObjectResult(context.HttpContext.Response)
        {
            StatusCode = (int)status,
            Value = new ErrorType { msg = showErr, code = errorCode }
        };
    }
}