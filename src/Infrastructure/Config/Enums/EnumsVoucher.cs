﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Config.Enums
{
    /// <summary>
    /// 凭证类型
    /// </summary>
    public enum VoucherType
    {
        订单转账
    }

    /// <summary>
    /// 凭证状态
    /// </summary>
    public enum VoucherStatus
    {
        待生成, 生效中, 凭证生成失败 = 99
    }

    /// <summary>
    /// 凭证申请开票状态
    /// </summary>
    public enum VoucherInvoiceStatus
    {
        尚未开票, 已申请开票, 申请开票失败, 开票成功, 开票失败,
    }
}
