﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 数字诺亚项目表
/// </summary>
[Table("ndn_project")]
public class Ndn_Project
{
    /// <summary>
    /// 主键
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 项目编码
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ProjectName { get; set; } = default!;

    /// <summary>
    /// 主体账套
    /// </summary>
    public string BookCode { get; set; } = default!;

    /// <summary>
    /// 主体账套名称
    /// </summary>
    public string BookName { get; set; } = default!;

    /// <summary>
    /// 客户账套
    /// </summary>
    public string? ClientBookCode { get; set; }

    /// <summary>
    /// 客户账套名称
    /// </summary>
    public string? ClientBookName { get; set; }

    /// <summary>
    /// 项目类型
    /// </summary>
    public NdnProjType Type { get; set; }

    /// <summary>
    /// 项目状态
    /// </summary>
    public NdnProjStatus Status { get; set; }

    /// <summary>
    /// 合同编号
    /// </summary>
    public string? ContractNo { get; set; }

    /// <summary>
    /// 合同地址
    /// </summary>
    public string? ContractUrl { get; set; }

    /// <summary>
    /// 操作人ID
    /// </summary>
    public string? OperatorId { get; set; }

    /// <summary>
    /// 操作人姓名
    /// </summary>
    public string? OperatorName { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}

public enum NdnProjType
{
    诺聘主体, 其他主体, 数字诺亚 = 9
}

public enum NdnProjStatus
{
    待办, 完成
}