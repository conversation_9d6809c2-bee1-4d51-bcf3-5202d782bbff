using System.Text.Json.Serialization;
using Config.Enums;

namespace Config.CommonModel.Tasks;

public class GetMyTasks : QueryRequest
{

}

public class GetMyTasksResponse : QueryResponse
{
    public List<MyTaskInfo> Rows { get; set; } = new List<MyTaskInfo>();
}

public class MyTaskInfo
{
    /// <summary>
    /// 主键id
    /// </summary>
    public string? TaskId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; } = string.Empty;

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; } = string.Empty;

    /// <summary>
    /// 内容
    /// </summary>
    [JsonIgnore]
    public string? Content { get; set; }

    /// <summary>
    /// 任务类型
    /// </summary>
    public TaskHandlingType? Type { get; set; }

    /// <summary>
    /// 任务类型
    /// </summary>
    public string? TypeName { get; set; }

    /// <summary>
    /// 任务状态
    /// </summary>
    public TaskHandlingStatus? Status { get; set; }

    /// <summary>
    /// 任务状态
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 导入方式
    /// </summary>
    public ProjectMemberImportEntryType? Mode { get; set; }

    /// <summary>
    /// 导入方式
    /// </summary>
    public string? ModeName { get; set; }

    // /// <summary>
    // /// 结果
    // /// </summary>
    // public TaskHandlingResult? Result { get; set; }

    /// <summary>
    /// 结果文本
    /// </summary>
    public string? ResultText { get; set; }

    /// <summary>
    /// 总数
    /// </summary>
    public int Total { get; set; }

    /// <summary>
    /// 成功数
    /// </summary>
    public int Successful { get; set; }

    /// <summary>
    /// 失败数
    /// </summary>
    public int Failed { get; set; }

    /// <summary>
    /// 重复数
    /// </summary>
    public int Duplicate { get; set; }

    /// <summary>
    /// 任务结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    //任务创建时间
    public DateTime? CreatedTime { get; set; }
}

public class GetMyTaskDetails : QueryRequest
{
    [JsonIgnore]
    public string? TaskId { get; set; }

    /// <summary>
    /// 结果
    /// </summary>
    public TaskHandlingResult? Result { get; set; }
}

public class GetMyTaskDetailsResponse<T> : QueryResponse
{
    public List<MyTaskDetailInfo<T>> Rows { get; set; } = new List<MyTaskDetailInfo<T>>();
}

public class MyTaskDetailInfo<T>
{
    public string? Id { get; set; }

    /// <summary>
    /// 任务Id
    /// </summary>
    public string? TaskId { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    [JsonIgnore]
    public string? ContentStr { get; set; }

    /// <summary>
    /// 内容对象
    /// </summary>
    public T? Content { get; set; }

    /// <summary>
    /// 结果
    /// </summary>
    public TaskHandlingResult Result { get; set; }

    /// <summary>
    /// 结果文本
    /// </summary>
    public string? ResultText { get; set; }

    /// <summary>
    /// 状态分组（用来查询比如重复的）
    /// </summary>
    public TaskHandlingGroupStatus GroupStatus { get; set; } = TaskHandlingGroupStatus.默认;

    public DateTime CreatedTime { get; set; } = DateTime.Now;
}