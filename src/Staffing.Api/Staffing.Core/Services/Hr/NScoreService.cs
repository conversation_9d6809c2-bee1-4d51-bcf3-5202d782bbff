﻿// using Config.Enums;
// using Infrastructure.Common;
// using Infrastructure.Extend;
// using LinqKit;
// using Microsoft.Extensions.DependencyInjection;
// using Microsoft.Extensions.Hosting;
// using Microsoft.Extensions.Options;
// using Staffing.Entity;
// using Staffing.Core.Interfaces.Hr;
// using Config;
// using Microsoft.EntityFrameworkCore;
// using Infrastructure.CommonService;
// using Staffing.Model.Common.NScore;
// using Staffing.Entity.Staffing;
// using Infrastructure.Exceptions;
// using Config.CommonModel;
// using Config.CommonModel.Business;

// namespace Staffing.Core.Services.Hr;

// [Service(ServiceLifetime.Transient)]
// public class NScoreService : INScoreService
// {
//     private readonly StaffingContext _context;
//     private readonly RequestContext _user;
//     private readonly ConfigManager _config;
//     private readonly CacheHelper _cacheHelper;
//     private readonly IHostEnvironment _hostingEnvironment;
//     private readonly LogManager _log;
//     private readonly CommonProjectService _commonProjectService;
//     private readonly CommonDicService _commonDicService;
//     private readonly CommonNScoreService _commonNScoreService;
//     private readonly IDbContextFactory<StaffingContext> _contextFactory;
//     public NScoreService(StaffingContext context, RequestContext user,
//         IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
//         CacheHelper cacheHelper, LogManager log,
//         IDbContextFactory<StaffingContext> contextFactory, CommonDicService commonDicService,
//         CommonProjectService commonProjectService, CommonNScoreService commonNScoreService)
//     {
//         _user = user;
//         _context = context;
//         _config = config.Value;
//         _hostingEnvironment = hostingEnvironment;
//         _cacheHelper = cacheHelper;
//         _log = log;
//         _contextFactory = contextFactory;
//         _commonDicService = commonDicService;
//         _commonProjectService = commonProjectService;
//         _commonNScoreService = commonNScoreService;
//     }

//     public GetScoreResponse GetScore(GetScore model)
//     {
//         var result = _context.User_NScore.Where(x => x.UserId == _user.Id)
//         .Select(s => new GetScoreResponse
//         {
//             Score = s.HrScore,
//             ScoreTotal = s.HrScoreTotal
//         }).FirstOrDefault() ?? new GetScoreResponse();

//         result.ScoreUsed = result.ScoreTotal - result.Score;
//         result.ScoreUsed = result.ScoreUsed < 0 ? 0 : result.ScoreUsed;

//         result.Address = MyRedis.Client.HGet<KdAddressInfo?>(RedisKey.NScore.HrScoreAddress, _user.Id);

//         return result;
//     }

//     public async Task<AddScoreResponse> AddScore(AddScore model)
//     {
//         if (!model.ScoreType.HasValue)
//             throw new BadRequestException("缺少ScoreType");

//         var result = new AddScoreResponse { ScoreType = model.ScoreType };

//         var scoreConfig = Tools.GetNScoreCfg(SeekerOrHr.Hr);

//         //区分注册还是登录
//         if (model.ScoreType == NScoreType.每日登录)
//         {
//             var usr = _context.User_Hr.Where(x => x.UserId == _user.Id)
//             .Select(s => new
//             {
//                 s.User.Mobile,
//                 s.CreatedTime,
//                 s.Status
//             }).First();

//             if (usr.Status != UserStatus.Active)
//                 return result;

//             if (usr.CreatedTime >= Constants.NScore.NewSeekerDate && MyRedis.Client.HSetNx($"{RedisKey.NScore.Key}b_{RedisKey.NScore.Register}", _user.Id, 0))
//             {
//                 //注册用户
//                 model.ScoreType = NScoreType.注册;

//                 MyRedis.Client.SetNx($"{RedisKey.NScore.Key}{RedisKey.NScore.DailyLogin}b_{_user.Id}:{DateTime.Today.ToYYYY_MM_DD()}", 0, TimeSpan.FromDays(1));
//             }
//             else
//             {
//                 //登录用户
//                 var go = MyRedis.Client.SetNx($"{RedisKey.NScore.Key}{RedisKey.NScore.DailyLogin}b_{_user.Id}:{DateTime.Today.ToYYYY_MM_DD()}", 0, TimeSpan.FromDays(1));
//                 if (!go)
//                     return result;
//             }
//         }
//         else if (model.ScoreType == NScoreType.分享)
//         {
//             var tms = MyRedis.Client.HIncrBy($"{RedisKey.NScore.Key}{RedisKey.NScore.DailyShare}", $"b_{_user.Id}:{DateTime.Today.ToYYYY_MM_DD()}", 1);
//             if (tms > 3)
//                 return result;
//             else
//                 result.TimesToday = (int)tms;
//         }

//         result.ScoreType = model.ScoreType;

//         var increment = model.ScoreType switch
//         {
//             NScoreType.分享 => scoreConfig.First(x => x.Type == NScoreType.分享).Score,
//             NScoreType.每日登录 => scoreConfig.First(x => x.Type == NScoreType.每日登录).Score,
//             NScoreType.注册 => scoreConfig.First(x => x.Type == NScoreType.注册).Score,
//             _ => 0
//         };

//         if (increment < 0)
//             throw new BadRequestException("不支持的类型");

//         var content = model.ScoreType switch
//         {
//             NScoreType.分享 => $"您分享好友（{result.TimesToday}/3）",
//             NScoreType.每日登录 => "您成功登录",
//             NScoreType.注册 => "您成功注册",
//             _ => string.Empty
//         };

//         var scoreModel = new NScoreModel
//         {
//             Content = content,
//             EventTime = DateTime.Now,
//             Increment = increment,
//             Type = model.ScoreType.Value,
//             UserId = _user.Id,
//             UserType = SeekerOrHr.Hr
//         };

//         result.NScore = await _commonNScoreService.AddNScore(scoreModel);

//         return result;
//     }

//     public GetScoreNotifyResponse GetScoreNotify(GetScoreNotify model)
//     {
//         var result = new GetScoreNotifyResponse();

//         var now = DateTime.Now;
//         var lastNotifyTime = MyRedis.Client.HGet<DateTime?>($"{RedisKey.NScore.Key}b_{RedisKey.NScore.LastNotifyTime}", _user.Id);
//         lastNotifyTime = lastNotifyTime ?? Constants.DefaultTime;

//         var scoreTypes = new List<NScoreType> { NScoreType.发布职位, NScoreType.协同项目, NScoreType.首次入职, NScoreType.分享好友入职, NScoreType.分享好友报名, NScoreType.分享好友注册, NScoreType.分享好友登录 };
//         var day7 = DateTime.Now.AddDays(-3);

//         var score = _context.User_NScore_Record.Where(x => x.UserId == _user.Id && x.UserType == SeekerOrHr.Hr
//         && x.CreatedTime >= lastNotifyTime && x.CreatedTime <= now && scoreTypes.Contains(x.Type))
//         .Select(s => new
//         {
//             s.Increment,
//             s.Type
//         }).ToList();

//         MyRedis.Client.HSet($"{RedisKey.NScore.Key}b_{RedisKey.NScore.LastNotifyTime}", _user.Id, now);

//         result.NScore = score.Sum(s => s.Increment);

//         if (score.Count == 1)
//             result.ScoreType = score.First().Type;

//         return result;
//     }

//     public EmptyResponse UpdateKdAddress(UpdateKdAddress model)
//     {
//         if (string.IsNullOrWhiteSpace(model.User))
//             throw new BadRequestException("缺少姓名");

//         if (string.IsNullOrWhiteSpace(model.Mobile))
//             throw new BadRequestException("缺少电话");

//         if (string.IsNullOrWhiteSpace(model.Province))
//             throw new BadRequestException("缺少省");

//         if (string.IsNullOrWhiteSpace(model.City))
//             throw new BadRequestException("缺少市");

//         if (string.IsNullOrWhiteSpace(model.County))
//             throw new BadRequestException("缺少区");

//         if (string.IsNullOrWhiteSpace(model.Address))
//             throw new BadRequestException("缺少详细地址");

//         var addressInfo = Tools.ModelConvert<KdAddressInfo>(model);
//         MyRedis.Client.HSet(RedisKey.NScore.HrScoreAddress, _user.Id, addressInfo);

//         return new EmptyResponse();
//     }

//     public GetGoodsListResponse GetGoodsList(GetGoodsList model)
//     {
//         if (!model.Type.HasValue)
//             throw new BadRequestException("缺少type");

//         var result = new GetGoodsListResponse();

//         result.Rows = _context.User_Nscore_Goods.Where(x => x.UserType == SeekerOrHr.Hr
//         && x.Type == model.Type && x.Active && x.Stock > 0)
//         .OrderBy(x => x.Score)
//         .Skip((model.PageIndex - 1) * model.PageSize)
//         .Take(model.PageSize)
//         .Select(s => new GoodsInfo
//         {
//             Id = s.Id,
//             Money = s.Money,
//             Name = s.Name,
//             Describe = s.Describe,
//             Score = s.Score,
//             Thumbnail = s.Content.Thumbnail,
//             Type = s.Type
//         }).ToList();

//         if (result.Rows.Count >= model.PageSize)
//             result.IsLast = false;

//         return result;
//     }

//     public async Task<BuyGoodsResponse> BuyGoods(BuyGoods model)
//     {
//         var result = new BuyGoodsResponse();

//         //锁商品
//         var lockerKey3 = $"{RedisKey.NScore.GoodsLocker}{model.GoodsId}";
//         using var locker3 = await MyRedis.Lock(lockerKey3, 15);

//         if (locker3 == null)
//             throw new Exception("当前购买人数太多，稍后再试");

//         var goods = _context.User_Nscore_Goods.FirstOrDefault(x => x.UserType == SeekerOrHr.Hr && x.Id == model.GoodsId);

//         if (goods == null)
//             throw new BadRequestException("商品不存在");

//         if (goods.Stock < 1)
//             throw new BadRequestException("商品已售罄");

//         if (goods.Score <= 0)
//             throw new Exception("商品价格异常");

//         KdAddressInfo? address = null;
//         if (goods.Type != NScoreGoodsType.现金)
//         {
//             address = MyRedis.Client.HGet<KdAddressInfo?>(RedisKey.NScore.HrScoreAddress, _user.Id);

//             if (address == null)
//                 throw new BadRequestException("尚未填写地址");
//         }

//         //锁用户积分
//         var lockerKey = $"{RedisKey.NScore.Key}{_user.Id}";
//         using var locker = await MyRedis.Lock(lockerKey, 15);

//         if (locker == null)
//             throw new Exception("用户购物获取积分锁失败");

//         //锁用户钱包
//         var lockerKey2 = $"{RedisKey.Balance.LockKey}{_user.Id}";
//         using var locker2 = await MyRedis.Lock(lockerKey2, 15);

//         if (locker2 == null)
//             throw new Exception("用户购物获取钱包锁失败");

//         var nscore = _context.User_NScore.FirstOrDefault(x => x.UserId == _user.Id);

//         if (nscore == null || nscore.HrScore < goods.Score)
//             throw new BadRequestException("积分不足");

//         nscore!.HrScore -= goods.Score;

//         goods.Stock--;

//         var scoreRecord = new User_NScore_Record
//         {
//             Amount = nscore.HrScore,
//             Increment = goods.Score * -1,
//             UserType = SeekerOrHr.Hr,
//             Type = NScoreType.兑换商品,
//             Content = $"兑换 {goods.Name}",
//             Data = goods.Id,
//             EventTime = DateTime.Now,
//             UserId = _user.Id
//         };
//         _context.Add(scoreRecord);

//         if (goods.Type == NScoreGoodsType.实物)
//         {
//             nscore.HrPrize += 1;

//             //添加订单
//             var order = new User_Nscore_Order
//             {
//                 GoodsId = goods.Id,
//                 UserId = _user.Id,
//                 Status = NScoreOrderStatus.待发货,
//                 Name = goods.Name,
//                 Score = goods.Score,
//                 Money = goods.Money,
//                 Address = address!,
//             };
//             _context.Add(order);
//         }
//         else if (goods.Type == NScoreGoodsType.现金)
//         {
//             nscore.HrMoney += goods.Money;

//             //添加钱包
//             var balance = _context.User_Balance.First(x => x.UserId == _user.Id);
//             balance.HrBalance += goods.Money;
//             balance.HrBalanceFull += goods.Money;

//             var balanceRecord = new User_Balance_Record
//             {
//                 Amount = balance.HrBalance,
//                 Increment = goods.Money,
//                 UserType = SeekerOrHr.Hr,
//                 Type = BalanceRecordType.兑换红包,
//                 Content = "兑换-红包",
//                 Data = goods.Id,
//                 UserId = _user.Id
//             };
//             _context.Add(balanceRecord);
//         }
//         else
//             throw new BadRequestException("不支持的商品类型");


//         _context.SaveChanges();

//         if (goods.Type == NScoreGoodsType.现金)
//             MyRedis.Client.HIncrByFloat(RedisKey.Balance.TodayBalance, $"{DateTime.Now.ToYYYY_MM_DD()}_{_user.Id}", goods.Money);

//         return result;
//     }

//     public GetOrdersResponse GetOrders(GetOrders model)
//     {
//         var result = new GetOrdersResponse();

//         var predicate = PredicateBuilder.New<User_Nscore_Order>(x => x.UserId == _user.Id && x.User_Nscore_Goods.UserType == SeekerOrHr.Hr);

//         if (model.Status.HasValue)
//             predicate = predicate.And(x => x.Status == model.Status);

//         result.Rows = _context.User_Nscore_Order.Where(predicate)
//         .OrderByDescending(x => x.CreatedTime)
//         .Skip((model.PageIndex - 1) * model.PageSize)
//         .Take(model.PageSize)
//         .Select(s => new OrderInfo
//         {
//             Id = s.Id,
//             Money = s.Money,
//             Name = s.Name,
//             Describe = s.User_Nscore_Goods.Describe,
//             Score = s.Score,
//             Status = s.Status,
//             StatusName = s.Status.GetDescription(),
//             Thumbnail = s.User_Nscore_Goods.Content.Thumbnail,
//             CreatedTime = s.CreatedTime,
//             DeliveryTime = s.DeliveryTime,
//             Express = s.Express,
//             ExpressNo = s.ExpressNo
//         }).ToList();

//         if (result.Rows.Count >= model.PageSize)
//             result.IsLast = false;

//         return result;
//     }

//     public GetScoreRecordResponse GetScoreRecord(GetScoreRecord model)
//     {
//         var result = new GetScoreRecordResponse();

//         var predicate = PredicateBuilder.New<User_NScore_Record>(x => x.UserId == _user.Id && x.UserType == SeekerOrHr.Hr);

//         if (model.Type == 0)
//             predicate = predicate.And(x => x.Increment > 0);
//         else if (model.Type > 0)
//             predicate = predicate.And(x => x.Increment < 0);

//         result.Rows = _context.User_NScore_Record.Where(predicate)
//         .OrderByDescending(x => x.CreatedTime)
//         .Skip((model.PageIndex - 1) * model.PageSize)
//         .Take(model.PageSize)
//         .Select(s => new ScoreRecordInfo
//         {
//             Amount = s.Amount,
//             Content = s.Content,
//             EventTime = s.EventTime,
//             Increment = s.Increment,
//             Type = s.Type,
//             UserType = s.UserType
//         }).ToList();

//         if (result.Rows.Count >= model.PageSize)
//             result.IsLast = false;

//         return result;
//     }
// }