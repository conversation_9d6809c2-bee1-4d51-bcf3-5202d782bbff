﻿using Config.CommonModel;
using Config.Enums;
using System.Text.Json.Serialization;

namespace Staffing.Model.Interviewer
{
    public class Interview
    {
    }

    #region 面试官个人信息相关模型
    /// <summary>
    /// 面试官基础信息返回模型
    /// </summary>
    public class InterviewerInfoResponse
    {
        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 手机号
        /// </summary>
        public string Phone { get; set; } = string.Empty;

        /// <summary>
        /// 公司名称
        /// </summary>
        public string InterviewerEnt { get; set; } = string.Empty;

        /// <summary>
        /// 是否关注公众号
        /// </summary>
        public bool WeChatH5Subscribe { get; set; }
    }

    /// <summary>
    /// 面试官基础信息修改接收模型
    /// </summary>
    public class EditInterviewerInfoRequest
    {
        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 公司名称
        /// </summary>
        public string InterviewerEnt { get; set; } = string.Empty;
    }
    #endregion

    #region 待办相关模型
    /// <summary>
    /// 待办接收模型
    /// </summary>
    public class TodoListRequest : QueryRequest
    {
        /// <summary>
        /// 0待筛选 1待面试 2待反馈
        /// </summary>
        public int Type { get; set; }
    }

    /// <summary>
    /// 待办返回集合
    /// </summary>
    public class TodoListResponse : QueryResponse
    {
        public List<TodoModel> Rows { get; set; } = default!;
    }

    /// <summary>
    /// 待办模型
    /// </summary>
    public class TodoModel
    {
        /// <summary>
        /// 待办id
        /// </summary>
        public string? TodoId { get; set; }

        /// <summary>
        /// 用户id
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 头像
        /// </summary>
        public string HeadPortrait { get; set; } = string.Empty;

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 性别解释
        /// </summary>
        public string? SexName { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }

        /// <summary>
        /// 职业
        /// </summary>
        public string Occupation { get; set; } = string.Empty;

        /// <summary>
        /// 学历
        /// </summary>
        public string Education { get; set; } = string.Empty;

        /// <summary>
        /// 应聘职位
        /// </summary>
        public string JobApplication { get; set; } = string.Empty;

        /// <summary>
        /// 待办类型
        /// </summary>
        public RecruitInterviewerTodoType InterviewerTodoType { get; set; }

        /// <summary>
        /// 待办类型说明
        /// </summary>
        public string InterviewerTodoTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatTime { get; set; }

        /// <summary>
        /// 项目id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; } = string.Empty;

        /// <summary>
        /// 项目经理
        /// </summary>
        public string ProjectManager { get; set; } = string.Empty;

        /// <summary>
        /// 项目经历头像
        /// </summary>
        public string ProjectHeadPortrait { get; set; } = string.Empty;

        [JsonIgnore]
        public string? RelationId { get; set; }
    }
    #endregion

    #region 筛简历相关模型
    /// <summary>
    /// 筛选接收模型
    /// </summary>
    public class ScreenListRequest : QueryRequest
    {
        /// <summary>
        /// 用户姓名
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 筛选状态
        /// </summary>
        public RecruitInterviewerScreenStatus Status { get; set; }

        /// <summary>
        /// 项目id
        /// </summary>
        public string? ProjectId { get; set; }
    }

    /// <summary>
    /// 筛选返回集合
    /// </summary>
    public class ScreenListResponse : QueryResponse
    {
        public List<ScreenModel> Rows { get; set; } = default!;
    }

    /// <summary>
    /// 筛选模型
    /// </summary>
    public class ScreenModel
    {
        /// <summary>
        /// 待办id
        /// </summary>
        public string? TodoId { get; set; }

        /// <summary>
        /// 用户id
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 头像
        /// </summary>
        public string HeadPortrait { get; set; } = string.Empty;

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 性别解释
        /// </summary>
        public string? SexName { get; set; }

        /// <summary>
        /// 项目经历头像
        /// </summary>
        public string ProjectHeadPortrait { get; set; } = string.Empty;

        /// <summary>
        /// 生日
        /// </summary>
        public DateOnly? Birthday { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }

        /// <summary>
        /// 职业
        /// </summary>
        public OccupationType? Occupation { get; set; }

        /// <summary>
        /// 职业解释
        /// </summary>
        public string? OccupationName { get; set; }

        /// <summary>
        /// 学历
        /// </summary>
        public EducationType? Education { get; set; }

        /// <summary>
        /// 学历解释
        /// </summary>
        public string? EducationName { get; set; }

        /// <summary>
        /// 应聘职位
        /// </summary>
        public string JobApplication { get; set; } = string.Empty;

        /// <summary>
        /// 筛选状态
        /// </summary>
        public RecruitInterviewerScreenStatus InterviewerScreenStatus { get; set; }

        /// <summary>
        /// 筛选状态说明
        /// </summary>
        public string InterviewerScreenStatusName { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatTime { get; set; }

        /// <summary>
        /// 项目id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; } = string.Empty;

        /// <summary>
        /// 项目经理
        /// </summary>
        public string ProjectManager { get; set; } = string.Empty;
    }
    #endregion

    #region 面试相关模型
    /// <summary>
    /// 面试接收模型
    /// </summary>
    public class InterviewListRequest : QueryRequest
    {
        /// <summary>
        /// 用户姓名
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 面试结果
        /// </summary>
        public RecruitInterviewOutcome Outcome { get; set; }

        /// <summary>
        /// 项目id
        /// </summary>
        public string? ProjectId { get; set; }
    }

    /// <summary>
    /// 面试返回集合
    /// </summary>
    public class InterviewListResponse : QueryResponse
    {
        public List<InterviewModel> Rows { get; set; } = default!;
    }

    public class InterviewModel
    {
        /// <summary>
        /// 待办id
        /// </summary>
        public string? TodoId { get; set; }

        /// <summary>
        /// 用户id
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 头像
        /// </summary>
        public string HeadPortrait { get; set; } = string.Empty;

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 性别解释
        /// </summary>
        public string? SexName { get; set; }

        /// <summary>
        /// 生日
        /// </summary>
        public DateOnly? Birthday { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }

        /// <summary>
        /// 职业
        /// </summary>
        public OccupationType? Occupation { get; set; }

        /// <summary>
        /// 职业解释
        /// </summary>
        public string? OccupationName { get; set; }

        /// <summary>
        /// 学历
        /// </summary>
        public EducationType? Education { get; set; }

        /// <summary>
        /// 学历解释
        /// </summary>
        public string? EducationName { get; set; }

        /// <summary>
        /// 应聘职位
        /// </summary>
        public string JobApplication { get; set; } = string.Empty;

        /// <summary>
        /// 面试结果
        /// </summary>
        public RecruitInterviewOutcome Outcome { get; set; }

        /// <summary>
        /// 面试结果解释
        /// </summary>
        public string OutcomeName { get; set; } = string.Empty;

        /// <summary>
        /// 面试方式
        /// </summary>
        public RecruitInterviewForms Forms { set; get; }

        /// <summary>
        /// 面试方式解释
        /// </summary>
        public string FormsName { get; set; } = string.Empty;

        /// <summary>
        /// 面试过程
        /// </summary>
        public RecruitInterviewProcess Process { set; get; }

        /// <summary>
        /// 面试过程解释
        /// </summary>
        public string ProcessName { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatTime { get; set; }

        /// <summary>
        /// 项目id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; } = string.Empty;

        /// <summary>
        /// 项目经理
        /// </summary>
        public string ProjectManager { get; set; } = string.Empty;

        /// <summary>
        /// 项目经理头像
        /// </summary>
        public string ProjectHeadPortrait { get; set; } = string.Empty;

        /// <summary>
        /// 面试时间
        /// </summary>
        public DateTime InterviewTime { get; set; }
    }
    #endregion

    #region 项目合作相关模型
    public class ProjectCooperationList : QueryRequest
    {

    }

    /// <summary>
    /// 项目合作返回模型
    /// </summary>
    public class ProjectCooperationListResponse : QueryResponse
    {
        public List<ProjectCooperationModel> Rows { get; set; } = default!;
    }

    /// <summary>
    /// 项目合作
    /// </summary>
    public class ProjectCooperationModel
    {

        /// <summary>
        /// 项目id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目状态
        /// </summary>
        public ProjectStatus ProjectStatus { get; set; }

        /// <summary>
        /// 项目状态说明
        /// </summary>
        public string? ProjectStatusName { get; set; }

        /// <summary>
        /// 待筛选人数
        /// </summary>
        public int WaitScreen { get; set; } = 0;

        /// <summary>
        /// 待面试人数
        /// </summary>
        public int WaitInterview { get; set; } = 0;

        /// <summary>
        /// 已处理人数
        /// </summary>
        public int ProcessedPeople { get; set; } = 0;

        /// <summary>
        /// 项目经理名称
        /// </summary>
        public string? ProjectManagerName { get; set; }

        /// <summary>
        /// 项目经理手机号
        /// </summary>
        public string? ProjectManagerPhone { get; set; }

        /// <summary>
        /// 项目经理头像
        /// </summary>
        public string? ProjectManagerImg { get; set; }
    }
    #endregion

    #region 修改面试状态相关模型
    /// <summary>
    /// 修改面试状态接收模型
    /// </summary>
    public class EditInterviewOutcomeRequest
    {
        /// <summary>
        /// 待办id
        /// </summary>
        public string? TodoId { get; set; }

        /// <summary>
        /// 面试结果
        /// </summary>
        public RecruitInterviewOutcome Outcome { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; } = string.Empty;
    }
    #endregion

    #region 修改筛简历状态相关模型
    /// <summary>
    /// 修改筛简历状态接收模型
    /// </summary>
    public class EditScreenStatusRequest
    {
        /// <summary>
        /// 待办id
        /// </summary>
        public string? TodoId { get; set; }

        /// <summary>
        /// 筛简历状态
        /// </summary>
        public RecruitInterviewerScreenStatus Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; } = string.Empty;
    }
    #endregion

    #region 获取用户基本信息相关模型
    /// <summary>
    /// 获取用户基本信息返回模型
    /// </summary>
    public class UserInformationResponse
    {
        /// <summary>
        /// 用户id
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 用户姓名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 头像
        /// </summary>
        public string? HeadPortrait { get; set; }

        /// <summary>
        /// 用户地址
        /// </summary>
        public string? UserAddress { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// 最近活跃（天）
        /// </summary>
        public int? RecentlyActive { get; set; }

        /// <summary>
        /// 最近浏览职位个数
        /// </summary>
        public int? BrowsePosition { get; set; }

        /// <summary>
        /// 被吸引的企业个数
        /// </summary>
        public int? Attracted { get; set; }

        /// <summary>
        /// 原始简历名称
        /// </summary>
        public string? OriginalName { get; set; }

        /// <summary>
        /// 原始简历地址
        /// </summary>
        public string? OriginalUrl { get; set; }

        /// <summary>
        /// 用户基本信息
        /// </summary>
        public UserInfo? UserInfo { get; set; }

        /// <summary>
        /// 用户教育信息
        /// </summary>
        public UserEducation? UserEducation { get; set; }

        /// <summary>
        /// 用户自我评价
        /// </summary>
        public UserSelfEvaluate? UserSelfEvaluate { get; set; }

        /// <summary>
        /// 用户标签
        /// </summary>
        public UserLabel? UserLabel { get; set; }

        /// <summary>
        /// 面试结果（通过、淘汰等）
        /// </summary>
        public RecruitInterviewOutcome? RecruitInterviewOutcome { get; set; }

        /// <summary>
        /// 面试结果解释
        /// </summary>
        public string? RecruitInterviewOutcomeName { get; set; }

        /// <summary>
        /// 面试过程（初试、复试、终试）
        /// </summary>
        public RecruitInterviewProcess? RecruitInterviewProcess { get; set; }

        /// <summary>
        /// 面试过程解释
        /// </summary>
        public string? RecruitInterviewProcessName { get; set; }

        /// <summary>
        /// 面试反馈
        /// </summary>
        public string? InterviewRemarks { get; set; }

        /// <summary>
        /// 面试官筛选状态
        /// </summary>
        public RecruitInterviewerScreenStatus? RecruitInterviewerScreenStatus { get; set; }

        /// <summary>
        /// 面试官筛选状态解释
        /// </summary>
        public string? RecruitInterviewerScreenStatusName { get; set; }

        /// <summary>
        /// 面试官筛选反馈
        /// </summary>
        public string? InterviewerScreenRemarks { get; set; }
    }

    /// <summary>
    /// 用户基本信息
    /// </summary>
    public class UserInfo
    {
        /// <summary>
        /// 生日
        /// </summary>
        public DateOnly? Birthday { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 性别解释
        /// </summary>
        public string? SexName { get; set; }

        /// <summary>
        /// 身份
        /// </summary>
        public OccupationType? Occupation { get; set; }

        /// <summary>
        /// 身份解释
        /// </summary>
        public string? OccupationName { get; set; }
    }

    /// <summary>
    /// 用户教育信息
    /// </summary>
    public class UserEducation
    {
        /// <summary>
        /// 学校名称
        /// </summary>
        public string? SchoolName { get; set; } = string.Empty;

        /// <summary>
        /// 毕业时间
        /// </summary>
        public DateOnly? EntranceTime { get; set; }

        /// <summary>
        /// 专业
        /// </summary>
        public string? Major { get; set; } = string.Empty;

        /// <summary>
        /// 学历
        /// </summary>
        public EducationType? Education { get; set; }

        /// <summary>
        /// 学历解释
        /// </summary>
        public string? EducationName { get; set; }
    }

    /// <summary>
    /// 用户自我评价
    /// </summary>
    public class UserSelfEvaluate
    {
        /// <summary>
        /// 评价内容
        /// </summary>
        public string? SelfEvaluate { get; set; }
    }

    /// <summary>
    /// 用户标签
    /// </summary>
    public class UserLabel
    {
        /// <summary>
        /// 标签内容集合
        /// </summary>
        public List<string>? Label { get; set; }

        /// <summary>
        /// 性格
        /// </summary>
        public List<string>? Nature { get; set; }

        /// <summary>
        /// 技能
        /// </summary>
        public List<string>? Skill { get; set; }

        /// <summary>
        /// 外貌
        /// </summary>
        public List<string>? Appearance { get; set; }
    }
    #endregion

    #region 招聘流程相关模型
    public class RecruitmentProcessResponse
    {
        public List<RecruitmentProcessInfo> Rows { get; set; } = default!;
    }

    /// <summary>
    /// 招聘流程返回模型
    /// </summary>
    public class RecruitmentProcessInfo
    {
        /// <summary>
        /// 用户姓名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 招聘流程状态
        /// </summary>
        public RecruitStatus RecruitStatus { get; set; }

        /// <summary>
        /// 招聘流程状态名称
        /// </summary>
        public string RecruitStatusName { get; set; } = string.Empty;

        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime OperationTime { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string OperationPeople { get; set; } = string.Empty;

        /// <summary>
        /// 岗位名称
        /// </summary>
        public string WorkPositionName { get; set; } = string.Empty;

        /// <summary>
        /// 面试模型
        /// </summary>
        public RecruitInterviewModel? RecruitInterviewModel { get; set; }

        /// <summary>
        /// 面试官筛选模型
        /// </summary>
        public RecruitScreenModel? RecruitScreenModel { get; set; }

        /// <summary>
        /// Offer模型
        /// </summary>
        public RecruitOfferModel? RecruitOfferModel { get; set; }

        /// <summary>
        /// 归档模型
        /// </summary>
        public RecruitFileAwayModel? RecruitFileAwayModel { get; set; }
    }

    /// <summary>
    /// 招聘流程面试模型
    /// </summary>
    public class RecruitInterviewModel
    {
        /// <summary>
        /// 面试状态
        /// </summary>
        public RecruitInterviewOutcome? InterviewOutcome { get; set; }

        /// <summary>
        /// 面试状态解释
        /// </summary>
        public string? InterviewOutcomeName { get; set; }

        /// <summary>
        /// 面试过程（初试、复试、终试）
        /// </summary>
        public RecruitInterviewProcess? InterviewProcess { get; set; }

        /// <summary>
        /// 面试过程解释
        /// </summary>
        public string? InterviewProcessName { get; set; }

        /// <summary>
        /// 面试时间
        /// </summary>
        public DateTime? InterviewTime { get; set; }

        /// <summary>
        /// 面试官名称
        /// </summary>
        public string? Interviewer { get; set; }

        /// <summary>
        /// 评价
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 反馈时间
        /// </summary>
        public DateTime? TodoTime { get; set; }

        /// <summary>
        /// 候选人的反馈
        /// </summary>
        public RecruitInterviewUserFeedBack? UserFeedBack { get; set; }

        /// <summary>
        /// 候选人的反馈解释
        /// </summary>
        public string? UserFeedBackName { get; set; } = string.Empty;

        /// <summary>
        /// 取消面试用户名
        /// </summary>
        public string? CancelName { get; set; }
    }

    /// <summary>
    /// 招聘流程面试官筛选模型
    /// </summary>
    public class RecruitScreenModel
    {
        /// <summary>
        /// 面试官筛选状态
        /// </summary>
        public RecruitInterviewerScreenStatus? InterviewerScreenStatus { get; set; }

        /// <summary>
        /// 面试官筛选状态解释
        /// </summary>
        public string? InterviewerScreenStatusName { get; set; }

        /// <summary>
        /// 面试官名称
        /// </summary>
        public string? Interviewer { get; set; }

        /// <summary>
        /// 反馈备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 反馈时间
        /// </summary>
        public DateTime? TodoTime { get; set; }
    }

    /// <summary>
    /// 招聘流程Offer模型
    /// </summary>
    public class RecruitOfferModel
    {
        /// <summary>
        /// 发放时间
        /// </summary>
        public DateTime? ReleaseTime { get; set; }

        /// <summary>
        /// 是否已通知
        /// </summary>
        public bool? IsNotice { get; set; }
    }

    /// <summary>
    /// 招聘流程归档模型
    /// </summary>
    public class RecruitFileAwayModel
    {
        /// <summary>
        /// 归档类型
        /// </summary>
        public RecruitFileAway? RecruitFileAway { get; set; }

        /// <summary>
        /// 归档类型解释
        /// </summary>
        public string? RecruitFileAwayName { get; set; }

        /// <summary>
        /// 归档原因
        /// </summary>
        public string? FileAwayReason { get; set; }
    }
    #endregion

    #region 日历模式相关模型
    /// <summary>
    /// 获取待办计划接收模型
    /// </summary>
    public class GetTodoPlanRequest
    {
        /// <summary>
        /// 查询的时间
        /// </summary>
        public DateTime CreatTime { get; set; } = default!;
    }

    /// <summary>
    /// 获取待办计划返回模型
    /// </summary>
    public class GetTodoPlanResponse
    {
        /// <summary>
        /// 待办集合
        /// </summary>
        public List<int> TodoList { get; set; } = new List<int>();

        /// <summary>
        /// 未处理的待办集合
        /// </summary>
        public List<int> UntreatedTodoList { get; set; } = new List<int>();
    }

    /// <summary>
    /// 获取待办计划列表接收模型
    /// </summary>
    public class GetTodoPlanListRequest : QueryRequest
    {
        /// <summary>
        /// 查询的时间
        /// </summary>
        public DateTime CreatTime { get; set; } = default!;
    }

    /// <summary>
    /// 获取待办计划列表返回模型
    /// </summary>
    public class GetTodoPlanListResponse : QueryResponse
    {
        public List<GetTodoPlanModel> Rows { get; set; } = default!;

        /// <summary>
        /// 未处理面试数量
        /// </summary>
        public int UntreatedTodoCount { get; set; }
    }

    /// <summary>
    /// 获取待办计划列表模型
    /// </summary>
    public class GetTodoPlanModel
    {
        /// <summary>
        /// 待办id
        /// </summary>
        public string? TodoId { get; set; }

        /// <summary>
        /// 用户id
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 头像
        /// </summary>
        public string HeadPortrait { get; set; } = string.Empty;

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 性别解释
        /// </summary>
        public string? SexName { get; set; }

        /// <summary>
        /// 生日
        /// </summary>
        public DateOnly? Birthday { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }

        /// <summary>
        /// 职业
        /// </summary>
        public OccupationType? Occupation { get; set; }

        /// <summary>
        /// 职业解释
        /// </summary>
        public string? OccupationName { get; set; }

        /// <summary>
        /// 学历
        /// </summary>
        public EducationType? Education { get; set; }

        /// <summary>
        /// 学历解释
        /// </summary>
        public string? EducationName { get; set; }

        /// <summary>
        /// 应聘职位
        /// </summary>
        public string JobApplication { get; set; } = string.Empty;

        /// <summary>
        /// 面试结果
        /// </summary>
        public RecruitInterviewOutcome Outcome { get; set; }

        /// <summary>
        /// 面试结果解释
        /// </summary>
        public string OutcomeName { get; set; } = string.Empty;

        /// <summary>
        /// 面试方式
        /// </summary>
        public RecruitInterviewForms Forms { set; get; }

        /// <summary>
        /// 面试方式解释
        /// </summary>
        public string FormsName { get; set; } = string.Empty;

        /// <summary>
        /// 面试过程
        /// </summary>
        public RecruitInterviewProcess Process { set; get; }

        /// <summary>
        /// 面试过程解释
        /// </summary>
        public string ProcessName { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatTime { get; set; }

        /// <summary>
        /// 项目id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; } = string.Empty;

        /// <summary>
        /// 项目经理
        /// </summary>
        public string ProjectManager { get; set; } = string.Empty;

        /// <summary>
        /// 项目经理头像
        /// </summary>
        public string ProjectHeadPortrait { get; set; } = string.Empty;

        /// <summary>
        /// 面试时间
        /// </summary>
        public DateTime InterviewTime { get; set; }
    }
    #endregion
}
