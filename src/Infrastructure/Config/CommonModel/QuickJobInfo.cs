﻿using Config.Enums;

namespace Config.CommonModel;

/// <summary>
/// 零工市场业务字段
/// </summary>
public class QuickJobInfo
{
    /// <summary>
    /// 项目行业 = 项目执行/发布项目（项目行业）/ 所属产业
    /// </summary>
    public ProjectIndustry? Industry { get; set; }

    /// <summary>
    /// 公司所属行业
    /// </summary>
    public int? CompanyIndustry { get; set; }

    /// <summary>
    /// 联系人姓名 = 项目执行 /编辑名片（姓名）
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 担任职位
    /// </summary>
    public string? PositionName { get; set; }

    /// <summary>
    /// 招聘职位名称 - name
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 职位类别 - id
    /// </summary>
    public int Category { get; set; } = 1043;// 其他

    /// <summary>
    /// 联系人手机 = 项目执行 /编辑名片（有手机号码不可修改）
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 联系人电子邮箱 = 项目执行 /编辑名片（邮箱）
    /// </summary>
    public string Email { get; set; } = default!;

    /// <summary>
    /// 对接单位
    /// </summary>
    public string? ContactCompany { get; set; }

    /// <summary>
    /// 推荐码
    /// </summary>
    public string? ReferralCode { get; set; }

    /// <summary>
    /// 营业执照
    /// </summary>
    public string? CompanyLicense { get; set; }

    /// <summary>
    /// 公司介绍
    /// </summary>
    public string? CompanyInfo { get; set; }

    /// <summary>
    /// 纳税人类型
    /// </summary>
    public TaxpayerType TaxpayerType { get; set; }

    /// <summary>
    /// 企业logo
    /// </summary>
    public string LogoUrl { get; set; } = string.Empty;

    /// <summary>
    /// 公司名称
    /// </summary>
    public string CompanyName { get; set; } = default!;

    /// <summary>
    /// 公司简称
    /// </summary>
    public string CompanyAbbr { get; set; } = string.Empty;

    /// <summary>
    /// 工作地址
    /// </summary>
    public string CompanyAddress { get; set; } = default!;

    /// <summary>
    /// 工作地址坐标 new Point(model.Lat, model.Lng);
    /// </summary>
    public double? Lat { get; set; }
    public double? Lng { get; set; }

    /// <summary>
    /// 企业性质
    /// </summary>
    public EnterpriseNature Nature { get; set; } = EnterpriseNature.个体工商户;

    /// <summary>
    /// 企业规模
    /// </summary>
    public EnterpriseScale Scale { get; set; } = EnterpriseScale.二十人以下;

    /// <summary>
    /// 融资阶段
    /// </summary>
    public EnterpriseCapital Capital { get; set; }

    /// <summary>
    /// 地区ID
    /// </summary>
    public string RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 职位信息
    /// </summary>
    public PostInfo? PostInfo { get; set; }
}

/// <summary>
/// 职位信息
/// </summary>
public class PostInfo
{
    /// <summary>
    /// 职位描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 职位性质
    /// </summary>
    public PostWorkNature? WorkNature { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 薪资最小值
    /// </summary>
    public int? MinSalary { get; set; }

    /// <summary>
    /// 薪资最大值
    /// </summary>
    public int? MaxSalary { get; set; }

    /// <summary>
    /// 几薪
    /// </summary>
    public int? Salary { get; set; }

    /// <summary>
    /// 福利（更新只提交Id即可）
    /// </summary>
    public List<WelfareModel>? Welfare { get; set; } = new List<WelfareModel>();

    /// <summary>
    /// 自定义福利:只传name
    /// </summary>
    public List<string> Welfarecustom { get; set; } = new List<string> { };

    /// <summary>
    /// 招聘人数
    /// </summary>
    public int RecruitNumber { get; set; }

    /// <summary>
    /// 结算方式
    /// </summary>
    public PostSalaryType SalaryType { get; set; } = PostSalaryType.月薪;

    /// <summary>
    /// 结算方式
    /// </summary>
    public PostSettlementType SettlementType { get; set; } = PostSettlementType.月结;

    /// <summary>
    /// 是否发布
    /// </summary>
    public bool Published { get; set; } = false;
}