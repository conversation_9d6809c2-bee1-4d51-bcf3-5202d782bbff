DROP TABLE IF EXISTS `kuaishou_tenant`;
CREATE TABLE `kuaishou_tenant` (
  `Id` varchar(18) NOT NULL,
  `TenantOpenId` varchar(50) NOT NULL COMMENT '租户openid',
  `Name` varchar(50) NOT NULL COMMENT '租户名称',
  `Creator` varchar(18) NOT NULL,
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='快手租户';

ALTER TABLE recruit ADD ResumeBufferId VARCHAR(18) NULL COMMENT'第三方简历池Id';

-- 添加字段
alter table project_teambounty 
add column ResumeBufferId VARCHAR(36) COMMENT '第三方简历池主键id' AFTER RecruitId,
add index `ix_ResumeBufferId` ( `ResumeBufferId` desc) USING BTREE;
-- 新建表
CREATE TABLE `kuaishou_hr_talent_relations` (
	`Id` VARCHAR(18) NOT NULL COLLATE 'utf8mb4_general_ci',
	`HrId` VARCHAR(18) NOT NULL COMMENT '跟进人' COLLATE 'utf8mb4_general_ci',
	`ApplicationId` VARCHAR(36) NOT NULL COMMENT 'ApplicationId' COLLATE 'utf8mb4_general_ci',
	`ReturnVisit` TINYINT(3) NOT NULL DEFAULT '0' COMMENT '是否回访：默认0-未回访，1-已回访',
	`VisitMemo` VARCHAR(2000) NULL DEFAULT NULL COMMENT '回访情况描述' COLLATE 'utf8mb4_general_ci',
	`Result` TINYINT(3) NULL DEFAULT '1' COMMENT '是否有效：默认1-有效，0-无效',
	`InvalidType` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`SecondVisit` TINYINT(3) NULL DEFAULT '0' COMMENT '二次回访是否转换：0-未转化，1-已转化',
	`SecondVisitMemo` VARCHAR(2000) NULL DEFAULT NULL COMMENT '转化情况描述' COLLATE 'utf8mb4_general_ci',
	`WaitToPhone` TINYINT(3) NULL DEFAULT '0' COMMENT '是否等会再打电话：默认0-不是，1-是',
	`Status` TINYINT(3) NULL DEFAULT '0' COMMENT '简历是否推送：默认0-未推送，1-已推送',
	`CreatedTime` DATETIME NULL DEFAULT NULL,
	`UpdatedTime` DATETIME NULL DEFAULT NULL,
	`CreatedUser` VARCHAR(36) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`UpdatedUser` VARCHAR(36) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`Deleted` BIT(1) NULL DEFAULT 'b\'0\'',
	PRIMARY KEY (`Id`) USING BTREE,
	UNIQUE INDEX `ApplicationId_uni_index` (`ApplicationId`) USING BTREE
)
COMMENT='顾问简历关系表'
COLLATE='utf8mb4_general_ci'
ENGINE=INNODB;

CREATE TABLE `kuaishou_talent_infos` (
	`ApplicationId` VARCHAR(36) NOT NULL DEFAULT '' COMMENT '投递id' COLLATE 'utf8mb4_general_ci',
	`OpenTenantId` VARCHAR(36) NOT NULL DEFAULT '' COMMENT '绑定的租户 id' COLLATE 'utf8mb4_general_ci',
	`ResumeId` VARCHAR(36) NOT NULL DEFAULT '' COMMENT '简历id' COLLATE 'utf8mb4_general_ci',
	`Name` VARCHAR(50) NULL DEFAULT '' COMMENT '姓名' COLLATE 'utf8mb4_general_ci',
	`PlatType` TINYINT(3) NULL DEFAULT '0' COMMENT '表唯一标识，值不能修改',
	`GenderCode` TINYINT(3) NULL DEFAULT NULL COMMENT '性别编码',
	`GenderName` VARCHAR(5) NULL DEFAULT NULL COMMENT '性别名称' COLLATE 'utf8mb4_general_ci',
	`Phone` VARCHAR(20) NULL DEFAULT NULL COMMENT '手机号' COLLATE 'utf8mb4_general_ci',
	`Age` INT(10) NULL DEFAULT NULL COMMENT '年龄',
	`JobId` VARCHAR(50) NULL DEFAULT NULL COMMENT '职位id' COLLATE 'utf8mb4_general_ci',
	`JobName` VARCHAR(200) NULL DEFAULT NULL COMMENT '职位名称' COLLATE 'utf8mb4_general_ci',
	`SendTeamPostId` VARCHAR(36) NULL DEFAULT NULL COMMENT '最终推送职位' COLLATE 'utf8mb4_general_ci',
	`SendTeamPostName` VARCHAR(100) NULL DEFAULT NULL COMMENT '最终推送职位名称' COLLATE 'utf8mb4_general_ci',
	`SendPostId` VARCHAR(36) NULL DEFAULT NULL COMMENT '最终推送职位postid' COLLATE 'utf8mb4_general_ci',
	`ChannelName` VARCHAR(30) NULL DEFAULT NULL COMMENT '来源渠道（简历来源，直播、短视频等）' COLLATE 'utf8mb4_general_ci',
	`Recommender` VARCHAR(30) NULL DEFAULT NULL COMMENT '推荐人（来源主播昵称）' COLLATE 'utf8mb4_general_ci',
	`RecommenderId` VARCHAR(30) NULL DEFAULT NULL COMMENT '推荐人（来源主播uid）' COLLATE 'utf8mb4_general_ci',
	`ApplyTime` DATETIME NULL DEFAULT NULL COMMENT '申请时间',
	`CompanyId` VARCHAR(30) NULL DEFAULT NULL COMMENT '企业id' COLLATE 'utf8mb4_general_ci',
	`CompanyBusinessName` VARCHAR(300) NOT NULL COMMENT '企业中文' COLLATE 'utf8mb4_general_ci',
	`CompanyBusinessCode` VARCHAR(50) NULL DEFAULT NULL COMMENT '企业工商code' COLLATE 'utf8mb4_general_ci',
	`Platform` INT(10) NULL DEFAULT NULL COMMENT '0 - 未知的\n1 - 自招职位投递\n2 - 分销职位投递',
	`DataChannelSourceCode` VARCHAR(50) NULL DEFAULT NULL COMMENT '来源渠道 英文，比如：livesream/plc' COLLATE 'utf8mb4_general_ci',
	`DataChannelSourceName` VARCHAR(50) NULL DEFAULT NULL COMMENT '来源渠道 中文，比如：直播/视频' COLLATE 'utf8mb4_general_ci',
	`DataQualityLabel` VARCHAR(2000) NULL DEFAULT NULL COMMENT '简历质量标签：Json：{"age":"匹配","intentionCity":"匹配","intentionJobCategory":"匹配","multiApplyRecently":"匹配","liveCity":"匹配"}' COLLATE 'utf8mb4_general_ci',
	`DataDynamicResumeInfo` TEXT NULL DEFAULT NULL COMMENT '投递时的动态简历信息Json：\r\n{"intentionCity":"北京市","workExperience":"3~5年操作工经验","basicSituation":"身高175cm以上","jobHuntingStatus":"在职，月内到岗"}' COLLATE 'utf8mb4_general_ci',
	`DynamicResumeInfoOriginal` TEXT NULL DEFAULT NULL COMMENT '动态信息原文信息' COLLATE 'utf8mb4_general_ci',
	`DataTrafficSources` VARCHAR(50) NULL DEFAULT NULL COMMENT '流量来源：自然流量/粉条/磁力快招' COLLATE 'utf8mb4_general_ci',
	`DataUserCity` VARCHAR(100) NULL DEFAULT NULL COMMENT '用户所在城市，比如：玉溪市' COLLATE 'utf8mb4_general_ci',
	`DataLocationCitys` VARCHAR(2000) NULL DEFAULT NULL COMMENT '位置信息，Json' COLLATE 'utf8mb4_general_ci',
	`ExtInfoAgeMin` INT(10) NULL DEFAULT NULL COMMENT '年龄范围的最小值',
	`ExtInfoAgeMax` INT(10) NULL DEFAULT NULL COMMENT '年龄范围的最大值',
	`ExtInfoIntentionJob` VARCHAR(2000) NULL DEFAULT NULL COMMENT '意向职位:JsonArray:[{"id":10,"content":"制冷/水暖工"}]' COLLATE 'utf8mb4_general_ci',
	`ExtInfoIntentionCity` VARCHAR(2000) NULL DEFAULT NULL COMMENT '意向城市:JsonArray:' COLLATE 'utf8mb4_general_ci',
	`ExtInfoJobHuntingStatus` INT(10) NULL DEFAULT NULL COMMENT '在职状态:1 - 离职，找工作中，随时到岗\n2 - 在职，找工作中，月内到岗\n3 - 在职，看看好的工作机会\n4 - 在职，暂时不找工作\n',
	`CreatedTime` DATETIME NULL DEFAULT NULL,
	`UpdatedTime` DATETIME NULL DEFAULT NULL,
	`Deleted` INT(10) NULL DEFAULT '0',
	`CreatedUser` VARCHAR(30) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`UpdatedUser` VARCHAR(30) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	PRIMARY KEY (`ApplicationId`) USING BTREE
)
COMMENT='快手投递简历表'
COLLATE='utf8mb4_general_ci'
ENGINE=INNODB;

CREATE TABLE `resume_buffer` (
	`Id` VARCHAR(36) NOT NULL COLLATE 'utf8mb4_general_ci',
	`ResumeId` VARCHAR(36) NOT NULL COMMENT '第三方表主键' COLLATE 'utf8mb4_general_ci',
	`Source` TINYINT(3) NOT NULL COMMENT '来源：0-快招工，1-导入',
	`Valid` TINYINT(3) NOT NULL DEFAULT '0' COMMENT '0-待筛选，1-有效线索，2-无效线索',
	`PostName` VARCHAR(100) NOT NULL COMMENT '诺快聘职位名称' COLLATE 'utf8mb4_general_ci',
	`PostId` VARCHAR(36) NOT NULL COMMENT '诺快聘职位Id-TeamPostId' COLLATE 'utf8mb4_general_ci',
	`ApplyTime` DATETIME NOT NULL COMMENT '报名时间/导入时间',
	`Name` VARCHAR(30) NOT NULL COMMENT '姓名' COLLATE 'utf8mb4_general_ci',
	`Avatar` VARCHAR(300) NULL DEFAULT NULL COMMENT '头像' COLLATE 'utf8mb4_general_ci',
	`Sex` VARCHAR(10) NULL DEFAULT NULL COMMENT '性别' COLLATE 'utf8mb4_general_ci',
	`Birthday` DATETIME NULL DEFAULT NULL COMMENT '生日',
	`Age` TINYINT(3) NULL DEFAULT NULL COMMENT '年龄',
	`Mobile` VARCHAR(30) NOT NULL COMMENT '电话' COLLATE 'utf8mb4_general_ci',
	`HrId` VARCHAR(36) NULL DEFAULT NULL COMMENT '顾问Id' COLLATE 'utf8mb4_general_ci',
	`HrName` VARCHAR(30) NULL DEFAULT NULL COMMENT '顾问名称' COLLATE 'utf8mb4_general_ci',
	`ChannelId` VARCHAR(36) NULL DEFAULT NULL COMMENT '渠道商Id' COLLATE 'utf8mb4_general_ci',
	`ChannelName` VARCHAR(30) NULL DEFAULT NULL COMMENT '渠道商名称' COLLATE 'utf8mb4_general_ci',
	`WeChat` VARCHAR(100) NULL DEFAULT NULL COMMENT '微信号码' COLLATE 'utf8mb4_general_ci',
	`QQ` VARCHAR(30) NULL DEFAULT NULL COMMENT 'QQ号码' COLLATE 'utf8mb4_general_ci',
	`Mailbox` VARCHAR(30) NULL DEFAULT NULL COMMENT '邮箱号' COLLATE 'utf8mb4_general_ci',
	`RegionId` VARCHAR(30) NULL DEFAULT NULL COMMENT '地区Id' COLLATE 'utf8mb4_general_ci',
	`Location` VARCHAR(100) NULL DEFAULT NULL COMMENT '所在地' COLLATE 'utf8mb4_general_ci',
	`LocationPoint` POINT NULL DEFAULT NULL COMMENT '所在地经纬度',
	`TopEducation` VARCHAR(30) NULL DEFAULT NULL COMMENT '最高学历' COLLATE 'utf8mb4_general_ci',
	`SelfEvaluation` VARCHAR(500) NULL DEFAULT NULL COMMENT '自我评价' COLLATE 'utf8mb4_general_ci',
	`Skills` VARCHAR(1000) NULL DEFAULT NULL COMMENT '技能Json对象' COLLATE 'utf8mb4_general_ci',
	`IndustryLabel` VARCHAR(1000) NULL DEFAULT NULL COMMENT '行业标签（来源小析职业标签字段）' COLLATE 'utf8mb4_general_ci',
	`PostLabel` VARCHAR(1000) NULL DEFAULT NULL COMMENT '职位标签（来源小析职业标签字段）' COLLATE 'utf8mb4_general_ci',
	`OtherLabel` VARCHAR(1000) NULL DEFAULT NULL COMMENT '其他标签（来源小析职业标签字段）' COLLATE 'utf8mb4_general_ci',
	`Hopes` TEXT NULL DEFAULT NULL COMMENT '求职期望Json对象' COLLATE 'utf8mb4_general_ci',
	`Educations` TEXT NULL DEFAULT NULL COMMENT '教育经历Json对象' COLLATE 'utf8mb4_general_ci',
	`Projects` TEXT NULL DEFAULT NULL COMMENT '项目经历Json对象' COLLATE 'utf8mb4_general_ci',
	`Works` TEXT NULL DEFAULT NULL COMMENT '工作经历Json对象' COLLATE 'utf8mb4_general_ci',
	`CreatedTime` DATETIME NULL DEFAULT NULL COMMENT '创建时间',
	`UpdatedTime` DATETIME NULL DEFAULT NULL COMMENT '更新时间',
	`CreatedUser` VARCHAR(36) NULL DEFAULT NULL COMMENT '创建人' COLLATE 'utf8mb4_general_ci',
	`UpdatedUser` VARCHAR(36) NULL DEFAULT NULL COMMENT '更新人' COLLATE 'utf8mb4_general_ci',
	`Deleted` BIT(1) NOT NULL DEFAULT 'b\'0\'',
	PRIMARY KEY (`Id`) USING BTREE,
	UNIQUE INDEX `ResumeId_Source` (`ResumeId`, `Source`) USING BTREE
)
COMMENT='第三方简历池：快招工，诺聘，导入简历等'
COLLATE='utf8mb4_general_ci'
ENGINE=INNODB;

CREATE TABLE `post_team_third_jobid_rel` (
	`Id` VARCHAR(36) NOT NULL COLLATE 'utf8mb4_general_ci',
	`TeamPostId` VARCHAR(36) NOT NULL COMMENT 'post_team表主键' COLLATE 'utf8mb4_general_ci',
	`JobId` VARCHAR(36) NOT NULL COMMENT '第三方jobid' COLLATE 'utf8mb4_general_ci',
	`PlatType` TINYINT(3) NOT NULL COMMENT '平台类型：0-快招工',
	`CreatedTime` DATETIME NOT NULL,
	`UpdatedTime` DATETIME NOT NULL,
	`CreatedUser` VARCHAR(36) NULL DEFAULT '' COLLATE 'utf8mb4_general_ci',
	`UpdatedUser` VARCHAR(36) NULL DEFAULT '' COLLATE 'utf8mb4_general_ci',
	`Deleted` BIT(1) NOT NULL DEFAULT 'b\'0\'',
	PRIMARY KEY (`Id`) USING BTREE,
	UNIQUE INDEX `TeamId_JobId_Uni_Index` (`PlatType`, `JobId`) USING BTREE
)
COMMENT='诺快聘职位与第三方平台关系表'
COLLATE='utf8mb4_general_ci'
ENGINE=INNODB;

CREATE TABLE `dic_base_info` (
	`Id` INT(10) NOT NULL AUTO_INCREMENT,
	`Code` VARCHAR(50) NOT NULL COLLATE 'utf8mb4_general_ci',
	`Value` VARCHAR(500) NOT NULL COLLATE 'utf8mb4_general_ci',
	`Type` VARCHAR(100) NOT NULL COMMENT '类别' COLLATE 'utf8mb4_general_ci',
	`Sort` INT(10) NULL DEFAULT '0' COMMENT '排序字段',
	`CreatedTime` DATETIME NULL DEFAULT NULL,
	`UpdatedTime` DATETIME NULL DEFAULT NULL,
	`CreatedUser` VARCHAR(36) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`UpdatedUser` VARCHAR(36) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`Deleted` TINYINT(3) NOT NULL DEFAULT '0',
	PRIMARY KEY (`Id`) USING BTREE,
	UNIQUE INDEX `Code_Value_Type` (`Code`, `Value`, `Type`) USING BTREE
)
COMMENT='配置信息，枚举'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
AUTO_INCREMENT=18;
-- 添加数据
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (1, '1', '投递者误点击，个人不知情，且无求职意向', 'kzg_invalid_resume_enum', 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (2, '2', '投递者明确表示对岗位无意向，且不考虑其他岗位', 'kzg_invalid_resume_enum', 2, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (3, '3', '电话空号、电话拨打3次无法接通，接挂者', 'kzg_invalid_resume_enum', 3, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (4, '4', '身体问题，无法工作者', 'kzg_invalid_resume_enum', 4, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (5, '5', '代为投递，但无意向添加微信，无法转化', 'kzg_invalid_resume_enum', 5, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (6, '6', '年龄过大，无合适工作', 'kzg_invalid_resume_enum', 6, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (7, '7', '省外异地，不找河北工作', 'kzg_invalid_resume_enum', 7, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (8, '8', '放弃找工作', 'kzg_invalid_resume_enum', 8, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (9, '9', '在职，无法转化跟进', 'kzg_invalid_resume_enum', 9, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (10, '10', '年龄和岗位年龄要求不一致', 'kzg_invalid_resume_enum', 10, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (11, '11', '素质低', 'kzg_invalid_resume_enum', 11, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (12, '12', '同行', 'kzg_invalid_resume_enum', 12, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (13, '99', '其他', 'kzg_invalid_resume_enum', 13, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (14, '当前岗位不匹配，已添加微信', '当前岗位不匹配，已添加微信', 'kzg_valid_resume_enum', 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (15, '意向不强烈，已添加微信', '意向不强烈，已添加微信', 'kzg_valid_resume_enum', 2, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (16, '代替亲属投递，已添加微信', '代替亲属投递，已添加微信', 'kzg_valid_resume_enum', 3, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dic_base_info` (`Id`, `Code`, `Value`, `Type`, `Sort`, `CreatedTime`, `UpdatedTime`, `CreatedUser`, `UpdatedUser`, `Deleted`) VALUES (17, '添加微信', '添加微信', 'kzg_valid_resume_enum', 4, NULL, NULL, NULL, NULL, 0);

