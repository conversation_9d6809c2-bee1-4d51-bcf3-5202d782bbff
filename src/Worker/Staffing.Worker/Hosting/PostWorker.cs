using Staffing.Worker.Service;

namespace Staffing.Worker.Hosting;

public class PostWorker : HostingBase
{
    private readonly PostService _postSubService;

    public PostWorker(IHostApplicationLifetime lifetime, PostService postSubService) : base(lifetime)
    {
        _postSubService = postSubService;
    }

    public override async Task StartAsync(CancellationToken cancel)
    {
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.SubPostStatis(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.SubTeamBountyChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.SubRecruitStatusChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.SubRecruitStatusInvalidChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        // _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.SubPostStatusChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.SubTeamHrTj(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.SubProjectStatusChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.SubViewPost(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.SubProjectTeamBounty(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.SubPostLocationChange(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.SubPostAppletQrCode(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.SubRecruitProcessTime(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.SubZddPost(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.DownZddPost(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.SubWbMoFangPost(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.DownWbMoFangPost(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        // _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.AddNScore(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _postSubService.RecruitChangeToTalentResume(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        // return Task.CompletedTask;
    }
}