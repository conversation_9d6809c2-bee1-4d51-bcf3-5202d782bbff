﻿using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Config;
using Microsoft.Extensions.Options;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Singleton)]
public class QueueHelper
{
    private readonly LogManager _log;
    private readonly ConfigManager _config;
    public QueueHelper(LogManager log, IOptionsSnapshot<ConfigManager> config)
    {
        _log = log;
        _config = config.Value;
    }

    // public async Task SendKafkaMsg<T>(string? topic, T t)
    // {
    //     if (string.IsNullOrWhiteSpace(topic))
    //         return;

    //     var config = new ProducerConfig
    //     {
    //         BootstrapServers = _config.DataKafkaServer,
    //         MessageTimeoutMs = 3000
    //     };

    //     // using (var p = new ProducerBuilder<Null, string>(config).Build())
    //     // {
    //     //     p.ProduceAsync(topic, new Message<Null, string> { Value = JsonSerializer.SerializeToString(t) });
    //     // }

    //     using (var p = new ProducerBuilder<Null, string>(config).Build())
    //     {
    //         try
    //         {
    //             await p.ProduceAsync(topic, new Message<Null, string> { Value = JsonSerializer.SerializeToString(t) });
    //         }
    //         catch (ProduceException<Null, string> e)
    //         {
    //             _log.Error("发送kafka消息失败", e.Error.Reason, JsonSerializer.SerializeToString(t));
    //         }
    //     }
    // }
}