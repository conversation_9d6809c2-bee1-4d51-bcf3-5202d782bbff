﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.CommonModel.Business;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 钉钉通知记录
/// </summary>
[Table("dd_notify_record")]
public class Dd_Notify_Record
{
    [Key]
    public long Id { get; set; }

    /// <summary>
    /// 通知类型
    /// </summary>
    public MsgNotifyType Type { get; set; }

    /// <summary>
    /// 通知状态
    /// </summary>
    public DdNotifyRecordStatus Status { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 通知内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 数据
    /// </summary>
    public string Data { get; set; } = string.Empty;

    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 钉钉消息唯一标识
    /// </summary>
    public string? ProcessQueryKey { get; set; }

    /// <summary>
    /// 消息已读标识
    /// </summary>
    public string? ReadStatus { get; set; }


    public User_Hr User_Hr { get; set; } = default!;
}

public enum DdNotifyRecordStatus
{
    已发送 = 1, 尚未绑定钉钉 = 2
}