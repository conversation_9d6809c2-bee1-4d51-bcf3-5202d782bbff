﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.CommonModel.Ndn;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 数字诺亚待办表
/// </summary>
[Table("ndn_todo")]
public class Ndn_Todo
{
    /// <summary>
    /// 主键
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 待办标题
    /// </summary>
    public string Title { get; set; } = default!;

    /// <summary>
    /// 待办描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 项目编号
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public NdnTodoStatus Status { get; set; }

    /// <summary>
    /// 关联实体ID
    /// </summary>
    public string RelatedEntityId { get; set; } = default!;

    /// <summary>
    /// 关联实体类型
    /// </summary>
    public NdnTodoType RelatedEntityType { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}
