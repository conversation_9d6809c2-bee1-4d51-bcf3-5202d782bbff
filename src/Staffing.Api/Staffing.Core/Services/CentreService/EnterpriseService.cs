﻿using Config.Enums;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces;
using Staffing.Core.Interfaces.CentreService;
using Staffing.Core.Interfaces.Common;
using Config;
using Infrastructure.CommonService;
using EntityFrameworkCore.AutoHistory.Extensions;
using Staffing.Model.CentreService.Enterprise;

namespace Staffing.Core.Services.CentreService;

[Service(ServiceLifetime.Transient)]
public class EnterpriseService : IEnterpriseService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private ISharedService _sharedService;
    private ICommonService _commonService;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CommonUserService _commonUserService;
    public EnterpriseService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, ICommonService commonService,
        LogManager log, ISharedService sharedService, CommonUserService commonUserService)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _commonService = commonService;
        _log = log;
        _sharedService = sharedService;
        _commonUserService = commonUserService;
    }

    public EmptyResponse UpdateEntName(UpdateEntName model)
    {
        var lockerKey = $"adminupdateent:{model.EntId}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey, 10);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var enterprise = _context.Enterprise.FirstOrDefault(x => x.EntId == model.EntId);
        if (enterprise == null)
            throw new BadRequestException("企业不存在");

        if (!string.IsNullOrWhiteSpace(model.EntName))
        {
            if (_context.Enterprise.Any(x => x.Name == model.EntName && x.EntId != model.EntId))
                throw new BadRequestException("企业名称已存在");

            enterprise.Name = model.EntName;
        }

        if (!string.IsNullOrWhiteSpace(model.EntAbbr))
            enterprise.Abbreviation = model.EntAbbr;

        enterprise.UpdatedTime = DateTime.Now;

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        return new EmptyResponse();
    }

    public EmptyResponse UpdateEntGroup(UpdateEntGroup model)
    {
        var result = new EmptyResponse();
        if (model.SubEntId == null || model.SubEntId.Count == 0)
            return result;

        var mainEnt = _context.Enterprise.Where(x => x.EntId == model.MainEntId).FirstOrDefault();

        if (mainEnt == null)
            throw new BadRequestException("主企业不存在");

        if (mainEnt.GroupType != EnterpriseGroupType.MainGroup && mainEnt.GroupType != EnterpriseGroupType.Ordinary)
            throw new BadRequestException("无法成为主企业");

        var subEnts = _context.Enterprise.Where(x => model.SubEntId.Contains(x.EntId) && x.EntId != mainEnt.EntId).ToList();

        if (subEnts.Any(x => x.GroupType != EnterpriseGroupType.Ordinary))
            throw new BadRequestException("分公司状态错误");

        mainEnt.GroupType = EnterpriseGroupType.MainGroup;
        mainEnt.GroupEntId = mainEnt.EntId;

        foreach (var item in subEnts)
        {
            item.GroupEntId = mainEnt.EntId;
            item.GroupType = EnterpriseGroupType.Group;

            var re = new Enterprise_Relation
            {
                Creator = mainEnt.EntId,
                EntId = item.EntId,
                GroupEntId = mainEnt.EntId
            };
            _context.Add(re);
        }

        _context.SaveChanges();

        return result;
    }

    public EmptyResponse DeleteEntGroup(DeleteEntGroup model)
    {
        var result = new EmptyResponse();

        if (model.SubEntId == null || model.SubEntId.Count == 0)
            return result;

        var subEnts = _context.Enterprise.Where(x => model.SubEntId.Contains(x.EntId)).ToList();

        if (subEnts.Any(x => x.GroupType != EnterpriseGroupType.Group))
            throw new BadRequestException("分公司状态错误");

        foreach (var item in subEnts)
        {
            item.GroupType = EnterpriseGroupType.Ordinary;
            item.GroupEntId = string.Empty;
        }

        _context.Enterprise_Relation.RemoveRange(_context.Enterprise_Relation.Where(x => model.SubEntId.Contains(x.EntId)));

        _context.SaveChanges();

        return result;
    }
}