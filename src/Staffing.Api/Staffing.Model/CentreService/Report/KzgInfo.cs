namespace Staffing.Model.CentreService.Report;

public class ExportKzgResume
{
    /// <summary>
    /// 起始时间
    /// </summary>
    public DateTime? BeginTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

public class ExportKzgResumeResponse
{
    public List<ExportKzgResumeInfo> Rows { get; set; } = new List<ExportKzgResumeInfo>();
}

public class ExportKzgResumeInfo
{
    public string? Name { get; set; }
    public string? JobName { get; set; }
    public string? SendTeamPostName { get; set; }
    public string? RbCreatedTime { get; set; }
    public string? Recommender { get; set; }
    public string? ApplyTime { get; set; }
    public string? CompanyBusinessName { get; set; }
    public string? ReturnVisit { get; set; }
    public string? VisitMemo { get; set; }
    public string? NickName { get; set; }
    public string? Result { get; set; }
    public string? Status { get; set; }
}