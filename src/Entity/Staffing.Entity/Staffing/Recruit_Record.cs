﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 招聘流程记录表
/// </summary>
[Table("recruit_record")]
public class Recruit_Record
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 招聘id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 状态
    /// </summary>
    public Config.Enums.RecruitStatus Status { get; set; }

    /// <summary>
    /// 归档状态
    /// </summary>
    public Config.Enums.RecruitFileAway FileAway { get; set; } = Config.Enums.RecruitFileAway.TalentReserve;

    /// <summary>
    /// 面试官筛选id
    /// </summary>
    public string? InterviewerScreenId { get; set; } = string.Empty;

    /// <summary>
    /// 面试id
    /// </summary>
    public string? InterviewId { get; set; } = string.Empty;

    /// <summary>
    /// OfferId
    /// </summary>
    public string? OfferId { get; set; } = string.Empty;

    /// <summary>
    /// json数据，可能不用
    /// </summary>
    public string Data { get; set; } = string.Empty;

    /// <summary>
    /// 备注
    /// </summary>
    public string FileAwayRemarks { get; set; } = string.Empty;

    /// <summary>
    /// 操作人
    /// </summary>
    public string? Creator { get; set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Recruit Recruit { get; set; } = default!;

    public Recruit_Interview Recruit_Interview { get; set; } = default!;

    public Recruit_Offer Recruit_Offer { get; set; } = default!;

    public Recruit_Interviewer_Screen Recruit_Interviewer_Screen { get; set; } = default!;
    
    public User_Hr User_Hr { get; set; } = default!;
    
}

