<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk.Web">
	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>
	<PropertyGroup>
		<WarningLevel>4</WarningLevel>
		<DocumentationFile>bin\Debug\net8.0\Staffing.Api.xml</DocumentationFile>
		<NoWarn>1701;1702;1591;NU1803;CS8981;</NoWarn>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.10" />
		<PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.9.0" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\Staffing.Core\Staffing.Core.csproj">
		</ProjectReference>
	</ItemGroup>
	<ItemGroup>
		<None Include="TemplateFiles\resume\**">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="TemplateFiles\kzg\kzgresume.xlsx">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="wwwroot\Certificate\msyh.ttf">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="wwwroot\Certificate\postertemplateBottom.jpg">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="wwwroot\Certificate\postertemplateTableTr.jpg">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="wwwroot\Certificate\postertemplateTop.jpg">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="wwwroot\TempFile\Excel简历模板.xlsx">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="wwwroot\TempFile\ResumeTemp.docx">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="TemplateFiles\ProjectBoard\zhaopinweidu.xlsx">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="TemplateFiles\ProjectBoard\zhaopinjiaofukpi.xlsx">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="TemplateFiles\Szny\project.xlsx">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="TemplateFiles\ProjectBoard\hrtongji.xlsx">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="TemplateFiles\ProjectBoard\teambounty.xlsx">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="TemplateFiles\ProjectBoard\bumenkpi.xlsx">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="TemplateFiles\ProjectBoard\诺快聘运营数据周报.xlsx">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="wwwroot\TempFile\超时记录.xlsx" />
	</ItemGroup>
	<ItemGroup>
	  <Content Update="wwwroot\Certificate\qw_Certificate.jpg">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	  <Content Update="wwwroot\Certificate\qw_Certificate2.jpg">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	  <Content Update="wwwroot\TempFile\超时记录.xlsx">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	</ItemGroup>
	<ItemGroup>
	  <None Update="TemplateFiles\resume\Recruit\ResumeTempRecruit.docx">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	  <None Update="TemplateFiles\resume\Talent\ResumeTempTalent.docx">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>
</Project>