using Config.Enums;

namespace Config.CommonModel.Business;

public class PostSyncResponse
{
    public PostSyncType Type { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public PostSyncInfo? Post { get; set; }

    /// <summary>
    /// 顾问
    /// </summary>
    public PostSyncHrInfo? Hr { get; set; }

    /// <summary>
    /// 代招企业信息
    /// </summary>
    public PostSyncEntInfo? Ent { get; set; }
}
public class UpdatePostResponseNew
{
    /// <summary>
    /// 职位Id
    /// </summary>
    public string? PostId { get; set; }
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }
    /// <summary>
    /// 返费周期状态（0：持续返费，1：自定义返费）
    /// </summary>
    public int PaymentDurationStatus { get; set; }
}
public class OrderChangeResponse
{
    public string? PostId { get; set; }//岗位Id
    public string? UserId { get; set; }//用户Id
    public int FanfeiStatus { get; set; }//返费状态
    public double Fanfei { get; set; }//更改后的返费金额
}
public class PostSyncInfo : UpdatePost
{
    /// <summary>
    /// 地区
    /// </summary>
    public CityModel? Region { get; set; }

    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 金额/人
    /// </summary>
    public decimal? Money { get; set; }

    /// <summary>
    /// 是否展示
    /// </summary>
    public bool Show { get; set; }
}

public class PostSyncHrInfo
{
    public string? HrId { get; set; }

    /// <summary>
    /// 诺聘PK_EAID
    /// </summary>
    public string? NuoId { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// 上次登录时间
    /// </summary>
    public DateTime? LoginTime { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }
}

public class PostSyncEntInfo
{
    /// <summary>
    /// 代招企业Id
    /// </summary>
    public string? AgentEntId { get; set; }

    /// <summary>
    /// 名字
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 企业简称
    /// </summary>
    public string Abbr { get; set; } = string.Empty;

    /// <summary>
    /// 展示项
    /// </summary>
    public EntDisplayType Display { get; set; } = EntDisplayType.企业名称;

    /// <summary>
    /// 展示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 企业Logo
    /// </summary>
    public string? LogoUrl { get; set; }

    /// <summary>
    /// 公司行业Id
    /// </summary>
    public string? Industry { get; set; }

    /// <summary>
    /// 企业性质
    /// </summary>
    public EnterpriseNature Nature { get; set; } = EnterpriseNature.个体工商户;

    /// <summary>
    /// 企业规模
    /// </summary>
    public EnterpriseScale Scale { get; set; } = EnterpriseScale.二十人以下;

    /// <summary>
    /// 融资阶段
    /// </summary>
    public EnterpriseCapital Capital { get; set; } = EnterpriseCapital.不需要融资;

    /// <summary>
    /// 状态
    /// </summary>
    public EnterpriseStatus Status { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    public double Lat { get; set; }
    public double Lng { get; set; }

    /// <summary>
    /// 公司行业名称
    /// </summary>
    public string? IndustryName { get; set; }

    /// <summary>
    /// 企业规模名称
    /// </summary>
    public string? ScaleName { get; set; }

    /// <summary>
    /// 企业性质名称
    /// </summary>
    public string? NatureName { get; set; }

    // /// <summary>
    // /// 融资阶段名称
    // /// </summary>
    // public string? CapitalName { get; set; }

    /// <summary>
    /// 地区名称
    /// </summary>
    public CityModel? Region { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; }
}

public enum PostSyncType
{
    职位变更, 顾问变更
}