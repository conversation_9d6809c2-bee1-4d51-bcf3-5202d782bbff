FROM crpi-6hsqzgqhxpg54bm7.cn-beijing.personal.cr.aliyuncs.com/nkp/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80

USER root

# 安装 fontconfig 以便使用 fc-cache
RUN apt-get update && apt-get install -y fontconfig

ENV ASPNETCORE_URLS=http://+:80

FROM --platform=$BUILDPLATFORM crpi-6hsqzgqhxpg54bm7.cn-beijing.personal.cr.aliyuncs.com/nkp/sdk:8.0 AS build
ARG configuration=Release
WORKDIR /src

COPY ["Worker/Staffing.Worker/Staffing.Worker.csproj", "Worker/Staffing.Worker/"]
COPY ["nuget.config", "Worker/Staffing.Worker/"]
COPY ["Infrastructure/Config/Config.csproj", "Infrastructure/Config/"]
COPY ["Infrastructure/Infrastructure/Infrastructure.csproj", "Infrastructure/Infrastructure/"]
COPY ["Entity/Staffing.Entity/Staffing.Entity.csproj", "Entity/Staffing.Entity/"]
RUN dotnet restore "Worker/Staffing.Worker/Staffing.Worker.csproj" --configfile "Worker/Staffing.Worker/nuget.config"

COPY . .

WORKDIR "/src/Worker/Staffing.Worker"
RUN dotnet build "Staffing.Worker.csproj" -c Release -o /app/build --no-restore

FROM build AS publish
RUN dotnet publish "Staffing.Worker.csproj" -c Release -o /app/publish --no-restore

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# 安装字体到容器
RUN mkdir -p /usr/share/fonts/truetype/custom && \
    cp /app/Font/fs.ttf /usr/share/fonts/truetype/custom/ && \
    fc-cache -f -v

ENTRYPOINT ["dotnet", "Staffing.Worker.dll"]