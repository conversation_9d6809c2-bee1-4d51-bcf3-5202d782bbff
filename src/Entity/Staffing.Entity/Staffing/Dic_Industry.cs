﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 行业字典
/// </summary>
[Table("dic_industry")]
public class Dic_Industry
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 父Id
    /// </summary>
    public int? ParentId { get; set; }

    /// <summary>
    /// 层级
    /// </summary>
    public string Level { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public ActiveStatus Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    public Dic_Industry? Parent { get; set; }
}