# 设计文档

## 概述

本设计文档描述了在 ProjectRouter 类中添加一个新接口的实现方案，该接口用于根据项目ID查询 project_contract 表中是否存在默认合同。该功能将通过现有的 IProjectContractsService 服务来实现，遵循项目现有的架构模式和编码规范。

## 架构

### 整体架构
```
ProjectRouter (API层)
    ↓
IProjectContractsService (服务接口层)
    ↓
ProjectContractsService (服务实现层)
    ↓
StaffingContext (数据访问层)
    ↓
Project_Contract (实体层)
```

### 请求流程
1. 客户端通过 HTTP GET 请求调用 `/project/contract/default/{projectId}` 接口
2. ProjectRouter 接收请求并验证 projectId 参数
3. 调用 IProjectContractsService.HasDefaultContract 方法
4. 服务层查询 project_contract 表，检查是否存在 IsDefault=1 的记录
5. 返回布尔值结果给客户端

## 组件和接口

### 1. API 路由定义
**位置：** `src/Staffing.Api/Staffing.Api/Apis/Hr/Project.cs`

在 ProjectRouter.AddRoutes 方法中添加新的路由：
```csharp
group.MapGet("/contract/default/{projectId}", (string projectId, IProjectContractsService rep) => rep.HasDefaultContract(projectId))
    .WithOpenApi(operation => new(operation) { Summary = "检查项目是否有默认合同" });
```

### 2. 服务接口扩展
**位置：** `src/Staffing.Api/Staffing.Core/Interfaces/Hr/IProjectContractsService.cs`

在接口中添加新方法：
```csharp
/// <summary>
/// 检查项目是否有默认合同
/// </summary>
/// <param name="projectId">项目ID</param>
/// <returns>是否存在默认合同</returns>
HasDefaultContractResponse HasDefaultContract(string projectId);
```

### 3. 服务实现
**位置：** `src/Staffing.Api/Staffing.Core/Services/Hr/ProjectContractsService.cs`

实现 HasDefaultContract 方法：
```csharp
public HasDefaultContractResponse HasDefaultContract(string projectId)
{
    if (string.IsNullOrWhiteSpace(projectId))
        throw new BadRequestException("项目ID不能为空");

    var hasDefaultContract = _context.Project_Contract
        .Any(x => x.ProjectId == projectId && x.IsDefault == 1);

    return new HasDefaultContractResponse { HasDefault = hasDefaultContract };
}
```

### 4. 响应模型
**位置：** `src/Staffing.Api/Staffing.Model/Hr/Project/ProjectContract.cs`

添加新的响应模型：
```csharp
/// <summary>
/// 检查项目默认合同响应
/// </summary>
public class HasDefaultContractResponse
{
    /// <summary>
    /// 是否存在默认合同
    /// </summary>
    public bool HasDefault { get; set; }
}
```

## 数据模型

### 数据库查询逻辑
- **表名：** project_contract
- **查询条件：** ProjectId = {projectId} AND IsDefault = 1
- **查询方法：** 使用 LINQ 的 Any() 方法进行存在性检查
- **性能考虑：** Any() 方法在找到第一个匹配记录时即停止查询，性能优于 Count() > 0

### 数据流
```
输入: projectId (string)
    ↓
验证: 检查 projectId 是否为空或null
    ↓
查询: SELECT COUNT(*) FROM project_contract WHERE ProjectId = @projectId AND IsDefault = 1
    ↓
处理: 将查询结果转换为布尔值
    ↓
输出: HasDefaultContractResponse { HasDefault: bool }
```

## 错误处理

### 异常类型和处理策略

1. **BadRequestException**
   - **触发条件：** projectId 为空或null
   - **错误消息：** "项目ID不能为空"
   - **HTTP状态码：** 400

2. **数据库异常**
   - **触发条件：** 数据库连接失败或查询异常
   - **处理方式：** 让异常向上传播，由全局异常处理器处理
   - **HTTP状态码：** 500

3. **一般异常**
   - **处理方式：** 记录日志并返回通用错误响应
   - **HTTP状态码：** 500

### 错误响应格式
遵循项目现有的错误响应格式，通过全局异常处理器统一处理。

## 测试策略

### 单元测试
1. **正常情况测试**
   - 项目存在默认合同时返回 true
   - 项目不存在默认合同时返回 false
   - 项目不存在任何合同时返回 false

2. **异常情况测试**
   - projectId 为 null 时抛出 BadRequestException
   - projectId 为空字符串时抛出 BadRequestException
   - projectId 为空白字符时抛出 BadRequestException

3. **边界情况测试**
   - 项目存在多个合同但都不是默认合同
   - 项目存在多个合同但只有一个是默认合同
   - 数据库中不存在该项目ID

### 集成测试
1. **API端到端测试**
   - 测试完整的HTTP请求响应流程
   - 验证OpenAPI文档生成正确
   - 测试不同HTTP状态码的返回

2. **数据库集成测试**
   - 测试实际数据库查询逻辑
   - 验证查询性能
   - 测试并发访问情况

### 性能测试
1. **响应时间测试**
   - 单次查询响应时间应小于100ms
   - 并发查询时的响应时间稳定性

2. **数据库查询优化**
   - 确保在 ProjectId 和 IsDefault 字段上有适当的索引
   - 监控查询执行计划

## 安全考虑

### 输入验证
- 对 projectId 参数进行严格的空值检查
- 虽然当前不需要复杂的格式验证，但应确保参数不包含恶意内容

### 权限控制
- 继承现有的认证和授权机制
- 确保只有有权限的用户才能查询项目合同信息

### 数据安全
- 不暴露敏感的合同详细信息
- 只返回必要的布尔值结果

## 部署和监控

### 部署注意事项
- 该功能为纯查询操作，不涉及数据修改，部署风险较低
- 可以通过功能开关控制新接口的启用

### 监控指标
- API调用频率和响应时间
- 数据库查询性能
- 错误率和异常类型统计

### 日志记录
- 记录API调用日志（包括参数和响应时间）
- 记录异常情况的详细日志
- 不记录敏感的项目信息