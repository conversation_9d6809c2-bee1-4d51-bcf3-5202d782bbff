using Config;
using Config.CommonModel.Business;
using Infrastructure.Extend;
using Staffing.Model.Hr.TalentVirtual;
using Staffing.Model.Seeker.Channel;
using Staffing.Model.Seeker.TalentVirtual;
using Staffing.Core.Interfaces.Seeker;

namespace Staffing.Api.Apis.Seeker;

[Service(ServiceLifetime.Transient)]
public class ChannelRouter : ISeekerRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("channel").WithTags("渠道");
        group.MapGet("/check", ([AsParameters] CheckChannel model, IChannelService rep) => rep.CheckChannel(model))
        .WithOpenApi(operation => new(operation) { Summary = "检测渠道商关系" });

        group.MapPost("/join", (JoinChannel model, IChannelService rep) => rep.JoinChannel(model))
        .WithOpenApi(operation => new(operation) { Summary = "加入渠道商" });

        group.MapGet("/info", ([AsParameters] GetChannel model, IChannelService rep) => rep.GetChannelInfo(model))
        .WithOpenApi(operation => new(operation) { Summary = "渠道商信息" });

        group.MapGet("/qrcode", ([AsParameters] GetChannelQrCode model, IChannelService rep) => rep.GetChannelQrCode(model))
        .WithOpenApi(operation => new(operation) { Summary = "渠道商信小程序邀约码" });

        group.MapGet("/hrs", ([AsParameters] GetHrs model, IChannelService rep) => rep.GetHrs(model))
        .WithOpenApi(operation => new(operation) { Summary = "顾问信息" });

        group.MapPost("/hrs/change", (SetAdviser model, IChannelService rep) => rep.SetAdviser(model))
        .WithOpenApi(operation => new(operation) { Summary = "切换顾问" });

        group.MapPost("/post/top", (TopPost model, IChannelService rep) => rep.TopPost(model))
        .WithOpenApi(operation => new(operation) { Summary = "职位置顶" });

        group.MapPost("/post/list", (SeekerGetPosts model, IChannelService rep) => rep.SeekerGetPosts(model))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "职位列表" });

        group.MapGet("/post/info", async ([AsParameters] SeekerGetPost model, IChannelService rep) => await rep.SeekerGetPost(model))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "职位详情" });

        group.MapPost("/recruit/list", (GetRecruit model, IChannelService rep) => rep.GetRecruit(model))
        .WithOpenApi(operation => new(operation) { Summary = "招聘流程列表" });

        group.MapPost("/talent/platform/list", (GetTalentPlatform model, IChannelService rep) => rep.GetTalentPlatform(model))
        .WithOpenApi(operation => new(operation) { Summary = "获取平台人才库列表" });

        group.MapPost("/talent/virtual/list", (GetTalentVirtual model, IChannelService rep) => rep.GetTalentVirtual(model))
        .WithOpenApi(operation => new(operation) { Summary = "获取简历人才库列表" });

        group.MapGet("/platformInfo/{platformId}", async (string platformId, IChannelService rep) => await rep.GetTalentPlatformDetailsAsync(platformId))
        .WithOpenApi(operation => new(operation) { Summary = "获取真实人才库简历详情" });

        group.MapGet("/virtualInfo/{virtualId}", async (string virtualId, IChannelService rep) => await rep.GetTalentVirtualInfoAsync(virtualId))
        .WithOpenApi(operation => new(operation) { Summary = "获取虚拟人才库简历详情" });

        group.MapPost("/resume", (UpdateTalentVirtaual model, IChannelService rep) => rep.UpdateTalentVirtual(model))
        .WithOpenApi(operation => new(operation) { Summary = "创建/修改虚拟人才库简历-基本信息" });

        group.MapPost("/resume/projects", (UpdateTalentVirtualResumeProjectSub model, IChannelService rep) => rep.UpdateTalentVirtualProjects(model))
        .WithOpenApi(operation => new(operation) { Summary = "创建/修改虚拟人才库简历-项目经历" });

        group.MapPost("/resume/works", (UpdateTalentVirtualResumeWorkSub model, IChannelService rep) => rep.UpdateTalentVirtualWorks(model))
        .WithOpenApi(operation => new(operation) { Summary = "创建/修改虚拟人才库简历-工作经历" });

        group.MapDelete("/resume/projects/{id}", (string id, IChannelService rep) => rep.DeleteTalentVirtualProjects(id))
        .WithOpenApi(operation => new(operation) { Summary = "删除虚拟人才库简历-项目经历" });

        group.MapDelete("/resume/works/{id}", (string id, IChannelService rep) => rep.DeleteTalentVirtualWorks(id))
        .WithOpenApi(operation => new(operation) { Summary = "删除虚拟人才库简历-工作经历" });

        group.MapGet("/resume/active/{id}", async (string id, IChannelService rep) => await rep.SendMessageToActive(id))
        .WithOpenApi(operation => new(operation) { Summary = "短信激活" });

        group.MapPost("/resume/url", (UpLoadResumeRequest model, IChannelService rep) => rep.UpLoadResume(model))
        .WithOpenApi(operation => new(operation) { Summary = "上传简历并解析" });
    }
}