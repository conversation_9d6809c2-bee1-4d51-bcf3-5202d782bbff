﻿using Config.CommonModel.DingDing;

namespace Infrastructure.DingTalk;

/// <summary>
/// 审批流
/// </summary>
//[Service(ServiceLifetime.Transient)]
public class DingTalkProcessInstanceApply : IDingTalkCallBackExcute
{
    private DingTalkOAProcessService _dingTalkProcess;

    public DingTalkProcessInstanceApply(DingTalkOAProcessService dingTalkProcess)
    {
        _dingTalkProcess = dingTalkProcess;
    }

    public void ProcessInstanceExcute(DingTalkApprovalCallback dingtalkApproval)
    {
        // 审批正常结束（同意或拒绝）
        if (dingtalkApproval.type == "finish")
        {
            if (dingtalkApproval.result == "agree")
            {
                GetApproved(dingtalkApproval.processInstanceId!);
            }
            else if (dingtalkApproval.result == "refuse")
            {
                GetRejected(dingtalkApproval.processInstanceId!);
            }
        }
        // 审批终止（发起人撤销审批单）
        else if (dingtalkApproval.type == "terminate")
        {
            Cancelled(dingtalkApproval.processInstanceId!);
        }

        // 获取审批实例详情并保存 todo:保留打印凭证
        _dingTalkProcess.GetProcessInstance(dingtalkApproval.processInstanceId!);
    }

    /// <summary>
    /// 审批通过
    /// </summary>
    public virtual void GetApproved(string processInstanceId)
    {

    }

    /// <summary>
    /// 审批拒绝
    /// </summary>
    public virtual void GetRejected(string processInstanceId)
    {

    }

    /// <summary>
    /// 审批撤销
    /// </summary>
    public virtual void Cancelled(string processInstanceId)
    {

    }
}
