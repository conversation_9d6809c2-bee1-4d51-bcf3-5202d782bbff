using System.ComponentModel;
using System.Reflection;

namespace Infrastructure.Extend;

public static class EnumExtension
{
    /// <summary>
    /// 获取枚举的描述 (Description)
    /// </summary>
    /// <param name="val"></param>
    /// <returns>没有描述信息时返回其Name属性</returns>
    public static string GetDescription(this System.Enum? val)
    {
        var t = val?.GetType();
        if (t == null || val == null)
            return string.Empty;

        var name = System.Enum.GetName(t, val);
        if (string.IsNullOrWhiteSpace(name))
            return string.Empty;

        var attr = t.GetMember(name)[0].GetCustomAttribute<DescriptionAttribute>(false);
        return (attr != null && !string.IsNullOrEmpty(attr.Description)) ? attr.Description : name;
    }
}