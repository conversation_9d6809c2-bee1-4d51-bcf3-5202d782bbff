
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationModels;

namespace Infrastructure.Extend;

/// <summary>
/// 实现全局自定义路由同能
/// </summary>
public class RouteConvention : IApplicationModelConvention
{
    public RouteConvention()
    {
    }
    
    /// <summary>
    /// 注册所有控制器
    /// </summary>
    /// <param name="application"></param>
    public void Apply(ApplicationModel application)
    {
        AttributeRouteModel routePre = new AttributeRouteModel(new RouteAttribute(string.Empty));
        //遍历所有的 Controller
        foreach (var controller in application.Controllers)
        {
            var ns = controller.ControllerType.Namespace?.ToLower();
            if (ns != null)
            {
                var nsa = ns.Split(".").ToList();
                var newnsa = new List<string>();

                var isC = false;
                foreach (var item in nsa)
                {
                    if (isC)
                        newnsa.Add(item);

                    if (item == "controllers")
                        isC = true;
                }

                // if (newnsa.Count > 0)
                //     newnsa.RemoveAt(newnsa.Count - 1);

                routePre = new AttributeRouteModel(new RouteAttribute(string.Join('/', newnsa)));
            }

            // 没有标记 RouteAttribute 的 Controller 默认使用[controller]/[action]
            var unmatchedSelectors = controller.Selectors.Where(x => x.AttributeRouteModel == null).ToList();
            if (unmatchedSelectors.Any())
            {
                foreach (var selectorModel in unmatchedSelectors)
                {
                    selectorModel.AttributeRouteModel = new AttributeRouteModel(new RouteAttribute("[controller]/[action]"));
                }
            }
            // 已经标记了 RouteAttribute 的 Controller
            var matchedSelectors = controller.Selectors.Where(x => x.AttributeRouteModel != null).ToList();
            if (matchedSelectors.Any())
            {
                foreach (var selectorModel in matchedSelectors)
                {
                    // 在 当前路由上 再 添加一个 路由前缀
                    selectorModel.AttributeRouteModel = AttributeRouteModel.CombineAttributeRouteModel(routePre, selectorModel.AttributeRouteModel);
                }
            }
        }
    }
}

// public static class MvcOptionsExpand
// {
//     public static void UseCentralRoutePrefix(this MvcOptions opts)
//     {
//         // 添加我们自定义 实现IApplicationModelConvention的RouteConvention
//         opts.Conventions.Insert(0, new RouteConvention());
//     }
//     public static void UseCentralRoutePrefix(this MvcOptions opts, params RouteConvention[] routeConventions)
//     {
//         // 添加我们自定义 实现IApplicationModelConvention的RouteConvention
//         foreach (var item in routeConventions)
//         {
//             opts.Conventions.Insert(0, item);
//         }
//     }
// }