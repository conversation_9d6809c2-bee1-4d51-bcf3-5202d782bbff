﻿using Config.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing
{
    /// <summary>
    /// 快手投递简历表
    /// </summary>
    [Table("kuaishou_hr_talent_relations")]
    public class Kuaishou_Hr_Talent_Relations
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [Key]
        public string Id { get; set; } = EntityTools.SnowflakeId();
        /// <summary>
        /// 诺快聘顾问Id
        /// </summary>
        public string HrId { get; set; } = default!;
        /// <summary>
        /// 快手简历ApplicationId
        /// </summary>
        public string ApplicationId { get; set; } = default!;
        /// <summary>
        /// 是否回访：默认0-未回访，1-已回访
        /// </summary>
        public ReturnVisit ReturnVisit { get; set; } = ReturnVisit.未回访;
        /// <summary>
        /// 回访情况描述
        /// </summary>
        public string? VisitMemo { get; set; }
        /// <summary>
        /// 是否有效：默认1-有效，0-无效
        /// </summary>
        public ResultStatus Result { get; set; } = ResultStatus.有效;
        /// <summary>
        /// 无效简历类型
        /// </summary>
        public string? InvalidType { get; set; }
        /// <summary>
        /// 二次回访是否转换：0-未转化，1-已转化
        /// </summary>
        public SecondVisit SecondVisit { get; set; } = SecondVisit.未转化;
        /// <summary>
        /// 转化情况描述
        /// </summary>
        public string? SecondVisitMemo { get; set; }
        /// <summary>
        /// 是否等会再打电话：默认0-不是，1-是
        /// </summary>
        public WaitToPhone WaitToPhone { get; set; } = WaitToPhone.不是;
        /// <summary>
        /// 是否推送：0-未推送，1-已推送
        /// </summary>
        public KuaishouStatus Status { get; set; } = KuaishouStatus.未推送;
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime UpdatedTime { get; set; } = DateTime.Now;
        /// <summary>
        /// 是否删除 1 - 已删除
        /// </summary>
        public int Deleted { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedUser { get; set; }
        /// <summary>
        /// 修改人
        /// </summary>
        public string? UpdatedUser { get; set; }

        public User_Hr User_Hr { get; set; } = default!;

        public Kuaishou_Talent_Infos Kuaishou_Talent_Infos { get; set; } = default!;
    }
}
