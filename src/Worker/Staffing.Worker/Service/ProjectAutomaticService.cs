﻿using Config;
using Config.CommonModel.Business;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class ProjectAutomaticService
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _logger;
    private ConfigManager _config;
    private readonly RetryWithBackoff<Xbb_Contract> _retryWithBackoff;

    /// <summary>
    ///  注入
    /// </summary>
    public ProjectAutomaticService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger, IOptionsSnapshot<ConfigManager> config, RetryWithBackoff<Xbb_Contract> retryWithBackoff)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _config = config.Value;
        _retryWithBackoff = retryWithBackoff;
    }

    /// <summary>
    /// offer自流转 - 7天不处理，算入职，入职后立即加入成员表(暂时不处理)
    /// </summary>
    /// <returns></returns>
    public async Task AutomaticForOffer(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;
            //var today = DateTime.Today;
            //if (DateTime.Now >= today.AddHours(3))
            //    return;
            //加锁
            var lockerKey = $"automaticforofferlocker";
            using var locker = MyRedis.TryLock(lockerKey, 3600);
            if (locker == null)
                return;

            using var _context = _contextFactory.CreateDbContext();
            DateTime calculationTime = DateTime.Now.AddDays(-7);
            var recruitList = _context.Recruit.IgnoreQueryFilters().Where(o => o.Status == RecruitStatus.Offer && o.StatusTime < calculationTime)// && o.Post_Delivery.Post.Project.Project_Automatic!.OfferStatus == true
                .Select(o => new
                {
                    o.RecruitId,
                    o.Recruit_Offer.OrderByDescending(s => s.CreatedTime).First().InductionTime
                }).Take(100).ToList();

            List<string> ids = new List<string>();
            ids = recruitList.Select(o => o.RecruitId).ToList();
            if (ids.Count <= 0)
                return;

            _logger.Info("AutomaticForOffer", "offer自流转", $"倒计时7天,1次/30秒，开始处理，时间：{DateTime.Now}");
            var recruitEditList = _context.Recruit.Where(o => ids.Contains(o.RecruitId)).ToList();

            List<Recruit_Record> addRecord = new List<Recruit_Record>();
            int consume = 0;
            foreach (var i in recruitEditList)
            {
                var recruit = recruitList.First(o => o.RecruitId == i.RecruitId);
                if (CheckPostStockLeft(_context, i.RecruitId, consume))
                {
                    i.Status = RecruitStatus.Induction;
                    i.FileRemarks = "offer自流转中自动入职";// 招聘流程记录显示
                    i.InductionTime = (recruit.InductionTime.Equals(DateTime.MinValue) || recruit.InductionTime.Equals(Constants.DefaultTime)) ? DateTime.Now : recruit.InductionTime;
                }
                else// todo:这里直接归档
                {
                    i.Status = RecruitStatus.FileAway;
                    i.FileRemarks = "offer自流转中交付数量为0";// 招聘流程记录显示
                    i.InvalidReason = "offer自流转中交付数量为0，直接归档";// 订单描述
                }

                i.UpdatedTime = DateTime.Now;
                i.StatusTime = DateTime.Now;

                #region 招聘流程记录表
                addRecord.Add(new Recruit_Record()
                {
                    Creator = i.HrId,
                    RecruitId = i.RecruitId,
                    Status = i.Status,
                });
                #endregion

                #region 反查真实人才库表修改用户等级
                var platformModel = _context.Talent_Platform.IgnoreQueryFilters().Where(o => o.SeekerId == i.SeekerId && o.HrId == i.HrId).FirstOrDefault();
                if (platformModel is not null)
                {
                    if (platformModel.Level != TalentPlatformLevel.合同用户)
                    {
                        platformModel.Level = i.Status switch
                        {
                            RecruitStatus.Induction => TalentPlatformLevel.入职用户,
                            RecruitStatus.FileAway => TalentPlatformLevel.归档用户,
                            _ => throw new Exception()
                        };
                    }
                }
                #endregion
                consume++;
            }

            _context.AddRange(addRecord);
            _context.SaveChanges();
            _logger.Info("AutomaticForOffer", "offer自流转", $"倒计时7天,1次/30秒，{ids.Count}处理完成，时间：{DateTime.Now}\r\n{string.Join(',', ids)}");
            await Task.Delay(10);
        }
        catch (Exception ex)
        {
            _logger.Error("offer状态自动处理出错", Tools.GetErrMsg(ex));
        }
    }

    /// <summary>
    /// offer自流转 - 7天不处理，前一天发消息提醒
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task AutomaticForOfferMessage(CancellationToken cancel)
    {
        if (cancel.IsCancellationRequested)
            return;

        using var _context = _contextFactory.CreateDbContext();
        DateTime calculationTime = DateTime.Now.AddDays(-6);
        string msgType = "offer";
        var sendedIds = MyRedis.Client.LRange(SubscriptionKey.AutomaticMessage + ":" + msgType + ":" + DateTime.Now.ToString("yyyy-MM-dd"), 0, -1) ?? new string[1];
        var list = _context.Recruit.Where(o => o.Status == RecruitStatus.Offer && o.StatusTime <= calculationTime && !sendedIds.Contains(o.RecruitId))
                .Select(o => new RecruitInfo
                {
                    RecruitId = o.RecruitId,
                    HrId = o.HrId,
                    HrName = o.User_Hr.NickName,
                    TeamProjectId = o.Post_Delivery.Post_Team.TeamProjectId,
                    TeamHrId = o.Post_Delivery.Post_Team.Project_Team.HrId,
                    TeamPostId = o.Post_Delivery.TeamPostId,
                    PostName = o.Post_Delivery.Post.Name,
                    SeekerName = o.User_Seeker.NickName,
                    Type = o.Post_Delivery.Post_Team.Project_Team.Type,
                    EventTime = o.StatusTime,
                    OrderId = o.Post_Bounty.Id
                }).Take(100).ToList();

        if (list.Count <= 0)
            return;

        RecordSendedRecruit(msgType, list.Select(s => s.RecruitId).ToList());

        AutoProjectSendMessage(list);
        await Task.Delay(10);
    }

    /// <summary>
    /// 将提醒记录缓存到redis
    /// </summary>
    /// <param name="list"></param>
    /// <exception cref="NotImplementedException"></exception>
    private void RecordSendedRecruit(string type, List<string?> list)
    {
        var timeStr = DateTime.Now.ToString("yyyy-MM-dd");
        MyRedis.Client.LPush(SubscriptionKey.AutomaticMessage + ":" + type + ":" + timeStr, list.ToArray());
    }

    /// <summary>
    /// 项目自流转 - 主创15天不上线，归档项目，下架所有职位
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task AutomaticForProject(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            //加锁
            var lockerKey = $"automaticforprojectlocker";
            using var locker = MyRedis.TryLock(lockerKey, 3600);
            if (locker == null)
                return;

            using var _context = _contextFactory.CreateDbContext();
            // 找到所有已上线项目且hr超过15天未登录，找到后归档
            // 这里注意只处理未到结束日期的项目，因为存在其他定时任务去处理，避免并发操作
            DateTime calculationTime = DateTime.Now.AddDays(-15);
            var list = _context.Project.Where(w => w.Status == ProjectStatus.已上线 && w.User_Hr.User_Extend.LoginTime < calculationTime && w.EndTime > DateTime.Now.Date)
                .Take(100).ToList();
            if (list.Count <= 0)
                return;
            _logger.Info("AutomaticForProject", "项目自流转", $"倒计时15天,1次/30秒，开始处理，时间：{DateTime.Now}");
            foreach (var item in list)
                item.Status = ProjectStatus.归档;
            _context.SaveChanges();
            await Task.Delay(10);// 保证数据库处理完
            _logger.Info("AutomaticForProject", "项目自流转", $"倒计时15天,1次/30秒，{list.Count}处理完成，时间：{DateTime.Now}\r\n{string.Join(',', list.Select(s => s.ProjectId).ToArray())}");

            // 项目状态变更消息
            MyRedis.Client.SAdd(SubscriptionKey.ProjectStatusChange, list.Select(s => new Sub_Project_StatusChange { ProjectId = s.ProjectId }).ToArray());
        }
        catch (Exception ex)
        {
            _logger.Error("项目自流转处理出错", Tools.GetErrMsg(ex));
        }
    }

    public class TestModel
    {
        public string? RecruitId { get; set; }
        public HrProjectType? Type { get; set; }
    }

    /// <summary>
    /// Hr初筛自流转 - 7天不处理，分润交付算入职，非分润算归档
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task AutomaticForHrScreen(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            //加锁
            var lockerKey = $"automaticforhrscreenlocker";
            using var locker = MyRedis.TryLock(lockerKey, 100);
            if (locker == null)
                return;

            using var _context = _contextFactory.CreateDbContext();
            DateTime calculationTime = DateTime.Now.AddDays(-7);
            // todo：使用匿名时，如果字段返回空，会报错
            var list = _context.Recruit.IgnoreQueryFilters().Where(o => o.Status == RecruitStatus.HrScreening && o.StatusTime < calculationTime)
                    .Select(o => new
                    {
                        o.RecruitId,
                        o.Post_Delivery.Post_Team.Project_Team.Type
                    }).Take(100).ToList();
            var ids = list.Select(s => s.RecruitId).ToList();
            if (ids.Count <= 0)
                return;
            _logger.Info("AutomaticForHrScreen", "Hr初筛自流转", $"倒计时7天,1次/30秒，开始处理，时间：{DateTime.Now}");
            var recruitList = _context.Recruit.Where(w => ids.Contains(w.RecruitId)).ToList();// 更新状态用,必须立即执行，否则到foreach之后会出现dbconnect被占用情况
            List<Recruit_Record> addRecord = new List<Recruit_Record>();
            int consume = 0;
            foreach (var i in recruitList)
            {
                // 入职前判断库存
                if (CheckPostStockLeft(_context, i.RecruitId, consume))
                {
                    i.Status = RecruitStatus.Induction;
                    i.FileRemarks = "Hr初筛自流转中自动入职";// 招聘流程记录显示
                    i.InductionTime = DateTime.Now;// todo:默认当前时间立即入职
                }
                else// todo:这里直接归档
                {
                    i.Status = RecruitStatus.FileAway;
                    i.FileRemarks = "Hr初筛自流转中交付数量为0";// 招聘流程记录显示
                    i.InvalidReason = "Hr初筛自流转中交付数量为0，直接归档";// 订单描述
                }


                i.StatusTime = DateTime.Now;
                i.UpdatedTime = DateTime.Now;
                #region 招聘流程记录表
                addRecord.Add(new Recruit_Record()
                {
                    Creator = i.HrId,
                    RecruitId = i.RecruitId,
                    Status = i.Status,
                    FileAwayRemarks = i.FileRemarks,
                });
                #endregion

                #region 反查真实人才库表修改用户等级
                var platformModel = _context.Talent_Platform.IgnoreQueryFilters().Where(o => o.SeekerId == i.SeekerId && o.HrId == i.HrId).FirstOrDefault();
                if (platformModel is not null)
                {
                    if (platformModel.Level != TalentPlatformLevel.合同用户)
                    {
                        platformModel.Level = i.Status switch
                        {
                            RecruitStatus.Induction => TalentPlatformLevel.入职用户,
                            RecruitStatus.FileAway => TalentPlatformLevel.归档用户,
                            _ => throw new Exception()
                        };
                    }
                }
                #endregion
                consume++;
            }
            _context.AddRange(addRecord);
            _context.SaveChanges();

            // 触发入职自流转进入正式成员
            //var formalRecruitList = _context.Recruit.Where(o => ids.Contains(o.RecruitId))
            //    .Select(o => o.RecruitId).ToList();

            _logger.Info("AutomaticForHrScreen", "Hr初筛自流转", $"倒计时7天,1次/30秒，{list.Count}处理完成，时间：{DateTime.Now}\r\n{string.Join(',', ids)}");
            await Task.Delay(10);// 保证数据库处理完
        }
        catch (Exception ex)
        {
            _logger.Error("Hr初筛自流转处理出错", Tools.GetErrMsg(ex));
        }
    }

    /// <summary>
    /// Hr初筛自流转 - 7天不处理，前一天发消息提醒
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task AutomaticForHrScreenMessage(CancellationToken cancel)
    {
        if (cancel.IsCancellationRequested)
            return;

        using var _context = _contextFactory.CreateDbContext();
        DateTime calculationTime = DateTime.Now.AddDays(-6);
        string msgType = "hrscreen";
        var sendedIds = MyRedis.Client.LRange(SubscriptionKey.AutomaticMessage + ":" + msgType + ":" + DateTime.Now.ToString("yyyy-MM-dd"), 0, -1) ?? new string[1];
        var list = _context.Recruit.Where(o => o.Status == RecruitStatus.HrScreening && o.StatusTime < calculationTime && !sendedIds.Contains(o.RecruitId))
                .Select(o => new RecruitInfo
                {
                    RecruitId = o.RecruitId,
                    HrId = o.HrId,
                    HrName = o.User_Hr.NickName,
                    TeamProjectId = o.Post_Delivery.Post_Team.TeamProjectId,
                    TeamHrId = o.Post_Delivery.Post_Team.Project_Team.HrId,
                    TeamPostId = o.Post_Delivery.TeamPostId,
                    PostName = o.Post_Delivery.Post.Name,
                    SeekerName = o.User_Seeker.NickName,
                    Type = o.Post_Delivery.Post_Team.Project_Team.Type,
                    EventTime = o.StatusTime,
                    OrderId = o.Post_Bounty.Id
                }).Take(100).ToList();

        if (list.Count <= 0)
            return;

        RecordSendedRecruit(msgType, list.Select(s => s.RecruitId).ToList());

        AutoProjectSendMessage(list);
        await Task.Delay(10);
    }

    /// <summary>
    /// 面试官筛选自流转 - 7天不处理，分润交付算入职，非分润算归档
    /// 20241008 填写反馈则退出自流转,存在待反馈才倒计时
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task AutomaticForInterviewerScreen(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            //加锁
            var lockerKey = $"automaticforinterviewerscreenlocker";
            using var locker = MyRedis.TryLock(lockerKey, 3600);
            if (locker == null)
                return;

            using var _context = _contextFactory.CreateDbContext();
            DateTime calculationTime = DateTime.Now.AddDays(-7);
            var list = _context.Recruit.IgnoreQueryFilters().Where(o => o.Status == RecruitStatus.InterviewerScreening
            && o.Recruit_Interviewer_Screen.Any(a => a.Status == RecruitInterviewerScreenStatus.NoFedBack)
            && o.StatusTime < calculationTime)
                    .Select(o => new
                    {
                        o.RecruitId,
                        o.Post_Delivery.Post_Team.Project_Team.Type
                    }).Take(100).ToList();
            var ids = list.Select(s => s.RecruitId).ToList();
            if (ids.Count <= 0)
                return;
            _logger.Info("AutomaticForInterviewerScreen", "面试官筛选自流转", $"倒计时7天,1次/30秒，开始处理，时间：{DateTime.Now}");
            var recruitList = _context.Recruit.Where(w => ids.Contains(w.RecruitId)).ToList();// 更新状态用,必须立即执行，否则到foreach之后会出现dbconnect被占用情况
            List<Recruit_Record> addRecord = new List<Recruit_Record>();
            int consume = 0;
            foreach (var i in recruitList)
            {
                // if (list.FirstOrDefault(f => f.RecruitId.Equals(i.RecruitId))?.Type == HrProjectType.协同)// 分润
                if (true)// 主创也分润了
                {
                    // 入职前判断库存
                    if (CheckPostStockLeft(_context, i.RecruitId, consume))
                    {
                        i.Status = RecruitStatus.Induction;
                        i.FileRemarks = "面试官筛选自流转中自动入职";// 招聘流程记录显示
                        i.InductionTime = DateTime.Now;// todo:默认当前时间立即入职
                    }
                    else// todo:这里直接归档
                    {
                        i.Status = RecruitStatus.FileAway;
                        i.FileRemarks = "面试官筛选自流转中交付数量为0";// 招聘流程记录显示
                        i.InvalidReason = "面试官筛选自流转中交付数量为0，直接归档";// 订单描述
                    }
                }
                // else
                // {
                //     i.Status = RecruitStatus.FileAway;
                //     i.FileRemarks = "面试官筛选自流转中自动归档";// 招聘流程记录显示
                // }


                i.StatusTime = DateTime.Now;
                i.UpdatedTime = DateTime.Now;
                #region 招聘流程记录表
                addRecord.Add(new Recruit_Record()
                {
                    Creator = i.HrId,
                    RecruitId = i.RecruitId,
                    Status = i.Status,
                    FileAwayRemarks = i.FileRemarks,
                });
                #endregion

                #region 反查真实人才库表修改用户等级
                var platformModel = _context.Talent_Platform.IgnoreQueryFilters().Where(o => o.SeekerId == i.SeekerId && o.HrId == i.HrId).FirstOrDefault();
                if (platformModel is not null)
                {
                    if (platformModel.Level != TalentPlatformLevel.合同用户)
                    {
                        platformModel.Level = i.Status switch
                        {
                            RecruitStatus.Induction => TalentPlatformLevel.入职用户,
                            RecruitStatus.FileAway => TalentPlatformLevel.归档用户,
                            _ => throw new Exception()
                        };
                    }
                }
                #endregion
                consume++;
            }
            _context.AddRange(addRecord);
            _context.SaveChanges();

            // 触发入职自流转进入正式成员
            //var formalRecruitList = _context.Recruit.Where(o => ids.Contains(o.RecruitId))
            //    .Select(o => o.RecruitId).ToList();

            _logger.Info("AutomaticForInterviewerScreen", "面试官初筛自流转", $"倒计时7天,1次/30秒，{list.Count}处理完成，时间：{DateTime.Now}\r\n{string.Join(',', ids)}");
            await Task.Delay(10);// 保证数据库处理完
        }
        catch (Exception ex)
        {
            _logger.Error("面试官筛选自流转处理出错", Tools.GetErrMsg(ex));
        }
    }

    /// <summary>
    /// 面试官初筛自流转 - 7天不处理，前一天发消息提醒
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task AutomaticForInterviewerScreenMessage(CancellationToken cancel)
    {
        if (cancel.IsCancellationRequested)
            return;

        using var _context = _contextFactory.CreateDbContext();
        DateTime calculationTime = DateTime.Now.AddDays(-6);
        string msgType = "interviewer";
        var sendedIds = MyRedis.Client.LRange(SubscriptionKey.AutomaticMessage + ":" + msgType + ":" + DateTime.Now.ToString("yyyy-MM-dd"), 0, -1) ?? new string[1];
        var list = _context.Recruit.Where(o => o.Status == RecruitStatus.InterviewerScreening && o.StatusTime < calculationTime && !sendedIds.Contains(o.RecruitId))
                .Select(o => new RecruitInfo
                {
                    RecruitId = o.RecruitId,
                    HrId = o.HrId,
                    HrName = o.User_Hr.NickName,
                    TeamProjectId = o.Post_Delivery.Post_Team.TeamProjectId,
                    TeamHrId = o.Post_Delivery.Post_Team.Project_Team.HrId,
                    TeamPostId = o.Post_Delivery.TeamPostId,
                    PostName = o.Post_Delivery.Post.Name,
                    SeekerName = o.User_Seeker.NickName,
                    Type = o.Post_Delivery.Post_Team.Project_Team.Type,
                    EventTime = o.StatusTime,
                    OrderId = o.Post_Bounty.Id
                }).Take(100).ToList();

        if (list.Count <= 0)
            return;

        RecordSendedRecruit(msgType, list.Select(s => s.RecruitId).ToList());

        AutoProjectSendMessage(list);
        await Task.Delay(10);
    }

    private static void AutoProjectSendMessage(List<RecruitInfo> list)
    {
        List<MsgNotifyModel> models = new List<MsgNotifyModel>();
        foreach (var item in list)
        {

            var type = MsgNotifyType.自流转归档提醒;
            if (!string.IsNullOrWhiteSpace(item.OrderId))// 交互分润
            {
                type = MsgNotifyType.自流转入职提醒;
            }
            // 主创消息
            var msg = new MsgNotifyModel
            {
                Type = type,
                EventTime = item.EventTime.AddDays(7),// hr初筛，面试官筛选，offer自流转都是7天，所以这里统一加7天，如果时间不一致，就不能在这里加
                UserId = item.HrId,
                Data = new MsgAutomatic
                {
                    OrderId = item.OrderId,
                    HrName = item.HrName,
                    PostName = item.PostName,
                    SeekerName = item.SeekerName,
                    IsSelfProj = true
                }
            };
            models.Add(msg);
            // 协同消息
            var msg_team = new MsgNotifyModel
            {
                Type = type,
                EventTime = item.EventTime.AddDays(7),
                UserId = item.TeamHrId,
                Data = new MsgAutomatic
                {
                    OrderId = item.OrderId,
                    HrName = item.HrName,
                    PostName = item.PostName,
                    SeekerName = item.SeekerName,
                    IsSelfProj = false
                }
            };
            models.Add(msg_team);
        }
        MsgHelper.SendDingdingNotify(models.ToArray());
    }

    /// <summary>
    /// 进入职员工表 - 暂时不处理
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task EnterInductionTable(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;
            await Task.Delay(10);
            using var _context = _contextFactory.CreateDbContext();
            _logger.Info("EnterInductionTable", "入职员工表", $"1次/5秒，开始处理，时间：{DateTime.Now}");
            //入职
            //var updateMember = _context2.Project_Member
            //.Where(x => x.ProjectId == task.TargetId && x.IdentityCard == item.IdentityCard)
            //.FirstOrDefault();

            //if (updateMember != null)
            //{
            //    groupStatus = TaskHandlingGroupStatus.重复;
            //    if (updateMember.Status != ProjectMemberStatus.入职)
            //        updateMember.InductionTimes++;
            //}
            //else
            //{
            //    updateMember = new Project_Member
            //    {
            //        ProjectId = task.TargetId!,
            //        IdentityCard = item.IdentityCard
            //    };
            //    _context2.Add(updateMember);
            //}
            //var idCardInfo = Tools.GetIdCardInfo(item.IdentityCard);
            //updateMember.Source = taskContent.Mode switch
            //{
            //    ProjectMemberImportEntryType.Excel模板 => ProjectMemberSource.Excel导入,
            //    ProjectMemberImportEntryType.数字诺亚 => ProjectMemberSource.数字诺亚,
            //    _ => ProjectMemberSource.手动添加
            //};
            //updateMember.Status = ProjectMemberStatus.入职;
            //updateMember.QuitStatus = ProjectMemberQuitStatus.未离职;
            //updateMember.QuitTime = null;
            //updateMember.IdentityCardType = item.IdentityCardType.Value;
            //updateMember.IdentityCardName = item.IdentityCardName;
            //updateMember.Mobile = item.Mobile;
            //updateMember.Birthday = idCardInfo?.Birthday;
            //updateMember.Sex = idCardInfo?.Sex;
            //updateMember.RegionId = idCardInfo?.RegionId ?? string.Empty;
            //updateMember.EmploymentMode = item.EmploymentMode.Value;
            //updateMember.ProbationMonth = item.ProbationMonth;
            //updateMember.PostName = item.PostName;
            //updateMember.Department = item.Department;
            //updateMember.EntryTime = DateOnly.FromDateTime(item.EntryTime.Value);
            //updateMember.EMail = item.EMail;
            //updateMember.UpdatedTime = DateTime.Now;

            //_context2.SaveChanges();
        }
        catch (Exception ex)
        {
            _logger.Error("入职员工表处理出错", Tools.GetErrMsg(ex));
        }
    }

    /// <summary>
    /// 面试，offer，入职操作，如果协同或主创渠道则验证库存是否大于0才能继续操作
    /// </summary>
    /// <param name="_context"></param>
    /// <param name="recruitId"></param>
    /// <param name="currentConsume">当前消耗：本此commit之前入职数量</param>
    /// <returns></returns>
    /// <exception cref="BadRequestException"></exception>
    public bool CheckPostStockLeft(StaffingContext _context, string recruitId, int currentConsume = 0)
    {
        var postInfo = (from a in _context.Recruit
                        join b in _context.Post_Delivery on a.DeliveryId equals b.DeliveryId
                        join c in _context.Post_Team on b.TeamPostId equals c.TeamPostId
                        join d in _context.Project_Team on c.TeamProjectId equals d.TeamProjectId
                        join e in _context.Post on c.PostId equals e.PostId
                        where a.RecruitId == recruitId
                        select new { b.PostId, e.LeftStock }).FirstOrDefault();
        if (postInfo != null && postInfo.LeftStock - currentConsume <= 0)
            return false;
        else
            return true;
    }

    /// <summary>
    /// 项目监控回执
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task ProjectCallBack(CancellationToken cancel)
    {
        while (!cancel.IsCancellationRequested)
        {
            string? msg = null;
            try
            {
                msg = MyRedis.Client.SPop<string?>(SubscriptionKey.ProjectCallBack);
                if (string.IsNullOrWhiteSpace(msg))
                {
                    await Task.Delay(500);
                    continue;
                }

                using var _context = _contextFactory.CreateDbContext();
                var xbbInfo = _context.Xbb_Contract.Where(x => !string.IsNullOrWhiteSpace(x.ProductId) && x.Code == msg).FirstOrDefault();// 这玩意儿也不知道有没有重复出现的，姑且认为没有

                if (xbbInfo == null)
                    continue;
                string oldStatus = xbbInfo.NkpStatus == null ? "空" : xbbInfo.NkpStatus.Value.GetDescription();
                xbbInfo.NkpStatus = xbbInfo.NkpStatus == null ? XbbNkpStatus.已上诺快聘 : xbbInfo.NkpStatus switch
                {
                    XbbNkpStatus.已上诺快聘 => XbbNkpStatus.已上诺快聘,
                    XbbNkpStatus.已上诺优考诺快聘 => XbbNkpStatus.已上诺优考诺快聘,
                    XbbNkpStatus.已上诺优考 => XbbNkpStatus.已上诺优考诺快聘,
                    XbbNkpStatus.等待上线 => XbbNkpStatus.已上诺快聘,
                    XbbNkpStatus.补充合同 => XbbNkpStatus.已上诺快聘,
                    XbbNkpStatus.无招聘需求 => XbbNkpStatus.已上诺快聘,
                    _ => throw new Exception($"未知状态：{xbbInfo.NkpStatus}")
                };
                string newStatus = xbbInfo.NkpStatus.Value.GetDescription();

                _context.SaveChanges();

                // 日志记录
                _logger.Info($"项目监控自动回执:{xbbInfo.Id}", oldStatus, newStatus);
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.ProjectCallBack}消息处理出错", Tools.GetErrMsg(e), (msg ?? string.Empty).ToString());
                await Task.Delay(500);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 合同同步时项目监控回执
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task ContractUpdateProjectCallBack(CancellationToken cancel)
    {
        while (!cancel.IsCancellationRequested)
        {
            string? msg = null;
            try
            {
                msg = MyRedis.Client.SPop<string?>(SubscriptionKey.ContractUpdatedProjectCallBack);
                if (string.IsNullOrWhiteSpace(msg))
                {
                    await Task.Delay(500);
                    continue;
                }

                using var _context = _contextFactory.CreateDbContext();
                var xbbInfo = _context.Xbb_Contract.Where(x => !string.IsNullOrWhiteSpace(x.ProductId) && x.Code == msg).FirstOrDefault();// 这玩意儿也不知道有没有重复出现的，姑且认为没有

                if (xbbInfo == null)
                {
                    // 重试，说明数据库操作还没完成
                    var resp = _retryWithBackoff.RetryAction(new Func<Xbb_Contract>(() =>
                    {
                        var xbbContract = new Xbb_Contract();
                        xbbContract = _context.Xbb_Contract.FirstOrDefault(f => !string.IsNullOrWhiteSpace(f.ProductId) && f.Code == msg) ?? xbbContract;
                        return xbbContract;
                    })
                    );
                    if (resp == null)
                        continue;
                    xbbInfo = resp;
                }

                if (xbbInfo.NkpStatus == XbbNkpStatus.已上诺快聘)
                    continue;

                if (_context.Project.Any(a => a.XbbContractNo == msg))
                {
                    string oldStatus = xbbInfo.NkpStatus == null ? "空" : xbbInfo.NkpStatus.Value.GetDescription();
                    xbbInfo.NkpStatus = xbbInfo.NkpStatus == null ? XbbNkpStatus.已上诺快聘 : xbbInfo.NkpStatus switch
                    {
                        XbbNkpStatus.已上诺快聘 => XbbNkpStatus.已上诺快聘,
                        XbbNkpStatus.已上诺优考诺快聘 => XbbNkpStatus.已上诺优考诺快聘,
                        XbbNkpStatus.已上诺优考 => XbbNkpStatus.已上诺优考诺快聘,
                        XbbNkpStatus.等待上线 => XbbNkpStatus.已上诺快聘,
                        XbbNkpStatus.补充合同 => XbbNkpStatus.已上诺快聘,
                        XbbNkpStatus.无招聘需求 => XbbNkpStatus.已上诺快聘,
                        _ => throw new Exception($"未知状态：{xbbInfo.NkpStatus}")
                    };
                    string newStatus = xbbInfo.NkpStatus.Value.GetDescription();
                    _context.SaveChanges();
                    // 日志记录
                    _logger.Info($"项目监控自动回执-合同同步:{xbbInfo.Id}", oldStatus, newStatus);
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.ContractUpdatedProjectCallBack}消息处理出错", Tools.GetErrMsg(e), (msg ?? string.Empty).ToString());
                await Task.Delay(500);
            }

            await Task.Delay(10);
        }
    }
}

