﻿using Config.CommonModel;
using Staffing.Model.Hr.WxGroup;

namespace Staffing.Core.Interfaces.Hr;

public interface IWxGroupService
{
    /// <summary>
    /// 查看群
    /// </summary>
    /// <param name="groupId"></param>
    EmptyResponse ViewGroup(long groupId);

    /// <summary>
    /// 社群列表
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    ListResponse GetWxGroupList(SearchRequest request);

    /// <summary>
    /// 社群统计数据
    /// </summary>
    /// <returns></returns>
    SummaryInfoModel GetGroupSummary();

    /// <summary>
    /// 获取二级数据
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    dynamic GetTwoLevelDatas(DataType type);
}
