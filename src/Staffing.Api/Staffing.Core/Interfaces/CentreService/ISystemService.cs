﻿using Config.CommonModel;
using Staffing.Model.CentreService.System;

namespace Staffing.Core.Interfaces.CentreService;

public interface ISystemService
{
    GetSystemAppTreeResponse GetSystemAppTree(GetSystemAppTree model);
    SystemAppInfo GetSystemApp(string? id);
    SendSmsResponse SendSms(SendSms model);

    /// <summary>
    /// 发送自定义内容的短信
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    /// <exception cref="BadRequestException"></exception>
    Task<EmptyResponse> SendSmsCustomContent(SendSmsCustom model);

    /// <summary>
    /// 获取自定义内容短信
    /// </summary>
    GetSmsCustomResponse GetSendSmsCustomList(GetSmsCustom model);
    AddSystemAppResponse AddSystemApp(AddSystemApp model);
    EmptyResponse UpdateSystemApp(UpdateSystemApp model);
    EmptyResponse DeleteSystemApp(DeleteSystemApp model);
    EmptyResponse UpdateSystemAppStatus(UpdateSystemAppStatus model);
    Task<EmptyResponse> AddUserSms(AddUserSms model);
    GetWithdrawRecordResponse GetWithdrawRecord(GetWithdrawRecord model);
    Task<EmptyResponse> WithdrawChangeStatus(WithdrawChangeStatus model);


    /// <summary>
    /// 保存广告
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse AdvertSave(AdvertSave model);
    
    /// <summary>
    /// 分页获取广告列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    AdvertPageResponse AdvertGetPageList(QueryRequest model);
    
    /// <summary>
    /// 修改广告上下架状态
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse AdvertUpdateState(AdvertUpdateState model);
    AdminLoginResponse AdminLogin(AdminLogin model);
    GetRenCaiConfigResponse GetRenCaiConfig(GetRenCaiConfig model);
    EmptyResponse AddRenCaiConfig(AddRenCaiConfig model);
    EmptyResponse DeleteRenCaiConfig(DeleteRenCaiConfig model);
}