﻿using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;

namespace Staffing.Model.Hr.Post;

public class GetHallPost : QueryRequest
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 职位名称或编码
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 职位类别
    /// </summary>
    public int? Category { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public List<string>? Citys { get; set; }
    /// <summary>
    /// 新地区Id（单选）
    /// </summary>
    public string? City {  get; set; }
    /// <summary>
    /// 结算方式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }
    /// <summary>
    /// 薪资要求(最低)
    /// </summary>
    public int? MinSalary { get; set; }

    /// <summary>
    /// 薪资要求(最高)
    /// </summary>
    public int? MaxSalary { get; set; }
    /// <summary>
    /// 求职者学历
    /// </summary>
    public EducationType? SeekerEducation { get; set; }
    /// <summary>
    /// 面试开始时间
    /// </summary>
    public DateTime? InterviewStartTime { get; set; }
    /// <summary>
    /// 面试结束时间
    /// </summary>
    public DateTime? InterviewEndTime { get; set; }
    /// <summary>
    /// 本周上新
    /// </summary>
    public bool IsNew { get; set; }

    /// <summary>
    /// 结算过
    /// </summary>
    public bool IsSettled { get; set; }

    /// <summary>
    /// 简历免审
    /// </summary>
    public bool? IsResumeExempt { get; set; }

    /// <summary>
    /// 能约面试（可以约面试的岗位）
    /// </summary>
    public bool IsInterview { get; set; }
    /// <summary>
    /// 只看未接单
    /// </summary>
    public IsOnlyAcceptOrder? IsOnlyUnAcceptOrder { get; set; }
}

public class GetHallPostResponse : QueryResponse
{
    public List<GetHallPostDetail> Rows { get; set; } = default!;
}

public class PostCrowdsourceReq
{
    /// <summary>
    /// 协同岗位id
    /// </summary>
    public string TeamPostId { get; set; } = default!;
    
    /// <summary> 
    /// 类别
    /// </summary>
    public ProjectTeamConfigType ModeType { get; set; }
    
    /// <summary>
    /// 接收人id
    /// </summary>
    public string? ReceiverId { get; set; }
    /// <summary>
    /// 0开启 1关闭
    /// </summary>
    public int? Status { get; set; }
}

public class PostCrowdsourceResp
{
    
    /// <summary>
    /// 协同岗位id
    /// </summary>
    public string TeamPostId { get; set; } = default!;
    
    /// <summary>
    /// 协调项目表id
    /// </summary>
    public string TeamProjectId { get; set; } = default!;
    
    /// <summary>
    /// 类别
    /// </summary>
    public ProjectTeamConfigType ModeType { get; set; }
    
    /// <summary>
    /// 接收人id
    /// </summary>
    public string? ReceiverId { get; set; }
    /// <summary>
    /// 0开启 1关闭
    /// </summary>
    public int? Status { get; set; }
}

public class GetHallPostDetail : GetPostDetail
{
    
    /// <summary>
    /// 面试看板
    /// </summary>
    public PostRecentInterviews InterviewDashboard { get; set; } = new PostRecentInterviews();

    /// <summary>
    /// 平台直营
    /// </summary>
    public bool IsPlatform { get; set; }

    /// <summary>
    /// 已协同
    /// </summary>
    public bool HasShare { get; set; }

    /// <summary>
    /// 项目接单数量
    /// </summary>
    public int ProjectShareNum { get; set; }

    /// <summary>
    /// 职位接单数量
    /// </summary>
    public int PostShareNum { get; set; }
    
    /// <summary>
    /// 交付金额（线索+邀面）
    /// </summary>
    public decimal? DeliveryBounty { get; set; }
    ///// <summary>
    ///// 小程序岗位上架状态
    ///// </summary>
    //public bool Show { get; set; }
}

public class PostSync
{
    /// <summary>
    /// 大厅的职位Id
    /// </summary>
    public string? PostId { get; set; }
}


#region 职位审核列表
public class GetAuditPostResponse : QueryResponse
{
    public List<GetAuditPostDetail> Rows { get; set; } = default!;

    /// <summary>
    /// 全部
    /// </summary>
    public int AllNum { get; set; }

    /// <summary>
    /// 待审核
    /// </summary>
    public int AuditNum { get; set; }

    /// <summary>
    /// 审核通过 - 发布中
    /// </summary>
    public int OkNum { get; set; }

    /// <summary>
    /// 审核驳回
    /// </summary>
    public int FailedNum { get; set; }

    /// <summary>
    /// 已关闭
    /// </summary>
    public int CloseNum { get; set; }
}

public class GetAuditPostDetail : GetPostDetail
{
    /// <summary>
    /// 审批驳回原因
    /// </summary>
    public string? RejectReason { get; set; }

    /// <summary>
    /// 审批人userid
    /// </summary>
    public string? AuditUserId { get; set; }

    /// <summary>
    /// 审批人名称
    /// </summary>
    public string? AuditUserName { get; set; }

    /// <summary>
    /// 审批时间
    /// </summary>
    public DateTime? AuditTime { get; set; }

    /// <summary>
    /// 诺亚项目编码
    /// </summary>
    public string? NuoId { get; set; }
}

public class GetAuditPost : QueryRequest
{
    /// <summary>
    /// 职位状态
    /// </summary>
    public PostStatus? Status { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 职位名称或编码
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 职位类别
    /// </summary>
    public int? Category { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public List<string>? Citys { get; set; }
}

public class AuditRequest
{
    /// <summary>
    /// 职位id
    /// </summary>
    public string PostId { get; set; } = default!;

    /// <summary>
    /// 审批状态
    /// </summary>
    public PostStatus Status { get; set; }

    /// <summary>
    /// 审批驳回原因
    /// </summary>
    public string? RejectReason { get; set; }
}
#endregion

public class PostPaymentTypeResponse
{
    /// <summary>
    /// 交付类型枚举
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }

    /// <summary>
    /// 交付类型名称
    /// </summary>
    public string? PaymentNodeName { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? UnitName { get; set; }

    /// <summary>
    /// 占位符提示
    /// </summary>
    public string? PlaceHolder { get; set; }
}