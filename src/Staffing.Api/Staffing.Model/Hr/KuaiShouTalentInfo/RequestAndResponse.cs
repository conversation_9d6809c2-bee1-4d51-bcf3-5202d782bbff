﻿using System.ComponentModel;
using Config.CommonModel;
using Config.Enums;

namespace Staffing.Model.Hr.KuaiShouTalentInfo;

public class RequestAndResponse
{
}

/// <summary>
/// Search 请求
/// </summary>
public class TalentInfoPageListRequest : QueryRequest
{
    /// <summary>
    /// tab标签页
    /// </summary>
    public TabStatus TabStatus { get; set; } = TabStatus.待我跟进;

    /// <summary>
    /// 搜索（姓名、手机号、职位、跟进人）
    /// </summary>
    public string Search { get; set; } = string.Empty;

    /// <summary>
    /// 回访状态
    /// </summary>
    public ReturnVisit? ReturnVisit { get; set; }

    /// <summary>
    /// 是否有效线索:1-有效，0-无效
    /// </summary>
    public ResultStatus? Result { get; set; }

    /// <summary>
    /// 是否绑定诺快聘职位:0-未绑定，1-已绑定
    /// </summary>
    public BindJob? BindJob { get; set; }

    /// <summary>
    /// 是否推送至项目:0-未推送，1-已推送
    /// </summary>
    public KuaishouStatus? Status { get; set; }

    /// <summary>
    /// 报名开始时间
    /// </summary>
    public string? StartTime { get; set; }

    /// <summary>
    /// 报名结束时间
    /// </summary>
    public string? EndTime { get; set; }

    /// <summary>
    /// 最小年龄
    /// </summary>
    public int? MinAge { get; set; }

    /// <summary>
    /// 最大年龄
    /// </summary>
    public int? MaxAge { get; set; }
}

public class RelationRequest
{
    /// <summary>
    /// 简历主键ids -  ApplicationId
    /// </summary>
    public List<string> ApplicationIds { get; set; } = default!;
    /// <summary>
    /// 转同事HrId
    /// </summary>
    public string? HrId { get; set; }
    /// <summary>
    /// 回访情况描述
    /// </summary>
    public string? VisitMemo { get; set; }
    /// <summary>
    /// 是否有效：0-无效，1-有效
    /// </summary>
    public ResultStatus? Result { get; set; }
    /// <summary>
    /// 协同职位id
    /// </summary>
    public string? TeamPostId { get; set; }
    /// <summary>
    /// 简历是否推送：0-未推送，1-已推送
    /// </summary>
    public KuaishouStatus? Status { get; set; }
    /// <summary>
    /// 是否等会再打：0-不是，1-是
    /// </summary>
    public WaitToPhone? WaitToPhone { get; set; }
    /// <summary>
    /// 无效简历类型
    /// </summary>
    public string? InvalidType { get; set; }
}

/// <summary>
/// 列表返回
/// </summary>
public class TalentInfoPageListResponse : QueryResponse
{
    public List<KuaishouTalentInfo> Rows { get; set; } = default!;
    public TabNum TabNum { get; set; } = new TabNum();
}

/// <summary>
/// 简历详情页返回
/// </summary>
public class TalentInfoEditResponse
{
    /// <summary>
    /// 主键
    /// </summary>
    public string ApplicationId { get; set; } = default!;
    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }
    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }
    /// <summary>
    /// 性别
    /// </summary>
    public string? GenderName { get; set; }
    /// <summary>
    /// 手机号
    /// </summary>
    public string? Phone { get; set; }
    /// <summary>
    /// 来源渠道（简历来源，直播、短视频等）
    /// </summary>
    public string? ChannelName { get; set; }
    /// <summary>
    /// 意向城市
    /// </summary>
    public string? IntentionCity { get; set; }
    /// <summary>
    /// 意向工作
    /// </summary>
    public string? IntentionJob { get; set; }
    /// <summary>
    /// 学历
    /// </summary>
    public string? Education { get; set; }
    /// <summary>
    /// 回访顾问
    /// </summary>
    public string? HrName { get; set; }
    public string? HrId { get; set; }
    /// <summary>
    /// 顾问头像
    /// </summary>
    public string? Avatar { get; set; }
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; }
    /// <summary>
    /// 回访情况
    /// </summary>
    public string? VisitMemo { get; set; }
    /// <summary>
    /// 是否有效：0-无效，1-有效
    /// </summary>
    public ResultStatus? Result { get; set; }
    /// <summary>
    /// 简历是否推送：0-未推送，1-已推送
    /// </summary>
    public KuaishouStatus? Status { get; set; }
    /// <summary>
    /// 是否等会再打：0-不是，1-是
    /// </summary>
    public WaitToPhone? WaitToPhone { get; set; }
    /// <summary>
    /// 无效简历类型
    /// </summary>
    public string? InvalidType { get; set; }
    /// <summary>
    /// 招聘流程Id
    /// </summary>
    public string? RecruitId { get; set; }
    /// <summary>
    /// 基本信息 - name:value
    /// </summary>
    public List<EnumInfo> BaseInfos { get; set; } = default!;
    /// <summary>
    /// 简历信息反馈 - name:value
    /// </summary>
    public List<EnumInfo> ResumeFeedBack { get; set; } = default!;
}

/// <summary>
/// 快手简历信息
/// </summary>
public class KuaishouTalentInfo
{
    /// <summary>
    /// 主键id
    /// </summary>
    public string ApplicationId { get; set; } = default!;
    /// <summary>
    /// 快手昵称
    /// </summary>
    public string? NickName { get; set; }
    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }
    /// <summary>
    /// 性别名称
    /// </summary>
    public string? GenderName { get; set; }
    /// <summary>
    /// 手机号
    /// </summary>
    public string? Phone { get; set; }
    /// <summary>
    /// 年龄
    /// </summary>
    public int Age { get; set; }
    /// <summary>
    /// 快手职位id
    /// </summary>
    public string? JobId { get; set; }
    /// <summary>
    /// 快手投递职位
    /// </summary>
    public string? JobName { get; set; }
    /// <summary>
    /// 最终推送职位
    /// </summary>
    public string? SendTeamPostId { get; set; }
    /// <summary>
    /// 最终推送职位名称
    /// </summary>
    public string? SendTeamPostName { get; set; }
    /// <summary>
    /// 最终推送职位postid
    /// </summary>
    public string? SendPostId { get; set; }
    /// <summary>
    /// 诺快聘职位id(协同)
    /// </summary>
    public string? TeamPostId { get; set; }
    /// <summary>
    /// 诺快聘职位id
    /// </summary>
    public string? PostId { get; set; }
    /// <summary>
    /// 诺快聘职位
    /// </summary>
    public string? PostName { get; set; }
    /// <summary>
    /// 快手来源
    /// </summary>
    public string? ChannelName { get; set; }
    /// <summary>
    /// 报名时间
    /// </summary>
    public DateTime ApplyTime { get; set; }
    /// <summary>
    /// 学历
    /// </summary>
    public string? EducationName {get;set;}
    /// <summary>
    /// 回访信息
    /// </summary>
    public KuaishouTalentHrRelation? KuaishouTalentHrRelation { get; set; }
}

/// <summary>
/// 标签页总数统计
/// </summary>
public class TabNum
{
    /// <summary>
    /// 待我跟进数量
    /// </summary>
    public int myNum { get; set; } = 0;
    /// <summary>
    /// 待同事跟进数量
    /// </summary>
    public int TeamWorkerNum { get; set; } = 0;
    /// <summary>
    /// 等会再打
    /// </summary>
    public int WaitToPhoneNum { get; set; } = 0;
    /// <summary>
    /// 无效简历
    /// </summary>
    public int InvalidNum { get; set; } = 0;
}

/// <summary>
/// 回访信息
/// </summary>
public class KuaishouTalentHrRelation
{
    /// <summary>
    /// 跟进人id
    /// </summary>
    public string? HrId { get; set; }
    /// <summary>
    /// 跟进人
    /// </summary>
    public string? HrName { get; set; }
    /// <summary>
    /// 回访情况
    /// </summary>
    public string? VisitMemo { get; set; }
    /// <summary>
    /// 是否回访：0-未回访，1-已回访
    /// </summary>
    public ReturnVisit ReturnVisit { get; set; } = ReturnVisit.未回访;
    /// <summary>
    /// 是否有效：0-无效，1-有效
    /// </summary>
    public ResultStatus Result { get; set; } = ResultStatus.有效;
    /// <summary>
    /// 简历是否推送：0-未推送，1-已推送
    /// </summary>
    public KuaishouStatus Status { get; set; } = KuaishouStatus.未推送;
    /// <summary>
    /// 是否等会再打：0-不是，1-是
    /// </summary>
    public WaitToPhone WaitToPhone { get; set; } = WaitToPhone.不是;

    /// <summary>
    /// 无效简历类型
    /// </summary>
    public string? InvalidType { get; set; }
}

/// <summary>
/// 同事选择框
/// </summary>
public class TeamHrResponse
{
    /// <summary>
    /// 同事HrId
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 同事姓名
    /// </summary>
    public string HrName { get; set; } = default!;
}

/// <summary>
/// 项目
/// </summary>
public class ProjectResponse
{
    public string HrId { get; set; } = default!;

    public string ProjectId { get; set; } = default!;

    public string ProjectName { get; set; } = default!;
}

/// <summary>
/// 职位选择框查询
/// </summary>
public class PostTeamPageListRequest : QueryRequest
{
    public List<string>? ApplicationIds { get; set; }
    public string? Search { get; set; }
}

/// <summary>
/// 职位选择框返回信息
/// </summary>
public class PostTeamInfoPageListResponse : QueryResponse
{
    public List<PostTeamInfo> Rows { get; set; } = default!;
}

public class PostTeamInfo
{
    /// <summary>
    /// 主创名称 - 项目经理
    /// </summary>
    public string HrName { get; set; } = default!;

    /// <summary>
    /// 主创电话
    /// </summary>
    public string Phone { get; set; } = default!;

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; } = default!;

    /// <summary>
    /// 项目编码
    /// </summary>
    public string ProjectCode { get; set; } = default!;

    /// <summary>
    /// 项目创建时间
    /// </summary>
    public DateTime? ProjectCreateTime { get; set; }

    /// <summary>
    /// 职位Id
    /// </summary>
    public string TeamPostId { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 招聘人数
    /// </summary>
    public int RecruitNumber { get; set; } = default!;

    /// <summary>
    /// 剩余库存
    /// </summary>
    public int? LeftStock { get; set; }

    /// <summary>
    /// 工作性质 - 全职/兼职
    /// </summary>
    public string? WorkNature { get; set; }
    public PostWorkNature WorkNatureType { get; set; }

    /// <summary>
    /// 毕业年份
    /// </summary>
    public int? GraduationYear { get; set; }

    /// <summary>
    /// 职位描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 职位类别
    /// </summary>
    public string? CategoryName { get; set; }

    /// <summary>
    /// 薪资要求(最低)
    /// </summary>
    public int MinSalary { get; set; }

    /// <summary>
    /// 薪资要求(最高)
    /// </summary>
    public int MaxSalary { get; set; }

    /// <summary>
    /// 几薪
    /// </summary>
    public int Salary { get; set; }
    public PostSalaryType SalaryType { get; set; }

    /// <summary>
    /// 薪酬描述
    /// </summary>
    public string SalaryName { get; set; } = string.Empty;

    /// <summary>
    /// 学历
    /// </summary>
    public string? Education { get; set; }

    /// <summary>
    /// 地区id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 城市
    /// </summary>
    public string? City { get; set; }

    // /// <summary>
    // /// 坐标
    // /// </summary>
    // public Point? Location { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 福利
    /// </summary>
    public List<WelfareModel>? Welfare { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string? Sex { get; set; }

    /// <summary>
    /// 最小年龄
    /// </summary>
    public int? MinAge { get; set; }

    /// <summary>
    /// 最大年龄
    /// </summary>
    public int? MaxAge { get; set; }

    public bool IsFirst { get; set; } = false;
}

public class EditKuaiShouTalent
{
    /// <summary>
    /// 快招工主表id
    /// </summary>
    public string? ApplicationId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public KuaiShouSex? GenderCode { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public KuaiShouEducation? Education { get; set; }
}

public enum KuaiShouEducation
{
    初中及以下 = 1,
    [Description("中专/技校/职高")]
    中专 = 2,
    高中 = 3,
    大专 = 4,
    本科及以上 = 5
}

public enum KuaiShouSex
{
    男 = 1,
    女 = 2
}