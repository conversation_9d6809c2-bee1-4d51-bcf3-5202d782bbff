﻿using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Staffing.Entity;

namespace Infrastructure.CommonService;

/// <summary>
/// 虚拟人才库通用服务
/// </summary>
[Service(ServiceLifetime.Transient)]
public class CommonVirtualService
{
    private IDbContextFactory<StaffingContext> _staffingContextFactory;

    /// <summary>
    /// 注入
    /// </summary>
    public CommonVirtualService(IDbContextFactory<StaffingContext> staffingContextFactory)
    {
        _staffingContextFactory = staffingContextFactory;
    }

    /// <summary>
    /// 计算虚拟人才库简历完整度
    /// </summary>
    /// <param name="virtualId"></param>
    /// <returns></returns>
    public int CountVirtualResumeScore(string virtualId)
    {
        using var context = _staffingContextFactory.CreateDbContext();
        var countModel = context.Talent_Virtual.Where(o => o.Id == virtualId).Select(o => new
        {
            Name = string.IsNullOrEmpty(o.Name) ? 0 : 4,
            Sex = 4,
            Birthday = o.Birthday == Convert.ToDateTime("1970-01-01") ? 0 : 4,
            Location = string.IsNullOrEmpty(o.Location) ? 0 : 4,
            SelfEvaluation = string.IsNullOrEmpty(o.SelfEvaluation) ? 0 : 10,
            HeadPortrait = string.IsNullOrEmpty(o.HeadPortrait) ? 0 : 4,
            SkillAnalysis = o.SkillAnalysis == null ? 0 : 10,

            IndustryName = string.IsNullOrEmpty(o.Talent_Virtual_Hope.IndustryName) ? 0 : 4,
            HopeCity = string.IsNullOrEmpty(o.Talent_Virtual_Hope.HopeCity) ? 0 : 4,
            PostName = string.IsNullOrEmpty(o.Talent_Virtual_Hope.PostName) ? 0 : 4,
            Salary = (o.Talent_Virtual_Hope.MaxSalary == 0 || o.Talent_Virtual_Hope.MinSalary == 0) ? 0 : 4,

            WorkCompanyName = o.Talent_Virtual_Work.Any(o => !string.IsNullOrEmpty(o.CompanyName)) ? 4 : 0,
            WorkIndustryName = o.Talent_Virtual_Work.Any(o => !string.IsNullOrEmpty(o.IndustryName)) ? 4 : 0,
            WorkPostName = o.Talent_Virtual_Work.Any(o => !string.IsNullOrEmpty(o.PostName)) ? 4 : 0,
            WorkCompanyRemarks = o.Talent_Virtual_Work.Any(o => !string.IsNullOrEmpty(o.CompanyRemarks)) ? 4 : 0,
            WorkTime = o.Talent_Virtual_Work.Any(o => o.StartTime != Convert.ToDateTime("1970-01-01")) ? 4 : 0,

            EduEducation = 4,
            EduSchoolName = o.Talent_Virtual_Edu.Any(o => !string.IsNullOrEmpty(o.SchoolName)) ? 4 : 0,
            EduMajorName = o.Talent_Virtual_Edu.Any(o => !string.IsNullOrEmpty(o.MajorName)) ? 4 : 0,
            EduTime = o.Talent_Virtual_Edu.Any(o => o.StartTime != Convert.ToDateTime("1970-01-01")) ? 4 : 0,
            EduFull = 2
        }).FirstOrDefault();

        int count = 0;

        if (countModel != null)
        {
            count = countModel.Name + countModel.Sex + countModel.Birthday + countModel.Location + countModel.SelfEvaluation + countModel.HeadPortrait + countModel.SkillAnalysis
                + countModel.IndustryName + countModel.HopeCity + countModel.PostName + countModel.Salary + countModel.WorkCompanyName + countModel.WorkIndustryName + countModel.WorkPostName
                + countModel.WorkCompanyRemarks + countModel.WorkTime + countModel.EduEducation + countModel.EduSchoolName + countModel.EduMajorName + countModel.EduTime + countModel.EduFull;
        }

        var talentVirtual = context.Talent_Virtual.Where(o => o.Id == virtualId).FirstOrDefault();
        if (talentVirtual != null)
        {
            talentVirtual.Perfection = count;
            context.SaveChanges();
        }

        return count;
    }
}

