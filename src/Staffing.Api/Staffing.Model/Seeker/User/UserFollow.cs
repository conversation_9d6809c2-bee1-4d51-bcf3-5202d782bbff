﻿using Config.CommonModel;
using Config.Enums;

namespace Staffing.Model.Seeker.User;

public class GetFollowIndustry : QueryRequest
{

}

public class GetFollowIndustryResponse : QueryResponse
{
    public List<GetFollowIndustryInfo> Rows { get; set; } = new List<GetFollowIndustryInfo>();
}

public class GetFollowIndustryInfo
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 项目行业
    /// </summary>
    public ProjectIndustry? Industry { get; set; }

    /// <summary>
    /// 项目行业名称
    /// </summary>
    public string? IndustryName { get; set; }

    /// <summary>
    /// 岗位数量
    /// </summary>
    public int Post { get; set; }

    /// <summary>
    /// hrId
    /// </summary>
    public string? HrId { get; set; }

        /// <summary>
    /// hr名称
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// hr头像
    /// </summary>
    public string? HrAvatar { get; set; }

    /// <summary>
    /// 是否是当前顾问
    /// </summary>
    public bool IsCurrent { get; set; }
}

public class UpdateFollowIndustry
{
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? AdviserId { get; set; }

    /// <summary>
    /// true=关注，false=取关
    /// </summary>
    public bool Follow { get; set; }

    /// <summary>
    /// 项目行业，如果是关注必填
    /// </summary>
    public ProjectIndustry? Industry { get; set; }

}