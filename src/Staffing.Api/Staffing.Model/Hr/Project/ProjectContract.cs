﻿using System.ComponentModel.DataAnnotations;
using Config.CommonModel;
using Config.CommonModel.Business;
using Config.CommonModel.Ndn;
using Config.Enums;
using Staffing.Entity.Staffing;

namespace Staffing.Model.Hr.Project;

public class GetNoahContract
{
    /// <summary>
    /// 合同编号
    /// </summary>
    public string? ContractCode { get; set; }
}

public class UpdateProjectContract
{
    /// <summary>
    /// 合同编号
    /// </summary>
    public string? ContractCode { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public ProjectContractSource? Source { get; set; }

    /// <summary>
    /// 合同开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 合同截止时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

public class UpdateProjectContractResponse
{
    /// <summary>
    /// 合同Id
    /// </summary>
    public string? ContractId { get; set; }
}

public class GetProjectContract
{
    /// <summary>
    /// 合同Id
    /// </summary>
    public string? ContractId { get; set; }
}

public class GetProjectContractList : QueryRequest
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 合同编号
    /// </summary>
    public string? ContractNo { get; set; }

    /// <summary>
    /// 合同状态
    /// </summary>
    public ProjectContractStatus? Status { get; set; }
}

public class GetProjectContractListResponse : QueryResponse
{
    /// <summary>
    /// 合同列表
    /// </summary>
    public List<ProjectContractInfo>? Rows { get; set; }
}

public class GetProjectContractResponse : ProjectContractInfo
{

}

public class SetDefaultProjectContract
{
    /// <summary>
    /// 合同Id
    /// </summary>
    public string? ContractId { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }
}

public class HasDefaultContractResponse
{
    /// <summary>
    /// 是否存在默认合同
    /// </summary>
    public bool HasDefault { get; set; }
}