﻿using Newtonsoft.Json;

namespace Config.CommonModel.SCRMBeschannels
{
    public class BesChannelsBaseOutput<T>
    {
        public int code { get; set; }
        public string? msg { get; set; }
        public T data { get; set; } = default(T)!;
    }
    /// <summary>
    /// 获取token接口返回对象
    /// </summary>
    public class BesChannelsTokenOutput
    {
        public string? access_token { get; set; }
        public int expires { get; set; }
    }
    /// <summary>
    /// 分类列表接口返回对象
    /// </summary>
    public class BesChannelsBaseCate
    {
        /// <summary>
        /// 二级分类id
        /// </summary>
        [JsonProperty("id")]
        public string? Id { get; set; }

        /// <summary>
        /// 二级分类名称
        /// </summary>
        [JsonProperty("name")]
        public string? Name { get; set; }

        /// <summary>
        /// 父级分类id
        /// </summary>
        [JsonProperty("pid")]
        public string? Pid { get; set; }
    }
    public class BesChannelsCateOutput : BesChannelsBaseCate
    {
        [JsonProperty("childrenInfo")]
        public List<BesChannelsBaseCate>? ChildrenInfo { get; set; }

        [JsonProperty("children")]
        public List<BesChannelsBaseCate>? Children
        {
            get
            {
                return ChildrenInfo;
            }
        }
    }

    /// <summary>
    /// 会议列表接口返回对象
    /// </summary>
    public class BesChannelsMeetingOutput
    {
        /// <summary>
        /// 会议id
        /// </summary>
        [JsonProperty("id")]
        public string? Id { get; set; }

        /// <summary>
        /// 会议名称
        /// </summary>
        [JsonProperty("meeting_name")]
        public string? MeetingName { get; set; }

        /// <summary>
        /// 直播类型，1=展示互动，2=微吼，3=保利威，4=目睹
        /// </summary>
        [JsonProperty("meeting_type")]
        public string? MeetingType { get; set; }

        /// <summary>
        /// 会议类型，1=线上，2=线下，3=线上线下
        /// </summary>
        [JsonProperty("meeting_form")]
        public string? MeetingForm { get; set; }

        /// <summary>
        /// 是否允许分享，1=允许，2=不允许
        /// </summary>
        [JsonProperty("is_sharing")]
        public string? IsSharing { get; set; }

        /// <summary>
        /// 直播地址
        /// </summary>
        [JsonProperty("meeting_url")]
        public string? MeetingUrl { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [JsonProperty("start_time")]
        public string? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [JsonProperty("end_time")]
        public string? EndTime { get; set; }

        /// <summary>
        /// 分类id
        /// </summary>
        [JsonProperty("category")]
        public string[]? Category { get; set; }

        /// <summary>
        /// 封面图
        /// </summary>
        [JsonProperty("cover")]
        public string? Cover { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [JsonProperty("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// 来源
        /// </summary>
        [JsonProperty("lead_source")]
        public bool LeadSource { get; set; }

        /// <summary>
        /// 会议当前状态
        /// </summary>
        [JsonProperty("status")]
        public string? Status { get; set; }

        /// <summary>
        /// 详情页链接
        /// </summary>
        [JsonProperty("link_url")]
        public string? LinkUrl { get; set; }

        /// <summary>
        /// 回放地址
        /// </summary>
        [JsonProperty("playback_url")]
        public string? PlaybackUrl { get; set; }

        /// <summary>
        /// 下载文档链接
        /// </summary>
        [JsonProperty("playback_file")]
        public string? PlaybackFile { get; set; }
    }

    /// <summary>
    /// 会议报名 返回对象
    /// </summary>
    public class MemberSubmitOutput
    {
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("data_id")]
        public string? DataID { get; set; }
    }

    /// <summary>
    /// 根据会员id或会员手机号获取会员详细信息
    /// </summary>
    public class MemberInfoOutput
    {
        /// <summary>
        /// 又封装了一层
        /// </summary>
        [JsonProperty("data")]
        public MemberInfoOutputData? Data { get; set; }
    }
    /// <summary>
    /// 根据会员id或会员手机号获取会员详细信息
    /// </summary>
    public class MemberInfoOutputData
    {
        /// <summary>
        /// 会员id
        /// </summary>
        [JsonProperty("id")]
        public string? ID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("main_id")]
        public string? MainID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("wx_system_user_id")]
        public string? WXSystemUserID { get; set; }
        /// <summary>
        /// openid
        /// </summary>
        [JsonProperty("open_id")]
        public string? OpenID { get; set; }
        /// <summary>
        /// 手机号
        /// </summary>
        [JsonProperty("mobile")]
        public string? Mobile { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        [JsonProperty("name")]
        public string? Name { get; set; }
        /// <summary>
        /// 性别
        /// *1=男，2=女，其他=位置
        /// </summary>
        [JsonProperty("sex")]
        public string? Sex { get; set; }

        /// <summary>
        /// 身份证号码
        /// </summary>
        [JsonProperty("identification")]
        public string? Identification { get; set; }
        /// <summary>
        /// 会员码
        /// </summary>
        [JsonProperty("sn")]
        public string? SN { get; set; }
        /// <summary>
        /// 邮箱
        /// </summary>
        [JsonProperty("email")]
        public string? Email { get; set; }
        /// <summary>
        /// 当前剩余积分
        /// </summary>
        [JsonProperty("points")]
        public string? Points { get; set; }
        /// <summary>
        /// 累计积分
        /// </summary>
        [JsonProperty("points_total")]
        public string? PointsTotal { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        [JsonProperty("address")]
        public string? Address { get; set; }
        /// <summary>
        /// 省份id
        /// </summary>
        [JsonProperty("province")]
        public string? Province { get; set; }
        /// <summary>
        /// 市id
        /// </summary>
        [JsonProperty("city")]
        public string? City { get; set; }
        /// <summary>
        /// 区id
        /// </summary>
        [JsonProperty("region")]
        public string? Region { get; set; }
        /// <summary>
        /// 生日
        /// </summary>
        [JsonProperty("birthday")]
        public string? Birthday { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("custom_9")]
        public string? Custom_9 { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("is_del")]
        public string? IsDel { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("is_frozen")]
        public string? IsFrozen { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("identity")]
        public string? Identity { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("source")]
        public string? Source { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        [JsonProperty("create_time")]
        public string? CreateTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("update_time")]
        public string? UpdateTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("outlet")]
        public string? Outlet { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("level_id")]
        public string? LevelID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("level")]
        public string? Level { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("unionid")]
        public string? UnionID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("bind_type")]
        public string? BindType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("frozen_status")]
        public string? FrozenStatus { get; set; }

        /// <summary>
        /// 自定义字段列表
        /// </summary>
        [JsonProperty("field")]
        public Dictionary<string, MemberInfoCustomFields>? CustomFields { get; set; }
    }

    /// <summary>
    /// 根据会员id或会员手机号获取会员详细信息-->自定义字段
    /// </summary>
    public class MemberInfoCustomFields
    {
        /// <summary>
        /// 自定义字段显示名称
        /// </summary>
        [JsonProperty("name")]
        public string? Name { get; set; }
        /// <summary>
        /// 自定义字段值
        /// </summary>
        [JsonProperty("value")]
        public string? Value { get; set; }
        /// <summary>
        /// 自定义字段名称
        /// </summary>
        [JsonProperty("field_name")]
        public string? FieldName { get; set; }
    }

    /// <summary>
    /// 会员管理-获取基础信息
    /// </summary>
    public class MemberBasicOutput
    {
        /// <summary>
        /// 会员唯一标识
        /// </summary>
        [JsonProperty("id")]
        public string? ID { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        [JsonProperty("name")]
        public string? Name { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        [JsonProperty("sex")]
        public string? Sex { get; set; }
        /// <summary>
        /// 邮箱
        /// </summary>
        [JsonProperty("email")]
        public string? Email { get; set; }
        /// <summary>
        /// openid
        /// </summary>
        [JsonProperty("open_id")]
        public string? OpenID { get; set; }

        /// <summary>
        /// 剩余积分
        /// </summary>
        [JsonProperty("points")]
        public string? Points { get; set; }
        /// <summary>
        /// 累计积分
        /// </summary>
        [JsonProperty("points_total")]
        public string? PointsTotal { get; set; }

        /// <summary>
        /// 省
        /// </summary>
        [JsonProperty("province")]
        public string? Province { get; set; }
        /// <summary>
        /// 市
        /// </summary>
        [JsonProperty("city")]
        public string? City { get; set; }

        /// <summary>
        /// 区
        /// </summary>
        [JsonProperty("region")]
        public string? Region { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        [JsonProperty("Address")]
        public string? address { get; set; }
        /// <summary>
        /// 手机号
        /// </summary>
        [JsonProperty("mobile")]
        public string? Mobile { get; set; }

        /// <summary>
        /// 生日
        /// </summary>
        [JsonProperty("birthday")]
        public string? Birthday { get; set; }

        /// <summary>
        /// 身份证
        /// </summary>
        [JsonProperty("identification")]
        public string? Identification { get; set; }

        /// <summary>
        /// 等级id
        /// </summary>
        [JsonProperty("level_id")]
        public string? LevelID { get; set; }

        /// <summary>
        /// 等级名称
        /// </summary>
        [JsonProperty("level_name")]
        public string? LevelName { get; set; }

        /// <summary>
        /// 是否冻结:1=正常，2=冻结
        /// </summary>
        [JsonProperty("is_frozen")]
        public string? IsFrozen { get; set; }

        /// <summary>
        /// 注册时间,纯数字时间戳
        /// </summary>
        [JsonProperty("create_time")]
        public string? CreateTime { get; set; }
    }

    public enum OutputEnum
    {
        成功 = 0,
        手机号未注册 = 91015,
        失败 = -1
    }
}
