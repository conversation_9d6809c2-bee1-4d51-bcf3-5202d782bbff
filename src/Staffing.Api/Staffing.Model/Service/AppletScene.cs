﻿using Config.Enums;

namespace Staffing.Model.Service;

public class GetAppletScene
{
    /// <summary>
    /// Scene
    /// </summary>
    public string? Scene { get; set; }
}

public class GetAppletSceneResponse
{
    /// <summary>
    /// 协同的职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? AdviserId { get; set; }

    /// <summary>
    /// 渠道Id
    /// </summary>
    public string? ChannelId { get; set; }

    /// <summary>
    /// 分享人Id
    /// </summary>
    public string? ShareUserId { get; set; }

    /// <summary>
    /// 分享类型
    /// </summary>
    public AppletShareType Type { get; set; }
    
    /// <summary>
    /// HrId
    /// </summary>
    public string? HrId { get; set; }
    /// <summary>
    /// 证书id
    /// </summary>
    public string? CertificateId { get; set; }
}