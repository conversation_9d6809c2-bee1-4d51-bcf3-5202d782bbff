using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;

namespace Staffing.Model.Seeker.Post;

public class SeekerGetProjects : QueryRequest
{
    /// <summary>
    /// 项目行业
    /// </summary>
    public ProjectIndustry? ProjectIndustry { get; set; }

    /// <summary>
    /// 地区Id数组
    /// </summary>
    public List<string>? Citys { get; set; }
}

public class SeekerGetProjectsResponse : QueryResponse
{
    public List<SeekerGetProjectInfo> Rows { get; set; } = new List<SeekerGetProjectInfo>();
}

public class SeekerGetProjectInfo : GetAgentEntDetail
{
    public string? TeamProjectId { get; set; }

    /// <summary>
    /// 热招职位
    /// </summary>
    public string? HotPost { get; set; }
}

public class SeekerGetProjectIndustry
{
    /// <summary>
    /// 行业类别
    /// </summary>
    public ProjectIndustry? ProjectIndustry { get; set; }
}

public class SeekerGetProjectIndustryInfo
{
    /// <summary>
    /// 行业类别
    /// </summary>
    public ProjectIndustry ProjectIndustry { get; set; }

    /// <summary>
    /// 行业类别名称
    /// </summary>
    public string? ProjectIndustryName { get; set; }

    /// <summary>
    /// 是否已关注
    /// </summary>
    public bool Follow { get; set; }

    /// <summary>
    /// 岗位数量
    /// </summary>
    public int Post { get; set; }

    /// <summary>
    /// 浏览量
    /// </summary>
    public int Visit { get; set; }

    /// <summary>
    /// 头像数组
    /// </summary>
    public List<string>? TalentAvatars { get; set; }
}