-- ----------------------------
-- 项目调研表
-- ----------------------------
DROP TABLE IF EXISTS `project_survey`;
CREATE TABLE `project_survey` (
  `Id` varchar(18) NOT NULL,
  `ProjectId` varchar(18) NOT NULL COMMENT '项目ID',
  `Content` TEXT COMMENT '内容',
  `UpdatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  `CreatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_project_survey_ProjectId` (`ProjectId`),
  KEY `IX_project_survey_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目调研表';


-- ----------------------------
-- 职位面试配置表
-- ----------------------------
DROP TABLE IF EXISTS `post_interview_config`;
CREATE TABLE `post_interview_config` (
  `Id` varchar(18) NOT NULL,
  `PostId` varchar(18) NOT NULL COMMENT '职位ID',
  `InterviewerId` varchar(18) COMMENT '面试官ID',
  `Status` INT NOT NULL DEFAULT 0 COMMENT '状态',
  `InterviewMode` INT NOT NULL DEFAULT 0 COMMENT '面试方式',
  `Address` varchar(1024) COMMENT '地址',
  `RegionId` varchar(18) NULL COMMENT '地区ID',
  `Location` point NOT NULL DEFAULT (point(0,0)),
  `ContactName` varchar(64) COMMENT '联络人姓名',
  `ContactPhone` varchar(32) COMMENT '联络人电话',
  `AdvanceDays` INT NOT NULL DEFAULT 0 COMMENT '可提前几天预约',
  `Remark` TEXT COMMENT '备注',
  `UpdatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  `CreatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_post_interview_config_PostId` (`PostId`),
  KEY `IX_post_interview_config_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职位面试配置表';


-- ----------------------------
-- 职位面试日期表
-- ----------------------------
DROP TABLE IF EXISTS `post_interivew_date`;
CREATE TABLE `post_interivew_date` (
  `Id` varchar(18) NOT NULL,
  `PostId` varchar(18) NOT NULL COMMENT '职位ID',
  `Status` INT NOT NULL DEFAULT 0 COMMENT '状态',
  `Num` INT NOT NULL DEFAULT 0 COMMENT '预约人数',
  `Time` datetime(3) NOT NULL DEFAULT NOW(3),
  `CreatedTime` datetime(3) NOT NULL DEFAULT NOW(3),
  PRIMARY KEY (`Id`),
  KEY `IX_post_interivew_date_PostId` (`PostId`),
  KEY `IX_post_interivew_date_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职位面试日期表';



ALTER TABLE project ADD `IsResumeExempt` BIT(1) NOT NULL DEFAULT b'0' COMMENT '简历是否免审';
ALTER TABLE project ADD `ContractType` INT NULL COMMENT '签约方式';
ALTER TABLE post_extend ADD `RecentDeliveryRate` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '最近交付率';
ALTER TABLE post_extend ADD `HistoryBillingRate` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '历史计费率';
-- ALTER TABLE project_interviewer ADD `RegionId` varchar(18) COMMENT '地区编码';
-- ALTER TABLE project_interviewer ADD `Location` point NULL COMMENT '坐标';
-- UPDATE project_interviewer SET Location = ST_GeomFromText('POINT(0.0 0.0)');
-- ALTER TABLE `project_interviewer` MODIFY COLUMN `Location` POINT NOT NULL DEFAULT (point(0.0,0.0));
-- ALTER TABLE post_interview_config ADD `InterviewerId` varchar(18) COMMENT '面试官ID';