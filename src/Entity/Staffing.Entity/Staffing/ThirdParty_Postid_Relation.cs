﻿using Config.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Staffing.Entity.Staffing
{
    [Table("thirdparty_postid_relation")]
    public class ThirdParty_Postid_Relation
    {
        [Key]
        public string Id { get; set; } = EntityTools.SnowflakeId();
        public string? PostId {  get; set; }//诺快聘岗位id
        public string? ThirdPostId { get; set;}//职多多岗位id
        public PostStatus Status { get; set; }//岗位状态  0:关闭, 1:发布中, 2:待审核
        public DateTime? CreatedTime { get; set; } = DateTime.Now;
        public DateTime? UpdatedTime { get; set;} = DateTime.Now;
        public PostSouce Source {  get; set; }//岗位来源  0:诺快聘, 1:职多多， 2、58魔方
        public FanfeiStatus FanfeiStatus { get; set; }//返费是否变化  0:不变, 1:改变
    }
}
