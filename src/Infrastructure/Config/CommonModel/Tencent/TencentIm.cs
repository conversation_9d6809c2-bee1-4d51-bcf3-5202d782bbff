﻿using Config.Enums;

namespace Config.CommonModel.Tencent;

public class ImMsgRead
{
    /// <summary>
    /// 对方ImId
    /// </summary>
    public string? ToImId { get; set; }
}


public class ImMsgReadFull : ImMsgRead
{
    /// <summary>
    /// 要设置已读的imid
    /// </summary>
    public string? ImId { get; set; }
}

public class ImMsgReadResponse
{
    /// <summary>
    /// 对方读消息时间
    /// </summary>
    public long? ReversalTime { get; set; }
}

public class ImBaseResponse
{
    /// <summary>
    /// 请求处理的结果，OK 表示处理成功，FAIL 表示失败
    /// </summary>
    public string? ActionStatus { get; set; }

    /// <summary>
    /// 错误码，0表示成功，非0表示失败
    /// </summary>
    public int ErrorCode { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorInfo { get; set; }
}

public class ImBAccountTag
{
    /// <summary>
    /// 标签
    /// </summary>
    public string? Tag { get; set; }

    /// <summary>
    /// 值
    /// </summary>
    public object? Value { get; set; }
}

//注册Im账号
public class CreateImAccount
{
    /// <summary>
    /// 必填	用户名，长度不超过32字节
    /// </summary>
    public string? Identifier { get; set; }

    /// <summary>
    /// 选填	用户昵称
    /// </summary>
    public string? Nick { get; set; }

    /// <summary>
    /// 选填	用户头像 URL
    /// </summary>
    public string? FaceUrl { get; set; }
}

//获取用户属性
public class SetImAccount
{
    /// <summary>
    /// 需要设置该 UserID 的资料
    /// </summary>
    public string? From_Account { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string? NickName { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public OccupationType? Occupation { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }
}

public class GetImAccount
{
    /// <summary>
    /// 用户Id数组
    /// </summary>
    public List<string>? To_Account { get; set; }

    /// <summary>
    /// 标签数组，不用填
    /// </summary>
    public List<string>? TagList { get; set; }
}

public class GetImAccountResponse : ImBaseResponse
{
    /// <summary>
    /// 用户Id数组
    /// </summary>
    public List<GetImAccountUserProfile>? UserProfileItem { get; set; }
}

public class GetImAccountUserProfile
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? To_Account { get; set; }

    /// <summary>
    /// 属性
    /// </summary>
    public List<ImBAccountTag>? ProfileItem { get; set; }
}

public class GetImUserSigResponse
{
    /// <summary>
    /// 签名
    /// </summary>
    public string? UserSig { get; set; }

    /// <summary>
    /// Im账号Id
    /// </summary>
    public string? ImId { get; set; }

    /// <summary>
    /// 有效期（秒）
    /// </summary>
    public int Expire { get; set; }
}

public class ImNickNameJson
{
    /// <summary>
    /// 名字
    /// </summary>
    public string? n { get; set; }

    /// <summary>
    /// 企业
    /// </summary>
    public string? e { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? p { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public int? s { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public int? v { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public int? b { get; set; }
}

public class GetImUserAccount
{
    /// <summary>
    /// UserID List
    /// </summary>
    public List<string>? To_Account { get; set; }
}
public class QueryImUserStatus : ImBaseResponse
{
    public List<ImUserStatus>? QueryResult { get; set; }
}

public class ImUserStatus
{
    /// <summary>
    /// 用户的 UserID
    /// </summary>
    public string? To_Account { get; set; }
    /// <summary>
    /// 用户状态 1.前台运行状态（Online）2.后台运行状态（PushOnline）3.未登录状态（Offline）
    /// </summary>
    public string? Status { get; set; }
}
public class GetC2CUnreadMsgNumResponse : ImBaseResponse
{
    /// <summary>
    /// 单聊消息总未读数
    /// </summary>
    public int AllC2CUnreadMsgNum { get; set; }
}


public class GetMessageList
{
    /// <summary>
    /// 会话其中一方的 UserID，以该 UserID 的角度去查询消息。同一个会话，分别以会话双方的角度去查询消息，结果可能会不一样，请参考本接口的接口说明
    /// </summary>
    public string? Operator_Account { get; set; }

    /// <summary>
    /// 会话的另一方 UserID
    /// </summary>
    public string? Peer_Account { get; set; }

    /// <summary>
    /// 请求的消息条数
    /// </summary>
    public int? MaxCnt { get; set; }

    /// <summary>
    /// 请求的消息时间范围的最小值
    /// </summary>
    public int MinTime { get; set; }

    /// <summary>
    /// 请求的消息时间范围的最大值
    /// </summary>
    public int MaxTime { get; set; }

    /// <summary>
    /// 上一次拉取到的最后一条消息的 MsgKey，续拉时需要填该字段，填写方法见上方
    /// </summary>
    public string? LastMsgKey { get; set; }
}

public class GetMessageListResponse : ImBaseResponse
{
    /// <summary>
    /// 是否全部拉取，0表示未全部拉取，需要续拉，1表示已全部拉取
    /// </summary>
    public int Complete { get; set; }

    /// <summary>
    /// 本次拉取到的消息条数
    /// </summary>
    public int MsgCnt { get; set; }

    /// <summary>
    /// 本次拉取到的消息里的最后一条消息的时间
    /// </summary>
    public int LastMsgTime { get; set; }

    /// <summary>
    /// 本次拉取到的消息里的最后一条消息的标识
    /// </summary>
    public string? LastMsgKey { get; set; }

    public List<ImMessageInfo> MsgList { get; set; } = new List<ImMessageInfo>();
}

public class ImMessageInfo
{
    public string? From_Account { get; set; }

    public string? To_Account { get; set; }

    public long MsgSeq { get; set; }

    public long MsgRandom { get; set; }

    public long MsgTimeStamp { get; set; }

    public string? CloudCustomData { get; set; }

    public List<ImMessageBody>? MsgBody { get; set; }

    /// <summary>
    /// 该条消息的属性，0表示正常消息，8表示被撤回的消息
    /// </summary>
    public int MsgFlagBits { get; set; }

    /// <summary>
    /// 标识该条消息，可用于 REST API 撤回单聊消息
    /// </summary>
    public string? MsgKey { get; set; }
}

public class ImMessageBody
{
    public string? MsgType { get; set; }

    public ImMessageBodyContent? MsgContent { get; set; }
}

public class ImMessageBodyContent
{
    public string? Text { get; set; }
}

public class ImSetMsgRead
{
    /// <summary>
    /// 进行消息已读的用户 UserId
    /// </summary>
    public string? Report_Account { get; set; }

    /// <summary>
    /// 进行消息已读的单聊会话的另一方用户 UserId
    /// </summary>
    public string? Peer_Account { get; set; }

    /// <summary>
    /// 时间戳（秒），该时间戳之前的消息全部已读。若不填，则取当前时间戳
    /// </summary>
    public string? MsgReadTime { get; set; }
}

/// <summary>
/// 用户在线状态
/// </summary>
public class ImUserOnline
{
    public string? ImId { get; set; }

    /// <summary>
    /// 在线状态（1=在线）
    /// </summary>
    public int? Active { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? Time { get; set; }
}

public class ImSendMsgInfo
{
    public int? SyncOtherMachine {  get; set; }////1：把消息同步到 From_Account 在线终端和漫游上 2：消息不同步至 From_Account 3：消息不同步至 To_Account 若不填写默认情况下会将消息存 From_Account 漫游 选填
    public string? From_Account { get; set; }//发送者ID 选填
    public string? To_Account { get; set; }//接收者ID 必填
    public long? MsgSeq {  get; set; }//消息序列号（32位无符号整数），后台会根据该字段去重及进行同秒内消息的排序。若不填该字段，则由后台填入随机数 选填
    public long? MsgRandom {  get; set; }//消息随机数（32位无符号整数），后台用于同一秒内的消息去重。请确保该字段填的是随机数 必填
    public List<SendImMsgBody>? MsgBody { get; set; }//消息内容 必填
    public string? CloudCustomData {  get; set; }//消息自定义数据（云端保存，会发送到对端，程序卸载重装后还能拉取到） 选填
    public int? SupportMessageExtension {  get; set; }// 该条消息是否支持消息扩展：0为不支持 1为支持 选填
}