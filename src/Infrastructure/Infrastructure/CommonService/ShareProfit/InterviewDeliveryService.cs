﻿// using Config.CommonModel.Recruit;
// using Config.CommonModel.ThirdTalentInfo;
// using Config.Enums;

// namespace Infrastructure.CommonService.ShareProfit;

// /// <summary>
// /// 到面交付场景
// /// </summary>
// public class InterviewDeliveryService : CommonDeliveryService
// {
//     public InterviewDeliveryService(ProjectPostInfo projectPostInfo, RecruitInfos recruit, CommonPostOrder orderService)
//         : base(projectPostInfo, recruit, orderService)
//     {
//     }

//     public override void Interview(string orderId)
//     {
//         // 减库存
//         _orderService.PostStockOut(orderId);
//     }

//     public override void Offer(string orderId)
//     {
//         // 不安排面试，直接点offer，会不会默认生成一条面试安排记录？ -- 如果当前在hr初筛，直接发offer不会生成面试安排记录
//         // 再次确认是不是只要点了offer，不管是不是安排过面试，都直接交付成功？ -- 2023-07-06 15:03 确认是交付成功（产品/测试/开发）
//         if (_projectPostInfo.Status == Config.Enums.BountyStatus.交付中)
//         {
//             // 减库存
//             _orderService.PostStockOut(orderId);
//             // 交付成功
//             _orderService.PaySuccess(orderId);
//         }
//     }

//     public override void Induction(string orderId)
//     {
//         if (_projectPostInfo.Status == Config.Enums.BountyStatus.交付中)
//         {
//             // 减库存
//             _orderService.PostStockOut(orderId);
//             // 交付成功
//             _orderService.PaySuccess(orderId);
//         }
//     }

//     public override void FileAway(string orderId)
//     {
//         if (_projectPostInfo.Status == Config.Enums.BountyStatus.交付中)
//         {
//             if(_recruit.FileAwayType == RecruitFileAway.Invalid)
//             {
//                 // 交付失败
//                 _orderService.PayFailed(orderId, _recruit.InvalidReason ?? "无效线索归档");
//                 // 加库存，能不能加成功要看是否减过库存，比如：hr初筛环节直接归档，此时会交付失败但是不会加库存，因为根本没减库存
//                 _orderService.PostStockIn(orderId);
//             }
//             // 安排面试之前点击“归档”：(Hr初筛，面试官筛选) 交付失败，安排面试后点击"归档"继续倒计时 0927
//             else if(_recruit.LastStatus == Config.Enums.RecruitStatus.HrScreening || _recruit.LastStatus == Config.Enums.RecruitStatus.InterviewerScreening)
//             {
//                 // 交付失败
//                 _orderService.PayFailed(orderId, _recruit.InvalidReason ?? "到面交付 - 交付中,安排面试之前归档");
//                 // 加库存，能不能加成功要看是否减过库存，比如：hr初筛环节直接归档，此时会交付失败但是不会加库存，因为根本没减库存
//                 _orderService.PostStockIn(orderId);
//             }
//         }
//     }
// }

// /**
//  * 问题：
//  * 
//  * 1.到面交付场景，如果求职者线下都进入了面试环节，但是主创hr没有在线上(诺快聘系统)安排面试，而是直接在“hr初筛”或者“面试官筛选”阶段点了“归档”
//  * 或者“无效简历”，是不是bug？相当于线上不交付，但是线下其实已经交付成功了，因为求职者到面了。 -- 存在且合理，可以申请售后
//  * 
//  * 2.协同hr没办法在线上招聘流程里知道求职者是否已经到面过，除非打电话或者通过其他方式跟求职者沟通，如果协同hr通过跟求职者沟通，发现确实到面但是“交付失败”，该如何申诉处理？ -- 售后，之后版本解决
//  */
