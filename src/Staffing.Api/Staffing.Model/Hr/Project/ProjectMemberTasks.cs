﻿using Config.CommonModel.Tasks;

namespace Staffing.Model.Hr.Project;


// public class GetMyTasks : QueryRequest
// {

// }

// public class GetMyTasksResponse : QueryResponse
// {
//     public List<MyTaskInfo> Rows { get; set; } = new List<MyTaskInfo>();
// }

// public class MyTaskInfo
// {
//     /// <summary>
//     /// 主键id
//     /// </summary>
//     public string? TaskId { get; set; }

//     /// <summary>
//     /// 名称
//     /// </summary>
//     public string? Name { get; set; } = string.Empty;

//     /// <summary>
//     /// 任务类型
//     /// </summary>
//     public TaskHandlingType? Type { get; set; }

//     /// <summary>
//     /// 任务状态
//     /// </summary>
//     public TaskHandlingStatus? Status { get; set; }

//     /// <summary>
//     /// 结果
//     /// </summary>
//     public TaskHandlingResult? Result { get; set; }

//     /// <summary>
//     /// 结果文本
//     /// </summary>
//     public string? ResultText { get; set; }

//     /// <summary>
//     /// 任务结束时间
//     /// </summary>
//     public DateTime? EndTime { get; set; }

//     //任务创建时间
//     public DateTime? CreatedTime { get; set; }
// }

// public class GetMyTaskDetails : QueryRequest
// {
//     [JsonIgnore]
//     public string? TaskId { get; set; }
// }

// public class GetMyTaskDetailsResponse : QueryResponse
// {
//     public List<MyTaskDetailInfo> Rows { get; set; } = new List<MyTaskDetailInfo>();
// }

// public class MyTaskDetailInfo
// {
//     public string? Id { get; set; }

//     /// <summary>
//     /// 任务Id
//     /// </summary>
//     public string? TaskId { get; set; }

//     /// <summary>
//     /// 内容
//     /// </summary>
//     [JsonIgnore]
//     public string? Content { get; set; }

//     /// <summary>
//     /// 结果
//     /// </summary>
//     public TaskHandlingResult Result { get; set; }

//     /// <summary>
//     /// 结果文本
//     /// </summary>
//     public string? ResultText { get; set; }

//     public DateTime CreatedTime { get; set; } = DateTime.Now;
// }

public class MyTaskContent : ImportEntry
{
    
}