
using Config.Enums;
using Magicodes.ExporterAndImporter.Core;

namespace Config.CommonModel.Tasks;


public class ImportEntryContent
{
    /// <summary>
    /// 导入方式
    /// </summary>
    public ProjectMemberImportEntryType Mode { get; set; } = ProjectMemberImportEntryType.Excel模板;

    /// <summary>
    /// 文件地址(excel)
    /// </summary>
    public string? FileUrl { get; set; }

    /// <summary>
    /// 数字诺亚项目编号
    /// </summary>
    public string? NoahProjectNo { get; set; }
}

/// <summary>
/// 入职导入
/// </summary>
public class ImportEntry
{
    /// <summary>
    /// 姓名
    /// </summary>
    [ImporterHeader(Name = "姓名")]
    public string? IdentityCardName { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    [ImporterHeader(Name = "手机号")]
    public string? Mobile { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    [ImporterHeader(Name = "证件类型")]
    public string? IdentityCardTypeName { get; set; }

    /// <summary>
    /// 证件号码
    /// </summary>
    [ImporterHeader(Name = "证件号码")]
    public string? IdentityCard { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    [ImporterHeader(Name = "邮箱")]
    public string? EMail { get; set; }

    /// <summary>
    /// 用工形式名称
    /// </summary>
    [ImporterHeader(Name = "用工形式")]
    public string? EmploymentModeName { get; set; }

    /// <summary>
    /// 入职时间
    /// </summary>
    [ImporterHeader(Name = "入职时间")]
    public DateTime? EntryTime { get; set; }

    /// <summary>
    /// 试用期（月）
    /// </summary>
    [ImporterHeader(Name = "试用期（月）")]
    public int ProbationMonth { get; set; }

    /// <summary>
    /// 入职部门
    /// </summary>
    [ImporterHeader(Name = "入职部门")]
    public string? Department { get; set; }

    /// <summary>
    /// 入职岗位
    /// </summary>
    [ImporterHeader(Name = "入职岗位")]
    public string? PostName { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    public IdCardType? IdentityCardType { get; set; }

    /// <summary>
    /// 用工形式
    /// </summary>
    public ProjectMemberEmploymentMode? EmploymentMode { get; set; }
}