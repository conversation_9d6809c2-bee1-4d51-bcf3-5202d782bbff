namespace Staffing.Model.Hr.Dashboard;

public class GetStaffData
{
    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? BeginDate { get; set; }

    /// <summary>
    /// 截止日期
    /// </summary>
    public DateTime? EndDate { get; set; }

    public string? Key { get; set; }
}

public class GetStaffDataResponse
{
    public List<GetStaffDataInfo> Rows { get; set; } = new List<GetStaffDataInfo>();
    public object? Str { get; set; }
}

public class GetStaffDataInfo
{
    public string? UserId { get; set; }

    /// <summary>
    /// 顾问姓名
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// 在线项目
    /// </summary>
    public int OnlineProject { get; set; }

    /// <summary>
    /// 集团协同项目数量
    /// </summary>
    public int GroupShareProject { get; set; }

    /// <summary>
    /// 在线职位
    /// </summary>
    public int OnlinePost { get; set; }

    /// <summary>
    /// 集团协同职位数量
    /// </summary>
    public int GroupSharePost { get; set; }

    /// <summary>
    /// 协同岗位数量
    /// </summary>
    public int TeamPosts { get; set; }

    /// <summary>
    /// 报名次数
    /// </summary>
    public int DeliveryNum { get; set; }

    /// <summary>
    /// 协同报名数量
    /// </summary>
    public int TeamDeliveryNum { get; set; }

    /// <summary>
    /// 协同入职数量
    /// </summary>
    public int TeamEntryNum { get; set; }

    /// <summary>
    /// 人才库数量
    /// </summary>
    public int Talent { get; set; }

    /// <summary>
    /// 简历人才库数量
    /// </summary>
    public int VirtualTalent { get; set; }
}