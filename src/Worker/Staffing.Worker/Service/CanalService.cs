using CanalSharp.Connections;
using CanalSharp.Protocol;
using Config;
using Config.CommonModel.Business;
using Config.Enums;
using FreeRedis;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Infrastructure.NoahCommon;
using Infrastructure.Proxy;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Infrastructure.CommonRepository.ZhaoPinYun;

namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class CanalService
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _logger;
    private readonly ParseResume _parseResume;
    private readonly CommonDicService _commonDicService;
    private readonly CommonVirtualService _commonVirtualService;
    private readonly WeChatHelper _weChatHelper;
    private readonly TencentImHelper _tencentImHelper;
    private readonly CacheHelper _cacheHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly CommonEsService _commonEsService;
    private ConfigManager _config;
    private readonly CommonPostService _commonPostService;
    private readonly CommonProjectService _commonProjectService;
    private readonly NuoPinApi _nuoPinApi;
    private readonly ZPYunRepository _zpyunRepository;
    public CanalService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger, NuoPinApi nuoPinApi,
    ParseResume parseResume, CommonDicService commonDicService, CommonProjectService commonProjectService,
    CommonVirtualService commonVirtualService, WeChatHelper weChatHelper, TencentImHelper tencentImHelper,
    CacheHelper cacheHelper, IHostEnvironment hostingEnvironment, CommonEsService commonEsService,
    IOptionsSnapshot<ConfigManager> config, CommonPostService commonPostService, ZPYunRepository zpyunRepository)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _parseResume = parseResume;
        _commonDicService = commonDicService;
        _commonVirtualService = commonVirtualService;
        _weChatHelper = weChatHelper;
        _tencentImHelper = tencentImHelper;
        _cacheHelper = cacheHelper;
        _hostingEnvironment = hostingEnvironment;
        _commonEsService = commonEsService;
        _config = config.Value;
        _commonPostService = commonPostService;
        _commonProjectService = commonProjectService;
        _nuoPinApi = nuoPinApi;
        _zpyunRepository = zpyunRepository;
    }

    /// <summary>
    /// 订阅staffing库的canal消息
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubCanalStaffing(CancellationToken cancel)
    {
        var subTables = new List<string>
        {
            "staffing.talent_platform",
            "staffing.talent_virtual",
            "staffing.talent_label",
            "staffing.user",
            "staffing.user_extend",
            "staffing.user_seeker",
            "staffing.user_resume",
            "staffing.user_work",
            "staffing.talent_virtual_work",
            "staffing.talent_virtual_edu",
            "staffing.talent_virtual_hope",
            "staffing.kuaishou_talent_infos",
            "staffing.project",
            "staffing.post",
            "staffing.user_agent_ent",
            "staffing.user_hr",
            "staffing.recruit",
            "staffing.project_teambounty",
            "staffing.post_team",
            "staffing.project_team",
            "staffing.talent_resume"
        };
        while (!cancel.IsCancellationRequested)
        {
            SimpleCanalConnection? conn = null;
            try
            {
                using var loggerFactory = LoggerFactory.Create(builder =>
                {
                    builder
                        .AddFilter("Microsoft", LogLevel.Error)
                        .AddFilter("System", LogLevel.Error)
                        .AddConsole();
                });
                var logger = loggerFactory.CreateLogger<SimpleCanalConnection>();

                conn = new SimpleCanalConnection(new SimpleCanalOptions(_config.Canal!.Server, _config.Canal.Port, _config.Canal.ClientId), logger);
                await conn.ConnectAsync();
                await conn.SubscribeAsync(string.Join(',', subTables));

                // var batchId = MyRedis.Client.HGet<long>(RedisKey.Canal.Key, RedisKey.Canal.CurrentBatchId);
                // if (batchId > 0)
                //     await conn.RollbackAsync(batchId);
            }
            catch (Exception e)
            {
                _logger.Error($"连接canal出错", Tools.GetErrMsg(e));
                await Task.Delay(60000);
                continue;
            }

            if (conn == null)
            {
                _logger.Error($"连接canal出错:conn不存在", string.Empty);
                await Task.Delay(60000);
                continue;
            }

            while (!cancel.IsCancellationRequested)
            {
                try
                {
                    var msg = await conn.GetAsync(1024);
                    var canalData = GetCanalData(msg.Entries);
                    var canalDataArray = canalData.Distinct().ToArray();
                    if (canalDataArray.Count() > 0)
                        MyRedis.Client.SAdd(SubscriptionKey.TableChange, canalDataArray);

                    var batchId = msg.Id;
                    if (batchId > 0)
                        MyRedis.Client.HSet(RedisKey.Canal.Key, RedisKey.Canal.CurrentBatchId, batchId);

                    await Task.Delay(20);
                }
                catch (Exception e)
                {
                    _logger.Error($"canal消息处理出错", Tools.GetErrMsg(e));
                    await Task.Delay(60000);
                    break;
                }

                await Task.Delay(10);
            }
        }
    }

    /// <summary>
    /// 表变更
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubTableChange(CancellationToken cancel)
    {
        //处理消息
        CanalData? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<CanalData?>(SubscriptionKey.TableChange);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                using var _context = _contextFactory.CreateDbContext();

                var tbName = msg.TableName?.ToLower();

                var primaryValues = new List<string?>
                {
                    msg.BeforeColumns?.Where(x=> x.IsKey).Select(s=> s.Value).FirstOrDefault(),
                    msg.AfterColumns?.Where(x=> x.IsKey).Select(s=> s.Value).FirstOrDefault()
                }.Where(x => !string.IsNullOrEmpty(x)).ToList();

                if (tbName == "talent_platform")
                {
                    MyRedis.Client.SAdd(SubscriptionKey.TalentTableChange, primaryValues
                    .Select(s => new Sub_TalentTableChange { TalentId = s }).ToArray());
                }
                else if (tbName == "talent_virtual")
                {
                    MyRedis.Client.SAdd(SubscriptionKey.VirtualTalentTableChange, primaryValues
                    .Select(s => new Sub_TalentTableChange { TalentId = s }).ToArray());
                }
                else if (tbName == "talent_label")
                {
                    var talentLabel = _context.Talent_Label.Where(x => primaryValues.Contains(x.Id))
                    .Select(s => new
                    {
                        s.PlatformId,
                        s.VirtualId
                    }).ToList();

                    var platforms = talentLabel.Where(x => !string.IsNullOrEmpty(x.PlatformId))
                    .Select(s => new Sub_TalentTableChange
                    {
                        TalentId = s.PlatformId
                    }).Distinct().ToArray();

                    var virtuals = talentLabel.Where(x => !string.IsNullOrEmpty(x.VirtualId))
                    .Select(s => new Sub_TalentTableChange
                    {
                        TalentId = s.VirtualId
                    }).Distinct().ToArray();

                    if (platforms.Count() > 0)
                        MyRedis.Client.SAdd(SubscriptionKey.TalentTableChange, platforms);
                    else if (virtuals.Count() > 0)
                        MyRedis.Client.SAdd(SubscriptionKey.VirtualTalentTableChange, virtuals);
                }
                else if (new string[] { "user_extend", "user_seeker", "user_resume" }.Contains(tbName))
                {
                    var talents = _context.Talent_Platform.Where(x => primaryValues.Contains(x.SeekerId))
                    .Select(s => new Sub_TalentTableChange
                    {
                        TalentId = s.Id
                    }).Distinct().ToArray();

                    if (talents.Count() > 0)
                        MyRedis.Client.SAdd(SubscriptionKey.TalentTableChange, talents);
                }
                else if (tbName == "user_work")
                {
                    var userIds = new List<string?>
                    {
                        msg.BeforeColumns?.Where(x=>x.Name?.ToLower() == "userid").FirstOrDefault()?.Value,
                        msg.AfterColumns?.Where(x=>x.Name?.ToLower() == "userid").FirstOrDefault()?.Value
                    }.Where(x => !string.IsNullOrEmpty(x)).ToList();

                    var talents = _context.Talent_Platform.Where(x => userIds.Contains(x.SeekerId))
                    .Select(s => new Sub_TalentTableChange
                    {
                        TalentId = s.Id
                    }).Distinct().ToArray();

                    if (talents.Count() > 0)
                        MyRedis.Client.SAdd(SubscriptionKey.TalentTableChange, talents);
                }
                else if (new string[] { "talent_virtual_work", "talent_virtual_edu", "talent_virtual_hope" }.Contains(tbName))
                {
                    var virtuals = new List<string?>
                    {
                        msg.BeforeColumns?.Where(x=>x.Name?.ToLower() == "virtualid").FirstOrDefault()?.Value,
                        msg.AfterColumns?.Where(x=>x.Name?.ToLower() == "virtualid").FirstOrDefault()?.Value
                    }.Where(x => !string.IsNullOrEmpty(x)).Select(s => new Sub_TalentTableChange
                    {
                        TalentId = s
                    }).Distinct().ToArray();

                    if (virtuals.Count() > 0)
                        MyRedis.Client.SAdd(SubscriptionKey.VirtualTalentTableChange, virtuals);
                }
                else if (new string[] { "post" }.Contains(tbName))
                {
                    var pcModel = new Sub_PostChange
                    {
                        PostId = primaryValues.First()
                    };

                    //检测职位状态变更
                    var beforeStatus = msg.BeforeColumns?.Where(x => x.Name?.ToLower() == "status").FirstOrDefault()?.Value;
                    var afterStatus = msg.AfterColumns?.Where(x => x.Name?.ToLower() == "status").FirstOrDefault()?.Value;
                    var beforeShare = msg.BeforeColumns?.Where(x => x.Name?.ToLower() == "share").FirstOrDefault()?.Value;
                    var afterShare = msg.AfterColumns?.Where(x => x.Name?.ToLower() == "share").FirstOrDefault()?.Value;
                    if (beforeStatus != afterStatus || beforeShare != afterShare)
                        pcModel.StatusChanged = true;

                    //检测坐标变化,生成职位地图
                    var beforeLocation = msg.BeforeColumns?.Where(x => x.Name?.ToLower() == "location").FirstOrDefault()?.Value;
                    var afterLocation = msg.AfterColumns?.Where(x => x.Name?.ToLower() == "location").FirstOrDefault()?.Value;

                    if (beforeLocation != afterLocation)
                        pcModel.LocationChanged = true;

                    MyRedis.Client.SAdd(SubscriptionKey.Canal.PostChange, pcModel);
                }
                else if (new string[] { "post_team" }.Contains(tbName))
                {
                    //检测职位状态变更
                    var beforeStatus = msg.BeforeColumns?.Where(x => x.Name?.ToLower() == "status").FirstOrDefault()?.Value;
                    var afterStatus = msg.AfterColumns?.Where(x => x.Name?.ToLower() == "status").FirstOrDefault()?.Value;
                    var beforeShow = msg.BeforeColumns?.Where(x => x.Name?.ToLower() == "show").FirstOrDefault()?.Value;
                    var afterShow = msg.AfterColumns?.Where(x => x.Name?.ToLower() == "show").FirstOrDefault()?.Value;
                    if (beforeStatus != afterStatus || beforeShow != afterShow)
                        MyRedis.Client.SAdd(SubscriptionKey.Canal.TeamPostStatusChange, new Sub_TeamPostStatusChange
                        {
                            TeamPostId = primaryValues.First()!,
                            isNew = msg.EventType == CanalEventType.Insert
                        });
                }
                else if (new string[] { "recruit" }.Contains(tbName))
                {
                    //检测招聘流程状态变更
                    var beforeStatus = msg.BeforeColumns?.Where(x => x.Name?.ToLower() == "status").FirstOrDefault()?.Value;
                    var afterStatus = msg.AfterColumns?.Where(x => x.Name?.ToLower() == "status").FirstOrDefault()?.Value;
                    if (beforeStatus != afterStatus)
                    {
                        var statusStr = !string.IsNullOrWhiteSpace(afterStatus) ? afterStatus : beforeStatus;
                        int.TryParse(statusStr, out int status);
                        MyRedis.Client.RPush(SubscriptionKey.RecruitStatusChange, new Sub_Recruit_StatusChange
                        {
                            RecruitId = primaryValues.First()!,
                            Status = (RecruitStatus)status,
                            IsNew = msg.EventType == CanalEventType.Insert
                        });
                    }

                    // 归档状态改变
                    var beforeStatusFileAway = msg.BeforeColumns?.Where(x => x.Name?.ToLower() == "fileaway").FirstOrDefault()?.Value;
                    var afterStatusFileAway = msg.AfterColumns?.Where(x => x.Name?.ToLower() == "fileaway").FirstOrDefault()?.Value;
                    if (beforeStatusFileAway != afterStatusFileAway)
                    {
                        var statusStr = !string.IsNullOrWhiteSpace(afterStatusFileAway) ? afterStatusFileAway : beforeStatusFileAway;
                        int.TryParse(statusStr, out int status);
                        MyRedis.Client.RPush(SubscriptionKey.RecruitStatusChange2, new Sub_Recruit_StatusChange
                        {
                            RecruitId = primaryValues.First()!,
                            Status = (RecruitStatus)status,
                            IsNew = msg.EventType == CanalEventType.Insert
                        });
                    }
                }
                else if (new string[] { "post_bounty" }.Contains(tbName))
                {
                    MyRedis.Client.SAdd(SubscriptionKey.TeamBountyChange, new Sub_TeamBountyChange
                    {
                        Id = primaryValues.First()!
                    });
                }
                else if (new string[] { "user_agent_ent" }.Contains(tbName))
                {
                    //如果诺快聘账号协同了职位
                    var postTeam = _context.Post_Team.Where(x => x.Project_Team.HrId == Constants.PlatformHrId && primaryValues.Contains(x.Post.Project.AgentEntId))
                    .Select(s => s.TeamPostId).Distinct().ToArray();

                    if (postTeam.Count() > 0)
                    {
                        var ut = DateTime.Now.ToUnixTimeMs();
                        MyRedis.Client.ZAdd(SubscriptionKey.Canal.TeamNkpTeamPostChange, postTeam.Select(s => new ZMember(s, ut)).ToArray());
                    }
                }
                else if (new string[] { "user_hr" }.Contains(tbName))
                {
                    //如果是诺快聘账号
                    if (primaryValues.Contains(Constants.PlatformHrId))
                    {
                        // var hr = new Sub_NkpHrChange
                        // {
                        //     HrId = primaryValues.First()
                        // };
                        MyRedis.Client.ZAdd(SubscriptionKey.Canal.HrChange, DateTime.Now.ToUnixTimeMs(), primaryValues.First());
                    }
                }
                else if (new string[] { "user" }.Contains(tbName))
                {
                    //人才库变更
                    var talents = _context.Talent_Platform.Where(x => primaryValues.Contains(x.SeekerId))
                    .Select(s => new Sub_TalentTableChange
                    {
                        TalentId = s.Id
                    }).Distinct().ToArray();

                    if (talents.Count() > 0)
                        MyRedis.Client.SAdd(SubscriptionKey.TalentTableChange, talents);

                    //如果是诺快聘账号
                    if (primaryValues.Contains(Constants.PlatformHrId))
                    {
                        var beforeMobile = msg.BeforeColumns?.Where(x => x.Name?.ToLower() == "mobile").FirstOrDefault()?.Value;
                        var afterMobile = msg.AfterColumns?.Where(x => x.Name?.ToLower() == "mobile").FirstOrDefault()?.Value;
                        if (beforeMobile != afterMobile)
                        {
                            // var hr = new Sub_NkpHrChange
                            // {
                            //     HrId = primaryValues.First()
                            // };
                            MyRedis.Client.ZAdd(SubscriptionKey.Canal.HrChange, DateTime.Now.ToUnixTimeMs(), primaryValues.First());
                        }
                    }
                }
                else if (new string[] { "talent_resume" }.Contains(tbName))
                {
                    MyRedis.Client.SAdd(SubscriptionKey.TalentResumeChange, primaryValues.ToArray());
                }
                //else if (new string[] { "kuaishou_talent_infos", "kuaishou_hr_talent_relations" }.Contains(tbName))
                //{
                //    var virtuals = new List<string?>
                //    {
                //        msg.BeforeColumns?.Where(x => x.Name?.ToLower() == "applicationid").FirstOrDefault()?.Value,
                //        msg.AfterColumns?.Where(x => x.Name?.ToLower() == "applicationid").FirstOrDefault()?.Value
                //    }.Where(x => !string.IsNullOrEmpty(x)).Select(s => new Sub_KuaishouTalentInfoTableChange
                //    {
                //        ApplicationId = s
                //    }).Distinct().ToArray();

                //    if (virtuals.Count() > 0)
                //        MyRedis.Client.SAdd(SubscriptionKey.KuaishouTalentInfoTableChange, virtuals);
                //}
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.TableChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 平台人才库变更
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubTalentTableChange(CancellationToken cancel)
    {
        //处理消息
        List<Sub_TalentTableChange?>? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_TalentTableChange?>(SubscriptionKey.TalentTableChange, 1)?.ToList();

                if (msg == null || msg?.Count == 0)
                {
                    await Task.Delay(500);
                    continue;
                }

                var tasks = new List<Task>();

                foreach (var item in msg!)
                {
                    tasks.Add(Task.Factory.StartNew(() => _commonEsService.UpdateTalentPlatform(item!.TalentId)));
                }

                Task.WaitAll(tasks.ToArray());

            }
            catch (Exception e)
            {
                _logger.Error($"SubTalentTableChange-{SubscriptionKey.TalentTableChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 简历人才库变更
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubVirtualTableChange(CancellationToken cancel)
    {
        //处理消息
        List<Sub_VirtualTableChange?>? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_VirtualTableChange?>(SubscriptionKey.VirtualTalentTableChange, 1).ToList();

                if (!(msg?.Count > 0))
                {
                    await Task.Delay(500);
                    continue;
                }

                var tasks = new List<Task>();

                foreach (var item in msg!)
                {
                    tasks.Add(Task.Factory.StartNew(() => _commonEsService.UpdateTalentVirtual(item!.TalentId)));
                }

                Task.WaitAll(tasks.ToArray());
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.VirtualTalentTableChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    public async Task SubKuaishouTalentInfoTableChange(CancellationToken cancel)
    {
        //处理消息
        List<Sub_KuaishouTalentInfoTableChange?>? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_KuaishouTalentInfoTableChange?>(SubscriptionKey.KuaishouTalentInfoTableChange, 5).ToList();

                if (!(msg?.Count > 0))
                {
                    await Task.Delay(500);
                    continue;
                }

                var tasks = new List<Task>();

                foreach (var item in msg!)
                {
                    tasks.Add(Task.Factory.StartNew(() => _commonEsService.UpdateKuaishouTalentInfo(item!.ApplicationId)));
                }

                Task.WaitAll(tasks.ToArray());
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.KuaishouTalentInfoTableChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    private List<CanalData> GetCanalData(List<Entry> entries)
    {
        var result = new List<CanalData>();
        foreach (var entry in entries)
        {
            if (entry.EntryType == EntryType.Transactionbegin || entry.EntryType == EntryType.Transactionend)
                continue;

            RowChange? rowChange = null;

            rowChange = RowChange.Parser.ParseFrom(entry.StoreValue);

            if (rowChange != null)
            {
                foreach (var rowData in rowChange.RowDatas)
                {
                    result.Add(new CanalData
                    {
                        BeforeColumns = rowData.BeforeColumns,
                        AfterColumns = rowData.AfterColumns,
                        EventType = rowChange.EventType switch
                        {
                            EventType.Delete => CanalEventType.Delate,
                            EventType.Insert => CanalEventType.Insert,
                            EventType.Update => CanalEventType.Update,
                            _ => null
                        },
                        TableName = entry.Header.TableName
                    });
                }
            }
        }

        return result;
    }

    /// <summary>
    /// 诺快聘职位变更
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubNkpPostChange(CancellationToken cancel)
    {
        //处理消息
        List<ZMember?>? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            try
            {
                var maxTime = DateTime.Now.ToUnixTimeMs();

                //查询到时间的消息
                msg = MyRedis.Client.ZRangeByScoreWithScores(SubscriptionKey.Canal.TeamNkpTeamPostChange, 0, maxTime, 0, 5).ToList();

                if (msg == null || msg.Count == 0)
                {
                    await Task.Delay(10000);
                    continue;
                }

                //取到之后移除
                MyRedis.Client.ZRem(SubscriptionKey.Canal.TeamNkpTeamPostChange, msg.Select(s => s!.member).ToArray());

                var ids = msg.Select(s => s!.member).ToList();

                try
                {
                    var data = _commonPostService.PostSync(ids);

                    if (data != null && data.Count > 0)
                        await _nuoPinApi.PostPush(data);
                }
                catch (Exception e)
                {
                    // //失败后延迟1分钟重试
                    // var newMsg = msg.Select(s => new ZMember(s!.member, DateTime.Now.AddMinutes(1).ToUnixTimeMs())).ToArray();
                    // MyRedis.Client.ZAdd(SubscriptionKey.Canal.TeamNkpTeamPostChange, newMsg);
                    _logger.Error($"{SubscriptionKey.Canal.TeamNkpTeamPostChange}消息处理出错，1分钟后重试", Tools.GetErrMsg(e), msg.Select(s => s!.member).ToList());
                    await Task.Delay(1000);
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.Canal.TeamNkpTeamPostChange}消息处理出错", Tools.GetErrMsg(e), msg!.Select(s => s!.member).ToList());
                await Task.Delay(1000);
            }

            await Task.Delay(20);
        }
    }

    /// <summary>
    /// 诺快官方账号顾问变更
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubNkpHrChange(CancellationToken cancel)
    {
        //处理消息
        List<ZMember?>? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            try
            {
                var maxTime = DateTime.Now.ToUnixTimeMs();

                //查询到时间的消息
                msg = MyRedis.Client.ZRangeByScoreWithScores(SubscriptionKey.Canal.HrChange, 0, maxTime, 0, 5).ToList();

                if (msg == null || msg.Count == 0)
                {
                    await Task.Delay(10000);
                    continue;
                }

                //取到之后移除
                MyRedis.Client.ZRem(SubscriptionKey.Canal.HrChange, msg.Select(s => s!.member).ToArray());

                var ids = msg.Select(s => s!.member).ToList();

                try
                {
                    var data = _commonPostService.HrSync(ids);

                    if (data != null && data.Count > 0)
                        await _nuoPinApi.PostPush(data);
                }
                catch (Exception e)
                {
                    // //失败后延迟1分钟重试
                    // var newMsg = msg.Select(s => new ZMember(s!.member, DateTime.Now.AddMinutes(1).ToUnixTimeMs())).ToArray();
                    // MyRedis.Client.ZAdd(SubscriptionKey.Canal.HrChange, newMsg);
                    _logger.Error($"{SubscriptionKey.Canal.HrChange}消息处理出错，1分钟后重试", Tools.GetErrMsg(e), msg!.Select(s => s!.member).ToList());
                    await Task.Delay(1000);
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.Canal.HrChange}消息处理出错", Tools.GetErrMsg(e), msg!.Select(s => s!.member).ToList());
                await Task.Delay(1000);
            }

            await Task.Delay(20);
        }
    }

    /// <summary>
    /// 职位变更
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task PostChange(CancellationToken cancel)
    {
        //处理消息
        Sub_PostChange? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_PostChange>(SubscriptionKey.Canal.PostChange);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                //by zhenghe 2024-12-03
                //不再使用主创岗位，应该为协同岗位
                //_logger.Info("msg.PostId:", $"{msg.PostId}");
                //await _zpyunRepository.AsyncJobInfo(msg.PostId);//任何变更均同步到云生职位信息
                //_logger.Info("msg", "");
                //如果位置变更
                if (msg.LocationChanged)
                    MyRedis.Client.SAdd(SubscriptionKey.PostLocationChange, msg.PostId);

                //如果诺快聘已经协同，通知诺聘
                using var _context = _contextFactory.CreateDbContext();
                var nkpTeamPost = _context.Post_Team.Where(x => x.PostId == msg.PostId && x.Project_Team.HrId == Constants.PlatformHrId)
                .Select(s => new
                {
                    s.TeamPostId
                }).FirstOrDefault();

                if (nkpTeamPost != null)
                    MyRedis.Client.ZAdd(SubscriptionKey.Canal.TeamNkpTeamPostChange, DateTime.Now.ToUnixTimeMs(), nkpTeamPost.TeamPostId);

                //如果状态变更,自动协同诺快聘
                if (msg.StatusChanged)
                {
                    var post = _context.Post.Where(x => x.PostId == msg.PostId)
                    .Select(s => new
                    {
                        s.Status,
                        s.CreatorId,
                        ProjStatus = s.Project.Status,
                        s.PostId,
                        s.ProjectId,
                        ProjectHrId = s.Project.HrId,
                        EntName = s.Project.Agent_Ent.Name,
                        HrName = s.Project.User_Hr.NickName,
                        SaleName = s.Project.SaleUser.NickName,
                        s.Name
                    }).First();

                    if (post.Status == PostStatus.发布中 && post.ProjStatus == ProjectStatus.已上线
                    && !_context.Post_Team.Any(x => x.Project_Team.HrId == Constants.PlatformHrId && x.PostId == msg.PostId))
                    {
                        var projectTeam = _context.Project_Team.FirstOrDefault(x => x.ProjectId == post.ProjectId && x.HrId == Constants.PlatformHrId);
                        if (projectTeam == null)
                        {
                            projectTeam = new Project_Team
                            {
                                Type = HrProjectType.协同,
                                HrId = Constants.PlatformHrId,
                                ProjectId = post.ProjectId!,
                                Source = HrProjectSource.平台协同,
                                Status = ProjectStatus.已上线,
                                UpdatedBy = Constants.PlatformHrId
                            };
                            _context.Add(projectTeam);
                        }

                        var teamPost = new Post_Team
                        {
                            PostId = post.PostId,
                            Status = PostStatus.发布中,
                            UpdatedBy = Constants.PlatformHrId,
                            TeamProjectId = projectTeam.TeamProjectId
                        };
                        _context.Add(teamPost);
                        _context.Add(new Post_Team_Extend { TeamPostId = teamPost.TeamPostId });

                        _context.SaveChanges();

                        var teamPostId = teamPost.TeamPostId;

                        //职位状态变更处理
                        _commonProjectService.UpdatePostShow(teamPostId, UpdatePostShowType.协同职位);
                    }

                    if (_hostingEnvironment.IsProduction() && post.Status == PostStatus.待审核)
                    {
                        //通知相关人员审核
                        var phrs = _context.User_Hr.Where(x => x.Status == UserStatus.Active).Select(s => new { s.UserId, s.Powers }).ToList();
                        var ids = phrs.Where(x => x.Powers?.Contains(Power.NkpAdmin) == true).Select(s => s.UserId).Take(100).ToList();

                        var hrName = string.IsNullOrEmpty(post.HrName) ? post.SaleName : post.HrName;
                        var msgs = ids.Select(s => new MsgNotifyModel
                        {
                            Type = MsgNotifyType.职位需审核通知,
                            EventTime = DateTime.Now,
                            UserId = s,
                            Data = new MsgNotifyPostNeedAudit { EntName = post.EntName, HrName = hrName, PostName = post.Name }
                        }).ToList();

                        if (!string.IsNullOrEmpty(post.ProjectHrId) && !ids.Contains(post.ProjectHrId))
                        {
                            msgs.Add(new MsgNotifyModel
                            {
                                Type = MsgNotifyType.职位需审核通知,
                                EventTime = DateTime.Now,
                                UserId = post.ProjectHrId,
                                Data = new MsgNotifyPostNeedAudit { EntName = post.EntName, HrName = hrName, PostName = post.Name }
                            });
                        }

                        MsgHelper.SendDingdingNotify(msgs.ToArray());
                    }
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.Canal.PostChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(50);
        }
    }

    /// <summary>
    /// 协同职位状态变更
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task TeamPostStatusChange(CancellationToken cancel)
    {
        //处理消息
        Sub_TeamPostStatusChange? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_TeamPostStatusChange>(SubscriptionKey.Canal.TeamPostStatusChange);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                _logger.Info("msg.TeamPostId:", $"{msg.TeamPostId}");
                await _zpyunRepository.AsyncJobInfo(msg.TeamPostId);//任何变更均同步到云生职位信息
                _logger.Info("msg", "协同职位同步");

                //处理消息
                using var _context = _contextFactory.CreateDbContext();

                //如果是新协同
                if (msg.isNew)
                {
                    //生成小程序二维码
                    MyRedis.Client.SAdd(SubscriptionKey.PostAppletQrCode, msg.TeamPostId);
                }

                var teamPost = _context.Post_Team.Where(x => x.TeamPostId == msg.TeamPostId)
                .Select(s => new
                {
                    TeamHrId = s.Project_Team.HrId
                }).First();

                MyRedis.Client.ZAdd(SubscriptionKey.TeamHrTj, DateTime.Now.ToUnixTimeMs(), teamPost.TeamHrId);
                // MyRedis.Client.SAdd(SubscriptionKey.TeamHrTj, new Sub_TeamHrTj
                // {
                //     TeamHrId = teamPost.TeamHrId
                // });

                //更新hr统计数据
                _commonProjectService.UpdateHrProjectData(new Sub_UpdateHrData { UserId = teamPost.TeamHrId, Type = UpdateHrDataType.职位 });

                //如果诺快聘账号协同了职位，推送诺聘
                var postTeam = _context.Post_Team.Where(x => x.Project_Team.HrId == Constants.PlatformHrId && x.TeamPostId == msg.TeamPostId)
                .Select(s => s.TeamPostId).Distinct().ToArray();

                if (postTeam.Count() > 0)
                {
                    var ut = DateTime.Now.ToUnixTimeMs();
                    MyRedis.Client.ZAdd(SubscriptionKey.Canal.TeamNkpTeamPostChange, postTeam.Select(s => new ZMember(s, ut)).ToArray());
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.Canal.TeamPostStatusChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(20);
        }
    }

    /// <summary>
    /// 人才融合库数据变更
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task TalentResumeChange(CancellationToken cancel)
    {
        //处理消息
        List<string>? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<string>(SubscriptionKey.TalentResumeChange, 2).ToList();

                if (!(msg?.Count > 0))
                {
                    await Task.Delay(500);
                    continue;
                }

                var tasks = new List<Task>();

                foreach (var item in msg!)
                {
                    tasks.Add(Task.Factory.StartNew(() => _commonEsService.UpdateTalentResumeIndex(item)));
                }

                Task.WaitAll(tasks.ToArray());

                //foreach (var item in msg!)
                //{
                //    _commonEsService.UpdateTalentResumeIndex(item);
                //}

            }
            catch (Exception e)
            {
                _logger.Error($"CanalService.TalentResumeChange{SubscriptionKey.TalentResumeChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }
}