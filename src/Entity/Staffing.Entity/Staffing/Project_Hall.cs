﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 项目大厅
/// </summary>
[Table("project_hall")]
public class Project_Hall
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 项目Id
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 用户Id
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Project Project { get; set; } = default!;
    public User_Hr User_Hr { get; set; } = default!;
}
