using Infrastructure.Extend;
using Microsoft.AspNetCore.Http;
using Microsoft.Net.Http.Headers;
using Serilog;

namespace Infrastructure.Middleware;

/// <summary>
/// 中间件
/// 记录请求和响应数据
/// </summary>
public class RequRespLog
{
    private readonly RequestDelegate _next;
    private readonly IDiagnosticContext _diagnosticContext;

    /// <summary>
    ///
    /// </summary>
    /// <param name="next"></param>
    public RequRespLog(RequestDelegate next, IDiagnosticContext diagnosticContext)
    {
        _next = next;
        _diagnosticContext = diagnosticContext;
    }

    public async Task InvokeAsync(HttpContext context, RequestContext _user)
    {
        context.Request.EnableBuffering();
        Stream originalBody = context.Response.Body;

        var apiStartTime = DateTime.Now;
        var requestId = context.TraceIdentifier;
        _user.RequestId = requestId;
        _user.ConnectionId = context.Connection.Id;

        //拿到真实ip
        var ipAddress = context.Request.Headers["X-Forwarded-For"].ToString();
        if (string.IsNullOrWhiteSpace(ipAddress))
            ipAddress = context.Connection?.RemoteIpAddress?.ToString().Replace("::ffff:", string.Empty);
        else
        {
            var ips = ipAddress?.Trim().Trim(',').Split(',');
            ipAddress = ips?.FirstOrDefault();
        }

        _diagnosticContext.Set("User",
            $"{(!string.IsNullOrEmpty(_user.Name) ? $"昵称：{_user.Name}，" : string.Empty)}Id：{_user.Id}，账号：{_user.Account}，客户端：{_user.ClientType}，顾问Id：{_user.AdviserId}，渠道Id：{_user.ChannelId}");

        var method = context.Request.Method.ToUpper();
        var para = RequestParams.GetParams(context);

        var agent = context.Request?.Headers?[HeaderNames.UserAgent].ToString() ?? string.Empty;

        _user.UserAgent = agent;
        _user.RequestIpAddress = ipAddress;

        await using var responseBody = new MemoryStream();
        context.Response.Body = responseBody;

        //去掉get请求中过的空字符串，改为null，否则会导致转换错误
        if (context.Request?.Method == HttpMethods.Get)
        {
            var queryCollection = context.Request.Query
                .Where(kvp => !string.IsNullOrEmpty(kvp.Value))
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value.ToString());

            var queryString = string.Join("&", queryCollection.Select(kvp => $"{kvp.Key}={kvp.Value}"));
            context.Request.QueryString = new QueryString("?" + queryString);
        }

        _diagnosticContext.Set("Request", para);

        await _next(context);

        // 存储响应数据
        context.Response.Body.Seek(0, SeekOrigin.Begin);
        var respBody = await new StreamReader(context.Response.Body).ReadToEndAsync();
        context.Response.Body.Seek(0, SeekOrigin.Begin);

        _diagnosticContext.Set("Response", respBody);

        await responseBody.CopyToAsync(originalBody);
    }
}