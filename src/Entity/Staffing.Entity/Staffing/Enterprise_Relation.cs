﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 企业集团关系
/// </summary>
[Table("enterprise_relation")]
public class Enterprise_Relation
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 公司Id
    /// </summary>
    public string EntId { get; set; } = default!;

    /// <summary>
    /// 所属集团公司Id
    /// </summary>
    public string? GroupEntId { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string? Creator { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Enterprise Enterprise { get; set; } = default!;
    public Enterprise GroupEnterprise { get; set; } = default!;
}
