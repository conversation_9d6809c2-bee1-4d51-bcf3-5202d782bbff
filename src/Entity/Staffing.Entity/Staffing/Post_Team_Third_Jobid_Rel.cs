﻿using Config.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 诺快聘职位与第三方平台关系表
/// </summary>
[Table("post_team_third_jobid_rel")]
public class Post_Team_Third_Jobid_Rel
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// post_team表主键
    /// </summary>
    public string TeamPostId { get; set; } = default!;

    /// <summary>
    /// 第三方jobid
    /// </summary>
    public string JobId { get; set; } = default!;

    /// <summary>
    /// 平台类型：0-快招工
    /// </summary>
    public ThirdPlatType PlatType { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreatedUser { get; set; }

    /// <summary>
    /// 修改人
    /// </summary>
    public string? UpdatedUser { get; set; }

    /// <summary>
    /// 是否删除
    /// </summary>
    public int Deleted { get; set; } = 0;

    public Post_Team Post_Team { get; set; } = default!;

    public Kuaishou_Talent_Infos Kuaishou_Talent_Infos { get; set; } = default!;
}