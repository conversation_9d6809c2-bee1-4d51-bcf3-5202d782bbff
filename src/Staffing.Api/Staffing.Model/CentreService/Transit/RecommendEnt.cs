using Config.Enums;

namespace Staffing.Model.CentreService.Transit;

public class RecommendEnt
{
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? AdviserId { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public HrRecommendEntSource? Source { get; set; }

    /// <summary>
    /// 三方企业Id
    /// </summary>
    public string? TEntId { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// 公司名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// Hr名称
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// Hr电话
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    public bool IsCover { get; set; } = false;

    /// <summary>
    /// 网点Id
    /// </summary>
    public string? OutletId { get; set; }

    /// <summary>
    /// 网点名称
    /// </summary>
    public string? OutletName { get; set; }

    /// <summary>
    /// 致趣百川会议ID
    /// </summary>
    public string? BeschannelsMeetingId { get; set; }
}

public class RecommendEntResponse
{
    /// <summary>
    /// 唯一标识
    /// </summary>
    public string? Id { get; set; }
}

public class UpdateRecommendEnt
{
    /// <summary>
    /// 唯一标识
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? AdviserId { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public HrRecommendEntSource? Source { get; set; }

    /// <summary>
    /// 网点Id
    /// </summary>
    public string? OutletId { get; set; }

    /// <summary>
    /// 网点名称
    /// </summary>
    public string? OutletName { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}
