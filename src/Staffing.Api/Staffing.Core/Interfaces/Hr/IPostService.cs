﻿using Config.CommonModel;
using Staffing.Model.Hr.Post;


namespace Staffing.Core.Interfaces.Hr;

public interface IPostService
{
    EmptyResponse AuditPost(AuditRequest request);
    GetAuditPostResponse GetAuditPost(GetAuditPost model);
    GetHallPostResponse GetHasOrderPost(Model.Hr.Post.GetHallPost model);
    GetDicTreeResponse GetHasOrderPostCityTree(GetHallPost model);
    GetHallPostResponse GetHallPost(Model.Hr.Post.GetHallPost model);

    GetDicTreeResponse GetHallPostCityTree(GetHallPost model);
    // PostPaymentTypeResponse GetPostPaymentType(string id);
    Task<EmptyResponse> PostSync(PostSync model);

}