﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Nyk;

/// <summary>
/// 旧版用户报名
/// </summary>
[Table("Apply")]
public class OldApply
{
    /// <summary>
    /// id
    /// </summary>
    [Key]
    public string PK_AID { get; set; } = default!;

    /// <summary>
    /// 求职者Id
    /// </summary>
    public string? PK_JHID { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public int? Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateTime? Birthday { get; set; }

    /// <summary>
    /// 手机号码
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// 扩展信息
    /// </summary>
    public string? ExpandForm { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 照片
    /// </summary>
    public string? PhotoUrl { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public int IsRemove { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
}

public class OldApply2
{
    [Key]
    public string PK_AID { get; set; } = default!;
}