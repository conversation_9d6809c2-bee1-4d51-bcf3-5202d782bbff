﻿using System.Data.Entity;
using System.Text.Json;
using Config.Enums;
using Config.CommonModel;
using Infrastructure.Aliyun;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Model.Service;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Noah.Aliyun.Storage;
using Infrastructure.CommonService;
using Config;
using LinqKit;
using Infrastructure.Aop;
using Staffing.Model.Certificate;

namespace Staffing.Core.Services.Service;

[Service(ServiceLifetime.Transient)]
public class SharedService : ISharedService
{
    private readonly StaffingContext _context;
    private readonly RequestContext _user;
    private readonly ConfigManager _config;
    private readonly SmsHelper _smsHelper;
    private readonly ICommonService _commonService;
    private readonly Noah.Aliyun.AFS.IAFS _aliyunAFS;
    private readonly IObjectStorage _objectStorage;
    private readonly CommonDicService _commonDicService;
    public SharedService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, ICommonService commonService, SmsHelper smsHelper,
        Noah.Aliyun.AFS.IAFS aliyunAFS, IObjectStorage objectStorage, CommonDicService commonDicService)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _commonService = commonService;
        _smsHelper = smsHelper;
        _aliyunAFS = aliyunAFS;
        _objectStorage = objectStorage;
        _commonDicService = commonDicService;
    }

    public CheckSMSCodeResponse CheckSMSCode(CheckSMSCode model)
    {
        if (string.IsNullOrWhiteSpace(model.Phone) || string.IsNullOrWhiteSpace(model.SMSCode))
            throw new BadRequestException("手机号和验证码不能为空");

        //产品环境并且不是测试账号，才验证
        // if (_hostingEnvironment.IsProduction())
        {
            //检测次数，防止暴力破解
            var today = DateTime.Today.ToString("yyyyMMdd");

            var checktimes = _commonService.CheckIdempotencyTimes(IdempotencyCheckType.Sms, model.Phone + today);

            if (!checktimes)
                throw new BadRequestException("验证码失败次数过多，今日禁止使用");

            //验证码检测
            var expireTime = DateTime.Now.AddSeconds(Constants.SmsExpireSeconds * -1);
            var smscode = _context.Sys_Sms.Where(a => a.Mobile == model.Phone && a.Content == model.SMSCode
            && a.Type == model.Type && a.CreatedTime > expireTime && a.Active).FirstOrDefault();

            var result = new CheckSMSCodeResponse
            {
                Result = (smscode != null)
            };

            //如果验证失败，记录失败次数
            if (smscode == null)
                _commonService.CheckIdempotencyFaild(IdempotencyCheckType.Sms, model.Phone + today);
            else
            {
                smscode.Active = false;
                _context.SaveChanges();
            }

            return result;
        }

        // return new CheckSMSCodeResponse { Result = true };
    }
    public void InvalidCode(CheckSMSCode model)
    {

        var expireTime = DateTime.Now.AddSeconds(Constants.SmsExpireSeconds * -1);
        var sms = _context.Sys_Sms.Where(a => a.Mobile == model.Phone && a.Content == model.SMSCode
        && a.Type == model.Type && a.CreatedTime > expireTime && a.Active).FirstOrDefault();
        if (sms != null)
        {
            sms.Active = false;
            _context.SaveChanges();
        }
    }

    public async Task<SMSCodeResponse> SMSCode(SMSCode model)
    {
        var lockerKey = $"sms:{model.Phone}";
        if (!string.IsNullOrWhiteSpace(_user.Id))
            lockerKey = $"sms:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var result = new SMSCodeResponse();

        try
        {
            //如果用户登录了则不需要验证
            if (string.IsNullOrWhiteSpace(_user.Id))
            {
                var jToken = JToken.Parse(System.Text.Encoding.UTF8.GetString(Convert.FromBase64String(model.Verify!)));
                var sessionId = jToken.Value<string>("SessionId");
                var sig = jToken.Value<string>("Sig");
                var token = jToken.Value<string>("Token");
                var scene = jToken.Value<string>("Scene");
                var remoteIp = _user.RequestIpAddress;
                var isVerify = await _aliyunAFS.Verify(sessionId, sig, token, scene, remoteIp, out int msgcode);

                if (!isVerify)
                    throw new BadRequestException("校验失败");
            }
        }
        catch
        {
            throw new BadRequestException("校验失败");
        }

        var today = DateTime.Today;
        var todayCount = _context.Sys_Sms.AsNoTracking()
            .Count(c => c.Mobile == model.Phone && c.CreatedTime > today);
        if (todayCount > _config.Aliyun!.AliSMS!.MaxEveryDay)
            throw new BadRequestException("此手机号今日短信数量已超出最大限制");

        if (!string.IsNullOrWhiteSpace(_user.Id))
        {
            var todayCount2 = _context.Sys_Sms.AsNoTracking()
                    .Count(c => c.UserId == _user.Id && c.CreatedTime > today);
            if (todayCount2 > _config.Aliyun!.AliSMS!.MaxEveryDay)
                throw new BadRequestException("您今日短信数量已超出最大限制");
        }

        var lastSendTime = _context.Sys_Sms.AsNoTracking()
            .Where(c => c.Mobile == model.Phone && c.CreatedTime > today)
            .Select(s => (DateTime?)s.CreatedTime).Max();

        if (lastSendTime.HasValue && (DateTime.Now - lastSendTime.Value).TotalSeconds < 60)
            throw new BadRequestException("发送频繁，请稍后操作");

        var random = new Random();
        var code = random.Next(100000, 999999);

        var sms = new Sys_Sms
        {
            Content = code.ToString(),
            CreatedTime = DateTime.Now,
            Mobile = model.Phone!,
            Type = model.Type,
            Active = true,
            UserId = _user.Id ?? string.Empty
        };

        _context.Add(sms);
        _context.SaveChanges();

        _smsHelper.SendCode(sms.Mobile, sms.Content);

        return result;
    }

    public GetAliyunOssTokenResponse GetAliyunOssToken(GetAliyunOssToken model)
    {
        var uid = string.IsNullOrEmpty(_user.Id) ? _user.NuoPinId : _user.Id;
        var id = string.IsNullOrEmpty(uid) ? "adm" : uid;
        var dir = $"{_config.Aliyun!.Oss!.Dir!}/user/{id}";

        if (!string.IsNullOrWhiteSpace(model.Dir))
            dir = $"{dir}/{model.Dir}";

        var defaultfiletypes = new List<string> { "image/png", "image/jpg", "image/jpeg", "image/gif", "image/bmp",
            "application/vnd.ms-excel", "application/msword", "text/plain", "application/pdf","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "text/csv", "audio/mpeg", "video/mp4" };

        var token = _objectStorage.GetClientUploadToken(dir, defaultfiletypes);
        var result = Tools.ModelConvert<GetAliyunOssTokenResponse>(token);

        result.OssDomain = $"{Constants.OssDomain}/{token.Path}";
        result.Host = Constants.OssDomain;

        return result;
    }

    public GetWelfareResponse GetWelfare(GetWelfare model)
    {
        var result = new GetWelfareResponse();
        var cache = _commonDicService.GetWelfare();
        if (!string.IsNullOrWhiteSpace(model.Search))
            cache = cache.Where(x => x.Name != null && x.Name.Contains(model.Search)).ToList();
        result.Rows = cache;
        return result;
    }

    public GetSchoolResponse GetSchool(GetSchool model)
    {
        var result = new GetSchoolResponse();
        var cache = _commonDicService.GetSchool();
        if (!string.IsNullOrWhiteSpace(model.Search))
            cache = cache.Where(x => x.Name.Contains(model.Search)).ToList();
        result.Rows = cache.Select(s => new GeneralDic
        {
            Id = s.Id,
            Name = s.Name
        }).ToList();
        return result;
    }

    public GetMajorResponse GetMajor(GetMajor model)
    {
        var result = new GetMajorResponse();
        var cache = _commonDicService.GetMajor();
        if (!string.IsNullOrWhiteSpace(model.Search))
            cache = cache.Where(x => x.Name.Contains(model.Search)).ToList();
        result.Rows = cache.Select(s => new GeneralDic
        {
            Id = s.Id,
            Name = s.Name
        }).ToList();
        return result;
    }

    public GetIndustryResponse GetIndustry(GetIndustry model)
    {
        var result = new GetIndustryResponse();
        var cache = _commonDicService.GetIndustry();
        if (!string.IsNullOrWhiteSpace(model.Search))
            cache = cache.Where(x => x.Name.Contains(model.Search)).ToList();

        var treeNotes = Tools.GetChildrenTreeNode(cache, x => x.Id, y => y.ParentId);
        result.Rows = Tools.ModelConvert<List<GeneralRecursionDic>>(treeNotes);

        return result;
    }

    public GetDicTreeResponse GetPostCategory(GetPostCategory model)
    {
        var result = new GetDicTreeResponse();
        var cache = _commonDicService.GetPost();

        //cache = cache.Where(x => !x.Level.StartsWith("1041.")).ToList();

        if (model.Level.HasValue)
            cache = cache.Where(x => x.Level?.Count(c => c == '.') == model.Level).ToList();

        if (!string.IsNullOrWhiteSpace(model.Search))
            cache = cache.Where(x => x.Name.Contains(model.Search)).ToList();

        if (!model.Level.HasValue)
        {
            var treeNotes = Tools.GetChildrenTreeNode(cache, x => x.Id, y => y.ParentId);
            result.Rows = Tools.ModelConvert<List<GeneralRecursionDic>>(treeNotes);
        }
        else
        {
            result.Rows = cache.OrderBy(o => o.Name)
            .Select(s => new GeneralRecursionDic
            {
                Id = s.Id,
                Name = s.Name,
                ParentId = s.ParentId
            }).ToList();
        }

        return result;
    }

    public GetDicTreeResponse GetCert(GetCert model)
    {
        var result = new GetDicTreeResponse();
        var cache = _commonDicService.GetCert();

        if (!string.IsNullOrWhiteSpace(model.Search))
        {
            var newList = cache.Where(x => x.Name.Contains(model.Search)
            && !cache.Any(c => c.ParentId == x.Id)).ToList();

            var addList = new List<CommonDicModel>();
            var addList2 = new List<CommonDicModel>();
            foreach (var item in newList)
            {
                var p = cache.FirstOrDefault(x => x.Id == item.ParentId);
                if (p != null)
                    addList.Add(p);
            }

            foreach (var item in addList)
            {
                var p = cache.FirstOrDefault(x => x.Id == item.ParentId);
                if (p != null)
                    addList2.Add(p);
            }
            cache = newList.Union(addList).Union(addList2).ToList();
        }

        var treeNotes = Tools.GetChildrenTreeNode(cache, x => x.Id, y => y.ParentId);
        result.Rows = Tools.ModelConvert<List<GeneralRecursionDic>>(treeNotes);

        return result;
    }

    public GetDicTreeResponse GetCityTree(GetCityTree model)
    {
        var result = new GetDicTreeResponse();

        result.Rows = _commonDicService.GetRegionTree(model.Level);

        return result;
    }

    public GetCityResponse GetCity(GetCity model)
    {
        var result = new GetCityResponse();

        // var cache = _commonDicService.GetRegion();

        var predicate = PredicateBuilder.New<Dic_Region>(true);

        if (!string.IsNullOrWhiteSpace(model.ParentId))
            predicate = predicate.And(x => x.ParentId == model.ParentId);
        else
            predicate = predicate.And(x => x.ParentId == string.Empty);

        result.Rows = _context.Dic_Region.Where(predicate)
        .Select(s => new GeneralDic
        {
            Id = s.Id,
            Name = s.Name
        }).ToList();

        // if (!string.IsNullOrWhiteSpace(model.ParentId))
        //     cache = cache.Where(x => x.ParentId == model.ParentId).ToList();
        // else
        //     cache = cache.Where(x => string.IsNullOrWhiteSpace(x.ParentId)).ToList();

        // result.Rows = cache.Select(s => new GeneralDic
        // {
        //     Id = s.Id,
        //     Name = s.Name
        // }).ToList();

        return result;
    }

    [RedisCache(Expire = 300)]
    public GetAppletSceneResponse GetAppletScene(GetAppletScene model)
    {
        var result = new GetAppletSceneResponse();

        var appletShareType = AppletShareType.首页;
        var idx = model?.Scene?.IndexOf('_');
        var scene = string.Empty;
        if (idx > 0 && int.TryParse(model!.Scene!.Substring(0, idx.Value), out var ast))
        {
            appletShareType = (AppletShareType)ast;
            scene = model.Scene.Substring(idx.Value + 1);
        }

        //职位解析
        if (appletShareType == AppletShareType.职位)
        {
            var teamPost = _context.Post_Team.Where(x => x.TeamPostId == scene)
            .Select(s => new
            {
                s.Project_Team.HrId,
                s.TeamPostId
            }).FirstOrDefault();

            result.AdviserId = teamPost?.HrId;
            result.TeamPostId = teamPost?.TeamPostId;
        }
        else if (appletShareType == AppletShareType.首页)
        {
            result.AdviserId = scene;
        }
        else if (appletShareType == AppletShareType.求职者分享职位)
        {
            var obj = MyRedis.Client.HGet<GetSeekerShareQrCodeInfo?>(RedisKey.SeekerShareQrCode, scene);
            if (obj == null)
                throw new BadRequestException("内容不存在");

            result.AdviserId = obj.HrId;
            result.TeamPostId = obj.TeamPostId;
            result.ShareUserId = obj.ShareUserId;
        }
        else if (appletShareType == AppletShareType.渠道商二维码)
        {
            var teamPost = _context.Qd_Hr_Channel.Where(x => x.Id == scene)
           .Select(s => new
           {
               s.HrId,
               s.Id
           }).FirstOrDefault();

            result.AdviserId = teamPost?.HrId;
            result.ChannelId = teamPost?.Id;
        }
        else if (appletShareType == AppletShareType.渠道商职位二维码)
        {
            var obj = MyRedis.Client.HGet<GetSeekerShareQrCodeInfo?>(RedisKey.SeekerShareQrCode, scene);
            if (obj == null)
                throw new BadRequestException("内容不存在");

            result.AdviserId = obj.HrId;
            result.TeamPostId = obj.TeamPostId;
            result.ChannelId = obj.ChannelId;
        }
        else if (appletShareType == AppletShareType.证书PC)
        {
            var obj = MyRedis.Client.HGet<string?>(RedisKey.CertificateQrcode, model.Scene);
            if (obj == null)
                throw new BadRequestException("内容不存在");
            var qrcodeData = JsonSerializer.Deserialize<QrCodeData>(obj);
            result.HrId = qrcodeData.UserId;
            result.CertificateId = qrcodeData.CertificateId;
        }
        result.Type = appletShareType;
        return result;
    }
    private string ExtractUrlFromQrCode(string qrcode)
    {
        var parts = qrcode?.Split(';');
        return parts?.Length > 1 ? parts[1] : string.Empty;
    }

    public void WarmApi()
    {
        Task.Run(() =>
        {
            _commonDicService.GetRegionTree();
            _commonDicService.GetRegionTree(1);
            _commonDicService.GetRegionTree(2);
            _commonDicService.GetRegionTree(3);
            _commonDicService.GetWelfare();
            _commonDicService.GetIndustry();
            _commonDicService.GetMajor();
            _commonDicService.GetSchool();
            _commonDicService.GetPost();
            _commonDicService.GetCert();
        });

        var warmDb = _context.User.OrderBy(o => o.UserId)
        .Select(s => s.UserId).FirstOrDefault();

        Console.WriteLine("预热完毕");
    }

    public async Task<SMSCodeResponse> SMSCodeForDy(SMSCode model)
    {
        await Task.FromResult(1);
        model.Phone = model.Phone?.Trim();
        var lockerKey = $"sms:{model.Phone}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey) ?? throw new BadRequestException("操作频繁");

        var today = DateTime.Today;
        var todayCount = _context.Sys_Sms.AsNoTracking()
            .Count(c => c.Mobile == model.Phone && c.CreatedTime > today);
        if (todayCount > _config.Aliyun!.AliSMS!.MaxEveryDay)
            throw new BadRequestException("此手机号今日短信数量已超出最大限制");

        var lastSendTime = _context.Sys_Sms.AsNoTracking()
            .Where(c => c.Mobile == model.Phone && c.CreatedTime > today)
            .Select(s => (DateTime?)s.CreatedTime).Max();

        if (lastSendTime.HasValue && (DateTime.Now - lastSendTime.Value).TotalSeconds < 60)
            throw new BadRequestException("发送频繁，请稍后操作");

        //因为前端没有对接验证码，因此临时全局验证防刷
        var gk = $"nkpsms:{DateTime.Now:yyyyMMddHHmm}";
        var tms = MyRedis.Client.Incr(gk);
        MyRedis.Client.Expire(gk, TimeSpan.FromMinutes(2));
        if (tms > 20)
            throw new BadRequestException("该服务暂停使用，请稍后再试");

        var random = new Random();
        var code = random.Next(100000, 999999);

        var sms = new Sys_Sms
        {
            Content = code.ToString(),
            CreatedTime = DateTime.Now,
            Mobile = model.Phone!,
            Type = model.Type,
            Active = true,
            UserId = _user.Id ?? string.Empty
        };

        _context.Add(sms);
        _context.SaveChanges();

        _smsHelper.SendCode(sms.Mobile, sms.Content);

        var result = new SMSCodeResponse();
        return result;
    }
}