
-- ----------------------------
-- 顾问数据
-- ----------------------------
DROP TABLE IF EXISTS `user_hr_data`;
CREATE TABLE `user_hr_data`  (
  `UserId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `YesterdayTalentCount` int NOT NULL DEFAULT 0 COMMENT '昨日新增人才库数量',
  `MyProjectCount` int NOT NULL DEFAULT 0 COMMENT 'HR发布的项目数量',
  `ProjectShareCount` int NOT NULL DEFAULT 0 COMMENT '主创项目-开启集团协同数据',
  `ProjectShareInternalCount` int NOT NULL DEFAULT 0 COMMENT '主创项目-开启公司协同数据',
  `ProjectShareDepartCount` int NOT NULL DEFAULT 0 COMMENT '主创项目-开启部门协同数据',
  `ProjectShareCrossInternalCount` int NOT NULL DEFAULT 0 COMMENT '主创项目-开启跨公司协同数据',
  `ProjectSharePlatformCount` int NOT NULL DEFAULT 0 COMMENT '主创项目-开启平台协同数据',
  `MyPostCount` int NOT NULL DEFAULT 0 COMMENT 'HR发布的岗位数量',
  `MyPostRecruitNumber` int NOT NULL DEFAULT 0 COMMENT 'HR发布的岗位招聘总人数',
  `MyPostDeliveryNum` int NOT NULL DEFAULT 0 COMMENT '投递用户数',
  `MyPostInterviewNum` int NOT NULL DEFAULT 0 COMMENT '面试用户数',
  `MyPostInductionNum` int NOT NULL DEFAULT 0 COMMENT '入职用户数',
  PRIMARY KEY (`UserId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '顾问数据' ROW_FORMAT = Dynamic;


-- ----------------------------
-- 初始化顾问数据
-- ----------------------------
INSERT INTO  user_hr_data(UserId) SELECT UserId FROM user_hr WHERE NOT EXISTS(SELECT UserId FROM user_hr_data WHERE user_hr_data.UserId=user_hr.UserId);


UPDATE user_hr_data  
INNER JOIN (
	SELECT HrId,COUNT(DISTINCT D.SeekerId) MyPostDeliveryNum
	FROM Post_Delivery D
	JOIN Post P ON D.PostId=P.PostId
	JOIN Project PJ ON PJ.ProjectId=P.ProjectId
	GROUP BY PJ.HrId
)T ON T.HrId=user_hr_data.UserId
SET user_hr_data.MyPostDeliveryNum=T.MyPostDeliveryNum;

UPDATE user_hr_data  
INNER JOIN (
	SELECT T.HrId,SUM(IF(T.`Status`=5,Num,0)) as MyPostInductionNum,SUM(IF(T.`Status`=3,Num,0)) as MyPostInterviewNum FROM (
		SELECT PJ.HrId,R.`Status`,COUNT(DISTINCT D.SeekerId) Num
		FROM Recruit R 
		join Post_Delivery D ON R.DeliveryId=D.DeliveryId
		JOIN Post P ON D.PostId=P.PostId
		JOIN Project PJ ON PJ.ProjectId=P.ProjectId
		GROUP BY PJ.HrId,R.`Status`
	) T
	GROUP BY T.HrId
)T ON T.HrId=user_hr_data.UserId
SET user_hr_data.MyPostInductionNum=T.MyPostInductionNum,user_hr_data.MyPostInterviewNum=T.MyPostInterviewNum


-- ----------------------------
-- 用户访问职位
-- ----------------------------
DROP TABLE IF EXISTS `user_post_visit`;
CREATE TABLE `user_post_visit` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL,
  `HrId` varchar(18) NULL,
  `Post` int(2) NOT NULL DEFAULT 0 COMMENT '职位类别',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_post_visit_UserId_Post_CreatedTime` (`UserId`, `Post`, `CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

