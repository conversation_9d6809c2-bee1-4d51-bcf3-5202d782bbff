﻿using QuestPDF.Fluent;
using QuestPDF.Infrastructure;

namespace Infrastructure.CommonService.QuestPDF;

public class PDFHelper
{
    /// <summary>
    /// 获取生成pdf的字节数组
    /// </summary>
    /// <param name="document"></param>
    /// <returns></returns>
    public static byte[] GeneratePdfToByte(IDocument document)
    {
        return document.GeneratePdf();
    }

    /// <summary>
    /// 查看生成的pdf
    /// </summary>
    /// <param name="document"></param>
    public static void GeneratePdfToShow(IDocument document)
    {
        document.GeneratePdfAndShow();
    }
}
