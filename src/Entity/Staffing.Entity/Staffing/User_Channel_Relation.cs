﻿namespace Staffing.Entity.Staffing;

// /// <summary>
// /// 渠道顾问关系
// /// </summary>
// [Table("user_channel_relation")]
// public class User_Channel_Relation
// {
//     /// <summary>
//     /// 渠道Id
//     /// </summary>
//     [Key]
//     public string ChannelId { get; set; } = default!;

//     /// <summary>
//     /// 顾问Id
//     /// </summary>
//     public string HrId { get; set; } = default!;

//     public DateTime CreatedTime { get; set; } = DateTime.Now;
// }