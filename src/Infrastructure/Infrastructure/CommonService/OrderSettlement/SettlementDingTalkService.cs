﻿// using Infrastructure.DingTalk;
// using Infrastructure.Extend;
// using Microsoft.Extensions.DependencyInjection;
// using Staffing.Entity;

// namespace Infrastructure.CommonService.OrderSettlement;

// [TypeService(ServiceLifetime.Transient)]
// public class SettlementDingTalkService : DingTalkProcessInstanceApply
// {
//     protected readonly StaffingContext _context;
//     public SettlementDingTalkService(DingTalkOAProcessService dingTalkProcess, StaffingContext context) 
//         : base(dingTalkProcess)
//     {
//         _context = context;
//     }

//     public override void GetApproved(string processInstanceId)
//     {
//         base.GetApproved(processInstanceId);
//         var instance = _context.Dingding_Process_Instancet.FirstOrDefault(f => f.ProcessInstanceId == processInstanceId);
//         if(instance != null && !string.IsNullOrWhiteSpace(instance.BussinessId))
//         {
//             var settlements = _context.Project_Settlement.Where(w => w.OrderId == instance.BussinessId).ToList();
//             settlements.ForEach(f =>
//             {
//                 f.Status = Staffing.Entity.Staffing.SettleStatus.已结算;
//                 f.SettlementTime = DateTime.Now;
//                 f.SettlementNum = processInstanceId;
//             });
//         }
//         _context.SaveChanges();
//     }

//     public override void GetRejected(string processInstanceId)
//     {
//         base.GetRejected(processInstanceId);
//         var instance = _context.Dingding_Process_Instancet.FirstOrDefault(f => f.ProcessInstanceId == processInstanceId);
//         if (instance != null && !string.IsNullOrWhiteSpace(instance.BussinessId))
//         {
//             var settlements = _context.Project_Settlement.Where(w => w.OrderId == instance.BussinessId).ToList();
//             settlements.ForEach(f =>
//             {
//                 f.Status = Staffing.Entity.Staffing.SettleStatus.结算失败;
//                 f.SettlementTime = DateTime.Now;
//                 f.SettlementNum = processInstanceId;
//             });
//         }
//         _context.SaveChanges();
//     }

//     public override void Cancelled(string processInstanceId)
//     {
//         base.Cancelled(processInstanceId);
//     }
// }
