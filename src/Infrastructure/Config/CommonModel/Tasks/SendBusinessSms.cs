namespace Config.CommonModel.Tasks;

public class SendBusinessSms
{
    /// <summary>
    /// 短信模板
    /// </summary>
    public string? SmsTempCode { get; set; }

    /// <summary>
    /// 短信json参数
    /// </summary>
    public SendBusinessSmsTempCode SmsTempJson { get; set; } = default!;

    /// <summary>
    /// 手机号
    /// </summary>
    public List<SendInviteSmsUserInfo> ToUsers { get; set; } = new List<SendInviteSmsUserInfo>();
}

public class SendBusinessSmsTempCode
{
    public string? seeker { get; set; }
    public string? hr { get; set; }
    public string? position { get; set; }
    public string? jumpurl { get; set; }
}

public class SendInviteSmsUserInfo
{
    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }
}