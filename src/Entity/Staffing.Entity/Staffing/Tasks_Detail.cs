﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 任务明细
/// </summary>
[Table("tasks_detail")]
public class Tasks_Detail
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 任务Id
    /// </summary>
    public string TaskId { get; set; } = default!;

    /// <summary>
    /// 检索关键字（预留）
    /// </summary>
    public string Key { get; set; } = string.Empty;

    /// <summary>
    /// 内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 结果
    /// </summary>
    public TaskHandlingResult Result { get; set; }

    /// <summary>
    /// 状态分组（用来查询比如重复的）
    /// </summary>
    public TaskHandlingGroupStatus GroupStatus { get; set; } = TaskHandlingGroupStatus.默认;

    /// <summary>
    /// 结果文本
    /// </summary>
    public string? ResultText { get; set; }

    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Tasks Tasks { get; set; } = default!;
}
