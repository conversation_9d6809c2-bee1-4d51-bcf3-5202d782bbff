﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Config.CommonModel.ZhaoPinYun
{
    public class ZPYPublishJob
    {
        public string? openId {  get; set; }//企业会员唯一标识
        public string? jobId { get; set; }//职位 ID
        public string? customerId { get; set; }//客户 ID 为空时归属到默认客户
        public string? name { get; set; }//职位名称
        public string? categoryId { get; set; }//职位分类 ID
        public string? industryId { get; set; }//所属行业 ID
        public int employeeType { get; set; }//雇佣类型：1、全职；2、兼职；3、实习；4、小时工；9、其他；
        public string? description { get; set; }//职位描述
        public int minimumEducation { get; set; }//最低学历：0、不限；1、初中及以下；2、中专/技校；3、高中；4、大专；5、本科；6、硕士；7、博士；
        public string? workYear { get; set; }//工作年限：0、不限；1、应届毕业生；2、1 年以下；3、1-3 年；4、3-5 年；5、5-10 年；6、10年以上；
        public int salaryType { get; set; }//薪资类型 1、月薪；2、时薪；
        public int salaryStart { get; set; }//薪资范围(起始)
        public int salaryEnd { get; set; }//薪资范围(结束)
        public int monthlySalaryNum { get; set; }//月薪数
        public int zhaopinPersonNum { get;set; }//招聘人数
        public string? workAddress { get; set; }//工作地点
        public string? workAddressGuide { get; set; }//工作地点到达指引
        public string? city { get; set; }//所在城市
        public string? district { get; set; }//行政区
        public string? business { get; set; }//商圈
    }

    public class PersonResumeInfo
    {
        public string? openId { get; set; }//企业会员唯一标识
        public string? traceId { get; set; }//人才跟进 id 用于判定是否是同一个人
        public string? fileUrl { get; set; }//简历文件地址
        public string? avatarUrl { get; set; }//头像链接
        public basicInfo? basicInfo { get; set; }//基础信息
        public contactInfo? contactInfo { get; set; }//联系方式
        public educationExperiences? educationExperiences { get; set; }//学历经历
    }

    public class basicInfo
    {
        public string? name { get; set; }//中文姓名
        public string? gender { get; set; }//性别
        public string? height { get; set; }//身高
        public string? nationalIdentityNumber { get; set; }//身份证号
        public string? dateOfBirth { get; set; }//出生日期
        public string? ethnic { get; set; }//民族
    }

    public class contactInfo
    {
        public string? phoneNumber { get; set; }//手机
        public string? homePhoneNumber { get; set; }//固定电话
        public string? email { get; set; }//邮箱
        public string? qq { get; set; }//QQ 号
        public string? wechat { get; set; }//微信
    }

    public class educationExperiences
    {
        public string? startTimeYear { get; set; }//开始时间年份 格式 yyyy
        public string? startTimeMonth { get; set; }//开始时间月份 格式 MM
        public string? endTimeYear { get; set; }//结束时间年份 格式 yyyy
        public string? endTimeMonth { get; set; }//结束时间月份 格式 MM
        public string? schoolName { get; set; }//学校
    }

    public class ChatMessage
    {
        public string? openId { get; set; }//企业会员唯一标识
        public string? chatId { get; set; }//聊天会话 ID
        public List<chatInfo>? messages { get; set; }//消息内容列表
    }

    public class chatInfo 
    {
        public string? role { get; set; }//消息角色 assistant：机器人 user：用户
        public int? type { get; set; }//回复类型：1 消息；2 动作
        public string? content { get; set; }//回复内容
        public List<string>? commands { get; set; }//动作内容 OBTAIN_RESUME_INFO：获取到简历 OBTAIN_TEL_INFO：获取到电话 OBTAIN_WECHAT_INFO：获取到微信
        public long? messageTime { get; set; }//消息时间：时间戳（毫秒）
    }
    
    public class ChatMessageReceive
    {
        public List<messageList>? messages { get; set; }
    }
    public class messageList
    {
        public string? messageId { get; set; }//消息 ID
        public int? type { get; set; }//回复类型：1 消息；2 动作
        public string? content { get; set; }//消息内容
        public List<string>? commands { get; set; }//OBTAIN_TEL：请求获取电话 OBTAIN_WECHAT：请求获取微信 OBTAIN_RESUME：请求获取简历
    }
    public class GetChatMessageHistory
    {
        public string? openId { get; set; }//企业会员唯一标识
        public string? chatId { get; set; }//聊天会话 ID
        public long? startTime { get; set; }//消息时间开始时间：时间戳（毫秒）
        public long? endTime { get; set; }//消息时间结束时间：时间戳（毫秒）
        public int? pageNo { get; set; }//页码
        public int? pageSize { get; set; }//每页数量
    }
    public class ChatMessageHistoryRecieve
    {
        public long? total { get; set; }//总数
        public List<recordList>? records { get; set; }//分页数据
    }
    public class recordList
    {
        public long? messageId { get; set; }//消息ID
        public string? role { get; set; }//消息角色 assistant：机器人 user：用户
        public int? type { get; set; }//回复类型：1 消息；2 动作
        public string? content { get; set; }//消息内容
        public List<string>? commands { get; set; }//动作内容 OBTAIN_TEL：获取电话 0BTAIN_WECHAT：获取微信 OBTAIN_RESUME：获取简历
        public long? messageTime { get; set; }//消息时间戳（毫秒）
    }
}
