﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.CommonModel.Ndn;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 数字诺亚项目转账
/// </summary>
[Table("ndn_project_transfers")]
public class Ndn_Project_Transfers
{
    /// <summary>
    /// 主键
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 转帐方的项目编码
    /// </summary>
    public string FromProjectId { get; set; } = default!;

    /// <summary>
    /// 收款方的项目编码
    /// </summary>
    public string ToProjectId { get; set; } = default!;

    /// <summary>
    /// 转帐方的项目名称
    /// </summary>
    public string? FromProjectName { get; set; }

    /// <summary>
    /// 收款方的项目名称
    /// </summary>
    public string? ToProjectName { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [ConcurrencyCheck]
    public NdnAuditStatus Status { get; set; }

    /// <summary>
    /// 服务奖金状态
    /// </summary>
    [ConcurrencyCheck]
    public NdnAuditStatus BonusStatus { get; set; } = NdnAuditStatus.未开始;

    /// <summary>
    /// 申请奖金时间
    /// </summary>
    public DateTime? BonusTime { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 数字诺亚服务奖金Id
    /// </summary>
    public string? NdnId { get; set; }

    /// <summary>
    /// 操作人Id
    /// </summary>
    public string? OperatorId { get; set; }

    /// <summary>
    /// 操作人姓名
    /// </summary>
    public string? OperatorName { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Ndn_Project Project_From { get; set; } = default!;
    public Ndn_Project Project_To { get; set; } = default!;
}
