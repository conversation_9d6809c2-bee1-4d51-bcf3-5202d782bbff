﻿using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces.Hr;
using Staffing.Model.Hr.Bounty;
using Infrastructure.CommonService;
using Pipelines.Sockets.Unofficial.Arenas;
using Config.CommonModel.Ndn;
using Infrastructure.Proxy;
using Infrastructure.Exceptions;
using Config.CommonModel;
using Infrastructure.CommonService.Ndn;
using Config.CommonModel.Business;

namespace Staffing.Core.Services.Hr;

[Service(ServiceLifetime.Transient)]
public class BountyProjectService : IBountyProjectService
{
    private readonly StaffingContext _context;
    private readonly RequestContext _user;
    private readonly CacheHelper _cacheHelper;
    private readonly ICommonService _commonService;
    private readonly LogManager _log;
    private readonly CommonDicService _commonDicService;
    private readonly CommonUserService _commonUserService;
    private readonly NdnApi _ndnApi;
    private readonly INdnService _ndnService;
    private readonly CommonCacheService _commonCacheService;
    public BountyProjectService(StaffingContext context,
        RequestContext user,
        CacheHelper cacheHelper,
        ICommonService commonService,
        LogManager log,
        CommonDicService commonDicService,
        CommonUserService commonUserService,
        NdnApi ndnApi,
        CommonCacheService commonCacheService,
        INdnService ndnService)
    {
        _user = user;
        _context = context;
        _cacheHelper = cacheHelper;
        _commonService = commonService;
        _log = log;
        _commonDicService = commonDicService;
        _commonUserService = commonUserService;
        _ndnApi = ndnApi;
        _commonCacheService = commonCacheService;
        _ndnService = ndnService;
    }

    public BountyProjectSumData GetProjectSumData()
    {
        var response = new BountyProjectSumData();

        // 订单信息
        var teamInfos = _context.Post_Bounty.Where(w => w.Status == BountyStatus.交付中)
            .Select(s => new
            {
                s.Id,
                s.Post.ProjectId,
                s.Status,
                s.CreatedTime,
                s.GuaranteeStatus,
                Money = s.Money,
                // SettleStatus = s.project_Settlements.Where(a => a.Type == SettleType.协同).Select(s => s.Status).FirstOrDefault(),
                // SettleId = s.project_Settlements.Where(a => a.Type == SettleType.协同).Select(s => s.Id).FirstOrDefault()
            }).ToList();

        response.ExecOrderNum = teamInfos == null ? 0
            : teamInfos.Where(w => w.Status == BountyStatus.交付中).Count();
        response.ExecOrderMoney = teamInfos == null ? 0
            : teamInfos.Where(w => w.Status == BountyStatus.交付中).Sum(s => s.Money);
        response.SucceedOrderNum = teamInfos == null ? 0
            : teamInfos.Where(w => w.GuaranteeStatus == GuaranteeStatus.过保).Count();
        response.SucceedOrderMoney = teamInfos == null ? 0
            : teamInfos.Where(w => w.Status == BountyStatus.交付中).Sum(s => s.Money);
        // response.ExpendMoney = teamInfos == null ? 0
        //     : teamInfos.Where(w => w.SettleStatus == SettleStatus.已结算 && !string.IsNullOrWhiteSpace(w.SettleId)).Sum(s => s.Money);
        response.FreezedMoney = _context.Ndn_Hr_Wallet.Sum(s => s.Amount);
        response.LeftStockNum = _context.Post.Where(w => w.Status == PostStatus.发布中).Sum(s => s.LeftStock);
        response.IncomeMoney = response.SucceedOrderMoney;
        response.ProfitMoney = response.IncomeMoney - response.ExpendMoney;

        return response;
    }

    public GetProjectSummaryResponse GetProjectSummary(GetProjectRequest model)
    {
        if (string.IsNullOrWhiteSpace(model.ProjectCode))
            throw new BadRequestException("缺少ProjectCode");

        var predicate = PredicateBuilder.New<Ndn_Project>(true);

        predicate = predicate.And(p => p.ProjectCode == model.ProjectCode);

        var result = _context.Ndn_Project.Where(predicate)
            .Select(s => new GetProjectSummaryResponse
            {
                ProjectCode = s.ProjectCode,
                ProjectName = s.ProjectName
            }).FirstOrDefault();

        if (result == null)
            throw new BadRequestException("未找到项目");

        return result;
    }

    public async Task<GetProjectInfo> GetProject(GetProjectRequest model)
    {
        // var predicate = PredicateBuilder.New<Ndn_Project>(true);

        // predicate = predicate.And(p => p.ProjectCode == model.ProjectCode);

        if (string.IsNullOrWhiteSpace(model.ProjectCode))
            throw new BadRequestException("缺少ProjectCode");

        var localProj = _context.Ndn_Project.Where(x => x.ProjectCode == model.ProjectCode)
            .Select(s => new
            {
                s.ContractNo,
                s.ContractUrl,
                s.Type
            }).FirstOrDefault();

        var project = await _ndnApi.GetProjectInfo(model.ProjectCode);

        if (project == null)
            throw new BadRequestException("数字诺亚项目不存在");

        var result = new GetProjectInfo
        {
            ProjectCode = project.projectCode,
            ProjectName = project.projectName,
            BookName = project.bookName,
            ServiceSpecialist = project.beneficiaryName,
            RemittanceStatus = project.collectionStatus,
            CustomerName = project.customerName,
            ProjectBusinessType = $"{project.projectCategory}-{project.productName}",
            FrozenAmount = project.budget?.frozenAmount ?? 0,
            ProjectBudget = (project.budget?.surplusAmount ?? 0) + (project.budget?.frozenAmount ?? 0),
            // ServiceBonusAmount = _context.Ndn_Service_Bonus.Where(w => w.ProjectCode == model.ProjectCode && w.Ndn_Project_Transfers.BonusStatus == NdnAuditStatus.已完成).Sum(s => s.Project_Settlement.ActualSettlementMoney),
            ProjectCompletionTime = project.endTime,
            ProjectCreateTime = project.startTime,
            ProjectFlag = project.projectFlag,
            ProjectIncomeAmount = _context.Ndn_Project_Transfer_Details.Where(w => w.Project_To.ProjectCode == model.ProjectCode && w.Status == NdnAuditStatus.已完成).Sum(s => s.Ndn_Project_Transfers.Amount),
            ProjectStatus = project.status,
            ContractUrl = localProj?.ContractUrl,
            ContractNo = localProj?.ContractNo,
            Type = localProj?.Type
        };

        return result;
    }

    public async Task<EmptyResponse> SaveInvoice(SaveInvoice model)
    {
        var result = new EmptyResponse();
        await Task.CompletedTask;
        // //查询当前用户工号
        // var jobNo = _commonUserService.GetUserNo(_user.Id);

        // if (string.IsNullOrWhiteSpace(jobNo))
        //     throw new BadRequestException("未获取到当前用户工号");

        // //请求数字诺亚提交发票
        // await _ndnService.InvoiceIssuance(new ProcessTasks
        // {
        //     Id = model.Id,
        //     HrNo = jobNo
        // });

        return result;
    }

    public async Task<EmptyResponse> SaveTransfer(SaveTransfer model)
    {
        var result = new EmptyResponse();
        await Task.CompletedTask;
        // if (string.IsNullOrWhiteSpace(model.Id))
        //     throw new BadRequestException("缺少Id");

        // //查询当前用户工号
        // var jobNo = _commonUserService.GetUserNo(_user.Id);

        // if (string.IsNullOrWhiteSpace(jobNo))
        //     throw new BadRequestException("未获取到当前用户工号");

        // //请求数字诺亚提交转账
        // await _ndnService.TransferReimbursement(new ProcessTasks
        // {
        //     Id = model.Id,
        //     HrNo = jobNo
        // });

        return result;
    }

    public async Task<EmptyResponse> SaveServiceBonus(SaveServiceBonus model)
    {
        var result = new EmptyResponse();
        await Task.CompletedTask;
        // //查询当前用户工号
        // var jobNo = _commonUserService.GetUserNo(_user.Id);

        // if (string.IsNullOrWhiteSpace(jobNo))
        //     throw new BadRequestException("未获取到当前用户工号");

        // //请求数字诺亚提交服务奖金
        // await _ndnService.ServiceBonus(new ProcessTasks
        // {
        //     Id = model.Id,
        //     HrNo = jobNo
        // });

        return result;
    }

    public FlowInfoResponse GetFlowInfo(GetFlowInfo model)
    {
        var result = new FlowInfoResponse();

        if (string.IsNullOrWhiteSpace(model.ProjectCode))
            throw new BadRequestException("缺少ProjectCode");

        var sql = _context.Ndn_Project_Invoice.Where(w => w.Ndn_Project_Transfer_Details.Project_To.ProjectCode == model.ProjectCode && w.Status != NdnAuditStatus.未开始 && w.Status != NdnAuditStatus.待提交)
        .Select(s => new FlowInfo
        {
            Id = s.Id,
            CreatedTime = s.CreatedTime,
            Status = s.Status,
            Remark = s.Description,
            TypeText = "开发票"
        }).Union(_context.Ndn_Project_Transfer_Details.Where(w => w.Project_From.ProjectCode == model.ProjectCode && w.Status != NdnAuditStatus.未开始 && w.Status != NdnAuditStatus.待提交)
        .Select(s => new FlowInfo
        {
            Id = s.Id,
            CreatedTime = s.CreatedTime,
            Status = s.Status,
            Remark = s.Description,
            TypeText = "转账报销"
        })).Union(_context.Ndn_Project_Transfers.Where(w => w.Project_To.ProjectCode == model.ProjectCode && w.BonusStatus != NdnAuditStatus.未开始 && w.BonusStatus != NdnAuditStatus.待提交)
        .Select(s => new FlowInfo
        {
            Id = s.Id,
            CreatedTime = s.BonusTime ?? s.CreatedTime,
            Status = s.BonusStatus,
            Remark = s.Description,
            TypeText = "发放服务奖金"
        }));

        result.Total = sql.Select(s => s.Id).Count();
        result.Rows = sql
            .OrderByDescending(o => o.CreatedTime)
            .Skip((model.PageIndex - 1) * model.PageSize)
            .Take(model.PageSize).ToList();

        result.Rows.ForEach(x =>
        {
            x.StatusText = x.Status.GetDescription();
        });

        return result;
    }

    public async Task<InvoiceOverviewResponse> GetInvoiceOverview(GetProjectRequest model)
    {
        if (string.IsNullOrWhiteSpace(model.ProjectCode))
            throw new BadRequestException("缺少ProjectCode");

        var result = _context.Ndn_Project.Where(x => x.ProjectCode == model.ProjectCode)
            .Select(s => new InvoiceOverviewResponse
            {
                ProjectCode = s.ProjectCode,
                ProjectName = s.ProjectName,
                BookCode = s.BookCode,
                CompanyName = s.BookName
            }).FirstOrDefault();

        if (result == null)
            throw new BadRequestException("未找到项目");

        var book = _context.Ndn_Books.Where(x => x.BookCode == result.BookCode)
            .Select(s => new
            {
                s.BookCode,
                s.BookName,
                s.InvoiceInfo,
                s.ContractSeal
            }).FirstOrDefault();

        result.InvoiceInfo = book?.InvoiceInfo ?? new NdnInvoiceInfo();
        result.ContractSeal = book?.ContractSeal;

        var ndnBook = await _ndnService.GetBookInfo(result.BookCode);

        result.BillingClerk = ndnBook?.drawerName;
        result.Cashier = ndnBook?.cashierName;
        result.Accountant = ndnBook?.bookKeeperName;
        result.FinanceMinister = ndnBook?.financeMinisterName;

        var invoiceAmount = _context.Ndn_Project_Invoice
        .Where(w => w.Ndn_Project_Transfer_Details.Project_To.ProjectCode == model.ProjectCode)
        .GroupBy(g => g.Status)
        .Select(s => new
        {
            s.Key,
            Amount = s.Sum(s => s.Ndn_Project_Transfer_Details.Ndn_Project_Transfers.Amount)
        }).ToList();

        result.InvoicedAmount = invoiceAmount.Where(x => x.Key == NdnAuditStatus.已完成).Sum(s => s.Amount);
        result.PendingSubmissionAmount = invoiceAmount.Where(x => x.Key == NdnAuditStatus.待提交).Sum(s => s.Amount);
        result.UnderReviewAmount = invoiceAmount.Where(x => x.Key == NdnAuditStatus.进行中).Sum(s => s.Amount);
        result.RejectedAmount = invoiceAmount.Where(x => x.Key == NdnAuditStatus.已驳回).Sum(s => s.Amount);

        //天眼查
        var ent = await Task.Run(() => _commonCacheService.GetAgentEntFromTyc(result.CompanyName));
        if (ent != null)
        {
            result.EntIndustry = ent.TycIndustry;
            result.EntLegalPerson = ent.LegalPersonName;
        }

        return result;
    }

    public async Task<EmptyResponse> UpdateBook(UpdateBook model)
    {
        if (string.IsNullOrWhiteSpace(model.BookCode))
            throw new BadRequestException("缺少BookCode");

        var invoice = _context.Ndn_Books.FirstOrDefault(f => f.BookCode == model.BookCode);

        if (invoice == null)
        {
            //数字诺亚查询
            var ndnBook = await _ndnService.GetBookInfo(model.BookCode);
            if (ndnBook == null)
                throw new BadRequestException("未找到账套信息");

            invoice = new Ndn_Books
            {
                BookCode = model.BookCode,
                BookName = ndnBook.bookName ?? string.Empty,
                OperatorId = _user.Id,
                OperatorName = _user.Name
            };
            _context.Ndn_Books.Add(invoice);
        }

        if (!string.IsNullOrWhiteSpace(model.ContractSeal))
            invoice.ContractSeal = model.ContractSeal;

        if (!string.IsNullOrWhiteSpace(model.InvoiceTitle))
            invoice.InvoiceInfo.InvoiceTitle = model.InvoiceTitle;

        if (!string.IsNullOrWhiteSpace(model.TaxNumber))
            invoice.InvoiceInfo.TaxNumber = model.TaxNumber;

        if (!string.IsNullOrWhiteSpace(model.Bank))
            invoice.InvoiceInfo.Bank = model.Bank;

        if (!string.IsNullOrWhiteSpace(model.BankAccount))
            invoice.InvoiceInfo.BankAccount = model.BankAccount;

        if (!string.IsNullOrWhiteSpace(model.Address))
            invoice.InvoiceInfo.Address = model.Address;

        if (!string.IsNullOrWhiteSpace(model.Phone))
            invoice.InvoiceInfo.Phone = model.Phone;

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public InvoiceListResponse GetInvoiceList(GetInvoiceList model)
    {
        var result = new InvoiceListResponse();

        if (string.IsNullOrWhiteSpace(model.ProjectCode))
            throw new BadRequestException("缺少ProjectCode");

        var predicate = PredicateBuilder.New<Ndn_Project_Invoice>(true);

        predicate = predicate.And(p => p.Ndn_Project_Transfer_Details.Project_To.ProjectCode == model.ProjectCode);

        var sql = _context.Ndn_Project_Invoice.Where(predicate);
        result.Total = sql.Select(s => s.Id).Count();
        result.Rows = sql
            .OrderByDescending(o => o.CreatedTime)
            .Skip((model.PageIndex - 1) * model.PageSize)
            .Select(s => new InvoiceList
            {
                Id = s.Id,
                Status = s.Status,
                StatusText = s.Status.GetDescription(),
                InvoiceUrl = s.InvoiceUrl,
                ContractUrl = s.Ndn_Project_Transfer_Details.ContractUrl,
                CustomerName = s.Ndn_Project_Transfer_Details.Project_From.BookName,
                CreatedTime = s.CreatedTime,
                OperatorName = s.OperatorName
            }).ToList();

        return result;
    }

    public TransferListResponse GetTransferList(GetTransferList model)
    {
        var result = new TransferListResponse();

        if (string.IsNullOrWhiteSpace(model.ProjectCode))
            throw new BadRequestException("缺少ProjectCode");

        var predicate = PredicateBuilder.New<Ndn_Project_Transfer_Details>(true);

        predicate = predicate.And(p => p.Project_From.ProjectCode == model.ProjectCode);

        if (model.Status.HasValue)
            predicate = predicate.And(p => p.Status == model.Status);

        if (model.MinAmount.HasValue)
            predicate = predicate.And(p => p.Ndn_Project_Transfers.Amount >= model.MinAmount);

        if (model.MaxAmount.HasValue)
            predicate = predicate.And(p => p.Ndn_Project_Transfers.Amount <= model.MaxAmount);

        if (!string.IsNullOrWhiteSpace(model.CompanyName))
            predicate = predicate.And(p => p.Project_To.BookName.Contains(model.CompanyName));

        var sql = _context.Ndn_Project_Transfer_Details.Where(predicate);
        result.Total = sql.Select(s => s.Id).Count();
        result.Rows = sql
            .OrderByDescending(o => o.CreatedTime)
            .Skip((model.PageIndex - 1) * model.PageSize)
            .Take(model.PageSize)
            .Select(s => new TransferList
            {
                Id = s.Id,
                Status = s.Status,
                StatusText = s.Status.GetDescription(),
                CreatedTime = s.CreatedTime,
                OperatorName = s.OperatorName,
                ContractUrl = s.ContractUrl,
                InvoiceUrl = s.InvoiceUrl,
                FeeName = "招聘费",
                ImplementationAmount = s.Ndn_Project_Transfers.Amount,
                ReimbursementAmount = s.Ndn_Project_Transfers.Amount,
                ReimbursementAccount = s.Project_From.BookName,
                CompanyName = s.Project_To.BookName
            }).ToList();

        return result;
    }

    public GetServiceBonusBatchResponse GetServiceBonusBatch(GetServiceBonusBatch model)
    {
        var result = new GetServiceBonusBatchResponse();

        if (string.IsNullOrWhiteSpace(model.ProjectCode))
            throw new BadRequestException("缺少ProjectCode");

        var predicate = PredicateBuilder.New<Ndn_Project_Transfers>(true);

        predicate = predicate.And(x => x.Project_To.ProjectCode == model.ProjectCode
        && x.Status == NdnAuditStatus.已完成);

        var sql = _context.Ndn_Project_Transfers.Where(predicate);
        result.Total = sql.Select(s => s.Id).Count();

        var data = sql
            .OrderByDescending(o => o.CreatedTime)
            .Skip((model.PageIndex - 1) * model.PageSize)
            .Take(model.PageSize)
            .Select(s => new
            {
                Id = s.Id,
                BatchName = s.Project_To.BookName,
                CreatedTime = s.CreatedTime,
                Status = s.Status,
                s.BonusStatus,
                StatusText = s.Status.GetDescription()
            }).ToList();

        result.Rows = data.Select(s => new ServiceBonusBatch
        {
            Id = s.Id,
            BatchName = s.BatchName,
            CreatedTime = s.CreatedTime,
            Status = s.BonusStatus,
            StatusText = s.BonusStatus.GetDescription()
        }).ToList();

        // result.Rows.ForEach(x =>
        // {
        //     x.BatchName = $"{x.BatchName}_{x.CreatedTime.ToYYYY_MM_DD()}";
        // });

        return result;
    }

    public ServiceBonusListResponse GetServiceBonusList(GetServiceBonusList model)
    {
        var result = new ServiceBonusListResponse();

        if (string.IsNullOrWhiteSpace(model.BatchId))
            throw new BadRequestException("缺少BatchId");

        // var predicate = PredicateBuilder.New<Ndn_Service_Bonus>(true);

        // predicate = predicate.And(x => x.TransferId == model.BatchId);

        // var sql = _context.Ndn_Service_Bonus.Where(predicate);
        // result.Total = sql.Select(s => s.Id).Count();

        // result.Rows = sql
        //     .OrderByDescending(o => o.CreatedTime)
        //     .Skip((model.PageIndex - 1) * model.PageSize)
        //     .Take(model.PageSize)
        //     .Select(s => new ServiceBonusList
        //     {
        //         Id = s.Id,
        //         Status = s.Ndn_Project_Transfers.BonusStatus,
        //         StatusText = s.Ndn_Project_Transfers.BonusStatus.GetDescription(),
        //         CreatedTime = s.CreatedTime,
        //         CommissionAmount = s.Project_Settlement.SettlementMoney ?? 0,
        //         DeliveryUserName = s.Project_Settlement.Project_Teambounty.TeamHrName,
        //         NkpProjectName = s.Project_Settlement.Project_Teambounty.ProjectName,
        //         NkpPostName = s.Project_Settlement.Project_Teambounty.PostName,
        //         OrderNo = s.Id
        //     }).ToList();

        return result;
    }

    public PersonRankResponse GetRankList()
    {
        var today = DateTime.Now;
        DateTime firstDayOfMonth = new DateTime(today.Year, today.Month, 1);
        var success = _context.Post_Bounty.Where(w => w.Status == BountyStatus.交付中 && w.GuaranteeStatus == GuaranteeStatus.过保 && w.CreatedTime >= firstDayOfMonth);
        var response = new PersonRankResponse
        {
            UpdateTime = today
        };
        // 统计协同
        var result = success.GroupBy(g => g.TeamHrId).Select(s => new PersonRankInfo
        {
            HrId = s.Key,
            Money = s.Sum(s => s.Money),
            Count = s.Count()
        }).ToList();

        // // 统计渠道,这里的渠道，指的是申丽华等帮诺快聘账号交付的内部员工，并没有外部
        // var result_channel = success.Where(w => w.TeamHrId != w.ChannelHrId && !string.IsNullOrWhiteSpace(w.ChannelHrId))
        //     .GroupBy(g => new { g.ChannelHrName, g.ChannelHrId })
        //     .Select(s => new PersonRankInfo
        //     {
        //         HrId = s.Key.ChannelHrId,
        //         Money = 0,
        //         Count = s.Count()
        //     }).ToList();

        // // 合并
        // if (result != null && result_channel != null && result_channel.Count > 0)
        // {
        //     result = result.Concat(result_channel!).ToList();
        // }

        // 按money排序并取前50条
        result = result.GroupBy(g => g.HrId).Select(s => new PersonRankInfo
        {
            HrId = s.Key,
            Money = s.Sum(s => s.Money),
            Count = s.Sum(s => s.Count)
        }).OrderByDescending(o => o.Money).Take(50).ToList();

        response.Total = result.Count;

        var userIds = result.Select(s => s.HrId).Distinct().ToList();
        var userInfos = _context.User_Hr.Where(w => userIds.Contains(w.UserId)).ToList();

        //查询员工工号
        var userNos = _context.User_DingDing.Where(x => userIds.Contains(x.UserId)).Select(s => new { s.UserId, s.DingJobNo }).ToList();

        var ndnEmployees = new Dictionary<string, NdnEmployee>();
        //查询员工帐套
        foreach (var userId in userIds)
        {
            var userNo = userNos.FirstOrDefault(x => x.UserId == userId)?.DingJobNo;
            if (string.IsNullOrWhiteSpace(userNo))
                continue;
            try
            {
                var empInfo = _ndnApi.GetEmployee(userNo).GetAwaiter().GetResult();
                ndnEmployees.Add(userId, empInfo);
                Task.Delay(10);
            }
            catch (Exception ex)
            {
                _log.Error($"首页排行榜：数字诺亚员工信息查询失败", Tools.GetErrMsg(ex), userNo);
            }
        }

        // 查询员工最新姓名
        var hrInfos = _context.User_Hr.Where(w => userIds.Contains(w.UserId)).Select(s => new { s.UserId, s.NickName }).ToList();

        foreach (var item in result)
        {
            item.HrCompany = "未获取到账套关系";
            var userInfo = userInfos.FirstOrDefault(f => f.UserId == item.HrId);
            if (userInfo != null)
            {
                item.Avatar = userInfo.Avatar;
                item.HrPostName = userInfo.Post;
            }
            _ = ndnEmployees.TryGetValue(item.HrId, out NdnEmployee? empInfo);
            if (empInfo != null)
            {
                item.HrCompany = empInfo.bookName;
            }
            var hrInfo = hrInfos.FirstOrDefault(f => f.UserId == item.HrId);
            if (hrInfo != null)
            {
                item.HrName = hrInfo.NickName;
            }
        }

        response.Rows = result;
        return response;
    }

    public TodoListResponse GetTodoList(TodoListReqeust request)
    {
        var response = new TodoListResponse();
        var predicate = PredicateBuilder.New<Ndn_Todo>(true);
        if (request.TabSelect != TabEnum.全部)
        {
            predicate = predicate.And(p => p.Status == NdnTodoStatus.待办 || p.Status == NdnTodoStatus.进行中);
        }
        var sql = _context.Ndn_Todo.Where(predicate);
        response.Total = sql.Select(s => s.Id).Count();
        response.Rows = sql
            .OrderByDescending(o => o.CreatedTime)
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(s => new TodoInfo
            {
                Id = s.Id,
                RelatedEntityId = s.RelatedEntityId,
                Name = s.Title,
                ProjectCode = s.ProjectCode,
                Description = s.Description,
                Type = s.RelatedEntityType,
                Status = s.Status,
                CreatedTime = s.CreatedTime
            }).ToList();

        response.ProjectNum = sql.Count(c => c.RelatedEntityType == NdnTodoType.创建数字诺亚项目);
        response.TransferNum = sql.Count(c => c.RelatedEntityType == NdnTodoType.转账报销);
        response.BonusNum = sql.Count(c => c.RelatedEntityType == NdnTodoType.发放服务奖金);
        response.InvoiceNum = sql.Count(c => c.RelatedEntityType == NdnTodoType.开发票);

        var 转账报销Ids = response.Rows.Where(x => x.Type == NdnTodoType.转账报销).Select(s => s.RelatedEntityId).ToList();
        var 开发票Ids = response.Rows.Where(x => x.Type == NdnTodoType.开发票).Select(s => s.RelatedEntityId).ToList();
        var 发放服务奖金Ids = response.Rows.Where(x => x.Type == NdnTodoType.发放服务奖金).Select(s => s.RelatedEntityId).ToList();
        var 创建数字诺亚项目Ids = response.Rows.Where(x => x.Type == NdnTodoType.创建数字诺亚项目).Select(s => s.RelatedEntityId).ToList();

        var 转账报销 = _context.Ndn_Project_Transfer_Details.Where(w => 转账报销Ids.Contains(w.Id))
            .Select(s => new { s.TransferId, s.Id }).ToList();

        var 开发票 = _context.Ndn_Project_Invoice.Where(w => 开发票Ids.Contains(w.Id))
            .Select(s => new { s.Ndn_Project_Transfer_Details.TransferId, s.Id }).ToList();

        var 创建数字诺亚项目 = _context.Ndn_Project.Where(w => 创建数字诺亚项目Ids.Contains(w.Id))
            .Select(s => new { s.ProjectCode, s.Id }).ToList();

        response.Rows.ForEach(x =>
        {
            if (x.Type == NdnTodoType.转账报销)
                x.Name = $"{转账报销.FirstOrDefault(f => f.Id == x.RelatedEntityId)?.TransferId}-{x.Name}";
            else if (x.Type == NdnTodoType.开发票)
                x.Name = $"{开发票.FirstOrDefault(f => f.Id == x.RelatedEntityId)?.TransferId}-{x.Name}";
            else if (x.Type == NdnTodoType.发放服务奖金)
                x.Name = $"{x.RelatedEntityId}-{x.Name}";
            else if (x.Type == NdnTodoType.创建数字诺亚项目)
                x.ProjectCode = 创建数字诺亚项目.FirstOrDefault(f => f.Id == x.RelatedEntityId)?.ProjectCode;
        });

        return response;
    }

    public ProjectDetail GetProjectDetail(string projectId)
    {
        var result = _context.Ndn_Project.Where(w => w.Id == projectId)
            .Select(s => new ProjectDetail
            {
                Id = s.Id,
                ProjectCode = s.ProjectCode,
                ProjectName = s.ProjectName,
                BookCode = s.BookCode,
                BookName = s.BookName,
                ClientBookCode = s.ClientBookCode,
                ClientBookName = s.ClientBookName,
                Type = s.Type,
                Status = s.Status
            }).FirstOrDefault();

        return result ?? throw new BadRequestException("未找到项目");
    }

    public ProjectListResponse GetProjectList(ProjectListRequest request)
    {
        var response = new ProjectListResponse();
        var predicate = PredicateBuilder.New<Ndn_Project>(x => x.Status == NdnProjStatus.完成);

        if (request.Type.HasValue)
        {
            predicate = predicate.And(p => p.Type == request.Type);
        }

        if (!string.IsNullOrWhiteSpace(request.ProjectCode))
        {
            predicate = predicate.And(p => p.ProjectCode.Contains(request.ProjectCode));
        }

        if (!string.IsNullOrWhiteSpace(request.ProjectName))
        {
            predicate = predicate.And(p => p.ProjectName.Contains(request.ProjectName));
        }

        if (!string.IsNullOrWhiteSpace(request.Search))
            predicate = predicate.And(p => p.ProjectCode.Contains(request.Search) || p.ProjectName.Contains(request.Search));

        var sql = _context.Ndn_Project.Where(predicate);
        response.Total = sql.Select(s => s.Id).Count();
        response.Rows = sql
            .OrderByDescending(o => o.CreatedTime)
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(s => new ProjectDetail
            {
                Id = s.Id,
                ProjectCode = s.ProjectCode,
                ProjectName = s.ProjectName,
                BookCode = s.BookCode,
                BookName = s.BookName,
                ClientBookCode = s.ClientBookCode,
                ClientBookName = s.ClientBookName,
                Type = s.Type,
                Status = s.Status,
                CreatedTime = s.CreatedTime,
                UpdatedTime = s.UpdatedTime
            }).ToList();

        return response;
    }

    public EmptyResponse SaveProject(ProjectSaveRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.Id))
            throw new BadRequestException("缺少主键Id");

        var project = _context.Ndn_Project.FirstOrDefault(f => f.Id == request.Id);

        if (project == null)
            throw new BadRequestException("未找到项目");

        // 查询创建人工号
        var userNo = _context.User_DingDing.Where(x => x.UserId == _user.Id).Select(s => new { s.UserId, s.DingJobNo }).FirstOrDefault();
        if (userNo == null || string.IsNullOrWhiteSpace(userNo.DingJobNo))
            throw new BadRequestException("未获取到创建人工号");

        // _ndnService.CreateProject(new CreateNdnProject
        // {
        //     ProjId = request.Id,
        //     title = project.ProjectName,
        //     projectName = project.ProjectName,
        //     contractCode = request.ContractCode,
        //     bookCode = project.BookCode,
        //     creatByHrNo = userNo.DingJobNo,
        //     expenseBudget = 0
        // }).GetAwaiter().GetResult();

        return new EmptyResponse();
    }
}