﻿namespace Config.CommonModel;

public class ConnectToken
{
    /// <summary>
    /// 要请求的服务器资源(由后台创建，可空，后台默认补充)
    /// </summary>
    public string? scope { get; set; }

    /// <summary>
    /// 客户端Id(ios、android、web)
    /// </summary>
    public string? client_id { get; set; }

    /// <summary>
    /// 客户端秘钥(由后台分配)
    /// </summary>
    public string? client_secret { get; set; }

    /// <summary>
    /// 授权方式(目前支持mobile、password、refresh_token)
    /// </summary>
    public string? grant_type { get; set; }

    /// <summary>
    /// 用户名、手机号
    /// </summary>
    public string? username { get; set; }

    /// <summary>
    /// 授权方式为password时填写登录手机号
    /// </summary>
    public string? password { get; set; }

    /// <summary>
    /// 手机号验证码
    /// </summary>
    public string? code { get; set; }

    /// <summary>
    /// 授权方式为refresh_token时候填写登录手机号
    /// </summary>
    public string? refresh_token { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public int? source { get; set; }

    /// <summary>
    /// 推送需要的设备标识
    /// </summary>
    public string? DeviceToken { get; set; }
}

public class ConnectTokenResponse
{
    /// <summary>
    /// 访问api的令牌
    /// </summary>
    public string? access_token { get; set; }

    /// <summary>
    /// X秒后过期
    /// </summary>
    public int expires_in { get; set; }

    /// <summary>
    /// Token类型
    /// </summary>
    public string? token_type { get; set; }

    /// <summary>
    /// 刷新令牌
    /// </summary>
    public string? refresh_token { get; set; }

    /// <summary>
    /// 用户Id
    /// </summary>
    public string? userId { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string? nickname { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? mobile { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? avatar { get; set; }

    /// <summary>
    /// 权限
    /// </summary>
    public List<string> powers { get; set; } = new List<string>();
}

