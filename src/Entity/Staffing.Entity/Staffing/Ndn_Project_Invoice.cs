﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.CommonModel.Ndn;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 数字诺亚项目发票
/// </summary>
[Table("ndn_project_invoice")]
public class Ndn_Project_Invoice
{
    /// <summary>
    /// 主键
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 数字诺亚项目流水Id
    /// </summary>
    public string TransferStepId { get; set; } = default!;

    /// <summary>
    /// 状态
    /// </summary>
    [ConcurrencyCheck]
    public NdnAuditStatus Status { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 附件地址
    /// </summary>
    public List<string>? InvoiceUrl { get; set; }

    /// <summary>
    /// 数字诺亚开票Id
    /// </summary>
    public string? NdnId { get; set; }

    /// <summary>
    /// 回款金额
    /// </summary>
    public decimal? ReceivedAmount { get; set; }

    /// <summary>
    /// 操作人Id
    /// </summary>
    public string? OperatorId { get; set; }

    /// <summary>
    /// 操作人姓名
    /// </summary>
    public string? OperatorName { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Ndn_Project_Transfer_Details Ndn_Project_Transfer_Details { get; set; } = default!;
}
