﻿using Config;
using Config.CommonModel.ZhaoPinYun;
using Config.Enums;
using DocumentFormat.OpenXml.Spreadsheet;
using Infrastructure.Common;
using Infrastructure.Extend;
using Infrastructure.Proxy;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using ServiceStack;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Infrastructure.Proxy.ZhaoPinYunApi;

namespace Infrastructure.CommonRepository.ZhaoPinYun;
[Service(ServiceLifetime.Singleton)]
public class ZPYunRepository
{
    private IDbContextFactory<StaffingContext> _staffingContextFactory;
    private ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly ZhaoPinYunApi _zhaopinyunApi;
    public ZPYunRepository(IDbContextFactory<StaffingContext> staffingContextFactory, IOptionsSnapshot<ConfigManager> config,
    IHostEnvironment hostingEnvironment, LogManager log, TencentImHelper tencentImHelper, ZhaoPinYunApi zhaopinyunApi)
    {
        _staffingContextFactory = staffingContextFactory;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
        _zhaopinyunApi = zhaopinyunApi;
    }

    public async Task AsyncJobInfo(string? teamPostId)
    {
        try
        {
            using var context = _staffingContextFactory.CreateDbContext();

            //查询协同职位状态
            var postteam = context.Post_Team.
                Where(postteam => postteam.TeamPostId == teamPostId).FirstOrDefault();
            if (postteam != null)
            {
                //查询云生职位关联表
                var yunshengJobRelation = context.YunSheng_JobId_Relation.
                    Where(x => x.TeamPostId == teamPostId && x.PostId == postteam.PostId).FirstOrDefault();

                //查询项目状态
                var post = context.Post.
                    Where(post => post.PostId == postteam.PostId).FirstOrDefault();
                var project = context.Project.
                    Where(project => project.ProjectId == post!.ProjectId).FirstOrDefault();

                //若项目不存在或者岗位不存在，则返回
                if (project == null || post == null)
                    return;
                
                _log.Info("项目状态", $"查询项目状态,status:{project.Status}");
                _log.Info("职位状态", $"查询职位状态,status:{post.Status}");
                _log.Info("协同职位状态", $"查询协同职位状态,status:{postteam.Status}");


                if (yunshengJobRelation == null &&
                    project != null &&
                    post != null &&
                    postteam != null &&
                    project.Status == ProjectStatus.已上线 &&
                    post.Status == PostStatus.发布中 &&
                    postteam.Status == PostStatus.发布中)
                {
                    var result = await _zhaopinyunApi.PublishJobInfo(postteam.PostId);
                    if (result != null && result.code == 200)
                    {
                        var yunShengPostIdRelation = new YunSheng_PostId_Relation()
                        {
                            PostId = postteam.PostId,
                            TeamPostId = postteam.TeamPostId,
                            YunShengPostId = result.data!.jobId,
                            Status = YunShengPostStatus.上线,
                            UpdatedTime = DateTime.Now
                        };

                        context.YunSheng_JobId_Relation.Add(yunShengPostIdRelation);
                        context.SaveChanges();
                        _log.Info("发布招聘信息", $"发布招聘信息成功，PostId:{postteam.PostId}");
                        await UpdateRobotConfig(result.data!.jobId);//机器人配置初始化
                    }
                    else
                    {
                        _log.Error("发布招聘信息", $"发布招聘信息失败，PostId:{postteam.PostId}， 错误代码：{result?.code} 错误信息：{result?.message}");
                    }
                }
                else if (yunshengJobRelation != null && 
                        project != null && 
                        post != null &&
                        postteam != null &&
                        project.Status == ProjectStatus.已上线 &&
                        post.Status == PostStatus.发布中 &&
                        postteam.Status == PostStatus.发布中 &&
                        yunshengJobRelation.Status != YunShengPostStatus.下架)
                {
                    var result = await _zhaopinyunApi.UpdateJobInfo(postteam.PostId, yunshengJobRelation.YunShengPostId);
                    if (result != null && result.code == 200)
                    {
                        yunshengJobRelation.Status = YunShengPostStatus.更新;
                        yunshengJobRelation.UpdatedTime = DateTime.Now;
                        context.SaveChanges();
                        _log.Info("更新招聘信息", $"更新招聘信息成功，PostId:{postteam.PostId}");
                        await UpdateRobotConfig(yunshengJobRelation!.YunShengPostId);//机器人配置初始化
                    }
                    else
                    {
                        _log.Error("更新招聘信息", $"更新招聘信息失败，PostId:{postteam.PostId} 错误代码：{result?.code} 错误信息：{result?.message}");
                    }
                }
                else if (yunshengJobRelation != null &&
                         project != null &&
                         post != null && 
                         postteam != null &&
                        (project.Status != ProjectStatus.已上线 ||
                         post.Status != PostStatus.发布中 ||
                         postteam.Status != PostStatus.发布中))
                {
                    var result = await _zhaopinyunApi.DownShelfJobInfo(yunshengJobRelation.YunShengPostId);
                    if (result != null && result.code == 200)
                    {
                        yunshengJobRelation.Status = YunShengPostStatus.下架;
                        yunshengJobRelation.UpdatedTime = DateTime.Now;
                        context.SaveChanges();
                        _log.Info("下架招聘信息", $"下架招聘信息成功，PostId:{postteam.PostId}");
                    }
                    else
                    {
                        _log.Error("下架招聘信息", $"下架招聘信息失败，PostId:{postteam.PostId} 错误代码：{result?.code} 错误信息：{result?.message}");
                    }
                }
                else if (yunshengJobRelation != null &&
                        project != null &&
                        post != null &&
                        postteam != null &&
                        project.Status == ProjectStatus.已上线 &&
                        post.Status == PostStatus.发布中 &&
                        postteam.Status == PostStatus.发布中 &&
                        yunshengJobRelation.Status == YunShengPostStatus.下架)
                {
                    var result = await _zhaopinyunApi.UpShelfJobInfo(yunshengJobRelation.YunShengPostId);
                    if (result != null && result.code == 200)
                    {
                        yunshengJobRelation.Status = YunShengPostStatus.上架;
                        yunshengJobRelation.UpdatedTime = DateTime.Now;
                        context.SaveChanges();
                        _log.Info("上架招聘信息", $"上架招聘信息成功，PostId:{postteam.PostId}");
                    }
                    else
                    {
                        _log.Error("上架招聘信息", $"上架招聘信息失败，PostId:{postteam.PostId} 错误代码：{result?.code} 错误信息：{result?.message}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _log.Error("发布/更新/上架/下架招聘信息有误", ex.Message, ex.StackTrace!);
        }
    }

    public async Task UpdateRobotConfig(string? jobid)
    {
        try
        {
            var result = await _zhaopinyunApi.UpdateRobotConfig(jobid);
            if (result != null && result.code == 200)
            {
                _log.Info("更新机器人配置", $"更新机器人配置成功，jobid:{jobid}");
            }
            else
            {
                _log.Error("更新机器人配置", $"更新机器人配置失败，jobid:{jobid} 错误代码：{result?.code} 错误信息：{result?.message}");
            }
        }
        catch (Exception ex)
        {
            _log.Error("更新机器人配置有误", ex.Message, ex.StackTrace!);
        }
    }

    public async Task UploadAllResumes()
    {
        try
        {
            using var context = _staffingContextFactory.CreateDbContext();
            var userIds = context.User_Seeker.
                Where(x => x.Status == UserStatus.Active).
                Select(x => x.UserId).ToArray();
            foreach (var userId in userIds.Chunk(5000))
            {
                MyRedis.Client.SAdd(SubscriptionKey.UpdateYunshengPlatform, userId.ToArray());
                await Task.Delay(1000);
            }
        }
        catch (Exception ex)
        {
            _log.Error("上传所有简历信息有误", ex.Message, ex.StackTrace!);
        }
    }

    public async Task AsyncResumeInfo(string? userid)
    {
        try
        {
            var result = await _zhaopinyunApi.AddResumeInfo(userid);
            if (result != null && result.code == 200)
            {
                using var context = _staffingContextFactory.CreateDbContext();
                //查询关系表中是否已经有该用户的userid，如果有表示已经添加过，直接更新
                var entity = context.YunSheng_ResumeId_Relation.
                    Where(e => e.UserId == userid).
                    FirstOrDefault();
                if (entity != null)
                {
                    entity.PersonnelId = result.data!.personnelId;
                    entity.ResumeId = result.data!.resumeId;
                    entity.UpdatedTime = DateTime.Now;
                }
                else
                {
                    var yunShengResumeIdRelation = new YunSheng_ResumeId_Relation()
                    {
                        UserId = userid,
                        PersonnelId = result.data!.personnelId,
                        ResumeId = result.data!.resumeId,
                        CreatedTime = DateTime.Now,
                        UpdatedTime = DateTime.Now
                    };
                    context.YunSheng_ResumeId_Relation.Add(yunShengResumeIdRelation);
                }
                context.SaveChanges();
                _log.Info("添加简历信息", $"添加简历信息成功，userId:{userid}");
            }
            else
            {
                _log.Error("添加简历信息", $"添加简历信息失败，userid:{userid} 错误代码：{result?.code} 错误信息：{result?.message}");
            }
        }
        catch (Exception ex)
        {
            _log.Error("添加简历信息有误", ex.Message, ex.StackTrace!);
        }
    }
    public async Task<Yunsheng_NewChat_Result?> NewChat(string? jobid, string? resumeid)
    {
        try
        {
            var result = await _zhaopinyunApi.NewChat(jobid, resumeid);
            if (result != null && result.code == 200)
            {
                _log.Info("创建聊天会话", $"创建聊天会话成功，jobid:{jobid},resumeid:{resumeid}");
                using var context = _staffingContextFactory.CreateDbContext();
                var newchat = context.Yunsheng_NewChat_Result.
                    Where(x=>x.YunshengPostId == jobid).
                    FirstOrDefault();

                if (newchat == null)
                {                
                    var ret = new Yunsheng_NewChat_Result()
                    {
                        chatId = result.data!.chatId,
                        mateScore = result.data!.mateScore,
                        YunshengPostId = jobid,
                        ResumeId = resumeid,
                        CreatedTime = DateTime.Now,
                        UpdatedTime = DateTime.Now
                    };
                    context.Yunsheng_NewChat_Result.Add(ret);
                    context.SaveChanges();
                    return ret;
                }
                else
                {
                    newchat.chatId = result.data!.chatId;
                    newchat.ResumeId = resumeid;
                    newchat.mateScore = result.data!.mateScore;
                    newchat.UpdatedTime = DateTime.Now;
                    context.SaveChanges();
                    return newchat;
                }
            }
            else
            {
                _log.Error("创建聊天会话", $"创建聊天会话失败，jobid:{jobid},resumeid:{resumeid} 错误代码：{result?.code} 错误信息：{result?.message}");
                return null;
            }
        }
        catch (Exception ex)
        {
            _log.Error("创建聊天会话有误", ex.Message, ex.StackTrace!);
            return null;
        }
    }
    public async Task<ChatMessageReceive?> GetChatMessage(ChatMessage? chat)
    {
        try
        {
            var result = await _zhaopinyunApi.GetChatMessage(chat);
            if (result != null && result.code == 200)
            {
                _log.Info("获取聊天信息", $"获取聊天信息成功，chatId:{chat?.chatId}");
            }
            else
            {
                _log.Error("获取聊天信息", $"获取聊天信息失败，chatId:{chat?.chatId} 错误代码：{result?.code} 错误信息：{result?.message}");
            }
            //var content = JsonSerializer.DeserializeFromString<ChatMessageReceive>(result.data.messages);
            ChatMessageReceive? content = result?.data;
            return content!;
        }
        catch (Exception ex)
        {
            _log.Error("获取聊天信息有误", ex.Message, ex.StackTrace!);
            return null;
        }
    }
    public async Task GetMessageHistory(GetChatMessageHistory param)
    {
        try
        {
            var result = await _zhaopinyunApi.GetMessageHistory(param);
            if (result != null && result.code == 200)
            {
                _log.Info("获取聊天记录", $"获取聊天记录成功，param:{JsonSerializer.SerializeToString(param)}");
            }
            else
            {
                _log.Error("获取聊天记录", $"获取聊天记录失败，param:{JsonSerializer.SerializeToString(param)}");
            }
        }
        catch (Exception ex)
        {
            _log.Error("获取聊天记录有误", ex.Message, ex.StackTrace!);
        }
    }

    //public class Status
    //{
    //    public ProjectStatus? projectStatus { get; set; }
    //    public PostStatus? postStatus { get; set; }
    //}
    //public class YunshengPostIdAndStatus
    //{
    //    public string? yunshengPostId { get; set; }
    //    public YunShengPostStatus? status { get; set; }
    //    public DateTime? UpdatedTime { get; set; }
    //}
}

