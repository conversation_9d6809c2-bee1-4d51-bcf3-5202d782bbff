using Infrastructure.Extend;
using Staffing.Model.Hr.Post;
using Staffing.Core.Interfaces.Hr;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class PostRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("post").WithTags("职位");
        group.MapPost("/hall", (GetHallPost model, IPostService rep) => rep.GetHallPost(model))
            .WithOpenApi(operation => new(operation) { Summary = "职位大厅" });

        group.MapPost("/hall/citytree", (GetHallPost model, IPostService rep) => rep.GetHallPostCityTree(model))
       .WithOpenApi(operation => new(operation) { Summary = "职位大厅关联的城市树" });

        group.MapPost("/hall/sync", async (PostSync model, IPostService rep) => await rep.PostSync(model))
            .WithOpenApi(operation => new(operation) { Summary = "职位协同" });

        group.MapPost("/hall/orderpost", (GetHallPost model, IPostService rep) => rep.GetHasOrderPost(model))
            .WithOpenApi(operation => new(operation) { Summary = "已接单职位" });
        
        group.MapPost("/hall/orderpost/citytree", (GetHallPost model, IPostService rep) => rep.GetHasOrderPostCityTree(model))
            .WithOpenApi(operation => new(operation) { Summary = "已接单职位关联的城市树" });

        group.MapPost("/auditlist", (GetAuditPost model, IPostService rep) => rep.GetAuditPost(model))
            .WithOpenApi(operation => new(operation) { Summary = "职位审核列表" });

        group.MapPost("/audit", (AuditRequest model, IPostService rep) => rep.AuditPost(model))
            .WithOpenApi(operation => new(operation) { Summary = "职位审核" });

        // group.MapGet("/projecttype/{id}", (string id, IPostService rep) => rep.GetProjectType(id))
        //     .WithOpenApi(operation => new(operation) { Summary = "获取职位的项目交付类型" });

        // group.MapGet("/posttype/{id}", (string id, IPostService rep) => rep.GetPostPaymentType(id))
        //     .WithOpenApi(operation => new(operation) { Summary = "获取职位的交付类型" });
        

    }
}