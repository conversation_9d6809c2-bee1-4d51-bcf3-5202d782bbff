
-- ----------------------------
-- Table structure for autohistories
-- ----------------------------
DROP TABLE IF EXISTS `autohistories`;
CREATE TABLE `autohistories` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `RowId` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `TableName` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `Changed` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '变更',
  `Kind` int NOT NULL,
  `Created` datetime(6) NOT NULL,
  PRIMARY KEY (`Id`),
  KEY `IX_autohistories_Created` (`Created` DESC)
) ENGINE=InnoDB AUTO_INCREMENT=13495 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据变更记录';

-- ----------------------------
-- Table structure for cdz_member
-- ----------------------------
DROP TABLE IF EXISTS `cdz_member`;
CREATE TABLE `cdz_member` (
  `member_id` mediumint NOT NULL AUTO_INCREMENT COMMENT '会员编号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '会员昵称',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '真实姓名',
  `user_card` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '身份证号',
  `is_real` tinyint(1) DEFAULT '0' COMMENT '是否实名：0未实名，1已实名',
  `user_sex` smallint DEFAULT '0' COMMENT '会员性别：0保密1男2女',
  `region_id` smallint DEFAULT '1' COMMENT '城市编码',
  `region_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '城市名称：如河北省 石家庄 裕华区 ',
  `phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '手机号码',
  PRIMARY KEY (`member_id`) USING BTREE,
  KEY `cca123fasdf` (`phone`) USING BTREE,
  KEY `ccasdf13` (`region_name`)
) ENGINE=InnoDB AUTO_INCREMENT=960406 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=COMPACT COMMENT='会员表';

-- ----------------------------
-- Table structure for dd_dept
-- ----------------------------
DROP TABLE IF EXISTS `dd_dept`;
CREATE TABLE `dd_dept` (
  `DdDeptId` int NOT NULL,
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `ParentId` int NOT NULL COMMENT '父Id',
  `Level` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '层级',
  `Sort` int NOT NULL COMMENT '排序',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`DdDeptId`),
  KEY `IX_dd_dept_Level` (`Level`),
  KEY `IX_dd_dept_CreatedTime` (`CreatedTime` DESC),
  KEY `IX_dd_dept_UpdatedTime` (`UpdatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='钉钉部门';

-- ----------------------------
-- Table structure for dd_push_record
-- ----------------------------
DROP TABLE IF EXISTS `dd_push_record`;
CREATE TABLE `dd_push_record` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Type` int NOT NULL COMMENT '推送类型',
  `UserId` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户Id',
  `Content` mediumtext COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_dd_push_record_UserId` (`UserId`),
  KEY `IX_dd_push_record_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='钉钉推送记录';

-- ----------------------------
-- Table structure for dd_user
-- ----------------------------
DROP TABLE IF EXISTS `dd_user`;
CREATE TABLE `dd_user` (
  `DdUserId` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '钉钉用户Id',
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `Mobile` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
  `Title` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职位',
  `Avatar` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '头像',
  `JobNumber` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工工号',
  `WorkPlace` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '办公地点',
  `Remark` varchar(5000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`DdUserId`),
  UNIQUE KEY `IX_dd_user_JobNumber` (`JobNumber`),
  KEY `IX_dd_user_CreatedTime` (`CreatedTime` DESC),
  KEY `IX_dd_user_UpdatedTime` (`UpdatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='钉钉用户';

-- ----------------------------
-- Table structure for dd_user_dept
-- ----------------------------
DROP TABLE IF EXISTS `dd_user_dept`;
CREATE TABLE `dd_user_dept` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `DdUserId` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '钉钉用户Id',
  `DdDeptId` int NOT NULL COMMENT '钉钉部门Id',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_dd_user_dept_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='钉钉用户部门关系';

-- ----------------------------
-- Table structure for dic_cert
-- ----------------------------
DROP TABLE IF EXISTS `dic_cert`;
CREATE TABLE `dic_cert` (
  `Id` int NOT NULL,
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `ParentId` int DEFAULT NULL COMMENT '父Id',
  `Level` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '层级',
  `Sort` int NOT NULL COMMENT '排序',
  `Status` int NOT NULL DEFAULT '1' COMMENT '状态',
  PRIMARY KEY (`Id`),
  KEY `IX_dic_cert_Status` (`Status` DESC),
  KEY `IX_dic_cert_ParentId` (`ParentId` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='证书字典';

-- ----------------------------
-- Table structure for dic_industry
-- ----------------------------
DROP TABLE IF EXISTS `dic_industry`;
CREATE TABLE `dic_industry` (
  `Id` int NOT NULL,
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `ParentId` int DEFAULT NULL COMMENT '父Id',
  `Level` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '层级',
  `Sort` int NOT NULL COMMENT '排序',
  `Status` int NOT NULL DEFAULT '1' COMMENT '状态',
  PRIMARY KEY (`Id`),
  KEY `IX_dic_industry_Status` (`Status` DESC),
  KEY `IX_dic_industry_ParentId` (`ParentId` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='行业字典';

-- ----------------------------
-- Table structure for dic_major
-- ----------------------------
DROP TABLE IF EXISTS `dic_major`;
CREATE TABLE `dic_major` (
  `Id` varchar(15) COLLATE utf8mb4_general_ci NOT NULL,
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `Level2Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Level2Name',
  `Level3Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Level3Name',
  `Type` int NOT NULL COMMENT '类别,0=专科，1=本科',
  `Year` varchar(10) COLLATE utf8mb4_general_ci NOT NULL COMMENT '几年',
  `Status` int NOT NULL DEFAULT '1' COMMENT '状态',
  PRIMARY KEY (`Id`),
  KEY `idx_dic_major_Name` (`Name`),
  KEY `idx_dic_major_Type` (`Type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='全国高校专业';

-- ----------------------------
-- Table structure for dic_post
-- ----------------------------
DROP TABLE IF EXISTS `dic_post`;
CREATE TABLE `dic_post` (
  `Id` int NOT NULL,
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `ParentId` int DEFAULT NULL COMMENT '父Id',
  `Level` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '层级',
  `Tags` varchar(5000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签json',
  `Sort` int NOT NULL COMMENT '排序',
  `Status` int NOT NULL DEFAULT '1' COMMENT '状态',
  PRIMARY KEY (`Id`),
  KEY `IX_dic_post_Status` (`Status` DESC),
  KEY `IX_dic_post_ParentId` (`ParentId` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职位类别';

-- ----------------------------
-- Table structure for dic_recruit_label
-- ----------------------------
DROP TABLE IF EXISTS `dic_recruit_label`;
CREATE TABLE `dic_recruit_label` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'HrId',
  `LabelName` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签名称',
  PRIMARY KEY (`Id`),
  KEY `IX_dic_recruit_label_HrId` (`HrId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='招聘流程标签字典表';

-- ----------------------------
-- Table structure for dic_region
-- ----------------------------
DROP TABLE IF EXISTS `dic_region`;
CREATE TABLE `dic_region` (
  `Id` varchar(15) COLLATE utf8mb4_general_ci NOT NULL,
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `ParentId` varchar(15) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '父Id',
  `County` varchar(12) COLLATE utf8mb4_general_ci NOT NULL COMMENT '区',
  `City` varchar(12) COLLATE utf8mb4_general_ci NOT NULL COMMENT '市',
  `Province` varchar(12) COLLATE utf8mb4_general_ci NOT NULL COMMENT '省',
  `Status` int NOT NULL DEFAULT '1' COMMENT '状态',
  PRIMARY KEY (`Id`),
  KEY `idx_dic_region_name` (`Name`),
  KEY `idx_dic_region_ParentId` (`ParentId` DESC),
  KEY `idx_dic_region_county` (`County`),
  KEY `idx_dic_region_city` (`City`),
  KEY `idx_dic_region_province` (`Province`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='省市县街道';

-- ----------------------------
-- Table structure for dic_school
-- ----------------------------
DROP TABLE IF EXISTS `dic_school`;
CREATE TABLE `dic_school` (
  `Id` varchar(15) COLLATE utf8mb4_general_ci NOT NULL,
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `Department` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主管部门',
  `City` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '所在市',
  `Type` int NOT NULL COMMENT '类别,0=专科，1=本科，2=成人',
  `Describe` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '描述',
  `Logo` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'logo',
  `Status` int NOT NULL DEFAULT '1' COMMENT '状态',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `idx_dic_school_Name` (`Name`),
  KEY `idx_dic_school_City` (`City`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='全国高校';

-- ----------------------------
-- Table structure for dic_talent_industry
-- ----------------------------
DROP TABLE IF EXISTS `dic_talent_industry`;
CREATE TABLE `dic_talent_industry` (
  `Id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `IndustryName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '行业名称',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`) USING BTREE,
  KEY `IX_dic_talent_industry_IndustryName` (`IndustryName`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='虚拟人才库行业(字典)';

-- ----------------------------
-- Table structure for dic_talent_label
-- ----------------------------
DROP TABLE IF EXISTS `dic_talent_label`;
CREATE TABLE `dic_talent_label` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'HrId',
  `LabelName` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签名称',
  PRIMARY KEY (`Id`),
  KEY `IX_dic_talent_label_HrId` (`HrId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='人才库标签字典表（真实虚拟通用表）';

-- ----------------------------
-- Table structure for dic_talent_post
-- ----------------------------
DROP TABLE IF EXISTS `dic_talent_post`;
CREATE TABLE `dic_talent_post` (
  `Id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `ParentId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '父级id',
  `PostName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位名称',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`) USING BTREE,
  KEY `IX_dic_talent_post_PostName` (`PostName`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='虚拟人才库职位(字典)';

-- ----------------------------
-- Table structure for dic_welfare
-- ----------------------------
DROP TABLE IF EXISTS `dic_welfare`;
CREATE TABLE `dic_welfare` (
  `Id` int NOT NULL,
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `Logo` varchar(1024) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Logo',
  `Describe` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `Sort` int NOT NULL COMMENT '排序',
  `Status` int NOT NULL DEFAULT '1' COMMENT '状态',
  PRIMARY KEY (`Id`),
  KEY `IX_dic_welfare_Status` (`Status` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='福利';

-- ----------------------------
-- Table structure for dingding
-- ----------------------------
DROP TABLE IF EXISTS `dingding`;
CREATE TABLE `dingding` (
  `DingUserid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户钉钉userid',
  `DingUnionid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉unionid',
  `DingAvatar` varchar(500) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉头像',
  `DingName` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉姓名',
  `DingJobNo` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉工号',
  `DingMobile` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉手机号',
  `DingTitle` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉职位',
  `DingBranch` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉部门',
  `PushTime` datetime(6) DEFAULT NULL COMMENT '消息推送时间',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`DingUserid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='钉钉信息表';

-- ----------------------------
-- Table structure for enterprise
-- ----------------------------
DROP TABLE IF EXISTS `enterprise`;
CREATE TABLE `enterprise` (
  `EntId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `NuoId` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '诺聘PK_EID',
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业名称',
  `LogoUrl` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业Logo',
  `Abbreviation` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '简称',
  `Type` int NOT NULL DEFAULT '0' COMMENT '类型',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `GroupType` int NOT NULL DEFAULT '0' COMMENT '集团类型',
  `GroupEntId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '集团企业Id',
  `RegionId` varchar(15) COLLATE utf8mb4_general_ci NOT NULL COMMENT '地区Id',
  `Address` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地址',
  `Location` point NOT NULL DEFAULT (point(0,0)),
  `EsignOrgId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'E签宝机构Id',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`EntId`) USING BTREE,
  UNIQUE KEY `IX_enterprise_NuoId` (`NuoId`),
  KEY `IX_enterprise_Name` (`Name`),
  KEY `IX_enterprise_GroupEntId` (`GroupEntId`),
  KEY `IX_enterprise_Status` (`Status`),
  KEY `IX_enterprise_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='企业';

-- ----------------------------
-- Table structure for enterprise_business
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_business`;
CREATE TABLE `enterprise_business` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业名称',
  `BaseData` mediumtext COLLATE utf8mb4_general_ci COMMENT '基本数据',
  `FullData` mediumtext COLLATE utf8mb4_general_ci COMMENT '全量数据',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_enterprise_business_Name` (`Name`),
  KEY `IX_enterprise_business_UpdatedTime` (`UpdatedTime` DESC),
  KEY `IX_enterprise_business_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='企业工商数据';

-- ----------------------------
-- Table structure for enterprise_org
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_org`;
CREATE TABLE `enterprise_org` (
  `OrgId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `EntId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业Id',
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `ParentId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '父Id',
  `Level` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '层级',
  `Sort` int NOT NULL COMMENT '排序',
  `Creator` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`OrgId`),
  KEY `IX_enterprise_org_EntId` (`EntId`),
  KEY `IX_enterprise_org_Level` (`Level`),
  KEY `IX_enterprise_org_Creator` (`Creator` DESC),
  KEY `IX_enterprise_org_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='组织架构';

-- ----------------------------
-- Table structure for enterprise_org_user
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_org_user`;
CREATE TABLE `enterprise_org_user` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `OrgId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '组织架构Id',
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户Id',
  `Creator` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_enterprise_org_user_OrgId_UserId` (`OrgId`,`UserId`),
  KEY `IX_enterprise_org_user_OrgId` (`OrgId`),
  KEY `IX_enterprise_org_user_UserId` (`UserId`),
  KEY `IX_enterprise_org_user_Creator` (`Creator` DESC),
  KEY `IX_enterprise_org_user_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='组织架构人员';

-- ----------------------------
-- Table structure for enterprise_relation
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_relation`;
CREATE TABLE `enterprise_relation` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `EntId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司Id',
  `GroupEntId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属集团公司Id',
  `Creator` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_enterprise_relation_EntId` (`EntId`),
  KEY `IX_enterprise_relation_GroupEntId` (`GroupEntId`),
  KEY `IX_enterprise_relation_Creator` (`Creator` DESC),
  KEY `IX_enterprise_relation_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='企业集团关系';

-- ----------------------------
-- Table structure for enterprise_role
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_role`;
CREATE TABLE `enterprise_role` (
  `RoleId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `EntId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业Id',
  `Type` int NOT NULL COMMENT '0=系统，1=自定义',
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `Describe` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `Scope` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '管辖范围',
  `Icon` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图标',
  `Powers` varchar(1024) COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限列表',
  `Creator` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`RoleId`),
  KEY `IX_enterprise_role_EntId` (`EntId`),
  KEY `IX_enterprise_role_Type` (`Type`),
  KEY `IX_enterprise_role_Creator` (`Creator` DESC),
  KEY `IX_enterprise_role_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='企业角色';

-- ----------------------------
-- Table structure for enterprise_whitelist
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_whitelist`;
CREATE TABLE `enterprise_whitelist` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `NuoId` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '诺聘PK_EID',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_enterprise_whitelist_NuoId` (`NuoId`),
  KEY `IX_user_enterprise_whitelist_audit_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='企业白名单';

-- ----------------------------
-- Table structure for msg_dingdingpush
-- ----------------------------
DROP TABLE IF EXISTS `msg_dingdingpush`;
CREATE TABLE `msg_dingdingpush` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'userid',
  `DingUserid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉userid',
  `CreateProject` int NOT NULL DEFAULT '0' COMMENT '创建项目(进行中）',
  `EnableCollaboration` int NOT NULL DEFAULT '0' COMMENT '开启协同（集团级）',
  `CollaborativeProject` int NOT NULL DEFAULT '0' COMMENT '协同项目',
  `RegistrablePositions` int NOT NULL DEFAULT '0' COMMENT '可报名职位',
  `ScreeningNum` int NOT NULL DEFAULT '0' COMMENT '初筛',
  `InterviewNum` int NOT NULL DEFAULT '0' COMMENT '面试安排',
  `MessageNoReply` int NOT NULL DEFAULT '0' COMMENT '即时通讯未回复',
  `CoordinationDelivery` int NOT NULL DEFAULT '0' COMMENT '协同交付中',
  `CoordinationSuccess` int NOT NULL DEFAULT '0' COMMENT '协同交付成功',
  `CoordinationFail` int NOT NULL DEFAULT '0' COMMENT '协同交付失败',
  `ReserveVirtual` int NOT NULL DEFAULT '0' COMMENT '简历储备',
  `ReservePlatform` int NOT NULL DEFAULT '0' COMMENT '真实用户',
  `PushTime` datetime(6) DEFAULT NULL COMMENT '消息推送时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `idx_msg_dingdingpush_UserId` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='钉钉推送消息记录';

-- ----------------------------
-- Table structure for msg_notify_hr
-- ----------------------------
DROP TABLE IF EXISTS `msg_notify_hr`;
CREATE TABLE `msg_notify_hr` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Type` int NOT NULL COMMENT '消息类型',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `Content` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `Data` varchar(5000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据json',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `idx_msg_notify_hr_UserIdFull` (`UserId`,`Type`,`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Hr站内消息';

-- ----------------------------
-- Table structure for msg_words
-- ----------------------------
DROP TABLE IF EXISTS `msg_words`;
CREATE TABLE `msg_words` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Type` int DEFAULT NULL COMMENT '类型，0=求职者，1=hr',
  `Content` varchar(1025) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `idx_msg_words_UserId` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='消息常用语';

-- ----------------------------
-- Table structure for post
-- ----------------------------
DROP TABLE IF EXISTS `post`;
CREATE TABLE `post` (
  `PostId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `AutoId` int NOT NULL AUTO_INCREMENT,
  `ProjectId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目Id',
  `Name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `Describe` mediumtext COLLATE utf8mb4_general_ci COMMENT '描述',
  `Category` int NOT NULL DEFAULT '0' COMMENT '职位类别',
  `WorkNature` int NOT NULL DEFAULT '0' COMMENT '工作性质',
  `SalaryType` int NOT NULL DEFAULT '0' COMMENT '薪资类型',
  `RecruitNumber` int NOT NULL DEFAULT '0' COMMENT '招聘人数',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `Share` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否协同',
  `RegionId` varchar(15) COLLATE utf8mb4_general_ci NOT NULL COMMENT '地区Id',
  `Address` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地址',
  `LocationMap` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '静态地图',
  `Location` point NOT NULL DEFAULT (point(0,0)),
  `Department` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '部门',
  `Education` int NOT NULL DEFAULT '0' COMMENT '教育',
  `MinSalary` int NOT NULL DEFAULT '0' COMMENT '薪资要求(最低)',
  `MaxSalary` int NOT NULL DEFAULT '0' COMMENT '薪资要求(最高)',
  `Salary` int NOT NULL DEFAULT '0' COMMENT '几薪',
  `SettlementType` int DEFAULT NULL COMMENT '结算方式（日结、月结）',
  `WorkingDays` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工作日',
  `WorkingHours` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工作时间',
  `MinMonths` int DEFAULT NULL COMMENT '最少实习月数',
  `DaysPerWeek` int DEFAULT NULL COMMENT '最少周到岗天数',
  `GraduationYear` int DEFAULT NULL COMMENT '毕业年份',
  `Money` decimal(18,2) DEFAULT '0.00' COMMENT '金额/人',
  `Tags` mediumtext COLLATE utf8mb4_general_ci COMMENT '标签',
  `Highlights` mediumtext COLLATE utf8mb4_general_ci COMMENT '职位亮点',
  `Welfare` mediumtext COLLATE utf8mb4_general_ci COMMENT '福利',
  `WelfareCustom` mediumtext COLLATE utf8mb4_general_ci COMMENT '福利自定义',
  `WelfareSearch` varchar(600) COLLATE utf8mb4_general_ci NOT NULL COMMENT '福利检索',
  `Sex` int DEFAULT NULL COMMENT '性别',
  `MinAge` int NOT NULL COMMENT '最小年龄',
  `MaxAge` int NOT NULL COMMENT '最大年龄',
  `Score` int NOT NULL COMMENT '分数' DEFAULT 100,
  `Deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '删除',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`PostId`),
  UNIQUE KEY `IX_post_AutoId` (`AutoId`),
  KEY `IX_post_ProjectId` (`ProjectId`),
  KEY `IX_post_WorkNature` (`WorkNature`),
  KEY `IX_post_Category` (`Category`),
  KEY `IX_post_WelfareSearch` (`WelfareSearch`),
  KEY `IX_post_Score` (`Score` DESC),
  KEY `IX_post_RegionId` (`RegionId`),
  KEY `IX_post_Status` (`Status`),
  KEY `IX_post_Deleted` (`Deleted`),
  KEY `IX_post_UpdatedTime` (`UpdatedTime` DESC),
  KEY `IX_post_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB AUTO_INCREMENT=558 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职位';

-- ----------------------------
-- Table structure for post_complaint
-- ----------------------------
DROP TABLE IF EXISTS `post_complaint`;
CREATE TABLE `post_complaint` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `DeliveryId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '投递Id',
  `SeekerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '求职者Id',
  `TeamPostId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '协同职位Id',
  `Describe` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `Type` int NOT NULL DEFAULT '0' COMMENT '类型',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `UpdatedBy` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_post_complaint_DeliveryId` (`DeliveryId` DESC),
  KEY `IX_post_complaint_SeekerId` (`SeekerId`),
  KEY `IX_post_complaint_Type` (`Type`),
  KEY `IX_post_complaint_TeamPostId` (`TeamPostId` DESC),
  KEY `IX_post_complaint_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职位投诉';

-- ----------------------------
-- Table structure for post_delivery
-- ----------------------------
DROP TABLE IF EXISTS `post_delivery`;
CREATE TABLE `post_delivery` (
  `DeliveryId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `SeekerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '求职者Id',
  `TeamPostId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Hr职位Id，可空',
  `PostId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '原始职位Id',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `Source` int NOT NULL DEFAULT '0' COMMENT '来源',
  `PaymentNode` int DEFAULT '0' COMMENT '结算方式',
  `PaymentDays` int DEFAULT '0' COMMENT '打款天数',
  `Money` decimal(18,2) DEFAULT '0.00' COMMENT '金额/人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`DeliveryId`) USING BTREE,
  KEY `IX_post_delivery_SeekerId_Status` (`SeekerId`,`Status`),
  KEY `IX_post_delivery_PostId` (`PostId`),
  KEY `IX_post_delivery_TeamPostId` (`TeamPostId` DESC),
  KEY `IX_post_delivery_SeekerId` (`SeekerId`),
  KEY `IX_post_delivery_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职位投递简历';

-- ----------------------------
-- Table structure for post_extend
-- ----------------------------
DROP TABLE IF EXISTS `post_extend`;
CREATE TABLE `post_extend` (
  `PostId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `SyncNum` int NOT NULL COMMENT '协同次数',
  `DeliveryNum` int NOT NULL COMMENT '投递人数累计去重',
  `HrScreeningNum` int NOT NULL COMMENT '简历初筛人数',
  `InterviewerScreeningNum` int NOT NULL COMMENT '面试官筛选人数',
  `InterviewNum` int NOT NULL COMMENT '面试人数',
  `OfferNum` int NOT NULL COMMENT 'Offer人数',
  `InductionNum` int NOT NULL COMMENT '入职人数',
  `ContractNum` int NOT NULL COMMENT '签约人数',
  `SettlementNum` int NOT NULL COMMENT '结算人数',
  PRIMARY KEY (`PostId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职位扩展表';

-- ----------------------------
-- Table structure for post_team
-- ----------------------------
DROP TABLE IF EXISTS `post_team`;
CREATE TABLE `post_team` (
  `TeamPostId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `TeamProjectId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Hr项目Id',
  `PostId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '原始职位Id',
  `Type` int NOT NULL DEFAULT '0' COMMENT '类别',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `Show` bit(1) NOT NULL DEFAULT b'0' COMMENT '上架状态，小程序端是否可以展示',
  `AppletQrCode` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '小程序二维码',
  `AppletShortLink` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '小程序短链接',
  `AppletShortLinkExp` datetime(6) DEFAULT NULL COMMENT '小程序短链接有效期',
  `UpdatedBy` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `AutoId` int NOT NULL AUTO_INCREMENT,
  `TopTime` datetime(6) NOT NULL DEFAULT '1970-01-01 00:00:00.000000',
  PRIMARY KEY (`TeamPostId`),
  UNIQUE KEY `IX_post_team_AutoId` (`AutoId`) USING BTREE,
  KEY `IX_post_team_Show` (`Show` DESC),
  KEY `IX_post_team_TeamProjectId` (`TeamProjectId`),
  KEY `IX_post_team_PostId` (`PostId`),
  KEY `IX_post_team_Status` (`Status`),
  KEY `IX_post_team_CreatedTime` (`CreatedTime` DESC),
  KEY `IX_post_team_TopTime` (`TopTime` DESC)
) ENGINE=InnoDB AUTO_INCREMENT=2964 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='协同职位';

-- ----------------------------
-- Table structure for post_team_extend
-- ----------------------------
DROP TABLE IF EXISTS `post_team_extend`;
CREATE TABLE `post_team_extend` (
  `TeamPostId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `DeliveryNum` int NOT NULL COMMENT '投递人数累计去重',
  `HrScreeningNum` int NOT NULL COMMENT '简历初筛人数',
  `InterviewerScreeningNum` int NOT NULL COMMENT '面试官筛选人数',
  `InterviewNum` int NOT NULL COMMENT '面试人数',
  `OfferNum` int NOT NULL COMMENT 'Offer人数',
  `InductionNum` int NOT NULL COMMENT '入职人数',
  `ContractNum` int NOT NULL COMMENT '签约人数',
  `SettlementNum` int NOT NULL COMMENT '结算人数',
  PRIMARY KEY (`TeamPostId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='协同职位扩展表';

-- ----------------------------
-- Table structure for project
-- ----------------------------
DROP TABLE IF EXISTS `project`;
CREATE TABLE `project` (
  `ProjectId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `AutoId` int NOT NULL AUTO_INCREMENT,
  `AgentEntId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '代招企业Id',
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `Level` int NOT NULL DEFAULT '0' COMMENT '项目级别',
  `Type` int NOT NULL DEFAULT '0' COMMENT '项目类型',
  `Industry` int NOT NULL DEFAULT '0' COMMENT '项目行业',
  `WorkCycleType` int NOT NULL DEFAULT '0' COMMENT '工作周期类型',
  `ClearingType` int NOT NULL DEFAULT '0' COMMENT '结算方式',
  `ClearingChannel` int NOT NULL DEFAULT '0' COMMENT '结算渠道',
  `NuoId` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '诺亚项目编码',
  `Describe` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `RegionData` mediumtext COLLATE utf8mb4_general_ci COMMENT '地区Json',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `EndTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '项目截止日期',
  `ShareInternal` int NOT NULL DEFAULT '0' COMMENT '分享公司内部',
  `SharePlatform` int NOT NULL DEFAULT '0' COMMENT '分享给平台',
  `Share` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否协同',
  `ShareAnyone` int NOT NULL DEFAULT '0' COMMENT '全网公开',
  `PaymentNode` int DEFAULT NULL COMMENT '打款节点',
  `PaymentDays` int DEFAULT NULL COMMENT '打款天数',
  `PaymentType` int DEFAULT NULL COMMENT '打款方式',
  `SharePost` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否有协同的职位（项目大厅检索用）',
  `Deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '删除',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`ProjectId`),
  UNIQUE KEY `IX_project_AutoId` (`AutoId`),
  KEY `IX_project_AgentEntId` (`AgentEntId`),
  KEY `IX_project_HrId` (`HrId`),
  KEY `IX_project_Share` (`Share`),
  KEY `IX_project_Status` (`Status`),
  KEY `IX_project_NuoId` (`NuoId`),
  KEY `IX_project_Deleted` (`Deleted`),
  KEY `IX_project_SharePost` (`SharePost` DESC),
  KEY `IX_project_EndTime` (`EndTime` DESC),
  KEY `IX_project_UpdatedTime` (`UpdatedTime` DESC),
  KEY `IX_project_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB AUTO_INCREMENT=528 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目';

-- ----------------------------
-- Table structure for project_automatic
-- ----------------------------
DROP TABLE IF EXISTS `project_automatic`;
CREATE TABLE `project_automatic` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `ProjectId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目Id',
  `RecruitmentCycle` bit(1) NOT NULL DEFAULT b'0' COMMENT '招聘周期',
  `RecruitmentNumber` bit(1) NOT NULL DEFAULT b'0' COMMENT '招聘人数',
  `InterviewerOutCome` bit(1) NOT NULL DEFAULT b'0' COMMENT '面试官筛选结果',
  `ScreenFileStatus` int NOT NULL DEFAULT '0' COMMENT '筛选归档类型',
  `ScreenFileRemarks` varchar(1000) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '筛选归档描述',
  `OfferStatus` bit(1) NOT NULL DEFAULT b'0' COMMENT 'offer状态',
  `OfferOrInduction` bit(1) NOT NULL DEFAULT b'0' COMMENT 'offer或入职',
  `QuitJob` bit(1) NOT NULL DEFAULT b'0' COMMENT '离职操作',
  `QuitFileStatus` int NOT NULL DEFAULT '0' COMMENT '离职归档类型',
  `QuitFileRemarks` varchar(1000) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '离职归档描述',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_project_automatic_ProjectId` (`ProjectId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目自流转表';

-- ----------------------------
-- Table structure for project_extend
-- ----------------------------
DROP TABLE IF EXISTS `project_extend`;
CREATE TABLE `project_extend` (
  `ProjectId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `SyncNum` int NOT NULL COMMENT '协同次数',
  `RegistrationNum` int NOT NULL COMMENT '报名人数',
  `DeliveriesNum` int NOT NULL COMMENT '交付人数',
  PRIMARY KEY (`ProjectId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目扩展表';

-- ----------------------------
-- Table structure for project_hall
-- ----------------------------
DROP TABLE IF EXISTS `project_hall`;
CREATE TABLE `project_hall` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `ProjectId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目Id',
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户Id',
  `Type` int NOT NULL COMMENT '类型',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_project_hall_ProjectId_Type` (`ProjectId`,`Type`),
  KEY `IX_project_hall_HrId` (`HrId`),
  KEY `IX_project_hall_ProjectId` (`ProjectId`),
  KEY `IX_project_hall_Type` (`Type`),
  KEY `IX_project_hall_Status` (`Status`),
  KEY `IX_project_hall_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目大厅';

-- ----------------------------
-- Table structure for project_interviewer
-- ----------------------------
DROP TABLE IF EXISTS `project_interviewer`;
CREATE TABLE `project_interviewer` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `ProjectId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目Id',
  `Name` varchar(10) COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `Phone` varchar(15) COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
  `Post` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位',
  `Mail` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '邮箱',
  `OfficeAddress` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '办公地址',
  PRIMARY KEY (`Id`),
  KEY `IX_project_interviewer_ProjectId` (`ProjectId`),
  KEY `IX_project_interviewer_Phone` (`Phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目面试官';

-- ----------------------------
-- Table structure for project_member
-- ----------------------------
DROP TABLE IF EXISTS `project_member`;
CREATE TABLE `project_member` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `ProjectId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `IdentityCardType` int NOT NULL DEFAULT '0' COMMENT '证件类型',
  `IdentityCard` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证号',
  `IdentityCardName` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '身份证名字',
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Mobile` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `Source` int NOT NULL DEFAULT '0' COMMENT '用户来源',
  `Birthday` date DEFAULT NULL COMMENT '生日',
  `Sex` int DEFAULT NULL COMMENT '性别',
  `RegionId` varchar(15) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地区Id',
  `EmploymentMode` int NOT NULL COMMENT '用工形式',
  `InductionTimes` int NOT NULL COMMENT '入职次数',
  `ProbationMonth` int NOT NULL COMMENT '试用期（月）',
  `ContractNum` int NOT NULL COMMENT '合同数量',
  `PostName` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '应聘职位',
  `Department` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '入职部门',
  `EntryTime` date DEFAULT NULL COMMENT '入职时间',
  `QuitTime` date DEFAULT NULL COMMENT '离职时间',
  `AuditTime` datetime(6) DEFAULT NULL COMMENT '审批通过时间',
  `QuitStatus` int NOT NULL DEFAULT '0' COMMENT '离职状态',
  `EMail` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `Describe` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_project_member_UserId` (`UserId` DESC),
  KEY `IX_project_member_IdentityCard` (`IdentityCard` DESC),
  KEY `IX_project_member_QuitStatus_QuitTime` (`QuitStatus` DESC,`QuitTime`),
  KEY `IX_project_member_ProjectIdFull` (`ProjectId`,`IdentityCard`,`IdentityCardName`,`PostName`,`Status`,`Mobile`,`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目成员';

-- ----------------------------
-- Table structure for project_member_record
-- ----------------------------
DROP TABLE IF EXISTS `project_member_record`;
CREATE TABLE `project_member_record` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `ProjectId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `IdentityCardType` int NOT NULL DEFAULT '0' COMMENT '证件类型',
  `IdentityCard` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证号',
  `IdentityCardName` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '身份证名字',
  `Type` int NOT NULL DEFAULT '0' COMMENT '记录类型',
  `Mobile` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `PostName` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '应聘职位',
  `Department` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '入职部门',
  `EntryTime` date DEFAULT NULL COMMENT '入职时间',
  `QuitTime` date DEFAULT NULL COMMENT '离职时间',
  `Describe` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_project_member_record_ProjectId` (`ProjectId` DESC),
  KEY `IX_project_member_record_IdentityCard` (`IdentityCard` DESC),
  KEY `IX_project_member_record_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目成员记录';

-- ----------------------------
-- Table structure for project_region
-- ----------------------------
DROP TABLE IF EXISTS `project_region`;
CREATE TABLE `project_region` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `ProjectId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目Id',
  `RegionId` varchar(15) COLLATE utf8mb4_general_ci NOT NULL COMMENT '地区Id',
  PRIMARY KEY (`Id`),
  KEY `IX_project_region_ProjectId` (`ProjectId`),
  KEY `IX_project_region_RegionId` (`RegionId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目地区';

-- ----------------------------
-- Table structure for project_team
-- ----------------------------
DROP TABLE IF EXISTS `project_team`;
CREATE TABLE `project_team` (
  `TeamProjectId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `ProjectId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '原始项目Id',
  `Type` int NOT NULL DEFAULT '0' COMMENT '类别',
  `Source` int NOT NULL DEFAULT '0' COMMENT '来源',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `UpdatedBy` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`TeamProjectId`),
  KEY `IX_project_team_HrId_Status` (`HrId`,`Status`),
  KEY `IX_project_team_Type` (`Type`),
  KEY `IX_project_team_ProjectId_Status` (`ProjectId`,`Status`),
  KEY `IX_project_team_UpdatedBy` (`UpdatedBy`),
  KEY `IX_project_team_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='协同项目';

-- ----------------------------
-- Table structure for project_teambounty
-- ----------------------------
DROP TABLE IF EXISTS `project_teambounty`;
CREATE TABLE `project_teambounty` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `ProjectId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目Id',
  `TeamProjectId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '协同项目Id',
  `RecruitId` varchar(18) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '招聘流程Id',
  `TeamPostId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '协同职位Id',
  `PostId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '原始职位Id',
  `SeekerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '求职者Id',
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主创Id',
  `TeamHrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '协同HrId',
  `PaymentNode` int DEFAULT NULL COMMENT '交付类型',
  `PaymentDays` int DEFAULT NULL COMMENT '打款天数',
  `PaymentType` int DEFAULT NULL COMMENT '打款方式',
  `Money` decimal(18,2) DEFAULT '0.00' COMMENT '金额',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `Source` int NOT NULL DEFAULT '0' COMMENT '来源',
  `SettlementTime` datetime(6) DEFAULT NULL COMMENT '结算时间',
  `SettlementMoney` decimal(18,2) DEFAULT '0.00' COMMENT '结算金额',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_project_teambounty_Status` (`Status`),
  KEY `IX_project_teambounty_RecruitId` (`RecruitId`),
  KEY `IX_project_teambounty_ProjectId_Full` (`ProjectId`,`TeamProjectId`,`TeamPostId`,`PostId`,`SeekerId`,`HrId`,`TeamHrId`,`Status`,`Source`,`CreatedTime` DESC),
  KEY `IX_project_teambounty_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目协同分润表';

-- ----------------------------
-- Table structure for recruit
-- ----------------------------
DROP TABLE IF EXISTS `recruit`;
CREATE TABLE `recruit` (
  `RecruitId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `DeliveryId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '投递Id，可空，数字诺亚可以不通过投递直接进入招聘',
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'HrId',
  `SeekerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '求职者Id',
  `PostName` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位名称',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `FileAway` int NOT NULL DEFAULT '0' COMMENT '归档状态',
  `FileRemarks` varchar(200) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '归档备注',
  `Type` int NOT NULL DEFAULT '0' COMMENT '类型',
  `InductionTime` datetime(6) DEFAULT NULL COMMENT '入职时间',
  `StatusTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`RecruitId`),
  KEY `IX_recruit_HrId` (`HrId`),
  KEY `IX_recruit_Type` (`Type`),
  KEY `IX_recruit_Status` (`Status`),
  KEY `IX_recruit_DeliveryId` (`DeliveryId` DESC),
  KEY `IX_recruit_SeekerId` (`SeekerId`),
  KEY `IX_recruit_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='招聘表';

-- ----------------------------
-- Table structure for recruit_comment
-- ----------------------------
DROP TABLE IF EXISTS `recruit_comment`;
CREATE TABLE `recruit_comment` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `RecruitId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '招聘流程id',
  `Content` varchar(5000) COLLATE utf8mb4_general_ci NOT NULL COMMENT '评论内容',
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'HrId',
  `HrName` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Hr姓名',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_recruit_comment_VirtualId` (`RecruitId`),
  KEY `IX_recruit_comment_HrId` (`HrId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='招聘流程简历评论';

-- ----------------------------
-- Table structure for recruit_interview
-- ----------------------------
DROP TABLE IF EXISTS `recruit_interview`;
CREATE TABLE `recruit_interview` (
  `InterviewId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `RecruitId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '招聘Id',
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'HrId，冗余',
  `SeekerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '求职者Id，冗余',
  `InterviewerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '面试官Id',
  `Process` int NOT NULL DEFAULT '0' COMMENT '面试过程',
  `Outcome` int NOT NULL DEFAULT '0' COMMENT '面试结果',
  `Forms` int NOT NULL DEFAULT '0' COMMENT '面试形式',
  `UserFeedBack` int NOT NULL DEFAULT '0' COMMENT '用户面试反馈',
  `InterviewTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '面试时间',
  `RegionId` varchar(15) COLLATE utf8mb4_general_ci NOT NULL COMMENT '地区Id',
  `Address` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '面试地点地址',
  `Lat` decimal(18,8) NOT NULL COMMENT '面试地点纬度',
  `Lng` decimal(18,8) NOT NULL COMMENT '面试地点经度',
  `Mobile` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `MobileContent` varchar(1000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '短信内容',
  `Mail` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '邮箱地址',
  `MailContent` mediumtext COLLATE utf8mb4_general_ci COMMENT '邮件内容',
  `Remarks` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `TodoTime` datetime(6) DEFAULT NULL COMMENT '面试官反馈时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CancelName` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '取消面试用户名',
  PRIMARY KEY (`InterviewId`),
  KEY `IX_recruit_interview_RecruitId_CreatedTime` (`RecruitId`,`CreatedTime` DESC),
  KEY `IX_recruit_interview_HrId_Outcome` (`HrId`,`Outcome`),
  KEY `IX_recruit_interview_SeekerId` (`SeekerId`),
  KEY `IX_recruit_interview_InterviewerId` (`InterviewerId` DESC),
  KEY `IX_recruit_interview_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='面试表';

-- ----------------------------
-- Table structure for recruit_interviewer_screen
-- ----------------------------
DROP TABLE IF EXISTS `recruit_interviewer_screen`;
CREATE TABLE `recruit_interviewer_screen` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `RecruitId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '招聘Id',
  `SeekerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '求职者Id，冗余',
  `InterviewerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '面试官Id',
  `Recommend` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '推荐理由',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `Remarks` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'hr备注',
  `TodoTime` datetime(6) DEFAULT NULL COMMENT '面试官反馈时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_recruit_interview_screen_RecruitId_CreatedTime` (`RecruitId`,`CreatedTime` DESC),
  KEY `IX_recruit_interview_screen_InterviewerId` (`InterviewerId` DESC),
  KEY `IX_recruit_interview_screen_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='面试官筛选表';

-- ----------------------------
-- Table structure for recruit_interviewer_todo
-- ----------------------------
DROP TABLE IF EXISTS `recruit_interviewer_todo`;
CREATE TABLE `recruit_interviewer_todo` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `RelationId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联id（面试官筛选表id或面试表id）',
  `RecruitId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '招聘流程id，冗余',
  `InterviewerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '面试官Id',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `Type` int NOT NULL DEFAULT '0' COMMENT '类型',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_recruit_interview_todo_Type` (`Type`),
  KEY `IX_recruit_interview_todo_RecruitId_CreatedTime` (`RecruitId`,`CreatedTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='面试官待办表';

-- ----------------------------
-- Table structure for recruit_label
-- ----------------------------
DROP TABLE IF EXISTS `recruit_label`;
CREATE TABLE `recruit_label` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `RecruitId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '招聘流程id',
  `DicLabelId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签id',
  PRIMARY KEY (`Id`),
  KEY `IX_recruit_label_RecruitId` (`RecruitId`),
  KEY `IX_recruit_label_DicLabelId` (`DicLabelId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='招聘流程对应标签表';

-- ----------------------------
-- Table structure for recruit_offer
-- ----------------------------
DROP TABLE IF EXISTS `recruit_offer`;
CREATE TABLE `recruit_offer` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `RecruitId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '招聘Id',
  `InductionSalary` int DEFAULT NULL COMMENT '入职薪资',
  `InductionTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '预期入职时间',
  `RegionId` varchar(15) COLLATE utf8mb4_general_ci NOT NULL COMMENT '地区Id',
  `Address` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '入职地点地址',
  `Lat` decimal(18,8) NOT NULL COMMENT '入职地点纬度',
  `Lng` decimal(18,8) NOT NULL COMMENT '入职地点经度',
  `Mobile` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `MobileContent` varchar(1000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '短信内容',
  `IsMobileNotice` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已短信通知',
  `Mail` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '邮箱地址',
  `MailContent` mediumtext COLLATE utf8mb4_general_ci COMMENT '邮件内容',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `NoticeSubjectId` varchar(18) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '通知主体id',
  `NoticeSubjectName` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '通知主体名称',
  `NoticeContent` varchar(5000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '通知书内容',
  `CarryInformation` varchar(5000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '携带资料集合',
  PRIMARY KEY (`Id`),
  KEY `IX_recruit_offer_RecruitId_CreatedTime` (`RecruitId`,`CreatedTime` DESC),
  KEY `IX_recruit_offer_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='offer表';

-- ----------------------------
-- Table structure for recruit_record
-- ----------------------------
DROP TABLE IF EXISTS `recruit_record`;
CREATE TABLE `recruit_record` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `RecruitId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '招聘Id',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `FileAway` int NOT NULL DEFAULT '0' COMMENT '归档状态',
  `InterviewerScreenId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '面试官筛选id',
  `InterviewId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '面试Id',
  `OfferId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT 'OfferId',
  `Data` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'json数据，可能不用',
  `FileAwayRemarks` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '归档备注',
  `Creator` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_recruit_record_RecruitId_CreatedTime` (`RecruitId`,`CreatedTime` DESC),
  KEY `IX_recruit_record_InterviewId` (`InterviewId` DESC),
  KEY `IX_recruit_record_OfferId` (`OfferId` DESC),
  KEY `IX_recruit_record_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='招聘记录';

-- ----------------------------
-- Table structure for rpt_daily_user
-- ----------------------------
DROP TABLE IF EXISTS `rpt_daily_user`;
CREATE TABLE `rpt_daily_user` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `NewUser` int NOT NULL,
  `NewSeeker` int NOT NULL,
  `NewHr` int NOT NULL,
  `ActiveUser` int NOT NULL,
  `ActiveSeeker` int NOT NULL,
  `ActiveHr` int NOT NULL,
  `ActiveInterviewer` int NOT NULL,
  `DeliverResume` int NOT NULL DEFAULT '0',
  `EventDate` date NOT NULL,
  `CreatedTime` datetime(6) NOT NULL,
  `AllHrHadProj` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`Id`) USING BTREE,
  KEY `IX_rpt_daily_user_EventDate` (`EventDate` DESC),
  KEY `IX_rpt_daily_user_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB AUTO_INCREMENT=193 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='每日用户统计';

-- ----------------------------
-- Table structure for rpt_ny_user
-- ----------------------------
DROP TABLE IF EXISTS `rpt_ny_user`;
CREATE TABLE `rpt_ny_user` (
  `员工UserID` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `姓名` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `工号` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `1级部门` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `2级部门` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `3级部门` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `4级部门` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `5级部门` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `6级部门` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `7级部门` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `手机号` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `职位` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `岗位序列` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `直属主管` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `直属主管id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `人员账套` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for rpt_szny_contract
-- ----------------------------
DROP TABLE IF EXISTS `rpt_szny_contract`;
CREATE TABLE `rpt_szny_contract` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `JobNumber` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工工号',
  `SJHT_CODE` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '合同编码',
  `SJHT_NAME` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '合同名称',
  `SJHT_SY_RYCODE` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '受益人编码',
  `SJHT_XBBBM` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '销帮帮合同编码',
  `SJHT_CPID` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品编码',
  `SJHT_CPMC` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品名称',
  `SJHT_SETDATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `SJHT_BEGINDATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '开始时间',
  `SJHT_ENDDATE` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '结束时间',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_rpt_szny_contract_SJHT_CODE` (`SJHT_CODE`),
  KEY `IX_rpt_szny_contract_JobNumber` (`JobNumber`),
  KEY `IX_rpt_szny_contract_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字诺亚合同';

-- ----------------------------
-- Table structure for rpt_szny_project
-- ----------------------------
DROP TABLE IF EXISTS `rpt_szny_project`;
CREATE TABLE `rpt_szny_project` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `JobNumber` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工工号',
  `XM_CODE` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目编码',
  `XM_NAME` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `XM_PRODUCTID` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品编码',
  `XM_PRODUCTNAME` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品名称',
  `XM_SETDATE` datetime(6) NOT NULL COMMENT '创建时间',
  `XM_BEGTIME` datetime(6) NOT NULL COMMENT '开始时间',
  `XM_ENDTIME` datetime(6) NOT NULL COMMENT '结束时间',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_rpt_szny_project_XM_CODE` (`XM_CODE`),
  KEY `IX_rpt_szny_project_JobNumber` (`JobNumber`),
  KEY `IX_rpt_szny_project_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数字诺亚项目';

-- ----------------------------
-- Table structure for sys_app
-- ----------------------------
DROP TABLE IF EXISTS `sys_app`;
CREATE TABLE `sys_app` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `Alias` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '别名',
  `Icon` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图标',
  `Type` int NOT NULL COMMENT '类型',
  `Url` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Url',
  `Powers` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所需权限',
  `Abstract` mediumtext COLLATE utf8mb4_general_ci COMMENT '概要',
  `ShortAbstract` mediumtext COLLATE utf8mb4_general_ci COMMENT '短概要',
  `AppInfo` mediumtext COLLATE utf8mb4_general_ci COMMENT '应用详情',
  `Manual` mediumtext COLLATE utf8mb4_general_ci COMMENT '操作指南',
  `Status` int NOT NULL COMMENT '状态',
  `Show` bit(1) NOT NULL DEFAULT b'1' COMMENT '显示',
  `ParentId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '父Id',
  `Level` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '层级',
  `Sort` int NOT NULL COMMENT '排序',
  `AdminId` varchar(64) COLLATE utf8mb4_general_ci NOT NULL,
  `AdminName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_sys_app_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='云市场应用';

-- ----------------------------
-- Table structure for sys_banner
-- ----------------------------
DROP TABLE IF EXISTS `sys_banner`;
CREATE TABLE `sys_banner` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Sort` int NOT NULL COMMENT '排序',
  `Type` int NOT NULL COMMENT '类型',
  `Status` int NOT NULL COMMENT '状态',
  `TargetType` int NOT NULL COMMENT '操作类型',
  `Title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标题',
  `ImageUrl` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图片',
  `Content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '内容',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_sys_banner_Type` (`Type`),
  KEY `IX_sys_banner_Status` (`Status`),
  KEY `IX_sys_banner_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for sys_idempotency_check
-- ----------------------------
DROP TABLE IF EXISTS `sys_idempotency_check`;
CREATE TABLE `sys_idempotency_check` (
  `Key` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一键',
  `Type` int NOT NULL COMMENT '类型，强制区分key',
  `CheckTimes` int NOT NULL COMMENT '次数',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Key`,`Type`),
  KEY `IX_sys_idempotency_check_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for sys_notice_system
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice_system`;
CREATE TABLE `sys_notice_system` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `Describe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '描述',
  `Content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `Type` int NOT NULL COMMENT '类型',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_notice_systeCreatedTime` (`CreatedTime` DESC),
  KEY `IX_notice_systeType` (`Type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统消息';

-- ----------------------------
-- Table structure for sys_settings
-- ----------------------------
DROP TABLE IF EXISTS `sys_settings`;
CREATE TABLE `sys_settings` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for sys_sms
-- ----------------------------
DROP TABLE IF EXISTS `sys_sms`;
CREATE TABLE `sys_sms` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Type` int NOT NULL COMMENT '短信类型',
  `Active` bit(1) NOT NULL COMMENT '有效',
  `Mobile` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机',
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户',
  `Content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '内容',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_sys_sms_Type` (`Type`),
  KEY `IX_sys_sms_Mobile` (`Mobile`),
  KEY `IX_sys_sms_UserId` (`UserId` DESC),
  KEY `IX_sys_sms_Active` (`Active` DESC),
  KEY `IX_sys_sms_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='短信表';

-- ----------------------------
-- Table structure for talent_label
-- ----------------------------
DROP TABLE IF EXISTS `talent_label`;
CREATE TABLE `talent_label` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `VirtualId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '虚拟人才库id',
  `PlatformId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '真实人才库id',
  `DicLabelId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签id',
  PRIMARY KEY (`Id`),
  KEY `IX_talent_label_DicLabelId` (`DicLabelId`),
  KEY `IX_talent_label_VirtualId` (`VirtualId`),
  KEY `IX_talent_label_PlatformId` (`PlatformId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='人才库对应标签表';

-- ----------------------------
-- Table structure for talent_platform
-- ----------------------------
DROP TABLE IF EXISTS `talent_platform`;
CREATE TABLE `talent_platform` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'HrId',
  `SeekerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '求职者Id',
  `Source` int NOT NULL COMMENT '来源',
  `SeekerVisitTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '求职者访问时间',
  `Level` int NOT NULL COMMENT '等级',
  `Status` int NOT NULL COMMENT '状态',
  `Remark` varchar(1024) COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `Deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '删除',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `ChannelId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '渠道Id',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE KEY `IX_talent_platform_HrId_SeekerId` (`HrId`,`SeekerId`),
  KEY `IX_talent_platform_SeekerId` (`SeekerId`),
  KEY `IX_talent_platform_Deleted` (`Deleted`),
  KEY `IX_talent_platform_Status` (`Status` DESC),
  KEY `IX_talent_platform_CreatedTime` (`CreatedTime` DESC),
  KEY `IX_talent_platform_ChannelId` (`ChannelId` DESC) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='平台人才库';

-- ----------------------------
-- Table structure for talent_platform_comment
-- ----------------------------
DROP TABLE IF EXISTS `talent_platform_comment`;
CREATE TABLE `talent_platform_comment` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `PlatformId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '虚拟人才库id',
  `Content` varchar(5000) COLLATE utf8mb4_general_ci NOT NULL COMMENT '评论内容',
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'HrId',
  `HrName` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Hr姓名',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_platform_comment_VirtualId` (`PlatformId`),
  KEY `IX_talent_platform_comment_HrId` (`HrId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='平台人才库简历评论';

-- ----------------------------
-- Table structure for talent_relation
-- ----------------------------
DROP TABLE IF EXISTS `talent_relation`;
CREATE TABLE `talent_relation` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'HrId',
  `SeekerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '求职者Id',
  `TalentId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '虚拟人才Id',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_talent_relation_HrId_TalentId` (`HrId`,`TalentId`),
  KEY `IX_talent_relation_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='人才库（平台用户和虚拟用户关系）';

-- ----------------------------
-- Table structure for talent_upload_record
-- ----------------------------
DROP TABLE IF EXISTS `talent_upload_record`;
CREATE TABLE `talent_upload_record` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'HrId',
  `FileUrl` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件地址',
  `FileName` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件名称',
  `FileExtension` int NOT NULL DEFAULT '0' COMMENT '文件扩展名',
  `RepeatType` int NOT NULL DEFAULT '0' COMMENT '重复类型',
  `UpLoadStatus` int NOT NULL DEFAULT '0' COMMENT '导入状态',
  `TotalNumber` int NOT NULL DEFAULT '0' COMMENT '简历总数量',
  `FailNumber` int NOT NULL DEFAULT '0' COMMENT '失败数量',
  `QualifiedNumber` int NOT NULL DEFAULT '0' COMMENT '合格数量',
  `RepeatNumber` int NOT NULL DEFAULT '0' COMMENT '重复数量',
  `WarehousingNumber` int NOT NULL DEFAULT '0' COMMENT '入库数量',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_upload_record_HrId` (`HrId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='虚拟人才库导入记录';

-- ----------------------------
-- Table structure for talent_upload_recordsub
-- ----------------------------
DROP TABLE IF EXISTS `talent_upload_recordsub`;
CREATE TABLE `talent_upload_recordsub` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `RecordId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '导入记录id',
  `VirtualId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '虚拟人才库id',
  `IsWarehousing` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否入库',
  `IsRepeat` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否重复',
  `IsSuccess` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否成功',
  `Remarks` varchar(1000) COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_upload_recordsub_RecordId` (`RecordId`),
  KEY `IX_talent_upload_recordsub_VirtualId` (`VirtualId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='虚拟人才库导入记录子表';

-- ----------------------------
-- Table structure for talent_virtual
-- ----------------------------
DROP TABLE IF EXISTS `talent_virtual`;
CREATE TABLE `talent_virtual` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'HrId',
  `Channel` int NOT NULL DEFAULT '0' COMMENT '简历渠道',
  `Status` int NOT NULL DEFAULT '0' COMMENT '简历状态',
  `SeekerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '求职者Id，建立关系后有值',
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `Mobile` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机',
  `HeadPortrait` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '头像',
  `Sex` int NOT NULL DEFAULT '0' COMMENT '性别',
  `Education` int NOT NULL DEFAULT '0' COMMENT '学历',
  `Mailbox` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '邮箱',
  `WeChat` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '微信',
  `QQ` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qq',
  `Birthday` date NOT NULL DEFAULT '1970-01-01' COMMENT '生日',
  `WorkTime` date NOT NULL DEFAULT '1970-01-01' COMMENT '开始工作日期',
  `Perfection` int NOT NULL DEFAULT '0' COMMENT '简历完善度',
  `SelfEvaluation` varchar(1000) COLLATE utf8mb4_general_ci NOT NULL COMMENT '自我评价',
  `SkillAnalysis` varchar(5000) COLLATE utf8mb4_general_ci NOT NULL COMMENT '技能分析（小析补充信息字段）',
  `IndustryLabel` varchar(1000) COLLATE utf8mb4_general_ci NOT NULL COMMENT '行业标签（来源小析职业标签字段）',
  `PostLabel` varchar(1000) COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位标签（来源小析职业标签字段）',
  `OtherLabel` varchar(1000) COLLATE utf8mb4_general_ci NOT NULL COMMENT '其他标签（来源小析职业标签字段）',
  `Highlights` varchar(1000) COLLATE utf8mb4_general_ci NOT NULL COMMENT '亮点',
  `Risks` varchar(1000) COLLATE utf8mb4_general_ci NOT NULL COMMENT '风险',
  `OriginalUrl` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '原始简历地址',
  `Location` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '所在地',
  `LocationNorm` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '所在地（标准地址）',
  `DetailedLocation` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细位置',
  `RegionId` varchar(15) COLLATE utf8mb4_general_ci NOT NULL COMMENT '地区Id',
  `Remark` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_virtual_HrId` (`HrId`),
  KEY `IX_talent_virtual_Mobile` (`Mobile`),
  KEY `IX_talent_virtual_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='虚拟人才库';

-- ----------------------------
-- Table structure for talent_virtual_comment
-- ----------------------------
DROP TABLE IF EXISTS `talent_virtual_comment`;
CREATE TABLE `talent_virtual_comment` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `VirtualId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '虚拟人才库id',
  `Content` varchar(5000) COLLATE utf8mb4_general_ci NOT NULL COMMENT '评论内容',
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'HrId',
  `HrName` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Hr姓名',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_virtual_comment_VirtualId` (`VirtualId`),
  KEY `IX_talent_virtual_comment_HrId` (`HrId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='虚拟人才库简历评论';

-- ----------------------------
-- Table structure for talent_virtual_edu
-- ----------------------------
DROP TABLE IF EXISTS `talent_virtual_edu`;
CREATE TABLE `talent_virtual_edu` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `VirtualId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '虚拟人才库id',
  `SchoolName` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '学校名称',
  `IsFullTime` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否全日制',
  `Education` int NOT NULL DEFAULT '0' COMMENT '学历',
  `MajorName` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '专业名称',
  `StartTime` date DEFAULT NULL COMMENT '教育开始时间',
  `EndTime` date DEFAULT NULL COMMENT '教育结束时间',
  `SchoolRemarks` mediumtext COLLATE utf8mb4_general_ci COMMENT '学校注释',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_virtual_edu_VirtualId` (`VirtualId`),
  KEY `IX_talent_virtual_edu_SchoolName` (`SchoolName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='虚拟人才库教育经历';

-- ----------------------------
-- Table structure for talent_virtual_hope
-- ----------------------------
DROP TABLE IF EXISTS `talent_virtual_hope`;
CREATE TABLE `talent_virtual_hope` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `VirtualId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '虚拟人才库id',
  `HopeCity` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '期望城市',
  `RegionId` varchar(15) COLLATE utf8mb4_general_ci NOT NULL COMMENT '地区Id',
  `IndustryName` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '行业名称（多个）',
  `PostName` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位名称（多个）',
  `MinSalary` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '最低薪资',
  `MaxSalary` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '最高薪资',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_talent_virtual_hope_VirtualId` (`VirtualId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='虚拟人才库求职期望';

-- ----------------------------
-- Table structure for talent_virtual_project
-- ----------------------------
DROP TABLE IF EXISTS `talent_virtual_project`;
CREATE TABLE `talent_virtual_project` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `VirtualId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '虚拟人才库id',
  `ProjectName` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `PostName` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位名称',
  `StartTime` date DEFAULT NULL COMMENT '开始时间',
  `EndTime` date DEFAULT NULL COMMENT '结束时间',
  `ProjectRemarks` mediumtext COLLATE utf8mb4_general_ci COMMENT '项目描述',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_virtual_project_VirtualId` (`VirtualId`),
  KEY `IX_talent_virtual_project_ProjectName` (`ProjectName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='虚拟人才库项目经历';

-- ----------------------------
-- Table structure for talent_virtual_work
-- ----------------------------
DROP TABLE IF EXISTS `talent_virtual_work`;
CREATE TABLE `talent_virtual_work` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `VirtualId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '虚拟人才库id',
  `CompanyName` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司名称',
  `Department` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司部门',
  `IndustryName` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '行业名称',
  `PostName` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位名称',
  `MinSalary` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '最低薪资',
  `MaxSalary` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '最高薪资',
  `StartTime` date DEFAULT NULL COMMENT '开始时间',
  `EndTime` date DEFAULT NULL COMMENT '结束时间',
  `CompanyRemarks` mediumtext COLLATE utf8mb4_general_ci COMMENT '公司描述',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_talent_virtual_work_VirtualId` (`VirtualId`),
  KEY `IX_talent_virtual_work_CompanyName` (`CompanyName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='虚拟人才库工作经历';

-- ----------------------------
-- Table structure for tasks
-- ----------------------------
DROP TABLE IF EXISTS `tasks`;
CREATE TABLE `tasks` (
  `TaskId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `TargetId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '相关Id，根据type区分类型',
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `Content` mediumtext COLLATE utf8mb4_general_ci COMMENT '任务内容',
  `Type` int NOT NULL DEFAULT '0' COMMENT '任务类型',
  `Status` int NOT NULL DEFAULT '0' COMMENT '任务状态',
  `ResultText` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '结果文本',
  `EndTime` datetime(6) DEFAULT NULL COMMENT '结束时间',
  `Total` int NOT NULL DEFAULT '0' COMMENT '总数',
  `Successful` int NOT NULL DEFAULT '0' COMMENT '成功数',
  `Failed` int NOT NULL DEFAULT '0' COMMENT '失败数',
  `Duplicate` int NOT NULL DEFAULT '0' COMMENT '重复数',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`TaskId`),
  KEY `IX_tasks_TargetId_Type_CreatedTime` (`Type`,`TargetId` DESC,`CreatedTime`),
  KEY `IX_tasks_Status_Type_CreatedTime` (`Status`,`Type`,`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='通用任务';

-- ----------------------------
-- Table structure for tasks_detail
-- ----------------------------
DROP TABLE IF EXISTS `tasks_detail`;
CREATE TABLE `tasks_detail` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `TaskId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Key` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '检索关键字（预留）',
  `Content` mediumtext COLLATE utf8mb4_general_ci COMMENT '内容',
  `Result` int NOT NULL DEFAULT '0' COMMENT '结果',
  `GroupStatus` int NOT NULL DEFAULT '0' COMMENT '状态分组（用来查询比如重复的）',
  `ResultText` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '结果文本',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_tasks_detail_full` (`TaskId`,`Result`,`GroupStatus`,`CreatedTime` DESC,`Key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='通用任务明细';

-- ----------------------------
-- Table structure for token_access
-- ----------------------------
DROP TABLE IF EXISTS `token_access`;
CREATE TABLE `token_access` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `RefreshTokenId` int DEFAULT NULL COMMENT 'RefreshTokenId',
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Type` int DEFAULT NULL COMMENT '类型',
  `Client` int DEFAULT NULL COMMENT '客户端',
  `Token` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'token',
  `ExpirationTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `idx_token_access_Token` (`Token`),
  KEY `idx_token_access_Type_UserId` (`Type`,`UserId`),
  KEY `idx_token_access_ExpirationTime` (`ExpirationTime` DESC)
) ENGINE=InnoDB AUTO_INCREMENT=37959 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='访问令牌';

-- ----------------------------
-- Table structure for token_refresh
-- ----------------------------
DROP TABLE IF EXISTS `token_refresh`;
CREATE TABLE `token_refresh` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Type` int DEFAULT NULL COMMENT '类型',
  `Client` int DEFAULT NULL COMMENT '客户端',
  `Token` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'token',
  `ExpirationTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `idx_token_refresh_Token` (`Token`),
  KEY `idx_token_refresh_Type_UserId` (`Type`,`UserId`),
  KEY `idx_token_refresh_ExpirationTime` (`ExpirationTime` DESC)
) ENGINE=InnoDB AUTO_INCREMENT=16240 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='刷新令牌';

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Mobile` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
  `WeChatId` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信UnionId',
  `WeChatName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信昵称',
  `WeChatH5OpenId` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公众号OpenId',
  `WeChatH5Subscribe` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否订阅公众号',
  `IdentityCardType` int DEFAULT '0',
  `IdentityCard` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '身份证号',
  `IdentityCardName` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '身份证名字',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`UserId`),
  UNIQUE KEY `IX_user_Mobile` (`Mobile`),
  UNIQUE KEY `IX_user_WeChatId` (`WeChatId` DESC),
  UNIQUE KEY `IX_user_WeChatH5OpenId` (`WeChatH5OpenId` DESC),
  KEY `IX_user_IdentityCard` (`IdentityCard` DESC),
  KEY `IX_user_WeChatH5Subscribe` (`WeChatH5Subscribe` DESC),
  KEY `IX_user_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户主表';

-- ----------------------------
-- Table structure for user_agent_ent
-- ----------------------------
DROP TABLE IF EXISTS `user_agent_ent`;
CREATE TABLE `user_agent_ent` (
  `AgentEntId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业名称',
  `Abbr` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业简称',
  `Display` int NOT NULL COMMENT '展示项',
  `DisplayName` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '展示名称',
  `LogoUrl` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业Logo',
  `AuthorizationUrl` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业授权证明',
  `Industry` int NOT NULL COMMENT '公司行业',
  `Nature` int NOT NULL COMMENT '企业性质',
  `Scale` int NOT NULL COMMENT '企业规模',
  `Capital` int NOT NULL COMMENT '融资阶段',
  `Describe` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `Status` int NOT NULL COMMENT '状态',
  `RegionId` varchar(15) COLLATE utf8mb4_general_ci NOT NULL COMMENT '地区Id',
  `Address` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地址',
  `Location` point NOT NULL DEFAULT (point(0,0)),
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`AgentEntId`),
  KEY `IX_user_agent_ent_UserId` (`UserId`),
  KEY `IX_user_agent_ent_Name` (`Name`),
  KEY `IX_user_agent_ent_RegionId` (`RegionId`),
  KEY `IX_user_agent_ent_Status` (`Status`),
  KEY `IX_user_agent_ent_Industry` (`Industry`),
  KEY `IX_user_agent_ent_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='代招企业';

-- ----------------------------
-- Table structure for user_balance
-- ----------------------------
DROP TABLE IF EXISTS `user_balance`;
CREATE TABLE `user_balance` (
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `HrBalance` decimal(18,6) NOT NULL DEFAULT '0.000000' COMMENT 'hr余额',
  `HrBalanceFull` decimal(18,6) NOT NULL DEFAULT '0.000000' COMMENT 'Hr历史余额',
  `HrWithdrawTime` datetime(6) DEFAULT NULL COMMENT 'hr提现时间',
  `SeekerBalance` decimal(18,6) NOT NULL DEFAULT '0.000000' COMMENT '求职者余额',
  `SeekerBalanceFull` decimal(18,6) NOT NULL DEFAULT '0.000000' COMMENT '求职者历史余额',
  `SeekerWithdrawTime` datetime(6) DEFAULT NULL COMMENT '求职者提现时间',
  PRIMARY KEY (`UserId`),
  KEY `IX_user_balance_HrBalance` (`HrBalance` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户余额表';

-- ----------------------------
-- Table structure for user_balance_record
-- ----------------------------
DROP TABLE IF EXISTS `user_balance_record`;
CREATE TABLE `user_balance_record` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Amount` decimal(18,6) NOT NULL COMMENT '当前值',
  `Increment` decimal(18,6) NOT NULL COMMENT '增量',
  `UserType` int NOT NULL COMMENT 'hr还是求职者',
  `Type` int NOT NULL COMMENT '类型',
  `Content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '记录内容',
  `Data` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据json',
  `EventTime` datetime(6) NOT NULL COMMENT '发生时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_balance_record_UserId` (`UserId`),
  KEY `IX_user_balance_record_Type` (`Type` DESC),
  KEY `IX_user_balance_record_UserType` (`UserType` DESC),
  KEY `IX_user_balance_record_EventTime` (`EventTime` DESC),
  KEY `IX_user_balance_record_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户余额记录表';

-- ----------------------------
-- Table structure for user_campus
-- ----------------------------
DROP TABLE IF EXISTS `user_campus`;
CREATE TABLE `user_campus` (
  `Id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `UserId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `Award` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位/奖项',
  `BeginDate` date NOT NULL COMMENT '开始时间',
  `EndDate` date NOT NULL COMMENT '结束时间',
  `Describe` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`Id`),
  KEY `IX_user_campus_UserId` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='校园经历';

-- ----------------------------
-- Table structure for user_channel_relation
-- ----------------------------
DROP TABLE IF EXISTS `user_channel_relation`;
CREATE TABLE `user_channel_relation` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `ChannelId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '渠道Id',
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '顾问Id',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_user_channel_relation_ChannelId_HrId` (`ChannelId`,`HrId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='渠道顾问关系';

-- ----------------------------
-- Table structure for user_collect_post
-- ----------------------------
DROP TABLE IF EXISTS `user_collect_post`;
CREATE TABLE `user_collect_post` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '顾问Id',
  `SeekerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '求职者Id',
  `TeamPostId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '协同职位Id',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_collect_post_TeamPostId` (`TeamPostId` DESC),
  KEY `IX_user_collect_post_SeekerId_HrId_TeamPostId_CreatedTime` (`SeekerId`,`HrId`,`TeamPostId`,`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职位收藏';

-- ----------------------------
-- Table structure for user_dingding
-- ----------------------------
DROP TABLE IF EXISTS `user_dingding`;
CREATE TABLE `user_dingding` (
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `DingAvatar` varchar(500) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉头像',
  `DingName` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉姓名',
  `DingJobNo` varchar(10) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉工号',
  `DingMobile` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉手机号',
  `DingUserid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉userid',
  `DingUnionid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉unionid',
  `IsPush` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否开启推送',
  `DingTitle` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉职位',
  `DingBranch` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户钉钉部门',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户钉钉信息扩展表';

-- ----------------------------
-- Table structure for user_extend
-- ----------------------------
DROP TABLE IF EXISTS `user_extend`;
CREATE TABLE `user_extend` (
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `AdviserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '当前顾问Id',
  `LoginTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '登录时间',
  `RegistrationIp` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '说明',
  `Inviter` varchar(18) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邀约人',
  PRIMARY KEY (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户扩展表';

-- ----------------------------
-- Table structure for user_follow_industry
-- ----------------------------
DROP TABLE IF EXISTS `user_follow_industry`;
CREATE TABLE `user_follow_industry` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `HrId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '顾问Id',
  `SeekerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '求职者Id',
  `Industry` int NOT NULL COMMENT '行业Id',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_follow_industry_SeekerId_HrId_CreatedTime_Industry` (`SeekerId`,`HrId`,`Industry`,`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='关注行业';

-- ----------------------------
-- Table structure for user_hr
-- ----------------------------
DROP TABLE IF EXISTS `user_hr`;
CREATE TABLE `user_hr` (
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `NuoId` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '诺聘PK_EAID',
  `InviteCode` varchar(9) COLLATE utf8mb4_general_ci NOT NULL COMMENT '邀请码',
  `EntId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业Id',
  `RoleId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '角色Id',
  `WeChatAppletId` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信小程序OpenId',
  `Powers` varchar(1024) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '权限列表',
  `Post` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职位',
  `EMail` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `Avatar` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '头像',
  `NickName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '昵称',
  `WeChatNo` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信号',
  `EntWeChatQrCode` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业微信二维码',
  `AppletQrCode` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '小程序二维码',
  `Sex` int DEFAULT NULL COMMENT '性别',
  `Describe` varchar(5000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `RegionId` varchar(15) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地区Id',
  `Address` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地址',
  `Location` point NOT NULL DEFAULT (point(0,0)),
  `Source` int NOT NULL DEFAULT '0' COMMENT '来源',
  `Status` int NOT NULL DEFAULT '1' COMMENT '状态',
  `Score` int NOT NULL COMMENT '资料完整度',
  `ApplicationTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '申请开通零工时间',
  `TencentImId` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'IM账号Id',
  `H5Notice` bit(1) NOT NULL DEFAULT b'1' COMMENT '公众号开关通知',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`UserId`) USING BTREE,
  UNIQUE KEY `IX_user_hr_NuoId` (`NuoId`),
  UNIQUE KEY `IX_user_hr_InviteCode` (`InviteCode`),
  KEY `IX_user_hr_RoleId` (`RoleId`),
  KEY `IX_user_hr_Status` (`Status`),
  KEY `IX_user_hr_EntId` (`EntId` DESC),
  KEY `IX_user_hr_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='hr主表';

-- ----------------------------
-- Table structure for user_hr_audit
-- ----------------------------
DROP TABLE IF EXISTS `user_hr_audit`;
CREATE TABLE `user_hr_audit` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `EntId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Status` int NOT NULL COMMENT '状态',
  `Describe` varchar(1025) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `AdminId` varchar(64) COLLATE utf8mb4_general_ci NOT NULL,
  `AdminName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_hr_UserId` (`UserId`),
  KEY `IX_user_hr_EntId` (`EntId`),
  KEY `IX_user_hr_audit_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='hr审批记录表';

-- ----------------------------
-- Table structure for user_login_record
-- ----------------------------
DROP TABLE IF EXISTS `user_login_record`;
CREATE TABLE `user_login_record` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Type` int NOT NULL COMMENT '类型',
  `Client` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ClientId',
  `IpAddress` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Ip',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_login_record_UserId` (`UserId`),
  KEY `IX_user_login_record_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户登录记录';

-- ----------------------------
-- Table structure for user_nscore
-- ----------------------------
DROP TABLE IF EXISTS `user_nscore`;
CREATE TABLE `user_nscore` (
  `UserId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `Score` int NOT NULL COMMENT '当前诺积分',
  `ScoreTotal` int NOT NULL COMMENT '历史累计诺积分',
  `SeekerScore` int NOT NULL COMMENT '求职者当前诺积分',
  `SeekerScoreTotal` int NOT NULL COMMENT '求职者历史累计诺积分',
  `HrScore` int NOT NULL COMMENT 'hr当前诺积分',
  `HrScoreTotal` int NOT NULL COMMENT 'hr历史累计诺积分',
  `SeekerMoney` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '求职者兑换现金',
  `SeekerPrize` int NOT NULL DEFAULT '0' COMMENT '求职者兑换奖品',
  PRIMARY KEY (`UserId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='诺积分';

-- ----------------------------
-- Table structure for user_nscore_goods
-- ----------------------------
DROP TABLE IF EXISTS `user_nscore_goods`;
CREATE TABLE `user_nscore_goods` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `UserType` int NOT NULL COMMENT 'hr还是求职者',
  `Type` int NOT NULL COMMENT '类型',
  `Name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `Score` int NOT NULL COMMENT '所需积分',
  `Money` decimal(18,2) NOT NULL COMMENT '商品价值',
  `Content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `UpdatedBy` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `Active` bit(1) NOT NULL DEFAULT b'1',
  `Describe` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `Stock` int NOT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='诺积分商城';

-- ----------------------------
-- Table structure for user_nscore_order
-- ----------------------------
DROP TABLE IF EXISTS `user_nscore_order`;
CREATE TABLE `user_nscore_order` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `GoodsId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Type` int NOT NULL COMMENT '类型',
  `Status` int NOT NULL,
  `Name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `Score` int NOT NULL COMMENT '所需积分',
  `Money` decimal(10,2) NOT NULL COMMENT '商品价值',
  `Address` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `DeliveryTime` datetime(6) DEFAULT NULL COMMENT '发货时间',
  `Express` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `ExpressNo` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `UpdatedBy` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_nscore_order_GoodsId` (`GoodsId`),
  KEY `IX_user_nscore_order_UserId` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='诺积分商城订单';

-- ----------------------------
-- Table structure for user_nscore_record
-- ----------------------------
DROP TABLE IF EXISTS `user_nscore_record`;
CREATE TABLE `user_nscore_record` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Amount` int NOT NULL COMMENT '当前值',
  `Increment` int NOT NULL COMMENT '增量',
  `UserType` int NOT NULL COMMENT 'hr还是求职者',
  `Type` int NOT NULL COMMENT '类型',
  `Content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '记录内容',
  `Data` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据json',
  `EventTime` datetime(6) NOT NULL COMMENT '发生时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  KEY `IX_user_nscore_record_UserId` (`UserId`),
  KEY `IX_user_nscore_record_Type` (`Type`),
  KEY `IX_user_nscore_record_UserType` (`UserType`),
  KEY `IX_user_nscore_record_EventTime` (`EventTime` DESC),
  KEY `IX_user_nscore_record_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='诺积分记录';

-- ----------------------------
-- Table structure for user_num
-- ----------------------------
DROP TABLE IF EXISTS `user_num`;
CREATE TABLE `user_num` (
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Talent` int NOT NULL COMMENT '人才数量',
  `VirtualTalent` int NOT NULL COMMENT '虚拟人才库数量',
  `Adviser` int NOT NULL COMMENT '顾问数量',
  `Likes` int NOT NULL COMMENT '点赞数量',
  PRIMARY KEY (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户各种数量';

-- ----------------------------
-- Table structure for user_resume
-- ----------------------------
DROP TABLE IF EXISTS `user_resume`;
CREATE TABLE `user_resume` (
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Occupation` int DEFAULT NULL COMMENT '职业',
  `Sex` int DEFAULT NULL COMMENT '性别',
  `Education` int DEFAULT NULL COMMENT '学历',
  `School` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '学校',
  `GraduationDate` date DEFAULT NULL COMMENT '毕业日期',
  `Major` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '专业名称',
  `Score` int NOT NULL COMMENT '简历积分',
  `Describe` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `Nature` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '性格',
  `Skill` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '技能',
  `Appearance` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '外貌',
  `Birthday` date DEFAULT NULL COMMENT '生日',
  `EMail` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `WeChatNo` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信号',
  `Qq` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'qq号',
  `Show` bit(1) NOT NULL DEFAULT b'0' COMMENT '简历开放',
  `Anonymous` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否匿名',
  `Certificate` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '证书',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`UserId`),
  KEY `IX_user_resume_UpdatedTime` (`UpdatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户简历';

-- ----------------------------
-- Table structure for user_resume_attach
-- ----------------------------
DROP TABLE IF EXISTS `user_resume_attach`;
CREATE TABLE `user_resume_attach` (
  `Id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `UserId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `Title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '附件标题',
  `Url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件地址',
  `Size` int NOT NULL COMMENT '大小',
  `Language` int NOT NULL COMMENT '语言',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_user_resume_attach_UserId` (`UserId`),
  KEY `IX_user_resume_attach_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='附件简历';

-- ----------------------------
-- Table structure for user_seeker
-- ----------------------------
DROP TABLE IF EXISTS `user_seeker`;
CREATE TABLE `user_seeker` (
  `UserId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `NuoId` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '诺聘PK_UAID',
  `WeChatAppletId` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信小程序OpenId',
  `WeChatInterviewerAppletId` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '面试官小程序OpenId',
  `Avatar` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '头像',
  `NickName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '昵称',
  `H5Notice` bit(1) NOT NULL DEFAULT b'1' COMMENT '公众号开关通知',
  `InterviewerEnt` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '面试官所在企业',
  `RegionId` varchar(15) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地区Id',
  `Address` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地址',
  `Location` point NOT NULL DEFAULT (point(0,0)),
  `Source` int NOT NULL DEFAULT '0' COMMENT '来源',
  `Status` int NOT NULL DEFAULT '1' COMMENT '状态',
  `TencentImId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'IM账号Id',
  `HrAppletQrCode` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '简历hr小程序二维码',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`UserId`) USING BTREE,
  KEY `IX_user_seeker_NuoId` (`NuoId`),
  KEY `IX_user_seeker_Status` (`Status`),
  KEY `IX_user_seeker_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='求职者主表';

-- ----------------------------
-- Table structure for user_seeker_adviser
-- ----------------------------
DROP TABLE IF EXISTS `user_seeker_adviser`;
CREATE TABLE `user_seeker_adviser` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `SeekerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '求职者Id',
  `AdviserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '顾问Id',
  `VisitTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '访问时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_user_seeker_adviser_SeekerId_AdviserId` (`SeekerId`,`AdviserId`),
  KEY `IX_user_seeker_adviser_CreatedTime` (`CreatedTime` DESC),
  KEY `IX_user_seeker_adviser_SeekerId_Full` (`SeekerId`,`AdviserId`,`VisitTime` DESC,`CreatedTime` DESC),
  KEY `IX_user_seeker_adviser_AdviserId_Full` (`AdviserId`,`SeekerId`,`VisitTime` DESC,`CreatedTime` DESC) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户的顾问列表';

-- ----------------------------
-- Table structure for user_seeker_guidance
-- ----------------------------
DROP TABLE IF EXISTS `user_seeker_guidance`;
CREATE TABLE `user_seeker_guidance` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `SeekerId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '求职者Id',
  `AdviserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '顾问Id',
  `FreeTimeType` int DEFAULT NULL COMMENT '可沟通时间类型',
  `FreeTime` datetime(6) DEFAULT NULL COMMENT '可沟通时间',
  `CanPhone` bit(1) DEFAULT NULL COMMENT '可电话沟通',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_user_seeker_guidance_SeekerId_AdviserId` (`SeekerId`,`AdviserId`),
  KEY `IX_user_seeker_guidance_SeekerId` (`SeekerId`),
  KEY `IX_user_seeker_guidance_AdviserId` (`AdviserId`),
  KEY `IX_user_seeker_guidance_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='就业指导';

-- ----------------------------
-- Table structure for user_sms
-- ----------------------------
DROP TABLE IF EXISTS `user_sms`;
CREATE TABLE `user_sms` (
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `BusinessSms` int NOT NULL COMMENT '业务短信数量',
  PRIMARY KEY (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户业务短信';

-- ----------------------------
-- Table structure for user_sms_record
-- ----------------------------
DROP TABLE IF EXISTS `user_sms_record`;
CREATE TABLE `user_sms_record` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Type` tinyint NOT NULL DEFAULT '0' COMMENT '类型',
  `UserId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `Amount` int NOT NULL COMMENT '数量',
  `Balance` int NOT NULL COMMENT '余额',
  `Content` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '内容',
  `TaskId` varchar(18) COLLATE utf8mb4_general_ci NOT NULL COMMENT '通用任务Id',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_sms_record_UserId` (`UserId`),
  KEY `IX_user_sms_record_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户短信记录表';

-- ----------------------------
-- Table structure for user_withdraw
-- ----------------------------
DROP TABLE IF EXISTS `user_withdraw`;
CREATE TABLE `user_withdraw` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `UserType` int NOT NULL COMMENT 'hr还是求职者',
  `Type` int NOT NULL COMMENT '提现类型',
  `Status` int NOT NULL COMMENT '状态',
  `Amount` decimal(10,2) NOT NULL COMMENT '数值',
  `Account` varchar(50) DEFAULT NULL COMMENT '提现账户',
  `AccountName` varchar(50) DEFAULT NULL COMMENT '提现账户名称',
  `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `Approver` varchar(18) DEFAULT NULL,
  `ApprovalTime` datetime(6) DEFAULT NULL COMMENT '审批通过时间',
  `WithdrawTime` datetime(6) DEFAULT NULL COMMENT '提现时间',
  `CreatedTime` datetime(6) NOT NULL,
  PRIMARY KEY (`Id`),
  KEY `IX_user_withdraw_UserId` (`UserId`),
  KEY `IX_user_withdraw_Type` (`Type`),
  KEY `IX_user_withdraw_Status` (`Status`),
  KEY `IX_user_withdraw_WithdrawTime` (`WithdrawTime` DESC),
  KEY `IX_user_withdraw_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='提现';

-- ----------------------------
-- Table structure for user_work
-- ----------------------------
DROP TABLE IF EXISTS `user_work`;
CREATE TABLE `user_work` (
  `Id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `UserId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `Company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司',
  `Post` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位',
  `BeginDate` date NOT NULL COMMENT '开始时间',
  `EndDate` date NOT NULL COMMENT '结束时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `Describe` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`Id`),
  KEY `IX_user_work_UserId` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工作经历';

-- ----------------------------
-- Table structure for wechat
-- ----------------------------
DROP TABLE IF EXISTS `wechat`;
CREATE TABLE `wechat` (
  `WeChatId` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '微信UnionId',
  `WeChatH5OpenId` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公众号OpenId',
  `WeChatH5Subscribe` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否订阅公众号',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`WeChatId`),
  UNIQUE KEY `IX_wechat_WeChatH5OpenId` (`WeChatH5OpenId` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='微信Id';

SET FOREIGN_KEY_CHECKS = 1;
