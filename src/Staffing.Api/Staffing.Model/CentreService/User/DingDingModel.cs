﻿using Config.CommonModel;

namespace Staffing.Model.CentreService.User;

public class DingDingModel
{
}

/// <summary>
/// 获取钉钉用户列表接收模型
/// </summary>
public class GetDingDingListRequest: QueryRequest
{
    /// <summary>
    /// 搜索类型（0用户名，1钉钉手机号，2工号，3诺聘手机号，4部门，5职位）
    /// </summary>
    public int? Type { get; set; }

    /// <summary>
    /// 搜索
    /// </summary>
    public string? Search { get; set; }
}

/// <summary>
/// 获取钉钉用户列表返回模型
/// </summary>
public class GetDingDingListResponse: QueryResponse
{
    public List<GetDingDingListModel> Rows { get; set; } = default!;
}

/// <summary>
/// 获取钉钉用户列表模型
/// </summary>
public class GetDingDingListModel
{
    /// <summary>
    /// 钉钉userid
    /// </summary>
    public string DingUserid { get; set; } = default!;

    /// <summary>
    /// 头像
    /// </summary>
    public string? DingAvatar { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? DingName { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? DingMobile { get; set; }

    /// <summary>
    /// 部门
    /// </summary>
    public string? DingBranch { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? DingTitle { get; set; }

    /// <summary>
    /// 工号
    /// </summary>
    public string? DingJobNo { get; set; }

    /// <summary>
    /// 最后推送时间
    /// </summary>
    public DateTime? PushTime { get; set; }

    /// <summary>
    /// 诺聘手机号
    /// </summary>
    public string? NuoPinMobile { get; set; } = string.Empty;

    /// <summary>
    /// 绑定状态
    /// </summary>
    public bool IsBind { get; set; } = false;
}

/// <summary>
/// 添加钉钉用户请求模型
/// </summary>
public class AddDingDingUserRequest
{
    /// <summary>
    /// 手机号
    /// </summary>
    public string Mobile { get; set; } = default!;
}

/// <summary>
/// 绑定钉钉用户请求模型
/// </summary>
public class BindDingDingByDingUserIdRequest
{
    /// <summary>
    /// 钉钉userid
    /// </summary>
    public string DingUserid { get; set; } = default!;

    /// <summary>
    /// 诺聘手机号
    /// </summary>
    public string NuoPinMobile { get; set; } = default!;
}

/// <summary>
/// 解除绑定请求模型
/// </summary>
public class UnBindDingDingByDingUserIdRequest
{
    /// <summary>
    /// 钉钉userid
    /// </summary>
    public string DingUserid { get; set; } = default!;
}

/// <summary>
/// 删除钉钉用户请求模型
/// </summary>
public class DeleteDingDingRequest
{
    public List<string> DingUserid { get; set; } = new List<string>();
}

