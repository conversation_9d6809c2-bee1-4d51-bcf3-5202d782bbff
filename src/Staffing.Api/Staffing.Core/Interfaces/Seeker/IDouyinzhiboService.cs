﻿using Config.CommonModel;
using Staffing.Model.Seeker.Douyinzhibo;

namespace Staffing.Core.Interfaces.Seeker;

public interface IDouyinzhiboService
{
    /// <summary>
    /// 创建直播时获取职位分组
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<PostsGroupsResponse> GetPostGroups(GroupRequest request);

    /// <summary>
    /// 创建直播时获取职位列表
    /// </summary>
    /// <returns></returns>
    PostListForDouyinZhiboResponse GetPostListForCreate(DouyinzhiboPostRequestForCreate request);

    /// <summary>
    /// 根据直播ID获取职位列表
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    Task<PostListForDouyinZhiboResponse> GetPostListByZhiboId(DouyinzhiboPostRequestForEdit request);

    /// <summary>
    /// 保存
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    EmptyResponse ZhiboSave(ZhiboSaveRequest request);

    /// <summary>
    /// 解说岗位
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    EmptyResponse SpeekThePost(SpeekRequest request);

    /// <summary>
    /// 获取直播表单数据
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    ZhiboEditResponse ZhiboEdit(string id);

    /// <summary>
    /// 直播挂载（打标）
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    EmptyResponse ZhiboLoad(string id);

    /// <summary>
    /// 开启加热
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    EmptyResponse HotTheGroup(HotRequest request);

    /// <summary>
    /// 获取直播列表
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    ZhiboListResponse ZhiboList(ZhiboListRequest request);
}