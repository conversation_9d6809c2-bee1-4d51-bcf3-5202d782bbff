﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Noah;

/// <summary>
/// 合同
/// </summary>
[Table("contract")]
public class Contract
{
    /// <summary>
    /// id
    /// </summary>
    [Key]
    public long ContractId { get; set; }
    /// <summary>
    /// 合同编号
    /// </summary>
    public string? ContractCode { get; set; }
    /// <summary>
    /// 合同主题
    /// </summary>
    public string? Item { get; set; }
    /// <summary>
    /// 原合同编码
    /// </summary>
    public string? OriginalCode { get; set; }
    /// <summary>
    /// 合同分类：\r\naf009cfa-bce5-6ef8-b4c4-d3ff04382dc9：产品类合同；\r\n3cbefb3c-cd15-b48a-d141-de0c6519f889： 采购类合同；\r\nf9ccabd2-6842-4e39-7172-22c3db83b55f：虚拟合同\r\n510d3393-3bd5-17dc-a3ee-f9cb9b734ce8：框架子协议-通信；\r\n209512a9-5769-e66e-48c9-029ef36df57f：框架子协议-人力；\r\n461cb193-540a-3f54-7c2f-84917d91bb48：其它类\r\n08528b58-dd8a-f310-6745-4d84899d6214：合同变更(作废勿选)；\r\na6c9766d-67d2-037b-57fd-60b22f67e9ac：解除合同(作废勿选)
    /// </summary>
    public string? Type { get; set; }
    /// <summary>
    /// 客户id
    /// </summary>
    public long? CustomerId { get; set; }
    /// <summary>
    /// 合同性质：\\r\\nd733f176-dc9f-657b-7047-8a4026b9b100：新签，\\r\\n7790fe46-2237-9669-c971-e8cb567f5cd8：续签，\\r\\nb70e8e90-e2c1-204e-1593-63e257ab2f99：变更，\\r\\n3c983fd8-901e-b60b-1ae6-b2dbae981b38：解除，\\r\\n3daa5d6e-c12d-1703-a2bc-45a27d25f49f：补充，\\r\\n09fbc921-53c5-74e0-4f0e-3cd2ebe28b0a：虚拟，\\r\\n60787a3b-4c33-533c-83d3-c72e422f9123：其它
    /// </summary>
    public string? Nature { get; set; }
    /// <summary>
    /// 营销规范\\r\\n8b5437fb-051d-54ae-d81e-67f21627beae：符合，\\r\\ne0656395-12ba-0ec8-bdae-157a1fc48010：不符合
    /// </summary>
    public string? SaleStandard { get; set; }
    /// <summary>
    /// 履约情况\\r\\n9a7643b6-8468-0f6e-1a91-424d06df0ec5：是，\\r\\nfdc3e60f-80ea-cca5-7192-b886a188f19d：否
    /// </summary>
    public string? Performance { get; set; }
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }
    /// <summary>
    /// 到期时间
    /// </summary>
    public DateTime? DueTime { get; set; }
    /// <summary>
    /// 合同范本\\r\\nf703a011-59ef-8565-92ba-0bab7db13adf：是\\r\\nd25d3ee5-cb8e-4284-c3ea-d512d3481de8：否
    /// </summary>
    public string? ModelContract { get; set; }
    /// <summary>
    /// 合同状态:1签约，2执行中，3完毕，4终止，5意外终止，6过期，7待续签
    /// </summary>
    public string? State { get; set; }
    /// <summary>
    /// 产品单位：\\r\\n2fbba066-18b9-3890-4281-407cc5ca3240：月（按月），\\r\\na13fef4b-82b0-e949-6f3d-ccea5121e4a7：人（按人），\\r\\n63c783d0-2187-4ccb-1883-b9426f62e6e8：人/月（单价），\\r\\ncd6183d3-89fb-ed75-bf79-3ceee81d0787：项目（总价），\\r\\n1b1d416b-4549-67c2-41db-e080a99a220e：人/年（单价），\\r\\n1ab48689-20c0-244b-92ca-4c2749c9f100：年（单价）
    /// </summary>
    public string? Unit { get; set; }
    /// <summary>
    /// 合同金额
    /// </summary>
    public decimal? Price { get; set; }
    /// <summary>
    /// 合同总金额说明\r\nad1fda96-4dab-727b-f699-12fada300df2：合同总金额，\r\n0850b0b8-0b7c-6901-e105-85fa608400fe：单价（合同金额不确定）
    /// </summary>
    public string? PriceRemark { get; set; }
    /// <summary>
    /// 1网银转账，2现金，3支票，4电汇，5承兑汇票
    /// </summary>
    public string? PaymentMode { get; set; }
    /// <summary>
    /// 签订日期
    /// </summary>
    public DateTime? SignDate { get; set; }
    /// <summary>
    /// 客户签约人
    /// </summary>
    public string? CustomerSigner { get; set; }
    /// <summary>
    /// 我方签约人
    /// </summary>
    public string? Signer { get; set; }
    /// <summary>
    /// 我方签约单位
    /// </summary>
    public string? SignDept { get; set; }
    /// <summary>
    /// 业务类型：\r\ndaba6f6d-7b6b-2055-f3ba-130a72ea6a0d：现金流新业务-001，\r\n886182d3-db7d-4a64-3a96-9ce8bc53e0e8：项目类产品收入-002，\r\n7debec53-aead-8f82-d6ea-1473516ebf75：现金流老业务-003，\r\nab7a96a3-95e9-9913-b419-e3c93b15d403：其他收入-004
    /// </summary>
    public string? BusinessType { get; set; }
    /// <summary>
    /// 合同附件信息：最多9份文件
    /// </summary>
    public string? Appendix { get; set; }
    /// <summary>
    /// 是否备案：\r\n50c14a15-769f-a488-75bf-df9bbb4b80ce:是；\r\nbe34ac53-b4b5-279a-b043-005804475b5e：否
    /// </summary>
    public string? KeepRecord { get; set; }
    /// <summary>
    /// 数字诺亚编码
    /// </summary>
    public string? SznyCode { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    /// <summary>
    /// 负责人
    /// </summary>
    public string? Principal { get; set; }
    /// <summary>
    /// 协同人
    /// </summary>
    public string? CollaborativePeople { get; set; }
    /// <summary>
    /// 所属部门
    /// </summary>
    public string? Dept { get; set; }
    /// <summary>
    /// 历史数据备注
    /// </summary>
    public string? HistoryRemark { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreateBy { get; set; }
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 更新人
    /// </summary>
    public string? UpdateBy { get; set; }
    /// <summary>
    /// 删除标志
    /// </summary>
    public string? DelFlag { get; set; }
    /// <summary>
    /// 销售机会id
    /// </summary>
    public string? SaleOpportunity { get; set; }
    /// <summary>
    /// 未开票金额
    /// </summary>
    public decimal? NotInvoice { get; set; }
    /// <summary>
    /// 坏账金额
    /// </summary>
    public decimal? UncollectibleAccount { get; set; }
    /// <summary>
    /// 已收金额
    /// </summary>
    public decimal? PaymentAmount { get; set; }
    /// <summary>
    /// 未收金额
    /// </summary>
    public decimal? NonpaymentAmount { get; set; }
    /// <summary>
    /// 收款比例
    /// </summary>
    public string? PaymentProportion { get; set; }
    /// <summary>
    /// 开票金额
    /// </summary>
    public decimal? Invoice { get; set; }
    /// <summary>
    /// 归档状态:1.归档 2.未归档;404:未知状态
    /// </summary>
    public string? ArchivingStatus { get; set; }
    /// <summary>
    /// 合同风险
    /// </summary>
    public string? RiskOfContract { get; set; }
    /// <summary>
    /// 收益部门
    /// </summary>
    public string? BenifitDept { get; set; }
    /// <summary>
    /// 受益人
    /// </summary>
    public string? Benifit { get; set; }
    /// <summary>
    /// 营销部备案:1.是 2.否
    /// </summary>
    public string? RecordOfMarketingdepartment { get; set; }
    /// <summary>
    /// 法务审批
    /// </summary>
    public string? LealApproval { get; set; }
    /// <summary>
    /// 合同备案
    /// </summary>
    public string? BackupContract { get; set; }
    /// <summary>
    /// 总监或一级巴（独立巴）巴长审批
    /// </summary>
    public string? DirectorApproval { get; set; }
    /// <summary>
    /// 营销部审批
    /// </summary>
    public string? ApprovalOfMarketingdepartment { get; set; }
    /// <summary>
    /// 副总审批
    /// </summary>
    public string? DeputyApproval { get; set; }
    /// <summary>
    /// 销售阶段
    /// </summary>
    public string? SaleStatus { get; set; }
    /// <summary>
    /// 合同的dataId(方便作业排序)
    /// </summary>
    public string? DataId { get; set; }


    public Customer Customer { get; set; } = default!;

    public VEmployee SignerModel { get; set; } = default!;

    public List<ContractProductRelation> ContractProductRelation { get; set; } = default!;
}
