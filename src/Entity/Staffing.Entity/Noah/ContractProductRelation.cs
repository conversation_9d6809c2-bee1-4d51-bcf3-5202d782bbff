using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Noah;

/// <summary>
/// 合同产品关系表
/// </summary>
[Table("contract_product_relation")]
public partial class ContractProductRelation
{
    [Key]
    public long Id { get; set; }
    public long? ContractId { get; set; }
    public string? ProductId { get; set; }


    public Contract Contract { get; set; } = default!;
}