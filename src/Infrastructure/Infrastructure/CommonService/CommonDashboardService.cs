using Config;
using Config.CommonModel.DataScreen;
using Config.CommonModel.TalentResume;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Nest;
using Staffing.Entity;
using Staffing.Entity.Elasticsearch;
using Staffing.Entity.Staffing;

namespace Infrastructure.CommonService;

[Service(ServiceLifetime.Transient)]
public class CommonDashboardService
{
    private IDbContextFactory<StaffingContext> _staffingContextFactory;
    private ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private RequestContext _user;
    private readonly EsHelper _esHelper;

    public CommonDashboardService(IDbContextFactory<StaffingContext> staffingContextFactory, IOptionsSnapshot<ConfigManager> config,
        IHostEnvironment hostingEnvironment, LogManager log, RequestContext user, EsHelper esHelper)
    {
        _staffingContextFactory = staffingContextFactory;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
        _user = user;
        _esHelper = esHelper;
    }

    public GetTopologyResponse GetTopology(DateTime? beginTime, DateTime? endTime)
    {
        (beginTime, endTime) = Tools.AdjustTimeRange(beginTime, endTime, new DateTime(2022, 1, 1));

        var topology = new GetTopologyResponse();
        using var _context = _staffingContextFactory.CreateDbContext();

        //初始化esclient
        var esClient = _esHelper.GetClient();

        topology.OnlineProjectCount = _context.Project.Where(o => o.Status == Config.Enums.ProjectStatus.已上线
            && o.CreatedTime >= beginTime && o.CreatedTime < endTime).Count();

        topology.OpenProjectCount = _context.Project.Where(o => o.Status == Config.Enums.ProjectStatus.已上线
        && o.CreatedTime >= beginTime && o.CreatedTime < endTime).Count();

        topology.CloseProjectCount = _context.Project.Where(o => o.Status == Config.Enums.ProjectStatus.已上线
        && o.CreatedTime >= beginTime && o.CreatedTime < endTime).Count();

        //es查询EsTalentResume表的数量
        var searchResponse = esClient.Search<EsTalentResume>(s => s
            .Index(_esHelper.GetIndex().TalentResume)
            .Query(q => q
                .Bool(b => b
                    .MustNot(mu => mu
                        .Term(t => t.Status, 9)
                    )
                    .Must(mu => mu
                        .DateRange(r => r
                            .Field(f => f.RegistedTime)
                            .GreaterThanOrEquals(beginTime)
                            .LessThan(endTime)
                        )
                    )
                )
            )
            .TrackTotalHits(true)
            .Size(0)
        );
        topology.TalentCount = (int)searchResponse.Total;

        //由于es注册时间字段有问题，以上es查询改为数据库查询，手机号去重
        // topology.TalentCount = _context.Talent_Resume.Where(o => o.RegistedTime >= month && o.RegistedTime < nextMonth)
        // .Select(o => o.Mobile).Distinct().Count();

        // 目前只有诺快聘数据，诺优考和诺聘需要后续迭代
        var blNotIn = new List<string> { "操作工", "客服", "电焊工", "搬运工", "缝纫工", "保安", "司机", "教师", "医生", "护士" };
        var llIn = new List<string> { "操作工", "客服", "电焊工", "搬运工", "缝纫工", "保安", "司机" };

        var llPredicate = PredicateBuilder.New<Post_Delivery>(o => o.User_Seeker.CreatedTime >= beginTime && o.User_Seeker.CreatedTime < endTime);
        var blPredicate = PredicateBuilder.New<Post_Delivery>(o => o.User_Seeker.CreatedTime >= beginTime && o.User_Seeker.CreatedTime < endTime);
        var allPredicate = PredicateBuilder.New<User_Seeker>(o => o.CreatedTime >= beginTime && o.CreatedTime < endTime);

        foreach (var item in blNotIn)
            blPredicate = blPredicate.And(o => !o.Post.Name.Contains(item));

        var llPredicate2 = PredicateBuilder.New<Post_Delivery>(false);
        foreach (var item in llIn)
            llPredicate2 = llPredicate2.Or(o => o.Post.Name.Contains(item));

        llPredicate = llPredicate.And(llPredicate2);

        topology.KuaiPinTalentCount = _context.User_Seeker.Where(allPredicate).Select(s => s.UserId).Count();
        //topology.KuaiPinTalentCount = Getd(beginTime.Value, endTime.Value);


        topology.BlueCollarCount = _context.Post_Delivery.Where(llPredicate).Select(s => s.SeekerId).Distinct().Count();
        topology.WhiteCollarCount = _context.Post_Delivery.Where(blPredicate).Select(s => s.SeekerId).Distinct().Count();

        //topology.BlueCollarCount_CaoZuoGong = _context.Post_Delivery.Where(o => o.User_Seeker.CreatedTime >= beginTime && o.User_Seeker.CreatedTime < endTime && (o.Post.Name.Contains("操作工") || o.Post.Name.Contains("操作员"))).Select(s => s.SeekerId).Distinct().Count();
        topology.BlueCollarCount_CaoZuoGong = _context.Post_Delivery.Where(o => o.User_Seeker.CreatedTime >= beginTime && o.User_Seeker.CreatedTime < endTime
                && o.Post.Dic_Post.Level.StartsWith("928.1006.")).Select(s => s.SeekerId).Distinct().Count();
        topology.BlueCollarCount_KeFu = _context.Post_Delivery.Where(o => o.User_Seeker.CreatedTime >= beginTime && o.User_Seeker.CreatedTime < endTime && o.Post.Name.Contains("客服")).Select(s => s.SeekerId).Distinct().Count();
        topology.BlueCollarCount_SiJi = _context.Post_Delivery.Where(o => o.User_Seeker.CreatedTime >= beginTime && o.User_Seeker.CreatedTime < endTime && o.Post.Name.Contains("司机")).Select(s => s.SeekerId).Distinct().Count();
        topology.BlueCollarCount_BaoAn = _context.Post_Delivery.Where(o => o.User_Seeker.CreatedTime >= beginTime && o.User_Seeker.CreatedTime < endTime && o.Post.Name.Contains("保安")).Select(s => s.SeekerId).Distinct().Count();
        topology.BlueCollarCount_BaoJie = _context.Post_Delivery.Where(o => o.User_Seeker.CreatedTime >= beginTime && o.User_Seeker.CreatedTime < endTime && (o.Post.Name.Contains("保洁") || o.Post.Name.Contains("家政"))).Select(s => s.SeekerId).Distinct().Count();
        topology.HighTalentsCount_BoShi = _context.User_Seeker.Where(o => o.CreatedTime >= beginTime && o.CreatedTime < endTime && o.User_Resume.Education == EducationType.博士).Count();
        topology.HighTalentsCount_YanJiuSheng = _context.User_Seeker.Where(o => o.CreatedTime >= beginTime && o.CreatedTime < endTime && o.User_Resume.Education == EducationType.硕士).Count();

        topology.WihteCollarCount_XiaoShou = _context.Post_Delivery.Where(o => o.User_Seeker.CreatedTime >= beginTime && o.User_Seeker.CreatedTime < endTime
                        &&o.Post.Dic_Post.Level.StartsWith("384.")).Select(s => s.SeekerId).Distinct().Count();//一级分类：销售
        topology.WihteCollarCount_IT = _context.Post_Delivery.Where(o => o.User_Seeker.CreatedTime >= beginTime && o.User_Seeker.CreatedTime < endTime
                && o.Post.Dic_Post.Level.StartsWith("12.")).Select(s => s.SeekerId).Distinct().Count();//一级分类：技术
        topology.WihteCollarCount_YunYing = _context.Post_Delivery.Where(o => o.User_Seeker.CreatedTime >= beginTime && o.User_Seeker.CreatedTime < endTime
                && o.Post.Dic_Post.Level.StartsWith("229.")).Select(s => s.SeekerId).Distinct().Count();//一级分类：运营

        //去年1月1日
        var lastYear = new DateOnly(DateTime.Now.AddYears(-1).Year, 1, 1);

        //毕业时间大于等于这个时间的算毕业生
        topology.StudentCount = _context.User_Seeker.Where(o => o.CreatedTime >= beginTime && o.CreatedTime < endTime && o.User_Resume.GraduationDate >= lastYear).Count();

        //高端人才【博士、研究生学历的】
        topology.HighTalentsCount = _context.User_Seeker.Where(o => o.CreatedTime >= beginTime && o.CreatedTime < endTime && (o.User_Resume.Education == EducationType.硕士 || o.User_Resume.Education == EducationType.博士)).Count();
        return topology;
    }

    private int Getd(DateTime BeginTime, DateTime EndTime)
    {
        using var _context = _staffingContextFactory.CreateDbContext();
        string sql = $@"
                SELECT COUNT(s.UserId) as UCount FROM user_seeker s
                WHERE s.CreatedTime  between '{BeginTime.ToString("yyyy-MM-dd")}'  and '{EndTime.ToString("yyyy-MM-dd 23:59:59")}';";
        var userCount = _context.SqlQueryDynamic(sql).FirstOrDefault();
        if (userCount != null && userCount!.UCount is long)
            return userCount!.UCount;
        else
            return 0;
    }

    public GetTopologyTalentResponse GetTopologyTalent(DateTime? beginTime, DateTime? endTime)
    {
        (beginTime, endTime) = Tools.AdjustTimeRange(beginTime, endTime, new DateTime(2022, 1, 1));

        var talent = new GetTopologyTalentResponse();
        using var _context = _staffingContextFactory.CreateDbContext();

        //初始化esclient
        var esClient = _esHelper.GetClient();

        //es查询EsTalentResume，根据source统计数量，Status!=9的
        var searchResponseTalent = esClient.Search<EsTalentResume>(s => s
            .Index(_esHelper.GetIndex().TalentResume)
            .Query(q => q
                .Bool(b => b
                    .MustNot(mu => mu
                        .Term(t => t.Status, 9)
                    )
                    .Must(mu => mu
                        .DateRange(r => r
                            .Field(f => f.RegistedTime)
                            .GreaterThanOrEquals(beginTime)
                            .LessThan(endTime)
                        )
                    )
                )
            )
            .Size(0)
            .Aggregations(a => a
                .Terms("source", t => t
                    .Field(f => f.Source)
                    .Size(1000)
                )
            )
        );
        foreach (var item in searchResponseTalent.Aggregations.Terms("source").Buckets)
        {
            if (int.TryParse(item.Key, out int ts))
            {
                var source = (TalentResumeSource)ts;
                //只统计诺聘,诺优考,快招工
                if (source == TalentResumeSource.诺聘 || source == TalentResumeSource.诺优考 || source == TalentResumeSource.快招工)
                {
                    talent.TalentPoolDrainage.Add(new NameCount
                    {
                        Name = source.GetDescription(),
                        Value = (int)(item.DocCount ?? 0)
                    });
                }
            }
        }

        talent.NewTalentClue = talent.TalentPoolDrainage.Sum(s => s.Value);
        talent.RecommendProjectCount = _context.Recruit.Where(o => o.CreatedTime >= beginTime && o.CreatedTime < endTime).Count();

        var categoryQuerys = new List<Func<QueryContainerDescriptor<EsTalentResume>, QueryContainer>>();
        // 时间范围内发布的职位的三级分类
        var categoryInfo = _context.Post.Where(o => o.CreatedTime >= beginTime && o.CreatedTime < endTime)
        .GroupBy(g => g.Category)
        .Select(s => new
        {
            CategoryLevel3 = s.Key.ToString(),
            CategoryLevel3Name = s.Max(m => m.Dic_Post.Name),
            Total = s.Sum(c => c.DeliveryNumber)
        }).AsEnumerable().Where(x => !string.IsNullOrEmpty(x.CategoryLevel3Name)).ToList();

        foreach (var item in categoryInfo)
        {
            categoryQuerys.Add(q => q.Match(t => t.Field(f => f.Hopes!.First().CategoryLevel3Name).Query(item.CategoryLevel3Name)));

            // 兼容诺优考
            categoryQuerys.Add(q => q.Match(t => t.Field(f => f.Hopes!.First().PositionLevel1Name).Query($"*{item.CategoryLevel3Name}*")));
        }
        categoryQuerys = categoryQuerys.Take(200).ToList();
        //es查询EsTalentResume，时间范围内，Status!=9的，并且满足mustQuerys中任意一个条件
        var searchResponse = esClient.Search<EsTalentResume>(s => s
            .Index(_esHelper.GetIndex().TalentResume)
            .Query(q => q
                .Bool(b => b
                    .MustNot(mu => mu
                        .Term(t => t.Status, 9)
                    )
                    .Must(mu => mu
                        .DateRange(r => r
                            .Field(f => f.RegistedTime)
                            .GreaterThanOrEquals(beginTime)
                            .LessThan(endTime)
                        )
                    )
                    .Must(q => q.Nested(n => n.Path(x => x.Hopes).Query(q1 => q1.Bool(b => b.Should(categoryQuerys))))
                ))
            )
            .TrackTotalHits()
            .Size(0)
        );

        //满足职位类别的人才数量
        var categoryCount = (int)searchResponse.Total;

        //职位招聘人数
        var postRecruitNumber = categoryInfo.Sum(s => s.Total);

        //talent.TalentSupplyRate = postRecruitNumber / categoryCount
        talent.TalentSupplyRate = postRecruitNumber == 0 ? 1 : categoryCount / (decimal)postRecruitNumber;

        //保留两位小数
        talent.TalentSupplyRate = Math.Round(talent.TalentSupplyRate, 2);

        // 入职数量排名
        // var entryTop = _context.Recruit_Record.Where(o => o.CreatedTime >= beginTime && o.CreatedTime < endTime && o.Status == RecruitStatus.Induction)
        // .GroupBy(o => o.Recruit.Post_Delivery.Post.ProjectId)
        // .Select(s => new
        // {
        //     ProjectName = s.Max(m => m.Recruit.Post_Delivery.Post.Project.Name),
        //     Count = s.Count()
        // }).Where(x => !string.IsNullOrEmpty(x.ProjectName)).OrderByDescending(o => o.Count).ToList();

        var entryTop = _context.Recruit_Record
        .Where(o => o.CreatedTime >= beginTime && o.CreatedTime < endTime && o.Status == RecruitStatus.Induction)
        .Select(o => new { o.Recruit.Post_Delivery.Post.ProjectId, o.Recruit.Post_Delivery.Post.Project.Agent_Ent.Name })
        .AsEnumerable()
        .GroupBy(o => o.ProjectId)
        .Select(s => new
        {
            ProjectName = s.Max(m => m.Name),
            Count = s.Count()
        })
        .Where(x => !string.IsNullOrEmpty(x.ProjectName))
        .OrderByDescending(o => o.Count)
        .ToList();

        talent.EntryProjectRanking.AddRange(entryTop.Select(s => new NameCount
        {
            Name = s.ProjectName,
            Value = s.Count
        }));

        //人才线索池使用情况
        var clue = from a in _context.Talent_Resume_Connect.Where(o => o.Type == ConnectType.拨打电话
        && o.CreatedTime >= beginTime && o.CreatedTime < endTime).GroupBy(g => new { g.HrId, g.ResumeId }).Select(s => new { s.Key.HrId, s.Key.ResumeId })
                   join b in _context.User_Hr on a.HrId equals b.UserId
                   join c in _context.User on b.UserId equals c.UserId
                   join d in _context.Dd_User on c.Mobile equals d.Mobile
                   join e in _context.Dd_User_Dept on d.DdUserId equals e.DdUserId
                   join f in _context.Dd_Dept on e.DdDeptId equals f.DdDeptId
                   group f by f.DdDeptId into g
                   orderby g.Count() descending
                   select new
                   {
                       DeptName = g.Max(m => m.Name),
                       Count = g.Count()
                   };

        talent.TalentPoolUsage = clue.Select(s => new NameCount
        {
            Name = s.DeptName,
            Value = s.Count
        }).ToList();

        talent.KuaiPinNewTalentCount = _context.User_Seeker.Where(o => o.CreatedTime >= beginTime && o.CreatedTime < endTime).Count();
        talent.NewTalentDeliveryCount = _context.Post_Delivery.Where(o => o.User_Seeker.CreatedTime >= beginTime && o.User_Seeker.CreatedTime < endTime).Select(s => s.SeekerId).Distinct().Count();
        talent.NewTalentInterviewCount = _context.Recruit_Record.Where(o => o.Recruit.Post_Delivery.User_Seeker.CreatedTime >= beginTime && o.Recruit.Post_Delivery.User_Seeker.CreatedTime < endTime && o.Status == RecruitStatus.Interview).Select(s => s.Recruit.SeekerId).Distinct().Count();
        talent.NewTalentEntryCount = _context.Recruit.Where(o => o.Post_Delivery.User_Seeker.CreatedTime >= beginTime && o.Post_Delivery.User_Seeker.CreatedTime < endTime && o.Status == RecruitStatus.Induction).Select(s => s.SeekerId).Distinct().Count();

        return talent;
    }

    public GetTopologyProjectResponse GetTopologyProject(DateTime? beginTime, DateTime? endTime)
    {
        (beginTime, endTime) = Tools.AdjustTimeRange(beginTime, endTime, new DateTime(2022, 1, 1));

        var project = new GetTopologyProjectResponse();
        using var _context = _staffingContextFactory.CreateDbContext();

        // 查询该阶段所有项目的职位招聘人数
        var recruitNumber = _context.Post.Where(o => o.Project.CreatedTime >= beginTime && o.Project.CreatedTime < endTime)
        .GroupBy(g => g.ProjectId)
        .Select(s => new
        {
            ProjectId = s.Key,
            RecruitNumber = s.Sum(c => c.DeliveryNumber)
        }).AsEnumerable().Where(x => x.RecruitNumber > 0).ToList();

        // 查询该阶段所有项目的入职人数
        var entryCount = _context.Recruit_Record.Where(o => o.Recruit.Post_Delivery.Post.Project.CreatedTime >= beginTime && o.Recruit.Post_Delivery.Post.Project.CreatedTime < endTime && o.Status == RecruitStatus.Induction)
        .GroupBy(g => g.Recruit.Post_Delivery.Post.ProjectId)
        .Select(s => new
        {
            ProjectId = s.Key,
            Count = s.Count()
        }).AsEnumerable().Where(x => x.Count > 0).ToList();

        // 查询该阶段所有职位的投递人数
        var deliveryCount = _context.Post_Delivery.Where(o => o.Post.CreatedTime >= beginTime && o.Post.CreatedTime < endTime)
            .Count();

        // 查询该阶段所有职位的招聘人数
        var postRecruitNumber = _context.Post.Where(o => o.CreatedTime >= beginTime && o.CreatedTime < endTime)
            .Sum(s => s.DeliveryNumber);

        // recruitNumber和entryCount通过ProjectId匹配项目完成度，再求平均值
        project.ProjectCompletion = recruitNumber.Join(entryCount, o => o.ProjectId, i => i.ProjectId, (o, i) => new
        {
            o.ProjectId,
            Completion = i.Count > o.RecruitNumber ? 1 : (i.Count / (decimal)o.RecruitNumber)
        }).Average(a => (decimal?)a.Completion) ?? 0;

        project.DeliveryCount = deliveryCount;
        project.PostRecruitNumber = postRecruitNumber;
        project.PositionMatchingRate = postRecruitNumber == 0 ? 1 : (decimal)deliveryCount / postRecruitNumber;

        //保留两位小数
        project.ProjectCompletion = Math.Round(project.ProjectCompletion, 2);
        project.PositionMatchingRate = Math.Round(project.PositionMatchingRate, 2);

        //不能大于1
        if (project.ProjectCompletion > 1)
            project.ProjectCompletion = 1;

        // if (project.PositionMatchingRate > 1)
        //     project.PositionMatchingRate = 1;

        project.ReleaseProjectCount = _context.Project.Where(o => o.CreatedTime >= beginTime && o.CreatedTime < endTime).Count();
        project.ReleasePositionCount = _context.Post.Where(o => o.CreatedTime >= beginTime && o.CreatedTime < endTime).Count();

        // 该阶段项目协同人数
        var teamProject = _context.Project_Team.Where(o => o.Project.CreatedTime >= beginTime && o.Project.CreatedTime < endTime).Count();

        //统计报名人数
        var applyCount = _context.Recruit.Where(o => o.Post_Delivery.Post.Project.CreatedTime >= beginTime && o.Post_Delivery.Post.Project.CreatedTime < endTime).Count();

        //统计面试、入职、归档数量
        var groupCount = _context.Recruit_Record.Where(o => o.Recruit.Post_Delivery.Post.Project.CreatedTime >= beginTime && o.Recruit.Post_Delivery.Post.Project.CreatedTime < endTime)
        .GroupBy(g => g.Status).Select(s => new
        {
            Status = s.Key,
            Count = s.Count()
        }).ToList();

        project.ProjectStageProgress.Add(new NameCount
        {
            Name = "协同人数",
            Value = teamProject
        });

        project.ProjectStageProgress.Add(new NameCount
        {
            Name = "推荐人数",
            Value = applyCount
        });

        project.ProjectStageProgress.Add(new NameCount
        {
            Name = "面试人数",
            Value = groupCount.FirstOrDefault(x => x.Status == RecruitStatus.Interview)?.Count ?? 0
        });

        project.ProjectStageProgress.Add(new NameCount
        {
            Name = "入职人数",
            Value = groupCount.FirstOrDefault(x => x.Status == RecruitStatus.Induction)?.Count ?? 0
        });

        project.ProjectStageProgress.Add(new NameCount
        {
            Name = "归档人数",
            Value = groupCount.FirstOrDefault(x => x.Status == RecruitStatus.FileAway)?.Count ?? 0
        });

        //部门协同参与人数
        var deptTeam = from a in _context.Project_Team.Where(o => o.Project.CreatedTime >= beginTime && o.Project.CreatedTime < endTime)
                       join b in _context.User on a.HrId equals b.UserId
                       join c in _context.Dd_User on b.Mobile equals c.Mobile
                       join d in _context.Dd_User_Dept on c.DdUserId equals d.DdUserId
                       join e in _context.Dd_Dept on d.DdDeptId equals e.DdDeptId
                       group e by e.DdDeptId into g
                       orderby g.Count() descending
                       select new
                       {
                           DeptName = g.Max(m => m.Name),
                           Count = g.Count()
                       };

        project.DepartmentCollaboration = deptTeam.Select(s => new NameCount
        {
            Name = s.DeptName,
            Value = s.Count
        }).ToList();

        return project;
    }
}