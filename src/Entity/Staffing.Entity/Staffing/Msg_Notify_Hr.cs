﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.CommonModel.Business;

namespace Staffing.Entity.Staffing;

/// <summary>
/// Hr站内消息
/// </summary>
[Table("msg_notify_hr")]
public class Msg_Notify_Hr
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 用户Id
    /// </summary>
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 类型
    /// </summary>
    public HrMsgType Type { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; } = default!;

    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; } = default!;

    /// <summary>
    /// 数据json
    /// </summary>
    public string? Data { get; set; }

    public DateTime CreatedTime { get; set; } = DateTime.Now;
}