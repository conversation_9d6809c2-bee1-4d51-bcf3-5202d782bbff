﻿using Config.Enums;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces.Hr;
using Config;
using Infrastructure.Proxy;
using Infrastructure.CommonService;
using Staffing.Model.Hr.Kzg;
using ServiceStack.Text;
using Infrastructure.Aop;
using Config.CommonModel.KuaiShou;
using Config.CommonModel.Business;
using Config.CommonModel.DingDingRoot;

namespace Staffing.Core.Services.Hr;

[Service(ServiceLifetime.Transient)]
public class KzgService : IKzgService
{
    private readonly StaffingContext _context;
    private readonly RequestContext _user;
    private readonly ConfigManager _config;
    private readonly CacheHelper _cacheHelper;
    private readonly ICommonService _commonService;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CommonDicService _commonDicService;
    private readonly KuaiShouHelper _kuaiShouHelper;
    private readonly NuoPinApi _nuoPinApi;
    private readonly DingDingRootHelper _dingDingRootHelper;
    private readonly CommonCacheService _commonCacheService;
    public KzgService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, ICommonService commonService, NuoPinApi nuoPinApi,
        LogManager log, CommonDicService commonDicService, KuaiShouHelper kuaiShouHelper,
        DingDingRootHelper dingDingRootHelper, CommonCacheService commonCacheService)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _commonService = commonService;
        _log = log;
        _commonDicService = commonDicService;
        _kuaiShouHelper = kuaiShouHelper;
        _nuoPinApi = nuoPinApi;
        _dingDingRootHelper = dingDingRootHelper;
        _commonCacheService = commonCacheService;
    }

    [RedisCache(Expire = 36000)]
    public async Task<GetKzgTreeResponse> GetKzgPost(GetKzgTree model)
    {
        var result = new GetKzgTreeResponse();
        var ksModel = await _kuaiShouHelper.GetPostCategory();
        result.Rows = ksModel;
        return result;
    }

    [RedisCache(Expire = 36000)]
    public async Task<GetKzgTreeResponse> GetKzgTag(GetKzgTree model)
    {
        var result = new GetKzgTreeResponse { Rows = new List<GetKsDicModel>() };
        var ksModel = await _kuaiShouHelper.GetPostTag();

        foreach (var item in ksModel)
        {
            if (item.children != null)
                result.Rows.AddRange(item.children);
        }
        return result;
    }

    [RedisCache(Expire = 36000)]
    public async Task<GetKzgRegionResponse> GetKzgRegion(GetKzgTree model)
    {
        var result = new GetKzgRegionResponse();
        var ksModel = await _kuaiShouHelper.GetRegion();

        foreach (var item in ksModel)
        {
            var province = new GeneralRecursionDic
            {
                Children = new List<GeneralRecursionDic>(),
                Id = item.provinceCode!,
                Name = item.provinceName!
            };

            foreach (var item2 in item.cityList ?? new List<GetKsCity>())
            {
                var city = new GeneralRecursionDic
                {
                    Children = new List<GeneralRecursionDic>(),
                    Id = item2.cityCode!,
                    Name = item2.cityName!
                };
                foreach (var item3 in item2.areaList ?? new List<GetKsArea>())
                {
                    var area = new GeneralRecursionDic
                    {
                        Children = null,
                        Id = item3.areaCode!,
                        Name = item3.areaName!
                    };
                    city.Children.Add(area);
                }
                province.Children.Add(city);
            }
            result.Rows.Add(province);
        }
        return result;
    }

    public GetXbbHtResponse GetXbbHt(GetXbbHt model)
    {
        var result = new GetXbbHtResponse();

        var isAdmin = _user.Powers?.Contains(Power.NkpAdmin) == true;
        if (!isAdmin)
            return new GetXbbHtResponse();

        var cp = new List<CommonDicModel>
        {
            new CommonDicModel { Id = "2201.01", Name = "业务外包" },
            new CommonDicModel { Id = "2201.02", Name = "短期用工" },
            new CommonDicModel { Id = "2201.03", Name = "劳务派遣" },
            new CommonDicModel { Id = "2204.02", Name = "委托招聘" },
            new CommonDicModel { Id = "2204.03", Name = "诺优考SaaS平台租赁" },
            new CommonDicModel { Id = "2208.01", Name = "诺优考SaaS运维服务" },
            new CommonDicModel { Id = "2208.03", Name = "诺灵工SaaS服务" },
            new CommonDicModel { Id = "2208.04", Name = "诺聘SaaS服务" },
            new CommonDicModel { Id = "2208.05", Name = "诺聘平台会员服务" },
            new CommonDicModel { Id = "2208.06", Name = "互联网宣传服务" },
            new CommonDicModel { Id = "2207.01", Name = "猎头" },
            new CommonDicModel { Id = "2101.01", Name = "招聘流程外包" },
            new CommonDicModel { Id = "2101.02", Name = "委托招聘" },
            new CommonDicModel { Id = "2101.04", Name = "诺聘平台会员服务" },
            new CommonDicModel { Id = "2101.05", Name = "诺聘平台宣传服务" },
            new CommonDicModel { Id = "2102.02", Name = "业务外包" },
            new CommonDicModel { Id = "2104.01", Name = "猎头" },
            new CommonDicModel { Id = "2001.04", Name = "招聘流程外包" },
            new CommonDicModel { Id = "2001.05", Name = "招聘平台租赁" },
            new CommonDicModel { Id = "2001.06", Name = "委托招聘" },
            new CommonDicModel { Id = "2001.07", Name = "诺招聘平台会员服务（企业会员、求职会员）" },
            new CommonDicModel { Id = "2001.08", Name = "诺招聘平台宣传服务（公众号、广告位）" },
            new CommonDicModel { Id = "2003.02", Name = "业务外包_政府公共服务外包" },
            new CommonDicModel { Id = "2003.03", Name = "业务外包_金融行业服务外包" },
            new CommonDicModel { Id = "2003.04", Name = "业务外包_蓝领服务外包" },
            new CommonDicModel { Id = "2003.05", Name = "业务外包_零工服务外包" },
            new CommonDicModel { Id = "2003.06", Name = "业务外包_通用型岗位服务外包" },
            new CommonDicModel { Id = "2003.07", Name = "业务外包_岗位外包其他" },
            new CommonDicModel { Id = "1902.1", Name = "非全日制用工" },
            new CommonDicModel { Id = "1902.06", Name = "岗位外包" },
            new CommonDicModel { Id = "1911.01", Name = "猎头" },
            new CommonDicModel { Id = "1901.01", Name = "员工关系外包" },
            new CommonDicModel { Id = "1901.02", Name = "员工关系外包（内）" },
            new CommonDicModel { Id = "1902.01", Name = "岗位外包焊工类" },
            new CommonDicModel { Id = "1902.02", Name = "岗位外包行政文秘类" },
            new CommonDicModel { Id = "1902.03", Name = "岗位外包呼叫中心类" },
            new CommonDicModel { Id = "1902.04", Name = "岗位外包促销导购类" },
            new CommonDicModel { Id = "1902.05", Name = "岗位外包其它" },
            new CommonDicModel { Id = "1902.08", Name = "生产流程外包" },
            new CommonDicModel { Id = "1902.09", Name = "代理代维" },
            new CommonDicModel { Id = "1903.01", Name = "保险外包" },
            new CommonDicModel { Id = "1904.01", Name = "委托招聘" },
            new CommonDicModel { Id = "1904.02", Name = "考务外包" },
            new CommonDicModel { Id = "1904.03", Name = "考务外包（内）" },
            new CommonDicModel { Id = "1904.04", Name = "招聘流程外包" },
            new CommonDicModel { Id = "1904.05", Name = "招聘流程外包（内）" },
            new CommonDicModel { Id = "2302.01", Name = "招聘流程外包" },
            new CommonDicModel { Id = "2302.02", Name = "委托招聘" },
            new CommonDicModel { Id = "2305.01", Name = "猎头" },
            new CommonDicModel { Id = "2306.01", Name = "业务外包" },
            new CommonDicModel { Id = "2306.02", Name = "业务外包（老）" },
            new CommonDicModel { Id = "2306.03", Name = "劳务派遣" },
            new CommonDicModel { Id = "2306.04", Name = "劳务派遣（老）" },
            new CommonDicModel { Id = "2307.01", Name = "诺优考SaaS平台租赁" },
            new CommonDicModel { Id = "2307.02", Name = "诺优考SaaS运维服务" },
            new CommonDicModel { Id = "2307.03", Name = "诺聘SaaS服务" },
            new CommonDicModel { Id = "2307.04", Name = "互联网宣传服务" },
            new CommonDicModel { Id = "2306.06", Name = "招聘外包" },
            new CommonDicModel { Id = "2307.05", Name = "人才速聘" }
        };

        cp.AddRange(new List<CommonDicModel>
        {
            new CommonDicModel { Id = "2402.01", Name = "2402.01" },
            new CommonDicModel { Id = "2405.01", Name = "2405.01" },
            new CommonDicModel { Id = "2406.01", Name = "2406.01" },
            new CommonDicModel { Id = "2406.02", Name = "2406.02" },
            new CommonDicModel { Id = "2406.03", Name = "2406.03" },
            new CommonDicModel { Id = "2406.04", Name = "2406.04" },
            new CommonDicModel { Id = "2406.05", Name = "2406.05" },
            new CommonDicModel { Id = "2406.06", Name = "2406.06" },
            new CommonDicModel { Id = "2406.07", Name = "2406.07" },
            new CommonDicModel { Id = "2406.08", Name = "2406.08" },
            new CommonDicModel { Id = "2406.09", Name = "2406.09" },
            new CommonDicModel { Id = "2407.01", Name = "2407.01" },
            new CommonDicModel { Id = "2407.02", Name = "2407.02" },
            new CommonDicModel { Id = "2407.04", Name = "2407.04" }
        });

        var cpIds = cp.Select(s => s.Id).ToList();

        var predicate = PredicateBuilder.New<Xbb_Contract>(x => cpIds.Contains(x.ProductId));

        if (!string.IsNullOrEmpty(model.Category))
            predicate = predicate.And(x => x.Category == model.Category);

        if (!string.IsNullOrEmpty(model.ProductId))
            predicate = predicate.And(x => x.ProductId == model.ProductId);

        if (!string.IsNullOrEmpty(model.ProductName))
            predicate = predicate.And(x => x.ProductName.Contains(model.ProductName));

        if (!string.IsNullOrEmpty(model.Nature))
            predicate = predicate.And(x => x.Nature == model.Nature);

        if (!string.IsNullOrEmpty(model.Status))
            predicate = predicate.And(x => x.Status == model.Status);

        if (model.BindNkp == true)
            predicate = predicate.And(x => _context.Project.Any(y => y.XbbContractNo == x.Code));
        else if (model.BindNkp == false)
            predicate = predicate.And(x => !_context.Project.Any(y => y.XbbContractNo == x.Code));

        if (!string.IsNullOrEmpty(model.Name))
            predicate = predicate.And(x => x.Name.Contains(model.Name));

        if (!string.IsNullOrEmpty(model.Code))
            predicate = predicate.And(x => x.Code.Contains(model.Code));

        if (!string.IsNullOrEmpty(model.CustomerName))
            predicate = predicate.And(x => x.CustomerName.Contains(model.CustomerName));

        if (!string.IsNullOrEmpty(model.Signatory))
            predicate = predicate.And(x => x.Signatory.Contains(model.Signatory));

        if (model.BeginTime.HasValue)
            predicate = predicate.And(x => x.NoahCreatedTime >= model.BeginTime.Value);

        if (model.EndTime.HasValue)
        {
            model.EndTime = model.EndTime.Value.Date.AddDays(1);
            predicate = predicate.And(x => x.NoahCreatedTime < model.EndTime.Value);
        }

        if (model.NkpStatus.HasValue)
            predicate = predicate.And(x => x.NkpStatus == model.NkpStatus);

        var sql = _context.Xbb_Contract.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetXbbHtInfo
        {
            Name = s.Name,
            Status = s.Status,
            BeginTime = s.BeginTime,
            BindNkp = _context.Project.Any(y => y.NuoId == s.NoahCode),
            Category = s.Category,
            Code = s.Code,
            ContractUrl = s.ContractUrl ?? new List<string>(),
            CustomerName = s.CustomerName,
            EndTime = s.EndTime,
            Id = s.Id,
            Nature = s.Nature,
            NkpStatus = s.NkpStatus,
            NkpStatusText = s.NkpStatus.GetDescription(),
            Signatory = s.Signatory,
            NoahUpdatedTime = s.NoahUpdatedTime,
            NoahCreatedTime = s.NoahCreatedTime,
            ProductId = s.ProductId,
            ProductName = s.ProductName
        }).ToList();

        foreach (var item in result.Rows)
        {
            item.StatusText = NoahTools.GetNoahStatus(item.Status);
            item.CategoryText = NoahTools.GetNoahCategory(item.Category);
            item.NatureText = NoahTools.GetNoahNature(item.Nature);
        }

        return result;
    }

    public EmptyResponse SetXbbHt(SetXbbHt model)
    {
        var isAdmin = _user.Powers?.Contains(Power.NkpAdmin) == true;
        if (!isAdmin)
            throw new ForbiddenException("无权限");

        var contract = _context.Xbb_Contract.Where(x => model.Ids.Contains(x.Id)).ToList();

        foreach (var item in contract)
        {
            item.NkpStatus = model.NkpStatus;
        }

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public async Task<EmptyResponse> XbbHtNotify(XbbHtNotify model)
    {
        var isAdmin = _user.Powers?.Contains(Power.NkpAdmin) == true;
        if (!isAdmin)
            throw new ForbiddenException("无权限");

        var lockerKey = "st:XbbHtNotify";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var contract = _context.Xbb_Contract.Where(x => model.Ids.Contains(x.Id)).ToList();

        var emcodes = contract.Select(s => s.SignerEmployeeCode).Distinct().ToList();

        if (emcodes.Any(x => string.IsNullOrWhiteSpace(x)))
            throw new BadRequestException("签约人工号不存在");

        var ddUserIds = _context.Dd_User.Where(x => emcodes.Contains(x.JobNumber))
        .Select(s => new { s.DdUserId, s.JobNumber }).ToList();

        var notExists = contract.ExceptBy(ddUserIds.Select(s => s.JobNumber), x => x.SignerEmployeeCode);

        if (notExists.Count() > 0)
        {
            var signatorys = string.Join(',', contract.Select(s => s.Signatory));
            throw new BadRequestException($"{signatorys}、工号未在钉钉找到");
        }

        foreach (var item in contract)
        {
            if (item.SendNotify == true)
                continue;

            var messageTemplate = $"诺快聘平台发现您已经在{item.SignDate!.Value:yyy年MM月dd日}与一家公司签订了一份名为{item.Name}的合同，如该合同涉及招聘需求，请您在三个工作日内将项目上架至诺快聘平台，以便我们为您提供更好的服务。谢谢！";

            //发送钉钉通知
            var notify = new SendDingDingRootMessageRequest()
            {
                userId = ddUserIds.First(x => x.JobNumber == item.SignerEmployeeCode).DdUserId,
                msgParam = JsonSerializer.SerializeToString(new { content = messageTemplate })
            };
            await _dingDingRootHelper.SendDingDingRootMessage(notify);

            item.SendNotify = true;
        }
        _context.SaveChanges();

        return new EmptyResponse();
    }

    public GetKzgUserResponse GetUser()
    {
        var result = new GetKzgUserResponse();

        var kzgUserToken = _context.User_OpenId.Where(x => x.UserId == _user.Id
        && x.Type == ClientType.Kzg).FirstOrDefault();

        var state = new KuaiShouEncryptModel
        {
            Key = Constants.KuaiShouEncryptKey,
            UserId = _user.Id
        };

        if (kzgUserToken != null)
            result.BindUser = true;
        else
        {
            //生成密钥，验证回调
            var key = Guid.NewGuid().ToString();
            MyRedis.Client.Set($"{RedisKey.KuaiShou.KuaiShouCallbackKey}{key}", state, TimeSpan.FromMinutes(10));
            result.BindUserUrl = _kuaiShouHelper.GetBindUserUrl(key);
        }

        var kzgTenantToken = _context.Kuaishou_Tenant.FirstOrDefault();

        if (kzgTenantToken != null)
            result.BindTenant = true;
        else
        {
            //生成密钥，验证回调
            var key = Guid.NewGuid().ToString();
            MyRedis.Client.Set($"{RedisKey.KuaiShou.KuaiShouCallbackKey}{key}", state, TimeSpan.FromMinutes(10));
            result.BindTenantUrl = _kuaiShouHelper.GetBindTenantUrl(key);
        }

        return result;
    }

    public async Task<SendPostToKzgResponse> SendPostToKzg(SendPostToKzg model)
    {
        if (string.IsNullOrWhiteSpace(model.name))
            throw new BadRequestException("缺少职位名称");

        if (string.IsNullOrWhiteSpace(model.category?.code) || string.IsNullOrWhiteSpace(model.category?.typeCode))
            throw new BadRequestException("缺少职位类别");

        if (string.IsNullOrWhiteSpace(model.location?.areaCode) || string.IsNullOrWhiteSpace(model.location?.cityCode)
        || string.IsNullOrWhiteSpace(model.location?.provinceCode) || string.IsNullOrWhiteSpace(model.location?.address))
            throw new BadRequestException("缺少职位地址");

        if (string.IsNullOrWhiteSpace(model.description))
            throw new BadRequestException("缺少职位描述");

        if (string.IsNullOrWhiteSpace(model.company?.introduction))
            throw new BadRequestException("缺少企业描述");

        if (string.IsNullOrWhiteSpace(model.company?.businessCode))
            throw new BadRequestException("缺少企业工商code");

        if (string.IsNullOrWhiteSpace(model.company?.businessName))
            throw new BadRequestException("缺少企业工商注册名");

        if (string.IsNullOrWhiteSpace(model.company?.scale))
            throw new BadRequestException("缺少企业规模");

        if (string.IsNullOrWhiteSpace(model.company.location?.areaCode) || string.IsNullOrWhiteSpace(model.company.location?.cityCode)
        || string.IsNullOrWhiteSpace(model.company.location?.provinceCode) || string.IsNullOrWhiteSpace(model.company.location?.address))
            throw new BadRequestException("缺少公司地址");

        var result = new SendPostToKzgResponse();

        var lockerKey = $"SendPostToKzg:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var post = _context.Post_Team.Where(x => x.TeamPostId == model.TeamPostId && x.Project_Team.HrId == _user.Id)
        .Select(s => s.Post)
        .FirstOrDefault();

        if (post == null)
            throw new BadRequestException("职位不存在");

        if (post.MaxSalary > post.MinSalary * 3)
            throw new BadRequestException("最大薪资不能高于最低薪资3倍");
        // 职位名称，描述字数限制
        if (post.Name.Length > 10 || post.Name.Length < 2)
            post.Name = GetStandardName(post.Name);
        if (model.description?.Length > 500)
            //post.Describe = post.Describe[0..500];
            throw new BadRequestException("职位描述超过500个字符");

        var thirdJobidRel = _context.Post_Team_Third_Jobid_Rel.Where(x => x.TeamPostId == model.TeamPostId
        && x.PlatType == ThirdPlatType.快招工).FirstOrDefault();

        if (thirdJobidRel != null)
            throw new BadRequestException("该职位已绑定快招工");

        var openId = _context.User_OpenId.Where(x => x.UserId == _user.Id && x.Type == ClientType.Kzg).FirstOrDefault()?.OpenId;
        if (string.IsNullOrWhiteSpace(openId))
            throw new BadRequestException("请先绑定快招工账号");

        // if (string.IsNullOrWhiteSpace(openId))
        // {
        //     var state = new KuaiShouEncryptModel
        //     {
        //         Key = Constants.KuaiShouEncryptKey,
        //         UserId = _user.Id
        //     };

        //     //生成密钥，验证回调
        //     var key = Guid.NewGuid().ToString();
        //     MyRedis.Client.Set($"{RedisKey.KuaiShou.KuaiShouCallbackKey}{key}", state, TimeSpan.FromMinutes(10));
        //     result.BindUserUrl = _kuaiShouHelper.GetBindUserUrl(key);

        //     return result;
        // }

        if (_context.Post_Team_Third_Jobid_Rel.Any(x => x.PlatType == ThirdPlatType.快招工
        && x.TeamPostId == model.TeamPostId))
            throw new BadRequestException("该职位已绑定");

        var kzgResult = await _kuaiShouHelper.SendPostToKzg(new
        {
            name = model.name,
            gender = post.Sex switch
            {
                Sex.女 => 2,
                Sex.男 => 1,
                _ => 0
            },
            workingYears = 0,
            educationalBackground = post.Education switch
            {
                EducationType.高中 => 5,
                EducationType.大专 => 6,
                EducationType.本科 => 7,
                EducationType.硕士 => 8,
                EducationType.博士 => 9,
                _ => 1
            },
            contractType = model.contractType,
            category = model.category,
            location = model.location,
            ageMin = post.MinAge <= 0 ? null : (int?)post.MinAge,
            ageMax = post.MaxAge <= 0 ? null : (int?)post.MaxAge,
            vacancy = model.recruitNum ?? post.DeliveryNumber,
            salaryMin = post.MinSalary,
            salaryMax = post.MaxSalary,
            description = model.description,
            tags = model.tags,
            company = new KsCompany
            {
                introduction = model.company.introduction,
                businessCode = model.company.businessCode,
                businessName = model.company.businessName,
                location = model.company.location,
                scale = model.company.scale
            }
        }, openId);

        // result.BindUser = true;

        if (kzgResult?.code != 0 || kzgResult?.result == null)
            throw new BadRequestException($"发布职位到快手出错:{kzgResult.code}_{kzgResult.message}");

        result.jobId = kzgResult?.result.ToString();

        MyRedis.Client.HSet(RedisKey.EntBusinessData, model.company.businessName, new EntBusinessData
        {
            Code = model.company.businessCode,
            Name = model.company.businessName
        });

        thirdJobidRel = new Post_Team_Third_Jobid_Rel
        {
            TeamPostId = model.TeamPostId!,
            JobId = result.jobId!,
            PlatType = ThirdPlatType.快招工,
            CreatedUser = _user.Id,
            UpdatedUser = _user.Id
        };
        _context.Add(thirdJobidRel);
        _context.SaveChanges();

        return result;
    }

    private string GetStandardName(string name)
    {
        return _context.Dic_Base_Info.Where(w => w.Code.Trim().Equals(name.Trim()) && w.Type.Equals("kzg_send_post_map")).FirstOrDefault()?.Value ?? name;
    }

    public GetKsPostByNkpResponse GetKsPostByNkp(GetKsPostByNkp model)
    {
        var teamPost = _context.Post_Team.Where(x => x.TeamPostId == model.TeamPostId && x.Project_Team.HrId == _user.Id)
        .Select(s => new
        {
            s.Post.Name,
            s.Post.Describe,
            s.Post.Address,
            s.Post.RegionId,
            Company = new
            {
                s.Project_Team.Project.Agent_Ent.Describe,
                s.Project_Team.Project.Agent_Ent.Name,
                s.Project_Team.Project.Agent_Ent.Address,
                s.Project_Team.Project.Agent_Ent.RegionId,
                s.Project_Team.Project.Agent_Ent.Scale
            }
        }).FirstOrDefault();

        if (teamPost == null)
            throw new BadRequestException("职位不存在");

        //查询企业工商code
        var gscode = MyRedis.Client.HGet<EntBusinessData?>(RedisKey.EntBusinessData, teamPost.Company.Name);

        // 根据企业名称查询天眼查
        var tycInfo = _commonCacheService.GetAgentEntFromTyc(teamPost.Company.Name);

        if (gscode == null)
            gscode = new EntBusinessData { Code = tycInfo.CreditCode ?? string.Empty };
        else if (!string.IsNullOrWhiteSpace(tycInfo.CreditCode) && !gscode.Code.Equals(tycInfo.CreditCode))
            gscode.Code = tycInfo.CreditCode;

        var result = new GetKsPostByNkpResponse
        {
            name = teamPost.Name,
            description = teamPost.Describe,
            location = new KsLocation
            {
                address = teamPost.Address,
                areaCode = teamPost.RegionId,
                cityCode = $"{teamPost.RegionId?.Substring(0, 4)}00",
                provinceCode = $"{teamPost.RegionId?.Substring(0, 2)}0000"
            },
            company = new KsCompany
            {
                introduction = !string.IsNullOrWhiteSpace(tycInfo?.Describe) ? tycInfo.Describe : teamPost.Company.Describe,
                businessName = teamPost.Company.Name,
                businessCode = gscode?.Code,
                location = new KsLocation
                {
                    address = !string.IsNullOrWhiteSpace(tycInfo?.Address) ? tycInfo.Address : teamPost.Company.Address,
                    provinceCode = $"{teamPost.RegionId?.Substring(0, 2)}0000",
                    cityCode = $"{teamPost.Company.RegionId?.Substring(0, 4)}00"
                },
                scale = teamPost.Company.Scale switch
                {
                    EnterpriseScale.二十人以下 => "less_50",
                    EnterpriseScale.二十至一百 => "in_50_99",
                    EnterpriseScale.一百至五百 => "in_100_499",
                    EnterpriseScale.五百至一千 => "in_500_999",
                    EnterpriseScale.一千至一万 => "in_1000_4999",
                    EnterpriseScale.一万以上 => "more_10000",
                    _ => "less_50"
                },
                tycEntId = tycInfo.TycEntId
            }
        };

        return result;
    }

    public GetRemarkDicResponse GetRemarkDic()
    {
        var result = new GetRemarkDicResponse();

        var data = Constants.RecommendEnt.RemarkDics;

        result.Rows = data.SelectMany(s => s.Names.Select(ss => new
        {
            Name = ss,
            s.Level,
            s.Status
        })).Select(s => new GeneralDic
        {
            Id = s.Name,
            Name = s.Name
        }).ToList();

        return result;
    }

    public async Task<GetRecommendEntsResponse> GetRecommendEnts(GetRecommendEnts model)
    {
        var result = new GetRecommendEntsResponse();

        var predicate = PredicateBuilder.New<User_Hr_RecommendEnt>(x => true);

        //运营特殊权限
        // var yyHrIds = MyRedis.Client.HGet<List<string>?>(RedisKey.HrAdmin.Key, RedisKey.HrAdmin.QyXscAdmin) ?? new List<string>();
        // if (yyHrIds.Count == 0)
        //     yyHrIds = new List<string> { "159812546984312837", "152970914565595781", "208013953637774469", "163854737242870405" };
        // if (!yyHrIds.Contains(_user.Id))
        //     predicate = PredicateBuilder.New<User_Hr_RecommendEnt>(x => x.AdviserId == _user.Id);

        var isAdmin = _user.Powers?.Contains(Power.QiYeXianSuoAdmin) == true;

        if (!isAdmin)
            predicate = PredicateBuilder.New<User_Hr_RecommendEnt>(x => x.AdviserId == _user.Id);

        if (model.Status.HasValue)
            predicate = predicate.And(x => x.Status == model.Status);

        if (model.Source?.Count > 0)
            predicate = predicate.And(x => model.Source.Contains(x.Source));

        if (model.Level.HasValue)
            predicate = predicate.And(x => x.Level == model.Level);

        if (!string.IsNullOrWhiteSpace(model.OutletId))
            predicate = predicate.And(x => x.OutletId == model.OutletId);

        if (model.AdviserIds?.Count > 0)
            predicate = predicate.And(x => model.AdviserIds.Contains(x.AdviserId));

        var today = DateTime.Today;
        if (model.NoahCategory == 1)
            predicate = predicate.And(x => x.LastPostTime >= today);
        else if (model.NoahCategory == 2)
            predicate = predicate.And(x => x.LastDeliverTime >= today);

        if (!string.IsNullOrWhiteSpace(model.Search))
            predicate = predicate.And(x => x.Mobile.Contains(model.Search) || x.EntName.Contains(model.Search)
            || x.HrName.Contains(model.Search) || x.Post.Contains(model.Search) || x.User_Hr.NickName.Contains(model.Search));

        if (model.BeginCreatedTime.HasValue)
            predicate = predicate.And(x => x.CreatedTime >= model.BeginCreatedTime.Value);

        if (model.EndCreatedTime.HasValue)
        {
            model.EndCreatedTime = model.EndCreatedTime.Value.Date.AddDays(1);
            predicate = predicate.And(x => x.CreatedTime < model.EndCreatedTime.Value);
        }

        if (model.BeginFollowUpTime.HasValue)
            predicate = predicate.And(x => x.FollowUpTime >= model.BeginFollowUpTime.Value);

        if (model.EndFollowUpTime.HasValue)
        {
            model.EndFollowUpTime = model.EndFollowUpTime.Value.Date.AddDays(1);
            predicate = predicate.And(x => x.FollowUpTime < model.EndFollowUpTime.Value);
        }

        var sql = _context.User_Hr_RecommendEnt.Where(predicate);

        result.Total = sql.Count();

        var sortSql = sql.OrderByDescending(x => x.CreatedTime);
        if (model.NoahCategory == 1)
            sortSql = sql.OrderByDescending(o => o.LastPostTime);
        else if (model.NoahCategory == 2)
            sortSql = sql.OrderByDescending(o => o.LastDeliverTime);

        result.Rows = sortSql
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetRecommendEntInfo
        {
            Id = s.Id,
            CreatedTime = s.CreatedTime,
            EntName = s.EntName,
            Post = s.Post,
            FollowUpTime = s.FollowUpTime,
            AdviserId = s.AdviserId,
            AdviserName = s.User_Hr.NickName,
            HrName = s.HrName,
            Level = s.Level,
            Mobile = s.Mobile,
            Remark = s.Remark,
            FeedBack = s.FeedBack,
            Source = s.Source,
            SourceName = s.Source.GetDescription(),
            Status = s.Status,
            TEntId = s.TEntId,
            OutletId = s.OutletId,
            OutletName = s.OutletName,
            XbbXsjhName = s.XbbXsjhName,
            XbbXsjhUName = s.XbbXsjhUName
        }).ToList();

        var npEntIds = result.Rows.Where(x => (x.Source == HrRecommendEntSource.诺聘平台 || x.Source == HrRecommendEntSource.诺聘网点)
        && !string.IsNullOrEmpty(x.TEntId))
        .Select(s => s.TEntId).Distinct().ToList();

        if (npEntIds.Count > 0)
        {
            try
            {
                var npData = await _nuoPinApi.GetEntDeliverCount(npEntIds!);
                foreach (var item in result.Rows)
                {
                    if (item.Source == HrRecommendEntSource.诺聘平台 || item.Source == HrRecommendEntSource.诺聘网点)
                    {
                        item.TodayDeliverCount = npData?.FirstOrDefault(x => x.EnterpriseID == item.TEntId)?.TodayDeliverCount ?? 0;
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error("获取诺聘投递数量出错", ex.Message, npEntIds);
            }
        }

        return result;
    }

    public async Task<GetNPEntJobPositionsResponse> GetNPEntJobPositions(GetNPEntJobPositions model)
    {
        var result = new GetNPEntJobPositionsResponse();
        var data = await _nuoPinApi.GetEntJobPositions(model.EnterpriseID, model.Search, model.PageIndex, model.PageSize, true);
        result.Total = data.Data.Total;
        result.Rows = data.Data.Rows ?? new();
        return result;
    }

    public async Task<GetNPDeliverResponse> GetNPDeliverList(GetNPDeliver model)
    {
        var result = new GetNPDeliverResponse();
        var data = await _nuoPinApi.GetDeliverList(model.EnterpriseID, model.PK_WPID, model.PageIndex, model.PageSize);
        result.Total = data.Data.Total;
        result.Rows = data.Data.Rows ?? new();
        result.Rows.ForEach((item) =>
        {
            if (item.WorkEmpirical != null && item.WorkEmpirical == "0" && item.WorkTime == null)
                item.WorkEmpirical = "--";
        });

        return result;
    }

    public async Task<GetNPUserContactResponse> GetNPUserContactList(GetNPUserContact model, string AccountID)
    {
        var result = new GetNPUserContactResponse();
        var data = await _nuoPinApi.GetUserContactRecord(model.UserID, AccountID, model.PageIndex, model.PageSize);
        result.Total = data.Data.Total;
        result.Rows = data.Data.Rows ?? new();

        return result;
    }

    public async Task<EmptyResponse> SaveNPUserContact(SaveNPUserContact model, string AccountID, string? AccountName)
    {
        var result = new GetNPUserContactResponse();
        await _nuoPinApi.SaveUserContactRecord(model.UserID, model.Design, model.Result, model.Remark, AccountID, AccountName);
        return new EmptyResponse();
    }

    public EmptyResponse UpdateRecommendEnts(UpdateRecommendEnts model)
    {
        var re = _context.User_Hr_RecommendEnt.FirstOrDefault(x => x.Id == model.Id);

        if (re == null)
            throw new BadRequestException("内容不存在");

        if (re.AdviserId != _user.Id)
            throw new BadRequestException("不允许操作其他顾问的企业");

        if (string.IsNullOrWhiteSpace(model.FeedBack))
            throw new BadRequestException("缺少反馈");

        model.FeedBack = model.FeedBack.Trim();

        var remark = Constants.RecommendEnt.RemarkDics
        .SelectMany(s => s.Names.Select(ss => new
        {
            Name = ss,
            s.Level,
            s.Status
        })).FirstOrDefault(x => x.Name == model.FeedBack);

        if (remark == null)
            throw new BadRequestException("反馈不合法");

        re.FeedBack = model.FeedBack;
        re.Level = remark.Level;
        re.Status = remark.Status;
        re.FollowUpTime = DateTime.Now;

        _context.SaveChanges();

        MyRedis.Client.SAdd(SubscriptionKey.SyncToSCRMBeschannel, re.Id);

        //高松干通知需求

        return new EmptyResponse();
    }

    public async Task<GetOutletsResponse> GetOutlets()
    {
        var result = new GetOutletsResponse();

        var npData = await _nuoPinApi.GetOutlets();

        result.Rows = npData.Select(s => new Model.Hr.Kzg.GetOutletsInfo
        {
            OutletId = s.ServiceHallID,
            OutletName = s.Name
        }).ToList();

        return result;
    }

    public GetRecommendHrsResponse GetRecommendHrs()
    {
        var result = new GetRecommendHrsResponse();

        var hrs = _context.User_Hr.Where(x => x.Status == UserStatus.Active).Select(s => new
        {
            s.UserId,
            s.NickName,
            s.Powers
        }).ToList();

        result.Rows = hrs.Where(x => x.Powers.Contains(Power.QiYeXianSuo))
        .Select(s => new GetRecommendHrsInfo
        {
            Id = s.UserId,
            Name = s.NickName
        }).OrderBy(o => o.Name).ToList();

        return result;
    }
}