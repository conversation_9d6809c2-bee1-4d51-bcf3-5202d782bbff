using Config;
using Config.CommonModel.ThirdTalentInfo;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Staffing.Entity;
using Staffing.Entity.Elasticsearch;
using Staffing.Entity.Staffing;

namespace Infrastructure.CommonService;

[Service(ServiceLifetime.Transient)]
public class CommonEsService
{
    private IDbContextFactory<StaffingContext> _staffingContextFactory;
    private ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CacheHelper _cacheHelper;
    private readonly EsHelper _esHelper;
    public CommonEsService(IDbContextFactory<StaffingContext> staffingContextFactory,
        IOptionsSnapshot<ConfigManager> config,
        IHostEnvironment hostingEnvironment, LogManager log,
        CacheHelper cacheHelper, EsHelper esHelper)
    {
        _staffingContextFactory = staffingContextFactory;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
        _cacheHelper = cacheHelper;
        _esHelper = esHelper;
    }

    /// <summary>
    /// 更新平台人才库
    /// </summary>
    /// <returns></returns>
    public void UpdateTalentPlatform(string? talentId)
    {
        if (string.IsNullOrEmpty(talentId))
            return;

        try
        {
            var client = _esHelper.GetClient();

            // client.Delete<EsTalentPlatform>(talentId, d => d.Index(_esHelper.GetIndex().TalentPlatform));

            var _context = _staffingContextFactory.CreateDbContext();

            var t = _context.Talent_Platform.IgnoreQueryFilters()
            .Where(x => x.Id == talentId)
            .Select(s => new
            {
                s.Id,
                s.ChannelId,
                s.User_Seeker.User_Extend.DesiredPost,
                s.User_Seeker.User_Extend.DesiredPostName,
                s.User_Seeker.HrAppletQrCode,
                s.HrId,
                s.User_Seeker.User_Resume.Anonymous,
                s.User_Seeker.Avatar,
                s.User_Seeker.User_Resume.Birthday,
                s.User_Seeker.User_Resume.Education,
                s.User_Seeker.User_Resume.GraduationDate,
                s.SeekerId,
                s.User_Seeker.User.IdentityCard,
                s.User_Seeker.User.IdentityCardName,
                s.User_Seeker.User_Extend.LoginTime,
                s.User_Seeker.User_Resume.Major,
                s.User_Seeker.User.Mobile,
                s.User_Seeker.NickName,
                s.User_Seeker.User_Resume.Occupation,
                s.User_Seeker.RegionId,
                s.User_Seeker.User_Resume.School,
                s.User_Seeker.User_Resume.Score,
                s.User_Seeker.User_Resume.Sex,
                s.User_Seeker.User_Resume.Show,
                SeekerSource = s.User_Seeker.Source,
                SeekerStatus = s.User_Seeker.Status,
                SeekerUpdatedTime = s.User_Seeker.User_Resume.UpdatedTime,
                s.SeekerVisitTime,
                TalentCreatedTime = s.CreatedTime,
                TalentDeleted = s.Deleted,
                s.Level,
                s.Source,
                s.Status,
                Tags = s.Talent_Label.Select(ss => ss.Dic_Talent_Label.LabelName).ToList(),
                Works = s.User_Seeker.User_Resume.User_Work.Select(s => new EsSeekerWork
                {
                    BeginDate = s.BeginDate.ToDateTime(TimeOnly.MinValue),
                    Company = s.Company,
                    EndDate = s.EndDate.ToDateTime(TimeOnly.MinValue),
                    Post = s.Post
                }).ToList()
            }).FirstOrDefault();

            if (t == null)
            {
                client.Delete<EsTalentPlatform>(talentId, d => d.Index(_esHelper.GetIndex().TalentPlatform));
                return;
            }

            var esTalent = new EsTalentPlatform
            {
                Id = talentId,
                ChannelId = t.ChannelId,
                DesiredPost = string.IsNullOrEmpty(t.DesiredPost) ? null : t.DesiredPost.Split(",").ToList(),
                DesiredPostName = string.IsNullOrEmpty(t.DesiredPostName) ? null : t.DesiredPostName.Split(",").ToList(),
                HrAppletQrCode = t.HrAppletQrCode,
                HrId = t.HrId,
                SeekerAnonymous = t.Anonymous,
                SeekerAvatar = t.Avatar,
                SeekerBirthday = t.Birthday?.ToDateTime(TimeOnly.MinValue),
                SeekerEducation = t.Education,
                SeekerGraduationDate = t.GraduationDate?.ToDateTime(TimeOnly.MinValue),
                SeekerId = t.SeekerId!,
                SeekerIdentityCard = t.IdentityCard,
                SeekerIdentityCardName = t.IdentityCardName,
                SeekerLoginTime = t.LoginTime,
                SeekerMajor = t.Major,
                SeekerMobile = t.Mobile,
                SeekerNickName = t.NickName,
                SeekerOccupation = t.Occupation,
                SeekerRegionId = t.RegionId,
                SeekerSchool = t.School,
                SeekerScore = t.Score,
                SeekerSex = t.Sex,
                SeekerShow = t.Show,
                SeekerSource = t.SeekerSource,
                SeekerStatus = t.SeekerStatus,
                SeekerUpdatedTime = t.SeekerUpdatedTime,
                SeekerVisitTime = t.SeekerVisitTime,
                TalentCreatedTime = t.TalentCreatedTime,
                TalentDeleted = t.TalentDeleted,
                TalentLevel = t.Level,
                TalentSource = t.Source,
                TalentStatus = t.Status,
                UpdatedTime = DateTime.Now,
                Tags = t.Tags,
                Works = t.Works,
                SeekerSearch = new List<string>
            {
                t.Mobile,
                t.NickName
            }
            };

            if (!string.IsNullOrWhiteSpace(t.School))
                esTalent.SeekerSearch.Add(t.School);

            if (esTalent.DesiredPostName?.Count > 0)
                esTalent.SeekerSearch.AddRange(esTalent.DesiredPostName);

            var indexResponse = client.Index(esTalent, i => i.Index(_esHelper.GetIndex().TalentPlatform));

            if (!indexResponse.IsValid)
                throw indexResponse.OriginalException;
        }
        catch (Exception e)
        {
            _log.Error($"CommonEsService.UpdateTalentPlatform{SubscriptionKey.TalentTableChange}消息处理出错", Tools.GetErrMsg(e), talentId!);
            throw;
        }

        // client.DeleteByQuery<EsTalentPlatform>(d => d.Index(_esHelper.GetIndex().TalentPlatform).Query(q => q.Term(t => t.Field(f => f.SeekerId).Value(seekerId))));
    }

    /// <summary>
    /// 更新简历人才库
    /// </summary>
    /// <returns></returns>
    public void UpdateTalentVirtual(string? talentId)
    {
        if (string.IsNullOrEmpty(talentId))
            return;

        try
        {
            var client = _esHelper.GetClient();

            // client.Delete<EsTalentVirtual>(talentId, d => d.Index(_esHelper.GetIndex().TalentVirtual));

            var _context = _staffingContextFactory.CreateDbContext();

            var t = _context.Talent_Virtual.IgnoreQueryFilters()
            .Where(x => x.Id == talentId)
            .Select(s => new
            {
                s.Id,
                s.ChannelId,
                s.HrId,
                s.SeekerId,
                s.Status,
                s.UpdatedTime,
                s.Birthday,
                s.CreatedTime,
                s.Channel,
                s.Location,
                s.LocationNorm,
                s.DetailedLocation,
                s.Education,
                s.HeadPortrait,
                s.Mailbox,
                s.Mobile,
                s.Name,
                s.OriginalUrl,
                s.Perfection,
                s.QQ,
                s.RegionId,
                s.Sex,
                s.WeChat,
                s.WorkTime,
                Tags = s.Talent_Label.Select(ss => ss.Dic_Talent_Label.LabelName).ToList(),
                Works = s.Talent_Virtual_Work.Select(ss => new EsTalentWork
                {
                    CompanyName = ss.CompanyName,
                    Department = ss.Department,
                    EndTime = ss.EndTime,
                    PostName = ss.PostName,
                    StartTime = ss.StartTime
                }).ToList(),
                Edus = s.Talent_Virtual_Edu.Select(ss => new EsTalentEdu
                {
                    EndTime = ss.EndTime,
                    StartTime = ss.StartTime,
                    Education = ss.Education,
                    IsFullTime = ss.IsFullTime,
                    MajorName = ss.MajorName,
                    SchoolName = ss.SchoolName
                }).ToList(),
                Hopes = s.Talent_Virtual_Hope,
                s.IndustryLabel,
                s.OtherLabel,
                s.PostLabel,
                s.SkillAnalysis
            }).FirstOrDefault();

            if (t == null)
            {
                client.Delete<EsTalentVirtual>(talentId, d => d.Index(_esHelper.GetIndex().TalentVirtual));
                return;
            }

            var esTalent = new EsTalentVirtual
            {
                Id = talentId,
                ChannelId = t.ChannelId,
                HrId = t.HrId,
                SeekerId = t.SeekerId,
                UpdatedTime = t.UpdatedTime,
                Birthday = t.Birthday.Date,
                Channel = t.Channel,
                CreatedTime = t.CreatedTime,
                DetailedLocation = t.DetailedLocation,
                Location = t.Location,
                LocationNorm = t.LocationNorm,
                Education = t.Education,
                HeadPortrait = t.HeadPortrait,
                Mailbox = t.Mailbox,
                Mobile = t.Mobile,
                Name = t.Name,
                OriginalUrl = t.OriginalUrl,
                Perfection = t.Perfection,
                Qq = t.QQ,
                RegionId = t.RegionId,
                Sex = t.Sex,
                Status = t.Status,
                WeChat = t.WeChat,
                WorkTime = t.WorkTime,
                Tags = t.Tags,
                Edus = t.Edus,
                Hopes = t.Hopes == null ? null : new List<EsTalentHope> {
                new EsTalentHope {
                    HopeCity = t.Hopes.HopeCity,
                    IndustryName = t.Hopes.IndustryName,
                    MaxSalary = t.Hopes.MaxSalary,
                    MinSalary = t.Hopes.MinSalary,
                    PostName = t.Hopes.PostName,
                    RegionId = t.Hopes.RegionId
            }},
                IndustryLabel = t.IndustryLabel,
                OtherLabel = t.OtherLabel,
                PostLabel = t.PostLabel,
                SkillBusiness = t.SkillAnalysis?.Business,
                SkillIT = t.SkillAnalysis?.IT,
                SkillProfessional = t.SkillAnalysis?.Professional,
                Works = t.Works,
                TalentSearch = new List<string>
            {
                t.Mobile,
                t.Name
            }
            };

            if (!string.IsNullOrWhiteSpace(t.Hopes?.PostName))
                esTalent.TalentSearch.Add(t.Hopes.PostName);

            if (esTalent.Edus?.Count > 0)
                esTalent.TalentSearch.AddRange(esTalent.Edus.Select(s => s.SchoolName));

            if (esTalent.Works?.Count > 0)
                esTalent.TalentSearch.AddRange(esTalent.Works.Select(s => s.CompanyName));

            if (t.SkillAnalysis?.IT?.Count > 0)
                esTalent.TalentSearch.AddRange(t.SkillAnalysis.IT);

            if (t.SkillAnalysis?.Business?.Count > 0)
                esTalent.TalentSearch.AddRange(t.SkillAnalysis.Business);

            if (t.SkillAnalysis?.Professional?.Count > 0)
                esTalent.TalentSearch.AddRange(t.SkillAnalysis.Professional);

            var indexResponse = client.Index(esTalent, i => i.Index(_esHelper.GetIndex().TalentVirtual));

            if (!indexResponse.IsValid)
                throw indexResponse.OriginalException;
        }
        catch (Exception e)
        {
            _log.Error($"{SubscriptionKey.TalentTableChange}消息处理出错", Tools.GetErrMsg(e), talentId!);
            throw;
        }
    }

    /// <summary>
    /// 更新快手投递简历表
    /// </summary>
    /// <returns></returns>
    public void UpdateKuaishouTalentInfo(string? ApplicationId)
    {
        if (string.IsNullOrEmpty(ApplicationId))
            return;

        try
        {
            var client = _esHelper.GetClient();

            client.Delete<EsKuaishouTalentInfo>(ApplicationId, d => d.Index(_esHelper.GetIndex().KuaishouTalentInfo));

            var _context = _staffingContextFactory.CreateDbContext();

            var t = _context.Kuaishou_Talent_Infos.IgnoreQueryFilters()
            .Where(x => x.ApplicationId == ApplicationId)
            .Select(s => new KsTalentInfo
            {
                ApplicationId = s.ApplicationId,
                OpenTenantId = s.OpenTenantId,
                ResumeId = s.ResumeId,
                Name = s.Name,
                GenderCode = s.GenderCode,
                GenderName = s.GenderName,
                Phone = s.Phone,
                Age = s.Age,
                JobId = s.JobId,
                JobName = s.JobName,
                PostName = s.Post_Team_Third_Jobid_Rel.Post_Team.Post.Name,
                ChannelName = s.ChannelName,
                Recommender = s.Recommender,
                RecommenderId = s.RecommenderId,
                ApplyTime = s.ApplyTime,
                CompanyId = s.CompanyId,
                CompanyBusinessName = s.CompanyBusinessName,
                CompanyBusinessCode = s.CompanyBusinessCode,
                Platform = s.Platform,
                DataChannelSourceCode = s.DataChannelSourceCode,
                DataChannelSourceName = s.DataChannelSourceName,
                DataQualityLabel = s.DataQualityLabel ?? new DataQualityLabel(),
                DataDynamicResumeInfo = s.DataDynamicResumeInfo ?? new DataDynamicResumeInfo(),
                DataTrafficSources = s.DataTrafficSources,
                DataUserCity = s.DataUserCity,
                DataLocationCitys = s.DataLocationCitys ?? new LocationCity(),
                ExtInfoAgeMin = s.ExtInfoAgeMin,
                ExtInfoAgeMax = s.ExtInfoAgeMax,
                ExtInfoIntentionJob = s.ExtInfoIntentionJob ?? new List<IntentionJob>(),
                ExtInfoIntentionCity = s.ExtInfoIntentionCity ?? new List<IntentionCity>(),
                ExtInfoJobHuntingStatus = s.ExtInfoJobHuntingStatus,
                //KSHrTalentRelations = s.Kuaishou_Hr_Talent_Relations == null ? null : new KSHrTalentRelations
                //{
                //    HrId = s.Kuaishou_Hr_Talent_Relations.FirstOrDefault()!.HrId,
                //    HrName = s.Kuaishou_Hr_Talent_Relations.FirstOrDefault()!.User_Hr.NickName,
                //    ApplicationId = ApplicationId,
                //    ReturnVisit = s.Kuaishou_Hr_Talent_Relations.FirstOrDefault()!.ReturnVisit,
                //    VisitMemo = s.Kuaishou_Hr_Talent_Relations.FirstOrDefault()!.VisitMemo,
                //    Result = s.Kuaishou_Hr_Talent_Relations.FirstOrDefault()!.Result,
                //    InvalidType = s.Kuaishou_Hr_Talent_Relations.FirstOrDefault()!.InvalidType,
                //    SecondVisit = s.Kuaishou_Hr_Talent_Relations.FirstOrDefault()!.SecondVisit,
                //    SecondVisitMemo = s.Kuaishou_Hr_Talent_Relations.FirstOrDefault()!.SecondVisitMemo,
                //    WaitToPhone = s.Kuaishou_Hr_Talent_Relations.FirstOrDefault()!.WaitToPhone,
                //    Status = s.Kuaishou_Hr_Talent_Relations.FirstOrDefault()!.Status
                //}
            })
            .FirstOrDefault();

            if (t == null)
                return;

            var esTalent = new EsKuaishouTalentInfo
            {
                ApplicationId = t.ApplicationId,
                OpenTenantId = t.OpenTenantId,
                ResumeId = t.ResumeId,
                Name = t.Name,
                GenderCode = t.GenderCode,
                GenderName = t.GenderName,
                Phone = t.Phone,
                Age = t.Age,
                JobId = t.JobId,
                JobName = t.JobName,
                PostName = t.PostName,
                ChannelName = t.ChannelName,
                Recommender = t.Recommender,
                RecommenderId = t.RecommenderId,
                ApplyTime = t.ApplyTime,
                CompanyId = t.CompanyId,
                CompanyBusinessName = t.CompanyBusinessName,
                CompanyBusinessCode = t.CompanyBusinessCode,
                Platform = t.Platform,
                DataChannelSourceCode = t.DataChannelSourceCode,
                DataChannelSourceName = t.DataChannelSourceName,
                DataQualityLabel = t.DataQualityLabel ?? new DataQualityLabel(),
                DataDynamicResumeInfo = t.DataDynamicResumeInfo ?? new DataDynamicResumeInfo(),
                DataTrafficSources = t.DataTrafficSources,
                DataUserCity = t.DataUserCity,
                DataLocationCitys = t.DataLocationCitys ?? new LocationCity(),
                ExtInfoAgeMin = t.ExtInfoAgeMin,
                ExtInfoAgeMax = t.ExtInfoAgeMax,
                ExtInfoIntentionJob = t.ExtInfoIntentionJob ?? new List<IntentionJob>(),
                ExtInfoIntentionCity = t.ExtInfoIntentionCity ?? new List<IntentionCity>(),
                ExtInfoJobHuntingStatus = ((ExtInfoJobHuntingStatus)(t.ExtInfoJobHuntingStatus ?? 1)).GetDescription(),
                //KSHrTalentRelations = t.KSHrTalentRelations
            };
            esTalent.Search = new List<string>();
            if (esTalent.ExtInfoIntentionJob?.Count > 0)
                esTalent.Search.AddRange(esTalent.ExtInfoIntentionJob.Select(s => s.content ?? string.Empty));
            if (esTalent.ExtInfoIntentionCity?.Count > 0)
                esTalent.Search.AddRange(esTalent.ExtInfoIntentionCity.Select(s => s.cityName ?? string.Empty));
            if (!string.IsNullOrEmpty(esTalent.Name))
                esTalent.Search.Add(esTalent.Name);
            if (!string.IsNullOrEmpty(esTalent.Name))
                esTalent.Search.Add(esTalent.Name);
            if (!string.IsNullOrEmpty(esTalent.Phone))
                esTalent.Search.Add(esTalent.Phone);
            if (esTalent.KSHrTalentRelations != null)
                esTalent.Search.Add(esTalent.KSHrTalentRelations.HrId);

            var indexResponse = client.Index(esTalent, i => i.Index(_esHelper.GetIndex().KuaishouTalentInfo));

            if (!indexResponse.IsValid)
                throw indexResponse.OriginalException;
        }
        catch (Exception e)
        {
            _log.Error($"{SubscriptionKey.TalentTableChange}消息处理出错", Tools.GetErrMsg(e), ApplicationId!);
            throw;
        }
    }

    /// <summary>
    /// 更新人才融合库索引
    /// </summary>
    /// <returns></returns>
    public void UpdateTalentResumeIndex(string? Id)
    {
        if (string.IsNullOrEmpty(Id))
            return;

        try
        {
            var client = _esHelper.GetClient();
            var _context = _staffingContextFactory.CreateDbContext();

            var talent_resume = _context.Talent_Resume.Where(w => w.Id == Id).FirstOrDefault();

            // if (talent_resume == null)
            //     return;

            if (talent_resume == null)
            {
                client.Delete<EsTalentResume>(Id, d => d.Index(_esHelper.GetIndex().TalentResume));
                return;
            }

            //if (!CheckDataEdus(talent_resume))
            //    return;

            var talentResume = new EsTalentResume
            {
                Id = talent_resume.Id,
                Source = talent_resume.Source,
                Status = talent_resume.Status,
                SeekerId = talent_resume.SeekerId,
                Name = talent_resume.Name,
                Mobile = talent_resume.Mobile,
                HeadPortrait = talent_resume.HeadPortrait,
                Sex = talent_resume.Sex,
                Mailbox = talent_resume.Mailbox,
                WeChat = talent_resume.WeChat,
                QQ = talent_resume.QQ,
                Birthday = talent_resume.Birthday,
                WorkTime = talent_resume.WorkTime,
                Perfection = talent_resume.Perfection,
                SelfEvaluation = talent_resume.SelfEvaluation,
                Education = talent_resume.Education,
                Edus = talent_resume.Edus == null ? null : JsonConvert.DeserializeObject<List<EsTalentResumeEdu>>(JsonConvert.SerializeObject(talent_resume.Edus)),
                Hopes = talent_resume.Hopes == null ? null : JsonConvert.DeserializeObject<List<EsTalentResumeHope>>(JsonConvert.SerializeObject(talent_resume.Hopes)),
                Projects = talent_resume.Projects == null ? null : JsonConvert.DeserializeObject<List<EsTalentResumeProject>>(JsonConvert.SerializeObject(talent_resume.Projects)),
                Works = talent_resume.Works == null ? null : JsonConvert.DeserializeObject<List<EsTalentResumeWork>>(JsonConvert.SerializeObject(talent_resume.Works)),
                Honours = talent_resume.Honours == null ? null : JsonConvert.DeserializeObject<List<EsTalentResumeHonour>>(JsonConvert.SerializeObject(talent_resume.Honours)),
                Products = talent_resume.Products,
                Certificates = talent_resume.Certificates,
                SkillProfessional = talent_resume.SkillAnalysis != null ? talent_resume.SkillAnalysis.Professional : null,
                SkillIT = talent_resume.SkillAnalysis != null ? talent_resume.SkillAnalysis.IT : null,
                SkillBusiness = talent_resume.SkillAnalysis != null ? talent_resume.SkillAnalysis.Business : null,
                IndustryLabel = talent_resume.IndustryLabel,
                PostLabel = talent_resume.PostLabel,
                OtherLabel = talent_resume.OtherLabel,
                OriginalUrl = talent_resume.OriginalUrl,
                City = talent_resume.City,
                Address = talent_resume.Address,
                RegionId = talent_resume.RegionId,
                UpdatedTime = talent_resume.UpdatedTime.ToUniversalTime(),
                CreatedTime = talent_resume.CreatedTime.ToUniversalTime(),
                LastLoginTime = talent_resume.LastLoginTime == null ? null : talent_resume.LastLoginTime.Value.ToUniversalTime(),
                RegistedTime = talent_resume.RegistedTime == null ? null : talent_resume.RegistedTime.Value.ToUniversalTime(),
                Deleted = talent_resume.Deleted,
                TalentSearch = new List<string>(),
                HrIds = talent_resume.HrIds,
                DeptIds = talent_resume.DeptIds,
                HideInvalidTime = talent_resume.HideInvalidTime == null ? null : talent_resume.HideInvalidTime.Value.ToUniversalTime(),
            };

            if (talentResume == null)
                return;

            //t.TalentSearch = new List<string>();
            //if (esTalent.ExtInfoIntentionJob?.Count > 0)
            //    esTalent.Search.AddRange(esTalent.ExtInfoIntentionJob.Select(s => s.content ?? string.Empty));
            //if (esTalent.ExtInfoIntentionCity?.Count > 0)
            //    esTalent.Search.AddRange(esTalent.ExtInfoIntentionCity.Select(s => s.cityName ?? string.Empty));
            //if (!string.IsNullOrEmpty(esTalent.Name))
            //    esTalent.Search.Add(esTalent.Name);
            //if (!string.IsNullOrEmpty(esTalent.Name))
            //    esTalent.Search.Add(esTalent.Name);
            //if (!string.IsNullOrEmpty(esTalent.Phone))
            //    esTalent.Search.Add(esTalent.Phone);
            //if (esTalent.KSHrTalentRelations != null)
            //    esTalent.Search.Add(esTalent.KSHrTalentRelations.HrId);

            var indexResponse = client.Index(talentResume, i => i.Index(_esHelper.GetIndex().TalentResume));

            if (!indexResponse.IsValid)
                throw indexResponse.OriginalException;
        }
        catch (Exception e)
        {
            _log.Error($"{SubscriptionKey.TalentResumeChange}消息处理出错", Tools.GetErrMsg(e), Id!);
            throw;
        }
    }

    /// <summary>
    /// 数据校验
    /// </summary>
    /// <param name="talent_resume"></param>
    /// <exception cref="NotImplementedException"></exception>
    private bool CheckDataEdus(Talent_Resume talent_resume)
    {
        if (talent_resume.Edus != null && talent_resume.Edus.Count > 0)
        {
            foreach (var item in talent_resume.Edus)
            {
                if (item.StartTime == null)
                    return false;
            }
        }
        return true;
    }
}