using System.Text.Json.Serialization;
using Config.CommonModel;
using Config.Enums;

namespace Staffing.Model.CentreService.Transit;

public class GetHrs : QueryRequest
{
    /// <summary>
    /// 检索，手机号或姓名
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 顾问名字
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 权限
    /// </summary>
    public string? Power { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public List<string>? AdviserIds { get; set; }
}

public class GetHrsResponse : QueryResponse
{
    public List<GetHrsDetail> Rows { get; set; } = default!;
}

public class GetHrsDetail
{
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? AdviserId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 在线状态
    /// </summary>
    public OnlineStatus? OnlineStatus { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 人才数量
    /// </summary>
    public int Talent { get; set; }

    /// <summary>
    /// 虚拟人才数量
    /// </summary>
    public int VirtualTalent { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 企业简称
    /// </summary>
    public string? EntAbbr { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// 企业微信二维码
    /// </summary>
    public string? EntWeChatQrCode { get; set; }

    /// <summary>
    /// 登录时间
    /// </summary>
    [JsonIgnore]
    public DateTime LoginTime { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 小程序二维码
    /// </summary>
    public string? AppletQrCode { get; set; }
}

public class GetHrYsc
{
    public string? PK_EAID { get; set; }
}

public class GetHrYscResponse
{
    public bool Ysc { get; set; }
}