﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 虚拟人才库项目经历
/// </summary>
[Table("talent_virtual_project")]
public class Talent_Virtual_Project
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 虚拟人才库id
    /// </summary>
    public string VirtualId { get; set; } = default!;

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ProjectName { get; set; } = default!;

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 项目描述
    /// </summary>
    public string ProjectRemarks { get; set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Talent_Virtual Talent_Virtual { get; set; } = default!;
}

