
-- ----------------------------
-- 管理员
-- ----------------------------
DROP TABLE IF EXISTS `admin`;
CREATE TABLE `admin` (
  `Id` varchar(18) NOT NULL,
  `Account` varchar(50) NOT NULL COMMENT '账号',
  `Password` varchar(1024) NOT NULL COMMENT '密码',
  `Powers` varchar(1024) NOT NULL COMMENT '权限',
  `Name` varchar(50) DEFAULT NULL COMMENT '名称',
  `Status` int(2) NOT NULL DEFAULT 1 COMMENT '状态',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_admin_Account` (`Account`),
  KEY `IX_admin_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='管理员';

INSERT INTO `admin` (`Id`, `Account`, `Password`, `Powers`, `Name`, `Status`, `CreatedTime`) VALUES ('1', '***********', 'aui5Fj1WUvO8xL74MsUM7DqupWbi6Www4PZSb9s8itw=', '', '管理员', 1, '2023-04-09 14:19:21.000000');
