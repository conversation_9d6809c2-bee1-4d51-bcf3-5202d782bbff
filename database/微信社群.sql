
-- ----------------------------
-- 微信社群
-- ----------------------------
DROP TABLE IF EXISTS `wx_group`;
CREATE TABLE `wx_group` (
  `Id` bigint NOT NULL,
  `GroupType` int NOT NULL COMMENT '1-微信群，2-企业微信',
  `GroupName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '群名称',
  `Describe` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '群描述（进群率）',
  `GroupNumber` int NOT NULL COMMENT '群人数',
  `FindTime` bigint NOT NULL COMMENT '发现时间戳',
  `QRCode` varchar(1024) DEFAULT NULL COMMENT '二维码url',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='微信社群';


-- ----------------------------
-- 隐私号码保护
-- ----------------------------
DROP TABLE IF EXISTS `privacy_number`;
CREATE TABLE `privacy_number` (
  `id` bigint NOT NULL COMMENT '主键',
  `fromId` varchar(50) COMMENT '拨打者Id',
  `fromType` int COMMENT '拨打类型',
  `phone_no` varchar(20) COMMENT '电话号码',
  `city` varchar(50) COMMENT '城市',
  `call_out_time` datetime COMMENT '呼出时间',
  `ring_time` datetime COMMENT '响铃时间',
  `record_url` varchar(500) COMMENT '录音URL',
  `ring_record_url` varchar(500) COMMENT '响铃录音URL',
  `free_ring_time` datetime COMMENT '免费响铃时间',
  `control_msg` varchar(500) COMMENT '控制消息',
  `secret_no` varchar(20) COMMENT '隐私号码',
  `call_type` int COMMENT '呼叫类型',
  `control_type` varchar(50) COMMENT '控制类型',
  `release_time` datetime COMMENT '释放时间',
  `pool_key` varchar(255) COMMENT '池关键字',
  `sub_id` bigint COMMENT '子ID',
  `unconnected_cause` int COMMENT '未连接原因',
  `call_time` datetime COMMENT '呼叫时间',
  `peer_no` varchar(20) COMMENT '对方号码',
  `called_display_no` varchar(20) COMMENT '被叫显示号码',
  `release_dir` int COMMENT '释放方向',
  `call_id` varchar(50) COMMENT '呼叫ID',
  `start_time` datetime COMMENT '开始时间',
  `partner_key` varchar(50) COMMENT '合作伙伴关键字',
  `out_id` varchar(50) COMMENT '外部ID',
  `release_cause` int COMMENT '释放原因',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6) COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='隐私号码保护';
