﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 合同模板
/// </summary>
[Table("contract_doctemplate")]
public class Contract_Doctemplate
{
    [Key]
    public string TemplateId { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// 企业Id
    /// </summary>
    public string EntId { get; set; } = null!;

    /// <summary>
    /// 模板类型
    /// </summary>
    public int Type { get; set; }

    /// <summary>
    /// 模板组类型
    /// </summary>
    public int GroupType { get; set; }

    /// <summary>
    /// E签宝模板Id
    /// </summary>
    public string EtemplateId { get; set; } = null!;

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 删除
    /// </summary>
    public bool Deleted { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string CreatedBy { get; set; } = null!;

    /// <summary>
    /// 更新人
    /// </summary>
    public string UpdatedBy { get; set; } = null!;

    public DateTime UpdatedTime { get; set; } = DateTime.Now;
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}
