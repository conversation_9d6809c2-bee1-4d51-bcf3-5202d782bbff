﻿using Microsoft.EntityFrameworkCore;

namespace Staffing.Entity.Noah;

public partial class NoahContext : DbContext
{
    public NoahContext(DbContextOptions<NoahContext> options)
        : base(options)
    {

    }

    //主
    public virtual DbSet<Contract> Contract { get; set; } = default!;
    public virtual DbSet<Customer> Customer { get; set; } = default!;
    public virtual DbSet<CustomerContact> CustomerContact { get; set; } = default!;
    public virtual DbSet<VEmployee> VEmployee { get; set; } = default!;
    public virtual DbSet<VProduct> VProduct { get; set; } = default!;
    public virtual DbSet<VSysSetBooks> VSysSetBooks { get; set; } = default!;
    public virtual DbSet<OpportUnity> OpportUnity { get; set; } = default!;
    public virtual DbSet<ContractProductRelation> ContractProductRelation { get; set; } = default!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Contract>().HasQueryFilter(b => b.DelFlag == "0");
        modelBuilder.Entity<Customer>().HasQueryFilter(b => b.DelFlag == "0");
        modelBuilder.Entity<OpportUnity>().HasQueryFilter(b => b.DelFlag == "0");
        modelBuilder.Entity<CustomerContact>().HasQueryFilter(b => b.DelFlag == "0");

        // modelBuilder.Entity<VEmployee>().HasNoKey().ToView("v_employee");

        modelBuilder.Entity<Contract>()
            .HasOne(x => x.Customer)
            .WithMany()
            .HasForeignKey(x => x.CustomerId);

        modelBuilder.Entity<OpportUnity>()
            .HasOne(x => x.Customer)
            .WithMany()
            .HasForeignKey(x => x.CustomerId);

        modelBuilder.Entity<Contract>()
            .HasOne(x => x.SignerModel)
            .WithOne().IsRequired(false)
            .HasForeignKey<Contract>(x => x.Signer)
            .HasPrincipalKey<VEmployee>(x => x.EmployeeCode);

        modelBuilder.Entity<OpportUnity>()
            .HasOne(x => x.CreateByModel)
            .WithOne().IsRequired(false)
            .HasForeignKey<OpportUnity>(x => x.CreateBy)
            .HasPrincipalKey<VEmployee>(x => x.EmployeeCode);

        modelBuilder.Entity<ContractProductRelation>()
            .HasOne(x => x.Contract)
            .WithMany(x => x.ContractProductRelation)
            .HasForeignKey(x => x.ContractId);
    }
}
