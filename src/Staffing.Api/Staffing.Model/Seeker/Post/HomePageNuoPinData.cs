using Config.CommonModel.Business;

namespace Staffing.Model.Seeker.Post;

public class HomePageThirdPartyDataResponse
{
    /// <summary>
    /// 诺聘职位数据看板
    /// </summary>
    public NuopinPostPanel NuopinPostPanel { get; set; } = new NuopinPostPanel();

    /// <summary>
    /// 诺优考项目数据看板
    /// </summary>
    public NuoyoukaoProjectPanel NuoyoukaoProjectPanel { get; set; } = new NuoyoukaoProjectPanel();

    /// <summary>
    /// 诺聘顾问列表
    /// </summary>
    public List<NuopinAdvisersModel> NuopinAdvisers = new List<NuopinAdvisersModel>();
}