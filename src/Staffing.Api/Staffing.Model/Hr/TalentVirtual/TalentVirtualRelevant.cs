﻿using Config.CommonModel;
using Config.CommonModel.Business;
using Config.CommonModel.JavaDataApi;
using Config.Enums;
using Staffing.Entity.Staffing;
using System.Text.Json.Serialization;

namespace Staffing.Model.Hr.TalentVirtual
{
    /// <summary>
    /// 虚拟人才库项目类
    /// </summary>
    public class TalentVirtualRelevant
    {
    }

    #region 虚拟人才库列表相关模型
    /// <summary>
    /// 虚拟人才库列表接收模型
    /// </summary>
    public class TalentVirtualResumeListRequest : QueryRequest
    {
        /// <summary>
        /// 搜索（姓名、手机号、邮箱、学校、岗位、行业、公司）
        /// </summary>
        public string Search { get; set; } = string.Empty;

        /// <summary>
        /// 用户形式
        /// </summary>
        public TalentVirtualStatus? Status { get; set; }

        /// <summary>
        /// 期望职位
        /// </summary>
        public string? HopePost { get; set; }

        /// <summary>
        /// 期望行业
        /// </summary>
        public string? HopeIndustry { get; set; }

        /// <summary>
        /// 学历
        /// </summary>
        public TalentVirtualEducation? Education { get; set; }

        /// <summary>
        /// 渠道
        /// </summary>
        public TalentVirtualChannel? Channel { get; set; }

        /// <summary>
        /// 城市Code集合
        /// </summary>
        public List<string>? RegionIdArray { get; set; }

        /// <summary>
        /// 入库开始时间
        /// </summary>
        public string? StartTime { get; set; }

        /// <summary>
        /// 入库结束时间
        /// </summary>
        public string? EndTime { get; set; }

        /// <summary>
        /// 人才库标签id
        /// </summary>
        public string? TalentLabelId { get; set; }

        /// <summary>
        /// 最小年龄
        /// </summary>
        public int? MinAge { get; set; }

        /// <summary>
        /// 最大年龄
        /// </summary>
        public int? MaxAge { get; set; }

        /// <summary>
        /// 渠道商Id
        /// </summary>
        public string? ChannelId { get; set; }
    }

    /// <summary>
    /// 虚拟人才库列表返回模型
    /// </summary>
    public class TalentVirtualResumeListResponse : QueryResponse
    {
        public List<TalentVirtualResumeListModel> Rows { get; set; } = new List<TalentVirtualResumeListModel>();

        public int Count { get; set; } = 0;

        // /// <summary>
        // /// 人才数量
        // /// </summary>
        // public int Talent { get; set; }

        // /// <summary>
        // /// 虚拟人才库数量
        // /// </summary>
        // public int VirtualTalent { get; set; }
    }

    /// <summary>
    /// 虚拟人才库列表返实体
    /// </summary>
    public class TalentVirtualResumeListModel
    {
        /// <summary>
        /// 虚拟人才库id
        /// </summary>
        public string? Id { get; set; } = default!;

        /// <summary>
        /// 用户姓名
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 用户头像
        /// </summary>
        public string? HeadPortrait { get; set; }

        /// <summary>
        /// 用户形式
        /// </summary>
        public TalentVirtualStatus? Status { get; set; }

        /// <summary>
        /// 用户形式解释
        /// </summary>
        public string? StatusName { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 性别解释
        /// </summary>
        public string? SexName { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 生日
        /// </summary>
        [JsonIgnore]
        public DateTime? Birthday { get; set; }

        /// <summary>
        /// 学历
        /// </summary>
        public TalentVirtualEducation? Education { get; set; }

        /// <summary>
        /// 学历解释
        /// </summary>
        public string? EducationName { get; set; }

        /// <summary>
        /// 工作经验
        /// </summary>
        public int? WorkExperience { get; set; }

        /// <summary>
        /// 所在地
        /// </summary>
        public string? Location { get; set; }

        /// <summary>
        /// 期望行业
        /// </summary>
        public string? HopeIndustry { get; set; }

        /// <summary>
        /// 期望职位
        /// </summary>
        public string? HopePost { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 渠道
        /// </summary>
        public TalentVirtualChannel? Channel { get; set; }

        /// <summary>
        /// 渠道解释
        /// </summary>
        public string? ChannelName { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        public List<string>? Label { get; set; }

        /// <summary>
        /// 工作经历
        /// </summary>
        public string? WorkExp { get; set; }

        /// <summary>
        /// 教育经历
        /// </summary>
        public string? EduExp { get; set; }

        /// <summary>
        /// 期望薪资
        /// </summary>
        public string? HopeSalary { get; set; }

        /// <summary>
        /// 人才库标签
        /// </summary>
        public List<TalentLabelClass>? TalentLabel { get; set; }

        /// <summary>
        /// 渠道商Id
        /// </summary>
        public string? ChannelId { get; set; }

        /// <summary>
        /// 渠道商名称
        /// </summary>
        public string? ChannelUserName { get; set; }
    }

    public class DeleteTalentVirtualResumeRequest
    {
        public List<string> VirtualIds { get; set; } = new List<string>();
    }
    #endregion

    #region 虚拟人才库详情相关模型
    /// <summary>
    /// 虚拟人才库详情返回模型
    /// </summary>
    public class TalentVirtualResumeDetailsResponse : TalentVirtualResumeDetailsModel
    {

    }

    /// <summary>
    /// 修改虚拟人才库简历接收模型
    /// </summary>
    public class EditVirtualResumeDetailsRequest : TalentVirtualResumeDetailsModel
    {

    }

    /// <summary>
    /// 虚拟人才库详情模型
    /// </summary>
    public class TalentVirtualResumeDetailsModel
    {
        /// <summary>
        /// 虚拟人才库id
        /// </summary>
        public string VirtualId { get; set; } = default!;

        /// <summary>
        /// 用户姓名
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 用户头像
        /// </summary>
        public string HeadPortrait { get; set; } = string.Empty;

        /// <summary>
        /// 简历状态
        /// </summary>
        public TalentVirtualStatus Status { get; set; }

        /// <summary>
        /// 简历状态解释
        /// </summary>
        public string StatusName { get; set; } = string.Empty;

        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }

        /// <summary>
        /// 生日
        /// </summary>
        public DateTime Birthday { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex Sex { get; set; }

        /// <summary>
        /// 性别解释
        /// </summary>
        public string SexName { get; set; } = string.Empty;

        /// <summary>
        /// 工作经验
        /// </summary>
        public int WorkExperience { get; set; }

        /// <summary>
        /// 工作开始时间
        /// </summary>
        public DateTime WorkTime { get; set; }

        /// <summary>
        /// 学历
        /// </summary>
        public TalentVirtualEducation Education { get; set; }

        /// <summary>
        /// 学历解释
        /// </summary>
        public string EducationName { get; set; } = string.Empty;

        /// <summary>
        /// 手机号
        /// </summary>
        public string Mobile { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱
        /// </summary>
        public string Mailbox { get; set; } = string.Empty;

        /// <summary>
        /// 微信
        /// </summary>
        public string WeChat { get; set; } = string.Empty;

        /// <summary>
        /// QQ
        /// </summary>
        public string QQ { get; set; } = string.Empty;

        /// <summary>
        /// 简历完整度
        /// </summary>
        public int Perfection { get; set; }

        /// <summary>
        /// 其他标签（小析职业标签除去行业与职位）
        /// </summary>
        public List<string>? OtherLabel { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string Location { get; set; } = string.Empty;


        /// <summary>
        /// 亮点
        /// </summary>
        public TalentVirtaualResumeHighlights? Highlights { get; set; }

        /// <summary>
        /// 风险
        /// </summary>
        public TalentVirtaualResumeRisks? Risks { get; set; }

        /// <summary>
        /// 自我评价
        /// </summary>
        public string SelfEvaluation { get; set; } = string.Empty;

        /// <summary>
        /// 原始简历地址
        /// </summary>
        public string OriginalUrl { get; set; } = string.Empty;

        /// <summary>
        /// 工作经历
        /// </summary>
        public List<TalentVirtaualResumeWorkSub>? WorkSub { get; set; }

        /// <summary>
        /// 教育经历
        /// </summary>
        public List<TalentVirtaualResumeEduSub>? EduSub { get; set; }

        /// <summary>
        /// 项目经历
        /// </summary>
        public List<TalentVirtaualResumeProjectSub>? ProjectSub { get; set; }

        /// <summary>
        /// 求职期望
        /// </summary>
        public TalentVirtaualResumeHopeSub? HopeSub { get; set; }

        /// <summary>
        /// 技能分析
        /// </summary>
        public TalentVirtaualResumeSkillSub? SkillSub { get; set; }

        /// <summary>
        /// 真实用户
        /// </summary>
        public TalentVirtaualResumeRealUserSub? RealUserSub { get; set; }
    }

    /// <summary>
    /// 真实用户（详情子表）
    /// </summary>
    public class TalentVirtaualResumeRealUserSub
    {
        /// <summary>
        /// 用户id
        /// </summary>
        public string? SeekerId { get; set; }

        /// <summary>
        /// 用户姓名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 用户头像
        /// </summary>
        public string? HeadPortrait { get; set; }

        /// <summary>
        /// 是否实名认证
        /// </summary>
        public bool? IsRealName { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 性别解释
        /// </summary>
        public string? SexName { get; set; }

        /// <summary>
        /// 学历
        /// </summary>
        public EducationType? Education { get; set; }

        /// <summary>
        /// 学历解释
        /// </summary>
        public string? EducationName { get; set; }

        /// <summary>
        /// 职业
        /// </summary>
        public OccupationType? Occupation { get; set; }

        /// <summary>
        /// 职业解释
        /// </summary>
        public string? OccupationName { get; set; }

        /// <summary>
        /// 最近登录时间
        /// </summary>
        public DateTime LoginTime { get; set; }

        /// <summary>
        /// 最近活跃
        /// </summary>
        public int? ActiveDay { get; set; }

        /// <summary>
        /// 最近浏览
        /// </summary>
        public int? BrowseNumber { get; set; }

        /// <summary>
        /// 对Ta感兴趣
        /// </summary>
        public int? Interested { get; set; }
    }

    /// <summary>
    /// 修改虚拟人才库原始简历地址接收模型
    /// </summary>
    public class EditTalentVirtaualOriginalUrlRequest
    {
        /// <summary>
        /// 虚拟人才库id
        /// </summary>
        public string VirtaualId { get; set; } = string.Empty;

        /// <summary>
        /// 原始简历地址
        /// </summary>
        public string Url { get; set; } = string.Empty;
    }
    #endregion

    #region 虚拟人才库评论相关模型
    /// <summary>
    /// 添加虚拟人才库简历评论接收模型
    /// </summary>
    public class AddTalentVirtualResumeCommentRequest
    {
        /// <summary>
        /// 虚拟人才库id
        /// </summary>
        public string VirtualId { get; set; } = string.Empty;

        /// <summary>
        /// 评论内容
        /// </summary>
        public string Content { get; set; } = string.Empty;
    }

    /// <summary>
    /// 获取虚拟人才库简历评论接收模型
    /// </summary>
    public class GetTalentVirtualResumeCommentRequest : QueryRequest
    {
        /// <summary>
        /// 虚拟人才库id
        /// </summary>
        public string VirtualId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 获取虚拟人才库简历评论返回模型
    /// </summary>
    public class GetTalentVirtualResumeCommentResponse : QueryResponse
    {
        public List<GetTalentVirtualResumeCommentModel> Rows { get; set; } = new List<GetTalentVirtualResumeCommentModel>();
    }

    /// <summary>
    /// 获取虚拟人才库简历评论模型
    /// </summary>
    public class GetTalentVirtualResumeCommentModel
    {
        /// <summary>
        /// 评论id
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// hr姓名
        /// </summary>
        public string HrName { get; set; } = string.Empty;

        /// <summary>
        /// hr头像
        /// </summary>
        public string HeadPortrait { get; set; } = string.Empty;

        /// <summary>
        /// 评论内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 评论时间
        /// </summary>
        public DateTime CreatedTime { get; set; }
    }
    #endregion

    #region 虚拟人才库列表下拉菜单相关模型
    /// <summary>
    /// 获取职位集合返回模型
    /// </summary>
    public class TalentVirtualIndustryListResponse
    {
        /// <summary>
        /// 职位集合
        /// </summary>
        public List<TalentVirtualIndustryListModel>? Rows { get; set; }
    }

    /// <summary>
    /// 获取职位集合模型
    /// </summary>
    public class TalentVirtualIndustryListModel
    {
        /// <summary>
        /// 职位id
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 职位名称
        /// </summary>
        public string Name { get; set; } = string.Empty;
    }

    /// <summary>
    /// 获取行业集合返回模型
    /// </summary>
    public class TalentVirtualPostListResponse
    {
        /// <summary>
        /// 行业树模型
        /// </summary>
        public List<TalentVirtualPostOneTree>? Rows { get; set; }
    }

    /// <summary>
    /// 获取行业集合树状列表（一级）
    /// </summary>
    public class TalentVirtualPostOneTree
    {
        /// <summary>
        /// 行业id
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 行业名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 父级id
        /// </summary>
        public string ParentId { get; set; } = string.Empty;

        /// <summary>
        /// 二级列表
        /// </summary>
        public List<TalentVirtualPostTwoTree>? Children { get; set; }
    }

    /// <summary>
    /// 获取行业集合树状列表（二级）
    /// </summary>
    public class TalentVirtualPostTwoTree
    {
        /// <summary>
        /// 行业id
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 行业名称
        /// </summary>
        public string Name { get; set; } = string.Empty;
    }
    #endregion

    #region 上传简历解析简历相关模型
    /// <summary>
    /// 上传简历接收模型
    /// </summary>
    public class UpLoadResumeRequest
    {
        public List<UpLoadResumeModel>? resumes { get; set; }

        public string? HrId { get; set; }
    }

    /// <summary>
    /// 上传简历模型
    /// </summary>
    public class UpLoadResumeModel
    {
        /// <summary>
        /// 文件地址
        /// </summary>
        public string FileUrl { get; set; } = string.Empty;

        /// <summary>
        /// 文件名称
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 文件扩展名
        /// </summary>
        public TalentUpLoadFileExtension FileExtension { get; set; }

        /// <summary>
        /// 重复类型
        /// </summary>
        public TalentUpLoadRepeatType RepeatType { get; set; }
    }

    /// <summary>
    /// 解析原始简历接收模型
    /// </summary>
    public class AnalyzeOriginalResumeRequest
    {
        /// <summary>
        /// 人才库id
        /// </summary>
        public string? VirtualId { get; set; }
    }

    /// <summary>
    /// 获取虚拟人才库上传简历记录接收模型
    /// </summary>
    public class GetTalentUploadRecordListRequest : QueryRequest
    {
        /// <summary>
        /// 文件类型（0word，10excel）
        /// </summary>
        public int FileType { get; set; }
    }

    /// <summary>
    /// 获取虚拟人才库上传简历记录返回模型
    /// </summary>
    public class GetTalentUploadRecordListResponse : QueryResponse
    {
        public List<GetTalentUploadRecordListModel> Rows { get; set; } = default!;
    }

    /// <summary>
    /// 获取虚拟人才库上传简历记录模型
    /// </summary>
    public class GetTalentUploadRecordListModel
    {
        /// <summary>
        /// 记录id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// hr名称
        /// </summary>
        public string? HrName { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// 简历总数量
        /// </summary>
        public int? TotalNumber { get; set; }

        /// <summary>
        /// 失败数量
        /// </summary>
        public int? FailNumber { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        public int? QualifiedNumber { get; set; }

        /// <summary>
        /// 重复数量
        /// </summary>
        public int? RepeatNumber { get; set; }

        /// <summary>
        /// 入库数量
        /// </summary>
        public int? WarehousingNumber { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }
    }

    /// <summary>
    /// 获取虚拟人才库上传简历记录（子表）接收模型
    /// </summary>
    public class GetTalentUploadRecordSubListRequest : QueryRequest
    {
        /// <summary>
        /// 记录id
        /// </summary>
        public string? RecordId { get; set; }
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool? IsSuccess { get; set; }
    }

    /// <summary>
    /// 获取虚拟人才库上传简历记录（子表）返回模型
    /// </summary>
    public class GetTalentUploadRecordSubListResponse : QueryResponse
    {
        public List<GetTalentUploadRecordSubListModel> Rows { get; set; } = default!;
    }

    /// <summary>
    /// 获取虚拟人才库上传简历记录（子表）模型
    /// </summary>
    public class GetTalentUploadRecordSubListModel
    {
        /// <summary>
        /// 虚拟人才库id
        /// </summary>
        public string? VirtualId { get; set; }

        /// <summary>
        /// 虚拟人才库简历姓名
        /// </summary>
        public string? VirtualName { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// 是否入库
        /// </summary>
        public bool? IsWarehousing { get; set; }

        /// <summary>
        /// 是否重复
        /// </summary>
        public bool? IsRepeat { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool? IsSuccess { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 简历完整度
        /// </summary>
        public int Perfection { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }
    }

    #endregion

    #region 人才库标签相关模型
    /// <summary>
    /// 标签列表返回模型
    /// </summary>
    public class TalentLabelListResponse
    {
        public List<TalentLabelModel> Rows { get; set; } = default!;
    }

    /// <summary>
    /// 标签模型
    /// </summary>
    public class TalentLabelModel
    {
        /// <summary>
        /// 标签id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 标签名称
        /// </summary>
        public string? Name { get; set; }
    }

    /// <summary>
    /// 添加标签接收模型
    /// </summary>
    public class AddTalentLabelRequest
    {
        public string LabelName { get; set; } = default!;
    }

    /// <summary>
    /// 虚拟人才库标签列表返回模型
    /// </summary>
    public class TalentVirtualLabelResponse
    {
        public List<TalentVirtualLabelModel> Rows { get; set; } = default!;
    }

    /// <summary>
    /// 虚拟人才库标签模型
    /// </summary>
    public class TalentVirtualLabelModel
    {
        /// <summary>
        /// 标签id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 标签名称
        /// </summary>
        public string? Name { get; set; }
    }

    /// <summary>
    /// 变更虚拟人才库标签
    /// </summary>
    public class EditTalentVirtualLabelRequest
    {
        /// <summary>
        /// 虚拟人才库id
        /// </summary>
        public string? VirtualId { get; set; }

        /// <summary>
        /// 标签id
        /// </summary>
        public List<string> DicLabelId { get; set; } = new List<string>();
    }
    #endregion
}
