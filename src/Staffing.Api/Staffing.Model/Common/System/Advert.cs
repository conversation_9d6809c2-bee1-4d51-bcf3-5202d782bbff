﻿using Staffing.Entity.Staffing;

namespace Staffing.Model.Common.System
{
    /// <summary>
    /// 广告列表输出
    /// </summary>
    public class AdvertListRecord
    {
        /// <summary>
        /// 轮播图
        /// </summary>
        public string? ImageUrl { get; set; }

        /// <summary>
        /// 链接地址
        /// </summary>
        public string? LinkUrl { get; set; }

        /// <summary>
        /// 链接方式
        /// </summary>
        public Advert_LinkType LinkType { get; set; }
    }
    public class AdvertListResponse
    {
        public List<AdvertListRecord> Rows { get; set; } = default!;
    }
}
