﻿using System.Text.Json.Serialization;
using Config.CommonModel;
using Config.CommonModel.Business;

namespace Staffing.Model.Hr.User;

public class GetHrMessageSum
{
    /// <summary>
    /// 消息类型
    /// </summary>
    public HrMsgType? MsgType { get; set; }

    /// <summary>
    /// 设置已读
    /// </summary>
    public bool SetRead { get; set; }
}

public class GetHrMessageSumResponse
{
    /// <summary>
    /// 是否有未读消息
    /// </summary>
    public bool NewMsg { get; set; }

    /// <summary>
    /// 上次读消息时间戳（用来比较消息是否已读）
    /// </summary>
    public long ReadTime { get; set; }
}

public class GetHrMessage : QueryRequest
{
    /// <summary>
    /// 消息类型
    /// </summary>
    public HrMsgType? MsgType { get; set; }

    /// <summary>
    /// 设置已读
    /// </summary>
    public bool SetRead { get; set; }
}

public class GetHrMessageResponse : QueryResponse
{
    public List<GetHrMessageInfo> Rows { get; set; } = new List<GetHrMessageInfo>();

    // /// <summary>
    // /// 上次读消息时间
    // /// </summary>
    // public long ReadTime { get; set; }
}

public class GetHrMessageInfo
{
    public string? Id { get; set; }

    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public HrMsgType? Type { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 数据json
    /// </summary>
    [JsonIgnore]
    public string? DataStr { get; set; }

    /// <summary>
    /// json信息
    /// </summary>
    public object? Data { get; set; }

    /// <summary>
    /// 消息的时间戳（用来比较消息是否已读）
    /// </summary>
    public long TimeStamp { get; set; }

    public DateTime? CreatedTime { get; set; }
}