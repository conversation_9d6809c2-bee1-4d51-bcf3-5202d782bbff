﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 合同机构-企业关系表
/// </summary>
[Table("contract_org_ent")]
public class Contract_Org_Ent
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 企业Id
    /// </summary>
    public string EntId { get; set; } = null!;

    /// <summary>
    /// 机构账号Id
    /// </summary>
    public string OrgId { get; set; } = null!;

    public string CreatedBy { get; set; } = null!;
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Contract_Org Contract_Org { get; set; } = default!;
    public User_Hr CreatedUser { get; set; } = default!;
}
