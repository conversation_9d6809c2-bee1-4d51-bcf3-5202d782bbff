﻿using EntityFrameworkCore.AutoHistory.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using ServiceStack.Text;
using Staffing.Entity.Staffing;
using Yitter.IdGenerator;

namespace Staffing.Entity;

public partial class StaffingContext : DbContext
{
    public StaffingContext(DbContextOptions<StaffingContext> options)
        : base(options)
    {

    }

    //主
    public virtual DbSet<User> User { get; set; } = default!;
    public virtual DbSet<User_Extend> User_Extend { get; set; } = default!;
    public virtual DbSet<User_Num> User_Num { get; set; } = default!;

    public virtual DbSet<User_Hr_Data> User_Hr_Data { get; set; } = default!;

    public virtual DbSet<User_Channel_Source> User_Channel_Source { get; set; } = default!;
    public virtual DbSet<User_Login_Record> User_Login_Record { get; set; } = default!;
    public virtual DbSet<User_Hr> User_Hr { get; set; } = default!;
    public virtual DbSet<User_Hr_RecommendEnt> User_Hr_RecommendEnt { get; set; } = default!;
    public virtual DbSet<User_Qrcode> User_Qrcode { get; set; } = default!;
    public virtual DbSet<User_Seeker> User_Seeker { get; set; } = default!;

    public virtual DbSet<User_Seeker_Jd> User_Seeker_Jd { get; set; } = default!;
    public virtual DbSet<User_Balance> User_Balance { get; set; } = default!;
    public virtual DbSet<User_Balance_Record> User_Balance_Record { get; set; } = default!;
    public virtual DbSet<User_Withdraw> User_Withdraw { get; set; } = default!;
    public virtual DbSet<User_Hr_Audit> User_Hr_Audit { get; set; } = default!;
    // public virtual DbSet<User_Agent_Ent> User_Agent_Ent { get; set; } = default!;
    public virtual DbSet<User_Resume> User_Resume { get; set; } = default!;
    public virtual DbSet<User_Resume_Attach> User_Resume_Attach { get; set; } = default!;
    public virtual DbSet<User_Campus> User_Campus { get; set; } = default!;
    public virtual DbSet<User_Work> User_Work { get; set; } = default!;
    public virtual DbSet<User_Collect_Post> User_Collect_Post { get; set; } = default!;
    public virtual DbSet<User_Follow_Industry> User_Follow_Industry { get; set; } = default!;
    // public virtual DbSet<User_Seeker_Adviser> User_Seeker_Adviser { get; set; } = default!;
    public virtual DbSet<User_Seeker_Guidance> User_Seeker_Guidance { get; set; } = default!;
    public virtual DbSet<User_Sms> User_Sms { get; set; } = default!;
    public virtual DbSet<User_Sms_Record> User_Sms_Record { get; set; } = default!;
    // public virtual DbSet<User_Channel_Relation> User_Channel_Relation { get; set; } = default!;

    public virtual DbSet<WeChat> WeChat { get; set; } = default!;
    public virtual DbSet<User_QYWechat> User_QYWechat { get; set; } = default!;
    public virtual DbSet<User_DingDing> User_DingDing { get; set; } = default!;
    public virtual DbSet<DingDing> DingDing { get; set; } = default!;

    public virtual DbSet<User_Post_Visit> User_Post_Visit { get; set; } = default!;

    //dic
    public virtual DbSet<Dic_Region> Dic_Region { get; set; } = default!;
    public virtual DbSet<Dic_Post> Dic_Post { get; set; } = default!;
    public virtual DbSet<Dic_Industry> Dic_Industry { get; set; } = default!;
    public virtual DbSet<Dic_School> Dic_School { get; set; } = default!;
    public virtual DbSet<Dic_Major> Dic_Major { get; set; } = default!;
    public virtual DbSet<Dic_Welfare> Dic_Welfare { get; set; } = default!;
    public virtual DbSet<Dic_Cert> Dic_Cert { get; set; } = default!;
    public virtual DbSet<Dic_Talent_Label> Dic_Talent_Label { get; set; } = default!;
    public virtual DbSet<Dic_Talent_Industry> Dic_Talent_Industry { get; set; } = default!;
    public virtual DbSet<Dic_Talent_Post> Dic_Talent_Post { get; set; } = default!;
    public virtual DbSet<Dic_Recruit_Label> Dic_Recruit_Label { get; set; } = default!;
    public virtual DbSet<Dic_Base_Info> Dic_Base_Info { get; set; } = default!;

    //系统
    public virtual DbSet<Sys_Sms> Sys_Sms { get; set; } = default!;
    public virtual DbSet<Sys_Settings> Sys_Settings { get; set; } = default!;
    public virtual DbSet<Sys_Idempotency_Check> Sys_Idempotency_Check { get; set; } = default!;
    public virtual DbSet<Sys_Notice_System> Sys_Notice_System { get; set; } = default!;
    public virtual DbSet<Sys_Banner> Sys_Banner { get; set; } = default!;
    public virtual DbSet<Sys_App> Sys_App { get; set; } = default!;
    public virtual DbSet<Rpt_Daily_User> Rpt_Daily_User { get; set; } = default!;
    public virtual DbSet<Rpt_Szny_Contract> Rpt_Szny_Contract { get; set; } = default!;
    public virtual DbSet<Rpt_Szny_Project> Rpt_Szny_Project { get; set; } = default!;
    public virtual DbSet<Rpt_Ny_User> Rpt_Ny_User { get; set; } = default!;

    public virtual DbSet<Advert_Set> Advert_Set { get; set; } = default!;
    public virtual DbSet<File_Path_Of_Oss> File_Path_Of_Oss { get; set; } = default!;

    //企业
    public virtual DbSet<Enterprise> Enterprise { get; set; } = default!;
    public virtual DbSet<Enterprise_Business> Enterprise_Business { get; set; } = default!;
    public virtual DbSet<Enterprise_WhiteList> Enterprise_WhiteList { get; set; } = default!;
    public virtual DbSet<Enterprise_Org> Enterprise_Org { get; set; } = default!;
    public virtual DbSet<Enterprise_Org_User> Enterprise_Org_User { get; set; } = default!;
    public virtual DbSet<Enterprise_Role> Enterprise_Role { get; set; } = default!;
    public virtual DbSet<Enterprise_Relation> Enterprise_Relation { get; set; } = default!;
    public virtual DbSet<Enterprise_Merchant> Enterprise_Merchant { get; set; } = default!;
    public virtual DbSet<Ums_Complex_Upload> Ums_Complex_Upload { get; set; } = default!;
    public virtual DbSet<Ums_Cert> Ums_Cert { get; set; } = default!;

    //招聘流程
    public virtual DbSet<Recruit> Recruit { get; set; } = default!;
    public virtual DbSet<Recruit_Interview> Recruit_Interview { get; set; } = default!;
    public virtual DbSet<Recruit_Offer> Recruit_Offer { get; set; } = default!;
    public virtual DbSet<Recruit_Record> Recruit_Record { get; set; } = default!;
    public virtual DbSet<Recruit_Interviewer_Screen> Recruit_Interviewer_Screen { get; set; } = default!;
    public virtual DbSet<Recruit_Interviewer_Todo> Recruit_Interviewer_Todo { get; set; } = default!;
    public virtual DbSet<Recruit_Label> Recruit_Label { get; set; } = default!;
    public virtual DbSet<Recruit_Comment> Recruit_Comment { get; set; } = default!;


    //项目
    public virtual DbSet<Project> Project { get; set; } = default!;
    public virtual DbSet<Project_Extend> Project_Extend { get; set; } = default!;
    public virtual DbSet<Project_Interviewer> Project_Interviewer { get; set; } = default!;
    public virtual DbSet<Project_Region> Project_Region { get; set; } = default!;
    public virtual DbSet<Project_Funds> Project_Funds { get; set; } = default!;
    public virtual DbSet<Project_Contract> Project_Contract { get; set; } = default!;
    public virtual DbSet<Project_Funds_Transfers> Project_Funds_Transfers { get; set; } = default!;
    public virtual DbSet<Project_Team> Project_Team { get; set; } = default!;
    public virtual DbSet<Post_Bounty> Post_Bounty { get; set; } = default!;
    public virtual DbSet<Post_Bounty_Stage> Post_Bounty_Stage { get; set; } = default!;
    public virtual DbSet<Post_Settlement> Post_Settlement { get; set; } = default!;
    public virtual DbSet<Post_Settlement_Detail> Post_Settlement_Detail { get; set; } = default!;
    public virtual DbSet<Voucher> Voucher { get; set; } = default!;
    public virtual DbSet<Voucher_Detail> Voucher_Detail { get; set; } = default!;
    public virtual DbSet<Voucher_Settlement> Voucher_Settlement { get; set; } = default!;

    public virtual DbSet<Project_Team_Config> Project_Team_Config { get; set; } = default!;
    public virtual DbSet<Project_Hall> Project_Hall { get; set; } = default!;
    public virtual DbSet<Project_Member> Project_Member { get; set; } = default!;
    public virtual DbSet<Project_Survey> Project_Survey { get; set; } = default!;
    public virtual DbSet<Project_Member_Record> Project_Member_Record { get; set; } = default!;
    public virtual DbSet<Tasks> Tasks { get; set; } = default!;
    public virtual DbSet<Tasks_Detail> Tasks_Detail { get; set; } = default!;
    public virtual DbSet<Post> Post { get; set; } = default!;
    public virtual DbSet<Post_Extend> Post_Extend { get; set; } = default!;
    public virtual DbSet<Post_Team> Post_Team { get; set; } = default!;
    public virtual DbSet<Post_Profit_Stage> Post_Profit_Stage { get; set; } = default!;
    public virtual DbSet<Post_Interview_Config> Post_Interview_Config { get; set; } = default!;
    public virtual DbSet<Post_Interview_Date> Post_Interview_Date { get; set; } = default!;
    public virtual DbSet<Post_Team_Channel> Post_Team_Channel { get; set; } = default!;
    public virtual DbSet<Post_Team_Extend> Post_Team_Extend { get; set; } = default!;
    public virtual DbSet<Post_Team_Third_Jobid_Rel> Post_Team_Third_Jobid_Rel { get; set; } = default!;
    public virtual DbSet<Post_Delivery> Post_Delivery { get; set; } = default!;
    public virtual DbSet<Post_Complaint> Post_Complaint { get; set; } = default!;
    // public virtual DbSet<Project_Teambounty> Project_Teambounty { get; set; } = default!;
    // public virtual DbSet<Project_Teambounty_Stage> Project_Teambounty_Stage { get; set; } = default!;
    // public virtual DbSet<Project_Settlement> Project_Settlement { get; set; } = default!;
    public virtual DbSet<Post_Excellent> Post_Excellent { get; set; } = default!;
    public virtual DbSet<Project_Automatic> Project_Automatic { get; set; } = default!;

    //代招企业
    public virtual DbSet<Agent_Ent> Agent_Ent { get; set; } = default!;

    //合同
    public virtual DbSet<Contract> Contract { get; set; } = default!;
    public virtual DbSet<Contract_Doctemplate> Contract_Doctemplate { get; set; } = default!;
    public virtual DbSet<Contract_Doctemplate_Component> Contract_Doctemplate_Component { get; set; } = default!;
    public virtual DbSet<Contract_File> Contract_File { get; set; } = default!;
    public virtual DbSet<Contract_File_Component> Contract_File_Component { get; set; } = default!;
    public virtual DbSet<Contract_Org> Contract_Org { get; set; } = default!;
    public virtual DbSet<Contract_Org_Ent> Contract_Org_Ent { get; set; } = default!;
    public virtual DbSet<Contract_Seal> Contract_Seal { get; set; } = default!;
    public virtual DbSet<Contract_User> Contract_User { get; set; } = default!;

    //人才库
    public virtual DbSet<Talent_Platform> Talent_Platform { get; set; } = default!;
    public virtual DbSet<Talent_Platform_Comment> Talent_Platform_Comment { get; set; } = default!;
    public virtual DbSet<Talent_Virtual> Talent_Virtual { get; set; } = default!;
    public virtual DbSet<Talent_Virtual_Edu> Talent_Virtual_Edu { get; set; } = default!;
    public virtual DbSet<Talent_Virtual_Work> Talent_Virtual_Work { get; set; } = default!;
    public virtual DbSet<Talent_Virtual_Project> Talent_Virtual_Project { get; set; } = default!;
    public virtual DbSet<Talent_Virtual_Hope> Talent_Virtual_Hope { get; set; } = default!;
    public virtual DbSet<Talent_Virtual_Comment> Talent_Virtual_Comment { get; set; } = default!;
    public virtual DbSet<Talent_Upload_Record> Talent_Upload_Record { get; set; } = default!;
    public virtual DbSet<Recruit_Upload_Record> Recruit_Upload_Record { get; set; } = default!;
    public virtual DbSet<Talent_Upload_Recordsub> Talent_Upload_Recordsub { get; set; } = default!;
    // public virtual DbSet<Talent_Relation> Talent_Relation { get; set; } = default!;
    public virtual DbSet<Talent_Label> Talent_Label { get; set; } = default!;
    public virtual DbSet<Kuaishou_Talent_Infos> Kuaishou_Talent_Infos { get; set; } = default!;
    public virtual DbSet<Kuaishou_Hr_Talent_Relations> Kuaishou_Hr_Talent_Relations { get; set; } = default!;
    public virtual DbSet<Kuaishou_Tenant> Kuaishou_Tenant { get; set; } = default!;
    public virtual DbSet<Resume_Buffer> Resume_Buffer { get; set; } = default!;
    public virtual DbSet<Talent_Transfer> Talent_Transfer { get; set; } = default!;
    public virtual DbSet<Talent_Transfer_Record> Talent_Transfer_Record { get; set; } = default!;

    public virtual DbSet<Talent_Resume> Talent_Resume { get; set; } = default!;

    public virtual DbSet<Talent_Resume_Connect> Talent_Resume_Connect { get; set; } = default!;

    //token
    public virtual DbSet<Token_Refresh> Token_Refresh { get; set; } = default!;
    public virtual DbSet<Token_Access> Token_Access { get; set; } = default!;

    //消息
    public virtual DbSet<Msg_Words> Msg_Words { get; set; } = default!;
    public virtual DbSet<Msg_Notify_Hr> Msg_Notify_Hr { get; set; } = default!;
    public virtual DbSet<Msg_DingDingPush> Msg_DingDingPush { get; set; } = default!;

    //钉钉
    public virtual DbSet<Dd_Dept> Dd_Dept { get; set; } = default!;
    public virtual DbSet<Dd_User> Dd_User { get; set; } = default!;
    public virtual DbSet<Dd_Notify_Record> Dd_Notify_Record { get; set; } = default!;
    public virtual DbSet<Dd_User_Dept> Dd_User_Dept { get; set; } = default!;
    public virtual DbSet<Dingding_Process_Instancet> Dingding_Process_Instancet { get; set; } = default!;

    //积分
    // public virtual DbSet<User_NScore> User_NScore { get; set; } = default!;
    // public virtual DbSet<User_NScore_Record> User_NScore_Record { get; set; } = default!;
    // public virtual DbSet<User_Nscore_Goods> User_Nscore_Goods { get; set; } = default!;
    // public virtual DbSet<User_Nscore_Order> User_Nscore_Order { get; set; } = default!;
    public virtual DbSet<Sms_Tasks> Sms_Tasks { get; set; } = default!;
    public virtual DbSet<Sms_Tasks_Detail> Sms_Tasks_Detail { get; set; } = default!;
    public virtual DbSet<User_OpenId> User_OpenId { get; set; } = default!;
    public virtual DbSet<App> App { get; set; } = default!;
    public virtual DbSet<Abc> Abc { get; set; } = default!;

    //销帮帮
    public virtual DbSet<Xbb_Contract> Xbb_Contract { get; set; } = default!;
    public virtual DbSet<Xbb_User> Xbb_User { get; set; } = default!;
    // public virtual DbSet<Xbb_Xsjh> Xbb_Xsjh { get; set; } = default!;
    public virtual DbSet<Xbb_XiaoShouJh> Xbb_XiaoShouJh { get; set; } = default!;

    //数字诺亚
    public virtual DbSet<Ndn_Hr_Wallet_Details> Ndn_Hr_Wallet_Details { get; set; } = default!;
    public virtual DbSet<Ndn_Hr_Wallet_Transfers> Ndn_Hr_Wallet_Transfers { get; set; } = default!;
    public virtual DbSet<Ndn_Hr_Wallet> Ndn_Hr_Wallet { get; set; } = default!;
    public virtual DbSet<Ndn_Project_Invoice> Ndn_Project_Invoice { get; set; } = default!;
    public virtual DbSet<Ndn_Project_Transfer_Details> Ndn_Project_Transfer_Details { get; set; } = default!;
    public virtual DbSet<Ndn_Project_Transfers> Ndn_Project_Transfers { get; set; } = default!;
    // public virtual DbSet<Ndn_Service_Bonus> Ndn_Service_Bonus { get; set; } = default!;
    public virtual DbSet<Ndn_Project> Ndn_Project { get; set; } = default!;
    public virtual DbSet<Project_Survey> ProjectSurvey { get; set; } = default!;
    public virtual DbSet<Ndn_Books> Ndn_Books { get; set; } = default!;
    // public virtual DbSet<Ndn_Book_User> Ndn_Book_User { get; set; } = default!;
    public virtual DbSet<Ndn_Todo> Ndn_Todo { get; set; } = default!;

    //admin
    public virtual DbSet<Admin> Admin { get; set; } = default!;

    //渠道商
    public virtual DbSet<Qd_Hr_Channel> Qd_Hr_Channel { get; set; } = default!;
    public virtual DbSet<Qd_Hr_Channel_Qrcode> Qd_Hr_Channel_Qrcode { get; set; } = default!;

    public virtual DbSet<Tyc_Enterprise> Tyc_Enterprise { get; set; } = default!;
    public virtual DbSet<Wx_Group> Wx_Group { get; set; } = default!;
    public virtual DbSet<Wx_Group_View> Wx_Group_View { get; set; } = default!;
    public virtual DbSet<Nkp_Config_Info> Nkp_Config_Info { get; set; } = default!;

    public virtual DbSet<PrivacyNumber> PrivacyNumber { get; set; } = default!;
    //云生相关
    public virtual DbSet<YunSheng_PostId_Relation> YunSheng_JobId_Relation { get; set; } = default!;
    public virtual DbSet<YunSheng_ResumeId_Relation> YunSheng_ResumeId_Relation { get; set; } = default!;
    public virtual DbSet<Yunsheng_NewChat_Result> Yunsheng_NewChat_Result { get; set; } = default!;
    public virtual DbSet<YunSheng_Dic_Industry_Relation> YunSheng_Dic_Industry_Relation { get; set; } = default!;
    public virtual DbSet<YunSheng_Dic_Post_Relation> YunSheng_Dic_Post_Relation { get; set; } = default!;
    public virtual DbSet<YunSheng_ChatId_Relation> YunSheng_ChatId_Relation { get; set; } = default!;
    public virtual DbSet<YunSheng_Chat_Messages> YunSheng_Chat_Messages { get; set; } = default!;
    //三方平台相关
    public virtual DbSet<ThirdParty_Postid_Relation> ThirdParty_Postid_Relation { get; set; } = default!;
    public virtual DbSet<Thirdparty_Postid_Temp> Thirdparty_Postid_Temp { get; set; } = default!;
    public virtual DbSet<ThirdParty_Category_Dic_Relation> ThirdParty_Category_Dic_Relation { get; set; } = default!;
    // 抖音直播
    public virtual DbSet<Douyinzhibo> Douyinzhibo { get; set; } = default!;
    public virtual DbSet<Post_Team_Douyinzhibo> Post_Team_Douyinzhibo { get; set; } = default!;
    public virtual DbSet<Douyinzhibo_Post_Type> Douyinzhibo_Post_Type { get; set; } = default!;
    // public virtual DbSet<Post_Stock> Post_Stock { get; set; } = default!;
    // 证书

    public virtual DbSet<CertificateRegistrationForm> CertificateRegistrationForm { get; set; } = default!;

    public virtual DbSet<CertificateTable> CertificateTable { get; set; } = default!;

    public virtual DbSet<CertificateSpec> CertificateSpec { get; set; } = default!;


    public virtual DbSet<CertificateTrainingBatch> CertificateTrainingBatch { get; set; } = default!;


    public virtual DbSet<CertificateUserConfig> CertificateUserConfig { get; set; } = default!;

    public virtual DbSet<CertificateStudyRecord> CertificateStudyRecord { get; set; } = default!;

    //字典

    public virtual DbSet<DictData> DictData { get; set; } = default!;

    //企业微信
    public virtual DbSet<QYWechat_Employee> QYWechat_Employee { get; set; } = default!;
    public virtual DbSet<QYWechat_GroupChat> QYWechat_GroupChat { get; set; } = default!;
    public virtual DbSet<QYWechat_GroupMember> QYWechat_GroupMember { get; set; } = default!;
    
    
    //项目交易记录
    public virtual DbSet<Project_Transaction_Record> Project_Transaction_Record { get; set; } = default!;
    
    //子账户
    public virtual DbSet<SubAccount> SubAccount { get; set; } = default!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        //人员管理不允许看未审核通过的人
        // modelBuilder.Entity<User_Hr>().HasQueryFilter(b => b.Status == UserStatus.Active);

        modelBuilder.Entity<Project>().HasQueryFilter(b => !b.Deleted);
        modelBuilder.Entity<Project_Team>().HasQueryFilter(b => !b.Project.Deleted);
        modelBuilder.Entity<Post>().HasQueryFilter(b => !b.Deleted);
        modelBuilder.Entity<Post_Team>().HasQueryFilter(b => !b.Post.Deleted);
        modelBuilder.Entity<Talent_Platform>().HasQueryFilter(b => !b.Deleted);
        // modelBuilder.Entity<Project_Teambounty>().HasQueryFilter(b => !b.Deleted);
        modelBuilder.Entity<Post_Bounty>().HasQueryFilter(b => !b.Deleted);
        // modelBuilder.Entity<Project_Settlement>().HasQueryFilter(b => !b.Deleted);

        modelBuilder.Entity<CertificateTable>().HasQueryFilter(b => !b.Deleted);

        //启用AutoHistory
        modelBuilder.EnableAutoHistory();
        // modelBuilder.ApplyConfigurationsFromAssembly(typeof(StaffingContext).Assembly);

        modelBuilder.Entity<Sys_Idempotency_Check>()
            .HasKey(t => new
            {
                t.Key,
                t.Type
            });

        modelBuilder.Entity<Dd_User_Dept>()
            .HasOne(x => x.Dd_User)
            .WithMany(x => x.Dd_User_Dept)
            .HasForeignKey(x => x.DdUserId);

        modelBuilder.Entity<Dd_User_Dept>()
            .HasOne(x => x.Dd_Dept)
            .WithMany()
            .HasForeignKey(x => x.DdDeptId);

        modelBuilder.Entity<Dd_Notify_Record>()
            .HasOne(x => x.User_Hr)
            .WithMany()
            .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<User>()
            .HasOne(x => x.Dd_User)
            .WithOne(x => x.User).IsRequired(false)
            .HasForeignKey<User>(x => x.Mobile)
            .HasPrincipalKey<Dd_User>(x => x.Mobile);

        modelBuilder.Entity<Sms_Tasks_Detail>()
            .HasOne(x => x.Sms_Tasks)
            .WithMany()
            .HasForeignKey(x => x.TaskId);

        modelBuilder.Entity<User_Hr>()
            .HasOne(x => x.User)
            .WithOne()
            .HasForeignKey<User_Hr>(x => x.UserId);

        modelBuilder.Entity<User_Seeker>()
            .HasOne(x => x.User)
            .WithOne(x => x.User_Seeker)
            .HasForeignKey<User_Seeker>(x => x.UserId);

        modelBuilder.Entity<User_Hr>()
            .HasOne(x => x.User)
            .WithOne(x => x.User_Hr)
            .HasForeignKey<User_Hr>(x => x.UserId);

        modelBuilder.Entity<User>()
            .HasOne(x => x.User_Extend)
            .WithOne(x => x.User)
            .HasForeignKey<User_Extend>(x => x.UserId);

        modelBuilder.Entity<User>()
            .HasOne(x => x.User_Num)
            .WithOne(x => x.User)
            .HasForeignKey<User_Num>(x => x.UserId);

        modelBuilder.Entity<User_Hr>()
        .HasOne(x => x.User_Hr_Data)
        .WithOne(x => x.User_Hr)
        .HasForeignKey<User_Hr_Data>(x => x.UserId);

        modelBuilder.Entity<User_Seeker>()
           .HasOne(x => x.User_Num)
           .WithOne()
           .HasForeignKey<User_Seeker>(x => x.UserId);

        modelBuilder.Entity<User_Hr>()
            .HasOne(x => x.User_Num)
            .WithOne()
            .HasForeignKey<User_Hr>(x => x.UserId);

        modelBuilder.Entity<User_Hr>()
            .HasOne(x => x.User_Extend)
            .WithOne()
            .HasForeignKey<User_Extend>(x => x.UserId);

        modelBuilder.Entity<User_Seeker>()
            .HasOne(x => x.User_Extend)
            .WithOne()
            .HasForeignKey<User_Extend>(x => x.UserId);

        modelBuilder.Entity<User_Hr>()
            .HasOne(x => x.User_Sms)
            .WithOne().IsRequired(false)
            .HasForeignKey<User_Hr>(x => x.UserId);

        modelBuilder.Entity<User_Extend>()
            .HasOne(x => x.Adviser)
            .WithMany()
            .HasForeignKey(x => x.AdviserId);

        modelBuilder.Entity<User_Hr>()
            .HasOne(x => x.Enterprise)
            .WithMany()
            .HasForeignKey(x => x.EntId);

        modelBuilder.Entity<User_OpenId>()
            .HasOne(x => x.User)
            .WithMany(x => x.User_OpenId)
            .HasForeignKey(x => x.UserId);

        modelBuilder.Entity<Dd_Dept>()
            .HasOne(x => x.Parent)
            .WithMany()
            .HasForeignKey(x => x.ParentId);

        modelBuilder.Entity<User_OpenId>()
            .HasOne(x => x.User_Seeker)
            .WithMany()
            .HasForeignKey(x => x.UserId);

        modelBuilder.Entity<User_Balance>()
            .HasOne(x => x.User)
            .WithOne()
            .HasForeignKey<User_Balance>(x => x.UserId);

        modelBuilder.Entity<User_Seeker>()
            .HasOne(x => x.User_Balance)
            .WithOne()
            .HasForeignKey<User_Seeker>(x => x.UserId);

        modelBuilder.Entity<User_Withdraw>()
            .HasOne(x => x.User)
            .WithMany(x => x.User_Withdraw)
            .HasForeignKey(x => x.UserId);

        // modelBuilder.Entity<User_NScore_Record>()
        //     .HasOne(x => x.User)
        //     .WithMany(x => x.User_NScore_Record)
        //     .HasForeignKey(x => x.UserId);

        // modelBuilder.Entity<User_NScore_Record>()
        //     .HasOne(x => x.User_Hr)
        //     .WithMany()
        //     .HasForeignKey(x => x.UserId);

        modelBuilder.Entity<User_Withdraw>()
            .HasOne(x => x.User_Seeker)
            .WithMany(x => x.User_Withdraw)
            .HasForeignKey(x => x.UserId);

        modelBuilder.Entity<User_Balance_Record>()
            .HasOne(x => x.User_Seeker)
            .WithMany(x => x.User_Balance_Record)
            .HasForeignKey(x => x.UserId);

        modelBuilder.Entity<User_Hr_Audit>()
            .HasOne(x => x.User_Hr)
            .WithMany()
            .HasForeignKey(x => x.UserId);

        modelBuilder.Entity<User_Hr_Audit>()
            .HasOne(x => x.Enterprise)
            .WithMany()
            .HasForeignKey(x => x.EntId);

        modelBuilder.Entity<Post_Delivery>()
            .HasOne(x => x.User_Channel_Source)
            .WithMany()
            .HasForeignKey(x => x.ChannelSource);

        // modelBuilder.Entity<User_Hr_RecommendEnt>()
        //     .HasOne(x => x.Xbb_Creator)
        //     .WithMany()
        //     .HasForeignKey(x => x.XbbXsjhUId);

        modelBuilder.Entity<Post_Interview_Date>()
            .HasOne(x => x.Post)
            .WithMany(x => x.Post_Interview_Date)
            .HasForeignKey(x => x.PostId);

        modelBuilder.Entity<Post_Interview_Config>()
            .HasOne(x => x.Project_Interviewer)
            .WithMany()
            .HasForeignKey(x => x.InterviewerId);

        modelBuilder.Entity<User_Hr_RecommendEnt>()
            .HasOne(x => x.User_Hr)
            .WithMany()
            .HasForeignKey(x => x.AdviserId);

        modelBuilder.Entity<User_Seeker_Guidance>()
            .HasOne(x => x.User_Hr)
            .WithMany()
            .HasForeignKey(x => x.AdviserId);

        modelBuilder.Entity<User_Seeker_Guidance>()
            .HasOne(x => x.User_Seeker)
            .WithMany()
            .HasForeignKey(x => x.SeekerId);

        modelBuilder.Entity<User>()
            .HasOne(x => x.User_Resume)
            .WithOne(x => x.User)
            .HasForeignKey<User>(x => x.UserId);

        // modelBuilder.Entity<Project_Settlement>()
        //     .HasOne(x => x.User_Hr)
        //     .WithMany()
        //     .HasForeignKey(x => x.UserId);

        modelBuilder.Entity<Ndn_Hr_Wallet_Details>()
            .HasOne(x => x.Ndn_Hr_Wallet)
            .WithMany()
            .HasForeignKey(x => x.UserId);

        // modelBuilder.Entity<Ndn_Service_Bonus>()
        //     .HasOne(x => x.Project_Settlement)
        //     .WithMany()
        //     .HasForeignKey(x => x.SettlementId);

        // modelBuilder.Entity<Ndn_Service_Bonus>()
        //     .HasOne(x => x.Ndn_Project_Transfers)
        //     .WithMany()
        //     .HasForeignKey(x => x.TransferId);

        modelBuilder.Entity<Ndn_Project_Invoice>()
            .HasOne(x => x.Ndn_Project_Transfer_Details)
            .WithMany()
            .HasForeignKey(x => x.TransferStepId);

        modelBuilder.Entity<Ndn_Project_Transfers>()
           .HasOne(x => x.Project_From)
           .WithMany()
           .HasForeignKey(x => x.FromProjectId);

        modelBuilder.Entity<Ndn_Project_Transfers>()
           .HasOne(x => x.Project_To)
           .WithMany()
           .HasForeignKey(x => x.ToProjectId);

        modelBuilder.Entity<Ndn_Project_Transfer_Details>()
           .HasOne(x => x.Project_From)
           .WithMany()
           .HasForeignKey(x => x.FromProjectId);

        modelBuilder.Entity<Ndn_Project_Transfer_Details>()
           .HasOne(x => x.Project_To)
           .WithMany()
           .HasForeignKey(x => x.ToProjectId);

        modelBuilder.Entity<Ndn_Project_Transfer_Details>()
            .HasOne(x => x.Ndn_Project_Transfers)
            .WithMany()
            .HasForeignKey(x => x.TransferId);

        modelBuilder.Entity<User_Campus>()
            .HasOne(x => x.User_Resume)
            .WithMany(x => x.User_Campus)
            .HasForeignKey(x => x.UserId);

        modelBuilder.Entity<User_Work>()
            .HasOne(x => x.User_Resume)
            .WithMany(x => x.User_Work)
            .HasForeignKey(x => x.UserId);

        modelBuilder.Entity<Dic_Post>()
            .HasOne(x => x.Parent)
            .WithMany()
            .HasForeignKey(x => x.ParentId);

        modelBuilder.Entity<Dic_Cert>()
            .HasOne(x => x.Parent)
            .WithMany()
            .HasForeignKey(x => x.ParentId);

        modelBuilder.Entity<Dic_Industry>()
            .HasOne(x => x.Parent)
            .WithMany()
            .HasForeignKey(x => x.ParentId);

        modelBuilder.Entity<User_Hr>()
            .HasOne(x => x.Enterprise_Role)
            .WithMany()
            .HasForeignKey(x => x.RoleId);

        modelBuilder.Entity<User_Resume_Attach>()
            .HasOne(x => x.User_Seeker)
            .WithOne(x => x.User_Resume_Attach)
            .HasForeignKey<User_Resume_Attach>(x => x.UserId);

        modelBuilder.Entity<User_Collect_Post>()
            .HasOne(x => x.User_Seeker)
            .WithMany()
            .HasForeignKey(x => x.SeekerId);

        modelBuilder.Entity<User_Collect_Post>()
            .HasOne(x => x.User_Hr)
            .WithMany()
            .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<Post_Team>()
            .HasOne(x => x.Post_Excellent)
            .WithOne(x => x.Post_Team).IsRequired(false)
            .HasForeignKey<Post_Excellent>(x => x.TeamPostId);

        modelBuilder.Entity<User_Collect_Post>()
            .HasOne(x => x.Post_Team)
            .WithMany()
            .HasForeignKey(x => x.TeamPostId);

        modelBuilder.Entity<User_Follow_Industry>()
            .HasOne(x => x.User_Seeker)
            .WithMany()
            .HasForeignKey(x => x.SeekerId);

        // modelBuilder.Entity<User_Seeker_Adviser>()
        //     .HasOne(x => x.User_Hr)
        //     .WithMany()
        //     .HasForeignKey(x => x.AdviserId);

        // modelBuilder.Entity<User_Seeker_Adviser>()
        //     .HasOne(x => x.User_Seeker)
        //     .WithMany()
        //     .HasForeignKey(x => x.SeekerId);

        modelBuilder.Entity<User_Follow_Industry>()
            .HasOne(x => x.User_Hr)
            .WithMany()
            .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<Enterprise_Role>()
            .HasOne(x => x.Creator_User)
            .WithMany()
            .HasForeignKey(x => x.Creator);

        modelBuilder.Entity<Enterprise_Org_User>()
            .HasOne(x => x.Enterprise_Org)
            .WithMany()
            .HasForeignKey(x => x.OrgId);

        modelBuilder.Entity<Enterprise_Org_User>()
            .HasOne(x => x.User_Hr)
            .WithMany()
            .HasForeignKey(x => x.UserId);

        modelBuilder.Entity<Enterprise_Relation>()
            .HasOne(x => x.Enterprise)
            .WithMany()
            .HasForeignKey(x => x.EntId);

        modelBuilder.Entity<Enterprise_Relation>()
            .HasOne(x => x.GroupEnterprise)
            .WithMany()
            .HasForeignKey(x => x.GroupEntId);

        modelBuilder.Entity<Recruit>()
           .HasOne(x => x.User_Hr)
           .WithMany()
           .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<Recruit>()
            .HasOne(x => x.User_Seeker)
            .WithMany()
            .HasForeignKey(x => x.SeekerId);

        modelBuilder.Entity<Recruit>()
            .HasOne(x => x.FollowerUser)
            .WithMany()
            .IsRequired(false)
            .HasForeignKey(x => x.FollowerId);

        modelBuilder.Entity<Recruit_Interview>()
            .HasOne(x => x.Recruit)
            .WithMany(x => x.Recruit_Interview)
            .HasForeignKey(x => x.RecruitId);

        modelBuilder.Entity<Recruit_Offer>()
           .HasOne(x => x.Recruit)
           .WithMany(x => x.Recruit_Offer)
           .HasForeignKey(x => x.RecruitId);

        modelBuilder.Entity<Recruit_Record>()
            .HasOne(x => x.Recruit)
            .WithMany(x => x.Recruit_Record)
            .HasForeignKey(x => x.RecruitId);

        modelBuilder.Entity<Recruit_Record>()
            .HasOne(x => x.Recruit_Interviewer_Screen)
            .WithMany()
            .HasForeignKey(x => x.InterviewerScreenId);

        modelBuilder.Entity<Recruit_Record>()
            .HasOne(x => x.Recruit_Interview)
            .WithMany()
            .HasForeignKey(x => x.InterviewId);

        modelBuilder.Entity<Recruit_Record>()
            .HasOne(x => x.Recruit_Offer)
            .WithMany()
            .HasForeignKey(x => x.OfferId);
        modelBuilder.Entity<Recruit_Record>()
            .HasOne(x => x.User_Hr)
            .WithOne()
            .HasForeignKey<Recruit_Record>(x => x.Creator);

        modelBuilder.Entity<Enterprise_Org>()
            .HasOne(x => x.Parent)
            .WithMany()
            .HasForeignKey(x => x.ParentId);

        modelBuilder.Entity<Enterprise_Org_User>()
            .HasOne(x => x.User_Hr)
            .WithMany(x => x.Enterprise_Org_User)
            .HasForeignKey(x => x.UserId);

        modelBuilder.Entity<Enterprise_Org>()
            .HasOne(x => x.Enterprise)
            .WithMany()
            .HasForeignKey(x => x.EntId);

        modelBuilder.Entity<Enterprise>()
            .HasOne(x => x.Enterprise_Business)
            .WithMany()
            .HasForeignKey(x => x.Name)
            .HasPrincipalKey(x => x.Name);

        modelBuilder.Entity<Agent_Ent>()
            .HasOne(x => x.Dic_Industry)
            .WithMany()
            .HasForeignKey(x => x.Industry);

        modelBuilder.Entity<Agent_Ent>()
            .HasOne(x => x.Enterprise_Business)
            .WithMany()
            .HasForeignKey(x => x.Name)
            .HasPrincipalKey(x => x.Name);

        modelBuilder.Entity<Project_Region>()
            .HasOne(x => x.Project)
            .WithMany()
            .HasForeignKey(x => x.ProjectId);

        modelBuilder.Entity<Project_Contract>()
            .HasOne(x => x.Project)
            .WithMany()
            .HasForeignKey(x => x.ProjectId);

        modelBuilder.Entity<Project_Contract>()
            .HasOne(x => x.user_Hr)
            .WithMany()
            .HasForeignKey(x => x.CreatedBy);

        modelBuilder.Entity<Project_Member>()
            .HasOne(x => x.Project)
            .WithMany()
            .HasForeignKey(x => x.ProjectId);

        modelBuilder.Entity<Project_Member_Record>()
            .HasOne(x => x.Project)
            .WithMany()
            .HasForeignKey(x => x.ProjectId);

        modelBuilder.Entity<Tasks_Detail>()
            .HasOne(x => x.Tasks)
            .WithMany()
            .HasForeignKey(x => x.TaskId);

        modelBuilder.Entity<Tasks>()
            .HasOne(x => x.Project)
            .WithMany()
            .HasForeignKey(x => x.TargetId);

        modelBuilder.Entity<Project>()
            .HasOne(x => x.Project_Extend)
            .WithOne()
            .HasForeignKey<Project>(x => x.ProjectId);

        modelBuilder.Entity<Project>()
            .HasOne(x => x.Project_Survey)
            .WithOne(x => x.Project)
            .HasForeignKey<Project_Survey>(x => x.ProjectId);

        modelBuilder.Entity<Project_Interviewer>()
            .HasOne(x => x.Project)
            .WithMany()
            .HasForeignKey(x => x.ProjectId);

        modelBuilder.Entity<Project_Team>()
            .HasOne(x => x.Project)
            .WithMany(x => x.Project_Team)
            .HasForeignKey(x => x.ProjectId);

        // modelBuilder.Entity<Project_Team_Config>()
        //     .HasOne(x => x.ProjectTeam)
        //     .WithOne(x => x.ProjectTeamConfig)
        //     .HasForeignKey<Project_Team_Config>(x => x.TeamProjectId);

        modelBuilder.Entity<Project_Region>()
            .HasOne(x => x.Project)
            .WithMany(x => x.Project_Region)
            .HasForeignKey(x => x.ProjectId);

        modelBuilder.Entity<Project_Region>()
          .HasOne(x => x.Dic_Region)
          .WithMany()
          .HasForeignKey(x => x.RegionId);

        modelBuilder.Entity<Project_Team>()
            .HasOne(x => x.User_Hr)
            .WithMany()
            .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<Project_Hall>()
            .HasOne(x => x.Project)
            .WithMany()
            .HasForeignKey(x => x.ProjectId);

        modelBuilder.Entity<Project_Hall>()
            .HasOne(x => x.User_Hr)
            .WithMany()
            .HasForeignKey(x => x.HrId);
        //项目经理
        modelBuilder.Entity<Project>()
            .HasOne(x => x.User_Hr)
            .WithMany()
            .IsRequired(false)
            .HasForeignKey(x => x.HrId);
        //销售人员
        modelBuilder.Entity<Project>()
            .HasOne(x => x.SaleUser)
            .WithMany()
            .IsRequired(false)
            .HasForeignKey(x => x.SaleUserId);
        //财务人员
        modelBuilder.Entity<Project>()
            .HasOne(x => x.Settlement)
            .WithMany()
            .HasForeignKey(x => x.SettlementPersonId);

        modelBuilder.Entity<Project>()
            .HasOne(x => x.Agent_Ent)
            .WithMany()
            .HasForeignKey(x => x.AgentEntId);

        modelBuilder.Entity<Post>()
            .HasOne(x => x.Agent_Ent)
            .WithOne()
            .HasForeignKey<Post>(x => x.AgentEntId);

        modelBuilder.Entity<Post>()
            .HasOne(x => x.Post_Extend)
            .WithOne()
            .HasForeignKey<Post>(x => x.PostId);

        modelBuilder.Entity<Post>()
            .HasOne(x => x.Dic_Post)
            .WithMany()
            .HasForeignKey(x => x.Category);

        modelBuilder.Entity<Post>()
            .HasOne(x => x.Dic_Region)
            .WithMany()
            .HasForeignKey(x => x.RegionId);

        modelBuilder.Entity<Post>()
            .HasOne(x => x.Creator)
            .WithMany()
            .HasForeignKey(x => x.CreatorId).IsRequired(false);

        modelBuilder.Entity<Post>()
            .HasOne(x => x.Audit_User)
            .WithMany()
            .HasForeignKey(x => x.AuditUserId);

        modelBuilder.Entity<Post_Team>()
            .HasOne(x => x.Post)
            .WithMany()
            .HasForeignKey(x => x.PostId);

        // modelBuilder.Entity<Post_Team>()
        //     .HasOne(x => x.User_Hr)
        //     .WithMany()
        //     .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<Token_Refresh>()
            .HasOne(x => x.User)
            .WithMany()
            .HasForeignKey(x => x.UserId);

        modelBuilder.Entity<Token_Access>()
            .HasOne(x => x.User)
            .WithMany()
            .HasForeignKey(x => x.UserId);

        modelBuilder.Entity<Token_Access>()
            .HasOne(x => x.Token_Refresh)
            .WithMany()
            .HasForeignKey(x => x.RefreshTokenId);

        modelBuilder.Entity<Post_Team>()
            .HasOne(x => x.Project_Team)
            .WithMany(x => x.Post_Team)
            .HasForeignKey(x => x.TeamProjectId);

        modelBuilder.Entity<Post_Team>()
            .HasOne(x => x.Post_Team_Extend)
            .WithOne()
            .HasForeignKey<Post_Team>(x => x.TeamPostId);

        modelBuilder.Entity<Recruit>()
            .HasOne(x => x.Post_Delivery)
            .WithMany()
            .HasForeignKey(x => x.DeliveryId);

        modelBuilder.Entity<Post_Delivery>()
            .HasOne(x => x.User_Seeker)
            .WithMany()
            .HasForeignKey(x => x.SeekerId);

        modelBuilder.Entity<Post_Delivery>()
            .HasOne(x => x.User_Resume)
            .WithMany()
            .HasForeignKey(x => x.SeekerId);

        modelBuilder.Entity<Post_Delivery>()
            .HasOne(x => x.Post)
            .WithMany()
            .HasForeignKey(x => x.PostId);

        modelBuilder.Entity<Post_Delivery>()
            .HasOne(x => x.Post_Team)
            .WithMany()
            .HasForeignKey(x => x.TeamPostId);

        modelBuilder.Entity<Post_Delivery>()
            .HasOne(x => x.Qd_Hr_Channel)
            .WithMany()
            .HasForeignKey(x => x.ChannelId);

        modelBuilder.Entity<Post>()
            .HasOne(x => x.Project)
            .WithMany(x => x.Posts)
            .HasForeignKey(x => x.ProjectId);

        // modelBuilder.Entity<Xbb_Xsjh>()
        //    .HasOne(x => x.Xbb_Creator)
        //    .WithMany()
        //    .HasForeignKey(x => x.Creator);

        modelBuilder.Entity<Recruit_Interviewer_Screen>()
           .HasOne(x => x.Recruit)
           .WithMany(x => x.Recruit_Interviewer_Screen)
           .HasForeignKey(x => x.RecruitId);

        modelBuilder.Entity<Recruit_Interviewer_Screen>()
           .HasOne(x => x.Project_Interviewer)
           .WithMany()
           .HasForeignKey(x => x.InterviewerId);

        modelBuilder.Entity<Recruit_Interviewer_Todo>()
           .HasOne(x => x.Project_Interviewer)
           .WithMany()
           .HasForeignKey(x => x.InterviewerId);

        modelBuilder.Entity<Recruit_Interviewer_Todo>()
           .HasOne(x => x.Recruit_Interviewer_Screen)
           .WithMany()
           .HasForeignKey(x => x.RelationId);

        modelBuilder.Entity<Recruit_Interviewer_Todo>()
           .HasOne(x => x.Recruit_Interview)
           .WithMany()
           .HasForeignKey(x => x.RelationId);

        modelBuilder.Entity<Recruit_Interview>()
           .HasOne(x => x.User_Seeker)
           .WithMany()
           .HasForeignKey(x => x.SeekerId);

        modelBuilder.Entity<Recruit_Interview>()
           .HasOne(x => x.Project_Interviewer)
           .WithMany()
           .HasForeignKey(x => x.InterviewerId);

        modelBuilder.Entity<Post_Profit_Stage>()
           .HasOne(x => x.Post)
           .WithMany()
           .HasForeignKey(x => x.PostId);

        // modelBuilder.Entity<Post_Bounty_Stage>()
        //    .HasOne(x => x.Post_Bounty)
        //    .WithMany(x => x.Post_Bounty_Stage)
        //    .HasForeignKey(x => x.BountyId);

        // modelBuilder.Entity<Project_Teambounty>()
        //    .HasOne(x => x.Project)
        //    .WithMany()
        //    .HasForeignKey(x => x.ProjectId);

        // modelBuilder.Entity<Project_Teambounty>()
        //    .HasOne(x => x.Recruit)
        //    .WithMany()
        //    .HasForeignKey(x => x.RecruitId);

        // modelBuilder.Entity<Project_Teambounty>()
        //    .HasOne(x => x.Project_Team)
        //    .WithMany()
        //    .HasForeignKey(x => x.TeamProjectId);

        // modelBuilder.Entity<Project_Teambounty>()
        //    .HasOne(x => x.User_Seeker)
        //    .WithMany()
        //    .HasForeignKey(x => x.SeekerId);

        // modelBuilder.Entity<Project_Teambounty>()
        //    .HasOne(x => x.User_Hr)
        //    .WithMany()
        //    .HasForeignKey(x => x.HrId);

        // modelBuilder.Entity<Project_Teambounty>()
        //     .HasOne(x => x.SaleUser)
        //     .WithMany()
        //     .HasForeignKey(x => x.SaleUserId);

        // modelBuilder.Entity<Project_Teambounty>()
        //    .HasOne(x => x.Team_User_Hr)
        //    .WithMany()
        //    .HasForeignKey(x => x.TeamHrId);

        // modelBuilder.Entity<Project_Teambounty>()
        //    .HasOne(x => x.Post)
        //    .WithMany()
        //    .HasForeignKey(x => x.PostId);

        // modelBuilder.Entity<Project_Teambounty>()
        //    .HasOne(x => x.Post_Team)
        //    .WithMany()
        //    .HasForeignKey(x => x.TeamPostId);

        // modelBuilder.Entity<Project_Settlement>()
        //    .HasOne(x => x.Project_Teambounty)
        //    .WithMany(x => x.project_Settlements)
        //    .HasForeignKey(x => x.OrderId);

        modelBuilder.Entity<Recruit_Interviewer_Screen>()
           .HasOne(x => x.User_Seeker)
           .WithMany()
           .HasForeignKey(x => x.SeekerId);

        modelBuilder.Entity<Recruit_Interviewer_Todo>()
          .HasOne(x => x.Recruit)
          .WithMany(x => x.Recruit_Interviewer_Todo)
          .HasForeignKey(x => x.RecruitId);

        modelBuilder.Entity<Recruit>()
          .HasOne(x => x.Resume_Buffer)
          .WithMany()
          .HasForeignKey(x => x.ResumeBufferId);

        modelBuilder.Entity<Recruit>()
          .HasOne(x => x.Post_Bounty)
          .WithOne(x => x.Recruit)
          .HasForeignKey<Post_Bounty>(x => x.RecruitId);

        //modelBuilder.Entity<Recruit>()
        //  .HasOne(x => x.Project_Teambounty)
        //  .WithMany()
        //  .HasForeignKey(x => x.RecruitId);

        modelBuilder.Entity<Talent_Platform>()
          .HasOne(x => x.User_Hr)
          .WithMany()
          .HasForeignKey(x => x.HrId);


        modelBuilder.Entity<Talent_Platform>()
          .HasOne(x => x.User_Seeker)
          .WithMany()
          .HasForeignKey(x => x.SeekerId);

        modelBuilder.Entity<Talent_Virtual>()
          .HasOne(x => x.User_Hr)
          .WithMany()
          .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<Talent_Virtual>()
          .HasOne(x => x.User_Seeker)
          .WithMany()
          .HasForeignKey(x => x.SeekerId);

        modelBuilder.Entity<Talent_Virtual_Edu>()
          .HasOne(x => x.Talent_Virtual)
          .WithMany(x => x.Talent_Virtual_Edu)
          .HasForeignKey(x => x.VirtualId);

        modelBuilder.Entity<Talent_Virtual_Work>()
          .HasOne(x => x.Talent_Virtual)
          .WithMany(x => x.Talent_Virtual_Work)
          .HasForeignKey(x => x.VirtualId);

        modelBuilder.Entity<Talent_Virtual_Project>()
          .HasOne(x => x.Talent_Virtual)
          .WithMany(x => x.Talent_Virtual_Project)
          .HasForeignKey(x => x.VirtualId);

        modelBuilder.Entity<Talent_Virtual_Comment>()
         .HasOne(x => x.Talent_Virtual)
         .WithMany()
         .HasForeignKey(x => x.VirtualId);

        modelBuilder.Entity<Talent_Virtual_Comment>()
         .HasOne(x => x.User_Hr)
         .WithMany()
         .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<Talent_Virtual>()
           .HasOne(x => x.Talent_Virtual_Hope)
           .WithOne(x => x.Talent_Virtual)
           .HasForeignKey<Talent_Virtual>()
           .HasPrincipalKey<Talent_Virtual_Hope>(x => x.VirtualId);

        modelBuilder.Entity<User_Seeker>()
           .HasOne(x => x.User_Resume)
           .WithOne(x => x.User_Seeker)
           .HasForeignKey<User_Seeker>(x => x.UserId);

        // modelBuilder.Entity<User>()
        //    .HasOne(x => x.User_NScore)
        //    .WithOne(x => x.User).IsRequired(false)
        //    .HasForeignKey<User>(x => x.UserId);

        // modelBuilder.Entity<User_Seeker>()
        //    .HasOne(x => x.User_NScore)
        //    .WithOne(x => x.User_Seeker).IsRequired(false)
        //    .HasForeignKey<User_Seeker>(x => x.UserId);

        // modelBuilder.Entity<User_Nscore_Order>()
        //  .HasOne(x => x.User_Nscore_Goods)
        //  .WithMany()
        //  .HasForeignKey(x => x.GoodsId);

        // modelBuilder.Entity<User_Nscore_Order>()
        //  .HasOne(x => x.User_Seeker)
        //  .WithMany(x => x.User_Nscore_Order)
        //  .HasForeignKey(x => x.UserId);

        // modelBuilder.Entity<User_NScore_Record>()
        //  .HasOne(x => x.User_Seeker)
        //  .WithMany(x => x.User_NScore_Record)
        //  .HasForeignKey(x => x.UserId);

        modelBuilder.Entity<User_Withdraw>()
         .HasOne(x => x.User_Hr)
         .WithMany()
         .HasForeignKey(x => x.UserId);

        modelBuilder.Entity<Talent_Upload_Record>()
         .HasOne(x => x.User_Hr)
         .WithMany()
         .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<Talent_Upload_Recordsub>()
         .HasOne(x => x.Talent_Virtual)
         .WithMany()
         .HasForeignKey(x => x.VirtualId)
         .IsRequired(false);

        modelBuilder.Entity<Talent_Upload_Recordsub>()
        .HasOne(x => x.Talent_Upload_Record)
        .WithMany()
        .HasForeignKey(x => x.RecordId);

        modelBuilder.Entity<Post_Team_Channel>()
        .HasOne(x => x.Post_Team)
        .WithMany(x => x.Post_Team_Channel)
        .HasForeignKey(x => x.TeamPostId);

        //     modelBuilder.Entity<Talent_Relation>()
        //     .HasOne(x => x.Talent_Virtual)
        //     .WithMany()
        //     .HasForeignKey(x => x.TalentId);

        //     modelBuilder.Entity<Talent_Relation>()
        //    .HasOne(x => x.User_Seeker)
        //    .WithMany()
        //    .HasForeignKey(x => x.SeekerId);

        //     modelBuilder.Entity<Talent_Relation>()
        //    .HasOne(x => x.User_Hr)
        //    .WithMany()
        //    .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<Talent_Platform_Comment>()
       .HasOne(x => x.User_Hr)
       .WithMany()
       .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<Talent_Platform_Comment>()
       .HasOne(x => x.Talent_Platform)
       .WithMany()
       .HasForeignKey(x => x.PlatformId);

        modelBuilder.Entity<Talent_Label>()
       .HasOne(x => x.Talent_Virtual)
       .WithMany(x => x.Talent_Label)
       .HasForeignKey(x => x.VirtualId);

        modelBuilder.Entity<Talent_Label>()
       .HasOne(x => x.Talent_Platform)
       .WithMany(x => x.Talent_Label)
       .HasForeignKey(x => x.PlatformId);

        modelBuilder.Entity<Talent_Label>()
       .HasOne(x => x.Dic_Talent_Label)
       .WithMany()
       .HasForeignKey(x => x.DicLabelId);

        modelBuilder.Entity<Recruit_Label>()
       .HasOne(x => x.Dic_Recruit_Label)
       .WithMany()
       .HasForeignKey(x => x.DicLabelId);

        modelBuilder.Entity<Recruit_Label>()
       .HasOne(x => x.Recruit)
       .WithMany(x => x.Recruit_Label)
       .HasForeignKey(x => x.RecruitId);

        modelBuilder.Entity<Recruit_Comment>()
       .HasOne(x => x.User_Hr)
       .WithMany()
       .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<Recruit_Comment>()
       .HasOne(x => x.Recruit)
       .WithMany()
       .HasForeignKey(x => x.RecruitId);

        modelBuilder.Entity<Project>()
       .HasOne(x => x.Project_Automatic)
       .WithOne(x => x.Project)
       .HasForeignKey<Project_Automatic>(x => x.ProjectId);

        // modelBuilder.Entity<Project_Teambounty>()
        // .HasOne(x => x.Project)
        // .WithMany(x => x.Project_Teambounty)
        // .HasForeignKey(x => x.ProjectId);

        modelBuilder.Entity<User_DingDing>()
       .HasOne(x => x.User_Hr)
       .WithOne(x => x.User_DingDing).IsRequired(false)
       .HasForeignKey<User_Hr>(x => x.UserId);

        modelBuilder.Entity<User_QYWechat>()
       .HasOne(x => x.User_Hr)
       .WithOne(x => x.User_QYWechat).IsRequired(false)
       .HasForeignKey<User_Hr>(x => x.UserId);

        modelBuilder.Entity<QYWechat_GroupChat>()
        .HasOne(x => x.User_QYWechat)
        .WithMany(x => x.QYWechat_GroupChat).IsRequired(false)
        .HasForeignKey(x => x.OwnerId);

        modelBuilder.Entity<QYWechat_GroupMember>()
        .HasOne(x => x.QYWechat_GroupChat)
        .WithMany(x => x.QYWechat_GroupMember).IsRequired(false)
        .HasForeignKey(x => x.GroupChatID);

        modelBuilder.Entity<Contract_Org_Ent>()
        .HasOne(x => x.Contract_Org)
        .WithMany()
        .HasForeignKey(x => x.OrgId);

        modelBuilder.Entity<Contract_Org_Ent>()
        .HasOne(x => x.CreatedUser)
        .WithMany()
        .HasForeignKey(x => x.CreatedBy);

        modelBuilder.Entity<User_Post_Visit>()
        .HasOne(x => x.Dic_Post)
        .WithMany()
        .HasForeignKey(x => x.Post);

        modelBuilder.Entity<Qd_Hr_Channel>()
        .HasOne(x => x.User_Seeker)
        .WithMany(x => x.Qd_Hr_Channel)
        .HasForeignKey(x => x.ChannelUserId);

        modelBuilder.Entity<Qd_Hr_Channel>()
        .HasOne(x => x.User_Hr)
        .WithMany()
        .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<Kuaishou_Talent_Infos>()
        .HasOne(x => x.Post_Team_Third_Jobid_Rel)
        .WithOne(x => x.Kuaishou_Talent_Infos)
        .HasPrincipalKey<Kuaishou_Talent_Infos>("JobId", "PlatType")// Kuaishou_Talent_Infos 主表键
        .HasForeignKey<Post_Team_Third_Jobid_Rel>(x => new { x.JobId, x.PlatType });// Post_Team_Third_Jobid_Rel 从表外键

        modelBuilder.Entity<Kuaishou_Talent_Infos>()
        .HasOne(x => x.Post_Team)
        .WithMany()
        .HasForeignKey(x => x.SendTeamPostId);

        modelBuilder.Entity<Kuaishou_Hr_Talent_Relations>()
        .HasOne(x => x.User_Hr)
        .WithMany(x => x.Kuaishou_Hr_Talent_Relations)
        .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<Kuaishou_Hr_Talent_Relations>()
        .HasOne(x => x.Kuaishou_Talent_Infos)
        .WithOne(x => x.Kuaishou_Hr_Talent_Relations)
        .HasForeignKey<Kuaishou_Hr_Talent_Relations>(x => x.ApplicationId);

        modelBuilder.Entity<Post_Team_Third_Jobid_Rel>()
        .HasOne(x => x.Post_Team)
        .WithOne(x => x.Post_Team_Third_Jobid_Rel)
        .HasForeignKey<Post_Team_Third_Jobid_Rel>(x => x.TeamPostId);

        modelBuilder.Entity<Recruit_Upload_Record>()
         .HasOne(x => x.Post_Team)
         .WithMany()
         .HasForeignKey(x => x.TeamPostId);

        modelBuilder.Entity<Recruit_Upload_Record>()
         .HasOne(x => x.User_Hr)
         .WithMany()
         .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<Talent_Resume_Connect>()
         .HasOne(x => x.Talent_Resume)
         .WithMany(w => w.Talent_Resume_Connects)
         .HasForeignKey(x => x.ResumeId);

        modelBuilder.Entity<Talent_Resume_Connect>()
         .HasOne(x => x.User_Hr)
         .WithMany()
         .HasForeignKey(x => x.HrId);

        modelBuilder.Entity<Talent_Resume_Connect>()
         .HasOne(x => x.Post_Team)
         .WithMany()
         .HasForeignKey(x => x.TeamPostId);

        modelBuilder.Entity<Dic_Region>()
         .HasOne(x => x.Dic_Region_City)
         .WithMany()
         .HasForeignKey(x => x.City);

        modelBuilder.Entity<Talent_Resume>()
         .HasOne(x => x.User_Seeker)
         .WithMany()
         .HasForeignKey(x => x.SeekerId);

        modelBuilder.Entity<Post_Team_Douyinzhibo>()
        .HasOne(x => x.Post_Team)
        .WithMany(x => x.Post_Team_Douyinzhibo).IsRequired(false)
        .HasForeignKey(x => x.TeamPostId);

        // modelBuilder.Entity<Post_Stock>()
        // .HasOne(x => x.Post)
        // .WithMany()
        // .HasForeignKey(x => x.PostId);

        // modelBuilder.Entity<Post_Stock>()
        // .HasOne(x => x.Project_Teambounty)
        // .WithMany()
        // .HasForeignKey(x => x.OrderId);

        //证书
        modelBuilder.Entity<CertificateTable>()
            .HasMany(c => c.Specs)
            .WithOne(s => s.Certificate)
            .HasForeignKey(s => s.CertificateId);


        modelBuilder.Entity<CertificateTable>()
            .HasMany(c => c.TrainingBatch)
            .WithOne(s => s.Certificate)
            .HasForeignKey(s => s.CertificateId);

        modelBuilder.Entity<CertificateTable>()
            .HasOne(x => x.UserHr)
            .WithMany()
            .IsRequired(false)
            .HasForeignKey(x => x.Creator);


        modelBuilder.Entity<CertificateRegistrationForm>()
            .HasOne(x => x.CertificateSpec)
            .WithMany()
            .HasForeignKey(x => x.CertificateSpecsId)
            .IsRequired(false);

        modelBuilder.Entity<CertificateRegistrationForm>()
            .HasOne(x => x.CertificateTable)
            .WithMany()
            .HasForeignKey(x => x.CertificateId)
            .IsRequired(false);

        PropertConvert(modelBuilder);




    }

    private static void PropertConvert(ModelBuilder modelBuilder)
    {
        // 配置枚举与数据库字符串的转换
        modelBuilder.Entity<CertificateRegistrationForm>()
            .Property(e => e.Gender)
            .HasConversion<string>();

        modelBuilder.Entity<CertificateTable>()
            .Property(e => e.CertificateType)
            .HasConversion<string>();

        modelBuilder.Entity<CertificateTable>()
            .Property(e => e.EnterpriseGroupBuying)
            .HasConversion<string>();

        modelBuilder.Entity<CertificateTable>()
            .Property(e => e.TrainingBatchType)
            .HasConversion<string>();

        modelBuilder.Entity<User_Hr>()
        .Property(x => x.Powers)
        .HasJsonConversion();

        modelBuilder.Entity<Enterprise_Role>()
        .Property(x => x.Powers)
        .HasJsonConversion();

        // modelBuilder.Entity<Project>()
        // .Property(x => x.RegionData)
        // .HasJsonConversionNullable();

        modelBuilder.Entity<Post>()
        .Property(x => x.Tags)
        .HasJsonConversion();

        modelBuilder.Entity<Post>()
        .Property(x => x.Welfare)
        .HasJsonConversion();

        modelBuilder.Entity<Post>()
        .Property(x => x.WelfareCustom)
        .HasJsonConversion();

        modelBuilder.Entity<Post>()
        .Property(x => x.Highlights)
        .HasJsonConversion();

        modelBuilder.Entity<Post>()
        .Property(x => x.WorkingDays)
        .HasJsonConversion();

        modelBuilder.Entity<Post>()
        .Property(x => x.WorkingHours)
        .HasJsonConversion();

        modelBuilder.Entity<User_Resume>()
       .Property(x => x.Nature)
       .HasJsonConversion();

        modelBuilder.Entity<User_Resume>()
       .Property(x => x.Skill)
       .HasJsonConversion();

        modelBuilder.Entity<User_Resume>()
       .Property(x => x.Appearance)
       .HasJsonConversion();

        modelBuilder.Entity<User_Resume>()
       .Property(x => x.Certificate)
       .HasJsonConversion();

        modelBuilder.Entity<Enterprise_Merchant>()
       .Property(x => x.InvoiceInfo)
       .HasJsonConversionNullable();

        //人才库
        modelBuilder.Entity<Talent_Virtual>()
        .Property(x => x.Highlights)
        .HasJsonConversion();

        modelBuilder.Entity<Talent_Virtual>()
        .Property(x => x.Risks)
        .HasJsonConversion();

        modelBuilder.Entity<Talent_Virtual>()
        .Property(x => x.OtherLabel)
        .HasJsonConversion();

        modelBuilder.Entity<Talent_Virtual>()
       .Property(x => x.IndustryLabel)
       .HasJsonConversion();

        modelBuilder.Entity<Talent_Virtual>()
       .Property(x => x.PostLabel)
       .HasJsonConversion();

        modelBuilder.Entity<Talent_Virtual>()
        .Property(x => x.SkillAnalysis)
        .HasJsonConversion();

        //     modelBuilder.Entity<User_Nscore_Goods>()
        //    .Property(x => x.Content)
        //    .HasJsonConversion();

        //     modelBuilder.Entity<User_Nscore_Order>()
        //     .Property(x => x.Address)
        //     .HasJsonConversion();

        modelBuilder.Entity<User_Seeker>()
        .Property(x => x.Avatar)
        .HasImg300Conversion();

        modelBuilder.Entity<User_Hr>()
        .Property(x => x.Avatar)
        .HasImg300Conversion();

        modelBuilder.Entity<Agent_Ent>()
        .Property(x => x.LogoUrl)
        .HasImg300Conversion();

        modelBuilder.Entity<Enterprise>()
       .Property(x => x.LogoUrl)
       .HasImg300Conversion();

        modelBuilder.Entity<User_Hr>()
        .Property(x => x.Tags)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Kuaishou_Talent_Infos>()
        .Property(x => x.DataDynamicResumeInfo)
        .HasJsonConversion();

        modelBuilder.Entity<Kuaishou_Talent_Infos>()
        .Property(x => x.DataQualityLabel)
        .HasJsonConversion();

        modelBuilder.Entity<Kuaishou_Talent_Infos>()
        .Property(x => x.DataLocationCitys)
        .HasJsonConversion();

        modelBuilder.Entity<Kuaishou_Talent_Infos>()
        .Property(x => x.ExtInfoIntentionJob)
        .HasJsonConversion();

        modelBuilder.Entity<Kuaishou_Talent_Infos>()
        .Property(x => x.ExtInfoIntentionCity)
        .HasJsonConversion();

        modelBuilder.Entity<Project_Survey>()
        .Property(x => x.Content)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Project_Survey>()
            .Property(e => e.ContentLength)
            .ValueGeneratedOnAddOrUpdate()
            .Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);

        modelBuilder.Entity<Admin>()
        .Property(x => x.Powers)
        .HasJsonConversion();

        modelBuilder.Entity<Xbb_Contract>()
        .Property(x => x.ContractUrl)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Resume_Buffer>()
        .Property(x => x.Skills)
        .HasJsonConversion();

        modelBuilder.Entity<Resume_Buffer>()
        .Property(x => x.IndustryLabel)
        .HasJsonConversion();

        modelBuilder.Entity<Resume_Buffer>()
        .Property(x => x.PostLabel)
        .HasJsonConversion();

        modelBuilder.Entity<Resume_Buffer>()
        .Property(x => x.OtherLabel)
        .HasJsonConversion();

        modelBuilder.Entity<Resume_Buffer>()
        .Property(x => x.Hopes)
        .HasJsonConversion();

        modelBuilder.Entity<Resume_Buffer>()
        .Property(x => x.Educations)
        .HasJsonConversion();

        modelBuilder.Entity<Resume_Buffer>()
        .Property(x => x.Projects)
        .HasJsonConversion();

        modelBuilder.Entity<Resume_Buffer>()
        .Property(x => x.Works)
        .HasJsonConversion();

        modelBuilder.Entity<Recruit_Upload_Record>()
        .Property(x => x.ResumeInfo)
        .HasJsonConversion();

        modelBuilder.Entity<Enterprise>()
        .Property(x => x.Specific)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Tyc_Enterprise>()
        .Property(x => x.IndustryAll)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
        .Property(x => x.Edus)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
        .Property(x => x.Hopes)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
        .Property(x => x.Projects)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
        .Property(x => x.Works)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
        .Property(x => x.Products)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
        .Property(x => x.Honours)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
        .Property(x => x.Highlights)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
        .Property(x => x.Risks)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
        .Property(x => x.OtherLabel)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
       .Property(x => x.IndustryLabel)
       .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
       .Property(x => x.PostLabel)
       .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
        .Property(x => x.SkillAnalysis)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
        .Property(x => x.Certificates)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
        .Property(x => x.Tags)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
        .Property(x => x.HrIds)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Ndn_Books>()
        .Property(x => x.InvoiceInfo)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Ndn_Book_User>()
        .HasOne(b => b.Ndn_Books)
        .WithMany()
        .HasForeignKey(u => u.BookCode)
        .HasPrincipalKey(b => b.BookCode);

        modelBuilder.Entity<Ndn_Project_Invoice>()
        .Property(x => x.InvoiceUrl)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Ndn_Project_Transfer_Details>()
        .Property(x => x.InvoiceUrl)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Sys_Settings>()
        .Property(x => x.PlatformFull)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Sys_Settings>()
        .Property(x => x.PlatformNoSale)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Sys_Settings>()
        .Property(x => x.NoahFull)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Sys_Settings>()
        .Property(x => x.NoahNoSale)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Talent_Resume>()
        .Property(x => x.DeptIds)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Project_Contract>()
        .Property(x => x.Attachment)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Nkp_Config_Info>()
        .Property(x => x.Tags)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Dingding_Process_Instancet>()
        .Property(x => x.ApproverUserIds)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Dingding_Process_Instancet>()
        .Property(x => x.CcUserIds)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Dingding_Process_Instancet>()
        .Property(x => x.FormComponentValues)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Dingding_Process_Instancet>()
        .Property(x => x.OperationRecords)
        .HasJsonConversionNullable();

        modelBuilder.Entity<Dingding_Process_Instancet>()
        .Property(x => x.Tasks)
        .HasJsonConversionNullable();

        modelBuilder.Entity<CertificateSpec>()
        .Property(x => x.StudyList)
        .HasJsonConversionNullable();
    }
}

public class EntityTools
{
    public static string SnowflakeId()
    {
        return YitIdHelper.NextId().ToString();
    }
}

public static class EntityFrameworkExtend
{
    public static PropertyBuilder<T> HasJsonConversion<T>(this PropertyBuilder<T> propertyBuilder) where T : class, new()
    {
        var converter = new ValueConverter<T, string>
        (
            v => JsonSerializer.SerializeToString(v) ?? string.Empty,
            v => JsonSerializer.DeserializeFromString<T>(v) ?? new T()
        );
        var comparer = new ValueComparer<T>
        (
            (l, r) => JsonSerializer.SerializeToString(l) == JsonSerializer.SerializeToString(r),
            v => v == null ? 0 : JsonSerializer.SerializeToString(v).GetHashCode(),
            v => JsonSerializer.DeserializeFromString<T>(JsonSerializer.SerializeToString(v))
        );
        propertyBuilder.HasConversion(converter);
        propertyBuilder.Metadata.SetValueConverter(converter);
        propertyBuilder.Metadata.SetValueComparer(comparer);
        // propertyBuilder.HasColumnType("jsonb");
        return propertyBuilder;
    }

    public static PropertyBuilder<T?> HasJsonConversionNullable<T>(this PropertyBuilder<T?> propertyBuilder) where T : class, new()
    {
        var converter = new ValueConverter<T, string>
        (
            v => JsonSerializer.SerializeToString(v) ?? string.Empty,
            v => JsonSerializer.DeserializeFromString<T>(v) ?? new T()
        );
        var comparer = new ValueComparer<T>
        (
            (l, r) => JsonSerializer.SerializeToString(l) == JsonSerializer.SerializeToString(r),
            v => v == null ? 0 : JsonSerializer.SerializeToString(v).GetHashCode(),
            v => JsonSerializer.DeserializeFromString<T>(JsonSerializer.SerializeToString(v))
        );
        propertyBuilder.HasConversion(converter!);
        propertyBuilder.Metadata.SetValueConverter(converter);
        propertyBuilder.Metadata.SetValueComparer(comparer);
        // propertyBuilder.HasColumnType("jsonb");
        return propertyBuilder;
    }

    public static PropertyBuilder<string?> HasImg300Conversion(this PropertyBuilder<string?> propertyBuilder)
    {
        var converter = new ValueConverter<string?, string?>
        (
            v => RevImageStyle300(v),
            v => ImageStyle300(v)
        );
        var comparer = new ValueComparer<string?>
        (
            (l, r) => RevImageStyle300(l) == RevImageStyle300(r),
            v => v == null ? 0 : RevImageStyle300(v)!.GetHashCode()
            // v => ImageStyle300(v)
        );
        propertyBuilder.HasConversion(converter);
        propertyBuilder.Metadata.SetValueConverter(converter);
        propertyBuilder.Metadata.SetValueComparer(comparer);
        return propertyBuilder;
    }

    public static string? ImageStyle300(string? str)
    {
        var result = str;

        if (string.IsNullOrWhiteSpace(str))
            return result;

        result = str.Split('?')[0];
        result = $"{result}?x-oss-process=style/300x300";

        // if (str.IndexOf('?') <= 0)
        // result = $"{str}?x-oss-process=style/300x300";
        // else if (str.IndexOf("x-oss-process=") <= 0)
        //     result = $"{str}&x-oss-process=style/300x300";

        return result;
    }

    public static string? RevImageStyle300(string? str)
    {
        var result = str;

        if (string.IsNullOrWhiteSpace(str))
            return result;

        result = str.Split('?')[0];
        // result = str.Replace("?x-oss-process=style/300x300", string.Empty).Replace("&x-oss-process=style/300x300", string.Empty);

        return result;
    }
}