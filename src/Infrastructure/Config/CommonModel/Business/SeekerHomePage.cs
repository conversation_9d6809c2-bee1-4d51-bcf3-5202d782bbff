namespace Config.CommonModel.Business;

public class NuopinPostPanel
{
    /// <summary>
    /// 企业总数量
    /// </summary>
    public int EntNumber { get; set; }

    /// <summary>
    /// 最近新增企业数量
    /// </summary>
    public int EntRecentNumber { get; set; }

    /// <summary>
    /// 在招职位数量
    /// </summary>
    public int PostsProceedNumber { get; set; }
}

public class NuoyoukaoProjectPanel
{
    /// <summary>
    /// 项目总数量
    /// </summary>
    public int ProjectNumber { get; set; }

    /// <summary>
    /// 进行中项目总数量
    /// </summary>
    public int ProjectProceedNumber { get; set; }

    /// <summary>
    /// 可报名项目总数量
    /// </summary>
    public int ProjectApplyNumber { get; set; }

    /// <summary>
    /// 岗位总数量（正在进行中项目的岗位）
    /// </summary>
    public int ProjectPositionNumber { get; set; }

    /// <summary>
    /// 可报名岗位总数量（正在进行中并且处于可报名项目的岗位）
    /// </summary>
    public int ProjectApplyPositionNumber { get; set; }
}

public class NuopinAdvisersModel
{
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? AdviserId { get; set; }

    /// <summary>
    /// 名字
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }
}