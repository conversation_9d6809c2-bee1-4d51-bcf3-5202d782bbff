namespace Staffing.Model.CentreService.Transit;

public class GetXqdpNkp
{
    /// <summary>
    /// 所查询的顾问
    /// </summary>
    public List<string>? NewCityHrIds { get; set; }
}

public class GetXqdpNkpResponse
{
    /// <summary>
    /// 项目数量-累计
    /// </summary>
    public int ProjectTotal { get; set; }

    /// <summary>
    /// 在招岗位数量
    /// </summary>
    public int PostOnline { get; set; }

    /// <summary>
    /// 求职者数量
    /// </summary>
    public int Seeker { get; set; }

    /// <summary>
    /// 新区大厅求职者数量
    /// </summary>
    public int NewCitySeeker { get; set; }

    /// <summary>
    /// 项目数量-城市分组
    /// </summary>
    public List<GetXqdpNkpCity> ProjectCity { get; set; } = new List<GetXqdpNkpCity>();

    /// <summary>
    /// 在招岗位数量-城市分组
    /// </summary>
    public List<GetXqdpNkpCity> PostOnlineCity { get; set; } = new List<GetXqdpNkpCity>();

    /// <summary>
    /// 求职者数量-城市分组
    /// </summary>
    public List<GetXqdpNkpCity> SeekerCity { get; set; } = new List<GetXqdpNkpCity>();
}

public class GetXqdpNkpCity
{
    /// <summary>
    /// 城市Id
    /// </summary>
    public string? CityId { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Num { get; set; }
}