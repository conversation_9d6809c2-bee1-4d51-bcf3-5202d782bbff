ALTER TABLE post
ADD RewardType INT(4) NULL DEFAULT 0 COMMENT '奖励时效类型（0: 单次结算, 1: 长期结算）',
ADD PaymentCycle INT(4) NULL COMMENT '结算周期(按小时、天、月)',
ADD PaymentDuration INT(4) NULL COMMENT '返费周期, 0代表无限期';

-- ALTER TABLE project_teambounty
-- ADD RewardType INT(4) NULL DEFAULT 0 COMMENT '奖励时效类型（0: 单次结算, 1: 长期结算）',
-- ADD PaymentCycle INT(4) NULL COMMENT '结算周期(按小时、天、月)',
-- ADD PaymentDuration INT(4) NULL COMMENT '返费周期, null代表无限期',
-- -- ADD GuaranteeStartDate datetime(6) NULL COMMENT '计算过保开始时间，可空',
-- ADD DeliveryDate datetime(6) NULL COMMENT '交付时间，入职过保就是入职时间，简历交付就是投递时间',
-- ADD DeliveryStatus INT(4) NULL DEFAULT 0 COMMENT '交付状态';

DROP TABLE IF EXISTS `post_profit_stage`;
CREATE TABLE `post_profit_stage` (
  `Id` VARCHAR(18) NOT NULL COMMENT '主键',
  `PostId` VARCHAR(18) NOT NULL COMMENT '职位ID，关联职位表',
  `GuaranteeDays` INT NOT NULL COMMENT '过保天数',
  `Amount` DECIMAL(18,2) NOT NULL COMMENT '分润金额',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_post_profit_stage_PostId` (`PostId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职位分润阶段表';


DROP TABLE IF EXISTS `post_bounty`;
CREATE TABLE `post_bounty` (
  `Id` varchar(18) NOT NULL,
  `RecruitId` varchar(37) DEFAULT NULL COMMENT '招聘流程Id',
  `ResumeBufferId` varchar(36) DEFAULT NULL COMMENT '第三方简历池主键id',
  `TeamPostId` varchar(18) NOT NULL COMMENT '协同职位Id',
  `PostId` varchar(18) NOT NULL COMMENT '原始职位Id',
  `PostName` varchar(100) DEFAULT NULL COMMENT '职位名称',
  `SeekerId` varchar(18) NOT NULL COMMENT '求职者Id',
  `SaleUserId` varchar(18) DEFAULT NULL COMMENT '销售Id',
  `HrId` varchar(18) NOT NULL COMMENT '主创Id',
  `TeamHrId` varchar(18) NOT NULL COMMENT '协同HrId',
  `ChannelId` varchar(30) DEFAULT NULL COMMENT '渠道商关系Id',
  `PaymentNode` int DEFAULT NULL COMMENT '交付类型',
  `PaymentDays` int DEFAULT NULL COMMENT '打款天数',
  `PaymentType` int DEFAULT NULL COMMENT '打款方式',
  `Money` decimal(18,2) DEFAULT '0.00' COMMENT '金额',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `GuaranteeStatus` int NOT NULL DEFAULT '0' COMMENT '过保状态',
  `IfStockOut` int DEFAULT '0' COMMENT '是否减库存，默认0-否，1-是',
  `Description` varchar(100) DEFAULT NULL COMMENT '交付失败原因描述',
  `Source` int NOT NULL DEFAULT '0' COMMENT '来源',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `Deleted` bit(1) DEFAULT b'0' COMMENT '是否删除，1-删除',
  `FollowerId` varchar(18) DEFAULT NULL COMMENT '邀面人Id',
  `SalesRate` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '销售佣金比例',
  `ManagerRate` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '项目管理佣金比例',
  `ClueRate` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '线索佣金比例',
  `FollowerRate` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '邀面佣金比例',
  `PlatformRate` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '平台佣金比例',
  `SalesBounty` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '销售佣金',
  `ManagerBounty` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '项目管理佣金',
  `ClueBounty` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '线索佣金',
  `FollowerBounty` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '邀面佣金',
  `PlatformBounty` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '平台佣金',
  `RewardType` int DEFAULT '0' COMMENT '奖励时效类型（0: 单次结算, 1: 长期结算）',
  `PaymentCycle` int DEFAULT NULL COMMENT '结算周期(按小时、天、月)',
  `PaymentDuration` int DEFAULT NULL COMMENT '返费周期, 0代表无限期',
  `FileAwayDate` datetime(6) DEFAULT NULL COMMENT '归档日期',
  `GuaranteeStartDate` datetime(6) DEFAULT NULL COMMENT '计算过保开始时间，可空',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `RecruitId` (`RecruitId`),
  KEY `IX_post_bounty_TeamPostId` (`TeamPostId`),
  KEY `IX_post_bounty_PostId` (`PostId`),
  KEY `IX_post_bounty_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职位分润表';


DROP TABLE IF EXISTS `post_bounty_stage`;
CREATE TABLE `post_bounty_stage` (
  `Id` VARCHAR(18) NOT NULL COMMENT '主键',
  `BountyId` VARCHAR(18) NOT NULL COMMENT 'post_bounty',
  `Status` INT(4) NOT NULL COMMENT '状态',
  `GuaranteeDays` INT NOT NULL COMMENT '过保天数',
  `Money` DECIMAL(18,2) NOT NULL COMMENT '分润金额',
  `GuaranteeStatus` int NOT NULL DEFAULT '0' COMMENT '过保状态',
  `SettlementEndTime` datetime(6) NULL COMMENT '结算截止日期',
  PRIMARY KEY (`Id`),
  KEY `IX_post_bounty_stage_BountyId` (`BountyId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职位分润阶段表';


-- ----------------------------
-- 职位结算单
-- ----------------------------
DROP TABLE IF EXISTS `post_settlement`;
CREATE TABLE `post_settlement` (
  `Id` varchar(18)  NOT NULL,
  `BountyStageId` varchar(18) NOT NULL COMMENT 'Post_Bounty_Stage表主键id',
  `Status` int NOT NULL DEFAULT '0' COMMENT '结算状态',
  `ApprovalStatus` int NOT NULL DEFAULT '0' COMMENT '审核状态',
  `SettlementAmount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '结算量',
  `SettlementMoney` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '结算金额',
  `ManagerConfirmUser` varchar(36) NULL COMMENT '项目经理确认人',
  `ManagerConfirmTime` datetime(6) NULL COMMENT '项目经理确认时间',
  `PlatformConfirmUser` varchar(36) NULL COMMENT '平台确认人',
  `PlatformConfirmTime` datetime(6) NULL COMMENT '平台确认时间',
  `FinanceConfirmUser` varchar(36) NULL COMMENT '财务确认人',
  `FinanceConfirmTime` datetime(6) NULL COMMENT '财务确认时间',
  `StartTime` datetime(6) NULL COMMENT '结算开始时间',
  `EndTime` datetime(6) NULL COMMENT '结算截止时间',
  `Remark` varchar(5000) NULL COMMENT '备注',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_post_settlement_BountyStageId` (`BountyStageId`),
  KEY `IX_post_settlement_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职位结算单';


-- ----------------------------
-- 职位结算单明细表
-- ----------------------------
DROP TABLE IF EXISTS `post_settlement_detail`;
CREATE TABLE `post_settlement_detail` (
  `Id` varchar(18)  NOT NULL,
  `SettlementId` varchar(18) NOT NULL COMMENT 'Post_Bounty_Stage表主键id',
  `UserId` varchar(18) NULL COMMENT '结算人id',
  `UserNo` varchar(30) NULL COMMENT '结算人工号',
  `EntId` varchar(18) NULL COMMENT '结算人企业id',
  `SettlementNo` varchar(50) COMMENT '支付单号-调用结算接口返回唯一标识',
  `PaymentStatus` int NOT NULL DEFAULT '0' COMMENT '打款状态',
  `PaymentTime` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '打款时间',
  `SettleBountyType` int NULL COMMENT '结算类型，内部、外部、个人',
  `UserType` int NOT NULL DEFAULT '0' COMMENT '类型:销售、项目经理、线索提供方等',
  `SettlementMoney` decimal(19,2) NOT NULL DEFAULT '0.00' COMMENT '结算金额',
  `Remark` varchar(5000) NULL COMMENT '备注',
  PRIMARY KEY (`Id`),
  KEY `IX_post_settlement_detail_SettlementId` (`SettlementId`),
  KEY `IX_post_settlement_detail_UserId` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职位结算单明细表';