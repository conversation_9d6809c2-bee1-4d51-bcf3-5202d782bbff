﻿// using Config.CommonModel.Recruit;
// using Config.CommonModel.ThirdTalentInfo;
// using Config.Enums;

// namespace Infrastructure.CommonService.ShareProfit;

// /// <summary>
// /// 简历交付场景
// /// </summary>
// public class ResumeDeliveryService : CommonDeliveryService
// {
//     public ResumeDeliveryService(ProjectPostInfo projectPostInfo, RecruitInfos recruit, CommonPostOrder orderService)
//         : base(projectPostInfo, recruit, orderService)
//     {
//     }

//     public override void InterviewerScreening(string orderId)
//     {
//         _orderService.PostStockOut(orderId);
//     }

//     public override void FileAway(string orderId)
//     {
//         // 归档类型必须为无效线索
//         if (_projectPostInfo.Status == BountyStatus.交付中 && _recruit.FileAwayType == RecruitFileAway.Invalid)
//         {
//             // 交付失败
//             _orderService.PayFailed(orderId, _recruit.InvalidReason ?? "无效线索归档");
//             // 加库存
//             _orderService.PostStockIn(orderId);
//         }
//     }
// }
