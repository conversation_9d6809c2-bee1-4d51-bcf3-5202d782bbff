﻿using System.Collections.Generic;

namespace Infrastructure.QYWechat.Model
{
    /// <summary>
    /// 获取客户群列表
    /// </summary>
    public class Response_GroupChatList : ResponseBase_Page
    {
        /// <summary>
        /// 客户群信息列表
        /// </summary>
        public List<GroupChatListItem>? Group_Chat_List { get; set; }
    }
    public class GroupChatListItem
    {
        /// <summary>
        /// 客户群唯一标识
        /// </summary>
        public string? Chat_Id { get; set; }

        /// <summary>
        /// 客户群跟进状态
        /// 0-跟进人正常 1-跟进人离职 2-离职继承中 3- 离职继承完成
        /// </summary>
        public GroupChatListItemStatus Status { get; set; }
    }

    public enum GroupChatListItemStatus
    {
        正常 = 0,
        离职 = 1,
        继承中 = 2,
        继承完成 = 3
    }

    /// <summary>
    /// 获取客户群详情
    /// </summary>
    public class Response_GroupChatInfo : ResponseBase
    {
        /// <summary>
        /// 客户群详情
        /// </summary>
        public GroupChat? Group_Chat { get; set; }
    }

    public class GroupChat
    {
        /// <summary>
        /// 客户群ID
        /// </summary>
        public string? Chat_Id { get; set; }
        /// <summary>
        /// 群名
        /// </summary>
        public string? Name { get; set; }
        /// <summary>
        /// 群主ID
        /// </summary>
        public string? Owner { get; set; }
        /// <summary>
        /// 群的创建时间
        /// </summary>
        public long Create_Time { get; set; }
        /// <summary>
        /// 群公告
        /// </summary>
        public string? Notice { get; set; }
        /// <summary>
        /// 群成员列表
        /// </summary>
        public List<GroupChatMember>? Member_List { get; set; }
        /// <summary>
        /// 群管理员列表
        /// </summary>
        public List<GroupChatAdmin>? Admin_List { get; set; }
        /// <summary>
        /// 当前群成员版本号。可以配合客户群变更事件减少主动调用本接口的次数
        /// </summary>
        public string? Member_Version { get; set; }
    }

    public class GroupChatMember
    {
        /// <summary>
        /// 群成员id
        /// </summary>
        public string? UserId { get; set; }
        /// <summary>
        /// 成员类型
        /// </summary>
        public GroupChatInfoMemberType Type { get; set; }
        /// <summary>
        /// 外部联系人在微信开放平台的唯一身份标识（微信unionid），通过此字段企业可将外部联系人与公众号/小程序用户关联起来。仅当群成员类型是微信用户（包括企业成员未添加好友），且企业绑定了微信开发者ID有此字段
        /// </summary>
        public string? UnionId { get; set; }
        /// <summary>
        /// 入群时间
        /// </summary>
        public long Join_Time { get; set; }
        /// <summary>
        /// 入群方式。
        /// </summary>
        public GroupChatInfoMemberJoin_Scene Join_Scene { get; set; }
        /// <summary>
        /// 邀请者
        /// </summary>
        public GroupChatInvitor? Invitor { get; set; }
        /// <summary>
        /// 在群里的昵称
        /// </summary>
        public string? Group_Nickname { get; set; }
        /// <summary>
        /// 名字
        /// </summary>
        public string? Name { get; set; }
    }
    public enum GroupChatInfoMemberType
    {
        企业成员 = 1,
        外部联系人 = 2
    }
    public enum GroupChatInfoMemberJoin_Scene
    {
        由群成员直接邀请入群 = 1,
        由群成员通过链接邀请入群 = 2,
        通过扫描群二维码入群 = 3
    }
    public class GroupChatInvitor
    {
        /// <summary>
        /// 邀请者的userid
        /// </summary>
        public string? UserId { get; set; }
    }

    public class GroupChatAdmin
    {
        /// <summary>
        /// 群管理员userid
        /// </summary>
        public string? UserId { get; set; }
    }

}
