using Config.CommonModel;
using Config.Enums;

namespace Staffing.Model.CentreService.System;

public class GetWithdrawRecord : QueryRequest
{
    /// <summary>
    /// hr还是求职者
    /// </summary>
    public SeekerOrHr? UserType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public WithdrawStatus? Status { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// 用户手机
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 起始时间
    /// </summary>
    public DateTime? BeginTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

public class GetWithdrawRecordResponse : QueryResponse
{
    /// <summary>
    /// 提现记录
    /// </summary>
    public List<WithdrawRecordInfo>? Rows { get; set; }
}

public class WithdrawRecordInfo
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// 用户手机
    /// </summary>
    public string? Mobile { get; set; }

    public string UserId { get; set; } = null!;

    /// <summary>
    /// hr还是求职者
    /// </summary>
    public SeekerOrHr UserType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public WithdrawStatus Status { get; set; }

    /// <summary>
    /// 状态名称
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 提现账户
    /// </summary>
    public string? Account { get; set; }

    /// <summary>
    /// 提现账户名称
    /// </summary>
    public string? AccountName { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 审批人
    /// </summary>
    public string? Approver { get; set; }

    /// <summary>
    /// 审批通过时间
    /// </summary>
    public DateTime? ApprovalTime { get; set; }

    /// <summary>
    /// 提现时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }
}

public class WithdrawChangeStatus
{
    public string? Id { get; set; }

    /// <summary>
    /// 状态（只能改成退还、提现完成）
    /// </summary>
    public WithdrawStatus? Status { get; set; }
}