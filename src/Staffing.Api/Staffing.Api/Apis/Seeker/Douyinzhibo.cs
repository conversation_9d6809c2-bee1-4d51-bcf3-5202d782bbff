using Config;
using Infrastructure.Extend;
using Staffing.Model.Seeker.Douyinzhibo;
using Staffing.Core.Interfaces.Seeker;

namespace Staffing.Api.Apis.Seeker;

[Service(ServiceLifetime.Transient)]
public class DouyinzhiboRouter : ISeekerRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("douyinzhibo").WithTags("抖音直播");
        group.MapPost("/createzb/posts", (DouyinzhiboPostRequestForCreate request, IDouyinzhiboService rep) => rep.GetPostListForCreate(request))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "创建直播时获取职位列表" });

        group.MapPost("/zb/posts", async (DouyinzhiboPostRequestForEdit request, IDouyinzhiboService rep) => await rep.GetPostListByZhiboId(request))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "根据直播id获取职位列表" });

        group.MapPost("/createzb/groups", async (GroupRequest request, IDouyinzhiboService rep) => await rep.GetPostGroups(request))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "创建直播时获取职位分组（一级分类，城市，公司）" });

        group.MapPost("/createzb/list", (ZhiboListRequest request, IDouyinzhiboService rep) => rep.ZhiboList(request))
        .WithOpenApi(operation => new(operation) { Summary = "获取直播列表" });

        group.MapPost("/createzb/save", (ZhiboSaveRequest request, IDouyinzhiboService rep) => rep.ZhiboSave(request))
        .WithOpenApi(operation => new(operation) { Summary = "创建直播时保存（上下架岗位）" });

        group.MapGet("/createzb/edit/{id}", (string id, IDouyinzhiboService rep) => rep.ZhiboEdit(id))
        .WithOpenApi(operation => new(operation) { Summary = "获取直播表单信息" });

        group.MapGet("/createzb/load/{id}", (string id, IDouyinzhiboService rep) => rep.ZhiboLoad(id))
        .WithOpenApi(operation => new(operation) { Summary = "直播挂载" });

        group.MapPost("/zb/speek", (SpeekRequest request, IDouyinzhiboService rep) => rep.SpeekThePost(request))
        .WithOpenApi(operation => new(operation) { Summary = "直播时开启关闭讲解" });

        group.MapPost("/zb/hot", (HotRequest request, IDouyinzhiboService rep) => rep.HotTheGroup(request))
        .WithOpenApi(operation => new(operation) { Summary = "直播时开启关闭热度" });
    }
}