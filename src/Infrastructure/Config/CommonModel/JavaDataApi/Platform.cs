using Config.Enums;

namespace Config.CommonModel.JavaDataApi;

#region 平台人才库请求月斌模型
/// <summary>
/// 月斌服务请求模型
/// </summary>
public class PlatformExternalRequest
{
    /// <summary>
    /// hrid
    /// </summary>
    public string hrId { get; set; } = default!;

    /// <summary>
    /// 搜索项
    /// </summary>
    public string? unionSearch { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public int? sex { get; set; }

    /// <summary>
    /// 用户渠道
    /// </summary>
    public int? channel { get; set; }

    /// <summary>
    /// 用户城市
    /// </summary>
    public List<string>? city { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public int? education { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public string? endDate { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public string? startDate { get; set; }

    /// <summary>
    /// 寻访意向
    /// </summary>
    public string? jobType { get; set; }

    /// <summary>
    /// 用户等级
    /// </summary>
    public int? level { get; set; }

    /// <summary>
    /// 等级解释
    /// </summary>
    public string? levelName { get; set; }

    /// <summary>
    /// 最小年龄
    /// </summary>
    public int? minAge { get; set; }

    /// <summary>
    /// 最大年龄
    /// </summary>
    public int? maxAge { get; set; }

    /// <summary>
    /// 人才库标签id
    /// </summary>
    public string? talentLabelId { get; set; }

    /// <summary>
    /// 页索引
    /// </summary>
    public int pageNum { get; set; } = default!;

    /// <summary>
    /// 页大小
    /// </summary>
    public int pageSize { get; set; } = default!;
}

/// <summary>
/// 月斌服务返回模型
/// </summary>
public class PlatformExternalResponse
{
    public int? total { get; set; }

    public int? count { get; set; }

    public List<PlatformExternalModel>? list { get; set; }
}

/// <summary>
/// 月斌服务返回模型
/// </summary>
public class PlatformExternalModel
{
    /// <summary>
    /// 生日
    /// </summary>
    public DateTime? birthday { get; set; }

    /// <summary>
    /// 所在城市
    /// </summary>
    public string? city { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? createdTime { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public string? eduExp { get; set; }

    /// <summary>
    /// 学历枚举
    /// </summary>
    public EducationType? education { get; set; }

    /// <summary>
    /// 学历枚举值
    /// </summary>
    public string? educationName { get; set; }

    /// <summary>
    /// 寻访意向
    /// </summary>
    public string? expectedJob { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? headPortrait { get; set; }

    /// <summary>
    /// 平台人才库id
    /// </summary>
    public string? id { get; set; }

    /// <summary>
    /// 是否实名用户
    /// </summary>
    public bool? ifCertification { get; set; }

    /// <summary>
    /// 用户等级
    /// </summary>
    public TalentPlatformLevel? level { get; set; }

    /// <summary>
    /// 用户等级枚举值
    /// </summary>
    public string? levelName { get; set; }

    /// <summary>
    /// 最近登录时间
    /// </summary>
    public DateTime? loginTime { get; set; }

    /// <summary>
    /// 用户名称
    /// </summary>
    public string? name { get; set; }

    /// <summary>
    /// 职业类型key
    /// </summary>
    public OccupationType? occupation { get; set; }

    /// <summary>
    /// 职业类型value
    /// </summary>
    public string? occupationName { get; set; }

    /// <summary>
    /// 真实用户id
    /// </summary>
    public string? seekerId { get; set; }

    /// <summary>
    /// 性别枚举
    /// </summary>
    public Sex? sex { get; set; }

    /// <summary>
    /// 性别枚举值
    /// </summary>
    public string? sexName { get; set; }

    /// <summary>
    /// 用户来源解释
    /// </summary>
    public string? sourceName { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    public string? workExp { get; set; }

    /// <summary>
    /// 人才库标签
    /// </summary>
    public List<TalentLabelClass>? talentLabel { get; set; }

    /// <summary>
    /// 简历二维码
    /// </summary>
    public string? hrAppletQrCode { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? mobile { get; set; }
}

/// <summary>
/// 人才库标签类
/// </summary>
public class TalentLabelClass
{
    public string Id { get; set; } = default!;

    public string Name { get; set; } = default!;
}
#endregion

#region 虚拟人才库列表接收月斌相关模型
/// <summary>
/// 月斌服务请求模型
/// </summary>
public class ExternalRequest
{
    /// <summary>
    /// hrid
    /// </summary>
    public string hrId { get; set; } = default!;

    /// <summary>
    /// 搜索
    /// </summary>
    public string? unionSearch { get; set; }

    /// <summary>
    /// 用户状态
    /// </summary>
    public int? status { get; set; }

    /// <summary>
    /// 期望职位
    /// </summary>
    public string? expectedJob { get; set; }

    /// <summary>
    /// 期望行业
    /// </summary>
    public string? expectedIndustry { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public int? education { get; set; }

    /// <summary>
    /// 地区code
    /// </summary>
    public List<string>? city { get; set; }

    /// <summary>
    /// 用户来源
    /// </summary>
    public int? source { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public string? startDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public string? endDate { get; set; }

    /// <summary>
    /// 人才库标签id
    /// </summary>
    public string? talentLabelId { get; set; }

    /// <summary>
    /// 最小年龄
    /// </summary>
    public int? minAge { get; set; }

    /// <summary>
    /// 最大年龄
    /// </summary>
    public int? maxAge { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int pageNum { get; set; }

    /// <summary>
    /// 页大小
    /// </summary>
    public int pageSize { get; set; }
}

/// <summary>
/// 月斌服务返回模型
/// </summary>
public class ExternalResponse
{
    public int? total { get; set; }

    public int? count { get; set; }

    public List<ExternalModel>? list { get; set; }
}

/// <summary>
/// 月斌服务返回模型
/// </summary>
public class ExternalModel
{
    /// <summary>
    /// 主键id
    /// </summary>
    public string? id { get; set; }

    /// <summary>
    /// 性别解释
    /// </summary>
    public string? sexName { get; set; }

    /// <summary>
    /// 学历解释
    /// </summary>
    public string? educationName { get; set; }

    /// <summary>
    /// 真实用户id
    /// </summary>
    public string? seekerId { get; set; }

    /// <summary>
    /// 微信
    /// </summary>
    public string? weChat { get; set; }

    /// <summary>
    /// qq
    /// </summary>
    public string? qq { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? headPortrait { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateTime? birthday { get; set; }

    /// <summary>
    /// 工作开始时间
    /// </summary>
    public DateTime? workTime { get; set; }

    /// <summary>
    /// 工作状态
    /// </summary>
    public int? jobStatus { get; set; }

    /// <summary>
    /// 简历完善度
    /// </summary>
    public int? perfection { get; set; }

    /// <summary>
    /// 自我介绍
    /// </summary>
    public string? selfEvaluation { get; set; }

    /// <summary>
    /// 技能
    /// </summary>
    public string? skillAnalysis { get; set; }

    /// <summary>
    /// 行业标签
    /// </summary>
    public string? industryLabel { get; set; }

    /// <summary>
    /// 职位标签
    /// </summary>
    public string? postLabel { get; set; }

    /// <summary>
    /// 其他标签
    /// </summary>
    public string? otherLabel { get; set; }

    /// <summary>
    /// 所在地（标准）
    /// </summary>
    public string? locationNorm { get; set; }

    /// <summary>
    /// 所在地详情
    /// </summary>
    public string? detailedLocation { get; set; }

    /// <summary>
    /// 期望城市
    /// </summary>
    public string? hopeCity { get; set; }

    /// <summary>
    /// 期望薪资
    /// </summary>
    public string? salaryHope { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    public string? workExp { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? updatedTime { get; set; }

    /// <summary>
    /// 渠道
    /// </summary>
    public TalentVirtualChannel? channel { get; set; }

    /// <summary>
    /// hrId
    /// </summary>
    public string? hrId { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? mobile { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? sex { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public TalentVirtualEducation? education { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? mailbox { get; set; }

    /// <summary>
    /// 行业名称
    /// </summary>
    public string? industryName { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? postName { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public string? eduExp { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? createdTime { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public TalentVirtualStatus? status { get; set; }

    /// <summary>
    /// 所在地
    /// </summary>
    public string? location { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? name { get; set; }

    /// <summary>
    /// 人才库标签
    /// </summary>
    public List<TalentLabelClass>? talentLabel { get; set; }
}
#endregion