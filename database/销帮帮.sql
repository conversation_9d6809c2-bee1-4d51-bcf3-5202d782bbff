DROP TABLE IF EXISTS `xbb_user`;
CREATE TABLE `xbb_user` (
  `Id` varchar(50) NOT NULL,
  `Name` varchar(50) NULL COMMENT '用户姓名',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='销帮帮用户';


DROP TABLE IF EXISTS `xbb_xsjh`;
CREATE TABLE `xbb_xsjh` (
  `Id` bigint NOT NULL,
  `Name` varchar(255) NULL COMMENT '销售机会名称',
  `CustomerName` varchar(255) NULL COMMENT '客户名称',
  `Creator` varchar(50) NULL,
  `XbbUpdatedTime`bigint NOT NULL DEFAULT 0,
  `XbbCreatedTime` bigint NOT NULL DEFAULT 0,
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='销帮帮销售机会';


ALTER TABLE user_hr_recommendent ADD `XbbXsjhName` varchar(255) NULL COMMENT '销帮帮销售机会名称';
ALTER TABLE user_hr_recommendent ADD `XbbXsjhUId` varchar(50) NULL COMMENT '销帮帮销售机会录入人Id';


DROP TABLE IF EXISTS `xbb_xiaoshoujh`;
CREATE TABLE `xbb_xiaoshoujh` (
  `Id` bigint NOT NULL,
  `Name` varchar(255) NULL COMMENT '销售机会名称',
  `CustomerName` varchar(255) NULL COMMENT '客户名称',
  `Creator` varchar(50) NULL,
  `CreatorId` varchar(50) NULL,
  `XbbUpdatedTime` datetime(6) NOT NULL,
  `XbbCreatedTime` datetime(6) NOT NULL,
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='销帮帮销售机会';

ALTER TABLE user_hr_recommendent ADD `XbbXsjhUName` varchar(50) NULL COMMENT '销帮帮销售机会录入人';

ALTER TABLE xbb_xiaoshoujh ADD `CreatorNumber` varchar(50) NULL COMMENT '销帮帮销售机会录入人工号';