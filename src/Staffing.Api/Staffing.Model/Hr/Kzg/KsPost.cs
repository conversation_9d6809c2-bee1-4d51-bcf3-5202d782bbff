using Config.CommonModel.KuaiShou;

namespace Staffing.Model.Hr.Kzg;

/// <summary>
/// 发布职位到快手
/// </summary>
public class SendPostToKzg : GetKsPostInfo
{
    /// <summary>
    /// 协同职位id
    /// </summary>
    public string? TeamPostId { get; set; }
}

/// <summary>
/// 发布职位到快手
/// </summary>
public class SendPostToKzgResponse
{
    /// <summary>
    /// 职位id
    /// </summary>
    public string? jobId { get; set; }

    // /// <summary>
    // /// 是否绑定快招工用户
    // /// </summary>
    // public bool BindUser { get; set; }

    // /// <summary>
    // /// 绑定用户页面地址
    // /// </summary>
    // public string? BindUserUrl { get; set; }
}

public class GetKsPostByNkp
{
    /// <summary>
    /// 协同职位id
    /// </summary>
    public string? TeamPostId { get; set; }
}

public class GetKsPostByNkpResponse : GetKsPostInfo
{

}

public class GetKsPostInfo
{
    /// <summary>
    /// 职位名称
    /// </summary>
    public string? name { get; set; }

    /// <summary>
    /// 职位描述
    /// </summary>
    public string? description { get; set; }

    /// <summary>
    /// 招聘人数
    /// </summary>
    public int? recruitNum { get; set; }

    /// <summary>
    /// 合同说明 1 - 与第三方劳务签订合同 2 - 与用工单位签订合同
    /// </summary>
    public int? contractType { get; set; }

    /// <summary>
    /// 公司信息
    /// </summary>
    public KsCompany? company { get; set; }

    /// <summary>
    /// 职位类别
    /// </summary>
    public KsDicTree? category { get; set; }

    /// <summary>
    /// 位置
    /// </summary>
    public KsLocation? location { get; set; }

    /// <summary>
    /// 职位标签
    /// </summary>
    public List<KsDicTree>? tags { get; set; }
}