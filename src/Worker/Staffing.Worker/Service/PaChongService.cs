using Config;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Infrastructure.NoahCommon;
using Infrastructure.Proxy;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class PaChongService
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _logger;
    private readonly ParseResume _parseResume;
    private readonly CommonDicService _commonDicService;
    private readonly CommonVirtualService _commonVirtualService;
    private readonly WeChatHelper _weChatHelper;
    private readonly TencentImHelper _tencentImHelper;
    private readonly CacheHelper _cacheHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly CommonEsService _commonEsService;
    private ConfigManager _config;
    private readonly CommonPostService _commonPostService;
    private readonly CommonProjectService _commonProjectService;
    private readonly NuoPinApi _nuoPinApi;
    private readonly WxSheQunApi _wxSheQunApi;
    public PaChongService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger, NuoPinApi nuoPinApi,
    ParseResume parseResume, CommonDicService commonDicService, CommonProjectService commonProjectService,
    CommonVirtualService commonVirtualService, WeChatHelper weChatHelper, TencentImHelper tencentImHelper,
    CacheHelper cacheHelper, IHostEnvironment hostingEnvironment, CommonEsService commonEsService,
    IOptionsSnapshot<ConfigManager> config, CommonPostService commonPostService, WxSheQunApi wxSheQunApi)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _parseResume = parseResume;
        _commonDicService = commonDicService;
        _commonVirtualService = commonVirtualService;
        _weChatHelper = weChatHelper;
        _tencentImHelper = tencentImHelper;
        _cacheHelper = cacheHelper;
        _hostingEnvironment = hostingEnvironment;
        _commonEsService = commonEsService;
        _config = config.Value;
        _commonPostService = commonPostService;
        _commonProjectService = commonProjectService;
        _nuoPinApi = nuoPinApi;
        _wxSheQunApi = wxSheQunApi;
    }

    /// <summary>
    /// 微信社群爬虫
    /// </summary>
    public async Task WxSheQun(CancellationToken cancel, int hours)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            var today = DateTime.Today;
            if (DateTime.Now >= today.AddHours(5) && DateTime.Now <= today.AddHours(8))
                return;

            //加锁
            var lockerKey = $"pachong:wxshequn";
            using var locker = MyRedis.TryLock(lockerKey, 1200);
            if (locker == null)
                return;

            //查询汇总数据
            try
            {
                var summaryData = await _wxSheQunApi.GetWxSheQunSummy();
                if (summaryData.allcheck > 0)
                    MyRedis.Client.HSet(RedisKey.WxSheQun.Key, RedisKey.WxSheQun.SummaryData, summaryData);
            }
            catch (Exception e)
            {
                _logger.Error("微信社群爬虫出错1", Tools.GetErrMsg(e));
            }

            var page = 1;

            var ran = new Random(Guid.NewGuid().GetHashCode());
            while (true)
            {
                var data = await _wxSheQunApi.GetWxList(page, hours);

                if (data == null || data.Count == 0 || page > 100)
                    break;

                var group = data.Chunk(50);
                foreach (var newGp in group)
                {
                    var ids = newGp.Select(s => s.qrid).ToList();

                    if (newGp == null || newGp.Count() == 0)
                        break;

                    using (var _context = _contextFactory.CreateDbContext())
                    {
                        var wxGroups = _context.Wx_Group.Where(x => ids.Contains(x.Id)).ToList();
                        foreach (var item in newGp)
                        {
                            var localItem = wxGroups.FirstOrDefault(x => x.Id == item.qrid);
                            if (localItem == null)
                            {
                                localItem = new Wx_Group
                                {
                                    Id = item.qrid
                                };
                                _context.Add(localItem);
                            }
                            localItem.GroupType = item.type switch { 1 => GroupType.微信群, 2 => GroupType.企业微信群, _ => GroupType.其他 };
                            localItem.GroupName = item.filename ?? string.Empty;
                            localItem.GroupNumber = item.people;
                            localItem.FindTime = item.time;
                            localItem.QRCode = item.url;
                            localItem.UpdatedTime = DateTime.Now;
                        }
                        _context.SaveChanges();
                    }
                }

                page++;

                //随机延迟1-5秒
                await Task.Delay(ran.Next(1000, 5001));
            }
        }
        catch (Exception e)
        {
            _logger.Error("微信社群爬虫出错", Tools.GetErrMsg(e));
        }
    }
}
