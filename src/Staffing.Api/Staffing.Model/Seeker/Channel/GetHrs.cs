using System.Text.Json.Serialization;
using Config.CommonModel;
using Config.Enums;

namespace Staffing.Model.Seeker.Channel;

public class GetHrs : QueryRequest
{
    /// <summary>
    /// 顾问名字
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? AdviserId { get; set; }
}

public class GetHrsResponse : QueryResponse
{
    public List<GetHrsDetail> Rows { get; set; } = default!;
}

public class GetHrsDetail
{
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? AdviserId { get; set; }

    /// <summary>
    /// 是否是当前顾问
    /// </summary>
    public bool IsCurrent { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 在线状态
    /// </summary>
    public OnlineStatus? OnlineStatus { get; set; }

    /// <summary>
    /// 人才数量
    /// </summary>
    public int Talent { get; set; }

    /// <summary>
    /// 虚拟人才数量
    /// </summary>
    public int VirtualTalent { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 企业简称
    /// </summary>
    public string? EntAbbr { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 登录时间
    /// </summary>
    [JsonIgnore]
    public DateTime LoginTime { get; set; }

    /// <summary>
    /// 资料完整性
    /// </summary>
    public int DataScore { get; set; }

    /// <summary>
    /// 评分
    /// </summary>
    public string? Grade { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }
}

/// <summary>
/// 切换顾问
/// </summary>
public class SetAdviser
{
    /// <summary>
    /// HrId
    /// </summary>
    public string? HrId { get; set; }
}