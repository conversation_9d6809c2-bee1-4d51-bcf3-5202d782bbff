﻿namespace Infrastructure.Common;

public class NoahTools
{
    public static string GetNoahStatus(string id)
    {
        return id switch
        {
            "1" => "签约",
            "2" => "执行中",
            "3" => "完毕",
            "4" => "终止",
            "5" => "意外终止",
            "6" => "过期",
            _ => string.Empty
        };
    }

    public static string GetNoahCategory(string id)
    {
        return id switch
        {
            "af009cfa-bce5-6ef8-b4c4-d3ff04382dc9" => "产品类合同",
            "3cbefb3c-cd15-b48a-d141-de0c6519f889" => "采购类合同",
            "f9ccabd2-6842-4e39-7172-22c3db83b55f" => "虚拟合同",
            "510d3393-3bd5-17dc-a3ee-f9cb9b734ce8" => "框架子协议-通信",
            "209512a9-5769-e66e-48c9-029ef36df57f" => "框架子协议-人力",
            "461cb193-540a-3f54-7c2f-84917d91bb48" => "其它类",
            "08528b58-dd8a-f310-6745-4d84899d6214" => "合同变更(作废勿选)",
            "a6c9766d-67d2-037b-57fd-60b22f67e9ac" => "解除合同(作废勿选)",
            _ => string.Empty
        };
    }

    public static string GetNoahNature(string id)
    {
        return id switch
        {
            "d733f176-dc9f-657b-7047-8a4026b9b100" => "新签（经营类合同选择）",
            "7790fe46-2237-9669-c971-e8cb567f5cd8" => "续签",
            "b70e8e90-e2c1-204e-1593-63e257ab2f99" => "变更",
            "3c983fd8-901e-b60b-1ae6-b2dbae981b38" => "解除",
            "3daa5d6e-c12d-1703-a2bc-45a27d25f49f" => "补充",
            "09fbc921-53c5-74e0-4f0e-3cd2ebe28b0a" => "虚拟",
            "f6eb0524-7a32-befc-9cd6-ce116fa6c9f7" => "新签（采购类合同选择）",
            "60787a3b-4c33-533c-83d3-c72e422f9123" => "其他",
            _ => string.Empty
        };
    }
}
