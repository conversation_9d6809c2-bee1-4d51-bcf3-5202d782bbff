﻿using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;
using Staffing.Model.Hr.Post;
using Staffing.Model.Hr.User;

namespace Staffing.Model.Hr.Project;

public class UpdatePostResponse
{
    /// <summary>
    /// 职位Id
    /// </summary>
    public string? PostId { get; set; }
}

public class GetPostTags
{
    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }
}

public class GetPostTagsResponse
{
    /// <summary>
    /// 标签
    /// </summary>
    public List<string> Rows { get; set; } = default!;
}

public class HrGetPostDetail : GetPostDetail
{
    /// <summary>
    /// 交付佣金
    /// </summary>
    public decimal DeliveriesBounty { get; set; }
    
    /// <summary>
    /// 是否自己职位
    /// </summary>
    public bool SelfPost { get; set; }

    /// <summary>
    /// 面试看板
    /// </summary>
    public PostRecentInterviews InterviewDashboard { get; set; } = new PostRecentInterviews();

    /// <summary>
    /// 优选状态(null=尚未提交申请)
    /// </summary>
    public ExcellentPostStatus? ExcellentStatus { get; set; }

    /// <summary>
    /// 72小时入职
    /// </summary>
    public bool EntryIn72hour { get; set; }

    /// <summary>
    /// 24小时面试
    /// </summary>
    public bool InterviewIn24Hour { get; set; }

    /// <summary>
    /// 当前hrId
    /// </summary>
    public string? CurrentHrId { get; set; }

    // /// <summary>
    // /// 已协同
    // /// </summary>
    // public bool Shared { get; set; }

    /// <summary>
    /// 协同推荐
    /// </summary>
    /// <returns></returns>
    public PostRecommendedNum? PostRecommendedNum { get; set; }

    /// <summary>
    /// 访问人数
    /// </summary>
    /// <returns></returns>
    public PostRecommendedNum? PostVisitorsNum { get; set; }

    /// <summary>
    /// 报名人数
    /// </summary>
    /// <returns></returns>
    public PostRecommendedNum? PostRegistrationNum { get; set; }

    /// <summary>
    /// 面试人数
    /// </summary>
    /// <returns></returns>
    public PostRecommendedNum? PostInterviewNum { get; set; }

    /// <summary>
    /// 入职人数
    /// </summary>
    /// <returns></returns>
    public PostRecommendedNum? PostInductionNum { get; set; }

    //b端小程序增加

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 项目状态
    /// </summary>
    public ProjectStatus? ProjectStatus { get; set; }

    /// <summary>
    /// 项目地区
    /// </summary>
    public List<CityModel>? ProjectRegion { get; set; }

    /// <summary>
    /// 项目级别
    /// </summary>
    public string? ProjectLevelName { get; set; }

    /// <summary>
    /// 项目编号
    /// </summary>
    public string? ProjectNo { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public int ProjectAutoId { get; set; }

    public ProjectNodeType? ProjectNodeType { get; set; }
    /// <summary>
    /// 财务人员
    /// </summary>
    public HrModel? FinanceInfo { get; set; } 
    /// <summary>
    /// 项目经理
    /// </summary>
    public HrModel? PMInfo { get; set; } 
    /// <summary>
    /// 简历处理时长
    /// </summary>
    public int? ResumeProcessingDuration { get; set; }
    /// <summary>
    /// x天内有面试安排
    /// </summary>
    public int InterviewArrangement { get; set; }
}

// public class UserInfo
// {
//     /// <summary>
//     /// 用户id
//     /// </summary>
//     public string? UserId { get; set; }
//     /// <summary>
//     /// 用户名称
//     /// </summary>
//     public string? UserName { get; set; }
//     /// <summary>
//     /// 头像
//     /// </summary>
//     public string? Avatar { get; set; }
    
//     /// <summary>
//     /// 微信号
//     /// </summary>
//     public string? WeChatNo { get; set; }

//     /// <summary>
//     /// 企业微信二维码
//     /// </summary>
//     public string? EntWeChatQrCode { get; set; }
//     /// <summary>
//     /// 腾讯IM账号Id
//     /// </summary>
//     public string? TencentImId { get; set; }
//     /// <summary>
//     /// 电话
//     /// </summary>
//     public string? phone { get; set; }
    
// }

public class PostRecommendedNum
{
    /// <summary>
    /// 总数
    /// </summary>
    public int? Num { get; set; }

    /// <summary>
    /// 自己报名
    /// </summary>
    public int? Self { get; set; }

    /// <summary>
    /// 公司协同（null则不显示）
    /// </summary>
    public int? Internal { get; set; }

    /// <summary>
    /// 平台协同（null则不显示）
    /// </summary>
    public int? Anyone { get; set; }

    /// <summary>
    /// 诺聘协同（null则不显示）
    /// </summary>
    public int? Platform { get; set; }
}

public class HrGetPostText
{
    /// <summary>
    /// 协同的职位Id
    /// </summary>
    public string? TeamPostId { get; set; }
}

public class HrGetPostTextInfo : GetPostDetail
{
    /// <summary>
    /// 短链接
    /// </summary>
    public string? AppletShortLink { get; set; }

    /// <summary>
    /// 有效期
    /// </summary>
    public DateTime? AppletShortLinkExp { get; set; }

    /// <summary>
    /// 小程序路径
    /// </summary>
    public string? AppletPath { get; set; }
}

public class GetPostDeliverys : QueryRequest
{
    /// <summary>
    /// 协同的职位Id
    /// </summary>
    public string? TeamPostId { get; set; }
}

public class GetPostDeliverysResponse : QueryResponse
{
    public List<PostDeliveryDetail> Rows { get; set; } = default!;
}

public class PostDeliveryDetail
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 招聘流程Id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public OccupationType? Occupation { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public string? OccupationName { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 报名时间
    /// </summary>
    public DateTime EventTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 地区
    /// </summary>
    public CityModel? Region { get; set; }
}

public class HrGetPostCopywriting
{
    /// <summary>
    /// 协同的职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 0=小程序文案,1=公众号文案
    /// </summary>
    public int Type { get; set; }
}

public class HrGetPostCopywritingResponse
{
    /// <summary>
    /// 文案
    /// </summary>
    public string? Text { get; set; }

    /// <summary>
    /// 链接文案
    /// </summary>
    public string? UrlText { get; set; }
}

public class HrUpdatePostCopywriting
{
    /// <summary>
    /// 协同的职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 文案
    /// </summary>
    public string? Text { get; set; }
}

public class GetPostBusinessSmsText
{
    /// <summary>
    /// 协同职位编码
    /// </summary>
    public string? TeamPostNo { get; set; }
}

public class GetPostBusinessSmsTextResponse
{
    /// <summary>
    /// 内容
    /// </summary>
    public string? Text { get; set; }
}

public class GetNoahProj
{
    /// <summary>
    /// 职位Id
    /// </summary>
    public string? PostId { get; set; }
}

public class GetNoahProjResponse
{
    /// <summary>
    /// 合同地址
    /// </summary>
    public List<string> ContractUrl { get; set; } = new List<string>();

    /// <summary>
    /// 销帮帮合同编号
    /// </summary>
    public string? Htcode { get; set; }
}

public class ProjectNodeType
{
    /// <summary>
    /// 交付类型枚举
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }

    /// <summary>
    /// 交付类型名称
    /// </summary>
    public string? PaymentNodeName { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? UnitName { get; set; }

    /// <summary>
    /// 占位符提示
    /// </summary>
    public string? PlaceHolder { get; set; }
}

public class GetPostInterviewResponse : PostInterviewInfo
{
    /// <summary>
    /// 当天可预约的面试时间
    /// </summary>
    public List<PostInterviewTimeModel>? TodayInterviewTime { get; set; }
}