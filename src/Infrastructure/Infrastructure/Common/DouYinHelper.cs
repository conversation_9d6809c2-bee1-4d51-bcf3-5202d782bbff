﻿using Flurl.Http;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using ServiceStack.Text;
using Config;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Hosting;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Transient)]
public class DouYinHelper
{
    private readonly ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    public DouYinHelper(IOptionsSnapshot<ConfigManager> config,
    IHostEnvironment hostingEnvironment, LogManager log)
    {
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
    }

    /// <summary>
    /// 获取用户抖音小程序手机
    /// </summary>
    /// <param name="appID"></param>
    /// <param name="appSecret"></param>
    /// <param name="code"></param>
    /// <param name="encryptedData"></param>
    /// <param name="iv"></param>
    /// <returns></returns>
    public async Task<GetDyAppletPhone> GetAppletPhone(string appID, string appSecret, string code, string? anonymous_code, string encryptedData, string iv)
    {
        var code2SessionUrl = "https://developer.toutiao.com/api/apps/v2/jscode2session";
        var tokenResult = await code2SessionUrl.PostJsonAsync(new
        {
            code = code,
            anonymous_code = anonymous_code,
            appid = appID,
            secret = appSecret
        }).ReceiveJson<GetDyBaseResponse<GetDyAppletPhone>>();
        
        if (tokenResult?.err_no != 0)
            throw new Exception($"抖音获取session_key错误:{JsonSerializer.SerializeToString(tokenResult)}");

        var ed = Senparc.Weixin.WxOpen.Helpers.EncryptHelper.DecodeEncryptedData(tokenResult.data?.session_key, encryptedData, iv);
        var phoneInfo = JsonSerializer.DeserializeFromString<GetDyAppletPhone>(ed);

        tokenResult.data!.purePhoneNumber = phoneInfo.purePhoneNumber;

        return tokenResult.data;
    }
}

public class GetDyBaseResponse<T>
{
    public int err_no { get; set; }
    public string? err_tips { get; set; }
    public T? data { get; set; }
}

public class GetDyAppletPhone
{
    /// <summary>
    /// 返回1代表成功
    /// </summary>
    public int result { get; set; }

    /// <summary>
    /// 没有区号的手机号
    /// </summary>
    public string? purePhoneNumber { get; set; }

    /// <summary>
    /// 用户在当前小程序的 ID，如果请求时有 code 参数才会返回
    /// </summary>
    public string? openid { get; set; }

    /// <summary>
    /// 匿名用户在当前小程序的 ID，如果请求时有 anonymous_code 参数才会返回
    /// </summary>
    public string? anonymous_openid { get; set; }

    /// <summary>
    /// 用户在小程序平台的唯一标识符，请求时有 code 参数才会返回。如果开发者拥有多个小程序，可通过 unionid 来区分用户的唯一性。
    /// </summary>
    public string? unionid { get; set; }

    /// <summary>
    /// 会话密钥
    /// </summary>
    public string? session_key { get; set; }
}