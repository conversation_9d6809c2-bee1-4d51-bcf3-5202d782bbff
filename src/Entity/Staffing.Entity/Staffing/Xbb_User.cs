﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 销帮帮用户
/// </summary>
[Table("xbb_user")]
public class Xbb_User
{
    [Key]
    public string Id { get; set; } = default!;

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? Name { get; set; }
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}