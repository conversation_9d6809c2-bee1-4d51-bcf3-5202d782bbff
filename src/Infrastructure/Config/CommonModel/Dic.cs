﻿namespace Config.CommonModel;

public class GeneralDic
{
    /// <summary>
    /// 编号
    /// </summary>
    public string Id { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;
}

public class GeneralRecursionDic
{
    /// <summary>
    /// 编号
    /// </summary>
    public string Id { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 父节点Id
    /// </summary>
    public string? ParentId { get; set; } = string.Empty;

    /// <summary>
    /// 子节点
    /// </summary>
    public List<GeneralRecursionDic>? Children { get; set; }
}

public class GetWelfare
{
    /// <summary>
    /// 福利名称
    /// </summary>
    public string? Search { get; set; }
}

public class GetWelfareResponse
{
    public List<WelfareModel> Rows { get; set; } = default!;
}

public class GetSchool
{
    /// <summary>
    /// 学校名称
    /// </summary>
    public string? Search { get; set; }
}

public class GetSchoolResponse
{
    public List<GeneralDic> Rows { get; set; } = default!;
}

public class GetMajor
{
    /// <summary>
    /// 专业名称
    /// </summary>
    public string? Search { get; set; }
}

public class GetMajorResponse
{
    public List<GeneralDic> Rows { get; set; } = default!;
}

public class GetIndustry
{
    /// <summary>
    /// 行业名称
    /// </summary>
    public string? Search { get; set; }
}

public class GetIndustryResponse
{
    public List<GeneralRecursionDic> Rows { get; set; } = default!;
}

public class GetPostCategory
{
    /// <summary>
    /// 职位类别名称
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 级别
    /// </summary>
    public int? Level { get; set; }
}

public class GetDicTreeResponse
{
    public List<GeneralRecursionDic> Rows { get; set; } = default!;
}

public class GetCity
{
    /// <summary>
    /// 父Id
    /// </summary>
    public string? ParentId { get; set; }
}

public class GetCityResponse
{
    public List<GeneralDic> Rows { get; set; } = default!;
}

public class GetCityTree
{
    /// <summary>
    /// 查几级（省市区）
    /// </summary>
    public int? Level { get; set; }
}

public class GetCert
{
    /// <summary>
    /// 搜索
    /// </summary>
    public string? Search { get; set; }
}

public class GeneralKeyValue<T>
{
    public string Key { get; set; } = default!;

    public T Value { get; set; } = default!;
}