﻿using Config.CommonModel;
using Config.CommonModel.Business;
using Config.CommonModel.DataScreen;
using Google.Protobuf.WellKnownTypes;
using Microsoft.AspNetCore.Http;
using Staffing.Model.Hr.Project;


namespace Staffing.Core.Interfaces.Hr;

public interface IProjectContractsService
{
    Task<GetProjectContractResponse> GetNoahContract(GetNoahContract model);

    Task<GetProjectContractResponse> GetProjectContract(GetProjectContract model);
    GetProjectContractListResponse GetProjectContractList(GetProjectContractList model);
    EmptyResponse SetDefaultProjectContract(SetDefaultProjectContract model);
    UpdateProjectContractResponse UpdateProjectContract(UpdateProjectContract model);
    
    /// <summary>
    /// 检查项目是否有默认合同
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>是否存在默认合同</returns>
    HasDefaultContractResponse HasDefaultContract(string projectId);
}