ALTER TABLE post_delivery ADD ChannelId varchar(18) NULL COMMENT'渠道Id';


DROP TABLE IF EXISTS `user_hr_recommendent`;
CREATE TABLE `user_hr_recommendent` (
  `Id` varchar(18) NOT NULL,
  `AdviserId` varchar(18) NOT NULL COMMENT '顾问Id',
  `Status` int NULL COMMENT '状态',
  `Source` int NOT NULL COMMENT '来源',
  `Level` int NULL COMMENT '级别',
  `TEntId` varchar(50) NOT NULL COMMENT '三方企业Id',
  `Post` varchar(255) CHARACTER SET utf8mb4 NULL COMMENT '职位',
  `EntName` varchar(255) CHARACTER SET utf8mb4 NOT NULL COMMENT '公司名称',
  `HrName` varchar(255) CHARACTER SET utf8mb4 NOT NULL COMMENT 'Hr名称',
  `Mobile` varchar(255) NOT NULL COMMENT 'Hr电话',
  `Remark` varchar(5000) CHARACTER SET utf8mb4 NULL COMMENT '备注',
  `FollowUpTime` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '最新跟进时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_hr_recommendent_AdviserId` (`AdviserId`)
) ENGINE=InnoDB COMMENT='顾问推荐企业';


DROP TABLE IF EXISTS `post_team_channel`;
CREATE TABLE `post_team_channel` (
  `Id` varchar(18) NOT NULL,
  `TeamPostId` varchar(18) NOT NULL COMMENT '协同职位Id',
  `ChannelId` varchar(18) NOT NULL COMMENT '渠道Id',
  `TopTime` datetime(6) NULL COMMENT '置顶时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_post_team_channel_ChannelId_TeamPostId` (`ChannelId`,`TeamPostId`)
) ENGINE=InnoDB COMMENT='渠道置顶职位';