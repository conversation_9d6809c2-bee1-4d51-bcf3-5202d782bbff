using System.Security.Cryptography;
using System.Text;
using Config;
using Config.CommonModel.Xbb;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using ServiceStack.Text;

namespace Infrastructure.Proxy;

/// <summary>
/// 销帮帮
/// </summary>
[Service(ServiceLifetime.Transient)]
public class XbbApi
{
    private readonly ConfigManager _config;
    private readonly LogManager _log;
    public XbbApi(IOptionsSnapshot<ConfigManager> config, LogManager log)
    {
        _config = config.Value;
        _log = log;
    }

    /// <summary>
    /// 表单列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetFormListResponse> GetFormList(GetFormList model)
    {
        var res = await HttpPost<GetFormList, GetFormListResponse>($"{XbbConfig.ApiBaseUrl}/pro/v2/api/form/list", model);
        return res!;
    }

    /// <summary>
    /// 表单详情
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetFormResponse> GetForm(GetForm model)
    {
        var res = await HttpPost<GetForm, GetFormResponse>($"{XbbConfig.ApiBaseUrl}/pro/v2/api/form/get", model);
        return res!;
    }

    /// <summary>
    /// 表单数据列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetFormDataResponse> GetFormData(GetFormData model)
    {
        var res = await HttpPost<GetFormData, GetFormDataResponse>($"{XbbConfig.ApiBaseUrl}/pro/v2/api/paas/list", model);
        return res!;
    }

    /// <summary>
    /// 表单数据详情
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetFormDataDetailResponse> GetFormDataDetail(GetFormDataDetail model)
    {
        var res = await HttpPost<GetFormDataDetail, GetFormDataDetailResponse>($"{XbbConfig.ApiBaseUrl}/pro/v2/api/paas/detail", model);
        return res!;
    }

    /// <summary>
    /// 用户列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetUserListResponse> GetUserList(GetUserList model)
    {
        var res = await HttpPost<GetUserList, GetUserListResponse>($"{XbbConfig.ApiBaseUrl}/pro/v2/api/user/list", model);
        return res!;
    }

    /// <summary>
    /// 合同订单
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetProjectListResponse> GetProjectList(GetProjectList model)
    {
        var res = await HttpPost<GetProjectList, GetProjectListResponse>($"{XbbConfig.ApiBaseUrl}/pro/v2/api/contract/list", model);
        return res!;
    }

    /// <summary>
    /// 销售机会
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetXsjhResponse> GetXsjh(GetXsjh model)
    {
        var res = await HttpPost<GetXsjh, GetXsjhResponse>($"{XbbConfig.ApiBaseUrl}/pro/v2/api/opportunity/list", model);
        return res!;
    }

    private async Task<TReturn?> HttpPost<T, TReturn>(string url, T param) where TReturn : class where T : GetXbbRequest
    {
        param.userId = XbbConfig.UserId;
        param.corpid = XbbConfig.Corpid;

        var strParam = JsonSerializer.SerializeToString(param);

        var sign = SHA256Encrypt(strParam + XbbConfig.Token);

        // var resultStr = await RetryHelper.Do(async () =>
        // await url.WithHeader("Content-Type", "application/json").WithHeader("sign", sign)
        // .PostStringAsync(strParam).ReceiveString());

        var resultStr = await url.WithHeader("Content-Type", "application/json").WithHeader("sign", sign)
        .PostStringAsync(strParam).ReceiveString();

        XBBResponse<TReturn> result;
        try
        {
            result = JsonSerializer.DeserializeFromString<XBBResponse<TReturn>>(resultStr);
        }
        catch
        {
            _log.Error($"销帮帮接口错误1:{url}", resultStr, $"sign:{sign},para:{strParam}");
            throw new InternalServerException($"销帮帮接口错误:{resultStr}");
        }

        if (result == null)
            throw new Exception("销帮帮未获取到数据");

        if (!result.success)
        {
            //销帮帮接口经常报错，暂时屏蔽错误
            _log.Info($"销帮帮接口错误:{url}", resultStr, $"sign:{sign},para:{strParam}");
            // _log.Error($"销帮帮接口错误:{url}", resultStr, $"sign:{sign},para:{strParam}");
            // throw new InternalServerException($"销帮帮接口错误:{resultStr}");
        }

        return result.result;
    }

    private string SHA256Encrypt(string str)
    {
        byte[] bytes = Encoding.UTF8.GetBytes(str);
        byte[] hash = SHA256.Create().ComputeHash(bytes);
        var builder = new StringBuilder();
        for (int i = 0; i < hash.Length; i++)
        {
            builder.Append(hash[i].ToString("X2").ToLower());
        }
        return builder.ToString();
    }
}

