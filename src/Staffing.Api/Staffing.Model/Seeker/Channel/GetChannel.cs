namespace Staffing.Model.Seeker.Channel;

public class GetChannel
{
}

public class GetChannelResponse : ChannelInfo
{
}

public class ChannelInfo
{
    /// <summary>
    /// 渠道Id
    /// </summary>
    public string? ChannelId { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// 顾问
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// 人才数量
    /// </summary>
    public int Talent { get; set; }

    /// <summary>
    /// 虚拟人才库数量
    /// </summary>
    public int VirtualTalent { get; set; }

    /// <summary>
    /// 活跃用户
    /// </summary>
    public int ActiveTalent { get; set; }

    /// <summary>
    /// 余额
    /// </summary>
    public decimal Balance { get; set; }

    /// <summary>
    /// 群二维码
    /// </summary>
    public string? GroupQrCode { get; set; }
}

public class GetChannelQrCode
{
    /// <summary>
    /// 渠道Id
    /// </summary>
    public string? ChannelId { get; set; }
}

public class GetChannelQrCodeResponse
{
    /// <summary>
    /// 渠道二维码
    /// </summary>
    public string? ChannelQrCode { get; set; }
}