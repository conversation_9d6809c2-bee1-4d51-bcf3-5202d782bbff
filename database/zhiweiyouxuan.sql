-- ----------------------------
-- 职位优选
-- ----------------------------
DROP TABLE IF EXISTS `post_excellent`;
CREATE TABLE `post_excellent` (
  `Id` varchar(18) NOT NULL,
  `TeamPostId` varchar(18) NOT NULL,
  `Status` int(11) NOT NULL,
  `InterviewIn24Hour` bit NOT NULL DEFAULT 0,
  `EntryIn72hour` bit NOT NULL DEFAULT 0,
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `RefreshTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_post_excellent_TeamPostId` (`TeamPostId` DESC),
  KEY `IX_post_excellent_Status_RefreshTime` (`Status` DESC, `RefreshTime` DESC),
  KEY `IX_post_excellent_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;