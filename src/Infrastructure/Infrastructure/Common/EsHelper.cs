﻿using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Config;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Hosting;
using Nest;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Singleton)]
public class EsHelper
{
    private readonly ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    public EsHelper(IOptionsSnapshot<ConfigManager> config,
    IHostEnvironment hostingEnvironment, LogManager log)
    {
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
    }

    public EsIndex GetIndex()
    {
        var result = new EsIndex();

        result.TalentPlatform = "talent_platform";
        result.TalentVirtual = "talent_virtual";
        result.Seeker = "st_seeker";
        result.KuaishouTalentInfo = "st_talent_kuaishou";
        result.TalentResume = "talent_resume";

        // if (!_hostingEnvironment.IsProduction())
        // {
        //     result.TalentPlatform = $"test_{result.TalentPlatform}";
        //     result.TalentVirtual = $"test_{result.TalentVirtual}";
        //     result.Seeker = $"test_{result.Seeker}";
        //     result.KuaishouTalentInfo = $"test_{result.KuaishouTalentInfo}";
        // }

        return result;
    }

    public ElasticClient GetClient()
    {
        var settings = new ConnectionSettings(new Uri(_config.ElasticSearch!.Address!))
                .BasicAuthentication(_config.ElasticSearch.UserName, _config.ElasticSearch.Password);
        return new ElasticClient(settings);
    }
}

public class EsIndex
{
    public string TalentPlatform { get; set; } = default!;
    public string TalentVirtual { get; set; } = default!;
    public string Seeker { get; set; } = default!;
    public string KuaishouTalentInfo { get; set; } = default!;
    public string TalentResume { get; set; } = default!;
}