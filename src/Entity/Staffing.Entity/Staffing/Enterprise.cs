﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;
using NetTopologySuite.Geometries;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 企业表
/// </summary>
[Table("enterprise")]
public class Enterprise
{
    [Key]
    public string EntId { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 诺聘Id
    /// </summary>
    public string NuoId { get; set; } = default!;

    /// <summary>
    /// 名字
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// logo
    /// </summary>
    public string? LogoUrl { get; set; }

    /// <summary>
    /// 简称
    /// </summary>
    public string Abbreviation { get; set; } = string.Empty;

    /// <summary>
    /// 类型
    /// </summary>
    public int Type { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public EnterpriseStatus Status { get; set; }

    /// <summary>
    /// 集团类型
    /// </summary>
    [ConcurrencyCheck]
    public EnterpriseGroupType GroupType { get; set; }

    /// <summary>
    /// 集团企业Id
    /// </summary>
    public string? GroupEntId { get; set; } = string.Empty;

    /// <summary>
    /// 地区Id
    /// </summary>
    public string RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 坐标
    /// </summary>
    public Point Location { get; set; } = new Point(0, 0);

    /// <summary>
    /// E签宝机构Id
    /// </summary>
    public string EsignOrgId { get; set; } = string.Empty;

    /// <summary>
    /// 特性
    /// </summary>
    public List<string>? Specific { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Enterprise_Business Enterprise_Business { get; set; } = default!;
}