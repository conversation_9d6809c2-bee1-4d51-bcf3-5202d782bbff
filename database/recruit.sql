-- ----------------------------
-- 招聘表
-- ----------------------------
DROP TABLE IF EXISTS `recruit`;
CREATE TABLE `recruit` (
  `RecruitId` varchar(18) NOT NULL,
  `DeliveryId` varchar(18) NOT NULL COMMENT '投递Id，可空，数字诺亚可以不通过投递直接进入招聘',
  `HrId` varchar(18) NOT NULL COMMENT 'HrId',
  `SeekerId` varchar(18) NOT NULL COMMENT '求职者Id',
  `PostName` varchar(255) NOT NULL COMMENT '职位名称',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '状态',
  `FileAway` int(3) NOT NULL DEFAULT 0 COMMENT '归档状态',
  `FileRemarks` varchar(200) NOT NULL DEFAULT '' COMMENT '归档备注',
  `Type` int(3) NOT NULL DEFAULT 0 COMMENT '类型',
  `InductionTime` datetime(6) COMMENT '入职时间',
  `StatusTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`RecruitId`),
  KEY `IX_recruit_HrId` (`HrId`),
  KEY `IX_recruit_Type` (`Type`),
  KEY `IX_recruit_Status` (`Status`),
  KEY `IX_recruit_DeliveryId` (`DeliveryId` DESC),
  KEY `IX_recruit_SeekerId` (`SeekerId`),
  KEY `IX_recruit_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 招聘记录
-- ----------------------------
DROP TABLE IF EXISTS `recruit_record`;
CREATE TABLE `recruit_record` (
  `Id` varchar(18) NOT NULL,
  `RecruitId` varchar(18) NOT NULL COMMENT '招聘Id',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '状态',
  `FileAway` int(3) NOT NULL DEFAULT 0 COMMENT '归档状态',
  `InterviewerScreenId` varchar(18) NOT NULL DEFAULT 0 COMMENT '面试官筛选id',
  `InterviewId` varchar(18) NOT NULL DEFAULT 0 COMMENT '面试Id',
  `OfferId` varchar(18) NOT NULL DEFAULT 0 COMMENT 'OfferId',
  `Data` varchar(5000) CHARACTER SET utf8mb4 NOT NULL COMMENT 'json数据，可能不用',
  `FileAwayRemarks` varchar(5000) CHARACTER SET utf8mb4 NOT NULL COMMENT '归档备注',
  `Creator` varchar(18) NOT NULL COMMENT '操作人',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_recruit_record_RecruitId_CreatedTime` (`RecruitId`, `CreatedTime` DESC),
  KEY `IX_recruit_record_InterviewId` (`InterviewId` DESC),
  KEY `IX_recruit_record_OfferId` (`OfferId` DESC),
  KEY `IX_recruit_record_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- offer表
-- ----------------------------
DROP TABLE IF EXISTS `recruit_offer`;
CREATE TABLE `recruit_offer` (
  `Id` varchar(18) NOT NULL,
  `RecruitId` varchar(18) NOT NULL COMMENT '招聘Id',
  `InductionSalary` int(11) COMMENT '入职薪资',
  `InductionTime` datetime(6) NOT NULL DEFAULT NOW(6) COMMENT '预期入职时间',
  `RegionId` varchar(15) NOT NULL COMMENT '地区Id',
  `Address` varchar(1024) NULL COMMENT '入职地点地址',
  `Lat` decimal(18,8) NOT NULL COMMENT '入职地点纬度',
  `Lng` decimal(18,8) NOT NULL COMMENT '入职地点经度',
  `Mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `MobileContent` varchar(1000) COMMENT '短信内容',
  `IsMobileNotice` bit NOT NULL DEFAULT 0 COMMENT '是否已短信通知',
  `Mail` varchar(20) NOT NULL DEFAULT '' COMMENT '邮箱地址',
  `MailContent` text COMMENT '邮件内容',
  `NoticeSubjectId` varchar(18) COMMENT '通知主体id',
  `NoticeSubjectName` varchar(100) COMMENT '通知主体名称',
  `NoticeContent` varchar(5000) COMMENT '通知书内容',
  `CarryInformation` varchar(5000) COMMENT '携带资料集合',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_recruit_offer_RecruitId_CreatedTime` (`RecruitId`, `CreatedTime` DESC),
  KEY `IX_recruit_offer_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 面试官筛选表
-- ----------------------------
DROP TABLE IF EXISTS `recruit_interviewer_screen`;
CREATE TABLE `recruit_interviewer_screen` (
  `Id` varchar(18) NOT NULL,
  `RecruitId` varchar(18) NOT NULL COMMENT '招聘Id',
  `SeekerId` varchar(18) NOT NULL COMMENT '求职者Id，冗余',
  `InterviewerId` varchar(18) NOT NULL COMMENT '面试官Id',
  `Recommend` varchar(100) NOT NULL COMMENT '推荐理由',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '状态',
  `Remarks` varchar(200) NOT NULL COMMENT 'hr备注',
  `TodoTime` datetime(6) COMMENT '面试官反馈时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_recruit_interview_screen_RecruitId_CreatedTime` (`RecruitId`,`CreatedTime` DESC),
  KEY `IX_recruit_interview_screen_InterviewerId` (`InterviewerId` DESC),
  KEY `IX_recruit_interview_screen_CreatedTime` (`CreatedTime` DESC)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 面试表
-- ----------------------------
DROP TABLE IF EXISTS `recruit_interview`;
CREATE TABLE `recruit_interview` (
  `InterviewId` varchar(18) NOT NULL,
  `RecruitId` varchar(18) NOT NULL COMMENT '招聘Id',
  `HrId` varchar(18) NOT NULL COMMENT 'HrId，冗余',
  `SeekerId` varchar(18) NOT NULL COMMENT '求职者Id，冗余',
  `InterviewerId` varchar(18) NOT NULL COMMENT '面试官Id',
  `Process` int(2) NOT NULL DEFAULT 0 COMMENT '面试过程',
  `Outcome` int(2) NOT NULL DEFAULT 0 COMMENT '面试结果',
  `Forms` int(2) NOT NULL DEFAULT 0 COMMENT '面试形式',
  `UserFeedBack` int(2) NOT NULL DEFAULT 0 COMMENT '用户面试反馈',
  `InterviewTime` datetime(6) NOT NULL DEFAULT NOW(6) COMMENT '面试时间',
  `RegionId` varchar(15) NOT NULL COMMENT '地区Id',
  `Address` varchar(1024) NULL COMMENT '面试地点地址',
  `Lat` decimal(18,8) NOT NULL COMMENT '面试地点纬度',
  `Lng` decimal(18,8) NOT NULL COMMENT '面试地点经度',
  `Mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `MobileContent` varchar(1000) COMMENT '短信内容',
  `Mail` varchar(20) NOT NULL DEFAULT '' COMMENT '邮箱地址',
  `MailContent` text COMMENT '邮件内容',
  `Remarks` varchar(200) NOT NULL COMMENT '备注',
  `CancelName` varchar(100) COMMENT '取消面试用户名',
  `TodoTime` datetime(6) COMMENT '面试官反馈时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`InterviewId`),
  KEY `IX_recruit_interview_RecruitId_CreatedTime` (`RecruitId`, `CreatedTime` DESC),
  KEY `IX_recruit_interview_HrId_Outcome` (`HrId`, `Outcome`),
  KEY `IX_recruit_interview_SeekerId` (`SeekerId`),
  KEY `IX_recruit_interview_InterviewerId` (`InterviewerId` DESC),
  KEY `IX_recruit_interview_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 面试官待办表
-- ----------------------------
DROP TABLE IF EXISTS `recruit_interviewer_todo`;
CREATE TABLE `recruit_interviewer_todo` (
  `Id` varchar(18) NOT NULL,
  `RelationId` varchar(18) NOT NULL COMMENT '关联id（面试官筛选表id或面试表id）',
  `RecruitId` varchar(18) NOT NULL COMMENT '招聘流程id，冗余',
  `InterviewerId` varchar(18) NOT NULL DEFAULT 0 COMMENT '面试官Id',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '状态',
  `Type` int(3) NOT NULL DEFAULT 0 COMMENT '类型',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_recruit_interview_todo_Type` (`Type`),
  KEY `IX_recruit_interview_todo_RecruitId_CreatedTime` (`RecruitId`,`CreatedTime`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- 招聘流程标签字典表
-- ----------------------------
DROP TABLE IF EXISTS `dic_recruit_label`;
CREATE TABLE `dic_recruit_label` (
  `Id` varchar(18) NOT NULL,
  `HrId` varchar(18) NOT NULL COMMENT 'HrId',
  `LabelName` varchar(20) NOT NULL COMMENT '标签名称',
  PRIMARY KEY (`Id`),
  KEY `IX_dic_recruit_label_HrId` (`HrId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- 招聘流程对应标签表
-- ----------------------------
DROP TABLE IF EXISTS `recruit_label`;
CREATE TABLE `recruit_label` (
  `Id` varchar(18) NOT NULL,
  `RecruitId` varchar(18) NOT NULL COMMENT '招聘流程id',
  `DicLabelId` varchar(18) NOT NULL COMMENT '标签id',
  PRIMARY KEY (`Id`),
  KEY `IX_recruit_label_RecruitId` (`RecruitId`),
  KEY `IX_recruit_label_DicLabelId` (`DicLabelId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 招聘流程简历评论
-- ----------------------------
DROP TABLE IF EXISTS `recruit_comment`;
CREATE TABLE `recruit_comment` (
  `Id` varchar(18) NOT NULL,
  `RecruitId` varchar(18) NOT NULL COMMENT '招聘流程id',
  `Content` varchar(5000) NOT NULL COMMENT '评论内容',
  `HrId` varchar(18) NOT NULL COMMENT 'HrId',
  `HrName` varchar(50) NOT NULL COMMENT 'Hr姓名',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_recruit_comment_VirtualId` (`RecruitId`),
  KEY `IX_recruit_comment_HrId` (`HrId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;