using System.ComponentModel.DataAnnotations;

namespace Staffing.Model.Common.System;

public class Dictionary
{
    
}

public class DictDataSaveReq
{
    /// <summary>
    /// 字典类型
    /// </summary>
    [Required(ErrorMessage = "字典类型不能为空")]
    public string DictType { get; set; } = default!;
    /// <summary>
    /// 字典标签
    /// </summary>
    [Required(ErrorMessage = "字典标签不能为空")]
    public string DictLabel { get; set; } = default!;
    /// <summary>
    /// 字典键值
    /// </summary>
    [Required(ErrorMessage = "字典键值不能为空")]
    public string DictValue { get; set; } = default!;
    /// <summary>
    /// 字典排序
    /// </summary>
    public int? DictSort { get; set; }
    
}



public class DictDataListReq
{
    /// <summary>
    /// 字典类型
    /// </summary>
    public string DictType { get; set; } = default!;
    
}

public class DictDataListResp
{
    
    public string? DictType { get; set; }
    public string? DictLabel { get; set; }
    public string? DictValue { get; set; }
    public int? DictSort { get; set; }
    
}


