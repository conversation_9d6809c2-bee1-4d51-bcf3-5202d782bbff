/*
 Navicat Premium Data Transfer

 Source Server         : 诺聘**************（测试环境）
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : **************:3306
 Source Schema         : staffing

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 25/03/2025 09:51:05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for thirdparty_category_dic_relation
-- ----------------------------
DROP TABLE IF EXISTS `thirdparty_category_dic_relation`;
CREATE TABLE `thirdparty_category_dic_relation`  (
  `ThirdCategoryId` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `ThirdCategoryName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `NkpCategoryId` int NOT NULL,
  `NkpCategoryName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `UpdatedTime` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `CreatedTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Source` int NOT NULL COMMENT '岗位来源 1：职多多 2：58魔方 3：后续',
  PRIMARY KEY (`ThirdCategoryId`, `NkpCategoryId`, `Source`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '第三方与诺快聘岗位类别字典对照表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of thirdparty_category_dic_relation
-- ----------------------------
INSERT INTO `thirdparty_category_dic_relation` VALUES ('010809', 'IT技术支持', 61, 'IT技术支持', '2025-03-24 17:02:03', '2025-03-24 17:02:03', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('040104', '用户运营', 232, '用户运营', '2025-03-24 20:43:19', '2025-03-24 20:43:19', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('040112', '内容审核', 249, '内容审核', '2025-03-24 20:55:56', '2025-03-24 20:55:56', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('040114', '数据标注', 250, '数据标注', '2025-03-24 21:00:08', '2025-03-24 21:00:08', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('040301', '售前客服', 264, '售前客服', '2025-03-24 20:47:25', '2025-03-24 20:47:25', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('040302', '网络/在线客服', 266, '网络客服', '2025-03-24 17:25:14', '2025-03-24 17:25:14', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('040304', '客服专员/助理', 268, '客服专员', '2025-03-24 20:53:12', '2025-03-24 20:53:12', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('040305', '电话客服', 270, '电话客服', '2025-03-24 21:18:14', '2025-03-24 21:18:14', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('040306', '售后客服', 265, '售后客服', '2025-03-24 16:57:16', '2025-03-24 16:57:16', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('040404', '运营经理/主管', 278, '运营经理/主管', '2025-03-24 17:09:59', '2025-03-24 17:09:59', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('050101', '销售专员', 422, '销售专员', '2025-03-24 16:12:21', '2025-03-24 16:12:21', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('050102', '电话销售', 428, '电话销售', '2025-03-24 16:55:04', '2025-03-24 16:55:08', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('050106', '网络销售', 430, '网络销售', '2025-03-24 16:51:52', '2025-03-24 16:51:56', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('050107', '渠道销售', 426, '渠道销售', '2025-03-24 17:00:54', '2025-03-24 17:00:54', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('050111', '销售顾问', 429, '销售顾问', '2025-03-24 16:50:31', '2025-03-24 16:50:31', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('050113', 'BD经理', 425, 'BD经理', '2025-03-24 21:10:35', '2025-03-24 21:10:35', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('050701', '保险顾问', 415, '保险顾问', '2025-03-24 21:04:28', '2025-03-24 21:04:28', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('050702', '金融销售', 412, '金融销售', '2025-03-24 21:25:34', '2025-03-24 21:25:34', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('050704', '理财顾问', 416, '理财顾问', '2025-03-24 21:28:09', '2025-03-24 21:28:09', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('051001', '课程顾问', 434, '课程顾问', '2025-03-24 16:52:58', '2025-03-24 16:52:58', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('06041b', '地推', 410, '会展活动销售', '2025-03-24 16:46:58', '2025-03-24 16:46:58', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('070202', '行政专员/助理', 353, '行政专员/助理', '2025-03-24 21:56:13', '2025-03-24 21:56:13', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('070205', '前台/接待', 865, '礼仪/迎宾/接待', '2025-03-24 16:39:07', '2025-03-24 16:39:07', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('080105', '催收', 548, '催收员', '2025-03-24 17:13:13', '2025-03-24 17:13:13', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('090304', '配/理/拣/发货', 728, '配/理/拣/发货', '2025-03-24 21:35:54', '2025-03-24 21:35:54', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('090702', '送餐员/外卖骑手', 861, '送餐员', '2025-03-24 21:08:30', '2025-03-24 21:08:30', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('0b0307', '汽车主播', 501, '主播', '2025-03-24 17:14:25', '2025-03-24 17:14:25', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('0b030e', '主播', 501, '主播', '2025-03-24 21:20:04', '2025-03-24 21:20:04', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('0b0310', '带货主播', 501, '主播', '2025-03-24 17:21:51', '2025-03-24 17:21:51', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('0d0401', '药店营业员', 685, '药店店员', '2025-03-24 21:50:56', '2025-03-24 21:50:56', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('0f0513', '生产跟单', 938, '生产跟单', '2025-03-24 21:53:53', '2025-03-24 21:53:53', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('110107', '后厨', 842, '后厨', '2025-03-24 21:40:29', '2025-03-24 21:40:29', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('110701', '餐饮店长', 862, '餐饮店长', '2025-03-24 21:34:20', '2025-03-24 21:34:20', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('110802', '咖啡师', 860, '咖啡师', '2025-03-24 21:52:42', '2025-03-24 21:52:42', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('110901', '服务员', 858, '服务员', '2025-03-24 21:47:33', '2025-03-24 21:47:33', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('120301', '保洁', 912, '保洁', '2025-03-24 17:27:57', '2025-03-24 17:27:57', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('12030b', '家政服务', 910, '安保/家政/维修', '2025-03-24 21:39:11', '2025-03-24 21:39:11', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('130101', '焊工', 1010, '焊工', '2025-03-24 21:30:24', '2025-03-24 21:30:24', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('130206', '技工', 1006, '技工/普工', '2025-03-24 21:37:36', '2025-03-24 21:37:36', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('130301', '普工/操作工', 1007, '普工/操作工', '2025-03-24 21:06:23', '2025-03-24 21:06:23', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('140301', '安检员', 917, '地铁安检', '2025-03-24 21:49:33', '2025-03-24 21:49:33', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('150101', '店员/营业员', 881, '店员/营业员', '2025-03-24 17:17:23', '2025-03-24 17:17:23', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('150103', '促销/导购员', 880, '导购', '2025-03-24 17:19:40', '2025-03-24 17:19:40', 2);
INSERT INTO `thirdparty_category_dic_relation` VALUES ('150105', '服装销售', 421, '销售', '2025-03-24 21:45:22', '2025-03-24 21:45:22', 2);

SET FOREIGN_KEY_CHECKS = 1;
