﻿using Config.CommonModel;
using Config.CommonModel.Business;
using Staffing.Model.Hr.Dashboard;
using Staffing.Model.Hr.Recruit;

namespace Staffing.Core.Interfaces.Hr;

/// <summary>
/// 招聘流程服务接口
/// </summary>
public interface IRecruitService
{
    /// <summary>
    /// 查询当前用户是否有权限
    /// </summary>
    /// <param name="projectId"></param>
    /// <returns></returns>
    GetRecruitAuthorityResponse GetRecruitAuthority(string projectId);

    /// <summary>
    /// 获取项目职位列表
    /// </summary>
    /// <param name="projectId"></param>
    /// <returns></returns>
    GetRecruitProjectPostListResponse GetRecruitProjectPostList(string projectId);

    /// <summary>
    /// 获取招聘流程列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetRecentRecruitsResponse GetRecruitListByProjectId(RecruitListRequest model);
    //RecruitListResponse GetRecruitListByProjectId(RecruitListRequest model);

    /// <summary>
    /// 获取招聘流程标签集合(总标签)
    /// </summary>
    /// <returns></returns>
    RecruitLabelListResponse GetRecruitLabelList();

    /// <summary>
    /// 添加招聘流程标签(总标签)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse AddRecruitLabel(AddRecruitLabelRequest model);

    /// <summary>
    /// 删除招聘流程标签(总标签)
    /// </summary>
    /// <param name="labelId"></param>
    /// <returns></returns>
    EmptyResponse DeleteRecruitLabel(string labelId);

    /// <summary>
    /// 获取招聘流程标签
    /// </summary>
    /// <param name="recruitId"></param>
    /// <returns></returns>
    RecruitLabelResponse GetRecruitLabel(string recruitId);

    /// <summary>
    /// 变更招聘流程标签
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse EditRecruitLabel(EditRecruitLabelRequest model);

    /// <summary>
    /// 获取项目面试官列表
    /// </summary>
    /// <param name="projectId"></param>
    /// <returns></returns>
    ProjectInterviewerListResponse GetProjectInterviewerList(string projectId);

    /// <summary>
    /// 获取招聘流程简历详情
    /// </summary>
    /// <param name="recruitId"></param>
    /// <returns></returns>
    RecruitResumeDetailsResponse GetRecruitResumeDetails(string recruitId);

    /// <summary>
    /// 获取招聘流程记录列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    RecruitmentProcessResponse GetRecruitmentProcessList(RecruitmentProcessRequest model);

    /// <summary>
    /// 获取招聘流程简历评论记录
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetRecruitResumeCommentResponse GetRecruitResumeComment(GetRecruitResumeCommentRequest model);

    /// <summary>
    /// 添加招聘流程简历评论
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse AddRecruitResumeComment(AddRecruitResumeCommentRequest model);

    /// <summary>
    /// 删除招聘流程简历评论
    /// </summary>
    /// <param name="commentId"></param>
    /// <returns></returns>
    EmptyResponse DeleteRecruitResumeComment(string commentId);

    /// <summary>
    /// 根据id获取招聘进程
    /// </summary>
    /// <param name="recruitId">招聘流程id</param>
    /// <returns></returns>
    GetRecruitResponse GetRecruitById(string recruitId);

    /// <summary>
    /// 修改招聘流程(面试官初筛)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> EditRecruitInterviewerScreening(RequestEditRecruitInterviewerScreening model);

    /// <summary>
    /// 修改招聘流程(面试)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> EditRecruitInterview(RequestEditRecruitInterview model);

    /// <summary>
    /// 修改招聘流程(Offer)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> EditRecruitOffer(RequestEditRecruitOffer model);

    /// <summary>
    /// 发送offer获取主体集合
    /// </summary>
    /// <param name="recruitId"></param>
    /// <returns></returns>
    SendRecruitOfferGetNoticeSubject GetNoticeSubjectForOffer(string recruitId);

    /// <summary>
    /// 修改招聘流程(入职)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> EditRecruitInduction(RequestEditRecruitInduction model);

    /// <summary>
    /// 修改招聘流程(归档)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> EditRecruitFileAway(RequestEditRecruitFileAway model);

    /// <summary>
    /// 取消面试邀请（hr主动取消）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse CancelInterview(CancelInterviewRequest model);

    /// <summary>
    /// hr填写面试反馈
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse InterviewFeedback(InterviewFeedbackRequest model);

    /// <summary>
    /// hr填写面试官筛选反馈
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse InterviewerScreenFeedback(InterviewerScreenFeedbackRequest model);

    /// <summary>
    /// 新增招聘流程面试
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse AddRecruitInterview(RequestEditRecruitInterview model);

    /// <summary>
    /// 获取项目面试官列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetProjectInterviewerListResponse GetProjectInterviewerList(GetProjectInterviewerListRequest model);

    /// <summary>
    /// 变更项目面试官信息（添加、修改）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse ChangeProjectInterviewer(ChangeProjectInterviewerRequest model);

    /// <summary>
    /// 删除项目面试官信息
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    EmptyResponse DeleteProjectInterviewer(string id);
    RecruitResumeExtend GetRecruitResumeExtend(string recruitId);

    /// <summary>
    /// 下载用户简历
    /// </summary>
    /// <param name="recruitid"></param>
    /// <returns></returns>
    string DownLoadResume(string recruitid);
    UpLoadResumeInfoResponse UpLoadResume(UpLoadResumeModel model);
    List<PostTeamInfoEnum> GetPostsList(string TeamProjectId);
    EmptyResponse ResumeDelivery(UpLoadResumeInfoRequest request);

    /// <summary>
    /// 无效线索
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> EditRecruitInvalidResume(RequestEditRecruitInvalidResume model);
    EmptyResponse GetExists(UpLoadResumeInfoRequest request);

    EmptyResponse EditKuaiShouResume(EditKuaiShouResume request);
    EmptyResponse WaitToFeedback(string recruitId);
    List<ExportRecruitData> ExportRecruit(ExportRecruitRequest model);

    /// <summary>
    /// 批量入职导入
    /// </summary>
    /// <param name="url"></param>
    /// <returns></returns>
    Task<BatchInductionResponse> BatchInduction(BatchInductionRequest request);

    /// <summary>
    /// 获取职位面试时间
    /// </summary>
    /// <param name="recruitid"></param>
    /// <returns></returns>
    Task<List<PostInterviewTimeModel>> GetInterviewTime(string recruitid);
    /// <summary>
    ///  线索大厅列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    CluesHallListResp GetCluesHallList(CluesHallReq model);
    /// <summary>
    /// 线索大厅接单
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse AcceptOrder(ClueAcceptOrderReq model);

    /// <summary>
    /// 线索大厅获取历史项目评价字典
    /// </summary>
    /// <returns></returns>
    List<ClueTagsResp> GetClueTags();
}

