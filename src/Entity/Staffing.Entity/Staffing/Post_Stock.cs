﻿// using System.ComponentModel.DataAnnotations;
// using System.ComponentModel.DataAnnotations.Schema;

// namespace Staffing.Entity.Staffing;

// /// <summary>
// /// 库存表
// /// </summary>
// [Table("post_stock")]
// public class Post_Stock
// {
//     /// <summary>
//     /// 主键id
//     /// </summary>
//     [Key]
//     public string Id { get; set; } = EntityTools.SnowflakeId();

//     /// <summary>
//     /// 订单Id
//     /// </summary>
//     public string? OrderId { get; set; }

//     /// <summary>
//     /// 职位id
//     /// </summary>
//     public string PostId { get; set; } = default!;

//     public StockType StockType { get; set; }

//     /// <summary>
//     /// 出库数量
//     /// </summary>
//     public int StockNum { get; set; }

//     /// <summary>
//     /// 出库后剩余库存
//     /// </summary>
//     public int LeftStock { get; set; }

//     /// <summary>
//     /// 创建时间时间
//     /// </summary>
//     public DateTime? CreatedTime { get; set; }

//     public Project_Teambounty Project_Teambounty { get; set; } = default!;
//     public Post Post { get; set; } = default!;
// }

// public enum StockType
// {
//     订单出库 = 1,
//     改交付数量出库,
//     改协同状态出库,
//     订单入库 = 11,
//     创建职位入库,
//     改交付数量入库,
//     改协同状态入库
// }