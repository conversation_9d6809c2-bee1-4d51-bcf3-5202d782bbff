using Config;
using Config.CommonModel.NkpConfig;
using Infrastructure.CommonInterface;
using Infrastructure.Extend;

namespace Staffing.Api.Apis.Admin;

[Service(ServiceLifetime.Transient)]
public class NkpConfigRouter : IAdminRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("nkpconfig").WithTags("Nkp配置");
        group.MapPost("/nkp/config/save", (ConfigSaveModel model, INkpConfigService rep) => rep.SaveConfig(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "保存" });

        group.MapPost("/nkp/config/tree", (INkpConfigService rep) => rep.GetConfigTreeList())
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "获取配置tree" });
    }
}