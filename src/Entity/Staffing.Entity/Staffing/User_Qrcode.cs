﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 用户二维码
/// </summary>
[Table("user_qrcode")]
public class User_Qrcode
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    public string UserId { get; set; } = default!;

    /// <summary>
    /// AppId
    /// </summary>
    public string AppId { get; set; } = default!;

    /// <summary>
    /// 类型
    /// </summary>
    public UserQrcodeType Type { get; set; }

    /// <summary>
    /// 渠道二维码
    /// </summary>
    public string QrCode { get; set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}
