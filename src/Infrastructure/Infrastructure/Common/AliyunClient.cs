using Config;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Singleton)]
public class AliyunClient
{
    private readonly LogManager _log;
    private readonly ConfigManager _config;
    public AliyunClient(LogManager log, IOptionsSnapshot<ConfigManager> config)
    {
        _log = log;
        _config = config.Value;
    }

    public AlibabaCloud.SDK.Dyplsapi20170525.Client GetDyplsapiClient()
    {
        AlibabaCloud.OpenApiClient.Models.Config config = new AlibabaCloud.OpenApiClient.Models.Config();
        // 您的AccessKey ID。
        config.AccessKeyId = _config.Aliyun!.FullAccess!.AccessKeyId;
        // 您的AccessKey Secret。
        config.AccessKeySecret = _config.Aliyun!.FullAccess!.AccessKeySecret;
        // 您的可用区ID。
        config.RegionId = "cn-beijing";
        AlibabaCloud.SDK.Dyplsapi20170525.Client client = new AlibabaCloud.SDK.Dyplsapi20170525.Client(config);

        return client;
    }
}