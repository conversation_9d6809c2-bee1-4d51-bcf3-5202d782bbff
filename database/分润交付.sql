-- ----------------------------
-- 企业商户表
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_merchant`;
CREATE TABLE `enterprise_merchant` (
  `Id` varchar(18) NOT NULL COMMENT '主键',
  `MerchantName` varchar(255) NOT NULL COMMENT '商户名称',
  `MerchantType` int(4) NOT NULL COMMENT '商户类型',
  `PartyAContractNo` varchar(255) NULL COMMENT '商户作为甲方与平台签署的合同号',
  `PartyBContractNo` varchar(255) NULL COMMENT '商户作为乙方与平台签署的合同号',
  `OfficeSeal` longtext COMMENT '公章文件（Base64编码）',
  `LegalSeal` longtext COMMENT '法人章文件（Base64编码）',
  `ContractSeal` longtext COMMENT '合同章文件（Base64编码）',
  `CreatedBy` varchar(50) NOT NULL COMMENT '创建人',
  `UpdatedBy` varchar(50) NOT NULL COMMENT '更新人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='企业商户表';


-- ALTER TABLE `enterprise_merchant` ADD COLUMN `PartyAContractNo` varchar(255) NULL COMMENT '商户作为甲方与平台签署的合同号';
-- ALTER TABLE `enterprise_merchant` ADD COLUMN `PartyBContractNo` varchar(255) NULL COMMENT '商户作为乙方与平台签署的合同号';

-- ----------------------------
-- 为 post_settlement_detail 表添加字段
-- ----------------------------
ALTER TABLE `post_settlement_detail` ADD COLUMN `InMerchantId` varchar(18) NULL COMMENT '入金商户Id';
ALTER TABLE `post_settlement_detail` ADD COLUMN `OutMerchantId` varchar(18) NULL COMMENT '出金商户Id';
ALTER TABLE `post_settlement_detail` ADD COLUMN `AgentHrNo` varchar(18) NULL COMMENT '经办人工号';
ALTER TABLE `post_settlement_detail` ADD COLUMN `ContractNo` varchar(255) NULL COMMENT '合同号';

ALTER TABLE `project_contract` ADD COLUMN `IsDefault` int(4) NOT NULL DEFAULT 0 COMMENT '默认合同';

-- post_settlement_detail表增加是否生成凭字段
ALTER TABLE `post_settlement_detail` ADD COLUMN `IsVoucher` int(4) NOT NULL DEFAULT 0 COMMENT '是否生成凭证';


DROP TABLE IF EXISTS `voucher`;
CREATE TABLE `voucher` (
  `Id` varchar(18) COLLATE utf8mb4_general_ci NOT NULL,
  `InMerchantId` varchar(18) NOT NULL COMMENT '入金商户ID',
  `OutMerchantId` varchar(18) NOT NULL COMMENT '出金商户ID',
  `InMerchantName` varchar(255) NOT NULL COMMENT '入金商户名称',
  `OutMerchantName` varchar(255) NOT NULL COMMENT '出金商户名称',
  `VoucherTime` datetime NOT NULL COMMENT '凭证时间',
  `Subject` varchar(255) NOT NULL COMMENT '凭证主题',
  `Amount` decimal(16,2) NOT NULL COMMENT '凭证总额',
  `VoucherType` int NOT NULL COMMENT '凭证类型',
  `VoucherStatus` int NOT NULL COMMENT '凭证状态',
  `Remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `VoucherUrl` varchar(1024)  NULL COMMENT '凭证地址',
  `CreatedTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='凭证表';


DROP TABLE IF EXISTS `voucher_detail`;
CREATE TABLE `voucher_detail` (
  `Id` varchar(18) NOT NULL COMMENT '主键',
  `VoucherId` varchar(18) NOT NULL COMMENT '凭证Id',
  `ProjectId` varchar(18) NOT NULL COMMENT '项目Id',
  `ProjectName` varchar(255) NULL COMMENT '项目名称',
  `SettlementId` varchar(18) NOT NULL COMMENT '结算单Id',
  `ServiceType` varchar(255) NULL COMMENT '服务方式',
  `ConfirmResult` varchar(255) NULL COMMENT '确认成果',
  `ConfirmTime` datetime NULL COMMENT '确认时间',
  `Amount` decimal(16,2) NOT NULL COMMENT '金额',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  PRIMARY KEY (`Id`),
  KEY `IX_voucher_detail_ProjectId` (`ProjectId`),
  KEY `IX_voucher_detail_VoucherId` (`VoucherId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='凭证明细表';


DROP TABLE IF EXISTS `voucher_settlement`;
CREATE TABLE `voucher_settlement` (
  `Id` varchar(18) NOT NULL COMMENT '主键',
  `SettlementDetailId` varchar(18) NOT NULL COMMENT 'post_settlement_detail',
  `VoucherDetailId` varchar(18) NOT NULL COMMENT '凭证明细Id',
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  PRIMARY KEY (`Id`),
  KEY `IX_voucher_settlement_SettlementDetailId` (`SettlementDetailId`),
  KEY `IX_voucher_settlement_VoucherDetailId` (`VoucherDetailId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='凭证和结算单关系表';