﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 项目面试官表
/// </summary>
[Table("project_interviewer")]
public class Project_Interviewer
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 项目id
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 姓名 
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 手机号
    /// </summary>
    public string Phone { get; set; } = default!;

    /// <summary>
    /// 职位
    /// </summary>
    public string Post { get; set; } = default!;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string Mail { get; set; } = default!;

    // /// <summary>
    // /// 地区Id
    // /// </summary>
    // public string? RegionId { get; set; }

    // /// <summary>
    // /// 坐标
    // /// </summary>
    // public NetTopologySuite.Geometries.Point Location { get; set; } = new NetTopologySuite.Geometries.Point(0, 0);

    /// <summary>
    /// 办公地址
    /// </summary>
    public string OfficeAddress { get; set; } = default!;

    public Project Project { get; set; } = default!;
}

