﻿namespace Config.CommonModel.DingDing;

public class DingdingCallbackResponse
{
    public string msg_signature { get; set; } = default!;

    public string timeStamp { get; set; } = default!;

    public string nonce { get; set; } = default!;

    public string encrypt { get; set; } = default!;
}

public class DingTalkApprovalCallback
{
    public string? processInstanceId { get; set; }
    public string? corpId { get; set; }
    public string? EventType { get; set; }
    public string? businessId { get; set; }
    public string? title { get; set; }
    /// <summary>
    /// finish：审批正常结束（同意或拒绝)
    /// terminate：审批终止（发起人撤销审批单）
    /// </summary>
    public string? type { get; set; }
    public long? createTime { get; set; }
    public string? processCode { get; set; }
    public string? bizCategoryId { get; set; }
    public string? staffId { get; set; }
    public long? taskId { get; set; }
    public string? result { get; set; }

    /// <summary>
    /// body原生密文
    /// </summary>
    public string? BodyCiphertext { get; set; }
}