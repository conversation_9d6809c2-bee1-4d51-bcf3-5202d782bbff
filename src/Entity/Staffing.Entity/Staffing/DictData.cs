using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

[Table("dict_data")]
public class DictData
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long DictCode { get; set; }
    
    public int DictSort { get; set; } = 0;
    
    public string DictLabel { get; set; } = default!;

    public string DictValue { get; set; } = default!;
    
    public string DictType { get; set; } = default!;
    
    public string? CssClass { get; set; }
    
    public string? ListClass { get; set; }
    
    public string IsDefault { get; set; } = "N";
    
    public string Status { get; set; } = "0";
    
    public string CreateBy { get; set; } = string.Empty;
    
    public DateTime CreateTime { get; set; } = DateTime.Now;
    
    public string UpdateBy { get; set; } = string.Empty;


    public DateTime? UpdateTime { get; set; }
    
    public string? Remark { get; set; }
}