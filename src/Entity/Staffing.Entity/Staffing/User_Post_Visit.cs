﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 用户访问职位
/// </summary>
[Table("user_post_visit")]
public class User_Post_Visit
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 用户Id
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// 职位Id
    /// </summary>
    public int Post { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Dic_Post Dic_Post { get; set; } = default!;
}