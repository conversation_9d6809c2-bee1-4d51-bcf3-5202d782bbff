﻿using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Transient)]
public class LogManager
{
    private RequestContext _request;
    ILogger<LogManager> _logger;
    public LogManager(RequestContext request,
        ILogger<LogManager> logger)
    {
        _request = request;
        _logger = logger;
    }

    public void Info(string title, string message, string data = "")
    {
        _logger.LogInformation($"{title} {message} {data}");
    }

    public void Error<T>(string title, string message, T data)
    {
        var dataStr = string.Empty;
        if (data != null)
            dataStr = ServiceStack.Text.JsonSerializer.SerializeToString(data);

        Error(title, message, dataStr);
    }

    public void Error(string title, string message, string data = "")
    {
        var user = $"{_request?.Name}/{_request?.Account}/{(_request?.Id)}/{_request?.AdviserId}";
        _logger.LogError($"{title} {message} {data}");
    }
}

