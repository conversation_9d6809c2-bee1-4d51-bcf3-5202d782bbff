public class CallRecord
{
    // ID
    public long? id { get; set; }

    // 电话号码
    public string? phone_no { get; set; }

    // 城市
    public string? city { get; set; }

    // 呼出时间
    public DateTime? call_out_time { get; set; }

    // 响铃时间
    public DateTime? ring_time { get; set; }

    // 录音URL
    public string? record_url { get; set; }

    // 响铃录音URL
    public string? ring_record_url { get; set; }

    // 自由响铃时间
    public DateTime? free_ring_time { get; set; }

    // 控制消息
    public string? control_msg { get; set; }

    // 隐私号码
    public string? secret_no { get; set; }

    // 呼叫类型
    public PrivacyNumberCallType? call_type { get; set; }

    // 控制类型
    public string? control_type { get; set; }

    // 释放时间
    public DateTime? release_time { get; set; }

    // 池键
    public string? pool_key { get; set; }

    // 子ID
    public long? sub_id { get; set; }

    // 未连接原因
    public PrivacyNumberUnconnectedCause? unconnected_cause { get; set; }

    // 呼叫时间
    public DateTime? call_time { get; set; }

    // 对端号码
    public string? peer_no { get; set; }

    // 被叫显示号码
    public string? called_display_no { get; set; }

    // 释放方向
    public PrivacyNumberReleaseDir? release_dir { get; set; }

    // 呼叫ID
    public string? call_id { get; set; }

    // 开始时间
    public DateTime? start_time { get; set; }

    // 合作伙伴键
    public string? partner_key { get; set; }

    // 外部ID
    public string? out_id { get; set; }

    // 释放原因
    public int? release_cause { get; set; }
}

//返回模型
public class SecretNoResponse
{
    //code
    public int? code { get; set; }

    //msg
    public string? msg { get; set; } = string.Empty;
}

public enum PrivacyNumberFromType
{
    诺快聘
}

public enum PrivacyNumberCallType
{
    主叫 = 0,
    被叫 = 1,
    呼叫拦截 = 4
}

public enum PrivacyNumberReleaseDir
{
    平台释放 = 0,
    主叫挂断 = 1,
    被叫挂断 = 2
}

public enum PrivacyNumberUnconnectedCause
{
    正常通话 = 0,
    黑名单拦截 = 1,
    无绑定关系 = 2,
    呼叫限制 = 3,
    其他 = 4
}