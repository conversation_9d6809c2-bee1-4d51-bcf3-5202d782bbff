﻿using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.Hr;
using Config;
using Staffing.Model.Hr.Project;
using Config.CommonModel;
using Infrastructure.CommonService;
using ServiceStack.Text;
using Staffing.Core.Interfaces.Common;
using EntityFrameworkCore.AutoHistory.Extensions;
using Config.CommonModel.Tasks;

namespace Staffing.Core.Services.Hr;

[Service(ServiceLifetime.Transient)]
public class ProjectMemberService : IProjectMemberService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CommonDicService _commonDicService;
    private readonly ICommonService _commonService;
    private readonly CommonProjectService _commonProjectService;
    private readonly FigureNoahHelper _figureNoahHelper;
    public ProjectMemberService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, LogManager log, CommonDicService commonDicService,
        ICommonService commonService, CommonProjectService commonProjectService,
        FigureNoahHelper figureNoahHelper)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _log = log;
        _commonDicService = commonDicService;
        _commonService = commonService;
        _commonProjectService = commonProjectService;
        _figureNoahHelper = figureNoahHelper;
    }

    public async Task<UpdateProjectMemberResponse> UpdateProjectMember(UpdateProjectMember model)
    {
        var result = new UpdateProjectMemberResponse();
        if (string.IsNullOrWhiteSpace(model.IdentityCardName))
            throw new BadRequestException("姓名不能为空");

        if (!model.IdentityCardType.HasValue)
            throw new BadRequestException("证件类型不能为空");

        if (string.IsNullOrWhiteSpace(model.IdentityCard))
            throw new BadRequestException("证件号不能为空");
        else if (model.IdentityCardType == IdCardType.身份证 && !Tools.Certification(model.IdentityCard))
            throw new BadRequestException("身份证号不合法");

        if (!model.EmploymentMode.HasValue)
            throw new BadRequestException("请选择用工形式");

        if (!model.EntryTime.HasValue)
            throw new BadRequestException("请选择入职时间");

        if (model.ProbationMonth < 0)
            throw new BadRequestException("请选择试用期时长");

        if (string.IsNullOrWhiteSpace(model.PostName))
            throw new BadRequestException("职位不能为空");

        if (!DataValid.IsMobile(model.Mobile))
            throw new BadRequestException("手机号格式不正确");

        Project_Member? member = null;
        if (!string.IsNullOrWhiteSpace(model.Id))
        {
            member = _context.Project_Member.FirstOrDefault(x => x.Id == model.Id && x.Project.HrId == _user.Id);
            if (member == null)
                throw new NotFoundException("内容不存在");
        }
        else
        {
            if (string.IsNullOrWhiteSpace(model.ProjectId))
                throw new BadRequestException("缺少项目Id");

            if (!_context.Project.Any(x => x.ProjectId == model.ProjectId && x.HrId == _user.Id))
                throw new NotFoundException("项目不存在");

            member = new Project_Member
            {
                ProjectId = model.ProjectId!,
                Status = ProjectMemberStatus.待入职,
                Source = ProjectMemberSource.手动添加
            };
            _context.Add(member);
        }

        var lockerKey = $"{RedisKey.Lock.ProjectMember}:{model.ProjectId}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey, 10);

        if (locker == null)
            throw new BadRequestException("该项目正在操作中，请稍后尝试");

        await Task.FromResult(1);

        if (_context.Project_Member.Any(x => x.ProjectId == model.ProjectId && x.IdentityCard == model.IdentityCard && x.Id != member.Id))
            throw new BadRequestException("身份证号已经存在");

        var idCardInfo = Tools.GetIdCardInfo(model.IdentityCard);

        member.IdentityCardType = model.IdentityCardType.Value;
        member.IdentityCard = model.IdentityCard;
        member.IdentityCardName = model.IdentityCardName;
        member.Mobile = model.Mobile;
        member.Birthday = idCardInfo?.Birthday;
        member.Sex = idCardInfo?.Sex;
        member.RegionId = idCardInfo?.RegionId ?? string.Empty;
        member.EmploymentMode = model.EmploymentMode.Value;
        member.ProbationMonth = model.ProbationMonth;
        member.PostName = model.PostName;
        member.Department = model.Department;
        member.EntryTime = model.EntryTime;
        member.EMail = model.EMail;
        member.Describe = model.Describe;

        member.UpdatedTime = DateTime.Now;

        //添加记录
        var record = new Project_Member_Record
        {
            ProjectId = member.ProjectId,
            IdentityCardType = member.IdentityCardType,
            IdentityCard = member.IdentityCard,
            Mobile = member.Mobile,
            Type = ProjectMemberRecordType.待入职,
            PostName = member.PostName,
            Department = member.Department,
            EntryTime = member.EntryTime,
            QuitTime = member.QuitTime
        };
        _context.Add(record);

        _context.EnsureAutoHistory();
        _context.SaveChanges();
        result.Id = member.Id;

        return result;
    }

    public GetProjectMembersResponse GetProjectMembers(GetProjectMembers model)
    {
        Console.WriteLine(JsonSerializer.SerializeToString(model));
        var result = new GetProjectMembersResponse();

        var predicate = PredicateBuilder.New<Project_Member>(x => x.ProjectId == model.ProjectId);

        if (model.Status == ProjectMemberStatus.离职 && model.QuitStatus == ProjectMemberQuitStatus.待离职)
            predicate = predicate.And(x => x.Status == ProjectMemberStatus.入职);
        else if (model.Status == ProjectMemberStatus.入职)
            predicate = predicate.And(x => x.Status == model.Status && x.QuitStatus == ProjectMemberQuitStatus.未离职);
        else
            predicate = predicate.And(x => x.Status == model.Status);

        if (model.QuitStatus.HasValue && model.Status == ProjectMemberStatus.离职)
            predicate = predicate.And(x => x.QuitStatus == model.QuitStatus);

        if (!string.IsNullOrWhiteSpace(model.Search))
            predicate = predicate.And(x => x.IdentityCard == model.Search
            || x.Mobile == model.Search || x.IdentityCardName.Contains(model.Search));

        if (!string.IsNullOrWhiteSpace(model.PostName))
            predicate = predicate.And(x => x.PostName == model.PostName);

        if (model.Source.HasValue)
            predicate = predicate.And(x => x.Source == model.Source);

        if (model.EntryOrQuitBeginTime.HasValue)
        {
            if (model.Status == ProjectMemberStatus.离职)
                predicate = predicate.And(x => x.QuitTime >= model.EntryOrQuitBeginTime.Value);
            else
                predicate = predicate.And(x => x.EntryTime >= model.EntryOrQuitBeginTime.Value);
        }

        if (model.EntryOrQuitEndTime.HasValue)
        {
            model.EntryOrQuitEndTime = model.EntryOrQuitEndTime.Value.AddDays(1);

            if (model.Status == ProjectMemberStatus.离职)
                predicate = predicate.And(x => x.QuitTime < model.EntryOrQuitEndTime.Value);
            else
                predicate = predicate.And(x => x.EntryTime < model.EntryOrQuitEndTime.Value);
        }

        if (model.BeginTime.HasValue)
            predicate = predicate.And(x => x.UpdatedTime >= model.BeginTime.Value);

        if (model.EndTime.HasValue)
        {
            model.EndTime = model.EndTime.Value.Date.AddDays(1);
            predicate = predicate.And(x => x.UpdatedTime < model.EndTime.Value);
        }

        if (model.Status == ProjectMemberStatus.待入职)
        {
            if (model.HasIdCard == true)
                predicate = predicate.And(x => !string.IsNullOrEmpty(x.IdentityCard));
            else if (model.HasIdCard == false)
                predicate = predicate.And(x => string.IsNullOrEmpty(x.IdentityCard));
        }

        var sql = _context.Project_Member.Where(predicate);
        result.Total = sql.Count();

        result.Rows = sql.Where(predicate)
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new ProjectMemberInfo
        {
            Id = s.Id,
            ProjectId = s.ProjectId,
            IdentityCardType = s.IdentityCardType,
            IdentityCard = s.IdentityCard,
            IdentityCardName = s.IdentityCardName,
            Mobile = s.Mobile,
            EmploymentMode = s.EmploymentMode,
            ProbationMonth = s.ProbationMonth,
            PostName = s.PostName,
            Department = s.Department,
            EntryTime = s.EntryTime,
            EMail = s.EMail,
            Describe = s.Describe,
            UserId = s.UserId,
            RegionId = s.RegionId,
            Source = s.Source,
            Birthday = s.Birthday,
            Sex = s.Sex,
            InductionTimes = s.InductionTimes,
            ContractNum = s.ContractNum,
            QuitTime = s.QuitTime,
            AuditTime = s.AuditTime,
            QuitStatus = s.QuitStatus,
            CreatedTime = s.CreatedTime,
            UpdatedTime = s.UpdatedTime,
            SourceName = s.Source.GetDescription(),
            EmploymentModeName = s.EmploymentMode.GetDescription()
        }).ToList();

        foreach (var item in result.Rows)
        {
            item.SexName = item.Sex?.GetDescription();
            if (item.EntryTime.HasValue)
                item.RegularDate = item.EntryTime.Value.AddMonths(item.ProbationMonth);
        }

        return result;
    }

    public EmptyResponse DeleteProjectMember(DeleteProjectMember model)
    {
        var result = new EmptyResponse();

        if (!(model.Ids?.Count > 0))
            return result;

        model.Ids = model.Ids.Take(100).ToList();

        var canDeleteStatus = new List<ProjectMemberStatus> { ProjectMemberStatus.待入职, ProjectMemberStatus.离职 };
        var members = _context.Project_Member.Where(x => model.Ids.Contains(x.Id)
        && x.Project.HrId == _user.Id && canDeleteStatus.Contains(x.Status));

        _context.RemoveRange(members);

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        return result;
    }

    public async Task<EmptyResponse> ProjectMemberEntry(ProjectMemberEntry model)
    {
        var result = new EmptyResponse();

        if (!(model.Ids?.Count > 0))
            return result;

        model.Ids = model.Ids.Take(100).ToList();

        var canEntryStatus = new List<ProjectMemberStatus> { ProjectMemberStatus.待入职, ProjectMemberStatus.离职 };

        var members = _context.Project_Member.Where(x => model.Ids.Contains(x.Id)
        && x.Project.HrId == _user.Id && canEntryStatus.Contains(x.Status))
        .Select(s => new
        {
            s.Id,
            s.ProjectId
        }).ToList();

        if (members.Count == 0)
            return result;

        if (members.Select(s => s.ProjectId).Distinct().Count() > 1)
            throw new BadRequestException("不允许同时对多个项目进行操作");

        var projectId = members.Select(s => s.ProjectId).First();

        var lockerKey = $"{RedisKey.Lock.ProjectMember}:{projectId}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey, 10);

        if (locker == null)
            throw new BadRequestException("该项目正在操作中，请稍后尝试");

        await Task.FromResult(1);

        var ids = members.Select(s => s.Id).ToList();

        var updateMembers = _context.Project_Member.Where(x => ids.Contains(x.Id)).ToList();

        var needUpdatePostInfo = model.EntryTime.HasValue;

        if (needUpdatePostInfo)
        {
            if (!model.EmploymentMode.HasValue)
                throw new BadRequestException("请选择用工形式");

            if (!model.EntryTime.HasValue)
                throw new BadRequestException("请选择入职时间");

            if (model.ProbationMonth < 0)
                throw new BadRequestException("请选择试用期时长");

            if (string.IsNullOrWhiteSpace(model.PostName))
                throw new BadRequestException("职位不能为空");
        }

        foreach (var item in updateMembers)
        {
            if (!canEntryStatus.Contains(item.Status))
                continue;

            //如果没有身份证，跳过
            if (string.IsNullOrWhiteSpace(item.IdentityCard))
                continue;

            item.Status = ProjectMemberStatus.入职;
            item.QuitStatus = ProjectMemberQuitStatus.未离职;
            item.QuitTime = null;
            item.InductionTimes++;
            item.UpdatedTime = DateTime.Now;

            if (needUpdatePostInfo)
            {
                item.EntryTime = model.EntryTime;
                item.EmploymentMode = model.EmploymentMode!.Value;
                item.ProbationMonth = model.ProbationMonth;
                item.PostName = model.PostName;
                if (!string.IsNullOrEmpty(model.Department))
                    item.Department = model.Department;
                if (!string.IsNullOrEmpty(model.Describe))
                    item.Describe = model.Describe;
            }

            //添加记录
            var record = new Project_Member_Record
            {
                ProjectId = item.ProjectId,
                IdentityCardType = item.IdentityCardType,
                IdentityCard = item.IdentityCard,
                Mobile = item.Mobile,
                Type = ProjectMemberRecordType.入职,
                PostName = item.PostName,
                Department = item.Department,
                EntryTime = item.EntryTime,
                QuitTime = item.QuitTime
            };
            _context.Add(record);
        }

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        return result;
    }

    public async Task<EmptyResponse> ProjectMemberQuit(ProjectMemberQuit model)
    {
        var result = new EmptyResponse();

        ProjectMemberStatus? memberStatus = null;
        if (model.QuitStatus == ProjectMemberQuitStatus.待离职)
        {
            if (!model.QuitTime.HasValue)
                throw new BadRequestException("缺少离职时间");

            if (model.QuitTime.Value <= DateOnly.FromDateTime(DateTime.Today))
                throw new BadRequestException("离职时间早于今天，请选择直接离职");
            memberStatus = ProjectMemberStatus.入职;
        }
        else if (model.QuitStatus == ProjectMemberQuitStatus.离职)
        {
            if (!model.QuitTime.HasValue)
                throw new BadRequestException("缺少离职时间");

            if (model.QuitTime.Value > DateOnly.FromDateTime(DateTime.Today))
                throw new BadRequestException("离职时间大于今天，不允许直接离职");
            memberStatus = ProjectMemberStatus.离职;
        }
        else if (model.QuitStatus == ProjectMemberQuitStatus.未离职)
        {
            memberStatus = ProjectMemberStatus.入职;
        }
        else
            throw new BadRequestException("请求有误：缺少QuitStatus");

        if (!(model.Ids?.Count > 0))
            return result;

        model.Ids = model.Ids.Take(100).ToList();

        var canEntryStatus = new List<ProjectMemberStatus> { ProjectMemberStatus.入职 };

        var members = _context.Project_Member.Where(x => model.Ids.Contains(x.Id)
        && x.Project.HrId == _user.Id && canEntryStatus.Contains(x.Status))
        .Select(s => new
        {
            s.Id,
            s.ProjectId
        }).ToList();

        if (members.Count == 0)
            return result;

        if (members.Select(s => s.ProjectId).Distinct().Count() > 1)
            throw new BadRequestException("不允许同时对多个项目进行操作");

        var projectId = members.Select(s => s.ProjectId).First();

        var lockerKey = $"{RedisKey.Lock.ProjectMember}:{projectId}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey, 10);

        if (locker == null)
            throw new BadRequestException("该项目正在操作中，请稍后尝试");

        await Task.FromResult(1);

        var ids = members.Select(s => s.Id).ToList();

        var updateMembers = _context.Project_Member.Where(x => ids.Contains(x.Id)).ToList();
        foreach (var item in updateMembers)
        {
            if (!canEntryStatus.Contains(item.Status))
                continue;

            //如果状态不需要改变
            if (item.Status == memberStatus!.Value && item.QuitStatus == model.QuitStatus.Value)
                continue;

            if (model.QuitStatus == ProjectMemberQuitStatus.未离职)
            {
                if (item.QuitStatus != ProjectMemberQuitStatus.待离职)
                    continue;
                item.QuitTime = null;
            }
            else
                item.QuitTime = model.QuitTime;

            item.Status = memberStatus!.Value;
            item.QuitStatus = model.QuitStatus.Value;
            item.UpdatedTime = DateTime.Now;

            var recordType = item.QuitStatus switch
            {
                ProjectMemberQuitStatus.待离职 => ProjectMemberRecordType.待离职,
                ProjectMemberQuitStatus.未离职 => ProjectMemberRecordType.取消离职,
                ProjectMemberQuitStatus.离职 => ProjectMemberRecordType.离职,
                _ => ProjectMemberRecordType.离职
            };

            //添加记录
            var record = new Project_Member_Record
            {
                ProjectId = item.ProjectId,
                IdentityCardType = item.IdentityCardType,
                IdentityCard = item.IdentityCard,
                Mobile = item.Mobile,
                Type = recordType,
                PostName = item.PostName,
                Department = item.Department,
                EntryTime = item.EntryTime,
                QuitTime = item.QuitTime,
                Describe = model.Describe
            };
            _context.Add(record);
        }

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        return result;
    }

    public EmptyResponse EntryImport(EntryImport model)
    {
        var result = new EmptyResponse();

        if (string.IsNullOrWhiteSpace(model.FileUrl))
            throw new BadRequestException("文件url不能为空");

        if (string.IsNullOrWhiteSpace(model.FileName))
            throw new BadRequestException("文件名不能为空");

        if (!_context.Project.Any(x => x.ProjectId == model.ProjectId && x.HrId == _user.Id))
            throw new BadRequestException("项目不存在");

        var task = new Tasks
        {
            TargetId = model.ProjectId!,
            UserId = _user.Id,
            Type = TaskHandlingType.项目成员入职,
            Name = model.FileName,
            Content = JsonSerializer.SerializeToString(new ImportEntryContent
            {
                Mode = ProjectMemberImportEntryType.Excel模板,
                FileUrl = model.FileUrl
            })
        };
        _context.Add(task);

        _context.SaveChanges();

        return result;
    }

    public EmptyResponse EntryImportForNoah(EntryImportForNoah model)
    {
        var result = new EmptyResponse();

        if (string.IsNullOrWhiteSpace(model.NoahProjectNo))
            throw new BadRequestException("数字诺亚项目编号不能为空");

        if (!_context.Project.Any(x => x.ProjectId == model.ProjectId && x.HrId == _user.Id))
            throw new BadRequestException("项目不存在");

        var task = new Tasks
        {
            TargetId = model.ProjectId!,
            UserId = _user.Id,
            Type = TaskHandlingType.项目成员入职,
            Name = model.NoahProjectName,
            Content = JsonSerializer.SerializeToString(new ImportEntryContent
            {
                Mode = ProjectMemberImportEntryType.数字诺亚,
                NoahProjectNo = model.NoahProjectNo
            })
        };
        _context.Add(task);

        _context.SaveChanges();

        return result;
    }

    public GetMyTasksResponse GetMyTasks(GetMyTasks model)
    {
        var result = new GetMyTasksResponse();

        var memberTaskType = new TaskHandlingType[] { TaskHandlingType.项目成员入职, TaskHandlingType.项目成员离职 };
        var predicate = PredicateBuilder.New<Tasks>(x => x.UserId == _user.Id && memberTaskType.Contains(x.Type));

        var sql = _context.Tasks.Where(predicate);
        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new MyTaskInfo
        {
            CreatedTime = s.CreatedTime,
            EndTime = s.EndTime,
            Name = s.Name,
            ResultText = s.ResultText,
            Status = s.Status,
            TaskId = s.TaskId,
            Type = s.Type,
            Duplicate = s.Duplicate,
            Failed = s.Failed,
            Successful = s.Successful,
            Total = s.Total,
            Content = s.Content,
            StatusName = s.Status.GetDescription(),
            TypeName = s.Type.GetDescription(),
            ProjectName = s.Project.Agent_Ent.Name
        }).ToList();

        foreach (var item in result.Rows)
        {
            ImportEntryContent? content = null;
            try
            {
                content = JsonSerializer.DeserializeFromString<ImportEntryContent>(item.Content);
            }
            catch { }
            item.Mode = content?.Mode;
            item.ModeName = item.Mode?.GetDescription();
        }

        return result;
    }

    public GetMyTaskDetailsResponse<MyTaskContent> GetMyTaskDetails(GetMyTaskDetails model)
    {
        var result = new GetMyTaskDetailsResponse<MyTaskContent>();

        var predicate = PredicateBuilder.New<Tasks_Detail>(x => x.TaskId == model.TaskId && x.Tasks.UserId == _user.Id);

        if (model.Result.HasValue)
            predicate = predicate.And(x => x.Result == model.Result);

        var sql = _context.Tasks_Detail.Where(predicate);
        result.Total = sql.Count();

        result.Rows = sql.Where(predicate)
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new MyTaskDetailInfo<MyTaskContent>
        {
            CreatedTime = s.CreatedTime,
            Result = s.Result,
            ResultText = s.ResultText,
            TaskId = s.TaskId,
            ContentStr = s.Content,
            GroupStatus = s.GroupStatus
        }).ToList();

        foreach (var item in result.Rows)
        {
            item.Content = JsonSerializer.DeserializeFromString<MyTaskContent?>(item.ContentStr);
            item.ContentStr = null;
            if (string.IsNullOrWhiteSpace(item.ResultText) && item.GroupStatus == TaskHandlingGroupStatus.重复)
                item.ResultText = "重复";
        }

        return result;
    }

    public async Task<GetNoahProjectByNoResponse> GetNoahProjectByNo(GetNoahProjectByNo model)
    {
        var result = new GetNoahProjectByNoResponse();
        var noahData = await _figureNoahHelper.GetProjectMember(model.NoahProjectNo ?? string.Empty, 1, 1);

        if (string.IsNullOrWhiteSpace(noahData?.fwzybm) && string.IsNullOrWhiteSpace(noahData?.xmname))
            throw new BadRequestException("数字诺亚项目不存在");

        result.Members = noahData?.allcount ?? 0;
        result.UserName = noahData?.fwzymc;
        result.Name = noahData?.xmname;

        return result;
    }
}