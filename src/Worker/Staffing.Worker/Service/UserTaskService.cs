using Config;
using Config.CommonModel.Tasks;
using Config.Enums;
using EntityFrameworkCore.AutoHistory.Extensions;
using Flurl.Http;
using FreeRedis;
using Infrastructure.Aliyun;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Microsoft.Extensions.Options;

namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class UserTaskService
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _logger;
    private readonly CommonDicService _commonDicService;
    private readonly FigureNoahHelper _figureNoahHelper;
    private readonly SmsHelper _smsHelper;
    private readonly Infrastructure.Common.SMSHelper _commonSMSHelper;
    private readonly WeChatHelper _weChatHelper;
    private ConfigManager _config;
    public UserTaskService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger,
    CommonDicService commonDicService, FigureNoahHelper figureNoahHelper, SmsHelper smsHelper,
    Infrastructure.Common.SMSHelper commonSMSHelper,
    WeChatHelper weChatHelper, IOptionsSnapshot<ConfigManager> config)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _commonDicService = commonDicService;
        _figureNoahHelper = figureNoahHelper;
        _smsHelper = smsHelper;
        _commonSMSHelper = commonSMSHelper;
        _weChatHelper = weChatHelper;
        _config = config.Value;
    }

    /// <summary>
    /// 项目成员入职导入
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task ProjectMemberEntryImport(CancellationToken cancel)
    {
        //处理消息
        while (!cancel.IsCancellationRequested)
        {
            var currentTaskId = string.Empty;
            try
            {
                using var _context = _contextFactory.CreateDbContext();
                var task = _context.Tasks.Where(x => x.Status == TaskHandlingStatus.进行中
                && x.Type == TaskHandlingType.项目成员入职)
                .OrderBy(o => o.CreatedTime).FirstOrDefault();

                if (task == null)
                {
                    await Task.Delay(3000);
                    continue;
                }

                currentTaskId = task.TaskId;

                var lockerKey = $"{RedisKey.Lock.ProjectMember}:{task.TargetId}";

                //redis分布式锁，自动释放
                using var locker = MyRedis.TryLock(lockerKey, 300);

                if (locker == null)
                {
                    await Task.Delay(1000);
                    continue;
                }

                if (!_context.Project.Any(x => x.ProjectId == task.TargetId))
                    throw new Exception("项目不存在");

                var taskContent = JsonSerializer.DeserializeFromString<ImportEntryContent>(task.Content);
                if (taskContent == null)
                    throw new Exception("任务内容解析错误");

                var members = new List<ImportEntry>();

                if (taskContent.Mode == ProjectMemberImportEntryType.Excel模板)
                {
                    var fileBytes = await taskContent.FileUrl.GetAsync().ReceiveBytes();

                    var importer = new ExcelImporter();
                    var import = await importer.Import<ImportEntry>(new MemoryStream(fileBytes));

                    if (import is null)
                        throw new Exception("解析文件为空");

                    if (import?.Exception != null)
                        throw import.Exception;

                    foreach (var item in import!.Data)
                    {
                        if (Enum.TryParse<IdCardType>(item.IdentityCardTypeName, out var a))
                            item.IdentityCardType = a;

                        if (Enum.TryParse<ProjectMemberEmploymentMode>(item.EmploymentModeName, out var b))
                            item.EmploymentMode = b;
                    }
                    members = import!.Data.ToList();
                }
                else if (taskContent.Mode == ProjectMemberImportEntryType.数字诺亚)
                {
                    var noahTaskKey = $"task:noah:{task.TaskId}";
                    MyRedis.Client.Del(noahTaskKey);

                    var pageIndex = 1;
                    var pagesize = 100;

                    while (true)
                    {
                        var noahMemberResp = await _figureNoahHelper.GetProjectMember(taskContent.NoahProjectNo ?? string.Empty, pageIndex, pagesize);
                        var noahMember = noahMemberResp.data.rows;
                        if (noahMember.Count == 0)
                            break;

                        var pageMembers = noahMember.Select(s => new ImportEntry
                        {
                            Department = string.Empty,
                            EMail = s.YG_EMAIL,
                            EmploymentMode = (ProjectMemberEmploymentMode)(s.YG_LABORFORM ?? 0),
                            EntryTime = s.LDHT_SENDTIME?.ToNullableDate(),
                            IdentityCard = s.YG_IDNUMBER,
                            IdentityCardName = s.YG_NAME,
                            Mobile = s.YG_MOBILEPHONE,
                            IdentityCardType = IdCardType.身份证,
                            PostName = s.LDHT_GW,
                            ProbationMonth = s.LDHT_PROBATION
                        }).ToArray();
                        MyRedis.Client.RPush(noahTaskKey, pageMembers);
                        pageIndex++;

                        await Task.Delay(10);
                    }
                    members = MyRedis.Client.LRange<ImportEntry>(noahTaskKey, 0, 99999).ToList();
                }
                else
                    throw new Exception("不支持的导入方式");

                //查看已经处理了多少，跳过不处理
                var taskDetailCount = _context.Tasks_Detail.Where(x => x.TaskId == task.TaskId).Count();
                members = members.Skip(taskDetailCount).ToList();

                var i = 0;
                foreach (var item in members)
                {
                    i++;
                    //如果服务即将停止，不再继续处理，防止处理了一半的情况
                    if (cancel.IsCancellationRequested)
                        return;

                    var groupStatus = TaskHandlingGroupStatus.默认;

                    var errorMsg = string.Empty;
                    using var _context2 = _contextFactory.CreateDbContext();
                    try
                    {
                        if (string.IsNullOrWhiteSpace(item.IdentityCardName))
                            throw new BadRequestException("缺少姓名");

                        if (!item.IdentityCardType.HasValue)
                            throw new BadRequestException("缺少证件类型");

                        if (string.IsNullOrWhiteSpace(item.IdentityCard))
                            throw new BadRequestException("缺少证件号");
                        else if (item.IdentityCardType == IdCardType.身份证 && !Tools.Certification(item.IdentityCard))
                            throw new BadRequestException("身份证号不合法");

                        if (!item.EmploymentMode.HasValue)
                            throw new BadRequestException("缺少用工形式");

                        if (!item.EntryTime.HasValue)
                            throw new BadRequestException("缺少入职时间");

                        if (item.ProbationMonth < 0)
                            throw new BadRequestException("缺少试用期时长");

                        if (string.IsNullOrWhiteSpace(item.PostName))
                            throw new BadRequestException("缺少职位");

                        if (!DataValid.IsMobile(item.Mobile))
                            throw new BadRequestException("手机号格式不正确");

                        //入职
                        var updateMember = _context2.Project_Member
                        .Where(x => x.ProjectId == task.TargetId && x.IdentityCard == item.IdentityCard)
                        .FirstOrDefault();

                        if (updateMember != null)
                        {
                            groupStatus = TaskHandlingGroupStatus.重复;
                            if (updateMember.Status != ProjectMemberStatus.入职)
                                updateMember.InductionTimes++;
                        }
                        else
                        {
                            updateMember = new Project_Member
                            {
                                ProjectId = task.TargetId!,
                                IdentityCard = item.IdentityCard
                            };
                            _context2.Add(updateMember);
                        }
                        var idCardInfo = Tools.GetIdCardInfo(item.IdentityCard);
                        updateMember.Source = taskContent.Mode switch
                        {
                            ProjectMemberImportEntryType.Excel模板 => ProjectMemberSource.Excel导入,
                            ProjectMemberImportEntryType.数字诺亚 => ProjectMemberSource.数字诺亚,
                            _ => ProjectMemberSource.手动添加
                        };
                        updateMember.Status = ProjectMemberStatus.入职;
                        updateMember.QuitStatus = ProjectMemberQuitStatus.未离职;
                        updateMember.QuitTime = null;
                        updateMember.IdentityCardType = item.IdentityCardType.Value;
                        updateMember.IdentityCardName = item.IdentityCardName;
                        updateMember.Mobile = item.Mobile;
                        updateMember.Birthday = idCardInfo?.Birthday;
                        updateMember.Sex = idCardInfo?.Sex;
                        updateMember.RegionId = idCardInfo?.RegionId ?? string.Empty;
                        updateMember.EmploymentMode = item.EmploymentMode.Value;
                        updateMember.ProbationMonth = item.ProbationMonth;
                        updateMember.PostName = item.PostName;
                        updateMember.Department = item.Department;
                        updateMember.EntryTime = DateOnly.FromDateTime(item.EntryTime.Value);
                        updateMember.EMail = item.EMail;
                        updateMember.UpdatedTime = DateTime.Now;

                        _context2.SaveChanges();

                        //添加记录
                        var record = new Project_Member_Record
                        {
                            ProjectId = updateMember.ProjectId,
                            IdentityCardType = updateMember.IdentityCardType,
                            IdentityCard = updateMember.IdentityCard,
                            Mobile = updateMember.Mobile,
                            Type = ProjectMemberRecordType.入职,
                            PostName = updateMember.PostName,
                            Department = updateMember.Department,
                            EntryTime = updateMember.EntryTime,
                            QuitTime = updateMember.QuitTime,
                            Describe = taskContent.Mode.ToString()
                        };
                        _context2.Add(record);
                    }
                    catch (BadRequestException e)
                    {
                        errorMsg = e.Message;
                    }
                    catch (Exception e)
                    {
                        _logger.Error($"项目成员入职导入出错d", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(item));
                        errorMsg = "未知错误";
                    }
                    finally
                    {
                        var taskDetail = new Tasks_Detail
                        {
                            TaskId = task.TaskId,
                            GroupStatus = groupStatus,
                            Content = JsonSerializer.SerializeToString(item),
                            Result = string.IsNullOrEmpty(errorMsg) ? TaskHandlingResult.成功 : TaskHandlingResult.失败,
                            ResultText = errorMsg
                        };
                        _context2.Add(taskDetail);
                        _context2.SaveChanges();
                    }

                    //如果是100的倍或最后一条，更新统计数字
                    if (i % 100 == 0 || i == members.Count)
                    {
                        var resultGroup = _context2.Tasks_Detail.Where(x => x.TaskId == task.TaskId)
                        .GroupBy(g => g.Result).Select(s => new { s.Key, Count = s.Count() }).ToList();

                        var statusGroup = _context2.Tasks_Detail.Where(x => x.TaskId == task.TaskId)
                        .GroupBy(g => g.GroupStatus).Select(s => new { s.Key, Count = s.Count() }).ToList();

                        var updateTask = new Tasks
                        {
                            TaskId = task.TaskId
                        };
                        _context2.Attach(updateTask);
                        updateTask.Total = resultGroup.Sum(s => s.Count);
                        updateTask.Successful = resultGroup.FirstOrDefault(x => x.Key == TaskHandlingResult.成功)?.Count ?? 0;
                        updateTask.Failed = resultGroup.FirstOrDefault(x => x.Key == TaskHandlingResult.失败)?.Count ?? 0;
                        updateTask.Duplicate = statusGroup.FirstOrDefault(x => x.Key == TaskHandlingGroupStatus.重复)?.Count ?? 0;
                        _context2.SaveChanges();
                    }
                    await Task.Delay(3);
                }

                task.Status = TaskHandlingStatus.已完成;
                task.EndTime = DateTime.Now;
                _context.SaveChanges();
            }
            catch (Exception e)
            {
                try
                {
                    using var _context = _contextFactory.CreateDbContext();
                    var task = _context.Tasks.First(x => x.TaskId == currentTaskId);
                    if (task.Status == TaskHandlingStatus.进行中)
                    {
                        task.Status = TaskHandlingStatus.失败;
                        task.ResultText = "未知错误";
                        task.EndTime = DateTime.Now;
                        _context.SaveChanges();
                    }
                }
                catch
                {
                    _logger.Error($"项目成员入职导入日志出错", Tools.GetErrMsg(e), currentTaskId);
                }

                _logger.Error($"项目成员入职导入出错", Tools.GetErrMsg(e), currentTaskId);
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 处理预离职员工
    /// </summary>
    public void ProjectMemberQuit(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            var today = DateTime.Today;
            if (DateTime.Now >= today.AddHours(1))
                return;

            using var _context = _contextFactory.CreateDbContext();

            var todayDate = DateOnly.FromDateTime(DateTime.Today);
            var expProj = _context.Project_Member.Where(x => x.Status == ProjectMemberStatus.入职
            && x.QuitStatus == ProjectMemberQuitStatus.待离职 && x.QuitTime <= todayDate)
            .OrderBy(o => o.CreatedTime)
            .Take(10).ToList();

            foreach (var item in expProj)
            {
                item.Status = ProjectMemberStatus.离职;
                item.QuitStatus = ProjectMemberQuitStatus.离职;
                item.UpdatedTime = DateTime.Now;

                var recordType = item.QuitStatus switch
                {
                    ProjectMemberQuitStatus.待离职 => ProjectMemberRecordType.待离职,
                    ProjectMemberQuitStatus.未离职 => ProjectMemberRecordType.取消离职,
                    ProjectMemberQuitStatus.离职 => ProjectMemberRecordType.离职,
                    _ => ProjectMemberRecordType.离职
                };

                //添加记录
                var record = new Project_Member_Record
                {
                    ProjectId = item.ProjectId,
                    IdentityCardType = item.IdentityCardType,
                    IdentityCard = item.IdentityCard,
                    Mobile = item.Mobile,
                    Type = recordType,
                    PostName = item.PostName,
                    Department = item.Department,
                    EntryTime = item.EntryTime,
                    QuitTime = item.QuitTime,
                    Describe = "预离职到期自动离职"
                };
                _context.Add(record);
            }

            _context.EnsureAutoHistory();
            _context.SaveChanges();
        }
        catch (Exception e)
        {
            _logger.Error("处理预离职员工出错", Tools.GetErrMsg(e));
        }
    }

    /// <summary>
    /// 业务短信发送任务
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task HrSendSms(CancellationToken cancel)
    {
        //处理消息
        while (!cancel.IsCancellationRequested)
        {
            var currentTaskId = string.Empty;
            try
            {
                using var _context = _contextFactory.CreateDbContext();
                var task = _context.Tasks.Where(x => x.Status == TaskHandlingStatus.进行中
                && x.Type == TaskHandlingType.职位邀约短信)
                .OrderBy(o => o.CreatedTime).FirstOrDefault();

                if (task == null)
                {
                    await Task.Delay(3000);
                    continue;
                }

                currentTaskId = task.TaskId;

                var lockerKey = $"{RedisKey.Lock.ProjectMember}:{task.TargetId}";

                //redis分布式锁，自动释放
                using var locker = MyRedis.TryLock(lockerKey, 300);

                if (locker == null)
                {
                    await Task.Delay(1000);
                    continue;
                }

                var taskContent = JsonSerializer.DeserializeFromString<SendBusinessSms>(task.Content);
                if (taskContent == null)
                    throw new Exception("任务内容解析错误");

                var toUsers = taskContent.ToUsers.OrderBy(o => o.Mobile).ToList();

                //查看已经处理了多少，跳过不处理
                var taskDetailCount = _context.Tasks_Detail.Where(x => x.TaskId == task.TaskId).Count();
                toUsers = toUsers.Skip(taskDetailCount).ToList();

                var i = 0;
                foreach (var item in toUsers)
                {
                    i++;
                    //如果服务即将停止，不再继续处理，防止处理了一半的情况
                    if (cancel.IsCancellationRequested)
                        return;

                    var groupStatus = TaskHandlingGroupStatus.默认;

                    var errorMsg = string.Empty;
                    using var _context2 = _contextFactory.CreateDbContext();
                    try
                    {
                        taskContent.SmsTempJson.seeker = $"{item.Name ?? string.Empty}您好！";
                        //发短信
                        var smsResult = _smsHelper.SendSMSAliyunForJumpApplet(item.Mobile!, taskContent.SmsTempCode!, taskContent.SmsTempJson!);

                        if (!smsResult.Success)
                            throw new Exception($"{smsResult.Code}_{smsResult.Message}");
                    }
                    catch (Exception e)
                    {
                        _logger.Error($"发送业务短信出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(item));
                        errorMsg = "未知错误";
                    }
                    finally
                    {
                        var taskDetail = new Tasks_Detail
                        {
                            TaskId = task.TaskId,
                            GroupStatus = groupStatus,
                            Content = JsonSerializer.SerializeToString(item),
                            Result = string.IsNullOrEmpty(errorMsg) ? TaskHandlingResult.成功 : TaskHandlingResult.失败,
                            ResultText = errorMsg
                        };
                        _context2.Add(taskDetail);
                        _context2.SaveChanges();
                    }

                    //如果是100的倍或最后一条，更新统计数字
                    if (i % 100 == 0 || i == toUsers.Count)
                    {
                        var resultGroup = _context2.Tasks_Detail.Where(x => x.TaskId == task.TaskId)
                        .GroupBy(g => g.Result).Select(s => new { s.Key, Count = s.Count() }).ToList();

                        var updateTask = new Tasks
                        {
                            TaskId = task.TaskId
                        };
                        _context2.Attach(updateTask);
                        updateTask.Total = resultGroup.Sum(s => s.Count);
                        updateTask.Successful = resultGroup.FirstOrDefault(x => x.Key == TaskHandlingResult.成功)?.Count ?? 0;
                        updateTask.Failed = resultGroup.FirstOrDefault(x => x.Key == TaskHandlingResult.失败)?.Count ?? 0;
                        _context2.SaveChanges();
                    }
                    await Task.Delay(3);
                }

                task.Status = TaskHandlingStatus.已完成;
                task.EndTime = DateTime.Now;
                _context.SaveChanges();
            }
            catch (Exception e)
            {
                try
                {
                    using var _context = _contextFactory.CreateDbContext();
                    var task = _context.Tasks.First(x => x.TaskId == currentTaskId);
                    if (task.Status == TaskHandlingStatus.进行中)
                    {
                        task.Status = TaskHandlingStatus.失败;
                        task.ResultText = "未知错误";
                        task.EndTime = DateTime.Now;
                        _context.SaveChanges();
                    }
                }
                catch
                {
                    _logger.Error($"发送业务短信出错日志出错", Tools.GetErrMsg(e), currentTaskId);
                }

                _logger.Error($"发送业务短信出错", Tools.GetErrMsg(e), currentTaskId);
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 通用短信发送
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task CommonSendSms(CancellationToken cancel)
    {
        //处理消息
        while (!cancel.IsCancellationRequested)
        {
            var currentTaskId = 0;
            try
            {
                using var _context = _contextFactory.CreateDbContext();
                var task = _context.Sms_Tasks.Where(x => x.Status == SmsTasksStatus.进行中
                && x.ExeTime < DateTime.Now)
                .OrderBy(o => o.ExeTime).FirstOrDefault();

                if (task == null)
                {
                    await Task.Delay(3000);
                    continue;
                }

                currentTaskId = task.Id;

                var lockerKey = $"{RedisKey.Lock.SmsTasks}:{task.Id}";

                //redis分布式锁，自动释放
                using var locker = MyRedis.TryLock(lockerKey, 300);

                if (locker == null)
                {
                    await Task.Delay(1000);
                    continue;
                }

                task.Status = SmsTasksStatus.已完成;
                _context.SaveChanges();

                var toUsers = _context.Sms_Tasks_Detail.AsNoTracking()
                .Where(x => x.TaskId == task.Id && x.Status == SmsTasksDetailStatus.待处理)
                .Select(s => new Sub_SendSms
                {
                    Id = s.Id,
                    JsonData = s.JsonData,
                    Mobile = s.Mobile,
                    TempCode = task.SenderType == SmsTasksSenderType.阿里云 ? s.TempCode : task.TaskTemplateContent,
                    Sender = task.SenderType == SmsTasksSenderType.阿里云 ? SMSSender.阿里云 : SMSSender.庄点科技drondea,
                    Sgin = "诺快聘"
                }).ToArray();
                if (toUsers.Count() > 0)
                    MyRedis.Client.RPush(SubscriptionKey.SendSms, toUsers);

            }
            catch (Exception e)
            {
                _logger.Error($"发送短信出错", Tools.GetErrMsg(e), currentTaskId);
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 发短信消息队列
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SendSms(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.LPop(SubscriptionKey.SendSms);

                if (string.IsNullOrWhiteSpace(msg))
                {
                    await Task.Delay(500);
                    continue;
                }

                // 处理消息
                var model = JsonSerializer.DeserializeFromString<Sub_SendSms>(msg);

                //发短信
                bool Success = false; string? Message = null, Code = string.Empty;
                if (model.Sender == SMSSender.阿里云)
                {
                    var smsResult = _smsHelper.SendSmsByJson(model.Mobile!, model.TempCode, model.JsonData, model.Sgin);
                    Success = smsResult.Success;
                    Message = smsResult.Message;
                    Code = smsResult.Code;
                }
                else if (model.Sender == SMSSender.庄点科技drondea)
                {
                    //获取小程序url,组装最新发送内容
                    string paramUrl = JObject.Parse(model.JsonData)["Url"]!.ToString();
                    string[] paramUrl_arr = paramUrl.Split("?");
                    string url = await _weChatHelper.GetWxShortLinkForJump(ClientApps.SeekerApplet.AppID, paramUrl_arr[0], paramUrl_arr.Length == 1 ? "" : paramUrl_arr[1]);

                    //下发短信
                    var smsResult = await _commonSMSHelper.ZhuangDianSendSMS(model.Mobile, model.TempCode.Replace(paramUrl, url));
                    Success = smsResult.Success;
                    Message = smsResult.resp.desc;
                    Code = smsResult.resp.result;
                }
                else if (model.Sender == SMSSender.庄点科技通用)
                {
                    //下发短信
                    var smsResult = await _commonSMSHelper.ZhuangDianSendSMS(model.Mobile, model.Content);
                    Success = smsResult.Success;
                    Message = smsResult.resp.desc;
                    Code = smsResult.resp.result;
                }

                //更新DB
                using var _context = _contextFactory.CreateDbContext();
                var td = _context.Sms_Tasks_Detail.FirstOrDefault(x => x.Id == model.Id);
                if (td != null)
                {
                    td.SendTime = DateTime.Now;
                    td.Status = Success ? SmsTasksDetailStatus.成功 : SmsTasksDetailStatus.失败;
                    td.Remark = $"{Code}_{Message}";

                    _context.SaveChanges();
                }

                await Task.Delay(20);//休息时间改为20毫秒，防止腾讯取短链接超限(每分钟限制5000条)
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.SendSms}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(100);
            }
        }
    }

    /// <summary>
    /// 统计hr信息
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task UpdateHrData(CancellationToken cancel)
    {
        //处理消息
        while (!cancel.IsCancellationRequested)
        {
            ZMember? msg = null;
            try
            {
                // msg = MyRedis.Client.SPop<Sub_UpdateHrData>(SubscriptionKey.UpdateHrData);

                // if (msg == null)
                // {
                //     await Task.Delay(500);
                //     continue;
                // }

                var maxTime = DateTime.Now.AddSeconds(-10).ToUnixTimeMs();

                //查询到时间的消息
                msg = MyRedis.Client.ZRangeByScoreWithScores(SubscriptionKey.UpdateHrData, 0, maxTime, 0, 1).FirstOrDefault();

                if (msg == null || (msg is IDictionary<string, object> dict && dict.Count == 0))
                {
                    await Task.Delay(10000);
                    continue;
                }

                //取出之后移除
                MyRedis.Client.ZRem(SubscriptionKey.UpdateHrData, msg.member);

                using var _context = _contextFactory.CreateDbContext();

                var hrData = _context.User_Hr_Data.Where(x => x.UserId == msg.member).FirstOrDefault();

                if (hrData == null)
                {
                    _logger.Error($"{SubscriptionKey.UpdateHrData}消息处理出错", "hrData不存在", $"{msg?.member}");
                    continue;
                }

                var projs = _context.Project_Team.Where(x => x.HrId == msg.member)
                    .GroupBy(g => new { g.Project.Status, g.Type })
                    .Select(s => new
                    {
                        s.Key,
                        Ct = s.Count()
                    }).ToList();

                hrData.MyProjectTotal = projs.Where(x => x.Key.Type == HrProjectType.自己).Sum(s => s.Ct);
                hrData.TeamProjectTotal = projs.Where(x => x.Key.Type == HrProjectType.协同).Sum(s => s.Ct);
                hrData.MyProjectOnline = projs.Where(x => x.Key.Type == HrProjectType.自己 && x.Key.Status == ProjectStatus.已上线).Sum(s => s.Ct);
                hrData.TeamProjectOnline = projs.Where(x => x.Key.Type == HrProjectType.协同 && x.Key.Status == ProjectStatus.已上线).Sum(s => s.Ct);

                var posts = _context.Post_Team.Where(x => x.Project_Team.HrId == msg.member && !x.Post.Deleted)
                     .GroupBy(g => new { g.Show, g.Project_Team.Type })
                     .Select(s => new
                     {
                         s.Key,
                         Ct = s.Count(),
                         RecruiNum = s.Sum(c => c.Post.DeliveryNumber)
                     }).ToList();

                hrData.MyPostTotal = posts.Where(x => x.Key.Type == HrProjectType.自己).Sum(s => s.Ct);
                hrData.TeamPostTotal = posts.Where(x => x.Key.Type == HrProjectType.协同).Sum(s => s.Ct);
                hrData.MyPostOnline = posts.Where(x => x.Key.Type == HrProjectType.自己 && x.Key.Show).Sum(s => s.Ct);
                hrData.TeamPostOnline = posts.Where(x => x.Key.Type == HrProjectType.协同 && x.Key.Show).Sum(s => s.Ct);
                hrData.MyRecruitOnline = posts.Where(x => x.Key.Type == HrProjectType.自己 && x.Key.Show).Sum(s => s.RecruiNum);
                hrData.TeamRecruitOnline = posts.Where(x => x.Key.Type == HrProjectType.协同 && x.Key.Show).Sum(s => s.RecruiNum);
                hrData.RecruitTotal = posts.Where(x => x.Key.Type == HrProjectType.自己).Sum(s => s.RecruiNum);

                var rec = _context.Recruit.Where(x => x.HrId == msg.member)
                    .GroupBy(g => new { RecruitStatus = (Config.Enums.RecruitStatus?)g.Status, ProjectStatus = (Config.Enums.ProjectStatus?)g.Post_Delivery.Post.Project.Status })
                    .Select(s => new
                    {
                        s.Key,
                        Ct = s.Count()
                    }).ToList();
                hrData.RecruitHrScreening = rec.Where(x => x.Key.ProjectStatus == ProjectStatus.已上线 && x.Key.RecruitStatus == RecruitStatus.HrScreening).Sum(s => s.Ct);
                hrData.RecruitInterviewerScreening = rec.Where(x => x.Key.ProjectStatus == ProjectStatus.已上线 && x.Key.RecruitStatus == RecruitStatus.InterviewerScreening).Sum(s => s.Ct);
                hrData.RecruitInterview = rec.Where(x => x.Key.ProjectStatus == ProjectStatus.已上线 && x.Key.RecruitStatus == RecruitStatus.Interview).Sum(s => s.Ct);
                hrData.RecruitOffer = rec.Where(x => x.Key.ProjectStatus == ProjectStatus.已上线 && x.Key.RecruitStatus == RecruitStatus.Offer).Sum(s => s.Ct);
                hrData.RecruitInduction = rec.Where(x => x.Key.ProjectStatus == ProjectStatus.已上线 && x.Key.RecruitStatus == RecruitStatus.Induction).Sum(s => s.Ct);
                hrData.RecruitContract = rec.Where(x => x.Key.ProjectStatus == ProjectStatus.已上线 && x.Key.RecruitStatus == RecruitStatus.Contract).Sum(s => s.Ct);
                hrData.RecruitFileAway = rec.Where(x => x.Key.ProjectStatus == ProjectStatus.已上线 && x.Key.RecruitStatus == RecruitStatus.FileAway).Sum(s => s.Ct);

                hrData.RecruitDelivery = rec.Sum(s => s.Ct);
                hrData.RecruitInterviewTotal = _context.Recruit_Interview.Where(x => x.HrId == msg.member).Select(s => s.RecruitId).Distinct().Count();
                hrData.RecruitInductionTotal = rec.Where(x => x.Key.RecruitStatus == RecruitStatus.Induction).Sum(s => s.Ct);

                _context.SaveChanges();
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.UpdateHrData}消息处理出错", Tools.GetErrMsg(e), $"{msg?.member}");
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }
}