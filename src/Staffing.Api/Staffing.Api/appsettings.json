﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "sqlCon": "Server=**************;port=3306;Database=staffing;User=root;Password=**************;max pool size=100;connect timeout = 30",
    //"sqlCon": "Server=rm-2zeqj9nyu1s4b81mg4o.mysql.rds.aliyuncs.com;port=3306;Database=staffing;User=staffingreadonly;Password=***************;max pool size=100;connect timeout = 30",
    "noahCon": "Server=rm-2ze6r4dh3n01af30azo.mysql.rds.aliyuncs.com;port=3306;Database=ehrx_customer;User=nkp_view;Password=****************;SslMode=None;max pool size=100;connect timeout = 30"
  },
  "Security": {
    "AesKey": "SzlNTVWC+bh6RG2OeBw)qgor5#HF0JjKmupU"
  },
  "WeChatPay": {
    "MchId": "1607031206",
    "APIKey": "",
    "APIv3Key": "",
    "AppId": "wx8b4490f70e538c0e"
  },
  "LogsOptions": {
    "IsLocalDebug": true,
    "Endpoint": "cn-beijing.log.aliyuncs.com",
    "Project": "noahplatform",
    "LogStore": "noahlogs-test",
    "AccessKeyId": "LTAI4Fq6HCM4X2ZDWVZCUagu",
    "SecretAccessKey": "A1hDO4ehDokLnOK2EubAJVJX-9hcJvCNn24cksMqWG8="
  },
  "SenparcWeixinSetting": {
    "Token": "9c5404bde33e430abd837de213223c9c",
    "EncodingAESKey": "buFlRZN3agAXuLUO8zvgotZkXw1FYbaIhbiJRMOj6lT"
  },
  "SenparcWeixinSetting2": {
    "Token": "596450613DE44EB5944D21C19396385C",
    "EncodingAESKey": "cI4yRneOT9G0B7Xms42YrEfxu7f5EwAkZqFYka73qRR"
  },
  "Settings": {
    "OAuthServer": "https://test-auth.nuopin.cn/",
    "RedisAddress": "**************,password=k8s_20220315:4vA0KjQFDzB1VhUn,defaultDatabase=8",
    "ElasticSearch": {
      "Address": "http://**************:9200",
      "UserName": "elastic",
      "Password": "elastic@9128"
    },
    "PublicRedisAddress": "r-2ze75zijbicae2qi2mpd.redis.rds.aliyuncs.com,password=NZPredis!@#$%,defaultDatabase=7",
    "ESign": {
      "Domain": "https://smlopenapi.esign.cn",
      "AppId": "7438802758",
      "Secret": "36396f841422f46da92ec629604a0090"
    },
    "Canal": {
      "Server": "**************",
      "Port": 11111,
      "ClientId": "111"
    },
    "NoahDataApiDomain": "https://data.nuopin.cn",
    "ParseResumeDomain": "http://************",
    "ParseResumeModeType": "fast",
    "DataRdsServer": "https://test-openapi.nuopin.cn",
    "DataKafkaServer": "***********:9092,***********:9092",
    "DataKafkaTopic": "linggong_user_post_action_test",
    "TencentOptions": {
      "FaceIdSecretId": "AKIDrYy82g4B2cvxjFUTBcplQpypPjmY4JHy",
      "FaceIdSecretKey": "RdXBYeT0GEwNYR5SXLsBE0VkiPp0uXx7",
      "ImAppId": "1400690536",
      "ImAppKey": "840732c056f38994c7734ae82507ba480c38eab74233f90fd901c37e72a5f32c"
    },
    "Ndn": {
      "ApiDomain": "http://task.royeinfo.com:9999",
      "OAuthDomain": "http://*************:3000",
      "ClientId": "fenrun",
      "ClientSecret": "fenrun",
      "GrantType": "client_credentials",
      "NuoPinBookCode": "38"
    },
    //"Ndn": {
    //  "ApiDomain": "http://ehr.hbhro.com",
    //  "OAuthDomain": "http://auth.hbhro.com",
    //  "ClientId": "fenrun",
    //  "ClientSecret": "fenrun",
    //  "GrantType": "client_credentials",
    //  "NuoPinBookCode": "38"
    //},
    "Aliyun": {
      "AliSMS": {
        "AccessKeyId": "LTAI4Fq6HCM4X2ZDWVZCUagu",
        "AccessKeySecret": "******************************",
        "Domain": "dysmsapi.aliyuncs.com",
        "Version": "2017-05-25",
        "Action": "SendSms",
        "SignName": "诺快聘",
        "TemplateCode": "SMS_190788647",
        "OfferTemplateCode": "SMS_244985338",
        "InterviewTemplateCode": "SMS_245020282",
        "ExpireSeconds": 300,
        "SendInterval": 59,
        "MaxEveryDay": 20
      },
      "FullAccess": {
        "AccessKeyId": "LTAI5tAj9XP4rRbJGKpoLQiC",
        "AccessKeySecret": "******************************"
      },
      "Oss": {
        "Dir": "teststaffing"
      }
    },
    "ServiceKeys": [
      "C1B7FDE9F7A742D2A87E641C7D8B99NC", // 诺快聘
      "A1B7FD89F7A74212A87E641C7D8B99E7",
      "8F842AACB09F4A3986314929CA99B553",
      "CTCKA3XWCDDZ0ZKJJG3W7GEDGG7D8631", // 诺优考
      "TJKQ9XWCDJZ0ZKJJG4W7GEDGG7D9731" // 数字诺亚
    ],
    "InternalService": {
      "Domain": "https://test-nkpapi.nuopin.cn",
      "LocalServiceKeys": {
        "Settlement": "C1B7FDE9F7A742D2A87E641C7D8B99NC"
      }
    },
    "RequestLog": "true",
    "WeChat": {
      "SeekerApplet": {
        "AppId": "wx8b4490f70e538c0e",
        "Secret": "eef62b094083343fd25b8d37f472a1ad"
      },
      "InterviewApplet": {
        "AppId": "wxe38d12b563661718",
        "Secret": "64e77b38b3ba84adac8fde1e22a45036"
      },
      "HrApplet": {
        "AppId": "wx96b65aafffa82a6b",
        "Secret": "36695648cb721126328ca0e57f8e8b44"
      }
    },
    "ZhuangDian": {
      "Enterprise_No": "10383",
      "Account": "nynyk",
      "Http_Sign_Key": "0D25142A1FE08BDF97F3BD230E14A8D5",
      "Api_IP": "api.sms.drondea.com",
      "SMSSignature": [
        "【诺快聘】",
        "【诺聘】",
        "【诺优考】"
      ]
    }
  }
}