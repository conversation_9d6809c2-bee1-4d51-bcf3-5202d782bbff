﻿using Config.CommonModel;
using Config.CommonModel.Business;
using Config.CommonModel.Cache;
using Config.Enums;

namespace Staffing.Model.Seeker.User;

public class GetSeeker
{

}

public class SeekerInfo
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 是否实名
    /// </summary>
    public bool Identity { get; set; }

    /// <summary>
    /// 是否开通渠道商
    /// </summary>
    public bool IsChannel { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdCard { get; set; }

    /// <summary>
    /// 身份证姓名
    /// </summary>
    public string? IdName { get; set; }

    /// <summary>
    /// 简历完善度
    /// </summary>
    public int ResumeScore { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public UserStatus Status { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 顾问数量
    /// </summary>
    public int AdviserNum { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 当前顾问Id
    /// </summary>
    public MyAdviserInfo? Adviser { get; set; }

    /// <summary>
    /// 附件简历
    /// </summary>
    public AttachmentResume? AttachmentResume { get; set; }

    /// <summary>
    /// 用户投递行为数据
    /// </summary>
    public SeekerDeliveryNum? DeliveryBehavior { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 地区
    /// </summary>
    public CityModel? Region { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public double Lat { get; set; }

    /// <summary>
    /// 经度
    /// </summary>
    public double Lng { get; set; }

    /// <summary>
    /// 公众号通知
    /// </summary>
    public bool H5Notice { get; set; }

    /// <summary>
    /// 是否关注公众号
    /// </summary>
    public bool WeChatH5Subscribe { get; set; }

    /// <summary>
    /// 第三方简历池主键id
    /// </summary>
    public string? ResumeBufferId { get; set; }

    /// <summary>
    /// 是否面试官
    /// </summary>
    public bool IsInterview { get; set; }

    /// <summary>
    /// 是否顾问
    /// </summary>
    public bool IsAdviser { get; set; }
}

public class UpdateSeekerLocation
{
    /// <summary>
    /// 地区Id（必填）
    /// </summary>
    public string? RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public double Lat { get; set; }

    /// <summary>
    /// 经度
    /// </summary>
    public double Lng { get; set; }
}

public class CheckSeekerTokenForNykResponse
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? PK_UID { get; set; }

    /// <summary>
    /// 账户ID
    /// </summary>
    public string? PK_UAID { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? LoginName { get; set; }

    public CheckSeekerTokenForNykUser? User { get; set; }
}

public class CheckSeekerTokenForNykUser
{
    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? HeadPortrait { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }
}

/// <summary>
/// 用户打开小程序
/// </summary>
public class SeekerAppShow
{
    /// <summary>
    /// 渠道Id
    /// </summary>
    public string? ChannelId { get; set; }
}

/// <summary>
/// 通过渠道进入顾问人才库
/// </summary>
public class CheckChannelResponse
{
    /// <summary>
    /// 群二维码
    /// </summary>
    public string? GroupQrCode { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// 0=成功，1=已存在
    /// </summary>
    public int Status { get; set; }
}

public class GetKzgResume
{
    /// <summary>
    /// 快手简历Id
    /// </summary>
    public string? KsId { get; set; }
}

public class GetKzgResumeResponse : ResumeInfo
{
    /// <summary>
    /// 快手昵称
    /// </summary>
    public string? KsName { get; set; }
}