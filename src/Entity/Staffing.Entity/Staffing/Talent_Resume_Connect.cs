﻿using Config.CommonModel.TalentResume;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

[Table("talent_resume_connect")]
public class Talent_Resume_Connect
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 人才融合简历库主键Id
    /// </summary>
    public string ResumeId { get; set; } = default!;

    /// <summary>
    /// 联系方式
    /// </summary>
    public ConnectType Type { get; set; } = default!;

    /// <summary>
    /// 虚拟电话号
    /// </summary>
    public string? VirtualTeleNo { get; set; }

    /// <summary>
    /// 短信内容
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 联系人id
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 沟通职位id
    /// </summary>
    public string? TeamPostId { get; set; }

    public DateTime? CreatedTime { get; set; }

    public User_Hr User_Hr { get; set; } = default!;

    public Talent_Resume Talent_Resume { get; set; } = default!;

    public Post_Team? Post_Team { get; set; }
}
