using System.Text.Json.Serialization;
using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;
using Staffing.Model.Hr.User;

namespace Staffing.Model.Hr.Project;

/// <summary>
/// 我的项目列表
/// </summary>
public class MyProjects : QueryRequest
{
    /// <summary>
    /// 项目名称
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 我的项目类型
    /// </summary>
    public MyProjectsType? Type { get; set; }
}

public class GetHRProsQuery : QueryRequest
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }
}

public enum MyProjectsType
{
    自己, 协同, 归档 = 99
}

public class MyProjectsResponse : QueryResponse
{
    public List<MyProjectsDetailInfo> Rows { get; set; } = default!;
}

public class MyProjectsDetailInfo : GetProjectDetail
{
    /// <summary>
    /// 协同者状态
    /// </summary>
    public ProjectStatus? TeamStatus { get; set; }

    /// <summary>
    /// 协同者状态名称
    /// </summary>
    public string? TeamStatusName { get; set; }
}

public class HRProSimpleResponse : QueryResponse
{
    public List<HRProSimpleInfo> Rows { get; set; } = default!;
}
public class HRProSimpleInfo
{
    public int ProjectAutoId { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 协同项目Id
    /// </summary>
    public string? TeamProjectId { get; set; }

    /// <summary>
    /// 项目编号
    /// </summary>
    public string? ProjectNo { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? Name { get; set; } = default!;

    /// <summary>
    /// 项目类型
    /// </summary>
    public ProjectType Type { get; set; } = ProjectType.人才派遣;

    /// <summary>
    /// 项目行业
    /// </summary>
    public ProjectIndustry Industry { get; set; } = ProjectIndustry.其他;

    /// <summary>
    /// hr名称
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// hr头像
    /// </summary>
    public string? HrAvatar { get; set; }

    /// <summary>
    /// hr企业名称
    /// </summary>
    public string? HrEntName { get; set; }

    /// <summary>
    /// hr职位
    /// </summary>
    public string? HrPost { get; set; }

    /// <summary>
    /// 项目级别
    /// </summary>
    public string? LevelName { get; set; }

    /// <summary>
    /// 项目类型
    /// </summary>
    public string? TypeName { get; set; }

    /// <summary>
    /// 项目行业
    /// </summary>
    public string? IndustryName { get; set; }

    /// <summary>
    /// 岗位数量
    /// </summary>
    public int PostCount { get; set; }

    /// <summary>
    /// 招聘人数
    /// </summary>
    public int RecruitNumber { get; set; }

    /// <summary>
    /// 交付人数
    /// </summary>
    public int DeliveriesNum { get; set; }

    /// <summary>
    /// 报名人数
    /// </summary>
    public int RegistrationNum { get; set; }

    /// <summary>
    /// 签约人数
    /// </summary>
    public int ContractNum { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ProjectStatus? Status { get; set; }

    /// <summary>
    /// 工作周期类型
    /// </summary>
    public string? WorkCycleTypeName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 是否自己项目
    /// </summary>
    public bool SelfProj { get; set; }
}

/// <summary>
/// 项目职位列表
/// </summary>
public class MyProjectPosts : QueryRequest
{
    /// <summary>
    /// 协同项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    ///  当前角色是否是项目经理
    /// </summary>
    public bool? IsReceiver { get; set; }

    /// <summary>
    /// 职位搜索
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 职位类别
    /// </summary>
    public int? Category { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public List<string>? Citys { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public MyProjectPostsStatus? Status { get; set; }
}

//我的职位协同类型
public enum MyProjectShareType
{
    诺快聘 = 1, 诺协同, 诺聘
}

public enum MyProjectPostsStatus
{
    待审核, 发布中, 关闭 = 9
}

public class MyProjectPostsResponse : QueryResponse
{
    public List<MyProjectPostInfo> Rows { get; set; } = default!;
}

public class MyProjectPostInfo : GetPostDetail
{
    /// <summary>
    /// 面试看板
    /// </summary>
    public PostRecentInterviews InterviewDashboard { get; set; } = new PostRecentInterviews();

    /// <summary>
    /// 已协同
    /// </summary>
    public bool? Shared { get; set; }

    /// <summary>
    /// 已招满
    /// </summary>
    public bool FullRecruit { get; set; }

    /// <summary>
    /// 发布渠道
    /// </summary>
    public List<MyProjectShareType>? Channel { get; set; }

    /// <summary>
    /// 审批驳回原因
    /// </summary>
    public string? RejectReason { get; set; }

    /// <summary>
    /// 审批人
    /// </summary>
    public string? AuditUser { get; set; }

    /// <summary>
    /// 审批时间
    /// </summary>
    public DateTime? AuditTime { get; set; }
}

/// <summary>
/// 我所有发布中的职位
/// </summary>
public class GetShowPosts : QueryRequest
{
    /// <summary>
    /// 检索
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 工作性质
    /// </summary>
    public PostWorkNature? WorkNature { get; set; }

    /// <summary>
    /// 地区Id（最多10个）
    /// </summary>
    public List<string>? Citys { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 结算方式（日结、月结）
    /// </summary>
    public List<PostSettlementType?>? SettlementType { get; set; }

    /// <summary>
    /// 工作周期类型
    /// </summary>
    public List<ProjectWorkCycleType>? WorkCycleType { get; set; }
}

public class GetAdviserCity
{
    /// <summary>
    /// 父Id
    /// </summary>
    public string? ParentId { get; set; }
}

public class GetAdviserCityResponse
{
    public List<GeneralDic> Rows { get; set; } = default!;
}

public class GetAllHrResp
{
    public List<HrInfo> Rows { get; set; } = default!;
}

public class GetMyProjectReq : QueryRequest
{
    /// <summary>
    /// 模糊查询字符串
    /// </summary>
    public string? Search { get; set; }

    public GetMyProjectReqTypeEnum Type { get; set; }
}

public enum GetMyProjectReqTypeEnum
{
    商机管理, 项目大厅, 我管理的项目
}

public class GetMyProjectResp : QueryResponse
{
    public List<MyProjectInfo> Rows { get; set; } = default!;
}


public class MyProjectInfo
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 协同项目Id
    /// </summary>
    public string? TeamProjectId { get; set; }

    /// <summary>
    /// 项目code
    /// </summary>
    public string? ProjectCode { get; set; }
    /// <summary>
    /// 公司名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 项目类型
    /// </summary>
    public ProjectType Type { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string? TypeName { get; set; }

    /// <summary>
    /// 项目行业
    /// </summary>
    public ProjectIndustry Industry { get; set; }

    public string? IndustryName { get; set; }

    /// <summary>
    /// 工作周期类型
    /// 2023-11-01 郭思成：项目周期默认值改成 长期用工
    /// </summary>
    public ProjectWorkCycleType WorkCycleType { get; set; }

    /// <summary>
    /// 结算方式
    /// </summary>
    public ProjectClearingType ClearingType { get; set; }

    /// <summary>
    /// 结算渠道
    /// </summary>
    public ProjectClearingChannel ClearingChannel { get; set; }

    /// <summary>
    /// 诺亚项目编码
    /// </summary>
    public string NuoId { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; } = default!;

    /// <summary>
    /// 地区Id
    /// </summary>
    public List<string> RegionIds { get; set; } = new List<string>();

    /// <summary>
    /// 项目截止日期
    /// </summary>
    public string? EndTime { get; set; }

    /// <summary>
    /// 合作模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }
    /// <summary>
    /// 合作模式中文
    /// </summary>
    public string? PaymentNodeString { get; set; }

    /// <summary>
    /// 合同金额
    /// </summary>
    public decimal? Price { get; set; }
    /// <summary>
    /// 销售人员名称
    /// </summary>
    public string? SalesPerson { get; set; }
    /// <summary>
    /// 项目经理名称
    /// </summary>
    public string? PmName { get; set; }
    /// <summary>
    /// 城市
    /// </summary>
    public List<string>? Citys { get; set; }
    /// <summary>
    /// 职位数量
    /// </summary>
    public int? PositionCount { get; set; }
    /// <summary>
    /// 招聘人数
    /// </summary>
    public int? RecruitmentCount { get; set; }

    /// <summary>
    /// 是否接单0 否 1是
    /// </summary>
    public RecruitAcceptOrderEnum? AcceptOrder { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ProjectStatus Status { get; set; }

    public string? StatusName { get; set; }
    /// <summary>
    /// 企业logo
    /// </summary>
    public string? LogoUrl { get; set; }

    /// <summary>
    /// 回款金额
    /// </summary>
    public decimal? ReceivedAmount { get; set; }


}


public class ProjectAssetOverviewResp
{
    /// <summary>
    /// 可用余额
    /// </summary>
    public decimal? AvailableAmount { get; set; }
    /// <summary>
    /// 开票费用（Invoice Cost） - 来源于SubAccount的InvoiceVatRateAmount，单位元，可能为负数
    /// </summary>
    public decimal InvoiceCost { get; set; } = decimal.Zero;

    /// <summary>
    /// 项目盈利（Project Profit）
    /// </summary>
    public decimal ProjectProfit { get; set; } = decimal.Zero;

    /// <summary>
    /// 已打款（Paid Amount）
    /// </summary>
    public decimal PaidAmount { get; set; } = decimal.Zero;

    /// <summary>
    /// 待确认订单数量
    /// </summary>
    public int? PendingConfirmationCount { get; set; } = 0;

    /// <summary>
    /// 销售佣金
    /// </summary>
    public decimal? SalesBounty { get; set; } = decimal.Zero;

    /// <summary>
    /// 项目管理佣金
    /// </summary>
    public decimal? ManagerBounty { get; set; } = decimal.Zero;

    /// <summary>
    /// 线索佣金
    /// </summary>
    public decimal? ClueBounty { get; set; } = decimal.Zero;

    /// <summary>
    /// 邀面佣金(邀面)
    /// </summary>
    public decimal? FollowerBounty { get; set; } = decimal.Zero;

    /// <summary>
    /// 平台佣金
    /// </summary>
    public decimal? PlatformBounty { get; set; } = decimal.Zero;


}

public class TransactionListReq : QueryRequest
{
    // /// <summary>
    // /// 求职者姓名
    // /// </summary>
    // public string? SeekerName { get; set; }
    //
    // /// <summary>
    // /// 求职者联系电话
    // /// </summary>
    // public string? SeekerMobile { get; set; }
    //
    /// <summary>
    /// 求职者姓名/求职者联系电话
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public BountyStatus? Status { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public string? BeginDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public string? EndDate { get; set; }
    /// <summary>
    /// 项目id
    /// </summary>
    public string? ProjectId { get; set; }
    /// <summary>
    /// 岗位id
    /// </summary>
    public string? PostId { get; set; }



}

/// <summary>
/// 交易记录
/// </summary>
public class TransactionListResp : QueryResponse
{
    public List<TransactionInfo> Rows { get; set; } = default!;
}
/// <summary>
/// 订单
/// </summary>
public class TransactionInfo
{
    /// <summary>
    /// 订单编码
    /// </summary>
    public string OrderId { get; set; } = default!;

    /// <summary>
    /// 订单金额
    /// </summary>
    public decimal? Amount { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public BountyStatus Status { get; set; }

    /// <summary>
    /// 订单状态名称
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 销售经理Id
    /// </summary>
    public string SaleId { get; set; } = default!;

    /// <summary>
    /// 销售经理Id
    /// </summary>
    public string? SaleName { get; set; }

    /// <summary>
    /// 销售佣金
    /// </summary>
    public decimal SalesBounty { get; set; }

    /// <summary>
    /// 项目经理Id
    /// </summary>
    public string PmId { get; set; } = default!;

    ///<summary>
    /// 项目经理Id
    /// </summary>
    public string? PmName { get; set; }

    /// <summary>
    /// 项目管理佣金
    /// </summary>
    public decimal ManagerBounty { get; set; }

    /// <summary>
    /// 求职者Id
    /// </summary>
    public string? SeekerId { get; set; }

    /// <summary>
    /// 求职者名称
    /// </summary>
    public string? SeekerName { get; set; }

    /// <summary>
    ///  求职者电话
    /// </summary>
    public string? SeekerMobile { get; set; }

    /// <summary>
    /// 投递职位
    /// </summary>
    public string PostName { get; set; } = string.Empty;

    /// <summary>
    /// 投递时间
    /// </summary>
    public DateTime DeliveryTime { get; set; }

    /// <summary>
    /// 线索方Id
    /// </summary>
    public string? ClueId { get; set; }

    /// <summary>
    /// 线索方名称
    /// </summary>
    public string? ClueName { get; set; }

    /// <summary>
    /// 线索佣金
    /// </summary>
    public decimal ClueBounty { get; set; }

    /// <summary>
    /// 邀面方Id
    /// </summary>
    public string? FollowerId { get; set; }

    /// <summary>
    /// 邀面方名称
    /// </summary>
    public string? FollowerName { get; set; }

    /// <summary>
    /// 邀面佣金(邀面)
    /// </summary>
    public decimal FollowerBounty { get; set; }

    /// <summary>
    ///  平台方
    /// </summary>
    public string? PlatformName { get; set; } = string.Empty;

    /// <summary>
    /// 平台佣金
    /// </summary>
    public decimal PlatformBounty { get; set; }

    /// <summary>
    /// 交付金额（线索+邀面）
    /// </summary>
    public decimal? DeliveryBounty { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }
    [JsonIgnore]
    public RecruitStatus RecruitStatus { get; set; }

}
public class ProjectFundsListReq : QueryRequest
{
    public string? ProjectId { get; set; }

}


public class ProjectFundsListResp : QueryResponse
{
    public List<ProjectFundsInfo> Rows { get; set; } = default!;
}

public class ProjectFundsInfo
{
    public string Id { get; set; } = default!;

    /// <summary>
    /// 项目ID
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 余额
    /// </summary>
    public decimal Balance { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }
}

public class BountySummary
{
    public decimal? TotalSalesBounty { get; set; }
    public decimal? TotalManagerBounty { get; set; }
    public decimal? TotalClueBounty { get; set; }
    public decimal? TotalFollowerBounty { get; set; }
}