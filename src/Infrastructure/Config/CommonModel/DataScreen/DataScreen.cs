﻿using Config.Enums;

namespace Config.CommonModel.DataScreen;

/// <summary>
/// 数据大屏中变动频率一致的数据字段
/// </summary>
public class DataScreenInfo
{
    /// <summary>
    /// 顾问数量
    /// </summary>
    public int AdvCount { get; set; }
    /// <summary>
    /// 在线顾问数量(近24小时内在线的)
    /// </summary>
    public int AdvOnlineCount { get; set; }

    /// <summary>
    /// 人才库数量
    /// </summary>
    public int TalentCount { get; set; }

    /// <summary>
    /// 进行中项目数量
    /// </summary>
    public int ProOngoingCount { get; set; }
    /// <summary>
    /// 已归档项目数量
    /// </summary>
    public int ProArcCount { get; set; }

    /// <summary>
    /// 入驻公司
    /// </summary>
    public int EntCount { get; set; }

    /// <summary>
    /// 入驻集团
    /// </summary>
    public int GroupEntCount { get; set; }
}

/// <summary>
/// 顾问人才储备TOP榜
/// </summary>
public class AdvTalRankingInfo
{
    /// <summary>
    /// 顾问ID
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 顾问姓名
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// 所属公司
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 人才库数量
    /// </summary>
    public int Talent { get; set; }

    /// <summary>
    /// 简历人才库数量
    /// </summary>
    public int VirtualTalent { get; set; }

    /// <summary>
    /// 昨日新增人才储备
    /// </summary>
    public int YesterdayTalent { get; set; }

    /// <summary>
    /// 播报类型
    /// </summary>
    public AdvTalRankingType Type { get; set; }
}

/// <summary>
/// 顾问规模最大的机构
/// </summary>
public class EntAdvRankingInfo
{
    /// <summary>
    /// 公司Id
    /// </summary>
    public string? EntId { get; set; }

    /// <summary>
    /// 公司名
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 顾问数量
    /// </summary>
    public int AdvCount { get; set; }
}

/// <summary>
/// 人才储备最大的机构
/// </summary>
public class EntTalRankingInfo
{
    /// <summary>
    /// 组织架构Id
    /// </summary>
    public long? OrgId { get; set; }

    /// <summary>
    /// 组织架构名
    /// </summary>
    public string? OrgName { get; set; }

    public string? OrgLevel { get; set; }

    /// <summary>
    /// 人才库数量
    /// </summary>
    public int TalentCount { get; set; }
}

/// <summary>
/// 涉及地区
/// </summary>
public class RegionEntInfo
{
    /// <summary>
    /// 地区编码
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 地区名称
    /// </summary>
    public string? RegionName { get; set; }

    /// <summary>
    /// 公司数量
    /// </summary>
    public int EntCount { get; set; }
}


/// <summary>
/// 分布
/// </summary>
public class DistributeInfo
{
    /// <summary>
    /// 行业名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public decimal Value { get; set; }
}

/// <summary>
/// 地区项目数量
/// </summary>
public class RegionProjectInfo
{
    /// <summary>
    /// 地区编码
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 地区名称
    /// </summary>
    public string? RegionName { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Count { get; set; }
}

/// <summary>
/// 处理效率
/// </summary>
public class ChuLiXiaoLvInfo
{
    /// <summary>
    /// 注册转化率
    /// </summary>
    public decimal? ZhuCe { get; set; }

    /// <summary>
    /// 投递转化率
    /// </summary>
    public decimal? TouDi { get; set; }

    /// <summary>
    /// 反馈转化率
    /// </summary>
    public decimal? FanKui { get; set; }

    /// <summary>
    /// 入职转化率
    /// </summary>
    public decimal? RuZhi { get; set; }

    /// <summary>
    /// 签约转化率
    /// </summary>
    public decimal? QianYue { get; set; }
}

/// <summary>
/// 地区人才数量
/// </summary>
public class RegionSeekersInfo
{
    /// <summary>
    /// 地区编码
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 地区名称
    /// </summary>
    public string? RegionName { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Count { get; set; }
}

#region 数据大屏 平台级-招聘流程

/// <summary>
/// 面试官概况
/// </summary>
public class DSPlatInterviewerOfRecruitInfo
{
    /// <summary>
    /// 入驻面试官数量
    /// </summary>
    public int InterviewerCount { get; set; }

    /// <summary>
    /// 今日在线面试官数量(24小时内在线)
    /// </summary>
    public int InterviewerOnlineCount { get; set; }

    /// <summary>
    /// 简历待筛选
    /// </summary>
    public int InterviewerRecruitScreening { get; set; }

    /// <summary>
    /// 面试待筛选
    /// </summary>
    public int InterviewerSeekerScreening { get; set; }

    /// <summary>
    /// 已处理
    /// </summary>
    public int InterviewerHandled { get; set; }
}

/// <summary>
/// 投递概况
/// </summary>
public class DSPlatDeliveryInfo
{
    /// <summary>
    /// 年月格式："yyyy-MM"
    /// </summary>
    public string YearMonth { get; set; } = default!;

    /// <summary>
    /// 投递数量
    /// </summary>
    public int Count { get; set; }
}

/// <summary>
/// 投递概况明细
/// </summary>
public class DSPlatDeliverySumInfo
{
    /// <summary>
    /// 今年数据
    /// </summary>
    public List<DSPlatDeliveryInfo>? CurrentYear { get; set; }

    /// <summary>
    /// 去年数据
    /// </summary>
    public List<DSPlatDeliveryInfo>? LastYear { get; set; }
}

/// <summary>
/// 投递实时播报Top30
/// </summary>
public class DSPlatDeliveryTop30Info
{
    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 投递时间
    /// </summary>
    public DateTime DeliveryTime { get; set; } = default!;

    /// <summary>
    /// 投递岗位
    /// </summary>
    public string PostName { get; set; } = default!;

}

#endregion

#region 数据大屏 平台级-用户概况
public class DSPlatSeekerDataInfo
{
    /// <summary>
    /// 今日登录
    /// </summary>
    public int LoginedNum { get; set; }

    /// <summary>
    /// 今日新注册
    /// </summary>
    public int NewRegisterNum { get; set; }

    /// <summary>
    /// 用户注册来源
    /// </summary>
    public List<UserSourceInfo>? RegisterResource { get; set; }

    /// <summary>
    /// 平台简历储备数量
    /// </summary>
    public int ResumeNum { get; set; }

    /// <summary>
    /// 平台人才储备数量
    /// </summary>
    public int TalentNum { get; set; }

    /// <summary>
    /// 访问次数
    /// </summary>
    public int VisitNum { get; set; }

    /// <summary>
    /// 访客数(UV)
    /// </summary>
    public int VisiterNum { get; set; }

    /// <summary>
    /// 用户性别概况
    /// </summary>
    public List<DSPlatSeekerSexInfo>? SexInfo { get; set; }

    /// <summary>
    /// 用户学历概况
    /// </summary>
    public List<DSPlatSeekerEducationInfo>? EducationInfo { get; set; }

    /// <summary>
    /// 用户年龄概况
    /// </summary>
    public List<DSPlatSeekerAgeInfo>? AgeInfo { get; set; }

    /// <summary>
    /// 行业人才储备Top5
    /// </summary>
    public List<DSPlatSeekerIndustryInfo>? IndustryTop5 { get; set; }

    /// <summary>
    /// 求职意向职位Top5
    /// </summary>
    public List<DSPlatSeekerHopePostInfo>? HopePostTop5 { get; set; }

    /// <summary>
    /// 地区人才储备
    /// </summary>
    public List<DSPlatSeekerAreaInfo>? AreaInfos { get; set; }

}

/// <summary>
/// 用户性别概况
/// </summary>
public class DSPlatSeekerSexInfo
{
    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 性别人数
    /// </summary>
    public int SexCount { get; set; }

    /// <summary>
    /// 性别占比
    /// </summary>
    public decimal SexPercent { get; set; }
}

/// <summary>
/// 用户年龄概况
/// </summary>
public class DSPlatSeekerAgeInfo
{
    /// <summary>
    /// 年龄段
    /// </summary>
    public AgeGroup? AgeGroup { get; set; }

    /// <summary>
    /// 年龄段人数
    /// </summary>
    public int AgeCount { get; set; }

    /// <summary>
    /// 各年龄段占比
    /// </summary>
    public decimal AgePercent { get; set; }
}

/// <summary>
/// 用户学历
/// </summary>
public class DSPlatSeekerEducationInfo
{
    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 学历人数
    /// </summary>
    public int EduCount { get; set; }

    /// <summary>
    /// 各学历占比
    /// </summary>
    public decimal EduPercent { get; set; }
}

/// <summary>
/// 行业人才储备
/// </summary>
public class DSPlatSeekerIndustryInfo
{
    /// <summary>
    /// 行业名称
    /// </summary>
    public string IndustryName { get; set; } = default!;

    /// <summary>
    /// 人才储备量
    /// </summary>
    public int Quantity { get; set; }
}

/// <summary>
/// 意向职位
/// </summary>
public class DSPlatSeekerHopePostInfo
{
    /// <summary>
    /// 职位名称
    /// </summary>
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 求职人数
    /// </summary>
    public int Quantity { get; set; }
}

/// <summary>
/// 地区人才储备
/// </summary>
public class DSPlatSeekerAreaInfo
{
    public string? RegionId { get; set; }
    /// <summary>
    /// 地区 - 省
    /// </summary>
    public string? Province { get; set; } = "河北省";

    /// <summary>
    /// 地区 - 市
    /// </summary>
    public string? City { get; set; } = default!;

    /// <summary>
    /// 地区 - 县（区）
    /// </summary>
    public string? County { get; set; }

    /// <summary>
    /// 人数
    /// </summary>
    public int Quantity { get; set; }

    public int OrderIndex { get; set; }
}

/// <summary>
/// 用户信息
/// </summary>
public class UserSeekerInfo
{
    public string UserId { get; set; } = default!;
    public DateOnly? Birthday { get; set; }
    public Sex? Sex { get; set; }
    public EducationType? Education { get; set; }
    public string? RegionId { get; set; }
    /// <summary>
    /// 地市名称
    /// </summary>
    public string? RegionCityName { get; set; }
    /// <summary>
    /// 县(区)名称
    /// </summary>
    public string? RegionCountyName { get; set; }
    public DateTime? CreatedTime { get; set; }
}

/// <summary>
/// 用户注册来源
/// </summary>
public class UserSourceInfo
{
    /// <summary>
    /// 头像
    /// </summary>
    public string? Avator { get; set; }

    public RegisterSource? SourceName { get; set; }

    public int Quantity { get; set; }

    public decimal? QuantityPercent { get; set; }
}

#endregion

#region 数据大屏 平台级-项目看板

public class DSPlatProjectDataInfo
{
    /// <summary>
    /// 项目累计数
    /// </summary>
    public int ProjectSum { get; set; }

    /// <summary>
    /// 运行中项目数
    /// </summary>
    public int ProjectExecNum { get; set; }

    /// <summary>
    /// 已归档项目数
    /// </summary>
    public int ProjectArchivedNum { get; set; }

    /// <summary>
    /// 平台交付中项目数
    /// </summary>
    public int PlatProjectExecNum { get; set; }

    /// <summary>
    /// 平台项目入驻数
    /// </summary>
    public int PlatProjectSum { get; set; }

    /// <summary>
    /// 平台项目已归档
    /// </summary>
    public int PlatProjectArchivedNum { get; set; }

    /// <summary>
    /// 诺亚项目数
    /// </summary>
    public int NoahProjectSum { get; set; }

    /// <summary>
    /// 诺亚项目占比
    /// </summary>
    public decimal NoahProjectSumPercent { get; set; }

    /// <summary>
    /// 平台项目入驻数占比
    /// </summary>
    public decimal PlatProjectSumPercent { get; set; }

    /// <summary>
    /// 第三方项目数
    /// </summary>
    public int OtherProjectSum { get; set; }

    /// <summary>
    /// 第三方项目数占比
    /// </summary>
    public decimal OtherProjectSumPercent { get; set; }

    /// <summary>
    /// 项目概况
    /// </summary>
    public List<ProjectInfo>? ProjectInfos { get; set; }

    /// <summary>
    /// 项目行业概况
    /// </summary>
    public List<ProjectIndustryInfo>? ProjectIndustryInfos { get; set; }

    /// <summary>
    /// 项目地区概况
    /// </summary>
    public List<ProjectAreaInfo>? ProjectAreaInfos { get; set; }
}

/// <summary>
/// 项目概况
/// </summary>
public class ProjectInfo
{
    /// <summary>
    /// 项目类别
    /// </summary>
    public ProjectType? ProjectType { get; set; }

    /// <summary>
    /// 运行中项目数
    /// </summary>
    public int ExecNum { get; set; }

    /// <summary>
    /// 占比
    /// </summary>
    public decimal ExecNumPercent { get; set; }
}

/// <summary>
/// 项目行业概况
/// </summary>
public class ProjectIndustryInfo
{
    /// <summary>
    /// 项目行业
    /// </summary>
    public ProjectIndustry? ProjectIndustry { get; set; }

    /// <summary>
    /// 运行中项目数
    /// </summary>
    public int IndustryExecNum { get; set; }

    /// <summary>
    /// 占比
    /// </summary>
    public decimal IndustryExecNumPercent { get; set; }
}

/// <summary>
/// 项目地区概况
/// </summary>
public class ProjectAreaInfo
{
    /// <summary>
    /// 项目地区 - 省
    /// </summary>
    public string? ProjectProvince { get; set; }

    /// <summary>
    /// 项目地区 - 市
    /// </summary>
    public string? ProjectCity { get; set; }

    /// <summary>
    /// 运行中项目数
    /// </summary>
    public int AreaExecNum { get; set; }

    /// <summary>
    /// 占比
    /// </summary>
    public decimal AreaExecNumPercent { get; set; }
}

public class ProjectAreaDetailInfo
{
    public string? ProjectId { get; set; }
    public string? RegionId { get; set; }
    public string? CityName { get; set; }
    public string? ProvinceName { get; set; }

    public override bool Equals(object? obj)
    {
        return obj is ProjectAreaDetailInfo info &&
               ProvinceName == info.ProvinceName &&
               ProjectId == info.ProjectId;
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(ProvinceName, ProjectId);
    }
}

#endregion

public class GetTopologyResponse
{
    /// <summary>
    /// 上线项目数量
    /// </summary>
    public int OnlineProjectCount { get; set; }

    /// <summary>
    /// 开启协同项目数量
    /// </summary>
    public int OpenProjectCount { get; set; }

    /// <summary>
    /// 关闭协同项目数量
    /// </summary>
    public int CloseProjectCount { get; set; }

    /// <summary>
    /// 人才数量
    /// </summary>
    public int TalentCount { get; set; }

    /// <summary>
    /// 诺快聘人才数量
    /// </summary>
    public int KuaiPinTalentCount { get; set; }

    /// <summary>
    /// 蓝领数量
    /// </summary>
    public int BlueCollarCount { get; set; }

    /// <summary>
    /// C端蓝领-操作工数量【以求职意向、投递岗位、进行汇总，满足一个即可统计+1，同一个人投递多个类型，多个类型都+1】
    /// </summary>
    public int BlueCollarCount_CaoZuoGong { get; set; }

    /// <summary>
    /// C端蓝领-客服数量【以求职意向、投递岗位、进行汇总，满足一个即可统计+1，同一个人投递多个类型，多个类型都+1】
    /// </summary>
    public int BlueCollarCount_KeFu { get; set; }

    /// <summary>
    /// C端蓝领-司机数量【以求职意向、投递岗位、进行汇总，满足一个即可统计+1，同一个人投递多个类型，多个类型都+1】
    /// </summary>
    public int BlueCollarCount_SiJi { get; set; }

    /// <summary>
    /// C端蓝领-保安数量【以求职意向、投递岗位、进行汇总，满足一个即可统计+1，同一个人投递多个类型，多个类型都+1】
    /// </summary>
    public int BlueCollarCount_BaoAn { get; set; }

    /// <summary>
    /// C端蓝领-保洁数量【以求职意向、投递岗位、进行汇总，满足一个即可统计+1，同一个人投递多个类型，多个类型都+1】
    /// </summary>
    public int BlueCollarCount_BaoJie { get; set; }

    /// <summary>
    /// 高端人才- 博士【博士、研究生学历的】
    /// </summary>
    public int HighTalentsCount_BoShi { get; set; }
    /// <summary>
    /// 高端人才- 研究生【博士、研究生学历的】
    /// </summary>
    public int HighTalentsCount_YanJiuSheng { get; set; }

    /// <summary>
    /// 白领数量
    /// </summary>
    public int WhiteCollarCount { get; set; }

    /// <summary>
    /// C端白领数量-销售【以求职意向、投递岗位、进行汇总，满足一个即可统计+1，同一个人投递多个类型，多个类型都+1】
    /// </summary>
    public int WihteCollarCount_XiaoShou { get; set; }
    /// <summary>
    /// C端白领数量-IT互联网【以求职意向、投递岗位、进行汇总，满足一个即可统计+1，同一个人投递多个类型，多个类型都+1】
    /// </summary>
    public int WihteCollarCount_IT { get; set; }
    /// <summary>
    /// C端白领数量-运营【以求职意向、投递岗位、进行汇总，满足一个即可统计+1，同一个人投递多个类型，多个类型都+1】
    /// </summary>
    public int WihteCollarCount_YunYing { get; set; }

    /// <summary>
    /// 学生数量
    /// </summary>
    public int StudentCount { get; set; }

    /// <summary>
    /// 高端人才数量【博士、研究生学历的】
    /// </summary>
    public int HighTalentsCount { get; set; }
}

public class GetTopologyTalentResponse
{
    /// <summary>
    /// 新增人才线索
    /// </summary>
    public int NewTalentClue { get; set; }

    /// <summary>
    /// 人才供给率
    /// </summary>
    public decimal TalentSupplyRate { get; set; }

    /// <summary>
    /// 推荐项目人数
    /// </summary>
    public int RecommendProjectCount { get; set; }

    /// <summary>
    /// 人才线索池引流情况
    /// </summary>
    public List<NameCount> TalentPoolDrainage { get; set; } = new List<NameCount>();

    /// <summary>
    /// 入职交付项目排名
    /// </summary>
    public List<NameCount> EntryProjectRanking { get; set; } = new List<NameCount>();

    /// <summary>
    /// 人才线索池使用情况
    /// </summary>
    public List<NameCount> TalentPoolUsage { get; set; } = new List<NameCount>();

    /// <summary>
    /// 诺快聘新增人才数量
    /// </summary>
    public int KuaiPinNewTalentCount { get; set; }

    /// <summary>
    /// 新增人才投递人数
    /// </summary>
    public int NewTalentDeliveryCount { get; set; }

    /// <summary>
    /// 新增人才面试人数
    /// </summary>
    public int NewTalentInterviewCount { get; set; }

    /// <summary>
    /// 新增人才入职人数
    /// </summary>
    public int NewTalentEntryCount { get; set; }
}

public class GetTopologyProjectResponse
{
    /// <summary>
    /// 发布项目数量
    /// </summary>
    public int ReleaseProjectCount { get; set; }

    /// <summary>
    /// 人岗匹配率
    /// </summary>
    public decimal PositionMatchingRate { get; set; }

    /// <summary>
    /// 所有职位的投递人数
    /// </summary>
    public int DeliveryCount { get; set; }

    /// <summary>
    /// 所有职位的招聘人数
    /// </summary>
    public int PostRecruitNumber { get; set; }

    /// <summary>
    /// 发布职位数量
    /// </summary>
    public int ReleasePositionCount { get; set; }

    /// <summary>
    /// 项目完成度
    /// </summary>
    public decimal ProjectCompletion { get; set; }

    /// <summary>
    /// 项目阶段进展
    /// </summary>
    public List<NameCount> ProjectStageProgress { get; set; } = new List<NameCount>();

    /// <summary>
    /// 部门协同人数
    /// </summary>
    public List<NameCount> DepartmentCollaboration { get; set; } = new List<NameCount>();
}

public class NameCount
{
    public string? Name { get; set; }
    public int Value { get; set; }
}