namespace Staffing.Model.CentreService.Enterprise;

public class GetEntGroups
{
    /// <summary>
    /// 主企业名称
    /// </summary>
    public string? GroupName { get; set; }
}

public class GetEntGroupsResponse
{
    public List<EntGroupInfo> Rows { get; set; } = new List<EntGroupInfo>();
}

public class EntGroupInfo
{
    /// <summary>
    /// 集团名称
    /// </summary>
    public string? GroupName { get; set; }

    /// <summary>
    /// 分公司数量
    /// </summary>
    public int Ents { get; set; }
}

public class GetGroup
{
    /// <summary>
    /// 主企业名称
    /// </summary>
    public string? GroupName { get; set; }
}

//临时
public class UpdateEntGroup
{
    /// <summary>
    /// 主企业Id
    /// </summary>
    public string? MainEntId { get; set; }

    /// <summary>
    /// 分公司企业Id
    /// </summary>
    public List<string>? SubEntId { get; set; }
}

//临时
public class DeleteEntGroup
{
    /// <summary>
    /// 分公司企业Id
    /// </summary>
    public List<string>? SubEntId { get; set; }
}