using Staffing.Model.Hr.Project;
using Staffing.Core.Interfaces.Hr;
using Infrastructure.Exceptions;
using Infrastructure.NoahCommon;
using Config.CommonModel.Business;
using Config;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Microsoft.AspNetCore.Mvc;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class ProjectRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("project").WithTags("项目");
        // group.MapPost("/agentent", (UpdateAgentEnt model, IProjectService rep) => rep.UpdateAgentEnt(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "添加/更新代招企业" });

        // group.MapGet("/agentent", ([AsParameters] GetAgentEntList model, IProjectService rep) => rep.GetAgentEntList(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "代招企业列表" });

        // group.MapGet("/agentent/less", ([AsParameters] GetAgentEntLess model, IProjectService rep) => rep.GetAgentEntLess(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "代招企业列表（简版）" });

        group.MapGet("/agentent/{id}", async (string id, IProjectService rep) => await rep.GetAgentEntInfo(id))
            .WithOpenApi(operation => new(operation) { Summary = "代招企业详情" });

        // group.MapGet("/agentent/recently", (IProjectService rep) => rep.GetAgentEntRecently())
        //     .WithOpenApi(operation => new(operation) { Summary = "最近使用的企业（暂不用，前端从列表取前三个）" });

        // group.MapDelete("/agentent/{id}", (string id, IProjectService rep) => rep.DeleteAgentEnt(id))
        //     .WithOpenApi(operation => new(operation) { Summary = "删除代招企业" });

        // group.MapPost("", async (UpdateProject model, IProjectService rep) => await rep.UpdateProject(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "添加/更新项目" });

        group.MapPost("/survey", async (UpdateProjectSurvey model, IProjectService rep) => await rep.UpdateProjectSurvey(model))
            .WithOpenApi(operation => new(operation) { Summary = "更新项目调研表" });

        group.MapPost("/survey/import", async ([FromForm] string? projectId, [FromForm] string? postId, HttpRequest request, IProjectService rep) =>
        {
            var file = request.Form.Files.FirstOrDefault();
            var result = await rep.ImportProjectSurvey(projectId, postId, file);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "导入项目调研表" });

        group.MapGet("/survey", (string? projectId, string? postId, IProjectService rep) => rep.GetProjectSurvey(projectId, postId))
            .WithOpenApi(operation => new(operation) { Summary = "查询项目调研表" });

        // group.MapPost("/ent/bind", (UpdateProjectAgentEnt model, IProjectService rep) => rep.UpdateProjectAgentEnt(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "项目绑定代招企业" });

        group.MapGet("/hall/project/{id}", async (string id, IProjectService projectRep, IProjectHallService hallRep, IProjectContractsService projectContractsService) =>
        {
            var result = hallRep.GetHallProject(id);
            result.AgentEnt = await projectRep.GetAgentEntInfo(result.AgentEntId);
            result.HasDefaultContract = projectContractsService.HasDefaultContract(id).HasDefault;
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "大厅项目详情" });

        group.MapGet("/{id}", async (string id, IProjectService rep,IProjectContractsService projectContractsService) =>
        {
            var result = rep.GetProject(id);
            result.AgentEnt = await rep.GetAgentEntInfo(result.AgentEntId);
            result.HasDefaultContract = projectContractsService.HasDefaultContract(id).HasDefault;
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "项目详情" });

        group.MapPost("/post/tags", async (GetPostTags model, IProjectService rep, ParseResume parseResume) =>
        {
            if (string.IsNullOrWhiteSpace(model?.Name))
                throw new BadRequestException("缺少职位名称");

            if (string.IsNullOrWhiteSpace(model?.Describe))
                throw new BadRequestException("缺少职位描述");

            var result = new GetPostTagsResponse();
            var resp = await parseResume.ParsePosition(new XxJobExplain { job_title = model.Name, description = model.Describe });
            result.Rows = resp.Skills ?? new List<string>();

            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "解析技能标签" });

        group.MapGet("/noah/proj", async ([AsParameters] GetNoahProj model, IProjectService rep) => await rep.GetNoahProj(model))
            .WithOpenApi(operation => new(operation) { Summary = "查询数字诺亚合同" });

        group.MapPost("/post", async (UpdatePost model, IProjectService rep) => await rep.UpdatePost(model))
            .WithOpenApi(operation => new(operation) { Summary = "添加/更新职位" });

        group.MapGet("/post/{postId}", async (string postId, IProjectService rep) => await rep.GetPost(postId))
            .WithOpenApi(operation => new(operation) { Summary = "职位详情" });

        group.MapGet("/post/intervew", (string? postId, string? recruitId, IProjectService rep) => rep.GetPostInterview(postId, recruitId))
            .WithOpenApi(operation => new(operation) { Summary = "获取职位面试信息" });

        group.MapPost("/post/copywriting", async (HrGetPostCopywriting model, IProjectService rep) => await rep.HrGetPostCopywriting(model))
            .WithOpenApi(operation => new(operation) { Summary = "职位文案信息" });

        group.MapPost("/post/copywriting/update", (HrUpdatePostCopywriting model, IProjectService rep) => rep.HrUpdatePostCopywriting(model))
            .WithOpenApi(operation => new(operation) { Summary = "更新职位文案信息" });

        group.MapPost("/hall", async (GetProjectHall model, IProjectHallService rep) => await rep.GetProjectHall(model))
            .WithOpenApi(operation => new(operation) { Summary = "项目大厅" });

        group.MapPost("/hall/post", (GetHallPost model, IProjectHallService rep) => rep.GetHallPost(model))
            .WithOpenApi(operation => new(operation) { Summary = "项目大厅-职位列表" });

        group.MapPost("/hall/sync", async (ProjectSync model, IProjectHallService rep) => await rep.ProjectSync(model))
            .WithOpenApi(operation => new(operation) { Summary = "项目协同" });

        group.MapPost("/allpost/status", async (SetProjectPostStatus model, IProjectService rep) => await rep.SetProjectPostStatus(model))
            .WithOpenApi(operation => new(operation) { Summary = "一键上下架全部职位" });

        group.MapGet("/teamhr", ([AsParameters] GetProjectTeamHr model, IProjectService rep) => rep.GetProjectHr(model))
            .WithOpenApi(operation => new(operation) { Summary = "项目协同人员列表" });

        group.MapPost("/me", (MyProjects model, IProjectService rep) => rep.MyProjects(model))
            .WithOpenApi(operation => new(operation) { Summary = "我的项目列表" });

        group.MapPost("/post/me", (MyProjectPosts model, IProjectService rep) => rep.MyProjectPosts(model))
            .WithOpenApi(operation => new(operation) { Summary = "项目详情-项目职位" });

        group.MapGet("/post/city", ([AsParameters] GetAdviserCity model, IProjectService rep) => rep.GetAdviserCity(model))
            .WithOpenApi(operation => new(operation) { Summary = "顾问开通的城市" });

        group.MapPost("/showposts", (GetShowPosts model, IProjectService rep) => rep.GetShowPosts(model))
            .WithOpenApi(operation => new(operation) { Summary = "我所有发布中的职位" });

        group.MapPost("/post/status", (SetPostStatus model, IProjectService rep) => rep.SetPostStatus(model))
            .WithOpenApi(operation => new(operation) { Summary = "更新职位状态" });

        group.MapPost("/post/top", (TopPost model, IProjectService rep) => rep.TopPost(model))
            .WithOpenApi(operation => new(operation) { Summary = "职位置顶" });

        group.MapPost("/post/excellent", (ExcellentPost model, IProjectService rep) => rep.ExcellentPost(model))
            .WithOpenApi(operation => new(operation) { Summary = "职位优选操作（status支持待审核和取消）" });

        group.MapDelete("/post/{postId}", (string postId, IProjectService rep) => rep.DeletePost(postId))
            .WithOpenApi(operation => new(operation) { Summary = "删除职位" });

        group.MapGet("/post/deliver", ([AsParameters] GetPostDeliverys model, IProjectService rep) => rep.GetPostDeliverys(model))
            .WithOpenApi(operation => new(operation) { Summary = "职位投递信息" });

        group.MapPost("/bounty/config", (IProjectService rep) => rep.GetBountyConfig())
            .WithOpenApi(operation => new(operation) { Summary = "获取平台佣金配置信息" });

        group.MapPost("/project/transfer", (ProjectTransfer model, IProjectService rep) => rep.ProjectTransfer(model))
            .WithOpenApi(operation => new(operation) { Summary = "项目转移" });

        // 查询数字诺亚的合同信息
        group.MapGet("/noah/contract", async ([AsParameters] GetNoahContract model, IProjectContractsService rep) => await rep.GetNoahContract(model))
            .WithOpenApi(operation => new(operation) { Summary = "查询数字诺亚的合同信息" });

        // 查询项目合同信息
        group.MapGet("/contract", async ([AsParameters] GetProjectContract model, IProjectContractsService rep) => await rep.GetProjectContract(model))
            .WithOpenApi(operation => new(operation) { Summary = "查询项目合同" });

        // 查询项目合同列表
        group.MapGet("/contract/list", ([AsParameters] GetProjectContractList model, IProjectContractsService rep) => rep.GetProjectContractList(model))
            .WithOpenApi(operation => new(operation) { Summary = "查询项目合同列表" });

        // 保存项目合同信息
        group.MapPost("/contract/save", (UpdateProjectContract model, IProjectContractsService rep) => rep.UpdateProjectContract(model))
            .WithOpenApi(operation => new(operation) { Summary = "保存项目合同" });

        // 设置默认合同
        group.MapPost("/contract/set", (SetDefaultProjectContract model, IProjectContractsService rep) => rep.SetDefaultProjectContract(model))
            .WithOpenApi(operation => new(operation) { Summary = "设置默认合同" });

        // 检查项目是否有默认合同
        group.MapGet("/contract/default/{projectId}", (string projectId, IProjectContractsService rep) => rep.HasDefaultContract(projectId))
            .WithOpenApi(operation => new(operation) { Summary = "检查项目是否有默认合同" });

        group.MapGet("/teampost/busisms/text", ([AsParameters] GetPostBusinessSmsText model, IProjectService rep) => rep.GetPostBusinessSmsText(model))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "根据协同职位编码获取职位邀约短信内容" });

        group.MapGet("/automatic/{projectId}", (string projectId, IProjectService rep) => rep.GetProjectAutomatic(projectId))
            .WithOpenApi(operation => new(operation) { Summary = "获取项目自流转信息详情" });

        group.MapPost("/automatic", (EditProjectAutomaticRequest model, IProjectService rep) => rep.EditProjectAutomatic(model))
            .WithOpenApi(operation => new(operation) { Summary = "修改项目自流转信息" });

        group.MapGet("/gettyc/{name}", (string name, CommonCacheService rep) => rep.GetAgentEntFromTyc(name))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "根据名字获取天眼查企业信息" });

        group.MapPost("/post/hragentstatus", (TeamPostHrAgentChat model, IProjectService rep) => rep.UpdatePostHrAgentChatStatus(model))
            .WithMetadata(new ApiMeta { AuthType = ApiAuthType.诺聘验证 })
            .WithOpenApi(operation => new(operation) { Summary = "更新Hr代聊状态" });

        group.MapPost("/project/crowdsource", (ProjectCrowdsourceReq model, IProjectService rep) => rep.EditProjectCrowdsource(model))
            .WithOpenApi(operation => new(operation) { Summary = "项目配置众包模式" });

        group.MapGet("/getProjectCrowdsource", (IProjectService rep) => rep.GetProjectCrowdsource())
            .WithOpenApi(operation => new(operation) { Summary = "获取项目众包配置" });

        group.MapGet("/getAllHr", (IProjectService rep) => rep.GetAllHr())
            .WithOpenApi(operation => new(operation) { Summary = "获取所有hr" });
        //
        group.MapGet("/getAllProjectManager", (IProjectService rep) => rep.GetAllProjectManager())
            .WithOpenApi(operation => new(operation) { Summary = "获取所有项目经理" });

        group.MapGet("/getAllfinance", (IProjectService rep) => rep.GetAllfinance())
            .WithOpenApi(operation => new(operation) { Summary = "获取所有（结算）财物人员" });
        //
        group.MapGet("/getNoahBooks", (IProjectService rep) => rep.GetNoahBooks())
            .WithOpenApi(operation => new(operation) { Summary = "获取全部账套信息" });

        group.MapPost("/newUpdateProject", async (NewUpdateProject model, IProjectService rep) => await rep.NewUpdateProject(model))
            .WithOpenApi(operation => new(operation) { Summary = "new添加/更新项目" });

        group.MapGet("/getMyProjectList", ([AsParameters] GetMyProjectReq req, IProjectService rep) => rep.GetMyProjects(req))
            .WithOpenApi(operation => new(operation) { Summary = "商机列表管理" });

        group.MapPost("/projectAcceptOrder", (string projectId, IProjectService rep) => rep.ProjectAcceptOrder(projectId))
            .WithOpenApi(operation => new(operation) { Summary = "项目接单接口" });

        group.MapGet("/getProjectAssetOverview", (string projectId, IProjectService rep) => rep.GetProjectAssetOverview(projectId))
            .WithOpenApi(operation => new(operation) { Summary = "获取项目资产概述" });

    }
}