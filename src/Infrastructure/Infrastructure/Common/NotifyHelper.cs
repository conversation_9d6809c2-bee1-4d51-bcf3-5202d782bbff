﻿namespace Infrastructure.Common;

// public static class NotifyHelper
// {
//     public static void SendNotify(NpNotifyModel model)
//     {
//         var dt = model.NotifyTime.ToUnixTime();
//         MyRedis.Client.ZAdd("notify:aaa", dt, model.Id);
//     }
// }

// public class NpNotifyModel
// {
//     public string? Id { get; set; }
//     public NpNotifyType Type { get; set; }
//     public string? UserId { get; set; }
//     public string? Content { get; set; }
//     public bool DdNotify { get; set; }
//     public bool WeChatNotify { get; set; }
//     public DateTime NotifyTime { get; set; }
// }

// public enum NpNotifyType
// {
//     企业线索回访, 企业线索回访2
// }