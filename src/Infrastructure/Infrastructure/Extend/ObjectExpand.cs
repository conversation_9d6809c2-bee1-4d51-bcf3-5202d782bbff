﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Reflection;

namespace Infrastructure.Extend
{
    public static class ObjectExpand
    {
        /// <summary>
        /// 转成json
        /// </summary>
        /// <param name="val"></param>
        /// <param name="defaultValue"></param>
        /// <returns></returns>
        public static string ToJsonString(this object val, string defaultValue = "{}")
        {
            try
            {
                return Newtonsoft.Json.JsonConvert.SerializeObject(val);
            }
            catch (Exception)
            {
                return defaultValue;
            }
        }
        /// <summary>
        /// 将对象的值映射赋值到另外一个对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="source"></param>
        /// <param name="byEntity">被赋值的对象</param>
        /// <param name="Exc">忽略的字段</param>
        /// <returns></returns>
        public static T ToMapValue<T>(this T source, T byEntity, params string[] Exc) where T : notnull
        {
            List<string> exc = new List<string>();
            exc.Add("TagTimeStamp");
            if (Exc != null)
            {
                exc.AddRange(Exc);
            }
            foreach (PropertyInfo item in source.GetType().GetProperties())
            {
                //排除字段
                if (!exc.Contains(item.Name))
                {
                    PropertyInfo? infid = byEntity.GetType().GetProperty(item.Name);
                    if (infid != null && infid.PropertyType == item.PropertyType)
                    {
                        infid.SetValue(byEntity, item.GetValue(source));
                    }
                }
            }
            return byEntity;
        }

        #region 对象处理
        /// <summary>
        /// 对象新增属性
        /// </summary>
        /// <param name="val"></param>
        /// <param name="fieldName"></param>
        /// <param name="fieldValue"></param>
        /// <returns></returns>
        public static object AddProperty(this object val, string fieldName, object fieldValue)
        {
            return AddProperty(val, new KeyValuePair<string, object>(fieldName, fieldValue));
        }
        /// <summary>
        /// 新增枚举描述字段
        /// </summary>
        /// <param name="val"></param>
        /// <param name="enumFieldName"></param>
        /// <param name="enumType"></param>
        /// <param name="outSuffix"></param>
        /// <returns></returns>
        public static object? AddEnumExplainProperty(this object val, string enumFieldName, Type enumType, string outSuffix = "Text")
        {
            return val.AddProperty(enumFieldName + outSuffix, (JToken obj) =>
            {
                int val = obj.Value<int>(enumFieldName);
                var arry = enumType.GetEnumValues();
                string? valText = string.Empty;
                foreach (var item in arry)
                {
                    if (item.GetHashCode() == val)
                    {
                        valText = item.ToString();
                        break;
                    }
                }
                return valText ?? string.Empty;
            });
        }
        /// <summary>
        /// 对象新增属性动态表达式
        /// </summary>
        /// <param name="val"></param>
        /// <param name="fieldName"></param>
        /// <param name="fieldValue"></param>
        /// <returns></returns>
        public static object? AddProperty(this object val, string fieldName, Func<JToken, object> fieldValue)
        {
            return AddProperty(val, new KeyValuePair<string, object>(fieldName, fieldValue));
        }
        /// <summary>
        /// 新增处理
        /// </summary>
        /// <param name="val"></param>
        /// <param name="field"></param>
        /// <returns></returns>
        private static object AddProperty(this object val, params KeyValuePair<string, object>[] field)
        {
            field = field ?? new KeyValuePair<string, object>[0];
            JToken jToken = JToken.Parse(JsonConvert.SerializeObject(val));
            if (jToken.Type == JTokenType.Array)
            {
                //数组
                JArray jArray = (JArray)jToken;
                List<Dictionary<string, object>> newArrayObj = new List<Dictionary<string, object>>();
                foreach (var titem in jArray)
                {
                    JObject jObject = (JObject)titem;
                    Dictionary<string, object>? newObj = jObject.ToObject<Dictionary<string, object>>();
                    if (newObj != null)
                    {
                        foreach (var item in field)
                        {
                            object? value = item.Value;
                            if (item.Value.GetType() == typeof(Func<JToken, object>))
                                value = (item.Value as Func<JToken, object>)?.Invoke(titem);
                            if (value != null)
                                newObj.Add(item.Key, value);
                        }
                        newArrayObj.Add(newObj);
                    }
                }
                return newArrayObj;
            }
            else if (jToken.Type == JTokenType.Object)
            {
                //对象
                JObject jObject = (JObject)jToken;
                Dictionary<string, object> newObj = jObject.ToObject<Dictionary<string, object>>()!;

                foreach (var item in field)
                {
                    object? value = item.Value;
                    if (item.Value.GetType() == typeof(Func<JToken, object>))
                        value = (item.Value as Func<JToken, object>)?.Invoke(jToken);
                    if (value != null)
                        newObj.Add(item.Key, value);
                }
                return newObj;
            }
            else
            {
                throw new Exception("无法处理类型:" + jToken.Type.ToString());
            }
        }
        /// <summary>
        /// 移除属性
        /// </summary>
        /// <param name="val"></param>
        /// <param name="fieldName"></param>
        /// <returns></returns>
        public static object RemoveProperty(this object val, params string[] fieldName)
        {
            fieldName = fieldName ?? new string[0];
            JToken jToken = JToken.Parse(JsonConvert.SerializeObject(val));
            if (jToken.Type == JTokenType.Array)
            {
                //数组
                JArray jArray = (JArray)jToken;
                List<Dictionary<string, object>> newObj = new List<Dictionary<string, object>>();
                List<Dictionary<string, object>>? cObj = jArray.ToObject<List<Dictionary<string, object>>>();
                if (cObj != null)
                    foreach (var titem in cObj)
                    {
                        Dictionary<string, object> newItemObj = new Dictionary<string, object>();
                        foreach (var item in titem)
                        {
                            if (!fieldName.Contains(item.Key))
                            {
                                newItemObj.Add(item.Key, item.Value);
                            }
                        }
                        newObj.Add(newItemObj);
                    }
                return newObj;
            }
            else if (jToken.Type == JTokenType.Object)
            {
                //对象
                Dictionary<string, object> newObj = new Dictionary<string, object>();
                Dictionary<string, object>? cObj = jToken.ToObject<Dictionary<string, object>>();
                if (cObj != null)
                    foreach (var item in cObj)
                    {
                        if (!fieldName.Contains(item.Key))
                        {
                            newObj.Add(item.Key, item.Value);
                        }
                    }
                return newObj;
            }
            else
            {
                throw new Exception("无法处理类型:" + jToken.Type.ToString());
            }
        }
        /// <summary>
        /// 修改属性
        /// </summary>
        /// <param name="val"></param>
        /// <param name="fieldName"></param>
        /// <param name="fieldValue"></param>
        /// <returns></returns>
        public static object? UpdateProperty(this object val, string fieldName, Func<JToken, object> fieldValue)
        {
            JToken jToken = JToken.Parse(JsonConvert.SerializeObject(val));
            if (jToken.Type == JTokenType.Array)
            {
                //数组
                JArray jArray = (JArray)jToken;
                List<Dictionary<string, object>> newArrayObj = new List<Dictionary<string, object>>();
                foreach (var titem in jArray)
                {
                    JObject jObject = (JObject)titem;
                    Dictionary<string, object>? newObj = jObject.ToObject<Dictionary<string, object>>();
                    //获取值
                    if (newObj != null)
                    {
                        newObj[fieldName] = fieldValue.Invoke(titem);
                        newArrayObj.Add(newObj);
                    }
                }
                return newArrayObj;
            }
            else if (jToken.Type == JTokenType.Object)
            {
                //对象
                JObject jObject = (JObject)jToken;
                Dictionary<string, object>? newObj = jObject.ToObject<Dictionary<string, object>>();
                //获取值
                if (newObj != null)
                    newObj[fieldName] = fieldValue.Invoke(jToken);
                return newObj;
            }
            else
            {
                throw new Exception("无法处理类型:" + jToken.Type.ToString());
            }
        }
        /// <summary>
        /// 修改属性
        /// </summary>
        /// <param name="val"></param>
        /// <param name="fieldName"></param>
        /// <param name="fieldValue"></param>
        /// <returns></returns>
        public static object? UpdateProperty(this object val, string fieldName, object fieldValue)
        {
            JToken jToken = JToken.Parse(JsonConvert.SerializeObject(val));
            if (jToken.Type == JTokenType.Array)
            {
                //数组
                JArray jArray = (JArray)jToken;
                List<Dictionary<string, object>> newArrayObj = new List<Dictionary<string, object>>();
                foreach (var titem in jArray)
                {
                    JObject jObject = (JObject)titem;
                    Dictionary<string, object>? newObj = jObject.ToObject<Dictionary<string, object>>();
                    //获取值
                    if (newObj != null)
                    {
                        newObj[fieldName] = fieldValue;
                        newArrayObj.Add(newObj);
                    }
                }
                return newArrayObj;
            }
            else if (jToken.Type == JTokenType.Object)
            {
                //对象
                JObject jObject = (JObject)jToken;
                Dictionary<string, object>? newObj = jObject.ToObject<Dictionary<string, object>>();
                //获取值
                if (newObj != null)
                    newObj[fieldName] = fieldValue;
                return newObj;
            }
            else
            {
                throw new Exception("无法处理类型:" + jToken.Type.ToString());
            }
        }
        #endregion
    }
}
