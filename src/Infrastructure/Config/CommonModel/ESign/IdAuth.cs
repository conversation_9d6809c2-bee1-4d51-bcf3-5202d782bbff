namespace Config.CommonModel.ESign;

/// <summary>
/// 发起运营商3要素核身认证
/// </summary>
public class Telecom3Factors
{
    /// <summary>
    /// 个人姓名
    /// </summary>
    public string? name { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    public string? idNo { get; set; }

    /// <summary>
    /// 手机号,仅限中国大陆11位手机号
    /// </summary>
    public string? mobileNo { get; set; }

    /// <summary>
    /// 指定是否使用运营商3要素信息比对详情版，如指定则核验失败可返回具体不匹配信息。该参数为空时默认为普通版。
    /// ADVANCED-详情版
    /// STANDARD-普通版
    /// 详情版：需要单独购买，具体购买方式请咨询e签宝工作人员
    /// 普通版：无需单独购买，信息比对核验失败，不会返回具体的不匹配信息
    /// </summary>
    public string? grade { get; set; } = "STANDARD";
}

public class Telecom3FactorsResponse
{
    /// <summary>
    /// 认证流程Id
    /// </summary>
    public string? flowId { get; set; }
}

/// <summary>
/// 运营商短信验证码校验
/// </summary>
public class Telecom3FactorsSmsCheck
{
    /// <summary>
    /// 认证流程Id
    /// </summary>
    public string? flowId { get; set; }

    /// <summary>
    /// 短信验证码，用户收到的6位数字验证码
    /// </summary>
    public string? authcode { get; set; }
}

/// <summary>
/// 发起个人刷脸核身认证
/// </summary>
public class FaceIndividual
{
    /// <summary>
    /// 个人姓名
    /// </summary>
    public string? name { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    public string? idNo { get; set; }

    /// <summary>
    /// 认证完成后业务重定向地址
    /// 注意：微信小程序刷脸，此字段无用
    /// </summary>
    public string? callbackUrl { get; set; }

    /// <summary>
    /// 个人刷脸实名认证方式
    /// TENCENT  腾讯微众银行认证
    /// ZHIMACREDIT  支付宝芝麻信用认证
    /// ESIGN  e签宝刷脸
    /// WE_CHAT_FACE  微信小程序刷脸
    /// </summary>
    public string? faceauthMode { get; set; }
}

public class FaceIndividualResponse
{
    /// <summary>
    /// 认证流程Id
    /// </summary>
    public string? flowId { get; set; }

    /// <summary>
    /// 获取个人刷脸实名认证短链接。
    /// </summary>
    public string? authUrl { get; set; }

    /// <summary>
    /// 获取个人刷脸实名认证长链接。
    /// </summary>
    public string? originalUrl { get; set; }

    /// <summary>
    /// 微信小程序刷脸使用
    /// </summary>
    public string? faceToken { get; set; }

    /// <summary>
    /// 链接失效时间,毫秒值
    /// </summary>
    public long? expire { get; set; }
}

/// <summary>
/// 个人实名刷脸结果核对
/// </summary>
public class FaceCheck
{
    /// <summary>
    /// 认证流程Id
    /// </summary>
    public string? flowId { get; set; }
}

public class FaceCheckResponse
{
    /// <summary>
    /// 刷脸结果，具体状态：
    /// ING：刷脸进行中
    /// SUCCESS：认证成功
    /// SCAN：已扫描
    /// FAIL：认证失败
    /// </summary>
    public string? status { get; set; }

    /// <summary>
    /// 刷脸结果信息
    /// </summary>
    public string? message { get; set; }
}