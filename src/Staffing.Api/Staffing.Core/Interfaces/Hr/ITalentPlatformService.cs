﻿using Config.CommonModel;
using Config.CommonModel.Business;
using Staffing.Model.Hr.TalentPlatform;

namespace Staffing.Core.Interfaces.Hr;

/// <summary>
/// 平台人才库服务接口
/// </summary>
public interface ITalentPlatformService
{
    /// <summary>
    /// 获取平台人才库列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    TalentPlatformListResponse GetTalentPlatformList(TalentPlatformListRequest model);

    /// <summary>
    /// 获取平台人才库简历详情
    /// </summary>
    /// <param name="platformId"></param>
    /// <returns></returns>
    Task<TalentPlatformResumeDetailsResponse> GetTalentPlatformDetails(string platformId);

    /// <summary>
    /// 删除平台人才库
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> DeleteTalentPlatform(DeleteTalentPlatformRequest model);

    /// <summary>
    /// 获取平台人才库简历评论列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    TalentPlatformCommentListResponse GetTalentPlatformCommentList(TalentPlatformCommentListRequest model);

    /// <summary>
    /// 新增平台人才库简历评论
    /// </summary>
    /// <param name="mdoel"></param>
    /// <returns></returns>
    EmptyResponse AddTalentPlatformComment(AddTalentPlatformCommentRequest model);

    /// <summary>
    /// 删除平台人才库简历评论
    /// </summary>
    /// <param name="commentId"></param>
    /// <returns></returns>
    EmptyResponse DeleteTalentPlatformComment(string commentId);

    /// <summary>
    /// 获取平台人才库标签
    /// </summary>
    /// <param name="platformId"></param>
    /// <returns></returns>
    TalentPlatformLabelResponse GetTalentPlatformLabel(string platformId);

    /// <summary>
    /// 变更平台人才库标签
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> EditTalentVirtualLabel(EditTalentPlatformLabelRequest model);
    
    /// <summary>
    /// 简历下载
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    string DownLoadResume(string id);

    /// <summary>
    /// 获取真实人才渠道商列表
    /// </summary>
    /// <returns></returns>
    List<ChannelInfo> GetChannelInfo();
    
    Task<TalentPlatformResumeDetailsResponse> GetResumeDetails(string userId);
}

