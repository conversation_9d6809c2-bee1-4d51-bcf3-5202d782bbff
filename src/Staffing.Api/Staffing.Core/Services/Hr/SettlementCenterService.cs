using Config.CommonModel;
using Infrastructure.Extend;
using Staffing.Entity;
using Staffing.Core.Interfaces.Hr;
using Staffing.Model.Hr.SettlementCenter;
using Microsoft.Extensions.Options;
using Config;
using Infrastructure.Common;
using Microsoft.Extensions.DependencyInjection;
using OfficeOpenXml.Packaging.Ionic.Zip;
using LinqKit;
using Staffing.Entity.Staffing;
using Google.Rpc;
using Infrastructure.Exceptions;

namespace Staffing.Core.Services.Hr;

[Service(ServiceLifetime.Transient)]
public class SettlementCenterService : ISettlementCenterService
{
    private readonly StaffingContext _context;
    private readonly RequestContext _user;
    private readonly ConfigManager _config;

    public SettlementCenterService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config)
    {
        _user = user;
        _context = context;
        _config = config.Value;
    }

    /// <summary>
    /// 获取订单收入列表
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <returns>收入列表</returns>
    public OrderIncomeListResponse GetOrderIncomeList(OrderIncomeRequest request)
    {
        var result = new OrderIncomeListResponse();

        var predicate = PredicateBuilder.New<Post_Settlement_Detail>(x => x.PaymentStatus != Config.Enums.PaymentStatus.未打款);

        if (string.IsNullOrEmpty(request.MerchantId))
            throw new BadRequestException("商户ID不能为空");

        var platformMerchant = _context.Enterprise_Merchant.Where(x => x.MerchantType == Config.Enums.MerchantType.平台)
            .Select(x => new { x.MerchantName, x.Id }).FirstOrDefault();

        if (platformMerchant == null)
            throw new BadRequestException("缺少平台商户");

        var isPlatform = request.MerchantId.Trim() == platformMerchant.Id.Trim();

        if (isPlatform)
        {
            predicate = predicate.And(x => x.InMerchantId != x.OutMerchantId || x.InMerchantId == request.MerchantId);
        }
        else
        {
            // 非平台商户查询收入
            predicate = predicate.And(x => x.InMerchantId == request.MerchantId);
        }

        if (request.StartTime.HasValue)
            predicate = predicate.And(x => x.Post_Settlement.FinanceConfirmTime >= request.StartTime.Value);

        if (request.EndTime.HasValue)
        {
            var endTime = request.EndTime.Value.Date.AddDays(1);
            predicate = predicate.And(x => x.Post_Settlement.FinanceConfirmTime < endTime);
        }

        var sql = _context.Post_Settlement_Detail.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
            .OrderByDescending(o => o.Post_Settlement.FinanceConfirmTime)
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(o => new OrderIncomeResponse
            {
                OrderNumber = o.Id,
                TransactionSuccessTime = o.Post_Settlement.FinanceConfirmTime ?? o.Post_Settlement.CreatedTime,
                Receiver = o.UserType == Config.Enums.PostSettleUserType.平台 ? platformMerchant.MerchantName : o.User_Hr.NickName,
                JobNumber = o.UserNo ?? string.Empty,
                SettlementAmount = o.SettleBountyType == Config.Enums.PostSettleBountyType.内部 ? o.SettlementMoney * Constants.ServiceFeeMultiplier : o.SettlementMoney,
                SettlementStatus = o.PaymentStatus == Config.Enums.PaymentStatus.打款成功 ? 1 : 0,
                Amount = o.SettlementMoney,
                OutMerchantName = o.OutMerchant.MerchantName,
                IncomeType = "收入",
                Remark = o.Remark ?? string.Empty,
                TransactionType = o.InMerchantId == o.OutMerchantId ? "调账" : "转账",
            }).ToList();

        foreach (var item in result.Rows)
        {
            item.SettlementStatusText = item.SettlementStatus == 1 ? "已结算" : "未结算";
        }

        return result;
    }

    /// <summary>
    /// 获取订单消费列表
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <returns>消费列表</returns>
    public OrderExpenseListResponse GetOrderExpenseList(OrderExpenseRequest request)
    {
        var result = new OrderExpenseListResponse();
        var predicate = PredicateBuilder.New<Post_Settlement_Detail>(x => x.PaymentStatus != Config.Enums.PaymentStatus.未打款);

        if (string.IsNullOrEmpty(request.MerchantId))
            throw new BadRequestException("商户ID不能为空");

        var platformMerchant = _context.Enterprise_Merchant.Where(x => x.MerchantType == Config.Enums.MerchantType.平台)
        .Select(x => new { x.MerchantName, x.Id }).FirstOrDefault();

        if (platformMerchant == null)
            throw new BadRequestException("平台商户不存在");

        var isPlatform = request.MerchantId.Trim() == platformMerchant.Id.Trim();

        if (isPlatform)
        {
            predicate = predicate.And(x => x.OutMerchantId == request.MerchantId || (x.InMerchantId != x.OutMerchantId && x.InMerchantId != platformMerchant.Id));
        }
        else
        {
            // 非平台商户查询支出
            predicate = predicate.And(x => x.OutMerchantId == request.MerchantId);
        }

        if (request.StartTime.HasValue)
            predicate = predicate.And(x => x.Post_Settlement.FinanceConfirmTime >= request.StartTime.Value);

        if (request.EndTime.HasValue)
        {
            var endTime = request.EndTime.Value.Date.AddDays(1);
            predicate = predicate.And(x => x.Post_Settlement.FinanceConfirmTime < endTime);
        }

        var sql = _context.Post_Settlement_Detail.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
            .OrderByDescending(o => o.Post_Settlement.FinanceConfirmTime)
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(o => new OrderExpenseResponse
            {
                OrderNumber = o.Id,
                TransactionSuccessTime = o.Post_Settlement.FinanceConfirmTime ?? o.Post_Settlement.CreatedTime,
                PlatformProject = o.Post_Settlement.Post_Bounty_Stage.Post_Bounty.Post.Project.Agent_Ent.Name,
                AutoId = o.Post_Settlement.Post_Bounty_Stage.Post_Bounty.Post.Project.AutoId,
                SigningEntity = o.OutMerchant.MerchantName,
                ProjectType = o.Post_Settlement.Post_Bounty_Stage.Post_Bounty.Post.Project.Type,
                ExpenseType = "支出",
                Payee = (isPlatform || o.InMerchantId == o.OutMerchantId) ? o.InMerchant.MerchantName : platformMerchant.MerchantName,
                TransactionType = o.InMerchantId == o.OutMerchantId ? "调账" : "转账",
                Amount = o.SettlementMoney
            }).ToList();

        var idPre = Constants.ProjectIdPre;
        foreach (var item in result.Rows)
        {
            item.ProjectCode = $"{idPre}{item.AutoId.ToString().PadLeft(6, '0')}";
            item.ProjectTypeText = item.ProjectType?.GetDescription() ?? string.Empty;
        }

        return result;
    }
}
