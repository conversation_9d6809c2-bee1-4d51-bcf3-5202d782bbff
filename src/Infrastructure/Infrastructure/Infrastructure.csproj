<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup>
    <NoWarn>1701;1702;1591;NU1803;CS8981;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AlibabaCloud.SDK.Dingtalk" Version="2.0.81" />
    <PackageReference Include="AlibabaCloud.SDK.Dyplsapi20170525" Version="1.0.5" />
    <PackageReference Include="AspectCore.Extensions.DependencyInjection" Version="2.4.0" />
    <PackageReference Include="CanalSharp" Version="1.2.0" />
    <PackageReference Include="DateOnlyTimeOnly.AspNet.Swashbuckle" Version="2.1.1" />
    <PackageReference Include="Essensoft.Paylink.WeChatPay" Version="4.1.2" />
    <PackageReference Include="FreeRedis" Version="1.1.5" />
    <PackageReference Include="IGeekFan.AspNetCore.Knife4jUI" Version="0.0.16" />
    <PackageReference Include="LinqKit" Version="1.3.0" />
    <PackageReference Include="MailKit" Version="4.1.0" />
    <PackageReference Include="Flurl.Http" Version="3.2.4" />
    <PackageReference Include="Aliyun.OSS.SDK.NetCore" Version="2.13.0" />
    <PackageReference Include="MiniWord" Version="0.7.0" />
    <PackageReference Include="NPOI" Version="2.6.1" />
    <PackageReference Include="QuestPDF" Version="2024.6.4" />
    <PackageReference Include="Senparc.Weixin.Cache.Redis" Version="2.15.12.2" />
    <PackageReference Include="Senparc.Weixin.MP" Version="16.19.2.2" />
    <PackageReference Include="Senparc.Weixin.MP.Middleware" Version="0.8.6" />
    <PackageReference Include="Senparc.Weixin.WxOpen" Version="3.16.2.2" />
    <PackageReference Include="ConfigMapFileProvider" Version="2.0.1" />
    <PackageReference Include="Noah.Aliyun" Version="5.3.3" />
	  <PackageReference Include="Noah.ImExportTools" Version="0.1.5" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.10" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="10.0.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
    <PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.1.5" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.9.0" />
    <PackageReference Include="Unchase.Swashbuckle.AspNetCore.Extensions" Version="2.7.1" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="4.8.1" />
    <PackageReference Include="System.ServiceModel.Federation" Version="4.8.1" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.8.1" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="4.8.1" />
    <PackageReference Include="System.ServiceModel.Security" Version="4.8.1" />
    <PackageReference Include="TencentCloudSDK.Faceid" Version="3.0.809" />
    <PackageReference Include="tls-sig-api-v2" Version="1.0.1" />
    <PackageReference Include="RocketMQ.Client" Version="5.1.0" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Entity\Staffing.Entity\Staffing.Entity.csproj" />
  </ItemGroup>
  <ItemGroup>
		<None Include="Font\fs.ttf">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="CommonService\OrderSettlement\" />
  </ItemGroup>
</Project>