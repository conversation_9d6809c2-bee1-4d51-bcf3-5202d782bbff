using Config;
using Config.CommonModel.Business;
using Config.CommonModel.Noah;
using Config.Enums;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using ServiceStack.Text;

namespace Infrastructure.Proxy;

[Service(ServiceLifetime.Transient)]
public class NuoPinApi
{
    private readonly ConfigManager _config;
    private readonly LogManager _log;
    public NuoPinApi(IOptionsSnapshot<ConfigManager> config, LogManager log)
    {
        _config = config.Value;
        _log = log;
    }

    /// <summary>
    /// 获取诺聘token
    /// </summary>
    /// <returns></returns>
    public async Task<GetNuoPinTokenInfo> NoahHrLogin(string? mobile, string? pkeaid)
    {
        try
        {
            string url = $"{_config.DataRdsServer}/NoahAccredit/CentreService/v1/Transit/StaffingHrLogin";

            var token = await GetToken();

            var result = await url.WithOAuthBearerToken(token.AccessToken)
            .PostJsonAsync(new { mobile = mobile, pkeaid = pkeaid })
            .ReceiveJson<NuoApi<GetNuoPinTokenInfo>>();

            if (result?.Data == null || result?.ErrCode != 200)
                throw new Exception("hr获取诺聘token失败");

            return result.Data;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"hr获取诺聘token失败:{ex.StatusCode}_{error}");
        }
        catch (Exception e)
        {
            _log.Error("hr获取诺聘token失败", Tools.GetErrMsg(e), $"{mobile}_{pkeaid}");
            throw;
        }
    }

    /// <summary>
    /// 解析诺聘用户token
    /// </summary>
    /// <returns></returns>
    public async Task<GetUserByToken?> xxx(string token, TokenType type)
    {
        string url = $"{_config.DataRdsServer}/NoahAccredit/OpenService/v1/User/GetUserByToken";

        var result = (await url.WithOAuthBearerToken(token)
        .PostJsonAsync(new { tokenType = type })
        .ReceiveJson<NuoApi<GetUserByToken>>())?.Data;

        return result;
    }

    /// <summary>
    /// 解析诺聘用户token
    /// </summary>
    /// <returns></returns>
    public async Task<GetUserByToken?> GetUserByToken(string token, TokenType type)
    {
        string url = $"{_config.DataRdsServer}/NoahAccredit/OpenService/v1/User/GetUserByToken";

        var result = (await url.WithOAuthBearerToken(token)
        .PostJsonAsync(new { tokenType = type })
        .ReceiveJson<NuoApi<GetUserByToken>>())?.Data;

        return result;
    }

    /// <summary>
    /// 解析诺聘平台端token
    /// </summary>
    /// <returns></returns>
    public async Task<GetUserByToken?> GetPlatformUserByToken(string token)
    {
        string url = $"{_config.DataRdsServer}/NoahAccredit/CentreService/v1/User/GetUserByToken";

        var result = (await url.WithOAuthBearerToken(token)
        .PostJsonAsync(null)
        .ReceiveJson<NuoApi<GetUserByToken>>())?.Data;

        return result;
    }

    /// <summary>
    /// 获取诺聘hr信息
    /// </summary>
    /// <returns></returns>
    public async Task<GetEnterpriseAccount> GetEnterpriseAccount(string eaid)
    {
        string url = $"{_config.DataRdsServer}/NoahAccredit/CentreService/v1/Transit/GetEnterpriseAccountForStaffing";

        var token = await GetToken();

        var result = (await url.WithOAuthBearerToken(token.AccessToken)
        .PostJsonAsync(new { PK_EAID = eaid })
        .ReceiveJson<NuoApi<GetEnterpriseAccount>>())?.Data;

        if (result == null)
            throw new Exception("获取诺聘hr信息失败");

        return result;
    }

    /// <summary>
    /// 获取诺聘网点列表
    /// </summary>
    /// <returns></returns>
    public async Task<List<GetOutletsInfo>> GetOutlets()
    {
        string url = $"{_config.DataRdsServer}/NoahAccredit/CentreService/v1/Transit/GetAllSceneServiceHall";

        var token = await GetToken();

        var result = (await url.WithOAuthBearerToken(token.AccessToken)
        .PostJsonAsync(new { })
        .ReceiveJson<NuoApi<List<GetOutletsInfo>>>())?.Data;

        if (result == null)
            throw new Exception("获取诺聘网点列表失败");

        return result;
    }

    /// <summary>
    /// 诺聘静默注册
    /// </summary>
    /// <returns></returns>
    public async Task SilentRegistration(string mobile)
    {
        try
        {
            string url = $"{_config.DataRdsServer}/NoahAccredit/CentreService/v1/Transit/SilentRegistration";

            var token = await GetToken();

            var result = await url.WithOAuthBearerToken(token.AccessToken)
            .PostJsonAsync(new { Phone = mobile, FromType = 50 })
            .ReceiveJson<NuoApi<object>>();

            if (result?.ErrCode != 200)
                throw new Exception($"诺聘静默注册失败：{JsonSerializer.SerializeToString(result)}");
        }
        catch (Exception e)
        {
            _log.Error("诺聘静默注册失败", $"{e.Message}、{e.InnerException?.Message}", mobile);
            throw;
        }
    }

    /// <summary>
    /// 职位变更推送诺聘
    /// </summary>
    /// <returns></returns>
    public async Task PostPush(List<PostSyncResponse> model)
    {
        string url = $"{_config.DataRdsServer}/NoahRecruit/CentreService/v2/Transit/PostReceive";

        var token = await GetToken();

        foreach (var item in model)
        {
            if (item.Post != null)
                item.Post.Describe = Tools.HandleStringByMark(item.Post.Describe, "/*", "*/");
        }

        try
        {
            var result = await url.WithOAuthBearerToken(token.AccessToken)
            .PostJsonAsync(new { RequestModel = model })
            .ReceiveJson<NuoApi<object?>>();

            if (result?.ErrCode != 200)
            {
                _log.Error("职位变更推送诺聘失败1", JsonSerializer.SerializeToString(result), JsonSerializer.SerializeToString(model));
                throw new Exception($"职位变更推送诺聘失败2{JsonSerializer.SerializeToString(result)}");
            }

            _log.Info("职位变更推送诺聘", result.ErrCode.ToString(), string.Join(',', model.Select(s => s.Type == PostSyncType.职位变更 ? s.Post?.Name : s.Hr?.HrName)));
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            _log.Error("职位变更推送诺聘失败3", $"httpstatus:{ex.StatusCode}_{error}", JsonSerializer.SerializeToString(model));
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 获取诺聘看板
    /// </summary>
    /// <returns></returns>
    public async Task<NuopinPostPanel> GetPostPanel()
    {
        string url = $"{_config.DataRdsServer}/NoahRecruit/CentreService/v1/Transit/StaffingGetPostPanel";

        var token = await GetToken();

        try
        {

            var result = (await url.WithOAuthBearerToken(token.AccessToken)
            .PostJsonAsync(null)
            .ReceiveJson<NuoApi<NuopinPostPanel>>())?.Data;

            if (result == null)
                throw new Exception("首页获取诺聘看板失败");

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 获取诺聘企业发布职位时间
    /// </summary>
    /// <returns></returns>
    public async Task<List<GetPositionIncr>> GetPositionIncr(DateTime beginTime)
    {
        string url = $"{_config.DataRdsServer}/NoahRecruit/CentreService/v2/Transit/GetPositionIncr";

        var token = await GetToken();

        try
        {
            var result = (await url.WithOAuthBearerToken(token.AccessToken)
            .PostJsonAsync(new { LastTime = beginTime.ToYYYY_MM_DD_HH() })
            .ReceiveJson<NuoApi<List<GetPositionIncr>>>())?.Data;

            if (result == null)
                throw new Exception("获取诺聘企业发布职位时间失败");

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 获取诺聘企业收到简历时间
    /// </summary>
    /// <returns></returns>
    public async Task<List<GetDeliverIncr>> GetDeliverIncr(DateTime beginTime)
    {
        string url = $"{_config.DataRdsServer}/NoahRecruit/CentreService/v2/Transit/GetDeliverIncr";

        var token = await GetToken();

        try
        {
            // var result = (await url.WithOAuthBearerToken(token.AccessToken)
            // .PostJsonAsync(new { LastTime = beginTime.ToYYYY_MM_DD_HH() })
            // .ReceiveJson<NuoApi<List<GetDeliverIncr>>>())?.Data;

            var resultStr = (await url.WithOAuthBearerToken(token.AccessToken)
            .PostJsonAsync(new { LastTime = beginTime.ToYYYY_MM_DD_HH() })
            .ReceiveString());

            List<GetDeliverIncr>? result;
            try
            {
                result = JsonSerializer.DeserializeFromString<NuoApi<List<GetDeliverIncr>>>(resultStr)?.Data;
                if (result == null)
                    throw new Exception("获取诺聘企业收到简历时间失败，序列化失败");

                return result;
            }
            catch
            {
                _log.Error("获取诺聘企业收到简历时间失败", resultStr);
                throw;
            }
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 诺聘获取token
    /// </summary>
    /// <returns></returns>
    public async Task<NuoPinToken> GetToken()
    {
        var cacheKey = $"nuopintoken";

        var result = MyRedis.Client.Get<NuoPinToken?>(cacheKey);

        if (!string.IsNullOrWhiteSpace(result?.AccessToken))
            return result;

        using var locker = await MyRedis.Lock("getnpinternaltoken", 30);

        if (locker == null)
            throw new Exception("自动注册雪花worker获取redis锁失败");

        result = MyRedis.Client.Get<NuoPinToken?>(cacheKey);

        if (!string.IsNullOrWhiteSpace(result?.AccessToken))
            return result;

        string url = $"{_config.DataRdsServer}/noahaccredit/oauth/token";

        try
        {
            result = (await url.PostJsonAsync(new
            {
                grant_type = "client_credentials",
                client_id = "6030519482000010006",
                client_secret = "2F031256C24542339C36597E1F381876",
                scope = "NoahAccredit NoahRecruit NoahMetadata NoahCircle NoahActivity NoahSettle NoahBring NoahTalentPool"
            }).ReceiveJson<NuoApi<NuoPinToken>>())?.Data;

            if (string.IsNullOrWhiteSpace(result?.AccessToken))
                throw new Exception("获取诺聘内部token失败");

            MyRedis.Client.Set(cacheKey, result, result.TokenExpiresTime / 2);

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 获取企业今日投递数量
    /// </summary>
    /// <returns></returns>
    public async Task<List<GetEntDeliverCountResponse>> GetEntDeliverCount(List<string> entIds)
    {
        string url = $"{_config.DataRdsServer}/NoahRecruit/CentreService/V1/Transit/GetEntDeliverCount";

        var token = await GetToken();

        try
        {
            var result = (await url.WithOAuthBearerToken(token.AccessToken)
            .PostJsonAsync(entIds)
            .ReceiveJson<NuoApi<List<GetEntDeliverCountResponse>>>())?.Data;

            if (result == null)
                throw new Exception("获取企业今日投递数量失败");

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 获取企业岗位列表
    /// </summary>
    /// <param name="EnterpriseID"></param>
    /// <param name="PageIndex"></param>
    /// <param name="PageSize"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<NuoApi<NuoApiPageData<List<GetNPJobPositionsInfo>>>> GetEntJobPositions(string EnterpriseID, string Search, int PageIndex, int PageSize, bool IsGetDeliverCount = false)
    {
        string url = $"{_config.DataRdsServer}/NoahRecruit/CentreService/V1/Transit/GetEntJobPositions";

        var token = await GetToken();

        try
        {
            var result = (await url.WithOAuthBearerToken(token.AccessToken)
            .PostJsonAsync(new { EnterpriseID, Search, PageIndex, PageSize, Status = 1, IsGetDeliverCount = IsGetDeliverCount })
            .ReceiveJson<NuoApi<NuoApiPageData<List<GetNPJobPositionsInfo>>>>());

            if (result == null)
                throw new Exception("获取企业岗位列表失败");

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 获取投递的简历
    /// </summary>
    /// <param name="EnterpriseID"></param>
    /// <param name="PageIndex"></param>
    /// <param name="PageSize"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<NuoApi<NuoApiPageData<List<GetNPDeliverInfo>>>> GetDeliverList(string EnterpriseID, string PK_WPID, int PageIndex, int PageSize)
    {
        string url = $"{_config.DataRdsServer}/NoahRecruit/CentreService/V1/Transit/GetDeliverList";

        var token = await GetToken();

        try
        {
            var result = (await url.WithOAuthBearerToken(token.AccessToken)
            .PostJsonAsync(new { EnterpriseID, PK_WPID, PageIndex, PageSize })
            .ReceiveJson<NuoApi<NuoApiPageData<List<GetNPDeliverInfo>>>>());

            if (result == null)
                throw new Exception("获取投递的简历失败");

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 新增与求职者的沟通记录
    /// </summary>
    /// <param name="PK_UID">求职者ID</param>
    /// <param name="Design">目的</param>
    /// <param name="Result">结果</param>
    /// <param name="Remark">备注</param>
    /// <param name="AccountID">操作人账号ID</param>
    /// <param name="AccountName">操作人姓名</param>
    /// <returns></returns>
    public async Task SaveUserContactRecord(string PK_UID,
            [FromForm] string Design,
            [FromForm] string Result,
            [FromForm] string Remark,
            [FromForm] string AccountID,
            [FromForm] string AccountName)
    {
        try
        {
            string url = $"{_config.DataRdsServer}/NoahAccredit/CentreService/v1/Transit/SaveUserContactRecord";

            var token = await GetToken();

            var result = await url.WithOAuthBearerToken(token.AccessToken)
            .PostJsonAsync(new
            {
                PK_UID,
                Design = Design ?? string.Empty,
                Result = Result ?? string.Empty,
                Remark = Remark ?? string.Empty,
                AccountID,
                AccountType = 3,
                AccountName
            })
            .ReceiveJson<NuoApi<object>>();

            if (result?.ErrCode != 200)
                throw new Exception($"诺聘新增与求职者的沟通记录失败：{JsonSerializer.SerializeToString(result)}");
        }
        catch (Exception e)
        {
            _log.Error("诺聘新增与求职者的沟通记录失败", $"{e.Message}、{e.InnerException?.Message}", $"PK_UID={PK_UID},Design={Design ?? string.Empty},Result={Result ?? string.Empty},Remark={Remark ?? string.Empty},AccountID={AccountID}");
            throw;
        }
    }

    /// <summary>
    /// 获取与求职者的沟通记录(仅获取快聘操作的记录)
    /// </summary>
    /// <param name="PK_UID">求职者ID</param>
    /// <param name="AccountID">操作人账号ID</param>
    /// <param name="PageIndex">页码</param>
    /// <param name="PageSize">每页显示记录数</param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<NuoApi<NuoApiPageData<List<GetNPUserContactInfo>>>> GetUserContactRecord(string PK_UID, string AccountID, int PageIndex, int PageSize)
    {
        string url = $"{_config.DataRdsServer}/NoahAccredit/CentreService/V1/Transit/GetUserContactRecordForStaffing";

        var token = await GetToken();

        try
        {
            var result = (await url.WithOAuthBearerToken(token.AccessToken)
            .PostJsonAsync(new { PK_UID, AccountID = AccountID ?? string.Empty, PageIndex, PageSize, AccountType = 3 })
            .ReceiveJson<NuoApi<NuoApiPageData<List<GetNPUserContactInfo>>>>());

            if (result == null)
                throw new Exception("获取诺聘与求职者的沟通记录失败");

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 根据企业名称查询企业信息
    /// </summary>
    /// <returns></returns>
    public async Task<List<GetEntByNameResponse>> GetEntByName(string EntName)
    {
        string url = $"{_config.DataRdsServer}/NoahAccredit/CentreService/V1/Transit/GetEntByName";

        var token = await GetToken();

        try
        {
            var result = (await url.WithOAuthBearerToken(token.AccessToken)
            .PostJsonAsync(new { EntName })
            .ReceiveJson<NuoApi<List<GetEntByNameResponse>>>())?.Data;

            if (result == null)
                throw new Exception("根据企业名称查询企业信息失败");

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 销售易企业同步诺聘
    /// </summary>
    /// <returns></returns>
    public async Task<List<SyncSalesEasyHRInfo>> SyncSalesEasyHR(SyncSalesEasyHRRequest model)
    {
        string url = $"{_config.DataRdsServer}/NoahAccredit/CentreService/V1/Transit/SalesEasySyncHRInfo";

        var token = await GetToken();

        try
        {
            // var result = (await url.WithOAuthBearerToken(token.AccessToken)
            // .PostJsonAsync(new { model })
            // .ReceiveJson<NuoApi<List<SyncSalesEasyHRInfo>>>())?.Data;

            var resultStr = await url.WithOAuthBearerToken(token.AccessToken)
            .PostJsonAsync(model)
            .ReceiveString();

            List<SyncSalesEasyHRInfo>? result;
            try
            {
                result = JsonSerializer.DeserializeFromString<NuoApi<List<SyncSalesEasyHRInfo>>>(resultStr)?.Data;
                if (result == null)
                    throw new Exception("销售易企业同步诺聘失败，序列化失败");
            }
            catch
            {
                _log.Error("销售易企业同步诺聘失败", resultStr, model);
                throw;
            }

            if (result == null)
                throw new Exception("销售易企业同步诺聘失败");

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }
}

public class NuoPinToken
{
    public string AccessToken { get; set; } = string.Empty;
    public int TokenExpiresTime { get; set; }
    public string TokenType { get; set; } = string.Empty;
    public string Scope { get; set; } = string.Empty;
}

public class NuoApi<T>
{
    public int ErrCode { get; set; }
    public string ErrMsg { get; set; } = string.Empty;
    public T? Data { get; set; }
}

public class NuoApiPageData<T>
{
    public T? Rows { get; set; }

    public int Total { get; set; }
}