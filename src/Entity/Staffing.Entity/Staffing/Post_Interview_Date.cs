﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 职位面试日期表
/// </summary>
[Table("post_interivew_date")]
public class Post_Interview_Date
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 职位ID
    /// </summary>
    public string PostId { get; set; } = default!;

    /// <summary>
    /// 状态
    /// </summary>
    public PostInterviewTimeStatus Status { get; set; } = PostInterviewTimeStatus.可预约;

    /// <summary>
    /// 预约人数
    /// </summary>
    public int Num { get; set; }

    /// <summary>
    /// 面试时间
    /// </summary>
    public DateTime Time { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Post Post { get; set; } = default!;
}