﻿using FreeRedis;
using ServiceStack.Text;

namespace Infrastructure.Common;

public class MyRedis
{
    public static RedisClient Client = default!;

    // <summary>
    /// 开启分布式锁，若超时返回null
    /// </summary>
    /// <param name="name">锁名称</param>
    /// <param name="timeoutSeconds">超时（秒）</param>
    /// <param name="autoDelay">自动延长锁超时时间，看门狗线程的超时时间为timeoutSeconds/2 ， 在看门狗线程超时时间时自动延长锁的时间为timeoutSeconds。除非程序意外退出，否则永不超时。</param>
    /// <param name="reConnectMs">重新尝试获取锁的时间（毫秒，freeredis默认3毫秒一次不支持修改）</param>
    /// <returns></returns>
    public static Task<MyLocker?> Lock(string name, int timeoutSeconds, bool autoDelay = true, int reConnectMs = 50)
    {
        return Task.Run(() =>
        {
            name = $"RdsLock:{name}";
            var startTime = DateTime.Now;
            while (DateTime.Now.Subtract(startTime).TotalSeconds < timeoutSeconds)
            {
                var value = Guid.NewGuid().ToString();
                if (Client.SetNx(name, value, timeoutSeconds) == true)
                {
                    double refreshSeconds = (double)timeoutSeconds / 2.0;
                    return new MyLocker(Client, name, value, timeoutSeconds, refreshSeconds, autoDelay);
                }
                Thread.CurrentThread.Join(reConnectMs);
            }
            return null;
        });
    }

    /// <summary>
    /// 尝试开启分布式锁，若失败立刻返回null
    /// </summary>
    /// <param name="name"></param>
    /// <param name="timeoutSeconds"></param>
    /// <returns></returns>
    public static MyLocker? TryLock(string name, int timeoutSeconds = 10)
    {
        name = $"RdsLock:{name}";
        var value = Guid.NewGuid().ToString();
        if (Client.SetNx(name, value, timeoutSeconds) == true)
            return new MyLocker(Client, name, value, 0, 0, false);
        return null;
    }

    /// <summary>
    /// redis封装延迟消息队列
    /// </summary>
    /// <param name="queueName"></param>
    /// <param name="values"></param>
    /// <param name="delaySeconds"></param>
    /// <param name="cover">是否覆盖</param>
    public static void DelayQueue(string queueName, string[] values, int delaySeconds, bool cover = true)
    {
        var ut = DateTime.Now.AddSeconds(delaySeconds).ToUnixTime();
        if (cover)
            Client.ZAdd(queueName, values.Select(s => new ZMember(s, ut)).ToArray());
        else
            Client.ZAddNx(queueName, values.Select(s => new ZMember(s, ut)).ToArray());
    }

    /// <summary>
    /// 从redis延迟队列取消息
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="queueName"></param>
    /// <param name="num"></param>
    /// <returns></returns>
    public static string?[]? DelayQueuePop(string queueName, int num)
    {
        var now = DateTime.Now.ToUnixTime();

        var script = @$"
            local elements = redis.call('ZRANGEBYSCORE', KEYS[1], '-inf', ARGV[1], 'LIMIT', 0, {num})
            for i, element in ipairs(elements) do
                redis.call('ZREM', KEYS[1], element)
            end
            return elements
            ";

        var obj = Client.Eval(script, new[] { queueName }, now.ToString());

        if (obj == null) return null;

        var result = obj as object[];
        return result?.Select(s => s.ToString())?.ToArray();
    }

    /// <summary>
    /// 删除队列
    /// </summary>
    /// <param name="queueName"></param>
    /// <param name="value"></param>
    public static void DelayQueueDel(string queueName, string value)
    {
        Client.ZRem(queueName, value);
    }
}

// public class MyRedisLocker : IDisposable
// {

//     string _name;
//     int _timeoutSeconds;

//     internal MyRedisLocker(string name, int timeoutSeconds)
//     {
//         _name = name;
//         _timeoutSeconds = timeoutSeconds;
//     }

//     public void Dispose() => MyRedisHelper.MyRedis.Del(_name);
// }

public class MyLocker : IDisposable
{

    RedisClient _client;
    string _name;
    string _value;
    int _timeoutSeconds;
    Timer? _autoDelayTimer;

    internal MyLocker(RedisClient rds, string name, string value, int timeoutSeconds, double refreshSeconds, bool autoDelay)
    {
        _client = rds;
        _name = name;
        _value = value;
        _timeoutSeconds = timeoutSeconds;
        if (autoDelay)
        {
            var refreshMilli = (int)(refreshSeconds * 1000);
            var timeoutMilli = timeoutSeconds * 1000;
            _autoDelayTimer = new Timer(state2 => Refresh(timeoutMilli), null, refreshMilli, refreshMilli);
        }
    }

    public bool Refresh(int milliseconds)
    {
        var ret = _client.Eval(@"local gva = redis.call('GET', KEYS[1])
if gva == ARGV[1] then
  redis.call('PEXPIRE', KEYS[1], ARGV[2])
  return 1
end
return 0", new[] { _name }, _value, milliseconds)?.ToString() == "1";
        if (ret == false) _autoDelayTimer?.Dispose(); //未知情况，关闭定时器
        return ret;
    }

    /// <summary>
    /// 释放分布式锁
    /// </summary>
    /// <returns>成功/失败</returns>
    public bool Unlock()
    {
        _autoDelayTimer?.Dispose();
        return _client.Eval(@"local gva = redis.call('GET', KEYS[1])
if gva == ARGV[1] then
  redis.call('DEL', KEYS[1])
  return 1
end
return 0", new[] { _name }, _value)?.ToString() == "1";
    }

    public void Dispose() => this.Unlock();
}
