using Config;
using Config.CommonModel.TalentResume;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Elasticsearch;
using Staffing.Entity.Staffing;

namespace Infrastructure.Aliyun;

[Service(ServiceLifetime.Transient)]
public class SecretNoHelper
{
    private readonly ConfigManager _config;
    private readonly RequestContext _user;
    private readonly IDbContextFactory<StaffingContext> _staffingContextFactory;
    private readonly EsHelper _esHelper;
    private readonly LogManager _logger;
    public SecretNoHelper(IOptionsSnapshot<ConfigManager> config, RequestContext user,
    IDbContextFactory<StaffingContext> staffingContextFactory, EsHelper esHelper, LogManager logger)
    {
        _config = config.Value;
        _user = user;
        _staffingContextFactory = staffingContextFactory;
        _esHelper = esHelper;
        _logger = logger;
    }

    public void CallRecord(List<CallRecord> model)
    {
        ///TODO:临时增加逻辑给客服使用座机，后续需要优化
        foreach (var item in model)
        {
            if (item.phone_no == "031166571203")
                item.phone_no = "18032887650";
            else if (item.phone_no == "031166571206")
                item.phone_no = "18032200083";
            else if (item.phone_no == "031185096169")
                item.phone_no = "18831106877";
            else if (item.phone_no == "031185090261")
                item.phone_no = "13292878828";
        }

        var fullIds = model.Select(s => s.id).ToList();

        var client = _esHelper.GetClient();

        //一次处理100条，防止数据量过大
        foreach (var ids in fullIds.Chunk(100))
        {
            using var db = _staffingContextFactory.CreateDbContext();
            var existsIds = db.PrivacyNumber.Where(w => ids.Contains(w.id)).Select(s => s.id).ToList();
            var notExistsIds = ids.Except(existsIds).ToList();
            var mobiles = model.Where(w => ids.Contains(w.id)).Select(s => s.phone_no).ToList();
            var toMobiles = model.Where(w => ids.Contains(w.id)).Select(s => s.peer_no).ToList();
            var userIds = db.User.Where(x => mobiles.Contains(x.Mobile)).Select(s => new
            {
                s.UserId,
                s.Mobile
            }).ToList();

            foreach (var item in notExistsIds)
            {
                var callRecord = model.FirstOrDefault(f => f.id == item);
                if (callRecord == null)
                    continue;

                var privacyNumber = new PrivacyNumber
                {
                    id = callRecord.id,
                    fromId = userIds.FirstOrDefault(f => f.Mobile == callRecord.phone_no)?.UserId,
                    phone_no = callRecord.phone_no,
                    city = callRecord.city,
                    call_out_time = callRecord.call_out_time,
                    ring_time = callRecord.ring_time,
                    record_url = callRecord.record_url,
                    ring_record_url = callRecord.ring_record_url,
                    free_ring_time = callRecord.free_ring_time,
                    control_msg = callRecord.control_msg,
                    secret_no = callRecord.secret_no,
                    call_type = callRecord.call_type,
                    control_type = callRecord.control_type,
                    release_time = callRecord.release_time,
                    pool_key = callRecord.pool_key,
                    sub_id = callRecord.sub_id,
                    unconnected_cause = callRecord.unconnected_cause,
                    call_time = callRecord.call_time,
                    peer_no = callRecord.peer_no,
                    called_display_no = callRecord.called_display_no,
                    call_id = callRecord.call_id,
                    out_id = callRecord.out_id,
                    partner_key = callRecord.partner_key,
                    release_cause = callRecord.release_cause,
                    release_dir = callRecord.release_dir,
                    start_time = callRecord.start_time
                };
                db.PrivacyNumber.Add(privacyNumber);
            }

            db.SaveChanges();

            //打通电话的话记录
            var phones = model.Where(w => notExistsIds.Contains(w.id)).Select(s => new
            {
                s.phone_no,
                s.peer_no,
                s.start_time,
                s.release_time
            }).ToList();

            var resumes = client.Search<EsTalentResume>(s => s
                .Index(_esHelper.GetIndex().TalentResume)
                .TrackTotalHits()
                //判断手机在toMobiles数组里的
                .Query(q => q.Terms(t => t.Field(f => f.Mobile).Terms(toMobiles)))
                //查询某字段
                .Source(s => s.Includes(i => i.Fields(f => f.Id, f => f.Mobile, f => f.Status)))
            ).Documents.ToList();

            var xis = new List<TalentResumeRecordRedis>();
            var hides = new List<TalentResumeHideRecord>();
            foreach (var item in phones)
            {
                //如果未接通，不记录，如果start_time时间等于release_time，说明未接通
                if (item.start_time == item.release_time)
                    continue;

                var hrId = userIds.Where(f => f.Mobile == item.phone_no).Select(s => s.UserId).FirstOrDefault();
                var resumeIds = resumes.Where(w => w.Mobile == item.peer_no).Select(s => s.Id).Distinct().ToList();
                if (!string.IsNullOrWhiteSpace(hrId))
                {
                    foreach (var item2 in resumeIds)
                    {
                        xis.Add(new TalentResumeRecordRedis
                        {
                            HrId = hrId,
                            ResumeId = item2,
                            ConnectType = ConnectType.拨打电话,
                            //拨打电话时间
                            CreatedTime = item.start_time
                        });
                    }
                }

                if((int)(item.release_time! - item.start_time!).Value.TotalSeconds >= 30)
                {
                    var resume = resumes.FirstOrDefault(f => f.Status != Config.Enums.TalentResumeStatus.Deleted && f.Mobile == item.peer_no);
                    if (resume == null)
                        continue;

                    hides.Add(new TalentResumeHideRecord
                    {
                        HideType = HideType.呼叫,
                        StartTime = item.release_time!.Value,
                        ResumeId = resume.Id,
                        status = resume.Status.GetDescription()
                    });
                }
            }

            // 添加操作记录
            if (xis.Count > 0)
                MyRedis.Client.LPush(SubscriptionKey.TalentResumeRecord, xis.ToArray());

            if (hides.Count > 0)
            {
                MyRedis.Client.LPush(SubscriptionKey.TalentResumeHide, hides.ToArray());
                _logger.Info("talentresumehide",hides.Count + "", hides.FirstOrDefault()!.ResumeId + "_" + hides.FirstOrDefault()!.status);
            }
        }
    }
}