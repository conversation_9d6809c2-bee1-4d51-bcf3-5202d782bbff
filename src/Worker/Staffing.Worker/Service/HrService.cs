using Config;
using Config.CommonModel.Business;
using Config.CommonModel.Noah;
using Config.Enums;
using DocumentFormat.OpenXml.Spreadsheet;
using Infrastructure.Aliyun;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Infrastructure.Proxy;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Noah;
using Staffing.Entity.Staffing;

namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class HrService
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _logger;
    private readonly CommonDicService _commonDicService;
    private readonly CacheHelper _cacheHelper;
    private readonly SmsHelper _smsHelper;
    private readonly DingDingRootHelper _dingDingRootHelper;
    private readonly IDbContextFactory<NoahContext> _noahContextFactory;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly NuoPinApi _nuoPinApi;
    private readonly CommonProjectService _commonProjectService;
    public HrService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger, CommonDicService commonDicService,
    CacheHelper cacheHelper, SmsHelper smsHelper, DingDingRootHelper dingDingRootHelper, IHostEnvironment hostingEnvironment,
    IDbContextFactory<NoahContext> noahContextFactory, NuoPinApi nuoPinApi, CommonProjectService commonProjectService)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _commonDicService = commonDicService;
        _cacheHelper = cacheHelper;
        _smsHelper = smsHelper;
        _dingDingRootHelper = dingDingRootHelper;
        _noahContextFactory = noahContextFactory;
        _hostingEnvironment = hostingEnvironment;
        _nuoPinApi = nuoPinApi;
        _commonProjectService = commonProjectService;
    }

    /// <summary>
    /// 销售机会通知顾问
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubXsjhToHr(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.LPop(SubscriptionKey.XsjhToHr);

                if (string.IsNullOrWhiteSpace(msg))
                {
                    await Task.Delay(500);
                    continue;
                }

                // 处理消息
                var model = JsonSerializer.DeserializeFromString<Sub_XsjhToHr>(msg);

                //是否处理过
                if (!MyRedis.Client.HSetNx($"{RedisKey.CheckExists.Key}:XsjhToHr", model.CustomerId.ToString(), 1))
                {
                    await Task.Delay(10);
                    continue;
                }

                using var _context = _contextFactory.CreateDbContext();
                using var _noahContext = _noahContextFactory.CreateDbContext();

                var opp = _noahContext.OpportUnity.Where(x => x.OpportunityId == model.OpportunityId)
                .Select(s => new
                {
                    s.CustomerId,
                    CustomerName = s.Customer.Name ?? string.Empty,
                    s.CreateBy,
                    CreateName = s.CreateByModel.Name,
                    s.CreateByModel.HrNo,
                    s.UpdateTime,
                    s.CreateTime
                }).FirstOrDefault();

                if (opp == null)
                {
                    await Task.Delay(10);
                    continue;
                }
                var hr = _context.User_DingDing.Where(x => x.DingJobNo == opp.HrNo)
                .Select(s => new
                {
                    s.DingJobNo,
                    s.UserId,
                    s.User_Hr.User.Mobile,
                    s.User_Hr.NickName,
                    s.User_Hr.EntWeChatQrCode
                }).FirstOrDefault();

                //如果企业是已分配线索，结束
                var ara = _context.User_Hr_RecommendEnt.Any(x => x.EntName == opp.CustomerName);
                if (ara)
                {
                    await Task.Delay(10);
                    continue;
                }

                //查询企业联系人
                var contacts = _noahContext.CustomerContact.Where(x => x.CustomerId == opp.CustomerId)
                .Select(s => new
                {
                    s.ContactId,
                    s.Name,
                    s.Phone,
                    s.Tel,
                    s.Tel2,
                    s.Position
                }).ToList();

                var cont = contacts.Where(x => !string.IsNullOrWhiteSpace(x.Name)
                && (x.Phone?.Trim().Length == 11 || x.Tel?.Trim().Length == 11 || x.Tel2?.Trim().Length == 11))
                .Select(s => new
                {
                    Mobile = s.Phone?.Trim().Length == 11 ? s.Phone.Trim() : (s.Tel?.Trim().Length == 11 ? s.Tel.Trim() : s.Tel2?.Trim()),
                    s.Name,
                    s.ContactId,
                    s.Position
                }).OrderByDescending(o => o.ContactId).FirstOrDefault();

                //没有联系人电话，结束
                if (cont == null || string.IsNullOrWhiteSpace(cont.Mobile) || !DataValid.IsMobile(cont.Mobile))
                {
                    await Task.Delay(10);
                    continue;
                }

                var huid = string.Empty;
                if (_hostingEnvironment.IsProduction())
                    huid = "208013953637774469";
                else
                    huid = "175974119808971525";

                var hrName = cont.Name ?? string.Empty;

                //企业同步到诺聘
                var rem = new SyncSalesEasyHRRequest
                {
                    AdviserId = hr?.UserId,
                    AdviserMobile = hr?.Mobile,
                    AdviserName = hr?.NickName,
                    EntName = opp.CustomerName,
                    HRMobile = cont.Mobile,
                    HRName = hrName,
                    EntWeChatQrCode = hr?.EntWeChatQrCode
                };
                var npEnts = await _nuoPinApi.SyncSalesEasyHR(rem);

                var es = new List<int?> { 1, 2, 4 };
                var npent = npEnts.FirstOrDefault(x => es.Contains(x.Status));
                if (npent != null)
                {
                    //销售易诺聘企业审核未通过
                    var stn = npent.Status switch
                    {
                        1 => "等待审核",
                        2 => "审核驳回",
                        3 => "禁用",
                        _ => "未知"
                    };

                    if (_hostingEnvironment.IsProduction())
                        MsgHelper.SendDingdingNotify(new MsgNotifyModel[] {
                        new MsgNotifyModel
                        {
                            Type = MsgNotifyType.销售易诺聘企业审核未通过,
                            EventTime = DateTime.Now,
                            UserId = huid,
                            Data = new MsgNotifyRecommendEntAlloc { HrName = hrName, HrMobile = cont.Mobile, EntName =  opp.CustomerName, AdviserNo = opp.HrNo, AdviserName = opp.CreateName, NpEnStatus = stn }
                        }
                    });

                    await Task.Delay(10);
                    continue;
                }
                else if (npEnts.Count > 0)
                {
                    await Task.Delay(10);
                    continue;
                }

                if (hr == null)
                {
                    //钉钉提醒管理员去找顾问绑定工号
                    if (_hostingEnvironment.IsProduction())
                        MsgHelper.SendDingdingNotify(new MsgNotifyModel[] {
                        new MsgNotifyModel
                        {
                            Type = MsgNotifyType.企业线索池顾问未绑定钉钉,
                            EventTime = DateTime.Now,
                            UserId = huid,
                            Data = new MsgNotifyRecommendEntAlloc { HrName = hrName, HrMobile = cont.Mobile, EntName =  opp.CustomerName, AdviserNo = opp.HrNo, AdviserName = opp.CreateName }
                        }
                    });
                }
                else
                {
                    //自动进入企业线索池
                    var re = new User_Hr_RecommendEnt
                    {
                        AdviserId = hr.UserId,
                        Source = HrRecommendEntSource.销售易,
                        TEntId = model.OpportunityId.ToString(),
                        EntName = opp.CustomerName,
                        HrName = hrName,
                        Mobile = cont.Mobile
                        // Remark = model.Remark,
                        // Post = model.Post ?? string.Empty,
                        // OutletId = model.OutletId,
                        // OutletName = model.OutletName
                    };
                    _context.Add(re);
                    _context.SaveChanges();

                    //提醒顾问去邀请企业开通诺聘
                    MsgHelper.SendDingdingNotify(new MsgNotifyModel[] {
                        new MsgNotifyModel
                        {
                            Type = MsgNotifyType.销售易企业线索池分配企业提醒,
                            EventTime = DateTime.Now,
                            UserId = hr.UserId,
                            Data = new MsgNotifyRecommendEntAlloc { HrName = hrName, HrMobile = cont.Mobile, EntName =  opp.CustomerName }
                        }
                    });
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.XsjhToHr}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 顾问自动协同项目
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task HrAutoTeamProject(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var today = DateTime.Today;
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.ZRevRange(SubscriptionKey.HrAutoTeam, 0, 1).FirstOrDefault();

                if (string.IsNullOrWhiteSpace(msg))
                {
                    await Task.Delay(5000);
                    continue;
                }

                //取出之后移除
                MyRedis.Client.ZRem(SubscriptionKey.HrAutoTeam, msg);

                using var _context = _contextFactory.CreateDbContext();

                if (!_context.User_Extend.Any(x => x.UserId == msg && x.HrAutoTeam == 1))
                {
                    await Task.Delay(50);
                    continue;
                }


                //var predicate = PredicateBuilder.New<Post>(x => x.Project.Status == ProjectStatus.已上线 && 
                //    x.Status == PostStatus.发布中 && x.LeftStock > 0);// 主创上线且协同

                
                //predicate = predicate.And(x => !_context.Post_Team
                //    .Any(a => a.Project_Team.HrId == msg &&
                //    a.Project_Team.ProjectId == x.ProjectId &&
                //    a.PostId == x.PostId));
                //_context.Post.Where(predicate);
                //没有权限的项目一定时间内不再重复判断
                //var llxms = MyRedis.Client.Get<List<string>>($"{RedisKey.Idempotency.HrAutoTeamProj}{msg}") ?? new List<string>();
                //var newllxms = new List<string>();

                //查询所有上线岗位
                var allPostIds = _context.Post.Where(x => x.Project.Status == ProjectStatus.已上线 &&
                    x.Status == PostStatus.发布中)
                .Select(s => new
                {
                    s.PostId,
                    s.ProjectId,
                }).ToList();

                //查询该顾问所有已协同岗位
                var hrTeamPostIds = _context.Post_Team.Where(x => x.Project_Team.HrId == msg)
                    .Select(s => s.PostId).ToList();

                foreach (var item in allPostIds)
                {
                    //如果已协同，跳过
                    if (hrTeamPostIds.Contains(item.PostId))
                        continue;

                    ////如果之前处理过并且没权限，跳过
                    //if (llxms.Contains(item.PostId))
                    //{
                    //    newllxms.Add(item.PostId);
                    //    continue;
                    //}

                    try
                    {
                        await _commonProjectService.PostSync(msg ?? string.Empty,item.ProjectId ?? string.Empty, item.PostId);
                    }
                    catch (CustomException)
                    {
                        //newllxms.Add(item.ProjectId!);
                        continue;
                    }
                    catch (Exception e)
                    {
                        _logger.Error($"{SubscriptionKey.HrAutoTeam}消息处理出错", Tools.GetErrMsg(e), msg ?? string.Empty);
                        await Task.Delay(500);
                        continue;
                    }

                    await Task.Delay(200);
                }

                //MyRedis.Client.Set($"{RedisKey.Idempotency.HrAutoTeamProj}{msg}", newllxms, TimeSpan.FromMinutes(5));
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.HrAutoTeam}消息处理出错", Tools.GetErrMsg(e), msg ?? string.Empty);
                await Task.Delay(1000);
            }

            await Task.Delay(50);
        }
    }
}