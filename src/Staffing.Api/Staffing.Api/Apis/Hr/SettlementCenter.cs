using Infrastructure.Extend;
using Staffing.Model.Hr.SettlementCenter;
using Staffing.Core.Interfaces.Hr;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using MiniExcelLibs;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class SettlementCenterRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("sc").WithTags("结算中心");

        group.MapPost("/order/income", (OrderIncomeRequest request, ISettlementCenterService rep) => rep.GetOrderIncomeList(request))
            .WithOpenApi(operation => new(operation) { Summary = "结算中心 - 订单收入列表" });

        group.MapPost("/order/income/export", (OrderIncomeRequest request, ISettlementCenterService rep) =>
        {
            request = request ?? new OrderIncomeRequest();
            request.PageSize = 10000; // 设置为最大值，导出全部数据
            var result = rep.GetOrderIncomeList(request);

            var memoryStream = new MemoryStream();
            memoryStream.SaveAs(result.Rows);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return Results.File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"快招工简历.xlsx");
        })
        .WithOpenApi(operation => new(operation) { Summary = "结算中心 - 订单收入列表导出" });

        group.MapPost("/order/expense", (OrderExpenseRequest request, ISettlementCenterService rep) => rep.GetOrderExpenseList(request))
            .WithOpenApi(operation => new(operation) { Summary = "结算中心 - 订单消费列表" });

        group.MapPost("/order/expense/export", (OrderExpenseRequest request, ISettlementCenterService rep) =>
        {
            request = request ?? new OrderExpenseRequest();
            request.PageSize = 10000; // 设置为最大值，导出全部数据
            var result = rep.GetOrderExpenseList(request);
            var memoryStream = new MemoryStream();
            memoryStream.SaveAs(result.Rows);
            memoryStream.Seek(0, SeekOrigin.Begin);
            return Results.File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"快招工简历.xlsx");
        })
        .WithOpenApi(operation => new(operation) { Summary = "结算中心 - 订单消费列表导出" });
    }
}
