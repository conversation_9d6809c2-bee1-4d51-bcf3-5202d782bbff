using AlibabaCloud.SDK.Dyplsapi20170525.Models;
using Config;
using Config.CommonModel.Aliyun;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using ServiceStack.Text;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Singleton)]
public class AliyunDyplsHelper
{
    private readonly LogManager _log;
    private readonly ConfigManager _config;
    private readonly AliyunClient _aliyunClient;
    private readonly static string PoolKey = "FC100000178344290";
    public AliyunDyplsHelper(LogManager log, IOptionsSnapshot<ConfigManager> config, AliyunClient aliyunClient)
    {
        _log = log;
        _config = config.Value;
        _aliyunClient = aliyunClient;
    }

    public async Task<BindAxbResult> BindAxb(BindAxb model)
    {
        ///TODO:临时增加逻辑给客服使用座机，后续需要优化
        if (model.PhoneNoA == "18032887650")
            model.PhoneNoA = "031166571203";
        else if (model.PhoneNoA == "18032200083")
            model.PhoneNoA = "031166571206";
        else if (model.PhoneNoA == "18831106877")
            model.PhoneNoA = "031185096169";
        else if (model.PhoneNoA == "13292878828")
            model.PhoneNoA = "031185090261";

        var exp = DateTime.Now.AddSeconds(model.ExpiredSeconds);
        var resp = await _aliyunClient.GetDyplsapiClient().BindAxbAsync(new BindAxbRequest
        {
            PoolKey = PoolKey,
            Expiration = exp.ToString("yyyy-MM-dd HH:mm:ss"),
            PhoneNoA = model.PhoneNoA,
            PhoneNoB = model.PhoneNoB
        });

        var data = resp?.Body?.SecretBindDTO;

        if (data == null)
        {
            _log.Error("隐私号码保护BindAxb错误", JsonSerializer.SerializeToString(resp), JsonSerializer.SerializeToString(model));
            throw new BadRequestException("暂无可用号码，请先尝试其他方式沟通");
        }

        var result = new BindAxbResult
        {
            Extension = data.Extension,
            SecretNo = data.SecretNo,
            SubsId = data.SubsId
        };

        //手机号绑定是双向，需要排序，否则会出现删除axb而导致bxa无法通信
        var phoneKey = $"{result.SecretNo}&{result.SubsId}";
        MyRedis.Client.ZAdd(RedisKey.PrivatePhoneCleanKey, exp.ToUnixTime(), phoneKey);

        return result;
    }

    public async Task UnbindSubscription(UnbindSubscription model)
    {
        var resp = await _aliyunClient.GetDyplsapiClient().UnbindSubscriptionAsync(new UnbindSubscriptionRequest
        {
            SubsId = model.SubsId,
            SecretNo = model.SecretNo
        });

        var data = resp.Body.RequestId;

        if (resp.Body.Code != "OK")
        {
            _log.Error("隐私号码保护UnbindSubscription错误", JsonSerializer.SerializeToString(resp), JsonSerializer.SerializeToString(model));
            throw new Exception("隐私号码保护UnbindSubscription错误");
        }
    }
}