namespace Config.CommonModel.ESign;

/// <summary>
/// 创建个人/机构图片印章
/// </summary>
public class CreateSeals
{
    /// <summary>
    /// 用户id
    /// </summary>
    public string? accountId { get; set; }

    /// <summary>
    /// 印章别名（别名不能重复）
    /// </summary>
    public string? alias { get; set; }

    /// <summary>
    /// 印章高度, 个人默认95px, 机构默认159px
    /// </summary>
    public int? height { get; set; }

    /// <summary>
    /// 印章宽度, 个人默认95px, 机构默认159px
    /// </summary>
    public int? width { get; set; }

    /// <summary>
    /// 印章数据类型，BASE64：base64格式
    /// </summary>
    public string type { get; set; } = "BASE64";

    /// <summary>
    /// 印章数据，目前只支持png格式，base64格式字符串，不包含格式前缀
    /// </summary>
    public string? data { get; set; }

    /// <summary>
    /// 是否对图片进行透明化处理，默认false。对于有背景颜色的图片，建议进行透明化处理，否则可能会遮挡文字
    /// </summary>
    public bool? transparentFlag { get; set; }
}

public class CreateSealsResponse
{
    /// <summary>
    /// 印章fileKey
    /// </summary>
    public string? fileKey { get; set; }

    /// <summary>
    /// 印章id
    /// </summary>
    public string? sealId { get; set; }

    /// <summary>
    /// 印章下载地址, 有效时间1小时
    /// </summary>
    public string? url { get; set; }

    /// <summary>
    /// 印章高度
    /// </summary>
    public int? height { get; set; }

    /// <summary>
    /// 印章宽度
    /// </summary>
    public int? width { get; set; }
}

/// <summary>
/// 删除机构印章
/// </summary>
public class DeleteSeals
{
    /// <summary>
    /// 机构id
    /// </summary>
    public string? orgId { get; set; }

    /// <summary>
    /// 印章id
    /// </summary>
    public string? sealId { get; set; }
}

/// <summary>
/// 设置静默签署授权
/// </summary>
public class SignAuth
{
    /// <summary>
    /// 个人/机构签署账号ID，通过创建个人/机构签署账号接口获取
    /// </summary>
    public string? accountId { get; set; }

    /// <summary>
    /// 授权截止时间, 格式为yyyy-MM-dd HH:mm:ss，默认无限期
    /// </summary>
    public string? deadline { get; set; }
}

/// <summary>
/// 查询机构印章
/// </summary>
public class GetSeals
{
    /// <summary>
    /// 机构id，该参数需放在请求地址里面，可以参考【请求示例】
    /// </summary>
    public string? orgId { get; set; }

    /// <summary>
    /// 分页起始位置
    /// </summary>
    public int offset { get; set; }

    /// <summary>
    /// 单页数量
    /// </summary>
    public int size { get; set; }
}

public class GetSealsResponse
{
    /// <summary>
    /// 印章列表
    /// </summary>
    public List<GetSealsInfo> seals { get; set; } = new List<GetSealsInfo>();

    /// <summary>
    /// 查询总数
    /// </summary>
    public int total { get; set; }
}

public class GetSealsInfo
{
    /// <summary>
    /// 印章id
    /// </summary>
    public string? sealId { get; set; }

    /// <summary>
    /// 印章类型
    /// （1）1-机构模板章
    /// （2）2-个人模板章
    /// （3）3-自定义印章
    /// </summary>
    public int sealType { get; set; }

    /// <summary>
    /// 印章别名
    /// </summary>
    public string? alias { get; set; }

    /// <summary>
    /// 印章创建时间
    /// </summary>
    public long createDate { get; set; }

    /// <summary>
    /// 默认印章标识
    /// </summary>
    public bool defaultFlag { get; set; }

    /// <summary>
    /// 印章fileKey
    /// </summary>
    public string? fileKey { get; set; }

    /// <summary>
    /// 印章高度
    /// </summary>
    public int height { get; set; }

    /// <summary>
    /// 印章宽度
    /// </summary>
    public int width { get; set; }

    /// <summary>
    /// 印章下载地址, 有效时间1小时
    /// </summary>
    public string? url { get; set; }

    /// <summary>
    /// 印章业务类型COMMON-其它
    /// </summary>
    public string? sealBizType { get; set; }
}