﻿using Config.Enums;

namespace Config.CommonModel.TalentResume;

/// <summary>
/// 教育经历
/// </summary>
public class Edus
{
    /// <summary>
    /// 学校名称
    /// </summary>
    public string SchoolName { get; set; } = default!;

    /// <summary>
    /// 是否全日制
    /// </summary>
    public bool IsFullTime { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public TalentVirtualEducation? Education { get; set; }

    /// <summary>
    /// 学历描述
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 专业名称
    /// </summary>
    public string? MajorName { get; set; }

    /// <summary>
    /// 教育开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 教育结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 学校注释
    /// </summary>
    public string? SchoolRemarks { get; set; }
}

/// <summary>
/// 求职期望
/// </summary>
public class Hopes
{
    /// <summary>
    /// 期望城市
    /// </summary>
    public string? HopeCity { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 职位类别
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// 非诺快聘职位类别名称 - 1级
    /// </summary>
    public string? PositionLevel1Name { get; set; }

    /// <summary>
    /// 非诺快聘职位类别名称 - 2级
    /// </summary>
    public string? PositionLevel2Name { get; set; }

    /// <summary>
    /// 非诺快聘职位类别名称 - 3级
    /// </summary>
    public string? PositionLevel3Name { get; set; }

    /// <summary>
    /// 所在行业
    /// </summary>
    public string? Industry { get; set; }

    /// <summary>
    /// 非诺快聘所在行业名称 - 1级
    /// </summary>
    public string? IndustryLevel1Name { get; set; }

    /// <summary>
    /// 非诺快聘所在行业名称 - 2级
    /// </summary>
    public string? IndustryLevel2Name { get; set; }

    /// <summary>
    /// 最低薪资
    /// </summary>
    public decimal MinSalary { get; set; } = 0;

    /// <summary>
    /// 最高薪资
    /// </summary>
    public decimal MaxSalary { get; set; } = 0;
}

/// <summary>
/// 求职期望 - 数据库
/// </summary>
public class NHopes
{
    /// <summary>
    /// 期望城市
    /// </summary>
    public string? HopeCity { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 职位类别 1级别
    /// </summary>
    public string? CategoryLevel1 { get; set; }

    /// <summary>
    /// 职位类别 2级别
    /// </summary>
    public string? CategoryLevel2 { get; set; }

    /// <summary>
    /// 职位类别 3级别
    /// </summary>
    public string? CategoryLevel3 { get; set; }

    /// <summary>
    /// 职位类别名称 - 1级
    /// </summary>
    public string? CategoryLevel1Name { get; set; }

    /// <summary>
    /// 职位类别名称 - 2级
    /// </summary>
    public string? CategoryLevel2Name { get; set; }

    /// <summary>
    /// 职位类别名称 - 3级
    /// </summary>
    public string? CategoryLevel3Name { get; set; }

    /// <summary>
    /// 非诺快聘职位类别名称 - 1级
    /// </summary>
    public string? PositionLevel1Name { get; set; }

    /// <summary>
    /// 非诺快聘职位类别名称 - 2级
    /// </summary>
    public string? PositionLevel2Name { get; set; }

    /// <summary>
    /// 非诺快聘职位类别名称 - 3级
    /// </summary>
    public string? PositionLevel3Name { get; set; }

    /// <summary>
    /// 所在行业
    /// </summary>
    public string? Industry { get; set; }

    /// <summary>
    /// 非诺快聘所在行业名称 - 1级
    /// </summary>
    public string? IndustryLevel1Name { get; set; }

    /// <summary>
    /// 非诺快聘所在行业名称 - 2级
    /// </summary>
    public string? IndustryLevel2Name { get; set; }

    /// <summary>
    /// 最低薪资
    /// </summary>
    public decimal MinSalary { get; set; } = 0;

    /// <summary>
    /// 最高薪资
    /// </summary>
    public decimal MaxSalary { get; set; } = 0;
}

/// <summary>
/// 项目经历
/// </summary>
public class Projects
{
    /// <summary>
    /// 项目名称
    /// </summary>
    public string ProjectName { get; set; } = string.Empty;

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string PostName { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 项目描述
    /// </summary>
    public string? ProjectRemarks { get; set; }
}

/// <summary>
/// 工作经历
/// </summary>
public class Works
{
    /// <summary>
    /// 公司名称
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;

    /// <summary>
    /// 公司部门
    /// </summary>
    public string? Department { get; set; }

    /// <summary>
    /// 行业名称
    /// </summary>
    public string? IndustryName { get; set; }

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 最低薪资
    /// </summary>
    public decimal MinSalary { get; set; } = 0;

    /// <summary>
    /// 最高薪资
    /// </summary>
    public decimal MaxSalary { get; set; } = 0;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 工作描述(注意，不是公司描述，程序员写错了)
    /// </summary>
    public string? CompanyRemarks { get; set; }
}

/// <summary>
/// 技能分析（详情子表）
/// </summary>
public class TalentResumeSkillSub
{
    /// <summary>
    /// 专业技能集合
    /// </summary>
    public List<string>? Professional { get; set; }

    /// <summary>
    /// IT技能集合
    /// </summary>
    public List<string>? IT { get; set; }

    /// <summary>
    /// 商业技能集合
    /// </summary>
    public List<string>? Business { get; set; }
}

/// <summary>
/// 亮点
/// </summary>
public class TalentResumeHighlights
{
    /// <summary>
    /// 工作经历亮点
    /// </summary>
    public List<string>? Occupation { get; set; }

    /// <summary>
    /// 学习经历亮点
    /// </summary>
    public List<string>? Education { get; set; }

    /// <summary>
    /// 项目经历亮点
    /// </summary>
    public List<string>? Project { get; set; }

    /// <summary>
    /// 其他亮点
    /// </summary>
    public List<string>? Others { get; set; }

    /// <summary>
    /// 亮点标签
    /// </summary>
    public List<string>? Tags { get; set; }
}

/// <summary>
/// 风险
/// </summary>
public class TalentResumeRisks
{
    /// <summary>
    /// 工作经历风险
    /// </summary>
    public List<string>? Occupation { get; set; }

    /// <summary>
    /// 学习经历风险点
    /// </summary>
    public List<string>? Education { get; set; }

    /// <summary>
    /// 风险点标签
    /// </summary>
    public List<string>? Tags { get; set; }
}

/// <summary>
/// 荣誉奖项
/// </summary>
public class Honours
{
    /// <summary>
    /// 荣誉名称
    /// </summary>
    public string? HonourName { get; set; }

    /// <summary>
    /// 颁发时间
    /// </summary>
    public DateTime? HonourTime { get; set; }

    /// <summary>
    /// 颁发机构
    /// </summary>
    public string? HonourCompany { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Remarks { get; set; }
}

public class TalentResumeRequestModel
{
    /// <summary>
    /// 用户来源
    /// </summary>
    public TalentResumeSource Source { get; set; } = TalentResumeSource.未知;

    /// <summary>
    /// 简历状态
    /// </summary>
    public TalentResumeStatus Status { get; set; } = TalentResumeStatus.UnRegistered;

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 手机
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 头像
    /// </summary>
    public string? HeadPortrait { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 最高学历
    /// </summary>
    public TalentVirtualEducation? Education { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Mailbox { get; set; }

    /// <summary>
    /// 微信
    /// </summary>
    public string? WeChat { get; set; }

    /// <summary>
    /// QQ
    /// </summary>
    public string? QQ { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateTime? Birthday { get; set; }

    /// <summary>
    /// 开始工作日期
    /// </summary>
    public DateTime? WorkTime { get; set; }

    /// <summary>
    /// 自我评价
    /// </summary>
    public string? SelfEvaluation { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public List<Edus>? Edus { get; set; }

    /// <summary>
    /// 求职期望
    /// </summary>
    public List<Hopes>? Hopes { get; set; }

    /// <summary>
    /// 项目经历
    /// </summary>
    public List<Projects>? Projects { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    public List<Works>? Works { get; set; }

    /// <summary>
    /// 作品集
    /// </summary>
    public List<string>? Products { get; set; }

    /// <summary>
    /// 荣誉奖项
    /// </summary>
    public List<Honours>? Honours { get; set; }

    /// <summary>
    /// 证书
    /// </summary>
    public List<string>? Certificates { get; set; }

    /// <summary>
    /// 技能分析
    /// </summary>
    public TalentResumeSkillSub? SkillAnalysis { get; set; }

    /// <summary>
    /// 行业标签（来源小析职业标签字段）
    /// </summary>
    public List<string>? IndustryLabel { get; set; }

    /// <summary>
    /// 职位标签（来源小析职业标签字段）
    /// </summary>
    public List<string>? PostLabel { get; set; }

    /// <summary>
    /// 自定义标签
    /// </summary>
    public List<string>? OtherLabel { get; set; }

    /// <summary>
    /// 通用标签
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 亮点
    /// </summary>
    public TalentResumeHighlights? Highlights { get; set; }

    /// <summary>
    /// 风险
    /// </summary>
    public TalentResumeRisks? Risks { get; set; }

    /// <summary>
    /// 原始简历地址 - 附件简历
    /// </summary>
    public string? OriginalUrl { get; set; }

    /// <summary>
    /// 所在城市
    /// </summary>
    public string? City { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 最后一次上线时间
    /// </summary>
    public DateTime? LastLoginTime { get; set; }

    /// <summary>
    /// 首次注册时间
    /// </summary>
    public DateTime? RegistedTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 扩展数据
    /// </summary>
    public string? ExtendedData { get; set; }

    /// <summary>
    /// 哪儿些顾问可以查看，为空则所有顾问可查看
    /// </summary>
    public List<string>? HrIds { get; set; }

    /// <summary>
    /// 哪儿些部门可以查看，为空则所有部门可查看
    /// </summary>
    public List<string>? DeptIds { get; set; }

}

public class TalentResumeRequestModelExtends : TalentResumeRequestModel
{
    /// <summary>
    /// 求职者ID
    /// </summary>
    public string? SeekerId { get; set; }

    // public ResumeSyncType ResumeSyncType { get; set; } = ResumeSyncType.简历变更;
}

public enum ResumeSyncType
{
    简历变更, 登录
}

public enum ConnectType
{
    拨打电话,
    短信邀约,
    在线访问,
    隐藏简历,
    查看简历 = 99
}

/// <summary>
/// 人才融合操作记录
/// </summary>
public class TalentResumeRecordRedis
{
    /// <summary>
    /// 操作人
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 人才库Id
    /// </summary>
    public string ResumeId { get; set; } = default!;

    /// <summary>
    /// 推荐职位
    /// </summary>
    public string? TeamPostId { get; set; }

    public DateTime? CreatedTime { get; set; }

    /// <summary>
    /// 操作类型
    /// </summary>
    public ConnectType ConnectType { get; set; } = default!;
}

public class TalentResumeHideRecord
{
    /// <summary>
    /// 隐藏类型
    /// </summary>
    public HideType HideType { get; set; } = HideType.呼叫;

    /// <summary>
    /// 人才库Id
    /// </summary>
    public string ResumeId { get; set; } = default!;

    /// <summary>
    /// 隐藏开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.Now;

    public string? status;
}

public enum HideType
{
    呼叫,投递简历
}

public class NykTalentUpdateLoginTime
{
    /// <summary>
    /// 登录时间
    /// </summary>
    public DateTime? LoginTime { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Mobile { get; set; } = default!;
}

public class NykTalentResume
{
    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 手机
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 性别
    /// </summary>
    public string? Sex { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public string? Education { get; set; }

    /// <summary>
    /// 所在城市
    /// </summary>
    public string? City { get; set; }

    // /// <summary>
    // /// 地区Id（国标编码，4位或6位均可）
    // /// </summary>
    // public string? RegionId { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateTime? Birthday { get; set; }

    // /// <summary>
    // /// 工作时间
    // /// </summary>
    // public DateTime? WorkTime { get; set; }

    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime? RegistedTime { get; set; }

    /// <summary>
    /// 最近登录时间
    /// </summary>
    public DateTime? LastLoginTime { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public List<NykLableValueModel>? Edus { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    public List<NykLableValueModel>? Works { get; set; }

    /// <summary>
    /// 报名项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 报名职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 报名时间
    /// </summary>
    public DateTime? PostTime { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 企业账号
    /// </summary>
    public string? CompanyAccount { get; set; }

    // /// <summary>
    // /// 更新时间戳（根据该时间戳判断是否更新）
    // /// </summary>
    // public long UpdatedTime { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public string? ProjectID { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public string? UserID { get; set; }

    /// <summary>
    /// 报名ID
    /// </summary>
    public string? ApplyID { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdCard { get; set; }
}

public class NykLableValueModel
{
    public string? Label { get; set; }

    public string? Value { get; set; }

}

public class NykTalentResumeEdu
{
    /// <summary>
    /// 学校名称
    /// </summary>
    public string SchoolName { get; set; } = default!;

    /// <summary>
    /// 学历
    /// </summary>
    public TalentVirtualEducation? Education { get; set; }

    /// <summary>
    /// 专业名称
    /// </summary>
    public string? MajorName { get; set; }

    /// <summary>
    /// 教育开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 教育结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

//增加以下属性：
public class NykTalentResumeWork
{
    /// <summary>
    /// 公司名称
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;

    /// <summary>
    /// 公司描述
    /// </summary>
    public string? CompanyRemarks { get; set; }

    /// <summary>
    /// 部门名称
    /// </summary>
    public string? Department { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 工作描述
    /// </summary>
    public string? WorkRemarks { get; set; }
}

public class NykExtendsModel
{
    /// <summary>
    /// 报名项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 报名职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 报名时间
    /// </summary>
    public DateTime? PostTime { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 企业账号
    /// </summary>
    public string? CompanyAccount { get; set; }

    // /// <summary>
    // /// 更新时间戳（根据该时间戳判断是否更新）
    // /// </summary>
    // public long UpdatedTime { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public string? ProjectID { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public string? UserID { get; set; }

    /// <summary>
    /// 报名ID
    /// </summary>
    public string? ApplyID { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdCard { get; set; }
}