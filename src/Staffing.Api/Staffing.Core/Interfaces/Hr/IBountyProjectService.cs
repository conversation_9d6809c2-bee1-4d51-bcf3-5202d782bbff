﻿using Config.CommonModel;
using Staffing.Model.Hr.Bounty;

namespace Staffing.Core.Interfaces.Hr;

public interface IBountyProjectService
{
    /// <summary>
    /// 项目管理首页汇总字段
    /// </summary>
    /// <returns></returns>
    BountyProjectSumData GetProjectSumData();

    /// <summary>
    /// 数字诺亚项目详情简要信息
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetProjectSummaryResponse GetProjectSummary(GetProjectRequest model);

    /// <summary>
    /// 数字诺亚项目详情
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<GetProjectInfo> GetProject(GetProjectRequest model);

    /// <summary>
    /// 数字诺亚项目详情-流程通知
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    FlowInfoResponse GetFlowInfo(GetFlowInfo model);

    /// <summary>
    /// 开票概览
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<InvoiceOverviewResponse> GetInvoiceOverview(GetProjectRequest model);

    /// <summary>
    /// 开发票
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> SaveInvoice(SaveInvoice model);

    /// <summary>
    /// 更新发票抬头
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> UpdateBook(UpdateBook model);

    /// <summary>
    /// 获取发票列表
    /// </summary>
    /// <returns></returns>
    InvoiceListResponse GetInvoiceList(GetInvoiceList model);

    /// <summary>
    /// 转账报销列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    TransferListResponse GetTransferList(GetTransferList model);

    /// <summary>
    /// 申请转账
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> SaveTransfer(SaveTransfer model);

    /// <summary>
    /// 服务奖金批次列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetServiceBonusBatchResponse GetServiceBonusBatch(GetServiceBonusBatch model);

    /// <summary>
    /// 服务奖金列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    ServiceBonusListResponse GetServiceBonusList(GetServiceBonusList model);

    /// <summary>
    /// 发放服务奖金
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<EmptyResponse> SaveServiceBonus(SaveServiceBonus model);

    /// <summary>
    /// 待办列表
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    TodoListResponse GetTodoList(TodoListReqeust request);

    /// <summary>
    /// 本月成单排行榜
    /// </summary>
    /// <returns></returns>
    PersonRankResponse GetRankList();

    /// <summary>
    /// 获取项目明细-创建
    /// </summary>
    /// <param name="projectId"></param>
    /// <returns></returns>
    ProjectDetail GetProjectDetail(string projectId);

    /// <summary>
    /// 数字诺亚项目列表
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    ProjectListResponse GetProjectList(ProjectListRequest request);

    /// <summary>
    /// 创建项目 - 提交数字诺亚
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    EmptyResponse SaveProject(ProjectSaveRequest request);
}