﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Staffing.Entity.Staffing
{
    /// <summary>
    /// 企业微信中的员工信息
    /// **用于获取群主的手机号码，因顾问与群主是根据手机号关联的，
    /// 当根据手机号无法关联顾问时，怀疑应该是群主手机号已修改为新的了，
    /// 需再次请求企业微信获取群主详情，若手机号已修改，则更新此表
    /// 此表仅在发布时手动同步下全量数据，之后仅在匹配不上时逐条更新
    /// </summary>
    [Table("qywechat_employee")]
    public class QYWechat_Employee
    {
        /// <summary>
        /// 员工用户ID
        /// </summary>
        [Key]
        public required string UserId { get; set; }

        /// <summary>
        /// 成员名称
        /// </summary>
        public required string Name { get; set; }

        /// <summary>
        /// 手机号码
        /// </summary>
        public required string Mobile { get; set; }

        /// <summary>
        /// 企微二维码URL
        /// </summary>
        public string? QRCodeURL { get; set; }

        /// <summary>
        /// 性别。0表示未定义，1表示男性，2表示女性
        /// </summary>
        public int Gender { get; set; }

        /// <summary>
        /// 激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 全局唯一ID。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的
        /// </summary>
        public string? Open_userid { get; set; }

        /// <summary>
        /// 成员所属部门id列表，仅返回该应用有查看权限的部门id
        /// </summary>
        public string? DepartmentIds { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }
    }
}
