﻿using System.Collections.Generic;

namespace Infrastructure.QYWechat.Model
{
    /// <summary>
    /// 获取成员ID列表
    /// </summary>
    public class Response_EmployeeListId : ResponseBase_Page
    {
        /// <summary>
        /// 用户-部门关系列表
        /// </summary>
        public List<EmployeeListId_DeptUser>? Dept_User { get; set; }
    }
    /// <summary>
    /// 根据部门ID获取成员列表
    /// </summary>
    public class Response_EmployeeByDepartment : ResponseBase_Page
    {
        /// <summary>
        /// 成员列表
        /// </summary>
        public List<Response_EmployeeInfo>? UserList { get; set; }
    }
    /// <summary>
    /// 用户-部门关系列表
    /// </summary>
    public class EmployeeListId_DeptUser
    {
        /// <summary>
        /// 用户userid，当用户在多个部门下时会有多条记录
        /// </summary>
        public string? Userid { get; set; }

        /// <summary>
        /// 用户所属部门
        /// </summary>
        public int Department { get; set; }
    }

    /// <summary>
    /// 获取成员ID列表
    /// </summary>
    public class Response_EmployeeInfo : ResponseBase
    {
        /// <summary>
        /// 成员UserID。对应管理端的账号，企业内必须唯一。不区分大小写，长度为1~64个字节；
        /// </summary>
        public string? Userid { get; set; }
        /// <summary>
        /// 成员名称
        /// </summary>
        public string? Name { get; set; }
        /// <summary>
        /// 手机号码
        /// </summary>
        public string? Mobile { get; set; }
        /// <summary>
        /// 成员所属部门id列表，仅返回该应用有查看权限的部门id
        /// </summary>
        public List<int>? Department { get; set; }
        /// <summary>
        /// 部门内的排序值，默认为0。数量必须和department一致，数值越大排序越前面。值范围是[0, 2^32)
        /// </summary>
        public List<int>? Order { get; set; }
        /// <summary>
        /// 职务信息
        /// </summary>
        public string? Position { get; set; }
        /// <summary>
        /// 性别。0表示未定义，1表示男性，2表示女性
        /// </summary>
        public int Gender { get; set; }
        /// <summary>
        /// 邮箱
        /// </summary>
        public string? Email { get; set; }
        /// <summary>
        /// 企业邮箱
        /// </summary>
        public string? Biz_Mail { get; set; }
        /// <summary>
        /// 表示在所在的部门内是否为部门负责人，数量与department一致
        /// </summary>
        public List<int>? Is_Leader_In_Dept { get; set; }
        /// <summary>
        /// 直属上级UserID，返回在应用可见范围内的直属上级列表，最多有1个直属上级
        /// </summary>
        public List<string>? Direct_Leader { get; set; }
        /// <summary>
        /// 头像url
        /// </summary>
        public string? Avatar { get; set; }
        /// <summary>
        /// 头像缩略图url
        /// </summary>
        public string? Thumb_Avatar { get; set; }
        /// <summary>
        /// 座机
        /// </summary>
        public string? Telephone { get; set; }
        /// <summary>
        /// 别名
        /// </summary>
        public string? Alias { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        public string? Address { get; set; }
        /// <summary>
        /// 全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的，最多64个字节。仅第三方应用可获取。
        /// </summary>
        public string? Open_Userid { get; set; }
        /// <summary>
        /// 主部门，仅当应用对主部门有查看权限时返回。
        /// </summary>
        public int Main_Department { get; set; }
        ///// <summary>
        ///// 扩展属性
        ///// </summary>
        //public ExtAttr? Extattr { get; set; }
        /// <summary>
        /// 激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业。
        /// 已激活代表已激活企业微信或已关注微信插件（原企业号）。未激活代表既未激活企业微信又未关注微信插件（原企业号）。
        /// </summary>
        public EmployeeStatus Status { get; set; }
        /// <summary>
        /// 员工个人二维码，扫描可添加为外部联系人(注意返回的是一个url，可在浏览器上打开该url以展示二维码)
        /// </summary>
        public string? Qr_Code { get; set; }
        /// <summary>
        /// 对外职务，如果设置了该值，则以此作为对外展示的职务，否则以position来展示
        /// </summary>
        public string? External_Position { get; set; }
        ///// <summary>
        ///// 成员对外属性
        ///// </summary>
        //public ExternalProfile? External_Profile { get; set; }
    }

    /// <summary>
    /// 根据手机号获取成员ID
    /// </summary>
    public class Response_EmployeeUserId : ResponseBase
    {
        /// <summary>
        /// 成员UserID。对应管理端的账号，企业内必须唯一。不区分大小写，长度为1~64个字节；
        /// </summary>
        public string? Userid { get; set; }
    }
    //public class ExtAttr
    //{
    //    public List<Attribute>? Attrs { get; set; }
    //}
    //public class Attribute
    //{
    //    public int Type { get; set; }
    //    public string? Name { get; set; }
    //    public Text? Text { get; set; }
    //    public Web? Web { get; set; }
    //    public Miniprogram? Miniprogram { get; set; }
    //}
    //public class Text
    //{
    //    public string? Value { get; set; }
    //}
    //public class Web
    //{
    //    public string? Url { get; set; }
    //    public string? Title { get; set; }
    //}
    //public class Miniprogram
    //{
    //    public string? Appid { get; set; }
    //    public string? Pagepath { get; set; }
    //    public string? Title { get; set; }
    //}
    //public class ExternalProfile
    //{
    //    public string? External_Corp_Name { get; set; }
    //    public WechatChannels? Wechat_Channels { get; set; }
    //    public List<Attribute>? External_Attr { get; set; } // Note: Reusing the Attribute class for simplicity
    //}
    //public class WechatChannels
    //{
    //    public string? Nickname { get; set; }
    //    public int Status { get; set; }
    //}

    public enum EmployeeStatus
    {
        已激活 = 1,
        已禁用 = 2,
        未激活 = 4,
        退出企业 = 5
    }
}
