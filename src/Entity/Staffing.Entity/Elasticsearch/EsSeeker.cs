﻿using Config.Enums;
using Nest;

namespace Staffing.Entity.Elasticsearch;

[ElasticsearchType(IdProperty = "UserId")]
public class EsSeeker
{
    [Keyword]
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 登录时间
    /// </summary>
    [Date]
    public DateTime LoginTime { get; set; }

    /// <summary>
    /// 求职者电话
    /// </summary>
    [Wildcard]
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 求职者身份证
    /// </summary>
    [Text(Index = false)]
    public string IdentityCard { get; set; } = default!;

    /// <summary>
    /// 求职者身份证名称
    /// </summary>
    [Keyword]
    public string IdentityCardName { get; set; } = default!;

    /// <summary>
    /// 求职者姓名
    /// </summary>
    [Wildcard]
    public string NickName { get; set; } = default!;

    /// <summary>
    /// 求职者头像
    /// </summary>
    [Text(Index = false)]
    public string Avatar { get; set; } = default!;

    /// <summary>
    /// 注册源
    /// </summary>
    [Keyword]
    public RegisterSource Source { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Keyword]
    public UserStatus Status { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    [Wildcard]
    public string? RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 职业
    /// </summary>
    [Keyword]
    public OccupationType? Occupation { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [Keyword]
    public Sex? Sex { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    [Keyword]
    public EducationType? Education { get; set; }

    /// <summary>
    /// 学校
    /// </summary>
    [Wildcard]
    public string? School { get; set; }

    /// <summary>
    /// 毕业日期
    /// </summary>
    [Date]
    public DateTime? GraduationDate { get; set; }

    /// <summary>
    /// 专业
    /// </summary>
    [Wildcard]
    public string? Major { get; set; }

    /// <summary>
    /// hr小程序简历二维码
    /// </summary>
    [Text(Index = false)]
    public string? HrAppletQrCode { get; set; }

    /// <summary>
    /// 简历完善度
    /// </summary>
    [Keyword]
    public int ResumeScore { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    [Date]
    public DateTime? Birthday { get; set; }

    /// <summary>
    /// 显示
    /// </summary>
    [Keyword]
    public bool ResumeShow { get; set; }

    /// <summary>
    /// 匿名（暂不用）
    /// </summary>
    [Keyword]
    public bool ResumeAnonymous { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [Date]
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    [Date]
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 他的顾问
    /// </summary>
    [Nested]
    public List<EsSeekerHr> Hrs { get; set; } = new List<EsSeekerHr>();

    /// <summary>
    /// 他的工作经历
    /// </summary>
    [Nested]
    public List<EsSeekerWork> Works { get; set; } = new List<EsSeekerWork>();
}

public class EsSeekerHr
{
    /// <summary>
    /// 人才库Id
    /// </summary>
    [Keyword]
    public string Id { get; set; } = default!;

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 来源
    /// </summary>
    [Keyword]
    public HrProjectSource Source { get; set; }

    /// <summary>
    /// 用户等级
    /// </summary>
    [Keyword]
    public TalentPlatformLevel Level { get; set; }

    /// <summary>
    /// 访问时间
    /// </summary>
    [Date]
    public DateTime SeekerVisitTime { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Keyword]
    public ActiveStatus Status { get; set; }

    /// <summary>
    /// 删除
    /// </summary>
    [Keyword]
    public bool Deleted { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Date]
    public DateTime CreatedTime { get; set; }
}