﻿using Flurl;
using Flurl.Http;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using ServiceStack.Text;
using Noah.Aliyun.Storage;
using Config;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Hosting;
using Staffing.Entity;
using Config.CommonModel.KuaiShou;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Transient)]
public class KuaiShouHelper
{
    private readonly IObjectStorage _objectStorage;
    private readonly ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly StaffingContext _context;
    public KuaiShouHelper(IObjectStorage objectStorage, IOptionsSnapshot<ConfigManager> config,
    IHostEnvironment hostingEnvironment, LogManager log, StaffingContext context)
    {
        _objectStorage = objectStorage;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
        _context = context;
    }

    /// <summary>
    /// 获取用户快手小程序手机
    /// </summary>
    /// <param name="appID"></param>
    /// <param name="appSecret"></param>
    /// <param name="code"></param>
    /// <param name="encryptedData"></param>
    /// <param name="iv"></param>
    /// <returns></returns>
    public async Task<GetKsAppletPhoneResponse> GetAppletPhone(string appID, string appSecret, string code, string encryptedData, string iv)
    {
        var code2SessionUrl = "https://open.kuaishou.com/oauth2/mp/code2session";
        var tokenResult = await code2SessionUrl.PostUrlEncodedAsync(new
        {
            js_code = code,
            app_id = ClientApps.KsAppletSeeker.AppID,
            app_secret = ClientApps.KsAppletSeeker.AppSecret
        }).ReceiveJson<GetKsAppletPhoneResponse>();

        if (tokenResult?.result != 1)
            throw new Exception($"快手获取session_key错误:{JsonSerializer.SerializeToString(tokenResult)}");

        var ed = Senparc.Weixin.WxOpen.Helpers.EncryptHelper.DecodeEncryptedData(tokenResult.session_key, encryptedData, iv);
        var phoneInfo = JsonSerializer.DeserializeFromString<GetKsAppletPhoneResponse>(ed);

        tokenResult.phoneNumber = phoneInfo.phoneNumber;

        return tokenResult;
    }

    /// <summary>
    /// 获取快手token
    /// </summary>
    /// <returns></returns>
    public async Task<KuaiShouResult> GetToken()
    {
        var cacheKey = $"token:kuaishou";

        var result = MyRedis.Client.Get<KuaiShouResult?>(cacheKey);

        if (!string.IsNullOrWhiteSpace(result?.accessToken))
            return result;

        string url = $"{Constants.KuaiShouApiDomain}/token/get";

        try
        {
            var kstoken = await url
            .SetQueryParams(new
            {
                appKey = Constants.KuaiShouAppKey,
                secretKey = Constants.KuaiShouSecretKey,
                grantType = "client_credentials"
            }).GetAsync().ReceiveJson<GetKzgResponse<KuaiShouResult>>();

            result = kstoken?.result;

            if (string.IsNullOrWhiteSpace(result?.accessToken))
                throw new Exception("获取快手token失败");

            MyRedis.Client.Set(cacheKey, result, result.expireTime / 2);

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    // /// <summary>
    /// // /// 获取快手 accessToken
    // /// </summary>
    // /// <returns></returns>
    // /// <exception cref="Exception"></exception>
    // public async Task<GetKuaiShouLoginResponse> GetKuaiShouLoginToken()
    // {
    //     var result = MyRedis.Client.Get<GetKuaiShouLoginResponse>(KuaiShou.KuaiShouLonginToken);
    //     if (!string.IsNullOrWhiteSpace(result?.result?.accessToken))
    //     {
    //         return result;
    //     }

    //     try
    //     {
    //         var url = $"{Constants.KuaiShouApiDomain}/token/get";
    //         var tokenResult = await url
    //             .SetQueryParams(new
    //             {
    //                 appKey = Constants.KuaiShouAppKey,
    //                 secretKey = Constants.KuaiShouSecretKey,
    //                 grantType = "client_credentials"
    //             })
    //             .GetAsync().ReceiveJson<GetKuaiShouLoginResponse>();

    //         if (tokenResult?.code != 0)
    //             throw new Exception($"快手获取登录accessToken错误:{JsonSerializer.SerializeToString(tokenResult)}");

    //         if (tokenResult.result != null)
    //             MyRedis.Client.Set(KuaiShou.KuaiShouLonginToken, tokenResult, tokenResult.result.expireTime / 2);

    //         return tokenResult;
    //     }
    //     catch (FlurlHttpException ex)
    //     {
    //         var error = await ex.GetResponseStringAsync();
    //         throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
    //     }
    // }


    //    /// <summary>
    //    /// 刷新 token
    //    /// </summary>
    //    /// <param name="refreshToken"></param>
    //    /// <returns></returns>
    //    /// <exception cref="Exception"></exception>
    //    public async Task<GetKuaiShouLoginResponse> GetKuaiShouRefreshToken(string refreshToken)
    //    {
    //        var url = $"{Constants.KuaiShouApiDomain}/token/refresh";
    //        var tokenResult = await url
    //            .SetQueryParams(new
    //            {
    //                appKey = Constants.KuaiShouAppKey, 
    //                secretKey = Constants.KuaiShouSecretKey,
    //                refreshToken = refreshToken,
    //                grantType = "refresh_token"
    //            })
    //            .GetAsync().ReceiveJson<GetKuaiShouLoginResponse>();

    //        if (tokenResult?.code != 0)
    //            throw new Exception($"快手获取session_key错误:{JsonSerializer.SerializeToString(tokenResult)}");

    //        return tokenResult;
    //    }

    /// <summary>
    /// 租户绑定url
    /// </summary>
    /// <returns></returns>
    public string GetBindTenantUrl(string state)
    {
        var url = $"{Constants.KuaiShouApiDomain}/#/oauth/bind/tenant/login";
        var backUrl = $"{_config.DataRdsServer}/staffing/thirdabut/KuaiShouAbut/tenantCallBack";

        return $"{url}?appKey={Constants.KuaiShouAppKey}&state={state}&callbackUrl={backUrl}";
    }

    /// <summary>
    /// 租户解除绑定
    /// </summary>
    /// <param name="callbackUrl"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<GetKzgResponse<KuaiShouResult>> KuaiShouUnBindTenant(string openTenantId)
    {
        var url = $"{Constants.KuaiShouApiDomain}/ats/openapi/v1/tenant/unbind";
        var tokenResult = await url.PostUrlEncodedAsync(new
        {
            openTenantId = openTenantId
        }).ReceiveJson<GetKzgResponse<KuaiShouResult>>();

        if (tokenResult?.code != 0)
            throw new Exception($"快手获取session_key错误:{JsonSerializer.SerializeToString(tokenResult)}");

        return tokenResult;
    }

    /// <summary>
    /// 用户绑定url
    /// </summary>
    /// <returns></returns>
    public string GetBindUserUrl(string state)
    {
        var url = $"{Constants.KuaiShouApiDomain}/#/oauth/bind/user/login";
        var backUrl = $"{_config.DataRdsServer}/staffing/thirdabut/KuaiShouAbut/userCallBack";

        return $"{url}?appKey={Constants.KuaiShouAppKey}&state={state}&callbackUrl={backUrl}";
    }

    /// <summary>
    /// 用户解除绑定
    /// </summary>
    /// <param name="callbackUrl"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<GetKzgResponse<KuaiShouResult>> KuaiShouUnBindUser(string openId)
    {
        var url = $"{Constants.KuaiShouApiDomain}/ats/openapi/v1/tenant/unbind";
        var tokenResult = await url.PostUrlEncodedAsync(new
        {
            openId = openId
        }).ReceiveJson<GetKzgResponse<KuaiShouResult>>();

        if (tokenResult?.code != 0)
            throw new Exception($"快手获取session_key错误:{JsonSerializer.SerializeToString(tokenResult)}");

        return tokenResult;
    }

    /// <summary>
    /// 发布职位到快手
    /// </summary>
    /// <returns></returns>
    public async Task<GetKzgResponse<int>> SendPostToKzg(object model, string openId)
    {
        try
        {
            var token = await GetToken();
            var url = $"{Constants.KuaiShouApiDomain}/ats/openapi/v1/job/create?openId={openId}";

            // 发送前记录日志
            _log.Info($"发布职位到快手", openId, JsonSerializer.SerializeToString(model));

            var sendPostResultStr = await url.WithOAuthBearerToken(token.accessToken)
            .PostJsonAsync(model).ReceiveString();

            var sendPostResult = JsonSerializer.DeserializeFromString<GetKzgResponse<int>>(sendPostResultStr);

            // if (sendPostResult?.code != 0 || sendPostResult?.result == null)
            //     throw new Exception($"发布职位到快手错误:{JsonSerializer.SerializeToString(sendPostResult)}");

            return sendPostResult;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 快手职位类别接口
    /// </summary>
    /// <returns></returns>
    public async Task<List<GetKsDicModel>> GetPostCategory()
    {
        try
        {
            var url = $"{Constants.KuaiShouApiDomain}/ats/openapi/v1/dictionary/job/category";
            var aoe = await url.GetAsync().ReceiveJson<GetKzgResponse<List<GetKsDicModel>>>();

            if (aoe?.code != 0 || aoe?.result == null)
                throw new Exception($"快手职位类别接口出错:{JsonSerializer.SerializeToString(aoe)}");

            return aoe.result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 快手标签接口
    /// </summary>
    /// <returns></returns>
    public async Task<List<GetKsDicModel>> GetPostTag()
    {
        try
        {
            var url = $"{Constants.KuaiShouApiDomain}/ats/openapi/v1/dictionary/job/tag";
            var aoe = await url.GetAsync().ReceiveJson<GetKzgResponse<List<GetKsDicModel>>>();

            if (aoe?.code != 0 || aoe?.result == null)
                throw new Exception($"快手标签接口出错:{JsonSerializer.SerializeToString(aoe)}");

            return aoe.result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 快手省市区三级
    /// </summary>
    /// <returns></returns>
    public async Task<List<GetKsProvince>> GetRegion()
    {
        try
        {
            var url = $"{Constants.KuaiShouApiDomain}/ats/openapi/v1/dictionary/province_city_area";
            var aoe = await url.GetAsync().ReceiveJson<GetKzgResponse<List<GetKsProvince>>>();

            if (aoe?.code != 0 || aoe?.result == null)
                throw new Exception($"快手省市区接口出错:{JsonSerializer.SerializeToString(aoe)}");

            return aoe.result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }
}

public class GetKsAppletPhoneResponse
{
    /// <summary>
    /// 返回1代表成功
    /// </summary>
    public int result { get; set; }

    /// <summary>
    /// 没有区号的手机号
    /// </summary>
    public string? phoneNumber { get; set; }

    /// <summary>
    /// 用户唯一标识
    /// </summary>
    public string? open_id { get; set; }

    /// <summary>
    /// 会话密钥
    /// </summary>
    public string? session_key { get; set; }
}

/// <summary>
/// 获取快招工统一返回模型
/// </summary>
public class GetKzgResponse<T>
{
    /// <summary>
    ///  code
    /// </summary>
    public int code { get; set; }

    /// <summary>
    /// message ok
    /// </summary>
    public string? message { get; set; }

    /// <summary>
    /// 具体 result
    /// </summary>
    public T? result { get; set; }
}

public class KuaiShouResult
{
    /// <summary>
    /// app id
    /// </summary>
    public int appId { get; set; }

    /// <summary>
    /// token
    /// </summary>
    public string? accessToken { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public int expireTime { get; set; }

    /// <summary>
    /// 刷新token
    /// </summary>
    public string? refreshToken { get; set; }
}

// /// <summary>
// /// 快手租户响应数据
// /// </summary>
// public class KuaiShouUnbindResponse
// {
//     /// <summary>
//     /// 成功与否
//     /// </summary>
//     public Boolean success { get; set; }

//     /// <summary>
//     ///  code
//     /// </summary>
//     public int code { get; set; }

//     /// <summary>
//     /// message ok
//     /// </summary>
//     public string? message { get; set; }

//     /// <summary>
//     /// 具体 result
//     /// </summary>
//     public KuaiShouResult? result { get; set; }
// }


// public class ReceivePositionToKS
// {
//     /// <summary>
//     /// key
//     /// </summary>
//     public string? appkey { get; set; }

//     /// <summary>
//     /// 租户id
//     /// </summary>
//     public string? openTenantId { get; set; }

//     /// <summary>
//     /// 快手投递数据
//     /// </summary>
//     public string? receiveData { get; set; }

// }

/// <summary>
/// 发布职位到快手返回
/// </summary>
public class SendPostResponse
{
    /// <summary>
    /// 职位id
    /// </summary>
    public string? jobId { get; set; }
}

/// <summary>
/// 快手通信加密模型
/// </summary>
public class KuaiShouEncryptModel
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 密钥
    /// </summary>
    public string? Key { get; set; }
}