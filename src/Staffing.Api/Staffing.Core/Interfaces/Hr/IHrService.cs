﻿using Config.CommonModel;
using Config.CommonModel.Business;
using Config.CommonModel.Tencent;
using Staffing.Model.Hr.Balance;
using Staffing.Model.Hr.User;

namespace Staffing.Core.Interfaces.Hr;

public interface IHrService
{
    GetAppList GetApps();
    Task<HrApplyAppResponse> HrApplyApp(HrApplyApp model);
    HrInfo GetHr();
    GetHrSimpleResponse GetHrSimpleSearch(QuerySearch model);
    GetAppInfo GetAppInfo(string id);
    EmptyResponse UseApp(UseApp model);
    HrCardInfo GetHrCard(string id);
    EmptyResponse UpdateHrCard(UpdateHrCard model);
    Task<GetImUserSigResponse> GetImUserSig();
    Task<HrLoginResponse> HrLogin(HrLogin model);
    Task<TokenInfo> HrRefreshToken(HrRefreshToken model);
    GetHrMessageSumResponse GetHrMessageSum();
    GetHrMessageResponse GetHrMessage(GetHrMessage model);
    Task<GetPrivacyPhoneNumberResponse> GetPrivacyPhoneNumber(GetPrivacyPhoneNumber model);
    GetRecentDeliveryResponse GetRecentDelivery(GetRecentDelivery model);
    GetNewTalentResponse GetNewTalent(GetNewTalent model);
    GetRecentVisitsResponse GetRecentVisits(GetRecentVisits model);
    HrDataInfo GetHrData();
    HrDataInfo GetHrData(string UserId);
    Task<SendInviteSmsResponse> SendInviteSms(SendInviteSms model);


    /// <summary>
    /// 获取钉钉绑定状态
    /// </summary>
    /// <returns></returns>
    GetDingDingBindingStatsuRsponse GetDingDingBindingStatsu();

    /// <summary>
    /// 绑定钉钉，获取钉钉用户信息
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<BindingDingDingRsponse> BindingDingDing(BindingDingDingRequest model);

    /// <summary>
    /// 移除钉钉绑定
    /// </summary>
    /// <returns></returns>
    EmptyResponse RelieveDingDingBinding();

    GetBalanceResponse GetBalance(GetBalance model);
    GetBalanceRecordResponse GetBalanceRecord(GetBalanceRecord model);
    Task<WithdrawResponse> Withdraw(Withdraw model);
    EmptyResponse SetHrCardShow(HrCardShow model);
    HrCardShow GetHrCardShow();
    Task<HrApplyAppResponse> HrApplyAppNew(HrApplyApp model);
    HrSettingInfo GetHrSetting();
    EmptyResponse UpdateHrSetting(HrSettingInfo model);
}