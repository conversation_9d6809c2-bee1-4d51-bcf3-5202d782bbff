﻿// using System.ComponentModel.DataAnnotations;
// using System.ComponentModel.DataAnnotations.Schema;
// using Config.Enums;

// namespace Staffing.Entity.Staffing;

// /// <summary>
// /// 销帮帮销售机会
// /// </summary>
// [Table("xbb_xsjh")]
// public class Xbb_Xsjh
// {
//     [Key]
//     public long Id { get; set; }

//     /// <summary>
//     /// 销售机会名称
//     /// </summary>
//     public string? Name { get; set; }

//     /// <summary>
//     /// 客户名称
//     /// </summary>
//     public string? CustomerName { get; set; }

//     /// <summary>
//     /// 创建人Id
//     /// </summary>
//     public string? Creator { get; set; }

//     public long XbbUpdatedTime { get; set; }
//     public long XbbCreatedTime { get; set; }
//     public DateTime CreatedTime { get; set; } = DateTime.Now;


//     public Xbb_User? Xbb_Creator { get; set; }
// }
