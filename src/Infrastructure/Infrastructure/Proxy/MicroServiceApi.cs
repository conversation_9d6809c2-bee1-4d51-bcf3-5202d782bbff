using Config;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace Infrastructure.Proxy;

[Service(ServiceLifetime.Transient)]
public class MicroServiceApi
{
    private readonly ConfigManager _config;
    private readonly LogManager _log;
    public MicroServiceApi(IOptionsSnapshot<ConfigManager> config, LogManager log)
    {
        _config = config.Value;
        _log = log;
    }

    /// <summary>
    /// 获取数字诺亚项目id
    /// </summary>
    /// <returns></returns>
    public async Task<string?> GetNuoId(string nuoId)
    {
        string url = $"{_config.DataRdsServer}/NoahAccredit/OpenService/v1/User/GetUserByToken";
        var result = (await url
        .PostJsonAsync(new { nuoId = nuoId })
        .ReceiveJson<MicroService<string>>())?.Data;
        return result;
    }

    /// <summary>
    /// 获取项目余额
    /// </summary>
    /// <param name="nuoId"></param>
    /// <returns></returns>
    public async Task<decimal?> GetBalance(string nuoId)
    {
        //string url = $"{_config.DataRdsServer}/NoahAccredit/OpenService/v1/User/GetUserByToken";
        //var result = (await url
        //.PostJsonAsync(new { nuoId = nuoId })
        //.ReceiveJson<MicroService<decimal>>())?.Data;
        //return result;

        await Task.FromResult(0);
        return 100000;
    }

    /// <summary>
    /// 请求冻结金额
    /// </summary>
    /// <param name="nuoId"></param>
    /// <param name="money"></param>
    /// <returns></returns>
    public async Task FreezeBalance(string nuoId, decimal money, string postId, string hrId)
    {
        await Task.FromResult(0);
        //string url = $"{_config.DataRdsServer}/NoahAccredit/OpenService/v1/User/GetUserByToken";
        //var result = (await url
        //.PostJsonAsync(new { nuoId = nuoId,freezeBalance = money}));
    }

    /// <summary>
    /// 请求解冻金额
    /// </summary>
    /// <param name="nuoId"></param>
    /// <param name="money"></param>
    /// <returns></returns>
    public async Task UnFreezeBalance(string nuoId, decimal money, string postId, string hrId)
    {
        await Task.FromResult(0);
        //string url = $"{_config.DataRdsServer}/NoahAccredit/OpenService/v1/User/GetUserByToken";
        //var result = (await url
        //.PostJsonAsync(new { nuoId = nuoId, unFreezeBalance = money }));
    }
}

public class MicroService<T>
{
    public int ErrCode { get; set; }
    public string ErrMsg { get; set; } = string.Empty;
    public T? Data { get; set; }
}