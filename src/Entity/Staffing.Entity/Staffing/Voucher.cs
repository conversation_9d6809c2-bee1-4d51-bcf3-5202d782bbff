﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 凭证表
/// </summary>
[Table("voucher")]
public class Voucher
{
    /// <summary>
    /// 凭证ID
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();
    /// <summary>
    /// 入金商户ID
    /// </summary>
    public string InMerchantId { get; set; } = default!;
    /// <summary>
    /// 出金商户ID
    /// </summary>
    public string OutMerchantId { get; set; } = default!;

    /// <summary>
    /// 入金商户
    /// </summary>
    public string InMerchantName { get; set; } = default!;
    /// <summary>
    /// 出金商户
    /// </summary>
    public string OutMerchantName { get; set; } = default!;

    /// <summary>
    /// 凭证时间
    /// </summary>
    public DateTime VoucherTime { get; set; } = DateTime.Now.Date;

    /// <summary>
    /// 凭证主题
    /// </summary>
    public string Subject { get; set; } = default!;

    /// <summary>
    /// 凭证总额
    /// </summary>
    public decimal Amount { get; set; }
    /// <summary>
    /// 凭证类型:1=订单合同
    /// </summary>
    public VoucherType VoucherType { get; set; }
    /// <summary>
    /// 凭证状态:1=生效中
    /// </summary>
    public VoucherStatus VoucherStatus { get; set; }
    /// <summary>
    /// 凭证申请开票状态
    /// </summary>
    public VoucherInvoiceStatus InvoiceStatus { get; set; } = VoucherInvoiceStatus.尚未开票;
    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; } = default!;
    /// <summary>
    /// 凭证地址
    /// </summary>
    public string? VoucherUrl { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    [ForeignKey(nameof(InMerchantId))]
    public Enterprise_Merchant InMerchant { get; set; } = default!;

    [ForeignKey(nameof(OutMerchantId))]
    public Enterprise_Merchant OutMerchant { get; set; } = default!;
}
