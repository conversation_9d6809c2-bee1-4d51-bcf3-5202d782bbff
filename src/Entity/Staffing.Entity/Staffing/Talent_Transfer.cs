﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 人才转移
/// </summary>
[Table("talent_transfer")]
public class Talent_Transfer
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    public string HrId { get; set; } = default!;

    public string ToHrId { get; set; } = default!;
    public string? Creator { get; set; } = string.Empty;
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}