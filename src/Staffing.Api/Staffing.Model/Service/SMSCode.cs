﻿using Config.Enums;

namespace Staffing.Model.Service;

public class SMSCode
{
    /// <summary>
    /// 手机号
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 验证码类型
    /// </summary>
    public SMSType Type { get; set; }

    /// <summary>
    /// 图形验证码的token
    /// </summary>
    public string? Verify { get; set; }
}

public class SMSCodeResponse
{

}

public class CheckSMSCode
{
    /// <summary>
    /// 手机号
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 验证码
    /// </summary>
    public string? SMSCode { get; set; }

    /// <summary>
    /// 验证码类型
    /// </summary>
    public SMSType Type { get; set; }
}

public class CheckSMSCodeResponse
{
    /// <summary>
    /// 发送是否成功
    /// </summary>
    public bool Result { get; set; }

    public string? Message { get; set; }
}