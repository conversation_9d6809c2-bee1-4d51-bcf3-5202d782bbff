﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 人才库标签字典表（真实虚拟通用表）
/// </summary>
[Table("dic_talent_label")]
public class Dic_Talent_Label
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// hrid
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 标签名称
    /// </summary>
    public string LabelName { get; set; } = default!;
}

