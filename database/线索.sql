

ALTER TABLE project_teambounty ADD `FollowerId` varchar(18) COMMENT '线索跟进者Id';




ALTER TABLE `staffing`.`recruit` ADD COLUMN `IsClues` int NULL DEFAULT 0 COMMENT '是否为线索 0 否 1是' AFTER `AutoId`;

ALTER TABLE `staffing`.`recruit` ADD COLUMN `ModeType` int NULL DEFAULT 0 COMMENT '0 众包 1指定人接受' AFTER `IsClues`;

ALTER TABLE `staffing`.`recruit` ADD COLUMN `ReceiverId` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收人id' AFTER `ModeType`;

ALTER TABLE `staffing`.`recruit` ADD COLUMN `AcceptOrder` int NULL DEFAULT 0 COMMENT '是否接单0 否 1是' AFTER `ReceiverId`;

ALTER TABLE `staffing`.`recruit` MODIFY COLUMN `AutoId` int NOT NULL AUTO_INCREMENT;



CREATE TABLE `staffing`.`project_team_config`  (
  `Id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `TeamProjectId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '协调项目表id',
  `HrId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '协同人id',
  `ProjectId` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '原始项目Id',
  `ModeType` int NOT NULL DEFAULT 0 COMMENT '0 众包 1指定人接受',
  `ReceiverId` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收人id',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `Creator` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态0开启 1关闭',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `idx_team_project_id`(`TeamProjectId` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '协同项目众包配置表' ROW_FORMAT = Dynamic;