using Config.CommonModel.Tencent;
using Config.CommonModel.ZhaoPinYun;
using Config.Enums;

namespace Config;

public class SubscriptionKey
{
    /// <summary>
    /// Hr注册消息
    /// </summary>
    public readonly static string HrRegister = "st:sub:reg_hr";

    /// <summary>
    /// 求职者注册消息
    /// </summary>
    public readonly static string SeekerRegister = "st:sub:reg_seeker";

    /// <summary>
    /// 真实人才库同步es
    /// </summary>
    public readonly static string UpdateTalentPlatform = "st:sub:updatetalentplatform";

    /// <summary>
    /// 虚拟人才库同步es
    /// </summary>
    public readonly static string UpdateTalentVirtual = "st:sub:updatetalentvirtual";

    /// <summary>
    /// 求职者注册或更换手机号
    /// </summary>
    public readonly static string NewSeekerMobile = "st:sub:newseekermobile";

    /// <summary>
    /// 计算hr排行榜
    /// </summary>
    public readonly static string HrRank = "st:sub:hrrank";

    /// <summary>
    /// 更换手机号的HR
    /// *value=UserId
    /// </summary>
    public readonly static string HrUpdateMobile = "st:sub:hrupdatemobile";

    /// <summary>
    /// 更新用户im资料
    /// </summary>
    public readonly static string UpdateSkImAct = "st:sub:skimact";

    /// <summary>
    /// 更新求职者信息
    /// </summary>
    public readonly static string UpdateSeeker = "st:sub:skupdate";

    /// <summary>
    /// 更新顾问im资料
    /// </summary>
    public readonly static string UpdateHrImAct = "st:sub:hrimact";

    //销售机会通知顾问
    public const string XsjhToHr = "st:sub:xsjhtohr";

    // /// <summary>
    // /// 求职者登录消息
    // /// </summary>
    // public readonly static string SeekerLogin = "st:sub:sklgn";

    /// <summary>
    /// 浏览职位消息
    /// </summary>
    public readonly static string PostView = "st:sub:viewpost";

    // /// <summary>
    // /// 职位变更
    // /// </summary>
    // public readonly static string PostChange = "st:sub:postchange";

    /// <summary>
    /// 职位数量统计
    /// </summary>
    public readonly static string PostStatis = "st:sub:post_statis";

    /// <summary>
    /// 计算分润状态（入职过保） - 所有场景
    /// </summary>
    public readonly static string ProjectTeamBounty = "st:sub:proj_tmbounty";

    /// <summary>
    /// 简历投递发送钉钉机器人通知消息
    /// </summary>
    public readonly static string RecruitDingTalkRobot = "st:dingtalk:robot";

    /// <summary>
    /// 入职员工表
    /// </summary>
    public readonly static string RecruitInduction = "st:sub:recruit_induction";

    /// <summary>
    /// 结算队列
    /// </summary>
    public readonly static string ProjectTeamBountySettle = "st:sub:proj_tmbounty_settle";

    /// <summary>
    /// 生成职位小程序二维码
    /// </summary>
    public readonly static string PostAppletQrCode = "st:sub:pt_apptqrcode";

    /// <summary>
    /// 消息通知
    /// </summary>
    public readonly static string MsgNotify = "st:sub:notify";

    /// <summary>
    /// 发短信
    /// </summary>
    public readonly static string SendSms = "st:sub:sendsms";

    /// <summary>
    /// 公众号消息通知
    /// </summary>
    public readonly static string MsgWeChatMpNotify = "st:sub:notifywemp";

    /// <summary>
    /// 钉钉消息
    /// </summary>
    public readonly static string MsgDingDingNotify = "st:sub:notifydd";

    /// <summary>
    /// 钉钉消息(24小时发送)
    /// </summary>
    public readonly static string MsgDingDingNotifyFullTime = "st:sub:notifyddfulltime";

    /// <summary>
    /// 同步企业线索 至 致趣百川
    /// </summary>
    public readonly static string SyncToSCRMBeschannel = "st:sync:SCRMBeschannel";

    /// <summary>
    /// 招聘状态变更
    /// </summary>
    public readonly static string RecruitStatusChange = "st:sub:recruitstchg";

    /// <summary>
    /// 计算简历处理时长
    /// </summary>
    public readonly static string ResumeProcessTime = "st:sub:resumeprocesstime";

    /// <summary>
    /// 结算状态变更
    /// </summary>
    public readonly static string TeamBountyChange = "st:sub:teambountychg";

    /// <summary>
    /// 归档状态更改：点击无效简历
    /// </summary>
    public readonly static string RecruitStatusChange2 = "st:sub:recruitstchg2";

    /// <summary>
    /// 招聘状态 - 面试 - 面试反馈
    /// </summary>
    public readonly static string RecruitInterviewBack = "st:sub:recruitInterviewback";

    /// <summary>
    /// 项目状态更新
    /// </summary>
    public readonly static string ProjectStatusChange = "st:sub:pjstup";

    /// <summary>
    /// 项目监控回执
    /// </summary>
    public readonly static string ProjectCallBack = "st:sub:callback";
    public readonly static string ProjectCallBackSum = "st:sub:callbacksum";
    public readonly static string ContractUpdatedProjectCallBack = "st:sub:contractcb";

    // /// <summary>
    // /// 职位状态变更
    // /// </summary>
    // public readonly static string PostStatusChange = "st:sub:postup";

    /// <summary>
    /// 职位顾问职位统计
    /// </summary>
    public readonly static string TeamHrTj = "st:sub:tmhrtj";

    /// <summary>
    /// 职位经纬度更新
    /// </summary>
    public readonly static string PostLocationChange = "st:sub:postlocup";

    /// <summary>
    /// 顾问数量变化
    /// </summary>
    public readonly static string AdviserCountChange = "st:sub:advctup";

    //im单聊消息回调
    public readonly static string ImC2CMsgUnReadMsg = "im:c2cunreadmsg";
    //im单聊消息回调
    public readonly static string ImC2CMsg = "im:c2cmsg";
    /// <summary>
    /// 发给云生机器人消息
    /// </summary>
    public readonly static string ImC2CChatMsg = "im:c2cchatmsg";
    /// <summary>
    /// 云生消息发回腾讯im
    /// </summary>
    public readonly static string ImB2CMsg = "im:b2cmsg";
    /// <summary>
    /// im上次聊天职位id
    /// </summary>
    public readonly static string ImC2CTeamPostId = "im:c2cteampostid";
    /// <summary>
    /// 真实人才库数量变化
    /// </summary>
    public readonly static string TalentCountChange = "st:sub:talctup";

    /// <summary>
    /// 虚拟人才库数量变化
    /// </summary>
    public readonly static string VirtualTalentCountChange = "st:sub:vrtalctup";

    /// <summary>
    /// 用户访问顾问消息
    /// </summary>
    public readonly static string VisitAdviser = "st:sub:visadv";

    // //项目成员入职导入
    // public const string ProjectMemberEntryImport = "excel:mementry:";

    /// <summary>
    /// 上传简历
    /// </summary>
    public readonly static string UpLoadResume = "st:sub:upload_resume";

    /// <summary>
    /// 表数据变更
    /// </summary>
    public readonly static string TableChange = "st:sub:tablechange";

    /// <summary>
    /// 平台人才库数据变更
    /// </summary>
    public readonly static string TalentTableChange = "st:sub:talenttablechange";

    /// <summary>
    /// 简历人才库数据变更
    /// </summary>
    public readonly static string VirtualTalentTableChange = "st:sub:virtualtttbchange";

    /// <summary>
    /// 快手投递简历库变更
    /// </summary>
    public readonly static string KuaishouTalentInfoTableChange = "st:sub:kuaishoutitbchange";

    // /// <summary>
    // /// 诺积分
    // /// </summary>
    // public readonly static string AddNScore = "st:sub:addnscore";

    /// <summary>
    /// 新用户通知
    /// </summary>
    public readonly static string NewUserMsgPush = "st:sub:newusermsgpush";

    /// <summary>
    /// 更新hr统计信息
    /// </summary>
    public readonly static string UpdateHrData = "st:sub:updatehrdata";

    /// <summary>
    /// 用户浏览职位
    /// </summary>
    public readonly static string UserPostVisit = "st:sub:userpostvisit";

    /// <summary>
    /// 腾讯广告上报
    /// </summary>
    public readonly static string TencentAdReport = "st:sub:tencentadreport";

    /// <summary>
    /// 诺快聘财务审核通过
    /// </summary>
    public readonly static string NkpFinanceAuditPass = "st:sub:nkpfinauditpass";

    /// <summary>
    /// 计算用户求职意向
    /// </summary>
    public readonly static string CountUserHope = "st:sub:countuserhope";

    /// <summary>
    /// oss地址存本地
    /// </summary>
    public readonly static string OssPathKey = "oss:osspathkey";

    /// <summary>
    /// 用户简历
    /// </summary>
    public readonly static string OssUserIdKey = "oss:resumeuserid";

    /// <summary>
    /// Hr初筛上传简历
    /// </summary>
    public readonly static string RecruitUpLoadResume = "st:sub:recruit_upload_resume";

    /// <summary>
    /// 百人快聘 - 快手简历投递
    /// </summary>
    public readonly static string KuaishouResumeDelivery = "st:sub:kuaishou_resume_delivery";

    /// <summary>
    /// 自流转钉钉消息提醒
    /// </summary>
    public readonly static string AutomaticMessage = "recruit:automessage";

    /// <summary>
    /// 顾问自动协同项目
    /// </summary>
    public readonly static string HrAutoTeam = "st:sub:hrautoteam";

    /// <summary>
    /// 职多多岗位
    /// </summary>
    public readonly static string ZddJobList = "st:sub:zddjoblist";
    public readonly static string ZddJobDetail = "st:sub:zddjobdetail";
    public readonly static string ZddJobDownList = "st:sub:zddjobdownlist";
    /// <summary>
    /// 58魔方岗位
    /// </summary>
    public readonly static string WbJobList = "st:sub:wbjoblist";
    public readonly static string WbJobDetail = "st:sub:wbjobdetail";
    public readonly static string WbJobDownList = "st:sub:wbjobdownlist";
    // 第三方岗位
    public readonly static string ThirdPartyJobList = "st:sub:thirdpartyjoblist";
    public readonly static string ThirdPartyJobDetail = "st:sub:thirdpartyjobdetail";
    public readonly static string ThirdPartyJobDownList = "st:sub:thirdpartyjobdownlist";

    /// <summary>
    /// talent_resume表变更
    /// </summary>
    public readonly static string TalentResumeChange = "st:canalsub:talentresumechange";
    public readonly static string SeekerToTalentResumeChange = "st:canalsub:sttrchange";
    public readonly static string RecruitChangeToTalentResume = "st:canalsub:recruit2tr";

    public readonly static string UpdateYunshengPlatform = "st:sub:updateyunshengplatform";
    /// <summary>
    /// 导入企微群信息的群ID列表
    /// </summary>
    public readonly static string QYWechatLoadGroupIds = "qywechat:load:groupids";
    /// <summary>
    /// 更新证书报名记录中  的 入群状态
    /// </summary>
    public readonly static string QYWechatMemUpToCerRegs = "qywechat:load:memuptocerreg";
    /// <summary>
    /// 生成顾问分享蓝领证书邀约海报的顾问ID列表
    /// </summary>
    public readonly static string AdviserShareCertificate = "certificate:advisersharecerid";

    public class Nyk
    {
        // public readonly static string ResumeSync = "st:nyk:resumesync";
        // public readonly static string OldResumeSync = "st:nyk:oldresumesync";

        //用户登录消息
        public const string NykUserLoginNotify = "nyk:userlastlogintime";

        //简历同步消息
        public const string NykResumeNotify = "nyk:resumenotify";

        //顾问15天内未读简历消息
        public const string NykHrUnreadResume = "nyk:hrunreadresume";

        //部门15天内未读简历消息
        public const string NykDeptUnreadResume = "nyk:deptunreadresume";
    }

    public class Canal
    {
        /// <summary>
        /// 诺快聘账号协同职位变更
        /// </summary>
        public readonly static string TeamNkpTeamPostChange = "st:canalsub:nkpteampostchange";

        /// <summary>
        /// 代招企业变更
        /// </summary>
        public readonly static string AgentEntChange = "st:canalsub:agententchange";

        /// <summary>
        /// 顾问变更
        /// </summary>
        public readonly static string HrChange = "st:canalsub:hrchange";

        // /// <summary>
        // /// 职位状态变更
        // /// </summary>
        // public readonly static string PostStatusChange = "st:canalsub:poststcg";

        /// <summary>
        /// 职位变更
        /// </summary>
        public readonly static string PostChange = "st:canalsub:postcg";

        /// <summary>
        /// 协同职位状态变更
        /// </summary>
        public readonly static string TeamPostStatusChange = "st:canalsub:tmpoststcg";
    }

    /// <summary>
    /// 人才库操作记录
    /// </summary>
    public readonly static string TalentResumeRecord = "st:tr:record";

    /// <summary>
    /// 人才库简历隐藏
    /// </summary>
    public readonly static string TalentResumeHide = "st:tr:hide";
}

/// <summary>
/// 群发短信
/// </summary>
public class Sub_SendSms
{
    public int Id { get; set; } = default!;

    /// <summary>
    /// 电话
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 签名
    /// </summary>
    public string? Sgin { get; set; }

    /// <summary>
    /// 短信模板编码
    /// </summary>
    public string TempCode { get; set; } = string.Empty;

    /// <summary>
    /// json内容数据(或发送的短信内容)
    /// </summary>
    public string JsonData { get; set; } = string.Empty;

    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 短信发送方
    /// </summary>
    public SMSSender Sender { get; set; } = default!;
}


/// <summary>
/// 招聘流程状态变更
/// </summary>
public class Sub_Recruit_StatusChange
{
    /// <summary>
    /// 招聘流程Id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 招聘流程状态
    /// </summary>
    public RecruitStatus Status { get; set; } = default!;

    // /// <summary>
    // /// 招聘流程状态变更记录Id
    // /// </summary>
    // public string? RecruitRecordId { get; set; }

    /// <summary>
    /// 是否新纪录(比如报名)
    /// </summary>
    public bool? IsNew { get; set; }
}

/// <summary>
/// TeamBounty状态变更
/// </summary>
public class Sub_TeamBountyChange
{
    /// <summary>
    /// TeamBountyId
    /// </summary>
    public string Id { get; set; } = default!;
}

/// <summary>
/// 项目状态变更
/// </summary>
public class Sub_Project_StatusChange
{
    public string ProjectId { get; set; } = default!;
}

// /// <summary>
// /// 职位状态变更
// /// </summary>
// public class Sub_Post_StatusChange
// {
//     public string PostId { get; set; } = default!;
// }

// /// <summary>
// /// 协同职位状态变更
// /// </summary>
// public class Sub_TeamHrTj
// {
//     public string TeamHrId { get; set; } = default!;
// }

public class Sub_TeamPostStatusChange
{
    public string TeamPostId { get; set; } = default!;
    public bool isNew { get; set; }
}

/// <summary>
/// 顾问数量变更
/// </summary>
public class Sub_AdviserCount_Change
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string SeekerId { get; set; } = default!;
}

/// <summary>
/// 人才库数量变更
/// </summary>
public class Sub_TalentCount_Change
{
    /// <summary>
    /// 必填
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 求职Id，如果有则统计用户顾问数量
    /// </summary>
    public string? SeekerId { get; set; }

    public List<string>? PostOld { get; set; }
    public List<string>? PostNew { get; set; }
    public List<string>? IndustryOld { get; set; }
    public List<string>? IndustryNew { get; set; }
}

/// <summary>
/// 职位浏览
/// </summary>
public class Sub_Post_View
{
    public string? PostId { get; set; }

    public string? TeamPostId { get; set; }

    public string? SeekerId { get; set; }

    public string? HrId { get; set; }

    /// <summary>
    /// 请求者+协同职位唯一标识
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 职位类别
    /// </summary>
    public int PostCategory { get; set; }
}

/// <summary>
/// 用户、顾问Id
/// </summary>
public class Sub_SeekerAndHr
{
    public string SeekerId { get; set; } = default!;

    public string HrId { get; set; } = default!;
}

/// <summary>
/// 销售机会通知顾问
/// </summary>
public class Sub_XsjhToHr
{
    public long CustomerId { get; set; }
    public long OpportunityId { get; set; }
}

/// <summary>
/// 新用户推送消息
/// </summary>
public class Sub_NewUserMsgPush
{
    public string UserId { get; set; } = default!;
    public int Day { get; set; }
    public string Mobile { get; set; } = default!;
    public string? H5OpenId { get; set; }
    public bool WeChatH5Subscribe { get; set; }
    public string? UserName { get; set; }
    public int Score { get; set; }
}

public class Sub_UpdateHrData
{
    public string UserId { get; set; } = default!;
    public UpdateHrDataType Type { get; set; } = default!;
}

public class Sub_UserPostVisit
{
    public string UserId { get; set; } = default!;
    public int Post { get; set; }
    public string? PostId { get; set; }
    public string? HrId { get; set; }
}

public class Sub_UpdateSeeker
{
    public string? SeekerId { get; set; }

    public bool IsNewSeeker { get; set; }
}

public class Sub_TalentTableChange
{
    public string? TalentId { get; set; }
}

public class Sub_PostChange
{
    public string? PostId { get; set; }

    public bool StatusChanged { get; set; }

    public bool LocationChanged { get; set; }
}

// public class Sub_TeamNkpTeamPostChange
// {
//     public string? TeamPostId { get; set; }
// }

// public class Sub_NkpHrChange
// {
//     public string? HrId { get; set; }
// }

public class Sub_VirtualTableChange
{
    public string? TalentId { get; set; }
}

public class Sub_TencentAdReport
{
    public string? ClickId { get; set; }
    public long SetId { get; set; }
    public int AccountId { get; set; }
    public string? At { get; set; }
}

public class Sub_KuaishouTalentInfoTableChange
{
    public string? ApplicationId { get; set; }
}
public class Sub_ImC2CMsg
{
    public int? SyncOtherMachine { get; set; }//1：把消息同步到 From_Account 在线终端和漫游上 2：消息不同步至 From_Account 3：消息不同步至 To_Account 若不填写默认情况下会将消息存 From_Account 漫游 选填
    public string? FromUserId { get; set; }//发送者ID 选填
    public string? ToUserId { get; set; }//接收者ID 必填
    public string? MsgKey { get; set; }//该条消息的唯一标识
    public long? MsgSeq { get; set; }//消息序列号（32位无符号整数），后台会根据该字段去重及进行同秒内消息的排序。若不填该字段，则由后台填入随机数
    public long? MsgRandom { get; set; }//消息随机数（32位无符号整数），后台用于同一秒内的消息去重。请确保该字段填的是随机数
    public string? MsgId { get; set; }//该条消息在客户端唯一标识
    public List<SendImMsgBody>? MsgBody { get; set; } = null;//消息体
    public DateTime? MsgTime { get; set; }//消息时间
}
public class Sub_ImC2CChatMsg
{
    public string? From_Account { get; set; }//发送方ID
    public string? To_Account { get; set; }//接收方ID
    public ChatMessage? chat { get; set; }//会话信息
}
public class Sub_ImB2CMsg
{
    public int? SyncOtherMachine { get; set; }//1：把消息同步到 From_Account 在线终端和漫游上 2：消息不同步至 From_Account 3：消息不同步至 To_Account 若不填写默认情况下会将消息存 From_Account 漫游 选填
    public string? From_Account { get; set; }//消息发送方 UserID（用于指定发送消息方账号） 选填
    public string? To_Account { get; set; }//消息接收方 UserID 必填
    public long? MsgSeq { get; set; }//消息序列号（32位无符号整数），后台会根据该字段去重及进行同秒内消息的排序。若不填该字段，则由后台填入随机数 选填
    public long? MsgRandom { get; set; }//消息随机数（32位无符号整数），后台用于同一秒内的消息去重。请确保该字段填的是随机数 必填
    public List<SendImMsgBody>? MsgBody { get; set; }//消息内容 必填
    public string? CloudCustomData { get; set; }//消息自定义数据（云端保存，会发送到对端，程序卸载重装后还能拉取到） 选填
    public int? SupportMessageExtension { get; set; }// 该条消息是否支持消息扩展：0为不支持 1为支持 选填
}
public class Sub_ImC2CTeamPostId
{
    public string? UserId { get; set; }//求职者ID
    public string? TeamPostId { get; set; }//协同职位ID
    public string? AdviserImId { get; set; }//顾问ID
}
// /// <summary>
// /// 职位统计
// /// </summary>
// public class Sub_Post_Statis
// {
//     public string? PostId { get; set; }

//     public int TeamPostId { get; set; }
// }