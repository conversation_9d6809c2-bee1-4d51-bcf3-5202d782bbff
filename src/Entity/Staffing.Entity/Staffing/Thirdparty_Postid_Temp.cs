﻿using Config.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Staffing.Entity.Staffing
{
    [Table("thirdparty_postid_temp")]
    public class Thirdparty_Postid_Temp
    {
        [Key]
        public string Id { get; set; } = EntityTools.SnowflakeId();
        public string? ThirdPostId { get; set; }//第三方岗位id
        public PostSouce Source { get; set; }//岗位来源  0:诺快聘, 1:职多多， 2、58魔方
    }
}
