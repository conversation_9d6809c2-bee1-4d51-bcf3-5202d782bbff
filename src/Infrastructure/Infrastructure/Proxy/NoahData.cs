using Config;
using Config.CommonModel;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace Infrastructure.Proxy;

[Service(ServiceLifetime.Transient)]
public class NoahData
{
    private readonly ConfigManager _config;
    private readonly CacheHelper _cacheHelper;
    private readonly CommonCacheService _commonCacheService;
    private readonly LogManager _log;
    public NoahData(IOptionsSnapshot<ConfigManager> config, CacheHelper cacheHelper, LogManager log,
    CommonCacheService commonCacheService)
    {
        _config = config.Value;
        _cacheHelper = cacheHelper;
        _log = log;
        _commonCacheService = commonCacheService;
    }

    public async Task<TycUpdateAgentEntResponse> GetEnterprise(string name)
    {
        // //java服务已挂，直接返回
        // await Task.FromResult(1);
        // return new EntSyncBasicModel();
        try
        {
            name = (name ?? string.Empty).Trim()
               .Replace("(", "（").Replace(")", "）")
               .Replace("[", "【").Replace("]", "】")
               .Replace("<", "《").Replace(">", "》");

            if (string.IsNullOrEmpty(name))
                throw new BadRequestException("请提供企业名称");

            // var resultStr = await $"{_config.NoahDataApiDomain}/api/v1/ent/sync/basic".SetQueryParams(new { name = name })
            // .GetStringAsync();

            // var result = ServiceStack.Text.JsonSerializer.DeserializeFromString<NoahDataModel<EntSyncBasicModel>>(resultStr);

            // return result?.data ?? new EntSyncBasicModel();

            return _commonCacheService.GetAgentEntFromTyc(name, false);
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
        catch (Exception e)
        {
            _log.Error("企业工商数据出错", Tools.GetErrMsg(e), name);
            return new TycUpdateAgentEntResponse();
        }
    }

    public async Task<EntBusiModel> GetEnterpriseCache(string name)
    {
        // //java服务已挂，直接返回
        // await Task.FromResult(1);
        // return new EntBusiModel();

        name = (name ?? string.Empty).Trim()
               .Replace("(", "（").Replace(")", "）")
               .Replace("[", "【").Replace("]", "】")
               .Replace("<", "《").Replace(">", "》");

        if (string.IsNullOrEmpty(name))
            throw new BadRequestException("请提供企业名称");

        var cacheKey = $"{RedisKey.Dic.BusiEnt}{Md5Helper.Md5(name)}";
        var disable = 3600 * 24 * 7;

        var result = await _cacheHelper.GetRedisCacheAsync<EntBusiModel>(async () =>
        {
            var entbusi = await GetEnterprise(name);

            if (entbusi == null)
                return new EntBusiModel();

            var ace = new EntBusiModel
            {
                Tags = new List<string>()
            };

            if (DateTime.TryParse(entbusi.EstiblishTime, out var dt))
            {
                ace.Age = DateTime.Now.Year - dt.Year + 1;
                ace.Age = ace.Age > 1 ? ace.Age : 1;
                ace.RegisterDate = dt;
            }

            // if (!string.IsNullOrWhiteSpace(entbusi.OpenStatus))
            //     ace.Tags.Add(entbusi.OpenStatus);

            if (entbusi.Tags?.Count > 0)
                ace.Tags.AddRange(entbusi.Tags);

            ace.LegalPerson = entbusi.LegalPersonName;
            ace.RegisteredCapital = entbusi.RegCapital;

            return ace;
        }, cacheKey, disable);

        return result;
    }
}

public class NoahDataModel<T>
{
    public T? data { get; set; }
    public bool success { get; set; }
    public string? message { get; set; }
}

public class EntSyncBasicModel
{
    public string? Id { get; set; }
    public string? QDID { get; set; }
    public string? Name { get; set; }
    public string? EntWord { get; set; }
    public string? Scope { get; set; }
    public string? RegisterDate { get; set; }
    public string? IndustryOne { get; set; }
    public string? IndustryTwo { get; set; }
    public string? Addr { get; set; }
    /// <summary>
    /// 营业状态 在营
    /// </summary>
    public string? OpenStatus { get; set; }
    public List<string>? Labels { get; set; }
    public string? LegalPerson { get; set; }
    public List<string>? Tags { get; set; }
    public string? Logo { get; set; }
    public string? StaffNum { get; set; }
    public string? SocialNum { get; set; }
    public string? RegisterCapital { get; set; }
    public EntSyncBasicBusinessModel? Business { get; set; }
    public string? Description { get; set; }
    public List<string>? EmailList { get; set; }
    public List<string>? PhoneList { get; set; }
    public List<string>? WebList { get; set; }
    public decimal RegLon { get; set; }
    public decimal RegLat { get; set; }
    public string? Source { get; set; }
    public string? SourceLink { get; set; }
}

public class EntSyncBasicBusinessModel
{
    public string? SocialCreditCode { get; set; }
    public string? LicenseNumber { get; set; }
    public string? OrgNo { get; set; }
    public string? TaxNum { get; set; }
    public string? RegOrg { get; set; }
    public string? EntType { get; set; }
}

public class EntBusiModel
{
    /// <summary>
    /// 成立年限
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime? RegisterDate { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 法人
    /// </summary>
    public string? LegalPerson { get; set; }

    /// <summary>
    /// 注册资金
    /// </summary>
    public string? RegisteredCapital { get; set; }
}