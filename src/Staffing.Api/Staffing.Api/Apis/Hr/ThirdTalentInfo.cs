using Config;
using Config.CommonModel.ThirdTalentInfo;
using Config.Enums;
using Infrastructure.Extend;
using Staffing.Model.Hr.KuaiShouTalentInfo;
using Staffing.Core.Interfaces.Hr;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class ThirdTalentInfoRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("thirdtalentinfo").WithTags("快手人才库")
        .WithMetadata(new Powers { Power = Power.KuaiShou });

        group.MapPost("/parse", async (object infos, IThirdTalentInfoService<KsTalentInfo> rep) => await rep.TryParseTalentInfoAsync(infos))
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "快手简历信息入库" });

        group.MapPost("/dblist", (TalentInfoPageListRequest request, IThirdTalentInfoService<KsTalentInfo> rep) => rep.GetKuaiShouTalentInfoFromDBPageList(request))
            .WithOpenApi(operation => new(operation) { Summary = "快手简历信息列表 - 数据库查询版本" });

        group.MapPost("/edit", (EditKuaiShouTalent request, IThirdTalentInfoService<KsTalentInfo> rep) => rep.EditKuaiShouTalent(request))
            .WithOpenApi(operation => new(operation) { Summary = "修改快手简历" });

        group.MapGet("/view", (string applicationId, IThirdTalentInfoService<KsTalentInfo> rep) => rep.GetKuaiShouTalentInfoForEditPage(applicationId))
            .WithOpenApi(operation => new(operation) { Summary = "快手简历详情查看" });

        group.MapPost("/save", (RelationRequest request, IThirdTalentInfoService<KsTalentInfo> rep) => rep.RelationHrAndKuaishouTalent(request))
            .WithOpenApi(operation => new(operation) { Summary = "快手简历回访" });

        group.MapGet("/teamworkers", (IThirdTalentInfoService<KsTalentInfo> rep) => rep.GetTeamWorkers())
            .WithOpenApi(operation => new(operation) { Summary = "获取同事列表" });

        group.MapPost("/posts", (PostTeamPageListRequest request, IThirdTalentInfoService<KsTalentInfo> rep) => rep.GetPostsByStr(request))
            .WithOpenApi(operation => new(operation) { Summary = "获取职位列表" });

        group.MapGet("/invalid", (IThirdTalentInfoService<KsTalentInfo> rep) => rep.GetInValidMemo())
            .WithOpenApi(operation => new(operation) { Summary = "无效简历描述列表" });

        group.MapGet("/valid", (IThirdTalentInfoService<KsTalentInfo> rep) => rep.GetValidMemo())
            .WithOpenApi(operation => new(operation) { Summary = "有效简历描述列表" });
    }
}