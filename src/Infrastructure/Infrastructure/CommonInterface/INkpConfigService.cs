﻿using Config.CommonModel;
using Config.CommonModel.NkpConfig;

namespace Infrastructure.CommonInterface;

public interface INkpConfigService
{
    /// <summary>
    /// 保存配置
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    EmptyResponse SaveConfig(ConfigSaveModel model);

    /// <summary>
    /// 全量获取配置tree
    /// </summary>
    /// <returns></returns>
    List<ConfigModel> GetConfigTreeList();
}
