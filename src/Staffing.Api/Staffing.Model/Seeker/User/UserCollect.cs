﻿using System.Text.Json.Serialization;
using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;

namespace Staffing.Model.Seeker.User;

public class GetCollectPost : QueryRequest
{

}

public class GetCollectPostResponse : QueryResponse
{
    public List<GetCollectPostInfo> Rows { get; set; } = new List<GetCollectPostInfo>();
}

public class GetCollectPostInfo : GetPostDetail
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 是否是当前顾问
    /// </summary>
    public bool IsCurrent { get; set; }

    /// <summary>
    /// 协同者状态
    /// </summary>
    [JsonIgnore]
    public PostStatus? TeamStatus { get; set; }

    /// <summary>
    /// 投递Id
    /// </summary>
    public string? DeliveryId { get; set; }
}

public class UpdateCollectPost
{
    /// <summary>
    /// 协同职位Id，如果是收藏必填
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// true=收藏，false=取消
    /// </summary>
    public bool Collect { get; set; }
}