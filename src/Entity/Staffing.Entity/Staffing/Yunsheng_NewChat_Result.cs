﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Staffing.Entity.Staffing
{
    [Table("yunsheng_newchat_result")]
    public class Yunsheng_NewChat_Result
    {
        [Key]
        public string? Id { get; set; } = EntityTools.SnowflakeId();
        public string? chatId { get; set; }//聊天会话 ID
        public string? mateScore { get; set; }//AI 匹配分
        public string? YunshengPostId {  get; set; }//云生岗位 ID
        public string? ResumeId {  get; set; }//简历 ID
        public DateTime? CreatedTime { get; set; }
        public DateTime? UpdatedTime { get; set; }
    }
}
