﻿namespace Infrastructure.Extend
{
    public static class DateTimeExtension
    {
        /// <summary>
        /// 日期时间转秒时间戳
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static long ToUnixTimeSeconds(this DateTime dt)
        {
            //要用ToUniversalTime来计算才不会有时区问题
            return (dt.ToUniversalTime().Ticks - 621355968000000000) / 10000000;
        }
    }
}
