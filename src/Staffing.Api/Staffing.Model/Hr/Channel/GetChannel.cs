using Config;
using Config.CommonModel;
using Config.Enums;

namespace Staffing.Model.Hr.Channel;

/// <summary>
/// 渠道信息，其他返回内容可继承
/// </summary>

// 获取列表使用
public class GetCounselorResponse : QueryResponse
{
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? HrId { get; set; } = null!;

    /// <summary>
    /// 渠道商 数量
    /// </summary>
    public int ChannelNum { get; set; }

    // public List<CounselorChannel> Rows { get; set; } = default!;

    public List<CounselorChannel> Rows { get; set; } = default!;

}

public class GetCounselors : QueryRequest
{
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? CounselorId { get; set; }
}

public class SearchChannelName
{
    /// <summary>
    /// 搜索内容
    /// </summary>
    public string? SearchText { get; set; }
}


public class SearchChannelNameResponse : CounselorChannel
{
    /// <summary>
    /// 求职者id
    /// </summary>
    public string? SeekerId { get; set; }

}


public class SetCounselorChannelCode
{
}

public class GetCounselorChannelCodeResponse
{
    /// <summary>
    /// 顾问邀约渠道二维码
    /// </summary>
    public string? ChannelInvQrCode { get; set; }
}

public class SetCounselorGroupCode
{

    /// <summary>
    /// 主键id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 群二维码
    /// </summary>
    public string? GroupCode { get; set; }
}


public class SetCounselor //: QueryRequest
{

    /// <summary>
    /// 类型 （角色）
    /// </summary>
    public ChannelType? Type { get; set; }

    // /// <summary>
    // /// 渠道用户id
    // /// </summary>
    // public string ChannelUserId { get; set; } = null!;

}

public class CounselorChannel
{
    /// <summary>
    /// 主键id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// 渠道用户id
    /// </summary>
    public string ChannelUserId { get; set; } = null!;

    /// <summary>
    /// 渠道名字
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 类型 （角色）
    /// </summary>
    public ChannelType? Type { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 项目截止日期
    /// </summary>
    public DateTime? EndTime { get; set; } = Constants.DefaultFutureTime;

    /// <summary>
    /// 描述 （备注)
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 群二维码
    /// </summary>
    public string? GroupQrCode { get; set; }


}