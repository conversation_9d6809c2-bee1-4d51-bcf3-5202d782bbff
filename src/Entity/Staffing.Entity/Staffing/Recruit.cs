﻿using Config.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 招聘流程表
/// </summary>
[Table("recruit")]
public class Recruit
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string RecruitId { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 投递Id，可空，数字诺亚可以不通过投递直接进入招聘
    /// </summary>
    public string? DeliveryId { get; set; }

    /// <summary>
    /// HrId（主创Id）
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 求职者Id
    /// </summary>
    public string SeekerId { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 简历处理时长
    /// </summary>
    public int? ResumeProcessTime { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public Config.Enums.RecruitStatus Status { get; set; }

    /// <summary>
    /// 归档状态
    /// </summary>
    public Config.Enums.RecruitFileAway FileAway { get; set; } = Config.Enums.RecruitFileAway.TalentReserve;

    /// <summary>
    /// 归档备注
    /// </summary>
    public string FileRemarks { get; set; } = string.Empty;

    /// <summary>
    /// 第三方简历池主键id
    /// </summary>
    public string? ResumeBufferId { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public Config.Enums.RecruitType Type { get; set; }

    /// <summary>
    /// 用户端
    /// </summary>
    public ClientType? UserClient { get; set; }

    /// <summary>
    /// 入职时间
    /// </summary>
    public DateTime? InductionTime { get; set; }

    /// <summary>
    /// 状态更新时间
    /// </summary>
    public DateTime StatusTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 无效原因 - 产品非要加这个东西，不知道脑子怎么想的，没办法只能加字段了
    /// </summary>
    public string? InvalidReason { get; set; }

    //[ForeignKey("HrId")]
    
    /// <summary>
    /// 自增Id
    /// </summary>
    public int AutoId { get; set; }
    /// <summary>
    /// 是否为线索 0 否 1是
    /// </summary>
    public ProjectTeamIsClues? IsClues { get; set; }
    /// <summary>
    /// 0 众包 1指定人接受
    /// </summary>
    public ProjectTeamConfigType? ModeType { get; set; }
    /// <summary>
    /// 接收人id
    /// </summary>
    public string? ReceiverId { get; set; }
    /// <summary>
    /// 邀面人id
    /// </summary>
    public string? FollowerId { get; set; }
    /// <summary>
    /// 是否接单0 否 1是
    /// </summary>
    public RecruitAcceptOrderEnum? AcceptOrder { get; set; } = RecruitAcceptOrderEnum.未接单;
    public User_Hr User_Hr { get; set; } = default!;

    public User_Seeker User_Seeker { get; set; } = default!;
    
    /// <summary>
    /// 邀面人
    /// </summary>
    public User_Hr FollowerUser { get; set; } = default!;

    public Post_Delivery Post_Delivery { get; set; } = default!;

    public List<Recruit_Interviewer_Screen> Recruit_Interviewer_Screen { get; set; } = default!;

    public List<Recruit_Interview> Recruit_Interview { get; set; } = default!;

    public List<Recruit_Offer> Recruit_Offer { get; set; } = default!;

    public List<Recruit_Interviewer_Todo> Recruit_Interviewer_Todo { get; set; } = default!;

    public List<Recruit_Label> Recruit_Label { get; set; } = default!;
    public Resume_Buffer Resume_Buffer { get; set; } = default!;
    public Post_Bounty Post_Bounty { get; set; } = default!;

    public List<Recruit_Record> Recruit_Record { get; set; } = default!;
}

