﻿namespace Config;

public class ConfigManager
{
    /// <summary>
    /// OAuth地址
    /// </summary>
    public string? OAuthServer { get; set; }

    /// <summary>
    /// 诺聘数据接口域名
    /// </summary>
    public string? NoahDataApiDomain { get; set; }

    /// <summary>
    /// 解析简历接口域名
    /// </summary>
    public string? ParseResumeDomain { get; set; }

    /// <summary>
    /// 解析简历接口域名
    /// </summary>
    public string? ParseResumeModeType { get; set; }

    /// <summary>
    /// redis
    /// </summary>
    public string RedisAddress { get; set; } = default!;

    /// <summary>
    /// 公共redis，解决测试环境与正式环境互刷微信token问题
    /// </summary>
    public string PublicRedisAddress { get; set; } = default!;

    // /// <summary>
    // /// Redis
    // /// </summary>
    // public RedisServer? RedisServer { get; set; }

    /// <summary>
    /// 是否启用请求日志
    /// </summary>
    public bool RequestLog { get; set; }

    /// <summary>
    /// 数字诺亚接口配置
    /// </summary>
    public NdnConfiguration? Ndn { get; set; }

    /// <summary>
    /// 腾讯云
    /// </summary>
    public TencentOptions? TencentOptions { get; set; }

    /// <summary>
    /// 测试手机号
    /// </summary>
    public string[]? TestMobiles { get; set; }

    /// <summary>
    /// 验签key
    /// </summary>
    public string? SignKey { get; set; }

    /// <summary>
    /// 诺聘服务器地址
    /// </summary>
    public string? StaffingServer { get; set; }

    /// <summary>
    /// es配置
    /// </summary>
    public ElasticSearchConfig? ElasticSearch { get; set; }

    /// <summary>
    /// rocket配置
    /// </summary>
    public RocketMqConfig? RocketMq { get; set; }
    /// <summary>
    /// cancal配置
    /// </summary>
    public CanalConfig? Canal { get; set; }

    /// <summary>
    /// e签宝
    /// </summary>
    public ESignConfig? ESign { get; set; }

    /// <summary>
    /// 阿里云
    /// </summary>
    public AliyunConfig? Aliyun { get; set; }

    /// <summary>
    /// 庄点科技短信配置
    /// </summary>
    public ZhuangDianSMS? ZhuangDian { get; set; }

    /// <summary>
    /// 微信配置
    /// </summary>
    public WeChatConfig? WeChat { get; set; }

    /// <summary>
    /// 服务器报警配置
    /// </summary>
    public WarningConfig? Warning { get; set; }

    /// <summary>
    /// 月斌服务器地址
    /// </summary>
    public string? DataRdsServer { get; set; }

    /// <summary>
    /// 月宾Kafka地址
    /// </summary>
    public string? DataKafkaServer { get; set; }

    /// <summary>
    /// Kafka topic
    /// </summary>
    public string? DataKafkaTopic { get; set; }

    /// <summary>
    /// 请求内部服务
    /// </summary>
    public InternalService? InternalService { get; set; }

    /// <summary>
    /// 服务通信密钥
    /// </summary>
    public List<string> ServiceKeys { get; set; } = new List<string>();
}

public class InternalService
{
    public string? Domain { get; set; }
    public LocalServiceKeys? LocalServiceKeys { get; set; }
}

public class LocalServiceKeys
{
    /// <summary>
    /// 结算服务密钥
    /// </summary>
    public string? Settlement { get; set; }
}

public class RedisServer
{
    public string? Address { get; set; }
}

public class WeChatConfig
{
    public WeChatApp? SeekerApplet { get; set; }
    public WeChatApp? InterviewApplet { get; set; }
    public WeChatApp? HrApplet { get; set; }
}

public class WeChatApp
{
    public string? AppId { get; set; }
    public string? Secret { get; set; }
}

public class ESignConfig
{
    public string? Domain { get; set; }
    public string? AppId { get; set; }
    public string? Secret { get; set; }
}

public class WarningConfig
{
    public int SendInterval { get; set; }

    public List<string>? EMails { get; set; }

    /// <summary>
    /// 发件人账号
    /// </summary>
    public string? SendMail { get; set; }

    /// <summary>
    /// 发件人key
    /// </summary>
    public string? SendMailKey { get; set; }

    /// <summary>
    /// 发件人Smtp
    /// </summary>
    public string? SendMailSmtp { get; set; }
}

public class TencentOptions
{
    public string FaceIdSecretId { get; set; } = default!;
    public string FaceIdSecretKey { get; set; } = default!;

    //im
    public string ImAppId { get; set; } = default!;
    public string ImAppKey { get; set; } = default!;
}

public class AliyunConfig
{
    //public string? AccessKeyId { get; set; }
    //public string? AccessKeySecret { get; set; }

    //public AliyunOssConfig? Oss { get; set; }
    public AliyunFullAccess? FullAccess { get; set; }
    public AliyunSMS? AliSMS { get; set; }
    public AliyunOss? Oss { get; set; }
    //public AliyunAfsConfig? Afs { get; set; }
}

//public class AliyunOssConfig
//{
//    public string? AccessKeyId { get; set; }
//    public string? AccessKeySecret { get; set; }
//    public string? Bucket { get; set; }
//    public string? Endpoint { get; set; }
//    public string? Host { get; set; }
//    public int ExpirationSeconds { get; set; }
//    public string? Dir { get; set; }
//    public string? SystemDir { get; set; }
//    public long MaxSize { get; set; }
//}

/// <summary>
/// es配置
/// </summary>
public class ElasticSearchConfig
{
    public string? Address { get; set; }
    public string? UserName { get; set; }
    public string? Password { get; set; }
}


/// <summary>
/// rocketmq配置
/// </summary>
public class RocketMqConfig
{
    public string? RocketmqBrokerAddress { get; set; }
    public string? RocketmqTopic { get; set; }
    public string? RocketConsumerGroup { get; set; }
}

/// <summary>
/// canal配置
/// </summary>
public class CanalConfig
{
    public string? Server { get; set; }
    public int Port { get; set; }
    public string? ClientId { get; set; }
}

public class NdnConfiguration
{
    public string? ApiDomain { get; set; }
    public string? OAuthDomain { get; set; }
    public string? ClientId { get; set; }
    public string? ClientSecret { get; set; }
    public string? GrantType { get; set; }

    /// <summary>
    /// 诺聘帐套
    /// </summary>
    public string? NuoPinBookCode { get; set; }
}

public class AliyunFullAccess
{
    public string? AccessKeyId { get; set; }
    public string? AccessKeySecret { get; set; }
}

public class AliyunOss
{
    public string? Dir { get; set; }
}

public class AliyunSMS
{
    public bool Active { get; set; }
    public string? AccessKeyId { get; set; }
    public string? AccessKeySecret { get; set; }
    public string? Domain { get; set; }
    public string? Version { get; set; }
    public string? Action { get; set; }
    public string? SignName { get; set; }
    public string? TemplateCode { get; set; }
    public string? OfferTemplateCode { get; set; }
    public string? InterviewTemplateCode { get; set; }
    //public string? FacingInterviewTemplateCode { get; set; }
    //public string? UnFacingInterviewTemplateCode { get; set; }

    /// <summary>
    /// 过期时间(秒)
    /// </summary>
    /// <value>The expire seconds.</value>
    public int ExpireSeconds { get; set; }

    /// <summary>
    /// 发送间隔(秒)
    /// </summary>
    /// <value>The send interval.</value>
    public int SendInterval { get; set; }

    public int MaxEveryDay { get; set; }
}

//public class AliyunAfsConfig
//{
//    public string? AppKey { get; set; }
//}

/// <summary>
/// 庄点科技短信配置
/// </summary>
public class ZhuangDianSMS
{
    /// <summary>
    /// 企业编号
    /// </summary>
    public string? Enterprise_No { get; set; }

    /// <summary>
    /// 账号
    /// </summary>
    public string? Account { get; set; }

    /// <summary>
    /// 密钥
    /// </summary>
    public string? Http_Sign_Key { get; set; }

    /// <summary>
    /// 接口地址
    /// </summary>
    public string? Api_IP { get; set; }

    /// <summary>
    /// 短信签名
    /// </summary>
    public string[]? SMSSignature { get; set; }
}