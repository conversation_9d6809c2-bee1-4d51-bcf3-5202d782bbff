﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 凭证和结算单关系表
/// </summary>
[Table("voucher_settlement")]
public class Voucher_Settlement
{
    /// <summary>
    /// 主键
    /// </summary>
    [Key]
    public string Id { get; set; } = Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// post_settlement_detail表Id
    /// </summary>
    public string SettlementDetailId { get; set; } = string.Empty;

    /// <summary>
    /// 凭证，明细Id
    /// </summary>
    public string VoucherDetailId { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    [ForeignKey(nameof(SettlementDetailId))]
    public Post_Settlement_Detail Post_Settlement_Detail { get; set; } = default!;


    [ForeignKey(nameof(VoucherDetailId))]
    public Voucher_Detail Voucher_Detail { get; set; } = default!;
}
