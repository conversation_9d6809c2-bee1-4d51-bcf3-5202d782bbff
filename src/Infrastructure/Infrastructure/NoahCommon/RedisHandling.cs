namespace Infrastructure.NoahCommon;

public class RedisHandling
{
    // public static string GetRedisString(Noah.Redis.RedisOptions redisOptionObj)
    // {
    //     //redis
    //     List<string> config = new List<string>
    //     {
    //         $"{redisOptionObj.Endpoint}:{redisOptionObj.Port}",
    //         $"defaultDatabase={redisOptionObj.Database}",
    //         $"name={(string.IsNullOrEmpty(redisOptionObj.ClientName) ? Assembly.GetEntryAssembly()!.GetName().Name : redisOptionObj.ClientName)}"
    //     };
    //     if (!redisOptionObj.IsClearPassword)
    //         redisOptionObj.Password = Noah.SimpleTools.ContentEncrypt.AESDecrypt(redisOptionObj.Password);

    //     if (!string.IsNullOrEmpty(redisOptionObj.Password))
    //         config.Add($"password={redisOptionObj.Password}");
    //     if (!string.IsNullOrEmpty(redisOptionObj.OtherOptions))
    //         config.Add(redisOptionObj.OtherOptions);

    //     return string.Join(",", config);
    // }
}