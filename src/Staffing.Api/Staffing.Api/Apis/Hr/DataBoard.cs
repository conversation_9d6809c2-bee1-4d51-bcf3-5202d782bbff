using Staffing.Model.Hr.Bounty;
using Staffing.Model.Hr.Dashboard;
using Staffing.Model.Hr.Project;
using Staffing.Core.Interfaces.Hr;
using Config;
using Infrastructure.Extend;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class DataBoardRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("databoard").WithTags("数据看板");
        group.MapGet("/post/board", async ([AsParameters] GetProjectBoard model, IDataBoardService rep) => await rep.GetPostBoard(model))
            .WithOpenApi(operation => new(operation) { Summary = "职位看板统计" });

        group.MapGet("/teamboard/analysis", async ([AsParameters] GetProjectBoard model, IDataBoardService rep) => await rep.GetTeamBoardAnalysis(model))
            .WithOpenApi(operation => new(operation) { Summary = "协同分析看板统计" });

        group.MapGet("/teamboard/stat", async ([AsParameters] GetProjectBoard model, IDataBoardService rep) => await rep.GetTeamBoardStat(model))
            .WithOpenApi(operation => new(operation) { Summary = "协同看板统计" });

        group.MapGet("/interviewer/stat", async ([AsParameters] GetProjectBoard model, IDataBoardService rep) => await rep.GetInterviewerStat(model))
            .WithOpenApi(operation => new(operation) { Summary = "面试官看板统计" });

        group.MapGet("/recruit/stat", async ([AsParameters] GetProjectBoard model, IDataBoardService rep) => await rep.GetRecruitStat(model))
            .WithOpenApi(operation => new(operation) { Summary = "招聘流程看板表格统计（主创）" });

        group.MapPost("/teambounty", (GetTeamBountys model, IBountyService rep) => rep.GetTeamBountys(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取协同分润列表" });

        group.MapPost("/recruits", (GetRecentRecruits model, IBountyService rep) => rep.GetRecentRecruits(model))
            .WithOpenApi(operation => new(operation) { Summary = "最近的招聘流程" });

        group.MapGet("/summary", (IDataBoardService rep) => rep.GetSummaryData())
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "投递汇总数据" });
    }
}