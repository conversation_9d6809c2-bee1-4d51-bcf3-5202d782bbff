﻿using Config;
using Config.CommonModel.Business;

namespace Infrastructure.Common;

public class MsgHelper
{
    /// <summary>
    /// 发送消息通知
    /// </summary>
    /// <param name="model"></param>
    public static void SendMsgNotify(MsgNotifyModel model)
    {
        Task.Run(() => MyRedis.Client.RPush(SubscriptionKey.MsgNotify, model));
    }

    public static void SendMsgNotify(MsgNotifyModel[] model)
    {
        if (model.Count() > 0)
            Task.Run(() => MyRedis.Client.RPush(SubscriptionKey.MsgNotify, model));
    }

    /// <summary>
    /// 发送钉钉消息
    /// </summary>
    /// <param name="model"></param>
    public static void SendDingdingNotify(MsgNotifyModel[] model)
    {
        if(model.Count() > 0)
            Task.Run(() => MyRedis.Client.RPush(SubscriptionKey.MsgDingDingNotify, model));
    }

    // todo:放在for循环了，task.run 是否会启动很多个新线程
    public static async Task SendDingdingNotifyAsync(MsgNotifyModel[] model)
    {
        if (model.Count() > 0)
            await Task.Run(() => MyRedis.Client.RPush(SubscriptionKey.MsgDingDingNotify, model));
    }

    // todo:放在for循环了，task.run 是否会启动很多个新线程
    public static async Task SendDingdingNotifyFullTimeAsync(MsgNotifyModel[] model)
    {
        if (model.Count() > 0)
            await Task.Run(() => MyRedis.Client.RPush(SubscriptionKey.MsgDingDingNotifyFullTime, model));
    }
}