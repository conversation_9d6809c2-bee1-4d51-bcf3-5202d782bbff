﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Nyk;

/// <summary>
/// 用户登录记录
/// </summary>
[Table("UserLoginRecords")]
public class UserLoginRecords
{
    /// <summary>
    /// id
    /// </summary>
    [Key]
    public string PK_ULRID { get; set; } = default!;

    public string PK_UAID { get; set; } = default!;

    public string PK_UID { get; set; } = default!;

    /// <summary>
    /// 更新时间
    /// </summary>
    public int IsRemove { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }


    [ForeignKey("PK_UAID")]
    public UserAccount UserAccount { get; set; } = default!;
}
