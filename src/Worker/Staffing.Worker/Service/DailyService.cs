using System.Text;
using Config;
using Config.CommonModel.Aliyun;
using Config.CommonModel.Business;
using Config.CommonModel.DataScreen;
using Config.CommonModel.ThirdTalentInfo;
using Config.CommonModel.Xbb;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.CommonInterface;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Infrastructure.Proxy;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using MimeKit;
using Newtonsoft.Json;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Noah;
using Staffing.Entity.Staffing;
using Staffing.Core.Service.PostOrder;
using System.Collections;
using static Infrastructure.Proxy.ZhiXiaoErApi;
using Infrastructure.CommonRepository.ZhaoPinYun;
using static Infrastructure.Proxy.WbMoFangApi;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Wordprocessing;
using System.Reflection;
using Config.CommonModel.Certificate;
using NPOI.OpenXmlFormats.Spreadsheet;
using Config.CommonModel;
using DocumentFormat.OpenXml.Spreadsheet;
using System.Collections.Generic;
using Infrastructure.QYWechat;
using Microsoft.EntityFrameworkCore.Internal;
using LinqKit;
using static System.Runtime.CompilerServices.RuntimeHelpers;
using Config.CommonModel.Ndn;


namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class DailyService
{
    private readonly IDbContextFactory<StaffingContext> _staffingContextFactory;
    private readonly LogManager _logger;
    private ConfigManager _config;
    private readonly EMailHelper _eMailHelper;
    private readonly NuoYouKaoApi _nuoYouKaoApi;
    private readonly EsHelper _esHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly NuoPinApi _nuoPinApi;
    private readonly XbbApi _xbbApi;
    private readonly ZPYunRepository _zPYunRepository;
    private readonly WeChatHelper _weChatHelper;
    private readonly AliyunDyplsHelper _aliyunDyplsHelper;
    private readonly TencentMapHelper _tencentMapHelper;
    private readonly ZhiXiaoErApi _zhiXiaoErApi;
    private readonly WbMoFangApi _WbMoFangApi;
    private readonly CommonDicService _commonDicService;
    private readonly CommonUserService _commonUserService;
    private readonly IDbContextFactory<NoahContext> _noahContextFactory;
    private readonly KsPostOrderService _ksPostOrderService;
    private readonly Noah.Aliyun.Storage.IObjectStorage _objectStorageService;
    public DailyService(IDbContextFactory<StaffingContext> staffingContextFactory, XbbApi xbbApi,
    LogManager logger, IOptionsSnapshot<ConfigManager> config, EsHelper esHelper,
    EMailHelper eMailHelper, IHostEnvironment hostingEnvironment, NuoYouKaoApi nuoYouKaoApi, NuoPinApi nuoPinApi, ZPYunRepository zPYunRepository,
    WeChatHelper weChatHelper, AliyunDyplsHelper aliyunDyplsHelper, TencentMapHelper tencentMapHelper, ZhiXiaoErApi zhiXiaoErApi, WbMoFangApi wbMoFangApi,
    CommonDicService commonDicService, CommonUserService commonUserService, IDbContextFactory<NoahContext> noahContextFactory, KsPostOrderService ksPostOrderService,
    Noah.Aliyun.Storage.IObjectStorage objectStorageService)
    {
        _staffingContextFactory = staffingContextFactory;
        _logger = logger;
        _config = config.Value;
        _esHelper = esHelper;
        _eMailHelper = eMailHelper;
        _hostingEnvironment = hostingEnvironment;
        _nuoYouKaoApi = nuoYouKaoApi;
        _nuoPinApi = nuoPinApi;
        _xbbApi = xbbApi;
        _zPYunRepository = zPYunRepository;
        _weChatHelper = weChatHelper;
        _aliyunDyplsHelper = aliyunDyplsHelper;
        _tencentMapHelper = tencentMapHelper;
        _zhiXiaoErApi = zhiXiaoErApi;
        _WbMoFangApi = wbMoFangApi;
        _commonDicService = commonDicService;
        _commonUserService = commonUserService;
        _noahContextFactory = noahContextFactory;
        _ksPostOrderService = ksPostOrderService;
        _objectStorageService = objectStorageService;
    }

    /// <summary>
    /// 清理日志，备份昨日日志
    /// </summary>
    public async Task ClearLogs(CancellationToken cancel)
    {
        await Task.FromResult(1);
        var today = DateTime.Today;
        if (DateTime.Now >= today.AddHours(2) && DateTime.Now <= today.AddHours(3).AddMinutes(15))
        {
            try
            {
                if (cancel.IsCancellationRequested)
                    return;

                if (!MyRedis.Client.SetNx($"clnlg:{today.ToYYYY_MM_DD()}", 1, TimeSpan.FromDays(1)))
                    return;

                // 清理之前日志，只保留最近30天的日志，es日志格式为：IndexFormat = "nkplogs-{0:yyyy.MM.dd}", // 日志索引格式
                var sevenDaysAgo = DateTime.Today.AddDays(-30);
                var esClient = _esHelper.GetClient();

                foreach (var item in Enumerable.Range(0, 29))
                {
                    var index = $"nkplogs-{sevenDaysAgo.AddDays(item * -1):yyyy.MM.dd}";

                    // 检查索引是否存在
                    var existsResponse = esClient.Indices.Exists(index);
                    if (!existsResponse.Exists)
                    {
                        // 如果索引不存在，停止循环
                        break;
                    }

                    // 删除索引
                    var response = esClient.Indices.Delete(index);
                    if (!response.IsValid)
                    {
                        _logger.Error($"删除日志失败： {index}", response.ServerError.Error.Reason);
                    }
                    else
                    {
                        _logger.Info("删除日志成功", $"删除日志成功： {index}");
                    }
                }

            }
            catch (Exception e)
            {
                _ = e;
            }
        }
    }

    /// <summary>
    /// 诺聘企业同步
    /// </summary>
    public async void NoahEntync(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            //redis分布式锁，自动释放
            var lockerKey = $"st:noahentsync";
            using var locker = MyRedis.TryLock(lockerKey);

            if (locker == null)
                return;

            var lastTime = MyRedis.Client.HGet<DateTime?>(RedisKey.NoahEntSync.Key, RedisKey.NoahEntSync.LastTime);
            lastTime = lastTime.HasValue ? lastTime : DateTime.Today.AddDays(-1);

            //调用诺聘接口，调用之前记录下时间，下次从这个时间开始
            var oceTime = DateTime.Now;
            var post = await RetryHelper.Do(async () => await _nuoPinApi.GetPositionIncr(lastTime.Value));
            var resume = await RetryHelper.Do(async () => await _nuoPinApi.GetDeliverIncr(lastTime.Value));

            var hbGroup = post.Select(s => s.EntId).Union(resume.Select(s => s.EntId)).Distinct().ToList();

            //分组批量处理
            var groupIds = hbGroup.Chunk(20);
            foreach (var entIds in groupIds)
            {
                using var _context = _staffingContextFactory.CreateDbContext();

                var reEnt = _context.User_Hr_RecommendEnt.Where(x => (x.Source == HrRecommendEntSource.诺聘平台 || x.Source == HrRecommendEntSource.诺聘网点) && entIds.Contains(x.TEntId)).ToList();
                foreach (var ent in reEnt)
                {
                    var lastPostTime = post.FirstOrDefault(x => x.EntId == ent.TEntId)?.LastTime;
                    var lastDeliverTime = resume.FirstOrDefault(x => x.EntId == ent.TEntId)?.LastTime;

                    if (lastPostTime.HasValue)
                        ent.LastPostTime = lastPostTime.Value;

                    if (lastDeliverTime.HasValue)
                        ent.LastDeliverTime = lastDeliverTime.Value;
                }
                _context.SaveChanges();

                await Task.Delay(20);
            }

            MyRedis.Client.HSet(RedisKey.NoahEntSync.Key, RedisKey.NoahEntSync.LastTime, oceTime.AddMinutes(-10));
        }
        catch (Exception e)
        {
            if (_hostingEnvironment.IsProduction())
                _logger.Error("企业发布职位时间同步错误", Tools.GetErrMsg(e));
        }
    }

    /// <summary>
    /// 计算日活
    /// </summary>
    public void CountDailyUser(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            using var _context = _staffingContextFactory.CreateDbContext();

            var yesterday = DateOnly.FromDateTime(DateTime.Today.AddDays(-1).Date);
            var today = DateOnly.FromDateTime(DateTime.Today);

            var yesterdayData = _context.Rpt_Daily_User.FirstOrDefault(x => x.EventDate == yesterday);
            if (yesterdayData == null)
            {
                yesterdayData = new Rpt_Daily_User
                {
                    CreatedTime = yesterday.ToDateTime(TimeOnly.MinValue),
                    EventDate = yesterday
                };
                _context.Add(yesterdayData);
            }

            //更新昨天的
            if (yesterdayData.CreatedTime < today.ToDateTime(TimeOnly.MinValue))
                CountUser(_context, yesterdayData, yesterday.ToDateTime(TimeOnly.MinValue), today.ToDateTime(TimeOnly.MinValue));

            //更新今天的
            var todayEvent = _context.Rpt_Daily_User
                .FirstOrDefault(x => x.EventDate == today);
            if (todayEvent == null)
            {
                todayEvent = new Rpt_Daily_User
                {
                    CreatedTime = DateTime.Today,
                    EventDate = today
                };
                _context.Add(todayEvent);
            }
            //更新今天的
            CountUser(_context, todayEvent, today.ToDateTime(TimeOnly.MinValue), today.AddDays(1).ToDateTime(TimeOnly.MinValue));

            _context.SaveChanges();
        }
        catch (Exception e)
        {
            _logger.Error("计算日活错误", Tools.GetErrMsg(e));
        }
    }

    /// <summary>
    /// 自动协同项目
    /// </summary>
    public void HrAutoTeam(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            // var can = false;

            // // 每天12点到13点执行
            // var today = DateTime.Today;
            // if (DateTime.Now >= today.AddHours(12) && DateTime.Now <= today.AddHours(14))
            //     if (MyRedis.Client.SetNx($"{RedisKey.Idempotency.HrAutoTeam}1", TimeSpan.FromDays(1)))
            //         can = true;

            // // 每天2点到3点执行
            // if (DateTime.Now >= today.AddHours(2) && DateTime.Now <= today.AddHours(4))
            //     if (MyRedis.Client.SetNx($"{RedisKey.Idempotency.HrAutoTeam}2", TimeSpan.FromDays(1)))
            //         can = true;

            // if (!can)
            //     return;

            using var _context = _staffingContextFactory.CreateDbContext();

            //查找所有开启协同的顾问
            var hrIds = _context.User_Hr.Where(x => x.Status == UserStatus.Active && x.User_Extend.HrAutoTeam == 1)
            .Select(s => s.UserId).ToList();

            if (hrIds.Count == 0)
                return;

            var ut = DateTime.Now.ToUnixTime();
            MyRedis.Client.ZAdd(SubscriptionKey.HrAutoTeam, hrIds.Select(s => new FreeRedis.ZMember(s, ut)).ToArray());
        }
        catch (Exception e)
        {
            _logger.Error("自动协同项目错误", Tools.GetErrMsg(e));
        }
    }

    public async Task GetZxerJobList(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;
            var today = DateTime.Today;
            if (DateTime.Now >= today.AddHours(16.5) && DateTime.Now <= today.AddHours(17.5))
            {
                int page = 1;
                // int limit = 10;
                var ran = new Random(Guid.NewGuid().GetHashCode());
                List<List<ZhiXiaoErModel>> list = new List<List<ZhiXiaoErModel>>();
                while (true)
                {
                    //var zxejoblist = await _zhiXiaoErApi.GetZhiXiaoErJobList(page, limit);
                    //if (zxejoblist == null || zxejoblist.Count == 0)
                    //{
                    //    MyRedis.Client.SAdd(SubscriptionKey.ZddJobList, list);
                    //    break;
                    //}
                    //if (zxejoblist.Count > 0)
                    //{
                    //    list.Add(zxejoblist);                        
                    //}

                    page++;
                    //随机延迟5-10秒
                    await Task.Delay(ran.Next(5000, 10001));
                }
            }
        }
        catch (Exception e)
        {
            _logger.Error("获取职多多岗位订单列表任务出错！", Tools.GetErrMsg(e));
        }
    }

    public async Task GetZxerJobDetail(CancellationToken cancel)
    {
        //处理消息
        List<ZhiXiaoErModel>? msg = null;
        var ran = new Random(Guid.NewGuid().GetHashCode());
        while (!cancel.IsCancellationRequested)
        {
            try
            {
                if (cancel.IsCancellationRequested)
                    return;
                msg = MyRedis.Client.SPop<List<ZhiXiaoErModel>>(SubscriptionKey.ZddJobList);
                if (msg == null || msg.Count == 0)
                {
                    await Task.Delay(5000);
                    continue;
                }

                using var _context = _staffingContextFactory.CreateDbContext();
                List<string?> thirdpostidlist = _context.ThirdParty_Postid_Relation.Where(x => x.Source == PostSouce.职多多).
                    Select(x => x.ThirdPostId).ToList();
                List<string?> zhaopinidlist = new List<string?>();
                msg.ForEach(x =>
                {
                    if (x.status == 1)
                    {
                        zhaopinidlist.Add(x.zhaopinid);
                    }
                });

                foreach (var item in msg)
                {
                    if (item.status == 1)
                    {
                        if (item.zhaopinid != null && item.jobname != null)
                        {
                            var zddjobdetail = await _zhiXiaoErApi.GetZhiXiaoErJobDetail(item.zhaopinid, item.jobname);
                            if (zddjobdetail != null)
                            {
                                MyRedis.Client.SAdd(SubscriptionKey.ZddJobDetail, zddjobdetail);
                            }
                        }
                    }
                    //随机延迟1-20秒
                    await Task.Delay(ran.Next(1000, 20001));
                }

                if (zhaopinidlist.Count > 0 && thirdpostidlist.Count > 0)
                {
                    //判断职多多岗位关系数据库中不在现查职多多缓存中的数据
                    //这些岗位信息需要下架
                    List<string?> except = thirdpostidlist.Except(zhaopinidlist).ToList();
                    MyRedis.Client.SAdd(SubscriptionKey.ZddJobDownList, except);
                }
            }
            catch (Exception e)
            {
                _logger.Error("获取职多多岗位详情任务出错！", Tools.GetErrMsg(e));
            }
        }
    }

    public async Task GetWbMoFangJobList(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;
            var today = DateTime.Today;

            int page = 1;
            int limit = 30;
            var ran = new Random(Guid.NewGuid().GetHashCode());
            List<List<WbMofangPostModel>> list = new List<List<WbMofangPostModel>>();
            while (true)
            {
                var wbjoblist = await _WbMoFangApi.GetWbMofangJobList(page, limit);
                if (wbjoblist == null || wbjoblist.Count == 0)
                {
                    MyRedis.Client.SAdd(SubscriptionKey.WbJobList, list);
                    break;
                }
                if (wbjoblist.Count > 0)
                {
                    list.Add(wbjoblist);
                }

                page++;
                //随机延迟5-10秒
                await Task.Delay(ran.Next(5000, 10001));
            }
        }
        catch (Exception e)
        {
            _logger.Error("获取58魔方岗位订单列表任务出错！", Tools.GetErrMsg(e));
        }
    }

    public async Task GetWbMoFangDetail(CancellationToken cancel)
    {
        List<WbMofangPostModel>? msg = null;
        var ran = new Random(Guid.NewGuid().GetHashCode());
        while (!cancel.IsCancellationRequested)
        {
            try
            {
                if (cancel.IsCancellationRequested)
                    return;
                msg = MyRedis.Client.SPop<List<WbMofangPostModel>>(SubscriptionKey.WbJobList);
                if (msg == null || msg.Count == 0)
                {
                    await Task.Delay(5000);
                    continue;
                }
                using var _context = _staffingContextFactory.CreateDbContext();
                List<string?> thirdpostidlist = _context.ThirdParty_Postid_Relation.Where(x => x.Source == PostSouce.魔方).
                    Select(x => x.ThirdPostId).ToList();
                List<string?> wbjobidlist = new List<string?>();
                msg.ForEach(x =>
                {
                    wbjobidlist.Add(x.jobId);
                });
                //判断第三方岗位关系数据库中不在现查58魔方缓存中的数据
                //这些岗位信息需要下架
                //List<string?> except = thirdpostidlist.Except(wbjobidlist).ToList();
                //MyRedis.Client.SAdd(SubscriptionKey.WbJobDownList, except);

                foreach (var item in msg)
                {
                    if (item.jobId != null && item.projectId != null)
                    {
                        var wbjobdetail = await _WbMoFangApi.GetWbMofangDetailJob(item.projectId);
                        if (wbjobdetail != null)
                        {
                            // 获取源对象和目标对象的类型
                            Type sourceType = item.GetType();
                            Type destinationType = wbjobdetail.GetType();

                            // 遍历源对象的所有属性
                            foreach (PropertyInfo sourceProperty in sourceType.GetProperties())
                            {
                                // 获取目标对象中同名的属性
                                PropertyInfo destinationProperty = destinationType.GetProperty(sourceProperty.Name)!;
                                if (destinationProperty != null && destinationProperty.CanWrite &&
                                    destinationProperty.PropertyType == sourceProperty.PropertyType)
                                {
                                    // 将源对象属性的值赋给目标对象的属性
                                    destinationProperty.SetValue(wbjobdetail, sourceProperty.GetValue(item));
                                }
                            }
                            MyRedis.Client.SAdd(SubscriptionKey.WbJobDetail, wbjobdetail);
                        }
                    }
                    //随机延迟1-5秒
                    //await Task.Delay(ran.Next(1000, 5001));
                }
            }
            catch (Exception e)
            {
                _logger.Error("获取58魔方详情任务出错！", Tools.GetErrMsg(e));
            }
        }
    }

    //public async Task GetThirdPartyJobList<T>(Func<T> func, CancellationToken cancel)
    //{
    //    try
    //    {
    //        if (cancel.IsCancellationRequested)
    //            return;

    //        List<List<T>> list = new List<List<T>>();
    //        while (true)
    //        {
    //            //Func<T> func = new Func<T>();
    //            //var wbjoblist = await _WbMoFangApi.GetWbMofangJobList(page, limit);
    //            //if (wbjoblist == null || wbjoblist.Count == 0)
    //            //{
    //            //    MyRedis.Client.SAdd(SubscriptionKey.WbJobList, list);
    //            //    break;
    //            //}
    //            //if (wbjoblist.Count > 0)
    //            //{
    //            //    list.Add(wbjoblist);
    //            //}

    //            //page++;
    //            //随机延迟5-10秒
    //            //await Task.Delay(ran.Next(5000, 10001));
    //        }
    //    }
    //    catch (Exception e)
    //    {
    //        _logger.Error("获取第三方岗位订单列表任务出错！", Tools.GetErrMsg(e));
    //    }
    //}


    public async Task GetThirdPartyJob<T>(CancellationToken cancel)
    {
        List<T>? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            try
            {
                if (cancel.IsCancellationRequested)
                    return;

                // 判断哪个第三方岗位平台
                // todo

                msg = MyRedis.Client.SPop<List<T>>(SubscriptionKey.ThirdPartyJobList);
                if (msg == null || msg.Count == 0)
                {
                    await Task.Delay(5000);
                    continue;
                }

                foreach (var item in msg)
                {
                    if (item != null)
                    {
                        MyRedis.Client.SAdd(SubscriptionKey.ThirdPartyJobDetail, item);
                    }
                    await Task.Delay(500);
                }
            }
            catch (Exception e)
            {
                _logger.Error($"获取第三方岗位订单列表任务出错！", Tools.GetErrMsg(e));
            }
        }
    }

    private void CountUser(StaffingContext _context, Rpt_Daily_User data, DateTime begin, DateTime end)
    {
        data.NewUser = _context.User
        .Where(x => x.CreatedTime >= begin && x.CreatedTime < end)
        .Select(s => new { a = 1 }).Count();

        data.NewSeeker = _context.User_Seeker
        .Where(x => x.CreatedTime >= begin && x.CreatedTime < end)
        .Select(s => new { a = 1 }).Count();

        data.NewHr = _context.User_Hr
        .Where(x => x.CreatedTime >= begin && x.CreatedTime < end)
        .Select(s => new { a = 1 }).Count();

        data.ActiveUser = _context.User_Login_Record.AsNoTracking()
        .Where(x => x.CreatedTime >= begin && x.CreatedTime < end)
        .GroupBy(g => g.UserId).Count();

        data.ActiveHr = _context.User_Login_Record.AsNoTracking()
        .Where(x => x.CreatedTime >= begin && x.CreatedTime < end
        && (x.Type == ClientType.HrWeb || x.Type == ClientType.HrApplet))
        .GroupBy(g => g.UserId).Count();

        data.ActiveSeeker = _context.User_Login_Record.AsNoTracking()
        .Where(x => x.CreatedTime >= begin && x.CreatedTime < end
        && (x.Type == ClientType.SeekerApplet || x.Type == ClientType.SeekerKsApplet))
        .GroupBy(g => g.UserId).Count();

        data.ActiveInterviewer = _context.Token_Access.AsNoTracking()
        .Where(x => x.CreatedTime >= begin && x.CreatedTime < end
        && x.Type == TokenType.用户端 && x.Client == ClientType.InterviewerApplet)
        .GroupBy(g => g.UserId).Count();

        data.DeliverResume = _context.Post_Delivery.AsNoTracking()
        .Where(x => x.CreatedTime >= begin && x.CreatedTime < end)
        .Count();

        data.AllHrHadProj = _context.Project_Team.Where(x => x.CreatedTime < end)
        .Select(s => s.HrId).Distinct().Count();

        data.CreatedTime = DateTime.Now;
    }

    /// <summary>
    /// 错误日志发送邮件通知
    /// </summary>
    /// <param name="cancel"></param>
    public void SendWarningMail(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            // 通过redis记录上次发送报警邮件的时间，如果距离航次发送时间小于5分钟，则不发送
            var warningMailLastKey = "st:warningmaillast";
            var lastSendTime = MyRedis.Client.Get<DateTime?>(warningMailLastKey) ?? DateTime.Now.AddMinutes(-5);
            if (DateTime.Now.Subtract(lastSendTime).TotalMinutes < 5)
                return;

            // redis记录本次发送报警邮件的时间
            MyRedis.Client.Set(warningMailLastKey, DateTime.Now, TimeSpan.FromMinutes(10));

            var redisWarningMailKey = "st:warningmail";
            // redis查询上次检测到的错误日志时间
            var lastTime = MyRedis.Client.Get<DateTime?>(redisWarningMailKey) ?? DateTime.Now.AddHours(-24);

            // es查询nkplog-*索引，不使用模型，查找message、fields.Response、fields.Request、fields.RequestPath、fields.RequestMethod、fields.StatusCode字段，根据时间倒序，取前10个，只查询level为error的日志
            var esClient = _esHelper.GetClient();
            var response = esClient.Search<Dictionary<string, object>>(s => s
                .Index("nkplogs-*")
                .Query(q => q
                    .Bool(b => b
                        .Filter(f => f
                            .DateRange(r => r
                                .Field("@timestamp")
                                .GreaterThan(lastTime)
                            )
                        )
                        .Must(m => m
                            .Term(t => t
                                .Field("level.keyword")
                                .Value("Error")
                            )
                        )
                    )
                )
                .Sort(so => so
                    .Descending("@timestamp")
                )
                .Size(10)
            );

            if (!response.IsValid)
                return;

            var logs = response.Documents.ToList();
            if (logs.Count == 0)
                return;

            var emailDetail = new StringBuilder();
            foreach (var item in logs)
            {
                // if (item.TryGetValue("@timestamp", out var dtStr) && DateTime.TryParse(dtStr.ToString(), out var dt)) emailDetail.AppendLine($"时间：{dt:yyyy-MM-dd HH:mm:ss}");
                if (item.TryGetValue("@timestamp", out var dtStr) && DateTime.TryParse(dtStr.ToString(), out var dt))
                {
                    var localTime = dt.ToLocalTime();
                    emailDetail.AppendLine($"时间：{localTime:yyyy-MM-dd HH:mm:ss}");
                }
                if (item.TryGetValue("fields", out var fields) && fields is IDictionary<string, object> fieldsDict)
                {
                    if (fieldsDict.TryGetValue("User", out var user)) emailDetail.AppendLine($"用户：{user}");
                    if (fieldsDict.TryGetValue("RequestPath", out var path)) emailDetail.AppendLine($"请求路径：{path}");
                    if (fieldsDict.TryGetValue("RequestMethod", out var method)) emailDetail.AppendLine($"请求方法：{method}");
                    if (fieldsDict.TryGetValue("Request", out var req)) emailDetail.AppendLine($"请求内容：{req}");
                    if (fieldsDict.TryGetValue("Response", out var resp)) emailDetail.AppendLine($"响应内容：{resp}");
                    if (fieldsDict.TryGetValue("StatusCode", out var code)) emailDetail.AppendLine($"状态码：{code}");
                }
                if (item.TryGetValue("message", out var msg)) emailDetail.AppendLine($"错误信息：{msg}");
                if (item.TryGetValue("exception", out var ex)) emailDetail.AppendLine($"错误详情：{ex}");

                emailDetail.AppendLine();
            }

            var eMails = _config.Warning?.EMails;
            if (eMails == null || eMails.Count == 0)
                return;

            // redis记录本次检测到的错误日志的最大时间
            var maxDt = logs.Select(m => DateTime.TryParse(m["@timestamp"]?.ToString(), out var dt) ? (DateTime?)dt : null).Max();
            if (maxDt == null)
                return;
            MyRedis.Client.Set(redisWarningMailKey, maxDt);

            var toAds = eMails.Select(s => new MailboxAddress(s, s)).ToList();

            var env = _hostingEnvironment.IsProduction() ? string.Empty : "(测试)";

            var message = new MimeMessage
            {
                Subject = $"诺零工接口出错{env}",
                Body = new BodyBuilder
                {
                    TextBody = emailDetail.ToString()
                }.ToMessageBody()
            };

            _ = _eMailHelper.SendEmailAsync(message, toAds);
        }
        catch (Exception e)
        {
            _logger.Error("发送报警邮件失败", Tools.GetErrMsg(e));
        }
    }

    /// <summary>
    /// 首页第三方数据统计
    /// </summary>
    /// <param name="cancel"></param>
    public async Task HomePageThirdPartyData(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            try
            {
                //获取诺聘看板
                var nuopinPostPanel = await _nuoPinApi.GetPostPanel();
                if (nuopinPostPanel != null && nuopinPostPanel.PostsProceedNumber > 0)
                    MyRedis.Client.HSet(RedisKey.HomePageTPData.Key, RedisKey.HomePageTPData.NuoPinPost, nuopinPostPanel);
            }
            catch (Exception e)
            {
                _logger.Error("首页获取诺聘看板失败", Tools.GetErrMsg(e));
            }

            try
            {
                //获取诺优考数据
                var nuoyoukaoProject = await _nuoYouKaoApi.GetProjectPanel();
                if (nuoyoukaoProject != null)
                    MyRedis.Client.HSet(RedisKey.HomePageTPData.Key, RedisKey.HomePageTPData.NuoyoukaoProject, nuoyoukaoProject);
            }
            catch (Exception e)
            {
                _logger.Error("首页获取诺优考数据失败", Tools.GetErrMsg(e));
            }

            using var _context = _staffingContextFactory.CreateDbContext();
            try
            {
                //获取诺聘金牌顾问
                var hrIds = _context.Post_Team.Where(x => x.Project_Team.HrId == Constants.PlatformHrId && x.Show)
                .Select(s => s.Post.Project.HrId).Distinct().OrderBy(o => EF.Functions.Random()).Take(10).ToList();

                var nuopinAdvisers = _context.User_Hr.Where(x => hrIds.Contains(x.UserId))
                .Select(s => new NuopinAdvisersModel
                {
                    AdviserId = s.UserId,
                    Avatar = s.Avatar,
                    Name = s.NickName
                }).ToList();

                MyRedis.Client.HSet(RedisKey.HomePageTPData.Key, RedisKey.HomePageTPData.NuopinAdvisers, nuopinAdvisers);
            }
            catch (Exception e)
            {
                _logger.Error("首页获取诺聘金牌顾问出错", Tools.GetErrMsg(e));
            }
        }
        catch (Exception e)
        {
            _logger.Error("首页第三方数据统计报错", Tools.GetErrMsg(e));
        }
    }

    /// <summary>
    /// 销帮帮用户同步
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task XbbUserSync(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            if (!MyRedis.Client.SetNx(RedisKey.Xbb.TaskUserCk, 1, 3600))
                return;

            GetUserListResponse? xbbUsers = null;
            var page = 1;
            while (true)
            {
                using (var _context = _staffingContextFactory.CreateDbContext())
                {
                    xbbUsers = await _xbbApi.GetUserList(new GetUserList
                    {
                        page = page,
                        pageSize = 100
                    });

                    var userIds = xbbUsers?.userList?.Select(s => s.userId).Distinct().ToList() ?? new List<string?>();


                    var localUsers = _context.Xbb_User.Where(x => userIds.Contains(x.Id)).ToList();

                    foreach (var item in xbbUsers?.userList ?? new List<GetUserListInfo>())
                    {
                        var localItem = localUsers.FirstOrDefault(x => x.Id == item.userId);
                        if (localItem == null)
                        {
                            localItem = new Xbb_User
                            {
                                Id = item.userId ?? string.Empty
                            };
                            _context.Add(localItem);
                        }
                        localItem.Name = item?.name ?? string.Empty;
                    }
                    _context.SaveChanges();
                }

                page++;

                if (xbbUsers == null || xbbUsers.userList == null || xbbUsers.userList.Count < 1 || page > 999)
                    break;

                await Task.Delay(1000);
            }

            MyRedis.Client.Del(RedisKey.Xbb.TaskUserCk);
        }
        catch (Exception e)
        {
            _logger.Error("销帮帮用户同步报错", Tools.GetErrMsg(e));
        }
    }

    // /// <summary>
    // /// 销帮帮销售机会同步
    // /// </summary>
    // /// <param name="cancel"></param>
    // /// <returns></returns>
    // public async Task XbbXsjhSync(CancellationToken cancel)
    // {
    //     try
    //     {
    //         if (cancel.IsCancellationRequested)
    //             return;

    //         if (!MyRedis.Client.SetNx(RedisKey.Xbb.TaskXsjhCk, 1, 600))
    //             return;

    //         using var _context = _staffingContextFactory.CreateDbContext();

    //         var lastTime = _context.Xbb_Xsjh.OrderByDescending(o => o.XbbCreatedTime).Select(s => (long?)s.XbbCreatedTime).FirstOrDefault() ?? 0;

    //         if (lastTime <= 0)
    //             lastTime = new DateTime(2023, 1, 1).ToUnixTime();

    //         var newXsjh = await _xbbApi.GetXsjh(new GetXsjh
    //         {
    //             page = 1,
    //             pageSize = 100,
    //             formId = 2881864,
    //             sortMap = new GetXbbBaseSort
    //             {
    //                 field = "addTime",
    //                 sort = "acs"
    //             },
    //             conditions = new List<object>
    //             {
    //                 new GetXbbBaseConditions<long>
    //                 {
    //                     attr = "addTime",
    //                     symbol="greaterequal",
    //                     value = new List<long> { lastTime }
    //                 }
    //             }
    //         });

    //         if (newXsjh == null)
    //             return;

    //         var newIds = newXsjh.list?.Select(s => s.dataId)?.ToList() ?? new List<long>();

    //         var xbbXsjhs = _context.Xbb_Xsjh.Where(x => newIds.Contains(x.Id)).ToList();

    //         foreach (var item in newXsjh.list ?? new List<GetXsjhDetail>())
    //         {
    //             if (string.IsNullOrWhiteSpace(item.data?.text_30))
    //                 continue;

    //             var localItem = xbbXsjhs.FirstOrDefault(x => x.Id == item.dataId);
    //             if (localItem == null)
    //             {
    //                 localItem = new Xbb_Xsjh
    //                 {
    //                     Id = item.dataId
    //                 };
    //                 _context.Add(localItem);
    //             }
    //             localItem.Name = item.data?.text_1;
    //             localItem.CustomerName = item.data?.text_30;
    //             localItem.Creator = item.data?.creatorId;
    //             localItem.XbbUpdatedTime = item.updateTime;
    //             localItem.XbbCreatedTime = item.addTime;
    //         }

    //         //同步企业线索池
    //         var entNames = newXsjh.list?.Select(s => s.data?.text_30)?.ToList() ?? new List<string?>();
    //         var reEnts = _context.User_Hr_RecommendEnt.Where(x => entNames.Contains(x.EntName)).ToList();
    //         foreach (var item in reEnts)
    //         {
    //             var xb = newXsjh.list?.FirstOrDefault(x => x.data?.text_30?.Trim() == item.EntName.Trim());
    //             item.XbbXsjhName = xb?.data?.text_1;
    //             item.XbbXsjhUId = xb?.data?.creatorId;
    //         }

    //         _context.SaveChanges();

    //         MyRedis.Client.Del(RedisKey.Xbb.TaskXsjhCk);
    //     }
    //     catch (Exception e)
    //     {
    //         _logger.Error("销帮帮销售机会同步报错", Tools.GetErrMsg(e));
    //     }
    // }

    public async Task XbbSync(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            if (!MyRedis.Client.SetNx(RedisKey.Xbb.TaskCk, 1, 600))
                return;

            var lastTime = Constants.DefaultTime;
            using (var _context = _staffingContextFactory.CreateDbContext())
            {
                var lt = _context.Xbb_Contract.OrderByDescending(o => o.NoahUpdatedTime).Select(s => (DateTime?)s.NoahUpdatedTime).FirstOrDefault();
                lastTime = lt ?? Constants.DefaultTime;
            }

            using var _noahContext = _noahContextFactory.CreateDbContext();

            var linq = from a in _noahContext.Contract
                       join b in _noahContext.ContractProductRelation.GroupBy(g => g.ContractId)
                       .Select(s => new { ContractId = s.Key, ProductId = s.Max(m => m.ProductId) })
                       on a.ContractId equals b.ContractId into c
                       from d in c.DefaultIfEmpty()
                       where a.UpdateTime >= lastTime
                       orderby a.UpdateTime
                       select new
                       {
                           a.ContractId,
                           a.Nature,
                           a.State,
                           a.StartTime,
                           a.DueTime,
                           a.Item,
                           a.Appendix,
                           a.ContractCode,
                           a.Type,
                           a.SignDate,
                           a.UpdateTime,
                           a.CreateTime,
                           CustomerName = a.Customer.Name,
                           SignerName = a.SignerModel.Name,
                           SignerEmployeeCode = a.SignerModel.HrNo,
                           a.SznyCode,
                           d.ProductId
                       };

            var noahContracts = linq.ToList();

            var products = _noahContext.VProduct.AsNoTracking().ToList();

            var group = noahContracts.Chunk(100);

            foreach (var item in group)
            {
                using (var _context = _staffingContextFactory.CreateDbContext())
                {
                    var cno = item.Select(s => s.ContractId).ToList();
                    var xbbContracts = _context.Xbb_Contract.Where(x => cno.Contains(x.NoahId)).ToList();
                    var codes = new List<string>();
                    foreach (var contract in item)
                    {
                        var code = contract.ContractCode;
                        if (string.IsNullOrEmpty(code))
                            continue;

                        codes.Add(code);

                        List<GetProjectListInfoDataFile> files = new List<GetProjectListInfoDataFile>();
                        try
                        {
                            files = ServiceStack.Text.JsonSerializer.DeserializeFromString<List<GetProjectListInfoDataFile>>(contract.Appendix);
                        }
                        catch { }

                        var xbbContract = xbbContracts.FirstOrDefault(x => x.NoahId == contract.ContractId);
                        if (xbbContract == null)
                        {
                            xbbContract = new Xbb_Contract { NoahId = contract.ContractId };
                            _context.Add(xbbContract);
                        }
                        xbbContract.NoahCode = contract.SznyCode;
                        xbbContract.UpdatedTime = DateTime.Now;
                        xbbContract.BeginTime = contract.StartTime ?? Constants.DefaultTime;
                        xbbContract.EndTime = contract.DueTime ?? Constants.DefaultTime;
                        xbbContract.Category = contract.Type;
                        xbbContract.Code = contract.ContractCode;
                        xbbContract.ContractUrl = files?.Select(s => s.attachIndex ?? string.Empty).ToList() ?? new List<string>();
                        xbbContract.CustomerName = contract.CustomerName;
                        xbbContract.Name = contract.Item;
                        xbbContract.Nature = contract.Nature;
                        xbbContract.Status = contract.State;
                        xbbContract.NoahUpdatedTime = contract.UpdateTime ?? Constants.DefaultTime;
                        xbbContract.NoahCreatedTime = contract.CreateTime ?? Constants.DefaultTime;
                        xbbContract.Signatory = contract.SignerName;
                        xbbContract.SignerEmployeeCode = contract.SignerEmployeeCode;
                        xbbContract.SignDate = contract.SignDate;
                        xbbContract.ProductId = contract.ProductId;
                        xbbContract.ProductName = products.FirstOrDefault(x => x.ProductCode == contract.ProductId)?.Name;
                    }
                    _context.SaveChanges();
                    await Task.Delay(20);
                    // 更新回执状态
                    MyRedis.Client.SAdd(SubscriptionKey.ContractUpdatedProjectCallBack, codes.ToArray());
                }
            }

            MyRedis.Client.Del(RedisKey.Xbb.TaskCk);
        }
        catch (Exception e)
        {
            _logger.Error("数字诺亚合同同步报错", Tools.GetErrMsg(e));
        }
    }

    public async Task NoahXsjhSync(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            if (!MyRedis.Client.SetNx(RedisKey.Xbb.TaskNoahXsjhCk, 1, 600))
                return;

            var lastTime = Constants.DefaultTime;
            using (var _context = _staffingContextFactory.CreateDbContext())
            {
                var lt = _context.Xbb_XiaoShouJh.OrderByDescending(o => o.XbbUpdatedTime).Select(s => (DateTime?)s.XbbUpdatedTime).FirstOrDefault();
                lastTime = lt ?? Constants.DefaultTime;
            }

            using var _noahContext = _noahContextFactory.CreateDbContext();

            var noahXsjh = _noahContext.OpportUnity.Where(x => x.UpdateTime >= lastTime).OrderBy(o => o.UpdateTime)
            .Select(s => new
            {
                s.OpportunityId,
                s.OpportunityName,
                s.CustomerId,
                CustomerName = s.Customer.Name ?? string.Empty,
                s.CreateBy,
                CreateName = s.CreateByModel.Name,
                s.CreateByModel.HrNo,
                s.UpdateTime,
                s.CreateTime
            }).ToList();

            var group = noahXsjh.Chunk(100);

            foreach (var newXsjh in group)
            {
                using (var _context = _staffingContextFactory.CreateDbContext())
                {
                    if (newXsjh == null)
                        return;

                    var newIds = newXsjh.Select(s => s.OpportunityId).ToList() ?? new List<long>();

                    var xbbXsjhs = _context.Xbb_XiaoShouJh.Where(x => newIds.Contains(x.Id)).ToList();

                    foreach (var item in newXsjh)
                    {
                        if (string.IsNullOrWhiteSpace(item.CustomerName))
                            continue;

                        var localItem = xbbXsjhs.FirstOrDefault(x => x.Id == item.OpportunityId);
                        if (localItem == null)
                        {
                            localItem = new Xbb_XiaoShouJh
                            {
                                Id = item.OpportunityId
                            };
                            _context.Add(localItem);
                        }
                        localItem.Name = item.OpportunityName;
                        localItem.CustomerName = item.CustomerName;
                        localItem.Creator = item.CreateName;
                        localItem.CreatorId = item.CreateBy;
                        localItem.CreatorNumber = item.HrNo;
                        localItem.XbbUpdatedTime = item.UpdateTime ?? Constants.DefaultTime;
                        localItem.XbbCreatedTime = item.CreateTime ?? Constants.DefaultTime;
                    }

                    //同步企业线索池
                    var entNames = newXsjh.Select(s => s.CustomerName).ToList();
                    var reEnts = _context.User_Hr_RecommendEnt.Where(x => entNames.Contains(x.EntName)).ToList();
                    foreach (var item in reEnts)
                    {
                        var xb = newXsjh.FirstOrDefault(x => x.CustomerName.Trim() == item.EntName.Trim());
                        item.XbbXsjhName = xb?.OpportunityName;
                        item.XbbXsjhUId = xb?.CreateBy;
                        item.XbbXsjhUName = xb?.CreateName;
                    }

                    _context.SaveChanges();
                }

                //发送消息推送顾问
                MyRedis.Client.LPush(SubscriptionKey.XsjhToHr, newXsjh.Select(s => new Sub_XsjhToHr
                {
                    OpportunityId = s.OpportunityId,
                    CustomerId = s.CustomerId ?? 0
                }).ToArray());

                await Task.Delay(20);
            }

            MyRedis.Client.Del(RedisKey.Xbb.TaskNoahXsjhCk);
        }
        catch (Exception e)
        {
            _logger.Error("数字诺亚销售机会同步报错", Tools.GetErrMsg(e));
        }
    }

    // /// <summary>
    // /// 销帮帮同步（废弃）
    // /// </summary>
    // /// <param name="cancel"></param>
    // public async Task XbbSync(CancellationToken cancel)
    // {
    //     try
    //     {
    //         if (cancel.IsCancellationRequested)
    //             return;

    //         if (!MyRedis.Client.SetNx(RedisKey.Xbb.TaskCk, 1, 600))
    //             return;

    //         //同步数据字典
    //         var dic = await _xbbApi.GetForm(new GetForm
    //         {
    //             formId = 52136
    //         });

    //         var alldic = dic.explainList?.SelectMany(x => x.items).ToList();
    //         MyRedis.Client.Set(RedisKey.Xbb.HtDic, alldic);

    //         long lastTime = 0;
    //         using (var _context = _staffingContextFactory.CreateDbContext())
    //         {
    //             var lt = _context.Xbb_Contract.OrderByDescending(o => o.XbbUpdatedTime).Select(s => (long?)s.XbbUpdatedTime).FirstOrDefault();
    //             lastTime = lt ?? 0;
    //         }

    //         GetProjectListResponse? contracts = null;
    //         var page = 1;
    //         while (true)
    //         {
    //             using (var _context = _staffingContextFactory.CreateDbContext())
    //             {
    //                 contracts = await _xbbApi.GetProjectList(new GetProjectList
    //                 {
    //                     formId = 52136,
    //                     pageSize = 100,
    //                     page = page,
    //                     sortMap = new GetXbbBaseSort
    //                     {
    //                         field = "updateTime",
    //                         sort = "asc"
    //                     },
    //                     conditions = new List<object>
    //                         {
    //                             new GetXbbBaseConditions<long>
    //                             {
    //                                 attr = "updateTime",
    //                                 symbol="greaterequal",
    //                                 value = new List<long> { lastTime }
    //                             }
    //                         }
    //                 });

    //                 var userIds = contracts?.list?.Select(s => s.data?.text_8).Distinct().ToList();

    //                 var users = await _xbbApi.GetUserList(new GetUserList
    //                 {
    //                     page = 1,
    //                     pageSize = 100,
    //                     userIdIn = userIds ?? new List<string?>()
    //                 });

    //                 //业务处理
    //                 foreach (var item in contracts?.list ?? new List<GetProjectListInfo>())
    //                 {
    //                     List<GetProjectListInfoDataFile> files = new List<GetProjectListInfoDataFile>();
    //                     try
    //                     {
    //                         files = ServiceStack.Text.JsonSerializer.DeserializeFromString<List<GetProjectListInfoDataFile>>(item.data?.file_1);
    //                     }
    //                     catch { }

    //                     var code = item.data?.serialNo ?? string.Empty;
    //                     var xbbContract = _context.Xbb_Contract.FirstOrDefault(x => x.Code == code);
    //                     if (xbbContract == null)
    //                     {
    //                         xbbContract = new Xbb_Contract { Code = code };
    //                         _context.Add(xbbContract);
    //                     }

    //                     xbbContract.UpdatedTime = DateTime.Now;
    //                     xbbContract.BeginTime = item.data?.date_3.ToDateTime() ?? Constants.DefaultTime;
    //                     xbbContract.EndTime = item.data?.date_2.ToDateTime() ?? Constants.DefaultTime;
    //                     xbbContract.Category = alldic?.FirstOrDefault(x => x.value == item.data?.text_14)?.text ?? string.Empty;
    //                     xbbContract.Code = item.data?.serialNo ?? string.Empty;
    //                     xbbContract.ContractUrl = files?.Select(s => s.attachIndex ?? string.Empty).ToList() ?? new List<string>();
    //                     xbbContract.CustomerName = item.data?.text_56 ?? string.Empty;
    //                     xbbContract.Name = item.data?.text_1 ?? string.Empty;
    //                     xbbContract.Nature = alldic?.FirstOrDefault(x => x.value == item.data?.text_16)?.text ?? string.Empty;
    //                     xbbContract.Status = item.data?.text_6 ?? string.Empty;
    //                     xbbContract.XbbUpdatedTime = item.updateTime;
    //                     xbbContract.XbbCreatedTime = item.addTime;
    //                     xbbContract.Signatory = users?.userList?.FirstOrDefault(x => x.userId == item.data?.text_8)?.name ?? string.Empty;
    //                 }
    //                 _context.SaveChanges();
    //             }

    //             page++;
    //             if (contracts == null || contracts.list == null || contracts.list.Count <= 1 || page > 9999)
    //                 break;
    //             await Task.Delay(1000);
    //         }

    //         MyRedis.Client.Del(RedisKey.Xbb.TaskCk);
    //     }
    //     catch (Exception e)
    //     {
    //         _logger.Error("销帮帮同步报错", Tools.GetErrMsg(e));
    //     }
    // }

    /// <summary>
    /// 平台各种数量统计
    /// </summary>
    /// <param name="cancel"></param>
    public void PlatformCount(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            using var _context = _staffingContextFactory.CreateDbContext();

            try
            {
                //获取平台端顾问数量
                var advisers = _context.User_Hr.Count(x => x.Status == UserStatus.Active);
                if (advisers > 0)
                    MyRedis.Client.HSet(RedisKey.PlatformCount.Key, RedisKey.PlatformCount.Adviser, advisers);

                //获取平台端用户数量
                var talent = _context.User_Seeker.Count(x => x.Status == UserStatus.Active);
                if (talent > 0)
                    MyRedis.Client.HSet(RedisKey.PlatformCount.Key, RedisKey.PlatformCount.Talent, talent);

                //获取平台端用户数量
                var resumes = _context.User_Hr.Where(x => x.Status == UserStatus.Active).Sum(s => s.User_Num.VirtualTalent);
                if (resumes > 0)
                    MyRedis.Client.HSet(RedisKey.PlatformCount.Key, RedisKey.PlatformCount.VirtualTalent, resumes);
            }
            catch (Exception e)
            {
                _logger.Error("平台各种数量统计失败", Tools.GetErrMsg(e));
            }
        }
        catch (Exception e)
        {
            _logger.Error("平台各种数量统计报错", Tools.GetErrMsg(e));
        }
    }

    /// <summary>
    /// 数据大屏首页数据
    /// </summary>
    /// <param name="cancel"></param>
    public async Task HrDataCenterHomePage(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            var mainKey = RedisKey.DataScreen.Key;

            using var _context = _staffingContextFactory.CreateDbContext();

            var hour24ago = DateTime.Now.AddHours(-24);

            //入住顾问
            var advisers = _context.User_Hr.Count(x => x.Status == UserStatus.Active);

            //在线顾问
            var onlineAdvisers = _context.User_Hr.Count(x => x.Status == UserStatus.Active && x.User_Extend.LoginTime > hour24ago);

            //平台人才总数
            var seekers = MyRedis.Client.HGet<int>(RedisKey.PlatformCount.Key, RedisKey.PlatformCount.Talent);

            //平台人简历总数
            var resumes = MyRedis.Client.HGet<int>(RedisKey.PlatformCount.Key, RedisKey.PlatformCount.VirtualTalent);

            //项目总数
            //var project = _context.Project.Where(w => w.Posts.Any(a => a.Status == PostStatus.发布中)).GroupBy(g => g.Status).Select(s => new
            //{
            //    Status = s.Key,
            //    Ct = s.Count()
            //}).ToList();
            var ProOngoingCount = _context.Project.Where(w => w.Posts.Any(a => a.Status == PostStatus.发布中) && w.Status == ProjectStatus.已上线).Count();
            var ProArcCount = _context.Project.Where(x => x.Status == ProjectStatus.归档).Count();

            //入驻公司
            var ents = _context.Enterprise_Org_User.Where(x => x.Enterprise_Org.Enterprise.Status == EnterpriseStatus.Active
            && x.User_Hr.Status == UserStatus.Active).GroupBy(g => g.Enterprise_Org.EntId)
            .Count();

            //入驻集团
            var entGroups = _context.Enterprise.Where(x => x.Status == EnterpriseStatus.Active
            && x.GroupType == EnterpriseGroupType.MainGroup).Count();

            var dataScreenInfo = new DataScreenInfo
            {
                AdvCount = advisers,
                AdvOnlineCount = onlineAdvisers,
                TalentCount = seekers + resumes,
                //ProOngoingCount = project.FirstOrDefault(x => x.Status == ProjectStatus.已上线)?.Ct ?? 0,
                ProOngoingCount = ProOngoingCount,
                //ProArcCount = project.FirstOrDefault(x => x.Status != ProjectStatus.已上线)?.Ct ?? 0,
                ProArcCount = ProArcCount,
                EntCount = ents,
                GroupEntCount = entGroups
            };
            MyRedis.Client.HSet(mainKey, RedisKey.DataScreen.InfoCount, dataScreenInfo);

            await Task.Delay(500);

            //规模最大的机构
            var topEnt = _context.User_Hr.Where(x => x.Status == UserStatus.Active)
            .GroupBy(g => new { g.Enterprise.EntId, g.Enterprise.Name })
            .OrderByDescending(o => o.Count()).Take(3)
            .Select(s => new EntAdvRankingInfo
            {
                EntId = s.Key.EntId,
                EntName = s.Key.Name,
                AdvCount = s.Count()
            }).ToList();
            MyRedis.Client.HSet(mainKey, RedisKey.DataScreen.EntAdvRanking, topEnt);

            //人才储备排行榜
            var subDepts = _context.Dd_Dept.Where(x => x.ParentId == 1)
            .Select(s => new EntTalRankingInfo
            {
                OrgId = s.DdDeptId,
                OrgName = s.Name,
                OrgLevel = s.Level
            }).ToList();

            foreach (var item in subDepts)
            {
                item.TalentCount = _context.User_Hr.Where(x => x.Status == UserStatus.Active
                && x.User.Dd_User.Dd_User_Dept.Any(c => c.Dd_Dept.Level.StartsWith(item.OrgLevel ?? string.Empty)))
                .Sum(c => c.User_Num.Talent + c.User_Num.VirtualTalent);

                item.OrgLevel = null;
                await Task.Delay(20);
            }

            subDepts = subDepts.OrderByDescending(o => o.TalentCount).ThenBy(o => o.OrgName).ToList();

            MyRedis.Client.HSet(mainKey, RedisKey.DataScreen.EntTalRanking, subDepts);

            await Task.Delay(500);

            //市
            var regions = new List<string> { "石家庄", "邯郸", "邢台", "保定", "张家口" };
            MyRedis.Client.HSet(mainKey, RedisKey.DataScreen.RegionEntList, regions);

            await Task.Delay(500);

            //各市包含项目数量
            var regionProjs = _context.Dic_Region.Where(x => x.ParentId == "13")
            .Select(s => new RegionProjectInfo
            {
                RegionId = s.Id,
                RegionName = s.Name
            }).ToList();

            var regionProjIds = regionProjs.Select(s => s.RegionId).ToList();

            var projs = _context.Project_Region.Where(x => regionProjIds.Contains(x.RegionId))
            .GroupBy(g => g.RegionId)
            .Select(s => new
            {
                s.Key,
                Count = s.Count()
            }).ToList();

            var regionProjResults = new List<RegionProjectInfo>();
            foreach (var item in regionProjs)
            {
                item.Count = projs.Where(x => x.Key == item.RegionId).FirstOrDefault()?.Count ?? 0;
            }

            MyRedis.Client.HSet(mainKey, RedisKey.DataScreen.CityProjectList, regionProjs);

            await Task.Delay(500);

            //实时播报
            var talentTop30 = _context.User_Hr.Where(x => x.Status == UserStatus.Active)
            .OrderByDescending(o => o.User_Num.Talent).Take(30)
            .Select(s => new AdvTalRankingInfo
            {
                EntName = s.Enterprise.Name,
                HrName = s.NickName,
                Talent = s.User_Num.Talent,
                Type = AdvTalRankingType.人才储备累计,
                UserId = s.UserId,
                VirtualTalent = s.User_Num.VirtualTalent
            }).ToList();

            var talentYesterdayTop30Hrs = _context.Talent_Platform.Where(x => x.CreatedTime > hour24ago)
            .GroupBy(g => g.HrId)
            .OrderByDescending(o => o.Count()).Take(30)
            .Select(s => new { HrId = s.Key, Ct = s.Count() }).ToList();

            var talentYesterdayTop30HrIds = talentYesterdayTop30Hrs.Select(s => s.HrId).ToList();

            var talentYesterdayTop30 = _context.User_Hr.Where(x => talentYesterdayTop30HrIds.Contains(x.UserId))
            .Select(s => new AdvTalRankingInfo
            {
                EntName = s.Enterprise.Name,
                HrName = s.NickName,
                Talent = s.User_Num.Talent,
                Type = AdvTalRankingType.昨日新增人才储备,
                UserId = s.UserId,
                VirtualTalent = s.User_Num.VirtualTalent
            }).ToList();

            foreach (var item in talentYesterdayTop30)
            {
                item.YesterdayTalent = talentYesterdayTop30Hrs.FirstOrDefault(x => x.HrId == item.UserId)?.Ct ?? 0;
            }
            talentYesterdayTop30 = talentYesterdayTop30.OrderByDescending(o => o.YesterdayTalent).ToList();

            talentTop30.AddRange(talentYesterdayTop30);
            MyRedis.Client.HSet(mainKey, RedisKey.DataScreen.AdvTalRanking, talentTop30);

            #region 平台级 - 招聘流程 数据缓存
            SaveRecruitDataToRedis(_context);
            await Task.Delay(500);
            #endregion

            #region 平台级 - 用户概况 数据缓存
            SaveSeekerDataToRedis(_context);
            await Task.Delay(500);
            #endregion

            #region 平台级 - 项目看板 数据缓存
            SaveProjectDataToRedis(_context);
            #endregion
        }
        catch (Exception e)
        {
            _logger.Error("数据大屏首页数据统计报错", Tools.GetErrMsg(e));
        }
    }

    /// <summary>
    /// 平台级 - 招聘流程 数据缓存
    /// </summary>
    /// <param name="_context"></param>
    public static void SaveRecruitDataToRedis(StaffingContext _context)
    {
        var hour24ago = DateTime.Now.AddHours(-24);// todo：今日登录、注册都是24小时之内，不是当前自然日，以后数据量多了之后应该要修改
                                                   // 投递概况 今年数据同比去年同期月份 todo：此处也可能展示近12个月数据及其同比，此处数据量级可能比较大，后期考虑从es直接查询
        int lastYear = DateTime.Now.AddYears(-1).Year;
        var deliveryNData = _context.Post_Delivery
        .Select(s => new { s.CreatedTime, s.CreatedTime.Year, s.CreatedTime.Month })
        .Where(x => x.CreatedTime >= Convert.ToDateTime(lastYear + "-01-01"))
        .GroupBy(g => new { g.Year, g.Month })
        .Select(s => new DSPlatDeliveryInfo
        {
            YearMonth = s.Key.Year + "-" + s.Key.Month,
            Count = s.Count()
        }).ToList();
        MyRedis.Client.HSet(RedisKey.DataScreen.ScreenRecruitInfo, RedisKey.DataScreen.RecruitDeliveryInfo, deliveryNData);

        // 实时播报 top30 todo：此处数据量级可能比较大，后期考虑从es直接查询
        var deliveryTop30 = _context.Post_Delivery.Include(i => i.User_Seeker).Include(i => i.Post)
        .Select(s => new DSPlatDeliveryTop30Info
        {
            Name = s.User_Seeker.NickName,
            PostName = s.Post.Name,
            DeliveryTime = s.CreatedTime
        }).OrderByDescending(o => o.DeliveryTime).Take(30).ToList();
        MyRedis.Client.HSet(RedisKey.DataScreen.ScreenRecruitInfo, RedisKey.DataScreen.RecruitDeliveryTop30, deliveryTop30);

        // 面试官概况
        // 登录过面试官端小程序，就认定为面试官（WeChatInterviewerAppletId不为空）
        // var interviewers = _context.User_Seeker.Include(i => i.User_Extend).Where(w => w.Status == UserStatus.Active && !string.IsNullOrWhiteSpace(w.WeChatInterviewerAppletId)).ToList();
        var interviewers = _context.User_OpenId.Where(w => w.AppId == ClientApps.InterviewApplet.AppID && w.User_Seeker.Status == UserStatus.Active);
        var interviewerSum = new DSPlatInterviewerOfRecruitInfo();
        interviewerSum.InterviewerCount = interviewers.Count();
        interviewerSum.InterviewerOnlineCount = interviewers.Where(c => c.User.User_Extend.LoginTime > hour24ago).Count();

        // 简历待筛选
        interviewerSum.InterviewerRecruitScreening = _context.Recruit_Interviewer_Screen.Count(c => c.Status == RecruitInterviewerScreenStatus.NoFedBack);
        // 面试待筛选
        interviewerSum.InterviewerSeekerScreening = _context.Recruit_Interview.Where(w => w.Outcome == RecruitInterviewOutcome.Waiting).GroupBy(g => new { g.RecruitId, g.Outcome }).Count();
        // 面试官已处理
        var recruitStatus2 = _context.Recruit_Interviewer_Screen.Count(c => c.Status != RecruitInterviewerScreenStatus.NoFedBack);// 招聘流程：面试官代筛选非未反馈
        var recruitStatus3 = _context.Recruit_Interview.Count(c => c.Outcome != RecruitInterviewOutcome.Waiting);// 招聘流程：面试安排中非未反馈
        interviewerSum.InterviewerHandled = recruitStatus2 + recruitStatus3;
        MyRedis.Client.HSet(RedisKey.DataScreen.ScreenRecruitInfo, RedisKey.DataScreen.RecruitInterviewerInfo, interviewerSum);

        // 处理效率
        var clxv = new ChuLiXiaoLvInfo();

        //投递转化率
        var toudi = _context.Post_Delivery.Select(s => s.SeekerId).Distinct().Count();
        var seekers = _context.User_Seeker.Count();
        if (seekers > 0)
            clxv.TouDi = ((decimal)toudi / seekers).ToFixed(2);

        //入职转化率
        var ruzhi = _context.Recruit.Where(x => x.Status == RecruitStatus.Induction).Select(s => s.SeekerId).Distinct().Count();
        if (toudi > 0)
            clxv.RuZhi = ((decimal)ruzhi / toudi).ToFixed(2);

        MyRedis.Client.HSet(RedisKey.DataScreen.ScreenRecruitInfo, RedisKey.DataScreen.RecruitXiaoLvInfo, clxv);

    }

    /// <summary>
    /// 平台级 - 用户概况 数据缓存
    /// </summary>
    /// <param name="_context"></param>
    public static void SaveSeekerDataToRedis(StaffingContext _context)
    {
        var hour24ago = DateTime.Now.AddHours(-24);// todo：今日登录、注册都是24小时之内，不是当前自然日，以后数据量多了之后应该要修改
        DSPlatSeekerDataInfo info = new DSPlatSeekerDataInfo();
        int user_count = 0;
        // 平台简历概况
        info.ResumeNum = _context.Talent_Virtual.Count();

        // 所有用户信息
        var userList = _context.User_Seeker.Where(w => w.Status == UserStatus.Active).Select(s => new Config.CommonModel.DataScreen.UserSeekerInfo
        {
            UserId = s.UserId,
            Birthday = s.User_Resume.Birthday,
            Sex = s.User_Resume.Sex,
            Education = s.User_Resume.Education,
            RegionId = s.RegionId,
            CreatedTime = s.CreatedTime
        }).ToList();

        if (userList is not null && userList.Count > 0)
        {
            user_count = userList.Count();
            // 平台人才储备
            info.TalentNum = user_count;
            // 用户学历概况
            info.EducationInfo = userList.GroupBy(g => g.Education).Select(s => new DSPlatSeekerEducationInfo { Education = s.Key, EduCount = s.Count(), EduPercent = s.Count() * 1.0m / user_count }).ToList();
            // 用户年龄概况
            int _0_15 = userList.Count(w => w.Birthday is not null && Tools.GetAgeByBirthdate(w.Birthday) < 16);
            int _16_24 = userList.Count(w => w.Birthday is not null && Tools.GetAgeByBirthdate(w.Birthday) >= 16 && Tools.GetAgeByBirthdate(w.Birthday) <= 24);
            int _25_34 = userList.Count(w => w.Birthday is not null && Tools.GetAgeByBirthdate(w.Birthday) >= 25 && Tools.GetAgeByBirthdate(w.Birthday) <= 34);
            int _35_44 = userList.Count(w => w.Birthday is not null && Tools.GetAgeByBirthdate(w.Birthday) >= 35 && Tools.GetAgeByBirthdate(w.Birthday) <= 44);
            int _45 = userList.Count(w => w.Birthday is not null && Tools.GetAgeByBirthdate(w.Birthday) >= 45);
            int othersAge = userList.Count(w => w.Birthday is null);
            info.AgeInfo = new List<DSPlatSeekerAgeInfo>
            {
                new DSPlatSeekerAgeInfo{ AgeGroup = AgeGroup.十六岁以下, AgeCount = _0_15, AgePercent = _0_15 * 1.0m / user_count },
                new DSPlatSeekerAgeInfo{ AgeGroup = AgeGroup.十六至二十四岁, AgeCount = _16_24, AgePercent = _16_24 * 1.0m / user_count },
                new DSPlatSeekerAgeInfo{ AgeGroup = AgeGroup.二十五至三十四岁, AgeCount = _25_34, AgePercent = _25_34 * 1.0m / user_count },
                new DSPlatSeekerAgeInfo{ AgeGroup = AgeGroup.三十五至四十四岁, AgeCount = _35_44, AgePercent = _35_44 * 1.0m / user_count },
                new DSPlatSeekerAgeInfo{ AgeGroup = AgeGroup.四十五岁以上, AgeCount = _45, AgePercent = _45 * 1.0m / user_count },
                new DSPlatSeekerAgeInfo{ AgeGroup = AgeGroup.其他, AgeCount = othersAge, AgePercent = othersAge * 1.0m / user_count },
            };
            // 用户性别概况
            info.SexInfo = userList.GroupBy(g => g.Sex).Select(s => new DSPlatSeekerSexInfo { Sex = s.Key, SexCount = s.Count(), SexPercent = s.Count() * 1.0m / user_count }).ToList();
            // 今日新注册
            info.NewRegisterNum = userList.Count(c => c.CreatedTime > hour24ago);
            // 各地区人才储备
            // 1.获取地区字典
            var hbProvinceStr = MyRedis.Client.HGet(RedisKey.DataScreen.ScreenSeekerInfo, RedisKey.DataScreen.ScreenDicRegionInfo);
            List<Dic_Region>? hbProvince = new();
            if (!string.IsNullOrWhiteSpace(hbProvinceStr))
            {
                hbProvince = JsonConvert.DeserializeObject<List<Dic_Region>>(hbProvinceStr);
            }
            else
            {
                hbProvince = _context.Dic_Region.ToList();
                MyRedis.Client.HSet(RedisKey.DataScreen.ScreenSeekerInfo, RedisKey.DataScreen.ScreenDicRegionInfo, hbProvince);
            }
            // 2.用户添加地市（city）,区县（country）
            var userAreaList = userList.GroupBy(g => g.RegionId).Select(s => new DSPlatSeekerAreaInfo { RegionId = s.Key, Quantity = s.Count() }).ToList();
            userAreaList.ForEach(f =>
            {
                // 地市信息
                if (!string.IsNullOrWhiteSpace(f.RegionId))
                {
                    f.City = FindRegionForSeeker(f.RegionId, hbProvince!);// 这里用的递归的方式，其实完全没必要，因为dic_region表有地市（City字段）信息
                }
                var areaInfo = hbProvince!.FirstOrDefault(s => s.Id == f.RegionId);
                // 省信息
                string? provinceId = areaInfo?.Province;
                if (!string.IsNullOrWhiteSpace(provinceId))
                    f.Province = hbProvince!.FirstOrDefault(s => s.Id == provinceId)!.Name;
                else
                    f.Province = areaInfo?.Name;
                // 区县信息
                string? countyId = areaInfo?.County;
                if (!string.IsNullOrWhiteSpace(countyId))
                    f.County = hbProvince!.FirstOrDefault(s => s.Id == countyId)!.Name;
                else
                    f.County = areaInfo?.Name;
            });
            MyRedis.Client.HSet(RedisKey.DataScreen.ScreenSeekerInfo, RedisKey.DataScreen.ScreenUserRegionInfo, userAreaList);
        }

        // 求职意向Top5
        info.HopePostTop5 = _context.User_Post_Visit.Select(
            s => new
            {
                s.UserId,
                PostName = s.Dic_Post.Name,
                PostId = s.Post
            })
            .Distinct()
            .GroupBy(g => new { g.PostName, g.PostId })
            .Select(s => new DSPlatSeekerHopePostInfo
            {
                PostName = s.Key.PostName,
                Quantity = s.Count()
            }).OrderByDescending(o => o.Quantity).Take(5).ToList();

        // 今日登录
        info.LoginedNum = _context.User_Seeker.Count(w => w.Status == UserStatus.Active && w.User_Extend.LoginTime >= hour24ago);

        // 注册来源
        info.RegisterResource = _context.User_Seeker
            .Where(w => w.Status == UserStatus.Active)
            .GroupBy(g => g.Source)
            .Select(s => new UserSourceInfo
            {
                SourceName = s.Key,
                Quantity = s.Count()
            }).OrderByDescending(o => o.Quantity).ToList();

        // 行业类型Top5 todo: 小析解析处理之后才有行业分类

        MyRedis.Client.HSet(RedisKey.DataScreen.ScreenSeekerInfo, RedisKey.DataScreen.ScreenSeekerInfo, info);
    }

    /// <summary>
    /// 根据地区id查找地市Name
    /// </summary>
    /// <param name="regionId"></param>
    /// <param name="hbProvince"></param>
    /// <returns></returns>
    private static string? FindRegionForSeeker(string regionId, List<Dic_Region> hbProvince)
    {
        string? regionName;
        var city = hbProvince.Where(w => w.Id == regionId).Select(s => new { s.ParentId, s.Province, s.Name }).FirstOrDefault();
        if (city == null)
        {
            return null;
        }
        else if (!string.IsNullOrWhiteSpace(city.ParentId) && !string.IsNullOrWhiteSpace(city.Province) && city.ParentId == city.Province)
        {
            return city.Name;
        }
        else
        {
            regionName = FindRegionForSeeker(city.ParentId, hbProvince);
        }
        return regionName;
    }

    /// <summary>
    /// 平台级 - 项目看板 数据缓存
    /// </summary>
    /// <param name="_context"></param>
    public static void SaveProjectDataToRedis(StaffingContext _context)
    {
        DSPlatProjectDataInfo info = new();

        // 项目累计
        var projects = _context.Project.Where(x => !string.IsNullOrEmpty(x.User_Hr.UserId)).Include(i => i.User_Hr);
        info.ProjectSum = projects.Count();

        if (info.ProjectSum <= 0)
            return;

        // 本平台 - 诺快聘项目数
        info.PlatProjectSum = projects.Count(c => c.HrId == "1");
        // 本平台 - 已归档项目数
        info.PlatProjectArchivedNum = projects.Count(c => c.HrId == "1" && c.Status == ProjectStatus.归档);
        // 本平台 - 交付中项目数
        info.PlatProjectExecNum = info.PlatProjectSum - info.PlatProjectArchivedNum;

        // 诺亚集团项目数
        string entid = "100989262137917573";// 生产环境
        info.NoahProjectSum = projects.Count(c => c.HrId != "1" && c.User_Hr.EntId == entid);

        // 第三方平台项目数 - 做差
        info.OtherProjectSum = info.ProjectSum - info.PlatProjectSum - info.NoahProjectSum;

        // 本平台 - 诺快聘项目数占比
        info.PlatProjectSumPercent = info.PlatProjectSum * 1.0m / info.ProjectSum;

        // 诺亚集团项目数占比
        info.NoahProjectSumPercent = info.NoahProjectSum * 1.0m / info.ProjectSum;

        // 第三方平台项目数占比
        info.OtherProjectSumPercent = info.OtherProjectSum * 1.0m / info.ProjectSum;

        // 运行中项目 - 判断post
        //info.ProjectExecNum = projects.Where(w => w.Posts.Any(a => a.Status == PostStatus.发布中) && w.Status == ProjectStatus.已上线)?.Count() ?? 0;
        var exeProjects = _context.Project.Where(w => w.Posts.Any(a => a.Status == PostStatus.发布中) && w.Status == ProjectStatus.已上线).ToList();
        info.ProjectExecNum = exeProjects?.Count() ?? 0;

        // 已归档项目
        info.ProjectArchivedNum = projects.Count(c => c.Status == ProjectStatus.归档);

        // 进行中项目大于0
        if (info.ProjectExecNum > 0)
        {
            // 项目概况 - 项目类型分组
            info.ProjectInfos = exeProjects!
                .GroupBy(g => g.Type)
                .Select(s => new ProjectInfo { ProjectType = s.Key, ExecNum = s.Count(), ExecNumPercent = s.Count() * 1.0m / info.ProjectExecNum }).ToList();

            // 项目行业概况
            info.ProjectIndustryInfos = exeProjects!
                .GroupBy(g => g.Industry)
                .Select(s => new ProjectIndustryInfo { ProjectIndustry = s.Key, IndustryExecNum = s.Count(), IndustryExecNumPercent = s.Count() * 1.0m / info.ProjectExecNum }).ToList();

            #region 联表方式
            // 项目地区概况 - 联表查询方式，暂时不用
            //var regionInfo = _context.Project.Include(i => i.Project_Region).
            //    ThenInclude(t => t.Dic_Region).
            //    Where(w => w.Status == ProjectStatus.已上线).
            //    ToList();
            //List<ProjectAreaDetailInfo> projectAreaInfos = new();
            //regionInfo.ForEach(f =>
            //{
            //    foreach(var item in f.Project_Region)
            //    {
            //        projectAreaInfos.Add(new ProjectAreaDetailInfo { RegionId = item.Id});
            //    }
            //});
            #endregion

            // 项目地区概况 - RegionData字段
            var execRegionInfo = exeProjects!;
            HashSet<ProjectAreaDetailInfo> projectAreaDetailInfos = new();
            // execRegionInfo.ForEach(f =>
            // {
            //     foreach (var item in f.RegionData)
            //     {
            //         projectAreaDetailInfos.Add(new ProjectAreaDetailInfo
            //         {
            //             ProjectId = f.ProjectId,
            //             ProvinceName = item.ProvinceName
            //         });
            //     }
            // });
            if (projectAreaDetailInfos.Count > 0)
            {
                var count = projectAreaDetailInfos.Count();
                info.ProjectAreaInfos = projectAreaDetailInfos
                    .GroupBy(g => g.ProvinceName)
                    .Select(s => new ProjectAreaInfo
                    {
                        ProjectProvince = s.Key,
                        AreaExecNum = s.Count(),
                        AreaExecNumPercent = s.Count() * 1.0m / count // 这里要注意，不能直接除以运行中项目，而是总次数，总次数是 不同省份相同项目，取省份数
                    }).ToList();
            }
        }

        MyRedis.Client.HSet(RedisKey.DataScreen.ScreenProjectInfo, RedisKey.DataScreen.ScreenProjectInfo, info);
    }

    /// <summary>
    /// 定期清理隐私号码绑定
    /// </summary>
    /// <param name="cancel"></param>
    public async Task PrivatePhoneClean(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            var nowticks = DateTime.Now.ToUnixTime();
            var maxticks = DateTime.Now.AddDays(1).ToUnixTime();

            //移除过期key
            MyRedis.Client.ZRemRangeByScore(RedisKey.PrivatePhoneCleanKey, 0, nowticks);

            //查询即将过期的key
            var maxCount = 501;//保留进行中的隐私号码数量
            var phones = MyRedis.Client.ZRangeByScoreWithScores(RedisKey.PrivatePhoneCleanKey, nowticks, maxticks, maxCount, 50);

            if (phones == null || phones.Count() == 0)
                return;

            foreach (var item in phones)
            {
                var marray = item.member.Split('&');
                if (marray.Count() < 2)
                    continue;

                var secretNo = marray[0];
                var subId = marray[1];

                try
                {
                    await _aliyunDyplsHelper.UnbindSubscription(new UnbindSubscription
                    {
                        SecretNo = secretNo,
                        SubsId = subId
                    });
                }
                catch { }
            }
        }
        catch (Exception e)
        {
            _logger.Error("定期清理隐私号码绑定失败", Tools.GetErrMsg(e));
        }
    }

    /// <summary>
    /// 定期检测缺失数据
    /// </summary>
    /// <param name="cancel"></param>
    public async Task CheckLoseData(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            //redis分布式锁，自动释放
            var lockerKey = $"st:checklosedata";
            using var locker = MyRedis.TryLock(lockerKey);

            if (locker == null)
                return;

            var checkTime = DateTime.Now.AddDays(-6);

            //职位地图
            try
            {
                using var _context = _staffingContextFactory.CreateDbContext();
                var post = _context.Post.Where(x => x.CreatedTime > checkTime && string.IsNullOrEmpty(x.LocationMap)
                && x.Location.X > 0 && x.Location.Y > 0)
                .OrderBy(o => o.CreatedTime).Take(50).ToList();

                foreach (var item in post)
                {
                    try
                    {
                        item.LocationMap = await _tencentMapHelper.GetStaticMap(item.PostId, item.Location.X, item.Location.Y);
                    }
                    catch { }
                    await Task.Delay(500);
                }
                _context.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("定期检测职位地图出错", Tools.GetErrMsg(ex));
            }

            //职位二维码
            try
            {
                using var _context = _staffingContextFactory.CreateDbContext();
                var post = _context.Post_Team.Where(x => x.CreatedTime > checkTime && string.IsNullOrEmpty(x.AppletQrCode))
                .OrderBy(o => o.CreatedTime).Take(50).ToList();

                foreach (var item in post)
                {
                    try
                    {
                        item.AppletQrCode = await RetryHelper.Do(async () => await _weChatHelper.GetWxaCodeUnlimitForPosition(item.TeamPostId));
                    }
                    catch { }
                    await Task.Delay(20);
                }
                _context.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("定期检测职位二维码出错", Tools.GetErrMsg(ex));
            }

            //顾问二维码
            try
            {
                using var _context = _staffingContextFactory.CreateDbContext();
                var hrs = _context.User_Hr.Where(x => x.CreatedTime > checkTime && string.IsNullOrEmpty(x.AppletQrCode))
                .OrderBy(o => o.CreatedTime).Take(50).ToList();

                foreach (var item in hrs)
                {
                    try
                    {
                        item.AppletQrCode = await RetryHelper.Do(async () => await _weChatHelper.GetWxaCodeUnlimitForHr(item.UserId));
                    }
                    catch { }
                    await Task.Delay(20);
                }
                _context.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("定期检测顾问二维码出错", Tools.GetErrMsg(ex));
            }

            //简历二维码
            try
            {
                using var _context = _staffingContextFactory.CreateDbContext();
                var seekers = _context.User_Seeker.Where(x => x.CreatedTime > checkTime && string.IsNullOrEmpty(x.HrAppletQrCode))
                .OrderBy(o => o.CreatedTime).Take(50).ToList();

                foreach (var item in seekers)
                {
                    try
                    {
                        item.HrAppletQrCode = await RetryHelper.Do(async () => await _weChatHelper.GetWxaCodeUnlimitForSeeker(item.UserId));
                    }
                    catch { }
                    await Task.Delay(20);
                }
                _context.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("定期检测简历二维码出错", Tools.GetErrMsg(ex));
            }
        }
        catch (Exception e)
        {
            _logger.Error("定期检测缺失数据", Tools.GetErrMsg(e));
        }
    }

    public async void SaveOssUrl(CancellationToken cancel)
    {
        //处理消息
        List<File_Path_Of_Oss>? msg = null;
        try
        {
            if (cancel.IsCancellationRequested)
                return;
            msg = MyRedis.Client.SPop<File_Path_Of_Oss>(SubscriptionKey.OssPathKey, 10)?.ToList();

            if (msg == null || msg.Count == 0)
            {
                await Task.Delay(500);
                return;
            }
            using var _context = _staffingContextFactory.CreateDbContext();
            msg.ForEach(f => { _context.Add(f); });
            _context.SaveChanges();

            await Task.FromResult(1);
        }
        catch (Exception e)
        {
            _logger.Error("保存Oss地址出错", Tools.GetErrMsg(e));
        }
    }

    public async void DeleteOssUrl(CancellationToken cancel)
    {
        //处理消息
        List<string>? msg = null;
        try
        {
            if (cancel.IsCancellationRequested)
                return;
            msg = MyRedis.Client.SPop(SubscriptionKey.OssUserIdKey, 10)?.ToList();

            if (msg == null || msg.Count == 0)
            {
                await Task.Delay(500);
                return;
            }
            using var _context = _staffingContextFactory.CreateDbContext();
            var ossUrls = _context.File_Path_Of_Oss.Where(w => msg.Contains(w.UserId)).ToList();
            if (ossUrls.Count == 0)
                return;
            ossUrls.ForEach(f => { f.Deleted = 1; });
            _context.SaveChanges();

            await Task.FromResult(1);
        }
        catch (Exception e)
        {
            _logger.Error("删除Oss地址出错", Tools.GetErrMsg(e));
        }
    }

    public async Task KSResumeToPost(CancellationToken cancellationToken)
    {
        //处理消息
        string? msg = null;
        while (!cancellationToken.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                //msg = MyRedis.Client.LPop(SubscriptionKey.KuaishouResumeDelivery);
                msg = MyRedis.Client.SPop(SubscriptionKey.KuaishouResumeDelivery);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }
                var obj = new object();
                _logger.Info("pop消息", msg, workStartTime.ToString("yyyy-MM-dd HH:mm:ss") + "_" + Thread.CurrentThread.ManagedThreadId + "_" + obj.GetHashCode());
                //处理消息
                await RelationHrAndKuaishouTalent(msg);
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.KuaishouResumeDelivery}消息处理出错", Tools.GetErrMsg(e), msg ?? string.Empty);
                await Task.Delay(500);
            }
        }
    }

    public async Task RelationHrAndKuaishouTalent(string ApplicationId)
    {
        using var _context = _staffingContextFactory.CreateDbContext();
        var info = _context.Kuaishou_Talent_Infos.FirstOrDefault(x => x.ApplicationId == ApplicationId);

        if (info == null)
            throw new Exception("无数据");

        string userId = "worker";
        var ksTalentInfo = JsonConvert.DeserializeObject<KsTalentInfo>(JsonConvert.SerializeObject(info));// 简历信息 + 回访信息
                                                                                                          // 先回访
        var relationInfo = new Kuaishou_Hr_Talent_Relations
        {
            ApplicationId = ApplicationId,
            ReturnVisit = ReturnVisit.已回访,
            HrId = userId,
            VisitMemo = "自动推送",
            CreatedUser = userId
        };
        _context.Add(relationInfo);

        // 再推送
        // 手机号****不推送
        if (string.IsNullOrWhiteSpace(info.Phone) || info.Phone.Contains("*"))
        {
            relationInfo.VisitMemo += ";手机号信息不全";
            _context.SaveChanges();
            return;
        }
        // 同一个手机号不能重复推送同一个职位
        var sendedCount = _context.Kuaishou_Hr_Talent_Relations.AsNoTracking().Where(w => w.Kuaishou_Talent_Infos.JobId == info.JobId && w.Kuaishou_Talent_Infos.Phone == info.Phone && w.Status == KuaishouStatus.已推送).Count();
        if (sendedCount > 0)
        {
            relationInfo.VisitMemo += ";重复投递相同职位";
            _context.SaveChanges();
            return;// todo：这里需要记录异常信息不？
        }
        // 查询需要推送的职位
        var rel = _context.Post_Team_Third_Jobid_Rel.Where(w => w.JobId == info.JobId).Select(s => new { s.Post_Team.Post.Name, s.TeamPostId, s.Post_Team.Post.PostId }).FirstOrDefault();
        if (rel == null)
        {
            relationInfo.VisitMemo += ";未同步职位信息";
            _context.SaveChanges();
            return;// todo：这里需要记录异常信息不？
        }
        info.SendPostId = rel.PostId;
        info.SendTeamPostId = rel.TeamPostId;
        info.SendTeamPostName = rel.Name;
        _context.SaveChanges();
        ksTalentInfo!.SendTeamPostId = rel.TeamPostId;
        ksTalentInfo!.SendTeamPostName = rel.Name;

        using var transaction = _context.Database.BeginTransaction();
        try
        {
            // 简历投进简历池
            var resume = new Resume_Buffer()
            {
                ResumeId = info.ApplicationId,
                Source = ThirdPlatType.快招工,
                HrId = userId,
                HrName = userId,
                Name = ksTalentInfo!.Name ?? string.Empty,
                Sex = ksTalentInfo.GenderName,
                Mobile = ksTalentInfo.Phone ?? string.Empty,
                Age = ksTalentInfo.Age,
                PostId = rel.TeamPostId,
                PostName = rel.Name,
                TopEducation = ksTalentInfo.DataDynamicResumeInfo.HighestDegree,
                ApplyTime = ksTalentInfo.ApplyTime,
                RegionId = ksTalentInfo.DataLocationCitys?.adCode,
                Location = ksTalentInfo.DataUserCity,
                LocationPoint = new NetTopologySuite.Geometries.Point(Convert.ToDouble(ksTalentInfo!.DataLocationCitys!.longitude), Convert.ToDouble(ksTalentInfo.DataLocationCitys.latitude)),
                Hopes = new ResumeBufferHopes(),
                Educations = new ResumeBufferEducations(),
                Works = new ResumeBufferWorks()
            };
            _context.Add(resume);

            relationInfo.Status = KuaishouStatus.已推送;

            _context.SaveChanges();
            //_logger.Info(SubscriptionKey.KuaishouResumeDelivery, "快招工投递-入职位-入简历池", resume.Id);

            ksTalentInfo.ResumeBufferId = resume.Id;
            ksTalentInfo.Education = GetMappingEducation(_context, ksTalentInfo.DataDynamicResumeInfo.HighestDegree);
            var projectPostInfo = new ProjectPostInfo();
            // 下单
            ksTalentInfo.NotCheck = true;
            var ex = new PostOrderExceptionMessage();
            _ksPostOrderService.SaveToOrderTable(ksTalentInfo, projectPostInfo, ex);
            if (ex != null && ex.ExceptionMessage != null && ex.ExceptionMessage.Count > 0)
            {
                _logger.Info(SubscriptionKey.KuaishouResumeDelivery, "快招工投递-入职位-投递职位异常", ex.ExceptionMessage[0]);
                transaction.Rollback();
                return;
            }
            //_logger.Info(SubscriptionKey.KuaishouResumeDelivery, "快招工投递-入职位-投递职位");
            transaction.Commit();
        }
        catch (Exception ex)
        {
            _logger.Info(SubscriptionKey.KuaishouResumeDelivery, "快招工投递-入职位-投递职位异常", ex.Message);
            transaction.Rollback();
            throw;
        }

        if (ksTalentInfo.newSeeker)
        {
            _commonUserService.SetAdviser(ksTalentInfo.SeekerId!, "1");
        }

        await Task.Delay(20);
    }

    private EducationType? GetMappingEducation(StaffingContext _context, string? highestDegree)
    {
        if (highestDegree == null)
            return null;
        var baseInfo = _context.Dic_Base_Info.FirstOrDefault(f => f.Type == "kgz_nkp_education_enum_map" && f.Code == highestDegree);
        if (baseInfo == null)
            return null;
        System.Enum.TryParse(typeof(EducationType), baseInfo.Value, out object? result);
        return result == null ? null : (EducationType)result;
    }

    /// <summary>
    /// 根据模板组装证书推广图片
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task GentoCerTificate(CancellationToken cancellationToken)
    {
        //处理消息
        string? msg = null;
        string key = "cer:gentocertificate";
        while (!cancellationToken.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;

            try
            {
                if (MyRedis.Client.Exists(key))
                {
                    await Task.Delay(500, cancellationToken);
                    continue;
                }
                var now = DateTime.Now;
                MyRedis.Client.Set(key, now, (new DateTime(now.Year, now.Month, now.Day, 3, 0, 0)).AddDays(1) - now);

                //try
                //{
                //    //生成证书推广图片
                //    await GentoCerTificateFun(ImgHelper._Certificate_TemplateImgConfig);
                //}
                //catch (Exception e)
                //{
                //    _logger.Error($"{SubscriptionKey.AdviserShareCertificate}消息处理出错:生成证书推广基础模板报错", Tools.GetErrMsg(e), msg ?? string.Empty);
                //    await Task.Delay(500);
                //}
                try
                {
                    //拉取所有员工企微二维码
                    await LoadEmploeeByAll();
                }
                catch (Exception e)
                {
                    _logger.Error($"{SubscriptionKey.AdviserShareCertificate}消息处理出错:拉取所有员工企微二维码", Tools.GetErrMsg(e), msg ?? string.Empty);
                    await Task.Delay(500, cancellationToken);
                }
                ////将所有顾问的推广图都更新一次
                //int PageSize = 500, PageIndex = 1, DataCount = 0;
                //using var _context = _staffingContextFactory.CreateDbContext();
                //do
                //{
                //    try
                //    {
                //        var adviserIds = _context.User_Hr.OrderBy(x => x.CreatedTime)
                //            .Select(x => x.UserId)
                //            .Skip((PageIndex - 1) * PageSize)
                //            .Take(PageSize)
                //            .ToArray();
                //        DataCount = adviserIds.Length;
                //        PageIndex++;
                //        MyRedis.Client.SAdd(SubscriptionKey.AdviserShareCertificate, adviserIds);
                //    }
                //    catch (Exception e)
                //    {
                //        _logger.Error($"{SubscriptionKey.AdviserShareCertificate}消息处理出错：向缓存中写入待更新推广证书的顾问ID列表", Tools.GetErrMsg(e), msg ?? string.Empty);
                //        await Task.Delay(500, cancellationToken);
                //    }
                //} while (DataCount == PageSize);
            }
            catch (TaskCanceledException)
            { return; }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.AdviserShareCertificate}消息处理出错", Tools.GetErrMsg(e), msg ?? string.Empty);
                await Task.Delay(500, cancellationToken);
            }
        }
    }

    /// <summary>
    /// 生成顾问的蓝领证书邀约推广图
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task GentoAdviserCerShare(CancellationToken cancel)
    {
        //处理消息
        string[]? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<string>(SubscriptionKey.AdviserShareCertificate, 20);

                if (msg == null || msg.Length == 0)
                {
                    await Task.Delay(500);
                    continue;
                }

                //生成顾问蓝领证书推广海报图
                bool isUseDBQWQR = IsUseDBQWQR();
                var templateBase64 = msg.Length == 1 ? string.Empty : await ImgHelper.DownloadImageAsBase64(Constants.CertificateTemplaeUrl());
                foreach (var item in msg)
                {
                    try
                    {
                        var result_qw = await GentoCerTificateFun_QW2(item, ImgHelper._Certificate_QWImgConfigConfig, isUseDBQWQR);
                        if (!result_qw.Result)
                            _logger.Error($"{SubscriptionKey.AdviserShareCertificate}消息处理出错QW", result_qw.ErrMsg, (msg?.ToJsonString() ?? string.Empty).ToString());
                        //var result = await GentoAdviserCerShareFun(item, templateBase64, !result_qw.Result);
                        //if (!result.Result)
                        //    _logger.Error($"{SubscriptionKey.AdviserShareCertificate}消息处理出错", result.ErrMsg, (msg?.ToJsonString() ?? string.Empty).ToString());
                    }
                    catch (Exception e)
                    {
                        _logger.Error($"{SubscriptionKey.AdviserShareCertificate}消息处理出错:{item}", Tools.GetErrMsg(e), (msg?.ToJsonString() ?? string.Empty).ToString());
                    }
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.AdviserShareCertificate}消息处理出错", Tools.GetErrMsg(e), (msg?.ToJsonString() ?? string.Empty).ToString());
                await Task.Delay(500);
            }

            await Task.Delay(10);
        }
    }
    /// <summary>
    /// 是否使用数据库存储的企微二维码
    /// </summary>
    /// <returns></returns>
    private bool IsUseDBQWQR()
    {
        string key = "cer:gentocertificate";
        try
        {
            if (MyRedis.Client.Exists(key))
            {
                var dt = MyRedis.Client.Get<DateTime>(key);
                if ((DateTime.Now - dt).TotalMinutes < 30)
                    return true;
            }
        }
        catch { }
        return false;
    }
    /// <summary>
    /// 根据手机号获取企微二维码s
    /// </summary>
    /// <param name="AdviserMobile"></param>
    /// <param name="dbcontext"></param>
    /// <returns></returns>
    /// <exception cref="Infrastructure.Exceptions.BadRequestException"></exception>
    private async Task<(bool Result, string ErrMsg, string qrCode, string AdviserMobile)> GetQW_QRCode(string AdviserId, StaffingContext dbcontext)
    {
        string qrCode = string.Empty, QWUserID = string.Empty, AdviserMobile = string.Empty, QWMobile = string.Empty;

        var hr = dbcontext.User_Hr.Include(i => i.User).Where(x => x.UserId == AdviserId).FirstOrDefault();
        if (hr != null)
            AdviserMobile = hr.User.Mobile;
        if (hr != null && !string.IsNullOrEmpty(hr.EntWeChatQrCode))
        {
            qrCode = hr.EntWeChatQrCode;
            QWUserID = hr.UserId;
            QWMobile = AdviserMobile;
        }
        else
        {
            if (hr != null)
            {
                //获取员工绑定的企微手机号
                var qwBind = dbcontext.User_QYWechat.Where(x => x.UserId == AdviserId).FirstOrDefault();
                if (qwBind != null)
                    QWMobile = qwBind.Mobile;
                else
                    QWMobile = AdviserMobile;
                var dbQW = dbcontext.QYWechat_Employee.Where(x => x.Mobile == QWMobile).FirstOrDefault();
                if (dbQW != null && !string.IsNullOrEmpty(dbQW.QRCodeURL))
                {
                    qrCode = dbQW.QRCodeURL;
                    QWUserID = dbQW.UserId;
                }
            }
        }

        if (string.IsNullOrEmpty(qrCode))
        {
            var qwUserIdResult = await Infrastructure.QYWechat.QYWechatHelper.GetUserIdByMobile(QWMobile);
            if (!qwUserIdResult.Result || string.IsNullOrEmpty(qwUserIdResult.Userid))
                return (false, $"获取企微用户失败：请检查手机号码是否为绑定企微的手机号", qrCode, AdviserMobile);
            var qwUserInfoResult = await Infrastructure.QYWechat.QYWechatHelper.GetEmployeeInfo(qwUserIdResult.Userid);
            if (!qwUserInfoResult.Result)
                return (false, $"获取企微用户信息失败：请检查企微状态是否正常", qrCode, AdviserMobile);
            else
            {
                qrCode = qwUserInfoResult.Qr_Code!;
                QWUserID = qwUserInfoResult.Userid!;
            }
        }
        return (true, string.Empty, qrCode, AdviserMobile);
    }
    /// <summary>
    /// 导入所有企微的员工信息
    /// </summary>
    /// <returns></returns>
    private async Task<(bool Result, string? ErrMsg)> LoadEmploeeByAll()
    {
        try
        {
            var departmentList = await QYWechatHelper.GetDepartmentList();
            if (departmentList.Result && departmentList.Department != null)
            {
                List<QYWechat_Employee> Employees = new();
                List<string> errors = new();
                foreach (var item in departmentList.Department)
                {
                    var QWEmployees = await QYWechatHelper.GetEmployeeByDepartment(item.Id.ToString());
                    if (!QWEmployees.Result)
                    {
                        errors.Add($"部门Id={item.Id},获取员工列表失败:{QWEmployees.ErrMsg}");
                        continue;
                    }

                    if (QWEmployees.UserList != null && QWEmployees.UserList.Count > 0)
                    {
                        Employees.AddRange(QWEmployees.UserList.Where(x => !Employees.Any(e => e.UserId == x.Userid)).Select(x => new QYWechat_Employee
                        {
                            UserId = x.Userid!,
                            Mobile = x.Mobile ?? string.Empty,
                            Name = x.Name ?? string.Empty,
                            QRCodeURL = x.Qr_Code ?? string.Empty,
                            DepartmentIds = x.Department?.ToJsonString() ?? string.Empty,
                            Gender = x.Gender,
                            Open_userid = x.Open_Userid,
                            Status = (int)x.Status
                        }));
                    }
                }

                //将Employees更新到数据库中

                using (var db = _staffingContextFactory.CreateDbContext())
                {
                    bool IsUpdateDB = false;//确认是否需更新数据库

                    var members = db.QYWechat_Employee.ToList();
                    var eIds = members.Select(x => x.UserId).ToList();

                    //添加不存在的记录
                    var newModel = Employees.Where(x => !eIds.Contains(x.UserId))
                        .Select(x => new QYWechat_Employee
                        {
                            UserId = x.UserId!,
                            Name = x.Name,
                            Mobile = x.Mobile,
                            QRCodeURL = x.QRCodeURL ?? string.Empty,
                            Gender = x.Gender,
                            Status = x.Status,
                            DepartmentIds = x.DepartmentIds,
                            Open_userid = x.Open_userid ?? string.Empty,
                            CreatedTime = DateTime.Now
                        })
                        .ToList();
                    if (newModel.Count > 0)
                    {
                        IsUpdateDB = true;
                        db.QYWechat_Employee.AddRange(newModel);
                    }
                    //更新已存在的记录
                    if (Employees.Any(x => eIds.Contains(x.UserId)))
                    {
                        IsUpdateDB = true;
                        Employees.Where(x => eIds.Contains(x.UserId)).ForEach((QYWechat_Employee x) =>
                        {
                            var temp = members.First(t => t.UserId == x.UserId);
                            temp.UserId = x.UserId!;
                            temp.Name = x.Name;
                            temp.Mobile = x.Mobile;
                            temp.QRCodeURL = x.QRCodeURL ?? string.Empty;
                            temp.Gender = x.Gender;
                            temp.Status = x.Status;
                            temp.DepartmentIds = x.DepartmentIds;
                            temp.Open_userid = x.Open_userid ?? string.Empty;
                        });
                    }
                    //删除退群的记录
                    var tempids = Employees.Select(x => x.UserId).ToList();
                    if (members.Any(x => !tempids.Contains(x.UserId)))
                    {
                        IsUpdateDB = true;
                        db.QYWechat_Employee.Where(x => !tempids.Contains(x.UserId)).ExecuteDelete();
                    }

                    //更新顾问端企微二维码 
                    foreach (var item in members)
                    {
                        if (!string.IsNullOrEmpty(item.QRCodeURL))
                        {
                            var hr = db.User_Hr.Where(x => x.User.Mobile == item.Mobile && string.IsNullOrEmpty(x.EntWeChatQrCode)).FirstOrDefault();
                            if (hr != null)
                            {
                                hr.EntWeChatQrCode = item.QRCodeURL;
                                IsUpdateDB = true;
                            }
                        }
                    }
                    if (IsUpdateDB)
                        db.SaveChanges();
                }
                return (true, $"导入成功，共下载{Employees.Count}条人员数据，{(errors.Count == 0 ? "已全部导入" : $"部分导入失败，原因如下:{errors.ToJsonString()}")}");
            }
            return (departmentList.Result, departmentList.ErrMsg);
        }
        catch (Exception e)
        {
            _logger.Error("企业微信：获取企业微信全量员工信息", Tools.GetErrMsg(e));
            return (false, e.Message);
        }
    }
    private async Task<string> GentoCerTificateFun(Certificate_ProImg_SplicingTemplate request)
    {
        if (request.GetCerCount < 1)
            request.GetCerCount = 20;
        using var context = _staffingContextFactory.CreateDbContext();
        //获取发证机构字典
        var dictDataMap = context.DictData.Where(w => w.DictType == "CertificationAuthority")
        .Select(s => new Staffing.Entity.Staffing.DictData()
        {
            DictValue = s.DictValue,
            DictLabel = s.DictLabel,
        }).ToDictionary(
            k => k.DictValue,
            v => v.DictLabel
        ) ?? new Dictionary<string, string>();

        var dbcer = context.CertificateTable.Where(x => !x.Deleted)
        .OrderByDescending(x => x.CreateTime)
        .Take(request.GetCerCount)
        .Include(c => c.Specs)
        .ToList();
        //var cerIds = dbcer.Select(x => x.Id).Distinct().ToList();
        //var dbspec = context.CertificateSpec.Where(x => cerIds.Contains(x.CertificateId)).Select(x => new
        //{
        //    x.CertificateId,
        //    x.SpecName,
        //    x.Price
        //}).ToList();
        List<Certificate_ProImg_Data> certificates = dbcer.Select(x => new Certificate_ProImg_Data
        {
            CertificateName = x.CertificateName,
            CertificateType = x.CertificateType.ToString(),
            //更新发证机构名称
            IssuingAuthority = string.IsNullOrEmpty(x.IssuingAuthority) ? string.Empty :
                            (dictDataMap.TryGetValue(x.IssuingAuthority, out var value) ? value : x.IssuingAuthority),
            //Specs = dbspec.Where(s => s.CertificateId == x.Id).Select(s => s.SpecName).ToList(),
            //Price = dbspec.Where(s => s.CertificateId == x.Id).OrderByDescending(m => m.Price).FirstOrDefault()?.Price ?? 0
            Specs = x.Specs == null || x.Specs.Count == 0 ? new() : x.Specs.OrderBy(s => s.Price).Select(s => s.SpecName).ToList(),
            Price = x.Specs == null || x.Specs.Count == 0 ? 0 : x.Specs.Min(s => s.Price)
        }).ToList();

        //组装模板基础图片及出现次数
        List<ImgHelper_InputPaths> inputPaths = new List<ImgHelper_InputPaths>();
        inputPaths.Add(new ImgHelper_InputPaths(request.Top_TemplatePath ?? "https://resources.noahjob.cn/staffing/Certificate/postertemplateTop.jpg"));
        if (certificates != null && certificates.Count > 0)
            inputPaths.Add(new ImgHelper_InputPaths(request.Table_TR_TemplatePath ?? "https://resources.noahjob.cn/staffing/Certificate/postertemplateTableTr.jpg", certificates.Count));
        inputPaths.Add(new ImgHelper_InputPaths(request.Bottom_TemplatePath ?? "https://resources.noahjob.cn/staffing/Certificate/postertemplateBottom.jpg"));

        //组装文本
        List<ImgHelper_TextLayer> textLayers = new();
        string FontPath = request.FontTTFPath ?? Path.Combine(AppContext.BaseDirectory, "Certificate", "msyh.ttf");
        var color = SixLabors.ImageSharp.Color.Black;
        SixLabors.Fonts.FontStyle fontStyle = SixLabors.Fonts.FontStyle.Bold;
        for (int i = 0; i < certificates!.Count; i++)
        {
            var item = certificates[i];
            var point_Y = request.CerFont_FirstPositionY + i * request.CerFont_HR_Height;
            // 证书表单--编号的起始位置X
            textLayers.Add(new ImgHelper_TextLayer
            {
                Text = (i + 1).ToString(),
                FontPath = FontPath,
                FontSize = request.CerFontSize,
                Color = color,
                FontStyle = fontStyle,
                Position = new SixLabors.ImageSharp.PointF(request.CerFont_Num_PositionX, point_Y)
            });
            // 证书表单--证书名称的起始位置X
            textLayers.Add(new ImgHelper_TextLayer
            {
                Text = item.CertificateName ?? string.Empty,
                FontPath = FontPath,
                FontSize = request.CerFontSize,
                Color = color,
                FontStyle = fontStyle,
                Position = new SixLabors.ImageSharp.PointF(request.CerFont_CertificateName_PositionX, point_Y)
            });
            // 证书表单--证书类别的起始位置X
            textLayers.Add(new ImgHelper_TextLayer
            {
                Text = item.CertificateType ?? string.Empty,
                FontPath = FontPath,
                FontSize = request.CerFontSize,
                Color = color,
                FontStyle = fontStyle,
                Position = new SixLabors.ImageSharp.PointF(request.CerFont_CertificateType_PositionX, point_Y)
            });
            // 证书表单--证书规格的起始位置X
            textLayers.Add(new ImgHelper_TextLayer
            {
                Text = item.Specs == null ? "[无规格]" : string.Join(@"\", item.Specs),
                FontPath = FontPath,
                FontSize = request.CerFontSize,
                Color = color,
                FontStyle = fontStyle,
                Position = new SixLabors.ImageSharp.PointF(request.CerFont_Specs_PositionX, point_Y)
            });

            // 证书表单--颁发机构的起始位置X
            textLayers.Add(new ImgHelper_TextLayer
            {
                Text = item.IssuingAuthority ?? string.Empty,
                FontPath = FontPath,
                FontSize = request.CerFontSize,
                Color = color,
                FontStyle = fontStyle,
                Position = new SixLabors.ImageSharp.PointF(request.CerFont_IssuingAuthority_PositionX, point_Y)
            });
            // 证书表单--报名费的起始位置X
            textLayers.Add(new ImgHelper_TextLayer
            {
                Text = item.Price.ToString(),
                FontPath = FontPath,
                FontSize = request.CerFontSize,
                Color = SixLabors.ImageSharp.Color.Red,
                FontStyle = fontStyle,
                Position = new SixLabors.ImageSharp.PointF(request.CerFont_Price_PositionX, point_Y)
            });
        }

        var stream = ImgHelper.CombineImagesWithOverlay(inputPaths, textLayers, null);
        string newName = System.IO.Path.GetFileName((new Uri(Constants.CertificateTemplaeUrl())).LocalPath);
        var ossurl = await _objectStorageService.OrdinaryUploadFile(stream, Path.GetFileName(newName), newName);

        return ossurl;
    }

    /// <summary>
    /// 生成顾问蓝领证书推广海报图
    /// </summary>
    /// <param name="AdviserID"></param>
    /// <returns></returns>
    /// <exception cref="Infrastructure.Exceptions.BadRequestException"></exception>
    private async Task<(bool Result, string ErrMsg)> GentoAdviserCerShareFun(string AdviserID, string templateBase64, bool IsUploadQW = false)
    {
        using var context = _staffingContextFactory.CreateDbContext();
        var hr = context.User_Hr.Where(x => x.UserId == AdviserID).FirstOrDefault();
        if (hr == null)
            return (false, "用户不存在");

        Certificate_ProImg_ForHR imgConfig = ImgHelper._Certificate_AdviserImgConfigConfig;

        var QrCodePath = await _weChatHelper.GetWxaCodeUnlimitForCertificateList(hr.UserId);
        if (string.IsNullOrEmpty(QrCodePath))
            throw new Infrastructure.Exceptions.BadRequestException("未获取到顾问蓝领证书推广小程序二维码");
        //组装拼接图片URL及位置
        var Overlay = new List<ImgHelper_ImageOverlay2>
            {
                new ImgHelper_ImageOverlay2(QrCodePath, imgConfig.QrCodePosition_x, imgConfig.QrCodePosition_y, imgConfig.QRSize)
            };

        if (!string.IsNullOrEmpty(hr.EntWeChatQrCode))
        {
            Overlay.Add(new ImgHelper_ImageOverlay2(hr.EntWeChatQrCode, imgConfig.QYWechatPosition_x, imgConfig.QYWechatPosition_y, imgConfig.QYWechatSize));
            Overlay.Add(new ImgHelper_ImageOverlay2(Path.Combine(AppContext.BaseDirectory, "TempFile", "Certificate", "postertemplateBottom_qwtip.png"), imgConfig.QYWechatTipPosition_x > 0 ? imgConfig.QYWechatTipPosition_x : (imgConfig.QYWechatPosition_x + imgConfig.QYWechatSize), imgConfig.QYWechatPosition_y, 0));
        }

        //合并图片
        Stream stream;
        if (string.IsNullOrEmpty(templateBase64))
            stream = await ImgHelper.MergeImagesAsync(Constants.CertificateTemplaeUrl(), Overlay);
        else
            stream = await ImgHelper.MergeImagesAsync_ForBase64Template(templateBase64, Overlay);
        // 上传到OSS，并返回生成的图片URL
        string newName = $"{hr.UserId}.jpg";
        string ossUrl = await _objectStorageService.OrdinaryUploadFile(stream, newName, newName, $"{_config.Aliyun!.Oss!.Dir!}/Certificate/promotionimg");
        if (IsUploadQW)
        {
            stream.Position = 0;
            ossUrl = await _objectStorageService.OrdinaryUploadFile(stream, newName, newName, $"{_config.Aliyun!.Oss!.Dir!}/Certificate/qw_promotionimg");
        }
        stream.Close();
        stream.Dispose();

        //// oss地址存本地,用set不会重复
        //MyRedis.Client.SAdd(SubscriptionKey.OssPathKey,
        //    new Entity.Staffing.File_Path_Of_Oss { Type = "CertificatePromotion", PrimaryId = hr.UserId, OssPath = ossUrl, UserId = hr.UserId });

        return (true, ossUrl + "?timestamp=" + DateTime.Now.Ticks);
    }

    private async Task<(bool Result, string ErrMsg)> GentoCerTificateFun_QW(string AdviserID, Certificate_ProImg_ForHR_QW imgConfig, bool IsUseDBQWQR)
    {
        using var context = _staffingContextFactory.CreateDbContext();
        (bool Result, string ErrMsg, string Qr_Code, string AdviserMobile) = await GetQW_QRCode(AdviserID, context);
        //if (!Result)
        //    return (false, ErrMsg);

        //组装拼接图片URL及位置
        List<ImgHelper_ImageOverlay2>? Overlay;
        if (string.IsNullOrEmpty(Qr_Code))
            Overlay = null;
        else
            Overlay = [
                        new ImgHelper_ImageOverlay2(Qr_Code!, imgConfig.QYWechatPosition_x, imgConfig.QYWechatPosition_y, imgConfig.QYWechatSize)
                    ];
        //组装文本
        string FontPath = Path.Combine(AppContext.BaseDirectory, "TempFile", "Certificate", "msyh.ttf");
        var color = SixLabors.ImageSharp.Color.White;
        SixLabors.Fonts.FontStyle fontStyle = SixLabors.Fonts.FontStyle.Bold;
        List<ImgHelper_TextLayer> textLayers = [
                new ImgHelper_TextLayer
            {
                Text = $"*咨询电话 {AdviserMobile.FormatPhoneNumber()}*",
                FontPath = FontPath,
                FontSize = imgConfig.Mobile_FontSize,
                Color = color,
                FontStyle = fontStyle,
                Position = new SixLabors.ImageSharp.PointF(imgConfig.Mobile_PositionX, imgConfig.Mobile_PositionY)
            }
            ];

        //合并图片
        var stream = await ImgHelper.MergeImagesAsync(imgConfig.TemplatePath ?? Path.Combine(AppContext.BaseDirectory, "TempFile", "Certificate", "qw_Certificate.jpg"), Overlay, textLayers);

        // 上传到OSS，并返回生成的图片URL
        string newName = $"{AdviserID}.jpg";
        string ossUrl = await _objectStorageService.OrdinaryUploadFile(stream, newName, newName, $"{_config.Aliyun!.Oss!.Dir!}/Certificate/qw_promotionimg");
        stream.Close();
        stream.Dispose();
        return (true, ossUrl + "?timestamp=" + DateTime.Now.Ticks);

        //context.Response.ContentType = "image/jpeg";
        //context.Response.Headers.Append("Content-Disposition", "attachment; filename=output.jpg");
        //await stream.CopyToAsync(context.Response.Body);
        //stream.Close();
        //stream.Dispose();
    }

    private async Task<(bool Result, string ErrMsg)> GentoCerTificateFun_QW2(string AdviserID, Certificate_ProImg_ForHR_QW imgConfig, bool IsUseDBQWQR)
    {
        using var context = _staffingContextFactory.CreateDbContext();
        string Qr_Code = await GenerateQRcode("343213757044576389", AdviserID);
        string AdviserMobile = context.User.Where(x => x.UserId == AdviserID).FirstOrDefault()?.Mobile ?? string.Empty;
        //组装拼接图片URL及位置
        List<ImgHelper_ImageOverlay2>? Overlay;
        if (string.IsNullOrEmpty(Qr_Code))
            Overlay = null;
        else
            Overlay = [
                        new ImgHelper_ImageOverlay2(Qr_Code!, imgConfig.QYWechatPosition_x, imgConfig.QYWechatPosition_y, imgConfig.QYWechatSize)
                    ];
        //组装文本
        string FontPath = Path.Combine(AppContext.BaseDirectory, "TempFile", "Certificate", "msyh.ttf");
        var color = SixLabors.ImageSharp.Color.White;
        SixLabors.Fonts.FontStyle fontStyle = SixLabors.Fonts.FontStyle.Bold;
        List<ImgHelper_TextLayer> textLayers = [
                new ImgHelper_TextLayer
            {
                Text = $"*咨询电话 {AdviserMobile.FormatPhoneNumber()}*",
                FontPath = FontPath,
                FontSize = imgConfig.Mobile_FontSize,
                Color = color,
                FontStyle = fontStyle,
                Position = new SixLabors.ImageSharp.PointF(imgConfig.Mobile_PositionX, imgConfig.Mobile_PositionY)
            }
            ];

        //合并图片
        var stream = await ImgHelper.MergeImagesAsync(imgConfig.TemplatePath ?? Path.Combine(AppContext.BaseDirectory, "TempFile", "Certificate", "qw_Certificate2.jpg"), Overlay, textLayers);

        // 上传到OSS，并返回生成的图片URL
        string newName = $"{AdviserID}.jpg";
        string ossUrl = await _objectStorageService.OrdinaryUploadFile(stream, newName, newName, $"{_config.Aliyun!.Oss!.Dir!}/Certificate/qw_promotionimg");
        stream.Close();
        stream.Dispose();
        return (true, ossUrl + "?timestamp=" + DateTime.Now.Ticks);
    }

    private async Task<string> GenerateQRcode(string CertificateId, string AdviserID)
    {

        var safeSign = Tools.MakeSign(AdviserID, CertificateId);
        const string redisKey = "CertificateQrcode";

        // 尝试从Redis获取现有QR码 
        var qrcodeJson = await MyRedis.Client.HGetAsync<string>(redisKey, $"{(int)AppletShareType.证书PC}_{safeSign}");

        QrCodeData? qrcodeData;
        if (string.IsNullOrWhiteSpace(qrcodeJson))
        {
            // 生成新的QR码
            var url = await RetryHelper.Do(async () =>
                await _weChatHelper.GetWxaCodeUnlimit(
                    ClientApps.SeekerApplet.AppID,
                    $"{(int)AppletShareType.证书PC}_{safeSign}",
                    "Mypackagedetail/certificate/certificatedetail", true));
            qrcodeData = new QrCodeData(AdviserID, CertificateId, url);

            // 存储到Redis
            await MyRedis.Client.HSetAsync(
                redisKey,
                $"{(int)AppletShareType.证书PC}_{safeSign}",
                System.Text.Json.JsonSerializer.Serialize(qrcodeData));
        }
        else
        {
            // 使用现有的QR码
            qrcodeData = System.Text.Json.JsonSerializer.Deserialize<QrCodeData>(qrcodeJson);
        }

        // 返回结果
        return qrcodeData?.Url ?? string.Empty;
    }
}

public class QrCodeData
{
    public string UserId { get; init; }
    public string CertificateId { get; init; }
    public string Url { get; init; }

    public QrCodeData(string userId, string certificateId, string url)
    {
        UserId = userId;
        CertificateId = certificateId;
        Url = url;
    }
}