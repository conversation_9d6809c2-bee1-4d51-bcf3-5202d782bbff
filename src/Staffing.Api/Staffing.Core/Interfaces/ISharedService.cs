﻿using Config.CommonModel;
using Staffing.Model.Service;

namespace Staffing.Core.Interfaces;

public interface ISharedService
{
    /// <summary>
    /// 发送短信
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<SMSCodeResponse> SMSCode(SMSCode model);

    /// <summary>
    /// 检测短信验证码
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    CheckSMSCodeResponse CheckSMSCode(CheckSMSCode model);

    /// <summary>
    /// 作废短信验证码
    /// </summary>
    /// <param name="model"></param>
    void InvalidCode(CheckSMSCode model);

    /// <summary>
    /// 阿里云osstoken
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetAliyunOssTokenResponse GetAliyunOssToken(GetAliyunOssToken model);

    /// <summary>
    /// 福利
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetWelfareResponse GetWelfare(GetWelfare model);

    /// <summary>
    /// 学校
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetSchoolResponse GetSchool(GetSchool model);

    /// <summary>
    /// 专业
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetMajorResponse GetMajor(GetMajor model);

    /// <summary>
    /// 行业
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetIndustryResponse GetIndustry(GetIndustry model);

    /// <summary>
    /// 职位类别
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetDicTreeResponse GetPostCategory(GetPostCategory model);

    /// <summary>
    /// 证书
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetDicTreeResponse GetCert(GetCert model);

    /// <summary>
    /// 城市
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetCityResponse GetCity(GetCity model);

    /// <summary>
    /// 城市树
    /// </summary>
    /// <returns></returns>
    GetDicTreeResponse GetCityTree(GetCityTree model);

    /// <summary>
    /// 小程序分享码解析
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    GetAppletSceneResponse GetAppletScene(GetAppletScene model);

    /// <summary>
    /// api预热
    /// </summary>
    void WarmApi();
    Task<SMSCodeResponse> SMSCodeForDy(SMSCode model);
}