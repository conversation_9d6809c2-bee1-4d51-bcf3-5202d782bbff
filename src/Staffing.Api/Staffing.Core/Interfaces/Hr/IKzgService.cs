﻿using Config.CommonModel;
using Staffing.Model.Hr.Kzg;

namespace Staffing.Core.Interfaces.Hr;

public interface IKzgService
{
    Task<GetKzgTreeResponse> GetKzgPost(GetKzgTree model);
    Task<GetKzgTreeResponse> GetKzgTag(GetKzgTree model);
    Task<GetKzgRegionResponse> GetKzgRegion(GetKzgTree model);
    GetKzgUserResponse GetUser();
    Task<SendPostToKzgResponse> SendPostToKzg(SendPostToKzg model);
    GetKsPostByNkpResponse GetKsPostByNkp(GetKsPostByNkp model);
    GetXbbHtResponse GetXbbHt(GetXbbHt model);
    EmptyResponse SetXbbHt(SetXbbHt model);
    Task<EmptyResponse> XbbHtNotify(XbbHtNotify model);
    GetRemarkDicResponse GetRemarkDic();
    Task<GetRecommendEntsResponse> GetRecommendEnts(GetRecommendEnts model);
    EmptyResponse UpdateRecommendEnts(UpdateRecommendEnts model);

    Task<GetNPEntJobPositionsResponse> GetNPEntJobPositions(GetNPEntJobPositions model);

    Task<GetNPDeliverResponse> GetNPDeliverList(GetNPDeliver model);
    Task<GetOutletsResponse> GetOutlets();

    Task<GetNPUserContactResponse> GetNPUserContactList(GetNPUserContact model, string AccountID);

    Task<EmptyResponse> SaveNPUserContact(SaveNPUserContact model, string AccountID, string? AccountName);
    GetRecommendHrsResponse GetRecommendHrs();
}