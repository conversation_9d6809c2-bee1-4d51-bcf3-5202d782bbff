﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 系统设置表
/// </summary>
[Table("sys_settings")]
public class Sys_Settings
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 平台-全部佣金
    /// </summary>
    public BountyConfig? PlatformFull { get; set; }

    /// <summary>
    /// 平台-不带销售佣金
    /// </summary>
    public BountyConfig? PlatformNoSale { get; set; }

    /// <summary>
    /// 诺亚-全部佣金
    /// </summary>
    public BountyConfig? NoahFull { get; set; }

    /// <summary>
    /// 诺亚-不带销售佣金
    /// </summary>
    public BountyConfig? NoahNoSale { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;
}

public class BountyConfig
{
    /// <summary>
    /// 销售佣金
    /// </summary>
    public decimal Sales { get; set; }

    /// <summary>
    /// 项目管理佣金
    /// </summary>
    public decimal Manager { get; set; }

    /// <summary>
    /// 线索佣金
    /// </summary>
    public decimal Clue { get; set; }

    /// <summary>
    /// 邀面佣金（跟进人）
    /// </summary>
    public decimal Follower { get; set; }

    /// <summary>
    /// 平台佣金
    /// </summary>
    public decimal Platform { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;
}