﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Staffing.Entity.Staffing
{
    [Table("yunsheng_resumeid_relation")]
    public class YunSheng_ResumeId_Relation
    {
        [Key]
        public string Id { get; set; } = EntityTools.SnowflakeId();
        public string? UserId {  get; set; }
        public string? PersonnelId { get; set; }
        public string? ResumeId { get; set; }
        public DateTime? CreatedTime { get; set; }
        public DateTime? UpdatedTime { get; set; }
    }
}
