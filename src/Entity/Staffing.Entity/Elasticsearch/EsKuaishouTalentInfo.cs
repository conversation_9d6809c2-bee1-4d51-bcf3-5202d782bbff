﻿using Config.CommonModel.ThirdTalentInfo;
using Nest;

namespace Staffing.Entity.Elasticsearch;

[ElasticsearchType(IdProperty = "ApplicationId")]
public class EsKuaishouTalentInfo
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Keyword]
    public string ApplicationId { get; set; } = default!;
    /// <summary>
    /// 绑定的租户 id
    /// </summary>
    [Keyword]
    public string OpenTenantId { get; set; } = default!;
    /// <summary>
    /// 简历id
    /// </summary>
    [Keyword]
    public string? ResumeId { get; set; }
    /// <summary>
    /// 姓名
    /// </summary>
    [Wildcard]
    public string? Name { get; set; }
    /// <summary>
    /// 性别编码
    /// </summary>
    [Keyword]
    public int? GenderCode { get; set; }
    /// <summary>
    /// 性别名称
    /// </summary>
    [Keyword]
    public string? GenderName { get; set; }
    /// <summary>
    /// 手机号
    /// </summary>
    [Keyword]
    public string? Phone { get; set; }
    /// <summary>
    /// 年龄
    /// </summary>
    [Keyword]
    public int Age { get; set; }
    /// <summary>
    /// 职位id
    /// </summary>
    [Text(Index = false)]
    public string? JobId { get; set; }
    /// <summary>
    /// 快手职位名称
    /// </summary>
    [Keyword]
    public string? JobName { get; set; }
    /// <summary>
    /// 来源渠道（简历来源，直播、短视频等）
    /// </summary>
    [Keyword]
    public string? ChannelName { get; set; }
    /// <summary>
    /// 推荐人（来源主播昵称）
    /// </summary>
    [Keyword]
    public string? Recommender { get; set; }
    /// <summary>
    /// 推荐人（来源主播uid）
    /// </summary>
    [Keyword]
    public string? RecommenderId { get; set; }
    /// <summary>
    /// 报名时间
    /// </summary>
    [Date]
    public DateTime ApplyTime { get; set; }
    /// <summary>
    /// 企业id
    /// </summary>
    [Text(Index = false)]
    public string? CompanyId { get; set; }
    /// <summary>
    /// 企业中文
    /// </summary>
    [Keyword]
    public string? CompanyBusinessName { get; set; }
    /// <summary>
    /// 企业工商code
    /// </summary>
    [Keyword]
    public string? CompanyBusinessCode { get; set; }
    /// <summary>
    /// 0 - 未知的 1 - 自招职位投递 2 - 分销职位投递
    /// </summary>
    [Keyword]
    public int Platform { get; set; }
    /// <summary>
    /// 来源渠道 英文
    /// </summary>
    [Keyword]
    public string? DataChannelSourceCode { get; set; }
    /// <summary>
    /// 来源渠道 中文
    /// </summary>
    [Keyword]
    public string? DataChannelSourceName { get; set; }
    /// <summary>
    /// 简历质量标签
    /// </summary>
    [Object]
    public DataQualityLabel? DataQualityLabel { get; set; }
    /// <summary>
    /// 投递时的动态简历信息
    /// </summary>
    [Object]
    public DataDynamicResumeInfo? DataDynamicResumeInfo { get; set; }
    /// <summary>
    /// 流量来源
    /// </summary>
    [Keyword]
    public string? DataTrafficSources { get; set; }
    /// <summary>
    /// 用户所在城市
    /// </summary>
    [Keyword]
    public string? DataUserCity { get; set; }
    /// <summary>
    /// 位置信息
    /// </summary>
    [Object]
    public LocationCity DataLocationCitys { get; set; } = new LocationCity();
    /// <summary>
    /// 年龄范围的最小值
    /// </summary>
    [Keyword]
    public int? ExtInfoAgeMin { get; set; }
    /// <summary>
    /// 年龄范围的最大值
    /// </summary>
    [Keyword]
    public int? ExtInfoAgeMax { get; set; }
    /// <summary>
    /// 意向职位
    /// </summary>
    [Nested]
    public List<IntentionJob> ExtInfoIntentionJob { get; set; } = new List<IntentionJob>();
    /// <summary>
    /// 意向城市
    /// </summary>
    [Nested]
    public List<IntentionCity> ExtInfoIntentionCity { get; set; } = new List<IntentionCity>();
    /// <summary>
    /// 在职状态
    /// </summary>
    [Keyword]
    public string? ExtInfoJobHuntingStatus { get; set; }

    /// <summary>
    /// 诺快聘职位名称
    /// </summary>
    [Keyword]
    public string? PostName { get; set; }
    /// <summary>
    /// 跟进人id（顾问id）
    /// </summary>
    [Keyword]
    public string? HrId { get; set; }
    /// <summary>
    /// 跟进人名称
    /// </summary>
    [Keyword]
    public string? HrName { get; set; }
    /// <summary>
    /// 回访信息
    /// </summary>
    [Object]
    public KSHrTalentRelations? KSHrTalentRelations { get; set; }
    /// <summary>
    /// 检索关键字
    /// </summary>
    [Wildcard]
    public List<string> Search { get; set; } = default!;
}
