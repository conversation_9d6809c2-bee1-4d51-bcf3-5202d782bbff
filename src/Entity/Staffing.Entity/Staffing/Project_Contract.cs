﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 项目合同表
/// </summary>
[Table("project_contract")]
public class Project_Contract
{
    /// <summary>
    /// 合同Id
    /// </summary>
    [Key]
    public string ContractId { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 项目Id
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 签署方式
    /// </summary>
    public int SignType { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public ProjectContractSource Source { get; set; }

    /// <summary>
    /// 合同名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 签署日期
    /// </summary>
    public DateTime SignDate { get; set; }

    /// <summary>
    /// 发起日期
    /// </summary>
    public DateTime InitiationDate { get; set; }

    /// <summary>
    /// 合同开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 合同结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 发起方
    /// </summary>
    public string Initiator { get; set; } = default!;

    /// <summary>
    /// 参与方
    /// </summary>
    public string Participant { get; set; } = default!;

    /// <summary>
    /// 发起方电话
    /// </summary>
    public string InitiatorPhone { get; set; } = default!;

    /// <summary>
    /// 参与方电话
    /// </summary>
    public string ParticipantPhone { get; set; } = default!;

    /// <summary>
    /// 发起方联系人
    /// </summary>
    public string InitiatorContact { get; set; } = default!;

    /// <summary>
    /// 参与方联系人
    /// </summary>
    public string ParticipantContact { get; set; } = default!;

    /// <summary>
    /// 合同状态
    /// </summary>
    public ProjectContractStatus Status { get; set; }

    /// <summary>
    /// 合同金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 合同编号
    /// </summary>
    public string ContractNo { get; set; } = default!;

    /// <summary>
    /// 合同附件
    /// </summary>
    public List<string>? Attachment { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 默认合同
    /// </summary>
    public int IsDefault { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string CreatedBy { get; set; } = default!;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Project Project { get; set; } = default!;
    public User_Hr user_Hr { get; set; } = default!;
}
