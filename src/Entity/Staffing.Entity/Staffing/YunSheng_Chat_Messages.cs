﻿using Config.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Staffing.Entity.Staffing
{
    [Table("yunsheng_chat_messages")]
    public class YunSheng_Chat_Messages
    {
        [Key]
        public string Id { get; set; } = EntityTools.SnowflakeId();
        public required string SeekerId {  get; set; }//求职者Id
        public required string HrId {  get; set; }//招聘者Id
        public required string ChatId {  get; set; }//云生会话Id
        public required YunshengChatRole Role {  get; set; }//会话角色 1：Seeker  2：Assistant(机器人)
        public string? Content {  get; set; }//会话内容
        public required YunshengChatType Type {  get; set; }//会话类型 1：消息  2：动作
        public string? Commands {  get; set; }//动作内容     要简历：OBTAIN_RESUME_INFO    要电话：OBTAIN_TEL_INFO     要微信：OBTAIN_WECHAT_INFO
        public string? MessageId { get; set; }//消息Id
        public DateTime? MessageTime {  get; set; }//消息时间
    }
}
