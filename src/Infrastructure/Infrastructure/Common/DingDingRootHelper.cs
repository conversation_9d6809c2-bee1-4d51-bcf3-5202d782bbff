﻿using Config;
using Config.CommonModel.DingDingRoot;
using Flurl.Http;
using ServiceStack.Text;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Config.CommonModel.DingDing;
using Microsoft.Extensions.Hosting;
using AlibabaCloud.SDK.Dingtalkrobot_1_0.Models;
using AlibabaCloud.TeaUtil.Models;
using Tea;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Transient)]
public class DingDingRootHelper
{
    private readonly LogManager _log;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly RetryWithBackoff<BatchOTOQueryResponse> _retryWithBackoff;

    public DingDingRootHelper(LogManager log, IHostEnvironment hostingEnvironment, RetryWithBackoff<BatchOTOQueryResponse> retryWithBackoff)
    {
        _log = log;
        _hostingEnvironment = hostingEnvironment;
        _retryWithBackoff = retryWithBackoff;
    }

    /// <summary>
    /// 获取钉钉机器人token（依托其他接口不可单独调用）
    /// </summary>
    /// <returns></returns>
    private async Task<string> GetDingDingRootAccessToken()
    {
        string redisKey = "dingdingrootkey";
        string token = MyRedis.Client.Get(redisKey);
        if (!string.IsNullOrWhiteSpace(token))
        {
            return token;
        }
        var url = string.Format("https://oapi.dingtalk.com/gettoken?appkey={0}&appsecret={1}", Constants.DingDingRootAppKey, Constants.DingDingRootAppSecret);


        var tokenResult = await url.GetJsonAsync<DingDingRootAccountToken>();

        if (tokenResult.errcode == 0)
        {
            MyRedis.Client.Set(redisKey, token, 3600);
            return tokenResult.access_token!;
        }
        else
        {
            throw new Exception($"获取钉钉机器人token失败:{url}_{tokenResult.errcode}_{tokenResult.errmsg}");
        }
    }

    /// <summary>
    /// 钉钉机器人发送消息
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<SendDingDingRootMessageResponse?> SendDingDingRootMessage(SendDingDingRootMessageRequest model)
    {
        string token = await GetDingDingRootAccessToken();
        List<string> _userIds = new List<string>();
        _userIds.Add(model.userId);
        string url = "https://api.dingtalk.com/v1.0/robot/oToMessages/batchSend";

        try
        {
            var restr = await $"{url}".WithHeader("x-acs-dingtalk-access-token", token).WithHeader("Content-Type", "application/json")
            .PostJsonAsync(new { robotCode = Constants.DingDingRootAppKey, userIds = _userIds, msgKey = model.msgType, msgParam = model.msgParam })
            .ReceiveString();

            var resp = JsonSerializer.DeserializeFromString<SendDingDingRootMessageResponse>(restr);
            return resp;
        }
        catch (FlurlHttpException ex)
        {
            await CheckDingDingError(ex);
            return null;
        }
    }

    private async Task CheckDingDingError(FlurlHttpException ex)
    {
        var error = await ex.GetResponseJsonAsync<DingDingErrorResponse>();
        switch (error.code)
        {
            case "staffId.notExisted":
                break;
            default:
                throw new Exception($"钉钉请求错误：{JsonSerializer.SerializeToString(error)}");
        }
    }

    /// <summary>
    /// 获取客户端
    /// </summary>
    /// <returns></returns>
    public AlibabaCloud.SDK.Dingtalkrobot_1_0.Client GetClient()
    {
        return new AlibabaCloud.SDK.Dingtalkrobot_1_0.Client(new AlibabaCloud.OpenApiClient.Models.Config
        {
            Protocol = "https",
            RegionId = "central"
        });
    }

    /// <summary>
    /// 获取钉钉消息已读状态
    /// </summary>
    /// <param name="processKey"></param>
    /// <returns></returns>
    public async Task<string?> DingDingMessageReadStatus(string processKey)
    {
        string token = await GetDingDingRootAccessToken();
        AlibabaCloud.SDK.Dingtalkrobot_1_0.Client client = GetClient();

        var headers = new BatchOTOQueryHeaders()
        {
            XAcsDingtalkAccessToken = token
        };

        var request = new BatchOTOQueryRequest
        {
            RobotCode = Constants.DingDingRootAppKey,
            ProcessQueryKey = processKey,
        };

        string? readStatus = null;
        try
        {
            var resp = _retryWithBackoff.RetryAction(new Func<BatchOTOQueryResponse>(() => client.BatchOTOQueryWithOptions(request, headers, new RuntimeOptions())));
            //var resp = client.BatchOTOQueryWithOptions(request, headers, new RuntimeOptions());
            if(resp != null)
            {
                var list = resp.Body?.MessageReadInfoList;
                if(list != null && list.Count > 0)
                {
                    readStatus = list[0].ReadStatus;
                }
            }
        }
        catch (TeaException err)
        {
            if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
            {
                string result = $"{err.Code}:{err.Message}";
                throw new Exception($"获取钉钉消息已读状态失败：{result}");
            }
            else
            {
                throw;
            }
        }

        return readStatus;
    }

    /// <summary>
    /// 钉钉机器人发送markdown消息
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task SendDingDingRootMDMessage(SendDingDingRootMessageRequest model)
    {
        string token = await GetDingDingRootAccessToken();
        AlibabaCloud.SDK.Dingtalkrobot_1_0.Client client = GetClient();

        BatchSendOTOHeaders batchSendOTOHeaders = new BatchSendOTOHeaders
        {
            XAcsDingtalkAccessToken = token
        };

        BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest
        {
            MsgKey = "sampleMarkdown",
            RobotCode = Constants.DingDingRootAppKey,
            UserIds = new List<string> { model.userId },
            MsgParam = model.msgParam
        };

        try
        {
            // md消息
            var response = client.BatchSendOTOWithOptions(batchSendOTORequest, batchSendOTOHeaders, new RuntimeOptions());
        }
        catch (TeaException err)
        {
            if (!AlibabaCloud.TeaUtil.Common.Empty(err.Code) && !AlibabaCloud.TeaUtil.Common.Empty(err.Message))
            {
                string result = $"{err.Code}:{err.Message}";
                throw new Exception($"钉钉机器人发送markdown消息失败：{result}");
            }
        }
    }
}

/// <summary>
/// 退避策略,503时重试
/// </summary>
[Service(ServiceLifetime.Transient)]
public class RetryWithBackoff<T>
{
    private const int MaxRetries = 3; // 最大重试次数  
    private const double BackoffFactor = 2.0; // 退避因子  
    private const int InitialBackoffMilliseconds = 100; // 初始退避时间（毫秒）  
    private const int MaxBackoffMilliseconds = 30000; // 最大退避时间（毫秒）  

    public T RetryAction(Func<T> func)
    {
        int retryCount = 0;
        int backoffTime = InitialBackoffMilliseconds;

        while (retryCount < MaxRetries)
        {
            try
            {
                return func.Invoke(); // 尝试执行操作,如果成功则退出循环
            }
            catch (Exception) // 捕获可能抛出的任何异常  
            {
                if (++retryCount == MaxRetries) throw; // 达到最大重试次数后抛出异常  

                // 增加退避时间，但不要超过最大退避时间  
                backoffTime = Math.Min((int)(backoffTime * BackoffFactor), MaxBackoffMilliseconds);
                Thread.Sleep(backoffTime); // 等待指定的退避时间后重试  
            }
        }

        // 如果达到最大重试次数仍未成功，则可以选择抛出异常或进行其他处理  
        throw new Exception("Maximum retry attempts exceeded.");
    }
}
