﻿using Config.Enums;
using Config.CommonModel;

namespace Staffing.Model.Hr.Enterprise;

public class GetEntUsers : QueryRequest
{
    /// <summary>
    /// 人员电话或名称
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 组织架构（必填）
    /// </summary>
    public string? OrgId { get; set; }

    /// <summary>
    /// 企业Id（必填，需要区分集团组织架构）
    /// </summary>
    public string? EntId { get; set; }
}

public class GetEntUsersResponse : QueryResponse
{
    public List<GetEntUsersInfo> Rows { get; set; } = default!;
}

public class GetEntUsersInfo
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 是否是本公司（集团架构）
    /// </summary>
    public bool CurrentEnt { get; set; } = true;

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public UserStatus Status { get; set; }

    /// <summary>
    /// 状态名称
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    // /// <summary>
    // /// 权限
    // /// </summary>
    // public List<string> Powers { get; set; } = new List<string>();

    /// <summary>
    /// 角色Id
    /// </summary>
    public string? RoleId { get; set; }

    // /// <summary>
    // /// 组织架构Id
    // /// </summary>
    // public string? OrgId { get; set; }

    /// <summary>
    /// 组织架构名称
    /// </summary>
    public List<string>? OrgName { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    public string? RoleName { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? EMail { get; set; }
}


public class GetDdUsers : QueryRequest
{
    /// <summary>
    /// 人员电话或名称
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 组织架构（必填）
    /// </summary>
    public long? OrgId { get; set; }

    /// <summary>
    /// 企业Id（必填，需要区分集团组织架构）
    /// </summary>
    public string? EntId { get; set; }
}

public class GetDdUsersResponse : QueryResponse
{
    public List<GetDdUsersInfo> Rows { get; set; } = default!;
}

public class GetDdUsersInfo
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}