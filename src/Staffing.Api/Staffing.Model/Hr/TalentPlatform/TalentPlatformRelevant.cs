﻿using Config.CommonModel;
using Config.CommonModel.JavaDataApi;
using Config.Enums;

namespace Staffing.Model.Hr.TalentPlatform
{
    public class TalentPlatformRelevant
    {
    }

    #region 列表相关模型
    /// <summary>
    /// 平台人才库请求模型
    /// </summary>
    public class TalentPlatformListRequest : QueryRequest
    {
        /// <summary>
        /// 搜索（姓名、最近浏览岗位、学校、公司、电话）
        /// </summary>
        public string Search { get; set; } = string.Empty;

        /// <summary>
        /// 用户等级
        /// </summary>
        public TalentPlatformLevel? Level { get; set; }

        /// <summary>
        /// 寻访岗位
        /// </summary>
        public string? SearchPost { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 学历
        /// </summary>
        public EducationType? Education { get; set; }

        /// <summary>
        /// 渠道
        /// </summary>
        public HrProjectSource? Channel { get; set; }

        /// <summary>
        /// 城市Code集合
        /// </summary>
        public List<string>? RegionIdArray { get; set; }

        /// <summary>
        /// 最小年龄
        /// </summary>
        public int? MinAge { get; set; }

        /// <summary>
        /// 最大年龄
        /// </summary>
        public int? MaxAge { get; set; }

        /// <summary>
        /// 入库开始时间
        /// </summary>
        public string? StartTime { get; set; }

        /// <summary>
        /// 入库结束时间
        /// </summary>
        public string? EndTime { get; set; }

        /// <summary>
        /// 人才库标签id
        /// </summary>
        public string? TalentLabelId { get; set; }

        /// <summary>
        /// 渠道商Id
        /// </summary>
        public string? ChannelId { get; set; }
    }

    /// <summary>
    /// 平台人才库返回模型
    /// </summary>
    public class TalentPlatformListResponse : QueryResponse
    {
        /// <summary>
        /// 返回模型
        /// </summary>
        public List<TalentPlatformModel> Rows { get; set; } = default!;

        public int Count { get; set; } = 0;

        // /// <summary>
        // /// 人才数量
        // /// </summary>
        // public int Talent { get; set; }

        // /// <summary>
        // /// 虚拟人才库数量
        // /// </summary>
        // public int VirtualTalent { get; set; }
    }

    /// <summary>
    /// 平台人才库模型
    /// </summary>
    public class TalentPlatformModel
    {
        /// <summary>
        /// 平台人才库id
        /// </summary>
        public string? Id { get; set; } = default!;

        /// <summary>
        /// 用户姓名
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 求职者ImId
        /// </summary>
        public string? SeekerTencentImId { get; set; }

        /// <summary>
        /// 用户头像
        /// </summary>
        public string? HeadPortrait { get; set; }

        /// <summary>
        /// 是否实名
        /// </summary>
        public bool? IsRealName { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public Sex? Sex { get; set; }

        /// <summary>
        /// 性别解释
        /// </summary>
        public string? SexName { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 职业
        /// </summary>
        public OccupationType? Occupation { get; set; }

        /// <summary>
        /// 职业解释
        /// </summary>
        public string? OccupationName { get; set; }

        /// <summary>
        /// 学历
        /// </summary>
        public EducationType? Education { get; set; }

        /// <summary>
        /// 学历解释
        /// </summary>
        public string? EducationName { get; set; }

        /// <summary>
        /// 所在地
        /// </summary>
        public string? Location { get; set; }

        /// <summary>
        /// 工作经历
        /// </summary>
        public string? WorkExp { get; set; }

        /// <summary>
        /// 教育经历
        /// </summary>
        public string? EduExp { get; set; }

        /// <summary>
        /// 寻访意向
        /// </summary>
        public string? HopeIndustry { get; set; }

        /// <summary>
        /// 用户来源
        /// </summary>
        public HrProjectSource? Source { get; set; }

        /// <summary>
        /// 用户来源解释
        /// </summary>
        public string? SourceName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreatTime { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastTime { get; set; }

        /// <summary>
        /// 等级
        /// </summary>
        public TalentPlatformLevel? Level { get; set; }

        /// <summary>
        /// 等级解释
        /// </summary>
        public string? LevelName { get; set; }

        /// <summary>
        /// 活跃度
        /// </summary>
        public OnlineStatus Active { get; set; }

        /// <summary>
        /// 活跃度解释
        /// </summary>
        public string ActiveName { get; set; } = default!;

        /// <summary>
        /// 人才库标签
        /// </summary>
        public List<TalentLabelClass>? TalentLabel { get; set; }

        /// <summary>
        /// 简历二维码
        /// </summary>
        public string? HrAppletQrCode { get; set; }

        /// <summary>
        /// 用户手机号
        /// </summary>
        public string? Mobile { get; set; }

        /// <summary>
        /// 渠道商Id
        /// </summary>
        public string? ChannelId { get; set; }

        /// <summary>
        /// 渠道商名称
        /// </summary>
        public string? ChannelUserName { get; set; }
        public string? SeekerId { get; set; }
    }
    #endregion

    #region 评论相关模型
    /// <summary>
    /// 获取平台人才库评论列表请求模型
    /// </summary>
    public class TalentPlatformCommentListRequest : QueryRequest
    {
        /// <summary>
        /// 平台人才库id
        /// </summary>
        public string PlatformId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 获取平台人才库评论列表返回模型
    /// </summary>
    public class TalentPlatformCommentListResponse : QueryResponse
    {
        /// <summary>
        /// 列表集合
        /// </summary>
        public List<TalentPlatformCommentModel> Rows { get; set; } = new List<TalentPlatformCommentModel>();
    }

    /// <summary>
    /// 获取平台人才库评论列表模型
    /// </summary>
    public class TalentPlatformCommentModel
    {
        /// <summary>
        /// 评论id
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// hr姓名
        /// </summary>
        public string HrName { get; set; } = string.Empty;

        /// <summary>
        /// hr头像
        /// </summary>
        public string HeadPortrait { get; set; } = string.Empty;

        /// <summary>
        /// 评论内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 评论时间
        /// </summary>
        public DateTime CreatedTime { get; set; }
    }

    /// <summary>
    /// 添加平台人才库评论
    /// </summary>
    public class AddTalentPlatformCommentRequest
    {
        /// <summary>
        /// 平台人才库id
        /// </summary>
        public string PlatformId { get; set; } = string.Empty;

        /// <summary>
        /// 评论内容
        /// </summary>
        public string Content { get; set; } = string.Empty;
    }
    #endregion

    #region 人才库标签相关模型
    /// <summary>
    /// 平台人才库标签列表返回模型
    /// </summary>
    public class TalentPlatformLabelResponse
    {
        public List<TalentPlatformLabelModel> Rows { get; set; } = default!;
    }

    /// <summary>
    /// 平台人才库标签模型
    /// </summary>
    public class TalentPlatformLabelModel
    {
        /// <summary>
        /// 标签id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 标签名称
        /// </summary>
        public string? Name { get; set; }
    }

    /// <summary>
    /// 添加平台人才库标签
    /// </summary>
    public class EditTalentPlatformLabelRequest
    {
        /// <summary>
        /// 平台人才库id
        /// </summary>
        public string? PlatformId { get; set; }

        /// <summary>
        /// 标签id
        /// </summary>
        public List<string> DicLabelId { get; set; } = new List<string>();
    }
    #endregion


    public class ChannelInfo
    {
        public string? ChannelId { get; set; }
        public string? ChannelUserName { get; set; }
    }
}
