<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\net8.0\Staffing.Model.xml</DocumentationFile>
    <NoWarn>1701;1702;1591;NU1803;CS8981;</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Entity\Staffing.Entity\Staffing.Entity.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Hr\" />
    <None Remove="Seeker\" />
    <None Remove="Admin\" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
  </ItemGroup>
</Project>
