using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Org.Apache.Rocketmq;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Infrastructure.Common;
using Infrastructure.Extend;
using ServiceStack;
using Config;
using Config.Enums;
using FreeRedis;
using Newtonsoft.Json;


namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class CanalConsumerService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger)
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory = contextFactory;
    private readonly LogManager _logger = logger;

    public void CanalConsumer(String message)
    {
        try
        {
            _logger.Info($"CanalConsumer:{message}", "");
            if (!String.IsNullOrEmpty(message))
            {
                using var _context = _contextFactory.CreateDbContext();
                var entry = JsonConvert.DeserializeObject<CanalRocketResp>(message);
                if (entry is { IsDdl: true })
                {
                    return;
                }
                var tbName = entry!.Table?.ToLower();
                var pkName = entry.PkNames?.FirstOrDefault() ?? String.Empty;
                var ids = entry.Data?
                    .Where(item => item[pkName] != null)
                    .Select(item => item[pkName]!.ToString())
                    .ToList();
                if (tbName == "talent_platform")
                {
                    MyRedis.Client.SAdd(SubscriptionKey.TalentTableChange, ids!.Select(s => new Sub_TalentTableChange { TalentId = s }).ToArray());
                }
                else if (tbName == "talent_virtual")
                {
                    MyRedis.Client.SAdd(SubscriptionKey.VirtualTalentTableChange, ids!
                        .Select(s => new Sub_TalentTableChange { TalentId = s }).ToArray());
                }
                else if (tbName == "talent_label")
                {
                    var talentLabel = _context.Talent_Label.Where(x => ids!.Contains(x.Id))
                        .Select(s => new
                        {
                            s.PlatformId,
                            s.VirtualId
                        }).ToList();

                    var platforms = talentLabel.Where(x => !string.IsNullOrEmpty(x.PlatformId))
                        .Select(s => new Sub_TalentTableChange
                        {
                            TalentId = s.PlatformId
                        }).Distinct().ToArray();

                    var virtuals = talentLabel.Where(x => !string.IsNullOrEmpty(x.VirtualId))
                        .Select(s => new Sub_TalentTableChange
                        {
                            TalentId = s.VirtualId
                        }).Distinct().ToArray();

                    if (platforms.Any())
                        MyRedis.Client.SAdd(SubscriptionKey.TalentTableChange, platforms);
                    else if (virtuals.Any())
                        MyRedis.Client.SAdd(SubscriptionKey.VirtualTalentTableChange, virtuals);
                }
                else if (new string[] { "user_extend", "user_seeker", "user_resume" }.Contains(tbName))
                {
                    var talents = _context.Talent_Platform.Where(x => ids!.Contains(x.SeekerId!))
                        .Select(s => new Sub_TalentTableChange
                        {
                            TalentId = s.Id
                        }).Distinct().ToArray();

                    if (talents.Any())
                        MyRedis.Client.SAdd(SubscriptionKey.TalentTableChange, talents);
                    if (new string[] { "user_seeker", "user_resume" }.Contains(tbName))
                    {
                        var seekerIds = _context.User_Seeker.Where(x => ids!.Contains(x.UserId)).
                            Select(s => s.UserId).ToArray();
                        if (seekerIds.Any())
                        {
                            foreach (var seekerid in seekerIds)
                            {
                                _logger.Info("seekerid: ", $"{seekerid}");
                            }
                            MyRedis.Client.SAdd(SubscriptionKey.UpdateYunshengPlatform, seekerIds);
                        }
                    }
                    var isNewSeeker = String.Equals(tbName, "user_seeker") && String.Equals(entry.Type, "INSERT");
                    // 求职者信息变更
                    MyRedis.Client.SAdd(SubscriptionKey.UpdateSeeker, ids!.Select(s => new Sub_UpdateSeeker
                    {
                        SeekerId = s,
                        IsNewSeeker = isNewSeeker
                    }).ToArray());
                }
                else if (tbName == "user_work")
                {
                    var userIds = entry.Data!
                        .Where(item => item["UserId"] != null)
                        .Select(item => item["UserId"]!.ToString())
                        .ToList();
                    var talents = _context.Talent_Platform.Where(x => userIds.Contains(x.SeekerId!))
                        .Select(s => new Sub_TalentTableChange
                        {
                            TalentId = s.Id
                        }).Distinct().ToArray();

                    if (talents.Any())
                        MyRedis.Client.SAdd(SubscriptionKey.TalentTableChange, talents);

                    // 求职者信息变更
                    MyRedis.Client.SAdd(SubscriptionKey.UpdateSeeker, ids!.Select(s => new Sub_UpdateSeeker
                    {
                        SeekerId = s
                    }).ToArray());
                }
                else if (new string[] { "talent_virtual_work", "talent_virtual_edu", "talent_virtual_hope" }.Contains(tbName))
                {
                    var virtuals = entry.Data!
                        .Where(item => item["VirtualId"] != null)
                        .Select(item => new Sub_TalentTableChange
                        {
                            TalentId = item["VirtualId"]!.ToString()
                        })
                        .Distinct().ToArray();
                    if (virtuals.Any())
                        MyRedis.Client.SAdd(SubscriptionKey.VirtualTalentTableChange, virtuals);
                }
                else if (new string[] { "post" }.Contains(tbName))
                {
                    var pcModel = new Sub_PostChange
                    {
                        PostId = ids!.First()
                    };

                    //检测职位状态变更
                    if (entry.Old != null)
                    {
                        var isStatusChange = entry.Old.Any(item => item["Status"] != null);
                        var isShareChange = entry.Old.Any(item => item["Share"] != null);
                        if (isStatusChange || isShareChange)
                            pcModel.StatusChanged = true;
                        //检测坐标变化,生成职位地图
                        var isLocationChange = entry.Old.Any(item => item["Location"] != null);
                        if (isLocationChange)
                            pcModel.LocationChanged = true;
                    }
                    else
                    {
                        pcModel.StatusChanged = true;
                        pcModel.LocationChanged = true;
                    }

                    MyRedis.Client.SAdd(SubscriptionKey.Canal.PostChange, pcModel);
                }
                else if (new string[] { "post_team" }.Contains(tbName))
                {
                    _logger.Info($"post_team:{tbName}", "");
                    if (entry.Old != null)
                    {
                        //检测职位状态变更
                        var isStatusChange = entry.Old.Any(item => item["Status"] != null);
                        var isShareChange = entry.Old.Any(item => item["Show"] != null);
                        if (isStatusChange || isShareChange)
                            MyRedis.Client.SAdd(SubscriptionKey.Canal.TeamPostStatusChange, new Sub_TeamPostStatusChange
                            {
                                TeamPostId = ids!.First()!,
                                isNew = String.Equals(entry.Type, "INSERT")
                            });

                    }
                    else
                    {

                        MyRedis.Client.SAdd(SubscriptionKey.Canal.TeamPostStatusChange, new Sub_TeamPostStatusChange
                        {
                            TeamPostId = ids!.First()!,
                            isNew = true
                        });

                    }
                }
                else if (new string[] { "recruit" }.Contains(tbName))
                {

                    //检测招聘流程状态变更
                    var isStatusChange = (entry.Old?.Any(item => item["Status"] != null) ?? false);
                    if (isStatusChange || String.Equals(entry.Type, "INSERT"))
                    {
                        var statusStr = String.Equals(entry.Type, "INSERT") ? (String)entry.Data![0]["Status"]! : (String)entry.Old![0]["Status"]!;
                        int.TryParse(statusStr, out int status);
                        MyRedis.Client.RPush(SubscriptionKey.RecruitStatusChange, new Sub_Recruit_StatusChange
                        {
                            RecruitId = ids!.First(),
                            Status = (RecruitStatus)status,
                            IsNew = String.Equals(entry.Type, "INSERT")
                        });
                    }
                    // 归档状态改变
                    var isFileAwayChange = (entry.Old?.Any(item => item["FileAway"] != null) ?? false);
                    if (String.Equals(entry.Type, "INSERT") || isFileAwayChange)
                    {
                        var statusStr = String.Equals(entry.Type, "INSERT") ? (String)entry.Data![0]["FileAway"]! : (String)entry.Old![0]["FileAway"]!;
                        int.TryParse(statusStr, out int status);
                        MyRedis.Client.RPush(SubscriptionKey.RecruitStatusChange2, new Sub_Recruit_StatusChange
                        {
                            RecruitId = ids!.First()!,
                            Status = (RecruitStatus)status,
                            IsNew = String.Equals(entry.Type, "INSERT")
                        });
                    }
                }
                else if (new string[] { "post_bounty" }.Contains(tbName))
                {
                    MyRedis.Client.SAdd(SubscriptionKey.TeamBountyChange, new Sub_TeamBountyChange
                    {
                        Id = ids!.First()
                    });
                }
                else if (new string[] { "user_agent_ent" }.Contains(tbName))
                {
                    //如果诺快聘账号协同了职位
                    var postTeam = _context.Post_Team.Where(x => x.Project_Team.HrId == Constants.PlatformHrId && ids!.Contains(x.Post.Project.AgentEntId!))
                        .Select(s => s.TeamPostId).Distinct().ToArray();

                    if (postTeam.Any())
                    {
                        var ut = DateTime.Now.ToUnixTimeMs();
                        MyRedis.Client.ZAdd(SubscriptionKey.Canal.TeamNkpTeamPostChange, postTeam.Select(s => new ZMember(s, ut)).ToArray());
                    }
                }
                else if (new string[] { "user_hr" }.Contains(tbName))
                {
                    //如果是诺快聘账号
                    if (ids!.Contains(Constants.PlatformHrId))
                    {
                        // var hr = new Sub_NkpHrChange
                        // {
                        //     HrId = primaryValues.First()
                        // };
                        MyRedis.Client.ZAdd(SubscriptionKey.Canal.HrChange, DateTime.Now.ToUnixTimeMs(), ids.First());
                    }
                }
                else if (new string[] { "user" }.Contains(tbName))
                {
                    //人才库变更
                    var talents = _context.Talent_Platform.Where(x => ids!.Contains(x.SeekerId!))
                        .Select(s => new Sub_TalentTableChange
                        {
                            TalentId = s.Id
                        }).Distinct().ToArray();

                    if (talents.Any())
                        MyRedis.Client.SAdd(SubscriptionKey.TalentTableChange, talents);

                    var seekerIds = _context.User_Seeker.Where(x => ids!.Contains(x.UserId)).
                        Select(s => s.UserId).ToArray();
                    if (seekerIds.Any())
                    {
                        foreach (var seekerid in seekerIds)
                        {
                            _logger.Info("seekerid: ", $"{seekerid}");
                        }
                        MyRedis.Client.SAdd(SubscriptionKey.UpdateYunshengPlatform, seekerIds);
                    }

                    //如果是诺快聘账号
                    if (ids!.Contains(Constants.PlatformHrId))
                    {
                        var isMobileChange = (entry.Old?.Any(item => item["Mobile"] != null) ?? false);
                        if (String.Equals(entry.Type, "INSERT") || isMobileChange)
                        {
                            // var hr = new Sub_NkpHrChange
                            // {
                            //     HrId = primaryValues.First()
                            // };
                            MyRedis.Client.ZAdd(SubscriptionKey.Canal.HrChange, DateTime.Now.ToUnixTimeMs(), ids.First());
                        }
                    }
                }
                else if (new string[] { "talent_resume" }.Contains(tbName))
                {
                    MyRedis.Client.SAdd(SubscriptionKey.TalentResumeChange, ids!.ToArray());
                }

            }
        }
        catch (Exception ex)
        {
            _logger.Error($"CanalConsumer:{message}", ex.Message, ex);
        }


    }

}