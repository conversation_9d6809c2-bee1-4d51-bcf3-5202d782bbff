﻿namespace Staffing.Model.Seeker.User;

public class GetSeekerBehaviorResponse
{
    /// <summary>
    /// 最近访问该hr的职位类别
    /// </summary>
    public List<PostCategoryVisitedInfo> PostCategoryVisited { get; set; } = new List<PostCategoryVisitedInfo>();
}

public class PostCategoryVisitedInfo
{
    /// <summary>
    /// 类别id
    /// </summary>
    public int Category { get; set; }

    /// <summary>
    /// 背景图
    /// </summary>
    public string? Logo { get; set; }

    /// <summary>
    /// 类别名称
    /// </summary>
    public string? CategoryName { get; set; }

    /// <summary>
    /// 次数
    /// </summary>
    public int Times { get; set; }
}
