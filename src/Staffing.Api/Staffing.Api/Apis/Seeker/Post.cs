using Config;
using Config.CommonModel.Business;
using Infrastructure.Common;
using Infrastructure.Extend;
using Staffing.Model.Seeker.Post;
using Staffing.Core.Interfaces.Seeker;

namespace Staffing.Api.Apis.Seeker;

[Service(ServiceLifetime.Transient)]
public class PostRouter : ISeekerRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("post").WithTags("职位");
        group.MapGet("/adviser/hotkeywords", (IPostService rep) => rep.PostHotKeywords())
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "顾问的热门搜索" });

        group.MapGet("/thirdparty/data", (IPostService rep) => rep.GetHomePageThirdPartyData())
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "诺聘首页三方数据" });

        group.MapGet("/adviser/industry", (IPostService rep) => rep.GetPostProjectIndustry())
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "顾问的项目行业" });

        group.MapGet("/adviser/city", ([AsParameters] GetAdviserCity model, IPostService rep) => rep.GetAdviserCity(model))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "顾问开通的城市" });

        group.MapGet("/excellent/city", ([AsParameters] GetAdviserCity model, IPostService rep) => rep.GetExcellentCity(model))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "职位优选开通的城市" });

        group.MapPost("", (SeekerGetPosts model, IPostService rep) => rep.SeekerGetPosts(model))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "职位列表" });

        group.MapGet("/info", async ([AsParameters] SeekerGetPost model, IPostService rep) =>
        {
            var result = await rep.SeekerGetPost(model);
            result.Describe = Tools.HandleStringByMark(result.Describe, "/*", "*/");
            return result;
        })
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "职位详情" });

        group.MapGet("/esinfo", async (string? TeamPostId, IPostService rep) =>
        {
            if (string.IsNullOrEmpty(TeamPostId))
                return null;

            var result = await rep.SeekerGetESPost(TeamPostId);
            if (result != null)
                result.Describe = Tools.HandleStringByMark(result.Describe, "/*", "*/");
            return result;
        })
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "获取同步到ES的职位详情" });

        group.MapPost("/shareqrcode", async (GetSeekerShareQrCode model, IPostService rep) =>
        {
            var result = await rep.GetSeekerShareQrCode(model);
            return result;
        })
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "获取分享二维码" });

        group.MapPost("/recommend", (GetRecommendPosts model, IPostService rep) =>
        {
            var result = rep.SeekerGetPosts(new SeekerGetPosts
            {
                RecommendTeamPostId = model.TeamPostId
            });
            result.Rows = result.Rows.Where(x => x.TeamPostId != model.TeamPostId).ToList();
            return result;
        })
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "相关推荐" });

        group.MapGet("/ent", async ([AsParameters] SeekerGetEnt model, IPostService rep) =>
        {
            var result = await rep.SeekerGetEnt(model);
            return result;
        })
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "代招企业详情" });

        group.MapGet("/project/industry", ([AsParameters] SeekerGetProjectIndustry model, IPostService rep) => rep.SeekerGetProjectIndustry(model))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "项目行业信息" });

        group.MapPost("/project", (SeekerGetProjects model, IPostService rep) => rep.SeekerGetProjects(model))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "项目列表" });
    }
}