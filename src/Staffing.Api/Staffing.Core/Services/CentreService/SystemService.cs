﻿using System.Data.Entity;
using Config.Enums;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces;
using Staffing.Core.Interfaces.CentreService;
using Staffing.Core.Interfaces.Common;
using Config;
using Staffing.Model.CentreService.System;
using EntityFrameworkCore.AutoHistory.Extensions;
using ServiceStack.Text;
using Microsoft.EntityFrameworkCore;

namespace Staffing.Core.Services.CentreService;

[Service(ServiceLifetime.Transient)]
public class SystemService : ISystemService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private ISharedService _sharedService;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly Infrastructure.Common.SMSHelper _commonSMSHelper;
    private readonly ICommonService _commonService;
    public SystemService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, LogManager log, ISharedService sharedService,
        Infrastructure.Common.SMSHelper commonSMSHelper, ICommonService commonService)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _log = log;
        _sharedService = sharedService;
        _commonSMSHelper = commonSMSHelper;
        _commonService = commonService;
    }

    public SendSmsResponse SendSms(SendSms model)
    {
        var result = new SendSmsResponse();

        // if (string.IsNullOrWhiteSpace(model.Mobiles))
        //     throw new BadRequestException("请填写手机号");

        // using var sr = new StringReader(model.Mobiles);
        // var line = string.Empty;

        // var mb = new List<string>();
        // while ((line = sr.ReadLine()) != null)
        // {
        //     if (!string.IsNullOrWhiteSpace(line))
        //         mb.Add(line.Trim());
        // }

        // if (mb.Count == 0)
        //     throw new BadRequestException("缺少手机号");

        // var dbmb = _context.aaa.AsNoTracking().Where(x => mb.Contains(x.mobile)).ToList();

        // if (dbmb.Count == 0)
        //     throw new BadRequestException("手机号不存在");

        // var tsk = new Sms_Tasks
        // {
        //     Name = string.Empty,
        //     Status = SmsTasksStatus.进行中,
        //     ExeTime = DateTime.Now,
        //     Worker = 1
        // };
        // _context.Add(tsk);

        // var tskdetials = dbmb.Select(s => new Sms_Tasks_Detail
        // {
        //     Sms_Tasks = tsk,
        //     JsonData = JsonSerializer.SerializeToString(new
        //     {
        //         xingming = s.xingming,
        //         time = s.time,
        //         shijuan = s.shijuan,
        //         url = s.url,
        //         neirong = s.neirong
        //     }),
        //     Mobile = s.mobile,
        //     TempCode = "SMS_264050305",
        //     Status = SmsTasksDetailStatus.待处理
        // });
        // _context.AddRange(tskdetials);
        // _context.SaveChanges();

        // var ntm = mb.Except(dbmb.Select(s => s.mobile));
        // result.NotExistsMobiles = ntm.ToList();

        return result;
    }

    /// <summary>
    /// 发送自定义内容的短信
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    /// <exception cref="BadRequestException"></exception>
    public async Task<EmptyResponse> SendSmsCustomContent(SendSmsCustom model)
    {
        var result = new SendSmsResponse();

        if (string.IsNullOrEmpty(model.Content))
            throw new BadRequestException("请填写短信内容");
        if (model.Mobiles == null || model.Mobiles.Length == 0)
            throw new BadRequestException("请填写手机号");
        if (!_commonSMSHelper.CheckSMSSignature(model.Content!, out var checkTip))
            throw new BadRequestException(checkTip + "，请按提示调整后重试");

        var mb = new List<string>();
        List<string> badMobiles = new List<string>();
        model.Mobiles.ForEach((string item) =>
        {
            if (!string.IsNullOrWhiteSpace(item))
            {
                if (!item.Trim().IsMobile())//验证手机号是否正确
                    badMobiles.Add(item.Trim());
                else
                    mb.Add(item.Trim());
            }
        });

        if (badMobiles.Count > 0)
            throw new BadRequestException($"{string.Join(',', badMobiles)}是非法手机号");
        else if (mb.Count == 0)
            throw new BadRequestException("缺少手机号");

        int errorIndex = -2;//-2表示无需立即发送，-1表示立即发送成功，大于-1表示到指定手机号处发送失败了
        //判断是否可以批量发布(内容中是否包含小程序链接；若包含 因链接需每人生成1个，所以此时只能逐条发送)
        SmsTasksSenderType SenderType = string.IsNullOrEmpty(model.Url) ? SmsTasksSenderType.庄点科技drondea批量 : SmsTasksSenderType.庄点科技drondea逐条;
        if (SenderType == SmsTasksSenderType.庄点科技drondea批量)
        {
            errorIndex = -1;
            //直接发送
            int pIndex = 1, pSize = 500;
            do
            {
                var sendResult = await _commonSMSHelper.ZhuangDianSendSMS(mb.Skip((pIndex - 1) * pSize).Take(pSize).ToArray(), model.Content!);
                if (!sendResult.Success)
                    errorIndex = (pIndex - 1) * pSize;
                pIndex++;
            } while ((pIndex - 1) * pSize < mb.Count);
        }


        var tsk = new Sms_Tasks
        {
            Name = string.Empty,
            Status = SenderType == SmsTasksSenderType.庄点科技drondea逐条 ? SmsTasksStatus.进行中 : (errorIndex == -1 ? SmsTasksStatus.已完成 : SmsTasksStatus.失败),
            ExeTime = DateTime.Now,
            CreatedTime = DateTime.Now,
            Worker = mb.Count,
            SenderType = SenderType,
            TaskTemplateContent = string.IsNullOrEmpty(model.Url) ? model.Content! : (
                model.Content.IndexOf($"[url] ") == -1 ? model.Content.Replace($"[url]", model.Url + " ") : model.Content.Replace($"[url]", model.Url)
               )
        };
        _context.Add(tsk);

        var tskdetials = mb.Select((s, i) => new Sms_Tasks_Detail
        {
            Sms_Tasks = tsk,
            JsonData = JsonSerializer.SerializeToString(new
            { Url = model.Url }),
            Mobile = s,
            TempCode = "",
            Status = errorIndex == -2 ? SmsTasksDetailStatus.待处理 : ((errorIndex == -1 || errorIndex < i) ? SmsTasksDetailStatus.成功 : (errorIndex == i) ? SmsTasksDetailStatus.失败 : SmsTasksDetailStatus.待处理)
        });
        _context.AddRange(tskdetials);
        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 获取自定义内容短信
    /// </summary>
    public GetSmsCustomResponse GetSendSmsCustomList(GetSmsCustom model)
    {
        //组装筛选条件
        var predicate = PredicateBuilder.New<Sms_Tasks>(x => x.SenderType == SmsTasksSenderType.庄点科技drondea逐条 || x.SenderType == SmsTasksSenderType.庄点科技drondea批量);
        if (!string.IsNullOrEmpty(model.BeginTime) && DateTime.TryParse(model.BeginTime, out var bt))
            predicate.And(x => x.CreatedTime >= bt);
        if (!string.IsNullOrEmpty(model.EndTime) && DateTime.TryParse(model.EndTime, out var et))
        {
            et = et.Date.AddDays(1).AddSeconds(-1);
            predicate.And(x => x.CreatedTime <= et);
        }
        if (!string.IsNullOrEmpty(model.Content))
            predicate.And(x => x.TaskTemplateContent.Contains(model.Content));

        var sql = _context.Sms_Tasks.Where(predicate);

        var result = new GetSmsCustomResponse() { Total = sql.Count() };

        //读取DB
        result.Rows = sql
              .OrderByDescending(o => o.CreatedTime)
              .Skip((model.PageIndex - 1) * model.PageSize)
              .Take(model.PageSize)
              .Select(x => new SmsCustomRecord()
              {
                  Id = x.Id,
                  Content = x.TaskTemplateContent,
                  CreatedTime = x.CreatedTime,
                  MobileCount = x.Worker,
                  Status = x.Status
              }).ToList();
        return result;
    }

    public GetSystemAppTreeResponse GetSystemAppTree(GetSystemAppTree model)
    {
        var result = new GetSystemAppTreeResponse();

        var predicate = PredicateBuilder.New<Sys_App>(true);

        if (model.Show.HasValue)
            predicate = predicate.And(x => x.Show == model.Show);

        if (model.Status.HasValue)
            predicate = predicate.And(x => x.Status == model.Status);

        var app = _context.Sys_App.Where(predicate)
        .OrderBy(o => o.Sort)
        .ThenBy(o => o.Id)
        .Select(s => new GetSystemAppTreeInfo
        {
            Id = s.Id,
            Name = s.Name,
            AppId = s.Id,
            Icon = s.Icon,
            ParentId = s.ParentId,
            Status = s.Status
        }).ToList();

        var treeNotes = Tools.GetChildrenTreeNode(app, x => x.Id, y => y.ParentId, string.Empty);
        result.Rows = Tools.ModelConvert<List<GetSystemAppTreeInfo>>(treeNotes);

        return result;
    }

    public SystemAppInfo GetSystemApp(string? id)
    {
        var result = _context.Sys_App.Where(x => x.Id == id)
        .Select(s => new SystemAppInfo
        {
            Id = s.Id,
            Name = s.Name,
            AppId = s.Id,
            Icon = s.Icon,
            ParentId = s.ParentId,
            Abstract = s.Abstract,
            AdminId = s.AdminId,
            AdminName = s.AdminName,
            Alias = s.Alias,
            AppInfo = s.AppInfo,
            CreatedTime = s.CreatedTime,
            Level = s.Level,
            Manual = s.Manual,
            ShortAbstract = s.ShortAbstract,
            Show = s.Show,
            Sort = s.Sort,
            Status = s.Status,
            Type = s.Type,
            UpdatedTime = s.UpdatedTime,
            Url = s.Url,
            PowersStr = s.Powers
        }).FirstOrDefault();

        if (result == null)
            throw new NotFoundException("内容不存在");

        result.Powers = result.PowersStr?.Split(',')?.Where(x => !string.IsNullOrWhiteSpace(x)).ToList() ?? new List<string>();

        return result;
    }

    public AddSystemAppResponse AddSystemApp(AddSystemApp model)
    {
        var result = new AddSystemAppResponse();

        if (string.IsNullOrWhiteSpace(model.Name))
            throw new BadRequestException("名称不能为空");

        var level = string.Empty;

        if (!string.IsNullOrWhiteSpace(model.ParentId))
        {
            var parent = _context.Sys_App
            .Where(x => x.Id == model.ParentId)
            .Select(s => new
            {
                s.Id,
                s.Level
            }).FirstOrDefault();

            if (parent == null)
                throw new BadRequestException("上级菜单无效");

            if (parent.Level.Count(c => c == '.') >= 1)
                throw new BadRequestException("最多2级");

            level = parent.Level;
        }

        var app = new Sys_App
        {
            Name = model.Name,
            ParentId = model.ParentId ?? string.Empty,
            Level = level,
            Abstract = model.Abstract,
            AdminId = _user.NuoPinId ?? string.Empty,
            AdminName = _user.Account ?? string.Empty,
            Alias = model.Alias,
            AppInfo = model.AppInfo,
            Manual = model.Manual,
            Icon = model.Icon,
            ShortAbstract = model.ShortAbstract,
            Show = model.Show,
            Sort = model.Sort,
            Status = ActiveStatus.Active,
            Type = model.Type,
            Url = model.Url,
            Powers = string.Join(",", model.Powers ?? new List<string>())
        };
        _context.Add(app);

        _context.SaveChanges();

        result.AppId = app.Id;

        RemoveAppsCache();
        return result;
    }

    public EmptyResponse UpdateSystemApp(UpdateSystemApp model)
    {
        var result = new EmptyResponse();

        if (string.IsNullOrWhiteSpace(model.Name))
            throw new BadRequestException("名称不能为空");

        var app = _context.Sys_App.Where(x => x.Id == model.Id).FirstOrDefault();

        if (app == null)
            throw new NotFoundException("内容不存在");

        app.Name = model.Name;
        app.ParentId = model.ParentId ?? string.Empty;
        app.Abstract = model.Abstract;
        app.AdminId = _user.NuoPinId ?? string.Empty;
        app.AdminName = _user.Account ?? string.Empty;
        app.Alias = model.Alias;
        app.AppInfo = model.AppInfo;
        app.Manual = model.Manual;
        app.Icon = model.Icon;
        app.ShortAbstract = model.ShortAbstract;
        app.Show = model.Show;
        app.Sort = model.Sort;
        app.Status = ActiveStatus.Active;
        app.Type = model.Type;
        app.Url = model.Url;

        if (model.Powers != null)
            app.Powers = string.Join(",", model.Powers ?? new List<string>());

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        RemoveAppsCache();
        return result;
    }

    public EmptyResponse DeleteSystemApp(DeleteSystemApp model)
    {
        var result = new EmptyResponse();

        if (_context.Sys_App.Any(x => x.ParentId == model.Id))
            throw new BadRequestException("请先删除子节点");

        var app = _context.Sys_App.Where(x => x.Id == model.Id).FirstOrDefault();

        if (app != null)
            _context.Remove(app);

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        RemoveAppsCache();
        return result;
    }

    public EmptyResponse UpdateSystemAppStatus(UpdateSystemAppStatus model)
    {
        var result = new EmptyResponse();

        if (!model.Status.HasValue)
            throw new BadRequestException("无效的状态");

        var app = _context.Sys_App.Where(x => x.Id == model.Id).FirstOrDefault();

        if (app == null)
            throw new NotFoundException("内容不存在");

        if (string.IsNullOrWhiteSpace(app.ParentId))
            throw new BadRequestException("根节点不允许操作");

        app.Status = model.Status.Value;

        _context.SaveChanges();

        RemoveAppsCache();

        return result;
    }

    public void RemoveAppsCache()
    {
        _cacheHelper.RemoveRedisCache(RedisKey.Dic.Apps);
        _cacheHelper.RemoveRedisCache(RedisKey.Dic.QuickJobApps);
    }

    public async Task<EmptyResponse> AddUserSms(AddUserSms model)
    {
        var lockerKey = $"updateusersms_{model.UserId}";

        //redis分布式锁，自动释放
        using var locker = await MyRedis.Lock(lockerKey, 10);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        if (!_context.User_Hr.Any(x => x.UserId == model.UserId))
            throw new BadRequestException("用户不存在");

        if (model.Amount == 0)
            throw new BadRequestException("数量无效");

        var userSms = _context.User_Sms.FirstOrDefault(x => x.UserId == model.UserId);
        if (userSms == null)
        {
            userSms = new User_Sms
            {
                UserId = model.UserId!,
                BusinessSms = 0
            };
            _context.Add(userSms);
        }

        userSms.BusinessSms += model.Amount;

        if (userSms.BusinessSms < 0)
            throw new BadRequestException("最终数量不能为负数");

        var userSmsRecord = new User_Sms_Record
        {
            Type = UserSmsRecordType.充值,
            Amount = model.Amount,
            UserId = model.UserId!,
            Content = "后台充值",
            Balance = userSms.BusinessSms
        };
        _context.Add(userSmsRecord);

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public GetWithdrawRecordResponse GetWithdrawRecord(GetWithdrawRecord model)
    {
        var result = new GetWithdrawRecordResponse();

        model.UserType = model.UserType.HasValue ? model.UserType : SeekerOrHr.Seeker;

        var predicate = PredicateBuilder.New<User_Withdraw>(x => x.UserType == model.UserType);

        if (model.Status.HasValue)
            predicate = predicate.And(x => x.Status == model.Status);

        if (!string.IsNullOrWhiteSpace(model.UserName))
            if (model.UserType == SeekerOrHr.Seeker)
                predicate = predicate.And(x => x.User_Seeker.NickName.Contains(model.UserName));
            else if (model.UserType == SeekerOrHr.Hr)
                predicate = predicate.And(x => x.User_Hr.NickName.Contains(model.UserName));

        if (!string.IsNullOrWhiteSpace(model.Mobile))
            if (model.UserType == SeekerOrHr.Seeker)
                predicate = predicate.And(x => x.User_Seeker.User.Mobile == model.Mobile);
            else if (model.UserType == SeekerOrHr.Hr)
                predicate = predicate.And(x => x.User_Hr.User.Mobile == model.Mobile);

        if (model.BeginTime.HasValue)
            predicate = predicate.And(x => x.CreatedTime >= model.BeginTime.Value);

        if (model.EndTime.HasValue)
        {
            model.EndTime = model.EndTime.Value.Date.AddDays(1);
            predicate = predicate.And(x => x.CreatedTime < model.EndTime.Value);
        }

        var sql = _context.User_Withdraw.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new WithdrawRecordInfo
        {
            Id = s.Id,
            Amount = s.Amount,
            UserType = s.UserType,
            Mobile = s.User_Seeker.User.Mobile,
            UserName = s.User_Seeker.NickName,
            Account = s.Account,
            AccountName = s.AccountName,
            ApprovalTime = s.ApprovalTime,
            Approver = s.Approver,
            Remark = s.Remark,
            Status = s.Status,
            StatusName = s.Status.GetDescription(),
            UserId = s.UserId,
            CreatedTime = s.CreatedTime
        }).ToList();

        return result;
    }

    public async Task<EmptyResponse> WithdrawChangeStatus(WithdrawChangeStatus model)
    {
        var withdraw = _context.User_Withdraw.FirstOrDefault(x => x.Id == model.Id);

        if (withdraw == null)
            throw new BadRequestException("内容不存在");

        if (withdraw.Status != WithdrawStatus.提现失败)
            throw new BadRequestException("仅支持对失败状态的提现进行修改");

        if (model.Status != WithdrawStatus.提现完成 && model.Status != WithdrawStatus.退还)
            throw new BadRequestException("无效的状态");

        withdraw.Status = model.Status.Value;

        //锁用户钱包
        var lockerKey = $"{RedisKey.Balance.LockKey}{withdraw.UserId}";
        using var locker = await MyRedis.Lock(lockerKey, 15);

        if (locker == null)
            throw new Exception("提现获取钱包锁失败");

        if (withdraw.Status == WithdrawStatus.退还)
        {
            var currentBalance = 0M;

            var balance = _context.User_Balance.Where(x => x.UserId == withdraw.UserId).First();
            if (withdraw.UserType == SeekerOrHr.Seeker)
            {
                balance.SeekerBalance += withdraw.Amount;
                currentBalance = balance.SeekerBalance;
            }
            else if (withdraw.UserType == SeekerOrHr.Hr)
            {
                balance.HrBalance += withdraw.Amount;
                currentBalance = balance.HrBalance;
            }
            else
                throw new Exception("不支持的操作");

            var balanceRecord = new User_Balance_Record
            {
                Amount = currentBalance,
                Increment = withdraw.Amount,
                UserType = withdraw.UserType,
                Type = BalanceRecordType.提现退还,
                Content = "提现失败退还",
                UserId = withdraw.UserId
            };
            _context.Add(balanceRecord);
        }

        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 保存广告
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse AdvertSave(AdvertSave model)
    {
        if (model.LinkType == null)
            throw new BadRequestException("链接类型错误");
        if (model.State == null)
            throw new BadRequestException("广告状态错误");
        if (string.IsNullOrEmpty(model.ImageUrl))
            throw new BadRequestException("轮播图不允许为空");

        if (string.IsNullOrEmpty(model.Id))
        {
            Advert_Set addModel = new Advert_Set()
            {
                ImageUrl = model.ImageUrl,
                LinkUrl = model.LinkUrl ?? String.Empty,
                LinkType = model.LinkType!.Value,
                State = model.State!.Value,
                Sort = model.Sort
            };

            _context.Add(addModel);
        }
        else
        {
            var updateModel = _context.Advert_Set.FirstOrDefault(x => x.Id == model.Id);
            if (updateModel == null)
                throw new BadRequestException("广告不存在");
            updateModel.ImageUrl = model.ImageUrl;
            updateModel.LinkUrl = model.LinkUrl ?? String.Empty;
            updateModel.LinkType = model.LinkType!.Value;
            updateModel.State = model.State!.Value;
            updateModel.Sort = model.Sort;
        }
        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 分页获取广告列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public AdvertPageResponse AdvertGetPageList(QueryRequest model)
    {
        if (model.PageIndex < 1)
            model.PageIndex = 1;
        if (model.PageSize < 1)
            model.PageSize = 20;
        var sql = _context.Advert_Set;
        var result = new AdvertPageResponse() { Total = sql.Count() };

        //读取DB
        result.Rows = sql
              .OrderBy(o => o.Sort)
              .Skip((model.PageIndex - 1) * model.PageSize)
              .Take(model.PageSize)
              .Select(x => new AdvertListRecord()
              {
                  Id = x.Id,
                  ImageUrl = x.ImageUrl,
                  LinkUrl = x.LinkUrl,
                  LinkType = x.LinkType,
                  LinkTypeText = x.LinkType.ToString(),
                  State = x.State,
                  StateText = x.State.ToString(),
                  Sort = x.Sort
              }).ToList();

        return result;
    }

    /// <summary>
    /// 修改广告上下架状态
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse AdvertUpdateState(AdvertUpdateState model)
    {
        if (string.IsNullOrEmpty(model.Id))
            throw new BadRequestException("广告标识为空");
        if (model.State == null)
            throw new BadRequestException("广告状态错误");
        var updateModel = _context.Advert_Set.FirstOrDefault(x => x.Id == model.Id);
        if (updateModel == null)
            throw new BadRequestException("广告不存在");

        updateModel.State = model.State!.Value;
        _context.SaveChanges();

        return new EmptyResponse();
    }

    public AdminLoginResponse AdminLogin(AdminLogin model)
    {
        var result = new AdminLoginResponse();

        model.Account = model.Account?.Trim();
        var admin = _context.Admin.FirstOrDefault(x => x.Account == model.Account);

        if (admin == null)
            throw new BadRequestException("用户不存在");

        if (!model.Password.Equals(LKEncrypt.DecryptString(admin.Password)))
            throw new BadRequestException("密码错误");

        var token = _commonService.CreateRefreshToken(admin.Id, TokenType.平台端, ClientType.Other);

        result.UserId = admin.Id;
        result.AccessToken = token.AccessToken;
        result.RefreshToken = token.RefreshToken;
        result.TokenExpiresTime = token.TokenExpiresTime;
        result.Account = admin.Account;
        result.NickName = admin.Name;
        result.Powers = admin.Powers;

        return result;
    }

    public GetRenCaiConfigResponse GetRenCaiConfig(GetRenCaiConfig model)
    {
        var data = MyRedis.Client.HGet<List<string>>(RedisKey.Config.Key, RedisKey.Config.RenCaiMobileIgnore);
        data = data ?? new List<string>();

        if (!string.IsNullOrEmpty(model.Mobile))
            data = data.Where(x => x.Contains(model.Mobile)).ToList();

        var result = new GetRenCaiConfigResponse() { Total = data.Count() };

        result.Rows = data
              .OrderByDescending(o => o)
              .Skip((model.PageIndex - 1) * model.PageSize)
              .Take(model.PageSize)
              .Select(s => new RenCaiConfigInfo()
              {
                  Mobile = s
              }).ToList();
        return result;
    }

    public EmptyResponse AddRenCaiConfig(AddRenCaiConfig model)
    {
        if (string.IsNullOrEmpty(model.Mobile))
            throw new BadRequestException("缺少手机号");

        var data = MyRedis.Client.HGet<List<string>>(RedisKey.Config.Key, RedisKey.Config.RenCaiMobileIgnore);
        data = data ?? new List<string>();

        using var sr = new StringReader(model.Mobile);
        var line = string.Empty;

        var dianhua = new List<string>();
        while ((line = sr.ReadLine()) != null)
        {
            if (!string.IsNullOrWhiteSpace(line))
                dianhua.Add(line);
        }

        dianhua = dianhua.Where(x => !string.IsNullOrWhiteSpace(x)).ToList();

        dianhua = dianhua.Distinct().ToList();

        data.AddRange(dianhua);
        data = data.Distinct().ToList();

        var group = dianhua.Chunk(100);

        using (var tran = _context.Database.BeginTransaction())
        {
            foreach (var item in group)
            {
                _context.Talent_Resume.Where(x => item.Contains(x.Mobile))
                .ExecuteUpdate(s => s.SetProperty(b => b.Deleted, true));
            }

            _context.SaveChanges();
            tran.Commit();
        }

        MyRedis.Client.HSet(RedisKey.Config.Key, RedisKey.Config.RenCaiMobileIgnore, data);

        return new EmptyResponse();
    }

    public EmptyResponse DeleteRenCaiConfig(DeleteRenCaiConfig model)
    {
        if (string.IsNullOrEmpty(model.Mobile))
            throw new BadRequestException("缺少手机号");

        var data = MyRedis.Client.HGet<List<string>>(RedisKey.Config.Key, RedisKey.Config.RenCaiMobileIgnore);
        data = data ?? new List<string>();

        using var sr = new StringReader(model.Mobile);
        var line = string.Empty;

        var dianhua = new List<string>();
        while ((line = sr.ReadLine()) != null)
        {
            if (!string.IsNullOrWhiteSpace(line))
                dianhua.Add(line);
        }

        dianhua = dianhua.Distinct().ToList();

        data = data.Except(dianhua).ToList();

        var group = dianhua.Chunk(100);

        using (var tran = _context.Database.BeginTransaction())
        {
            foreach (var item in group)
            {
                _context.Talent_Resume.Where(x => item.Contains(x.Mobile))
                .ExecuteUpdate(s => s.SetProperty(b => b.Deleted, false));
            }

            _context.SaveChanges();
            tran.Commit();
        }

        MyRedis.Client.HSet(RedisKey.Config.Key, RedisKey.Config.RenCaiMobileIgnore, data);

        return new EmptyResponse();
    }
}