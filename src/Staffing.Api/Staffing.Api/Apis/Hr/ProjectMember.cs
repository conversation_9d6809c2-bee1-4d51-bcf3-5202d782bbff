using Staffing.Model.Hr.Project;
using Staffing.Core.Interfaces.Hr;
using Config.CommonModel.Tasks;
using Infrastructure.Extend;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class ProjectMemberRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("project/members").WithTags("项目成员");
        group.MapPost("/add", async (UpdateProjectMember model, IProjectMemberService rep) => await rep.UpdateProjectMember(model))
            .WithOpenApi(operation => new(operation) { Summary = "添加/更新项目成员" });

        group.MapPost("/entry/import", (EntryImport model, IProjectMemberService rep) => rep.EntryImport(model))
            .WithOpenApi(operation => new(operation) { Summary = "入职导入-Excel" });

        group.MapGet("/noah/project", async ([AsParameters] GetNoahProjectByNo model, IProjectMemberService rep) => await rep.GetNoahProjectByNo(model))
            .WithOpenApi(operation => new(operation) { Summary = "查询数字诺亚项目人员" });

        group.MapPost("/entry/import/noah", (EntryImportForNoah model, IProjectMemberService rep) => rep.EntryImportForNoah(model))
            .WithOpenApi(operation => new(operation) { Summary = "入职导入-数字诺亚" });

        group.MapPost("/list", (GetProjectMembers model, IProjectMemberService rep) => rep.GetProjectMembers(model))
            .WithOpenApi(operation => new(operation) { Summary = "项目成员列表" });

        group.MapPost("/delete", (DeleteProjectMember model, IProjectMemberService rep) => rep.DeleteProjectMember(model))
            .WithOpenApi(operation => new(operation) { Summary = "批量删除项目成员" });

        group.MapPost("/entry", async (ProjectMemberEntry model, IProjectMemberService rep) => await rep.ProjectMemberEntry(model))
            .WithOpenApi(operation => new(operation) { Summary = "批量入职" });

        group.MapPost("/quit", async (ProjectMemberQuit model, IProjectMemberService rep) => await rep.ProjectMemberQuit(model))
            .WithOpenApi(operation => new(operation) { Summary = "批量离职、取消离职" });

        group.MapGet("/tasks", ([AsParameters] GetMyTasks model, IProjectMemberService rep) => rep.GetMyTasks(model))
            .WithOpenApi(operation => new(operation) { Summary = "任务列表" });

        group.MapGet("/tasks/{id}", (string id, [AsParameters] GetMyTaskDetails model, IProjectMemberService rep) =>
        {
            model.TaskId = id;
            return rep.GetMyTaskDetails(model);
        }).WithOpenApi(operation => new(operation) { Summary = "任务详情" });
    }
}