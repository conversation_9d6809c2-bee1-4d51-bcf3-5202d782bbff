﻿using System.Data.Entity;
using Config.Enums;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.CentreService;
using Config;
using Staffing.Model.CentreService.Report;
using Config.CommonModel.Business;
using Infrastructure.CommonService;

namespace Staffing.Core.Services.CentreService;

[Service(ServiceLifetime.Transient)]
public class PostService : IPostService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CommonDicService _commonDicService;

    /// <summary>
    /// 多久未处理算面试超时(单位:小时)
    /// </summary>
    private const int _InterviewOverTimeHour = 24;
    /// <summary>
    /// 多久未处理算入职超时(单位:小时)
    /// </summary>
    private const int _EntryOverTimeHour = 72;
    public PostService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, LogManager log, CommonDicService commonDicService)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _log = log;
        _commonDicService = commonDicService;
    }

    public GetExcellentPostResponse GetExcellentPost(GetExcellentPost model)
    {
        var result = new GetExcellentPostResponse();

        var predicate = PredicateBuilder.New<Post_Team>(x => (x.Post_Excellent.Status as ExcellentPostStatus?) != null);

        if (model.ExcellentStatus.HasValue)
            predicate = predicate.And(x => x.Post_Excellent.Status == model.ExcellentStatus);

        if (!string.IsNullOrWhiteSpace(model.Name))
            predicate = predicate.And(x => x.Post.Name.Contains(model.Name));

        if (!string.IsNullOrWhiteSpace(model.HrName))
            predicate = predicate.And(x => x.Post.Project.User_Hr.NickName.Contains(model.HrName));

        if (!string.IsNullOrWhiteSpace(model.EntName))
            predicate = predicate.And(x => x.Post.Project.Agent_Ent.DisplayName.Contains(model.EntName));

        var sql = _context.Post_Team.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(x => x.Post_Excellent.RefreshTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetExcellentPostInfo
        {
            AgentEnt = new GetAgentEntDetail
            {
                DisplayName = s.Post.Agent_Ent.DisplayName,
                AgentEntId = s.Post.AgentEntId,
                Scale = s.Post.Agent_Ent.Scale,
                Abbr = s.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post.Agent_Ent.Abbr : s.Post.Agent_Ent.DisplayName
            },
            Hr = new HrModel
            {
                HrId = s.Project_Team.HrId,
                HrName = s.Project_Team.User_Hr.NickName,
                Avatar = s.Project_Team.User_Hr.Avatar
            },
            PostAutoId = s.Post.AutoId,
            Name = s.Post.Name,
            CreatedTime = s.Post_Excellent.CreatedTime,
            Category = s.Post.Category,
            CategoryName = s.Post.Dic_Post.Name,
            Education = s.Post.Education,
            EducationName = s.Post.Education.GetDescription(),
            MaxSalary = s.Post.MaxSalary,
            MinSalary = s.Post.MinSalary,
            PostId = s.Post.PostId,
            RecruitNumber = s.Post.DeliveryNumber,
            RegionId = s.Post.RegionId,
            Salary = s.Post.Salary,
            WorkNature = s.Post.WorkNature,
            SalaryType = s.Post.SalaryType,
            SalaryTypeName = s.Post.SalaryType.GetDescription(),
            WorkNatureName = s.Post.WorkNature.GetDescription(),
            TeamPostId = s.TeamPostId,
            ExcellentStatus = s.Post_Excellent.Status,
            ExcellentStatusName = s.Post_Excellent.Status.GetDescription(),
            Describe = s.Post.Describe,
            MaxAge = s.Post.MaxAge,
            MinAge = s.Post.MinAge,
            RefreshTime = s.Post_Excellent.RefreshTime,
            Sex = s.Post.Sex,
            InterviewIn24Hour = s.Post_Excellent.InterviewIn24Hour,
            EntryIn72hour = s.Post_Excellent.EntryIn72hour
        }).ToList();

        foreach (var item in result.Rows)
        {
            item.SalaryName = Tools.FormattingSalary(item.MinSalary, item.MaxSalary, item.SalaryType, item.WorkNature, item.Salary);

            if (!string.IsNullOrWhiteSpace(item.RegionId))
                item.Region = _commonDicService.GetCityById(item.RegionId);
        }

        return result;
    }

    public EmptyResponse SetExcellentPost(SetExcellentPost model)
    {
        var exc = _context.Post_Excellent.FirstOrDefault(x => x.TeamPostId == model.TeamPostId);
        if (exc == null)
            throw new BadRequestException("内容不存在");

        if (model.Status == ExcellentPostStatus.Active || model.Status == ExcellentPostStatus.Rejected || model.Status == ExcellentPostStatus.UnReviewed)
        {
            exc.Status = model.Status.Value;

            if (model.Status == ExcellentPostStatus.Active)
                exc.ApprovalTime = DateTime.Now;
        }

        if (model.Refresh)
            exc.RefreshTime = DateTime.Now;

        _context.SaveChanges();

        //清除地区列表
        MyRedis.Client.Del(RedisKey.ExcellentPost.Citys);

        // var regionIds = _context.Post_Excellent.Where(x => x.Post_Team.Show)
        // .Select(s => s.Post_Team.Post.RegionId).Distinct().ToList();

        // MyRedis.Client.Set(RedisKey.ExcellentPost.Citys, regionIds);

        return new EmptyResponse();
    }

    /// <summary>
    /// 获取超时记录(24小时未面试+72小时未入职)
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public GetOvertimeExcellentPostResponse GetOvertimeExcellentPost(GetOvertimeExcellentPost model)
    {
        if (model.PageIndex < 1)
            model.PageIndex = 1;
        if (model.PageSize < 1)
            model.PageSize = 20;

        DateTime now = DateTime.Now;
        var excellentInterviewTime = now.AddHours(-1 * _InterviewOverTimeHour);
        var excellentEntryTime = now.AddHours(-1 * _EntryOverTimeHour);
        var etStatus = new List<RecruitStatus> { RecruitStatus.Contract, RecruitStatus.Induction, RecruitStatus.Offer, RecruitStatus.FileAway };
        var etInterviewStatus = new List<RecruitStatus> { RecruitStatus.HrScreening, RecruitStatus.InterviewerScreening };

        //职位优选面试超时及入职超时
        var predicate = PredicateBuilder.New<Recruit>(x => x.Post_Delivery.Post_Team.Post_Excellent.Status == ExcellentPostStatus.Active
          && x.CreatedTime > x.Post_Delivery.Post_Team.Post_Excellent.ApprovalTime
          && (
          (x.Post_Delivery.Post_Team.Post_Excellent.InterviewIn24Hour && x.CreatedTime < excellentInterviewTime && etInterviewStatus.Contains(x.Status))
          || (x.Post_Delivery.Post_Team.Post_Excellent.EntryIn72hour && x.CreatedTime < excellentEntryTime && !etStatus.Contains(x.Status))
          ));
        if (!string.IsNullOrEmpty(model.PostName))
            predicate.And(x => x.Post_Delivery.Post.Name.Contains(model.PostName));
        if (!string.IsNullOrEmpty(model.HRName))
            predicate.And(x => x.User_Hr.NickName.Contains(model.HRName));
        if (!string.IsNullOrEmpty(model.SeekerMobile))
            predicate.And(x => x.User_Seeker.User.Mobile.Contains(model.SeekerMobile));
        if (model.InterviewOverTimeHour != null && model.InterviewOverTimeHour > 0)
        {
            DateTime tInterviewOverTime = excellentInterviewTime.AddHours(-1 * model.InterviewOverTimeHour.Value);
            predicate.And(x => x.Post_Delivery.Post_Team.Post_Excellent.InterviewIn24Hour && x.CreatedTime < tInterviewOverTime);
        }
        if (model.EntryOverTimeHour != null && model.EntryOverTimeHour > 0)
        {
            DateTime tEntryOverTime = excellentEntryTime.AddHours(-1 * model.EntryOverTimeHour.Value);
            predicate.And(x => x.Post_Delivery.Post_Team.Post_Excellent.EntryIn72hour && x.CreatedTime < tEntryOverTime);
        }
        var sql = _context.Recruit.Where(predicate);

        var result = new GetOvertimeExcellentPostResponse();
        result.Total = sql.Count();
        result.Rows = sql
            .OrderByDescending(s => s.CreatedTime)
            .Skip((model.PageIndex - 1) * model.PageSize)
            .Take(model.PageSize)
            .Select(s => new GetOvertimeExcellentPostRecord
            {
                RecruitId = s.RecruitId,//招聘ID
                PostName = s.Post_Delivery.Post.Name,
                SeekerName = s.User_Seeker.NickName,
                SeekerMobile = s.User_Seeker.User.Mobile,
                HRName = s.User_Hr.NickName,//发布者
                EnterpriseName = s.Post_Delivery.Post.Project.Agent_Ent.DisplayName,//公司名(待招企业名称)
                CreatedTime = s.CreatedTime,//报名时间
                RecruitStatusText = s.Status.GetDescription(),//当前投递处理状态
                InterviewOverHour = (s.Post_Delivery.Post_Team.Post_Excellent.InterviewIn24Hour && s.CreatedTime < excellentInterviewTime && etInterviewStatus.Contains(s.Status)) ? Math.Round(s.CreatedTime.ToTimeDiffHour(now) - _InterviewOverTimeHour, 1, MidpointRounding.AwayFromZero).ToString() : "--",//面试超时(单位：小时)
                EntryInOverHour = (s.Post_Delivery.Post_Team.Post_Excellent.EntryIn72hour && s.CreatedTime < excellentEntryTime && !etStatus.Contains(s.Status)) ? Math.Round(s.CreatedTime.ToTimeDiffHour(now) - _EntryOverTimeHour, 1, MidpointRounding.AwayFromZero).ToString() : "--",//入职超时(单位：小时)
            })
            .ToList();

        return result;
    }
}