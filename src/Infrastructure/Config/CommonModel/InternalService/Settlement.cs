namespace Config.CommonModel.JavaDataApi;

/// <summary>
/// 分润转账结构
/// </summary>
public class TransactionRequest
{
    /// <summary>
    /// 需要确认当前交易金额提现后会有手续费，这部分谁承担
    /// 交易金额 单位分
    /// </summary>
    public long TransAmt { get; set; } = default!;

    /// <summary>
    /// 平台方收取的手续费 单位分 不存在则0
    /// </summary>
    public long PltfmFee { get; set; } = default!;

    /// <summary>
    /// 机构收取的手续费 单位分 不存在则0
    /// </summary>
    public long InstFee { get; set; } = default!;

    /// <summary>
    /// 商户订单号
    /// </summary>
    public string OrderNo { get; set; } = default!;

    /// <summary>
    /// 入金商户id 
    /// </summary>
    public string? InEntId { get; set; }

    /// <summary>
    /// 入金单位名称 暂时不可用
    /// </summary>
    public string? InEntName { get; set; }

    /// <summary>
    /// 项目id
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 合同 （例如：SO.202407120003）
    /// </summary>
    public string ContractCode { get; set; } = default!;

    /// <summary>
    /// 发放人员（钉钉工号：例如 000588）内部使用
    /// </summary>
    public string? HrNo { get; set; }

    /// <summary>
    /// 经办人是指这个流程的提交人、创建人（钉钉工号：例如 000588）
    /// </summary>
    public string AgentHrNo { get; set; } = default!;

    /// <summary>
    /// 姓名 外部项目使用
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 手机号 外部项目使用
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    public TransactionType? Type { get; set; }
}

public enum TransactionType
{
    内部, 外部, 个人, 平台 = 100
}

public class TransactionResponse
{
    /// <summary>
    /// 订单号
    /// </summary>
    public string? OrderNo { get; set; }

    /// <summary>
    /// 交易状态 0：失败 1：成功 2：处理中
    /// </summary>
    public string? TransStatus { get; set; }

    /// <summary>
    /// 附言(可选)
    /// </summary>
    public string? TransNote { get; set; }
}
