using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;

namespace Staffing.Model.Seeker.Channel;

/// <summary>
/// 简历人才库查询
/// </summary>
public class GetTalentVirtual : QueryRequest
{
    /// <summary>
    /// 渠道Id
    /// </summary>
    public string? ChannelId { get; set; }

    /// <summary>
    /// 搜索（姓名、手机号、邮箱、学校、岗位、行业、公司）
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 0=未注册，1=已注册
    /// </summary>
    public int? RegisteredStatus { get; set; }
}

public class GetTalentVirtualResponse : QueryResponse
{
    public List<GetTalentVirtualInfo> Rows { get; set; } = default!;
}

/// <summary>
/// 简历人才库
/// </summary>
public class GetTalentVirtualInfo : TalentVirtualBaseInfo
{
    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 所在地
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// 工作经验
    /// </summary>
    public int? WorkExperience { get; set; }

    /// <summary>
    /// 用户形式解释
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 期望职位
    /// </summary>
    public string? HopePost { get; set; }

    /// <summary>
    /// 期望薪资
    /// </summary>
    public string? HopeSalary { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    public string? WorkExp { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public string? EduExp { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 性别解释
    /// </summary>
    public string? SexName { get; set; }

    /// <summary>
    /// 渠道解释
    /// </summary>
    public string? ChannelName { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 用户形式
    /// </summary>
    public TalentVirtualStatus? Status { get; set; }

    /// <summary>
    /// 简历完善度
    /// </summary>
    public int Perfection { get; set; }

    /// <summary>
    /// 求职者Id，建立关系后有值
    /// </summary>
    public string? SeekerId { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public TalentVirtualEducation? Education { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatTime { get; set; }
}

public class TalentVirtualBaseInfo
{
    public string? Id { get; set; }

    /// <summary>
    /// HrId
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// 简历渠道
    /// </summary>
    public TalentVirtualChannel? Channel { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 手机
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? HeadPortrait { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Mailbox { get; set; }

    /// <summary>
    /// 微信
    /// </summary>
    public string? WeChat { get; set; }

    /// <summary>
    /// QQ
    /// </summary>
    public string? QQ { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateTime? Birthday { get; set; }

    /// <summary>
    /// 开始工作日期
    /// </summary>
    public DateTime? WorkTime { get; set; }

    /// <summary>
    /// 自我评价
    /// </summary>
    public string? SelfEvaluation { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 渠道Id
    /// </summary>
    public string? ChannelId { get; set; }
}

public class UpdateTalentVirtaual : TalentVirtualBaseInfo
{
    /// <summary>
    /// 工作经历
    /// </summary>
    public List<TalentVirtaualResumeWorkSub>? WorkSub { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public List<TalentVirtaualResumeEduSub>? EduSub { get; set; }

    /// <summary>
    /// 项目经历
    /// </summary>
    public List<TalentVirtaualResumeProjectSub>? ProjectSub { get; set; }

    /// <summary>
    /// 求职期望
    /// </summary>
    public TalentVirtaualResumeHopeSub? HopeSub { get; set; }
}