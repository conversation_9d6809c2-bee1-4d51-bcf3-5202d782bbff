﻿using System.ComponentModel.DataAnnotations;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.Hr;
using Config;
using Staffing.Model.Hr.Project;
using Config.CommonModel;
using Infrastructure.CommonService;
using Infrastructure.Proxy;
using Staffing.Core.Interfaces.Common;
using Microsoft.EntityFrameworkCore;
using EntityFrameworkCore.AutoHistory.Extensions;
using Config.CommonModel.Business;
using NetTopologySuite.Geometries;
using Config.CommonModel.JavaDataApi;
using System.Text;
using Infrastructure.Aop;
using System.Linq.Dynamic.Core;
using Staffing.Model.Hr.Post;
using Config.CommonModel.Ndn;
using Infrastructure.CommonService.Ndn;
using ServiceStack;
using Microsoft.AspNetCore.Http;
using MiniExcelLibs;
using Staffing.Model.Hr.User;
using Staffing.Entity.Noah;
using Config.CommonModel.Xbb;
using OfficeOpenXml.Packaging.Ionic.Zip;

namespace Staffing.Core.Services.Hr;

[Service(ServiceLifetime.Transient)]
public class ProjectService : IProjectService
{
    private readonly StaffingContext _context;
    private readonly IDbContextFactory<NoahContext> _noahContextFactory;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CommonDicService _commonDicService;
    private readonly ICommonService _commonService;
    private readonly CommonProjectService _commonProjectService;
    private readonly CommonPostService _commonPostService;
    private readonly CommonCacheService _commonCacheService;
    private readonly NoahData _noahData;
    private readonly WeChatHelper _weChatHelper;
    private readonly JavaDataApi _javaDataApi;
    private readonly FigureNoahHelper _figureNoahHelper;
    private readonly NdnApi _ndnApi;
    private readonly INdnService _ndnService;
    public ProjectService(StaffingContext context, RequestContext user, FigureNoahHelper figureNoahHelper,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment, NdnApi ndnApi,
        CacheHelper cacheHelper, LogManager log, CommonDicService commonDicService, IDbContextFactory<NoahContext> noahContextFactory,
        NoahData noahData, ICommonService commonService, CommonProjectService commonProjectService, CommonPostService commonPostService,
        CommonCacheService commonCacheService, WeChatHelper weChatHelper, JavaDataApi javaDataApi, INdnService ndnService)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _ndnApi = ndnApi;
        _cacheHelper = cacheHelper;
        _log = log;
        _commonDicService = commonDicService;
        _noahData = noahData;
        _commonService = commonService;
        _commonProjectService = commonProjectService;
        _commonPostService = commonPostService;
        _commonCacheService = commonCacheService;
        _noahContextFactory = noahContextFactory;
        _weChatHelper = weChatHelper;
        _javaDataApi = javaDataApi;
        _figureNoahHelper = figureNoahHelper;
        _ndnService = ndnService;
    }

    // public UpdateAgentEntResponse UpdateAgentEnt(UpdateAgentEnt model)
    // {
    //     var result = new UpdateAgentEntResponse();
    //     if (string.IsNullOrWhiteSpace(model.Name))
    //         throw new BadRequestException("企业名称不能为空");

    //     if (string.IsNullOrWhiteSpace(model.LogoUrl))
    //         throw new BadRequestException("企业Logo不能为空");

    //     if (string.IsNullOrWhiteSpace(model.RegionId))
    //         throw new BadRequestException("公司城市不能为空");

    //     if (string.IsNullOrWhiteSpace(model.DisplayName))
    //         throw new BadRequestException("展示名称不能为空");

    //     if (string.IsNullOrWhiteSpace(model.TycEntId))
    //         throw new BadRequestException("企业未通过天眼查核验");

    //     User_Agent_Ent? agentEnt = null;
    //     if (!string.IsNullOrWhiteSpace(model.AgentEntId))
    //     {
    //         agentEnt = _context.User_Agent_Ent.FirstOrDefault(x => x.UserId == _user.Id && x.AgentEntId == model.AgentEntId);
    //         if (agentEnt == null)
    //             throw new NotFoundException("内容不存在");
    //     }

    //     if (agentEnt == null)
    //     {
    //         agentEnt = new User_Agent_Ent
    //         {
    //             UserId = _user.Id
    //         };
    //         _context.Add(agentEnt);
    //     }

    //     int.TryParse(model.Industry, out var industry);

    //     agentEnt.Name = model.Name;
    //     agentEnt.LogoUrl = model.LogoUrl;
    //     agentEnt.DisplayName = model.DisplayName;
    //     agentEnt.Abbr = model.Abbr;
    //     agentEnt.Display = model.Display;
    //     agentEnt.AuthorizationUrl = model.AuthorizationUrl;
    //     agentEnt.Nature = model.Nature;
    //     agentEnt.Industry = industry;
    //     agentEnt.Scale = model.Scale;
    //     agentEnt.Capital = model.Capital;
    //     agentEnt.Status = EnterpriseStatus.Active;
    //     agentEnt.Describe = model.Describe;
    //     agentEnt.RegionId = Tools.TrimRegionId(model.RegionId)!;
    //     agentEnt.Address = model.Address;
    //     agentEnt.Location = new Point(model.Lat, model.Lng);

    //     _context.EnsureAutoHistory();
    //     _context.SaveChanges();
    //     result.AgentEntId = agentEnt.AgentEntId;

    //     return result;
    // }

    // public GetAgentEntListResponse GetAgentEntList(GetAgentEntList model)
    // {
    //     var result = new GetAgentEntListResponse();

    //     var predicate = PredicateBuilder.New<User_Agent_Ent>(x => x.UserId == _user.Id && x.Status == EnterpriseStatus.Active);

    //     if (!string.IsNullOrWhiteSpace(model.Search))
    //         predicate = predicate.And(x => x.Name.Contains(model.Search));
    //     result.Total = _context.User_Agent_Ent.Where(predicate).Count();
    //     result.Rows = _context.User_Agent_Ent.Where(predicate)
    //     .OrderByDescending(x => x.CreatedTime)
    //     .Skip((model.PageIndex - 1) * model.PageSize)
    //     .Take(model.PageSize)
    //     .Select(s => new GetAgentEntListDetail
    //     {
    //         AgentEntId = s.AgentEntId,
    //         Name = s.Name,
    //         RegionId = s.RegionId,
    //         Industry = s.Industry.ToString(),
    //         IndustryName = s.Dic_Industry.Name,
    //         UpdatedTime = s.UpdatedTime,
    //         Nature = s.Nature,
    //         Scale = s.Scale,
    //         Capital = s.Capital,
    //         Status = s.Status
    //     }).ToList();

    //     var ids = result.Rows.Select(s => s.AgentEntId).ToList();

    //     var groupCt = _context.Post.Where(x => x.Project.HrId == _user.Id && ids.Contains(x.Project.AgentEntId))
    //     .GroupBy(g => g.Project.AgentEntId)
    //     .Select(s => new
    //     {
    //         s.Key,
    //         Ct = s.Count()
    //     }).ToList();

    //     foreach (var item in result.Rows)
    //     {
    //         if (!string.IsNullOrWhiteSpace(item.RegionId))
    //             item.Region = _commonDicService.GetCityById(item.RegionId);
    //         var gp = groupCt.FirstOrDefault(x => x.Key == item.AgentEntId);
    //         item.Posts = gp?.Ct ?? 0;
    //     }

    //     return result;
    // }

    [RedisCache(Expire = 60)]
    public async Task<GetNoahProjResponse> GetNoahProj(GetNoahProj model)
    {
        var result = new GetNoahProjResponse();

        var nuoId = _context.Post.Where(x => x.PostId == model.PostId)
        .Select(s => s.Project.NuoId).FirstOrDefault();

        if (!string.IsNullOrWhiteSpace(nuoId))
        {
            var noahData = await _figureNoahHelper.GetProjectMember(nuoId ?? string.Empty, 1, 1);
            result.ContractUrl = noahData.hturl?.Split("|").ToList().Where(x => !string.IsNullOrEmpty(x)).ToList() ?? new List<string>();
            result.Htcode = noahData.htcode;
        }

        return result;
    }

    // public GetAgentEntLessResponse GetAgentEntLess(GetAgentEntLess model)
    // {
    //     var result = new GetAgentEntLessResponse();

    //     var predicate = PredicateBuilder.New<User_Agent_Ent>(x => x.UserId == _user.Id && x.Status == EnterpriseStatus.Active);

    //     if (!string.IsNullOrWhiteSpace(model.Search))
    //         predicate = predicate.And(x => x.Name.Contains(model.Search));

    //     result.Rows = _context.User_Agent_Ent.Where(predicate)
    //     .OrderByDescending(o => o.CreatedTime)
    //     .Select(s => new GetAgentEntLessDetail
    //     {
    //         AgentEntId = s.AgentEntId,
    //         Name = s.Name
    //     }).ToList();

    //     return result;
    // }

    // public GetAgentEntLessResponse GetAgentEntRecently()
    // {
    //     var result = new GetAgentEntLessResponse();

    //     var predicate = PredicateBuilder.New<Agent_Ent>(x => x.Status == EnterpriseStatus.Active);

    //     result.Rows = _context.Agent_Ent.Where(predicate)
    //     .OrderByDescending(o => o.CreatedTime)
    //     .Take(3)
    //     .Select(s => new GetAgentEntLessDetail
    //     {
    //         AgentEntId = s.AgentEntId,
    //         Name = s.Name
    //     }).ToList();

    //     return result;
    // }

    public async Task<GetAgentEntDetail> GetAgentEntInfo(string id)
    {
        var result = _context.Agent_Ent.Where(x => x.AgentEntId == id)
        .Select(s => new GetAgentEntDetail
        {
            AgentEntId = s.AgentEntId,
            LogoUrl = s.LogoUrl,
            Name = s.Name,
            DisplayName = s.DisplayName,
            Abbr = s.Abbr,
            Display = s.Display,
            AuthorizationUrl = s.AuthorizationUrl,
            Nature = s.Nature,
            Industry = s.Industry.ToString(),
            Scale = s.Scale,
            Capital = s.Capital,
            Status = EnterpriseStatus.Active,
            Describe = s.Describe,
            RegionId = s.RegionId,
            Address = s.Address,
            Lat = s.Location.X,
            Lng = s.Location.Y,
            IndustryName = s.Dic_Industry.Name,
            UpdatedTime = s.UpdatedTime
        }).FirstOrDefault();

        if (result == null)
            throw new NotFoundException("内容不存在");

        result.NatureName = result.Nature.GetDescription();
        result.ScaleName = result.Scale.GetDescription();
        result.CapitalName = result.Capital.GetDescription();

        if (!string.IsNullOrWhiteSpace(result.RegionId))
            result.Region = _commonDicService.GetCityById(result.RegionId);

        var entBus = await _noahData.GetEnterpriseCache(result.Name);
        result.Age = entBus.Age;
        result.Tags = entBus.Tags;
        result.RegisterDate = entBus.RegisterDate;

        var tycInfo = _context.Tyc_Enterprise.FirstOrDefault(f => f.RegStatus != "吊销" && f.Name == result.Name.Replace('）', ')').Replace('（', '(') || f.Name == result.Name.Replace(')', '）').Replace('(', '（'));

        if (tycInfo != null)
        {
            result.TycEntId = tycInfo.Id;
        }
        else
        {
            // 之前添加的企业，都调用一下天眼查接口
            if (!string.IsNullOrWhiteSpace(result.Name))
            {
                var info = _commonCacheService.GetAgentEntFromTyc(result.Name);
                if (info != null)
                    result.TycEntId = info.TycEntId;
            }
        }

        return result;
    }

    public async Task<EmptyResponse> SetProjectPostStatus(SetProjectPostStatus model)
    {
        await _commonProjectService.SetProjectPostStatus(model.ProjectId, model.On, _user.Id);

        return new EmptyResponse();
    }

    public GetBountyConfigResponse GetBountyConfig()
    {
        var result = new GetBountyConfigResponse();

        var settings = _context.Sys_Settings.FirstOrDefault();
        result.PlatformFull = settings?.PlatformFull;
        result.PlatformNoSale = settings?.PlatformNoSale;
        result.NoahFull = settings?.NoahFull;
        result.NoahNoSale = settings?.NoahNoSale;

        return result;
    }

    // public EmptyResponse DeleteAgentEnt(string id)
    // {
    //     var agentEnt = _context.User_Agent_Ent
    //     .FirstOrDefault(x => x.UserId == _user.Id && x.AgentEntId == id);

    //     if (agentEnt != null)
    //     {
    //         agentEnt.Status = EnterpriseStatus.Inactive;

    //         _context.EnsureAutoHistory();
    //         _context.SaveChanges();
    //     }

    //     return new EmptyResponse();
    // }

    // public EmptyResponse UpdateProjectAgentEnt(UpdateProjectAgentEnt model)
    // {
    //     if (!_context.User_Agent_Ent.Any(x => x.UserId == _user.Id && x.AgentEntId == model.AgentEntId))
    //         throw new BadRequestException("代招企业无效");

    //     var project = _context.Project.FirstOrDefault(x => x.HrId == _user.Id && x.ProjectId == model.ProjectId);
    //     if (project == null)
    //         throw new NotFoundException("内容不存在");

    //     project.AgentEntId = model.AgentEntId;

    //     _context.EnsureAutoHistory();
    //     _context.SaveChanges();

    //     return new EmptyResponse();
    // }

    public async Task<UpdateProjectResponse> UpdateProject(UpdateProject model)
    {
        var result = new UpdateProjectResponse();
        if (string.IsNullOrWhiteSpace(model.Name))
            throw new BadRequestException("项目名称不能为空");

        var oldNuoId = string.Empty;
        var xbbContractNo = string.Empty;

        Project? project = null;
        if (!string.IsNullOrWhiteSpace(model.ProjectId))
        {
            project = _context.Project.FirstOrDefault(x => x.HrId == _user.Id && x.ProjectId == model.ProjectId);
            if (project == null)
                throw new NotFoundException("内容不存在");

            oldNuoId = project.NuoId;
            xbbContractNo = project.XbbContractNo;

            // 编辑时需要存一下(新项目入驻兼容 - 只是后台处理的字段)
            model.PaymentType = project.PaymentType;
            model.ClearingChannel = project.ClearingChannel;
            model.Industry = project.Industry;
        }
        else
        {
            //新建项目判断是否需要绑定数字诺亚项目
            if (string.IsNullOrEmpty(model.NuoId))
            {
                var userEnt = _context.User_Hr.Where(x => x.UserId == _user.Id).Select(s => s.Enterprise.Specific).FirstOrDefault();
                if (userEnt?.Contains(EntSpecific.Noah) == true)
                    throw new BadRequestException("缺少数字诺亚项目编号");
            }

            // 新建项目时采取新入驻更改
            if (!string.IsNullOrWhiteSpace(model.NuoId))
                model.PaymentType = ProjectPaymentType.内部结算;
            else
                model.PaymentType = ProjectPaymentType.对公结算;

            model.ClearingChannel = ProjectClearingChannel.银行结算;// 默认银行结算

            model.Industry = GetIndustryFromMap(model.AgentEntId);// todo: 映射关系缺少维护功能
        }

        if (!model.ContractType.HasValue)
            throw new BadRequestException("缺少签约方式");

        if (!_context.Agent_Ent.Any(x => x.AgentEntId == model.AgentEntId))
            throw new BadRequestException("代招企业无效");

        var endTime = model.EndTime?.ToNullableDate();
        if (!endTime.HasValue)
            throw new BadRequestException("缺少截止日期");

        //检测数字诺亚编号
        if (!string.IsNullOrEmpty(model.NuoId) && string.IsNullOrWhiteSpace(oldNuoId))
        {
            // var noahData = await _figureNoahHelper.GetProjectMember(model.NuoId ?? string.Empty, 1, 1);
            //改为新数字诺亚接口
            Config.CommonModel.Ndn.GetProjectResponse? noahData;
            try
            {
                noahData = await _ndnApi.GetProjectInfo(model.NuoId);
            }
            catch (NdnApiException ex)
            {
                throw new BadRequestException(ex.Message);
            }

            if (string.IsNullOrWhiteSpace(noahData?.projectCode) && string.IsNullOrWhiteSpace(noahData?.projectName))
                throw new BadRequestException("数字诺亚项目不存在");

            xbbContractNo = noahData.contractCode;

            //判断ndn_project表是否存在
            if (!_context.Ndn_Project.Any(x => x.ProjectCode == model.NuoId))
            {
                //创建数字诺亚项目
                var ndnProject = new Ndn_Project
                {
                    ProjectCode = model.NuoId,
                    ProjectName = noahData?.projectName ?? string.Empty,
                    BookCode = noahData?.bookCode ?? string.Empty,
                    BookName = noahData?.bookName ?? string.Empty,
                    Type = NdnProjType.数字诺亚,
                    Status = NdnProjStatus.完成,
                    OperatorId = _user.Id,
                    OperatorName = _user.Name
                };
                _context.Add(ndnProject);
            }
        }
        else
        {
            model.NuoId = oldNuoId;
        }

        if (project == null)
        {
            project = new Project
            {
                HrId = _user.Id
            };
            _context.Add(project);

            var hrInfo = _context.User_Hr.Where(w => w.UserId == _user.Id)
            .Select(s => new
            {
                s.User.Mobile,
                s.NickName,
                s.EMail,
                s.Post
            }).First();

            // 添加项目面试官
            var addModel = new Project_Interviewer()
            {
                ProjectId = project.ProjectId,
                Mail = hrInfo.EMail ?? string.Empty,
                Name = hrInfo.NickName,
                OfficeAddress = string.Empty,
                Phone = hrInfo.Mobile,
                Post = hrInfo.Post ?? string.Empty
            };
            _context.Add(addModel);

            var projectExt = new Project_Extend
            {
                ProjectId = project.ProjectId
            };
            _context.Add(projectExt);

            //同时添加到我的协同
            var teamProj = new Project_Team
            {
                Project = project,
                HrId = _user.Id,
                Source = HrProjectSource.自己项目,
                Status = ProjectStatus.已上线,
                Type = HrProjectType.自己,
                UpdatedBy = _user.Id
            };
            _context.Add(teamProj);
        }

        project.ContractType = model.ContractType.Value;
        project.AgentEntId = model.AgentEntId;
        project.HrId = _user.Id;
        //project.Name = model.Name;
        project.NuoId = model.NuoId!;
        project.XbbContractNo = xbbContractNo;
        project.Level = model.Level;
        project.Type = model.Type;
        project.Industry = model.Industry;
        project.WorkCycleType = model.WorkCycleType;
        project.ClearingType = model.ClearingType;
        project.ClearingChannel = model.ClearingChannel;
        project.Describe = model.Describe;
        project.EndTime = endTime.Value;
        project.PaymentType = model.PaymentType;
        project.UpdatedTime = DateTime.Now;
        project.StaffService = model.StaffService;

        //项目原始状态
        var projectStatusOrg = project.Status;

        var day = DateTime.Today.AddDays(1);
        if (endTime < day)
            project.Status = ProjectStatus.归档;
        else
            project.Status = ProjectStatus.已上线;

        //项目状态是否变更
        var projectStatusChange = projectStatusOrg != project.Status;

        model.RegionIds = model.RegionIds.Distinct().ToList();

        var regions = new List<CityModel>();

        model.RegionIds = model.RegionIds.Distinct().ToList();

        if (!string.IsNullOrWhiteSpace(model.ProjectId))
            _context.RemoveRange(_context.Project_Region.Where(x => x.ProjectId == model.ProjectId));

        foreach (var item in model.RegionIds)
        {
            var region = _commonDicService.GetCityById(item);
            if (region == null)
                continue;

            _context.Add(new Project_Region
            {
                Project = project,
                RegionId = item
            });

            var cityModel = new CityModel
            {
                Id = item,
                TownName = region.TownName,
                CityName = region.CityName,
                CountyName = region.CountyName,
                ProvinceName = region.ProvinceName
            };
            regions.Add(cityModel);
        }

        // project.RegionData = regions;

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        if (projectStatusChange)
        {
            //项目状态变更消息
            MyRedis.Client.SAdd(SubscriptionKey.ProjectStatusChange, new Sub_Project_StatusChange
            {
                ProjectId = project.ProjectId
            });
        }
        else
        {
            //仅修改hr自己数据
            _commonProjectService.UpdateHrProjectData(new Sub_UpdateHrData { UserId = _user.Id, Type = UpdateHrDataType.项目 });
        }

        // 回执项目监控
        if (!string.IsNullOrWhiteSpace(xbbContractNo))
        {
            if (!MyRedis.Client.SIsMember(SubscriptionKey.ProjectCallBackSum, xbbContractNo))
            {
                MyRedis.Client.SAdd(SubscriptionKey.ProjectCallBack, xbbContractNo);
                MyRedis.Client.SAdd(SubscriptionKey.ProjectCallBackSum, xbbContractNo);
            }
        }


        result.ProjectId = project.ProjectId;
        return result;
    }


    public async Task<UpdateProjectResponse> NewUpdateProject(NewUpdateProject model)
    {

        var validationResults = new List<ValidationResult>();
        var validationContext = new ValidationContext(model);
        bool isValid = Validator.TryValidateObject(model, validationContext, validationResults, true);
        if (!isValid)
        {
            throw new BadRequestException(validationResults.FirstOrDefault().ErrorMessage);
        }
        Project? project = null;
        var xbbContractNo = string.Empty;
        var result = new UpdateProjectResponse();
        try
        {
            //验证数字诺亚项目是否存在
            if (!string.IsNullOrEmpty(model.NuoId))
            {
                //改为新数字诺亚接口
                Config.CommonModel.Ndn.GetProjectResponse? noahData;
                try
                {
                    noahData = await _ndnApi.GetProjectInfo(model.NuoId);
                }
                catch (NdnApiException ex)
                {
                    throw new BadRequestException(ex.Message);
                }

                if (string.IsNullOrWhiteSpace(noahData?.projectCode) &&
                    string.IsNullOrWhiteSpace(noahData?.projectName))
                    throw new BadRequestException("数字诺亚项目不存在");

                xbbContractNo = noahData.contractCode;

                //判断ndn_project表是否存在
                if (!_context.Ndn_Project.Any(x => x.ProjectCode == model.NuoId))
                {
                    //创建数字诺亚项目
                    var ndnProject = new Ndn_Project
                    {
                        ProjectCode = model.NuoId,
                        ProjectName = noahData?.projectName ?? string.Empty,
                        BookCode = noahData?.bookCode ?? string.Empty,
                        BookName = noahData?.bookName ?? string.Empty,
                        Type = NdnProjType.数字诺亚,
                        Status = NdnProjStatus.完成,
                        OperatorId = _user.Id,
                        OperatorName = _user.Name
                    };
                    _context.Add(ndnProject);
                }
            }

            //代招企业
            var agentEnt = _context.Agent_Ent.FirstOrDefault(s => s.AgentEntId == model.AgentEntId);
            if (agentEnt == null)
            {
                agentEnt = new Agent_Ent()
                {
                    Name = model.Name,
                    DisplayName = model.Name,
                    Nature = model.Nature,
                    Describe = model.Describe,
                    Address = model.Address,
                    Industry = (int)model.Industry,
                };
                _context.Add(agentEnt);
            }
            agentEnt.Name = model.Name;
            agentEnt.Nature = model.Nature;
            agentEnt.Describe = model.Describe;
            agentEnt.Address = model.Address;
            agentEnt.Industry = (int)model.Industry;

            //业务概况
            //新建项目
            if (string.IsNullOrWhiteSpace(model.ProjectId))
            {
                project = new Project()
                {
                    BookCode = model.BookCode,
                    NuoId = model.NuoId,
                    Status = ProjectStatus.待审核,
                    AgentEntId = agentEnt.AgentEntId,
                    SaleUserId = _user.Id,
                    HrId = model.ReceiverId,
                    Type = model.Type,
                    ContractType = model.ContractType,
                    PaymentNode = model.PaymentNode,
                    IsAssignedPerson = model.IsAssignedPerson,
                    SettlementPersonId = model.SettlementPersonId,
                    XbbContractNo = xbbContractNo,
                    StartTime = model.StartTime,
                    EndTime = model.EndTime,
                    Industry = model.Industry,
                    Price = model.Price,
                };
                _context.Add(project);
                var projectExt = new Project_Extend
                {
                    ProjectId = project.ProjectId,
                    InterviewRound = model.InterviewRound,
                    IsVideoInterviewSupported = model.IsVideoInterviewSupported,
                    InterviewProcess = model.InterviewProcess,
                    PositionCount = model.PositionCount,
                    RecruitmentCount = model.RecruitmentCount,
                    Price = model.Price,
                    Remark = model.Remark,
                    RequiredCondition = model.RequiredCondition,
                    ProjectManualUrl = model.ProjectManualUrl,
                    SignContractType = model.SignContractType,
                };
                _context.Add(projectExt);
            }
            //项目已经存在
            else
            {
                project = _context.Project.FirstOrDefault(p => p.ProjectId == model.ProjectId);
                if (project == null)
                {
                    throw new BadRequestException("项目不存在");
                }
                if (project.AcceptOrder == RecruitAcceptOrderEnum.未接单)
                {
                    project.IsAssignedPerson = model.IsAssignedPerson;
                    project.HrId = string.IsNullOrEmpty(model.ReceiverId) ? null : model.ReceiverId;
                }
                project.BookCode = model.BookCode;
                project.NuoId = model.NuoId;
                //project.Status = project.Status;
                project.AgentEntId = agentEnt.AgentEntId;
                project.Type = model.Type;
                project.ContractType = model.ContractType;
                project.PaymentNode = model.PaymentNode;
                project.SettlementPersonId = model.SettlementPersonId;
                project.XbbContractNo = xbbContractNo;
                project.StartTime = model.StartTime;
                project.EndTime = model.EndTime;
                project.Industry = model.Industry;
                project.Price = model.Price;
                // 判断项目时间是否结束-结束时配置项目为归档
                var day = DateTime.Today.AddDays(1);
                if (project.EndTime < day)
                    project.Status = ProjectStatus.归档;
                else if (project.AcceptOrder == RecruitAcceptOrderEnum.未接单)
                    project.Status = ProjectStatus.待审核;
                else
                    project.Status = ProjectStatus.已上线;
                var projectExtend = _context.Project_Extend.FirstOrDefault(s => s.ProjectId == project.ProjectId);
                projectExtend.InterviewRound = model.InterviewRound;
                projectExtend.IsVideoInterviewSupported = model.IsVideoInterviewSupported;
                projectExtend.InterviewProcess = model.InterviewProcess;
                projectExtend.PositionCount = model.PositionCount;
                projectExtend.RecruitmentCount = model.RecruitmentCount;
                projectExtend.Price = model.Price;
                projectExtend.Remark = model.Remark;
                projectExtend.RequiredCondition = model.RequiredCondition;
                projectExtend.ProjectManualUrl = model.ProjectManualUrl;
                projectExtend.SignContractType = model.SignContractType;
            }

            // 项目调研表
            if (model.SurveyContent != null)
            {
                Project_Survey? survey = null;
                if (!string.IsNullOrWhiteSpace(model.ProjectId))
                    survey = _context.Project_Survey.FirstOrDefault(x => x.ProjectId == model.ProjectId && string.IsNullOrEmpty(x.PostId));

                if (survey == null)
                {
                    survey = new Project_Survey
                    {
                        ProjectId = project.ProjectId,
                    };
                    _context.Add(survey);
                }
                survey.Content = model.SurveyContent;
            }

            //判断是否项目经理已经接单修改项目状态为上线 
            //修改为接单后直接上线
            // if (project.AcceptOrder == RecruitAcceptOrderEnum.已经接单 && project.HrId == _user.Id)
            // {
            //     project.Status = ProjectStatus.已上线;
            //     teamProj = new Project_Team()
            //     {
            //         Project = project,
            //         HrId = _user.Id,
            //         Source = HrProjectSource.自己项目,
            //         Status = ProjectStatus.已上线,
            //         Type = HrProjectType.自己,
            //         UpdatedBy = _user.Id,
            //     };
            //     _context.Add(teamProj);
            //     //添加自己为面试官
            //     var hrInfo = _context.User_Hr.Where(w => w.UserId == _user.Id)
            //         .Select(s => new
            //         {
            //             s.User.Mobile,
            //             s.NickName,
            //             s.EMail,
            //             s.Post
            //         }).First();
            //
            //     var addModel = new Project_Interviewer()
            //     {
            //         ProjectId = project.ProjectId,
            //         Mail = hrInfo.EMail ?? string.Empty,
            //         Name = hrInfo.NickName,
            //         OfficeAddress = string.Empty,
            //         Phone = hrInfo.Mobile,
            //         Post = hrInfo.Post ?? string.Empty
            //     };
            //     _context.Add(addModel);
            //
            // }
            _context.EnsureAutoHistory();
            _context.SaveChanges();

            //项目状态变更消息
            MyRedis.Client.SAdd(SubscriptionKey.ProjectStatusChange, new Sub_Project_StatusChange
            {
                ProjectId = project.ProjectId
            });

            // 回执项目监控
            if (!string.IsNullOrWhiteSpace(xbbContractNo))
            {
                if (!MyRedis.Client.SIsMember(SubscriptionKey.ProjectCallBackSum, xbbContractNo))
                {
                    MyRedis.Client.SAdd(SubscriptionKey.ProjectCallBack, xbbContractNo);
                    MyRedis.Client.SAdd(SubscriptionKey.ProjectCallBackSum, xbbContractNo);
                }
            }
        }
        catch (Exception e)
        {
            _log.Error("NewUpdateProject.error", e.Message);
            throw;
        }
        result.ProjectId = project.ProjectId;
        return result;
    }

    public async Task<UpdateProjectSurveyResponse> UpdateProjectSurvey(UpdateProjectSurvey model)
    {
        var result = new UpdateProjectSurveyResponse();

        if (string.IsNullOrWhiteSpace(model.ProjectId) && string.IsNullOrWhiteSpace(model.PostId))
            throw new BadRequestException("缺少Id");

        Project_Survey? survey = null;
        if (!string.IsNullOrWhiteSpace(model.PostId))
        {
            model.ProjectId = _context.Post.Where(x => x.PostId == model.PostId).Select(s => s.ProjectId).FirstOrDefault();
            survey = _context.Project_Survey.FirstOrDefault(x => x.PostId == model.PostId);
        }
        else
            survey = _context.Project_Survey.FirstOrDefault(x => x.ProjectId == model.ProjectId);

        if (!_context.Project.Any(x => x.ProjectId == model.ProjectId))
            throw new BadRequestException("内容不存在");

        // 查询并移除历史数据
        if (survey == null)
        {
            survey = new Project_Survey
            {
                ProjectId = model.ProjectId!,
                PostId = model.PostId
            };
            _context.Add(survey);
        }

        survey.Content = model.Content;

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        await Task.FromResult(1);

        return result;
    }

    public async Task<UpdateProjectSurveyResponse> ImportProjectSurvey(string? projectId, string? postId, IFormFile? file)
    {
        var result = new UpdateProjectSurveyResponse();

        Project_Survey? survey = null;
        if (!string.IsNullOrWhiteSpace(postId))
        {
            projectId = _context.Post.Where(x => x.PostId == postId).Select(s => s.ProjectId).FirstOrDefault();
            survey = _context.Project_Survey.FirstOrDefault(x => x.PostId == postId);
        }

        if (survey == null && !string.IsNullOrEmpty(projectId))
            survey = _context.Project_Survey.FirstOrDefault(x => x.ProjectId == projectId && string.IsNullOrEmpty(x.PostId));

        // 文件不能为空
        if (file == null || file.Length == 0)
            throw new BadRequestException("文件不能为空");

        var extension = Path.GetExtension(file.FileName).ToLower();
        if (!(extension == ".xlsx" || extension == ".xls"))
            throw new BadRequestException("非excel文件");

        // 使用MiniExcel处理，查到每一行数据
        using var stream = file.OpenReadStream();
        var data = MiniExcel.Query(stream).ToList();

        var newContent = new ProjectSurveyContent();
        newContent.Images = survey?.Content?.Images ?? new List<ProjectSurveyContentMedia>();
        newContent.Videos = survey?.Content?.Videos ?? new List<ProjectSurveyContentMedia>();


        var currentTitle = string.Empty;
        var nullTimes = 0;

        foreach (var item in data)
        {
            string? key = item.A;
            string? value = item.B?.ToString();

            if (key?.EndsWith("**") == true)
            {
                currentTitle = key;
                continue;
            }

            if (string.IsNullOrWhiteSpace(currentTitle))
            {
                nullTimes++;
                continue;
            }

            if (string.IsNullOrWhiteSpace(key))
            {
                nullTimes++;
                continue;
            }

            if (nullTimes > 999)
                break;

            switch (currentTitle)
            {
                case "重点必读**":
                    newContent.KeyRead = key;
                    break;
                case "薪资待遇**":
                    newContent.SalaryBenefits.Add(new ProjectSurveyContentValue
                    {
                        Title = key,
                        Content = value
                    });
                    break;
                case "硬性要求**":
                    newContent.HardConditions.Add(new ProjectSurveyContentValue
                    {
                        Title = key,
                        Content = value
                    });
                    break;
                case "工作时间**":
                    newContent.WorkingHours.Add(new ProjectSurveyContentValue
                    {
                        Title = key,
                        Content = value
                    });
                    break;
                case "工作地点**":
                    newContent.WorkLocation.Add(new ProjectSurveyContentValue
                    {
                        Title = key,
                        Content = value
                    });
                    break;
                case "面试流程**":
                    newContent.InterviewProcess.Add(new ProjectSurveyContentValue
                    {
                        Title = key,
                        Content = value
                    });
                    break;
                case "入职流程**":
                    newContent.OnboardingProcess.Add(new ProjectSurveyContentValue
                    {
                        Title = key,
                        Content = value
                    });
                    break;
                case "工作情况**":
                    newContent.WorkCondition.Add(new ProjectSurveyContentValue
                    {
                        Title = key,
                        Content = value
                    });
                    break;
            }
        }

        if (!string.IsNullOrWhiteSpace(projectId) || !string.IsNullOrWhiteSpace(postId))
        {
            // 查询并移除历史数据
            if (survey == null)
            {
                survey = new Project_Survey
                {
                    ProjectId = projectId!,
                    PostId = postId
                };
                _context.Add(survey);
            }

            survey.Content = newContent;

            _context.SaveChanges();
        }

        result.Content = newContent;

        await Task.FromResult(1);
        return result;
    }

    public GetProjectSurveyResponse GetProjectSurvey(string? projectId, string? postId)
    {
        Project_Survey? survey = null;
        if (!string.IsNullOrWhiteSpace(postId))
        {
            projectId = _context.Post.Where(x => x.PostId == postId).Select(s => s.ProjectId).FirstOrDefault();
            survey = _context.Project_Survey.FirstOrDefault(x => x.PostId == postId);
        }

        if (survey == null)
            survey = _context.Project_Survey.FirstOrDefault(x => x.ProjectId == projectId && string.IsNullOrEmpty(x.PostId));

        var result = new GetProjectSurveyResponse { Content = survey?.Content ?? new ProjectSurveyContent() };

        return result;
    }

    private ProjectIndustry GetIndustryFromMap(string agentEntId)
    {
        var agent = _context.Agent_Ent.FirstOrDefault(f => f.AgentEntId == agentEntId);
        var value = _context.Dic_Base_Info.FirstOrDefault(f => f.Type.Equals("industry_companyIndustry_map") && f.Code.Equals(agent.Industry + ""))?.Value;
        if (!string.IsNullOrWhiteSpace(value))
            return (ProjectIndustry)Enum.Parse(typeof(ProjectIndustry), value);
        return ProjectIndustry.其他;
    }

    public GetProjectDetail GetProject(string id)
    {
        var result = _context.Project.Where(x => x.ProjectId == id)
        .Select(s => new GetProjectDetail
        {
            ProjectId = s.ProjectId,
            Name = s.Agent_Ent.Name,
            ContractType = s.ContractType,
            ContractTypeText = s.ContractType.GetDescription(),
            AgentEntId = s.AgentEntId ?? string.Empty,
            Status = s.Status,
            Type = s.Type,
            ClearingChannel = s.ClearingChannel,
            ClearingChannelName = s.ClearingChannel.GetDescription(),
            ClearingType = s.ClearingType,
            ClearingTypeName = s.ClearingType.GetDescription(),
            CreatedTime = s.CreatedTime,
            Describe = s.Describe,
            EndTime = s.EndTime.ToYYYY_MM_DD(),
            HrId = s.HrId,
            HrName = s.User_Hr.NickName,
            Industry = s.Industry,
            IndustryName = s.Industry.GetDescription(),
            Level = s.Level,
            LevelName = s.Level.GetDescription(),
            NuoId = s.NuoId,
            PaymentType = s.PaymentType,
            PaymentNode = s.PaymentNode,
            IsAssignedPerson = s.IsAssignedPerson,
            SettlementPersonId = s.SettlementPersonId,
            InterviewRound = s.Project_Extend.InterviewRound,
            IsVideoInterviewSupported = s.Project_Extend.IsVideoInterviewSupported ?? true,
            InterviewProcess = s.Project_Extend.InterviewProcess,
            PositionCount = s.Project_Extend.PositionCount ?? 1,
            RecruitmentCount = s.Project_Extend.RecruitmentCount ?? 1,
            RequiredCondition = s.Project_Extend.RequiredCondition,
            Price = s.Project_Extend.Price ?? 0,
            Remark = s.Project_Extend.Remark,
            ProjectStartTime = s.StartTime,
            ProjectEndTime = s.EndTime,
            BookCode = s.BookCode,
            // Region = s.RegionData,
            StatusName = s.Status.GetDescription(),
            TypeName = s.Type.GetDescription(),
            WorkCycleType = s.WorkCycleType,
            WorkCycleTypeName = s.WorkCycleType.GetDescription(),
            HrAvatar = s.User_Hr.Avatar,
            HrEntName = s.User_Hr.Enterprise.Name,
            HrEntAddress = s.User_Hr.Enterprise.Address,
            HrMobile = s.User_Hr.User.Mobile,
            HrEmail = s.User_Hr.EMail,
            HrWeChat = s.User_Hr.WeChatNo,
            HrPost = s.User_Hr.Post,
            SelfProj = s.HrId == _user.Id,
            ProjectAutoId = s.AutoId,
            StaffService = s.StaffService
        }).FirstOrDefault();

        if (result == null)
            throw new NotFoundException("内容不存在");

        var teamprojectid = _context.Project_Team.Where(x => x.HrId == _user.Id && x.ProjectId == result.ProjectId).FirstOrDefault();
        if (teamprojectid != null)
            result.TeamProjectId = teamprojectid.TeamProjectId;

        var idPre = Constants.ProjectIdPre;
        result.ProjectNo = $"{idPre}{result.ProjectAutoId.ToString().PadLeft(6, '0')}";

        result.PaymentTypeName = result.PaymentType?.GetDescription();

        result.RegionIds = result.Region?.Select(s => s.Id!)?.ToList() ?? new List<string>();

        var et = result.EndTime?.ToNullableDate();
        if (et != null)
        {
            if (et.Value < DateTime.Now)
                result.LeftDays = 0;
            else
                result.LeftDays = (int)(et.Value - DateTime.Now).TotalDays + 1;
        }


        //项目汇总需优化走缓存
        var prosum = _context.Post
        .Where(x => x.ProjectId == result.ProjectId)
        .GroupBy(g => g.ProjectId)
        .Select(s => new
        {
            s.Key,
            PostCount = s.Count(),
            Bounty = s.Where(w => w.Status == PostStatus.发布中).Sum(c => c.Money * c.DeliveryNumber),
            RecruitNumber = s.Where(w => w.Status == PostStatus.发布中).Sum(c => c.DeliveryNumber),
            Deliveries = s.Sum(c => c.Post_Extend.InductionNum),
            // DeliveriesBounty = s.Sum(c => c.Post_Extend.InductionNum * c.Money),
            RegistrationNum = s.Sum(c => c.Post_Extend.DeliveryNum),
            ContractNum = s.Sum(c => c.Post_Extend.ContractNum)
        }).FirstOrDefault();

        if (prosum != null)
        {
            result.PostCount = prosum.PostCount;
            result.Bounty = prosum.Bounty;
            // result.DeliveriesBounty = prosum.DeliveriesBounty ?? 0;
            result.RecruitNumber = prosum.RecruitNumber;
            result.DeliveriesNum = prosum.Deliveries;
            result.RegistrationNum = prosum.RegistrationNum;
            result.ContractNum = prosum.ContractNum;
        }

        var cacheKey = $"st:pj:participant_{id}";
        result.Participant = _cacheHelper.GetRedisCache<ProjectParticipant>(() =>
        {
            //参与者
            var participant = new ProjectParticipant();
            var participantSql = _context.Project_Team
            .Where(x => x.ProjectId == id && x.Status == ProjectStatus.已上线 && x.Type == HrProjectType.协同);

            participant.Count = participantSql.Count();

            var pt = participantSql.OrderByDescending(o => o.CreatedTime).Take(3)
            .Select(s => new
            {
                s.User_Hr.Avatar,
                s.User_Hr.NickName
            }).ToList();

            participant.Avatars = pt.Select(s => s.Avatar ?? string.Empty).ToList();
            participant.FirstHrName = pt.Select(s => s.NickName).FirstOrDefault();
            return participant;
        }, cacheKey, 60);

        return result;
    }

    public async Task<UpdatePostResponse> UpdatePost(UpdatePost model)
    {
        model.UserId = _user.Id;
        //// 编辑岗位时先删除Post_Profit_Stage表中对应PostId的数据再添加，避免重复数据
        //if (!string.IsNullOrWhiteSpace(model.PostId))
        //{
        //    _context.RemoveRange(_context.Post_Profit_Stage.Where(x => x.PostId == model.PostId));
        //    _context.SaveChanges();
        //}
        var result = await _commonPostService.UpdatePost(model);
        UpdatePostResponse post = new UpdatePostResponse()
        {
            PostId = result.PostId,
        };
        return post;
    }

    // /// <summary>
    // /// 不协同的话，不判断金额
    // /// </summary>
    // /// <param name="paymentNode"></param>
    // /// <param name="money"></param>
    // /// <param name="post"></param>
    // /// <exception cref="BadRequestException"></exception>
    // private void CheckMoneyAmount(ProjectPaymentNode? paymentNode, decimal? money, Post? post)
    // {
    //     money ??= 0;
    //     switch (paymentNode)
    //     {
    //         case ProjectPaymentNode.入职过保: if (money < 200) throw new BadRequestException("入职过保，最低200元/人"); break;
    //         case ProjectPaymentNode.简历交付: if (money < 3) throw new BadRequestException("简历交付，最低3元/份"); break;
    //         case ProjectPaymentNode.到面交付: if (money < 30) throw new BadRequestException("到面交付，最低30元/次"); break;
    //             // case ProjectPaymentNode.按天入职过保: if (money < 1) throw new BadRequestException("按天入职过保，最低1元/人/天"); break;
    //     }

    //     // 客单价改变,post.Money为空，是前端没传，不一定是用户置空
    //     if (post != null && money != null && post.Money != money)
    //     {
    //         int count = _context.Project_Teambounty.Where(w => w.PostId == post.PostId && (w.Status == BountyStatus.交付中 || w.Status == BountyStatus.结算中)).Count();
    //         if (count > 0)
    //             throw new BadRequestException("已存在订单，无法修改金额");
    //     }
    // }

    public async Task<HrGetPostDetail> GetPost(string id)
    {
        var result = _context.Post.Where(x => x.PostId == id)
        .Select(s => new HrGetPostDetail
        {
            AgentEnt = new GetAgentEntDetail
            {
                Display = s.Agent_Ent.Display,
                AgentEntId = s.AgentEntId,
                Scale = s.Agent_Ent.Scale,
                ScaleName = s.Agent_Ent.Scale.GetDescription(),
                LogoUrl = s.Agent_Ent.LogoUrl,
                DisplayName = s.Agent_Ent.DisplayName,
                Name = s.Agent_Ent.Name,
                Abbr = s.Agent_Ent.Abbr,
                Nature = s.Agent_Ent.Nature,
                NatureName = s.Agent_Ent.Nature.GetDescription(),
                Industry = s.Agent_Ent.Industry.ToString(),
                Capital = s.Agent_Ent.Capital,
                RegionId = s.Agent_Ent.RegionId,
                Address = s.Agent_Ent.Address,
                Lat = s.Agent_Ent.Location.X,
                Lng = s.Agent_Ent.Location.Y,
                IndustryName = s.Agent_Ent.Dic_Industry.Name,
                UpdatedTime = s.UpdatedTime,
                Describe = s.Agent_Ent.Describe,
                CapitalName = s.Agent_Ent.Capital.GetDescription()
            },
            Hr = new HrModel
            {
                HrId = s.Project.HrId,
            },
            RewardType = s.RewardType,
            RewardTypeName = s.RewardType.GetDescription(),
            PaymentCycle = s.PaymentCycle,
            PaymentCycleName = s.PaymentCycle.GetDescription(),
            PaymentDuration = s.PaymentDuration,
            PaymentDurationStatus = s.PaymentDuration == null ? 0 : 1,
            ProfitStage = _context.Post_Profit_Stage.Where(x => x.PostId == id)
            .Select(s => new PostProfitStage
            {
                GuaranteeDays = s.GuaranteeDays,
                Amount = s.Amount
            }).ToList(),
            ProjectId = s.ProjectId,
            Name = s.Name,
            TieType = s.TieType,
            Status = s.Status,
            Describe = s.Describe,
            Address = s.Address,
            Category = s.Category,
            CategoryName = s.Dic_Post.Name,
            CreatedTime = s.CreatedTime,
            UpdatedTime = s.UpdatedTime,
            Department = s.Department,
            Education = s.Education,
            EducationName = s.Education.GetDescription(),
            Highlights = s.Highlights,
            Lat = s.Location.X,
            Lng = s.Location.Y,
            MaxSalary = s.MaxSalary,
            MinSalary = s.MinSalary,
            Money = s.Money ?? 0,
            PaymentType = s.Project.PaymentType,
            PostId = s.PostId,
            RecruitNumber = s.DeliveryNumber,
            RegionId = s.RegionId,
            Salary = s.Salary,
            SettlementType = s.SettlementType,
            WorkingDays = s.WorkingDays,
            WorkingHours = s.WorkingHours,
            MinMonths = s.MinMonths,
            DaysPerWeek = s.DaysPerWeek,
            GraduationYear = s.GraduationYear,
            Tags = s.Tags,
            Welfare = s.Welfare,
            WelfareCustom = s.WelfareCustom,
            WorkNature = s.WorkNature,
            SalaryType = s.SalaryType,
            SalaryTypeName = s.SalaryType.GetDescription(),
            WorkNatureName = s.WorkNature.GetDescription(),
            Sex = s.Sex,
            MinAge = s.MinAge,
            MaxAge = s.MaxAge,
            InterviewDashboard = new PostRecentInterviews
            {
                RecentDeliveryRate = s.Post_Extend.RecentDeliveryRate,
                HistoryBillingRate = s.Post_Extend.HistoryBillingRate
            },
            LocationMap = s.LocationMap,
            IsResumeExempt = s.IsResumeExempt,
            AgentEntId = s.AgentEntId,
            AgentEntName = s.Agent_Ent.Name,
            IsSalesCommission = s.IsSalesCommission,
            PaymentNode = s.PaymentNode,
            PaymentDays = s.PaymentDays,
            ContractType = s.ContractType,
            ProjectName = s.Project.Agent_Ent.Name,
            ProjectStatus = s.Project.Status,
            ProjectLevelName = s.Project.Level.GetDescription(),
            ProjectAutoId = s.Project.AutoId,
            // ProjectRegion = s.Project.RegionData,
            DeliveryNumber = s.DeliveryNumber,
            LeftStock = s.LeftStock,
            InterviewStatus = s.InterviewStatus,
            HrAgentStatus = s.HrAgentStatus,
            ResumeProcessingDuration = s.Post_Extend.ResumeProcessingDuration,
            FinanceInfo = new HrModel()
            {
                HrId = s.Project.Settlement.UserId,
                Avatar = s.Project.Settlement.Avatar,
                WeChatNo = s.Project.Settlement.WeChatNo,
                HrName = s.Project.Settlement.NickName,
                EntWeChatQrCode = s.Project.Settlement.EntWeChatQrCode,
            },
            PMInfo = new HrModel()
            {
                HrId = s.Project.User_Hr.UserId,
                Avatar = s.Project.User_Hr.Avatar,
                WeChatNo = s.Project.User_Hr.WeChatNo,
                HrName = s.Project.User_Hr.NickName,
                EntWeChatQrCode = s.Project.User_Hr.EntWeChatQrCode,
                Mobile = s.Project.User_Hr.User.Mobile,
                TencentImId = s.Project.User_Hr.TencentImId,
                Post = s.Project.User_Hr.Post,
                OnlineStatus = Tools.GetOnlineStatus(s.Project.User_Hr.User_Extend.LoginTime)
            }
        }).FirstOrDefault();

        if (result == null)
            throw new NotFoundException("内容不存在");

        result.ProfitStage = result.ProfitStage.Select(s => new PostProfitStage
        {
            GuaranteeDays = s.GuaranteeDays <= 0 ? null : s.GuaranteeDays,
            Amount = s.Amount
        }).ToList();

        // ResumeProcessingDuration是秒，改成小时
        result.ResumeProcessingDuration = result.ResumeProcessingDuration.HasValue ? ((result.ResumeProcessingDuration ?? 0) / 3600 + 1) : 0;

        //增加面试信息
        var interview = _context.Post_Interview_Config.Include(i => i.Project_Interviewer).FirstOrDefault(x => x.PostId == id);
        if (interview != null)
        {
            result.Interview = new PostInterviewInfo
            {
                AddressDetail = interview.AddressDetail,
                Address = interview.Address,
                RegionId = interview.RegionId,
                Lat = interview.Location.X,
                Lng = interview.Location.Y,
                InterviewerId = interview.InterviewerId,
                ContactName = interview.Project_Interviewer?.Name,
                ContactPhone = interview.Project_Interviewer?.Phone,
                InterviewMode = interview.InterviewMode,
                AdvanceDays = interview.AdvanceDays,
                InterviewTime = _context.Post_Interview_Date.Where(x => x.PostId == id && x.Time >= DateTime.Now).Select(s => new PostInterviewTimeModel
                {
                    Time = s.Time,
                    Status = s.Status
                }).ToList(),
                Remark = interview.Remark
            };

            // result.InterviewArrangement 设置为InterviewTime数组中的，最近的面试时间，距离现在几天
            if (result.InterviewStatus == InterviewStatus.Open)
            {
                var iad = result.Interview.InterviewTime
                .Where(w => w.Status == PostInterviewTimeStatus.可预约)
                .OrderBy(o => o.Time)
                .Select(s => (int?)(s.Time!.Value - DateTime.Now).TotalDays).FirstOrDefault();
                result.InterviewArrangement = iad.HasValue ? iad.Value + 1 : 0;
            }
        }
        result.Interview ??= new PostInterviewInfo();

        result.ProjectNo = $"{Constants.ProjectIdPre}{result.ProjectAutoId.ToString().PadLeft(6, '0')}";

        //查询协同
        var predicate = PredicateBuilder.New<Post_Team>(x => x.PostId == id);
        predicate = predicate.And(x => x.Project_Team.HrId == _user.Id);

        var teamPost = _context.Post_Team.Where(predicate)
        .Select(s => new
        {
            s.TeamPostId,
            s.TeamProjectId,
            s.Status,
            s.TopTime,
            s.AutoId,
            s.Show,
            AppletQrCode = s.AppletQrCode,
            EntryIn72hour = (bool?)s.Post_Excellent.EntryIn72hour,
            InterviewIn24Hour = (bool?)s.Post_Excellent.InterviewIn24Hour,
            ExcellentStatus = (ExcellentPostStatus?)s.Post_Excellent.Status,
            s.Project_Team.Type
        }).FirstOrDefault();

        if (teamPost != null)
        {
            result.CurrentHrId = _user.Id;
            result.TeamPostAutoId = teamPost.AutoId;
            result.TeamPostNo = $"{Constants.TeamPostIdPre}{result.TeamPostAutoId.ToString()!.PadLeft(2, '0')}";
            result.Top = teamPost.TopTime > Config.Constants.DefaultTime;
            result.TeamPostId = teamPost.TeamPostId;
            result.Show = teamPost.Show;
            result.AppletQrCode = teamPost.AppletQrCode;
            result.ExcellentStatus = teamPost.ExcellentStatus;
            result.InterviewIn24Hour = teamPost.InterviewIn24Hour ?? false;
            result.EntryIn72hour = teamPost.EntryIn72hour ?? false;
            result.TeamProjectId = teamPost.TeamProjectId;
        }

        result.PostNo = $"{Constants.PostIdPre}{result.PostAutoId.ToString()!.PadLeft(2, '0')}";

        // if (result.TeamPostAutoId.HasValue && result.TeamPostAutoId > 0)
        //     result.TeamPostNo = $"{Constants.TeamPostIdPre}{result.TeamPostAutoId.ToString()!.PadLeft(2, '0')}";

        result.PaymentNodeName = result.PaymentNode?.GetDescription();
        result.PaymentTypeName = result.PaymentType?.GetDescription();
        result.SettlementTypeName = result.SettlementType?.GetDescription();

        result.SalaryName = Tools.FormattingSalary(result.MinSalary, result.MaxSalary, result.SalaryType, result.WorkNature, result.Salary);

        if (result.Welfare?.Count > 0)
        {
            var welfare = _commonDicService.GetWelfare();
            result.Welfare = welfare.Where(x => result.Welfare.Any(a => a.Id == x.Id)).ToList();
        }

        if (!string.IsNullOrWhiteSpace(result.RegionId))
            result.Region = _commonDicService.GetCityById(result.RegionId);

        if (!string.IsNullOrWhiteSpace(result.AgentEnt?.RegionId))
            result.AgentEnt.Region = _commonDicService.GetCityById(result.AgentEnt.RegionId);

        if (result.AgentEnt != null)
        {
            var entBus = await _noahData.GetEnterpriseCache(result.AgentEnt.Name);
            result.AgentEnt.Age = entBus.Age;
            result.AgentEnt.Tags = entBus.Tags;
            result.AgentEnt.RegisterDate = entBus.RegisterDate;
            // result.AgentEnt.Name = string.Empty;
        }

        var javaPostBehaviorDataRequest = new JavaPostBehaviorData();

        //如果是主创
        if (teamPost?.Type == HrProjectType.自己)
        {
            result.SelfPost = true;

            javaPostBehaviorDataRequest.id = result.PostId!;
            javaPostBehaviorDataRequest.type = 1;

            // var ace = await _commonCacheService.GetPostData(id);
        }
        else
        {
            if (teamPost != null)
            {
                javaPostBehaviorDataRequest.id = teamPost.TeamPostId;
                javaPostBehaviorDataRequest.type = 2;
                result.Status = teamPost.Status;

                // var ace = await _commonCacheService.GetTeamPostData(teamPost.TeamPostId);
            }
        }

        result.StatusName = result.Status.GetDescription();

        JavaPostBehaviorDataResponse? pbd = null;
        // if (javaPostBehaviorDataRequest.type > 0)
        //     pbd = await _javaDataApi.GetPostBehaviorData(javaPostBehaviorDataRequest);

        pbd = pbd ?? new JavaPostBehaviorDataResponse();

        result.PostVisitorsNum = new PostRecommendedNum
        {
            Num = pbd.postVisitNum,
            Anyone = pbd.postPlatformVisitNum,
            Internal = pbd.postEntVisitNum,
            Platform = pbd.postNuopinVisitNum,
            Self = pbd.postSelfVisitNum
        };
        result.PostRecommendedNum = new PostRecommendedNum
        {
            Num = pbd.postRegistrationNum,
            Anyone = pbd.postPlatformRegistrationNum,
            Internal = pbd.postEntRegistrationNum,
            Platform = pbd.postNuopinRegistrationNum,
            Self = pbd.postSelfRegistrationNum
        };
        result.PostRegistrationNum = new PostRecommendedNum
        {
            Num = pbd.postRegistrationNum,
            Anyone = pbd.postPlatformRegistrationNum,
            Internal = pbd.postEntRegistrationNum,
            Platform = pbd.postNuopinRegistrationNum,
            Self = pbd.postSelfRegistrationNum
        };
        result.PostInterviewNum = new PostRecommendedNum
        {
            Anyone = pbd.postPlatformInterviewedNum,
            Internal = pbd.postEntInterviewedNum,
            Platform = pbd.postNuopinInterviewedNum,
            Self = pbd.postSelfInterviewedNum,
            Num = pbd.postInterviewedNum
        };
        result.PostInductionNum = new PostRecommendedNum
        {
            Anyone = pbd.postPlatformTakeOfficeNum,
            Internal = pbd.postEntTakeOfficeNum,
            Platform = pbd.postNuopinTakeOfficeNum,
            Self = pbd.postSelfTakeOfficeNum,
            Num = pbd.postTakeOfficeNum
        };

        if (result.PaymentNode.HasValue)
            result.ProjectNodeType = GetProjectNodeType(result.PaymentNode, result.PaymentDays);

        //面试数据看板
        if (interview != null)
        {
            //InterviewTime 只保留昨天至未来13天的面试时间
            var beginTime = DateTime.Now.Date.AddDays(-1);
            var endTime = beginTime.AddDays(13);
            result.InterviewDashboard ??= new PostRecentInterviews();

            foreach (var tm in Enumerable.Range(0, 14).Select(i => beginTime.AddDays(i)))
            {
                PostInterviewTimeStatus? st = null;
                if (result.Interview?.InterviewTime?.Any(x => x.Time?.Date == tm && x.Status == PostInterviewTimeStatus.可预约 && tm >= DateTime.Today.AddDays(interview.AdvanceDays)) == true)
                    st = PostInterviewTimeStatus.可预约;
                else
                    //if (result.Interview?.InterviewTime?.Any(x => x.Time?.Date == tm && x.Status == PostInterviewTimeStatus.取消预约) == true)
                    st = PostInterviewTimeStatus.取消预约;

                var its = new PostInterviewTimeModel
                {
                    Time = tm,
                    Status = st,
                    SubTime = result.Interview?.InterviewTime?.Where(x => x.Time?.Date == tm)?.Select(s => TimeOnly.FromDateTime(s.Time!.Value)).OrderBy(o => o).ToList()
                };
                result.InterviewDashboard.Recent.Add(its);
            }
        }

        //最近14天
        var recent14 = DateTime.Now.Date.AddDays(-13);

        //本周面试人数
        var weekInterviewCt = _context.Recruit_Record.Where(x => x.CreatedTime >= recent14 && x.Recruit.Post_Delivery.PostId == id && Constants.InterviewStatus.Contains(x.Status))
        .Count();

        result.InterviewDashboard.RecentInterviewNum = weekInterviewCt;
        result.InterviewDashboard.InterviewNum = result.Interview?.InterviewTime?.Where(x => x.Status == PostInterviewTimeStatus.可预约 && x.Time > DateTime.Now).Count() ?? 0;

        //最近7天
        var recent7 = DateTime.Now.Date.AddDays(-6);

        //本周面试人数
        var weekInterview = _context.Recruit_Record.Where(x => x.CreatedTime >= recent7
        && x.Recruit.Post_Delivery.PostId == id && Constants.InterviewStatus.Contains(x.Status))
        .Select(s => s.RecruitId).Distinct().Count();

        result.InterviewDashboard.RecentInterviewNum = weekInterview;

        //产品：该数是未来可预约面试的场次，与时间无关
        result.InterviewDashboard.InterviewNum = result.Interview.InterviewTime?.Where(x => x.Status == PostInterviewTimeStatus.可预约 && x.Time > DateTime.Now).Count() ?? 0;

        // 产品要求实时计算佣金
        var isNoah = _context.User_Hr.Include(userHr => userHr.Enterprise).First(s => s.UserId == _user.Id).Enterprise.Specific?.Contains(EntSpecific.Noah) == true;
        var settings = _context.Sys_Settings.FirstOrDefault();
        BountyConfig? bountyConfig;
        if (isNoah)
        {
            if ((bool)result.IsSalesCommission!)
                bountyConfig = settings.NoahFull;
            else
                bountyConfig = settings.NoahNoSale;
        }
        else
        {
            if ((bool)result.IsSalesCommission!)
                bountyConfig = settings.PlatformFull;
            else
                bountyConfig = settings.PlatformNoSale;
        }
        result.DeliveriesBounty = (((decimal)result.Money! * bountyConfig.Clue).ToFixed(2)) + (((decimal)result.Money! * bountyConfig.Follower).ToFixed(2));
        return result;
    }

    public ProjectNodeType GetProjectNodeType(ProjectPaymentNode? node, int? days)
    {
        var response = new ProjectNodeType();
        var PaymentNodeName = string.Empty;
        var UnitName = string.Empty;
        var PlaceHolder = string.Empty;

        switch (node)
        {
            case ProjectPaymentNode.入职过保: PaymentNodeName = $"交付分润（入职过保{days ?? 0}天）"; UnitName = "元/人"; PlaceHolder = "最低200元/人"; break;
            case ProjectPaymentNode.简历交付: PaymentNodeName = "交付分润（简历交付）"; UnitName = "元/份"; PlaceHolder = "最低3元/份"; break;
            case ProjectPaymentNode.到面交付: PaymentNodeName = "交付分润（到面交付）"; UnitName = "元/次"; PlaceHolder = "最低30元/次"; break;
                // case ProjectPaymentNode.按天入职过保: PaymentNodeName = $"交付分润（按天入职过保{days ?? 0}天）"; UnitName = "元/人/天"; PlaceHolder = "最低1元/人/天"; break;
        }
        response.PaymentNodeName = PaymentNodeName;
        response.UnitName = UnitName;
        response.PlaceHolder = PlaceHolder;

        return response;
    }

    public async Task<HrGetPostCopywritingResponse> HrGetPostCopywriting(HrGetPostCopywriting model)
    {
        // var result = new HrGetPostCopywritingResponse();

        var teamPost = _context.Post_Team.Where(x => x.TeamPostId == model.TeamPostId)
        .Select(s => new HrGetPostTextInfo
        {
            Hr = new HrModel
            {
                HrId = s.Project_Team.HrId
            },
            TeamPostId = s.TeamPostId,
            Name = s.Post.Name,
            Describe = s.Post.Describe,
            Address = s.Post.Address,
            CategoryName = s.Post.Dic_Post.Name,
            Education = s.Post.Education,
            EducationName = s.Post.Education.GetDescription(),
            MaxSalary = s.Post.MaxSalary,
            MinSalary = s.Post.MinSalary,
            PostId = s.Post.PostId,
            RegionId = s.Post.RegionId,
            Salary = s.Post.Salary,
            SettlementType = s.Post.SettlementType,
            Welfare = s.Post.Welfare,
            WelfareCustom = s.Post.WelfareCustom,
            WorkNature = s.Post.WorkNature,
            SalaryType = s.Post.SalaryType,
            SalaryTypeName = s.Post.SalaryType.GetDescription(),
            WorkNatureName = s.Post.WorkNature.GetDescription(),
            Sex = s.Post.Sex,
            MinAge = s.Post.MinAge,
            MaxAge = s.Post.MaxAge,
            AppletShortLink = s.AppletShortLink,
            AppletShortLinkExp = s.AppletShortLinkExp
        }).FirstOrDefault();

        if (teamPost == null)
            throw new NotFoundException("内容不存在");

        teamPost.PostNo = $"{Constants.PostIdPre}{teamPost.PostAutoId.ToString()!.PadLeft(6, '0')}";
        teamPost.PaymentNodeName = teamPost.PaymentNode?.GetDescription();
        teamPost.PaymentTypeName = teamPost.PaymentType?.GetDescription();
        teamPost.SalaryName = Tools.FormattingSalary(teamPost.MinSalary, teamPost.MaxSalary, teamPost.SalaryType, teamPost.WorkNature, teamPost.Salary);

        if (teamPost.Welfare?.Count > 0)
        {
            var welfare = _commonDicService.GetWelfare();
            teamPost.Welfare = welfare.Where(x => teamPost.Welfare.Any(a => a.Id == x.Id)).ToList();
        }

        if (!string.IsNullOrWhiteSpace(teamPost.RegionId))
            teamPost.Region = _commonDicService.GetCityById(teamPost.RegionId);

        if (!string.IsNullOrWhiteSpace(teamPost.AgentEnt?.RegionId))
            teamPost.AgentEnt.Region = _commonDicService.GetCityById(teamPost.AgentEnt.RegionId);

        //判断短链接是否失效
        var expDays = DateTime.Today.AddDays(29);
        if (!teamPost.AppletShortLinkExp.HasValue || teamPost.AppletShortLinkExp.Value < expDays)
        {
            var shortLink = await _weChatHelper.GetWxShortLinkForPosition(teamPost.TeamPostId!, teamPost.Hr!.HrId!, teamPost.Name);
            if (!string.IsNullOrWhiteSpace(shortLink))
            {
                var post = _context.Post_Team.First(x => x.TeamPostId == teamPost.TeamPostId);
                post.AppletShortLink = shortLink;
                post.AppletShortLinkExp = DateTime.Today.AddDays(30);
                _context.SaveChanges();

                teamPost.AppletShortLink = post.AppletShortLink;
                teamPost.AppletShortLinkExp = post.AppletShortLinkExp;
            }
        }

        teamPost.AppletPath = $"pages/positiondetail/positiondetail?id={teamPost.TeamPostId}&adviser={teamPost.Hr!.HrId}";

        var result = new HrGetPostCopywritingResponse();

        result.Text = MyRedis.Client.HGet(RedisKey.PostCopywriting, teamPost.TeamPostId);

        if (string.IsNullOrWhiteSpace(result.Text))
        {
            var sb = new StringBuilder();
            var sexName = teamPost.Sex == Sex.男 ? "男性" : teamPost.Sex == Sex.女 ? "女性" : "不限";
            var ageName = string.Empty;
            if (teamPost.MinAge + teamPost.MaxAge <= 0)
                ageName = "不限";
            else if (teamPost.MinAge > 0 && teamPost.MaxAge <= 0)
                ageName = $"{teamPost.MinAge}岁以上";
            else if (teamPost.MinAge <= 0 && teamPost.MaxAge > 0)
                ageName = $"{teamPost.MaxAge}岁以下";
            else if (teamPost.MinAge > 0 && teamPost.MaxAge > 0)
                ageName = $"{teamPost.MinAge}-{teamPost.MaxAge}岁";

            var educationName = string.Empty;
            if (teamPost.Education == EducationType.不限)
                educationName = "学历不限";
            else if ((int)teamPost.Education > 0 && (int)teamPost.Education < 5)
                educationName = $"{teamPost.EducationName}及以上";
            else
                educationName = teamPost.EducationName;

            var welfareText = string.Empty;
            var welfares = (teamPost.Welfare?.Select(s => s.Name).ToList() ?? new List<string?>())
            .Union(teamPost.WelfareCustom ?? new List<string>());
            if (welfares.Count() > 0)
                welfareText = $"、{string.Join('、', welfares)}";

            sb.AppendLine($"招聘岗位：{teamPost.Name}");
            sb.AppendLine($"职位性质：{teamPost.WorkNatureName}");
            sb.AppendLine($"岗位要求：{sexName}、{ageName}、{educationName}");
            sb.AppendLine($"薪资福利：{teamPost.SalaryName}{welfareText}");
            sb.Append($"工作地址：{teamPost.Region?.ProvinceName}{teamPost.Region?.CityName}{teamPost.Region?.CountyName}{teamPost?.Address}");

            result.Text = sb.ToString();
        }

        if (model.Type == 0 && !string.IsNullOrWhiteSpace(teamPost?.AppletShortLink))
            result.UrlText = $"简历投递直通车：可点击{teamPost.AppletShortLink} 页面右下角报名，最快当天入职";
        else if (model.Type == 1 && !string.IsNullOrWhiteSpace(teamPost?.AppletPath))
            result.UrlText = $"简历投递直通车：{teamPost.AppletPath}";

        return result;
    }

    public EmptyResponse ProjectTransfer(ProjectTransfer model)
    {
        var hrId = _context.User_Hr.Where(x => x.User.Mobile == model.HrMobile).Select(s => s.UserId).FirstOrDefault();
        if (string.IsNullOrWhiteSpace(hrId))
            throw new BadRequestException("顾问不存在");

        if (!_context.Project.Any(x => x.ProjectId == model.ProjectId && x.HrId == _user.Id))
            throw new BadRequestException("项目不存在");

        using (var tran = _context.Database.BeginTransaction())
        {
            _context.Project_Team.Where(x => x.ProjectId == model.ProjectId && x.Type == HrProjectType.自己)
            .ExecuteUpdate(s => s.SetProperty(b => b.HrId, hrId));

            _context.Project.Where(x => x.ProjectId == model.ProjectId)
            .ExecuteUpdate(s => s.SetProperty(b => b.HrId, hrId));

            _context.Recruit.Where(x => x.Post_Delivery.Post.Project.ProjectId == model.ProjectId)
            .ExecuteUpdate(s => s.SetProperty(b => b.HrId, hrId));

            _context.Recruit_Interview.Where(x => x.Project_Interviewer.Project.ProjectId == model.ProjectId)
            .ExecuteUpdate(s => s.SetProperty(b => b.HrId, hrId));

            tran.Commit();
        }

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public EmptyResponse HrUpdatePostCopywriting(HrUpdatePostCopywriting model)
    {
        var teamPost = _context.Post_Team.Where(x => x.TeamPostId == model.TeamPostId
        && x.Project_Team.HrId == _user.Id)
        .Select(s => new
        {
            s.TeamPostId
        }).FirstOrDefault();

        if (teamPost == null)
            throw new NotFoundException("内容不存在");

        MyRedis.Client.HSet(RedisKey.PostCopywriting, teamPost.TeamPostId, model.Text);

        return new EmptyResponse();
    }

    public GetProjectTeamHrResponse GetProjectHr(GetProjectTeamHr model)
    {
        var result = new GetProjectTeamHrResponse();

        var predicate = PredicateBuilder.New<Project_Team>(x => x.ProjectId == model.ProjectId
        && x.Status == ProjectStatus.已上线 && x.Type == HrProjectType.协同);

        var sql = _context.Project_Team.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetProjectTeamHrDetail
        {
            TeamProjectId = s.TeamProjectId,
            Avatar = s.User_Hr.Avatar,
            EntId = s.User_Hr.EntId,
            EntName = s.User_Hr.Enterprise.Name,
            Post = s.User_Hr.Post,
            HrName = s.User_Hr.NickName,
            CreatedTime = s.CreatedTime
        }).ToList();

        var teamProjectIds = result.Rows.Select(s => s.TeamProjectId).ToList();

        var groups = _context.Post_Delivery.Where(x => teamProjectIds.Contains(x.Post_Team.TeamProjectId))
        .GroupBy(g => g.Post_Team.TeamProjectId)
        .Select(s => new
        {
            s.Key,
            Ct = s.Count()
        });

        foreach (var item in result.Rows)
        {
            var group = groups.FirstOrDefault(x => x.Key == item.TeamProjectId);
            item.CreationDays = (int)((DateTime.Now - item.CreatedTime).TotalDays) + 1;
            item.CreationDays = item.CreationDays > 1 ? item.CreationDays : 1;
            item.ProjDeliver = "****";
            item.ProjBounty = "****";
            item.RecmdTalents = (group?.Ct ?? 0).ToString();
        }

        return result;
    }

    public MyProjectsResponse MyProjects(MyProjects model)
    {
        var result = new MyProjectsResponse();

        var idPre = Constants.ProjectIdPre;

        if (!model.Type.HasValue)
            throw new BadRequestException("缺少type");

        var predicate = PredicateBuilder.New<Project_Team>(x => x.HrId == _user.Id);

        if (model.Type == MyProjectsType.自己)
            predicate = predicate.And(x => x.Project.HrId == _user.Id && x.Project.Status != ProjectStatus.归档);
        else if (model.Type == MyProjectsType.协同)
            predicate = predicate.And(x => x.Project.HrId != _user.Id && x.Project.Status != ProjectStatus.归档);
        else if (model.Type == MyProjectsType.归档)
            predicate = predicate.And(x => x.Project.Status == ProjectStatus.归档);

        if (!string.IsNullOrWhiteSpace(model.Search))
        {
            if (model.Search.StartsWith(idPre) && int.TryParse(model.Search.Substring(1), out var id))
                predicate = predicate.And(x => x.Project.AutoId == id);
            else
                predicate = predicate.And(x => x.Project.Agent_Ent.Name.Contains(model.Search));
        }

        var sql = _context.Project_Team.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(x => x.Post_Team.Any(x => x.Show))
        .ThenByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new MyProjectsDetailInfo
        {
            ProjectAutoId = s.Project.AutoId,
            ProjectId = s.ProjectId,
            Name = s.Project.Agent_Ent.Name,
            AgentEntId = s.Project.AgentEntId ?? string.Empty,
            Status = s.Project.Status,
            Type = s.Project.Type,
            ClearingChannel = s.Project.ClearingChannel,
            ClearingChannelName = s.Project.ClearingChannel.GetDescription(),
            ClearingType = s.Project.ClearingType,
            ClearingTypeName = s.Project.ClearingType.GetDescription(),
            CreatedTime = s.CreatedTime,
            EndTime = s.Project.EndTime.ToYYYY_MM_DD(),
            HrId = s.Project.HrId,
            HrName = s.Project.User_Hr.NickName,
            Industry = s.Project.Industry,
            IndustryName = s.Project.Industry.GetDescription(),
            Level = s.Project.Level,
            LevelName = s.Project.Level.GetDescription(),
            NuoId = s.Project.NuoId,
            PaymentType = s.Project.PaymentType,
            // Region = s.Project.RegionData,
            StatusName = s.Project.Status.GetDescription(),
            TypeName = s.Project.Type.GetDescription(),
            WorkCycleType = s.Project.WorkCycleType,
            WorkCycleTypeName = s.Project.WorkCycleType.GetDescription(),
            AgentEnt = new GetAgentEntDetail { LogoUrl = s.Project.Agent_Ent.LogoUrl },
            SelfProj = s.Type == HrProjectType.自己,
            TeamStatus = s.Status,
            TeamStatusName = s.Status.GetDescription(),
            TeamProjectId = s.TeamProjectId
        }).ToList();


        //主创
        var projectIds = result.Rows.Where(x => x.SelfProj).Select(s => s.ProjectId).ToList();
        var projSummary = _context.Post
        .Where(x => projectIds.Contains(x.ProjectId) && x.Status == PostStatus.发布中)
        .GroupBy(g => g.ProjectId)
        .Select(s => new
        {
            s.Key,
            PostCount = s.Count(),
            Bounty = s.Sum(c => c.Money * c.DeliveryNumber),
            RecruitNumber = s.Where(x => true).Sum(c => c.DeliveryNumber),
            DeliveryNumber = s.Where(x => true).Sum(c => c.DeliveryNumber),
            Deliveries = s.Where(x => true).Sum(c => c.DeliveryNumber - c.LeftStock),
            DeliveriesBounty = s.Where(x => true).Sum(c => (c.DeliveryNumber - c.LeftStock) * c.Money),
            RegistrationNum = s.Sum(c => c.Post_Extend.DeliveryNum),
            ContractNum = s.Sum(c => c.Post_Extend.ContractNum)
        }).ToList();

        //协同
        var teamProjectIds = result.Rows.Where(x => !x.SelfProj).Select(s => s.TeamProjectId).ToList();
        var teamProjSummary = _context.Post_Team
        .Where(x => teamProjectIds.Contains(x.TeamProjectId))
        .GroupBy(g => g.TeamProjectId)
        .Select(s => new
        {
            s.Key,
            Deliveries = s.Sum(c => c.Post.DeliveryNumber - c.Post.LeftStock),
            DeliveriesBounty = s.Sum(c => (c.Post.DeliveryNumber - c.Post.LeftStock) * c.Post.Money),
            RegistrationNum = s.Sum(c => c.Post_Team_Extend.DeliveryNum),
            ContractNum = s.Sum(c => c.Post_Team_Extend.ContractNum)
        }).ToList();

        result.Rows.ForEach(item =>
        {
            item.PaymentTypeName = item.PaymentType?.GetDescription();

            item.ProjectNo = $"{idPre}{item.ProjectAutoId.ToString().PadLeft(6, '0')}";

            if (item.SelfProj)
            {
                var prosum = projSummary.FirstOrDefault(x => x.Key == item.ProjectId);
                if (prosum != null)
                {
                    item.PostCount = prosum.PostCount;
                    item.Bounty = prosum.Bounty ?? 0;
                    item.DeliveriesBounty = prosum.DeliveriesBounty ?? 0;
                    item.RecruitNumber = prosum.RecruitNumber;
                    item.DeliveriesNum = prosum.Deliveries;
                    item.RegistrationNum = prosum.RegistrationNum;
                    item.ContractNum = prosum.ContractNum;
                }
            }
            else
            {
                var prosum = teamProjSummary.FirstOrDefault(x => x.Key == item.TeamProjectId);
                if (prosum != null)
                {
                    item.DeliveriesBounty = prosum.DeliveriesBounty ?? 0;
                    item.DeliveriesNum = prosum.Deliveries;
                    item.RegistrationNum = prosum.RegistrationNum;
                    item.ContractNum = prosum.ContractNum;
                }
            }
        });

        return result;
    }

    public MyProjectsResponse GetProjectsByHR(GetHRProsQuery model)
    {
        var result = new MyProjectsResponse();

        var idPre = Constants.ProjectIdPre;

        var sql = _context.Project_Team.Where(x => x.HrId == model.UserId);

        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new MyProjectsDetailInfo
        {
            ProjectAutoId = s.Project.AutoId,
            ProjectId = s.ProjectId,
            Name = s.Project.Agent_Ent.Name,
            AgentEntId = s.Project.AgentEntId ?? string.Empty,
            Status = s.Project.Status,
            Type = s.Project.Type,
            ClearingChannel = s.Project.ClearingChannel,
            ClearingChannelName = s.Project.ClearingChannel.GetDescription(),
            ClearingType = s.Project.ClearingType,
            ClearingTypeName = s.Project.ClearingType.GetDescription(),
            CreatedTime = s.CreatedTime,
            EndTime = s.Project.EndTime.ToYYYY_MM_DD(),
            HrId = s.Project.HrId,
            HrName = s.Project.User_Hr.NickName,
            Industry = s.Project.Industry,
            IndustryName = s.Project.Industry.GetDescription(),
            Level = s.Project.Level,
            LevelName = s.Project.Level.GetDescription(),
            NuoId = s.Project.NuoId,
            PaymentType = s.Project.PaymentType,
            // Region = s.Project.RegionData,
            StatusName = s.Project.Status.GetDescription(),
            TypeName = s.Project.Type.GetDescription(),
            WorkCycleType = s.Project.WorkCycleType,
            WorkCycleTypeName = s.Project.WorkCycleType.GetDescription(),
            AgentEnt = new GetAgentEntDetail { LogoUrl = s.Project.Agent_Ent.LogoUrl },
            SelfProj = s.Type == HrProjectType.自己,
            TeamStatus = s.Status,
            TeamStatusName = s.Status.GetDescription(),
            TeamProjectId = s.TeamProjectId
        }).ToList();


        //主创
        var projectIds = result.Rows.Where(x => x.SelfProj).Select(s => s.ProjectId).ToList();
        var projSummary = _context.Post
        .Where(x => projectIds.Contains(x.ProjectId))
        .GroupBy(g => g.ProjectId)
        .Select(s => new
        {
            s.Key,
            PostCount = s.Count(),
            Bounty = s.Sum(c => c.Money * c.DeliveryNumber),
            RecruitNumber = s.Sum(c => c.DeliveryNumber),
            Deliveries = s.Sum(c => c.Post_Extend.InductionNum),
            DeliveriesBounty = s.Sum(c => c.Post_Extend.InductionNum * c.Money),
            RegistrationNum = s.Sum(c => c.Post_Extend.DeliveryNum),
            ContractNum = s.Sum(c => c.Post_Extend.ContractNum)
        }).ToList();

        //协同
        var teamProjectIds = result.Rows.Where(x => !x.SelfProj).Select(s => s.TeamProjectId).ToList();
        var teamProjSummary = _context.Post_Team
        .Where(x => teamProjectIds.Contains(x.TeamProjectId))
        .GroupBy(g => g.TeamProjectId)
        .Select(s => new
        {
            s.Key,
            Deliveries = s.Sum(c => c.Post_Team_Extend.InductionNum),
            DeliveriesBounty = s.Sum(c => c.Post_Team_Extend.InductionNum * c.Post.Money),
            RegistrationNum = s.Sum(c => c.Post_Team_Extend.DeliveryNum),
            ContractNum = s.Sum(c => c.Post_Team_Extend.ContractNum)
        }).ToList();

        result.Rows.ForEach(item =>
        {
            item.PaymentTypeName = item.PaymentType?.GetDescription();

            item.ProjectNo = $"{idPre}{item.ProjectAutoId.ToString().PadLeft(6, '0')}";

            if (item.SelfProj)
            {
                var prosum = projSummary.FirstOrDefault(x => x.Key == item.ProjectId);
                if (prosum != null)
                {
                    item.PostCount = prosum.PostCount;
                    item.Bounty = prosum.Bounty ?? 0;
                    item.DeliveriesBounty = prosum.DeliveriesBounty ?? 0;
                    item.RecruitNumber = prosum.RecruitNumber;
                    item.DeliveriesNum = prosum.Deliveries;
                    item.RegistrationNum = prosum.RegistrationNum;
                    item.ContractNum = prosum.ContractNum;
                }
            }
            else
            {
                var prosum = teamProjSummary.FirstOrDefault(x => x.Key == item.TeamProjectId);
                if (prosum != null)
                {
                    item.DeliveriesBounty = prosum.DeliveriesBounty ?? 0;
                    item.DeliveriesNum = prosum.Deliveries;
                    item.RegistrationNum = prosum.RegistrationNum;
                    item.ContractNum = prosum.ContractNum;
                }
            }
        });

        return result;
    }

    public HRProSimpleResponse GetProSimpleByHR(GetHRProsQuery model)
    {
        if (string.IsNullOrEmpty(model.UserId))
            throw new BadRequestException("顾问标识为空");

        var result = new HRProSimpleResponse();

        var idPre = Constants.ProjectIdPre;

        var sql = _context.Project_Team.Where(x => x.HrId == model.UserId && x.Project.Status != ProjectStatus.归档);

        result.Total = sql.Count();

        result.Rows = sql
        .OrderBy(x => x.Type)
        .ThenByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new HRProSimpleInfo
        {
            ProjectAutoId = s.Project.AutoId,
            ProjectId = s.ProjectId,
            TeamProjectId = s.TeamProjectId,
            Name = s.Project.Agent_Ent.Name,
            Status = s.Project.Status,
            Type = s.Project.Type,
            CreatedTime = s.CreatedTime,
            HrName = s.Project.User_Hr.NickName,
            HrPost = s.Project.User_Hr.Post,
            Industry = s.Project.Industry,
            IndustryName = s.Project.Industry.GetDescription(),
            LevelName = s.Project.Level.GetDescription(),
            StatusName = s.Project.Status.GetDescription(),
            TypeName = s.Project.Type.GetDescription(),
            WorkCycleTypeName = s.Project.WorkCycleType.GetDescription(),
            SelfProj = s.Type == HrProjectType.自己
        }).ToList();


        var projectIds = result.Rows.Select(s => s.ProjectId).ToList();
        var projSummary = _context.Post
        .Where(x => projectIds.Contains(x.ProjectId))
        .GroupBy(g => g.ProjectId)
        .Select(s => new
        {
            s.Key,
            PostCount = s.Count(),
            Bounty = s.Sum(c => c.Money * c.DeliveryNumber),
            RecruitNumber = s.Sum(c => c.DeliveryNumber),
            Deliveries = s.Sum(c => c.Post_Extend.InductionNum),
            DeliveriesBounty = s.Sum(c => c.Post_Extend.InductionNum * c.Money),
            RegistrationNum = s.Sum(c => c.Post_Extend.DeliveryNum),
            ContractNum = s.Sum(c => c.Post_Extend.ContractNum)
        }).ToList();

        result.Rows.ForEach(item =>
        {
            item.ProjectNo = $"{idPre}{item.ProjectAutoId.ToString().PadLeft(6, '0')}";

            var prosum = projSummary.FirstOrDefault(x => x.Key == item.ProjectId);
            if (prosum != null)
            {
                item.PostCount = prosum.PostCount;
                item.RecruitNumber = prosum.RecruitNumber;
                item.DeliveriesNum = prosum.Deliveries;
                item.RegistrationNum = prosum.RegistrationNum;
                item.ContractNum = prosum.ContractNum;
            }
        });

        return result;
    }

    public MyProjectPostsResponse MyProjectPosts(MyProjectPosts model)
    {
        var result = new MyProjectPostsResponse();

        var idPre = Constants.PostIdPre;

        // 销售或项目经理
        var predicate = PredicateBuilder.New<Post>(x => x.ProjectId == model.ProjectId);

        if (model.IsReceiver == true)
            predicate = predicate.And(x => x.Project.HrId == _user.Id);
        else
            predicate = predicate.And(x => x.Project.SaleUserId == _user.Id);

        if (!string.IsNullOrWhiteSpace(model.Search))
        {
            if (model.Search.StartsWith(idPre) && int.TryParse(model.Search.Substring(1), out var id))
                predicate = predicate.And(x => x.AutoId == id || x.Name.Contains(model.Search));
            else
                predicate = predicate.And(x => x.Name.Contains(model.Search));
        }

        if (model.Category.HasValue)
        {
            var categoryLevel = (_commonDicService.GetPost())
            .FirstOrDefault(x => x.Id == model.Category.ToString())?.Level;

            if (!string.IsNullOrWhiteSpace(categoryLevel))
                predicate = predicate.And(x => x.Dic_Post.Level.StartsWith(categoryLevel));
        }

        if (model.Citys?.Count > 0)
        {
            var subPredicate = PredicateBuilder.New<Post>(false);
            foreach (var item in model.Citys)
                subPredicate = subPredicate.Or(x => x.RegionId.StartsWith(item));

            predicate = predicate.And(subPredicate);
        }

        //职位状态筛选
        if (model.Status == MyProjectPostsStatus.关闭)
            predicate = predicate.And(x => x.Status == PostStatus.关闭);
        else if (model.Status == MyProjectPostsStatus.发布中)
            predicate = predicate.And(x => x.Status == PostStatus.发布中);
        else if (model.Status == MyProjectPostsStatus.待审核)
            predicate = predicate.And(x => x.Status == PostStatus.待审核);

        var sql = _context.Post.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new MyProjectPostInfo
        {
            Hr = new HrModel
            {
                HrId = s.Project.HrId,
                HrName = s.Project.User_Hr.NickName,
                Avatar = s.Project.User_Hr.Avatar,
                Post = s.Project.User_Hr.Post,
            },
            AgentEnt = new GetAgentEntDetail
            {
                DisplayName = s.Agent_Ent.DisplayName,
                AgentEntId = s.AgentEntId,
                Scale = s.Agent_Ent.Scale,
                Abbr = s.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Agent_Ent.Abbr : s.Agent_Ent.DisplayName,
                LogoUrl = s.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Agent_Ent.LogoUrl : string.Empty,
                ScaleName = s.Agent_Ent.Scale.GetDescription(),
                Industry = s.Agent_Ent.Industry.ToString(),
                IndustryName = s.Agent_Ent.Dic_Industry.Name
            },
            RewardType = s.RewardType,
            RewardTypeName = s.RewardType.GetDescription(),
            PaymentCycle = s.PaymentCycle,
            PaymentCycleName = s.PaymentCycle.GetDescription(),
            PaymentDuration = s.PaymentDuration,
            CommissionTotal = (s.Money ?? 0) * s.DeliveryNumber,
            PostAutoId = s.AutoId,
            ProjectId = s.ProjectId,
            Name = s.Name,
            Status = s.Status,
            CreatedTime = s.CreatedTime,
            UpdatedTime = s.UpdatedTime,
            PaymentType = s.Project.PaymentType,
            PaymentNode = s.PaymentNode,
            PaymentDays = s.PaymentDays,
            Address = s.Address,
            Category = s.Category,
            CategoryName = s.Dic_Post.Name,
            Department = s.Department,
            Describe = s.Describe,
            Education = s.Education,
            EducationName = s.Education.GetDescription(),
            Highlights = s.Highlights,
            Tags = s.Tags,
            Welfare = s.Welfare,
            WelfareCustom = s.WelfareCustom,
            Lat = s.Location.X,
            Lng = s.Location.Y,
            MaxSalary = s.MaxSalary,
            MinSalary = s.MinSalary,
            Money = s.Money,
            PostId = s.PostId,
            RecruitNumber = s.DeliveryNumber,
            RegionId = s.RegionId,
            Salary = s.Salary,
            SettlementType = s.SettlementType,
            WorkingDays = s.WorkingDays,
            WorkingHours = s.WorkingHours,
            MinMonths = s.MinMonths,
            DaysPerWeek = s.DaysPerWeek,
            GraduationYear = s.GraduationYear,
            WorkNature = s.WorkNature,
            SalaryType = s.SalaryType,
            SalaryTypeName = s.SalaryType.GetDescription(),
            WorkNatureName = s.WorkNature.GetDescription(),
            Sex = s.Sex,
            MinAge = s.MinAge,
            MaxAge = s.MaxAge,
            RegistrationNum = (int?)s.Post_Extend.DeliveryNum ?? 0,
            DeliveriesNum = (int?)s.Post_Extend.InductionNum ?? 0,
            ScreeningNum = (int?)s.Post_Extend.HrScreeningNum ?? 0,
            InterviewNum = (int?)s.Post_Extend.InterviewNum ?? 0,
            InductionNum = (int?)s.Post_Extend.InductionNum ?? 0,
            FullRecruit = s.LeftStock <= 0,
            DeliveryNumber = s.DeliveryNumber,
            LeftStock = s.LeftStock,
            RejectReason = s.RejectReason,
            InterviewStatus = s.InterviewStatus,
            InterviewDashboard = new PostRecentInterviews
            {
                RecentDeliveryRate = s.Post_Extend.RecentDeliveryRate,
                HistoryBillingRate = s.Post_Extend.HistoryBillingRate
            },
            AuditTime = s.AuditTime,
            AuditUser = s.Audit_User.NickName
        }).ToList();

        var postIds = result.Rows.Select(s => s.PostId).ToList();

        // 查询职位面试配置信息
        var postInterviews = _context.Post_Interview_Config.Where(x => postIds.Contains(x.PostId)).ToList();

        //最近7天
        var recent7 = DateTime.Now.Date.AddDays(-6);

        //本周面试人数
        var weekInterview = _context.Recruit_Record.Where(x => x.CreatedTime >= recent7 && postIds.Contains(x.Recruit.Post_Delivery.PostId) && Constants.InterviewStatus.Contains(x.Status))
        .GroupBy(g => g.RecruitId)
        .Select(s => new { s.Key, PostId = s.Max(m => m.Recruit.Post_Delivery.PostId) })
        .GroupBy(g => g.PostId)
        .Select(s => new { s.Key, Ct = s.Count() }).ToList();

        var bgt = DateTime.Now.Date.AddDays(-14);
        var interviewTimes = _context.Post_Interview_Date.Where(x => postIds.Contains(x.PostId) && x.Time >= bgt).ToList();

        var welfare = _commonDicService.GetWelfare();
        foreach (var item in result.Rows)
        {
            item.PaymentNodeName = item.PaymentNode?.GetDescription();
            item.PaymentTypeName = item.PaymentType?.GetDescription();

            item.PostNo = $"{Constants.PostIdPre}{item.PostAutoId.ToString()!.PadLeft(2, '0')}";

            item.SalaryName = Tools.FormattingSalary(item.MinSalary, item.MaxSalary, item.SalaryType, item.WorkNature, item.Salary);

            if (item.Welfare?.Count > 0)
                item.Welfare = welfare.Where(x => item.Welfare.Any(a => a.Id == x.Id)).ToList();

            if (!string.IsNullOrWhiteSpace(item.RegionId))
                item.Region = _commonDicService.GetCityById(item.RegionId);

            item.StatusName = item.Status.GetDescription();

            item.Channel = new List<MyProjectShareType>();
            item.Channel.Add(MyProjectShareType.诺快聘);

            var fanfei = _context.ThirdParty_Postid_Relation.Where(x => x.PostId == item.PostId && x.Source == PostSouce.职多多).FirstOrDefault();
            if (fanfei != null)
            {
                item.FanfeiStatus = fanfei.FanfeiStatus;//0不变 1改变
            }
            //面试信息
            var interview = postInterviews.FirstOrDefault(x => x.PostId == item.PostId) ?? new Post_Interview_Config();
            if (interview != null)
            {
                //InterviewTime 只保留昨天至未来6天的面试时间
                var beginTime = DateTime.Now.Date.AddDays(-1);
                // var endTime = beginTime.AddDays(6);

                var interviewTime = interviewTimes.Where(x => x.PostId == item.PostId).ToList();
                foreach (var tm in Enumerable.Range(0, 7).Select(i => beginTime.AddDays(i)))
                {
                    PostInterviewTimeStatus? st = null;
                    if (interviewTime.Any(x => x.Time.Date == tm && x.Status == PostInterviewTimeStatus.可预约 && tm >= DateTime.Today.AddDays(interview.AdvanceDays)))
                        st = PostInterviewTimeStatus.可预约;
                    else
                        //if (interviewTime.Any(x => (x.Time.Date == tm && x.Status == PostInterviewTimeStatus.取消预约) || tm < DateTime.Now.AddDays(interview.AdvanceDays)))
                        st = PostInterviewTimeStatus.取消预约;

                    var its = new PostInterviewTimeModel
                    {
                        Time = tm,
                        Status = st,
                        SubTime = interviewTime.Where(x => x.Time.Date == tm).Select(s => TimeOnly.FromDateTime(s.Time)).OrderBy(o => o).ToList()
                    };
                    item.InterviewDashboard.Recent.Add(its);
                }

                item.InterviewDashboard.RecentInterviewNum = weekInterview.Where(x => x.Key == item.PostId)?.Sum(c => (int?)c.Ct) ?? 0;

                //产品：该数是未来可预约面试的场次，与时间无关
                item.InterviewDashboard.InterviewNum = interviewTime?.Where(x => x.Status == PostInterviewTimeStatus.可预约 && x.Time > DateTime.Now).Count() ?? 0;
            }
        }

        return result;
    }

    public EmptyResponse SetPostStatus(SetPostStatus model)
    {
        _commonProjectService.SetPostStatus(model.PostId, model.Status, _user.Id);

        return new EmptyResponse();
    }

    public EmptyResponse DeletePost(string id)
    {
        var lockerKey = $"st:uppost:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var post = _context.Post.Include(i => i.Project)
        .FirstOrDefault(x => x.PostId == id && (x.Project.HrId == _user.Id || x.Project.SaleUserId == _user.Id));

        if (post == null)
            throw new NotFoundException("内容不存在");

        var postInfo = _context.Post.Where(x => x.PostId == id)
        .Select(s => new
        {
            s.Project.HrId,
            s.Project.SaleUserId
        }).FirstOrDefault();

        // 如果当前人是销售并且不是项目经理，需要判断是否审核通过
        if (_user.Id != postInfo.HrId && _user.Id == postInfo.SaleUserId)
        {
            if (post.Status != PostStatus.待审核)
                throw new BadRequestException("只有待审核的职位才能删除");
        }

        if (_context.Project_Team.Any(x => x.ProjectId == post.ProjectId && x.HrId != _user.Id))
            throw new BadRequestException("该项目已有人同步，不允许删除");

        if (_context.Post_Delivery.Any(x => x.PostId == post.PostId))
            throw new BadRequestException("该职位已有人投递，不允许删除");

        post.Deleted = true;

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public GetPostDeliverysResponse GetPostDeliverys(GetPostDeliverys model)
    {
        var result = new GetPostDeliverysResponse();

        var teamPost = _context.Post_Team.Where(x => x.TeamPostId == model.TeamPostId && x.Project_Team.HrId == _user.Id)
        .Select(s => new
        {
            s.PostId,
            s.Project_Team.Type
        }).FirstOrDefault();

        if (teamPost == null)
            throw new NotFoundException("内容不存在");

        var predicate = PredicateBuilder.New<Recruit>(true);

        if (teamPost.Type == HrProjectType.自己)
            predicate = predicate.And(x => x.Post_Delivery.PostId == teamPost.PostId && x.HrId == _user.Id);
        else
            predicate = predicate.And(x => x.Post_Delivery.TeamPostId == model.TeamPostId);

        var sql = _context.Recruit.Where(predicate);

        result.Rows = sql
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new PostDeliveryDetail
        {
            Avatar = s.User_Seeker.Avatar,
            Birthday = s.User_Seeker.User_Resume.Birthday,
            Education = s.User_Seeker.User_Resume.Education,
            EventTime = s.CreatedTime,
            Name = s.User_Seeker.NickName,
            Occupation = s.User_Seeker.User_Resume.Occupation,
            RecruitId = s.RecruitId,
            RegionId = s.User_Seeker.RegionId,
            UserId = s.SeekerId,
            Sex = s.User_Seeker.User_Resume.Sex
        }).ToList();

        if (result.Rows.Count < model.PageSize)
            result.IsLast = true;
        else
            result.IsLast = false;

        foreach (var item in result.Rows)
        {
            item.Age = Tools.GetAgeByBirthdate(item.Birthday);
            item.EducationName = item.Education?.GetDescription();
            item.OccupationName = item.Occupation?.GetDescription();

            if (!string.IsNullOrWhiteSpace(item.RegionId))
                item.Region = _commonDicService.GetCityById(item.RegionId);
        }

        return result;
    }

    /// <summary>
    /// 获取项目自流转信息详情
    /// </summary>
    /// <param name="projectId"></param>
    /// <returns></returns>
    public GetProjectAutomaticResponse GetProjectAutomatic(string projectId)
    {
        var existence = _context.Project.Any(o => o.ProjectId == projectId && o.HrId == _user.Id);

        if (!existence)
        {
            throw new BadRequestException("数据不存在！");
        }

        GetProjectAutomaticResponse? retModel;

        retModel = _context.Project_Automatic.Where(o => o.ProjectId == projectId).Select(o => new GetProjectAutomaticResponse
        {
            OfferOrInduction = o.OfferOrInduction,
            InterviewerOutCome = o.InterviewerOutCome,
            OfferStatus = o.OfferStatus,
            ProjectId = o.ProjectId,
            QuitFileRemarks = o.QuitFileRemarks,
            QuitFileStatus = o.QuitFileStatus,
            QuitJob = o.QuitJob,
            RecruitmentCycle = o.RecruitmentCycle,
            RecruitmentNumber = o.RecruitmentNumber,
            ScreenFileRemarks = o.ScreenFileRemarks,
            ScreenFileStatus = o.ScreenFileStatus,
            HrId = o.Project.HrId
        }).FirstOrDefault();

        if (retModel is not null)
        {
            retModel.QuitFileStatusName = retModel.QuitFileStatus?.GetDescription();
            retModel.ScreenFileStatusName = retModel.ScreenFileStatus?.GetDescription();
        }
        else
        {
            retModel = new GetProjectAutomaticResponse()
            {
                ProjectId = projectId,
                InterviewerOutCome = false,
                OfferOrInduction = false,
                OfferStatus = false,
                QuitFileRemarks = string.Empty,
                QuitFileStatus = RecruitFileAway.Quit,
                QuitFileStatusName = RecruitFileAway.Quit.GetDescription(),
                QuitJob = false,
                RecruitmentCycle = false,
                RecruitmentNumber = false,
                ScreenFileRemarks = string.Empty,
                ScreenFileStatus = RecruitFileAway.Eliminate,
                ScreenFileStatusName = RecruitFileAway.Eliminate.GetDescription()
            };
        }

        return retModel;
    }

    /// <summary>
    /// 修改项目自流转信息
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse EditProjectAutomatic(EditProjectAutomaticRequest model)
    {
        var existence = _context.Project.Any(o => o.ProjectId == model.ProjectId && o.HrId == _user.Id);

        if (!existence)
        {
            throw new BadRequestException("数据不存在！");
        }

        var projectAutomaticModel = _context.Project_Automatic.Where(o => o.ProjectId == model.ProjectId).FirstOrDefault();
        if (projectAutomaticModel is not null)
        {
            projectAutomaticModel.RecruitmentCycle = model.RecruitmentCycle ?? false;
            projectAutomaticModel.RecruitmentNumber = model.RecruitmentNumber ?? false;
            projectAutomaticModel.InterviewerOutCome = model.InterviewerOutCome ?? false;
            projectAutomaticModel.ScreenFileStatus = model.ScreenFileStatus ?? RecruitFileAway.Eliminate;
            projectAutomaticModel.ScreenFileRemarks = model.ScreenFileRemarks ?? string.Empty;
            projectAutomaticModel.OfferStatus = model.OfferStatus ?? false;
            projectAutomaticModel.OfferOrInduction = model.OfferOrInduction ?? false;
            projectAutomaticModel.QuitJob = model.QuitJob ?? false;
            projectAutomaticModel.QuitFileStatus = model.QuitFileStatus ?? RecruitFileAway.Quit;
            projectAutomaticModel.QuitFileRemarks = model.QuitFileRemarks ?? string.Empty;
            projectAutomaticModel.UpdatedTime = DateTime.Now;
        }
        else
        {
            projectAutomaticModel = new Project_Automatic()
            {
                ProjectId = model.ProjectId,
                InterviewerOutCome = model.InterviewerOutCome ?? false,
                OfferOrInduction = model.OfferOrInduction ?? false,
                OfferStatus = model.OfferStatus ?? false,
                QuitFileRemarks = model.QuitFileRemarks ?? string.Empty,
                QuitFileStatus = model.QuitFileStatus ?? RecruitFileAway.Quit,
                QuitJob = model.QuitJob ?? false,
                RecruitmentCycle = model.RecruitmentCycle ?? false,
                RecruitmentNumber = model.RecruitmentNumber ?? false,
                ScreenFileRemarks = model.ScreenFileRemarks ?? string.Empty,
                ScreenFileStatus = model.ScreenFileStatus ?? RecruitFileAway.Eliminate,
                UpdatedTime = DateTime.Now,
                CreatedTime = DateTime.Now
            };
            _context.Project_Automatic.Add(projectAutomaticModel);
        }

        _context.SaveChanges();

        return new EmptyResponse() { };
    }

    public GetPostBusinessSmsTextResponse GetPostBusinessSmsText(GetPostBusinessSmsText model)
    {
        if (!(model.TeamPostNo?.StartsWith(Constants.TeamPostIdPre) == true && int.TryParse(model.TeamPostNo.Substring(1), out var teamPostId)))
            throw new BadRequestException("职位编号格式错误");

        var teamPost = _context.Post_Team.Where(x => x.AutoId == teamPostId && x.Show)
        .Select(s => new
        {
            s.TeamPostId,
            s.PostId,
            EntName = s.Project_Team.User_Hr.Enterprise.Name,
            PostName = s.Post.Name,
            HrName = s.Project_Team.User_Hr.NickName,
            HrId = s.Project_Team.HrId
        }).FirstOrDefault();

        if (teamPost == null)
            throw new BadRequestException("职位不存在");

        var result = new GetPostBusinessSmsTextResponse();

        result.Text = $"***您好！业务顾问：{teamPost.HrName}({teamPost.EntName})对您非常感兴趣，想要一份您的电子简历，希望就({teamPost.PostName})职位和您进一步沟通。跳转小程序 https://wxaurl.cn/**** 了解职位详情。";

        return result;
    }

    public EmptyResponse TopPost(TopPost model)
    {
        var lockerKey = $"st:uppost:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var teamPost = _context.Post_Team
        .Where(x => x.PostId == model.PostId && x.Project_Team.HrId == _user.Id)
        .FirstOrDefault();

        if (teamPost == null)
            throw new BadRequestException("请先上架职位");

        if (teamPost.Status != PostStatus.发布中 && model.Top)
            throw new BadRequestException("请先上架职位");

        if (model.Top)
            teamPost.TopTime = DateTime.Now;
        else
            teamPost.TopTime = Config.Constants.DefaultTime;

        teamPost.UpdatedTime = DateTime.Now;

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public MyProjectPostsResponse GetShowPosts(GetShowPosts model)
    {
        var result = new MyProjectPostsResponse();

        var idPre = Constants.PostIdPre;

        var predicate = PredicateBuilder.New<Post_Team>(x => x.Project_Team.HrId == _user.Id && x.Show);

        if (model.Sex.HasValue)
            predicate = predicate.And(x => x.Post.Sex == model.Sex);

        if (model.SettlementType?.Count > 0)
            predicate = predicate.And(x => model.SettlementType.Contains(x.Post.SettlementType));

        if (model.WorkCycleType?.Count > 0)
            predicate = predicate.And(x => model.WorkCycleType.Contains(x.Post.Project.WorkCycleType));

        if (!string.IsNullOrWhiteSpace(model.Search))
        {
            if (model.Search.StartsWith(idPre) && int.TryParse(model.Search.Substring(1), out var id))
                predicate = predicate.And(x => x.AutoId == id || x.Post.Name.Contains(model.Search));
            else
                predicate = predicate.And(x => x.Post.Name.Contains(model.Search));
        }

        if (model.WorkNature.HasValue)
            predicate = predicate.And(x => x.Post.WorkNature == model.WorkNature);

        if (model.Citys?.Count > 0)
        {
            var subPredicate = PredicateBuilder.New<Post_Team>(false);
            foreach (var item in model.Citys)
                subPredicate = subPredicate.Or(x => x.Post.RegionId.StartsWith(item));

            predicate = predicate.And(subPredicate);
        }

        var fullPost = _context.Post_Team.Where(predicate);

        result.Total = fullPost.Count();

        // //是否诺聘
        // var isNuoPin = _user.Id == Constants.PlatformHrId;

        result.Rows = fullPost
        .OrderByDescending(x => x.TopTime)
        .ThenByDescending(x => x.Post.Score)
        .ThenByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new MyProjectPostInfo
        {
            Hr = new HrModel
            {
                HrId = s.Post.Project.HrId,
                HrName = s.Post.Project.User_Hr.NickName,
                Avatar = s.Post.Project.User_Hr.Avatar,
                Post = s.Post.Project.User_Hr.Post
            },
            AgentEnt = new GetAgentEntDetail
            {
                DisplayName = s.Post.Agent_Ent.DisplayName,
                AgentEntId = s.Post.Project.AgentEntId,
                Scale = s.Post.Agent_Ent.Scale,
                Abbr = s.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post.Agent_Ent.Abbr : s.Post.Agent_Ent.DisplayName,
                LogoUrl = s.Post.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Post.Agent_Ent.LogoUrl : string.Empty,
                ScaleName = s.Post.Agent_Ent.Scale.GetDescription(),
                Industry = s.Post.Agent_Ent.Industry.ToString(),
                IndustryName = s.Post.Agent_Ent.Dic_Industry.Name
            },
            PostAutoId = s.Post.AutoId,
            ProjectId = s.Post.ProjectId,
            Name = s.Post.Name,
            Status = s.Post.Status,
            CreatedTime = s.Post.CreatedTime,
            UpdatedTime = s.Post.UpdatedTime,
            PaymentType = s.Post.Project.PaymentType,
            Address = s.Post.Address,
            Category = s.Post.Category,
            CategoryName = s.Post.Dic_Post.Name,
            Department = s.Post.Department,
            Describe = s.Post.Describe,
            Education = s.Post.Education,
            EducationName = s.Post.Education.GetDescription(),
            Highlights = s.Post.Highlights,
            Tags = s.Post.Tags,
            Welfare = s.Post.Welfare,
            WelfareCustom = s.Post.WelfareCustom,
            Lat = s.Post.Location.X,
            Lng = s.Post.Location.Y,
            MaxSalary = s.Post.MaxSalary,
            MinSalary = s.Post.MinSalary,
            Money = s.Post.Money,
            PostId = s.Post.PostId,
            RecruitNumber = s.Post.DeliveryNumber,
            RegionId = s.Post.RegionId,
            Salary = s.Post.Salary,
            SettlementType = s.Post.SettlementType,
            WorkingDays = s.Post.WorkingDays,
            WorkingHours = s.Post.WorkingHours,
            MinMonths = s.Post.MinMonths,
            DaysPerWeek = s.Post.DaysPerWeek,
            GraduationYear = s.Post.GraduationYear,
            WorkNature = s.Post.WorkNature,
            SalaryType = s.Post.SalaryType,
            SalaryTypeName = s.Post.SalaryType.GetDescription(),
            WorkNatureName = s.Post.WorkNature.GetDescription(),
            Sex = s.Post.Sex,
            MinAge = s.Post.MinAge,
            MaxAge = s.Post.MaxAge,
            RegistrationNum = (int?)s.Post.Post_Extend.DeliveryNum ?? 0,
            DeliveriesNum = (int?)s.Post.Post_Extend.InductionNum ?? 0,
            ScreeningNum = (int?)s.Post.Post_Extend.HrScreeningNum ?? 0,
            InterviewNum = (int?)s.Post.Post_Extend.InterviewNum ?? 0,
            InductionNum = (int?)s.Post.Post_Extend.InductionNum ?? 0,
            FullRecruit = s.Post.DeliveryNumber <= s.Post.Post_Extend.InductionNum,
            TeamProjectId = s.TeamProjectId
        }).ToList();

        var welfare = _commonDicService.GetWelfare();
        foreach (var item in result.Rows)
        {
            item.PaymentNodeName = item.PaymentNode?.GetDescription();
            item.PaymentTypeName = item.PaymentType?.GetDescription();

            item.PostNo = $"{Constants.PostIdPre}{item.PostAutoId.ToString()!.PadLeft(2, '0')}";

            item.SalaryName = Tools.FormattingSalary(item.MinSalary, item.MaxSalary, item.SalaryType, item.WorkNature, item.Salary);

            if (item.Welfare?.Count > 0)
                item.Welfare = welfare.Where(x => item.Welfare.Any(a => a.Id == x.Id)).ToList();

            if (!string.IsNullOrWhiteSpace(item.RegionId))
                item.Region = _commonDicService.GetCityById(item.RegionId);


            item.StatusName = item.Status.GetDescription();

            item.Channel = new List<MyProjectShareType>();
        }

        return result;
    }

    public GetAdviserCityResponse GetAdviserCity(GetAdviserCity model)
    {
        var result = new GetAdviserCityResponse();
        var cacheKey = $"{RedisKey.HrBehavior.Key}{_user.Id}";
        var regionIds = MyRedis.Client.HGet<List<string>>(cacheKey, RedisKey.HrBehavior.PostCity);
        regionIds = regionIds ?? new List<string>();

        if (string.IsNullOrWhiteSpace(model.ParentId))
            regionIds = regionIds.Where(x => x.Count() >= 4).Select(s => s.Substring(0, 4)).Distinct().ToList();
        else
            regionIds = regionIds.Where(x => x.Count() > 4 && x.StartsWith(model.ParentId))
            .Distinct().ToList();

        var region = _commonDicService.GetRegion();
        result.Rows = region.Where(x => regionIds.Contains(x.Id))
        .OrderBy(o => o.Name)
        .Select(s => new GeneralDic
        {
            Id = s.Id,
            Name = s.Name
        }).ToList();

        return result;
    }

    public EmptyResponse ExcellentPost(ExcellentPost model)
    {
        if (!model.Status.HasValue)
            throw new BadRequestException("缺少Status");

        var teamPost = _context.Post_Team
        .Where(x => x.TeamPostId == model.TeamPostId)
        .Select(s => new
        {
            TeamProjectType = s.Project_Team.Type,
            s.Show
        }).FirstOrDefault();

        if (teamPost == null)
            throw new NotFoundException("内容不存在");

        if (teamPost.TeamProjectType != HrProjectType.自己)
            throw new NotFoundException("请选择自己发布的职位设置优选");

        if (!teamPost.Show)
            throw new BadRequestException("职位尚未上架");

        var exc = _context.Post_Excellent.FirstOrDefault(x => x.TeamPostId == model.TeamPostId);

        if (model.Status == ExcellentPostStatus.UnReviewed)
        {
            if (!model.InterviewIn24Hour && !model.EntryIn72hour)
                throw new BadRequestException("24小时面试、72小时入职，至少包含其中一个");

            if (exc == null)
            {
                exc = new Post_Excellent
                {
                    TeamPostId = model.TeamPostId!
                };
                _context.Add(exc);
            }
            exc.Status = ExcellentPostStatus.UnReviewed;
            exc.InterviewIn24Hour = model.InterviewIn24Hour;
            exc.EntryIn72hour = model.EntryIn72hour;
        }
        else if (model.Status == ExcellentPostStatus.Canceled)
        {
            if (exc != null)
                _context.Remove(exc);
        }

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        return new EmptyResponse();
    }

    public GetPostInterviewResponse GetPostInterview(string? postId, string? recruitId)
    {
        if (string.IsNullOrWhiteSpace(postId))
            postId = _context.Recruit.Where(x => x.RecruitId == recruitId).Select(s => s.Post_Delivery.PostId).FirstOrDefault();

        var result = _context.Post_Interview_Config.Where(x => x.PostId == postId)
        .Select(s => new GetPostInterviewResponse
        {
            Address = s.Address,
            AddressDetail = s.AddressDetail,
            AdvanceDays = s.AdvanceDays,
            ContactName = s.Project_Interviewer.Name,
            ContactPhone = s.Project_Interviewer.Phone,
            Id = s.Id,
            InterviewerId = s.InterviewerId,
            InterviewMode = s.InterviewMode,
            Lat = s.Location.X,
            Lng = s.Location.Y,
            RegionId = s.RegionId,
            //Status = s.Status,
            InterviewTime = _context.Post_Interview_Date.Where(x => x.PostId == postId && x.Time >= DateTime.Now).Select(s => new PostInterviewTimeModel
            {
                Time = s.Time,
                Status = s.Status
            }).ToList(),
        }).FirstOrDefault();

        if (result == null)
            return new GetPostInterviewResponse();

        //根据需要提前几天预约，计算哪儿天之后的时间可以

        var start = DateTime.Now.Date.AddDays(result.AdvanceDays ?? 0);
        result.TodayInterviewTime = result.InterviewTime.Where(x => x.Time >= start && x.Status == PostInterviewTimeStatus.可预约)
        .OrderBy(o => o.Time).ToList();

        return result;
    }
    /// <summary>
    /// 更新代聊状态
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task UpdatePostHrAgentChatStatus(TeamPostHrAgentChat model)
    {
        if (model == null)
            return;
        var chatstatus = _context.Post.
            Where(x => x.PostId == model.PostId && x.Project.ProjectId == model.ProjectId).
            FirstOrDefault();
        if (chatstatus != null)
        {
            chatstatus.HrAgentStatus = model.HrAgentStatus;
            chatstatus.UpdatedTime = DateTime.Now;

            var teamPost = _context.Post_Team.
                Where(x => x.PostId == model.PostId && x.TeamPostId == model.TeamPostId).
                FirstOrDefault();
            if (teamPost != null)
            {
                teamPost.HrAgentStatus = model.HrAgentStatus ?? 0;
                teamPost.UpdatedTime = DateTime.Now;
            }
            _context.SaveChanges();
            await Task.FromResult(1);
        }
        else
        {
            return;
        }
    }
    /// <summary>
    /// 项目众包
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    /// <exception cref="NotFoundException"></exception>
    public EmptyResponse EditProjectCrowdsource(ProjectCrowdsourceReq model)
    {
        using (var transaction = _context.Database.BeginTransaction())
        {
            try
            {
                var config = _context.Project_Team_Config
                    .FirstOrDefault(c => c.Creator == _user.Id);
                if (config == null)
                {
                    var newConfig = new Project_Team_Config()
                    {
                        ModeType = model.ModeType,
                        ReceiverId = model.ReceiverId,
                        Creator = _user.Id,
                        Status = model.Status,
                    };
                    _context.Project_Team_Config.Add(newConfig);
                }
                else
                {
                    config.ModeType = model.ModeType;
                    config.ReceiverId = model.ReceiverId;
                    config.Status = model.Status;
                    config.UpdatedTime = DateTime.Now;
                }

                if (model.Status == 0)
                {
                    var result = _context.Recruit
                        .Where(r => r.Post_Delivery.Post_Team.Project_Team.HrId == _user.Id
                                    && r.Status == RecruitStatus.HrScreening && r.AcceptOrder == RecruitAcceptOrderEnum.未接单)
                        .ExecuteUpdate(a => a
                            .SetProperty(c => c.IsClues, ProjectTeamIsClues.是)
                            .SetProperty(c => c.ModeType, model.ModeType)
                            .SetProperty(c => c.ReceiverId, model.ReceiverId)
                        );
                }
                else if (model.Status == 1)
                {
                    var result = _context.Recruit
                        .Where(r => r.Post_Delivery.Post_Team.Project_Team.HrId == _user.Id
                                    && r.Status == RecruitStatus.HrScreening
                                    && r.IsClues == ProjectTeamIsClues.是 && r.AcceptOrder == RecruitAcceptOrderEnum.未接单)
                        .ExecuteUpdate(a => a
                            .SetProperty(c => c.IsClues, ProjectTeamIsClues.否)
                            .SetProperty(c => c.ModeType, (ProjectTeamConfigType?)null)
                            .SetProperty(c => c.ReceiverId, null as string)
                        );
                }
                _context.SaveChanges();
                transaction.Commit();
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                _log.Error("项目众包错误", "EditProjectCrowdsource", ex.Message);
                throw;
            }
        }
        return new EmptyResponse();
    }
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public GetAllHrResp GetAllHr()
    {
        var result = new GetAllHrResp();
        var hrInfos = _context.User_Hr.Where(x => x.Status == UserStatus.Active).Select(s => new HrInfo()
        {
            UserId = s.UserId,
            Name = s.NickName

        }).ToList();
        result.Rows = hrInfos;
        return result;
    }

    public ProjectCrowdsourceResp GetProjectCrowdsource()
    {

        var result = _context.Project_Team_Config
            .Where(r => r.Creator == _user.Id)
            .Select(s => new ProjectCrowdsourceResp
            {
                ModeType = s.ModeType,
                ReceiverId = s.ReceiverId,
                Status = s.Status,
            })
            .FirstOrDefault() ?? new ProjectCrowdsourceResp
            {
                Status = 1
            };
        return result;
    }
    /// <summary>
    /// 获取所有帐套列表
    /// </summary>
    /// <returns></returns>
    public List<NoahBookResp> GetNoahBooks()
    {
        return _context.Ndn_Books.Where(s => !string.IsNullOrEmpty(s.BookCode)).Select(s => new NoahBookResp
        {
            Id = s.Id,
            BookName = s.BookName,
            BookCode = s.BookCode,
        }).ToList();

    }
    /// <summary>
    /// 销售获取商机&项目列表
    /// </summary>
    /// <returns></returns>
    public GetMyProjectResp GetMyProjects(GetMyProjectReq req)
    {
        var result = new GetMyProjectResp();
        var idPre = Constants.ProjectIdPre;
        var predicate = PredicateBuilder.New<Project>();

        if (req.Type == GetMyProjectReqTypeEnum.商机管理)
        {
            predicate = predicate.And(s => s.SaleUserId == _user.Id);
        }
        else if (req.Type == GetMyProjectReqTypeEnum.项目大厅)
        {
            predicate = predicate.And(s => s.AcceptOrder == RecruitAcceptOrderEnum.未接单);
            predicate = predicate.And(s => s.HrId == _user.Id || s.HrId == null || s.HrId == string.Empty);
            if (!string.IsNullOrWhiteSpace(req.Search))
            {
                if (req.Search.StartsWith(idPre) && int.TryParse(req.Search.Substring(1), out var id))
                    predicate = predicate.And(x => x.AutoId == id);
                else
                    predicate = predicate.And(x => x.Agent_Ent.Name.Contains(req.Search));
            }
        }
        else if (req.Type == GetMyProjectReqTypeEnum.我管理的项目)
        {
            predicate = predicate.And(s => s.HrId == _user.Id);
            predicate = predicate.And(s => s.Status != ProjectStatus.待审核);

            if (!string.IsNullOrWhiteSpace(req.Search))
            {
                if (req.Search.StartsWith(idPre) && int.TryParse(req.Search.Substring(1), out var id))
                    predicate = predicate.And(x => x.AutoId == id);
                else
                    predicate = predicate.And(x => x.Agent_Ent.Name.Contains(req.Search));
            }
        }
        var sql = _context.Project
            .Where(predicate);
        result.Total = sql.Select(s => s.ProjectId).Count();

        result.Rows = sql
            //.OrderByDescending(p => p.CreatedTime)
            .OrderByDescending(p => p.Status == ProjectStatus.已上线)  // 已上线(status=1)的排在最前面
            .ThenByDescending(p => p.CreatedTime)   // 最后按创建时间降序
            .Skip((req.PageIndex - 1) * req.PageSize)
            .Take(req.PageSize)
            .Select(s => new MyProjectInfo
            {
                ProjectId = s.ProjectId,
                ProjectCode = $"{Constants.ProjectIdPre}{s.AutoId.ToString().PadLeft(6, '0')}",
                EntName = s.Agent_Ent.Name,
                Type = s.Type,
                AcceptOrder = s.AcceptOrder,
                Status = s.Status,
                Price = s.Price,
                PositionCount = s.Project_Extend.PositionCount,
                RecruitmentCount = s.Project_Extend.RecruitmentCount,
                PaymentNode = s.PaymentNode,
                SalesPerson = s.SaleUser.NickName,
                PmName = s.User_Hr.NickName,
                LogoUrl = s.Agent_Ent.LogoUrl ?? "https://resources.nuopin.cn/DefaultFile/RecruitOthersEnterpriseLogo.png?x-oss-process=style/300x300",
                TypeName = s.Type.GetDescription(),
                StatusName = s.Status.GetDescription(),
                PaymentNodeString = s.PaymentNode.GetDescription(),
                Industry = s.Industry,
                IndustryName = s.Industry.GetDescription() == "" ? s.Agent_Ent.Dic_Industry.Name : s.Industry.GetDescription()

            })
            .ToList();
        return result;
    }
    /// <summary>
    /// 项目接单
    /// </summary>
    /// <param name="projectId"></param>
    /// <returns></returns>
    public EmptyResponse ProjectAcceptOrder(string projectId)
    {
        var project = _context.Project.FirstOrDefault(p => p.ProjectId == projectId);
        if (project == null)
        {
            throw new BadRequestException("项目不存在");
        }

        if (project.AcceptOrder == RecruitAcceptOrderEnum.已经接单)
        {
            throw new BadRequestException("项目已接单");
        }
        using (var transaction = _context.Database.BeginTransaction())
        {
            try
            {
                project.AcceptOrder = RecruitAcceptOrderEnum.已经接单;
                project.HrId = _user.Id;
                project.Status = ProjectStatus.已上线;

                var teamProj = new Project_Team()
                {
                    Project = project,
                    HrId = _user.Id,
                    Source = HrProjectSource.自己项目,
                    Status = ProjectStatus.已上线,
                    Type = HrProjectType.自己,
                    UpdatedBy = _user.Id,
                };
                _context.Add(teamProj);
                //添加自己为面试官
                var hrInfo = _context.User_Hr.Where(w => w.UserId == _user.Id)
                    .Select(s => new
                    {
                        s.User.Mobile,
                        s.NickName,
                        s.EMail,
                        s.Post
                    }).First();

                var addModel = new Project_Interviewer()
                {
                    ProjectId = project.ProjectId,
                    Mail = hrInfo.EMail ?? string.Empty,
                    Name = hrInfo.NickName,
                    OfficeAddress = string.Empty,
                    Phone = hrInfo.Mobile,
                    Post = hrInfo.Post ?? string.Empty
                };
                _context.Add(addModel);
                _context.SaveChanges();
                transaction.Commit();
            }
            catch (Exception e)
            {
                transaction.Rollback();
                _log.Error("项目接单操作失败", e.Message);
                throw;
            }
        }

        return new EmptyResponse();
    }
    /// <summary>
    ///  获取诺快聘财务人员
    /// </summary>
    /// <returns></returns>
    public GetAllHrResp GetAllfinance()
    {
        var result = new GetAllHrResp();
        var hrInfos = _context.User_Hr
            .Where(x => x.Status == UserStatus.Active)
            .AsEnumerable<User_Hr>()
            .Where(x => x.Powers.Contains(Power.NkpFinance))
            .Select(s => new HrInfo()
            {
                UserId = s.UserId,
                Name = s.NickName

            }).ToList();
        result.Rows = hrInfos;
        return result;
    }
    /// <summary>
    /// 获取项目资产概述
    /// </summary>
    /// <param name="projectId"></param>
    /// <returns></returns>
    public ProjectAssetOverviewResp GetProjectAssetOverview(string projectId)
    {
        var result = new ProjectAssetOverviewResp();

        // 获取子账户信息
        var subAccount = _context.SubAccount.FirstOrDefault(w => w.ExternalUserNo == projectId);
        if (subAccount == null)
        {
            throw new BadReadException("项目子账户不存在");
        }
        result.AvailableAmount = (subAccount?.Amount ?? 0) / 100m;
        // 开票费用字段 - 来源于SubAccount的InvoiceVatRateAmount，单位分，可能为负数，需要绝对值处理
        result.InvoiceCost = (subAccount?.InvoiceVatRateAmount ?? 0) / 100m;

        // PaidAmount字段 - Project_Transaction_Record中交易类型为支出的金额总和，单位分转元
        var paidAmountInCents = _context.Project_Transaction_Record
            .Where(r => r.ProjectId == projectId && r.TransactionType == ProjectTransactionType.支出)
            .Sum(r => (long?)r.Amount) ?? 0;
        result.PaidAmount = paidAmountInCents / 100m;

        // 项目收入 - Project_Transaction_Record中交易类型为收入的金额总和，单位分转元
        var projectIncomeInCents = _context.Project_Transaction_Record
            .Where(r => r.ProjectId == projectId && r.TransactionType == ProjectTransactionType.收入)
            .Sum(r => (long?)r.Amount) ?? 0;
        var projectIncome = projectIncomeInCents / 100m;

        // ProjectProfit字段 - 项目收入减去已打款再减去开票费用绝对值
        result.ProjectProfit = projectIncome - result.PaidAmount - Math.Abs(result.InvoiceCost);

        result.PendingConfirmationCount = _context.Post_Settlement
            .Count(r => r.Post_Bounty_Stage.Post_Bounty.Post.ProjectId == projectId && r.ApprovalStatus == PostSettleApprovalStatus.项目经理审核);

        return result;
    }

    /// <summary>
    /// 获取诺快聘项目经理
    /// </summary>
    /// <returns></returns>
    public GetAllHrResp GetAllProjectManager()
    {
        var result = new GetAllHrResp();
        var hrInfos = _context.User_Hr.Where(x => x.Status == UserStatus.Active)
            .AsEnumerable<User_Hr>()
            .Where(x => x.Powers.Contains(Power.NkpPm))
            .Select(s => new HrInfo()
            {
                UserId = s.UserId,
                Name = s.NickName

            }).ToList();
        result.Rows = hrInfos;
        return result;
    }
}