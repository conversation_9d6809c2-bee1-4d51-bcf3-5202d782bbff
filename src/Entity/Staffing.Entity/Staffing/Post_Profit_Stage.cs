﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 职位分润阶段表
/// </summary>
[Table("post_profit_stage")]
public class Post_Profit_Stage
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 职位Id
    /// </summary>
    public string PostId { get; set; } = default!;

    /// <summary>
    /// 过保天数
    /// </summary>
    public int GuaranteeDays { get; set; }

    /// <summary>
    /// 分润金额
    /// </summary>
    public decimal Amount { get; set; }

    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    
    public Post Post { get; set; } = default!;
}