﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace Staffing.Entity.Nyk;

/// <summary>
/// 用户报名档案
/// </summary>
[Table("UserApplyArchive")]
public class UserApplyArchive
{
    /// <summary>
    /// id
    /// </summary>
    [Key]
    public string PK_UAAID { get; set; } = default!;

    /// <summary>
    /// PK_UID
    /// </summary>
    public string PK_UID { get; set; } = default!;

    /// <summary>
    /// 档案标签
    /// </summary>
    public string TalentLableArray { get; set; } = default!;

    /// <summary>
    /// 档案内容
    /// </summary>
    public string ArchiveForm { get; set; } = default!;

    /// <summary>
    /// 时间戳
    /// </summary>
    [Timestamp]
    [JsonIgnore]
    public byte[]? TagTimeStamp { get; set; }

    // [NotMapped]
    public long TimeStampLong { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public int IsRemove { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
}
