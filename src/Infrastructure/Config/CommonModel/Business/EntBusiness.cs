using Config.Enums;

namespace Config.CommonModel.Business;

public class EntBusinessData
{
    /// <summary>
    /// 企业名称
    /// </summary>
    public string? Name { get; set; } = string.Empty;

    /// <summary>
    /// 企业工商编码
    /// </summary>
    public string Code { get; set; } = string.Empty;
}

/// <summary>
/// 发票
/// </summary>
public class NdnInvoiceInfo
{
    /// <summary>
    /// 发票类型
    /// </summary>
    public NovaPinInvoiceType? InvoiceType { get; set; }

    /// <summary>
    /// 发票抬头
    /// </summary>
    public string? InvoiceTitle { get; set; }

    /// <summary>
    /// 税号
    /// </summary>
    public string? TaxNumber { get; set; }

    /// <summary>
    /// 开户行
    /// </summary>
    public string? Bank { get; set; }

    /// <summary>
    /// 开户账号
    /// </summary>
    public string? BankAccount { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 开票电话
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 推送方式
    /// </summary>
    public NovaPinInvoicePushMode? PushMode { get; set; }

    /// <summary>
    /// 推送地址（选填)
    /// </summary>
    public string? PushAddress { get; set; }

    /// <summary>
    /// 开票人姓名
    /// </summary>
    public string? InvoiceOperatorName { get; set; }

    /// <summary>
    /// 开票人工号
    /// </summary>
    public string? InvoiceOperatorNo { get; set; }

    /// <summary>
    /// 合同人姓名
    /// </summary>
    public string? ContractOperatorName { get; set; }

    /// <summary>
    /// 合同人工号
    /// </summary>
    public string? ContractOperatorNo { get; set; }
}
