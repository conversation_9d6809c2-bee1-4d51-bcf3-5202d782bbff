﻿using Newtonsoft.Json;

namespace Config.CommonModel.SCRMBeschannels
{
    /// <summary>
    /// 会员/员工(注册/更新)传入参数
    /// </summary>
    public class BesChannelsMemmberInput
    {
        /// <summary>
        /// 手机号
        /// </summary>
        [JsonProperty("mobile")]
        public string? Mobile { get; set; }

        /// <summary>
        /// 身份 1=会员，2=员工
        /// </summary>
        [JsonProperty("identity")]
        public string? Identity { get; set; } = "1";

        /// <summary>
        /// 会员来源 1=注册，7=导入，11=API，4=未知
        /// </summary>
        [JsonProperty("svip_source")]
        public string? SvipSource { get; set; } = "1";

        /// <summary>
        /// 姓名
        /// </summary>
        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public string? Name { get; set; }

        /// <summary>
        /// 性别 1=男，2=女，0=未知
        /// </summary>
        [JsonProperty("sex", NullValueHandling = NullValueHandling.Ignore)]
        public string? Sex { get; set; }

        /// <summary>
        /// 地址 例: 测试地址
        /// </summary>
        [JsonProperty("address", NullValueHandling = NullValueHandling.Ignore)]
        public string? Address { get; set; }

        /// <summary>
        /// 出生日期 例：19900101
        /// </summary>
        [JsonProperty("birthday", NullValueHandling = NullValueHandling.Ignore)]
        public string? Birthday { get; set; }

        /// <summary>
        /// 城市id 例: 100063
        /// </summary>
        [JsonProperty("city", NullValueHandling = NullValueHandling.Ignore)]
        public string? City { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [JsonProperty("email", NullValueHandling = NullValueHandling.Ignore)]
        public string? Email { get; set; }

        /// <summary>
        /// 标识线索来源 1=注册
        /// </summary>
        [JsonProperty("lead_source", NullValueHandling = NullValueHandling.Ignore)]
        public string? LeadSource { get; set; }

        /// <summary>
        /// 微信openid（当此值需要传输时，需要对应传输对应公众号的appid，进行绑定）
        /// </summary>
        [JsonProperty("openid", NullValueHandling = NullValueHandling.Ignore)]
        public string? Openid { get; set; }

        /// <summary>
        /// 部门编号（ 如果身份为员工时，此字段必填）
        /// </summary>
        [JsonProperty("outlet", NullValueHandling = NullValueHandling.Ignore)]
        public string? Outlet { get; set; }

        /// <summary>
        /// 省份id 例: 10161
        /// </summary>
        [JsonProperty("province", NullValueHandling = NullValueHandling.Ignore)]
        public string? Province { get; set; }

        /// <summary>
        /// 地区id 例: 0
        /// </summary>
        [JsonProperty("region", NullValueHandling = NullValueHandling.Ignore)]
        public string? Region { get; set; }

        /// <summary>
        /// 微信unionid
        /// </summary>
        [JsonProperty("unionid", NullValueHandling = NullValueHandling.Ignore)]
        public string? Unionid { get; set; }

        #region 自定义字段
        /// <summary>
        /// 企业名称
        /// </summary>
        public string? field_35039 { get; set; }

        /// <summary>
        /// 顾问
        /// </summary>
        public string? field_36193 { get; set; }

        /// <summary>
        /// 顾问电话
        /// </summary>
        public string? field_36194 { get; set; }

        /// <summary>
        /// 顾问部门
        /// </summary>
        public string? field_36374 { get; set; }

        /// <summary>
        /// 来源
        /// </summary>
        public string? field_36375 { get; set; }
        /// <summary>
        /// 网点名称
        /// </summary>
        public string? field_36192 { get; set; }
        /// <summary>
        /// 网点名单
        /// </summary>
        public string? field_36376 { get; set; }
        /// <summary>
        /// 网点地市
        /// </summary>
        public string? field_36191 { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string? field_36377 { get; set; }
        /// <summary>
        /// 线索时间
        /// </summary>
        public string? field_36378 { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? field_36379 { get; set; }
        /// <summary>
        /// 所属行业
        /// </summary>
        public string? field_34480 { get; set; }
        /// <summary>
        /// 企业性质
        /// </summary>
        public string? field_36195 { get; set; }
        #endregion
    }

    /// <summary>
    /// 会议报名传入参数
    /// </summary>
    public class BesChannelsMeetingEntryInput
    {
        /// <summary>
        /// 手机号，必需
        /// </summary>
        [JsonProperty("mobile")]
        public string? Mobile { get; set; }

        /// <summary>
        /// 会议id，必需
        /// </summary>
        [JsonProperty("meeting_id")]
        public long MeetingId { get; set; }

        /// <summary>
        /// 报名会议的客户端 1微信端 2pc端，必需
        /// </summary>
        [JsonProperty("client")]
        public long Client { get; set; }

        /// <summary>
        /// 报名方式  1线上 2线下，必需
        /// </summary>
        [JsonProperty("meeting_form")]
        public long MeetingForm { get; set; }

        /// <summary>
        /// 报名渠道，可选
        /// </summary>
        [JsonProperty("channel_name", NullValueHandling = NullValueHandling.Ignore)]
        public string? ChannelName { get; set; }

        /// <summary>
        /// 报名时间  不传默认为当前时间戳，可选
        /// </summary>
        [JsonProperty("entry_time", NullValueHandling = NullValueHandling.Ignore)]
        public long? EntryTime { get; set; }

        /// <summary>
        /// 是否为新会员  1新 默认为0，可选
        /// </summary>
        [JsonProperty("is_new_member", NullValueHandling = NullValueHandling.Ignore)]
        public long? IsNewMember { get; set; }

        /// <summary>
        /// 注册渠道  is_new_member=1 生效，可选
        /// </summary>
        [JsonProperty("register_channel", NullValueHandling = NullValueHandling.Ignore)]
        public string? RegisterChannel { get; set; }

        /// <summary>
        /// 是否需要审核 0未审核 1审核通过 2审核不通过 默认为0 （仅开启审核功能生效），可选
        /// </summary>
        [JsonProperty("review_status", NullValueHandling = NullValueHandling.Ignore)]
        public long? ReviewStatus { get; set; }
    }
}
