﻿using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Core.Interfaces.Seeker;
using Config;
using Microsoft.EntityFrameworkCore;
using Infrastructure.CommonService;
using Staffing.Entity.Staffing;
using Infrastructure.Exceptions;
using Config.CommonModel;
using Config.CommonModel.Business;
using Staffing.Model.Seeker.Channel;
using Staffing.Entity.Elasticsearch;
using Nest;
using EntityFrameworkCore.AutoHistory.Extensions;
using Staffing.Model.Seeker.TalentVirtual;
using Infrastructure.Aliyun;
using Staffing.Model.Hr.TalentVirtual;

namespace Staffing.Core.Services.Seeker;

[Service(ServiceLifetime.Transient)]
public class ChannelService : IChannelService
{
    private readonly StaffingContext _context;
    private readonly RequestContext _user;
    private readonly ConfigManager _config;
    private readonly CacheHelper _cacheHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly WeChatHelper _weChatHelper;
    private readonly LogManager _log;
    private readonly CommonDicService _commonDicService;
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly EsHelper _esHelper;
    private readonly CommonVirtualService _commonVirtualService;
    private readonly CommonPostService _commonPostService;
    private readonly SmsHelper _smsHelper;
    public ChannelService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, LogManager log, EsHelper esHelper, WeChatHelper weChatHelper,
        IDbContextFactory<StaffingContext> contextFactory, CommonDicService commonDicService,
        CommonVirtualService commonVirtualService, SmsHelper smsHelper, CommonPostService commonPostService)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _weChatHelper = weChatHelper;
        _cacheHelper = cacheHelper;
        _log = log;
        _contextFactory = contextFactory;
        _commonDicService = commonDicService;
        _esHelper = esHelper;
        _commonVirtualService = commonVirtualService;
        _smsHelper = smsHelper;
        _commonPostService = commonPostService;
    }

    public CheckChannelResponse CheckChannel(CheckChannel model)
    {
        var result = _context.User_Seeker.Where(x => x.UserId == _user.Id)
        .Select(s => new CheckChannelResponse
        {
            Avatar = s.Avatar,
            Identity = !string.IsNullOrEmpty(s.User.IdentityCard),
            Name = s.NickName,
            Sex = s.User_Resume.Sex
        }).First();

        var qd = _context.Qd_Hr_Channel.Where(x => x.HrId == model.HrId && x.ChannelUserId == _user.Id)
        .Select(s => new
        {
            s.Type,
            s.Describe
        }).FirstOrDefault();

        result.Describe = qd?.Describe;
        result.ChannelType = qd?.Type;

        return result;
    }

    public EmptyResponse JoinChannel(JoinChannel model)
    {
        if (string.IsNullOrWhiteSpace(model.Name))
            throw new BadRequestException("缺少姓名");

        if (!_context.User_Hr.Any(x => x.UserId == model.HrId))
            throw new BadRequestException("顾问不存在");

        if (!model.Sex.HasValue)
            throw new BadRequestException("缺少性别");

        if (!model.ChannelType.HasValue)
            throw new BadRequestException("缺少类别");

        if (string.IsNullOrWhiteSpace(model.Avatar))
            throw new BadRequestException("缺少头像");

        if (model.ChannelType != ChannelType.网格员)
            throw new BadRequestException("暂不支持开通该渠道");

        if (_context.Qd_Hr_Channel.Any(x => x.HrId == model.HrId && x.ChannelUserId == _user.Id))
            throw new BadRequestException("操作失败：关系已存在");

        var seeker = _context.User_Seeker
        .Include(i => i.User).Include(i => i.User_Resume)
        .First(x => x.UserId == _user.Id);

        if (string.IsNullOrEmpty(seeker.User.IdentityCard))
            seeker.NickName = model.Name;

        if (!string.IsNullOrWhiteSpace(model.Avatar))
            seeker.Avatar = model.Avatar;

        seeker.User_Resume.Sex = model.Sex;

        var channel = new Qd_Hr_Channel
        {
            ChannelUserId = _user.Id,
            HrId = model.HrId!,
            Type = model.ChannelType.Value,
            EndTime = DateTime.Now.AddYears(1),
            Status = ActiveStatus.Active,
            Describe = model.Describe,
            IsDefaultHr = true
        };
        _context.Add(channel);

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public GetChannelResponse GetChannelInfo(GetChannel model)
    {
        var result = _context.Qd_Hr_Channel.Where(x => x.ChannelUserId == _user.Id)
        .OrderByDescending(o => o.IsDefaultHr)
        .ThenByDescending(o => o.CreatedTime)
        .Select(s => new GetChannelResponse
        {
            ChannelId = s.Id,
            Avatar = s.User_Seeker.Avatar,
            Balance = 0,
            GroupQrCode = s.GroupQrCode,
            HrId = s.HrId,
            HrName = s.User_Hr.NickName,
            Mobile = s.User_Seeker.User.Mobile,
            Name = s.User_Seeker.NickName
        }).FirstOrDefault();

        if (result == null)
            throw new BadRequestException("内容不存在");

        var yesterday = DateTime.Now.AddDays(-1);

        result.ActiveTalent = _cacheHelper.GetRedisCache<int?>(() =>
        {
            return _context.Talent_Platform.Where(x => x.ChannelId == result.ChannelId
            && x.User_Seeker.User_Extend.LoginTime >= yesterday)
            .Count();
        }, $"cache:clactusrct:{result.ChannelId}", 60) ?? 0;

        var client = _esHelper.GetClient();

        result.Talent = (int)client.Count<EsTalentPlatform>(s => s
        .Index(_esHelper.GetIndex().TalentPlatform)
        .Query(q => q.Bool(b => b.Must(m => m.Term(t => t.Field(f => f.ChannelId).Value(result.ChannelId))))))
        .Count;

        result.VirtualTalent = (int)client.Count<EsTalentVirtual>(s => s
        .Index(_esHelper.GetIndex().TalentVirtual)
        .Query(q => q.Bool(b => b.Must(m => m.Term(t => t.Field(f => f.ChannelId).Value(result.ChannelId))))))
        .Count;

        return result;
    }

    public async Task<GetChannelQrCodeResponse> GetChannelQrCode(GetChannelQrCode model)
    {
        var result = new GetChannelQrCodeResponse();

        var channel = _context.Qd_Hr_Channel.Where(x => x.Id == model.ChannelId && x.ChannelUserId == _user.Id)
        .FirstOrDefault();

        if (channel == null)
            throw new BadRequestException("内容不存在");

        var appIds = _commonDicService.GetAppIds();

        var appId = appIds.Where(x => x.Type == ClientType.SeekerApplet).FirstOrDefault()?.AppId;

        if (string.IsNullOrWhiteSpace(appId))
        {
            _log.Error("获取渠道二维码失败", model.ChannelId, _user);
            throw new BadRequestException("获取二维码失败，请稍后再试");
        }

        var qrcode = _context.Qd_Hr_Channel_Qrcode.FirstOrDefault(x => x.ChannelId == model.ChannelId && x.AppId == appId);

        if (qrcode != null)
        {
            result.ChannelQrCode = qrcode.ChannelQrCode;
            return result;
        }

        var lockerKey = $"getchannelqrcode_{model.ChannelId}";

        //redis分布式锁，自动释放
        using var locker = await MyRedis.Lock(lockerKey, 10);

        if (locker == null)
            throw new NotAcceptableException(string.Empty);

        qrcode = _context.Qd_Hr_Channel_Qrcode.FirstOrDefault(x => x.ChannelId == model.ChannelId && x.AppId == appId);

        if (qrcode == null)
        {
            var url = await RetryHelper.Do(async () => await _weChatHelper.GetWxaCodeUnlimit(appId, $"{(int)AppletShareType.渠道商二维码}_{channel.Id}", "pages/middlepage/middlepage"));

            if (string.IsNullOrWhiteSpace(url))
                throw new BadRequestException("获取二维码失败，请稍后再试");

            qrcode = new Qd_Hr_Channel_Qrcode
            {
                AppId = appId,
                ChannelId = model.ChannelId!,
                ChannelQrCode = url
            };
            _context.Add(qrcode);

            _context.SaveChanges();
        }

        result.ChannelQrCode = qrcode.ChannelQrCode;

        return result;
    }

    public GetHrsResponse GetHrs(GetHrs model)
    {
        var result = new GetHrsResponse();

        //如果登录
        var predicate = PredicateBuilder.New<Qd_Hr_Channel>(x => x.ChannelUserId == _user.Id);

        if (!string.IsNullOrWhiteSpace(model.Name))
            predicate = predicate.And(x => x.User_Hr.NickName.Contains(model.Name));

        var sql = _context.Qd_Hr_Channel.Where(predicate);
        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(o => o.IsDefaultHr)
        .ThenByDescending(o => o.CreatedTime)
        .ThenBy(x => x.Id)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetHrsDetail
        {
            Avatar = s.User_Hr.Avatar,
            Describe = s.User_Hr.Describe,
            EntName = s.User_Hr.Enterprise.Name,
            Name = s.User_Hr.NickName,
            Talent = s.User_Hr.User_Num.Talent,
            VirtualTalent = s.User_Hr.User_Num.VirtualTalent,
            AdviserId = s.User_Hr.UserId,
            LoginTime = s.User_Hr.User_Extend.LoginTime,
            IsCurrent = s.IsDefaultHr,
            DataScore = s.User_Hr.Score
        }).ToList();

        var currentHr = _context.Qd_Hr_Channel.Where(x => x.ChannelUserId == _user.Id)
        .OrderByDescending(o => o.IsDefaultHr)
        .ThenByDescending(o => o.CreatedTime)
        .Select(s => s.HrId)
        .FirstOrDefault();

        foreach (var item in result.Rows)
        {
            item.OnlineStatus = Tools.GetOnlineStatus(item.LoginTime);
            item.Grade = Tools.GetHrGrade(item.DataScore, item.LoginTime);
            item.IsCurrent = item.AdviserId == currentHr;
        }

        return result;
    }

    public EmptyResponse SetAdviser(SetAdviser model)
    {
        if (string.IsNullOrWhiteSpace(model.HrId))
            throw new BadRequestException("缺少顾问Id");

        var hrs = _context.Qd_Hr_Channel.Where(x => x.ChannelUserId == _user.Id &&
        (x.HrId == model.HrId || x.IsDefaultHr)).ToList();

        foreach (var item in hrs)
        {
            if (item.HrId == model.HrId)
                item.IsDefaultHr = true;
            else
                item.IsDefaultHr = false;
        }

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public GetRecruitResponse GetRecruit(GetRecruit model)
    {
        if (string.IsNullOrWhiteSpace(model.ChannelId))
            throw new BadRequestException("缺少渠道Id");

        //检测是否当前登录人的渠道
        var channel = _context.Qd_Hr_Channel.Where(x => x.ChannelUserId == _user.Id && x.Id == model.ChannelId)
        .Select(s => new
        {
            s.ChannelUserId,
            s.HrId
        }).FirstOrDefault();

        if (channel == null)
            throw new BadRequestException("内容不存在");

        var predicate = PredicateBuilder.New<Recruit>(x => x.Post_Delivery.ChannelId == model.ChannelId);

        if (!string.IsNullOrWhiteSpace(model.Search))
        {
            if (long.TryParse(model.Search, out var x))
                predicate = predicate.And(x => x.User_Hr.NickName.Contains(model.Search) || x.User_Hr.User.Mobile.Contains(model.Search));
            else
                predicate = predicate.And(x => x.User_Hr.NickName.Contains(model.Search));
        }

        var sql = _context.Recruit.Where(predicate);

        var result = new GetRecruitResponse { Rows = new List<GetRecruitInfo>() };

        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(o => o.CreatedTime)
        .ThenBy(x => x.RecruitId)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetRecruitInfo
        {
            Birthday = s.User_Seeker.User_Resume.Birthday,
            CreatTime = s.CreatedTime,
            Education = s.User_Seeker.User_Resume.Education,
            EducationName = s.User_Seeker.User_Resume.Education.GetDescription(),
            School = s.User_Seeker.User_Resume.School,
            Major = s.User_Seeker.User_Resume.Major,
            GraduationDate = s.User_Seeker.User_Resume.GraduationDate,
            HeadPortrait = s.User_Seeker.Avatar,
            HrAvatar = s.User_Hr.Avatar,
            HrName = s.User_Hr.NickName,
            HrPost = s.User_Hr.Post,
            HrAppletQrCode = s.User_Seeker.HrAppletQrCode,
            RecruitId = s.RecruitId,
            SeekerId = s.User_Seeker.UserId,
            IsRealName = !string.IsNullOrWhiteSpace(s.User_Seeker.User.IdentityCard),
            LastTime = s.User_Seeker.User_Extend.LoginTime,
            SeekerRegionId = s.User_Seeker.RegionId,
            Mobile = s.User_Seeker.User.Mobile,
            Name = s.User_Seeker.NickName,
            Occupation = s.User_Seeker.User_Resume.Occupation,
            OccupationName = s.User_Seeker.User_Resume.Occupation.GetDescription(),
            Sex = s.User_Seeker.User_Resume.Sex,
            SexName = s.User_Seeker.User_Resume.Sex.GetDescription(),
            PostName = s.Post_Delivery.Post.Name,
            RecruitStatus = s.Status,
            RecruitStatusName = s.Status.GetDescription(),
        }).ToList();

        foreach (var item in result.Rows)
        {
            var city = !string.IsNullOrWhiteSpace(item.SeekerRegionId) ? _commonDicService.GetCityById(item.SeekerRegionId) : new CityModel();
            item.Location = city.CityName;
            item.EduExp = $"{item.School}-{item.Major} {item.Education?.GetDescription()} {item.GraduationDate:yyyy-MM}";
            item.Active = Tools.GetOnlineStatus(item.LastTime);
            item.ActiveName = item.Active.GetDescription();
            item.Age = Tools.GetAgeByBirthdate(item.Birthday);
        }

        return result;
    }

    public GetTalentPlatformResponse GetTalentPlatform(GetTalentPlatform model)
    {
        if (string.IsNullOrWhiteSpace(model.ChannelId))
            throw new BadRequestException("缺少渠道Id");

        var client = _esHelper.GetClient();
        var mustQuerys = new List<Func<QueryContainerDescriptor<EsTalentPlatform>, QueryContainer>>();

        //检测是否当前登录人的渠道
        var channel = _context.Qd_Hr_Channel.Where(x => x.ChannelUserId == _user.Id && x.Id == model.ChannelId)
        .Select(s => new
        {
            s.ChannelUserId,
            s.HrId
        }).FirstOrDefault();

        if (channel == null)
            throw new BadRequestException("内容不存在");

        mustQuerys.Add(q => q.Term(t => t.Field(f => f.ChannelId).Value(model.ChannelId)) && q.Term(t => t.Field(f => f.HrId).Value(channel.HrId))
            && q.Term(t => t.Field(f => f.TalentDeleted).Value(false)));
        if (!string.IsNullOrEmpty(model.Search))
            mustQuerys.Add(q => q.Wildcard(t => t.Field(f => f.SeekerSearch).Value($"*{model.Search}*")));

        var talent = client.Search<EsTalentPlatform>(s => s
        .Index(_esHelper.GetIndex().TalentPlatform)
        .TrackTotalHits()
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Query(q => q.Bool(b => b.Must(mustQuerys)))
        .Sort(s => s.Field(f =>
        {
            f.Order(SortOrder.Descending);
            f.Field(c => c.SeekerVisitTime);
            return f;
        })));

        var reslt = new GetTalentPlatformResponse { Rows = new List<GetTalentPlatformInfo>() };
        reslt.Total = (int)talent.Total;
        if (reslt.Total < 0)
            reslt.Total = 0;

        foreach (var item in talent.Documents)
        {
            var wk = item.Works.OrderByDescending(o => o.BeginDate).FirstOrDefault();
            var city = !string.IsNullOrWhiteSpace(item.SeekerRegionId) ? _commonDicService.GetCityById(item.SeekerRegionId) : new CityModel();
            var obj = new GetTalentPlatformInfo
            {
                Active = Tools.GetOnlineStatus(item.SeekerLoginTime),
                Age = Tools.GetAgeByBirthdate(item.SeekerBirthday.HasValue ? DateOnly.FromDateTime(item.SeekerBirthday.Value) : null),
                CreatTime = item.TalentCreatedTime,
                Education = item.SeekerEducation,
                EducationName = item.SeekerEducation?.GetDescription(),
                EduExp = $"{item.SeekerSchool}-{item.SeekerMajor} {item.SeekerEducation?.GetDescription()} {item.SeekerGraduationDate:yyyy-MM}",
                HeadPortrait = item.SeekerAvatar,
                HrAppletQrCode = item.HrAppletQrCode,
                Id = item.Id,
                IsRealName = !string.IsNullOrWhiteSpace(item.SeekerIdentityCard),
                LastTime = item.SeekerLoginTime,
                Level = item.TalentLevel,
                LevelName = item.TalentLevel.GetDescription(),
                // Location = $"{city.ProvinceName}-{city.CityName}-{city.CountyName}",
                Location = city.CityName,
                Mobile = item.SeekerMobile,
                Name = item.SeekerNickName,
                Occupation = item.SeekerOccupation,
                OccupationName = item.SeekerOccupation?.GetDescription(),
                Sex = item.SeekerSex,
                SexName = item.SeekerSex?.GetDescription(),
                Source = item.TalentSource,
                SourceName = item.TalentSource.GetDescription(),
                WorkExp = $"{wk?.Company}-{wk?.Post}/{wk?.BeginDate:yyyy-MM}-{wk?.EndDate:yyyy-MM}",
                HopePost = string.Join(',', item.DesiredPostName ?? new List<string>())
            };
            reslt.Rows.Add(obj);
        }

        foreach (var item in reslt.Rows)
        {
            item.ActiveName = item.Active.GetDescription();
        }

        return reslt;
    }

    public GetTalentVirtualResponse GetTalentVirtual(GetTalentVirtual model)
    {
        if (string.IsNullOrWhiteSpace(model.ChannelId))
            throw new BadRequestException("缺少渠道Id");

        var client = _esHelper.GetClient();
        var mustQuerys = new List<Func<QueryContainerDescriptor<EsTalentVirtual>, QueryContainer>>();

        //检测是否当前登录人的渠道
        var channel = _context.Qd_Hr_Channel.Where(x => x.ChannelUserId == _user.Id && x.Id == model.ChannelId)
        .Select(s => new
        {
            s.ChannelUserId
        }).FirstOrDefault();

        if (channel == null)
            throw new BadRequestException("内容不存在");

        mustQuerys.Add(q => q.Term(t => t.Field(f => f.ChannelId).Value(model.ChannelId)));

        if (!string.IsNullOrEmpty(model.Search))
            mustQuerys.Add(q => q.Wildcard(t => t.Field(f => f.TalentSearch).Value($"*{model.Search}*")));

        if (model.RegisteredStatus == 0)
            mustQuerys.Add(q => q.Term(t => t.Field(f => f.Status).Value(TalentVirtualStatus.UnRegistered)));
        else if (model.RegisteredStatus == 1)
            mustQuerys.Add(q => !q.Term(t => t.Field(f => f.Status).Value(TalentVirtualStatus.UnRegistered)));

        var resultModel = client.Search<EsTalentVirtual>(s => s
        .Index(_esHelper.GetIndex().TalentVirtual)
        .TrackTotalHits()
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Query(q => q.Bool(b => b.Must(mustQuerys)))
        .Sort(s => s.Field(f =>
        {
            f.Order(SortOrder.Descending);
            f.Field(c => c.UpdatedTime);
            return f;
        })));

        var result = new GetTalentVirtualResponse { Rows = new List<GetTalentVirtualInfo>() };
        result.Total = (int)resultModel.Total;

        foreach (var i in resultModel.Documents)
        {
            var edu = i.Edus?.OrderByDescending(o => o.StartTime).FirstOrDefault();
            var wk = i.Works?.OrderByDescending(o => o.StartTime).FirstOrDefault();
            var addModel = new GetTalentVirtualInfo()
            {
                Birthday = i.Birthday,
                ChannelName = i.Channel == null ? null : i.Channel.GetDescription(),
                EducationName = i.Education == null ? null : i.Education.GetDescription(),
                EduExp = $"{edu?.SchoolName}-{edu?.MajorName} {edu?.Education.GetDescription()} {edu?.EndTime:yyyy-MM}",
                HeadPortrait = i.HeadPortrait,
                HopePost = i.Hopes == null ? null : string.Join('|', i.Hopes.Select(x => x.PostName)),
                HopeSalary = i.Hopes == null ? null : string.Join('|', i.Hopes.Select(x => Tools.FormattingSalary((int)x.MinSalary, (int)x.MaxSalary, PostSalaryType.月薪, PostWorkNature.全职))),
                Id = i.Id,
                Location = i.Hopes == null ? null : string.Join('|', i.Hopes.Select(x => x.HopeCity)),
                Name = i.Name,
                Sex = i.Sex,
                SexName = i.Sex == null ? null : i.Sex.ToString(),
                StatusName = i.Status == null ? null : i.Status.GetDescription(),
                UpdateTime = i.UpdatedTime,
                Channel = i.Channel,
                Education = i.Education,
                Status = i.Status,
                WorkExp = $"{wk?.CompanyName}-{wk?.PostName}/{wk?.StartTime:yyyy-MM}-{wk?.EndTime:yyyy-MM}",
                WorkExperience = i.WorkTime == null ? null : DateTime.Now.Year - i.WorkTime.Value.Year,
                CreatTime = i.CreatedTime
            };
            if (i.Birthday is not null)
                addModel.Age = Tools.GetAgeByBirthdate(DateOnly.FromDateTime(i.Birthday.Value));
            result.Rows.Add(addModel);
        }
        return result;
    }

    public TalentVitualMainIdResponse UpdateTalentVirtual(UpdateTalentVirtaual model)
    {
        // 检测是否当前登录人的渠道
        var channelId = _context.Qd_Hr_Channel.Where(x => x.ChannelUserId == _user.Id && x.HrId == model.HrId)
        .Select(s => new
        {
            s.Id
        }).FirstOrDefault();
        if (channelId == null)
            throw new BadRequestException("获取渠道失败");

        // 必填校验
        if (string.IsNullOrWhiteSpace(model.Name))
            throw new BadRequestException("姓名不能为空");
        if (model.Sex == null)
            throw new BadRequestException("性别不能为空");
        if (model.Birthday == null)
            throw new BadRequestException("生日不能为空");
        if (string.IsNullOrWhiteSpace(model.Mobile))
            throw new BadRequestException("电话不能为空");


        // 这里按有且仅有一条教育经历判断
        if (model.EduSub != null && model.EduSub?.Count > 0)
        {
            if (model.EduSub.FirstOrDefault()!.Education == null)
                throw new BadRequestException("学历不能为空");
            if (string.IsNullOrWhiteSpace(model.EduSub.FirstOrDefault()!.SchoolName))
                throw new BadRequestException("学校不能为空");
            if (model.EduSub.FirstOrDefault()!.StartTime == null || model.EduSub.FirstOrDefault()!.EndTime == null)
                throw new BadRequestException("毕业年份不完善");
            if (string.IsNullOrWhiteSpace(model.EduSub.FirstOrDefault()!.MajorName))
                throw new BadRequestException("专业不能为空");
            if (model.EduSub.FirstOrDefault()!.IsFullTime == null)
                throw new BadRequestException("招录形式不能为空");

            if (model.EduSub.FirstOrDefault()!.EndTime!.Value.CompareTo(model.EduSub.FirstOrDefault()!.StartTime!.Value) < 0)
                throw new BadRequestException("结束时间不能小于开始时间");
        }

        Talent_Virtual? talentVirtual = null;
        if (!string.IsNullOrWhiteSpace(model.Id))
        {
            talentVirtual = _context.Talent_Virtual
                .Include(i => i.Talent_Virtual_Hope)
                .Include(i => i.Talent_Virtual_Edu)
                .FirstOrDefault(x => x.Id == model.Id);

            if (talentVirtual == null)
                throw new NotFoundException("内容不存在");
        }

        if (talentVirtual == null)// 新增
        {
            // 检验同一个HR下是否已经存在该人才简历（电话号）
            if (_context.Talent_Virtual.Any(x => x.HrId == model.HrId && x.Mobile == model.Mobile))
                throw new BadRequestException("重复添加");

            talentVirtual = new Talent_Virtual();
            talentVirtual.Talent_Virtual_Hope = new Talent_Virtual_Hope() { VirtualId = talentVirtual.Id };
            _context.Add(talentVirtual);
        }

        // 判断用户状态
        // 1.有seekerid，直接去User表拿Idcard判断是否已实名
        if (!string.IsNullOrWhiteSpace(talentVirtual.SeekerId))
        {
            var userModel = _context.User.Where(o => o.UserId == talentVirtual.SeekerId).FirstOrDefault();
            if (userModel != null && !string.IsNullOrEmpty(userModel.IdentityCard))
            {
                talentVirtual.Status = TalentVirtualStatus.RealName;
            }
        }
        // 2.无seekerid，根据mobile去User表拿UserId，再看User_Seeker表是否存在，不存在直接未注册，存在则判断是否已实名
        else
        {
            var userModel = _context.User.Include(i => i.User_Seeker).FirstOrDefault(o => o.Mobile == model.Mobile);
            if (userModel != null)
            {
                if (userModel.User_Seeker != null)
                {
                    talentVirtual.SeekerId = userModel.UserId;
                    talentVirtual.Status = TalentVirtualStatus.Registered;

                    if (!string.IsNullOrEmpty(userModel.IdentityCard))
                    {
                        talentVirtual.Status = TalentVirtualStatus.RealName;
                    }
                }
            }
        }

        talentVirtual.HeadPortrait = model.HeadPortrait ?? string.Empty;
        talentVirtual.Name = model.Name;
        talentVirtual.Sex = model.Sex ?? Sex.女;
        talentVirtual.Birthday = model.Birthday ?? DateTime.MinValue;
        talentVirtual.Mobile = model.Mobile;
        talentVirtual.HrId = model.HrId ?? string.Empty;
        talentVirtual.Channel = model.Channel ?? TalentVirtualChannel.ResumeUpload;
        talentVirtual.ChannelId = channelId.Id;
        talentVirtual.SelfEvaluation = model.SelfEvaluation ?? string.Empty;
        talentVirtual.Talent_Virtual_Hope.PostName = model.HopeSub?.PostName ?? string.Empty;
        talentVirtual.Talent_Virtual_Hope.HopeCity = model.HopeSub?.HopeCity ?? string.Empty;// todo: 待确认
        talentVirtual.Talent_Virtual_Hope.MinSalary = Convert.ToDecimal(model.HopeSub?.MinSalary);
        talentVirtual.Talent_Virtual_Hope.MaxSalary = Convert.ToDecimal(model.HopeSub?.MaxSalary);
        talentVirtual.WeChat = model.WeChat ?? string.Empty;
        talentVirtual.QQ = model.QQ ?? string.Empty;
        talentVirtual.Mailbox = model.Mailbox ?? string.Empty;
        talentVirtual.UpdatedTime = DateTime.Now;
        if (model.EduSub != null)
        {
            talentVirtual.Talent_Virtual_Edu = new List<Talent_Virtual_Edu>();
            talentVirtual.Talent_Virtual_Edu = GetEduInfoFromModel(model.EduSub, out int topEducation);
            talentVirtual.Education = (TalentVirtualEducation)topEducation;
        }

        talentVirtual.RegionId = string.Empty;// todo: 待验证

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        // 计算虚拟简历库简历完善度 todo: 这里要测试一下事务一致性
        _commonVirtualService.CountVirtualResumeScore(talentVirtual.Id);

        var response = new TalentVitualMainIdResponse { VirtualId = talentVirtual.Id };
        return response;
    }

    /// <summary>
    /// 从requestmodel获取教育经历
    /// </summary>
    /// <param name="eduSub"></param>
    /// <returns></returns>
    private List<Talent_Virtual_Edu> GetEduInfoFromModel(List<TalentVirtaualResumeEduSub> eduSub, out int topEducation)
    {
        List<Talent_Virtual_Edu> edus = new();
        topEducation = 0;
        foreach (var item in eduSub)
        {
            int currentEducation = (int)item.Education!.Value;
            if (currentEducation > topEducation)
            {
                topEducation = currentEducation;
            }
            edus.Add(new Talent_Virtual_Edu
            {
                Education = item.Education ?? TalentVirtualEducation.Junior,
                SchoolName = item.SchoolName ?? string.Empty,
                MajorName = item.MajorName ?? string.Empty,
                IsFullTime = item.IsFullTime ?? false,
                SchoolRemarks = item.SchoolRemarks ?? string.Empty,
                StartTime = item.StartTime!.Value,
                EndTime = item.EndTime!.Value,
            });
        }
        return edus;
    }

    public UpdateTalentVirtualProjectsResponse UpdateTalentVirtualProjects(UpdateTalentVirtualResumeProjectSub model)
    {
        string virtualId = model.VirtualId;
        var lockerKey = $"st:up_tv_resume_projects:{model.VirtualId ?? model.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        if (string.IsNullOrWhiteSpace(model.ProjectName))
            throw new BadRequestException("项目名称不能为空");

        if (string.IsNullOrWhiteSpace(model.PostName))
            throw new BadRequestException("项目角色不能为空");

        if (!model.StartTime.HasValue)
            throw new BadRequestException("开始时间不能为空");

        if (model.StartTime.Value.CompareTo(DateTime.Now.Date) > 0)
            throw new BadRequestException("开始时间不能大于当前日期");

        if (model.EndTime.HasValue)
        {
            if (model.EndTime.Value.CompareTo(DateTime.Now.Date) > 0)
                throw new BadRequestException("结束时间不能大于当前日期");
            if (model.EndTime.Value.CompareTo(model.StartTime.Value) < 0)
                throw new BadRequestException("结束时间不能小于开始时间");
        }

        Talent_Virtual_Project? projects = null;
        if (!string.IsNullOrWhiteSpace(model.Id))
        {
            projects = _context.Talent_Virtual_Project.FirstOrDefault(x => x.Id == model.Id);
            if (projects == null)
                throw new NotFoundException("内容不存在");
            CheckChannelId(_context.Talent_Virtual.Where(x => x.Id == projects.VirtualId).Select(s => s.ChannelId).FirstOrDefault());
            virtualId = projects.VirtualId;
        }

        if (projects == null)
        {
            CheckChannelId(_context.Talent_Virtual.Where(x => x.Id == model.VirtualId).Select(s => s.ChannelId).FirstOrDefault());
            // 检测客户端重复提交： 项目名称+岗位名称+开始时间+结束时间
            if (_context.Talent_Virtual_Project.Any(x => x.VirtualId == model.VirtualId && x.ProjectName == model.ProjectName && x.PostName == model.PostName
            && x.StartTime == model.StartTime && x.EndTime == model.EndTime))
                throw new BadRequestException("重复添加");

            // 添加项目经历
            projects = new Talent_Virtual_Project
            {
                VirtualId = model.VirtualId!
            };
            _context.Add(projects);
        }

        projects.ProjectName = model.ProjectName;
        projects.PostName = model.PostName;
        projects.StartTime = model.StartTime.Value;
        projects.EndTime = model.EndTime;// 项目这里没有至今，结束时间允许为空
        projects.ProjectRemarks = model.ProjectRemarks ?? string.Empty;
        // 更新主表字段
        var talentV = new Talent_Virtual { Id = virtualId };
        _context.Attach(talentV);
        talentV.UpdatedTime = DateTime.Now;

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        // 计算虚拟简历库简历完善度
        _commonVirtualService.CountVirtualResumeScore(virtualId);

        var result = new UpdateTalentVirtualProjectsResponse { Id = projects.Id };
        return result;
    }

    public UpdateTalentVirtualWorksResponse UpdateTalentVirtualWorks(UpdateTalentVirtualResumeWorkSub model)
    {
        string virtualId = model.VirtualId;
        var lockerKey = $"st:up_tv_resume_works:{model.VirtualId ?? model.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        if (string.IsNullOrWhiteSpace(model.CompanyName))
            throw new BadRequestException("公司名称不能为空");

        if (string.IsNullOrWhiteSpace(model.PostName))
            throw new BadRequestException("职位不能为空");

        if (!model.StartTime.HasValue)
            throw new BadRequestException("开始时间不能为空");

        if (model.StartTime.Value.CompareTo(DateTime.Now.Date) > 0)
            throw new BadRequestException("开始时间不能大于当前日期");

        if (model.EndTime.HasValue)
        {
            if (model.EndTime.Value.CompareTo(DateTime.Now.Date) > 0)
                throw new BadRequestException("结束时间不能大于当前日期");
            if (model.EndTime.Value.CompareTo(model.StartTime.Value) < 0)
                throw new BadRequestException("结束时间不能小于开始时间");
        }

        Talent_Virtual_Work? work = null;
        if (!string.IsNullOrWhiteSpace(model.Id))
        {
            work = _context.Talent_Virtual_Work.FirstOrDefault(x => x.Id == model.Id);
            if (work == null)
                throw new NotFoundException("内容不存在");

            CheckChannelId(_context.Talent_Virtual.Where(x => x.Id == work.VirtualId).Select(s => s.ChannelId).FirstOrDefault());
            virtualId = work.VirtualId;
        }

        if (work == null)
        {
            CheckChannelId(_context.Talent_Virtual.Where(x => x.Id == model.VirtualId).Select(s => s.ChannelId).FirstOrDefault());
            // 检测客户端重复提交：公司名称+岗位名称+开始时间+结束时间
            if (_context.Talent_Virtual_Work.Any(x => x.VirtualId == model.VirtualId && x.CompanyName == model.CompanyName
            && x.PostName == model.PostName && x.StartTime == model.StartTime && x.EndTime == model.EndTime))
                throw new BadRequestException("重复添加");

            // 添加工作经历
            work = new Talent_Virtual_Work
            {
                VirtualId = model.VirtualId!
            };
            _context.Add(work);
        }

        work.CompanyName = model.CompanyName;
        work.PostName = model.PostName ?? string.Empty;
        work.StartTime = model.StartTime!.Value;
        work.EndTime = model.EndTime ?? DateTime.Now;// 至今
        work.CompanyRemarks = model.CompanyRemarks ?? string.Empty;
        work.Department = model.Department ?? string.Empty;
        work.IndustryName = model.IndustryName ?? string.Empty;
        // 更新主表字段
        var talentV = new Talent_Virtual { Id = virtualId };
        _context.Attach(talentV);
        talentV.UpdatedTime = DateTime.Now;

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        // 计算虚拟简历库简历完善度
        _commonVirtualService.CountVirtualResumeScore(virtualId);

        var result = new UpdateTalentVirtualWorksResponse { Id = work.Id };
        return result;
    }

    /// <summary>
    /// 当前登录人校验
    /// </summary>
    /// <param name="channelId"></param>
    /// <exception cref="BadRequestException"></exception>
    private void CheckChannelId(string? channelId)
    {
        if (string.IsNullOrWhiteSpace(channelId))
            throw new BadRequestException("缺少渠道Id");

        // 检测是否当前登录人的渠道
        var channel = _context.Qd_Hr_Channel.Where(x => x.ChannelUserId == _user.Id && x.Id == channelId)
        .Select(s => new
        {
            s.ChannelUserId
        }).FirstOrDefault();

        if (channel == null)
            throw new BadRequestException("内容不存在");
    }

    public EmptyResponse DeleteTalentVirtualProjects(string id)
    {
        var projects = _context.Talent_Virtual_Project.Include(i => i.Talent_Virtual).FirstOrDefault(x => x.Id == id);
        if (projects == null)
            throw new NotFoundException("内容不存在");
        // 校验登录人
        CheckChannelId(projects.Talent_Virtual.ChannelId);

        _context.Remove(projects);
        projects.Talent_Virtual.UpdatedTime = DateTime.Now;// 更新简历表updatedtime字段

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        // 计算虚拟简历库简历完善度
        _commonVirtualService.CountVirtualResumeScore(projects.Talent_Virtual.Id);

        return new EmptyResponse();
    }

    public EmptyResponse DeleteTalentVirtualWorks(string id)
    {
        var works = _context.Talent_Virtual_Work.Include(i => i.Talent_Virtual).FirstOrDefault(x => x.Id == id);
        if (works == null)
            throw new NotFoundException("内容不存在");
        // 校验登录人
        CheckChannelId(works.Talent_Virtual.ChannelId);

        _context.Remove(works);
        works.Talent_Virtual.UpdatedTime = DateTime.Now;// 更新简历表updatedtime字段

        _context.EnsureAutoHistory();
        _context.SaveChanges();

        // 计算虚拟简历库简历完善度
        _commonVirtualService.CountVirtualResumeScore(works.Talent_Virtual.Id);

        return new EmptyResponse();
    }

    public async Task<TalentPlatformResumeDetailsResponse> GetTalentPlatformDetailsAsync(string platformId)
    {
        await Task.FromResult(1);

        var talentPlatformModel = _context.Talent_Platform.Where(o => o.Id == platformId && o.Status == ActiveStatus.Active)
        .Select(o => new TalentPlatformResumeDetailsResponse
        {
            Name = o.User_Seeker.NickName,
            _Birthday = o.User_Seeker.User_Resume.Birthday,
            AttachmentUrl = o.User_Seeker.User_Resume_Attach.Url,
            HeadPortrait = o.User_Seeker.Avatar,
            _IdentityCard = o.User_Seeker.User.IdentityCard,
            Mailbox = o.User_Seeker.User_Resume.EMail,
            Mobile = o.User_Seeker.User.Mobile,
            WeChat = o.User_Seeker.User_Resume.WeChatNo,
            QQ = o.User_Seeker.User_Resume.Qq,
            Occupation = o.User_Seeker.User_Resume.Occupation,
            Education = o.User_Seeker.User_Resume.Education,
            Perfection = o.User_Seeker.User_Resume.Score,
            PlatformId = o.Id,
            Sex = o.User_Seeker.User_Resume.Sex,
            SelfEvaluation = o.User_Seeker.User_Resume.Describe,
            Nature = o.User_Seeker.User_Resume.Nature,
            Skill = o.User_Seeker.User_Resume.Skill,
            Appearance = o.User_Seeker.User_Resume.Appearance,
            SkillsCert = o.User_Seeker.User_Resume.Certificate,
            HrAppletQrCode = o.User_Seeker.HrAppletQrCode,
            Active = Tools.GetOnlineStatus(o.User_Seeker.User_Extend.LoginTime),
            Advisers = o.User_Seeker.User_Num.Adviser,
            Location = !string.IsNullOrEmpty(o.User_Seeker.RegionId) ? _commonDicService.GetCityById(o.User_Seeker.RegionId).CityName : null,
            EduSub = string.IsNullOrEmpty(o.User_Seeker.User_Resume.School) ? null : new TalentPlatformResumeEduSub
            {
                Education = o.User_Seeker.User_Resume.Education,
                SchoolName = o.User_Seeker.User_Resume.School,
                MajorName = o.User_Seeker.User_Resume.Major,
                EndTime = o.User_Seeker.User_Resume.GraduationDate,
            },
            WorkSub = o.User_Seeker.User_Resume.User_Work.Select(m => new TalentPlatformResumeWorkSub
            {
                CompanyName = m.Company,
                CompanyRemarks = m.Describe,
                PostName = m.Post,
                EndTime = m.EndDate,
                StartTime = m.BeginDate
            }).ToList(),
            PracticeSub = o.User_Seeker.User_Resume.User_Campus.Select(m => new TalentPlatformResumePracticeSub
            {
                PracticeName = m.Name,
                Experience = m.Describe,
                PostPrize = m.Award,
                EndTime = m.EndDate,
                StartTime = m.BeginDate
            }).ToList()
        }).FirstOrDefault();

        if (talentPlatformModel == null)
        {
            throw new NotFoundException("内容不存在！");
        }

        //整理数据
        talentPlatformModel.ActiveName = talentPlatformModel.Active.GetDescription();
        talentPlatformModel.Age = talentPlatformModel._Birthday == null ? null : DateTime.Now.Year - talentPlatformModel._Birthday.Value.Year;
        talentPlatformModel.IsRealName = !string.IsNullOrEmpty(talentPlatformModel._IdentityCard);
        talentPlatformModel.OccupationName = talentPlatformModel.Occupation == null ? null : talentPlatformModel.Occupation.GetDescription();
        talentPlatformModel.EducationName = talentPlatformModel.Education == null ? null : talentPlatformModel.Education.GetDescription();
        talentPlatformModel.SexName = talentPlatformModel.Sex == null ? null : talentPlatformModel.Sex.GetDescription();
        if (talentPlatformModel.EduSub != null)
        {
            talentPlatformModel.EduSub.EducationName = talentPlatformModel.EduSub.Education == null ? null : talentPlatformModel.EduSub.Education.GetDescription();
        }

        //拼接标签
        if (talentPlatformModel.Nature.Count > 0 || talentPlatformModel.Skill.Count > 0 || talentPlatformModel.Appearance.Count > 0)
        {
            talentPlatformModel.Label = talentPlatformModel.Nature.Union(talentPlatformModel.Skill).Union(talentPlatformModel.Appearance).ToList();
        }

        return talentPlatformModel;
    }

    public async Task<TalentVirtualBaseInfoResponse> GetTalentVirtualInfoAsync(string virtualId)
    {
        await Task.FromResult(1);
        //Task.Run();
        TalentVirtualBaseInfoResponse? talentVirtualModel = new();
        talentVirtualModel = _context.Talent_Virtual.Where(o => o.Id == virtualId)
        .Select(o => new TalentVirtualBaseInfoResponse
        {
            Name = o.Name,
            Birthday = DateOnly.FromDateTime(o.Birthday),
            OriginalUrl = o.OriginalUrl,
            HeadPortrait = o.HeadPortrait,
            Mailbox = o.Mailbox,
            Mobile = o.Mobile,
            WeChat = o.WeChat,
            QQ = o.QQ,
            SeekerId = o.SeekerId,
            Occupation = o.User_Seeker.User_Resume.Occupation,
            Education = o.Education,
            Perfection = o.Perfection,
            VirtualId = o.Id,
            Sex = o.Sex,
            SelfEvaluation = o.SelfEvaluation,
            Nature = o.User_Seeker.User_Resume.Nature,
            Skill = o.User_Seeker.User_Resume.Skill,
            SkillAnalysis = o.SkillAnalysis,
            Appearance = o.User_Seeker.User_Resume.Appearance,
            SkillsCert = o.User_Seeker.User_Resume.Certificate,
            //HrAppletQrCode = o.User_Seeker.HrAppletQrCode,
            Active = Tools.GetOnlineStatus(o.User_Seeker.User_Extend.LoginTime),
            Advisers = o.User_Seeker.User_Num.Adviser,
            Location = o.Talent_Virtual_Hope.HopeCity,
            HopeSub = new TalentVirtaualResumeHopeSubResponse
            {
                HopeCity = o.Talent_Virtual_Hope.HopeCity,
                PostName = o.Talent_Virtual_Hope.PostName,
                MinSalary = o.Talent_Virtual_Hope.MinSalary,
                MaxSalary = o.Talent_Virtual_Hope.MaxSalary
            },
            EduSub = o.Talent_Virtual_Edu.Select(e => new TalentVirtaualResumeEduSubResponse
            {
                Education = e.Education,
                EducationName = e.Education.GetDescription(),
                SchoolName = e.SchoolName,
                MajorName = e.MajorName,
                StartTime = e.StartTime.Year + "-09",// 如果只填了年份，那么入学时间为：年份 - 9月，毕业年份为：年份 - 6月 的形式
                EndTime = e.EndTime != null ? e.EndTime.Value.Year + "-06" : null,
                IsFullTime = e.IsFullTime
            }).ToList(),
            WorkSub = o.Talent_Virtual_Work.Select(w => new TalentVirtaualResumeWorkSubResponse
            {
                Id = w.Id,
                CompanyName = w.CompanyName,
                CompanyRemarks = w.CompanyRemarks,
                PostName = w.PostName,
                EndTime = w.EndTime != null ? w.EndTime.Value.ToString("yyyy-MM") : null,
                StartTime = w.StartTime.ToString("yyyy-MM")
            }).ToList(),
            ProjectSub = o.Talent_Virtual_Project.Select(p => new TalentVirtaualResumeProjectSubResponse
            {
                Id = p.Id,
                ProjectName = p.ProjectName,
                PostName = p.PostName,
                ProjectRemarks = p.ProjectRemarks,
                EndTime = p.EndTime != null ? p.EndTime.Value.ToString("yyyy-MM") : null,
                StartTime = p.StartTime.ToString("yyyy-MM")
            }).ToList()
        }).FirstOrDefault();

        if (talentVirtualModel == null)
        {
            throw new NotFoundException("内容不存在！");
        }

        // 整理数据
        talentVirtualModel.ActiveName = talentVirtualModel.Active.GetDescription();
        talentVirtualModel.Age = talentVirtualModel.Birthday == null ? null : DateTime.Now.Year - talentVirtualModel.Birthday.Value.Year;
        //talentVirtualModel.IsRealName = !string.IsNullOrEmpty(talentVirtualModel._IdentityCard);
        talentVirtualModel.OccupationName = talentVirtualModel.Occupation == null ? null : talentVirtualModel.Occupation.GetDescription();
        talentVirtualModel.EducationName = talentVirtualModel.Education == null ? null : talentVirtualModel.Education.GetDescription();
        talentVirtualModel.SexName = talentVirtualModel.Sex == null ? null : talentVirtualModel.Sex.GetDescription();

        // 拼接标签 todo: skill 与 SkillAnalysis的区别
        talentVirtualModel.Label = new List<string>();
        if (talentVirtualModel.Nature?.Count > 0)
        {
            talentVirtualModel.Label.Union(talentVirtualModel.Nature).ToList();
        }
        if (talentVirtualModel.Skill?.Count > 0)
        {
            talentVirtualModel.Label.Union(talentVirtualModel.Skill).ToList();
        }
        if (talentVirtualModel.Appearance?.Count > 0)
        {
            talentVirtualModel.Label.Union(talentVirtualModel.Appearance).ToList();
        }

        return talentVirtualModel;
    }

    public async Task<EmptyResponse> SendMessageToActive(string id)
    {
        var talentVirtualInfo = _context.Talent_Virtual.FirstOrDefault(o => o.Id == id);
        if (talentVirtualInfo == null)
        {
            throw new NotFoundException("内容不存在");
        }
        // 校验登录人
        CheckChannelId(talentVirtualInfo.ChannelId);

        // 一个网格员一天只能发送 100 条（24个小时）
        var rdsKey_100 = $"st:tv:{DateTime.Today.ToYYYY_MM_DD()}:{_user.Id}";
        var item_name = "times";
        if (Convert.ToInt32(MyRedis.Client.HGet(rdsKey_100, item_name)) >= 100)
        {
            throw new BadRequestException("今日激活次数超过一百次");
        }

        // 同一个手机号一天只能发送 1 条（24个小时），todo：同一个HR下是否添加
        var rdsKey_1 = $"st:tv:{DateTime.Today.ToYYYY_MM_DD()}:{talentVirtualInfo.Mobile}";
        if (!MyRedis.Client.SetNx(rdsKey_1, 1, TimeSpan.FromDays(1)))
        {
            throw new BadRequestException("该手机号今日已发起激活");
        }

        try
        {
            // 生成跳转链接 前端提供 跳转地址 以及 入参
            string jumpDetails = "Mypackagedetail/informationacq/informationacq";
            string query = "hrid={0}&channelid={1}&adviser={2}";
            string url = await _weChatHelper.GetWxShortLinkForJump(_config.WeChat!.SeekerApplet!.AppId!, jumpDetails, string.Format(query, talentVirtualInfo.HrId, talentVirtualInfo.ChannelId, talentVirtualInfo.HrId));
            string jumpUrl = url.Split('/')[^1];
            if (string.IsNullOrWhiteSpace(jumpUrl))
            {
                throw new Exception("小程序链接生成失败");
            }


            // 发送短信 - aliyun短信模板code，模板参数
            string seekName = "求职者";// 名字为空默认求职者
            string templateCode = "SMS_276045487";
            if (!string.IsNullOrWhiteSpace(talentVirtualInfo.Name))
            {
                seekName = talentVirtualInfo.Name;
            }
            var result = _smsHelper.SendSMSAliyunForJumpApplet(talentVirtualInfo.Mobile, templateCode, new
            {
                name = seekName,
                url = jumpUrl
            });
            if (result.Success)
            {
                var times = MyRedis.Client.HIncrBy(rdsKey_100, item_name, 1);
                if (times == 1)
                {
                    MyRedis.Client.Expire(rdsKey_100, TimeSpan.FromDays(1));
                }
                MyRedis.Client.SetNx(rdsKey_1, 1, TimeSpan.FromDays(1));
            }
            else
            {
                throw new Exception(result.Message);
            }
        }
        catch (Exception ex)
        {
            _log.Error("网格员短信激活失败", $"talentVirtualId:{id}", Tools.GetErrMsg(ex));
        }
        return new EmptyResponse();
    }

    public EmptyResponse TopPost(TopPost model)
    {
        if (string.IsNullOrWhiteSpace(model.TeamPostId) || string.IsNullOrWhiteSpace(model.ChannelId))
            throw new BadRequestException("缺少参数");

        var checkChannel = _context.Qd_Hr_Channel.Any(x => x.ChannelUserId == _user.Id && x.Id == model.ChannelId);
        if (!checkChannel)
            throw new BadRequestException("无权限");

        var checkTeamPost = _context.Post_Team.Any(x => x.TeamPostId == model.TeamPostId);
        if (!checkTeamPost)
            throw new BadRequestException("职位不存在");

        var lockerKey = $"st:cltoppost:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var postChannel = _context.Post_Team_Channel
        .FirstOrDefault(x => x.ChannelId == model.ChannelId && x.TeamPostId == model.TeamPostId);

        if (postChannel == null)
        {
            postChannel = new Post_Team_Channel
            {
                TeamPostId = model.TeamPostId,
                ChannelId = model.ChannelId
            };
            _context.Add(postChannel);
        }

        if (model.Top)
            postChannel.TopTime = DateTime.Now;
        else
            postChannel.TopTime = null;

        _context.SaveChanges();

        return new EmptyResponse();
    }

    public SeekerGetPostsResponse SeekerGetPosts(SeekerGetPosts model)
    {
        // if (string.IsNullOrWhiteSpace(model.ChannelId))
        //     throw new BadRequestException("缺少ChannelId");

        var result = _commonPostService.SeekerGetPosts(model);

        if (result.Rows.Count <= 0)
            return result;

        var teamPostIds = result.Rows.Select(s => s.TeamPostId).ToList();

        if (!string.IsNullOrWhiteSpace(model.ChannelId))
        {
            var postChannel = _context.Post_Team_Channel.Where(x => x.ChannelId == model.ChannelId
            && teamPostIds.Contains(x.TeamPostId)).Select(s => new
            {
                s.TopTime,
                s.TeamPostId
            });

            foreach (var item in result.Rows)
            {
                if (postChannel.Any(x => x.TeamPostId == item.TeamPostId && x.TopTime.HasValue))
                    item.ChannelTop = true;
            }
        }

        return result;
    }

    public async Task<SeekerGetPostInfo> SeekerGetPost(SeekerGetPost model)
    {
        if (string.IsNullOrWhiteSpace(model.ChannelId))
            throw new BadRequestException("缺少ChannelId");

        var result = await _commonPostService.SeekerGetPost(model);

        var postChannel = _context.Post_Team_Channel.Where(x => x.ChannelId == model.ChannelId
        && x.TeamPostId == result.TeamPostId).Select(s => new
        {
            s.TopTime
        }).FirstOrDefault();

        if (postChannel?.TopTime.HasValue == true)
            result.ChannelTop = true;

        return result;
    }

    /// <summary>
    /// 上传简历（调用小析服务解析简历）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse UpLoadResume(UpLoadResumeRequest model)
    {
        if (model.resumes != null && model.resumes.Count > 0)
        {
            List<Talent_Upload_Record> addList = new List<Talent_Upload_Record>();
            string? ChannelId = null;
            if (model.HrId != null)
            {
                var channelInfo = _context.Qd_Hr_Channel.Where(w => w.HrId == model.HrId && w.ChannelUserId == _user.Id).FirstOrDefault();
                if (channelInfo == null)
                    throw new BadRequestException("未获取到渠道信息");
                ChannelId = channelInfo.Id;
            }


            foreach (var i in model.resumes)
            {
                var record = new Talent_Upload_Record()
                {
                    CreatedTime = DateTime.Now,
                    FileExtension = i.FileExtension,
                    FileName = i.FileName,
                    FileUrl = i.FileUrl,
                    HrId = model.HrId ?? _user.Id,
                    RepeatType = i.RepeatType,
                    UpLoadStatus = TalentUpLoadStatus.Untreated,
                    FailNumber = 0,
                    QualifiedNumber = 0,
                    RepeatNumber = 0,
                    TotalNumber = 0,
                    WarehousingNumber = 0
                };
                if (ChannelId != null)
                    record.ChannelId = ChannelId;
                addList.Add(record);
            }

            _context.Talent_Upload_Record.AddRange(addList);
            _context.SaveChanges();

            MyRedis.Client.RPush(SubscriptionKey.UpLoadResume, addList.Select(o => o.Id).ToArray());
        }

        return new EmptyResponse();
    }
}