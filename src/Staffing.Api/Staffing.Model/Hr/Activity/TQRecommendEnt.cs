﻿namespace Staffing.Model.Hr.Activity
{
    public class TQRecommendEnt
    {
        /// <summary>
        /// 顾问ID
        /// </summary>
        public string? AdviserId { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string? EntName { get; set; }

        /// <summary>
        /// Hr名称
        /// </summary>
        public string? HrName { get; set; }

        /// <summary>
        /// 是否需要猎头服务或批量用工交付
        /// *页面选择项名称 
        /// </summary>
        public string? NeedService { get; set; }
        /// <summary>
        /// 招聘岗位
        /// *页面选择项名称 
        /// </summary>
        public string? JobRecruitment { get; set; }
        /// <summary>
        /// 招聘人数
        /// *页面选择项名称 
        /// </summary>
        public string? RecruitingNumber { get; set; }

        /// <summary>
        /// 腾讯广告clickid
        /// </summary>
        public string? Gdt_vid { get; set; }
    }
}
