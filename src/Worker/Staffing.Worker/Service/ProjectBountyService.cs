using Config;
using Config.CommonModel.JavaDataApi;
using Config.CommonModel.Ndn;
using Config.Enums;
using Elasticsearch.Net;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.CommonService.Ndn;
using Infrastructure.CommonService.QuestPDF;
using Infrastructure.CommonService.ShareProfit;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Infrastructure.Proxy;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class ProjectBountyService
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _logger;
    private readonly CommonDicService _commonDicService;
    private readonly CommonProjectService _commonProjectService;
    private readonly CommonPDFService _commonPDFService;
    private readonly ProfitService _profitService;
    private CommonPostOrder _commonPostOrder;

    private readonly NdnApi _ndnApi;
    private readonly ConfigManager _config;
    private readonly INdnService _ndnService;
    private readonly InternalApi _internalApi;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly CommonUserService _commonUserService;
    public ProjectBountyService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger, CommonPDFService commonPDFService, NdnApi ndnApi, IOptionsSnapshot<ConfigManager> config,
    CommonDicService commonDicService, CommonProjectService commonProjectService, CommonPostOrder commonPostOrder, INdnService ndnService, ProfitService profitService,
    InternalApi internalApi, CommonUserService commonUserService, IHostEnvironment hostingEnvironment)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _commonDicService = commonDicService;
        _commonPDFService = commonPDFService;
        _commonProjectService = commonProjectService;
        _commonPostOrder = commonPostOrder;
        _profitService = profitService;
        _internalApi = internalApi;
        _hostingEnvironment = hostingEnvironment;
        _commonUserService = commonUserService;
        _ndnApi = ndnApi;
        _config = config.Value;
        _ndnService = ndnService;
    }

    /// <summary>
    /// 交付过保到期检测
    /// </summary>
    public async void DeliveryBountyCheck(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            //加锁
            var lockerKey = $"postbounty:DeliveryBountyCheck";
            using var locker = MyRedis.TryLock(lockerKey, 100);
            if (locker == null)
                return;

            using var _context = _contextFactory.CreateDbContext();

            var bountyStages = _context.Post_Bounty_Stage
            .Include(i => i.Post_Bounty)
            .Where(x => x.Status == BountyStageStatus.交付中 // && x.Post_Bounty.Status == BountyStatus.交付中
            && x.GuaranteeStatus == GuaranteeStatus.未过保 && x.Post_Bounty.GuaranteeStartDate.HasValue
            && DateTime.Now >= x.Post_Bounty.GuaranteeStartDate.Value.AddDays(x.GuaranteeDays))
            .OrderByDescending(o => o.Post_Bounty.CreatedTime)
            .Take(50).ToList();

            if (bountyStages.Count == 0)
                return;

            foreach (var item in bountyStages)
            {
                item.GuaranteeStatus = GuaranteeStatus.过保;
                item.Post_Bounty.GuaranteeStatus = GuaranteeStatus.过保;

                if (item.Post_Bounty.RewardType == PostRewardType.单次结算)
                {
                    item.Status = BountyStageStatus.交付完成;
                    // 单次结算直接生成结算单
                    var salesSettle = new Post_Settlement
                    {
                        BountyStageId = item.Id,
                        // Status = PostSettleStatus.结算中,
                        ApprovalStatus = PostSettleApprovalStatus.项目经理审核
                    };
                    _context.Add(salesSettle);

                    item.SettlementEndTime = DateTime.Now;
                }
            }

            _context.SaveChanges();

            //var msgs = projectTeambountys.Select(s => new MsgNotifyModel
            //{
            //    Type = MsgNotifyType.协同交付,
            //    EventTime = DateTime.Now,
            //    TeamHrId = s.TeamHrId,
            //    HrId = s.HrId,
            //    Data = new MsgNotifyTeamDelivery
            //    {
            //        TeamPostId = s.TeamPostId,
            //        TeamProjectId = s.TeamProjectId,
            //        TeamHrId = s.TeamHrId,
            //        HrId = s.HrId,
            //        PostName = s.PostName,
            //        SeekerName = s.SeekerName,
            //        Money = s.SettlementMoney,
            //        Status = s.Status,
            //        RecruitId = s.RecruitId
            //    }
            //}).ToArray();

            //通知消息
            //MsgHelper.SendMsgNotify(msgs);

            // 今日交付
            var today = DateTime.Today;
            foreach (var item in bountyStages)
            {
                var todayKey = $"{RedisKey.HrStatBehavior.DailyKey}{DateTime.Today.ToString("yyyyMMdd")}";
                var todayBountyKey = $"{RedisKey.HrStatBehavior.DeliveryBounty}{item.Post_Bounty.TeamHrId}";
                MyRedis.Client.HIncrByFloat(todayKey, todayBountyKey, item.Money);

                //历史交付
                MyRedis.Client.HIncrByFloat(RedisKey.HrStatBehavior.FullKey, $"{RedisKey.HrStatBehavior.DeliveryBounty}{item.Post_Bounty.TeamHrId}", item.Money);
            }

            await Task.FromResult(1);
        }
        catch (Exception e)
        {
            _logger.Error("入职过保到期检测出错", Tools.GetErrMsg(e));
        }
    }

    /// <summary>
    /// 长期结算生成结算单
    /// </summary>
    public async void GenerateSettlementBill(CancellationToken cancel)
    {
        await Task.FromResult(1);
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            //加锁
            var lockerKey = $"postbounty:GenerateSettlementBill";
            using var locker = MyRedis.TryLock(lockerKey, 100);
            if (locker == null)
                return;

            // 结算上个月的，取上个月最后一天(本月第一天减1天)
            var maxEndTime = DateTime.Today.AddDays(-DateTime.Today.Day);

            using var _context = _contextFactory.CreateDbContext();

            var bountyStages = _context.Post_Bounty_Stage
            .Include(i => i.Post_Bounty)
            .Where(x => x.Post_Bounty.RewardType == PostRewardType.长期结算 && x.Status == BountyStageStatus.交付中
            && x.GuaranteeStatus == GuaranteeStatus.过保 && (x.SettlementEndTime == null || x.SettlementEndTime < maxEndTime))
            .OrderByDescending(o => o.Post_Bounty.CreatedTime)
            .Take(50).ToList();

            if (bountyStages.Count == 0)
                return;

            // 查询所有相关的结算单数据，提取到循环外部
            var settlementData = _context.Post_Settlement
                .Where(w => bountyStages.Select(b => b.Id).Contains(w.BountyStageId))
                .GroupBy(w => w.BountyStageId)
                .ToDictionary(g => g.Key, g => g.Max(m => m.EndTime));

            foreach (var item in bountyStages)
            {
                // 查询结算单，如果没有，则生成结算单
                // 从内存中的数据获取结算单的最大结束时间
                settlementData.TryGetValue(item.Id, out var lastEndTime);
                lastEndTime = lastEndTime?.Date;

                DateTime? finalEndTime = null;
                // 如果item.SettlementEndTime.HasValue有值，finalEndTime取他和lastMonth的最小值
                if (item.Post_Bounty.PaymentDuration.HasValue && item.Post_Bounty.PaymentDuration > 0)
                {
                    var et = item.Post_Bounty.GuaranteeStartDate?.AddDays(item.GuaranteeDays + item.Post_Bounty.PaymentDuration.Value).Date;
                    if (!et.HasValue)
                    {
                        _logger.Error("GenerateSettlementBill", "长期结算生成结算单", $"入职过保时间为空，订单号：{item.Id}");
                        continue;
                    }

                    finalEndTime = et.Value;

                    // 如果et大于上个月最后一天，则取上个月最后一天
                    maxEndTime = et.Value < maxEndTime ? et.Value : maxEndTime;
                }

                if (lastEndTime != null && lastEndTime >= maxEndTime)
                    continue;

                // 生成结算单，按照自然月结算，如果settlementEndTime为null，则开始时间为入职过保时间，
                // 如果settlementEndTime不为null，则开始时间为上次结算单结束时间+1天

                var startTime = lastEndTime == null ? (item.Post_Bounty.GuaranteeStartDate?.AddDays(item.GuaranteeDays)) : lastEndTime.Value.AddDays(1);
                if (!startTime.HasValue)
                {
                    _logger.Error("GenerateSettlementBill", "长期结算生成结算单", $"入职过保时间为空，订单号：{item.Id}");
                    continue;
                }

                startTime = startTime.Value.Date;

                // endTime为startTime月的最后一天，注意，startTime并不是1号

                // 是否是最后一次结算
                var isLastSettlement = false;
                while (startTime < maxEndTime)
                {
                    var endTime = startTime.Value.AddMonths(1).AddDays(-startTime.Value.Day);
                    // 确保 endTime 不超过 finalEndTime
                    if (endTime > maxEndTime)
                        endTime = maxEndTime;

                    var settlement = new Post_Settlement
                    {
                        BountyStageId = item.Id,
                        StartTime = startTime,
                        EndTime = endTime,
                        // Status = PostSettleStatus.结算中,
                        ApprovalStatus = PostSettleApprovalStatus.项目经理审核
                    };

                    // 更新阶段的结算截止时间
                    item.SettlementEndTime = endTime;

                    // 保存结算单到数据库
                    _context.Add(settlement);

                    // 更新下一个结算周期
                    startTime = endTime.AddDays(1);
                }

                if (finalEndTime.HasValue && item.SettlementEndTime.HasValue && item.SettlementEndTime.Value >= finalEndTime.Value)
                    isLastSettlement = true;

                // 如果结算到期，或者主订单归档并且归档日期小于本次结算截止日期，则标记上结束
                if (isLastSettlement || (item.Post_Bounty.Status == BountyStatus.结束
                    && item.Post_Bounty.FileAwayDate.HasValue && item.Post_Bounty.FileAwayDate <= maxEndTime))
                {
                    // 如果是最后一次结算，或者订单已结束（结束后仍然需要尝试结算），标记上完成
                    item.Status = BountyStageStatus.交付完成;
                }
            }

            _context.SaveChanges();
            #region
            // 如果所有的结算单都完成了，标记上完成
            // var ids = bountyStages.Select(s => s.BountyId).Distinct();

            //// 如果所有的结算单都完成了，标记上完成
            //_context.Post_Bounty.Where(x => x.Status == BountyStatus.交付中
            // && !_context.Post_Bounty_Stage.Any(a => a.Status == BountyStageStatus.交付中))
            // .ExecuteUpdate(x => x.SetProperty(s => s.Status, s => BountyStatus.结束));

            //// 如果订单未过保，但是主订单已经结束，标记上完成
            //_context.Post_Bounty_Stage.Where(x => x.Post_Bounty.Status == BountyStatus.结束
            //&& x.Status == BountyStageStatus.交付中 && x.GuaranteeStatus < GuaranteeStatus.过保)
            //.ExecuteUpdate(x => x.SetProperty(s => s.Status, s => BountyStageStatus.交付完成));
            #endregion
            await Task.FromResult(1);
        }
        catch (Exception e)
        {
            _logger.Error("长期结算生成结算单出错", Tools.GetErrMsg(e));
        }
    }
    /// <summary>
    /// 结算单交付完成检测
    /// </summary>
    /// <param name="cancel"></param>
    public async void PostBountyUpdateStatus(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            using var _context = _contextFactory.CreateDbContext();

            var bountyStages = _context.Post_Bounty_Stage
                .Include(i => i.Post_Bounty)
                .Where(x => x.Post_Bounty.RewardType == PostRewardType.长期结算
                        && x.Status == BountyStageStatus.交付中
                        && x.GuaranteeStatus == GuaranteeStatus.过保)
                .OrderByDescending(o => o.Post_Bounty.CreatedTime)
                .Take(50).ToList();

            if (bountyStages.Count == 0)
                return;

            // 如果所有的结算单都完成了，标记上完成
            _context.Post_Bounty.Where(x => x.Status == BountyStatus.交付中
             && !_context.Post_Bounty_Stage.Any(a => a.Status == BountyStageStatus.交付中))
             .ExecuteUpdate(x => x.SetProperty(s => s.Status, s => BountyStatus.结束));

            // 如果订单未过保，但是主订单已经结束，标记上完成
            _context.Post_Bounty_Stage.Where(x => x.Post_Bounty.Status == BountyStatus.结束
            && x.Status == BountyStageStatus.交付中 && x.GuaranteeStatus < GuaranteeStatus.过保)
            .ExecuteUpdate(x => x.SetProperty(s => s.Status, s => BountyStageStatus.交付完成));

            await Task.Delay(500);
        }
        catch (Exception e)
        {
            _logger.Error("岗位结算单状态更新出错", Tools.GetErrMsg(e));
        }
    }

    /// <summary>
    /// 同步数字诺亚帐套
    /// </summary>
    public async Task SyncNdnBooks(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            //加锁
            var lockerKey = $"projectbounty:SyncNdnBooks";
            using var locker = MyRedis.TryLock(lockerKey, 300);
            if (locker == null)
                return;

            var _context = _contextFactory.CreateDbContext();

            //查询本地帐套
            var books = _context.Ndn_Books.ToList();

            // 查询数字诺亚帐套
            var ndnBooks = await _ndnApi.GetBooks(new GetBooks { });

            // 对比本地帐套，如果不存在，则添加帐套
            foreach (var item in ndnBooks)
            {
                if (books.Any(a => a.BookCode == item.bookCode))
                    continue;

                if (string.IsNullOrWhiteSpace(item.bookCode))
                    continue;

                _context.Add(new Ndn_Books
                {
                    BookCode = item.bookCode,
                    BookName = item.bookName ?? string.Empty,
                });
            }

            _context.SaveChanges();
        }
        catch (Exception e)
        {
            _logger.Error("同步数字诺亚帐套出错", Tools.GetErrMsg(e));
        }
    }

    // /// <summary>
    // /// 入职过保到期检测
    // /// </summary>
    // public async void InductionBountyCheck(CancellationToken cancel)
    // {
    //     try
    //     {
    //         if (cancel.IsCancellationRequested)
    //             return;

    //         //加锁
    //         var lockerKey = $"projectbounty:inductioncheck";
    //         using var locker = MyRedis.TryLock(lockerKey, 100);
    //         if (locker == null)
    //             return;

    //         using var _context = _contextFactory.CreateDbContext();

    //         var projectTeambountys = _context.Project_Teambounty
    //         .Where(x => x.PaymentNode == ProjectPaymentNode.入职过保 && x.Status == BountyStatus.交付中
    //         && (x.Recruit.Status == RecruitStatus.Induction || x.Recruit.Status == RecruitStatus.Contract)
    //         && x.Recruit.InductionTime.HasValue && DateTime.Now >= x.Recruit.InductionTime.Value.AddDays(x.PaymentDays ?? 0))
    //         .OrderByDescending(o => o.CreatedTime)
    //         .Take(100).ToList();

    //         if (projectTeambountys.Count == 0)
    //             return;

    //         foreach (var item in projectTeambountys)
    //         {
    //             _profitService.PaySuccess(item.Id);
    //             _logger.Info("InductionBountyCheck", "入职过保到期检测", $"入职过保交付成功，订单号：{item.Id}");
    //             // todo：请求结算接口，入队列 - 先入先出 (rpush + lpop) - 弃用
    //             //MyRedis.Client.RPush(SubscriptionKey.ProjectTeamBountySettle, item.Id);
    //             //await Task.Delay(10);
    //         }

    //         //_context.SaveChanges();

    //         //var msgs = projectTeambountys.Select(s => new MsgNotifyModel
    //         //{
    //         //    Type = MsgNotifyType.协同交付,
    //         //    EventTime = DateTime.Now,
    //         //    TeamHrId = s.TeamHrId,
    //         //    HrId = s.HrId,
    //         //    Data = new MsgNotifyTeamDelivery
    //         //    {
    //         //        TeamPostId = s.TeamPostId,
    //         //        TeamProjectId = s.TeamProjectId,
    //         //        TeamHrId = s.TeamHrId,
    //         //        HrId = s.HrId,
    //         //        PostName = s.PostName,
    //         //        SeekerName = s.SeekerName,
    //         //        Money = s.SettlementMoney,
    //         //        Status = s.Status,
    //         //        RecruitId = s.RecruitId
    //         //    }
    //         //}).ToArray();

    //         //通知消息
    //         //MsgHelper.SendMsgNotify(msgs);

    //         // 今日交付
    //         var today = DateTime.Today;
    //         foreach (var item in projectTeambountys)
    //         {
    //             var todayKey = $"{RedisKey.HrStatBehavior.DailyKey}{DateTime.Today.ToString("yyyyMMdd")}";
    //             var todayBountyKey = $"{RedisKey.HrStatBehavior.DeliveryBounty}{item.TeamHrId}";
    //             MyRedis.Client.HIncrByFloat(todayKey, todayBountyKey, item.Money ?? 0);

    //             //历史交付
    //             MyRedis.Client.HIncrByFloat(RedisKey.HrStatBehavior.FullKey, $"{RedisKey.HrStatBehavior.DeliveryBounty}{item.TeamHrId}", item.Money ?? 0);
    //         }

    //         await Task.FromResult(1);
    //     }
    //     catch (Exception e)
    //     {
    //         _logger.Error("入职过保到期检测出错", Tools.GetErrMsg(e));
    //     }
    // }

    // /// <summary>
    // /// 分润任务 - 生成转账报销合同
    // /// </summary>
    // public async Task ProfitSharingTask(CancellationToken cancel)
    // {
    //     try
    //     {
    //         if (cancel.IsCancellationRequested)
    //             return;

    //         //加锁
    //         var lockerKey = $"projectbounty:profitsharing";
    //         using var locker = MyRedis.TryLock(lockerKey, 10);
    //         if (locker == null)
    //             return;

    //         var cn = "郭思诚";
    //         var cnphone = "18531006611";

    //         using var _context = _contextFactory.CreateDbContext();

    //         // 查找最近待提交的转账报销
    //         var transfer = _context.Ndn_Project_Transfer_Details
    //         .Where(x => x.Status == NdnAuditStatus.待提交 && string.IsNullOrEmpty(x.ContractUrl))
    //         .Include(i => i.Project_From)
    //         .Include(i => i.Project_To)
    //         .OrderBy(o => o.CreatedTime)
    //         .Take(100).ToList();

    //         if (transfer.Count == 0)
    //             return;

    //         var fromBookIds = transfer.Select(s => s.Project_From.BookCode).Distinct().ToList();
    //         var toBookIds = transfer.Select(s => s.Project_To.BookCode).Distinct().ToList();

    //         var bookIds = fromBookIds.Union(toBookIds).Distinct().ToList();

    //         var books = _context.Ndn_Books.Where(w => bookIds.Contains(w.BookCode)).ToList();

    //         foreach (var item in transfer)
    //         {
    //             var fromBook = books.FirstOrDefault(f => f.BookCode == item.Project_From.BookCode);
    //             var toBook = books.FirstOrDefault(f => f.BookCode == item.Project_To.BookCode);

    //             if (fromBook == null || string.IsNullOrEmpty(fromBook.InvoiceInfo?.Address)
    //             || string.IsNullOrEmpty(fromBook.ContractSeal) || string.IsNullOrEmpty(fromBook.BookName))
    //             {
    //                 _logger.Error("ProfitSharingTask", "分润任务", $"转账报销-转账方账套不存在或不完整，转账方账套：{item.Project_From.BookCode}");
    //                 continue;
    //             }

    //             if (toBook == null || string.IsNullOrEmpty(toBook.InvoiceInfo?.Address)
    //             || string.IsNullOrEmpty(toBook.BookName) || string.IsNullOrEmpty(toBook.ContractSeal)
    //             || string.IsNullOrEmpty(toBook.InvoiceInfo?.Bank) || string.IsNullOrEmpty(toBook.InvoiceInfo?.BankAccount))
    //             {
    //                 _logger.Error("ProfitSharingTask", "分润任务", $"转账报销-收款方账套不存在或不完整，收款方账套：{item.Project_To.BookCode}");
    //                 continue;
    //             }

    //             var model = new Config.CommonModel.QuestPDF.ContractModel
    //             {
    //                 BusinessTableId = item.Id,
    //                 FileName = $"转账报销合同",
    //                 ContractNumber = item.Id,
    //                 SellerCompanyName = item.Project_To.BookName,
    //                 SellerCompanyAddress = toBook.InvoiceInfo.Address,
    //                 SellerCompanyContacter = cn,
    //                 SellerCompanyContacterNumber = cnphone,
    //                 SellerCompanyImageUrl = toBook.ContractSeal!,
    //                 AccountName = toBook.BookName,
    //                 AccountAddress = toBook.InvoiceInfo.Bank!,
    //                 Account = toBook.InvoiceInfo.BankAccount!,
    //                 CustomerCompanyName = item.Project_From.BookName,
    //                 CustomerCompanyAddress = fromBook.InvoiceInfo.Address,
    //                 CustomerCompanyContacter = cn,
    //                 CustomerCompanyContacterNumber = cnphone,
    //                 CustomerCompanyImageUrl = fromBook.ContractSeal!
    //             };

    //             var dts = DateTime.Now.ToString("yyyy-MM");

    //             var items = _context.Ndn_Service_Bonus.Where(x => x.TransferId == item.TransferId)
    //             .Select(s => new
    //             {
    //                 OrderNumber = s.Id,
    //                 ProjectName = s.ProjectName ?? string.Empty,
    //                 BountyName = s.Project_Settlement.Project_Teambounty.PaymentNode.GetDescription(),
    //                 BountyAmount = s.Project_Settlement.SettlementMoney ?? 0,
    //                 BountyTime = dts,
    //                 s.Project_Settlement.Project_Teambounty.TeamHrName,
    //                 s.Project_Settlement.Project_Teambounty.ChannelHrName,
    //                 s.Project_Settlement.Type,
    //                 s.Project_Settlement.Project_Teambounty.Project.AutoId
    //             }).ToList();

    //             model.OrderInfo.Items = items
    //             .Select(s => new Config.CommonModel.QuestPDF.ContractOrderItem
    //             {
    //                 OrderNumber = s.OrderNumber,
    //                 ProjectName = $"{s.ProjectName}-{Constants.ProjectIdPre + s.AutoId.ToString().PadLeft(6, '0')}-{(s.Type == SettleType.协同 ? s.TeamHrName : s.ChannelHrName)}",
    //                 BountyName = s.BountyName,
    //                 BountyAmount = s.BountyAmount,
    //                 BountyTime = dts
    //             }).ToList();

    //             var ossPath = string.Empty;
    //             await Task.Run(() =>
    //             {
    //                 ossPath = _commonPDFService.GenerateContractPdfToOss(model);
    //             });

    //             if (string.IsNullOrWhiteSpace(ossPath))
    //                 continue;

    //             item.ContractUrl = ossPath;

    //             await Task.Delay(50);
    //         }

    //         _context.SaveChanges();

    //     }
    //     catch (Exception e)
    //     {
    //         _logger.Error("入职过保到期检测出错", Tools.GetErrMsg(e));
    //     }
    // }

    // /// <summary>
    // /// 分润任务 - 计算并生成服务奖金相关
    // /// </summary>
    // public async Task ProfitSharingTask2(CancellationToken cancel)
    // {
    //     try
    //     {
    //         if (cancel.IsCancellationRequested)
    //             return;

    //         //当前月1号
    //         var firstDay = DateTime.Today.AddDays(1 - DateTime.Today.Day);
    //         if (DateTime.Now <= firstDay.AddHours(1) || DateTime.Now >= firstDay.AddHours(23))
    //             return;

    //         //加锁
    //         var lockerKey = $"projectbounty:profitsharing2";
    //         using var locker = MyRedis.TryLock(lockerKey, 10);
    //         if (locker == null)
    //             return;

    //         //今天是否执行过
    //         if (!MyRedis.Client.SetNx($"projectbounty:profitsharing2-{DateTime.Today.ToString("yyyyMMdd")}", "1", TimeSpan.FromDays(3)))
    //             return;

    //         using var _context = _contextFactory.CreateDbContext();

    //         //随机取10个
    //         var settlements = _context.Project_Settlement.Where(x => x.Status == SettleStatus.结算中
    //         && !x.IsServiceBonusInitiated && x.Type == SettleType.协同 && !string.IsNullOrEmpty(x.Project_Teambounty.Project.NuoId))
    //         .Select(s => s.Project_Teambounty.Project.NuoId).Distinct().OrderBy(o => EF.Functions.Random())
    //         .Take(100);

    //         foreach (var item in settlements)
    //         {
    //             await _ndnService.GenerateServiceBonus(new CreateNdnServiceBonus
    //             {
    //                 projectCode = item
    //             });
    //             await Task.Delay(100);
    //         }
    //     }
    //     catch (Exception e)
    //     {
    //         _logger.Error("入职过保到期检测出错", Tools.GetErrMsg(e));
    //     }
    // }

    // /// <summary>
    // /// 分润任务 - 自动处理待办
    // /// </summary>
    // public async Task ProfitSharingTask3(CancellationToken cancel)
    // {
    //     try
    //     {
    //         if (cancel.IsCancellationRequested)
    //             return;

    //         //加锁
    //         var lockerKey = $"projectbounty:profitsharing3";
    //         using var locker = MyRedis.TryLock(lockerKey, 300);
    //         if (locker == null)
    //             return;

    //         var _context = _contextFactory.CreateDbContext();

    //         //查找最近的开票待办
    //         var invoices = _context.Ndn_Project_Invoice
    //         .Where(x => x.Status == NdnAuditStatus.待提交)
    //         .OrderByDescending(o => o.Id).Take(50).ToList();

    //         var invoicesIds = invoices.Select(s => s.Id).ToList();
    //         var todo1 = _context.Ndn_Todo.Where(x => invoicesIds.Contains(x.RelatedEntityId) && x.RelatedEntityType == NdnTodoType.开发票)
    //         .Select(s => s.Id).ToList();

    //         foreach (var item in todo1)
    //         {
    //             try
    //             {
    //                 await _ndnService.ProcessAutoTasks(item);
    //             }
    //             catch (BadRequestException) { }
    //             catch (Exception e)
    //             {
    //                 _logger.Error("ProfitSharingTask3", $"开票待办处理:{item}", Tools.GetErrMsg(e));
    //             }

    //             await Task.Delay(100);
    //         }

    //         //查找最近的转账待办
    //         var transfers = _context.Ndn_Project_Transfer_Details
    //         .Where(x => x.Status == NdnAuditStatus.待提交 && !string.IsNullOrEmpty(x.ContractUrl))
    //         .OrderByDescending(o => o.Id).Take(50).ToList();

    //         var transfersIds = transfers.Select(s => s.Id).ToList();
    //         var todo2 = _context.Ndn_Todo.Where(x => transfersIds.Contains(x.RelatedEntityId) && x.RelatedEntityType == NdnTodoType.转账报销)
    //         .Select(s => s.Id).ToList();

    //         foreach (var item in todo2)
    //         {
    //             try
    //             {
    //                 await _ndnService.ProcessAutoTasks(item);
    //             }
    //             catch (BadRequestException) { }
    //             catch (Exception e)
    //             {
    //                 _logger.Error("ProfitSharingTask3", $"转账待办处理:{item}", Tools.GetErrMsg(e));
    //             }

    //             await Task.Delay(100);
    //         }
    //     }
    //     catch (Exception e)
    //     {
    //         _logger.Error("入职过保到期检测出错", Tools.GetErrMsg(e));
    //     }
    // }

    // /// <summary>
    // /// 分润任务 - 同步数字诺亚帐套, 如果有新帐套并生成待办
    // /// </summary>
    // public async Task ProfitSharingTask4(CancellationToken cancel)
    // {
    //     try
    //     {
    //         if (cancel.IsCancellationRequested)
    //             return;

    //         //加锁
    //         var lockerKey = $"projectbounty:profitsharing4";
    //         using var locker = MyRedis.TryLock(lockerKey, 300);
    //         if (locker == null)
    //             return;

    //         var _context = _contextFactory.CreateDbContext();

    //         //查询本地帐套
    //         var books = _context.Ndn_Books.ToList();

    //         // 查询数字诺亚帐套
    //         var ndnBooks = await _ndnApi.GetBooks(new GetBooks { });

    //         var userBooks = MyRedis.Client.HGetAll<Config.CommonModel.UserBookRelation>(RedisKey.UserHrBookCodeKey);

    //         // 查询所有帐套
    //         var nuoBooks = userBooks.Select(s => s.Value.BookCode).Distinct().ToList();

    //         // 对比本地帐套，如果不存在，则添加帐套
    //         foreach (var item in nuoBooks)
    //         {
    //             if (books.Any(a => a.BookCode == item))
    //                 continue;

    //             if (string.IsNullOrWhiteSpace(item))
    //                 continue;

    //             _context.Ndn_Books.Add(new Ndn_Books
    //             {
    //                 BookCode = item,
    //                 BookName = ndnBooks.FirstOrDefault(x => x.bookCode == item)?.bookName ?? string.Empty
    //             });
    //         }

    //         // 查询本地数字诺亚项目
    //         var projects = _context.Ndn_Project.Where(x => x.Type == NdnProjType.其他主体).ToList();

    //         // 如果项目中不存在帐套，则生成待办
    //         foreach (var item in nuoBooks)
    //         {
    //             if (item == _config.Ndn!.NuoPinBookCode || projects.Any(a => a.BookCode == item))
    //                 continue;

    //             var ndnBook = ndnBooks.FirstOrDefault(x => x.bookCode == item);

    //             if (string.IsNullOrWhiteSpace(ndnBook?.bookName))
    //                 continue;

    //             //创建数字诺亚项目
    //             var pp = new Ndn_Project
    //             {
    //                 BookCode = _config.Ndn!.NuoPinBookCode!,
    //                 BookName = "河北诺聘网络科技有限公司",
    //                 ClientBookCode = item,
    //                 ClientBookName = ndnBook?.bookName,
    //                 ProjectName = $"河北诺聘网络科技有限公司-{ndnBook?.bookName}",
    //                 Status = NdnProjStatus.待办,
    //                 Type = NdnProjType.诺聘主体
    //             };

    //             //生成待办
    //             var todo = new Ndn_Todo
    //             {
    //                 Title = pp.ProjectName,
    //                 Status = NdnTodoStatus.待办,
    //                 RelatedEntityType = NdnTodoType.创建数字诺亚项目,
    //                 RelatedEntityId = pp.Id
    //             };

    //             //创建数字诺亚项目
    //             var pp2 = new Ndn_Project
    //             {
    //                 BookCode = item,
    //                 BookName = ndnBook?.bookName!,
    //                 ClientBookCode = _config.Ndn.NuoPinBookCode,
    //                 ClientBookName = "河北诺聘网络科技有限公司",
    //                 ProjectName = $"{ndnBook?.bookName}-河北诺聘网络科技有限公司",
    //                 Status = NdnProjStatus.待办,
    //                 Type = NdnProjType.其他主体
    //             };

    //             //生成待办
    //             var todo2 = new Ndn_Todo
    //             {
    //                 Title = pp2.ProjectName,
    //                 Status = NdnTodoStatus.待办,
    //                 RelatedEntityType = NdnTodoType.创建数字诺亚项目,
    //                 RelatedEntityId = pp2.Id
    //             };
    //             _context.Add(pp);
    //             _context.Add(todo);
    //             _context.Add(pp2);
    //             _context.Add(todo2);
    //         }

    //         _context.SaveChanges();
    //     }
    //     catch (Exception e)
    //     {
    //         _logger.Error("同步数字诺亚帐套并生成待办出错", Tools.GetErrMsg(e));
    //     }
    // }

    // /// <summary>
    // /// 简历交付倒计时
    // /// </summary>
    // /// <param name="cancel"></param>
    // public async void ResumeBountyCheck(CancellationToken cancel)
    // {
    //     try
    //     {
    //         if (cancel.IsCancellationRequested)
    //             return;

    //         //加锁
    //         var lockerKey = $"projectbounty:resumecheck";
    //         using var locker = MyRedis.TryLock(lockerKey, 100);
    //         if (locker == null)
    //             return;

    //         using var _context = _contextFactory.CreateDbContext();
    //         var projectTeambountys = _context.Project_Teambounty
    //         .Where(x => x.PaymentNode == ProjectPaymentNode.简历交付 && x.Status == BountyStatus.交付中
    //         && x.Recruit.Recruit_Interviewer_Screen.Any(a => DateTime.Now >= a.CreatedTime.AddDays(Constants.ResumeDeliveryDays))) // 倒计时7天 - 面试时间≈面试官筛选创建时间
    //         // && DateTime.Now >= x.CreatedTime.AddDays(7)) // 倒计时7天 - 订单创建时间≈招聘流程创建时间
    //         .OrderByDescending(o => o.CreatedTime)
    //         .Take(100).ToList();

    //         if (projectTeambountys.Count == 0)
    //             return;

    //         foreach (var item in projectTeambountys)
    //         {
    //             _profitService.PaySuccess(item.Id);
    //             _logger.Info("ResumeBountyCheck", "简历交付倒计时7天检测", $"简历交付交付成功，订单号：{item.Id}");
    //         }

    //         // 今日交付
    //         var today = DateTime.Today;
    //         foreach (var item in projectTeambountys)
    //         {
    //             var todayKey = $"{RedisKey.HrStatBehavior.DailyKey}{DateTime.Today.ToString("yyyyMMdd")}";
    //             var todayBountyKey = $"{RedisKey.HrStatBehavior.DeliveryBounty}{item.TeamHrId}";
    //             MyRedis.Client.HIncrByFloat(todayKey, todayBountyKey, item.Money ?? 0);

    //             //历史交付
    //             MyRedis.Client.HIncrByFloat(RedisKey.HrStatBehavior.FullKey, $"{RedisKey.HrStatBehavior.DeliveryBounty}{item.TeamHrId}", item.Money ?? 0);
    //         }
    //         await Task.FromResult(1);
    //     }
    //     catch (Exception e)
    //     {
    //         _logger.Error("简历交付到期检测出错", Tools.GetErrMsg(e));
    //     }
    // }

    // /// <summary>
    // /// 到面交付倒计时
    // /// </summary>
    // /// <param name="cancel"></param>
    // public async void InterviewBountyCheck(CancellationToken cancel)
    // {
    //     try
    //     {
    //         if (cancel.IsCancellationRequested)
    //             return;

    //         //加锁
    //         var lockerKey = $"projectbounty:interviewcheck";
    //         using var locker = MyRedis.TryLock(lockerKey, 100);
    //         if (locker == null)
    //             return;

    //         using var _context = _contextFactory.CreateDbContext();
    //         var projectTeambountys = _context.Project_Teambounty
    //         .Where(x => x.PaymentNode == ProjectPaymentNode.到面交付 && x.Status == BountyStatus.交付中
    //         && DateTime.Now >= x.Recruit.Recruit_Interview.FirstOrDefault(f => f.Outcome == RecruitInterviewOutcome.Waiting)!.InterviewTime.AddDays(Constants.ResumeDeliveryDays)) // 倒计时7天 - 面试时间
    //         .OrderByDescending(o => o.CreatedTime)
    //         .Take(100).ToList();

    //         if (projectTeambountys.Count == 0)
    //             return;

    //         foreach (var item in projectTeambountys)
    //         {
    //             _profitService.PaySuccess(item.Id);
    //             _logger.Info("InterviewBountyCheck", "到面交付倒计时7天检测", $"到面交付交付成功，订单号：{item.Id}");
    //         }

    //         // 今日交付
    //         var today = DateTime.Today;
    //         foreach (var item in projectTeambountys)
    //         {
    //             var todayKey = $"{RedisKey.HrStatBehavior.DailyKey}{DateTime.Today.ToString("yyyyMMdd")}";
    //             var todayBountyKey = $"{RedisKey.HrStatBehavior.DeliveryBounty}{item.TeamHrId}";
    //             MyRedis.Client.HIncrByFloat(todayKey, todayBountyKey, item.Money ?? 0);

    //             //历史交付
    //             MyRedis.Client.HIncrByFloat(RedisKey.HrStatBehavior.FullKey, $"{RedisKey.HrStatBehavior.DeliveryBounty}{item.TeamHrId}", item.Money ?? 0);
    //         }
    //         await Task.FromResult(1);
    //     }
    //     catch (Exception e)
    //     {
    //         _logger.Error("到面交付到期检测出错", Tools.GetErrMsg(e));
    //     }
    // }

    // /// <summary>
    // /// 按天入职过保检测 - 首次
    // /// </summary>
    // public async void MonthlyBountyCheck(CancellationToken cancel)
    // {
    //     try
    //     {
    //         if (cancel.IsCancellationRequested)
    //             return;

    //         //加锁
    //         var lockerKey = $"projectbounty:monthlycheck";
    //         using var locker = MyRedis.TryLock(lockerKey, 100);
    //         if (locker == null)
    //             return;

    //         using var _context = _contextFactory.CreateDbContext();

    //         var projectTeambountys = _context.Project_Teambounty
    //         .Where(x => x.PaymentNode == ProjectPaymentNode.按天入职过保 && x.Status == BountyStatus.交付中
    //         && (x.Recruit.Status == RecruitStatus.Induction || x.Recruit.Status == RecruitStatus.Contract)
    //         && x.Recruit.InductionTime.HasValue && DateTime.Now >= x.Recruit.InductionTime.Value.AddDays(x.PaymentDays ?? 0))
    //         .OrderByDescending(o => o.CreatedTime)
    //         .Take(100).ToList();

    //         if (projectTeambountys.Count == 0)
    //             return;

    //         foreach (var item in projectTeambountys)
    //         {
    //             _profitService.PaySuccess(item.Id);
    //             _logger.Info("MonthlyInductionBountyCheck", "按天入职过保到期检测", $"按天入职过保交付成功，订单号：{item.Id}");
    //         }

    //         //今日交付
    //         var today = DateTime.Today;
    //         foreach (var item in projectTeambountys)
    //         {
    //             var todayKey = $"{RedisKey.HrStatBehavior.DailyKey}{DateTime.Today.ToString("yyyyMMdd")}";
    //             var todayBountyKey = $"{RedisKey.HrStatBehavior.DeliveryBounty}{item.TeamHrId}";
    //             MyRedis.Client.HIncrByFloat(todayKey, todayBountyKey, item.Money ?? 0);

    //             //历史交付
    //             MyRedis.Client.HIncrByFloat(RedisKey.HrStatBehavior.FullKey, $"{RedisKey.HrStatBehavior.DeliveryBounty}{item.TeamHrId}", item.Money ?? 0);
    //         }

    //         await Task.FromResult(1);
    //     }
    //     catch (Exception e)
    //     {
    //         _logger.Error("按天入职过保到期检测出错", Tools.GetErrMsg(e));
    //     }
    // }

    // /// <summary>
    // /// 按天入职过保检测 - 每日
    // /// </summary>
    // public async void MonthlyBountyCheckByEveryDay(CancellationToken cancel)
    // {
    //     await Task.FromResult(1);
    //     // string id = string.Empty;
    //     // try
    //     // {
    //     //     if (cancel.IsCancellationRequested)
    //     //         return;

    //     //     // 每天1点到2点执行
    //     //     var today = DateTime.Today;
    //     //     if (DateTime.Now >= today.AddHours(2) || DateTime.Now <= today.AddHours(1))
    //     //         return;

    //     //     //加锁
    //     //     var lockerKey = $"projectbounty:monthlycheckeveryday";
    //     //     using var locker = MyRedis.TryLock(lockerKey, 1000);
    //     //     if (locker == null)
    //     //         return;

    //     //     using var _context = _contextFactory.CreateDbContext();

    //     //     var projectTeambountys = _context.Project_Teambounty
    //     //     .Where(x => x.PaymentNode == ProjectPaymentNode.按天入职过保 && x.Status == BountyStatus.交付成功
    //     //     && x.UpdatedTime.Date == today.AddDays(-1) && x.RecruitId!.IndexOf("-") < 0)
    //     //     .OrderByDescending(o => o.CreatedTime)
    //     //     .Take(100).Select(s => new { s.Recruit.Status, bounty = s }).ToList();

    //     //     if (projectTeambountys.Count == 0)
    //     //         return;

    //     //     List<Project_Teambounty> newBounties = new();
    //     //     foreach (var item in projectTeambountys)
    //     //     {
    //     //         //_context.Attach(item.bounty);

    //     //         var newBounty = GetNewObject(item.bounty); // JsonConvert.DeserializeObject<Project_Teambounty>(JsonConvert.SerializeObject(item.bounty));

    //     //         var postInfo = _context.Post.FirstOrDefault(f => f.PostId == newBounty.PostId);
    //     //         // 判断是否离职
    //     //         if (item.Status == RecruitStatus.FileAway)
    //     //         {
    //     //             newBounty!.Status = BountyStatus.交付失败;
    //     //             newBounty!.Description = "按天入职过保-每日订单归档";
    //     //             _logger.Info("MonthlyInductionBountyCheckByEveryDay", "按天入职过保到期检测-每日", $"按天入职过保交付失败，订单号：{item.bounty.Id}");
    //     //         }
    //     //         else
    //     //         {
    //     //             // 冻结金额，冻结失败，交付失败
    //     //             var nuoId = _context.Project.Where(w => w.ProjectId == newBounty.ProjectId).Select(s => s.NuoId).FirstOrDefault();
    //     //             try
    //     //             {
    //     //                 CreateSettle(_context, newBounty);
    //     //                 _logger.Info("TranStep_MonthlyInduction_4", $"step1", $"按天入职过保-每日-结算单创建成功_订单号：{item.bounty.Id}");
    //     //                 // todo:判断NuoId
    //     //                 if (!string.IsNullOrWhiteSpace(nuoId) && newBounty.SettlementActualMoney != null && newBounty.SettlementActualMoney > 0)
    //     //                 {
    //     //                     _ = _ndnService.FreezeBudget(new FreezeBudgetByPost
    //     //                     {
    //     //                         projectCode = nuoId!,
    //     //                         type = BudgetFreezeType.冻结,
    //     //                         amount = newBounty.SettlementActualMoney.Value,
    //     //                         postId = newBounty.PostId,
    //     //                         HrId = newBounty.HrId,
    //     //                         ProjectId = newBounty.ProjectId
    //     //                     }).GetAwaiter().GetResult();
    //     //                 }
    //     //                 _logger.Info("TranStep_MonthlyInduction_4", $"step2", $"按天入职过保-每日-冻结金额成功_订单号：{item.bounty.Id}");
    //     //                 // todo:判断NuoId,交付成功解冻金额(不走数字诺亚)
    //     //                 if (!string.IsNullOrWhiteSpace(nuoId) && newBounty.SettlementActualMoney != null && newBounty.SettlementActualMoney > 0)
    //     //                 {
    //     //                     _ = _ndnService.FreezeBudget(new FreezeBudgetByPost
    //     //                     {
    //     //                         projectCode = nuoId!,
    //     //                         type = BudgetFreezeType.消费,
    //     //                         amount = newBounty.SettlementActualMoney.Value,
    //     //                         postId = newBounty.PostId,
    //     //                         HrId = newBounty.HrId,
    //     //                         ProjectId = newBounty.ProjectId
    //     //                     }).GetAwaiter().GetResult();
    //     //                 }
    //     //                 _logger.Info("TranStep_MonthlyInduction_4", $"step3", $"按天入职过保-每日-消费成功订单号：{item.bounty.Id}");
    //     //                 newBounty!.Status = BountyStatus.交付成功;
    //     //                 _logger.Info("MonthlyInductionBountyCheckByEveryDay", "按天入职过保到期检测-每日", $"按天入职过保交付成功，订单号：{item.bounty.Id}");
    //     //             }
    //     //             catch (BadRequestException ex)
    //     //             {
    //     //                 if (ex.Message.Contains("预算不足"))
    //     //                 {
    //     //                     newBounty!.Status = BountyStatus.交付失败;
    //     //                     newBounty!.Description = "按天入职过保-每日冻结金额预算不足";
    //     //                     _logger.Info("MonthlyInductionBountyCheckByEveryDay", "按天入职过保到期检测-每日", $"按天入职过保交付失败，订单号：{item.bounty.Id}");
    //     //                 }
    //     //                 else
    //     //                 {
    //     //                     newBounty!.Status = BountyStatus.交付失败;
    //     //                     newBounty!.Description = "按天入职过保-每日交付异常";
    //     //                     _logger.Info("MonthlyInductionBountyCheckByEveryDay", ex.Message, $"按天入职过保交付失败，订单号：{item.bounty.Id}");
    //     //                 }
    //     //             }
    //     //         }
    //     //         newBounties.Add(newBounty);
    //     //         item.bounty.RecruitId += ("-" + EntityTools.SnowflakeId());// 老数据修改，因为recruitid是唯一键，所以新数据插入之前要先修改老数据
    //     //     }
    //     //     // 先保存编辑内容，否则有唯一键索引，新数据插入会失败
    //     //     _context.SaveChanges();

    //     //     if (newBounties.Count > 0)
    //     //     {
    //     //         _context.AddRange(newBounties);
    //     //         _context.SaveChanges();
    //     //     }
    //     //     _logger.Info("TranStep_MonthlyInduction_4", $"step4", $"按天入职过保-每日-全部成功");
    //     //     //今日交付
    //     //     foreach (var item in projectTeambountys)
    //     //     {
    //     //         var todayKey = $"{RedisKey.HrStatBehavior.DailyKey}{DateTime.Today.ToString("yyyyMMdd")}";
    //     //         var todayBountyKey = $"{RedisKey.HrStatBehavior.DeliveryBounty}{item.bounty.TeamHrId}";
    //     //         MyRedis.Client.HIncrByFloat(todayKey, todayBountyKey, item.bounty.SettlementMoney ?? 0);

    //     //         //历史交付
    //     //         MyRedis.Client.HIncrByFloat(RedisKey.HrStatBehavior.FullKey, $"{RedisKey.HrStatBehavior.DeliveryBounty}{item.bounty.TeamHrId}", item.bounty.SettlementMoney ?? 0);
    //     //     }

    //     //     await Task.FromResult(1);
    //     // }
    //     // catch (Exception e)
    //     // {
    //     //     _logger.Error("按天入职过保到期检测-每日出错", Tools.GetErrMsg(e), id);
    //     // }
    // }

    // private Project_Teambounty GetNewObject(Project_Teambounty bounty)
    // {
    //     var operateTime = DateTime.Now;
    //     return new Project_Teambounty
    //     {
    //         Id = EntityTools.SnowflakeId(),
    //         ProjectId = bounty.ProjectId,
    //         ProjectName = bounty.ProjectName,
    //         ProjectCode = bounty.ProjectCode,
    //         TeamProjectId = bounty.TeamProjectId,
    //         RecruitId = bounty.RecruitId,
    //         ResumeBufferId = bounty.ResumeBufferId,
    //         TeamPostId = bounty.TeamPostId,
    //         PostId = bounty.PostId,
    //         PostName = bounty.PostName,
    //         SeekerId = bounty.SeekerId,
    //         SeekerName = bounty.SeekerName,
    //         SeekerMobile = bounty.SeekerMobile,
    //         HrId = bounty.HrId,
    //         HrName = bounty.HrName,
    //         ChannelHrId = bounty.ChannelHrId,
    //         ChannelHrName = bounty.ChannelHrName,
    //         TeamHrId = bounty.TeamHrId,
    //         TeamHrName = bounty.TeamHrName,
    //         PaymentNode = bounty.PaymentNode,
    //         PaymentDays = bounty.PaymentDays,
    //         PaymentType = bounty.PaymentType,
    //         Money = bounty.Money,
    //         Status = bounty.Status,
    //         Source = bounty.Source,
    //         IfInValid = bounty.IfInValid,
    //         IfStockOut = bounty.IfStockOut,
    //         Description = bounty.Description,
    //         SettlementTime = bounty.SettlementTime,
    //         SalesBounty = bounty.SalesBounty,
    //         ManagerBounty = bounty.ManagerBounty,
    //         ClueBounty = bounty.ClueBounty,
    //         InvitationBounty = bounty.InvitationBounty,
    //         PlatformBounty = bounty.PlatformBounty,
    //         SalesRate = bounty.SalesRate,
    //         ManagerRate = bounty.ManagerRate,
    //         ClueRate = bounty.ClueRate,
    //         InvitationRate = bounty.InvitationRate,
    //         PlatformRate = bounty.PlatformRate,
    //         SettlementId = bounty.SettlementId,
    //         ChannelContractId = bounty.ChannelContractId,
    //         SettlementStatus = bounty.SettlementStatus,
    //         CreatedTime = operateTime,
    //         UpdatedTime = operateTime,
    //         ChannelId = bounty.ChannelId
    //     };
    // }

    /// <summary>
    /// 面试反馈
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task InterviewFeedback(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.LPop<string?>(SubscriptionKey.RecruitInterviewBack);

                if (string.IsNullOrWhiteSpace(msg))
                {
                    await Task.Delay(500);
                    continue;
                }

                // //处理消息
                // var recruitId = msg;
                // using var _context = _contextFactory.CreateDbContext();
                // var bounty = _context.Post_Bounty.Where(w => w.RecruitId == recruitId && w.PaymentNode == ProjectPaymentNode.到面交付 && w.Status == BountyStatus.交付中).FirstOrDefault();
                // if (bounty == null)
                //     return;

                // bounty.Status = BountyStatus.结算中;
                // bounty.UpdatedTime = DateTime.Now;
                // CreateSettle(_context, bounty);

                // _context.SaveChanges();

                // _logger.Info("InterviewFeedback", "面试反馈交付", $"面试反馈交付成功，订单号：{bounty.Id}");
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.RecruitInterviewBack}消息处理出错", Tools.GetErrMsg(e), (msg ?? string.Empty).ToString());
                await Task.Delay(500);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 财务审批通过消息
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task FinanceApproval(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.RPop<string?>(SubscriptionKey.NkpFinanceAuditPass);

                if (string.IsNullOrEmpty(msg))
                {
                    await Task.Delay(500);
                    continue;
                }

                using var _context = _contextFactory.CreateDbContext();

                var settlements = _context.Post_Settlement.First(f => f.Id == msg);
                var settlementDetails = _context.Post_Settlement_Detail.Where(w => w.SettlementId == settlements.Id).ToList();

                if (settlements.Status == PostSettleStatus.完成)
                {
                    await Task.Delay(500);
                    continue;
                }

                settlementDetails = settlementDetails.Where(w => w.PaymentStatus == PaymentStatus.未打款).ToList();

                // // 平台entid，取诺聘公司的
                // var platEntId = _hostingEnvironment.IsProduction() ? "150398478611504261" : "100989262137917573";
                // item.SettleBountyType == PostSettleBountyType.平台 ? platEntId : jsInfo.EntId，

                foreach (var item in settlementDetails)
                {
                    try
                    {
                        var jsInfo = _context.Post_Settlement_Detail.Where(w => w.Id == item.Id)
                        .Select(s => new
                        {
                            s.EntId,
                            s.SettleBountyType,
                            s.Post_Settlement.Post_Bounty_Stage.Post_Bounty.Post.ProjectId,
                            s.Post_Settlement.Post_Bounty_Stage.Post_Bounty.Post.Project.XbbContractNo,
                            HrId = s.UserId,
                            s.Post_Settlement.Post_Bounty_Stage.Post_Bounty.Post.Project.NuoId,
                            s.User_Hr.NickName,
                            s.User_Hr.User.Mobile,
                            remark = string.Empty
                        }).FirstOrDefault();

                        if (jsInfo == null)
                            throw new Exception($"结算单明细信息不存在，订单号：{item.Id}");

                        // var hrNo = item.SettleBountyType != PostSettleBountyType.平台 ? _commonUserService.GetUserNo(jsInfo.HrId!) : string.Empty;
                        var transactionType = item.SettleBountyType switch
                        {
                            PostSettleBountyType.平台 => TransactionType.平台,
                            PostSettleBountyType.个人 => TransactionType.个人,
                            PostSettleBountyType.内部 => TransactionType.内部,
                            PostSettleBountyType.外部 => TransactionType.外部,
                            _ => throw new Exception($"未知的结算类型：{item.SettleBountyType}，订单号：{item.Id}")
                        };

                        var resp = await _internalApi.RequestAsync<TransactionResponse>(InternalSerciceType.Settlement, "internal/transaction",
                        new TransactionRequest
                        {
                            TransAmt = (long)(item.SettlementMoney * 100),
                            OrderNo = item.Id,
                            InEntId = item.InMerchantId,
                            ProjectId = jsInfo.ProjectId!,
                            ContractCode = item.ContractNo!,
                            HrNo = item.UserNo,
                            AgentHrNo = item.AgentHrNo!,
                            Name = jsInfo.NickName,
                            Mobile = jsInfo.Mobile,
                            Remark = jsInfo.remark,
                            Type = transactionType,
                        });

                        if (resp.TransStatus != "1")
                        {
                            item.PaymentStatus = PaymentStatus.打款失败;
                            item.Remark = resp.TransNote;
                            _logger.Error($"{SubscriptionKey.NkpFinanceAuditPass}打款失败", $"打款失败，订单号：{item.Id}，错误信息：{JsonSerializer.SerializeToString(resp)}", msg);
                        }
                        else
                        {
                            item.PaymentStatus = PaymentStatus.打款中;
                        }
                    }
                    catch (Exception e)
                    {
                        item.PaymentStatus = PaymentStatus.打款失败;
                        item.Remark = Tools.GetErrMsg(e);
                        _logger.Error($"{SubscriptionKey.NkpFinanceAuditPass}打款失败", item.Remark, msg);
                    }

                    _context.SaveChanges();

                    await Task.Delay(100);
                }

                if (!settlementDetails.Any(w => w.PaymentStatus == PaymentStatus.未打款))
                {
                    settlements.Status = PostSettleStatus.完成;
                }

                _context.SaveChanges();
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.NkpFinanceAuditPass}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(500);
            }

            await Task.Delay(20);
        }
    }

    /// <summary>
    /// 定期生成凭证
    /// </summary>
    /// <param name="cancel"></param>
    public async Task GenerateVoucher(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            // 如果是产品环境，则改为每月1号生成，注意：任务是10分钟检测一次，你需要判断day
            if (_hostingEnvironment.IsProduction())
            {
                var today = DateTime.Today;
                // 每天2点到3点执行
                if (today.Day != 1 || DateTime.Now <= today.AddHours(2) || DateTime.Now >= today.AddHours(4))
                    return;
            }

            //加锁
            var lockerKey = $"projectbounty:generatevoucher";
            using var locker = MyRedis.TryLock(lockerKey, 100);
            if (locker == null)
                return;

            using var _context = _contextFactory.CreateDbContext();

            // 只查最近2个月的
            var startDate = DateTime.Now.AddMonths(-2);
            var settlementDetails = _context.Post_Settlement_Detail
            .Where(x => x.Post_Settlement.ApprovalStatus == PostSettleApprovalStatus.审核完成
            && x.IsVoucher == 0 && x.Post_Settlement.CreatedTime >= startDate && x.InMerchantId != x.OutMerchantId
            && !string.IsNullOrWhiteSpace(x.InMerchantId) && !string.IsNullOrWhiteSpace(x.OutMerchantId))
            .Select(g => new
            {
                g.Id,
                g.Post_Settlement.Post_Bounty_Stage.Post_Bounty.Post.ProjectId,
                g.OutMerchantId,
                g.InMerchantId,
                g.SettlementMoney,
                g.SettlementId
            }).ToList();

            if (settlementDetails.Count == 0)
                return;

            // 平台商户号
            var platformMerchantId = _context.Enterprise_Merchant
            .Where(x => x.MerchantType == MerchantType.平台)
            .Select(s => s.Id)
            .FirstOrDefault();

            var dataList = new List<VoucherModel>();

            foreach (var detail in settlementDetails)
            {
                var outMerchantId = detail.OutMerchantId?.Trim();
                var inMerchantId = detail.InMerchantId?.Trim();

                // 如果出金商户和入金商户相同，则不需要生成凭证
                if (outMerchantId == inMerchantId)
                    continue;

                if (inMerchantId == platformMerchantId || outMerchantId == platformMerchantId)
                {
                    // 如果其中有平台，只生成一个凭证
                    dataList.Add(new VoucherModel
                    {
                        InMerchantId = inMerchantId,
                        OutMerchantId = outMerchantId,
                        ProjectId = detail.ProjectId,
                        SettlementId = detail.SettlementId,
                        SettlementDetailId = detail.Id,
                        Amount = detail.SettlementMoney
                    });
                }
                else
                {
                    // 转账凭证（从出金商户到平台）
                    dataList.Add(new VoucherModel
                    {
                        InMerchantId = platformMerchantId,
                        OutMerchantId = outMerchantId,
                        ProjectId = detail.ProjectId,
                        SettlementId = detail.SettlementId,
                        SettlementDetailId = detail.Id,
                        Amount = detail.SettlementMoney
                    });

                    // 转账凭证（从平台到入金商户）
                    dataList.Add(new VoucherModel
                    {
                        InMerchantId = inMerchantId,
                        OutMerchantId = platformMerchantId,
                        ProjectId = detail.ProjectId,
                        SettlementId = detail.SettlementId,
                        SettlementDetailId = detail.Id,
                        Amount = detail.SettlementMoney
                    });
                }
            }

            // 查询商户信息
            var merchantIds = dataList.SelectMany(x => new[] { x.InMerchantId!, x.OutMerchantId! }).Distinct().ToList();
            var merchants = _context.Enterprise_Merchant
                .Where(x => merchantIds.Contains(x.Id))
                .ToDictionary(x => x.Id, x => x.MerchantName);

            // 查询项目信息
            var projectIds = dataList.Select(x => x.ProjectId!).Distinct().ToList();
            var projects = _context.Project
                .Where(x => projectIds.Contains(x.ProjectId))
                .Select(x => new { x.ProjectId, x.Agent_Ent.Name, x.AutoId })
                .ToList()
                .ToDictionary(x => x.ProjectId, x => $"{x.Name}-{Constants.ProjectIdPre + x.AutoId.ToString().PadLeft(6, '0')}");

            // 查询结算单信息
            var settlementIds = dataList.Select(x => x.SettlementId!).Distinct().ToList();
            var settlements = _context.Post_Settlement
                .Where(x => settlementIds.Contains(x.Id))
                .Select(s => new
                {
                    s.Id,
                    s.StartTime,
                    s.ManagerConfirmTime,
                    s.Post_Bounty_Stage.Post_Bounty.PaymentNode,
                    s.Post_Bounty_Stage.Post_Bounty.RewardType,
                    s.Post_Bounty_Stage.Post_Bounty.PaymentCycle,
                    s.SettlementAmount,
                    s.Post_Bounty_Stage.GuaranteeDays
                })
                .ToDictionary(x => x.Id, x => x);

            // 按出金商户和入金商户分组生成凭证
            var voucherGroups = dataList
                .GroupBy(x => new { x.InMerchantId, x.OutMerchantId })
                .ToList();

            var vouchers = new List<Voucher>();
            var voucherDetails = new List<Voucher_Detail>();
            var voucherSettlements = new List<Voucher_Settlement>();

            foreach (var voucherGroup in voucherGroups)
            {
                var inMerchantName = merchants.GetValueOrDefault(voucherGroup.Key.InMerchantId!, "未知商户");
                var outMerchantName = merchants.GetValueOrDefault(voucherGroup.Key.OutMerchantId!, "未知商户");

                // 生成凭证主记录
                var voucherId = EntityTools.SnowflakeId();
                var totalAmount = voucherGroup.Sum(x => x.Amount);

                vouchers.Add(new Voucher
                {
                    Id = voucherId,
                    InMerchantId = voucherGroup.Key.InMerchantId!,
                    OutMerchantId = voucherGroup.Key.OutMerchantId!,
                    InMerchantName = inMerchantName,
                    OutMerchantName = outMerchantName,
                    VoucherTime = DateTime.Now.Date,
                    Subject = $"{outMerchantName}向{inMerchantName}支付服务费（{DateTime.Now:yyyy-MM}）",
                    Amount = totalAmount,
                    VoucherType = VoucherType.订单转账,
                    VoucherStatus = VoucherStatus.待生成,
                    Remark = "系统自动创建"
                });

                // 按项目和结算单分组生成凭证明细
                var detailGroups = voucherGroup
                    .GroupBy(x => new { x.ProjectId, x.SettlementId })
                    .ToList();

                foreach (var detailGroup in detailGroups)
                {
                    var projectName = projects.GetValueOrDefault(detailGroup.Key.ProjectId!, "未知项目");

                    if (!settlements.TryGetValue(detailGroup.Key.SettlementId!, out var settlementInfo))
                    {
                        _logger.Error("GenerateVoucher", "结算信息不存在",
                            $"结算单ID：{detailGroup.Key.SettlementId}，项目ID：{detailGroup.Key.ProjectId}");
                        continue;
                    }

                    var voucherDetailId = EntityTools.SnowflakeId();

                    // 根据交付类型计算ConfirmResult
                    var confirmResult = settlementInfo.PaymentNode.ToString();
                    if (settlementInfo.PaymentNode == ProjectPaymentNode.入职过保)
                    {
                        if (settlementInfo.RewardType == PostRewardType.单次结算)
                            confirmResult = $"{confirmResult}(单次){settlementInfo.GuaranteeDays}天";
                        else if (settlementInfo.RewardType == PostRewardType.长期结算)
                            confirmResult = $"{confirmResult}(长期){settlementInfo.SettlementAmount}{settlementInfo.PaymentCycle?.ToString()}";
                    }

                    voucherDetails.Add(new Voucher_Detail
                    {
                        Id = voucherDetailId,
                        VoucherId = voucherId,
                        ProjectId = detailGroup.Key.ProjectId!,
                        ProjectName = projectName,
                        SettlementId = detailGroup.Key.SettlementId,
                        ServiceType = "委托交付",
                        ConfirmResult = confirmResult,
                        ConfirmTime = settlementInfo.ManagerConfirmTime ?? DateTime.Now,
                        Amount = detailGroup.Sum(x => x.Amount)
                    });

                    // 生成凭证和结算明细关联记录
                    foreach (var item in detailGroup)
                    {
                        voucherSettlements.Add(new Voucher_Settlement
                        {
                            SettlementDetailId = item.SettlementDetailId!,
                            VoucherDetailId = voucherDetailId,
                            CreatedTime = DateTime.Now
                        });
                    }
                }
            }

            using var transaction = _context.Database.BeginTransaction();

            // 批量插入数据
            if (vouchers.Count > 0)
            {
                _context.AddRange(vouchers);
            }

            if (voucherDetails.Count > 0)
            {
                _context.AddRange(voucherDetails);
            }

            if (voucherSettlements.Count > 0)
            {
                _context.AddRange(voucherSettlements);
            }

            _context.SaveChanges();

            // 标记结算明细已生成凭证
            var settlementDetailIds = settlementDetails.Select(x => x.Id!).ToList();
            _context.Post_Settlement_Detail
                .Where(x => settlementDetailIds.Contains(x.Id))
                .ExecuteUpdate(x => x.SetProperty(s => s.IsVoucher, 1));

            _logger.Info("GenerateVoucher", "生成凭证完成",
                $"生成凭证数量：{vouchers.Count}，凭证明细数量：{voucherDetails.Count}，关联记录数量：{voucherSettlements.Count}");

            transaction.Commit();
            await Task.FromResult(1);
        }
        catch (Exception e)
        {
            _logger.Error("定期生成凭证出错", Tools.GetErrMsg(e));
        }
    }

    /// <summary>
    /// 生成凭证凭证文件
    /// </summary>
    /// <param name="cancel"></param>
    public async Task GenerateVoucherFile(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            //加锁
            var lockerKey = $"projectbounty:generatevoucherfile";
            using var locker = MyRedis.TryLock(lockerKey, 100);
            if (locker == null)
                return;

            using var _context = _contextFactory.CreateDbContext();

            // 只查最近2个月的
            var startDate = DateTime.Now.AddMonths(-3);
            var vouchers = _context.Voucher.Where(x => x.VoucherStatus == VoucherStatus.待生成
            && x.CreatedTime >= startDate)
            .OrderByDescending(o => o.CreatedTime)
            .Take(10)
            .ToList();

            if (vouchers.Count == 0)
                return;

            var voucherIds = vouchers.Select(x => x.Id).ToList();
            var voucherDatas = _context.Voucher
            .Where(x => voucherIds.Contains(x.Id))
            .Select(s => new Config.CommonModel.QuestPDF.ContractModel2
            {
                Id = s.Id,
                FileName = $"转账凭证",
                ContractNumber = s.Id,
                SellerCompanyName = s.InMerchantName,
                SellerCompanyAddress = s.InMerchant.InvoiceInfo!.Address ?? string.Empty,
                SellerCompanyContacter = s.InMerchant.InvoiceInfo!.ContractOperatorName ?? string.Empty,
                SellerCompanyContacterNumber = s.InMerchant.InvoiceInfo!.Phone ?? string.Empty,
                SellerCompanyImageUrl = s.InMerchant.OfficeSeal ?? string.Empty,
                AccountName = string.Empty,
                AccountAddress = string.Empty,
                Account = string.Empty,
                CustomerCompanyName = s.OutMerchantName,
                CustomerCompanyAddress = s.OutMerchant.InvoiceInfo!.Address ?? string.Empty,
                CustomerCompanyContacter = s.OutMerchant.InvoiceInfo!.ContractOperatorName ?? string.Empty,
                CustomerCompanyContacterNumber = s.OutMerchant.InvoiceInfo!.Phone ?? string.Empty,
                CustomerCompanyImageUrl = s.OutMerchant.OfficeSeal ?? string.Empty,
            }).ToList();

            var voucherDetails = _context.Voucher_Detail
            .Where(x => voucherIds.Contains(x.VoucherId))
            .Select(s => new Config.CommonModel.QuestPDF.ContractOrderItem2
            {
                ParentId = s.VoucherId,
                OrderNumber = s.Id,
                ProjectName = s.ProjectName ?? string.Empty,
                Achievement = s.ConfirmResult ?? string.Empty,
                BountyAmount = s.Amount,
                BountyName = s.ServiceType ?? string.Empty,
                BountyTime = (s.ConfirmTime ?? s.CreatedTime).ToString("yyyy-MM-dd")
            }).ToList();

            foreach (var voucher in vouchers)
            {
                try
                {
                    var voucherData = voucherDatas.FirstOrDefault(x => x.Id == voucher.Id);
                    if (voucherData == null)
                    {
                        _logger.Error("GenerateVoucherFile", "凭证数据不存在", $"凭证ID：{voucher.Id}");
                        continue;
                    }

                    voucherData.OrderInfo2 = new Config.CommonModel.QuestPDF.ContractOrderInfo2
                    {
                        Items = voucherDetails
                        .Where(x => x.ParentId == voucher.Id)
                        .ToList()
                    };

                    var path = await _commonPDFService.GenerateContractPdfToOss2Async(voucherData);
                    voucher.VoucherUrl = path;
                    voucher.VoucherStatus = VoucherStatus.生效中;
                }
                catch (Exception e)
                {
                    _logger.Error("生成凭证文件出错", Tools.GetErrMsg(e), voucher.Id);
                    voucher.VoucherStatus = VoucherStatus.凭证生成失败;
                }
            }
            _context.SaveChanges();

            await Task.FromResult(1);
        }
        catch (Exception e)
        {
            _logger.Error("生成凭证凭证文件", Tools.GetErrMsg(e));
        }
    }
}

public class VoucherModel
{
    /// <summary>
    /// 入金商户Id
    /// </summary>
    public string? InMerchantId { get; set; }

    /// <summary>
    /// 出金商户Id
    /// </summary>
    public string? OutMerchantId { get; set; }

    public string? ProjectId { get; set; }

    public string? SettlementId { get; set; }

    public string? SettlementDetailId { get; set; }
    public decimal Amount { get; set; }
}