using Config.Enums;

namespace Config.CommonModel.Ndn;

public enum NdnAuditStatus
{
    未开始, 待提交, 进行中, 已完成 = 6, 已驳回 = 9
}

public class NdnEmployee
{
    /// <summary>
    /// 姓名
    /// </summary>
    public string? employeeName { get; set; }

    /// <summary>
    /// 部门名称
    /// </summary>
    public string? deptName { get; set; }

    /// <summary>
    /// 账套编码
    /// </summary>
    public string? bookCode { get; set; }

    /// <summary>
    /// 人员工号
    /// </summary>
    public string? hrNo { get; set; }

    /// <summary>
    /// 账套名称
    /// </summary>
    public string? bookName { get; set; }

    /// <summary>
    /// 部门编码
    /// </summary>
    public string? deptCode { get; set; }

    /// <summary>
    /// 员工状态
    /// </summary>
    public string? status { get; set; }
}

public class CreateProject
{
    /// <summary>
    /// 标题
    /// </summary>
    public string? title { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? projectName { get; set; }

    /// <summary>
    /// 合同编码
    /// </summary>
    public string? contractCode { get; set; }

    /// <summary>
    /// 帐套编码
    /// </summary>
    public string? bookCode { get; set; }

    /// <summary>
    /// 创建人工号
    /// </summary>
    public string? creatByHrNo { get; set; }

    /// <summary>
    /// 交付分润支出预算
    /// </summary>
    public decimal expenseBudget { get; set; }

}

public class CreateProjectResponse
{
    /// <summary>
    /// 项目编码
    /// </summary>
    public string? projectCode { get; set; }
}

/// <summary>
/// 项目预算类
/// </summary>
public class ProjectBudget
{
    /// <summary>
    /// 数量
    /// </summary>
    public int number { get; set; }

    /// <summary>
    /// 预算科目编码
    /// </summary>
    public string? budgetCode { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal price { get; set; }

    /// <summary>
    /// 费用额
    /// </summary>
    public decimal costAmount { get; set; }

    /// <summary>
    /// 实施余额(不含冻结)
    /// </summary>
    public decimal surplusAmount { get; set; }

    /// <summary>
    /// 预算科目名称
    /// </summary>
    public string? budgetName { get; set; }

    /// <summary>
    /// 预算分类名称
    /// </summary>
    public string? planClassifyName { get; set; }

    /// <summary>
    /// 冻结金额
    /// </summary>
    public decimal frozenAmount { get; set; }
}

/// <summary>
/// 项目详细信息类
/// </summary>
public class ProjectDetails
{
    /// <summary>
    /// 账套编码
    /// </summary>
    public string? bookCode { get; set; }

    /// <summary>
    /// 客户编码
    /// </summary>
    public string? customerCode { get; set; }

    /// <summary>
    /// 受益部门名称
    /// </summary>
    public string? beneficiaryDeptName { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    public string? title { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string? productName { get; set; }

    /// <summary>
    /// 项目标识
    /// </summary>
    public string? projectFlag { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    public string? projectCode { get; set; }

    /// <summary>
    /// 项目类别
    /// </summary>
    public string? projectCategory { get; set; }

    /// <summary>
    /// 受益人名称
    /// </summary>
    public string? beneficiaryName { get; set; }

    /// <summary>
    /// 项目终结状态
    /// </summary>
    public string? projectEnd { get; set; }

    /// <summary>
    /// 借款逾期状态
    /// </summary>
    public string? loanExpireStatus { get; set; }

    /// <summary>
    /// 项目预算
    /// </summary>
    public List<ProjectBudget>? projectBudget { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime startTime { get; set; }

    /// <summary>
    /// 实施完成状态
    /// </summary>
    public string? projectImplementationStatus { get; set; }

    /// <summary>
    /// 借款状态
    /// </summary>
    public string? isLoan { get; set; }

    /// <summary>
    /// 项目财务编码
    /// </summary>
    public string? projectFinanceCode { get; set; }

    /// <summary>
    /// 项目业务分类名称
    /// </summary>
    public string? projectClassificationName { get; set; }

    /// <summary>
    /// 账套名称
    /// </summary>
    public string? bookName { get; set; }

    /// <summary>
    /// 预算审批流程状态
    /// </summary>
    public string? profitMarginStatus { get; set; }

    /// <summary>
    /// 客户名称
    /// </summary>
    public string? customerName { get; set; }

    /// <summary>
    /// 受益部门编码
    /// </summary>
    public string? beneficiaryDeptCode { get; set; }

    /// <summary>
    /// 项目业务分类编码
    /// </summary>
    public string? projectClassification { get; set; }

    /// <summary>
    /// 产品编码
    /// </summary>
    public string? productCode { get; set; }

    /// <summary>
    /// 受益人工号
    /// </summary>
    public string? beneficiaryHrNo { get; set; }

    /// <summary>
    /// 项目回款状态
    /// </summary>
    public string? collectionStatus { get; set; }

    /// <summary>
    /// 开票金额
    /// </summary>
    public decimal billAmount { get; set; }

    /// <summary>
    /// 回款金额
    /// </summary>
    public decimal collectionAmount { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? endTime { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? projectName { get; set; }

    /// <summary>
    /// 合同编码
    /// </summary>
    public string? contractCode { get; set; }

    /// <summary>
    /// 项目状态
    /// </summary>
    public string? status { get; set; }
}

public class GetProjectResponse : ProjectDetails
{
    /// <summary>
    /// 诺快聘分润预算
    /// </summary>
    public ProjectBudget budget { get; set; } = new ProjectBudget();
}

public class Reimbursement
{
    /// <summary>
    /// 项目编码
    /// </summary>
    public string? projectCode { get; set; }

    /// <summary>
    /// 经办人
    /// </summary>
    public string? agentHrNo { get; set; }

    /// <summary>
    /// 费用名称
    /// </summary>
    public string? feeName { get; set; }

    /// <summary>
    /// 预算科目编码
    /// </summary>
    public string? budgetCode { get; set; }

    /// <summary>
    /// 报销事项
    /// </summary>
    public string? costName { get; set; }

    /// <summary>
    /// 报销金额
    /// </summary>
    public decimal amount { get; set; }

    /// <summary>
    /// 报销方式(1是现金,2是电汇)
    /// </summary>
    public int? payType { get; set; }

    /// <summary>
    /// 打款账户
    /// </summary>
    public string? account { get; set; }

    /// <summary>
    /// 订单编号
    /// </summary>
    public string? orderNumber { get; set; }

    /// <summary>
    /// 合同url
    /// </summary>
    public string? contract { get; set; }

    /// <summary>
    /// 发票url
    /// </summary>
    public List<string>? invoice { get; set; }
}

public class InitiateInvoice
{
    /// <summary>
    /// 项目编码
    /// </summary>
    public string? projectCode { get; set; }

    /// <summary>
    /// 经办人
    /// </summary>
    public string? agentHrNo { get; set; }

    /// <summary>
    /// 报销金额
    /// </summary>
    public decimal amount { get; set; }

    /// <summary>
    /// 订单编号
    /// </summary>
    public string? orderNumber { get; set; }
}

public class GetBooks
{
    /// <summary>
    /// 账套编码
    /// </summary>
    public string? bookCode { get; set; }
}

public class GetBooksInfo
{
    /// <summary>
    /// 账套编码
    /// </summary>
    public string? bookCode { get; set; }

    /// <summary>
    /// 账套名称
    /// </summary>
    public string? bookName { get; set; }

    /// <summary>
    /// 开票员工号
    /// </summary>
    public string? drawerHrNo { get; set; }

    /// <summary>
    /// 开票员姓名
    /// </summary>
    public string? drawerName { get; set; }

    /// <summary>
    /// 出纳人工号
    /// </summary>
    public string? cashierHrNo { get; set; }

    /// <summary>
    /// 出纳人姓名
    /// </summary>
    public string? cashierName { get; set; }

    /// <summary>
    /// 复核出纳工号
    /// </summary>
    public string? reviewCashierHrNo { get; set; }

    /// <summary>
    /// 复核出纳姓名
    /// </summary>
    public string? reviewCashierName { get; set; }

    /// <summary>
    /// 记账会计工号
    /// </summary>
    public string? bookKeeperHrNo { get; set; }

    /// <summary>
    /// 记账会计姓名
    /// </summary>
    public string? bookKeeperName { get; set; }

    /// <summary>
    /// 财务部长工号
    /// </summary>
    public string? financeMinisterHrNo { get; set; }

    /// <summary>
    /// 财务部长姓名
    /// </summary>
    public string? financeMinisterName { get; set; }
}

public class ServiceBonus
{
    /// <summary>
    /// 项目编码
    /// </summary>
    public string? projectCode { get; set; }

    /// <summary>
    /// 经办人
    /// </summary>
    public string? agentHrNo { get; set; }

    /// <summary>
    /// 费用名称
    /// </summary>
    public string? feeName { get; set; }

    /// <summary>
    /// 发放人员列表
    /// </summary>
    public List<IssueDetail>? issueList { get; set; }

    /// <summary>
    /// 订单编号
    /// </summary>
    public string? orderNumber { get; set; }
}

/// <summary>
/// 发放人员详情类，包含人员信息和发放相关的说明。
/// </summary>
public class IssueDetail
{
    /// <summary>
    /// 人员工号
    /// </summary>
    public string? hrNo { get; set; }

    /// <summary>
    /// 发放人员对应金额
    /// </summary>
    public decimal issuedAmount { get; set; }

    /// <summary>
    /// 发放说明
    /// </summary>
    public string? remark { get; set; }
}

public class FreezeBudget
{
    /// <summary>
    /// 项目编码
    /// </summary>
    public string? projectCode { get; set; }

    /// <summary>
    /// 费用名称
    /// </summary>
    public string? feeName { get; set; } = Constants.Ndn.FrKmName;

    /// <summary>
    /// 冻结/恢复标志（1：冻结；2：恢复）
    /// </summary>
    public BudgetFreezeType type { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal amount { get; set; }
}

public class NdnNotify
{
    /// <summary>
    /// 订单Id
    /// </summary>
    public string? OrderId { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public NdnTodoType Type { get; set; }

    /// <summary>
    /// 结果
    /// </summary>
    public NdnNotifyStatus Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Amount { get; set; }

    /// <summary>
    /// 发票url
    /// </summary>
    public List<string>? InvoiceUrl { get; set; }
}

public class ProjectContractInfo
{
    public string? ContractId { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 签署方式
    /// </summary>
    public int? SignType { get; set; }

    /// <summary>
    /// 合同状态
    /// </summary>
    public ProjectContractStatus? Status { get; set; }

    /// <summary>
    /// 合同状态文本
    /// </summary>
    public string? StatusText { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public ProjectContractSource? Source { get; set; }

    /// <summary>
    /// 合同名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 签署日期
    /// </summary>
    public DateTime? SignDate { get; set; }

    /// <summary>
    /// 发起日期
    /// </summary>
    public DateTime? InitiationDate { get; set; }

    /// <summary>
    /// 合同开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 合同结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 发起方
    /// </summary>
    public string? Initiator { get; set; }

    /// <summary>
    /// 参与方
    /// </summary>
    public string? Participant { get; set; }

    /// <summary>
    /// 发起方电话
    /// </summary>
    public string? InitiatorPhone { get; set; }

    /// <summary>
    /// 参与方电话
    /// </summary>
    public string? ParticipantPhone { get; set; }

    /// <summary>
    /// 发起方联系人
    /// </summary>
    public string? InitiatorContact { get; set; }

    /// <summary>
    /// 参与方联系人
    /// </summary>
    public string? ParticipantContact { get; set; }

    /// <summary>
    /// 合同金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 默认合同 1=是
    /// </summary>
    public int IsDefault { get; set; }

    /// <summary>
    /// 合同编号
    /// </summary>
    public string? ContractNo { get; set; }

    /// <summary>
    /// 合同附件
    /// </summary>
    public List<string>? Attachment { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 项目主题
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreatedById { get; set; }

    /// <summary>
    /// 创建人名字
    /// </summary>
    public string? CreatedByName { get; set; }
}

public class GetProjectListInfoDataFile
{
    public string? attachIndex { get; set; }
}

public enum BudgetFreezeType
{
    冻结 = 1,
    解冻 = 2,
    消费 = 3
}

public enum NdnNotifyStatus
{
    失败 = 0,
    成功 = 1
}

public enum NdnTodoStatus
{
    待办, 进行中, 完成
}

public enum NdnTodoType
{
    创建数字诺亚项目, 开发票, 转账报销, 发放服务奖金, 回款
}