﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;
/// <summary>
/// 虚拟人才库职位(字典)
/// </summary>
[Table("dic_talent_post")]
public class Dic_Talent_Post
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 父级id
    /// </summary>
    public string ParentId { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}

