using Staffing.Worker.Service;

namespace Staffing.Worker.Hosting;

public class TalentWorker : HostingBase
{
    private readonly TalentService _talentSubService;

    public TalentWorker(IHostApplicationLifetime lifetime, TalentService talentSubService) : base(lifetime)
    {
        _talentSubService = talentSubService;
    }

    public override async Task StartAsync(CancellationToken cancel)
    {
        _bgTasks.Add(await Task.Factory.StartNew(() => _talentSubService.UpLoadResume(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _talentSubService.TalentResumeRecordCreate(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _talentSubService.TalentResumeHide(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _talentSubService.SubUpdateYunshengPlatform(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        //_bgTasks.Add(await Task.Factory.StartNew(() => _talentSubService.RecruitUpLoadResume(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));// �첽������Ŀִ�м����ϴ� - ����
        // _bgTasks.Add(await Task.Factory.StartNew(() => _talentSubService.SubUpdateTalentPlatform(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        // _bgTasks.Add(await Task.Factory.StartNew(() => _talentSubService.SubUpdateTalentVirtual(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        // return Task.CompletedTask;
    }
}