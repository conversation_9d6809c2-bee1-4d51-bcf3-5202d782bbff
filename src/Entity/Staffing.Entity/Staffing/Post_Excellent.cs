﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 职位优选
/// </summary>
[Table("post_excellent")]
public class Post_Excellent
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string TeamPostId { get; set; } = null!;

    /// <summary>
    /// 状态
    /// </summary>
    public ExcellentPostStatus Status { get; set; }

    /// <summary>
    /// 72小时入职
    /// </summary>
    public bool EntryIn72hour { get; set; }

    /// <summary>
    /// 24小时面试
    /// </summary>
    public bool InterviewIn24Hour { get; set; }

    public DateTime ApprovalTime { get; set; } = Constants.DefaultTime;
    public DateTime CreatedTime { get; set; } = DateTime.Now;
    public DateTime RefreshTime { get; set; } = DateTime.Now;

    public Post_Team Post_Team { get; set; } = default!;
}
