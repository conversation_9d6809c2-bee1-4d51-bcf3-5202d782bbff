using System.Reflection;
using Config.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using Noah.Aliyun;
using Staffing.Entity;
using Infrastructure.Extend;
using Flurl.Http;
using AspectCore.Extensions.DependencyInjection;
using Infrastructure.Common;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Config;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Unchase.Swashbuckle.AspNetCore.Extensions.Extensions;
using Senparc.CO2NET;
using Senparc.Weixin.RegisterServices;
using Senparc.Weixin;
using Senparc.Weixin.WxOpen.Containers;
using Senparc.Weixin.MP.MessageHandlers.Middleware;
using Staffing.Api.MessageHandlers;
using Senparc.Weixin.Entities;
using Senparc.CO2NET.Cache;
using Senparc.CO2NET.Cache.Redis;
using ServiceStack.Text;
using Flurl.Http.Configuration;
using Essensoft.Paylink.WeChatPay;
using IGeekFan.AspNetCore.Knife4jUI;
using Staffing.Entity.Noah;
using Staffing.Api.MessageHandlers2;
using QuestPDF;
using QuestPDF.Infrastructure;
using Infrastructure.Middleware;
using Serilog;
using Serilog.Events;
using System.Web;
using Staffing.Api.Apis;

var builder = WebApplication.CreateBuilder(args);

builder.Services
.Configure<KestrelServerOptions>(x => x.AllowSynchronousIO = true)
.Configure<IISServerOptions>(x => x.AllowSynchronousIO = true);

//Senparc(盛派必须在前，否则会覆盖aop)
builder.Host.UseServiceProviderFactory(new SenparcServiceProviderFactory());

//aop支持
builder.Host.UseServiceProviderFactory(new DynamicProxyServiceProviderFactory());

builder.Services.AddSenparcWeixinServices(builder.Configuration);//Senparc.Weixin 注册（必须）

builder.Services.AddMemoryCache();
builder.Services.AddHttpContextAccessor();

//序列化枚举用int
JsConfig.TreatEnumAsInteger = true;
//允许任何类型序列化
JsConfig.AllowRuntimeType = _ => true;
JsConfig<DateTime>.SerializeFn = date => date.ToString("yyyy-MM-dd HH:mm:ss");
JsConfig<DateTime?>.SerializeFn = date => date?.ToString("yyyy-MM-dd HH:mm:ss");
JsConfig<DateOnly>.SerializeFn = date => date.ToString("yyyy-MM-dd");
JsConfig<DateOnly?>.SerializeFn = date => date?.ToString("yyyy-MM-dd");
JsConfig<TimeOnly?>.SerializeFn = date => date?.ToString("HH:mm:ss");
JsConfig<TimeOnly>.SerializeFn = date => date.ToString("HH:mm:ss");
JsConfig.TextCase = TextCase.CamelCase;
// JsConfig.Init(new ServiceStack.Text.Config
// {
//     TextCase = TextCase.CamelCase
//     // ExcludeDefaultValues = true,
// });

// #region 添加配置文件
// var svcEnvAppSettingsFile = $"appsettings.{builder.Environment.EnvironmentName}.json";

// if (builder.Configuration.GetValue<bool>("DOTNET_RUNNING_IN_CONTAINER")) //环境主要配置
//     builder.Configuration.AddJsonFile(ConfigMapFileProvider.FromRelativePath("config"), svcEnvAppSettingsFile, false, true);
// else //非容器≈本地开发环境
//     builder.Configuration.AddJsonFile(Path.Join(AppContext.BaseDirectory, "config", svcEnvAppSettingsFile), false, true);
// #endregion

builder.Services.AddControllers();

// 配置 Minimal API 使用 自定义序列化器
builder.Services.Configure<Microsoft.AspNetCore.Http.Json.JsonOptions>(options =>
{
    options.SerializerOptions.Converters.Add(new ServiceStackJsonConverter());
});

builder.Services.AddSwaggerGen(c =>
{
    // c.SwaggerDoc("v1", new OpenApiInfo { Title = "Staffing.Api", Version = "v1" });

    c.SwaggerDoc(Constants.ApiGroup.AdminApi, new OpenApiInfo() { Title = "平台端", Version = "1.0" });
    c.SwaggerDoc(Constants.ApiGroup.SeekerApi, new OpenApiInfo() { Title = "用户端", Version = "1.0" });
    c.SwaggerDoc(Constants.ApiGroup.HrApi, new OpenApiInfo() { Title = "顾问端", Version = "1.0" });
    c.SwaggerDoc(Constants.ApiGroup.InterviewerApi, new OpenApiInfo() { Title = "面试官服务", Version = "1.0" });
    c.SwaggerDoc(Constants.ApiGroup.CommonApi, new OpenApiInfo() { Title = "公共", Version = "1.0" });
    c.SwaggerDoc(Constants.ApiGroup.InternalApi, new OpenApiInfo() { Title = "内部服务", Version = "1.0" });
    c.SwaggerDoc(Constants.ApiGroup.CallbackApi, new OpenApiInfo() { Title = "第三方回调", Version = "1.0" });


    c.CustomSchemaIds(type => type.FullName);

    c.DescribeAllParametersInCamelCase();

    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath2 = Path.Combine(AppContext.BaseDirectory, "Staffing.Model.xml");
    var xmlPath3 = Path.Combine(AppContext.BaseDirectory, "Config.xml");
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    c.IncludeXmlComments(xmlPath, true);
    c.IncludeXmlComments(xmlPath2, true);
    c.IncludeXmlComments(xmlPath3, true);

    c.EnableAnnotations();

    //dateonly和timeonly支持
    c.UseDateOnlyTimeOnlyStringConverters();

    //枚举解释
    c.AddEnumsWithValuesFixFilters(configureOptions: o =>
    {
        o.IncludeDescriptions = true;
        o.NewLine = " ";
    });
});

builder.Services.AddWeChatPay();

builder.Services.Configure<ConfigManager>(builder.Configuration.GetSection("Settings"));
builder.Services.Configure<WeChatPayOptions>(builder.Configuration.GetSection("WeChatPay"));

//阿里云相关组件(人机检测、短信、内容检查、对象存储)
builder.Services.AddAliyun();

var connectionString = builder.Configuration.GetConnectionString("sqlCon");

builder.Services.AddDbContextPool<StaffingContext>(
    options => options.UseMySql(connectionString, new MySqlServerVersion(Constants.MySqlVersion), x => x.UseNetTopologySuite())
    .ConfigureWarnings(builder => builder.Ignore(CoreEventId.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning)));

builder.Services.AddDbContextFactory<StaffingContext>(
    options => options.UseMySql(connectionString, new MySqlServerVersion(Constants.MySqlVersion), x => x.UseNetTopologySuite())
    .ConfigureWarnings(builder => builder.Ignore(CoreEventId.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning)));

builder.Services.AddDbContextFactory<NoahContext>(
    options => options
    .UseLoggerFactory(LoggerFactory.Create(builder => builder.ClearProviders()))
    .UseSnakeCaseNamingConvention()
    .UseMySql(builder.Configuration.GetConnectionString("noahCon"), new MySqlServerVersion(Constants.MySqlVersion)));

var cfg = builder.Configuration.GetSection("Settings").Get<ConfigManager>();

var senparcWeixinSetting = builder.Configuration.GetSection("SenparcWeixinSetting").Get<SenparcWeixinSetting>();
var senparcWeixinSetting2 = builder.Configuration.GetSection("SenparcWeixinSetting2").Get<SenparcWeixinSetting>();

MyRedis.Client = new FreeRedis.RedisClient(cfg!.RedisAddress);
MyRedis.Client.Serialize = obj => ServiceStack.Text.JsonSerializer.SerializeToString(obj);
MyRedis.Client.Deserialize = (json, type) => ServiceStack.Text.JsonSerializer.DeserializeFromString(json, type);

var esUrl = cfg.ElasticSearch!.Address!;
var esUrlWithCredentials = $"http://{cfg.ElasticSearch.UserName}:{HttpUtility.UrlEncode(cfg.ElasticSearch.Password)}@{new Uri(esUrl).Host}:{new Uri(esUrl).Port}";

builder.Logging.ClearProviders();
Log.Logger = new LoggerConfiguration()
    .Enrich.FromLogContext() // 增加上下文信息
    .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
    .WriteTo.Console() // 控制台输出
    .WriteTo.Elasticsearch(new Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions(new Uri(esUrlWithCredentials))
    {
        AutoRegisterTemplate = true, // 自动注册索引模板
        IndexFormat = "nkplogs-{0:yyyy.MM.dd}", // 日志索引格式
    }).CreateLogger();

builder.Host.UseSerilog();


ProgramConfig.Init(builder);

builder.Services.AddCors(options =>
{
    options.AddPolicy("cors",
        builder => builder.AllowAnyOrigin()
        .AllowAnyHeader()
        .AllowAnyMethod()
    );
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();

//雪花算法worker注册
await Tools.SnowflakeWorkerInit(builder.Environment.IsProduction());

Tools.NextId();

var app = builder.Build();

ServiceLocator.Instance = app.Services;

// Configure the HTTP request pipeline.
if (!app.Environment.IsProduction())
{
    app.UseSwagger();
    app.UseKnife4UI(c =>
    {
        c.RoutePrefix = "swagger";
        c.SwaggerEndpoint("/seeker/swagger.json", "用户端");
        c.SwaggerEndpoint("/hr/swagger.json", "顾问端");
        c.SwaggerEndpoint("/interviewer/swagger.json", "面试官服务");
        c.SwaggerEndpoint("/common/swagger.json", "通用服务");
        c.SwaggerEndpoint("/internal/swagger.json", "内部服务");
        c.SwaggerEndpoint("/admin/swagger.json", "平台端");
        c.SwaggerEndpoint("/callback/swagger.json", "第三方回调");
    });
    // app.UseSwaggerUI(options =>
    // {
    //     options.SwaggerEndpoint("/swagger/c/swagger.json", "用户端");
    //     options.SwaggerEndpoint("/swagger/b/swagger.json", "顾问端");
    //     options.SwaggerEndpoint("/swagger/interview/swagger.json", "面试官服务");
    //     options.SwaggerEndpoint("/swagger/common/swagger.json", "通用服务");
    //     options.SwaggerEndpoint("/swagger/internal/swagger.json", "内部服务");
    //     options.SwaggerEndpoint("/swagger/platform/swagger.json", "平台端");
    //     //取消Schemas显示
    //     options.DefaultModelsExpandDepth(-1);
    // });

    app.UseDeveloperExceptionPage();
}

// app.UseRouting();

app.UseCors("cors");
app.UseStaticFiles();
app.UseAuthentication();

app.Use((context, next) =>
{
    context.Request.EnableBuffering();
    return next(context);
});

app.UseSerilogRequestLogging();

FlurlHttp.Configure(settings =>
{
    var jsonSettings = new JsonSerializerSettings
    {
        NullValueHandling = NullValueHandling.Ignore,
        ReferenceLoopHandling = ReferenceLoopHandling.Ignore
    };
    settings.JsonSerializer = new NewtonsoftJsonSerializer(jsonSettings);
});

app.MapControllers();

app.UseMiddleware<AuthorizationMiddleware>();
app.UseMiddleware<RequRespLog>();
app.UseMiddleware<ExceptionHandler>();

//微信Senparc注册
Senparc.CO2NET.RegisterServices.RegisterService.Start(new SenparcSetting
{
    Cache_Redis_Configuration = cfg.PublicRedisAddress
}).UseSenparcGlobal(false, null)
.UseSenparcWeixin(null, null);

//使用redis分布式缓存
CacheStrategyFactory.RegisterObjectCacheStrategy(() => RedisObjectCacheStrategy.Instance);

await AccessTokenContainer.RegisterAsync(cfg.WeChat!.SeekerApplet!.AppId, cfg.WeChat.SeekerApplet.Secret);
await AccessTokenContainer.RegisterAsync(cfg.WeChat.HrApplet!.AppId, cfg.WeChat.HrApplet.Secret);
await AccessTokenContainer.RegisterAsync(cfg.WeChat.InterviewApplet!.AppId, cfg.WeChat.InterviewApplet.Secret);

var contextFactory = app.Services.GetService<IDbContextFactory<StaffingContext>>();
using var _context = contextFactory!.CreateDbContext();
var applet = _context.App.Where(x => x.Type == ClientType.HrApplet
|| x.Type == ClientType.InterviewerApplet || x.Type == ClientType.SeekerApplet || x.Type == ClientType.JobMarketForSeeker
|| x.Type == ClientType.JobMarketForHr).ToList();

foreach (var item in applet)
{
    await AccessTokenContainer.RegisterAsync(item.AppId, item.AppSecret);
}

await Senparc.Weixin.MP.Containers.AccessTokenContainer.RegisterAsync(Constants.WeChatH5AppId, Constants.WeChatH5Secret);
await Senparc.Weixin.MP.Containers.AccessTokenContainer.RegisterAsync(Constants.WeChatH5AppId2, Constants.WeChatH5Secret2);

//产品环境接收微信公众号回调
app.UseMessageHandlerForMp("/wechath5notice", CustomMessageHandler.GenerateMessageHandler, options =>
    {
        options.AccountSettingFunc = context => senparcWeixinSetting;
    });
app.UseMessageHandlerForMp("/wechath5notice2", CustomMessageHandler2.GenerateMessageHandler, options =>
{
    options.AccountSettingFunc = context => senparcWeixinSetting2;
});

Console.WriteLine($"服务启动：{DateTime.Now:yyyy-MM-dd HH:mm:ss}");

app.Lifetime.ApplicationStarted.Register(OnAppStarted);
app.Lifetime.ApplicationStopped.Register(OnAppExit);

#region questPDF 设置
// 不设置的话会报异常。
Settings.License = LicenseType.Community;
// 禁用QuestPDF库中文本字符可用性的检查
Settings.CheckIfAllTextGlyphsAreAvailable = false;
#endregion

// 使用.DisableAntiforgery()禁用form请求防伪验证
var mainApi = app.MapGroup(string.Empty).DisableAntiforgery();

var seekerServices = app.Services.GetServices<ISeekerRouterBase>();
var seekerGroup = mainApi.MapGroup(Constants.ApiGroup.SeekerApi).MapGroup("v1")
.WithGroupName(Constants.ApiGroup.SeekerApi)
.WithMetadata(new ApiMeta { AuthType = ApiAuthType.用户端 });
foreach (var item in seekerServices)
    item.AddRoutes(seekerGroup);

var interviewerGroup = mainApi.MapGroup(Constants.ApiGroup.InterviewerApi).MapGroup("v1")
.WithGroupName(Constants.ApiGroup.InterviewerApi)
.WithMetadata(new ApiMeta { AuthType = ApiAuthType.用户端 });
var interviewerServices = app.Services.GetServices<IInterviewerRouterBase>();
foreach (var item in interviewerServices)
    item.AddRoutes(interviewerGroup);

var hrServices = app.Services.GetServices<IHrRouterBase>();
var hrGroup = mainApi.MapGroup(Constants.ApiGroup.HrApi).MapGroup("v1")
.WithGroupName(Constants.ApiGroup.HrApi)
.WithMetadata(new ApiMeta { AuthType = ApiAuthType.企业端 });
foreach (var item in hrServices)
    item.AddRoutes(hrGroup);

var adminServices = app.Services.GetServices<IAdminRouterBase>();
var adminGroup = mainApi.MapGroup("centreservice").MapGroup("v1")
.WithGroupName(Constants.ApiGroup.AdminApi)
.WithMetadata(new ApiMeta { AuthType = ApiAuthType.平台端 });
foreach (var item in adminServices)
    item.AddRoutes(adminGroup);

var internalServices = app.Services.GetServices<IInternalRouterBase>();
var internalGroup = mainApi.MapGroup(string.Empty)
.WithGroupName(Constants.ApiGroup.InternalApi)
.WithMetadata(new ApiMeta { AuthType = ApiAuthType.内部服务 });
foreach (var item in internalServices)
    item.AddRoutes(internalGroup);

var commonServices = app.Services.GetServices<ICommonRouterBase>();
var commonGroup = mainApi.MapGroup(Constants.ApiGroup.CommonApi).MapGroup("v1")
.WithGroupName(Constants.ApiGroup.CommonApi)
.WithMetadata(new ApiMeta { AuthType = ApiAuthType.任意登录 });
foreach (var item in commonServices)
    item.AddRoutes(commonGroup);

var callbackServices = app.Services.GetServices<ICallbackRouterBase>();
var callbackGroup = mainApi.MapGroup(string.Empty)
.WithGroupName(Constants.ApiGroup.CallbackApi);
foreach (var item in callbackServices)
    item.AddRoutes(callbackGroup);

app.Run();

void OnAppStarted()
{
    try
    {
        var host = app.Configuration[WebHostDefaults.ServerUrlsKey]!.Replace(":80", "").Replace("+", "localhost");
        var url = $"{host}/common/v1/service/warmapi";
        url.GetAsync();
    }
    catch { }
}

void OnAppExit()
{
    try
    {
        MyRedis.Client.Dispose();
    }
    catch { }
}
public class ProgramConfig
{
    public static void Init(WebApplicationBuilder builder)
    {
        builder.Services.AddScoped<RequestContext>();

        //自动注入
        builder.Services.AutoDependencyInjection();
        builder.Services.AutoDependencyInjectionForOnlyType();
    }
}

public static class ServiceLocator
{
    public static IServiceProvider Instance { get; set; } = default!;
}
