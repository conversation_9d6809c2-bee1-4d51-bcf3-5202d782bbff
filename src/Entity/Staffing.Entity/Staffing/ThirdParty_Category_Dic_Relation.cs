﻿using Config.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Staffing.Entity.Staffing
{
    [Table("thirdparty_category_dic_relation")]
    public class ThirdParty_Category_Dic_Relation
    {
        [Key]
        public required string ThirdCategoryId {  get; set; }
        public string? ThirdCategoryName { get; set;}
        public required int NkpCategoryId { get; set;}
        public string? NkpCategoryName { get; set;}
        public DateTime UpdatedTime {  get; set; }  = DateTime.Now;
        public DateTime CreatedTime {  get; set; }  = DateTime.Now;
        public required PostSouce Source {  get; set; }
    }
}
