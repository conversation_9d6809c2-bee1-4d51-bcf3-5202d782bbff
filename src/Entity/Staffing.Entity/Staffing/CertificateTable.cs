using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.ConstrainedExecution;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 证书表
/// </summary>
[Table("certificate_table")]
public class CertificateTable
{
    /// <summary>
    /// 证书记录唯一标识
    /// </summary>
    [Key]
    [Column("Id", TypeName = "varchar(18)")]
    [Required]
    [StringLength(18)]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 商品类型
    /// </summary>
    [Column("CertificateType", TypeName = "enum('国家职业资格证书','行业认证证书','国际通用证书','职业技能等级证书','专项能力证书','职称证书')")]
    [Required]
    public CertificateType CertificateType { get; set; }

    /// <summary>
    /// 证书名称，20个字以内
    /// </summary>
    [Column("CertificateName", TypeName = "varchar(20)")]
    [Required]
    [StringLength(20)]
    public string CertificateName { get; set; } = default!;

    /// <summary>
    /// 证书类目
    /// </summary>
    [Column("CertificateCategory", TypeName = "varchar(255)")]
    public string? CertificateCategory { get; set; }

    /// <summary>
    /// 商品图，存储图片路径等相关信息
    /// </summary>
    [Column("ProductImage", TypeName = "text")]
    public string? ProductImage { get; set; }

    /// <summary>
    /// 企微群码，存储二维码图片路径等相关信息
    /// </summary>
    [Column("EnterpriseWechatQrCode", TypeName = "text")]
    public string? EnterpriseWechatQrCode { get; set; }

    /// <summary>
    /// 企微群ID
    /// <para>List&lt;Config.CommonModel.QYWechat.EnterpriseWechatGroupId&gt;的JSON</para>
    /// </summary>
    [Column("EnterpriseWechatGroupId", TypeName = "varchar(255)")]
    public string? EnterpriseWechatGroupId { get; set; }

    /// <summary>
    /// 用户须知，60字以内
    /// </summary>
    [Column("UserNotice", TypeName = "varchar(60)")]
    [StringLength(60)]
    public string? UserNotice { get; set; }

    /// <summary>
    /// 报名表单
    /// </summary>
    [Column("RegistrationForm", TypeName = "varchar(255)")]
    public string? RegistrationForm { get; set; }

    /// <summary>
    /// 工种/职业方向
    /// </summary>
    [Column("JobPosition", TypeName = "varchar(255)")]
    public string? JobPosition { get; set; }

    /// <summary>
    /// 相关专业，建议800字以内
    /// </summary>
    [Column("RelevantMajors", TypeName = "text")]
    public string? RelevantMajors { get; set; }

    /// <summary>
    /// 发证机构
    /// </summary>
    [Column("IssuingAuthority", TypeName = "varchar(255)")]
    public string? IssuingAuthority { get; set; }

    /// <summary>
    /// 查询平台
    /// </summary>
    [Column("QueryPlatform", TypeName = "varchar(255)")]
    public string? QueryPlatform { get; set; }
    
    /// <summary>
    /// 企业团购状态
    /// </summary>
    [Column("EnterpriseGroupBuying", TypeName = "enum('开启','关闭')")]
    public EnterpriseGroupBuyingStatus EnterpriseGroupBuying { get; set; } = EnterpriseGroupBuyingStatus.关闭;

    /// <summary>
    /// 开售时间，定时开售时记录时间，否则可为空
    /// </summary>
    [Column("SaleStartTime", TypeName = "timestamp")]
    public DateTime? SaleStartTime { get; set; }

    /// <summary>
    /// 培训批次类型
    /// </summary>
    [Column("TrainingBatchType", TypeName = "enum('无线下培训','线下培训')")]
    public TrainingBatchType TrainingBatchType { get; set; } = TrainingBatchType.无线下培训;
    
    /// <summary>
    /// 培训时间
    /// </summary>
    public DateTime? TrainingTime { get; set; }

    /// <summary>
    /// 详情描述，记录证书详情相关文本内容
    /// </summary>
    [Column("DetailDescription", TypeName = "text")]
    public string? DetailDescription { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    [Column("Creator", TypeName = "varchar(255)")]
    public string? Creator { get; set; }

    /// <summary>
    /// 创建时间，默认当前时间
    /// </summary>
    [Column("CreateTime", TypeName = "timestamp")]
    public DateTime CreateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间，默认当前时间，更新时自动更新为当前时间
    /// </summary>
    [Column("UpdateTime", TypeName = "timestamp")]
    public DateTime UpdateTime { get; set; } = DateTime.Now;
    
    public bool Deleted { get; set; }
    /// <summary>
    /// 创建人企业微信名称
    /// </summary>
    public string? EnterpriseWechatName { get; set; }
    
    /// <summary>
    /// 企业微信群名称
    /// </summary>
    public string? EnterpriseWechatGroupName{ get; set; }
    
    
    /// <summary>
    /// 证书学习地址 需求变更不再使用
    /// </summary> 
    public string? StudyAddress { get; set; }
    
    // /// <summary>
    // /// 证书学习地址 需求变更不再使用
    // /// </summary>
    // public List<StudyModel>? StudyList { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    public ICollection<CertificateSpec>? Specs { get; set; }
    
    /// <summary>
    /// 培训批次
    /// </summary>
    public ICollection<CertificateTrainingBatch>? TrainingBatch { get; set; }
    
    /// <summary>
    /// hr表
    /// </summary>
    public User_Hr UserHr { get; set; } = default!;
}

// public class StudyModel
// {
//     /// <summary>
//     /// 标题
//     /// </summary>
//     public string? Title { get; set; }
//     /// <summary>
//     /// 学习地址
//     /// </summary>
//     public string? Url { get; set; }
// }

/// <summary>
/// 证书类型枚举
/// </summary>
public enum CertificateType
{
    国家职业资格证书,
    行业认证证书,
    国际通用证书,
    职业技能等级证书,
    专项能力证书,
    职称证书
}

/// <summary>
/// 企业团购状态枚举
/// </summary>
public enum EnterpriseGroupBuyingStatus
{
    开启,
    关闭
}

/// <summary>
/// 培训批次类型枚举
/// </summary>
public enum TrainingBatchType
{
    无线下培训,
    线下培训
}