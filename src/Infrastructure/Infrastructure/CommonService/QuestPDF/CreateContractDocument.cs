﻿using Config.CommonModel.QuestPDF;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace Infrastructure.CommonService.QuestPDF;

public class CreateContractDocument : IDocument
{
    public ContractModel _model { get; }
    public string FONT_NAME { get; set; } = "FangSong_GB2312"; // FangSong_GB2312, <PERSON><PERSON><PERSON><PERSON>, WenQuanYi Zen Hei Mono

    public CreateContractDocument(ContractModel model)
    {
        _model = model;
    }

    public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

    public void Compose(IDocumentContainer container)
    {
        container
            .Page(page =>
            {
                //设置页面的边距
                page.MarginHorizontal(30);
                //字体默认大小12号字体
                page.DefaultTextStyle(x => x.FontSize(12));
                //页眉部分
                page.Header().Element(BuildHeaderInfo);
                //内容部分
                page.Content().Element(BuildContentInfo);
                //页脚部分
                page.Footer().AlignCenter().Text(text =>
                {
                    text.CurrentPageNumber();
                    text.Span(" / ");
                    text.TotalPages();
                });
            });
    }

    void BuildHeaderInfo(IContainer container)
    {
        container.Column(column =>
        {
            column.Item().PaddingRight(5).AlignRight().Text(_model.ContractNumber).FontFamily(FONT_NAME).FontSize(12).Black();
        });
    }

    void BuildContentInfo(IContainer container)
    {
        container.PaddingVertical(10).Column(column =>
        {
            var contentTextStyle = TextStyle.Default.FontFamily(FONT_NAME).FontSize(12).Black();
            column.Spacing(2);// 行间距
            column.Item().PaddingVertical(15).AlignCenter().Text("服务协议").FontFamily(FONT_NAME).FontSize(15).Black();
            column.Item().PaddingHorizontal(20).Text("甲    方：" + _model.CustomerCompanyName).FontFamily(FONT_NAME).FontSize(12).Black();
            column.Item().PaddingHorizontal(20).Text("地    址：" + _model.CustomerCompanyAddress).FontFamily(FONT_NAME).FontSize(12).Black();
            column.Item().PaddingHorizontal(20).Text("联 系 人：" + _model.CustomerCompanyContacter).FontFamily(FONT_NAME).FontSize(12).Black();
            column.Item().PaddingHorizontal(20).Text("联系电话：" + _model.CustomerCompanyContacterNumber).FontFamily(FONT_NAME).FontSize(12).Black();
            column.Item().PaddingHorizontal(20).Text("");
            column.Item().PaddingHorizontal(20).Text("乙    方：" + _model.SellerCompanyName).FontFamily(FONT_NAME).FontSize(12).Black();
            column.Item().PaddingHorizontal(20).Text("地    址：" + _model.SellerCompanyAddress).FontFamily(FONT_NAME).FontSize(12).Black();
            column.Item().PaddingHorizontal(20).Text("联 系 人：" + _model.SellerCompanyContacter).FontFamily(FONT_NAME).FontSize(12).Black();
            column.Item().PaddingHorizontal(20).Text("联系电话：" + _model.SellerCompanyContacterNumber).FontFamily(FONT_NAME).FontSize(12).Black();
            column.Item().Text("");
            column.Item().Text($"一、您公司{_model.OrderInfo.Items.Count}个员工账号,通过数字诺亚订购诺快聘平台提供的如下产品/服务：").Style(contentTextStyle);
            column.Item().PaddingTop(5).Element(CreateTable);
            column.Item().PaddingTop(5).Text(@"二、您和诺快聘就上述服务的其它权利义务，应以双方共同确认的服务条款为准（确认形式包括但不限于双方盖章、或您在订购服务时予以线上点击确认）。").Style(contentTextStyle);
            column.Item().PaddingTop(5).Text(@"三、您应按时付款。您选择预付费服务的，您付费之后，诺快聘才开始为您提供服务，您未在下单后7天内付费的，您与诺快聘就服务所达成的一切行为失效。您选择后付费服务的，您先使用并在使用服务后根据所订购产品的不同按小时或按天支付费用，具体各产品的扣费规则请查看 www.nuopin.cn 上的产品页面且以页面公布的当时有效的计费模式、标准为准。").Style(contentTextStyle);
            column.Item().PaddingTop(5).Text(@"四、诺快聘保留在您未按照约定支付全部费用之前不向您提供服务和/或技术支持，或者终止服务和/或技术支持的权利。同时，诺快聘保留对后付费服务中的欠费行为追究法律责任的权利。").Style(contentTextStyle);
            column.Item().PaddingTop(5).Text("五、您应将款项支付至诺快聘指定的如下账号").Style(contentTextStyle);
            column.Item().Text("开户名称：" + _model.AccountName).FontFamily(FONT_NAME).FontSize(12).ExtraBold();
            column.Item().Text("开 户 行：" + _model.AccountAddress).FontFamily(FONT_NAME).FontSize(12).ExtraBold();
            column.Item().Text("账    号：" + _model.Account).FontFamily(FONT_NAME).FontSize(12).ExtraBold();
            column.Item().PaddingTop(5).Text(@"六、发票：您可登陆 www.nuopin.cn 点击申请发票，诺快聘将向您开具等额发票并邮寄给您").Style(contentTextStyle);
            column.Item().PaddingTop(25).Element(CreateTableForSignature);
        });
    }

    /// <summary>
    /// 创建表格
    /// </summary>
    /// <param name="container">container</param>
    void CreateTable(IContainer container)
    {
        var headerStyle = TextStyle.Default.FontFamily(FONT_NAME).Black().FontSize(11);

        container.Table(table =>
        {
            table.ColumnsDefinition(columns =>
            {
                columns.ConstantColumn(25);
                columns.RelativeColumn();
                columns.RelativeColumn(3);
                columns.RelativeColumn(0.6f);
                columns.RelativeColumn(0.5f);
                columns.RelativeColumn(0.5f);
            });

            table.Header(header =>
            {
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("序号").Style(headerStyle);
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("月账单编号").Style(headerStyle);
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("数字诺亚项目").Style(headerStyle);
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("采购产品").Style(headerStyle);
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("实付金额").Style(headerStyle);
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("账单时间").Style(headerStyle);
                // 设置了表头单元格的属性
                //header.Cell().ColumnSpan(6).PaddingTop(5).Border(1).BorderColor(Colors.Black);
            });

            int index = 1;
            foreach (var item in _model.OrderInfo.Items)
            {
                table.Cell().Element(CellStyle).AlignCenter().Text($"{index++}").FontFamily(FONT_NAME).FontSize(10);
                table.Cell().Element(CellStyle).AlignCenter().Text(item.OrderNumber).FontFamily(FONT_NAME).FontSize(10);
                table.Cell().Element(CellStyle).AlignCenter().Text($"{item.ProjectName}").FontFamily(FONT_NAME).FontSize(10);
                table.Cell().Element(CellStyle).AlignCenter().Text($"{item.BountyName}").FontFamily(FONT_NAME).FontSize(10);
                table.Cell().Element(CellStyle).AlignCenter().Text($"{item.BountyAmount}").FontFamily(FONT_NAME).FontSize(10);
                table.Cell().Element(CellStyle).AlignCenter().Text($"{item.BountyTime}").FontFamily(FONT_NAME).FontSize(10);
                static IContainer CellStyle(IContainer container) => container.Border(1).BorderColor(Colors.Black).PaddingVertical(5);
            }

            var totalPrice = _model.OrderInfo.Items.Sum(x => x.BountyAmount);
            table.Cell().Element(container => container.BorderLeft(1).BorderBottom(1));
            table.Cell().Element(container => container.BorderBottom(1).BorderRight(1).PaddingVertical(3)).AlignCenter().Text("总计").FontFamily(FONT_NAME).FontSize(11);
            table.Cell().Element(container => container.BorderBottom(1).PaddingVertical(3)).AlignRight().Text($"{totalPrice}元").FontFamily(FONT_NAME).FontSize(11);
            table.Cell().Element(container => container.BorderBottom(1)).Text("");
            table.Cell().Element(container => container.BorderBottom(1)).Text("");
            table.Cell().Element(container => container.BorderBottom(1).BorderRight(1)).Text("");
        });
    }

    /// <summary>
    /// 创建表格
    /// </summary>
    /// <param name="container">container</param>
    void CreateTableForSignature(IContainer container)
    {
        var headerStyle = TextStyle.Default.FontFamily(FONT_NAME).Black().FontSize(12);

        container.Table(table =>
        {
            table.ColumnsDefinition(columns =>
            {
                columns.RelativeColumn();
                columns.RelativeColumn();
            });

            table.Header(header =>
            {
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("甲方：" + _model.CustomerCompanyName).Style(headerStyle);
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("乙方：" + _model.SellerCompanyName).Style(headerStyle);
                // 设置了表头单元格的属性
                //header.Cell().ColumnSpan(6).PaddingTop(5).Border(1).BorderColor(Colors.Black);
            });

            table.Cell().Element(CellStyle).AlignCenter().AlignMiddle().Text("（盖章处）").FontFamily(FONT_NAME).Black().FontSize(10);
            table.Cell().Element(CellStyle).AlignCenter().AlignMiddle().Text("（盖章处）").FontFamily(FONT_NAME).Black().FontSize(10);
            static IContainer CellStyle(IContainer container) => container.Height(80).Border(1).BorderColor(Colors.Black).PaddingVertical(5);

            table.Cell().Element(CellStyle2).AlignLeft().Text($"日期：{DateTime.Now:yyyy-MM-dd}").FontFamily(FONT_NAME).Black().FontSize(10);
            table.Cell().Element(CellStyle2).AlignLeft().Text($"日期：{DateTime.Now:yyyy-MM-dd}").FontFamily(FONT_NAME).Black().FontSize(10);
            static IContainer CellStyle2(IContainer container) => container.Border(1).BorderColor(Colors.Black).PaddingVertical(5);

            // todo:图片尺寸现在是写死的 120*120，应该根据图片实际尺寸来 ImageUriToStreamAsync(Model.SellerCompanyImageUrl).GetAwaiter().GetResult()!
            table.Cell().Unconstrained().TranslateX(70).TranslateY(-110).Width(120).Height(120).Image(ImageUriToStreamAsync(_model.CustomerCompanyImageUrl).GetAwaiter().GetResult()!);
            table.Cell().Unconstrained().TranslateX(70).TranslateY(-110).Width(120).Height(120).Image(ImageUriToStreamAsync(_model.SellerCompanyImageUrl).GetAwaiter().GetResult()!);

        });
    }
    public async Task<Stream?> ImageUriToStreamAsync(string imageUri)
    {
        Stream? stream = null;
        using (HttpClient client = new HttpClient())
        {
            HttpResponseMessage response = await client.GetAsync(imageUri, HttpCompletionOption.ResponseHeadersRead);
            if (response.IsSuccessStatusCode)
            {
                stream = await response.Content.ReadAsStreamAsync();
            }
        }
        return stream;
    }
}

public class CreateContractDocument2 : IDocument
{
    public ContractModel2 _model { get; }
    public string FONT_NAME { get; set; } = "FangSong_GB2312"; // FangSong_GB2312, DejaVu Sans, WenQuanYi Zen Hei Mono

    public CreateContractDocument2(ContractModel2 model)
    {
        _model = model;
    }

    public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

    public void Compose(IDocumentContainer container)
    {
        container
            .Page(page =>
            {
                //设置页面的边距
                page.MarginHorizontal(30);
                //字体默认大小12号字体
                page.DefaultTextStyle(x => x.FontSize(12));
                ////页眉部分
                //page.Header().Element(BuildHeaderInfo);
                //内容部分
                page.Content().Element(BuildContentInfo);
                //页脚部分
                page.Footer().AlignCenter().Text(text =>
                {
                    text.CurrentPageNumber();
                    text.Span(" / ");
                    text.TotalPages();
                });
            });
    }

    void BuildHeaderInfo(IContainer container)
    {
        container.Column(column =>
        {
            column.Item().PaddingRight(5).AlignRight().Text(_model.ContractNumber).FontFamily(FONT_NAME).FontSize(12).Black();
        });
    }

    void BuildContentInfo(IContainer container)
    {
        container.PaddingVertical(10).Column(column =>
        {
            var contentTextStyle = TextStyle.Default.FontFamily(FONT_NAME).FontSize(12).LineHeight(2f);
            var topTextStyle = TextStyle.Default.FontFamily(FONT_NAME).FontSize(12).LineHeight(20f / 12);
            var topTextStyle2 = TextStyle.Default.FontFamily(FONT_NAME).FontSize(12).Black().ExtraBold();
            int contentPaddingTop = 0, topPaddingHorizontal = 5;
            column.Spacing(2);// 行间距
            column.Item().PaddingVertical(topPaddingHorizontal).AlignCenter().Text("服务协议").FontFamily(FONT_NAME).FontSize(18).Black().ExtraBold();
            column.Item().AlignMiddle().PaddingTop(10).Text("");
            column.Item().PaddingHorizontal(topPaddingHorizontal).Text("甲 方：" + _model.CustomerCompanyName).Style(topTextStyle2);
            column.Item().PaddingHorizontal(topPaddingHorizontal).Text("地 址：" + _model.CustomerCompanyAddress).Style(topTextStyle);
            column.Item().PaddingHorizontal(topPaddingHorizontal).Text("联 系 人：" + _model.CustomerCompanyContacter).Style(topTextStyle);
            column.Item().PaddingHorizontal(topPaddingHorizontal).Text("联系电话：" + _model.CustomerCompanyContacterNumber).Style(topTextStyle);
            //
            column.Item().PaddingHorizontal(topPaddingHorizontal).Text("乙 方：" + _model.SellerCompanyName).Style(topTextStyle2);
            column.Item().PaddingHorizontal(topPaddingHorizontal).Text("地 址：" + _model.SellerCompanyAddress).Style(topTextStyle);
            column.Item().PaddingHorizontal(topPaddingHorizontal).Text("联 系 人：" + _model.SellerCompanyContacter).Style(topTextStyle);
            column.Item().PaddingHorizontal(topPaddingHorizontal).Text("联系电话：" + _model.SellerCompanyContacterNumber).Style(topTextStyle);
            column.Item().Text("");
            column.Item().Text($"一、您和诺快聘就下述服务的其它权利义务，应以双方共同确认的服务条款为准（确认形式包括但不限于双方盖章或您在订购服务时予以线上点击确认）。").Style(contentTextStyle);
            column.Item().PaddingTop(contentPaddingTop).Text(@"二、您应按时付款。您选择后付费服务，诺快聘会按您要求开始为您提供服务，成果确认后3天内需要线上付费，具体各产品的扣费规则请查看linggong.nuopin.cn上的产品页面且以页面公布的当时有效的计费模式、标准为准。").Style(contentTextStyle);
            column.Item().PaddingTop(contentPaddingTop).Text(@"三、诺快聘保留在您未按照约定支付全部费用之前不向您提供服务和/或技术支持，或者终止服务和/ 或技术支持的权利。同时，诺快聘保留对后付费服务中的欠费行为追究法律责任的权利。").Style(contentTextStyle);
            column.Item().PaddingTop(contentPaddingTop).Text(@"四、您应通过诺快聘平台支付功能（支持银联在线、对公转账等），将款项充值至平台指定账户，充值完成后，系统将自动扣减相应服务费用。").Style(contentTextStyle);
            column.Item().PaddingTop(contentPaddingTop).Text(@"五、发票：您可登陆 linggong.nuopin.cn 点击申请发票，诺快聘将向您开具等额发票并邮寄给您。").Style(contentTextStyle);
            column.Item().PaddingTop(contentPaddingTop).Text("六、您通过诺快聘平台，订购如下产品/服务：").Style(contentTextStyle);
            column.Item().PaddingTop(5).Element(CreateTable);
            column.Item().PaddingTop(20).Element(CreateTableForSignature);
        });
    }

    /// <summary>
    /// 创建表格
    /// </summary>
    /// <param name="container">container</param>
    void CreateTable(IContainer container)
    {
        var headerStyle = TextStyle.Default.FontFamily(FONT_NAME).FontSize(11);
        var cellStyle = TextStyle.Default.FontFamily(FONT_NAME).FontSize(10);

        container.Table(table =>
        {
            table.ColumnsDefinition(columns =>
            {
                columns.ConstantColumn(35);
                columns.RelativeColumn();
                columns.RelativeColumn(2f);
                columns.RelativeColumn(0.6f);
                columns.RelativeColumn(0.7f);
                columns.RelativeColumn(0.6f);
                columns.RelativeColumn(0.5f);
            });

            table.Header(header =>
            {
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("序").Style(headerStyle);
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("订单号").Style(headerStyle);
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("项目名称").Style(headerStyle);
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("服务方式").Style(headerStyle);
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("确认成果").Style(headerStyle);
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("确认时间").Style(headerStyle);
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("应付金额").Style(headerStyle);
                // 设置了表头单元格的属性
                //header.Cell().ColumnSpan(6).PaddingTop(5).Border(1).BorderColor(Colors.Black);
            });

            int index = 1;
            foreach (var item in _model.OrderInfo2.Items)
            {
                table.Cell().Element(CellStyle).AlignCenter().Text($"{index++}").Style(cellStyle);
                table.Cell().Element(CellStyle).AlignCenter().Text(item.OrderNumber).Style(cellStyle);
                table.Cell().Element(CellStyle).AlignCenter().Text($"{item.ProjectName}").Style(cellStyle);
                table.Cell().Element(CellStyle).AlignCenter().Text($"{item.BountyName}").Style(cellStyle);
                table.Cell().Element(CellStyle).AlignCenter().Text($"{item.Achievement}").Style(cellStyle);
                table.Cell().Element(CellStyle).AlignCenter().Text($"{item.BountyTime}").Style(cellStyle);
                table.Cell().Element(CellStyle).AlignCenter().Text($"{item.BountyAmount}").Style(cellStyle);
                static IContainer CellStyle(IContainer container) => container.Border(1).BorderColor(Colors.Black).PaddingVertical(5);
            }

            var totalPrice = _model.OrderInfo2.Items.Sum(x => x.BountyAmount);
            table.Cell().Element(container => container.Border(1).BorderColor(Colors.Black).PaddingVertical(5)).AlignCenter().Text("总计").FontFamily(FONT_NAME).FontSize(11);
            table.Cell().ColumnSpan(6).Element(container => container.Border(1).BorderColor(Colors.Black).PaddingVertical(5).PaddingLeft(15)).AlignLeft().Text($"{totalPrice}元").FontFamily(FONT_NAME).FontSize(11);
        });
    }

    /// <summary>
    /// 创建表格
    /// </summary>
    /// <param name="container">container</param>
    void CreateTableForSignature(IContainer container)
    {
        var headerStyle = TextStyle.Default.FontFamily(FONT_NAME).Black().FontSize(12);

        container.Table(table =>
        {
            table.ColumnsDefinition(columns =>
            {
                columns.RelativeColumn();
                columns.RelativeColumn();
            });

            table.Header(header =>
            {
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("甲方：" + _model.CustomerCompanyName).Style(headerStyle);
                header.Cell().Border(1).BorderColor(Colors.Black).AlignCenter().PaddingVertical(1).Text("乙方：" + _model.SellerCompanyName).Style(headerStyle);
                // 设置了表头单元格的属性
                //header.Cell().ColumnSpan(6).PaddingTop(5).Border(1).BorderColor(Colors.Black);
            });

            table.Cell().Element(CellStyle).AlignCenter().AlignMiddle().Text("（盖章处）").FontFamily(FONT_NAME).Black().FontSize(10);
            table.Cell().Element(CellStyle).AlignCenter().AlignMiddle().Text("（盖章处）").FontFamily(FONT_NAME).Black().FontSize(10);
            static IContainer CellStyle(IContainer container) => container.Height(80).Border(1).BorderColor(Colors.Black).PaddingVertical(5);

            table.Cell().Element(CellStyle2).AlignLeft().Text($"日期：{DateTime.Now:yyyy-MM-dd}").FontFamily(FONT_NAME).Black().FontSize(10);
            table.Cell().Element(CellStyle2).AlignLeft().Text($"日期：{DateTime.Now:yyyy-MM-dd}").FontFamily(FONT_NAME).Black().FontSize(10);
            static IContainer CellStyle2(IContainer container) => container.Border(1).BorderColor(Colors.Black).PaddingVertical(5);

            // todo:图片尺寸现在是写死的 120*120，应该根据图片实际尺寸来 ImageUriToStreamAsync(Model.SellerCompanyImageUrl).GetAwaiter().GetResult()!
            table.Cell().Unconstrained().TranslateX(70).TranslateY(-110).Width(120).Height(120).Image(ImageUriToStreamAsync(_model.CustomerCompanyImageUrl).GetAwaiter().GetResult()!);
            table.Cell().Unconstrained().TranslateX(70).TranslateY(-110).Width(120).Height(120).Image(ImageUriToStreamAsync(_model.SellerCompanyImageUrl).GetAwaiter().GetResult()!);

        });
    }
    public async Task<Stream?> ImageUriToStreamAsync(string imageUri)
    {
        Stream? stream = null;
        using (HttpClient client = new HttpClient())
        {
            HttpResponseMessage response = await client.GetAsync(imageUri, HttpCompletionOption.ResponseHeadersRead);
            if (response.IsSuccessStatusCode)
            {
                stream = await response.Content.ReadAsStreamAsync();
            }
        }
        return stream;
    }
}