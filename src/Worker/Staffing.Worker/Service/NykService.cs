using System.Text;
using Config;
using Config.CommonModel.Business;
using Config.CommonModel.TalentResume;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Infrastructure.NoahCommon;
using Microsoft.EntityFrameworkCore;
using MiniSoftware;
using Noah.Aliyun.Storage;
using ServiceStack;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Elasticsearch;

namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class NykService
{
    private readonly LogManager _logger;
    private readonly IObjectStorage _objectStorage;
    private readonly ParseResume _parseResume;
    private readonly CommonDicService _commonDicService;
    private readonly TencentMapHelper _tencentMapHelper;
    private CommonTalentResumeService _commonTalentResumeService;
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly EsHelper _esHelper;
    public NykService(LogManager logger,
    IObjectStorage objectStorage, ParseResume parseResume, CommonDicService commonDicService, EsHelper esHelper,
    TencentMapHelper tencentMapHelper, CommonTalentResumeService commonTalentResumeService, IDbContextFactory<StaffingContext> contextFactory)
    {
        _logger = logger;
        _objectStorage = objectStorage;
        _parseResume = parseResume;
        _commonDicService = commonDicService;
        _tencentMapHelper = tencentMapHelper;
        _commonTalentResumeService = commonTalentResumeService;
        _contextFactory = contextFactory;
        _esHelper = esHelper;
    }

    public async Task SubNykResumeSync(CancellationToken cancel)
    {
        //处理消息
        NykTalentResume? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<NykTalentResume>(SubscriptionKey.Nyk.NykResumeNotify);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                // 处理消息
                try
                {
                    await SendNykResume(msg);
                }
                catch (Exception e)
                {
                    _logger.Error($"{SubscriptionKey.Nyk.NykResumeNotify}消息处理出错1", e.Message, msg);
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.Nyk.NykResumeNotify}消息处理出错", Tools.GetErrMsg(e), msg!);
                await Task.Delay(1000);
            }

            await Task.Delay(100);
        }
    }

    public async Task SubNykUserLoginNotifySync(CancellationToken cancel)
    {
        //处理消息
        string?[]? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.DelayQueuePop(SubscriptionKey.Nyk.NykUserLoginNotify, 10);

                if (msg == null || msg.Length == 0)
                {
                    await Task.Delay(500);
                    continue;
                }

                var data = msg.Select(s => JsonSerializer.DeserializeFromString<NykTalentUpdateLoginTime>(s)).ToList();

                using var _context = _contextFactory.CreateDbContext();

                //开启事务
                using var transaction = _context.Database.BeginTransaction();
                foreach (var item in data)
                {
                    _context.Talent_Resume.Where(w => w.Mobile == item.Mobile && w.Source == TalentResumeSource.诺优考 && w.LastLoginTime < item.LoginTime)
                    .ExecuteUpdate(s => s.SetProperty(b => b.LastLoginTime, item.LoginTime).SetProperty(b => b.UpdatedTime, DateTime.Now));
                }
                transaction.Commit();
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.Nyk.NykUserLoginNotify}消息处理出错", Tools.GetErrMsg(e), msg!);
                await Task.Delay(1000);
            }

            await Task.Delay(100);
        }
    }

    public async Task SubNykHrUnreadResumeSync(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.DelayQueuePop(SubscriptionKey.Nyk.NykHrUnreadResume, 1)?.FirstOrDefault();

                if (msg == null)
                {
                    await Task.Delay(5000);
                    continue;
                }

                using var _context = _contextFactory.CreateDbContext();

                var resume = _context.Talent_Resume.Where(x => x.Mobile == msg && x.Source == TalentResumeSource.诺优考).FirstOrDefault();
                if (resume == null)
                {
                    await Task.Delay(20);
                    continue;
                }

                if (resume.HrIds == null || resume.HrIds.Count == 0)
                {
                    await Task.Delay(20);
                    continue;
                }

                //只检索最近15天的联系记录，如果联系过，则不用处理
                var hasContact = _context.Talent_Resume_Connect.Any(w => w.CreatedTime > DateTime.Now.AddDays(-15)
                && resume.HrIds.Contains(w.HrId) && w.ResumeId == resume.Id && w.Type == ConnectType.拨打电话);
                if (hasContact)
                {
                    await Task.Delay(20);
                    continue;
                }

                var hrMobiles = _context.User_Hr.Where(w => resume.HrIds.Contains(w.UserId)).Select(s => s.User.Mobile).ToList();

                var deptLevels = _context.Dd_User_Dept.Where(w => hrMobiles.Contains(w.Dd_User.Mobile)).Select(s => s.Dd_Dept.Level)
                .Where(x => !string.IsNullOrEmpty(x) && x.Contains(".")).Distinct().ToList();
                resume.HrIds = null;
                //deptLevels是1.xxx.yyy.这种格式，查找第一个.之后和第二个.之间的字符
                var deptIds = deptLevels.Select(s => s.Split('.')[1]).Where(x => !string.IsNullOrEmpty(x)).Distinct().ToList();
                resume.DeptIds = deptIds;

                //部门15天未联系
                MyRedis.DelayQueue(SubscriptionKey.Nyk.NykDeptUnreadResume, new string[] { resume.Mobile }, 3600 * 24 * 15);

                _context.SaveChanges();
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.Nyk.NykHrUnreadResume}消息处理出错", Tools.GetErrMsg(e), msg!);
                await Task.Delay(10000);
            }

            await Task.Delay(100);
        }
    }

    /// <summary>
    /// 诺优考简历到期前3天未联系，发送钉钉消息
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubNykHrUnreadResumeNotifySync(CancellationToken cancel)
    {
        try
        {
            //当天只处理一次
            if (!MyRedis.Client.SetNx($"{RedisKey.CheckExists.Key}:nykurn:{DateTime.Now.ToYYYY_MM_DD()}", 1, 86400))
                return;

            // 从redis中取满足条件的数据
            var beginTime = DateTime.Today.AddDays(3).ToUnixTime();
            var endTime = DateTime.Today.AddDays(4).ToUnixTime();

            var msg = MyRedis.Client.ZRangeByScore(SubscriptionKey.Nyk.NykHrUnreadResume, beginTime, endTime);

            if (msg == null || msg.Length == 0)
                return;

            var esClient = _esHelper.GetClient();

            var hrIds = new List<string>();
            foreach (var mobiles in msg.Chunk(100))
            {
                //es查询文档EsTalentResume，mobiles包含在内的，status不是9，并且source是诺优考的，查询hrids
                var result = esClient.Search<EsTalentResume>(s => s
                .Index(_esHelper.GetIndex().TalentResume)
                .Query(q => q
                    .Bool(b => b
                        .Filter(f => f
                            .Terms(t => t.Field(ff => ff.Mobile).Terms(mobiles))
                            && f.Term(t => t.Source, TalentResumeSource.诺优考)
                        )
                        .MustNot(mn => mn
                            .Term(t => t.Status, TalentResumeStatus.Deleted)
                        )
                    )
                ).Source(sf => sf.Includes(i => i.Fields(f => f.HrIds)))
                .Size(1000));

                if (result.Documents.Count == 0)
                    continue;

                hrIds.AddRange(result.Documents.SelectMany(s => s.HrIds ?? new List<string>()));

                await Task.Delay(20);
            }

            //去掉空内容
            hrIds = hrIds.Where(w => !string.IsNullOrEmpty(w)).ToList();

            //根据hrids分组，发送消息
            var hrGroup = hrIds.GroupBy(g => g)
            .Select(s => new
            {
                hrId = s.Key,
                Ct = s.Count()
            }).ToList();

            var dHrids = hrIds.Distinct().ToList();

            //es查询hr的诺优考人才数量
            var hrResult = await esClient.SearchAsync<EsTalentResume>(s => s
            .Index(_esHelper.GetIndex().TalentResume)
            .Query(q => q
                .Bool(b => b
                    .Filter(f => f
                        .Terms(t => t.Field(ff => ff.HrIds).Terms(dHrids))
                        && f.Term(t => t.Source, TalentResumeSource.诺优考)
                    )
                    .MustNot(mn => mn
                        .Term(t => t.Status, TalentResumeStatus.Deleted)
                    )
                )
            ).Aggregations(a => a
                .Terms("hrId", t => t.Field(f => f.HrIds))
            ));

            var hrCount = hrResult.Aggregations.Terms("hrId").Buckets.ToDictionary(k => k.Key, v => v.DocCount);

            //发送消息
            var nts = new List<MsgNotifyModel>();
            foreach (var item in hrGroup)
            {
                var hrTalentNum = hrCount.GetValueOrDefault(item.hrId, 0) ?? 0;

                if (hrTalentNum <= 0)
                    continue;

                nts.Add(new MsgNotifyModel
                {
                    Type = MsgNotifyType.诺优考人才线索提醒,
                    EventTime = DateTime.Now,
                    UserId = item.hrId,
                    Data = $"诺优考人才线索池数据概况：您当前的诺优考人才线索储备有{hrTalentNum}人，其中{item.Ct}人12天内未处理，请及时跟进处理，若未处理3天后将推送至分公司人才池。"
                });
            }
            MsgHelper.SendDingdingNotify(nts.ToArray());
        }
        catch (Exception e)
        {
            _logger.Error($"诺优考简历到期前3天未联系，发送钉钉消息出错", Tools.GetErrMsg(e));
            await Task.Delay(10000);
        }
    }

    public async Task SubNykDeptUnreadResumeSync(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.DelayQueuePop(SubscriptionKey.Nyk.NykDeptUnreadResume, 1)?.FirstOrDefault();

                if (msg == null)
                {
                    await Task.Delay(5000);
                    continue;
                }

                using var _context = _contextFactory.CreateDbContext();

                var resume = _context.Talent_Resume.Where(x => x.Mobile == msg && x.Source == TalentResumeSource.诺优考).FirstOrDefault();
                if (resume == null)
                {
                    await Task.Delay(20);
                    continue;
                }

                var deptIds = resume.DeptIds?.Select(s => long.Parse(s)).ToList() ?? new List<long>();
                //根据resume.DeptIds查询部门level
                var lv = _context.Dd_Dept.Where(w => deptIds.Contains(w.DdDeptId)).Select(s => s.Level).ToList();

                //只检索最近15天的联系记录，如果联系过，则不用处理
                var deptLevels = _context.Talent_Resume_Connect.Where(w => w.CreatedTime > DateTime.Now.AddDays(-15)
                && w.ResumeId == resume.Id && w.Type == ConnectType.拨打电话)
                .Select(s => s.User_Hr.User.Dd_User.Dd_User_Dept.Select(ss => ss.Dd_Dept.Level).ToList())
                .SelectMany(s => s).Distinct().ToList();

                //如果deptLevels里有任何startwith deptIds的
                var hasContact = deptLevels.Any(a => lv.Any(l => a.StartsWith(l)));

                if (hasContact)
                {
                    await Task.Delay(20);
                    continue;
                }


                resume.HrIds = null;
                resume.DeptIds = null;
                _context.SaveChanges();
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.Nyk.NykDeptUnreadResume}消息处理出错", Tools.GetErrMsg(e), msg!);
                await Task.Delay(10000);
            }

            await Task.Delay(100);
        }
    }

    private async Task SendNykResume(NykTalentResume data)
    {
        try
        {
            var _context = _contextFactory.CreateDbContext();

            //判断是否存在更(四声)新的报名时间
            var nykResumes = _context.Talent_Resume.Where(w => w.Mobile == data.Mobile && w.Source == TalentResumeSource.诺优考)
            .Select(s => new
            {
                s.Id,
                s.Mobile,
                s.ExtendedData,
            }).ToList();

            foreach (var item in nykResumes)
            {
                if (string.IsNullOrWhiteSpace(item.ExtendedData)) continue;
                try
                {
                    var et = JsonSerializer.DeserializeFromString<NykExtendsModel>(item.ExtendedData);
                    if (et?.PostTime.HasValue == true && et.PostTime.Value > data.PostTime)
                        return;
                }
                catch { }
            }

            //生成word文件，走小析解析
            var sb = new StringBuilder();

            sb.AppendLine($"姓名:{data.Name}");
            sb.AppendLine($"性别:{data.Sex}");
            sb.AppendLine($"出生日期:{data.Birthday}");
            sb.AppendLine($"手机号:{data.Mobile}");
            sb.AppendLine($"地址:{data.City}");
            sb.AppendLine($"学历:{data.Education}");
            sb.AppendLine($"项目名称:{data.ProjectName}");
            sb.AppendLine($"公司名称:{data.CompanyName}");
            sb.AppendLine($"职位名称:{data.PostName}");

            sb.AppendLine();
            sb.AppendLine("教育经历：");
            foreach (var item in data.Edus ?? new List<NykLableValueModel>())
            {
                sb.Append($"{item.Label}：{item.Value} ， ");
                sb.AppendLine();
            }

            sb.AppendLine();
            sb.AppendLine("工作经历：");
            foreach (var item in data.Works ?? new List<NykLableValueModel>())
            {
                sb.Append($"{item.Label}：{item.Value} ， ");
            }

            var value = new Dictionary<string, object>()
            {
                { "text", sb.ToString() }
            };

            var templatePath = Path.Combine(AppContext.BaseDirectory, "TempFile", "resume.docx");
            MemoryStream memoryStream = new MemoryStream();
            MiniWord.SaveAsByTemplate(memoryStream, templatePath, value);
            memoryStream.Seek(0, SeekOrigin.Begin);
            var bt = memoryStream.ToArray();
            var base64Str = Convert.ToBase64String(bt);
            var dic = new Dictionary<string, AnalysisResumeItem>
            {
                { base64Str, new AnalysisResumeItem{ FileName=$"nyk_{data.ApplyID}.docx",UserName=data.Name,Mobile=data.Mobile} }
            };

            (List<AnalysisResumeResponse> AnalysisData, List<FaildResumeResponse> FaildData) = await _parseResume.AnalysisResumeByDic(dic);
            var result = AnalysisData?.FirstOrDefault();

            if (result?.parsing_result?.basic_info == null)
            {
                _logger.Info("诺优考简历未发送", $"小析未解析到数据", data.ApplyID ?? string.Empty);
                return;
            }

            var sex = data.Sex switch { "男" => (Sex?)Sex.男, "女" => Sex.女, _ => null };
            if (!sex.HasValue)
                sex = result.parsing_result.basic_info.gender switch { "男" => (Sex?)Sex.男, "女" => Sex.女, _ => null };

            var region = _commonDicService.GetCityByAddress(data.City);

            if (string.IsNullOrWhiteSpace(region?.CityId))
                region = _commonDicService.GetCityByAddress(result.parsing_result.basic_info.detailed_location);
            if (string.IsNullOrWhiteSpace(region?.CityId) && data.IdCard?.Length >= 4)
                region = _commonDicService.GetCityById(_commonDicService.GetRegion().FirstOrDefault(x => x.Id == data.IdCard?.Substring(0, 4))?.Id ?? string.Empty);

            var idCard = Tools.GetIdCardInfo(data.IdCard);

            if (idCard != null)
            {
                //如果生日不存在，从idcard取
                if (data.Birthday == null)
                    data.Birthday = idCard.Birthday?.ToDateTime(TimeOnly.MinValue);

                //如果性别不存在，从idcard取
                if (!sex.HasValue)
                    sex = idCard.Sex;
            }

            if (string.IsNullOrEmpty(data.Mobile) || string.IsNullOrEmpty(data.Name) || !sex.HasValue
             || string.IsNullOrEmpty(region?.CityId))
            {
                _logger.Info("诺优考简历数据缺失", "数据不完整", JsonSerializer.SerializeToString(data));
                return;
            }

            var extendedData = new NykExtendsModel
            {
                ProjectName = data.ProjectName,
                CompanyName = data.CompanyName,
                PostName = data.PostName,
                PostTime = data.PostTime,
                CompanyAccount = data.CompanyAccount,
                ApplyID = data.ApplyID,
                IdCard = data.IdCard,
                ProjectID = data.ProjectID,
                UserID = data.UserID
            };
            var model = new TalentResumeRequestModelExtends
            {
                RegistedTime = data.RegistedTime,
                City = region?.CityName,
                RegionId = region?.Id,
                Birthday = data.Birthday,
                Education = GetEdu(result.parsing_result.basic_info.degree),
                LastLoginTime = data.LastLoginTime,
                Mobile = data.Mobile,
                Name = data.Name,
                Source = TalentResumeSource.诺优考,
                Status = TalentResumeStatus.UnRegistered,
                Sex = sex,
                Tags = result.predicted_result?.tags?.skills?.Select(s => s.tag)?.ToList(),
                WorkTime = result.parsing_result.basic_info.work_start_year.ToNullableDate(),
                Edus = result.parsing_result.education_experience?.Select(s => new Edus
                {
                    Education = GetEdu(s.degree),
                    EndTime = $"{s.end_time_year}-{s.end_time_month}".ToNullableDate(),
                    IsFullTime = true,
                    MajorName = s.major,
                    SchoolName = s.school_name,
                    StartTime = $"{s.start_time_year}-{s.start_time_month}".ToNullableDate()
                }).ToList(),
                Projects = result.parsing_result.project_experience?.Select(s => new Projects
                {
                    EndTime = $"{s.end_time_year}-{s.end_time_month}".ToNullableDate(),
                    PostName = s.job_title,
                    ProjectName = s.project_name,
                    ProjectRemarks = s.description,
                    StartTime = $"{s.start_time_year}-{s.start_time_month}".ToNullableDate()
                }).ToList(),
                Works = result.parsing_result.work_experience?.Select(s => new Works
                {
                    EndTime = $"{s.end_time_year}-{s.end_time_month}".ToNullableDate(),
                    PostName = s.job_title,
                    CompanyName = s.company_name,
                    CompanyRemarks = s.description,
                    Department = s.department,
                    IndustryName = s.job_title,
                    MaxSalary = 0,
                    MinSalary = 0,
                    StartTime = $"{s.start_time_year}-{s.start_time_month}".ToNullableDate()
                }).ToList(),
                Hopes = new List<Hopes>
                {
                    new Hopes
                    {
                        IndustryLevel1Name = result.parsing_result.basic_info.desired_industry,
                        PositionLevel1Name = result.parsing_result.basic_info.desired_position,
                        HopeCity = region?.CityName,
                        RegionId = region?.CityId
                    }
                },
                ExtendedData = JsonSerializer.SerializeToString(extendedData)
            };

            if (!string.IsNullOrWhiteSpace(extendedData.CompanyAccount))
            {
                var ddUser = _context.Dd_User.FirstOrDefault(f => f.Mobile == extendedData.CompanyAccount);
                var mobile = ddUser?.Mobile;
                var hr = string.IsNullOrEmpty(mobile) ? null : _context.User_Hr.FirstOrDefault(f => f.User.Mobile == mobile);
                if (hr != null)
                {
                    //如果有对应顾问，发送一个15天的消息队列，15天内如果顾问联系过再删掉
                    model.HrIds = new List<string> { hr.UserId };
                    MyRedis.DelayQueue(SubscriptionKey.Nyk.NykHrUnreadResume, new string[] { data.Mobile }, 3600 * 24 * 15, false);
                }
                else
                {
                    model.HrIds = new List<string> { "-1" };
                    //诺优考简历，如果没有对应顾问，先记录下来
                    MyRedis.Client.SAdd("nyk:nohr", extendedData.CompanyAccount);
                }
            }

            //通知诺快聘
            if (model.Hopes.Count > 0)
                _commonTalentResumeService.TalentResumeReceive(new List<TalentResumeRequestModelExtends> { model });
        }
        catch (Exception e)
        {
            _logger.Error("诺优考简历处理错误", Tools.GetErrMsg(e), data);
        }
    }

    private TalentVirtualEducation? GetEdu(string? edu)
    {
        return edu switch
        {
            "初中" => TalentVirtualEducation.Junior, //初中及以下
            "中专" => TalentVirtualEducation.Specialized, //中专
            "高中" => TalentVirtualEducation.Senior, //高中
            "大专" => TalentVirtualEducation.Professional, //大专
            "本科" => TalentVirtualEducation.Undergraduate, //本科
            "硕士" => TalentVirtualEducation.Master, //硕士
            "博士" => TalentVirtualEducation.Doctor, //博士
            _ => null, //默认
        };
    }

    /// <summary>
    /// 快招工简历推送人才线索池
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task KgzResumeSync(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            //加锁
            var lockerKey = $"nykservice:KgzResumeSync";
            using var locker = MyRedis.TryLock(lockerKey, 10);
            if (locker == null)
                return;

            using var _context = _contextFactory.CreateDbContext();

            var todolist = _context.Kuaishou_Talent_Infos.Include(i => i.Kuaishou_Hr_Talent_Relations).Where(
                w => w.TalentResume == 0
                && w.CreatedTime < DateTime.Now.AddDays(-3))
                .OrderBy(w => w.CreatedTime)
                .Take(10)
                .ToList();

            if (todolist.Count == 0)
                return;

            var list = new List<TalentResumeRequestModelExtends>();
            foreach (var item in todolist)
            {
                if (list.Count(c => c.Mobile == item.Phone) > 0)
                {
                    continue;// 一次传递不能传相同手机号 todo:如果这里没锁住，被两台实例分别拿到同一个手机号，并传入，会怎么样？？
                }

                // 已推送项目打标
                if (item.Kuaishou_Hr_Talent_Relations?.Status == KuaishouStatus.已推送)
                {
                    item.TalentResume = TalentResumeType.已推送; // 已推送项目
                    continue;
                }

                // 不合格数据打标 电话，名称，性别，地区
                if (string.IsNullOrWhiteSpace(item.Phone))
                {
                    item.TalentResume = TalentResumeType.数据不合格;
                    continue;
                }

                if (string.IsNullOrWhiteSpace(item.Name))
                {
                    item.TalentResume = TalentResumeType.数据不合格;
                    continue;
                }

                if (string.IsNullOrWhiteSpace(item.GenderName))
                {
                    item.TalentResume = TalentResumeType.数据不合格;
                    continue;
                }

                if (string.IsNullOrWhiteSpace(item.DataUserCity))
                {
                    item.TalentResume = TalentResumeType.数据不合格;
                    continue;
                }

                // 不是快招工职位打标
                var teampost = _context.Post_Team_Third_Jobid_Rel.FirstOrDefault(f => f.JobId == item.JobId && f.PlatType == ThirdPlatType.快招工);
                if (teampost == null)
                {
                    item.TalentResume = TalentResumeType.非诺快聘职位;
                    continue;
                }

                // 根据投递职位推算求职意向
                var postInfo = _context.Post_Team
                    .Where(w => w.TeamPostId == teampost.TeamPostId)
                    .Select(s => new
                    {
                        s.Post.Category,
                        s.Post.RegionId
                    })
                    .FirstOrDefault();

                var regionInfo = _commonDicService.GetCityById(postInfo!.RegionId + "");

                // 直接执行
                var model = new TalentResumeRequestModelExtends
                {
                    RegistedTime = item.ApplyTime, // 这里用投递时间
                    Address = item.DataLocationCitys?.address,
                    RegionId = item.DataLocationCitys?.adCode,
                    City = item.DataUserCity,
                    Birthday = Convert.ToDateTime($"{DateTime.Now.Year - item.Age}-01-01"),// 没有生日久倒推生日，默认月日为"-01-01",
                    Education = GetMappingEducation(item.DataDynamicResumeInfo.HighestDegree),
                    HeadPortrait = string.Empty,
                    LastLoginTime = item.ApplyTime,// 这里用投递时间
                    IndustryLabel = null,
                    Mailbox = string.Empty,// 快手简历没有邮箱信息
                    Mobile = item.Phone!,
                    QQ = null,
                    Remark = null,
                    OtherLabel = null,
                    Risks = null,
                    SkillAnalysis = null,
                    PostLabel = null,
                    Name = item.Name,
                    Sex = item.GenderName switch { "男" => Sex.男, "女" => Sex.女, _ => null },
                    Tags = null,
                    Source = TalentResumeSource.快招工,
                    WorkTime = null,
                    Status = TalentResumeStatus.UnRegistered,
                    Edus = null,
                    Projects = null,
                    Works = null,
                    Hopes = new List<Hopes>
                    {
                        new Hopes
                        {
                            Category = postInfo.Category + "",
                            HopeCity = regionInfo?.CityName,
                            RegionId = postInfo.RegionId
                        }
                    }
                };

                list.Add(model);
                item.TalentResume = TalentResumeType.已同步;
            }
            if (list.Count > 0)
                _commonTalentResumeService.TalentResumeReceive(list);
            _context.SaveChanges();

            await Task.FromResult(1);
        }
        catch (Exception e)
        {
            _logger.Error("快招工同步人才库出错", Tools.GetErrMsg(e));
        }
    }

    private TalentVirtualEducation? GetMappingEducation(string? highestDegree)
    {
        return highestDegree switch
        {
            "初中及以下" => TalentVirtualEducation.Junior,
            "中专/技校/职高" => TalentVirtualEducation.Specialized,
            "高中" => TalentVirtualEducation.Senior,
            "大专" => TalentVirtualEducation.Professional,
            "本科及以上" => TalentVirtualEducation.Undergraduate,
            _ => null
        };
    }
}

// public class NykResumeModel
// {
//     public string? type { get; set; }
//     public long typeKey { get; set; }
//     public string? label { get; set; }
//     public string? key { get; set; }
//     public object? valueText { get; set; }
//     public object? valueExtra { get; set; }
// }

public class NykResumeSummary
{
    public SortedList<string, string> List { get; set; } = new SortedList<string, string>();
    public List<SortedList<string, string>> Works { get; set; } = new List<SortedList<string, string>>();
    public List<SortedList<string, string>> Edus { get; set; } = new List<SortedList<string, string>>();
}
