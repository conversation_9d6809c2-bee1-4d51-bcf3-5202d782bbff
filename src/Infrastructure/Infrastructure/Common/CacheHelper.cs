﻿using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Caching.Memory;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Singleton)]
public class CacheHelper
{
    private readonly IMemoryCache _memoryCache;
    public CacheHelper(IMemoryCache memoryCache)
    {
        _memoryCache = memoryCache;
    }

    /// <summary>
    /// 获取redis缓存，如果空则设置缓存
    /// </summary>
    /// <typeparam name="TResult"></typeparam>
    /// <param name="func"></param>
    /// <param name="cacheKey"></param>
    /// <param name="seconds"></param>
    /// <returns></returns>
    public TResult GetRedisCache<TResult>(Func<TResult> func, string cacheKey, int seconds = 60)
    {
        var result = MyRedis.Client.Get<TResult>(cacheKey);
        if (result == null)
        {
            result = func();
            if (seconds > 0)
                MyRedis.Client.Set(cacheKey, result, TimeSpan.FromSeconds(seconds));
            else
                MyRedis.Client.Set(cacheKey, result);
        }
        return result;
    }

    /// <summary>
    /// 获取redis缓存，如果空则设置缓存
    /// </summary>
    /// <typeparam name="TResult"></typeparam>
    /// <param name="func"></param>
    /// <param name="seconds"></param>
    /// <returns></returns>
    public async Task<TResult> GetRedisCacheAsync<TResult>(Func<Task<TResult>> func, string cacheKey, int seconds = 60)
    {
        var result = MyRedis.Client.Get<TResult>(cacheKey);
        if (result == null)
        {
            result = await func();
            MyRedis.Client.Set(cacheKey, result, TimeSpan.FromSeconds(seconds));
        }

        return result;
    }

    /// <summary>
    /// 获取内存缓存，如果空则设置缓存
    /// </summary>
    /// <typeparam name="TResult"></typeparam>
    /// <param name="func"></param>
    /// <param name="cacheKey"></param>
    /// <param name="seconds"></param>
    /// <returns></returns>
    public TResult GetMemoryCache<TResult>(Func<TResult> func, string cacheKey, int seconds = 60)
    {
        var result = _memoryCache.Get<TResult>(cacheKey);
        if (result == null)
        {
            result = func();
            if (seconds > 0)
                _memoryCache.Set(cacheKey, result, TimeSpan.FromSeconds(seconds));
            else
                _memoryCache.Set(cacheKey, result);
        }
        return result;
    }

    public TResult? GetMemoryCache2<TResult>(string cacheKey, int seconds = 60)
    {
        var result = _memoryCache.Get<TResult?>(cacheKey);
        return result;
    }

    public void SetMemoryCache2(string cacheKey, object obj, int seconds = 60)
    {
        _memoryCache.Set(cacheKey, obj, TimeSpan.FromSeconds(seconds));
    }

    /// <summary>
    /// 获取内存缓存，如果空则设置缓存
    /// </summary>
    /// <typeparam name="TResult"></typeparam>
    /// <param name="func"></param>
    /// <param name="seconds"></param>
    /// <returns></returns>
    public async Task<TResult> GetMemoryCacheAsync<TResult>(Func<Task<TResult>> func, string cacheKey, int seconds = 60)
    {
        var result = _memoryCache.Get<TResult>(cacheKey);
        if (result == null)
        {
            result = await func();
            if (seconds > 0)
                _memoryCache.Set(cacheKey, result, TimeSpan.FromSeconds(seconds));
            else
                _memoryCache.Set(cacheKey, result);

        }
        return result;
    }

    public void RemoveRedisCache(string cacheKey)
    {
        MyRedis.Client.Del(cacheKey);
    }

}