﻿using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.Hr;
using Config;
using Staffing.Model.Hr.Project;
using Infrastructure.CommonService;
using Infrastructure.Proxy;
using Staffing.Core.Interfaces.Common;
using Config.CommonModel.Business;

namespace Staffing.Core.Services.Hr;

[Service(ServiceLifetime.Transient)]
public class ProjectHallService : IProjectHallService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CommonDicService _commonDicService;
    private readonly NoahData _noahData;
    private readonly ICommonService _commonService;
    private readonly CommonNScoreService _commonNScoreService;
    private readonly CommonProjectService _commonProjectService;
    public ProjectHallService(StaffingContext context, RequestContext user, CommonProjectService commonProjectService,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, LogManager log, CommonDicService commonDicService,
        NoahData noahData, ICommonService commonService, CommonNScoreService commonNScoreService)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _log = log;
        _commonDicService = commonDicService;
        _noahData = noahData;
        _commonService = commonService;
        _commonNScoreService = commonNScoreService;
        _commonProjectService = commonProjectService;
    }

    public async Task<GetProjectHallResponse> GetProjectHall(GetProjectHall model)
    {
        var result = new GetProjectHallResponse();

        var idPre = Constants.ProjectIdPre;

        var predicate = PredicateBuilder.New<Project_Hall>(x => x.Project.Status == ProjectStatus.已上线 && x.Project.SharePost
        && x.Project.Posts.Any(a => a.Status == PostStatus.发布中 && a.LeftStock > 0));

        //我同步过的项目
        var myProIds = _context.Project_Team.Where(x => x.HrId == _user.Id).Select(s => s.ProjectId);

        if (!string.IsNullOrWhiteSpace(model.HrId))
            predicate = predicate.And(x => x.HrId == model.HrId);

        if (!string.IsNullOrWhiteSpace(model.Search))
        {
            if (model.Search.StartsWith(idPre) && int.TryParse(model.Search.Substring(1), out var id))
                predicate = predicate.And(x => x.Project.AutoId == id );
            else
                predicate = predicate.And(x => x.User_Hr.NickName.Contains(model.Search) || x.Project.Agent_Ent.Name.Contains(model.Search));
        }

        if (model.NotShareYet)
            predicate = predicate.And(x => !myProIds.Contains(x.ProjectId));

        if (model.Level.HasValue)
            predicate = predicate.And(x => x.Project.Level == model.Level);

        if (model.ProjectType.HasValue)
            predicate = predicate.And(x => x.Project.Type == model.ProjectType);

        if (model.Industry.HasValue)
            predicate = predicate.And(x => x.Project.Industry == model.Industry);

        if (model.WorkCycleType.HasValue)
            predicate = predicate.And(x => x.Project.WorkCycleType == model.WorkCycleType);

        if (model.ClearingType.HasValue)
            predicate = predicate.And(x => x.Project.ClearingType == model.ClearingType);

        if (model.PaymentType.HasValue)
            predicate = predicate.And(x => x.Project.PaymentType == model.PaymentType);

        if (model.ClearingChannel.HasValue)
            predicate = predicate.And(x => x.Project.ClearingChannel == model.ClearingChannel);

        var sql = _context.Project_Hall.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetProjectHallDetail
        {
            ProjectAutoId = s.Project.AutoId,
            HallProjectId = s.Id,
            ProjectId = s.ProjectId,
            ContractType = s.Project.ContractType,
            ContractTypeText = s.Project.ContractType.GetDescription(),
            Name = s.Project.Agent_Ent.Name,
            AgentEntId = s.Project.AgentEntId ?? string.Empty,
            Status = s.Project.Status,
            Type = s.Project.Type,
            ClearingChannel = s.Project.ClearingChannel,
            ClearingChannelName = s.Project.ClearingChannel.GetDescription(),
            ClearingType = s.Project.ClearingType,
            ClearingTypeName = s.Project.ClearingType.GetDescription(),
            CreatedTime = s.CreatedTime,
            EndTime = s.Project.EndTime.ToYYYY_MM_DD(),
            HrId = s.HrId,
            HrName = s.User_Hr.NickName,
            Industry = s.Project.Industry,
            IndustryName = s.Project.Industry.GetDescription(),
            Level = s.Project.Level,
            LevelName = s.Project.Level.GetDescription(),
            NuoId = s.Project.NuoId,
            PaymentType = s.Project.PaymentType,
            // Region = s.Project.RegionData,
            StatusName = s.Project.Status.GetDescription(),
            TypeName = s.Project.Type.GetDescription(),
            WorkCycleType = s.Project.WorkCycleType,
            WorkCycleTypeName = s.Project.WorkCycleType.GetDescription(),
            AgentEnt = new GetAgentEntDetail { LogoUrl = s.Project.Agent_Ent.LogoUrl },
            SelfProj = s.HrId == _user.Id
        }).ToList();

        //项目汇总需优化走缓存
        var projectIds = result.Rows.Select(s => s.ProjectId).ToList();
        var projSummary = _context.Post
        .Where(x => projectIds.Contains(x.ProjectId) && x.Status == PostStatus.发布中)
        .GroupBy(g => g.ProjectId)
        .Select(s => new
        {
            s.Key,
            PostCount = s.Count(),
            Bounty = s.Sum(c => c.Money * c.DeliveryNumber),
            RecruitNumber = s.Sum(c => c.DeliveryNumber),
            DeliveryNumber = s.Sum(c => c.DeliveryNumber),
            InductionNum = s.Sum(c => c.Post_Extend.InductionNum),
            DeliveriesBounty = s.Sum(c => c.Post_Extend.InductionNum * c.Money),
            RegistrationNum = s.Sum(c => c.Post_Extend.DeliveryNum),
            ContractNum = s.Sum(c => c.Post_Extend.ContractNum)
        }).ToList();

        result.Rows.ForEach(item =>
        {
            item.ProjectNo = $"{idPre}{item.ProjectAutoId.ToString().PadLeft(6, '0')}";

            item.HasShare = myProIds.Contains(item.ProjectId);

            var prosum = projSummary.FirstOrDefault(x => x.Key == item.ProjectId);
            if (prosum != null)
            {
                item.PostCount = prosum.PostCount;
                item.Bounty = prosum.Bounty;
                item.DeliveriesBounty = prosum.DeliveriesBounty ?? 0;
                item.RecruitNumber = prosum.RecruitNumber;
                item.DeliveryNumber = prosum.DeliveryNumber;
                item.DeliveriesNum = prosum.InductionNum;
                item.Internal = !string.IsNullOrWhiteSpace(item.NuoId);
                item.RegistrationNum = prosum.RegistrationNum;
                item.ContractNum = prosum.ContractNum;
            }

            item.PaymentTypeName = item.PaymentType?.GetDescription();
        });

        await Task.FromResult(1);
        return result;
    }

    public GetHallPostResponse GetHallPost(GetHallPost model)
    {
        var result = new GetHallPostResponse();

        var idPre = Constants.PostIdPre;

        var predicate = PredicateBuilder.New<Post>(x => x.ProjectId == model.ProjectId);

        if (!string.IsNullOrWhiteSpace(model.Search))
        {
            if (model.Search.StartsWith(idPre) && int.TryParse(model.Search.Substring(1), out var id))
                predicate = predicate.And(x => x.AutoId == id || x.Name.Contains(model.Search));
            else
                predicate = predicate.And(x => x.Name.Contains(model.Search));
        }

        if (model.Category.HasValue)
        {
            var categoryLevel = (_commonDicService.GetPost())
            .FirstOrDefault(x => x.Id == model.Category.ToString())?.Level;

            if (!string.IsNullOrWhiteSpace(categoryLevel))
                predicate = predicate.And(x => x.Dic_Post.Level.StartsWith(categoryLevel));
        }

        if (model.Citys?.Count > 0)
        {
            var subPredicate = PredicateBuilder.New<Post>(false);
            foreach (var item in model.Citys)
                subPredicate = subPredicate.Or(x => x.RegionId.StartsWith(item));

            predicate = predicate.And(subPredicate);
        }

        var sql = _context.Post.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetHallPostDetail
        {
            Hr = new HrModel
            {
                HrId = s.CreatorId,
                HrName = s.Creator.NickName,
                Avatar = s.Creator.Avatar,
                Post = s.Creator.Post,
                Mobile = s.Creator.User.Mobile
            },
            AgentEnt = new GetAgentEntDetail
            {
                DisplayName = s.Agent_Ent.DisplayName,
                AgentEntId = s.AgentEntId,
                Scale = s.Agent_Ent.Scale,
                Abbr = s.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Agent_Ent.Abbr : s.Agent_Ent.DisplayName,
                LogoUrl = s.Agent_Ent.Display == EntDisplayType.企业名称 ? s.Agent_Ent.LogoUrl : string.Empty,
                ScaleName = s.Agent_Ent.Scale.GetDescription(),
                Industry = s.Agent_Ent.Industry.ToString(),
                IndustryName = s.Agent_Ent.Dic_Industry.Name
            },
            RewardType = s.RewardType,
            RewardTypeName = s.RewardType.GetDescription(),
            PaymentCycle = s.PaymentCycle,
            PaymentCycleName = s.PaymentCycle.GetDescription(),
            PaymentDuration = s.PaymentDuration,
            PostAutoId = s.AutoId,
            ProjectId = s.ProjectId,
            Name = s.Name,
            Status = s.Status,
            CreatedTime = s.CreatedTime,
            UpdatedTime = s.UpdatedTime,
            IsResumeExempt = s.IsResumeExempt,
            AgentEntId = s.Project.AgentEntId,
            AgentEntName = s.Agent_Ent.Name,
            IsSalesCommission = s.IsSalesCommission,
            PaymentNode = s.PaymentNode,
            PaymentDays = s.PaymentDays,
            PaymentType = s.Project.PaymentType,
            StatusName = s.Project.Status.GetDescription(),
            Address = s.Address,
            Category = s.Category,
            CategoryName = s.Dic_Post.Name,
            Department = s.Department,
            Describe = s.Describe,
            Education = s.Education,
            EducationName = s.Education.GetDescription(),
            Highlights = s.Highlights,
            Tags = s.Tags,
            Welfare = s.Welfare,
            WelfareCustom = s.WelfareCustom,
            Lat = s.Location.X,
            Lng = s.Location.Y,
            MaxSalary = s.MaxSalary,
            MinSalary = s.MinSalary,
            Money = s.Money,
            PostId = s.PostId,
            RecruitNumber = s.LeftStock,// 都改成LeftStock,不需要管前端用哪个字段
            DeliveryNumber = s.LeftStock,
            RegionId = s.RegionId,
            Salary = s.Salary,
            SettlementType = s.SettlementType,
            WorkingDays = s.WorkingDays,
            WorkingHours = s.WorkingHours,
            MinMonths = s.MinMonths,
            DaysPerWeek = s.DaysPerWeek,
            GraduationYear = s.GraduationYear,
            WorkNature = s.WorkNature,
            SalaryType = s.SalaryType,
            SalaryTypeName = s.SalaryType.GetDescription(),
            WorkNatureName = s.WorkNature.GetDescription(),
            Sex = s.Sex,
            MinAge = s.MinAge,
            MaxAge = s.MaxAge,
            InterviewDashboard = new PostRecentInterviews
            {
                RecentDeliveryRate = s.Post_Extend.RecentDeliveryRate,
                HistoryBillingRate = s.Post_Extend.HistoryBillingRate
            },
            RegistrationNum = s.Post_Extend.DeliveryNum,
            DeliveriesNum = s.Post_Extend.InductionNum,
            ScreeningNum = s.Post_Extend.HrScreeningNum,
            InterviewNum = s.Post_Extend.InterviewNum,
            InductionNum = s.Post_Extend.InductionNum
        }).ToList();

        var welfare = _commonDicService.GetWelfare();

        var postIds = result.Rows.Select(s => s.PostId).ToList();

        // 查询职位面试配置信息
        var postInterviews = _context.Post_Interview_Config.Where(x => postIds.Contains(x.PostId)).ToList();

        //最近7天
        var recent7 = DateTime.Now.Date.AddDays(-6);

        //本周面试人数
        var weekInterview = _context.Recruit_Record.Where(x => x.CreatedTime >= recent7 && postIds.Contains(x.Recruit.Post_Delivery.PostId) && Constants.InterviewStatus.Contains(x.Status))
        .GroupBy(g => g.RecruitId)
        .Select(s => new { s.Key, PostId = s.Max(m => m.Recruit.Post_Delivery.PostId) })
        .GroupBy(g => g.PostId)
        .Select(s => new { s.Key, Ct = s.Count() }).ToList();

        var bgt = DateTime.Now.Date.AddDays(-14);
        var interviewTimes = _context.Post_Interview_Date.Where(x => postIds.Contains(x.PostId) && x.Time >= bgt).ToList();

        foreach (var item in result.Rows)
        {
            item.PostNo = $"{Constants.PostIdPre}{item.PostAutoId.ToString()!.PadLeft(2, '0')}";

            item.SalaryName = Tools.FormattingSalary(item.MinSalary, item.MaxSalary, item.SalaryType, item.WorkNature, item.Salary);

            if (item.Welfare?.Count > 0)
                item.Welfare = welfare.Where(x => item.Welfare.Any(a => a.Id == x.Id)).ToList();

            if (!string.IsNullOrWhiteSpace(item.RegionId))
                item.Region = _commonDicService.GetCityById(item.RegionId);

            item.PaymentNodeName = item.PaymentNode?.GetDescription();
            item.PaymentTypeName = item.PaymentType?.GetDescription();

            //面试信息
            var interview = postInterviews.FirstOrDefault(x => x.PostId == item.PostId) ?? new Post_Interview_Config();
            var interviewTime = interviewTimes.Where(x => x.PostId == item.PostId).ToList();
            if (interview != null)
            {
                //InterviewTime 只保留昨天至未来6天的面试时间
                var beginTime = DateTime.Now.Date.AddDays(-1);
                var endTime = beginTime.AddDays(6);

                foreach (var tm in Enumerable.Range(0, 7).Select(i => beginTime.AddDays(i)))
                {
                    PostInterviewTimeStatus? st = null;
                    if (interviewTime.Any(x => x.Time.Date == tm && x.Status == PostInterviewTimeStatus.可预约 && tm >= DateTime.Today.AddDays(interview.AdvanceDays)))
                        st = PostInterviewTimeStatus.可预约;
                    else 
                    //if (interviewTime.Any(x => (x.Time.Date == tm && x.Status == PostInterviewTimeStatus.取消预约) || tm < DateTime.Now.AddDays(interview.AdvanceDays)))
                        st = PostInterviewTimeStatus.取消预约;

                    var its = new PostInterviewTimeModel
                    {
                        Time = tm,
                        Status = st,
                        SubTime = interviewTime.Where(x => x.Time.Date == tm).Select(s => TimeOnly.FromDateTime(s.Time)).OrderBy(o => o).ToList()
                    };
                    item.InterviewDashboard.Recent.Add(its);
                }
            }

            item.InterviewDashboard.RecentInterviewNum = weekInterview.Where(x => x.Key == item.PostId)?.Sum(c => (int?)c.Ct) ?? 0;

            //产品：该数是未来可预约面试的场次，与时间无关
            item.InterviewDashboard.InterviewNum = interviewTime?.Where(x => x.Status == PostInterviewTimeStatus.可预约 && x.Time > DateTime.Now).Count() ?? 0;
        }

        return result;
    }

    public async Task<ProjectSyncResponse> ProjectSync(ProjectSync model)
    {
        var teamProjectId = await _commonProjectService.ProjectSync(model.ProjectId, _user.Id);
        var result = new ProjectSyncResponse
        {
            TeamProjectId = teamProjectId
        };
        return result;
    }

    public GetProjectHallDetail GetHallProject(string id)
    {
        var result = _context.Project.Where(x => x.ProjectId == id)
        .Select(s => new GetProjectHallDetail
        {
            ProjectId = s.ProjectId,
            Name = s.Agent_Ent.Name,
            AgentEntId = s.AgentEntId ?? string.Empty,
            Status = s.Status,
            Type = s.Type,
            ClearingChannel = s.ClearingChannel,
            ClearingChannelName = s.ClearingChannel.GetDescription(),
            ClearingType = s.ClearingType,
            ClearingTypeName = s.ClearingType.GetDescription(),
            CreatedTime = s.CreatedTime,
            Describe = s.Describe,
            EndTime = s.EndTime.ToYYYY_MM_DD(),
            HrId = s.HrId,
            HrName = s.User_Hr.NickName,
            Industry = s.Industry,
            IndustryName = s.Industry.GetDescription(),
            Level = s.Level,
            LevelName = s.Level.GetDescription(),
            NuoId = s.NuoId,
            PaymentType = s.PaymentType,
            // Region = s.RegionData,
            StatusName = s.Status.GetDescription(),
            TypeName = s.Type.GetDescription(),
            WorkCycleType = s.WorkCycleType,
            WorkCycleTypeName = s.WorkCycleType.GetDescription(),
            HrAvatar = s.User_Hr.Avatar,
            HrEntName = s.User_Hr.Enterprise.Name,
            HrEntAddress = s.User_Hr.Enterprise.Address,
            HrMobile = s.User_Hr.User.Mobile,
            HrEmail = s.User_Hr.EMail,
            HrWeChat = s.User_Hr.WeChatNo,
            HrPost = s.User_Hr.Post,
            SelfProj = s.HrId == _user.Id,
            ProjectAutoId = s.AutoId,
            ProjectManagerId = s.User_Hr.UserId,
            ProjectManagerName = s.User_Hr.NickName,
            PmAvatar = s.User_Hr.Avatar,
            PmEntName = s.User_Hr.Enterprise.Name,
            AcceptOrder = s.AcceptOrder,
            SaleUserId = s.SaleUser.UserId,
            SaleUserName = s.SaleUser.NickName,
            SaleUserAvatar = s.SaleUser.Avatar,
            SaleUserEmail = s.SaleUser.EMail,
            SaleUserWeChat = s.SaleUser.WeChatNo,
            SaleUserEntWeChatQrCode = s.SaleUser.EntWeChatQrCode,
            PmEntWeChatQrCode = s.User_Hr.EntWeChatQrCode,
            SaleUserEntName = s.SaleUser.Enterprise.Name,
            SaleUserMobile = s.SaleUser.User.Mobile,
        }).FirstOrDefault();

        if (result == null)
            throw new NotFoundException("内容不存在");

        //我同步过的项目
        var teamProjectId = _context.Project_Team.Where(x => x.HrId == _user.Id && x.ProjectId == result.ProjectId).Select(s => s.TeamProjectId).FirstOrDefault();
        result.HasShare = !string.IsNullOrWhiteSpace(teamProjectId);
        result.TeamProjectId = teamProjectId;

        var idPre = Constants.ProjectIdPre;
        result.ProjectNo = $"{idPre}{result.ProjectAutoId.ToString().PadLeft(6, '0')}";

        result.PaymentTypeName = result.PaymentType?.GetDescription();

        result.RegionIds = result.Region?.Select(s => s.Id!)?.ToList() ?? new List<string>();

        //项目汇总需优化走缓存
        var prosum = _context.Post
        .Where(x => x.ProjectId == result.ProjectId && x.Status == PostStatus.发布中)
        .GroupBy(g => g.ProjectId)
        .Select(s => new
        {
            s.Key,
            PostCount = s.Count(),
            Bounty = s.Sum(c => c.Money * c.DeliveryNumber),
            RecruitNumber = s.Sum(c => c.DeliveryNumber),
            Deliveries = s.Sum(c => c.Post_Extend.InductionNum),
            DeliveriesBounty = s.Sum(c => c.Post_Extend.InductionNum * c.Money),
            RegistrationNum = s.Sum(c => c.Post_Extend.DeliveryNum),
            ContractNum = s.Sum(c => c.Post_Extend.ContractNum)
        }).FirstOrDefault();

        var authorProsum = _context.Post_Team
        .Where(x => x.Project_Team.ProjectId == result.ProjectId && x.Project_Team.Type == HrProjectType.自己)
        .GroupBy(g => g.Project_Team.ProjectId)
        .Select(s => new
        {
            s.Key,
            // Deliveries = s.Sum(c => c.Post_Team_Extend.InductionNum),
            DeliveriesBounty = s.Sum(c => c.Post_Team_Extend.InductionNum * c.Post.Money)
            // RegistrationNum = s.Sum(c => c.Post_Extend.DeliveryNum),
            // ContractNum = s.Sum(c => c.Post_Extend.ContractNum)
        }).FirstOrDefault();

        if (prosum != null)
        {
            result.PostCount = prosum.PostCount;
            result.Bounty = prosum.Bounty;
            result.DeliveriesBounty = (prosum.DeliveriesBounty ?? 0) - (authorProsum?.DeliveriesBounty ?? 0);
            result.DeliveriesBounty = result.DeliveriesBounty < 0 ? 0 : result.DeliveriesBounty;// todo：这字段不知道干啥用的，先放着，而且覆盖前一条语句的赋值了。。
            result.RecruitNumber = prosum.RecruitNumber;
            result.DeliveryNumber = prosum.RecruitNumber;// 这个也加上，这样可以不用管前端用哪个都是对的
            result.DeliveriesNum = prosum.Deliveries;// todo：这字段不知道干啥用的，先放着
            result.RegistrationNum = prosum.RegistrationNum;
            result.ContractNum = prosum.ContractNum;
        }

        var cacheKey = $"st:pj:participant_{id}";
        result.Participant = _cacheHelper.GetRedisCache<ProjectParticipant>(() =>
        {
            //参与者
            var participant = new ProjectParticipant();
            var participantSql = _context.Project_Team
            .Where(x => x.ProjectId == id && x.Status == ProjectStatus.已上线 && x.Type == HrProjectType.协同);

            participant.Count = participantSql.Count();

            var pt = participantSql.OrderByDescending(o => o.CreatedTime).Take(3)
            .Select(s => new
            {
                s.User_Hr.Avatar,
                s.User_Hr.NickName
            }).ToList();

            participant.Avatars = pt.Select(s => s.Avatar ?? string.Empty).ToList();
            participant.FirstHrName = pt.Select(s => s.NickName).FirstOrDefault();
            return participant;
        }, cacheKey, 60);

        return result;
    }

    public Task<GetProjectHallResponse> NewGetProjectHall(GetProjectHall model)
    {
        var result = new GetProjectHallResponse();
        var predicate = PredicateBuilder.New<Project>(x=>x.HrId == _user.Id);
        
        
        throw new NotImplementedException();
    }
}