﻿using Config;
using Config.CommonModel;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Model.Interviewer;
using Staffing.Core.Interfaces.Interviewer;

namespace Staffing.Core.Services.Service.Interviewer;

/// <summary>
/// 面试官面试操作（面试官筛选）
/// </summary>
[Service(ServiceLifetime.Transient)]
public class InterviewerService : IInterviewerService
{
    private readonly StaffingContext _context;
    private readonly LogManager _log;
    private RequestContext _user;
    private ConfigManager _config;
    private CacheHelper _cacheHelper;

    /// <summary>
    /// 注入
    /// </summary>
    public InterviewerService(StaffingContext context, LogManager log, RequestContext user, IOptionsSnapshot<ConfigManager> config, CacheHelper cacheHelper)
    {
        _context = context;
        _log = log;
        _user = user;
        _config = config.Value;
        _cacheHelper = cacheHelper;
    }

    /// <summary>
    /// 获取面试官基本信息
    /// </summary>
    /// <returns></returns>
    public InterviewerInfoResponse GetIneterviewerInfo()
    {
        var userModel = _context.User_Seeker.Where(o => o.UserId == _user.Id).Select(o => new InterviewerInfoResponse()
        {
            Name = o.NickName ?? "",
            Phone = o.User.Mobile,
            InterviewerEnt = o.InterviewerEnt ?? "",
            WeChatH5Subscribe = o.User.WeChatH5Subscribe
        }).FirstOrDefault();

        if (userModel == null)
        {
            throw new NotFoundException("用户不存在");
        }

        return userModel;
    }

    /// <summary>
    /// 修改面试官基本信息
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse EditInterviewerInfo(EditInterviewerInfoRequest model)
    {
        var userModel = _context.User_Seeker.Where(o => o.UserId == _user.Id).FirstOrDefault();
        if (userModel == null)
        {
            throw new NotFoundException("用户不存在");
        }
        userModel.NickName = model.Name;
        userModel.InterviewerEnt = model.InterviewerEnt;

        _context.SaveChanges();
        return new EmptyResponse { };
    }

    /// <summary>
    /// 获取面试官待办列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public TodoListResponse GetTodoList(TodoListRequest model)
    {
        TodoListResponse retModel = new TodoListResponse();

        var userModel = _context.User.Where(o => o.UserId == _user.Id).First();

        var predicate = PredicateBuilder.New<Recruit_Interviewer_Todo>(o => o.Project_Interviewer.Phone == userModel.Mobile);

        switch (model.Type)
        {
            case 0: predicate = predicate.And(o => o.Status == RecruitInterviewerTodoStatus.Untreated && o.Type == RecruitInterviewerTodoType.InterviewScreen); break;
            case 1: predicate = predicate.And(o => o.Status == RecruitInterviewerTodoStatus.Untreated && o.Type == RecruitInterviewerTodoType.Interview); break;
            case 2: predicate = predicate.And(o => o.Status == RecruitInterviewerTodoStatus.Untreated); break;
        }

        var sql = _context.Recruit_Interviewer_Todo.Where(predicate);

        retModel.Total = sql.Count();

        retModel.Rows = sql
        .OrderByDescending(o => o.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new TodoModel
        {
            TodoId = s.Id,
            InterviewerTodoType = s.Type,
            RelationId = s.RelationId,
            CreatTime = s.CreatedTime,
        }).ToList();

        var interviewIdArray = retModel.Rows.Where(o => o.InterviewerTodoType == RecruitInterviewerTodoType.Interview).Select(s => s.RelationId).ToList();
        var screenIdArray = retModel.Rows.Where(o => o.InterviewerTodoType == RecruitInterviewerTodoType.InterviewScreen).Select(s => s.RelationId).ToList();


        var obj1 = _context.Recruit_Interview.Where(o => interviewIdArray.Contains(o.InterviewId))
            .Select(s => new
            {
                s.InterviewId,
                s.User_Seeker.NickName,
                s.User_Seeker.Avatar,
                s.User_Seeker.UserId,
                s.User_Seeker.User.User_Resume.Sex,
                s.User_Seeker.User.User_Resume.Birthday,
                s.User_Seeker.User.User_Resume.Education,
                s.User_Seeker.User.User_Resume.Occupation,
                s.Recruit.PostName,
                s.Recruit.Post_Delivery.Post.Project.Agent_Ent.Name,
                s.Recruit.Post_Delivery.Post.ProjectId,
                HrName = s.Recruit.User_Hr.NickName,
                HrAvatar = s.Recruit.User_Hr.Avatar
            }).ToList();

        var obj2 = _context.Recruit_Interviewer_Screen.Where(o => screenIdArray.Contains(o.Id))
            .Select(s => new
            {
                s.Id,
                s.User_Seeker.NickName,
                s.User_Seeker.Avatar,
                s.User_Seeker.UserId,
                s.User_Seeker.User.User_Resume.Sex,
                s.User_Seeker.User.User_Resume.Birthday,
                s.User_Seeker.User.User_Resume.Education,
                s.User_Seeker.User.User_Resume.Occupation,
                s.Recruit.PostName,
                s.Recruit.Post_Delivery.Post.Project.Agent_Ent.Name,
                s.Recruit.Post_Delivery.Post.ProjectId,
                HrName = s.Recruit.User_Hr.NickName,
                HrAvatar = s.Recruit.User_Hr.Avatar
            }).ToList();

        retModel.Rows.ForEach(item =>
        {
            if (item.InterviewerTodoType == RecruitInterviewerTodoType.Interview)
            {
                var obj = obj1.First(x => x.InterviewId == item.RelationId);
                item.UserName = obj.NickName;
                item.UserId = obj.UserId;
                item.HeadPortrait = obj.Avatar ?? "";
                item.Age = obj.Birthday == null ? 0 : DateTime.Now.Year - obj.Birthday.Value.Year;
                item.Occupation = obj.Occupation == null ? "" : obj.Occupation.GetDescription();
                item.Education = obj.Education == null ? "" : obj.Education.GetDescription();
                item.Sex = obj.Sex;
                item.SexName = obj.Sex == null ? "" : obj.Sex.GetDescription();
                item.InterviewerTodoTypeName = "待面试";
                item.JobApplication = obj.PostName;
                item.ProjectId = obj.ProjectId;
                item.ProjectName = obj.Name;
                item.ProjectManager = obj.HrName;
                item.ProjectHeadPortrait = obj.HrAvatar ?? "";
            }
            else if (item.InterviewerTodoType == RecruitInterviewerTodoType.InterviewScreen)
            {
                var obj = obj2.First(x => x.Id == item.RelationId);
                item.UserName = obj.NickName;
                item.UserId = obj.UserId;
                item.HeadPortrait = obj.Avatar ?? "";
                item.Age = obj.Birthday == null ? 0 : DateTime.Now.Year - obj.Birthday.Value.Year;
                item.Occupation = obj.Occupation == null ? "" : obj.Occupation.GetDescription();
                item.Education = obj.Education == null ? "" : obj.Education.GetDescription();
                item.Sex = obj.Sex;
                item.SexName = obj.Sex == null ? "" : obj.Sex.GetDescription();
                item.InterviewerTodoTypeName = "待筛选";
                item.JobApplication = obj.PostName;
                item.ProjectId = obj.ProjectId;
                item.ProjectName = obj.Name;
                item.ProjectManager = obj.HrName;
                item.ProjectHeadPortrait = obj.HrAvatar ?? "";
            }
        });

        return retModel;
    }

    /// <summary>
    /// 获取面试官筛选列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public ScreenListResponse GetScreenList(ScreenListRequest model)
    {
        ScreenListResponse retModel = new ScreenListResponse();

        var userModel = _context.User.Where(o => o.UserId == _user.Id).First();

        var predicate = PredicateBuilder.New<Recruit_Interviewer_Todo>(o => o.Project_Interviewer.Phone == userModel.Mobile && o.Type == RecruitInterviewerTodoType.InterviewScreen && o.Recruit_Interviewer_Screen.Status == model.Status);

        if (!string.IsNullOrEmpty(model.Name))
        {
            predicate = predicate.And(o => o.Recruit_Interviewer_Screen.User_Seeker.NickName.Contains(model.Name));
        }

        if (!string.IsNullOrEmpty(model.ProjectId))
        {
            predicate = predicate.And(o => o.Recruit.Post_Delivery.Post.ProjectId == model.ProjectId);
        }

        var sql = _context.Recruit_Interviewer_Todo.Where(predicate);

        retModel.Total = sql.Count();

        retModel.Rows = sql
        .OrderByDescending(o => o.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new ScreenModel
        {
            TodoId = s.Id,
            UserId = s.Recruit_Interviewer_Screen.User_Seeker.UserId,
            UserName = s.Recruit_Interviewer_Screen.User_Seeker.NickName,
            HeadPortrait = s.Recruit_Interviewer_Screen.User_Seeker.Avatar ?? "",
            Sex = s.Recruit_Interviewer_Screen.User_Seeker.User_Resume.Sex,
            Education = s.Recruit.User_Seeker.User.User_Resume.Education,
            Occupation = s.Recruit.User_Seeker.User.User_Resume.Occupation,
            Birthday = s.Recruit.User_Seeker.User.User_Resume.Birthday,
            JobApplication = s.Recruit_Interviewer_Screen.Recruit.PostName,
            ProjectId = s.Recruit.Post_Delivery.Post.Project.ProjectId,
            ProjectName = s.Recruit.Post_Delivery.Post.Project.Agent_Ent.Name,
            ProjectManager = s.Recruit.Post_Delivery.Post.Project.User_Hr.NickName,
            ProjectHeadPortrait = s.Recruit.Post_Delivery.Post.Project.User_Hr.Avatar ?? "",
            InterviewerScreenStatus = s.Recruit_Interviewer_Screen.Status,
            InterviewerScreenStatusName = s.Recruit_Interviewer_Screen.Status.GetDescription(),
            CreatTime = s.CreatedTime,
        }).ToList();

        if (retModel.Rows.Count > 0)
        {
            foreach (var i in retModel.Rows)
            {
                i.Age = i.Birthday == null ? 0 : DateTime.Now.Year - i.Birthday.Value.Year;
                i.OccupationName = i.Occupation == null ? "" : i.Occupation.GetDescription();
                i.EducationName = i.Education == null ? "" : i.Education.GetDescription();
                i.SexName = i.Sex == null ? "" : i.Sex.GetDescription();
            }
        }

        return retModel;
    }

    /// <summary>
    /// 获取面试官面试列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public InterviewListResponse GetInterviewList(InterviewListRequest model)
    {
        InterviewListResponse retModel = new InterviewListResponse();

        var userModel = _context.User.Where(o => o.UserId == _user.Id).First();

        var predicate = PredicateBuilder.New<Recruit_Interviewer_Todo>(o => o.Project_Interviewer.Phone == userModel.Mobile && o.Type == RecruitInterviewerTodoType.Interview && o.Recruit_Interview.Outcome == model.Outcome);

        if (!string.IsNullOrEmpty(model.Name))
        {
            predicate = predicate.And(o => o.Recruit_Interview.User_Seeker.NickName.Contains(model.Name));
        }

        if (!string.IsNullOrEmpty(model.ProjectId))
        {
            predicate = predicate.And(o => o.Recruit.Post_Delivery.Post.ProjectId == model.ProjectId);
        }

        var sql = _context.Recruit_Interviewer_Todo.Where(predicate);

        retModel.Total = sql.Count();

        retModel.Rows = sql
        .OrderByDescending(o => o.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new InterviewModel
        {
            TodoId = s.Id,
            UserId = s.Recruit_Interview.User_Seeker.UserId,
            UserName = s.Recruit_Interview.User_Seeker.NickName,
            HeadPortrait = s.Recruit_Interview.User_Seeker.Avatar ?? "",
            Sex = s.Recruit_Interview.User_Seeker.User_Resume.Sex,
            Education = s.Recruit_Interview.User_Seeker.User.User_Resume.Education,
            Occupation = s.Recruit_Interview.User_Seeker.User.User_Resume.Occupation,
            Birthday = s.Recruit_Interview.User_Seeker.User.User_Resume.Birthday,
            JobApplication = s.Recruit_Interview.Recruit.PostName,
            ProjectId = s.Recruit.Post_Delivery.Post.Project.ProjectId,
            ProjectName = s.Recruit.Post_Delivery.Post.Project.Agent_Ent.Name,
            ProjectManager = s.Recruit.Post_Delivery.Post.Project.User_Hr.NickName,
            ProjectHeadPortrait = s.Recruit.Post_Delivery.Post.Project.User_Hr.Avatar ?? "",
            CreatTime = s.CreatedTime,
            Forms = s.Recruit_Interview.Forms,
            FormsName = s.Recruit_Interview.Forms.GetDescription(),
            Outcome = s.Recruit_Interview.Outcome,
            OutcomeName = s.Recruit_Interview.Outcome.GetDescription(),
            Process = s.Recruit_Interview.Process,
            ProcessName = s.Recruit_Interview.Process.GetDescription(),
            InterviewTime = s.Recruit_Interview.InterviewTime
        }).ToList();

        if (retModel.Rows.Count > 0)
        {
            foreach (var i in retModel.Rows)
            {
                i.Age = i.Birthday == null ? 0 : DateTime.Now.Year - i.Birthday.Value.Year;
                i.OccupationName = i.Occupation == null ? "" : i.Occupation.GetDescription();
                i.EducationName = i.Education == null ? "" : i.Education.GetDescription();
                i.SexName = i.Sex == null ? "" : i.Sex.GetDescription();
            }
        }

        return retModel;
    }

    /// <summary>
    /// 面试官修改面试结果
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse EditInterviewOutcome(EditInterviewOutcomeRequest model)
    {
        var todoModel = _context.Recruit_Interviewer_Todo.Where(o => o.Id == model.TodoId && o.Type == RecruitInterviewerTodoType.Interview).FirstOrDefault();
        if (todoModel == null)
        {
            throw new NotFoundException("待办记录不存在！");
        }

        string projectId = _context.Recruit_Interviewer_Todo.Where(o => o.Id == model.TodoId && o.Type == RecruitInterviewerTodoType.Interview).Select(o => o.Recruit.Post_Delivery.Post.ProjectId).FirstOrDefault() ?? "";
        if (string.IsNullOrEmpty(projectId))
        {
            throw new NotFoundException("项目状态异常！");
        }

        string userPhone = _context.User.Where(o => o.UserId == _user.Id).Select(o => o.Mobile).First();
        var interviewerModel = _context.Project_Interviewer.Where(o => o.Phone == userPhone && o.ProjectId == projectId).FirstOrDefault();
        if (interviewerModel is null)
        {
            throw new NotFoundException("面试官账号不存在！");
        }

        if (model.Outcome == RecruitInterviewOutcome.Waiting)
        {
            throw new NotFoundException("操作无效！");
        }

        var interviewModel = _context.Recruit_Interview.Where(o => o.InterviewId == todoModel.RelationId).FirstOrDefault();
        if (interviewModel == null)
        {
            throw new NotFoundException("面试记录不存在！");
        }

        interviewModel.Outcome = model.Outcome;
        interviewModel.Remarks = model.Remarks;
        interviewModel.TodoTime = DateTime.Now;

        todoModel.Status = RecruitInterviewerTodoStatus.Processed;

        #region 自流转设置应用
        // bool isNotice = false;
        Recruit_Record? AddRecord = null;
        if (model.Outcome == RecruitInterviewOutcome.Fail)
        {
            var automaticModel = _context.Project_Automatic.Where(o => o.ProjectId == projectId).FirstOrDefault();
            if (automaticModel is not null)
            {
                if (automaticModel.InterviewerOutCome)
                {
                    var recruitModel = _context.Recruit.Where(o => o.RecruitId == interviewModel.RecruitId).FirstOrDefault();
                    if (recruitModel is not null)
                    {
                        recruitModel.Status = RecruitStatus.FileAway;
                        recruitModel.FileAway = automaticModel.ScreenFileStatus;
                        recruitModel.FileRemarks = automaticModel.ScreenFileRemarks;
                        recruitModel.UpdatedTime = DateTime.Now;
                        recruitModel.StatusTime = DateTime.Now;

                        #region 添加招聘流程记录表记录
                        AddRecord = new Recruit_Record()
                        {
                            Creator = recruitModel.HrId,
                            RecruitId = recruitModel.RecruitId,
                            Status = RecruitStatus.FileAway,
                            FileAway = automaticModel.ScreenFileStatus,
                            FileAwayRemarks = automaticModel.ScreenFileRemarks
                        };
                        _context.Add(AddRecord);
                        #endregion

                        #region 反查真实人才库表修改用户等级
                        var platformModel = _context.Talent_Platform.IgnoreQueryFilters().Where(o => o.SeekerId == recruitModel.SeekerId && o.HrId == recruitModel.HrId).FirstOrDefault();
                        if (platformModel is not null)
                        {
                            if (platformModel.Level != TalentPlatformLevel.入职用户 && platformModel.Level != TalentPlatformLevel.合同用户)
                            {
                                platformModel.Level = TalentPlatformLevel.归档用户;
                            }
                        }
                        #endregion

                        // isNotice = true;
                    }
                }
            }
        }
        #endregion

        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 面试官修改筛简历状态
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public EmptyResponse EditScreenStatus(EditScreenStatusRequest model)
    {
        var todoModel = _context.Recruit_Interviewer_Todo.Where(o => o.Id == model.TodoId && o.Type == RecruitInterviewerTodoType.InterviewScreen).FirstOrDefault();
        if (todoModel == null)
        {
            throw new NotFoundException("待办记录不存在！");
        }

        string projectId = _context.Recruit_Interviewer_Todo.Where(o => o.Id == model.TodoId && o.Type == RecruitInterviewerTodoType.InterviewScreen).Select(o => o.Recruit.Post_Delivery.Post.ProjectId).FirstOrDefault() ?? "";
        if (string.IsNullOrEmpty(projectId))
        {
            throw new NotFoundException("项目状态异常！");
        }

        string userPhone = _context.User.Where(o => o.UserId == _user.Id).Select(o => o.Mobile).First();
        var interviewerModel = _context.Project_Interviewer.Where(o => o.Phone == userPhone && o.ProjectId == projectId).FirstOrDefault();
        if (interviewerModel is null)
        {
            throw new NotFoundException("面试官账号不存在！");
        }

        if (model.Status == RecruitInterviewerScreenStatus.NoFedBack)
        {
            throw new NotFoundException("操作无效！");
        }

        var interviewScreenModel = _context.Recruit_Interviewer_Screen.Where(o => o.Id == todoModel.RelationId).FirstOrDefault();
        if (interviewScreenModel == null)
        {
            throw new NotFoundException("面试官筛选记录不存在！");
        }
        interviewScreenModel.Status = model.Status;
        interviewScreenModel.Remarks = model.Remarks;
        interviewScreenModel.TodoTime = DateTime.Now;

        todoModel.Status = RecruitInterviewerTodoStatus.Processed;

        #region 自流转设置应用
        // bool isNotice = false;
        Recruit_Record? AddRecord = null;
        if (model.Status == RecruitInterviewerScreenStatus.NoAdopt)
        {
            var automaticModel = _context.Project_Automatic.Where(o => o.ProjectId == projectId).FirstOrDefault();
            if (automaticModel is not null)
            {
                if (automaticModel.InterviewerOutCome)
                {
                    var recruitModel = _context.Recruit.Where(o => o.RecruitId == interviewScreenModel.RecruitId).FirstOrDefault();
                    if (recruitModel is not null)
                    {
                        recruitModel.Status = RecruitStatus.FileAway;
                        recruitModel.FileAway = automaticModel.ScreenFileStatus;
                        recruitModel.FileRemarks = automaticModel.ScreenFileRemarks;
                        recruitModel.UpdatedTime = DateTime.Now;
                        recruitModel.StatusTime = DateTime.Now;

                        #region 添加招聘流程记录表记录
                        AddRecord = new Recruit_Record()
                        {
                            Creator = recruitModel.HrId,
                            RecruitId = recruitModel.RecruitId,
                            Status = RecruitStatus.FileAway,
                            FileAway = automaticModel.ScreenFileStatus,
                            FileAwayRemarks = automaticModel.ScreenFileRemarks
                        };
                        _context.Add(AddRecord);
                        #endregion

                        #region 反查真实人才库表修改用户等级
                        var platformModel = _context.Talent_Platform.IgnoreQueryFilters().Where(o => o.SeekerId == recruitModel.SeekerId && o.HrId == recruitModel.HrId).FirstOrDefault();
                        if (platformModel is not null)
                        {
                            if (platformModel.Level != TalentPlatformLevel.入职用户 && platformModel.Level != TalentPlatformLevel.合同用户)
                            {
                                platformModel.Level = TalentPlatformLevel.归档用户;
                            }
                        }
                        #endregion

                        // isNotice = true;
                    }
                }
            }
        }
        #endregion

        _context.SaveChanges();

        return new EmptyResponse();
    }

    /// <summary>
    /// 获取用户基本信息
    /// </summary>
    /// <param name="todoId"></param>
    /// <returns></returns>
    public UserInformationResponse GetUserInformation(string todoId)
    {
        UserInformationResponse? retModel = new UserInformationResponse();
        retModel = _context.Recruit_Interviewer_Todo.Where(o => o.Id == todoId).Select(o => new UserInformationResponse
        {
            UserId = o.Recruit.User_Seeker.User.UserId,
            HeadPortrait = o.Recruit.User_Seeker.Avatar,
            UserName = o.Recruit.User_Seeker.NickName,
            UserAddress = o.Recruit.User_Seeker.Address,
            LastLoginTime = o.Recruit.User_Seeker.User_Extend.LoginTime,
            RecentlyActive = 0,
            Attracted = o.Recruit.User_Seeker.User_Num.Adviser,
            BrowsePosition = 0,
            OriginalName = o.Recruit.User_Seeker.User_Resume_Attach.Title,
            OriginalUrl = o.Recruit.User_Seeker.User_Resume_Attach.Url,
            InterviewRemarks = o.Recruit_Interview.Remarks,
            RecruitInterviewOutcome = o.Recruit_Interview.Outcome,
            RecruitInterviewProcess = o.Recruit_Interview.Process,
            RecruitInterviewerScreenStatus = o.Recruit_Interviewer_Screen.Status,
            InterviewerScreenRemarks = o.Recruit_Interviewer_Screen.Remarks,
            UserInfo = new UserInfo
            {
                Birthday = o.Recruit.User_Seeker.User.User_Resume.Birthday,
                Occupation = o.Recruit.User_Seeker.User.User_Resume.Occupation,
                Sex = o.Recruit.User_Seeker.User.User_Resume.Sex
            },
            UserEducation = new UserEducation
            {
                SchoolName = o.Recruit.User_Seeker.User_Resume.School,
                Major = o.Recruit.User_Seeker.User_Resume.Major,
                EntranceTime = o.Recruit.User_Seeker.User_Resume.GraduationDate,
                Education = o.Recruit.User_Seeker.User_Resume.Education,
            },
            UserLabel = new UserLabel
            {
                Nature = o.Recruit.User_Seeker.User_Resume.Nature,
                Skill = o.Recruit.User_Seeker.User_Resume.Skill,
                Appearance = o.Recruit.User_Seeker.User_Resume.Appearance
            },
            UserSelfEvaluate = new UserSelfEvaluate
            {
                SelfEvaluate = o.Recruit.User_Seeker.User_Resume.Describe
            }

        }).FirstOrDefault();

        if (retModel is null)
        {
            throw new NotFoundException("用户不存在！");
        }
        else
        {
            retModel.RecentlyActive = retModel.LastLoginTime == null ? 0 : (int)(DateTime.Now - retModel.LastLoginTime.Value).TotalDays;
            retModel.RecruitInterviewOutcomeName = retModel.RecruitInterviewOutcome?.GetDescription();
            retModel.RecruitInterviewProcessName = retModel.RecruitInterviewProcess?.GetDescription();
            retModel.RecruitInterviewerScreenStatusName = retModel.RecruitInterviewerScreenStatus?.GetDescription();
            if (retModel.UserInfo is not null)
            {
                retModel.UserInfo.Age = retModel.UserInfo.Birthday == null ? 0 : DateTime.Now.Year - retModel.UserInfo.Birthday.Value.Year;
            }
            if (retModel.UserEducation is not null)
            {
                retModel.UserEducation.EducationName = retModel.UserEducation.Education == null ? "" : retModel.UserEducation.Education.GetDescription();
            }
            if (retModel.UserLabel is not null)
            {
                retModel.UserLabel.Label = new List<string>();
                if (retModel.UserLabel.Nature is not null)
                {
                    retModel.UserLabel.Label.Union(retModel.UserLabel.Nature);
                }
                if (retModel.UserLabel.Skill is not null)
                {
                    retModel.UserLabel.Label.Union(retModel.UserLabel.Skill);
                }
                if (retModel.UserLabel.Appearance is not null)
                {
                    retModel.UserLabel.Label.Union(retModel.UserLabel.Appearance);
                }
            }
        }

        return retModel;
    }

    /// <summary>
    /// 获取招聘流程列表
    /// </summary>
    /// <param name="todoId"></param>
    /// <returns></returns>
    public RecruitmentProcessResponse GetRecruitmentProcessList(string todoId)
    {
        RecruitmentProcessResponse retModel = new RecruitmentProcessResponse();

        var DeliveryId = _context.Recruit_Interviewer_Todo.Where(o => o.Id == todoId).Select(o => o.Recruit.DeliveryId).FirstOrDefault();
        if (string.IsNullOrWhiteSpace(DeliveryId))
        {
            throw new NotFoundException("招聘流程不存在！");
        }

        retModel.Rows = _context.Recruit_Record.Where(o => o.Recruit.DeliveryId == DeliveryId).OrderByDescending(o => o.CreatedTime).Select(o => new RecruitmentProcessInfo
        {
            UserName = o.Recruit.User_Seeker.NickName,
            RecruitStatus = o.Status,
            OperationTime = o.CreatedTime,
            OperationPeople = o.Recruit.User_Hr.NickName,
            WorkPositionName = o.Recruit.PostName,
            RecruitInterviewModel = o.Status == RecruitStatus.Interview ? new RecruitInterviewModel()
            {
                Interviewer = o.Recruit_Interview.Project_Interviewer.Name,
                InterviewOutcome = o.Recruit_Interview.Outcome,
                InterviewTime = o.Recruit_Interview.InterviewTime,
                InterviewProcess = o.Recruit_Interview.Process,
                Remarks = o.Recruit_Interview.Remarks,
                UserFeedBack = o.Recruit_Interview.UserFeedBack,
                TodoTime = o.Recruit_Interview.TodoTime,
                CancelName = o.Recruit_Interview.CancelName
            } : null,
            RecruitScreenModel = o.Status == RecruitStatus.InterviewerScreening ? new RecruitScreenModel()
            {
                Interviewer = o.Recruit_Interviewer_Screen.Project_Interviewer.Name,
                InterviewerScreenStatus = o.Recruit_Interviewer_Screen.Status,
                Remarks = o.Recruit_Interviewer_Screen.Remarks,
                TodoTime = o.Recruit_Interviewer_Screen.TodoTime
            } : null,
            RecruitOfferModel = o.Status == RecruitStatus.Offer ? new RecruitOfferModel()
            {
                IsNotice = o.Recruit_Offer.IsMobileNotice,
                ReleaseTime = o.CreatedTime,
            } : null,
            RecruitFileAwayModel = o.Status == RecruitStatus.FileAway ? new RecruitFileAwayModel()
            {
                FileAwayReason = o.FileAwayRemarks,
                RecruitFileAway = o.FileAway,
            } : null,
        }).ToList();

        if (retModel.Rows.Count > 0)
        {
            foreach (var i in retModel.Rows)
            {
                i.RecruitStatusName = i.RecruitStatus.GetDescription();
                if (i.RecruitInterviewModel is not null)
                {
                    i.RecruitInterviewModel.InterviewOutcomeName = i.RecruitInterviewModel.InterviewOutcome?.GetDescription();
                    i.RecruitInterviewModel.InterviewProcessName = i.RecruitInterviewModel.InterviewProcess?.GetDescription();
                    i.RecruitInterviewModel.UserFeedBackName = i.RecruitInterviewModel.UserFeedBack?.GetDescription();
                }
                if (i.RecruitScreenModel is not null)
                {
                    i.RecruitScreenModel.InterviewerScreenStatusName = i.RecruitScreenModel.InterviewerScreenStatus?.GetDescription();
                }
                if (i.RecruitFileAwayModel is not null)
                {
                    i.RecruitFileAwayModel.RecruitFileAwayName = i.RecruitFileAwayModel.RecruitFileAway?.GetDescription();
                }
            }
        }

        return retModel;
    }

    /// <summary>
    /// 获取面试待办计划（日历模式）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public GetTodoPlanResponse GetTodoPlan(GetTodoPlanRequest model)
    {
        GetTodoPlanResponse retModel = new GetTodoPlanResponse();

        var userModel = _context.User.Where(o => o.UserId == _user.Id).First();

        DateTime startTime = model.CreatTime.Date.AddDays(1 - model.CreatTime.Date.Day);
        DateTime endTime = startTime.AddMonths(1);

        var todoList = _context.Recruit_Interviewer_Todo.Where(o => o.Recruit_Interview.InterviewTime >= startTime && o.Recruit_Interview.InterviewTime < endTime && o.Project_Interviewer.Phone == userModel.Mobile && o.Type == RecruitInterviewerTodoType.Interview).Select(o => o.Recruit_Interview.InterviewTime).ToList();

        var untreatedTodoList = _context.Recruit_Interviewer_Todo.Where(o => o.Recruit_Interview.InterviewTime >= startTime && o.Recruit_Interview.InterviewTime < endTime && o.Project_Interviewer.Phone == userModel.Mobile && o.Status == RecruitInterviewerTodoStatus.Untreated && o.Type == RecruitInterviewerTodoType.Interview).Select(o => o.Recruit_Interview.InterviewTime).ToList();

        todoList.ForEach(o => retModel.TodoList.Add(o.Day));

        untreatedTodoList.ForEach(o => retModel.UntreatedTodoList.Add(o.Day));

        return retModel;
    }

    /// <summary>
    /// 获取面试待办计划列表（日历模式）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public GetTodoPlanListResponse GetTodoPlanList(GetTodoPlanListRequest model)
    {
        GetTodoPlanListResponse retModel = new GetTodoPlanListResponse();

        var userModel = _context.User.Where(o => o.UserId == _user.Id).First();

        DateTime startTime = Convert.ToDateTime(model.CreatTime.ToString("yyyy-MM-dd 00:00:00"));
        DateTime endTime = Convert.ToDateTime(model.CreatTime.ToString("yyyy-MM-dd 23:59:59"));

        var predicate = PredicateBuilder.New<Recruit_Interviewer_Todo>(o => o.Recruit_Interview.InterviewTime >= startTime && o.Recruit_Interview.InterviewTime <= endTime && o.Project_Interviewer.Phone == userModel.Mobile && o.Type == RecruitInterviewerTodoType.Interview);

        var sql = _context.Recruit_Interviewer_Todo.Where(predicate);

        retModel.Total = sql.Count();

        retModel.Rows = sql
        .OrderByDescending(o => o.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetTodoPlanModel
        {
            TodoId = s.Id,
            UserId = s.Recruit_Interview.User_Seeker.UserId,
            UserName = s.Recruit_Interview.User_Seeker.NickName,
            HeadPortrait = s.Recruit_Interview.User_Seeker.Avatar ?? "",
            Sex = s.Recruit_Interview.User_Seeker.User_Resume.Sex,
            Education = s.Recruit_Interview.User_Seeker.User.User_Resume.Education,
            Occupation = s.Recruit_Interview.User_Seeker.User.User_Resume.Occupation,
            Birthday = s.Recruit_Interview.User_Seeker.User.User_Resume.Birthday,
            JobApplication = s.Recruit_Interview.Recruit.PostName,
            ProjectId = s.Recruit.Post_Delivery.Post.Project.ProjectId,
            ProjectName = s.Recruit.Post_Delivery.Post.Project.Agent_Ent.Name,
            ProjectManager = s.Recruit.Post_Delivery.Post.Project.User_Hr.NickName,
            ProjectHeadPortrait = s.Recruit.Post_Delivery.Post.Project.User_Hr.Avatar ?? "",
            CreatTime = s.CreatedTime,
            Forms = s.Recruit_Interview.Forms,
            FormsName = s.Recruit_Interview.Forms.GetDescription(),
            Outcome = s.Recruit_Interview.Outcome,
            OutcomeName = s.Recruit_Interview.Outcome.GetDescription(),
            Process = s.Recruit_Interview.Process,
            ProcessName = s.Recruit_Interview.Process.GetDescription(),
            InterviewTime = s.Recruit_Interview.InterviewTime
        }).ToList();

        if (retModel.Rows.Count > 0)
        {
            foreach (var i in retModel.Rows)
            {
                i.Age = i.Birthday == null ? 0 : DateTime.Now.Year - i.Birthday.Value.Year;
                i.OccupationName = i.Occupation == null ? "" : i.Occupation.GetDescription();
                i.EducationName = i.Education == null ? "" : i.Education.GetDescription();
                i.SexName = i.Sex == null ? "" : i.Sex.GetDescription();
            }
        }

        predicate = predicate.And(o => o.Status == RecruitInterviewerTodoStatus.Untreated);

        retModel.UntreatedTodoCount = _context.Recruit_Interviewer_Todo.Where(predicate).Count();

        return retModel;
    }

    /// <summary>
    /// 获取合作项目
    /// </summary>
    /// <returns></returns>
    public ProjectCooperationListResponse GetProjectCooperation(ProjectCooperationList model)
    {
        ProjectCooperationListResponse retModel = new ProjectCooperationListResponse();

        var userModel = _context.User.Where(o => o.UserId == _user.Id).First();

        retModel.Total = _context.Recruit_Interviewer_Todo
            .Where(o => o.Project_Interviewer.Phone == userModel.Mobile)
            .GroupBy(o => o.Recruit.Post_Delivery.Post.ProjectId).Count();


        var arrayList = _context.Recruit_Interviewer_Todo
           .Where(o => o.Project_Interviewer.Phone == userModel.Mobile)
           .GroupBy(o => o.Recruit.Post_Delivery.Post.ProjectId)
           .OrderBy(o => o.Key)
           .Skip((model.PageIndex - 1) * model.PageSize).Take(model.PageSize)
           .Select(o => o.Key).ToList();


        retModel.Rows = _context.Project.Where(o => arrayList.Contains(o.ProjectId)).Select(o => new ProjectCooperationModel
        {
            ProjectId = o.ProjectId,
            ProjectManagerImg = o.User_Hr.Avatar,
            ProjectManagerName = o.User_Hr.NickName,
            ProjectManagerPhone = o.User_Hr.User.Mobile,
            ProjectName = o.Agent_Ent.Name,
            ProjectStatus = o.Status,
            ProjectStatusName = o.Status.GetDescription(),
            ProcessedPeople = 0,
            WaitInterview = 0,
            WaitScreen = 0
        }).ToList();

        return retModel;
    }
}

