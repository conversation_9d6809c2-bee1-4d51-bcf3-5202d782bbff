using Infrastructure.Extend;
using Staffing.Model.Hr.Channel;
using Staffing.Core.Interfaces.Hr;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class ChannelRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("channel").WithTags("渠道");
        group.MapPost("/hrs/counselor", (SetCounselor model, IChannelService rep) => rep.GetCounselorInfo(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取顾问信息以及他下面渠道列表信息" });

        group.MapPost("/hrs/search", (SearchChannelName model, IChannelService rep) => rep.SearchChannel(model))
            .WithOpenApi(operation => new(operation) { Summary = "搜索求职者" });

        group.MapPost("/hrs/update", (CounselorChannel model, IChannelService rep) => rep.UpdateChannelInfo(model))
            .WithOpenApi(operation => new(operation) { Summary = "更新渠道信息" });

        group.MapPost("/hrs/counselorInviteCode", async (SetCounselorChannelCode model, IChannelService rep) => await rep.GetInviteChannelCode(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取顾问邀请渠道二维码" });
    }
}