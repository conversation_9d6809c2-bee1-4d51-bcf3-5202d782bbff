// using Config.Enums;
// using Config.CommonModel;
// using Staffing.Entity.Staffing;
// using Config.CommonModel.Business;

// namespace Staffing.Model.CentreService.NScore;

// public class GetOrders : QueryRequest
// {
//     /// <summary>
//     /// hr还是求职者
//     /// </summary>
//     public SeekerOrHr UserType { get; set; } = SeekerOrHr.Seeker;

//     /// <summary>
//     /// 订单状态
//     /// </summary>
//     public NScoreOrderStatus? Status { get; set; }

//     /// <summary>
//     /// 商品名称
//     /// </summary>
//     public string? GoodsName { get; set; }

//     /// <summary>
//     /// 用户手机号
//     /// </summary>
//     public string? UserMobile { get; set; }

//     /// <summary>
//     /// 起始时间
//     /// </summary>
//     public DateTime? BeginTime { get; set; }

//     /// <summary>
//     /// 结束时间
//     /// </summary>
//     public DateTime? EndTime { get; set; }
// }

// public class GetOrdersResponse : QueryResponse
// {
//     /// <summary>
//     /// 订单列表
//     /// </summary>
//     public List<OrderInfo>? Rows { get; set; }
// }

// public class OrderInfo
// {
//     /// <summary>
//     /// 订单Id
//     /// </summary>
//     public string? Id { get; set; }

//     /// <summary>
//     /// 商品名称
//     /// </summary>
//     public string GoodsName { get; set; } = null!;

//     /// <summary>
//     /// 用户手机号
//     /// </summary>
//     public string? UserMobile { get; set; }

//     /// <summary>
//     /// 用户姓名
//     /// </summary>
//     public string? UserName { get; set; }

//     /// <summary>
//     /// 积分
//     /// </summary>
//     public int Score { get; set; }

//     /// <summary>
//     /// 订单状态
//     /// </summary>
//     public NScoreOrderStatus Status { get; set; }

//     /// <summary>
//     /// 订单状态文本
//     /// </summary>
//     public string? StatusName { get; set; }

//     /// <summary>
//     /// 发货时间
//     /// </summary>
//     public DateTime? DeliveryTime { get; set; }

//     /// <summary>
//     /// 快递
//     /// </summary>
//     public string Express { get; set; } = null!;

//     /// <summary>
//     /// 快递单号
//     /// </summary>
//     public string ExpressNo { get; set; } = null!;

//     /// <summary>
//     /// 商品缩略图
//     /// </summary>
//     public string? Thumbnail { get; set; }

//     /// <summary>
//     /// 兑换时间
//     /// </summary>
//     public DateTime CreatedTime { get; set; } = DateTime.Now;

//     /// <summary>
//     /// 地址
//     /// </summary>
//     public KdAddressInfo Address { get; set; } = new KdAddressInfo();
// }

// public class SendOrders
// {
//     /// <summary>
//     /// 订单Id
//     /// </summary>
//     public string? OrderId { get; set; }

//     /// <summary>
//     /// 快递
//     /// </summary>
//     public string Express { get; set; } = string.Empty;

//     /// <summary>
//     /// 快递号
//     /// </summary>
//     public string ExpressNo { get; set; } = string.Empty;
// }

// public class SendOrdersResponse
// {

// }