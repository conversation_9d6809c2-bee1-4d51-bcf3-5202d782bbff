﻿using Config;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure.Proxy
{
    [Service(ServiceLifetime.Transient)]
    public class ThirdPartyApi
    {
        private readonly ConfigManager _config;
        private readonly CacheHelper _cacheHelper;
        private readonly LogManager _log;
        public ThirdPartyApi(IOptionsSnapshot<ConfigManager> config, CacheHelper cacheHelper, LogManager log)
        {
            _config = config.Value;
            _cacheHelper = cacheHelper;
            _log = log;
        }

        //public async Task SendSms()
        //{
        //}
    }
}
