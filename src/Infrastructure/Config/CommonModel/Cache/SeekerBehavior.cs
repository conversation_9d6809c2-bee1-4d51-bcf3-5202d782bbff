namespace Config.CommonModel.Cache;

public class SeekerDeliveryNum
{
    /// <summary>
    /// 报名职位
    /// </summary>
    public int RegistrationNum { get; set; }

    /// <summary>
    /// 面试数量
    /// </summary>
    public int InterviewNum { get; set; }

    /// <summary>
    /// 入职数量
    /// </summary>
    public int InductionNum { get; set; }

    /// <summary>
    /// 签约数量
    /// </summary>
    public int ContractNum { get; set; }
}

public class SeekerDeliveryBehavior
{
    /// <summary>
    /// Hr初筛
    /// </summary>
    public int HrScreeningNum { get; set; }

    /// <summary>
    /// 面试官初筛
    /// </summary>
    public int InterviewerScreeningNum { get; set; }

    /// <summary>
    /// 面试
    /// </summary>
    public int InterviewNum { get; set; }

    /// <summary>
    /// Offer数量
    /// </summary>
    public int OfferNum { get; set; }

    /// <summary>
    /// 入职数量
    /// </summary>
    public int InductionNum { get; set; }

    /// <summary>
    /// 签约数量
    /// </summary>
    public int ContractNum { get; set; }
}

public class PostBehavior
{

    /// <summary>
    /// 访问人数
    /// </summary>
    public int VisitorNum { get; set; }

    /// <summary>
    /// 报名职位
    /// </summary>
    public int RegistrationNum { get; set; }

    /// <summary>
    /// 面试数量
    /// </summary>
    public int InterviewNum { get; set; }

    /// <summary>
    /// 入职数量
    /// </summary>
    public int InductionNum { get; set; }

    /// <summary>
    /// 签约数量
    /// </summary>
    public int ContractNum { get; set; }
}