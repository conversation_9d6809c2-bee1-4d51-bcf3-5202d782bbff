using Config;
using Config.CommonModel.Tencent;
using Config.Enums;
using Flurl.Http;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using ServiceStack.Text;
using tencentyun;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Singleton)]
public class TencentImHelper
{
    private readonly ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    public TencentImHelper(IOptionsSnapshot<ConfigManager> config,
    IHostEnvironment hostingEnvironment, LogManager log)
    {
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
    }

    /// <summary>
    /// 创建Im账号
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<ImBaseResponse> CreateImAccount(CreateImAccount model)
    {
        var result = await IMRequest<ImBaseResponse>("im_open_login_svc/account_import", model);
        return result;
    }

    /// <summary>
    /// 拉取用户资料
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetImAccountResponse> GetImAccount(GetImAccount model)
    {
        model.TagList = new List<string> { "Tag_Profile_IM_Nick", "Tag_Profile_IM_Image","Tag_Profile_IM_Gender",
         "Tag_Profile_IM_BirthDay","Tag_Profile_Custom_Vocation","Tag_Profile_Custom_EntName","Tag_Profile_Custom_Post","Tag_Profile_IM_SelfSignature"};
        var result = await IMRequest<GetImAccountResponse>("profile/portrait_get", model);
        return result;
    }

    /// <summary>
    /// 设置用户资料
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task SetImAccount(SetImAccount model)
    {
        if (string.IsNullOrWhiteSpace(model.From_Account))
            throw new Exception("设置用户IM资料错误，IMId不存在");

        var profileItem = new List<ImBAccountTag>();

        var newNickName = new ImNickNameJson();

        if (!string.IsNullOrEmpty(model.NickName))
            newNickName.n = model.NickName;

        if (!string.IsNullOrEmpty(model.Avatar))
            profileItem.Add(new ImBAccountTag
            {
                Tag = "Tag_Profile_IM_Image",
                Value = model.Avatar
            });

        if (model.Sex.HasValue)
        {
            profileItem.Add(new ImBAccountTag
            {
                Tag = "Tag_Profile_IM_Gender",
                Value = model.Sex == Sex.男 ? "Gender_Type_Male" : "Gender_Type_Female"
            });
            newNickName.s = (int?)model.Sex;
        }

        if (model.Birthday.HasValue)
        {
            var bir = Convert.ToInt32(model.Birthday.Value.ToString("yyyyMMdd"));
            profileItem.Add(new ImBAccountTag
            {
                Tag = "Tag_Profile_IM_BirthDay",
                Value = bir
            });
            newNickName.b = bir;
        }

        if (model.Occupation.HasValue)
        {
            profileItem.Add(new ImBAccountTag
            {
                Tag = "Tag_Profile_Custom_Vocation",
                Value = model.Occupation.Value.GetDescription()
            });
            newNickName.v = (int?)model.Occupation;
        }

        if (!string.IsNullOrEmpty(model.EntName))
        {
            profileItem.Add(new ImBAccountTag
            {
                Tag = "Tag_Profile_Custom_EntName",
                Value = model.EntName
            });
            newNickName.e = model.EntName.Length > 20 ? model.EntName.Substring(0, 20) : model.EntName;
        }

        if (!string.IsNullOrEmpty(model.Post))
        {
            profileItem.Add(new ImBAccountTag
            {
                Tag = "Tag_Profile_Custom_Post",
                Value = model.Post
            });
            newNickName.p = model.Post;
        }

        profileItem.Add(new ImBAccountTag
        {
            Tag = "Tag_Profile_IM_Nick",
            Value = JsonSerializer.SerializeToString(newNickName)
        });

        if (profileItem.Count == 0)
            return;

        var result = await IMRequest<ImBaseResponse>("profile/portrait_set", new
        {
            From_Account = model.From_Account,
            ProfileItem = profileItem
        });
    }

    /// <summary>
    /// 获取用户未读消息数
    /// </summary>
    /// <param name="accountId"></param>
    /// <param name="peerAccount"></param>
    /// <returns></returns>
    public async Task<GetC2CUnreadMsgNumResponse> GetC2CUnreadMsgNum(string? accountId)
    {
        var result = await IMRequest<GetC2CUnreadMsgNumResponse>("openim/get_c2c_unread_msg_num", new { To_Account = accountId });
        return result;
    }
    /// <summary>
    /// 向Im发送消息
    /// </summary>
    /// <param name="msg"></param>
    /// <returns></returns>
    public async Task<ImBaseResponse> ImSendMsg(ImSendMsgInfo msg)
    {
        var result = await IMRequest<ImBaseResponse>("openim/sendmsg", msg);
        return result;
    }
    /// <summary>
    /// 获查询单聊消息
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetMessageListResponse> GetMessageList(GetMessageList model)
    {
        var result = await IMRequest<GetMessageListResponse>("openim/admin_getroammsg", model);
        return result;
    }

    /// <summary>
    /// 设置单聊消息已读
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<ImBaseResponse> ImSetMsgRead(ImSetMsgRead model)
    {
        var result = await IMRequest<ImBaseResponse>("openim/admin_set_msg_read", model);
        return result;
    }

    /// <summary>
    /// 消息本地已读（非IM）
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public ImMsgReadResponse ImMsgRead(ImMsgReadFull model)
    {
        if (string.IsNullOrWhiteSpace(model.ImId) || string.IsNullOrWhiteSpace(model.ToImId))
            throw new BadRequestException("缺少用户Id");

        var cacheKey = $"im:localread";

        MyRedis.Client.HSet(cacheKey, $"{model.ImId}_{model.ToImId}", DateTime.Now.ToUnixTime());

        var result = new ImMsgReadResponse();

        result.ReversalTime = MyRedis.Client.HGet<long>(cacheKey, $"{model.ToImId}_{model.ImId}");

        return result;
    }

    /// <summary>
    /// IM请求
    /// </summary>
    /// <typeparam name="TReturn"></typeparam>
    /// <param name="method"></param>
    /// <param name="obj"></param>
    /// <returns></returns>
    public async Task<TReturn> IMRequest<TReturn>(string method, object obj) where TReturn : ImBaseResponse
    {
        var result = await RetryHelper.Do(async () =>
        {
            var identifier = Constants.ImAdminId; //"administrator";
            var usersig = GetUserSig(identifier, 30);
            var sdkappid = _config.TencentOptions!.ImAppId;
            var random = DateTime.Now.Ticks;
            var url = $"https://console.tim.qq.com/v4/{method}?usersig={usersig}&identifier={identifier}&sdkappid={sdkappid}&random={random}&contenttype=json";

            var rstStr = await url
                .PostJsonAsync(obj)
                .ReceiveString();

            TReturn? rst = null;
            try
            {
                rst = JsonSerializer.DeserializeFromString<TReturn>(rstStr);
            }
            catch (Exception e)
            {
                throw new Exception($"腾讯Im接口解析出错：{Tools.GetErrMsg(e)}", new Exception(rstStr));
            }

            if (rst?.ErrorCode != 0)
                throw new Exception($"腾讯Im接口出错：{rst?.ErrorCode}##{rst?.ErrorInfo}", new Exception($"{method}：{JsonSerializer.SerializeToString(obj ?? new object())}"));

            return rst;
        });

        return result;
    }

    /// <summary>
    /// 生成UserSig
    /// </summary>
    /// <param name="id"></param>
    /// <param name="expire"></param>
    /// <returns></returns>
    public string GetUserSig(string id, int expireSeconds)
    {
        var sdkappid = _config.TencentOptions!.ImAppId;
        var key = _config.TencentOptions.ImAppKey;
        TLSSigAPIv2 api = new TLSSigAPIv2(Convert.ToInt32(sdkappid), key);
        var sig = api.GenSig(id, expireSeconds);

        return sig;
    }
}