using Config;
using Config.CommonModel.TalentResume;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.CommonRepository.ZhaoPinYun;
using Infrastructure.CommonService;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Infrastructure.NoahCommon;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Noah.Aliyun.Storage;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class TalentService
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _logger;
    private readonly IObjectStorage _objectStorage;
    private readonly ParseResume _parseResume;
    private readonly CommonDicService _commonDicService;
    private readonly CommonVirtualService _commonVirtualService;
    private readonly CommonEsService _commonEsService;
    private readonly ZPYunRepository _zpyunRepository;
    private CommonUserService _commonUserService;
    private ConfigManager _config;
    private PaymentStep _step;
    public TalentService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger,
    IObjectStorage objectStorage, ParseResume parseResume, CommonDicService commonDicService,
    CommonVirtualService commonVirtualService, IOptionsSnapshot<ConfigManager> config,
    CommonEsService commonEsService, CommonUserService commonUserService, PaymentStep step, ZPYunRepository zpyunRepository)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _objectStorage = objectStorage;
        _parseResume = parseResume;
        _commonDicService = commonDicService;
        _commonVirtualService = commonVirtualService;
        _config = config.Value;
        _commonEsService = commonEsService;
        _commonUserService = commonUserService;
        _step = step;
        _zpyunRepository = zpyunRepository;
    }

    /// <summary>
    /// 导入简历并解析
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task UpLoadResume(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.LPop<string>(SubscriptionKey.UpLoadResume);
                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息
                string? upLoadResumeId = msg;

                using var _context = _contextFactory.CreateDbContext();
                var upLoadRecordModel = _context.Talent_Upload_Record.Where(o => o.Id == upLoadResumeId).First();

                var notifyNew = new Sub_TalentCount_Change
                {
                    HrId = upLoadRecordModel.HrId
                };

                (Dictionary<string, AnalysisResumeItem> Dic, List<ExcelResume> Importer, int RepeatCount) = await _parseResume.AnalysisResumeFun(upLoadRecordModel.FileUrl, upLoadRecordModel.FileName);

                var regionModel = _commonDicService.GetRegion();

                #region 统计字段
                //简历总数量
                int TotalNumber = 0;

                //失败数量
                int FailNumber = 0;

                //合格数量
                int QualifiedNumber = 0;

                //重复数量
                int RepeatNumber = RepeatCount;

                //入库数量
                int WarehousingNumber = 0;

                //更新步数
                int UpdateDept = 5;
                #endregion

                #region 行业、职业添加模型
                List<string> industryList = new List<string>();
                List<string> postList = new List<string>();
                #endregion

                foreach (var item in _parseResume.AnalysisResumeByDicYield(UpdateDept, Dic, false, upLoadRecordModel.FileUrl, upLoadRecordModel.FileName))
                {
                    List<AnalysisResumeResponse> resume = item.AnalysisData;
                    List<FaildResumeResponse> FaildData = item.FaildData;
                    TotalNumber += resume.Count + FaildData.Count;
                    FailNumber += FaildData.Count;

                    //写入虚拟人才库
                    foreach (var i in resume)
                    {
                        //判断是否重复简历
                        string? _Name = i.parsing_result?.basic_info?.name;
                        string? _Mobile = i.parsing_result?.contact_info?.phone_number;
                        string Remark = string.Empty, VirtualId = string.Empty;

                        bool isImplement = true;
                        Talent_Virtual? oldTalentVirtualModel = null;

                        var notifyOld = new Sub_TalentCount_Change
                        {
                            HrId = upLoadRecordModel.HrId
                        };

                        if (!string.IsNullOrEmpty(_Name) && !string.IsNullOrEmpty(_Mobile))
                        {
                            oldTalentVirtualModel = _context.Talent_Virtual.Where(o => o.Name == _Name && o.Mobile == _Mobile && o.HrId == upLoadRecordModel.HrId).FirstOrDefault();
                            if (oldTalentVirtualModel != null)
                            {
                                RepeatNumber++;

                                switch (upLoadRecordModel.RepeatType)
                                {
                                    case TalentUpLoadRepeatType.Added:
                                        isImplement = true;
                                        break;
                                    case TalentUpLoadRepeatType.Skip:
                                        isImplement = false;
                                        Remark = "跳过";
                                        break;
                                    case TalentUpLoadRepeatType.Cover:
                                        isImplement = true;

                                        string oldVirtualId = oldTalentVirtualModel.Id;
                                        string oldvirtualHrId = oldTalentVirtualModel.HrId;

                                        //教育
                                        var eduList = _context.Talent_Virtual_Edu.Where(o => o.VirtualId == oldVirtualId);

                                        //工作
                                        var workList = _context.Talent_Virtual_Work.Where(o => o.VirtualId == oldVirtualId);

                                        //项目
                                        var projectList = _context.Talent_Virtual_Project.Where(o => o.VirtualId == oldVirtualId);

                                        //求职期望
                                        var hopeList = _context.Talent_Virtual_Hope.Where(o => o.VirtualId == oldVirtualId);

                                        // //关系表
                                        // var relationList = _context.Talent_Relation.Where(o => o.TalentId == oldVirtualId);

                                        //上传简历记录
                                        var uploadList = _context.Talent_Upload_Recordsub.Where(o => o.VirtualId == oldVirtualId);

                                        //记录原始信息
                                        if (isImplement)
                                        {
                                            notifyOld.PostOld = workList.Select(x => x.PostName).ToList();
                                            notifyOld.IndustryOld = workList.Select(x => x.IndustryName).ToList();
                                        }

                                        _context.Talent_Virtual.Remove(oldTalentVirtualModel);
                                        _context.Talent_Virtual_Edu.RemoveRange(eduList);
                                        _context.Talent_Virtual_Work.RemoveRange(workList);
                                        _context.Talent_Virtual_Project.RemoveRange(projectList);
                                        _context.Talent_Virtual_Hope.RemoveRange(hopeList);
                                        // _context.Talent_Relation.RemoveRange(relationList);

                                        foreach (var h in uploadList)
                                        {
                                            h.VirtualId = string.Empty;
                                        }

                                        _context.SaveChanges();

                                        //通知月斌删除虚拟人才库
                                        //string url = _config.DataRdsServer + "/datards/linggong/resume/virtual/remove";
                                        //var result = await url.PostJsonAsync(new { ids = new List<string> { oldVirtualId } }).ReceiveString();

                                        //删除人才库通知楷哥消息
                                        MyRedis.Client.SAdd(SubscriptionKey.VirtualTalentCountChange, notifyOld);

                                        break;
                                }
                            }
                        }

                        if (isImplement)
                        {
                            try
                            {
                                Talent_Virtual? addTalent = null;

                                //简历信息
                                if (i.parsing_result != null)
                                {

                                    //基本信息（求职期望）
                                    if (i.parsing_result.basic_info != null)
                                    {
                                        addTalent = new Talent_Virtual()
                                        {
                                            HeadPortrait = i.avatar_url ?? string.Empty,
                                            Name = i.parsing_result.basic_info.name ?? string.Empty,
                                            Mobile = i.parsing_result.contact_info?.phone_number ?? string.Empty,
                                            Sex = i.parsing_result.basic_info.gender == "男" ? Sex.男 : Sex.女,
                                            Mailbox = i.parsing_result.contact_info?.email ?? string.Empty,
                                            WeChat = i.parsing_result.contact_info?.wechat ?? string.Empty,
                                            QQ = i.parsing_result.contact_info?.QQ ?? string.Empty,
                                            WorkTime = DateTime.Now.AddYears(0 - i.parsing_result.basic_info.num_work_experience),
                                            Location = i.parsing_result.basic_info.current_location ?? string.Empty,
                                            DetailedLocation = i.parsing_result.basic_info.detailed_location ?? string.Empty,
                                            LocationNorm = i.parsing_result.basic_info.current_location_norm ?? string.Empty,
                                            Channel = TalentVirtualChannel.ResumeUpload,
                                            CreatedTime = DateTime.Now,
                                            HrId = upLoadRecordModel.HrId,
                                            ChannelId = upLoadRecordModel.ChannelId,
                                            OriginalUrl = upLoadRecordModel.FileUrl,
                                            Remark = "",
                                            Status = TalentVirtualStatus.UnRegistered,
                                            SeekerId = "",
                                        };
                                        _context.Talent_Virtual.Add(addTalent);

                                        if (!string.IsNullOrEmpty(i.parsing_result.basic_info.detailed_location))
                                        {
                                            addTalent.RegionId = regionModel.Where(o => o.Name.Contains(i.parsing_result.basic_info.detailed_location)).FirstOrDefault()?.Id ?? string.Empty;
                                        }
                                        else
                                        {
                                            addTalent.RegionId = string.Empty;
                                        }

                                        if (!string.IsNullOrEmpty(i.parsing_result.basic_info.degree))
                                        {
                                            switch (i.parsing_result.basic_info.degree)
                                            {
                                                case "硕士": addTalent.Education = TalentVirtualEducation.Master; break;
                                                case "博士": addTalent.Education = TalentVirtualEducation.Doctor; break;
                                                case "本科": addTalent.Education = TalentVirtualEducation.Undergraduate; break;
                                                case "大专": addTalent.Education = TalentVirtualEducation.Professional; break;
                                                case "高中": addTalent.Education = TalentVirtualEducation.Senior; break;
                                                case "中专": addTalent.Education = TalentVirtualEducation.Specialized; break;
                                                case "初中": addTalent.Education = TalentVirtualEducation.Junior; break;
                                                default: addTalent.Education = TalentVirtualEducation.Junior; break;
                                            }
                                        }

                                        if (i.parsing_result.basic_info.age.HasValue)
                                        {
                                            var bd = Tools.GetBirthdateByAge(i.parsing_result.basic_info.age);
                                            if (bd.HasValue)
                                                addTalent.Birthday = bd.Value.ToDateTime(TimeOnly.MinValue);
                                        }

                                        if (!string.IsNullOrEmpty(i.parsing_result.basic_info.date_of_birth))
                                        {
                                            if (i.parsing_result.basic_info.date_of_birth.Length == 4)
                                            {
                                                if (DateTime.TryParse(i.parsing_result.basic_info.date_of_birth + "-01-01", out DateTime _Birthday))
                                                {
                                                    addTalent.Birthday = _Birthday;
                                                }
                                            }
                                            else
                                            {
                                                if (DateTime.TryParse(i.parsing_result.basic_info.date_of_birth, out DateTime _Birthday))
                                                {
                                                    addTalent.Birthday = _Birthday;
                                                }
                                            }
                                        }

                                        if (i.parsing_result.contact_info != null)
                                        {
                                            if (!string.IsNullOrEmpty(i.parsing_result.contact_info.phone_number))
                                            {
                                                var userModel = _context.User.Where(o => o.Mobile == i.parsing_result.contact_info.phone_number).FirstOrDefault();
                                                if (userModel != null)
                                                {
                                                    // Talent_Relation addRelation = new Talent_Relation()
                                                    // {
                                                    //     HrId = addTalent.HrId,
                                                    //     SeekerId = userModel.UserId,
                                                    //     TalentId = addTalent.Id
                                                    // };
                                                    // _context.Talent_Relation.Add(addRelation);

                                                    addTalent.SeekerId = userModel.UserId;
                                                    addTalent.Status = TalentVirtualStatus.Registered;

                                                    if (!string.IsNullOrEmpty(userModel.IdentityCard))
                                                    {
                                                        addTalent.Status = TalentVirtualStatus.RealName;
                                                    }
                                                }
                                            }
                                        }

                                        //补充信息
                                        if (i.parsing_result.others != null)
                                        {
                                            addTalent.SelfEvaluation = i.parsing_result.others.self_evaluation ?? string.Empty;

                                            TalentVirtaualResumeSkillSub SkillAnalysis = new TalentVirtaualResumeSkillSub()
                                            {
                                                Business = i.parsing_result.others.business_skills,
                                                IT = i.parsing_result.others.it_skills,
                                                Professional = i.parsing_result.others.skills
                                            };
                                            addTalent.SkillAnalysis = SkillAnalysis;
                                        }
                                    }

                                    //求职期望
                                    if (addTalent != null && i.parsing_result.basic_info != null)
                                    {
                                        Talent_Virtual_Hope addHopeModel = new Talent_Virtual_Hope();
                                        addHopeModel.HopeCity = i.parsing_result.basic_info.expect_location ?? string.Empty;
                                        addHopeModel.IndustryName = i.parsing_result.basic_info.desired_industry ?? string.Empty;
                                        addHopeModel.PostName = i.parsing_result.basic_info.desired_position ?? string.Empty;
                                        addHopeModel.VirtualId = addTalent.Id;
                                        if (!string.IsNullOrEmpty(i.parsing_result.basic_info.expect_location))
                                        {
                                            addHopeModel.RegionId = regionModel.Where(o => o.Name.Contains(i.parsing_result.basic_info.expect_location)).FirstOrDefault()?.Id ?? string.Empty;
                                        }
                                        else
                                        {
                                            addHopeModel.RegionId = string.Empty;
                                        }

                                        if (!string.IsNullOrEmpty(i.parsing_result.basic_info.desired_salary))
                                        {
                                            string[] desired_salary = i.parsing_result.basic_info.desired_salary.Split("-");
                                            if (desired_salary.Length == 2)
                                            {
                                                string _MinSalary = desired_salary[0].Trim();
                                                string _MaxSalary = desired_salary[1].Trim();
                                                try
                                                {
                                                    addHopeModel.MinSalary = Convert.ToDecimal(System.Text.RegularExpressions.Regex.Replace(_MinSalary, @"[^\d]*", ""));
                                                    addHopeModel.MaxSalary = Convert.ToDecimal(System.Text.RegularExpressions.Regex.Replace(_MaxSalary, @"[^\d]*", ""));
                                                    if (_MinSalary.Contains('K') || _MinSalary.Contains('k'))
                                                    {
                                                        addHopeModel.MinSalary *= 1000;
                                                    }
                                                    if (_MaxSalary.Contains('K') || _MaxSalary.Contains('k'))
                                                    {
                                                        addHopeModel.MaxSalary *= 1000;
                                                    }
                                                }
                                                catch
                                                {
                                                    addHopeModel.MinSalary = 0;
                                                    addHopeModel.MaxSalary = 0;
                                                }
                                            }
                                        }
                                        _context.Add(addHopeModel);
                                    }

                                    //项目经历
                                    if (addTalent != null && i.parsing_result.project_experience != null && i.parsing_result.project_experience.Count > 0)
                                    {
                                        List<Talent_Virtual_Project> addProjectList = new List<Talent_Virtual_Project>();

                                        i.parsing_result.project_experience = i.parsing_result.project_experience.OrderBy(o => o.start_time_year).OrderBy(o => o.start_time_month).ToList();

                                        foreach (var m in i.parsing_result.project_experience)
                                        {
                                            addProjectList.Add(new Talent_Virtual_Project
                                            {
                                                CreatedTime = DateTime.Now,
                                                PostName = m.job_title ?? string.Empty,
                                                ProjectName = m.project_name ?? string.Empty,
                                                ProjectRemarks = m.description ?? string.Empty,
                                                StartTime = (string.IsNullOrEmpty(m.start_time_year) || string.IsNullOrEmpty(m.start_time_month)) ? DateTime.Now : Convert.ToDateTime(m.start_time_year + "-" + m.start_time_month + "-01"),
                                                EndTime = (string.IsNullOrEmpty(m.end_time_year) || string.IsNullOrEmpty(m.end_time_month)) ? null : Convert.ToDateTime(m.end_time_year + "-" + m.end_time_month + "-01"),
                                                VirtualId = addTalent.Id
                                            });
                                        }

                                        _context.Talent_Virtual_Project.AddRange(addProjectList);
                                    }

                                    //工作经历
                                    if (addTalent != null && i.parsing_result.work_experience != null && i.parsing_result.work_experience.Count > 0)
                                    {
                                        List<Talent_Virtual_Work> addWorkList = new List<Talent_Virtual_Work>();

                                        i.parsing_result.work_experience = i.parsing_result.work_experience.OrderBy(o => o.start_time_year).OrderBy(o => o.start_time_month).ToList();

                                        foreach (var m in i.parsing_result.work_experience)
                                        {
                                            Talent_Virtual_Work addWorkModel = new Talent_Virtual_Work()
                                            {
                                                CompanyName = m.company_name ?? string.Empty,
                                                CompanyRemarks = m.description ?? string.Empty,
                                                CreatedTime = DateTime.Now,
                                                Department = m.department ?? string.Empty,
                                                IndustryName = m.industry ?? string.Empty,
                                                PostName = m.job_title ?? string.Empty,
                                                StartTime = (string.IsNullOrEmpty(m.start_time_year) || string.IsNullOrEmpty(m.start_time_month)) ? DateTime.Now : Convert.ToDateTime(m.start_time_year + "-" + m.start_time_month + "-01"),
                                                EndTime = (string.IsNullOrEmpty(m.end_time_year) || string.IsNullOrEmpty(m.end_time_month)) ? null : Convert.ToDateTime(m.end_time_year + "-" + m.end_time_month + "-01"),
                                                VirtualId = addTalent.Id
                                            };

                                            if (!string.IsNullOrEmpty(m.industry))
                                            {
                                                industryList.Add(m.industry);
                                            }
                                            if (!string.IsNullOrEmpty(m.job_title))
                                            {
                                                postList.Add(m.job_title);
                                            }

                                            if (!string.IsNullOrEmpty(m.salary))
                                            {
                                                string[] salary = m.salary.Split("-");
                                                if (salary.Length == 2)
                                                {
                                                    string _MinSalary = salary[0].Trim();
                                                    string _MaxSalary = salary[1].Trim();
                                                    try
                                                    {
                                                        addWorkModel.MinSalary = Convert.ToDecimal(System.Text.RegularExpressions.Regex.Replace(_MinSalary, @"[^\d]*", ""));
                                                        addWorkModel.MaxSalary = Convert.ToDecimal(System.Text.RegularExpressions.Regex.Replace(_MaxSalary, @"[^\d]*", ""));
                                                        if (_MinSalary.Contains('K') || _MinSalary.Contains('k'))
                                                        {
                                                            addWorkModel.MinSalary *= 1000;
                                                        }
                                                        if (_MaxSalary.Contains('K') || _MaxSalary.Contains('k'))
                                                        {
                                                            addWorkModel.MaxSalary *= 1000;
                                                        }
                                                    }
                                                    catch
                                                    {
                                                        addWorkModel.MinSalary = 0;
                                                        addWorkModel.MaxSalary = 0;
                                                    }
                                                }
                                            }

                                            addWorkList.Add(addWorkModel);
                                        }

                                        _context.Talent_Virtual_Work.AddRange(addWorkList);

                                        notifyNew.PostNew = addWorkList.Select(x => x.PostName).ToList();
                                        notifyNew.IndustryNew = addWorkList.Select(x => x.IndustryName).ToList();
                                    }

                                    //教育经历
                                    if (addTalent != null && i.parsing_result.education_experience != null && i.parsing_result.education_experience.Count > 0)
                                    {
                                        List<Talent_Virtual_Edu> addEduList = new List<Talent_Virtual_Edu>();

                                        i.parsing_result.education_experience = i.parsing_result.education_experience.OrderBy(o => o.start_time_year).OrderBy(o => o.start_time_month).ToList();

                                        foreach (var m in i.parsing_result.education_experience)
                                        {
                                            Talent_Virtual_Edu addEduModel = new Talent_Virtual_Edu()
                                            {
                                                CreatedTime = DateTime.Now,
                                                IsFullTime = m.study_model == "非统招" ? false : true,
                                                MajorName = m.major ?? string.Empty,
                                                SchoolName = m.school_name ?? string.Empty,
                                                SchoolRemarks = string.Empty,
                                                StartTime = (string.IsNullOrEmpty(m.start_time_year) || string.IsNullOrEmpty(m.start_time_month)) ? DateTime.Now : Convert.ToDateTime(m.start_time_year + "-" + m.start_time_month + "-01"),
                                                EndTime = (string.IsNullOrEmpty(m.end_time_year) || string.IsNullOrEmpty(m.end_time_month)) ? null : Convert.ToDateTime(m.end_time_year + "-" + m.end_time_month + "-01"),
                                                VirtualId = addTalent.Id
                                            };

                                            switch (m.degree)
                                            {
                                                case "硕士": addEduModel.Education = TalentVirtualEducation.Master; break;
                                                case "博士": addEduModel.Education = TalentVirtualEducation.Doctor; break;
                                                case "本科": addEduModel.Education = TalentVirtualEducation.Undergraduate; break;
                                                case "大专": addEduModel.Education = TalentVirtualEducation.Professional; break;
                                                case "高中": addEduModel.Education = TalentVirtualEducation.Senior; break;
                                                case "中专": addEduModel.Education = TalentVirtualEducation.Specialized; break;
                                                case "初中": addEduModel.Education = TalentVirtualEducation.Junior; break;
                                                default: addEduModel.Education = TalentVirtualEducation.Junior; break;
                                            }

                                            addEduList.Add(addEduModel);
                                        }

                                        _context.Talent_Virtual_Edu.AddRange(addEduList);
                                    }
                                }

                                //画像信息
                                if (addTalent != null && i.predicted_result != null)
                                {
                                    //标签
                                    if (i.predicted_result.tags != null)
                                    {
                                        addTalent.IndustryLabel = i.predicted_result.tags.skills == null ? new List<string>() : i.predicted_result.tags.skills.Select(o => o.tag).ToList();
                                        addTalent.PostLabel = i.predicted_result.tags.professional == null ? new List<string>() : i.predicted_result.tags.professional.Select(o => o.tag).ToList();
                                        addTalent.OtherLabel = i.predicted_result.tags.others == null ? new List<string>() : i.predicted_result.tags.others.Select(o => o.tag).ToList();
                                    }

                                    //亮点
                                    if (i.predicted_result.highlights != null)
                                    {
                                        TalentVirtaualResumeHighlights _Highlights = new TalentVirtaualResumeHighlights()
                                        {
                                            education = i.predicted_result.highlights.education,
                                            occupation = i.predicted_result.highlights.occupation,
                                            others = i.predicted_result.highlights.others,
                                            project = i.predicted_result.highlights.project,
                                            tags = i.predicted_result.highlights.tags
                                        };

                                        addTalent.Highlights = _Highlights;
                                    }

                                    //风险
                                    if (i.predicted_result.risks != null)
                                    {
                                        TalentVirtaualResumeRisks _Risks = new TalentVirtaualResumeRisks()
                                        {
                                            education = i.predicted_result.risks.education,
                                            occupation = i.predicted_result.risks.occupation,
                                            tags = i.predicted_result.risks.tags
                                        };

                                        addTalent.Risks = _Risks;
                                    }
                                }

                                //保存与简历计算
                                if (addTalent != null)
                                {
                                    _context.SaveChanges();

                                    WarehousingNumber++;

                                    //计算简历完整度
                                    int totalCount = _commonVirtualService.CountVirtualResumeScore(addTalent.Id);
                                    if (totalCount >= 60)
                                    {
                                        QualifiedNumber++;
                                    }

                                    VirtualId = addTalent.Id;
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.Error("解析上传简历错误", ex.Message, JsonSerializer.SerializeToString(i));
                                FailNumber++;
                                Remark = ex.Message;
                            }
                        }

                        //添加上传简历记录表子表
                        SaveUploadRecordSub(_context, upLoadRecordModel, oldTalentVirtualModel, !string.IsNullOrEmpty(VirtualId), !string.IsNullOrEmpty(VirtualId), VirtualId, Remark);
                    }

                    //保存解析失败记录
                    if (FaildData.Count > 0)
                    {
                        _context.Talent_Upload_Recordsub.AddRange(FaildData.Select(x => new Talent_Upload_Recordsub
                        {
                            CreatedTime = DateTime.Now,
                            IsRepeat = false,
                            IsSuccess = false,
                            IsWarehousing = false,
                            RecordId = upLoadRecordModel.Id,
                            VirtualId = string.Empty,
                            Remarks = $"{x.UserName ?? string.Empty}({x.Mobile})导入失败：{x.FaildMsg ?? string.Empty}"
                        }));
                        _context.SaveChanges();
                    }

                    upLoadRecordModel.UpLoadStatus = TalentUpLoadStatus.Complete;
                    upLoadRecordModel.TotalNumber = TotalNumber;
                    upLoadRecordModel.FailNumber = FailNumber;
                    upLoadRecordModel.QualifiedNumber = QualifiedNumber;
                    upLoadRecordModel.RepeatNumber = RepeatNumber;
                    upLoadRecordModel.WarehousingNumber = WarehousingNumber;

                    if (postList.Count > 0)
                    {
                        var postDbList = _context.Dic_Talent_Post.Where(o => postList.Contains(o.PostName)).Select(o => o.PostName).ToList();
                        List<string> addPostList = new List<string>();
                        addPostList = postList.Except(postDbList).Distinct().ToList();
                        if (addPostList.Count > 0)
                        {
                            List<Dic_Talent_Post> addList = new List<Dic_Talent_Post>();
                            foreach (var i in addPostList)
                            {
                                addList.Add(new Dic_Talent_Post()
                                {
                                    CreatedTime = DateTime.Now,
                                    PostName = i,
                                    ParentId = "28"
                                });
                            }

                            _context.Dic_Talent_Post.AddRange(addList);
                        }
                    }

                    if (industryList.Count > 0)
                    {
                        var industryDbList = _context.Dic_Talent_Industry.Where(o => industryList.Contains(o.IndustryName)).Select(o => o.IndustryName).ToList();
                        List<string> addIndustryList = new List<string>();
                        addIndustryList = industryList.Except(industryDbList).Distinct().ToList();
                        if (addIndustryList.Count > 0)
                        {
                            List<Dic_Talent_Industry> addList = new List<Dic_Talent_Industry>();
                            foreach (var i in addIndustryList)
                            {
                                addList.Add(new Dic_Talent_Industry()
                                {
                                    CreatedTime = DateTime.Now,
                                    IndustryName = i
                                });
                            }

                            _context.Dic_Talent_Industry.AddRange(addList);
                        }
                    }

                    _context.SaveChanges();

                    //新增人才库通知楷哥消息
                    MyRedis.Client.SAdd(SubscriptionKey.VirtualTalentCountChange, notifyNew);
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.UpLoadResume}消息处理出错", e.Message, JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(100);
        }
    }

    /// <summary>
    /// 保存上传简历记录表子表
    /// </summary>
    /// <param name="_context"></param>
    /// <param name="upLoadRecordModel"></param>
    /// <param name="oldTalentVirtualModel"></param>
    /// <param name="IsSuccess"></param>
    /// <param name="IsWarehousing"></param>
    /// <param name="VirtualId"></param>
    /// <param name="Remarks"></param>
    private void SaveUploadRecordSub(StaffingContext _context, Talent_Upload_Record upLoadRecordModel,
        Talent_Virtual? oldTalentVirtualModel,
        bool IsSuccess,
        bool IsWarehousing,
        string VirtualId, string Remarks)
    {
        //添加上传简历记录表子表
        Talent_Upload_Recordsub addRecordsub = new Talent_Upload_Recordsub()
        {
            CreatedTime = DateTime.Now,
            IsRepeat = oldTalentVirtualModel == null ? false : true,
            IsSuccess = IsSuccess,
            IsWarehousing = IsWarehousing,
            RecordId = upLoadRecordModel.Id,
            VirtualId = VirtualId ?? string.Empty,
            Remarks = Remarks ?? string.Empty
        };
        _context.Talent_Upload_Recordsub.Add(addRecordsub);
        _context.SaveChanges();
    }

    /// <summary>
    /// 人才库同步es
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubUpdateTalentPlatform(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop(SubscriptionKey.UpdateTalentPlatform);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                _commonEsService.UpdateTalentPlatform(msg);

            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.UpdateTalentPlatform}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }
    /// <summary>
    /// 人才简历同步至云生人才库平台
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubUpdateYunshengPlatform(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop(SubscriptionKey.UpdateYunshengPlatform);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }
                _logger.Info($"{SubscriptionKey.UpdateYunshengPlatform}消息处理开始", "seekerid:" + msg);
                await _zpyunRepository.AsyncResumeInfo(msg);

            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.UpdateYunshengPlatform}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }
    /// <summary>
    /// 人才库同步es
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubUpdateTalentVirtual(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop(SubscriptionKey.UpdateTalentVirtual);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                _commonEsService.UpdateTalentVirtual(msg);

            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.UpdateTalentVirtual}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// Hr初筛导入简历并解析(暂时不用该方法，启用时需要增加用户来源)
    /// 1.简历转求职者  
    /// 2.投递职位  
    /// 3.投递成功，进人才库
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task RecruitUpLoadResume(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.LPop<string>(SubscriptionKey.RecruitUpLoadResume);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息
                string? upLoadResumeId = msg;
                using var _context = _contextFactory.CreateDbContext();
                var upLoadRecordModel = _context.Recruit_Upload_Record.Where(o => o.Id == upLoadResumeId).First();
                var notifyNew = new Sub_TalentCount_Change
                {
                    HrId = upLoadRecordModel.HrId
                };
                // 走小析解析
                (List<AnalysisResumeResponse> resume, _, _) = await _parseResume.AnalysisResume(upLoadRecordModel.FileUrl, upLoadRecordModel.FileName);
                // 获取地区字典
                var regionModel = _commonDicService.GetRegion();
                #region 行业、职业添加模型
                List<string> industryList = new List<string>();
                List<string> postList = new List<string>();
                #endregion

                //写入虚拟人才库
                foreach (var i in resume)
                {
                    try
                    {
                        string? userId = string.Empty;
                        Talent_Virtual? addTalent = null;
                        //简历信息
                        if (i.parsing_result != null)
                        {
                            //基本信息（求职期望）
                            if (i.parsing_result.basic_info != null)
                            {
                                // 简历信息转用户
                                userId = ResumeToSeeker(_context, regionModel, i);
                                if (userId == null)
                                    throw new Exception("创建求职者异常");
                                // 投递简历，重复投递会抛出异常，所以不会有重复入人才库的问题，只有成功投递才能进人才库
                                _commonUserService.DeliverResume(new CommonDeliverResume
                                {
                                    TeamPostId = upLoadRecordModel.TeamPostId,
                                    UserId = userId
                                });
                                addTalent = new Talent_Virtual()
                                {
                                    HeadPortrait = i.avatar_url ?? string.Empty,
                                    Name = i.parsing_result.basic_info.name ?? string.Empty,
                                    Mobile = i.parsing_result.contact_info?.phone_number ?? string.Empty,
                                    Sex = i.parsing_result.basic_info.gender == "男" ? Sex.男 : Sex.女,
                                    Mailbox = i.parsing_result.contact_info?.email ?? string.Empty,
                                    WeChat = i.parsing_result.contact_info?.wechat ?? string.Empty,
                                    QQ = i.parsing_result.contact_info?.QQ ?? string.Empty,
                                    WorkTime = DateTime.Now.AddYears(0 - i.parsing_result.basic_info.num_work_experience),
                                    Location = i.parsing_result.basic_info.current_location ?? string.Empty,
                                    DetailedLocation = i.parsing_result.basic_info.detailed_location ?? string.Empty,
                                    LocationNorm = i.parsing_result.basic_info.current_location_norm ?? string.Empty,
                                    Channel = TalentVirtualChannel.ResumeUpload,
                                    CreatedTime = DateTime.Now,
                                    HrId = upLoadRecordModel.HrId,
                                    OriginalUrl = upLoadRecordModel.FileUrl,
                                    Remark = "",
                                    Status = TalentVirtualStatus.UnRegistered,
                                    SeekerId = "",
                                };
                                _context.Talent_Virtual.Add(addTalent);

                                if (!string.IsNullOrEmpty(i.parsing_result.basic_info.detailed_location))
                                {
                                    addTalent.RegionId = regionModel.Where(o => o.Name.Contains(i.parsing_result.basic_info.detailed_location)).FirstOrDefault()?.Id ?? string.Empty;
                                }
                                else
                                {
                                    addTalent.RegionId = string.Empty;
                                }

                                if (!string.IsNullOrEmpty(i.parsing_result.basic_info.degree))
                                {
                                    switch (i.parsing_result.basic_info.degree)
                                    {
                                        case "硕士": addTalent.Education = TalentVirtualEducation.Master; break;
                                        case "博士": addTalent.Education = TalentVirtualEducation.Doctor; break;
                                        case "本科": addTalent.Education = TalentVirtualEducation.Undergraduate; break;
                                        case "大专": addTalent.Education = TalentVirtualEducation.Professional; break;
                                        case "高中": addTalent.Education = TalentVirtualEducation.Senior; break;
                                        case "中专": addTalent.Education = TalentVirtualEducation.Specialized; break;
                                        case "初中": addTalent.Education = TalentVirtualEducation.Junior; break;
                                        default: addTalent.Education = TalentVirtualEducation.Junior; break;
                                    }
                                }

                                if (i.parsing_result.basic_info.age.HasValue)
                                {
                                    var bd = Tools.GetBirthdateByAge(i.parsing_result.basic_info.age);
                                    if (bd.HasValue)
                                        addTalent.Birthday = bd.Value.ToDateTime(TimeOnly.MinValue);
                                }

                                if (!string.IsNullOrEmpty(i.parsing_result.basic_info.date_of_birth))
                                {
                                    if (i.parsing_result.basic_info.date_of_birth.Length == 4)
                                    {
                                        if (DateTime.TryParse(i.parsing_result.basic_info.date_of_birth + "-01-01", out DateTime _Birthday))
                                        {
                                            addTalent.Birthday = _Birthday;
                                        }
                                    }
                                    else
                                    {
                                        if (DateTime.TryParse(i.parsing_result.basic_info.date_of_birth, out DateTime _Birthday))
                                        {
                                            addTalent.Birthday = _Birthday;
                                        }
                                    }
                                }

                                if (i.parsing_result.contact_info != null)
                                {
                                    if (!string.IsNullOrEmpty(i.parsing_result.contact_info.phone_number))
                                    {
                                        var userModel = _context.User.Where(o => o.Mobile == i.parsing_result.contact_info.phone_number).FirstOrDefault();
                                        if (userModel != null)
                                        {
                                            addTalent.SeekerId = userModel.UserId;
                                            addTalent.Status = TalentVirtualStatus.Registered;

                                            if (!string.IsNullOrEmpty(userModel.IdentityCard))
                                            {
                                                addTalent.Status = TalentVirtualStatus.RealName;
                                            }
                                        }
                                    }
                                }

                                //补充信息
                                if (i.parsing_result.others != null)
                                {
                                    addTalent.SelfEvaluation = i.parsing_result.others.self_evaluation ?? string.Empty;

                                    TalentVirtaualResumeSkillSub SkillAnalysis = new TalentVirtaualResumeSkillSub()
                                    {
                                        Business = i.parsing_result.others.business_skills,
                                        IT = i.parsing_result.others.it_skills,
                                        Professional = i.parsing_result.others.skills
                                    };
                                    addTalent.SkillAnalysis = SkillAnalysis;
                                }
                            }

                            //求职期望
                            if (addTalent != null && i.parsing_result.basic_info != null)
                            {
                                Talent_Virtual_Hope addHopeModel = new Talent_Virtual_Hope();
                                addHopeModel.HopeCity = i.parsing_result.basic_info.expect_location ?? string.Empty;
                                addHopeModel.IndustryName = i.parsing_result.basic_info.desired_industry ?? string.Empty;
                                addHopeModel.PostName = i.parsing_result.basic_info.desired_position ?? string.Empty;
                                addHopeModel.VirtualId = addTalent.Id;
                                if (!string.IsNullOrEmpty(i.parsing_result.basic_info.expect_location))
                                {
                                    addHopeModel.RegionId = regionModel.Where(o => o.Name.Contains(i.parsing_result.basic_info.expect_location)).FirstOrDefault()?.Id ?? string.Empty;
                                }
                                else
                                {
                                    addHopeModel.RegionId = string.Empty;
                                }

                                if (!string.IsNullOrEmpty(i.parsing_result.basic_info.desired_salary))
                                {
                                    string[] desired_salary = i.parsing_result.basic_info.desired_salary.Split("-");
                                    if (desired_salary.Length == 2)
                                    {
                                        string _MinSalary = desired_salary[0].Trim();
                                        string _MaxSalary = desired_salary[1].Trim();
                                        try
                                        {
                                            addHopeModel.MinSalary = Convert.ToDecimal(System.Text.RegularExpressions.Regex.Replace(_MinSalary, @"[^\d]*", ""));
                                            addHopeModel.MaxSalary = Convert.ToDecimal(System.Text.RegularExpressions.Regex.Replace(_MaxSalary, @"[^\d]*", ""));
                                            if (_MinSalary.Contains('K') || _MinSalary.Contains('k'))
                                            {
                                                addHopeModel.MinSalary *= 1000;
                                            }
                                            if (_MaxSalary.Contains('K') || _MaxSalary.Contains('k'))
                                            {
                                                addHopeModel.MaxSalary *= 1000;
                                            }
                                        }
                                        catch
                                        {
                                            addHopeModel.MinSalary = 0;
                                            addHopeModel.MaxSalary = 0;
                                        }
                                    }
                                }
                                _context.Add(addHopeModel);
                            }

                            //项目经历
                            if (addTalent != null && i.parsing_result.project_experience != null && i.parsing_result.project_experience.Count > 0)
                            {
                                List<Talent_Virtual_Project> addProjectList = new List<Talent_Virtual_Project>();
                                List<User_Campus> addUserCampus = new List<User_Campus>();

                                i.parsing_result.project_experience = i.parsing_result.project_experience.OrderBy(o => o.start_time_year).OrderBy(o => o.start_time_month).ToList();

                                foreach (var m in i.parsing_result.project_experience)
                                {
                                    addProjectList.Add(new Talent_Virtual_Project
                                    {
                                        CreatedTime = DateTime.Now,
                                        PostName = m.job_title ?? string.Empty,
                                        ProjectName = m.project_name ?? string.Empty,
                                        ProjectRemarks = m.description ?? string.Empty,
                                        StartTime = (string.IsNullOrEmpty(m.start_time_year) || string.IsNullOrEmpty(m.start_time_month)) ? DateTime.Now : Convert.ToDateTime(m.start_time_year + "-" + m.start_time_month + "-01"),
                                        EndTime = (string.IsNullOrEmpty(m.end_time_year) || string.IsNullOrEmpty(m.end_time_month)) ? null : Convert.ToDateTime(m.end_time_year + "-" + m.end_time_month + "-01"),
                                        VirtualId = addTalent.Id
                                    });

                                    addUserCampus.Add(new User_Campus
                                    {
                                        Name = m.project_name ?? string.Empty,
                                        Award = m.job_title ?? string.Empty,
                                        BeginDate = DateOnly.FromDateTime((string.IsNullOrEmpty(m.start_time_year) || string.IsNullOrEmpty(m.start_time_month)) ? DateTime.Now : Convert.ToDateTime(m.start_time_year + "-" + m.start_time_month + "-01")),
                                        EndDate = DateOnly.FromDateTime((string.IsNullOrEmpty(m.end_time_year) || string.IsNullOrEmpty(m.end_time_month)) ? DateTime.MinValue : Convert.ToDateTime(m.end_time_year + "-" + m.end_time_month + "-01")),
                                        Describe = m.description ?? string.Empty,
                                        UserId = userId
                                    });
                                }

                                _context.Talent_Virtual_Project.AddRange(addProjectList);
                                _context.User_Campus.AddRange(addUserCampus);
                            }

                            //工作经历
                            if (addTalent != null && i.parsing_result.work_experience != null && i.parsing_result.work_experience.Count > 0)
                            {
                                List<Talent_Virtual_Work> addWorkList = new List<Talent_Virtual_Work>();
                                List<User_Work> addWorks = new List<User_Work>();// 用户工作经历

                                i.parsing_result.work_experience = i.parsing_result.work_experience.OrderBy(o => o.start_time_year).OrderBy(o => o.start_time_month).ToList();

                                foreach (var m in i.parsing_result.work_experience)
                                {
                                    Talent_Virtual_Work addWorkModel = new Talent_Virtual_Work()
                                    {
                                        CompanyName = m.company_name ?? string.Empty,
                                        CompanyRemarks = m.description ?? string.Empty,
                                        CreatedTime = DateTime.Now,
                                        Department = m.department ?? string.Empty,
                                        IndustryName = m.industry ?? string.Empty,
                                        PostName = m.job_title ?? string.Empty,
                                        StartTime = (string.IsNullOrEmpty(m.start_time_year) || string.IsNullOrEmpty(m.start_time_month)) ? DateTime.Now : Convert.ToDateTime(m.start_time_year + "-" + m.start_time_month + "-01"),
                                        EndTime = (string.IsNullOrEmpty(m.end_time_year) || string.IsNullOrEmpty(m.end_time_month)) ? null : Convert.ToDateTime(m.end_time_year + "-" + m.end_time_month + "-01"),
                                        VirtualId = addTalent.Id
                                    };

                                    User_Work addWord = new User_Work
                                    {
                                        Company = addWorkModel.CompanyName,
                                        Post = addWorkModel.PostName,
                                        BeginDate = DateOnly.FromDateTime(addWorkModel.StartTime),
                                        EndDate = addWorkModel.EndTime is null ? DateOnly.FromDateTime(DateTime.MinValue) : DateOnly.FromDateTime(addWorkModel.EndTime.Value),
                                        Describe = addWorkModel.CompanyRemarks,
                                        UserId = userId
                                    };

                                    addWorks.Add(addWord);

                                    if (!string.IsNullOrEmpty(m.industry))
                                    {
                                        industryList.Add(m.industry);
                                    }
                                    if (!string.IsNullOrEmpty(m.job_title))
                                    {
                                        postList.Add(m.job_title);
                                    }

                                    if (!string.IsNullOrEmpty(m.salary))
                                    {
                                        string[] salary = m.salary.Split("-");
                                        if (salary.Length == 2)
                                        {
                                            string _MinSalary = salary[0].Trim();
                                            string _MaxSalary = salary[1].Trim();
                                            try
                                            {
                                                addWorkModel.MinSalary = Convert.ToDecimal(System.Text.RegularExpressions.Regex.Replace(_MinSalary, @"[^\d]*", ""));
                                                addWorkModel.MaxSalary = Convert.ToDecimal(System.Text.RegularExpressions.Regex.Replace(_MaxSalary, @"[^\d]*", ""));
                                                if (_MinSalary.Contains('K') || _MinSalary.Contains('k'))
                                                {
                                                    addWorkModel.MinSalary *= 1000;
                                                }
                                                if (_MaxSalary.Contains('K') || _MaxSalary.Contains('k'))
                                                {
                                                    addWorkModel.MaxSalary *= 1000;
                                                }
                                            }
                                            catch
                                            {
                                                addWorkModel.MinSalary = 0;
                                                addWorkModel.MaxSalary = 0;
                                            }
                                        }
                                    }

                                    addWorkList.Add(addWorkModel);
                                }

                                _context.Talent_Virtual_Work.AddRange(addWorkList);
                                _context.User_Work.AddRange(addWorks);

                                notifyNew.PostNew = addWorkList.Select(x => x.PostName).ToList();
                                notifyNew.IndustryNew = addWorkList.Select(x => x.IndustryName).ToList();
                            }

                            //教育经历
                            if (addTalent != null && i.parsing_result.education_experience != null && i.parsing_result.education_experience.Count > 0)
                            {
                                List<Talent_Virtual_Edu> addEduList = new List<Talent_Virtual_Edu>();
                                var userResume = _context.User_Resume.FirstOrDefault(f => f.UserId == userId);
                                var topEducation = TalentVirtualEducation.Junior;
                                i.parsing_result.education_experience = i.parsing_result.education_experience.OrderBy(o => o.start_time_year).OrderBy(o => o.start_time_month).ToList();
                                foreach (var m in i.parsing_result.education_experience)
                                {
                                    Talent_Virtual_Edu addEduModel = new Talent_Virtual_Edu()
                                    {
                                        CreatedTime = DateTime.Now,
                                        IsFullTime = m.study_model == "非统招" ? false : true,
                                        MajorName = m.major ?? string.Empty,
                                        SchoolName = m.school_name ?? string.Empty,
                                        SchoolRemarks = string.Empty,
                                        StartTime = (string.IsNullOrEmpty(m.start_time_year) || string.IsNullOrEmpty(m.start_time_month)) ? DateTime.Now : Convert.ToDateTime(m.start_time_year + "-" + m.start_time_month + "-01"),
                                        EndTime = (string.IsNullOrEmpty(m.end_time_year) || string.IsNullOrEmpty(m.end_time_month)) ? null : Convert.ToDateTime(m.end_time_year + "-" + m.end_time_month + "-01"),
                                        VirtualId = addTalent.Id
                                    };
                                    switch (m.degree)
                                    {
                                        case "硕士": addEduModel.Education = TalentVirtualEducation.Master; break;
                                        case "博士": addEduModel.Education = TalentVirtualEducation.Doctor; break;
                                        case "本科": addEduModel.Education = TalentVirtualEducation.Undergraduate; break;
                                        case "大专": addEduModel.Education = TalentVirtualEducation.Professional; break;
                                        case "高中": addEduModel.Education = TalentVirtualEducation.Senior; break;
                                        case "中专": addEduModel.Education = TalentVirtualEducation.Specialized; break;
                                        case "初中": addEduModel.Education = TalentVirtualEducation.Junior; break;
                                        default: addEduModel.Education = TalentVirtualEducation.Junior; break;
                                    }
                                    if (topEducation < addEduModel.Education)
                                        topEducation = addEduModel.Education;
                                    addEduList.Add(addEduModel);
                                }
                                var topSchoolInfo = addEduList.Where(w => w.Education == topEducation).FirstOrDefault();
                                userResume!.Education = topEducation switch
                                {
                                    TalentVirtualEducation.Master => EducationType.硕士,
                                    TalentVirtualEducation.Doctor => EducationType.博士,
                                    TalentVirtualEducation.Undergraduate => EducationType.本科,
                                    TalentVirtualEducation.Professional => EducationType.大专,
                                    TalentVirtualEducation.Senior => EducationType.高中,
                                    TalentVirtualEducation.Specialized => EducationType.高中,
                                    TalentVirtualEducation.Junior => EducationType.其他,
                                    _ => EducationType.其他
                                };
                                userResume.School = topSchoolInfo!.SchoolName;
                                userResume.Major = topSchoolInfo!.MajorName;
                                userResume.GraduationDate = topSchoolInfo!.EndTime is null ? null : DateOnly.FromDateTime(topSchoolInfo!.EndTime.Value);

                                _context.Talent_Virtual_Edu.AddRange(addEduList);
                            }
                        }

                        //画像信息
                        if (addTalent != null && i.predicted_result != null)
                        {
                            //标签
                            if (i.predicted_result.tags != null)
                            {
                                addTalent.IndustryLabel = i.predicted_result.tags.skills == null ? new List<string>() : i.predicted_result.tags.skills.Select(o => o.tag).ToList();
                                addTalent.PostLabel = i.predicted_result.tags.professional == null ? new List<string>() : i.predicted_result.tags.professional.Select(o => o.tag).ToList();
                                addTalent.OtherLabel = i.predicted_result.tags.others == null ? new List<string>() : i.predicted_result.tags.others.Select(o => o.tag).ToList();
                            }

                            //亮点
                            if (i.predicted_result.highlights != null)
                            {
                                TalentVirtaualResumeHighlights _Highlights = new TalentVirtaualResumeHighlights()
                                {
                                    education = i.predicted_result.highlights.education,
                                    occupation = i.predicted_result.highlights.occupation,
                                    others = i.predicted_result.highlights.others,
                                    project = i.predicted_result.highlights.project,
                                    tags = i.predicted_result.highlights.tags
                                };
                                addTalent.Highlights = _Highlights;
                            }

                            //风险
                            if (i.predicted_result.risks != null)
                            {
                                TalentVirtaualResumeRisks _Risks = new TalentVirtaualResumeRisks()
                                {
                                    education = i.predicted_result.risks.education,
                                    occupation = i.predicted_result.risks.occupation,
                                    tags = i.predicted_result.risks.tags
                                };
                                addTalent.Risks = _Risks;
                            }
                        }
                        upLoadRecordModel.RepeatType = RecruitUpLoadRepeatType.投递正常;
                        upLoadRecordModel.UpLoadStatus = RecruitUpLoadStatus.已完成;
                        //保存与简历计算
                        if (addTalent != null)
                        {
                            _context.SaveChanges();
                            //计算简历完整度
                            int totalCount = _commonVirtualService.CountVirtualResumeScore(addTalent.Id);
                        }
                    }
                    catch (BadRequestException ex)
                    {
                        upLoadRecordModel.RepeatType = RecruitUpLoadRepeatType.投递异常;
                        upLoadRecordModel.UpLoadStatus = RecruitUpLoadStatus.已完成;
                        upLoadRecordModel.Remark = ex.Message;
                    }
                    catch (Exception ex)
                    {
                        _logger.Error("Hr初筛解析上传简历错误", ex.Message, JsonSerializer.SerializeToString(i));
                        upLoadRecordModel.RepeatType = RecruitUpLoadRepeatType.未投递;
                        upLoadRecordModel.UpLoadStatus = RecruitUpLoadStatus.解析失败;
                        upLoadRecordModel.Remark = ex.Message;
                    }
                }

                if (postList.Count > 0)
                {
                    var postDbList = _context.Dic_Talent_Post.Where(o => postList.Contains(o.PostName)).Select(o => o.PostName).ToList();
                    List<string> addPostList = new List<string>();
                    addPostList = postList.Except(postDbList).Distinct().ToList();
                    if (addPostList.Count > 0)
                    {
                        List<Dic_Talent_Post> addList = new List<Dic_Talent_Post>();
                        foreach (var i in addPostList)
                        {
                            addList.Add(new Dic_Talent_Post()
                            {
                                CreatedTime = DateTime.Now,
                                PostName = i,
                                ParentId = "28"
                            });
                        }
                        _context.Dic_Talent_Post.AddRange(addList);
                    }
                }

                if (industryList.Count > 0)
                {
                    var industryDbList = _context.Dic_Talent_Industry.Where(o => industryList.Contains(o.IndustryName)).Select(o => o.IndustryName).ToList();
                    List<string> addIndustryList = new List<string>();
                    addIndustryList = industryList.Except(industryDbList).Distinct().ToList();
                    if (addIndustryList.Count > 0)
                    {
                        List<Dic_Talent_Industry> addList = new List<Dic_Talent_Industry>();
                        foreach (var i in addIndustryList)
                        {
                            addList.Add(new Dic_Talent_Industry()
                            {
                                CreatedTime = DateTime.Now,
                                IndustryName = i
                            });
                        }
                        _context.Dic_Talent_Industry.AddRange(addList);
                    }
                }
                _context.SaveChanges();
                //新增人才库通知楷哥消息
                MyRedis.Client.SAdd(SubscriptionKey.VirtualTalentCountChange, notifyNew);
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.RecruitUpLoadResume}消息处理出错", e.Message, JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(100);
        }
    }

    /// <summary>
    /// 简历直接转成求职者，执行save
    /// </summary>
    /// <param name="_context"></param>
    /// <param name="regionModel"></param>
    /// <param name="i"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private string? ResumeToSeeker(StaffingContext _context, List<RegionModel> regionModel, AnalysisResumeResponse i)
    {
        var thirdTalentInfo = new Config.CommonModel.ThirdTalentInfo.ThirdTalentInfo()
        {
            Avatar = i.avatar_url ?? string.Empty,
            Name = i.parsing_result!.basic_info!.name ?? string.Empty,
            Phone = i.parsing_result.contact_info?.phone_number ?? string.Empty,
            GenderName = i.parsing_result.basic_info.gender,
            Email = i.parsing_result.contact_info?.email ?? string.Empty,
            WeChatNo = i.parsing_result.contact_info?.wechat ?? string.Empty,
            Qq = i.parsing_result.contact_info?.QQ ?? string.Empty,
            RegionId = !string.IsNullOrEmpty(i.parsing_result.basic_info.detailed_location) ? regionModel.Where(o => o.Name.Contains(i.parsing_result.basic_info.detailed_location)).FirstOrDefault()?.Id : null,
            DataUserCity = i.parsing_result.basic_info.detailed_location,
            Education = !string.IsNullOrEmpty(i.parsing_result.basic_info.degree)
                                                        ? i.parsing_result.basic_info.degree switch
                                                        {
                                                            "硕士" => EducationType.硕士,
                                                            "博士" => EducationType.博士,
                                                            "本科" => EducationType.本科,
                                                            "大专" => EducationType.大专,
                                                            "高中" => EducationType.高中,
                                                            "中专" => EducationType.其他,
                                                            "初中" => EducationType.其他,
                                                            _ => throw new Exception()
                                                        }
                                                        : null,
            Birthday = GetBirthDay(i.parsing_result.basic_info.date_of_birth),
            Describe = i.parsing_result.others?.self_evaluation,
            Skill = new List<string>().Union(i.parsing_result.others?.business_skills ?? new List<string>())
                                                    .Union(i.parsing_result.others?.it_skills ?? new List<string>())
                                                    .Union(i.parsing_result.others?.skills ?? new List<string>()).ToList()
        };
        _step.ResumeToSeeker(thirdTalentInfo, new Infrastructure.CommonInterface.PostOrderExceptionMessage());
        return thirdTalentInfo.SeekerId;
    }

    private DateOnly? GetBirthDay(string date_of_birth)
    {
        DateOnly? birthday = null;
        if (string.IsNullOrEmpty(date_of_birth))
            return null;
        if (date_of_birth.Length == 4)
        {
            if (DateTime.TryParse(date_of_birth + "-01-01", out DateTime _Birthday))
                birthday = DateOnly.FromDateTime(_Birthday);
        }
        else
        {
            if (DateTime.TryParse(date_of_birth, out DateTime _Birthday))
                birthday = DateOnly.FromDateTime(_Birthday);
        }
        return birthday;
    }

    /// <summary>
    /// 人才融合人才操作记录
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task TalentResumeRecordCreate(CancellationToken cancellationToken)
    {
        //处理消息
        TalentResumeRecordRedis? msg = null;
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                msg = MyRedis.Client.RPop<TalentResumeRecordRedis>(SubscriptionKey.TalentResumeRecord);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息
                using var _context = _contextFactory.CreateDbContext();

                _context.Add(new Talent_Resume_Connect
                {
                    HrId = msg.HrId,
                    ResumeId = msg.ResumeId,
                    Type = msg.ConnectType,
                    TeamPostId = msg.TeamPostId,
                    CreatedTime = msg.CreatedTime ?? DateTime.Now
                });

                _context.SaveChanges();
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.TalentResumeRecord}消息处理出错", e.Message, JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(100);
        }
    }

    /// <summary>
    /// 人才融合简历隐藏
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task TalentResumeHide(CancellationToken cancellationToken)
    {
        //处理消息
        TalentResumeHideRecord? msg = null;
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                msg = MyRedis.Client.RPop<TalentResumeHideRecord>(SubscriptionKey.TalentResumeHide);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息
                using var _context = _contextFactory.CreateDbContext();

                var resume = _context.Talent_Resume.FirstOrDefault(f => f.Id == msg.ResumeId);
                if (resume == null)
                    return;

                int addDays = msg.HideType switch { HideType.呼叫 => 7, HideType.投递简历 => 30, _ => 7 };
                resume.HideInvalidTime = msg.StartTime.AddDays(addDays);

                _context.SaveChanges();
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.TalentResumeHide}消息处理出错", e.Message, JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(100);
        }
    }
}