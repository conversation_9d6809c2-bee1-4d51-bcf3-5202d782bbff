using Config.Enums;
using Config.CommonModel;
using Config.CommonModel.Business;

namespace Staffing.Model.CentreService.Report;

public class GetExcellentPost : QueryRequest
{
    /// <summary>
    /// 职位名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 发布者
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ExcellentPostStatus? ExcellentStatus { get; set; }
}

public class GetExcellentPostResponse : QueryResponse
{
    public List<GetExcellentPostInfo> Rows { get; set; } = default!;
}

public class GetExcellentPostInfo
{
    /// <summary>
    /// 72小时入职
    /// </summary>
    public bool EntryIn72hour { get; set; }

    /// <summary>
    /// 24小时面试
    /// </summary>
    public bool InterviewIn24Hour { get; set; }

    /// <summary>
    /// Hr信息
    /// </summary>
    public HrModel? Hr { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 职位类别
    /// </summary>
    public int Category { get; set; }

    /// <summary>
    /// 工作性质
    /// </summary>
    public PostWorkNature WorkNature { get; set; } = PostWorkNature.全职;

    /// <summary>
    /// 薪资类型
    /// </summary>
    public PostSalaryType SalaryType { get; set; } = PostSalaryType.面议;

    /// <summary>
    /// 招聘人数
    /// </summary>
    public int RecruitNumber { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public PostStatus Status { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string RegionId { get; set; } = default!;

    /// <summary>
    /// 教育
    /// </summary>
    public EducationType Education { get; set; } = EducationType.不限;

    /// <summary>
    /// 教育名称
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 最小年龄
    /// </summary>
    public int MinAge { get; set; }

    /// <summary>
    /// 最大年龄
    /// </summary>
    public int MaxAge { get; set; }

    /// <summary>
    /// 薪资要求(最低)
    /// </summary>
    public int MinSalary { get; set; }

    /// <summary>
    /// 薪资要求(最高)
    /// </summary>
    public int MaxSalary { get; set; }

    /// <summary>
    /// 几薪
    /// </summary>
    public int Salary { get; set; } = 12;

    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 职位Id
    /// </summary>
    public string? PostId { get; set; }

    /// <summary>
    /// 职位编号
    /// </summary>
    public string? PostNo { get; set; }

    public int? PostAutoId { get; set; }

    /// <summary>
    /// 职位类别名称
    /// </summary>
    public string? CategoryName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ExcellentPostStatus ExcellentStatus { get; set; }

    /// <summary>
    /// 状态名称
    /// </summary>
    public string? ExcellentStatusName { get; set; }

    /// <summary>
    /// 工作性质名称
    /// </summary>
    public string? WorkNatureName { get; set; }

    /// <summary>
    /// 薪资类型名称
    /// </summary>
    public string? SalaryTypeName { get; set; }

    /// <summary>
    /// 薪资文本显示
    /// </summary>
    public string? SalaryName { get; set; }

    /// <summary>
    /// 地区
    /// </summary>
    public CityModel? Region { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    /// 刷新时间
    /// </summary>
    public DateTime? RefreshTime { get; set; }

    /// <summary>
    /// 代招企业
    /// </summary>
    /// <value></value>
    public GetAgentEntDetail? AgentEnt { get; set; }
}

public class SetExcellentPost
{
    /// <summary>
    /// 状态
    /// </summary>
    public ExcellentPostStatus? Status { get; set; }

    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 刷新
    /// </summary>
    public bool Refresh { get; set; }
}

/// <summary>
/// 获取优选职位 超时未处理投递的记录
/// </summary>
public class GetOvertimeExcellentPost : QueryRequest
{
    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 发布者
    /// </summary>
    public string? HRName { get; set; }

    /// <summary>
    /// 求职者手机号
    /// </summary>
    public string? SeekerMobile { get; set; }

    /// <summary>
    /// 面试超时时间(单位:小时)
    /// </summary>
    public double? InterviewOverTimeHour { get; set; }

    /// <summary>
    /// 入职超时时间(单位:小时)
    /// </summary>
    public double? EntryOverTimeHour { get; set; }
}

/// <summary>
/// 获取优选职位 超时未处理投递的记录
/// </summary>
public class GetOvertimeExcellentPostAll
{
    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 发布者
    /// </summary>
    public string? HRName { get; set; }

    /// <summary>
    /// 求职者手机号
    /// </summary>
    public string? SeekerMobile { get; set; }

    /// <summary>
    /// 面试超时时间(单位:小时)
    /// </summary>
    public double? InterviewOverTimeHour { get; set; }

    /// <summary>
    /// 入职超时时间(单位:小时)
    /// </summary>
    public double? EntryOverTimeHour { get; set; }
}


public class GetOvertimeExcellentPostRecord
{
    /// <summary>
    /// 招聘id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 求职者姓名
    /// </summary>
    public string? SeekerName { get; set; }

    /// <summary>
    /// 求职者手机号
    /// </summary>
    public string? SeekerMobile { get; set; }

    /// <summary>
    /// 发布者
    /// </summary>
    public string? HRName { get; set; }

    /// <summary>
    /// 公司名
    /// </summary>
    public string? EnterpriseName { get; set; }

    /// <summary>
    /// 报名时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 当前投递处理状态
    /// </summary>
    public string? RecruitStatusText { get; set; }
    //面试超时(单位：小时)
    public string? InterviewOverHour { get; set; }
    //面试超时(单位：小时)
    public string? EntryInOverHour { get; set; }
}

public class GetOvertimeExcellentPostResponse : QueryResponse
{
    /// <summary>
    /// 列表
    /// </summary>
    public List<GetOvertimeExcellentPostRecord>? Rows { get; set; }
}