﻿using Config.Enums;
namespace Config.CommonModel;

public class CityModel
{
    /// <summary>
    /// 行政Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 乡镇
    /// </summary>
    public string? TownName { get; set; }

    /// <summary>
    /// 区
    /// </summary>
    public string? CountyName { get; set; }

    /// <summary>
    /// 市
    /// </summary>
    public string? CityName { get; set; }

    public string? CityId { get; set; }

    /// <summary>
    /// 省
    /// </summary>
    public string? ProvinceName { get; set; }
}

public class WelfareModel
{
    public int Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Logo
    /// </summary>
    public string? Logo { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }
}

public class SchoolModel
{
    public string Id { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 类别,0=专科，1=本科，2=成人
    /// </summary>
    public SchoolType Type { get; set; }

    /// <summary>
    /// logo
    /// </summary>
    public string? Logo { get; set; }
}

public class TokenInfo
{
    /// <summary>
    /// 有效期（秒）
    /// </summary>
    public int TokenExpiresTime { get; set; }

    /// <summary>
    /// 访问token
    /// </summary>
    public string? AccessToken { get; set; }

    /// <summary>
    /// 刷新token
    /// </summary>
    public string? RefreshToken { get; set; }

    public string? UserId { get; set; }
}

/// <summary>
/// 身份证信息
/// </summary>
public class IdCardInfo
{
    /// <summary>
    /// 性别
    /// </summary>
    public Sex Sex { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; }
}

public class PostDistribute
{
    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Num { get; set; }
}

/// <summary>
/// 行业储备
/// </summary>
public class IndustryDistribute
{
    /// <summary>
    /// 行业名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public decimal Num { get; set; }
}

/// <summary>
/// name,value 枚举列表
/// </summary>
public class EnumInfo
{
    public string name { get; set; } = default!;
    public string value { get; set; } = default!;
}

/// <summary>
/// 简历 - 快手独立信息
/// </summary>
public class KuaishouResumeInfo
{
    /// <summary>
    /// 渠道来源
    /// </summary>
    public string? ChannelName { get; set; }

    /// <summary>
    /// 基本情况
    /// </summary>
    public string? BasicSituation { get; set; }

    /// <summary>
    /// 拥有技能
    /// </summary>
    public string? Skill { get; set; }

    /// <summary>
    /// 拥有证书
    /// </summary>
    public string? Certificate { get; set; }

    /// <summary>
    /// 工作方式
    /// </summary>
    public string? WorkWay { get; set; }

    /// <summary>
    /// 驾驶经历
    /// </summary>
    public string? DriveExperience { get; set; }

    /// <summary>
    /// 用户地区
    /// </summary>
    public string? DataUserCity { get; set; }
}

public class TycUpdateAgentEntResponse
{
    /// <summary>
    /// 天眼查企业Id
    /// </summary>
    public string? TycEntId { get; set; }

    /// <summary>
    /// 代招企业Id
    /// </summary>
    public string? AgentEntId { get; set; }

    /// <summary>
    /// 名字
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 企业简称
    /// </summary>
    public string? Abbr { get; set; }

    /// <summary>
    /// 公司行业Id
    /// </summary>
    public string? Industry { get; set; }

    /// <summary>
    /// 天眼查行业
    /// </summary>
    public string? TycIndustry { get; set; }

    /// <summary>
    /// 企业性质
    /// </summary>
    public EnterpriseNature? Nature { get; set; }

    /// <summary>
    /// 企业规模
    /// </summary>
    public EnterpriseScale? Scale { get; set; }

    /// <summary>
    /// 融资阶段
    /// </summary>
    public EnterpriseCapital? Capital { get; set; }

    /// <summary>
    /// 展示项
    /// </summary>
    public EntDisplayType Display { get; set; } = EntDisplayType.企业名称;

    /// <summary>
    /// 展示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 统一社会信用代码
    /// </summary>
    public string? CreditCode { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 注册资本
    /// </summary>
    public string? RegCapital { get; set; }

    /// <summary>
    /// 法人
    /// </summary>
    public string? LegalPersonName { get; set; }

    /// <summary>
    /// 成立时间
    /// </summary>
    public string? EstiblishTime { get; set; }

    /// <summary>
    /// 企业状态
    /// </summary>
    public string? RegStatus { get; set; }

    /// <summary>
    /// 经营开始时间
    /// </summary>
    public string? FromTime { get; set; }

    /// <summary>
    /// 经营截止时间
    /// </summary>
    public string? ToTime { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public List<string>? Tags { get; set; }
}