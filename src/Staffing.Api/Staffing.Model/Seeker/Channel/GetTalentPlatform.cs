using Config.CommonModel;
using Config.Enums;

namespace Staffing.Model.Seeker.Channel;

/// <summary>
/// 平台人才库查询
/// </summary>
public class GetTalentPlatform : QueryRequest
{
    /// <summary>
    /// 渠道Id
    /// </summary>
    public string? ChannelId { get; set; }

    /// <summary>
    /// 搜索（姓名、最近浏览岗位、学校、公司、电话）
    /// </summary>
    public string Search { get; set; } = string.Empty;
}

public class GetTalentPlatformResponse : QueryResponse
{

    public List<GetTalentPlatformInfo> Rows { get; set; } = default!;
}

/// <summary>
/// 平台人才库
/// </summary>
public class GetTalentPlatformInfo
{
    /// <summary>
    /// 平台人才库id
    /// </summary>
    public string? Id { get; set; } = default!;

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 用户头像
    /// </summary>
    public string? HeadPortrait { get; set; }

    /// <summary>
    /// 是否实名
    /// </summary>
    public bool? IsRealName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 性别解释
    /// </summary>
    public string? SexName { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public OccupationType? Occupation { get; set; }

    /// <summary>
    /// 职业解释
    /// </summary>
    public string? OccupationName { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 所在地
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    public string? WorkExp { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public string? EduExp { get; set; }

    /// <summary>
    /// 寻访意向
    /// </summary>
    public string? HopePost { get; set; }

    /// <summary>
    /// 用户来源
    /// </summary>
    public HrProjectSource? Source { get; set; }

    /// <summary>
    /// 用户来源解释
    /// </summary>
    public string? SourceName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatTime { get; set; }

    /// <summary>
    /// 最后登录时间
    /// </summary>
    public DateTime? LastTime { get; set; }

    /// <summary>
    /// 等级
    /// </summary>
    public TalentPlatformLevel? Level { get; set; }

    /// <summary>
    /// 等级解释
    /// </summary>
    public string? LevelName { get; set; }

    /// <summary>
    /// 活跃度
    /// </summary>
    public OnlineStatus Active { get; set; }

    /// <summary>
    /// 活跃度解释
    /// </summary>
    public string ActiveName { get; set; } = default!;

    /// <summary>
    /// 简历二维码
    /// </summary>
    public string? HrAppletQrCode { get; set; }

    /// <summary>
    /// 用户手机号
    /// </summary>
    public string? Mobile { get; set; }
}