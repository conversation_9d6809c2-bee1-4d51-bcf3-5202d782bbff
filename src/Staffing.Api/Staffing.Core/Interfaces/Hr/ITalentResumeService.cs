﻿using Config.CommonModel;
using Staffing.Model.Hr.TalentResume;

namespace Staffing.Core.Interfaces.Hr;

public interface ITalentResumeService
{
    /// <summary>
    /// 获取电话号码
    /// </summary>
    /// <param name="resumeId"></param>
    /// <returns></returns>
    GetTelephoneNumber GetPhoneNumber(string resumeId);

    /// <summary>
    /// 职位选择列表
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    PostTeamInfoPageListResponse GetPostsByStr(PostTeamPageListRequest request);
    
    /// <summary>
    /// 获取处理记录
    /// </summary>
    /// <param name="resumeId"></param>
    /// <returns></returns>
    ProcessRecordsResponse GetProcessRecords(string resumeId);

    /// <summary>
    /// 人才推荐列表
    /// </summary>
    /// <returns></returns>
    TalentResumeListResponse GetRecommendTalentResumeList(TalentResumePageListRequest request);
    TalentResumeListResponse GetRecommendTalentResumeListTest();

    /// <summary>
    /// 人才简历详情
    /// </summary>
    /// <param name="resumeId"></param>
    /// <returns></returns>
    TalentResumeDetailResponse GetTalentDetailByMainId(string resumeId);
    
    /// <summary>
    /// 短信邀约
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    EmptyResponse SendMessage(SendMessageRequest request);

    /// <summary>
    /// 获取最近筛选字符串
    /// </summary>
    /// <returns></returns>
    string GetSelectedRecords();

    /// <summary>
    /// 手动隐藏简历一年
    /// </summary>
    /// <param name="resumeId"></param>
    /// <returns></returns>
    EmptyResponse HideResume(string resumeId);
}
