﻿using Config.Enums;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces.Hr;
using Config;
using Staffing.Model.Hr.Bounty;
using Infrastructure.CommonService;
using Staffing.Model.Hr.Dashboard;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Pipelines.Sockets.Unofficial.Arenas;
using Microsoft.EntityFrameworkCore;
using System.Data.SqlTypes;
using Staffing.Model.Hr.Project;
using ServiceStack;
using System.Text;
using Org.BouncyCastle.Ocsp;
using DocumentFormat.OpenXml.Wordprocessing;
using System.Drawing;
using DocumentFormat.OpenXml.ExtendedProperties;
using Infrastructure.CommonService.Ndn;
using Infrastructure.Aop;

namespace Staffing.Core.Services.Hr;

[Service(ServiceLifetime.Transient)]
public class BountyService : IBountyService
{
    private readonly StaffingContext _context;
    private readonly RequestContext _user;
    private readonly ConfigManager _config;
    private readonly CacheHelper _cacheHelper;
    private readonly ICommonService _commonService;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly INdnService _ndnService;
    private readonly LogManager _log;
    private readonly CommonUserService _commonUserService;
    private readonly CommonDicService _commonDicService;
    public BountyService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, ICommonService commonService, CommonUserService commonUserService,
        LogManager log, CommonDicService commonDicService, INdnService ndnService)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _commonService = commonService;
        _ndnService = ndnService;
        _log = log;
        _commonUserService = commonUserService;
        _commonDicService = commonDicService;
    }

    public GetTeamBountysResponse GetTeamBountys(GetTeamBountys model)
    {
        var result = new GetTeamBountysResponse();

        var teamProj = _context.Project_Team.Where(x => x.TeamProjectId == model.TeamProjectId && x.HrId == _user.Id)
         .Select(s => new
         {
             s.Type,
             s.ProjectId,
             s.TeamProjectId
         }).FirstOrDefault();

        if (teamProj == null)
            throw new NotFoundException("内容不存在");

        var predicate = PredicateBuilder.New<Post_Bounty>(x => x.Post.ProjectId == teamProj.ProjectId);

        if (teamProj.Type != HrProjectType.自己)
            predicate = predicate.And(x => x.TeamHrId == _user.Id);

        if (model.Status.HasValue)
            predicate = predicate.And(x => x.Status == model.Status);

        if (model.Source.HasValue)
            predicate = predicate.And(x => x.Post_Team.Project_Team.Source == model.Source);

        if (!string.IsNullOrWhiteSpace(model.SeekerMobile))
            predicate = predicate.And(x => x.User_Seeker.User.Mobile == model.SeekerMobile);

        if (!string.IsNullOrWhiteSpace(model.SeekerName))
            predicate = predicate.And(x => x.User_Seeker.NickName == model.SeekerName);

        if (!string.IsNullOrWhiteSpace(model.PostName))
            predicate = predicate.And(x => x.Post.Name == model.PostName);

        if (!string.IsNullOrWhiteSpace(model.TeamHrName))
            predicate = predicate.And(x => x.User_Hr.NickName == model.TeamHrName);

        if (!string.IsNullOrWhiteSpace(model.TeamHrMobile))
            predicate = predicate.And(x => x.User_Hr.User.Mobile == model.TeamHrMobile);

        if (!string.IsNullOrWhiteSpace(model.TeamHrEntName))
            predicate = predicate.And(x => x.User_Hr.Enterprise.Name == model.TeamHrName || x.User_Hr.Enterprise.Abbreviation == model.TeamHrName);


        var sql = _context.Post_Bounty.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(o => o.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new GetTeamBountyDetail
        {
            HrId = s.HrId,
            CreatedTime = s.CreatedTime,
            TeamHrId = s.TeamHrId,
            TeamHrName = s.Team_User_Hr.NickName,
            TeamHrMobile = s.Team_User_Hr.User.Mobile,
            TeamHrEntName = s.Team_User_Hr.Enterprise.Name,
            TeamPostId = s.TeamPostId,
            PostId = s.PostId,
            SeekerId = s.SeekerId,
            SeekerMobile = s.User_Seeker.User.Mobile,
            SeekerName = s.User_Seeker.NickName,
            Source = s.Post_Team.Project_Team.Source,
            DeliveryTime = s.Recruit.CreatedTime,
            DeliveryId = s.Recruit.DeliveryId,
            Money = s.Money,
            // SettlementMoney = s.SettlementMoney,
            PaymentDays = s.PaymentDays,
            PaymentNode = s.PaymentNode,
            PaymentType = s.PaymentType,
            PostName = s.Post.Name,
            ProjectId = s.Post.ProjectId,
            RecruitId = s.RecruitId,
            // SettlementTime = s.SettlementTime,
            Status = s.Status,
            StatusName = s.Status.GetDescription(),
            UpdatedTime = s.UpdatedTime,
            PostCategoryName = s.Post.Dic_Post.Name,
            InductionTime = s.Recruit.InductionTime,
            RecruitStatus = s.Recruit.Status
        }).ToList();

        result.Rows.ForEach(item =>
        {
            if (item.PaymentNode.HasValue)
                item.PaymentNodeName = item.PaymentNode.GetDescription();

            if (item.PaymentType.HasValue)
                item.PaymentTypeName = item.PaymentType.GetDescription();

            if (item.Source.HasValue)
                item.SourceName = item.Source.GetDescription();

            if (item.RecruitStatus == RecruitStatus.Induction || item.RecruitStatus == RecruitStatus.Contract)
                if (item.InductionTime.HasValue)
                    item.EstimatedSettlementTime = item.InductionTime.Value.AddDays(item.PaymentDays ?? 0);
        });

        return result;
    }

    public GetRecentRecruitsResponse GetRecentRecruits(GetRecentRecruits model)
    {
        var result = new GetRecentRecruitsResponse();

        var predicate = PredicateBuilder.New<Recruit>(true);

        // 职位名称、用人企业、项目名称
        if (!string.IsNullOrEmpty(model.Search))
        {
            predicate = predicate.And(o => o.PostName.Contains(model.Search)
                                           || o.Post_Delivery.Post_Team.Project_Team.Project.Agent_Ent.Name.Contains(
                                               model.Search)
                                           || o.Post_Delivery.Post_Team.Project_Team.Project.Agent_Ent.DisplayName
                                               .Contains(model.Search));
            // || o.Post_Delivery.Post_Team.Project_Team.Project.Name.Contains(model.Search));
        }

        // 发单顾问：顾问名称/所属企业
        if (!string.IsNullOrEmpty(model.HrSearch))
        {
            predicate = predicate.And(o => o.Post_Delivery.Post_Team.Project_Team.User_Hr.NickName.Contains(model.HrSearch)
            || o.Post_Delivery.Post_Team.Project_Team.User_Hr.Enterprise.Name.Contains(model.HrSearch));
        }

        // 候选人：候选人姓名/手机号
        if (!string.IsNullOrEmpty(model.SeekerSearch))
        {
            predicate = predicate.And(o => o.User_Seeker.NickName.Contains(model.SeekerSearch)
            || o.User_Seeker.User.Mobile == model.SeekerSearch);
        }

        if (!string.IsNullOrEmpty(model.ProjectNoOrName))
        {
            var idPre = Constants.ProjectIdPre;
            if (model.ProjectNoOrName.StartsWith(idPre) && int.TryParse(model.ProjectNoOrName.Substring(1), out var id))
                predicate = predicate.And(x => x.Post_Delivery.Post.Project.AutoId == id);
            else
                predicate = predicate.And(x => x.Post_Delivery.Post.Project.Agent_Ent.Name.Contains(model.ProjectNoOrName));
        }

        _commonService.GetRecruitList(model, result, predicate);

        return result;
    }

    public async Task<byte[]> ExportZhaoPinJiaoFuKpi()
    {
        var data = new ExportZhaoPinJiaoFuKpi { Rows = new List<ExportZhaoPinJiaoFuKpiDetail>() };

        var today = DateOnly.FromDateTime(DateTime.Today.AddDays(-1));

        data.Month = DateTime.Now.Month;
        data.Today = DateTime.Today.ToString("yyyy-MM-dd");
        data.HrNum = _context.User_Hr.Where(x => x.Status == UserStatus.Active).Count();
        data.HrActiveNum = _context.Post.Where(x => x.Status == PostStatus.发布中 && x.Project.Status == ProjectStatus.已上线).GroupBy(g => g.Project.HrId).Count();
        data.PostNum = _context.Post.Where(x => x.Status == PostStatus.发布中 && x.Project.Status == ProjectStatus.已上线).Count();
        data.DailyActiveHr = _context.Rpt_Daily_User.FirstOrDefault(x => x.EventDate == today)?.ActiveHr ?? 0;
        data.RegistrationNum = _context.Post_Delivery.Count();

        var uids = new List<string>{
            "158056310704067973","150798239827097221","150398478601018501","158030630327047557","158043856026621573","158175796635523077"
        };

        data.Rows.AddRange(new List<ExportZhaoPinJiaoFuKpiDetail>
        {
            new ExportZhaoPinJiaoFuKpiDetail
            {
                HrId = "158056310704067973",
                XingMing = "曹玫梅",
                ShouRuMuBiao = 18.9M
            },
            new ExportZhaoPinJiaoFuKpiDetail
            {
                HrId = "150798239827097221",
                XingMing = "李玉慧",
                ShouRuMuBiao = 20.7M
            },
            new ExportZhaoPinJiaoFuKpiDetail
            {
                HrId = "150398478601018501",
                XingMing = "申丽华",
                ShouRuMuBiao = 25.1M
            },
            new ExportZhaoPinJiaoFuKpiDetail
            {
                HrId = "158030630327047557",
                XingMing = "马雅丽",
                ShouRuMuBiao = 19.1M
            },
            new ExportZhaoPinJiaoFuKpiDetail
            {
                HrId = "158043856026621573",
                XingMing = "李金涛",
                ShouRuMuBiao = 35.5M
            },
            new ExportZhaoPinJiaoFuKpiDetail
            {
                HrId = "158175796635523077",
                XingMing = "刘斌",
                ShouRuMuBiao = 26.6M
            }
        });

        var projectNum = _context.Project.Count(x => x.Status == ProjectStatus.已上线 && x.SharePost);
        var xietongxmnums = _context.Project_Team.Where(x => uids.Contains(x.HrId) && x.Type == HrProjectType.协同 && x.Status == ProjectStatus.已上线 && x.Project.Status == ProjectStatus.已上线)
        .GroupBy(g => g.HrId).Select(s => new
        {
            s.Key,
            Ct = s.Count()
        }).ToList();

        var mianshimnums = _context.Recruit_Interview.Where(x => uids.Contains(x.Recruit.Post_Delivery.Post_Team.Project_Team.HrId)
        && x.Recruit.Post_Delivery.Post_Team.Project_Team.Type == HrProjectType.协同)
        .GroupBy(g => new { g.Recruit.Post_Delivery.Post_Team.Project_Team.HrId, g.RecruitId, g.SeekerId })
        .Select(s => new { s.Key })
        .GroupBy(g => g.Key.HrId)
        .Select(s => new
        {
            s.Key,
            Ct = s.Count()
        }).ToList();

        var rcks = _context.User_Num.Where(x => uids.Contains(x.UserId)).Select(s => new { s.UserId, s.Talent });

        foreach (var item in data.Rows)
        {
            var xietongxmnum = xietongxmnums.FirstOrDefault(x => x.Key == item.HrId);
            var mianshimnum = mianshimnums.FirstOrDefault(x => x.Key == item.HrId);
            var rck = rcks.FirstOrDefault(x => x.UserId == item.HrId);
            item.XiangMuZongShu = projectNum;
            item.XieTongXiangMu = xietongxmnum?.Ct ?? 0;
            item.MianShiCiShu = mianshimnum?.Ct ?? 0;
            item.RenCaiShu = rck?.Talent ?? 0;
        }

        //模板路径
        var tplPath = Path.Combine(Directory.GetCurrentDirectory(), "TemplateFiles", "ProjectBoard", "zhaopinjiaofukpi.xlsx");

        //创建Excel导出对象
        IExportFileByTemplate exporter = new ExcelExporter();

        //根据模板导出
        var fileByte = await exporter.ExportBytesByTemplate(data, tplPath);

        return fileByte;
    }

    public GetStaffDataResponse GetStaffData(GetStaffData model)
    {
        var result = new GetStaffDataResponse();

        // var predicate = PredicateBuilder.New<Rpt_Daily_User>(true);

        // var sql = _context.Rpt_Daily_User
        // .Where(predicate);

        if (!model.BeginDate.HasValue)
            model.BeginDate = Constants.DefaultTime;
        else
            model.BeginDate = model.BeginDate.Value.Date;

        if (!model.EndDate.HasValue)
            model.EndDate = Constants.DefaultFutureTime;
        else
            model.EndDate = model.EndDate.Value.Date.AddDays(1);

        //在线项目
        var onlineProject = _context.Project.Where(x => x.Status == ProjectStatus.已上线 && x.Posts.Any(a => a.Status == PostStatus.发布中) && x.CreatedTime >= model.BeginDate.Value && x.CreatedTime < model.EndDate.Value)
        .GroupBy(g => g.HrId).Select(s => new { s.Key, Ct = s.Count() }).ToList();

        //在线职位
        var onlinePost = _context.Post.Where(x => x.Project.Status == ProjectStatus.已上线 && x.Status == PostStatus.发布中 && x.CreatedTime >= model.BeginDate.Value && x.CreatedTime < model.EndDate.Value)
        .GroupBy(g => g.Project.HrId).Select(s => new { s.Key, Ct = s.Count() }).ToList();

        //协同岗位
        var teamPosts = _context.Post_Team.Where(x => x.Show && x.Project_Team.Type == HrProjectType.协同 && x.CreatedTime >= model.BeginDate.Value && x.CreatedTime < model.EndDate.Value)
        .GroupBy(g => g.Project_Team.HrId).Select(s => new { s.Key, Ct = s.Count() }).ToList();

        //报名次数
        var deliveryNum = _context.Post_Delivery.Where(x => x.CreatedTime >= model.BeginDate.Value && x.CreatedTime < model.EndDate.Value)
        .Select(s => new
        {
            TeamHrId = s.Post_Team.Project_Team.HrId,
            s.Post.Project.HrId
        }).ToList();

        //协同报名数量
        var teamDeliveryNum = _context.Post_Delivery.Where(x => x.Post_Team.Project_Team.Type == HrProjectType.协同 && x.CreatedTime >= model.BeginDate.Value && x.CreatedTime < model.EndDate.Value)
        .GroupBy(g => g.Post_Team.Project_Team.HrId).Select(s => new { s.Key, Ct = s.Count() }).ToList();

        //协同入职数量
        var teamEntryNum = _context.Recruit.Where(x => x.Post_Delivery.Post_Team.Project_Team.Type == HrProjectType.协同 && (x.Status == RecruitStatus.Induction || x.Status == RecruitStatus.Contract) && x.InductionTime >= model.BeginDate.Value && x.InductionTime < model.EndDate.Value)
        .GroupBy(g => g.Post_Delivery.Post_Team.Project_Team.HrId).Select(s => new { s.Key, Ct = s.Count() }).ToList();

        //人才库数量
        var talent = _context.User_Hr.Select(s => new
        {
            s.UserId,
            s.User_Num.Talent,
            s.User_Num.VirtualTalent,
            s.NickName
        }).ToList();

        var realTalent = _context.Talent_Platform.Where(x => x.CreatedTime >= model.BeginDate.Value && x.CreatedTime < model.EndDate.Value)
        .GroupBy(g => g.HrId)
        .Select(s => new { s.Key, Ct = s.Count() })
        .ToList();

        foreach (var item in talent)
        {
            result.Rows.Add(new GetStaffDataInfo
            {
                UserId = item.UserId,
                Talent = realTalent.FirstOrDefault(x => x.Key == item.UserId)?.Ct ?? 0,
                VirtualTalent = item.VirtualTalent,
                HrName = item.NickName,
                OnlineProject = onlineProject.Where(x => x.Key == item.UserId).FirstOrDefault()?.Ct ?? 0,
                GroupShareProject = 0,
                OnlinePost = onlinePost.Where(x => x.Key == item.UserId).FirstOrDefault()?.Ct ?? 0,
                GroupSharePost = 0,
                DeliveryNum = deliveryNum.Where(x => x.HrId == item.UserId || x.TeamHrId == item.UserId).Count(),
                TeamDeliveryNum = teamDeliveryNum.Where(x => x.Key == item.UserId).FirstOrDefault()?.Ct ?? 0,
                TeamPosts = teamPosts.Where(x => x.Key == item.UserId).FirstOrDefault()?.Ct ?? 0,
                TeamEntryNum = teamEntryNum.Where(x => x.Key == item.UserId).FirstOrDefault()?.Ct ?? 0,
            });
        }

        result.Rows = result.Rows.OrderByDescending(o => o.OnlineProject + o.OnlinePost + o.DeliveryNum + o.TeamDeliveryNum + o.TeamPosts + o.TeamEntryNum).ToList();

        return result;
    }

    public GetDeptKpiResponse GetDeptKpi(GetDeptKpi model)
    {
        var result = new GetDeptKpiResponse { Nkp = new GetNkpDeptKpiNkp() };

        var beginTime = (model.BeginDate ?? Constants.DefaultTime).Date;
        var endTime = (model.EndDate ?? Constants.DefaultFutureTime).AddDays(1).Date;

        result.Nkp.NewB = _context.User_Hr.Count(c => c.Status == UserStatus.Active
        && c.CreatedTime >= beginTime && c.CreatedTime < endTime);

        result.Nkp.UseB = _context.Project_Team.Where(x => x.CreatedTime < endTime
        && x.User_Hr.User_Extend.LoginTime >= beginTime)
        .Select(s => s.HrId).Distinct().Count();

        result.Nkp.UserC = _context.User_Seeker.Count(c => c.Status == UserStatus.Active
        && c.CreatedTime >= beginTime && c.CreatedTime < endTime);

        result.Nkp.UserCt = _context.User_Seeker.Count(c => c.Status == UserStatus.Active
        && c.CreatedTime < endTime);

        result.Nkp.ZhaoPinNum = _context.Post_Delivery.Where(x => x.CreatedTime < endTime)
        .Select(s => new { s.PostId, s.Post.DeliveryNumber }).Distinct().Sum(s => s.DeliveryNumber);

        result.Nkp.TouDiNum = _context.Post_Delivery.Count(x => x.CreatedTime < endTime);

        result.Nkp.RenCaiNum = _context.Project_Team.Where(x => x.User_Hr.CreatedTime < endTime
        && x.User_Hr.User_Extend.LoginTime >= beginTime).Select(s => new { s.HrId, s.User_Hr.User_Num.Talent })
        .Distinct().Sum(s => s.Talent);

        result.Nkp.RenCaiPer = result.Nkp.RenCaiNum / (result.Nkp.UseB <= 0 ? 1 : result.Nkp.UseB);

        result.Nkp.JFLv = $"{Math.Round(((decimal)result.Nkp.TouDiNum / (decimal)(result.Nkp.ZhaoPinNum <= 0 ? 1 : result.Nkp.ZhaoPinNum)), 3) * 100}%";

        var beginDate = DateOnly.FromDateTime(beginTime);
        var endDate = DateOnly.FromDateTime(endTime);
        var bHyLv = _context.Rpt_Daily_User.Where(x => x.EventDate >= beginDate && x.EventDate < endDate)
        .Select(s => s.AllHrHadProj == 0 ? 0 : (decimal)s.ActiveHr / s.AllHrHadProj).ToList();

        if (bHyLv.Count > 0)
            result.Nkp.BHyLv = $"{Math.Round((decimal)bHyLv.Sum(s => s) / bHyLv.Count, 3) * 100}%";

        return result;
    }

    public ExportTeamBountysResponse ExportTeamBountys(ExportTeamBountys model)
    {
        var result = new ExportTeamBountysResponse();

        var proj = _context.Project.Where(x => x.ProjectId == model.ProjectId)
        .Select(s => new
        {
            s.Type,
            s.ProjectId,
            s.Agent_Ent.Name
        }).FirstOrDefault();

        if (proj == null)
            throw new NotFoundException("内容不存在");

        result.ProjectName = proj.Name;

        var predicate = PredicateBuilder.New<Post_Bounty>(x => x.Post.ProjectId == model.ProjectId);

        var sql = _context.Post_Bounty.Where(predicate);

        result.Rows = sql
        .OrderByDescending(o => o.CreatedTime)
        .Select(s => new ExportTeamBountyDetail
        {
            HrId = s.HrId,
            CreatedTime = s.CreatedTime,
            TeamHrId = s.TeamHrId,
            TeamHrName = s.Team_User_Hr.NickName,
            TeamHrMobile = s.Team_User_Hr.User.Mobile,
            TeamHrEntName = s.Team_User_Hr.Enterprise.Name,
            TeamPostId = s.TeamPostId,
            PostId = s.PostId,
            SeekerId = s.SeekerId,
            SeekerMobile = s.User_Seeker.User.Mobile,
            SeekerName = s.User_Seeker.NickName,
            Source = s.Post_Team.Project_Team.Source,
            DeliveryTime = s.Recruit.CreatedTime.ToYYYY_MM_DD_HH(),
            DeliveryId = s.Recruit.DeliveryId,
            Money = s.Money,
            PaymentDays = s.PaymentDays,
            PaymentNode = s.PaymentNode,
            PaymentType = s.PaymentType,
            PostName = s.Post.Name,
            ProjectId = s.Post.ProjectId,
            RecruitId = s.RecruitId,
            // SettlementTime = s.SettlementTime,
            Status = s.Status,
            StatusName = s.Status.GetDescription(),
            UpdatedTime = s.UpdatedTime,
            PostCategoryName = s.Post.Dic_Post.Name,
            InductionTime = s.Recruit.InductionTime,
            RecruitStatus = s.Recruit.Status,
            RecruitStatusName = s.Recruit.Status.GetDescription()
        }).ToList();

        result.Rows.ForEach(item =>
        {
            if (item.PaymentNode.HasValue)
                item.PaymentNodeName = item.PaymentNode.GetDescription();

            if (item.PaymentType.HasValue)
                item.PaymentTypeName = item.PaymentType.GetDescription();

            if (item.Source.HasValue)
                item.SourceName = item.Source.GetDescription();

            if (item.RecruitStatus == RecruitStatus.Induction || item.RecruitStatus == RecruitStatus.Contract)
                if (item.InductionTime.HasValue)
                    item.EstimatedSettlementTime = item.InductionTime.Value.AddDays(item.PaymentDays ?? 0).ToYYYY_MM_DD();
        });

        return result;
    }

    public NewTeamBounryResponse GetOrderPageList(NewTeamBountyRequest request)
    {
        // 支持postId
        if (!string.IsNullOrWhiteSpace(request.PostId))
            request.ProjectId = _context.Post.Where(f => f.PostId == request.PostId)?.Select(s => s.ProjectId).FirstOrDefault();

        // 根据项目id判断主创还是协同
        int userType = 0;
        var projectInfo = _context.Project.FirstOrDefault(f => f.ProjectId == request.ProjectId);
        if (projectInfo == null)
            throw new BadRequestException("内容不存在");
        if (projectInfo.HrId != _user.Id)
            userType = 1;// 非主创
        var result = new NewTeamBounryResponse();
        var predicate = PredicateBuilder.New<Post_Bounty>(x => x.Post.ProjectId == request.ProjectId);
        result.UserType = userType;

        if (userType == 0)
            predicate = predicate.And(x => x.HrId == _user.Id);
        if (userType == 1)
            predicate = predicate.And(x => x.TeamHrId == _user.Id && x.HrId != x.TeamHrId);
        if (!string.IsNullOrWhiteSpace(request.SeekerName))
            predicate = predicate.And(x => x.User_Seeker.NickName.IndexOf(request.SeekerName) >= 0);
        if (!string.IsNullOrWhiteSpace(request.SeekerMobile))
            predicate = predicate.And(x => x.User_Seeker.User.Mobile.IndexOf(request.SeekerMobile) >= 0);
        if (request.Status.HasValue)
            predicate = predicate.And(x => x.Status == request.Status);
        if (!string.IsNullOrWhiteSpace(request.ShareName))
            predicate = predicate.And(x => (string.IsNullOrWhiteSpace(x.ChannelId) && x.Team_User_Hr.NickName.IndexOf(request.ShareName) >= 0)
            || (!string.IsNullOrWhiteSpace(x.ChannelId) && x.Qd_Hr_Channel.User_Hr.NickName.IndexOf(request.ShareName) >= 0));
        if (!string.IsNullOrWhiteSpace(request.DeliveryPostName))
            predicate = predicate.And(x => x.PostName.IndexOf(request.DeliveryPostName) >= 0);
        if (!string.IsNullOrWhiteSpace(request.BeginDate))
            predicate = predicate.And(x => x.CreatedTime >= Convert.ToDateTime(request.BeginDate));
        if (!string.IsNullOrWhiteSpace(request.EndDate))
            predicate = predicate.And(x => x.CreatedTime <= Convert.ToDateTime(request.EndDate).AddDays(1).AddSeconds(-1));
        // if (request.SettlementType.HasValue)
        //     predicate = predicate.And(x => x.SettlementStatus == request.SettlementType);
        if (request.TeamBountySource.HasValue)
            predicate = predicate.And(x => x.Source == request.TeamBountySource);

        if (!string.IsNullOrEmpty(request.PostId))
            predicate = predicate.And(x => x.PostId == request.PostId);

        var sql = _context.Post_Bounty.Where(predicate);

        result.Total = sql.Select(s => new { a = 1 }).Count();
        result.Rows = sql
        .OrderByDescending(o => o.CreatedTime)
        .Skip((request.PageIndex - 1) * request.PageSize)
        .Take(request.PageSize)
        .Select(s => new NewTeamBountyPageList
        {
            SeekerName = s.User_Seeker.NickName ?? string.Empty,
            SeekerMobile = s.User_Seeker.User.Mobile ?? string.Empty,
            TeamBountySource = s.Source,
            TeamBountySourceName = s.Source.GetDescription() ?? string.Empty,
            PostName = s.PostName ?? string.Empty,
            Money = s.Money,
            ChannelMoney = s.Money,
            DeliveryTime = s.CreatedTime,
            //PlatformSettlementMoney = s.PlatformBounty,
            PlatformSettlementMoney = s.Money * s.PlatformRate,
            Status = s.Status,
            StatusName = s.Status.GetDescription() ?? string.Empty,
            ShareName = !string.IsNullOrWhiteSpace(s.ChannelId) ? s.Qd_Hr_Channel.User_Seeker.NickName : s.Team_User_Hr.NickName,
            ShareRelation = !string.IsNullOrWhiteSpace(s.ChannelId) ? "渠道商" : (userType == 0 ? "协同顾问" : "自己"),
            PaymentNode = s.PaymentNode,
            // SettlementStatus = s.SettlementStatus,
            // SettlementStatusName = s.SettlementStatus.GetDescription() ?? string.Empty,
            EndCountTime = "未开始",
            CreatedTime = s.CreatedTime,
            RecruitId = s.RecruitId,
            IfStockOut = s.IfStockOut,
            PaymentDays = s.PaymentDays
        }).ToList();
        RowExtendsInfo(result);

        return result;
    }

    public NewTeamBounryResponse GetCenterOrderPageList(CenterOrderListRequest request)
    {
        var result = new NewTeamBounryResponse();
        var predicate = PredicateBuilder.New<Post_Bounty>(true);

        if (!string.IsNullOrWhiteSpace(request.SeekerName))
            predicate = predicate.And(x => x.User_Seeker.NickName.Contains(request.SeekerName));
        if (!string.IsNullOrWhiteSpace(request.SeekerMobile))
            predicate = predicate.And(x => x.User_Seeker.User.Mobile.Contains(request.SeekerMobile));
        if (request.Status.HasValue)
            predicate = predicate.And(x => x.Status == request.Status);
        if (!string.IsNullOrWhiteSpace(request.ShareName))
            predicate = predicate.And(x => (string.IsNullOrWhiteSpace(x.ChannelId) && x.Team_User_Hr.NickName.Contains(request.ShareName))
            || (!string.IsNullOrWhiteSpace(x.ChannelId) && x.Qd_Hr_Channel.User_Hr.NickName.Contains(request.ShareName)));
        if (!string.IsNullOrWhiteSpace(request.DeliveryPostName))
            predicate = predicate.And(x => x.PostName.Contains(request.DeliveryPostName));
        if (!string.IsNullOrWhiteSpace(request.BeginDate))
            predicate = predicate.And(x => x.CreatedTime >= Convert.ToDateTime(request.BeginDate));
        if (!string.IsNullOrWhiteSpace(request.EndDate))
            predicate = predicate.And(x => x.CreatedTime <= Convert.ToDateTime(request.EndDate).AddDays(1).AddSeconds(-1));
        // if (request.SettleStatus.HasValue)
        // {
        //     var status = request.SettleStatus switch
        //     {
        //         SettleStatus.结算中 => SettlementType.待结算,
        //         SettleStatus.已结算 => SettlementType.已结算,
        //         SettleStatus.结算失败 => SettlementType.待结算,
        //         _ => SettlementType.待结算
        //     };
        //     predicate = predicate.And(x => x.SettlementStatus == status);
        // }
        if (request.BountySource.HasValue)
        {
            // todo:这里的逻辑感觉有点问题
            if (request.BountySource == BountySource.渠道)
                predicate = predicate.And(x => !string.IsNullOrWhiteSpace(x.ChannelId));
            else
                predicate = predicate.And(x => string.IsNullOrWhiteSpace(x.ChannelId));
        }
        if (!string.IsNullOrWhiteSpace(request.ProjectSearch))
            predicate = predicate.And(x => x.Post.Project.Agent_Ent.Name.Contains(request.ProjectSearch));
        if (request.TeamBountySource.HasValue)
            predicate = predicate.And(x => x.Source == request.TeamBountySource);
        var sql = _context.Post_Bounty.Where(predicate);

        result.Total = sql.Select(s => new { a = 1 }).Count();
        result.Rows = sql
        .OrderByDescending(o => o.CreatedTime)
        .Skip((request.PageIndex - 1) * request.PageSize)
        .Take(request.PageSize)
        .Select(s => new NewTeamBountyPageList
        {
            SeekerName = s.User_Seeker.NickName ?? string.Empty,
            SeekerMobile = s.User_Seeker.User.Mobile ?? string.Empty,
            ProjectHame = s.Post.Project.Agent_Ent.Name ?? string.Empty,
            ProjectNo = $"{Constants.ProjectIdPre}{s.Post.Project.AutoId.ToString().PadLeft(6, '0')}",
            TeamBountySource = s.Source,
            TeamBountySourceName = s.Source.GetDescription() ?? string.Empty,
            PostName = s.PostName ?? string.Empty,
            Money = s.Money,
            ChannelMoney = 0,
            //PlatformSettlementMoney = s.PlatformSettlementMoney,
            Status = s.Status,
            StatusName = s.Status.GetDescription() ?? string.Empty,
            ShareName = !string.IsNullOrWhiteSpace(s.ChannelId) ? s.Qd_Hr_Channel.User_Hr.NickName : s.Team_User_Hr.NickName,
            ShareRelation = !string.IsNullOrWhiteSpace(s.ChannelId) ? "渠道" : "协同",
            PaymentNode = s.PaymentNode,
            // SettlementStatus = s.SettlementStatus,
            // SettlementStatusName = s.SettlementStatus.GetDescription() ?? string.Empty,
            EndCountTime = "未开始",
            CreatedTime = s.CreatedTime,
            RecruitId = s.RecruitId,
            IfStockOut = s.IfStockOut,
            PaymentDays = s.PaymentDays
        }).ToList();
        RowExtendsInfo(result);

        return result;
    }

    /// <summary>
    /// 交付订单 - 行信息扩展
    /// </summary>
    /// <param name="result"></param>
    private void RowExtendsInfo(NewTeamBounryResponse result)
    {
        var recruitIds = result.Rows.Select(s => s.RecruitId).ToList();
        var recruits = _context.Recruit.Where(w => recruitIds.Contains(w.RecruitId))
        .Select(s => new { s.RecruitId, s.InductionTime, s.CreatedTime }).ToList();
        result.Rows.ForEach(item =>
        {
            // 倒计时处理
            switch (item.PaymentNode)
            {
                case ProjectPaymentNode.入职过保:
                    item.UnitName = "/人";
                    if (item.Status == BountyStatus.交付中 && item.IfStockOut == 1)
                    {
                        var recruitInfo = recruits.FirstOrDefault(f => f.RecruitId == item.RecruitId);
                        if (recruitInfo == null)
                        {
                            item.EndCountTime = "异常"; // 异常
                            return;
                        }
                        if (recruitInfo.InductionTime == null)
                        {
                            item.EndCountTime = "异常";// 异常
                            return;
                        }
                        item.EndCountTime = recruitInfo.InductionTime!.Value.AddDays(item.PaymentDays!.Value).ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    else if (item.Status == BountyStatus.结束)
                        item.EndCountTime = "已结束";
                    break;

                case ProjectPaymentNode.简历交付:
                    item.UnitName = "/份";
                    if (item.Status == BountyStatus.结束)
                        item.EndCountTime = "已结束";
                    else if (item.Status == BountyStatus.交付中 && item.IfStockOut == 1)
                    {
                        var interviewerScreen = _context.Recruit_Interviewer_Screen.Where(w => w.RecruitId == item.RecruitId)
                        .OrderBy(o => o.CreatedTime).Select(x => new { x.CreatedTime }).FirstOrDefault();
                        item.EndCountTime = (interviewerScreen?.CreatedTime ?? item.CreatedTime).AddDays(7).ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    break;

                case ProjectPaymentNode.到面交付:
                    item.UnitName = "/次";
                    if (item.Status == BountyStatus.结束)
                        item.EndCountTime = "已结束";
                    else if (item.Status == BountyStatus.交付中)
                    {
                        var recruit = _context.Recruit_Interview.Where(w => w.RecruitId == item.RecruitId && w.Outcome == RecruitInterviewOutcome.Waiting).FirstOrDefault();
                        if (recruit == null)
                            item.EndCountTime = "未开始";
                        else
                            item.EndCountTime = recruit.InterviewTime.AddDays(7).ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    break;

                    // case ProjectPaymentNode.按天入职过保:
                    //     item.UnitName = "/人/天";
                    //     if (item.Status == BountyStatus.交付中 && item.IfStockOut == 1)
                    //     {
                    //         var recruitInfo = recruits.FirstOrDefault(f => f.RecruitId == item.RecruitId);
                    //         if (recruitInfo == null)
                    //         {
                    //             item.EndCountTime = "异常";
                    //             return;
                    //         }
                    //         if (recruitInfo.InductionTime == null)
                    //         {
                    //             item.EndCountTime = "异常";
                    //             return;
                    //         }
                    //         item.EndCountTime = recruitInfo.InductionTime!.Value.AddDays(item.PaymentDays!.Value).ToString("yyyy-MM-dd HH:mm:ss");
                    //     }
                    //     else if (item.Status == BountyStatus.交付失败 || item.Status == BountyStatus.结算中)
                    //         item.EndCountTime = "已结束";
                    //     break;
            }

            // 处理投递时间
            string recruitId = item.RecruitId.Split("-")[0];
            item.DeliveryTime = recruits.FirstOrDefault(f => f.RecruitId == recruitId)?.CreatedTime ?? item.DeliveryTime;
        });
    }

    public CompanyOrderSumData GetCenterCompanyOrderSumData()
    {
        List<JObject>? teams = null, pros = null;
        var userList = GetCompanyUsers();
        var orderSumData = GetOrderSumData(userList.Select(s => s.UserId).ToList(), ref teams, ref pros);
        var response = JsonConvert.DeserializeObject<CompanyOrderSumData>(JsonConvert.SerializeObject(orderSumData));

        response.HrNum = pros.Select(s => s["HrId"]).Distinct().Count();// 已经发布过项目的hr去重数量
        response.WaitToSettleHrNum = teams == null
            ? 0
            : teams.Where(w => (SettleStatus)Enum.Parse(typeof(SettleStatus), w["SettleStatus"] + "") != SettleStatus.已结算 && w["SettleId"] + "" != "").Select(s => s["HrId"]).Distinct().Count();
        return response;
    }

    public PersonalOrderSumData GetCenterPersonalOrderSumData()
    {
        List<JObject>? teams = null, pros = null;
        var orderSumData = GetOrderSumData(new List<string> { _user.Id }, ref teams, ref pros);
        var response = JsonConvert.DeserializeObject<PersonalOrderSumData>(JsonConvert.SerializeObject(orderSumData));
        var firstDate = DateTime.Now.AddDays(1 - DateTime.Now.Day).Date;

        response.DeliveryNum = _context.Post_Delivery.IgnoreQueryFilters()
            .Where(w => w.CreatedTime >= firstDate
            && (w.Post_Team.Project_Team.HrId == _user.Id && w.Post_Team.Project_Team.Type == HrProjectType.协同 || w.Qd_Hr_Channel.ChannelUserId == _user.Id)).Count();// 不去重，投递次数，没有把作为渠道的加进去

        if (response.DeliveryNum == 0)
        {
            response.DeliveryRate = 0.00m;
        }
        else
        {
            response.DeliveryRate = teams == null
            ? 0.00m
            : teams.Where(w => (BountyStatus)Enum.Parse(typeof(BountyStatus), w["Status"] + "") == BountyStatus.交付中
                && Convert.ToDateTime(w["CreatedTime"]) >= firstDate).Select(s => s["Id"]).Distinct().Count() / response.DeliveryNum;
        }


        return response;
    }

    /// <summary>
    /// 获取公共数据
    /// </summary>
    /// <param name="userList"></param>
    /// <param name="teams"></param>
    /// <param name="pros"></param>
    /// <returns></returns>
    public OrderSumData GetOrderSumData(List<string> userList, ref List<JObject>? teams, ref List<JObject>? pros)
    {
        OrderSumData response = new();

        // **项目信息
        var projectInfos = _context.Project.IgnoreQueryFilters().Where(w => userList.Contains(w.HrId))
            .Select(s => new { s.ProjectId, s.HrId, s.Status, PublishPost = s.Project_Team.Any(a => a.Post_Team.Any(p => p.Show == true)) })
            .ToList();
        if (projectInfos != null && projectInfos.Count > 0)
            pros = JsonConvert.DeserializeObject<List<JObject>>(JsonConvert.SerializeObject(projectInfos));

        // **订单信息
        // 作为协同
        var teamInfos = _context.Post_Bounty.Where(w => userList.Contains(w.TeamHrId))
            .Select(s => new
            {
                s.Id,
                s.Post.ProjectId,
                s.Status,
                s.CreatedTime,
                s.GuaranteeStatus,
                // s.SettlementStatus,
                //Money = s.ClueBounty,
                Money = s.Money * s.ClueRate,
                HrId = s.TeamHrId,
                // SettleStatus = s.project_Settlements.Where(a => a.Type == SettleType.协同).Select(s => s.Status).FirstOrDefault(),
                // SettleId = s.project_Settlements.Where(a => a.Type == SettleType.协同).Select(s => s.Id).FirstOrDefault()
            }).ToList();

        // 作为渠道
        var channelInfos = _context.Post_Bounty.Where(w => userList.Contains(w.Qd_Hr_Channel.HrId))
            .Select(s => new
            {
                s.Id,
                s.Post.ProjectId,
                s.Status,
                s.CreatedTime,
                s.GuaranteeStatus,
                // s.SettlementStatus,
                Money = 0M,
                HrId = s.Qd_Hr_Channel.HrId,
                // SettleStatus = s.project_Settlements.Where(a => a.Type == SettleType.渠道).Select(s => s.Status).FirstOrDefault(),
                // SettleId = s.project_Settlements.Where(a => a.Type == SettleType.渠道).Select(s => s.Id).FirstOrDefault()
            }).ToList();

        if (teamInfos != null && channelInfos != null && channelInfos.Count > 0)
        {
            teamInfos = teamInfos.Concat(channelInfos!).ToList();
            if (teamInfos != null)
                teams = JsonConvert.DeserializeObject<List<JObject>>(JsonConvert.SerializeObject(teamInfos));
        }

        // response.WaitToSettleProjectNum = teamInfos == null ? 0 : teamInfos.Where(w => w.SettlementStatus != SettlementType.已结算 && w.Status != BountyStatus.交付失败).Select(s => s.ProjectId).Distinct().Count();
        // response.WaitToSettleMoney = teamInfos == null ? 0m : teamInfos.Where(w => w.SettleStatus != SettleStatus.已结算 && !string.IsNullOrWhiteSpace(w.SettleId)).Sum(s => s.Money);
        // response.SettledMoney = teamInfos == null ? 0m : teamInfos.Where(w => w.SettleStatus == SettleStatus.已结算 && !string.IsNullOrWhiteSpace(w.SettleId)).Sum(s => s.Money);
        response.ExecOrderNum = teamInfos == null ? 0 : teamInfos.Where(w => w.Status == BountyStatus.交付中).Select(s => s.Id).Distinct().Count();
        response.SucceedOrderNum = teamInfos == null ? 0 : teamInfos.Where(w => w.Status == BountyStatus.交付中).Select(s => s.Id).Distinct().Count();
        response.FailedOrderNum = teamInfos == null ? 0 : teamInfos.Where(w => w.Status == BountyStatus.结束 && w.GuaranteeStatus == GuaranteeStatus.未过保).Select(s => s.Id).Distinct().Count();
        response.ExecOrderMoney = teamInfos == null ? 0m : teamInfos.Where(w => w.Status == BountyStatus.交付中).Sum(s => s.Money);
        response.FailedOrderMoney = teamInfos == null ? 0m : teamInfos.Where(w => w.Status == BountyStatus.结束 && w.GuaranteeStatus == GuaranteeStatus.未过保).Sum(s => s.Money);
        response.ProjectNum = projectInfos.Count;// 所有项目数量
        response.ExecProjectNum = projectInfos.Count(c => c.Status == ProjectStatus.已上线 && c.PublishPost);// 已上线，且存在发布中职位
        response.SharedProjectNum = projectInfos.Count(c => c.PublishPost);// 开启分润且存在发布中职位（意味着必须是已上线项目，归档项目应该不存在发布中的职位）

        return response;
    }

    /// <summary>
    /// 获取登录人所在账套下所有人员
    /// </summary>
    /// <returns></returns>
    private List<UserInfo> GetCompanyUsers()
    {
        var currentUser = _user.Id;
        var userBooks = MyRedis.Client.HGetAll<UserBookRelation>(RedisKey.UserHrBookCodeKey);
        var relations = userBooks?.Select(s => s.Value).ToList();
        var bookCode = relations?.FirstOrDefault(w => w.UserId == currentUser)?.BookCode;
        var UserIds = relations?.Where(w => w.BookCode == bookCode).Select(s => s.UserId).ToList();

        if (UserIds == null)
            UserIds = new List<string> { currentUser };

        // todo: 现在没有账套，所以先查出所有人
        var list = _context.User_Hr
            .Where(w => UserIds.Contains(w.UserId))
            .Select(s => new UserInfo { UserId = s.UserId, UserName = s.NickName })
            .ToList();
        return list;
    }

    public CompanyOrderListResponse GetCenterCompanyOrderPageList(OrderListReqeust request)
    {
        var result = new CompanyOrderListResponse();
        var userLists = GetCompanyUsers();
        var userIds = userLists.Select(s => s.UserId).ToList();
        var predicate = PredicateBuilder.New<Project>(p => _context.Post_Bounty.Any(a => a.Post.ProjectId == p.ProjectId && userIds.Contains(a.TeamHrId)));
        var idPre = Constants.ProjectIdPre;

        // if (!string.IsNullOrWhiteSpace(request.ProjectName))
        //     predicate = predicate.And(x => x.Name.Contains(request.ProjectName));

        if (!string.IsNullOrWhiteSpace(request.ProjectCode))
            if (request.ProjectCode.StartsWith(idPre) && int.TryParse(request.ProjectCode.Substring(1), out var id))
                predicate = predicate.And(x => x.AutoId == id);
        // todo: 这里要确认预算是否已经为0，没有预算先按如下逻辑
        // if (request.Status.HasValue)
        // {
        //     if (request.Status == SStatus.已结算)
        //         predicate = predicate.And(x => x.Project_Teambounty.Any(a => a.SettlementStatus == SettlementType.已结算));
        //     else
        //         predicate = predicate.And(x => x.Project_Teambounty.Any(a => a.SettlementStatus != SettlementType.已结算));
        // }

        // if (request.MinMoney != null && request.MinMoney > 0)
        //     predicate = predicate.And(x => (x.Project_Teambounty.Where(w => userIds.Contains(w.TeamHrId) && w.Status != BountyStatus.交付失败).Sum(s => s.Money) +
        //     x.Project_Teambounty.Where(w => userIds.Contains(w.ChannelHrId) && w.Status != BountyStatus.交付失败).Sum(s => 0)) >= request.MinMoney);

        // if (request.MaxMoney != null && request.MaxMoney > 0)
        //     predicate = predicate.And(x => (x.Project_Teambounty.Where(w => userIds.Contains(w.TeamHrId) && w.Status != BountyStatus.交付失败).Sum(s => s.Money) +
        //     x.Project_Teambounty.Where(w => userIds.Contains(w.ChannelHrId) && w.Status != BountyStatus.交付失败).Sum(s => 0)) <= request.MaxMoney);

        // if (!string.IsNullOrWhiteSpace(request.PostUserId))
        //     predicate = predicate.And(x => x.Project_Teambounty.Any(a => a.TeamHrId == request.PostUserId) || x.Project_Teambounty.Any(a => a.ChannelHrId == request.PostUserId));

        var sql = _context.Project.Where(predicate);

        result.Total = sql.Select(s => new { a = 1 }).Count();
        result.Rows = sql
        .OrderByDescending(o => o.CreatedTime)
        .Skip((request.PageIndex - 1) * request.PageSize)
        .Take(request.PageSize)
        .Select(s => new CompanyOrderList
        {
            ProjectId = s.ProjectId,
            ProjectName = s.Agent_Ent.Name,
            ProjectCode = idPre + s.AutoId.ToString().PadLeft(6, '0'),
            ProjectHrName = s.User_Hr.NickName
        }).ToList();

        foreach (var item in result.Rows)
        {
            var info = GetBountyInfoByProjectId(item.ProjectId, userIds);
            item.OrderNum = info.OrderNum;
            item.ExecOrderNum = info.ExecOrderNum;
            item.SucceedOrderNum = info.SucceedOrderNum;
            item.SettledMoney = info.SettledMoney;
            item.FailedOrderNum = info.FailedOrderNum;
            item.SettleStatus = info.SettleStatus;
            item.DeliveryUserNum = info.DeliveryUserNum;
            item.PayableSettleMoney = info.PayableSettleMoney;
            item.ActualSettleMoney = info.ActualSettleMoney;
        }
        return result;
    }

    public PersonalOrderListResponse GetCenterPersonalOrderPageList(OrderListReqeust request)
    {
        var result = new PersonalOrderListResponse();
        var predicate = PredicateBuilder.New<Project>();//(p => p.Project_Teambounty.Any(a => a.TeamHrId == _user.Id && a.Status != BountyStatus.交付失败) || p.Project_Teambounty.Any(a => a.ChannelHrId == _user.Id && a.Status != BountyStatus.交付失败));

        var idPre = Constants.ProjectIdPre;

        // if (!string.IsNullOrWhiteSpace(request.ProjectName))
        //     predicate = predicate.And(x => x.Name.Contains(request.ProjectName));

        if (!string.IsNullOrWhiteSpace(request.ProjectCode))
            if (request.ProjectCode.StartsWith(idPre) && int.TryParse(request.ProjectCode.Substring(1), out var id))
                predicate = predicate.And(x => x.AutoId == id);

        // // todo: 这里要确认预算是否已经为0，没有预算先按如下逻辑
        // if (request.Status.HasValue)
        // {
        //     if (request.Status == SStatus.已结算)
        //         predicate = predicate.And(x => x.Project_Teambounty.Any(a => a.SettlementStatus == SettlementType.已结算));
        //     else
        //         predicate = predicate.And(x => x.Project_Teambounty.Any(a => a.SettlementStatus != SettlementType.已结算));
        // }

        // if (request.MinMoney != null && request.MinMoney > 0)
        //     predicate = predicate.And(x => x.Project_Teambounty.Sum(s => s.Money) >= request.MinMoney);

        // if (request.MaxMoney != null && request.MaxMoney > 0)
        //     predicate = predicate.And(x => x.Project_Teambounty.Sum(s => s.Money) <= request.MaxMoney);

        var sql = _context.Project.Where(predicate);

        result.Total = sql.Select(s => new { a = 1 }).Count();
        result.Rows = sql
        .OrderByDescending(o => o.CreatedTime)
        .Skip((request.PageIndex - 1) * request.PageSize)
        .Take(request.PageSize)
        .Select(s => new PersonalOrderList
        {
            ProjectId = s.ProjectId,
            ProjectName = s.Agent_Ent.Name,
            ProjectCode = idPre + s.AutoId.ToString().PadLeft(6, '0'),
            ProjectHrName = s.User_Hr.NickName
        }).ToList();

        foreach (var item in result.Rows)
        {
            var info = GetBountyInfoByProjectId(item.ProjectId, new List<string> { _user.Id });
            item.OrderNum = info.OrderNum;
            item.ExecOrderNum = info.ExecOrderNum;
            item.SucceedOrderNum = info.SucceedOrderNum;
            item.SettledMoney = info.SettledMoney;
            item.FailedOrderNum = info.FailedOrderNum;
            item.SettleStatus = info.SettleStatus;
            item.PayableSettleMoney = info.PayableSettleMoney;
            item.ActualSettleMoney = info.ActualSettleMoney;
        }
        return result;
    }

    public OrderDetailPageListResponse GetOrderDetial(OrderDetailRequst request)
    {
        List<string> userIds = new();
        if (request.Type == RequestType.公司)
        {
            var users = GetCompanyUsers();
            userIds = users.Select(s => s.UserId).ToList();
        }
        else
        {
            userIds.Add(_user.Id);
        }

        // var teams = _context.Project_Teambounty.Where(w => w.Status == BountyStatus.结算中 && w.ProjectId == request.ProjectId && userIds.Contains(w.TeamHrId))
        //     .Select(s => new OrderDetailPageList
        //     {
        //         UserId = s.TeamHrId,
        //         UserName = s.TeamHrName,
        //         UserNo = "",
        //         SettlementTypeName = "工资卡",
        //         PayableSettlementMoney = s.Money,
        //         ActualSettlementMoney = s.project_Settlements.Where(a => a.Type == SettleType.协同).Select(s => s.ActualSettlementMoney).FirstOrDefault(),
        //         StatusName = s.project_Settlements.Where(a => a.Type == SettleType.协同).Select(s => s.Status).FirstOrDefault().GetDescription(),
        //         OrderId = s.Id
        //     }).ToList();

        // var channels = _context.Project_Teambounty.Where(w => w.Status == BountyStatus.结算中 && w.ProjectId == request.ProjectId && userIds.Contains(w.ChannelHrId))
        //     .Select(s => new OrderDetailPageList
        //     {
        //         UserId = s.ChannelHrId,
        //         UserName = s.ChannelHrName,
        //         UserNo = "",
        //         SettlementTypeName = "工资卡",
        //         PayableSettlementMoney = 0,
        //         ActualSettlementMoney = 0,
        //         StatusName = s.project_Settlements.Where(a => a.Type == SettleType.渠道).Select(s => s.Status).FirstOrDefault().GetDescription(),
        //         OrderId = s.Id
        //     }).ToList();

        // var response = new List<OrderDetailPageList>();
        // if (teams != null && teams.Count > 0)
        //     response.AddRange(teams);
        // if (channels != null && channels.Count > 0)
        //     response.AddRange(channels);

        // 钉钉工号信息，todo:增加内存开销
        var ddInfo = _context.User_DingDing.Select(s => new { s.UserId, s.DingJobNo }).ToList();

        // // todo:这个很影响效率，后期再改
        // response.ForEach(f =>
        // {
        //     if (string.IsNullOrWhiteSpace(f.UserName))
        //     {
        //         var userName = _context.User_Hr.Where(x => x.UserId == f.UserId).Select(s => s.NickName).FirstOrDefault();
        //         userName ??= _context.User_Seeker.Where(x => x.UserId == f.UserId).Select(s => s.NickName).FirstOrDefault();
        //         f.UserName = userName;
        //     }

        //     f.UserNo = ddInfo?.FirstOrDefault(x => x.UserId == f.UserId)?.DingJobNo;
        // });

        // var result = new OrderDetailPageListResponse { Rows = response.Skip((request.PageIndex - 1) * request.PageSize).Take(request.PageSize).ToList(), Total = response.Count };
        var result = new OrderDetailPageListResponse { Rows = new List<OrderDetailPageList>(), Total = 0 };
        return result;
    }

    public CompanyOrderList GetBountyInfoByProjectId(string projectId, List<string> userIds)
    {
        CompanyOrderList info = new();
        // userIds.Contains(w.ChannelHrId)
        var bounty = _context.Post_Bounty
            .Where(w => w.Post.ProjectId == projectId && userIds.Contains(w.TeamHrId))
            .Select(s => new
            {
                s.Id,
                s.Post.ProjectId,
                s.TeamHrId,
                s.Qd_Hr_Channel.HrId,
                s.Status,
                s.GuaranteeStatus,
                // s.SettlementStatus,
                PayableSettleMoney = s.Money,
                ActualSettleMoney = 0
            }).ToList();

        var channelBounty = _context.Post_Bounty
            .Where(w => w.Post.ProjectId == projectId && userIds.Contains(w.Qd_Hr_Channel.HrId))
            .Select(s => new
            {
                s.Id,
                s.Post.ProjectId,
                s.TeamHrId,
                s.Qd_Hr_Channel.HrId,
                s.Status,
                s.GuaranteeStatus,
                // s.SettlementStatus,
                //PayableSettleMoney = s.Money - s.PlatformBounty,
                PayableSettleMoney = s.Money - s.Money * s.PlatformRate,
                ActualSettleMoney = 0
            }).ToList();

        bounty = bounty.Union(channelBounty).ToList();

        info.OrderNum = bounty.Select(s => s.Id).Distinct().Count();
        info.ExecOrderNum = bounty.Where(w => w.Status == BountyStatus.交付中).Select(s => s.Id).Distinct().Count();
        info.SucceedOrderNum = bounty.Where(w => w.Status == BountyStatus.交付中).Select(s => s.Id).Distinct().Count();
        info.SettledMoney = bounty.Where(w => w.Status == BountyStatus.交付中 || w.GuaranteeStatus == GuaranteeStatus.过保).Sum(s => s.PayableSettleMoney);
        info.PayableSettleMoney = bounty.Where(w => w.Status == BountyStatus.交付中).Sum(s => s.PayableSettleMoney);
        info.ActualSettleMoney = bounty.Where(w => w.Status == BountyStatus.交付中).Sum(s => s.ActualSettleMoney);
        info.FailedOrderNum = bounty.Where(w => w.Status == BountyStatus.结束 && w.GuaranteeStatus == GuaranteeStatus.未过保).Select(s => s.Id).Distinct().Count();
        //info.SettleStatus = "待结算";// todo：都是待结算
        // info.SettleStatus = bounty.Any(a => a.SettlementStatus != SettlementType.已结算) ? (bounty.Any(a => a.Status != BountyStatus.交付失败) ? "待结算" : "--") : "已结算";// todo：存在一个待结算订单就是待结算，渠道跟协同都结算完成回写SettlementStatus

        List<string> Ids = new();
        bounty.ForEach(f =>
        {
            if (!Ids.Contains(f.TeamHrId))
                Ids.Add(f.TeamHrId);

            // if (!Ids.Contains(f.ChannelHrId))
            //     Ids.Add(f.ChannelHrId);
        });


        info.DeliveryUserNum = userIds.Intersect(Ids).Count();
        return info;
    }

    public List<UserInfo> GetDeliveryUsers()
    {
        var allUsers = GetCompanyUsers();
        var userList = allUsers.Select(s => s.UserId).ToList();
        var teamUsers = _context.Post_Bounty.Where(w => userList.Contains(w.TeamHrId)).Select(s => new { UserId = s.TeamHrId }).ToList();
        var channelUsers = _context.Post_Bounty.Where(w => userList.Contains(w.Qd_Hr_Channel.HrId)).Select(s => new { UserId = s.Qd_Hr_Channel.HrId }).ToList();
        if (channelUsers != null && channelUsers.Count > 0)
            teamUsers = teamUsers.Union(channelUsers!).ToList();
        List<UserInfo> users = new();
        foreach (var item in teamUsers)
        {
            if (users.FirstOrDefault(f => f.UserId == item.UserId) == null)
            {
                if (item.UserId != null)
                {
                    var UserName = allUsers.FirstOrDefault(f => f.UserId == item.UserId)?.UserName;
                    users.Add(new UserInfo { UserId = item.UserId, UserName = UserName + "" });
                }
            }
        }
        return users;
    }
    /// <summary>
    /// 获取项目的订单交易记录
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    public TransactionListResp GetTransactionList(TransactionListReq req)
    {
        var result = new TransactionListResp();
        var predicate = PredicateBuilder.New<Post_Bounty>(x => x.Post.ProjectId == req.ProjectId);

        // 当前人是否是项目经理或者销售经理，查询全部订单
        var isManager = _context.Project.Any(a => a.ProjectId == req.ProjectId && (a.HrId == _user.Id || a.SaleUserId == _user.Id));
        if (!isManager)
            predicate = predicate.And(x => x.TeamHrId == _user.Id);

        if (!string.IsNullOrWhiteSpace(req.Search))
            predicate = predicate.And(x => x.User_Seeker.NickName.Contains(req.Search) || x.User_Seeker.User.Mobile.Contains(req.Search));
        if (req.Status.HasValue)
            predicate = predicate.And(x => x.Status == req.Status);
        if (!string.IsNullOrWhiteSpace(req.BeginDate))
            predicate = predicate.And(x => x.CreatedTime >= Convert.ToDateTime(req.BeginDate));
        if (!string.IsNullOrWhiteSpace(req.EndDate))
            predicate = predicate.And(x => x.CreatedTime <= Convert.ToDateTime(req.EndDate).AddDays(1).AddSeconds(-1));
        if (!string.IsNullOrWhiteSpace(req.PostId))
            predicate = predicate.And(x => x.PostId == req.PostId);
        var sql = _context.Post_Bounty.Where(predicate);
        result.Total = sql.Select(s => new { a = 1 }).Count();
        result.Rows = sql
            .OrderByDescending(o => o.CreatedTime)
            .Skip((req.PageIndex - 1) * req.PageSize)
            .Take(req.PageSize)
            .Select(s => new TransactionInfo
            {
                OrderId = s.Id,
                Amount = s.Money,
                Status = s.Status,
                StatusName = s.Status.GetDescription(),
                CreatedTime = s.CreatedTime,
                SaleId = s.SaleUserId!,
                SaleName = s.SaleUser.NickName,
                //SalesBounty = s.SalesBounty,
                SalesBounty = s.Money * s.SalesRate,
                PmId = s.HrId,
                PmName = s.User_Hr.NickName,
                //ManagerBounty = s.ManagerBounty,
                ManagerBounty = s.Money * s.ManagerRate,
                SeekerId = s.SeekerId,
                SeekerName = s.User_Seeker.NickName,
                SeekerMobile = s.User_Seeker.User.Mobile,
                PostName = s.PostName ?? string.Empty,
                DeliveryTime = s.CreatedTime,
                ClueId = s.TeamHrId,
                ClueName = s.Team_User_Hr.NickName,
                //ClueBounty = s.ClueBounty,
                ClueBounty = s.Money * s.ClueRate,
                FollowerId = s.FollowerId,
                FollowerName = s.Recruit.FollowerUser.NickName,
                //FollowerBounty = s.FollowerBounty,
                //PlatformBounty = s.PlatformBounty,
                //DeliveryBounty = s.ClueBounty + s.FollowerBounty,
                FollowerBounty = s.Money * s.FollowerRate,
                PlatformBounty = s.Money * s.PlatformRate,
                DeliveryBounty = s.Money * (s.ClueRate + s.FollowerRate),
                RecruitStatus = s.Recruit.Status,
            }).ToList();
        return result;
    }
    /// <summary>
    /// 获取计费审核列表
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    public BountyAccountingResponse GetAccountingList(BountyAccountingRequst req)
    {
        var result = new BountyAccountingResponse();
        var predicate = PredicateBuilder.New<Post_Settlement>(true);
        try
        {
            if (!string.IsNullOrWhiteSpace(req.Search))
            {
                // 项目名称
                // 筛选条件需要跟前端商量，有哪些条件可以单独出来，避免以下代码一个搜索查询全部条件都查询出来
                predicate = predicate.And(x => x.Post_Bounty_Stage.Post_Bounty.Post.Project.Agent_Ent.Name.Contains(req.Search) ||
                                            x.Post_Bounty_Stage.Post_Bounty.PostName.Contains(req.Search) ||
                                            x.Post_Bounty_Stage.Post_Bounty.User_Seeker.NickName.Contains(req.Search) ||
                                            x.Post_Bounty_Stage.Post_Bounty.SaleUser.NickName.Contains(req.Search) ||
                                            x.Post_Bounty_Stage.Post_Bounty.Team_User_Hr.NickName.Contains(req.Search) ||
                                            x.Post_Bounty_Stage.Post_Bounty.Recruit.FollowerUser.NickName.Contains(req.Search) ||
                                            x.Post_Bounty_Stage.Post_Bounty.User_Hr.NickName.Contains(req.Search) ||
                                            _context.Ndn_Books.Any(x => x.BookName.Contains(req.Search)));
            }
            if (!string.IsNullOrWhiteSpace(req.ProjectName))
            {
                predicate = predicate.And(x => x.Post_Bounty_Stage.Post_Bounty.Post.Project.Agent_Ent.Name.Contains(req.ProjectName));
            }
            if (!string.IsNullOrWhiteSpace(req.PostName))
            {
                predicate = predicate.And(x => x.Post_Bounty_Stage.Post_Bounty.PostName.Contains(req.PostName));
            }
            if (!string.IsNullOrEmpty(req.ManagerName))
            {
                predicate = predicate.And(x => x.Post_Bounty_Stage.Post_Bounty.User_Hr.NickName.Contains(req.ManagerName));
            }
            if (!string.IsNullOrEmpty(req.SaleName))
            {
                predicate = predicate.And(x => x.Post_Bounty_Stage.Post_Bounty.SaleUser.NickName.Contains(req.SaleName));
            }
            if (!string.IsNullOrWhiteSpace(req.SeekerName))
            {
                predicate = predicate.And(x => x.Post_Bounty_Stage.Post_Bounty.User_Seeker.NickName.Contains(req.SeekerName));
            }
            if (!string.IsNullOrWhiteSpace(req.RecruitId))
            {
                predicate = predicate.And(x => x.Post_Bounty_Stage.Post_Bounty.RecruitId == req.RecruitId);
            }
            if (req.ApprovalStatus.HasValue)
            {
                predicate = predicate.And(x => x.ApprovalStatus == req.ApprovalStatus);
            }
            if (req.PaymentNode.HasValue)
                predicate = predicate.And(x => x.Post_Bounty_Stage.Post_Bounty.PaymentNode == req.PaymentNode);
            if (req.RewardType.HasValue)
                predicate = predicate.And(x => x.Post_Bounty_Stage.Post_Bounty.RewardType == req.RewardType);
            if (req.Role.HasValue)
            {
                if (req.Role == PostSettleApprovalRole.项目经理审核)
                {
                    predicate = predicate.And(x => x.ApprovalStatus == PostSettleApprovalStatus.项目经理审核
                                                || x.ApprovalStatus == PostSettleApprovalStatus.审核拒绝);
                }
                else if (req.Role == PostSettleApprovalRole.平台审核)
                {
                    predicate = predicate.And(x => (x.ApprovalStatus == PostSettleApprovalStatus.平台审核
                                                || x.ApprovalStatus == PostSettleApprovalStatus.审核拒绝
                                                || x.ApprovalStatus == PostSettleApprovalStatus.审核完成
                                                || x.ApprovalStatus == PostSettleApprovalStatus.财务审核)
                                                && x.Post_Bounty_Stage.Money > 0);
                }
                else if (req.Role == PostSettleApprovalRole.财务审核)
                {
                    predicate = predicate.And(x => (x.ApprovalStatus == PostSettleApprovalStatus.财务审核
                                                || (x.ApprovalStatus == PostSettleApprovalStatus.审核拒绝
                                                    && x.FinanceConfirmTime != null)
                                                || x.ApprovalStatus == PostSettleApprovalStatus.审核完成)
                                                && x.Post_Bounty_Stage.Money > 0);
                }
                else
                {
                    predicate = predicate.And(x => x.ApprovalStatus != PostSettleApprovalStatus.项目经理审核
                                    && x.ApprovalStatus != PostSettleApprovalStatus.审核拒绝
                                    && x.Post_Bounty_Stage.Money > 0);
                }
            }
            if (req.IsClueHall == 1)
            {
                predicate = predicate.And(x => x.Post_Bounty_Stage.Post_Bounty.Recruit.FollowerId == _user.Id
                                            && x.Post_Bounty_Stage.Post_Bounty.Recruit.AcceptOrder == RecruitAcceptOrderEnum.已经接单);
            }
            if (req.IsClueHall == 0)
            {
                predicate = predicate.And(x => x.Post_Bounty_Stage.Post_Bounty.HrId == _user.Id
                            || x.Post_Bounty_Stage.Post_Bounty.TeamHrId == _user.Id);
            }
            if (!string.IsNullOrWhiteSpace(req.ProjectId))
            {
                predicate = predicate.And(x => x.Post_Bounty_Stage.Post_Bounty.Post.ProjectId == req.ProjectId);
            }
            if (!string.IsNullOrWhiteSpace(req.PostId))
            {
                predicate = predicate.And(x => x.Post_Bounty_Stage.Post_Bounty.PostId == req.PostId);
            }
            //账套字典
            var books = _context.Ndn_Books.ToList();

            var sql = _context.Post_Settlement.Where(predicate);
            result.Total = sql.Select(s => new { a = 1 }).Count();
            result.Rows = sql
                .OrderByDescending(o => o.CreatedTime)
                .Skip((req.PageIndex - 1) * req.PageSize)
                .Take(req.PageSize)
                .Select(s => new BountyAccounting
                {
                    SettlementId = s.Id,
                    Status = s.Post_Bounty_Stage.Post_Bounty.Status,
                    StatusName = s.Status.GetDescription(),
                    CreatedTime = s.CreatedTime,
                    SaleId = s.Post_Bounty_Stage.Post_Bounty.SaleUserId!,
                    SaleName = s.Post_Bounty_Stage.Post_Bounty.SaleUser.NickName,
                    SalesBounty = s.Post_Bounty_Stage.Post_Bounty.Money * s.Post_Bounty_Stage.Post_Bounty.SalesRate,
                    PmId = s.Post_Bounty_Stage.Post_Bounty.HrId,
                    PmName = s.Post_Bounty_Stage.Post_Bounty.User_Hr.NickName,
                    ManagerBounty = s.Post_Bounty_Stage.Post_Bounty.Money * s.Post_Bounty_Stage.Post_Bounty.ManagerRate,
                    SeekerId = s.Post_Bounty_Stage.Post_Bounty.SeekerId,
                    SeekerName = s.Post_Bounty_Stage.Post_Bounty.User_Seeker.NickName,
                    SeekerMobile = s.Post_Bounty_Stage.Post_Bounty.User_Seeker.User.Mobile,
                    PostName = s.Post_Bounty_Stage.Post_Bounty.PostName ?? string.Empty,
                    DeliveryTime = s.CreatedTime,
                    ClueId = $"{Constants.CluePre}{s.Post_Bounty_Stage.Post_Bounty.Recruit.AutoId.ToString()!.PadLeft(9, '0')}",
                    ClueName = s.Post_Bounty_Stage.Post_Bounty.Team_User_Hr.NickName,
                    ClueBounty = s.Post_Bounty_Stage.Post_Bounty.Money * s.Post_Bounty_Stage.Post_Bounty.ClueRate,
                    FollowerId = s.Post_Bounty_Stage.Post_Bounty.FollowerId,
                    FollowerName = s.Post_Bounty_Stage.Post_Bounty.Recruit.FollowerUser.NickName,
                    FollowerBounty = s.Post_Bounty_Stage.Post_Bounty.Money * s.Post_Bounty_Stage.Post_Bounty.FollowerRate,
                    PlatformBounty = s.Post_Bounty_Stage.Post_Bounty.Money * s.Post_Bounty_Stage.Post_Bounty.PlatformRate,
                    SettlementMoney = s.SettlementMoney,
                    StartTime = s.StartTime,
                    EndTime = s.EndTime,
                    Sex = s.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Resume.Sex,
                    Birthday = s.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Resume.Birthday,
                    Occupation = s.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Resume.Occupation,
                    Education = s.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Resume.Education,
                    RegionId = s.Post_Bounty_Stage.Post_Bounty.Recruit.User_Seeker.RegionId,
                    Source = s.Post_Bounty_Stage.Post_Bounty.Recruit.Post_Delivery.Post_Team.Project_Team.Source,
                    TeamHrId = s.Post_Bounty_Stage.Post_Bounty.Recruit.Post_Delivery.Post_Team.Project_Team.HrId,
                    Avatar = s.Post_Bounty_Stage.Post_Bounty.Recruit.Post_Delivery.Post.Project.User_Hr.Avatar,
                    Adviser = s.Post_Bounty_Stage.Post_Bounty.Recruit.Post_Delivery.Post_Team.Project_Team.User_Hr.NickName,
                    Active = Tools.GetOnlineStatus(s.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Extend.LoginTime),
                    GraduationDate = s.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Resume.GraduationDate,
                    ProjectName = s.Post_Bounty_Stage.Post_Bounty.Recruit.Post_Delivery.Post.Project.Agent_Ent.Name,
                    PaymentNode = s.Post_Bounty_Stage.Post_Bounty.PaymentNode,
                    PaymentNodeName = s.Post_Bounty_Stage.Post_Bounty.PaymentNode.GetDescription(),
                    RewardType = s.Post_Bounty_Stage.Post_Bounty.RewardType,
                    RewardTypeName = s.Post_Bounty_Stage.Post_Bounty.RewardType.GetDescription(),
                    Money = s.Post_Bounty_Stage.Money,
                    PaymentDays = s.Post_Bounty_Stage.Post_Bounty.PaymentDays,
                    Welfare = s.Post_Bounty_Stage.Post_Bounty.Recruit.Post_Delivery.Post.Welfare,
                    IsSalesCommission = s.Post_Bounty_Stage.Post_Bounty.Recruit.Post_Delivery.Post.IsSalesCommission,
                    AgentEntName = s.Post_Bounty_Stage.Post_Bounty.Recruit.Post_Delivery.Post.Agent_Ent.Name,
                    BookCode = s.Post_Bounty_Stage.Post_Bounty.Post.Project.BookCode,
                    //BookName = books.Where(x => x.BookCode == s.Post_Bounty_Stage.Post_Bounty.Post.Project.BookCode).Select(x => x.BookName).FirstOrDefault(),
                    EduExpSchool = s.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Resume.School,
                    EduExpMajor = s.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Resume.Major,
                    EduExpEducation = s.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Resume.Education,
                    WorkExpClass = s.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Resume.User_Work.OrderByDescending(work => work.BeginDate).Select(userWork =>
                        new Model.Hr.Recruit.WorkExpClass
                        {
                            BeginDate = userWork.BeginDate,
                            Company = userWork.Company,
                            EndDate = userWork.EndDate,
                            Post = userWork.Post,
                        }).FirstOrDefault(),
                    ApprovalStatus = s.ApprovalStatus
                }).ToList();

            foreach (var item in result.Rows)
            {
                item.SexName = item.Sex?.GetDescription();
                item.Age = Tools.GetAgeByBirthdate(item.Birthday);
                item.OccupationName = item.Occupation?.GetDescription();
                item.EducationName = item.Education?.GetDescription();
                item.SourceName = item.Source?.GetDescription();
                item.ApprovalStatusName = item.ApprovalStatus.GetDescription();
                if (!string.IsNullOrWhiteSpace(item.RegionId))
                    item.City = _commonDicService.GetCityById(item.RegionId);
                item.BookName = books.Where(x => x.BookCode == item.BookCode).Select(x => x.BookName).FirstOrDefault();
                item.EduExp = $"{item.EduExpSchool}-{item.EduExpMajor} {item.GraduationDate?.ToString("yyyy-MM")}";
                var workTimeStr = string.Empty;
                if (item.WorkExpClass?.BeginDate != null)
                    workTimeStr = $"{item.WorkExpClass?.BeginDate?.ToString("yyyy-MM")}~{item.WorkExpClass?.EndDate?.ToString("yyyy-MM")}";
                item.WorkExp = $"{item.WorkExpClass?.Company}-{item.WorkExpClass?.Post} {workTimeStr}";
            }
        }
        catch (Exception ex)
        {
            result.Rows = new List<BountyAccounting>();
            result.Total = 0;
            _log.Error("获取计费审核列表出错", ex.Message, ex);
        }
        return result;
    }
    /// <summary>
    /// 计算分佣金额
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public BountyRatioResponse GetBountyRatioList(ReqModel model)
    {
        var result = new BountyRatioResponse();
        if (string.IsNullOrWhiteSpace(model.SettlementId))
            return result;

        var details = _context.Post_Settlement_Detail
           .Where(x => x.Post_Settlement.Id == model.SettlementId)
           .Select(x => new
           {
               // 主实体字段
               x.Id,
               x.UserType,
               x.SettlementMoney,
               x.Post_Settlement.Post_Bounty_Stage.Money,
               x.PaymentStatus,
               x.PaymentTime,
               InMerchantName = x.InMerchant.MerchantName,
               x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.Post.AgentEntId,
               x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.Post.Agent_Ent.Name,
               x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.SalesRate,
               x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.ManagerRate,
               x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.ClueRate,
               x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.FollowerRate,
               x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.PlatformRate,
               x.SettleBountyType,

               // 导航属性字段（直接提取所需值）
               SaleName = x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.SaleUser.NickName,
               ManagerName = x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.User_Hr.NickName,
               ClueName = x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.Team_User_Hr.NickName,
               InviterName = x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.Recruit.FollowerUser.NickName
           })
           .ToList();

        // 使用委托字典优化角色处理逻辑
        var userTypeHandlers = new Dictionary<PostSettleUserType, Func<dynamic, string>>
        {
            [PostSettleUserType.销售] = x => x.SaleName ?? "未知销售",
            [PostSettleUserType.项目经理] = x => x.ManagerName ?? "未知项目经理",
            [PostSettleUserType.线索] = x => x.ClueName ?? "未知线索",
            [PostSettleUserType.邀面] = x => x.InviterName ?? "未知邀面",
            [PostSettleUserType.平台] = _ => "平台"
        };

        var userTypeRatio = new Dictionary<PostSettleUserType, Func<dynamic, decimal>>
        {
            [PostSettleUserType.销售] = x => x.SalesRate ?? 0,
            [PostSettleUserType.项目经理] = x => x.ManagerRate ?? 0,
            [PostSettleUserType.线索] = x => x.ClueRate ?? 0,
            [PostSettleUserType.邀面] = x => x.FollowerRate ?? 0,
            [PostSettleUserType.平台] = x => x.PlatformRate ?? 0
        };

        result.Total = details.Count();
        result.Rows = details.Select(item => new BountyRatio
        {
            SettlementDetailId = item.Id,
            BountyReceiver = userTypeHandlers[item.UserType](item),
            BountyReceiverType = item.UserType.GetDescription(),
            Ratio = userTypeRatio[item.UserType](item),
            SettleBountyType = item.SettleBountyType,
            SettleBountyTypeName = item.SettleBountyType switch
            {
                PostSettleBountyType.内部 => "内部交付",
                PostSettleBountyType.外部 => "外部交付",
                PostSettleBountyType.个人 => "个人交付",
                PostSettleBountyType.平台 => "平台服务费",
                _ => ""
            },
            AgentEntId = item.AgentEntId,
            AgentEntName = item.InMerchantName,
            Money = item.SettlementMoney,
            PaymentStatus = item.PaymentStatus,
            PaymentTime = item.PaymentTime
        }).ToList();
        return result;
    }
    /// <summary>
    /// 获取结算单信息
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public SettlementInfo GetSettlementInfo(ReqModel model)
    {
        var result = new SettlementInfo();
        if (string.IsNullOrWhiteSpace(model.SettlementId))
            return result;

        // 一次性联表查询所有字段，避免 N+1 和空引用
        var settlementInfo = _context.Post_Settlement
            .Where(x => x.Id == model.SettlementId)
            .Select(x => new SettlementInfo
            {
                // 基础信息
                SeekerId = x.Post_Bounty_Stage.Post_Bounty.SeekerId,
                SeekerName = x.Post_Bounty_Stage.Post_Bounty.User_Seeker.NickName,
                Mobile = x.Post_Bounty_Stage.Post_Bounty.User_Seeker.User.Mobile ?? string.Empty,
                Email = x.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Resume.EMail ?? string.Empty,
                Sex = x.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Resume.Sex,
                City = x.Post_Bounty_Stage.Post_Bounty.User_Seeker.RegionId,
                IdCard = x.Post_Bounty_Stage.Post_Bounty.User_Seeker.User.IdentityCard ?? string.Empty,
                Education = x.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Resume.Education,
                EducationName = x.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Resume.Education.GetDescription(),
                Occupation = x.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Resume.Occupation,
                Age = Tools.GetAgeByBirthdate(x.Post_Bounty_Stage.Post_Bounty.User_Seeker.User_Resume.Birthday),
                InductionTime = x.Post_Bounty_Stage.Post_Bounty.Recruit.InductionTime,
                RecruitId = x.Post_Bounty_Stage.Post_Bounty.RecruitId,

                // 佣金信息
                RewardType = x.Post_Bounty_Stage.Post_Bounty.RewardType,
                PaymentNode = x.Post_Bounty_Stage.Post_Bounty.PaymentNode,
                PaymentNodeName = x.Post_Bounty_Stage.Post_Bounty.PaymentNode.GetDescription(),
                PaymentCycle = x.Post_Bounty_Stage.Post_Bounty.PaymentCycle,
                PostId = x.Post_Bounty_Stage.Post_Bounty.PostId,
                PaymentDuration = x.Post_Bounty_Stage.Post_Bounty.PaymentDuration,
                GuaranteeStartDate = x.Post_Bounty_Stage.Post_Bounty.GuaranteeStartDate,
                GuaranteeDays = x.Post_Bounty_Stage.GuaranteeDays,
                Money = x.Post_Bounty_Stage.Money,
                BountyId = x.Post_Bounty_Stage.Post_Bounty.Id,
                SettlementAmount = x.SettlementAmount,
                SettlementMoney = x.SettlementMoney,

                // 结算信息
                StartTime = x.StartTime!.Value.Date.ToString("yyyy-MM-dd"),
                EndTime = x.EndTime!.Value.Date.ToString("yyyy-MM-dd"),
                Status = x.Status,
                ApprovalStatus = x.ApprovalStatus,
                ApprovalStatusName = x.ApprovalStatus.GetDescription(),
                Remark = x.Remark ?? string.Empty
            })
            .FirstOrDefault();

        if (settlementInfo.PaymentDuration != null && settlementInfo.GuaranteeStartDate != null)
        {
            settlementInfo.PaymentDurationEndTime = settlementInfo.GuaranteeStartDate
                + TimeSpan.FromDays(settlementInfo.GuaranteeDays)
                + TimeSpan.FromDays(settlementInfo.PaymentDuration.Value);
        }

        if (settlementInfo.BountyId != null)
        {
            var postProfitPayment = _context.Post_Bounty_Stage
                    .Where(x => x.Post_Bounty.Id == settlementInfo.BountyId)
                    .Select(x => new PostProfitPayment
                    {
                        RewardType = x.Post_Bounty.RewardType,
                        PaymentNode = x.Post_Bounty.PaymentNode,
                        PaymentNodeName = x.Post_Bounty.PaymentNode.GetDescription(),
                        GuaranteeDays = x.GuaranteeDays,
                        Money = x.Money,
                        PaymentCycle = x.Post_Bounty.PaymentCycle,
                        PaymentDuration = x.Post_Bounty.PaymentDuration,
                        PaymentDurationEndTime = settlementInfo.PaymentDurationEndTime
                    }).ToList();

            settlementInfo.BountyStages = postProfitPayment;
        }

        return settlementInfo == null
            ? new SettlementInfo()
            : settlementInfo;
    }

    /// <summary>
    /// 获取岗位分润信息
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    //public List<PostProfitPayment> GetPostProfitPayments(ReqModel model)
    //{
    //    var result = new List<PostProfitPayment>();
    //    if (string.IsNullOrWhiteSpace(model.PostId))
    //        return result;
    //    try
    //    {
    //        var postProfitPayment = _context.Post_Profit_Stage
    //            .Where(x => x.PostId == model.PostId)
    //            .Select(x => new PostProfitPayment
    //            {
    //                GuaranteeDays = x.GuaranteeDays,
    //                Money = x.Amount,
    //                PaymentCycle = x.Post.PaymentCycle
    //            }).ToList();
    //        return postProfitPayment;
    //    }
    //    catch (Exception ex)
    //    {
    //        _log.Error("获取岗位分润信息出错", ex.Message, ex);
    //        return result;
    //    }
    //}
    /// <summary>
    /// 历史结算单列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    //public SettlementHistoryResponse GetSettlementHistoryList(ReqModel model)
    //{
    //    var result = new SettlementHistoryResponse();
    //    if (string.IsNullOrWhiteSpace(model.SettlementId))
    //        return result;
    //    try
    //    {
    //        // 获取当前结算单及其关联的分润阶段表数据
    //        var settlement = _context.Post_Settlement
    //            .Where(s => s.ApprovalStatus != PostSettleApprovalStatus.项目经理审核 
    //                        && s.SettlementMoney > 0)
    //            .Include(s => s.Post_Bounty_Stage.Post_Bounty)
    //            .FirstOrDefault(s => s.Id == model.SettlementId);

    //        if (settlement == null)
    //            return result;

    //        // 获取同一职位分润阶段下的所有阶段ID
    //        var stageIds = _context.Post_Bounty_Stage
    //            .Where(s => s.Post_Bounty.Id == settlement.Post_Bounty_Stage.Post_Bounty.Id)
    //            .Select(s => s.Id)
    //            .ToList();

    //        result.Total = _context.Post_Settlement
    //            .Where(p => stageIds.Contains(p.BountyStageId)
    //                && p.ApprovalStatus != PostSettleApprovalStatus.项目经理审核
    //                && p.SettlementMoney > 0)
    //            .Count();
    //        // 查询历史结算记录并映射结果列表
    //        result.Rows = _context.Post_Settlement
    //            .Where(p => stageIds.Contains(p.BountyStageId)
    //                && p.ApprovalStatus != PostSettleApprovalStatus.项目经理审核
    //                && p.SettlementMoney > 0)
    //            .Include(p => p.Post_Bounty_Stage)
    //            .OrderBy(o => o.CreatedTime)
    //            .Skip((model.PageIndex - 1) * model.PageSize)
    //            .Take(model.PageSize)
    //            .Select(x => new SettlementHistory {
    //                SettlementId = x.Id,
    //                SettlementAmount = x.SettlementAmount,
    //                Money = x.SettlementMoney,
    //                SettlementTime = x.CreatedTime,
    //                Status = x.Status,
    //                ApprovalStatus = x.ApprovalStatus,
    //                ApprovalStatusName = x.ApprovalStatus.GetDescription(),
    //                BountysRatioMoney = new List<BountyRatioMoney>()
    //            })
    //            .ToList();

    //        // 使用委托字典优化角色处理逻辑
    //        var userTypeHandlers = new Dictionary<PostSettleUserType, Func<dynamic, string>>
    //        {
    //            [PostSettleUserType.销售] = x => x.SaleName ?? "未知销售",
    //            [PostSettleUserType.项目经理] = x => x.ManagerName ?? "未知项目经理",
    //            [PostSettleUserType.线索] = x => x.ClueName ?? "未知线索",
    //            [PostSettleUserType.邀面] = x => x.InviterName ?? "未知邀面",
    //            [PostSettleUserType.平台] = _ => "平台"
    //        };

    //        var settlementids = result.Rows.Select(x => x.SettlementId).ToList();
    //        var details = _context.Post_Settlement_Detail
    //                        .Where(x => settlementids.Contains(x.SettlementId))
    //                        .Include(x => x.Post_Settlement.Post_Bounty_Stage.Post_Bounty)
    //                        .Select(x=> new
    //                         {
    //                            x.SettlementId,
    //                            x.Post_Settlement,
    //                            x.UserType,
    //                            x.UserId,
    //                            x.SettlementMoney,

    //                            // 导航属性字段（直接提取所需值）
    //                            SaleName = x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.SaleUser.NickName,
    //                            ManagerName = x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.User_Hr.NickName,
    //                            ClueName = x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.Team_User_Hr.NickName,
    //                            InviterName = x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.Recruit.FollowerUser.NickName
    //                        })
    //                        .ToList();
    //        foreach (var item in details)
    //        {
    //            var set = result.Rows.Where(x => x.SettlementId.Contains(item.SettlementId)).FirstOrDefault();
    //            set.SettlementTitle = GetSettlementTitle(item.Post_Settlement);
    //            set.BountysRatioMoney.Add(new BountyRatioMoney
    //            {
    //                BountyReceiver = userTypeHandlers[item.UserType](item),
    //                BountyReceiverType = item.UserType.GetDescription(),
    //                SettlementMoney = item.SettlementMoney
    //            });
    //        }

    //    }
    //    catch (Exception ex)
    //    {
    //        _log.Error("获取历史结算单列表出错", ex.Message);
    //    }
    //    return result;
    //}

    #region 历史结算单列表（旧方法）
    public SettlementHistoryResponse GetSettlementHistoryList(ReqModel model)
    {
        var result = new SettlementHistoryResponse();
        if (string.IsNullOrWhiteSpace(model.SettlementId) && string.IsNullOrWhiteSpace(model.OrderId))
            return result;
        try
        {
            // 获取当前结算单及其关联的分润阶段表数据
            if (string.IsNullOrEmpty(model.OrderId))
                model.OrderId = _context.Post_Settlement.Where(s => s.Id == model.SettlementId).Select(s => s.Post_Bounty_Stage.BountyId).FirstOrDefault();

            // 获取同一职位分润阶段下的所有阶段ID
            // var stageIds = _context.Post_Bounty_Stage
            //     .Where(s => s.Post_Bounty.Id == model.OrderId)
            //     .Select(s => s.Id)
            //     .ToList();

            // 查询历史结算记录并映射结果列表
            var settlements = _context.Post_Settlement
                .Where(p => p.Post_Bounty_Stage.BountyId == model.OrderId
                    && p.ApprovalStatus != PostSettleApprovalStatus.项目经理审核
                    //&& p.SettlementMoney > 0)
                    && p.Post_Bounty_Stage.Money > 0)
                .Include(p => p.Post_Bounty_Stage)
                .Include(p => p.Post_Bounty_Stage.Post_Bounty.Recruit)
                .OrderByDescending(o => o.CreatedTime)
                .Skip((model.PageIndex - 1) * model.PageSize)
                .Take(model.PageSize)
                .ToList();

            List<SettlementHistory> list = new List<SettlementHistory>();
            foreach (var item in settlements)
            {
                var settlementHistory = new SettlementHistory();
                settlementHistory.SettlementTitle = GetSettlementTitle(item);
                settlementHistory.SettlementId = item.Id;
                settlementHistory.SettlementAmount = item.SettlementAmount;
                settlementHistory.SettlementMoney = item.SettlementMoney;
                settlementHistory.Money = item.Post_Bounty_Stage.Money;
                settlementHistory.ManagerConfirmTime = item.ManagerConfirmTime;
                settlementHistory.SettlementTime = item.CreatedTime;
                settlementHistory.Status = item.Status;
                settlementHistory.ApprovalStatus = item.ApprovalStatus;
                settlementHistory.ApprovalStatusName = item.ApprovalStatus.GetDescription();
                settlementHistory.PaymentCycle = item.Post_Bounty_Stage.Post_Bounty.PaymentCycle;
                settlementHistory.RewardType = item.Post_Bounty_Stage.Post_Bounty.RewardType;
                if (item.ApprovalStatus == PostSettleApprovalStatus.无效结算单)
                {
                    settlementHistory.IsInvalidSettlement = 1;
                    settlementHistory.Money = 0;
                }
                else
                {
                    settlementHistory.IsInvalidSettlement = 0;
                }
                list.Add(settlementHistory);
            }
            result.Total = list.Count();
            result.Rows = list;

            var settlementids = result.Rows.Select(x => x.SettlementId).ToList();
            var details = _context.Post_Settlement_Detail.Where(x => settlementids.Contains(x.SettlementId)).ToList();
            foreach (var item in details)
            {
                //item.SettlementMoney = item.Money;

            }

        }
        catch (Exception ex)
        {
            _log.Error("获取历史结算单列表出错", ex.Message);
        }
        return result;
    }
    #endregion

    /// <summary>
    /// 获取项目合同列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public ContractListResponse GetProjectContractListBySettlementId(ReqModel model)
    {
        var result = new ContractListResponse();
        if (string.IsNullOrWhiteSpace(model.SettlementId))
            return result;

        var settlement = _context.Post_Settlement.
                        Where(x => x.Id == model.SettlementId).
                        Include(x => x.Post_Bounty_Stage.Post_Bounty.Post.Project).
                        FirstOrDefault();
        if (settlement == null)
            return result;

        var predicate = PredicateBuilder.New<Project_Contract>(x => x.ProjectId == settlement.Post_Bounty_Stage.Post_Bounty.Post.Project.ProjectId);

        var sql = _context.Project_Contract.Where(predicate);
        result.Total = sql.Count();
        result.Rows = sql.OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new ContractInfo
        {
            ContractId = s.ContractId,
            ProjectId = s.ProjectId,
            Name = s.Project.Agent_Ent.Name,
            SignDate = s.SignDate,
            InitiationDate = s.InitiationDate,
            StartTime = s.StartTime,
            EndTime = s.EndTime,
            Initiator = s.Initiator,
            Participant = s.Participant,
            InitiatorPhone = s.InitiatorPhone,
            ParticipantPhone = s.ParticipantPhone,
            InitiatorContact = s.InitiatorContact,
            ParticipantContact = s.ParticipantContact,
            Amount = s.Amount,
            Status = s.Status,
            StatusText = s.Status.GetDescription(),
            ContractNo = s.ContractNo,
            Attachment = s.Attachment,
            SignType = s.SignType,
            Source = s.Source,
            Remark = s.Remark,
            CreatedById = s.CreatedBy,
            CreatedByName = s.user_Hr.NickName
        }).ToList();

        return result;
    }


    /// <summary>
    /// 结算单详情
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public SettlementHistoryDetail GetSettlementHistoryDetail(ReqModel model)
    {
        if (string.IsNullOrWhiteSpace(model.SettlementId))
            return new SettlementHistoryDetail();
        try
        {
            var settlement = _context.Post_Settlement
                .Where(x => x.Id == model.SettlementId)
                .Select(x => new SettlementHistoryDetail
                {
                    AgentName = x.Post_Bounty_Stage.Post_Bounty.Post.Agent_Ent.Name,
                    UserId = x.Post_Bounty_Stage.Post_Bounty.User_Hr.UserId,
                    PaymentNode = x.Post_Bounty_Stage.Post_Bounty.PaymentNode,
                    RewardType = x.Post_Bounty_Stage.Post_Bounty.RewardType,
                    PaymentCycle = x.Post_Bounty_Stage.Post_Bounty.PaymentCycle,
                    StartTime = x.StartTime,
                    EndTime = x.EndTime,
                    SettlementEndTime = x.Post_Bounty_Stage.SettlementEndTime,
                    ApprovalStatus = x.ApprovalStatus,
                    ApprovalStatusName = x.ApprovalStatus.GetDescription(),
                    SettlementTime = x.CreatedTime
                })
                .FirstOrDefault();

            if (settlement != null)
            {
                if ((settlement.StartTime == null || settlement.EndTime == null) && settlement.SettlementEndTime != null)
                {
                    settlement.SettlementDateTime = settlement.SettlementEndTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
                }
                else if (settlement.StartTime != null && settlement.EndTime != null)
                {
                    settlement.SettlementDateTime = $"{settlement.StartTime.Value.ToString("yyyy-MM-dd")}~{settlement.EndTime.Value.ToString("yyyy-MM-dd")}";
                }
            }
            return settlement == null
                ? new SettlementHistoryDetail()
                : settlement;
        }
        catch (Exception ex)
        {
            _log.Error("结算单详情出错", ex.Message);
            return new SettlementHistoryDetail();
        }
    }

    /// <summary>
    /// 获取结算单标题
    /// </summary>
    /// <param name="postSettlement"></param>
    /// <returns></returns>
    string GetSettlementTitle(Post_Settlement postSettlement)
    {
        var settlementTitleBuilder = new StringBuilder();
        if (postSettlement.Post_Bounty_Stage.Post_Bounty.PaymentNode == ProjectPaymentNode.简历交付)
        {
            settlementTitleBuilder.Append("简历交付确认");
        }
        if (postSettlement.Post_Bounty_Stage.Post_Bounty.PaymentNode == ProjectPaymentNode.入职过保 &&
            postSettlement.Post_Bounty_Stage.Post_Bounty.RewardType == PostRewardType.长期结算)
        {
            int year = postSettlement.EndTime!.Value.Year;
            int month = postSettlement.EndTime!.Value.Month;
            settlementTitleBuilder.Append($"{year}年{month}月对账单");
        }
        if (postSettlement.Post_Bounty_Stage.Post_Bounty.PaymentNode == ProjectPaymentNode.入职过保 &&
            postSettlement.Post_Bounty_Stage.Post_Bounty.RewardType == PostRewardType.单次结算)
        {
            settlementTitleBuilder.Append("质保期");
            int? guaranteedays = postSettlement.Post_Bounty_Stage.GuaranteeDays;
            PostPaymentCycle? paymentCycle = postSettlement.Post_Bounty_Stage.Post_Bounty.PaymentCycle;
            settlementTitleBuilder.Append(guaranteedays + "天结算单");
        }
        return settlementTitleBuilder.ToString();
    }

    //public async Task<DeliveryReceiptResonse> DeliveryReceipt(DetailRequestModel req)
    //{
    //    var result = new DeliveryReceiptResonse();
    //    if (string.IsNullOrWhiteSpace(req.SettlementId) && string.IsNullOrWhiteSpace(req.SettlementDetailId))
    //        throw new BadRequestException("传参不能同时为空");
    //    //账套字典
    //    var books = _context.Ndn_Books.ToList();
    //    var mobilelist = _context.User.Select(x => new
    //    {
    //        x.UserId,
    //        x.Mobile
    //    }).ToList();
    //    decimal sum = 0;
    //    if (!string.IsNullOrEmpty(req.SettlementId))
    //    {
    //        result = _context.Post_Settlement.
    //            Where(x => x.Id == req.SettlementId).
    //            Select(s => new DeliveryReceiptResonse
    //            {
    //                SettlementId = s.Id,
    //                BountyId = s.Post_Bounty_Stage.Post_Bounty.Id,
    //                ProjectId = s.Post_Bounty_Stage.Post_Bounty.Post.Project.ProjectId,
    //                ProjectName = s.Post_Bounty_Stage.Post_Bounty.Post.Project.Agent_Ent.Name,
    //                AutoNo = s.Post_Bounty_Stage.Post_Bounty.Post.Project.AutoId,
    //                SeekerName = s.Post_Bounty_Stage.Post_Bounty.User_Seeker.NickName,
    //                SaleName = s.Post_Bounty_Stage.Post_Bounty.SaleUser.NickName,
    //                ManagerName = s.Post_Bounty_Stage.Post_Bounty.User_Hr.NickName,
    //                ClueName = s.Post_Bounty_Stage.Post_Bounty.Team_User_Hr.NickName,
    //                FollowName = s.Post_Bounty_Stage.Post_Bounty.Recruit.FollowerUser.NickName,
    //                RewardType = s.Post_Bounty_Stage.Post_Bounty.RewardType,
    //                RewardTypeName = s.Post_Bounty_Stage.Post_Bounty.RewardType.GetDescription(),
    //                PaymentNode = s.Post_Bounty_Stage.Post_Bounty.PaymentNode,
    //                PaymentNodeName = s.Post_Bounty_Stage.Post_Bounty.PaymentNode.GetDescription(),
    //                PostName = s.Post_Bounty_Stage.Post_Bounty.Post.Name,
    //                DeliveryTime = s.Post_Bounty_Stage.Post_Bounty.Recruit.Post_Delivery.CreatedTime,
    //                TelNo = s.Post_Bounty_Stage.Post_Bounty.User_Hr.User.Mobile,
    //                ConfirmTime = s.ManagerConfirmTime,
    //                ConfirmResult = s.Post_Bounty_Stage.Post_Bounty.RewardType == PostRewardType.单次结算
    //                                            ? s.Post_Bounty_Stage.GuaranteeDays.ToString()
    //                                            : s.SettlementAmount.ToString() + "/" +
    //                                            s.Post_Bounty_Stage.Post_Bounty.PaymentCycle,
    //            }).
    //            FirstOrDefault();
    //        if (result == null)
    //            throw new BadRequestException("结算单不存在");

    //        result.DeliveryDetails = _context.Post_Settlement_Detail.
    //            Where(x => x.Post_Settlement.Id == req.SettlementId).
    //            Select(x => new DeliveryDetail
    //            {
    //                DeliveryType = x.UserType.GetDescription(),
    //                NdnBookCode = x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.Post.Project.BookCode,
    //                UserId = x.UserId,
    //                BountyMoney = x.SettlementMoney,
    //                PaymentType = x.InMerchantId == x.OutMerchantId ? "银联调账" : "银联转账",
    //                PaymentMoney = x.SettlementMoney * (decimal)0.94
    //            }).
    //            ToList();
    //    }
    //    else if (!string.IsNullOrEmpty(req.SettlementDetailId))
    //    {
    //        var detail = _context.Post_Settlement_Detail.
    //            Where(x => x.Id == req.SettlementDetailId).
    //            Select(x=> new DeliveryDetail 
    //            {
    //                SettlementId = x.SettlementId,
    //                DeliveryType = x.UserType.GetDescription(),
    //                NdnBookCode = x.Post_Settlement.Post_Bounty_Stage.Post_Bounty.Post.Project.BookCode,
    //                UserId = x.UserId,
    //                BountyMoney = x.SettlementMoney,
    //                PaymentType = x.InMerchantId == x.OutMerchantId ? "银联调账" : "银联转账",
    //                PaymentMoney = x.SettlementMoney * (decimal)0.94
    //            }).ToList();
    //        if(detail == null || detail.Count == 0)
    //            throw new BadRequestException("结算明细不存在");            

    //        result = _context.Post_Settlement.
    //            Where(x => x.Id == detail[0].SettlementId).
    //            Select(s => new DeliveryReceiptResonse
    //            {
    //                SettlementId = s.Id,
    //                BountyId = s.Post_Bounty_Stage.Post_Bounty.Id,
    //                ProjectId = s.Post_Bounty_Stage.Post_Bounty.Post.Project.ProjectId,
    //                ProjectName = s.Post_Bounty_Stage.Post_Bounty.Post.Project.Agent_Ent.Name,
    //                AutoNo = s.Post_Bounty_Stage.Post_Bounty.Post.Project.AutoId,
    //                SeekerName = s.Post_Bounty_Stage.Post_Bounty.User_Seeker.NickName,
    //                SaleName = s.Post_Bounty_Stage.Post_Bounty.SaleUser.NickName,
    //                ManagerName = s.Post_Bounty_Stage.Post_Bounty.User_Hr.NickName,
    //                ClueName = s.Post_Bounty_Stage.Post_Bounty.Team_User_Hr.NickName,
    //                FollowName = s.Post_Bounty_Stage.Post_Bounty.Recruit.FollowerUser.NickName,
    //                RewardType = s.Post_Bounty_Stage.Post_Bounty.RewardType,
    //                RewardTypeName = s.Post_Bounty_Stage.Post_Bounty.RewardType.GetDescription(),
    //                PaymentNode = s.Post_Bounty_Stage.Post_Bounty.PaymentNode,
    //                PaymentNodeName = s.Post_Bounty_Stage.Post_Bounty.PaymentNode.GetDescription(),
    //                PostName = s.Post_Bounty_Stage.Post_Bounty.Post.Name,
    //                DeliveryTime = s.Post_Bounty_Stage.Post_Bounty.Recruit.Post_Delivery.CreatedTime,
    //                TelNo = s.Post_Bounty_Stage.Post_Bounty.User_Hr.User.Mobile,
    //                ConfirmTime = s.ManagerConfirmTime,
    //                ConfirmResult = s.Post_Bounty_Stage.Post_Bounty.RewardType == PostRewardType.单次结算
    //                                            ? s.Post_Bounty_Stage.GuaranteeDays.ToString()
    //                                            : s.SettlementAmount.ToString() + "/" +
    //                                            s.Post_Bounty_Stage.Post_Bounty.PaymentCycle,
    //            }).
    //            FirstOrDefault();
    //        result.DeliveryDetails = detail;
    //        if (result == null)
    //            throw new BadRequestException("结算单不存在");
    //    }
    //    foreach (var item in result.DeliveryDetails)
    //    {
    //        item.NdnBookName = books.Where(x => x.BookCode == item.NdnBookCode).Select(x => x.BookName).FirstOrDefault();
    //        item.ContactNo = mobilelist.Where(x => x.UserId == item.UserId).Select(x => x.Mobile).FirstOrDefault();
    //        item.DeliveryName = item.DeliveryType switch
    //        {
    //            "销售" => result.SaleName,
    //            "项目经理" => result.ManagerName,
    //            "线索" => result.ClueName,
    //            "邀面" => result.FollowName,
    //            _ => "平台"
    //        };
    //        sum += item.PaymentMoney ?? 0;
    //    }
    //    result.Total = sum;
    //    await Task.FromResult(1);
    //    return result;
    //}

    public async Task<DeliveryReceiptResonse> DeliveryReceipt(DetailRequestModel req)
    {
        if (string.IsNullOrWhiteSpace(req.SettlementId) && string.IsNullOrWhiteSpace(req.SettlementDetailId))
            throw new BadRequestException("传参不能同时为空");

        var books = await _context.Ndn_Books.ToListAsync();
        var mobileList = await _context.User.
            Select(x => new UserMobile
            {
                UserId = x.UserId,
                Mobile = x.Mobile
            }).ToListAsync();

        var result = new DeliveryReceiptResonse();
        // decimal totalPayment = 0;

        if (!string.IsNullOrEmpty(req.SettlementId))
        {
            result = await GetBaseReceiptInfo(req.SettlementId) ??
                     throw new BadRequestException("结算单不存在");

            result.DeliveryDetails = await GetDeliveryDetails(req.SettlementId);
        }
        else if (!string.IsNullOrEmpty(req.SettlementDetailId))
        {
            var details = await GetDeliveryDetailsByDetailId(req.SettlementDetailId);
            if (details == null || !details.Any())
                throw new BadRequestException("结算明细不存在");

            result = await GetBaseReceiptInfo(details[0].SettlementId) ??
                     throw new BadRequestException("结算单不存在");

            result.DeliveryDetails = details;
        }
        foreach (var item in result.DeliveryDetails)
        {
            item.DeliveryName = item.DeliveryType switch
            {
                "销售" => result.SaleName,
                "项目经理" => result.ManagerName,
                "线索" => result.ClueName,
                "邀面" => result.FollowName,
                _ => "平台"
            };
        }
        EnrichDeliveryDetails(result.DeliveryDetails, books, mobileList);

        result.Total = result.DeliveryDetails.Sum(d => d.PaymentMoney ?? 0);
        result.TotalOrigin = result.DeliveryDetails.Sum(d => d.BountyMoney ?? 0);
        result.ConfirmResult = $"{result.TotalOrigin ?? 0}元";
        return result;
    }

    private async Task<DeliveryReceiptResonse?> GetBaseReceiptInfo(string settlementId)
    {
        return await _context.Post_Settlement
            .Where(x => x.Id == settlementId)
            .Select(s => new DeliveryReceiptResonse
            {
                SettlementId = s.Id,
                BountyId = s.Post_Bounty_Stage.Post_Bounty.Id,
                ProjectId = s.Post_Bounty_Stage.Post_Bounty.Post.Project.ProjectId,
                ProjectName = s.Post_Bounty_Stage.Post_Bounty.Post.Project.Agent_Ent.Name,
                AutoNo = s.Post_Bounty_Stage.Post_Bounty.Post.Project.AutoId,
                SeekerName = s.Post_Bounty_Stage.Post_Bounty.User_Seeker.NickName,
                SaleName = s.Post_Bounty_Stage.Post_Bounty.SaleUser.NickName,
                ManagerName = s.Post_Bounty_Stage.Post_Bounty.User_Hr.NickName,
                ClueName = s.Post_Bounty_Stage.Post_Bounty.Team_User_Hr.NickName,
                FollowName = s.Post_Bounty_Stage.Post_Bounty.Recruit.FollowerUser.NickName,
                RewardType = s.Post_Bounty_Stage.Post_Bounty.RewardType,
                RewardTypeName = s.Post_Bounty_Stage.Post_Bounty.RewardType.GetDescription(),
                PaymentNode = s.Post_Bounty_Stage.Post_Bounty.PaymentNode,
                PaymentNodeName = s.Post_Bounty_Stage.Post_Bounty.PaymentNode.GetDescription(),
                PostName = s.Post_Bounty_Stage.Post_Bounty.Post.Name,
                DeliveryTime = s.Post_Bounty_Stage.Post_Bounty.Recruit.Post_Delivery.CreatedTime,
                TelNo = s.Post_Bounty_Stage.Post_Bounty.User_Seeker.User.Mobile,
                ConfirmTime = s.ManagerConfirmTime,
                ConfirmResult = s.Post_Bounty_Stage.Post_Bounty.RewardType == PostRewardType.单次结算
                    ? s.Post_Bounty_Stage.GuaranteeDays.ToString()
                    : s.SettlementAmount.ToString() + "/" + s.Post_Bounty_Stage.Post_Bounty.PaymentCycle,
            })
            .FirstOrDefaultAsync();
    }

    private async Task<List<DeliveryDetail>> GetDeliveryDetails(string settlementId)
    {
        return await _context.Post_Settlement_Detail
            .Where(x => x.SettlementId == settlementId)
            .Select(x => new DeliveryDetail
            {
                SettlementId = x.SettlementId,
                DeliveryType = x.UserType.GetDescription(),
                NdnBookName = x.InMerchant.MerchantName,
                UserId = x.UserId,
                BountyMoney = x.SettlementMoney,
                PaymentType = x.InMerchantId == x.OutMerchantId ? "银联调账" : "银联转账",
                PaymentMoney = x.SettleBountyType == PostSettleBountyType.内部 ?
                    x.SettlementMoney * Constants.ServiceFeeMultiplier :
                    x.SettlementMoney
            })
            .ToListAsync();
    }

    private async Task<List<DeliveryDetail>> GetDeliveryDetailsByDetailId(string settlementDetailId)
    {
        return await _context.Post_Settlement_Detail
            .Where(x => x.Id == settlementDetailId)
            .Select(x => new DeliveryDetail
            {
                SettlementId = x.SettlementId,
                DeliveryType = x.UserType.GetDescription(),
                NdnBookName = x.InMerchant.MerchantName,
                UserId = x.UserId,
                BountyMoney = x.SettlementMoney,
                PaymentType = x.InMerchantId == x.OutMerchantId ? "银联调账" : "银联转账",
                PaymentMoney = x.SettleBountyType == PostSettleBountyType.内部 ?
                    x.SettlementMoney * Constants.ServiceFeeMultiplier :
                    x.SettlementMoney
            })
            .ToListAsync();
    }

    private void EnrichDeliveryDetails(List<DeliveryDetail> details, List<Ndn_Books> books, List<UserMobile> mobileList)
    {
        foreach (var item in details)
        {
            // item.NdnBookName = books.FirstOrDefault(x => x.BookCode == item.NdnBookCode)?.BookName;
            item.ContactNo = mobileList.FirstOrDefault(x => x.UserId == item.UserId)?.Mobile;
        }
    }

    public ProjectFundsListResp GetProjectFundsList(ProjectFundsListReq req)
    {
        var result = new ProjectFundsListResp();
        var predicate = PredicateBuilder.New<Project_Funds_Transfers>(x => x.ProjectId == req.ProjectId);
        predicate = predicate.And(x => x.Type == ProjectFundsTransfersTypeEnums.充值);
        var sql = _context.Project_Funds_Transfers.Where(predicate);
        result.Total = sql.Select(s => new { a = 1 }).Count();
        result.Rows = sql
            .OrderByDescending(o => o.CreatedTime)
            .Skip((req.PageIndex - 1) * req.PageSize)
            .Take(req.PageSize)
            .Select(s => new ProjectFundsInfo
            {
                ProjectId = s.ProjectId,
                Amount = s.Amount,
                Balance = s.Balance,
                Description = s.Description,
                CreatedTime = s.CreatedTime,
            })
            .ToList();
        return result;
    }

    [RedisCache(Expire = 60)]
    public async Task<BountyRatioResponse> ComputeBountyRatioList(ReqModel model)
    {
        var result = new BountyRatioResponse();
        result.Rows = new List<BountyRatio>();

        var recruitId = _context.Post_Settlement.
            Where(w => w.Id == model.SettlementId).
            Select(s => s.Post_Bounty_Stage.Post_Bounty.RecruitId).
            FirstOrDefault();

        var bountyInfo = _context.Post_Bounty.
            Where(w => w.RecruitId == recruitId).
            Select(s => new
            {
                SaleName = s.SaleUser.NickName,
                ManagerName = s.User_Hr.NickName,
                ClueName = s.Team_User_Hr.NickName,
                FollowerName = s.FollowerUser.NickName,
                s.SaleUserId,
                s.HrId,
                s.TeamHrId,
                s.FollowerId,
                s.SalesRate,
                s.ManagerRate,
                s.ClueRate,
                s.FollowerRate,
                s.PlatformRate,
                s.Post.Project.BookCode,
                s.Post.Agent_Ent.Name
            }).FirstOrDefault();

        if (bountyInfo == null)
            throw new BadRequestException("内容不存在");

        var userIds = new List<string>
        {
            bountyInfo.SaleUserId,
            bountyInfo.HrId,
            bountyInfo.TeamHrId,
            bountyInfo.FollowerId
        };
        var hrNos = _commonUserService.GetUserNos(userIds!);

        if (bountyInfo.SalesRate > 0)
        {
            var ratio = new BountyRatio
            {
                UserId = bountyInfo.SaleUserId,
                BountyReceiverType = PostSettleUserType.销售.GetDescription(),
                BountyReceiver = bountyInfo.SaleName,
                Ratio = bountyInfo.SalesRate,
                AgentEntName = bountyInfo.Name,
            };
            if (hrNos.TryGetValue(ratio.UserId, out var hrNo))
            {
                var book = await _ndnService.GetEmployee(hrNo);
                if (!string.IsNullOrEmpty(book?.bookName))
                    ratio.AgentEntName = book.bookName;
            }
            result.Rows.Add(ratio);
        }
        if (bountyInfo.ManagerRate > 0)
        {
            var ratio = new BountyRatio
            {
                UserId = bountyInfo.HrId,
                BountyReceiverType = PostSettleUserType.项目经理.GetDescription(),
                BountyReceiver = bountyInfo.ManagerName,
                Ratio = bountyInfo.ManagerRate,
                AgentEntName = bountyInfo.Name,
            };
            if (hrNos.TryGetValue(ratio.UserId, out var hrNo))
            {
                var book = await _ndnService.GetEmployee(hrNo);
                if (!string.IsNullOrEmpty(book?.bookName))
                    ratio.AgentEntName = book.bookName;
            }
            result.Rows.Add(ratio);
        }
        if (bountyInfo.ClueRate > 0)
        {
            var ratio = new BountyRatio
            {
                UserId = bountyInfo.TeamHrId,
                BountyReceiverType = PostSettleUserType.线索.GetDescription(),
                BountyReceiver = bountyInfo.ClueName,
                Ratio = bountyInfo.ClueRate,
                AgentEntName = bountyInfo.Name,
            };
            if (hrNos.TryGetValue(ratio.UserId, out var hrNo))
            {
                var book = await _ndnService.GetEmployee(hrNo);
                if (!string.IsNullOrEmpty(book?.bookName))
                    ratio.AgentEntName = book.bookName;
            }
            result.Rows.Add(ratio);
        }
        if (bountyInfo.FollowerRate > 0)
        {
            var ratio = new BountyRatio
            {
                UserId = bountyInfo.FollowerId,
                BountyReceiverType = PostSettleUserType.邀面.GetDescription(),
                BountyReceiver = bountyInfo.FollowerName,
                Ratio = bountyInfo.FollowerRate,
                AgentEntName = bountyInfo.Name,
            };
            if (hrNos.TryGetValue(ratio.UserId, out var hrNo))
            {
                var book = await _ndnService.GetEmployee(hrNo);
                if (!string.IsNullOrEmpty(book?.bookName))
                    ratio.AgentEntName = book.bookName;
            }
            result.Rows.Add(ratio);
        }
        if (bountyInfo.PlatformRate > 0)
            result.Rows.Add(new BountyRatio
            {
                BountyReceiverType = PostSettleUserType.平台.GetDescription(),
                BountyReceiver = "平台",
                Ratio = bountyInfo.PlatformRate,
                AgentEntName = Constants.PlatformName,
            });

        return result;
    }

    /// <summary>
    /// 确认结算
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task<EmptyResponse> ConfirmSettlement(ConfirmSettlementReq request)
    {
        var result = new EmptyResponse();

        await Task.FromResult(1);
        // 加锁防止连击造成的重复提交
        using var locker = MyRedis.TryLock(request.SettlementId, 100);
        if (locker == null)
            return result;

        var recruitId = _context.Post_Settlement
            .Where(w => w.Id == request.SettlementId)
            .Select(s => s.Post_Bounty_Stage.Post_Bounty.RecruitId)
            .FirstOrDefault();

        var bounty = _context.Post_Bounty.Include(i => i.Recruit)
        .FirstOrDefault(f => f.RecruitId == recruitId);

        var bountyInfo = _context.Post_Bounty
            .Where(w => w.RecruitId == recruitId)
            .Select(s => new
            {
                s.Id,
                s.Post.Project.HrId,
                s.TeamHrId,
                s.RewardType,
                s.PaymentNode,
                s.PaymentCycle,
                RecruitStatus = s.Recruit.Status,
                ProjectBookCode = s.Post.Project.BookCode,
                DefaultContract = _context.Project_Contract.Where(w => w.ProjectId == s.Post.ProjectId && w.IsDefault == 1)
                .Select(s => s.ContractNo).FirstOrDefault()
            }).FirstOrDefault();

        if (bountyInfo == null)
            throw new BadRequestException("内容不存在");

        if (!string.Equals(bountyInfo.HrId, _user.Id))
            throw new BadRequestException("当前登录人不是项目经理");

        if (!request.FileAway.HasValue)
            throw new BadRequestException("参数错误");

        var settlement = _context.Post_Settlement.Include(i => i.Post_Bounty_Stage)
        .Where(w => w.Id == request.SettlementId)
        .FirstOrDefault();

        if (settlement == null)
            throw new BadRequestException("结算单不存在");

        if (!(settlement.ApprovalStatus == PostSettleApprovalStatus.项目经理审核 ||
            settlement.ApprovalStatus == PostSettleApprovalStatus.审核拒绝))
            throw new BadRequestException("结算单审批状态错误");

        // 如果无效结算单
        if (request.FileAway.Value)
        {
            if (bountyInfo.PaymentNode == ProjectPaymentNode.入职过保)
            {
                if (bountyInfo.RecruitStatus != RecruitStatus.FileAway)
                    throw new BadRequestException("请先归档招聘流程方可进行此操作");
            }

            settlement.SettlementAmount = 0;
            settlement.SettlementMoney = 0;
            //settlement.ApprovalStatus = PostSettleApprovalStatus.审核完成;
            settlement.ApprovalStatus = PostSettleApprovalStatus.无效结算单;
        }
        else
        {
            if (bountyInfo.RewardType == PostRewardType.长期结算)
            {
                if (request.Amount <= 0)
                    throw new BadRequestException("结果值必须大于0");

                settlement.SettlementAmount = request.Amount;
                settlement.SettlementMoney = request.Amount * settlement.Post_Bounty_Stage.Money;
            }
            else if (bountyInfo.RewardType == PostRewardType.单次结算)
            {
                settlement.SettlementMoney = settlement.Post_Bounty_Stage.Money;
            }
            settlement.ApprovalStatus = PostSettleApprovalStatus.平台审核;
        }

        if (!string.IsNullOrEmpty(request.Remark))
            settlement.Remark = request.Remark;

        settlement.ManagerConfirmTime = DateTime.Now;
        settlement.ManagerConfirmUser = _user.Id;
        settlement.PlatformConfirmTime = null;
        settlement.PlatformConfirmUser = null;
        settlement.FinanceConfirmTime = null;
        settlement.FinanceConfirmUser = null;

        // 添加修改结算单明细前，先删除历史结算单明细数据，防止数据重复
        _context.RemoveRange(_context.Post_Settlement_Detail.Where(x => x.SettlementId == settlement.Id));

        // 添加结算单明细
        if (settlement.SettlementMoney > 0)
        {
            var settlementDetails = new List<Post_Settlement_Detail>();
            if (bounty.SalesRate > 0)
                settlementDetails.Add(new Post_Settlement_Detail { UserType = PostSettleUserType.销售, UserId = bounty.SaleUserId!, SettlementMoney = settlement.SettlementMoney * bounty.SalesRate });
            if (bounty.ManagerRate > 0)
                settlementDetails.Add(new Post_Settlement_Detail { UserType = PostSettleUserType.项目经理, UserId = bounty.HrId, SettlementMoney = settlement.SettlementMoney * bounty.ManagerRate });
            if (bounty.ClueRate > 0)
                settlementDetails.Add(new Post_Settlement_Detail { UserType = PostSettleUserType.线索, UserId = bounty.TeamHrId, SettlementMoney = settlement.SettlementMoney * bounty.ClueRate });
            if (bounty.FollowerRate > 0)
                settlementDetails.Add(new Post_Settlement_Detail { UserType = PostSettleUserType.邀面, UserId = bounty.FollowerId!, SettlementMoney = settlement.SettlementMoney * bounty.FollowerRate });
            if (bounty.PlatformRate > 0)
                settlementDetails.Add(new Post_Settlement_Detail { UserType = PostSettleUserType.平台, SettlementMoney = settlement.SettlementMoney * bounty.PlatformRate });

            var hrIds = settlementDetails.Select(s => s.UserId).Where(x => !string.IsNullOrEmpty(x)).Distinct().ToList();

            var hrs = _context.User_Hr
                .Where(w => hrIds.Contains(w.UserId))
                .Select(s => new
                {
                    s.UserId,
                    s.EntId,
                    s.Enterprise.Specific
                    // IsNoah = s.Enterprise.Specific.Contains(EntSpecific.Noah) == true
                })
                .ToList();

            // //查询员工工号
            // var userNos = _context.User_DingDing.Where(x => hrIds.Contains(x.UserId))
            // .Select(s => new { s.UserId, s.DingJobNo }).ToList();

            if (string.IsNullOrEmpty(bountyInfo.ProjectBookCode))
                throw new BadRequestException("项目未绑定账套，请先绑定");

            if (string.IsNullOrEmpty(bountyInfo.DefaultContract))
                throw new BadRequestException("项目尚未设置默认合同，请先设置");

            var outBook = _context.Ndn_Books
            .Where(w => w.BookCode == bountyInfo.ProjectBookCode)
            .Select(s => new { s.BookCode, s.BookName })
            .FirstOrDefault();

            var outBookName = outBook?.BookName;

            var outMerchant = _context.Enterprise_Merchant
            .Where(x => x.MerchantName == outBookName)
            .Select(s => new { s.Id, s.PartyAContractNo, s.PartyBContractNo, s.InvoiceInfo, s.OfficeSeal })
            .FirstOrDefault();

            if (string.IsNullOrEmpty(outMerchant.Id))
                throw new BadRequestException($"企业未开户：{outBookName}");

            if (!_context.Ums_Complex_Upload.Any(x => x.MerchantId == outMerchant.Id &&
                x.ApplyStatus == ApplyStatusEnum.入网成功 &&
                x.OpenStatus == SubAccountOpenStatus.成功))
                throw new BadRequestException($"企业未入网：{outBookName}");

            if (string.IsNullOrEmpty(outMerchant.PartyAContractNo) || string.IsNullOrEmpty(outMerchant.PartyBContractNo))
                throw new BadRequestException($"企业未签订框架合同：{outBookName}，请先签订框架合同");

            if (string.IsNullOrEmpty(outMerchant.InvoiceInfo?.ContractOperatorName))
                throw new BadRequestException($"企业未设置发票信息：{outBookName}，请先设置发票信息");

            if (string.IsNullOrEmpty(outMerchant.OfficeSeal))
                throw new BadRequestException($"商户未设置印章：{outBookName}，请先设置印章");

            // 平台商户号
            var platformMerchantId = _context.Enterprise_Merchant
            .Where(x => x.MerchantType == MerchantType.平台)
            .Select(s => s.Id)
            .FirstOrDefault();

            var gerenMerchantId = _context.Enterprise_Merchant
                .Where(x => x.MerchantType == MerchantType.个人)
                .Select(s => s.Id)
                .FirstOrDefault();

            if (string.IsNullOrEmpty(platformMerchantId))
                throw new BadRequestException("平台商户号未设置，请联系管理员");

            var endIds = hrs.Select(s => s.EntId).Where(x => !string.IsNullOrEmpty(x)).Distinct().ToList();
            var ents = _context.Enterprise
                .Where(w => endIds.Contains(w.EntId))
                .Select(s => new { s.EntId, s.Name })
                .ToList();

            var hrNos = _commonUserService.GetUserNos(hrIds!);

            var agentHrNo = _commonUserService.GetUserNo(bountyInfo.HrId);
            if (string.IsNullOrEmpty(agentHrNo))
                throw new BadRequestException($"项目经理尚未绑定工号，用户ID：{bountyInfo.HrId}");

            foreach (var item in settlementDetails)
            {
                var hr = hrs.FirstOrDefault(f => f.UserId == item.UserId);

                // 补充工号、企业Id
                item.SettlementId = settlement.Id;
                item.EntId = hr?.EntId;

                if (item.UserType == PostSettleUserType.平台)
                    item.SettleBountyType = PostSettleBountyType.平台;
                else if (hr?.Specific?.Contains(EntSpecific.GeRen) == true)
                    item.SettleBountyType = PostSettleBountyType.个人;
                else if (hr?.Specific?.Contains(EntSpecific.Noah) == true)
                    item.SettleBountyType = PostSettleBountyType.内部;
                else
                    item.SettleBountyType = PostSettleBountyType.外部;

                // 补充数据
                item.OutMerchantId = outMerchant.Id;

                if (item.SettleBountyType == PostSettleBountyType.内部)
                {
                    if (!hrNos.TryGetValue(item.UserId, out var hrNo))
                        throw new BadRequestException($"内部员工尚未绑定工号，用户ID：{item.UserId}");

                    if (string.IsNullOrEmpty(hrNo))
                        throw new BadRequestException($"内部员工尚未绑定工号，用户ID：{item.UserId}");

                    item.UserNo = hrNo;

                    // 内部走帐套
                    var book = await _ndnService.GetEmployee(item.UserNo);

                    if (string.IsNullOrEmpty(book?.bookCode) || string.IsNullOrEmpty(book?.bookName))
                        throw new BadRequestException($"内部员工未绑定账套，用户：{item.UserNo}");

                    var bookCode = book?.bookCode;
                    var bookName = book?.bookName;

                    SetMerchantIdByBookName(item, bookName);
                }
                else if (item.SettleBountyType == PostSettleBountyType.外部)
                {
                    // 外部获取bookName的逻辑
                    var bookName = ents
                        .Where(w => w.EntId == item.EntId)
                        .Select(s => s.Name)
                        .FirstOrDefault();

                    SetMerchantIdByBookName(item, bookName);
                }
                else if (item.SettleBountyType == PostSettleBountyType.个人)
                {
                    // 个人走平台商户
                    item.InMerchantId = gerenMerchantId;
                }
                else if (item.SettleBountyType == PostSettleBountyType.平台)
                {
                    // 平台走平台商户
                    item.InMerchantId = platformMerchantId;
                }
                else
                    throw new BadRequestException($"未知的结算类型：{item.SettleBountyType}");

                if (string.IsNullOrEmpty(item.AgentHrNo))
                {
                    item.AgentHrNo = agentHrNo;

                    if (string.IsNullOrEmpty(item.AgentHrNo))
                        throw new BadRequestException($"项目经理尚未绑定工号，用户ID：{bountyInfo.HrId}");
                }
            }

            // 优化：批量查询所有需要的商户信息，避免在循环中查询数据库
            var inMerchantIds = settlementDetails.Select(d => d.InMerchantId).Where(x => !string.IsNullOrEmpty(x)).Distinct().ToList();
            inMerchantIds.Append(platformMerchantId);
            inMerchantIds.Append(gerenMerchantId);
            inMerchantIds = inMerchantIds.Distinct().ToList();

            var inMerchantLookup = _context.Enterprise_Merchant
                .Where(x => inMerchantIds.Contains(x.Id))
                .Select(s => new { s.Id, s.PartyAContractNo, s.PartyBContractNo, s.MerchantType, s.MerchantName, s.InvoiceInfo, s.OfficeSeal })
                .ToDictionary(x => x.Id, x => x);

            // 预查询平台商户信息
            var platformMerchant = inMerchantLookup.GetValueOrDefault(platformMerchantId);
            if (platformMerchant == null)
                throw new BadRequestException("平台商户不存在，请联系管理员");

            // 如果inMerchantLookup中缺失合同，抛出异常
            foreach (var item in inMerchantLookup)
            {
                if (string.IsNullOrEmpty(item.Value.PartyAContractNo) || string.IsNullOrEmpty(item.Value.PartyBContractNo))
                    throw new BadRequestException($"商户未签订框架合同：{item.Value.MerchantName}，请先签订框架合同");

                if (string.IsNullOrEmpty(item.Value.InvoiceInfo?.ContractOperatorName))
                    throw new BadRequestException($"商户未设置发票信息：{item.Value.MerchantName}，请先设置发票信息");

                if (string.IsNullOrEmpty(item.Value.OfficeSeal))
                    throw new BadRequestException($"商户未设置印章：{item.Value.MerchantName}，请先设置印章");
            }

            foreach (var item in settlementDetails)
            {
                // 补合同号，分2种情况，出金商户和入金商户相同，则取客户合同号，否则，如果入金账户不是平台，则取入金商户的合同号，如果是平台，则取出金合同号
                if (item.OutMerchantId == item.InMerchantId)
                {
                    item.ContractNo = bountyInfo.DefaultContract;
                }
                else if (item.InMerchantId != platformMerchantId)
                {
                    // 入金商户不是平台，则取入金商户的合同号
                    var inMerchant = inMerchantLookup.GetValueOrDefault(item.InMerchantId!);

                    if (inMerchant == null)
                        throw new BadRequestException($"入金商户不存在，ID：{item.InMerchantId}");

                    item.ContractNo = inMerchant.PartyBContractNo;
                }
                else
                {
                    // 入金商户是平台，则取出金商户的合同号
                    item.ContractNo = outMerchant.PartyAContractNo;
                }
            }

            _context.AddRange(settlementDetails);
        }

        _context.SaveChanges();
        return result;
    }

    /// <summary>
    /// 审批结算
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    /// <exception cref="BadRequestException"></exception>
    public async Task<EmptyResponse> ApprovalSettlement(ApprovalSettlementReq request)
    {
        var result = new EmptyResponse();

        var predicate = PredicateBuilder.New<Post_Settlement>(x => x.Id == request.SettlementId);

        switch (request.Role)
        {
            case PostSettleApprovalRole.平台审核:
                predicate = predicate.And(x => x.ApprovalStatus == PostSettleApprovalStatus.平台审核);
                break;
            case PostSettleApprovalRole.财务审核:
                predicate = predicate.And(x => x.ApprovalStatus == PostSettleApprovalStatus.财务审核);
                break;
            default:
                throw new BadRequestException("暂不支持其他角色审核");
        }

        var settlements = _context.Post_Settlement
            .Include(i => i.Post_Bounty_Stage)
            .Where(predicate).FirstOrDefault();

        if (settlements == null)
            throw new BadRequestException("结算单不存在");

        switch (request.Role)
        {
            case PostSettleApprovalRole.平台审核:
                settlements.PlatformConfirmTime = DateTime.Now;
                settlements.PlatformConfirmUser = _user.Id;
                var isAdmin = _user.Powers?.Contains(Power.NkpAdmin) == true;
                if (!isAdmin)
                    throw new ForbiddenException("无权限操作平台审核");
                break;
            case PostSettleApprovalRole.财务审核:
                settlements.FinanceConfirmTime = DateTime.Now;
                settlements.FinanceConfirmUser = _user.Id;
                var isFinance = _user.Powers?.Contains(Power.NkpFinance) == true;
                if (!isFinance)
                    throw new ForbiddenException("无权限操作财务审核");
                break;
            default:
                throw new BadRequestException("暂不支持其他角色审核");
        }

        if (request.AuditStatus == AuditStatus.通过)
        {
            // if (request.Role == PostSettleApprovalRole.财务审核)
            // {
            //     // 判断项目是否绑定数字诺亚
            //     var sinfo = _context.Post_Bounty_Stage.Where(x => x.Id == settlements.BountyStageId)
            //         .Select(s => new
            //         {
            //             s.Post_Bounty.Post.Project.XbbContractNo
            //         }).FirstOrDefault();

            //     if (string.IsNullOrEmpty(sinfo?.XbbContractNo))
            //         throw new BadRequestException("项目未绑定数字诺亚，请先绑定");
            // }

            settlements.ApprovalStatus = request.Role switch
            {
                PostSettleApprovalRole.平台审核 => PostSettleApprovalStatus.财务审核,
                PostSettleApprovalRole.财务审核 => PostSettleApprovalStatus.审核完成,
                _ => throw new BadRequestException("暂不支持其他角色审核")
            };
        }
        else if (request.AuditStatus == AuditStatus.拒绝)
        {
            // 暂时不使用审核拒绝状态，后边确认不用的话直接从枚举里删掉
            settlements.ApprovalStatus = PostSettleApprovalStatus.审核拒绝;
        }
        //需要判断结算方与诺聘签订框架合同 从ndn_books表获取合同号

        if (!string.IsNullOrEmpty(request.Remark))
            settlements.Remark = request.Remark;

        var settlement = _context.Post_Settlement
        .Where(x => x.Id == settlements.Id)
        .Select(s => new
        {
            ProjectId = s.Post_Bounty_Stage.Post_Bounty.Post.ProjectId,
            ProjectHrId = s.Post_Bounty_Stage.Post_Bounty.Post.Project.HrId,
            ProjectBookCode = s.Post_Bounty_Stage.Post_Bounty.Post.Project.BookCode,
            DefaultContract = _context.Project_Contract.Where(w => w.ProjectId == s.Post_Bounty_Stage.Post_Bounty.Post.ProjectId && w.IsDefault == 1)
                .Select(s => s.ContractNo).FirstOrDefault()
        }).FirstOrDefault();

        if (request.AuditStatus == AuditStatus.通过)
        {
            // 判断余额是否够
            var balance = _context.SubAccount.Where(x => x.ExternalUserNo == settlement.ProjectId)
            .Select(s => s.Amount).FirstOrDefault() ?? 0;

            if (balance < settlements.SettlementMoney * 100)
                throw new BadRequestException("企业余额不足，请先充值");
        }

        // 补充逻辑：如果审核通过并且是平台审核，需要补充detail表的关键字段，若缺失则不允许审核通过
        if (request.AuditStatus == AuditStatus.通过 && request.Role == PostSettleApprovalRole.平台审核)
        {
            var details = _context.Post_Settlement_Detail
                .Where(x => x.SettlementId == settlements.Id)
                .ToList();

            if (details.Count == 0)
                throw new BadRequestException("结算单明细不能为空");

            if (string.IsNullOrEmpty(settlement.ProjectBookCode))
                throw new BadRequestException("项目未绑定账套，请先绑定");

            if (string.IsNullOrEmpty(settlement.DefaultContract))
                throw new BadRequestException("项目尚未设置默认合同，请先设置");

            var outBook = _context.Ndn_Books
            .Where(w => w.BookCode == settlement.ProjectBookCode)
            .Select(s => new { s.BookCode, s.BookName })
            .FirstOrDefault();

            var outBookName = outBook?.BookName;

            var outMerchant = _context.Enterprise_Merchant
            .Where(x => x.MerchantName == outBookName)
            .Select(s => new { s.Id, s.PartyAContractNo, s.PartyBContractNo, s.InvoiceInfo, s.OfficeSeal })
            .FirstOrDefault();

            if (string.IsNullOrEmpty(outMerchant.Id))
                throw new BadRequestException($"企业未开户：{outBookName}");

            if (!_context.Ums_Complex_Upload.Any(x => x.MerchantId == outMerchant.Id &&
                x.ApplyStatus == ApplyStatusEnum.入网成功 &&
                x.OpenStatus == SubAccountOpenStatus.成功))
                throw new BadRequestException($"企业未入网：{outBookName}");

            if (string.IsNullOrEmpty(outMerchant.PartyAContractNo) || string.IsNullOrEmpty(outMerchant.PartyBContractNo))
                throw new BadRequestException($"企业未签订框架合同：{outBookName}，请先签订框架合同");

            if (string.IsNullOrEmpty(outMerchant.InvoiceInfo?.ContractOperatorName))
                throw new BadRequestException($"企业未设置发票信息：{outBookName}，请先设置发票信息");

            if (string.IsNullOrEmpty(outMerchant.OfficeSeal))
                throw new BadRequestException($"商户未设置印章：{outBookName}，请先设置印章");

            // 平台商户号
            var platformMerchantId = _context.Enterprise_Merchant
                .Where(x => x.MerchantType == MerchantType.平台)
                .Select(s => s.Id)
                .FirstOrDefault();

            var gerenMerchantId = _context.Enterprise_Merchant
                .Where(x => x.MerchantType == MerchantType.个人)
                .Select(s => s.Id)
                .FirstOrDefault();

            if (string.IsNullOrEmpty(platformMerchantId))
                throw new BadRequestException("平台商户号未设置，请联系管理员");

            var endIds = details.Select(s => s.EntId).Where(x => !string.IsNullOrEmpty(x)).Distinct().ToList();
            var ents = _context.Enterprise
                .Where(w => endIds.Contains(w.EntId))
                .Select(s => new { s.EntId, s.Name })
                .ToList();

            var userIds = details.Select(s => s.UserId).Where(x => !string.IsNullOrEmpty(x)).Distinct().ToList();

            var hrNos = _commonUserService.GetUserNos(userIds!);

            var agentHrNo = _commonUserService.GetUserNo(settlement.ProjectHrId);
            if (string.IsNullOrEmpty(agentHrNo))
                throw new BadRequestException($"项目经理尚未绑定工号，用户ID：{settlement.ProjectHrId}");

            // 检查是否有缺失的关键字段
            foreach (var item in details)
            {
                item.OutMerchantId = outMerchant.Id;

                if (item.SettleBountyType == PostSettleBountyType.内部)
                {
                    if (string.IsNullOrEmpty(item.UserNo))
                    {
                        if (!hrNos.TryGetValue(item.UserId, out var hrNo))
                            throw new BadRequestException($"内部员工尚未绑定工号，用户ID：{item.UserId}");

                        if (string.IsNullOrEmpty(hrNo))
                            throw new BadRequestException($"内部员工尚未绑定工号，用户ID：{item.UserId}");

                        item.UserNo = hrNo;
                    }

                    if (string.IsNullOrEmpty(item.InMerchantId))
                    {
                        // 内部走帐套
                        var book = await _ndnService.GetEmployee(item.UserNo);

                        if (string.IsNullOrEmpty(book?.bookCode) || string.IsNullOrEmpty(book?.bookName))
                            throw new BadRequestException($"内部员工未绑定账套，用户：{item.UserNo}");

                        var bookCode = book?.bookCode;
                        var bookName = book?.bookName;

                        SetMerchantIdByBookName(item, bookName);
                    }
                }
                else if (item.SettleBountyType == PostSettleBountyType.外部)
                {
                    if (string.IsNullOrEmpty(item.InMerchantId))
                    {
                        // 外部获取bookName的逻辑
                        var bookName = ents
                            .Where(w => w.EntId == item.EntId)
                            .Select(s => s.Name)
                            .FirstOrDefault();

                        SetMerchantIdByBookName(item, bookName);
                    }
                }
                else if (item.SettleBountyType == PostSettleBountyType.个人)
                {
                    // 个人走平台商户
                    item.InMerchantId = gerenMerchantId;
                }
                else if (item.SettleBountyType == PostSettleBountyType.平台)
                {
                    // 平台走平台商户
                    item.InMerchantId = platformMerchantId;
                }
                else
                    throw new BadRequestException($"未知的结算类型：{item.SettleBountyType}");

                if (string.IsNullOrEmpty(item.AgentHrNo))
                {
                    item.AgentHrNo = agentHrNo;

                    if (string.IsNullOrEmpty(item.AgentHrNo))
                        throw new BadRequestException($"项目经理尚未绑定工号，用户ID：{settlement.ProjectHrId}");
                }
            }

            // 优化：批量查询所有需要的商户信息，避免在循环中查询数据库
            var inMerchantIds = details.Select(d => d.InMerchantId).Where(x => !string.IsNullOrEmpty(x)).Distinct().ToList();
            inMerchantIds.Append(platformMerchantId);
            inMerchantIds.Append(gerenMerchantId);
            inMerchantIds = inMerchantIds.Distinct().ToList();

            var inMerchantLookup = _context.Enterprise_Merchant
                .Where(x => inMerchantIds.Contains(x.Id))
                .Select(s => new { s.Id, s.PartyAContractNo, s.PartyBContractNo, s.MerchantType, s.MerchantName, s.InvoiceInfo, s.OfficeSeal })
                .ToDictionary(x => x.Id, x => x);

            // 预查询平台商户信息
            var platformMerchant = inMerchantLookup.GetValueOrDefault(platformMerchantId);
            if (platformMerchant == null)
                throw new BadRequestException("平台商户不存在，请联系管理员");

            // 如果inMerchantLookup中缺失合同，抛出异常
            foreach (var item in inMerchantLookup)
            {
                if (string.IsNullOrEmpty(item.Value.PartyAContractNo) || string.IsNullOrEmpty(item.Value.PartyBContractNo))
                    throw new BadRequestException($"商户未签订框架合同：{item.Value.MerchantName}，请先签订框架合同");

                if (string.IsNullOrEmpty(item.Value.InvoiceInfo?.ContractOperatorName))
                    throw new BadRequestException($"商户未设置发票信息：{item.Value.MerchantName}，请先设置发票信息");

                if (string.IsNullOrEmpty(item.Value.OfficeSeal))
                    throw new BadRequestException($"商户未设置印章：{item.Value.MerchantName}，请先设置印章");
            }

            foreach (var item in details)
            {
                // 补合同号，分2种情况，出金商户和入金商户相同，则取客户合同号，否则，如果入金账户不是平台，则取入金商户的合同号，如果是平台，则取出金合同号
                if (item.OutMerchantId == item.InMerchantId)
                {
                    item.ContractNo = settlement.DefaultContract;
                }
                else if (item.InMerchantId != platformMerchantId)
                {
                    // 入金商户不是平台，则取入金商户的合同号
                    var inMerchant = inMerchantLookup.GetValueOrDefault(item.InMerchantId!);

                    if (inMerchant == null)
                        throw new BadRequestException($"入金商户不存在，ID：{item.InMerchantId}");

                    item.ContractNo = inMerchant.PartyBContractNo;
                }
                else
                {
                    // 入金商户是平台，则取出金商户的合同号
                    item.ContractNo = outMerchant.PartyAContractNo;
                }
            }
        }

        _context.SaveChanges();

        // 财务审批通过发送消息，需要打款
        if (settlements.ApprovalStatus == PostSettleApprovalStatus.审核完成)
            MyRedis.Client.LPush(SubscriptionKey.NkpFinanceAuditPass, settlements.Id);

        return result;
    }

    private void SetMerchantIdByBookName(Post_Settlement_Detail item, string bookName)
    {
        if (string.IsNullOrEmpty(bookName))
            throw new BadRequestException("无法获取账套信息");

        var merchant = _context.Enterprise_Merchant
            .Where(x => x.MerchantName == bookName)
            .FirstOrDefault();

        if (merchant == null)
            throw new BadRequestException($"企业尚未开户：{bookName}");

        if (!_context.Ums_Complex_Upload.Any(x => x.MerchantId == merchant.Id &&
            x.ApplyStatus == ApplyStatusEnum.入网成功 &&
            x.OpenStatus == SubAccountOpenStatus.成功))
            throw new BadRequestException($"企业未入网：{bookName}");

        if (string.IsNullOrEmpty(merchant.PartyAContractNo) || string.IsNullOrEmpty(merchant.PartyBContractNo))
            throw new BadRequestException($"企业未签订框架合同：{bookName}，请先签订框架合同");

        item.InMerchantId = merchant.Id;
    }
}