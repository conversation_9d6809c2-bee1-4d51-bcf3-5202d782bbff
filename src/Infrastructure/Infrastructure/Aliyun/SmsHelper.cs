﻿using Aliyun.Acs.Core;
using Aliyun.Acs.Core.Http;
using Aliyun.Acs.Core.Profile;
using Config;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using ServiceStack.Text;

namespace Infrastructure.Aliyun;

[Service(ServiceLifetime.Transient)]
public class SmsHelper
{
    private readonly ConfigManager _config;
    private readonly RequestContext _user;
    public SmsHelper(IOptionsSnapshot<ConfigManager> config, RequestContext user)
    {
        _config = config.Value;
        _user = user;
    }

    public void SendCode(string phone, string msg)
    {
        //阿里云发短信
        SendSMSAliyun(phone, msg);
    }

    private void SendSMSAliyun(string phone, string msg)
    {
        var profile = DefaultProfile.GetProfile("default", _config.Aliyun!.AliSMS!.AccessKeyId, _config.Aliyun.AliSMS.AccessKeySecret);
        var client = new DefaultAcsClient(profile);
        var request = new CommonRequest();
        request.Method = MethodType.POST;
        request.Domain = _config.Aliyun.AliSMS.Domain;
        request.Version = _config.Aliyun.AliSMS.Version;
        request.Action = _config.Aliyun.AliSMS.Action;
        // request.Protocol = ProtocolType.HTTP;
        request.AddQueryParameters("PhoneNumbers", phone);
        request.AddQueryParameters("SignName", _config.Aliyun.AliSMS.SignName);
        request.AddQueryParameters("TemplateCode", _config.Aliyun.AliSMS.TemplateCode);
        request.AddQueryParameters("TemplateParam", "{\"code\":\"" + msg + "\"}");

        CommonResponse response = client.GetCommonResponse(request);
    }

    /// <summary>
    /// 发送短信跳转小程序
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="phone"></param>
    /// <param name="templateCode"></param>
    /// <param name="sendModel"></param>
    public AliSmsResultData SendSMSAliyunForJumpApplet<T>(string phone, string templateCode, T sendModel) where T : class
    {
        var profile = DefaultProfile.GetProfile("default", _config.Aliyun!.AliSMS!.AccessKeyId, _config.Aliyun.AliSMS.AccessKeySecret);
        var client = new DefaultAcsClient(profile);
        var request = new CommonRequest();
        request.Method = MethodType.POST;
        request.Domain = _config.Aliyun.AliSMS.Domain;
        request.Version = _config.Aliyun.AliSMS.Version;
        request.Action = _config.Aliyun.AliSMS.Action;
        request.AddQueryParameters("PhoneNumbers", phone);
        request.AddQueryParameters("SignName", _config.Aliyun.AliSMS.SignName);
        request.AddQueryParameters("TemplateCode", templateCode);
        request.AddQueryParameters("TemplateParam", JsonSerializer.SerializeToString(sendModel));

        var response = client.GetCommonResponse(request);
        var result = JsonSerializer.DeserializeFromString<AliSmsResultData>(response.Data);
        result.Success = result.Code?.ToLower() == "ok";
        return result;
    }

    /// <summary>
    /// 发短信（临时）
    /// </summary>
    /// <param name="phone"></param>
    /// <param name="json"></param>
    public AliSmsResultData SendSmsByJson(string phone, string templateCode, string json, string? sign = "诺优考")
    {
        var aoe = string.Empty;
        try
        {
            var profile = DefaultProfile.GetProfile("default", _config.Aliyun!.AliSMS!.AccessKeyId, _config.Aliyun.AliSMS.AccessKeySecret);
            var client = new DefaultAcsClient(profile);
            var request = new CommonRequest();
            request.Method = MethodType.POST;
            request.Domain = _config.Aliyun.AliSMS.Domain;
            request.Version = _config.Aliyun.AliSMS.Version;
            request.Action = _config.Aliyun.AliSMS.Action;
            // request.Protocol = ProtocolType.HTTP;
            request.AddQueryParameters("PhoneNumbers", phone);
            request.AddQueryParameters("SignName", sign);
            request.AddQueryParameters("TemplateCode", templateCode);
            request.AddQueryParameters("TemplateParam", json);

            var response = client.GetCommonResponse(request);
            aoe = response.Data;

            var result = JsonSerializer.DeserializeFromString<AliSmsResultData>(response.Data);
            result.Success = result.Code?.ToLower() == "ok";
            return result;
        }
        catch (Exception e)
        {
            return new AliSmsResultData
            {
                Code = aoe,
                Message = Tools.GetErrMsg(e),
                Success = false
            };
        }
    }
}

public class AliSmsResultData
{
    public string? Message { get; set; }
    public string? Code { get; set; }
    public bool Success { get; set; }
}