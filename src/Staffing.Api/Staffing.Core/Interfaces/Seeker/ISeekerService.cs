﻿using Staffing.Model.Seeker.User;
using Config.CommonModel;
using Config.CommonModel.Tencent;
using Config.CommonModel.Business;
using Staffing.Model.Seeker.Balance;

namespace Staffing.Core.Interfaces.Seeker;

public interface ISeekerService
{
    Task<SeekerLoginResponse> SeekerLogin(SeekerLogin model);
    SeekerInfo GetSeeker();
    CheckChannelResponse CheckChannel();
    GetHrBaseInfoResponse GetHrBaseInfo(GetHrBaseInfo model);
    EmptyResponse UpdateSeeker(UpdateSeeker model);
    UpdateCampusResponse UpdateCampus(UpdateCampus model);
    UpdateWorksResponse UpdateWorks(UpdateWorks model);
    EmptyResponse DeleteCampus(string id);
    EmptyResponse DeleteWorks(string id);
    GetMyAdviserResponse GetMyAdviser(GetMyAdviser model);
    MyAdviserInfo GetCurrentAdviser();
    EmptyResponse SetAdviser(SetAdviser model);
    EmptyResponse AddAdviser(AddAdviser model);
    EmptyResponse UpdateAttchResume(UpdateAttchResume model);
    EmptyResponse DeleteAttchResume();
    GetSeekerBehaviorResponse GetSeekerBehavior();
    EmptyResponse UpdateSeekerLocation(UpdateSeekerLocation model);
    GetCollectPostResponse GetCollectPost(GetCollectPost model);
    GetFollowIndustryResponse GetFollowIndustry(GetFollowIndustry model);
    EmptyResponse UpdateCollectPost(UpdateCollectPost model);
    EmptyResponse UpdateFollowIndustry(UpdateFollowIndustry model);
    SeekerGetAdviserInfo SeekerGetAdviser(string id);
    EmptyResponse UpdateSeekerGuidance(UpdateSeekerGuidance model);
    GetSeekerGuidanceResponse GetSeekerGuidance();
    Task<TokenInfo> SeekerRefreshToken(SeekerRefreshToken model);
    Task<GetImUserSigResponse> GetImUserSig();
    CheckSeekerTokenForNykResponse CheckSeekerTokenForNyk(string? token);
    Task<GetNykRequestNoResponse> GetNykRequestNo(GetNykRequestNo model);
    GetBalanceResponse GetBalance(GetBalance model);
    GetBalanceRecordResponse GetBalanceRecord(GetBalanceRecord model);
    Task<WithdrawResponse> Withdraw(Withdraw model);
    EmptyResponse SeekerAppShow(SeekerAppShow model);
    Task<SeekerLoginResponse> SeekerLogin2(SeekerLoginForDy key);
    GetKzgResumeResponse GetKzgResume(GetKzgResume model);
}