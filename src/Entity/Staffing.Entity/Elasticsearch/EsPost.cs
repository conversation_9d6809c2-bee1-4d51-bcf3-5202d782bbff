﻿using Nest;

namespace Staffing.Entity.Elasticsearch;

[ElasticsearchType(RelationName = "employee", IdProperty = "Id")]
public class EsPost
{
    [Keyword]
    public string? Id { get; set; }

    [Keyword]
    public string? ABCDEFG { get; set; }

    [Keyword]
    public string? Name { get; set; }

    [Keyword]
    public List<string>? HaHa { get; set; }

    [Keyword]
    public List<string>? NameList { get; set; }

    public int? ShuZi { get; set; }

    [Wildcard]
    public string? EMail { get; set; }

    public string? xxx { get; set; }

    public string? abc { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}