using Config.Enums;
using Config.CommonModel;

namespace Staffing.Model.CentreService.User;

public class GetHrList : QueryRequest
{
    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string? NickName { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public UserStatus? Status { get; set; }
}

public class GetHrListResponse : QueryResponse
{
    public List<GetHrInfo> Rows { get; set; } = default!;
}

public class GetHrInfo
{
    public string? UserId { get; set; }

    /// <summary>
    /// 诺聘Id(pk_eaid)
    /// </summary>
    public string NuoId { get; set; } = default!;

    /// <summary>
    /// 手机号
    /// </summary>
    public string Mobile { get; set; } = string.Empty;

    /// <summary>
    /// 企业Id
    /// </summary>
    public string? EntId { get; set; }

    /// <summary>
    /// 诺聘企业Id
    /// </summary>
    public string EntNuoId { get; set; } = string.Empty;

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 企业简称
    /// </summary>
    public string? EntAbbr { get; set; }

    /// <summary>
    /// 企业logo
    /// </summary>
    public string? EntLogo { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string? NickName { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// 注册源
    /// </summary>
    public RegisterSource? Source { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public UserStatus Status { get; set; }

    /// <summary>
    /// 申请开通零工时间
    /// </summary>
    public DateTime ApplicationTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    /// 业务短信数量
    /// </summary>
    public int BusinessSms { get; set; }

    /// <summary>
    /// 钉钉手机号
    /// </summary>
    public string? DingMobile { get; set; }
}

public class HrAudit
{
    /// <summary>
    /// UserId
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public UserStatus? Status { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }
}