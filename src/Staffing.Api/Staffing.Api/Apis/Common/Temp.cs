using Config;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.Options;
using OfficeOpenXml.Packaging.Ionic.Zip;
using Senparc.CO2NET.Extensions;
using ServiceStack;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Elasticsearch;
using Staffing.Entity.Noah;
using Staffing.Entity.Staffing;
using Staffing.Model.Common.System;
using System.Data.Entity;

namespace Staffing.Api.Apis.Common;

[Service(ServiceLifetime.Transient)]
public class TempRouter : ICommonRouterBase
{
    private const string skey = "e4b0c4423f8b4e2b1a5f7d8c9a1e6f3d";

    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("dataprocessing").WithTags("临时接口");

        group.MapGet("/xxaf", (string key, IDbContextFactory<NoahContext> _noahContextFactory) =>
        {
            using var _context = _noahContextFactory.CreateDbContext();

            var asf = _context.Contract.First();

            return asf;
        }).WithOpenApi(operation => new(operation) { Summary = "测试接口" })
        .WithMetadata(new ApiMeta());

        group.MapPost("/wx/getschemelink", async (Model.Common.WX.GetWXAPPLink model, WeChatHelper helper) =>
        {
            if (string.IsNullOrEmpty(model.urladdress))
                model.urladdress = "pages/index/index";
            else
                model.urladdress = model.urladdress.TrimStart('/');

            string key = $"schemelink/NoExpire";
            string key_Filed = Md5Helper.Md5(model.urladdress + (model.query ?? string.Empty));

            var url = MyRedis.Client.HGet<Config.CommonModel.GetAdviserSchemelinkInfo?>(key, key_Filed);

            if (url == null)
            {
                var schemeLink = await helper.GetWxSchemeLinkForJump(ClientApps.SeekerApplet.AppID, model.urladdress, model.query ?? string.Empty, false);
                url = new Config.CommonModel.GetAdviserSchemelinkInfo
                {
                    query = model.query,
                    urladdress = model.urladdress,
                    Schemelink = schemeLink
                };
                MyRedis.Client.HSet<Config.CommonModel.GetAdviserSchemelinkInfo?>(key, key_Filed, url);
            }

            return url;
        })
       .WithMetadata(new ApiMeta())
       .WithOpenApi(operation => new(operation) { Summary = "获取永久有效的小程序Scheme链接" });
        group.MapPost("/createvoucherpdf", async (Config.CommonModel.QuestPDF.ContractModel2 pre, Infrastructure.CommonService.QuestPDF.CommonPDFService service) =>
        {
            var asf = await service.GenerateContractPdfToOss2Async(pre);

            return asf;
        }).WithOpenApi(operation => new(operation) { Summary = "测试生成凭证pdf文件接口" })
        .WithMetadata(new ApiMeta());

        // 生成京东家政邀约二维码
        group.MapPost("/generatejdqrcode", async (GenerateJdQrcodeRequest model, StaffingContext context,
        IHostEnvironment hostingEnvironment, WeChatHelper weChatHelper, LogManager logger) =>
        {
            var result = new GenerateJdQrcodeResponse();
            try
            {
                var postId = hostingEnvironment.IsProduction() ? Constants.JdPostIdProd : Constants.JdPostIdTest;
                if (model.key != skey)
                    result.Message = "无权限";

                if (string.IsNullOrWhiteSpace(model.jobNo))
                {
                    result.Message = "工号不能为空";
                    return result;
                }

                var userId = context.User_DingDing.Where(w => w.DingJobNo == model.jobNo)
                .Select(s => s.User_Hr.UserId).FirstOrDefault();

                if (string.IsNullOrWhiteSpace(userId))
                {
                    result.Message = "请先注册诺快聘并绑定该工号";
                    return result;
                }

                var teamPostId = context.Post_Team.Where(w => w.PostId == postId && w.Project_Team.HrId == userId)
                .Select(s => s.TeamPostId).FirstOrDefault();

                if (string.IsNullOrWhiteSpace(teamPostId))
                {
                    result.Message = "请先同步岗位后再来生成邀约二维码";
                    return result;
                }

                var redisKey = $"JdQrcode";
                var qrcode = MyRedis.Client.HGet<string?>(redisKey, userId);
                if (string.IsNullOrWhiteSpace(qrcode))
                {
                    var url = await RetryHelper.Do(async () => await weChatHelper.GetWxaCodeUnlimit(ClientApps.SeekerApplet.AppID, teamPostId, "Channel/resumeindex/jdindex"));
                    MyRedis.Client.HSet(redisKey, userId, url);
                    qrcode = url;
                }

                result.HrMobile = context.User_Hr.Where(w => w.UserId == userId).Select(s => s.User.Mobile).FirstOrDefault();

                result.Url = qrcode;
                result.Result = true;
            }
            catch (Exception e)
            {
                logger.Error("生成京东家政邀约二维码失败", Tools.GetErrMsg(e), model.jobNo ?? string.Empty);
                result.Message = "生成失败，请联系技术人员";
            }
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "生成京东家政邀约二维码" })
        .WithMetadata(new ApiMeta());

        //生成招募计划推广海报图，其中包含企微二维码
        group.MapPost("/templateimg/recruitmentplanpro", async (Certificate_RecruitmentplanproReq request,
           StaffingContext dbcontext,
           IOptions<ConfigManager> _config,
           Noah.Aliyun.Storage.IObjectStorage _objectStorageService,
           LogManager logger) =>
                {
                    (string qrCode, string QWUserID) = await GetQW_QRCode(request.AdviserMobile, dbcontext);

                    Config.CommonModel.Certificate.Certificate_ProImg_ForHR imgConfig = new()
                    {
                        QYWechatPosition_x = request.QYWechatPosition_x,
                        QYWechatPosition_y = request.QYWechatPosition_y,
                        QYWechatSize = request.QYWechatSize,
                    };//ImgHelper._Certificate_QWImgConfigConfig;

                    //组装拼接图片URL及位置
                    var Overlay = new List<ImgHelper_ImageOverlay2>
                    {
                        new(qrCode, imgConfig.QYWechatPosition_x, imgConfig.QYWechatPosition_y, imgConfig.QYWechatSize)
                    };

                    //合并图片
                    var stream = await ImgHelper.MergeImagesAsync(request.TemplatePath ?? @".\wwwroot\Certificate\qw_postertemplate.png", Overlay);

                    // 上传到OSS，并返回生成的图片URL
                    string newName = $"{QWUserID}.jpg";
                    string ossUrl = await _objectStorageService.OrdinaryUploadFile(stream, newName, newName, $"{_config.Value.Aliyun!.Oss!.Dir!}/recruitmentplan/promotionimg");
                    stream.Close();
                    stream.Dispose();
                    return ossUrl + "?timestamp=" + DateTime.Now.Ticks;

                    //context.Response.ContentType = "image/jpeg";
                    //context.Response.Headers.Append("Content-Disposition", "attachment; filename=output.jpg");
                    //await stream.CopyToAsync(context.Response.Body);
                    //stream.Close();
                    //stream.Dispose();
                })
        .WithOpenApi(operation => new(operation) { Summary = "生成顾问的证书推广图【仅含企微】" })
        .WithMetadata(new ApiMeta());

        //生成蓝领证书推广海报图，其中包含企微二维码
        group.MapPost("/templateimg/certificate", async (Certificate_GentoCerTificateReq request,
           StaffingContext dbcontext,
           IOptions<ConfigManager> _config,
           Noah.Aliyun.Storage.IObjectStorage _objectStorageService,
           LogManager logger) =>
        {
            string AdviserMobile = request.AdviserMobile;
            (string qrCode, string QWUserID) = await GetQW_QRCode(AdviserMobile, dbcontext);

            var imgConfig = ImgHelper._Certificate_QWImgConfigConfig;

            //组装拼接图片URL及位置
            var Overlay = new List<ImgHelper_ImageOverlay2>
                    {
                        new(qrCode, imgConfig.QYWechatPosition_x, imgConfig.QYWechatPosition_y, imgConfig.QYWechatSize)
                    };
            //组装文本
            string FontPath = @".\wwwroot\Certificate\msyh.ttf";
            var color = SixLabors.ImageSharp.Color.White;
            SixLabors.Fonts.FontStyle fontStyle = SixLabors.Fonts.FontStyle.Bold;
            List<ImgHelper_TextLayer> textLayers = [
                new ImgHelper_TextLayer
                {
                    Text = $"*咨询电话 {AdviserMobile.FormatPhoneNumber()}*",
                    FontPath = FontPath,
                    FontSize = imgConfig.Mobile_FontSize,
                    Color = color,
                    FontStyle = fontStyle,
                    Position = new SixLabors.ImageSharp.PointF(imgConfig.Mobile_PositionX, imgConfig.Mobile_PositionY)
                }
            ];

            //合并图片
            var stream = await ImgHelper.MergeImagesAsync(imgConfig.TemplatePath ?? @".\wwwroot\Certificate\qw_Certificate.jpg", Overlay, textLayers);

            // 上传到OSS，并返回生成的图片URL
            string newName = $"{QWUserID}.jpg";
            string ossUrl = await _objectStorageService.OrdinaryUploadFile(stream, newName, newName, $"{_config.Value.Aliyun!.Oss!.Dir!}/Certificate/qw_promotionimg");
            stream.Close();
            stream.Dispose();
            return ossUrl + "?timestamp=" + DateTime.Now.Ticks;

            //context.Response.ContentType = "image/jpeg";
            //context.Response.Headers.Append("Content-Disposition", "attachment; filename=output.jpg");
            //await stream.CopyToAsync(context.Response.Body);
            //stream.Close();
            //stream.Dispose();
        })
        .WithOpenApi(operation => new(operation) { Summary = "生成顾问的证书推广图【仅含企微】" })
        .WithMetadata(new ApiMeta());

        group.MapGet("/xac", async (string key, IDbContextFactory<StaffingContext> _contextFactory) =>
        {
            if (key != skey)
                throw new BadReadException("无权限");

            //最近一个月的订单
            var startTime = DateTime.Now.AddDays(-365);

            using var _context = _contextFactory.CreateDbContext();
            var fullRIds = _context.Post_Bounty
                //.Where(w => w.CreatedTime >= startTime && w.FollowerBounty > 0 && w.FollowerRate <= 0)
                .Where(w => w.CreatedTime >= startTime && w.Money * w.FollowerRate > 0 && w.FollowerRate <= 0)
                .Select(s => s.RecruitId).ToList();

            var settings = _context.Sys_Settings.FirstOrDefault() ?? throw new Exception("系统配置缺失");

            foreach (var recruitIds in fullRIds.Chunk(100))
            {
                using var context = _contextFactory.CreateDbContext();
                var recruits = context.Recruit
                    .Where(w => recruitIds.Contains(w.RecruitId)).ToList();

                var bountys = context.Post_Bounty
                .Where(w => recruitIds.Contains(w.RecruitId)).ToList();

                var recruitInfos = context.Recruit.Where(w => recruitIds.Contains(w.RecruitId))
                .Select(s => new
                {
                    s.ReceiverId,
                    s.Post_Delivery.Post.IsSalesCommission,
                    ClueEntSpecific = s.Post_Delivery.Post_Team.Project_Team.User_Hr.Enterprise.Specific,
                    ManagerEntSpecific = s.Post_Delivery.Post_Team.Project_Team.Project.User_Hr.Enterprise.Specific,
                    SalesEntSpecific = s.Post_Delivery.Post.Project.SaleUser.Enterprise.Specific,
                    FollowEntSpecific = s.FollowerUser.Enterprise.Specific
                }).ToList();

                foreach (var recruit in recruits)
                {
                    var recruitInfo = recruitInfos.FirstOrDefault(w => w.ReceiverId == recruit.ReceiverId);

                    var managerIsNoah = recruitInfo!.ManagerEntSpecific?.Contains(EntSpecific.Noah) == true;
                    var clueIsNoah = recruitInfo.ClueEntSpecific?.Contains(EntSpecific.Noah) == true;
                    var salesIsNoah = recruitInfo.SalesEntSpecific?.Contains(EntSpecific.Noah) == true;
                    var followIsNoah = recruitInfo.FollowEntSpecific?.Contains(EntSpecific.Noah) == true;

                    BountyConfig? managerConfig;
                    BountyConfig? clueConfig;
                    BountyConfig? salesConfig;
                    BountyConfig? followConfig;

                    if (recruitInfo.IsSalesCommission)
                    {
                        salesConfig = salesIsNoah ? settings.NoahFull : settings.PlatformFull;
                        managerConfig = managerIsNoah ? settings.NoahFull : settings.PlatformFull;
                        clueConfig = clueIsNoah ? settings.NoahFull : settings.PlatformFull;
                        followConfig = followIsNoah ? settings.NoahFull : settings.PlatformFull;
                    }
                    else
                    {
                        salesConfig = salesIsNoah ? settings.NoahNoSale : settings.PlatformNoSale;
                        managerConfig = managerIsNoah ? settings.NoahNoSale : settings.PlatformNoSale;
                        clueConfig = clueIsNoah ? settings.NoahNoSale : settings.PlatformNoSale;
                        followConfig = followIsNoah ? settings.NoahNoSale : settings.PlatformNoSale;
                    }

                    var bounty = bountys.FirstOrDefault(w => w.RecruitId == recruit.RecruitId);

                    if (bounty == null)
                        continue;

                    //bounty.SalesBounty = (bounty.Money * salesConfig!.Sales).ToFixed(2);
                    //bounty.ManagerBounty = (bounty.Money * managerConfig!.Manager).ToFixed(2);
                    //bounty.ClueBounty = (bounty.Money * clueConfig!.Clue).ToFixed(2);
                    bounty.SalesRate = salesConfig!.Sales;
                    bounty.ManagerRate = managerConfig!.Manager;
                    bounty.ClueRate = clueConfig!.Clue;
                    if (!string.IsNullOrEmpty(bounty.FollowerId))
                    {
                        //bounty.FollowerBounty = (bounty.Money * followConfig!.Follower).ToFixed(2);
                        bounty.FollowerRate = followConfig!.Follower;

                        // 计算平台佣金，减去销售佣金和经理佣金和线索佣金和邀面佣金
                        //var platformBounty = bounty.Money - bounty.SalesBounty - bounty.ManagerBounty - bounty.ClueBounty - bounty.FollowerBounty;
                        var platformBounty = bounty.Money * (1 - bounty.SalesRate - bounty.ManagerRate - bounty.ClueRate - bounty.FollowerRate);
                        var platformRate = bounty.Money <= 0 ? 0 : platformBounty / bounty.Money;
                        platformBounty = platformBounty.ToFixed(2);
                        platformRate = platformRate.ToFixed(2);
                    }
                }

                context.SaveChanges();

                await Task.Delay(100);
            }

        }).WithOpenApi(operation => new(operation) { Summary = "重新计算订单" })
        .WithMetadata(new ApiMeta());

        group.MapGet("/xac2", async (string key, IDbContextFactory<StaffingContext> _contextFactory) =>
        {
            if (key != skey)
                throw new BadReadException("无权限");

            //最近一个月的订单
            var startTime = DateTime.Now.AddDays(-30);

            using var _context = _contextFactory.CreateDbContext();
            var fullRIds = _context.Post_Bounty
                .Where(w => w.CreatedTime >= startTime)
                .Select(s => s.RecruitId).ToList();

            foreach (var recruitIds in fullRIds.Chunk(100))
            {
                using var context = _contextFactory.CreateDbContext();
                var recruits = context.Recruit
                    .Where(w => recruitIds.Contains(w.RecruitId)).ToList();

                var projectTeamBounty = context.Post_Bounty
                .Where(w => recruitIds.Contains(w.RecruitId)).ToList();

                var recruitInfo = context.Recruit.Where(w => recruitIds.Contains(w.RecruitId))
                .Select(s => new
                {
                    s.ReceiverId,
                    TeamHrId = s.Post_Delivery.Post_Team.Project_Team.HrId,
                }).ToList();

                foreach (var recruit in recruits)
                {
                    // 是否需要补充邀面人
                    if (recruit.Status != Config.Enums.RecruitStatus.HrScreening)
                    {
                        if (string.IsNullOrWhiteSpace(recruit.FollowerId))
                        {
                            var followerId = recruitInfo.FirstOrDefault(w => w.ReceiverId == recruit.ReceiverId)?.TeamHrId;
                            if (!string.IsNullOrWhiteSpace(followerId))
                            {
                                recruit.FollowerId = followerId;
                                recruit.AcceptOrder = Config.Enums.RecruitAcceptOrderEnum.已经接单;
                            }

                            var recruitBounty = projectTeamBounty.FirstOrDefault(w => w.RecruitId == recruit.RecruitId);
                            if (recruitBounty != null)
                            {
                                recruitBounty.FollowerId = recruit.FollowerId;
                            }
                        }
                    }
                }

                context.SaveChanges();
                await Task.Delay(100);
            }

        }).WithOpenApi(operation => new(operation) { Summary = "补充邀面人" })
        .WithMetadata(new ApiMeta());
    }
    /// <summary>
    /// 根据手机号获取企微二维码s
    /// </summary>
    /// <param name="AdviserMobile"></param>
    /// <param name="dbcontext"></param>
    /// <returns></returns>
    /// <exception cref="Infrastructure.Exceptions.BadRequestException"></exception>
    private async Task<(string qrCode, string QWUserID)> GetQW_QRCode(string AdviserMobile, StaffingContext dbcontext)
    {
        if (!AdviserMobile.IsMobile())
            throw new Infrastructure.Exceptions.BadRequestException($"手机号码输入有误，请检查后重试");
        string qrCode = string.Empty, QWUserID = string.Empty, DBAdviserId = string.Empty;

        var hr = dbcontext.User_Hr.Where(x => x.User.Mobile == AdviserMobile).FirstOrDefault();
        if (hr != null && !string.IsNullOrEmpty(hr.EntWeChatQrCode))
        {
            qrCode = hr.EntWeChatQrCode;
            QWUserID = hr.UserId;
        }
        else
        {
            var dbQW = dbcontext.QYWechat_Employee.Where(x => x.Mobile == AdviserMobile).FirstOrDefault();
            if (dbQW != null && !string.IsNullOrEmpty(dbQW.QRCodeURL))
            {
                qrCode = dbQW.QRCodeURL;
                QWUserID = dbQW.UserId;
            }
        }

        if (string.IsNullOrEmpty(qrCode))
        {
            var qwUserIdResult = await Infrastructure.QYWechat.QYWechatHelper.GetUserIdByMobile(AdviserMobile);
            if (!qwUserIdResult.Result || string.IsNullOrEmpty(qwUserIdResult.Userid))
                throw new Infrastructure.Exceptions.BadRequestException($"获取企微用户失败：请检查手机号码是否为绑定企微的手机号");
            var qwUserInfoResult = await Infrastructure.QYWechat.QYWechatHelper.GetEmployeeInfo(qwUserIdResult.Userid);
            if (!qwUserInfoResult.Result)
                throw new Infrastructure.Exceptions.BadRequestException($"获取企微用户信息失败：请检查企微状态是否正常");
            else
            {
                qrCode = qwUserInfoResult.Qr_Code!;
                QWUserID = qwUserInfoResult.Userid!;
            }
        }
        return (qrCode, QWUserID);
    }
}

public class GenerateJdQrcodeRequest
{
    public string? key { get; set; }
    public string? jobNo { get; set; }
}

public class GenerateJdQrcodeResponse
{
    /// <summary>
    /// 成功、失败
    /// </summary>
    public bool Result { get; set; } = false;

    /// <summary>
    /// 顾问电话
    /// </summary>
    public string? HrMobile { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 二维码地址
    /// </summary>
    public string? Url { get; set; }
}