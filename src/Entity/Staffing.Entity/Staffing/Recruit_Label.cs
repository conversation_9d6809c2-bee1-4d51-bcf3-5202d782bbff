﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 招聘流程对应标签表
/// </summary>
[Table("recruit_label")]
public class Recruit_Label
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 标签id
    /// </summary>
    public string DicLabelId { get; set; } = default!;

    public Recruit Recruit { get; set; } = default!;

    public Dic_Recruit_Label Dic_Recruit_Label { get; set; } = default!;
}

