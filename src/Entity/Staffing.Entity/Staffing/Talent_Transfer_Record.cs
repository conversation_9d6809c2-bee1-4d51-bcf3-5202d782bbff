﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 人才转移记录
/// </summary>
[Table("talent_transfer_record")]
public class Talent_Transfer_Record
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    public string TransferId { get; set; } = default!;

    public string SeekerId { get; set; } = default!;
}
