﻿using Config.Enums;
using Nest;

namespace Staffing.Entity.Elasticsearch;

[ElasticsearchType(IdProperty = "Id")]
public class EsTalentPlatform
{
    [Keyword]
    public string Id { get; set; } = default!;

    [Keyword]
    public string HrId { get; set; } = default!;

    [Keyword]
    public string SeekerId { get; set; } = default!;

    /// <summary>
    /// 渠道Id
    /// </summary>
    [Keyword]
    public string? ChannelId { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    [Keyword]
    public HrProjectSource TalentSource { get; set; }

    /// <summary>
    /// 用户等级
    /// </summary>
    [Keyword]
    public TalentPlatformLevel TalentLevel { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Keyword]
    public ActiveStatus TalentStatus { get; set; }

    /// <summary>
    /// 删除
    /// </summary>
    [Keyword]
    public bool TalentDeleted { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Date]
    public DateTime TalentCreatedTime { get; set; }

    /// <summary>
    /// 登录时间
    /// </summary>
    [Date]
    public DateTime SeekerLoginTime { get; set; }

    /// <summary>
    /// 访问时间
    /// </summary>
    [Date]
    public DateTime SeekerVisitTime { get; set; }

    /// <summary>
    /// 求职者电话
    /// </summary>
    [Wildcard]
    public string SeekerMobile { get; set; } = default!;

    /// <summary>
    /// 求职者身份证
    /// </summary>
    [Text(Index = false)]
    public string SeekerIdentityCard { get; set; } = default!;

    /// <summary>
    /// 求职者身份证名称
    /// </summary>
    [Keyword]
    public string SeekerIdentityCardName { get; set; } = default!;

    /// <summary>
    /// 求职者姓名
    /// </summary>
    [Wildcard]
    public string SeekerNickName { get; set; } = default!;

    /// <summary>
    /// 求职者头像
    /// </summary>
    [Text(Index = false)]
    public string? SeekerAvatar { get; set; }

    /// <summary>
    /// 注册源
    /// </summary>
    [Keyword]
    public RegisterSource SeekerSource { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Keyword]
    public UserStatus SeekerStatus { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    [Wildcard]
    public string? SeekerRegionId { get; set; } = string.Empty;

    /// <summary>
    /// 职业
    /// </summary>
    [Keyword]
    public OccupationType? SeekerOccupation { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [Keyword]
    public Sex? SeekerSex { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    [Keyword]
    public EducationType? SeekerEducation { get; set; }

    /// <summary>
    /// 学校
    /// </summary>
    [Wildcard]
    public string? SeekerSchool { get; set; }

    /// <summary>
    /// 毕业日期
    /// </summary>
    [Date]
    public DateTime? SeekerGraduationDate { get; set; }

    /// <summary>
    /// 专业
    /// </summary>
    [Wildcard]
    public string? SeekerMajor { get; set; }

    /// <summary>
    /// 简历完善度
    /// </summary>
    [Keyword]
    public int SeekerScore { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    [Date]
    public DateTime? SeekerBirthday { get; set; }

    /// <summary>
    /// 显示
    /// </summary>
    [Keyword]
    public bool SeekerShow { get; set; }

    /// <summary>
    /// 匿名（暂不用）
    /// </summary>
    [Keyword]
    public bool SeekerAnonymous { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [Date]
    public DateTime SeekerUpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 检索关键字
    /// </summary>
    [Wildcard]
    public List<string> SeekerSearch { get; set; } = default!;

    /// <summary>
    /// hr小程序简历二维码
    /// </summary>
    [Text(Index = false)]
    public string? HrAppletQrCode { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [Date]
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 自定义标签
    /// </summary>
    [Keyword]
    public List<string> Tags { get; set; } = default!;

    /// <summary>
    /// 求职期望
    /// </summary>
    [Keyword]
    public List<string>? DesiredPost { get; set; }

    /// <summary>
    /// 期望职位
    /// </summary>
    [Wildcard]
    public List<string>? DesiredPostName { get; set; }


    /// <summary>
    /// 他的工作经历
    /// </summary>
    [Nested]
    public List<EsSeekerWork> Works { get; set; } = new List<EsSeekerWork>();

    [Keyword]
    public List<string>? Wlgq { get; set; }
}

public class EsSeekerWork
{
    /// <summary>
    /// 公司
    /// </summary>
    [Wildcard]
    public string Company { get; set; } = default!;

    /// <summary>
    /// 职务
    /// </summary>
    [Wildcard]
    public string Post { get; set; } = default!;

    /// <summary>
    /// 工作开始时间
    /// </summary>
    [Date]
    public DateTime? BeginDate { get; set; }

    /// <summary>
    /// 工作结束时间
    /// </summary>
    [Date]
    public DateTime? EndDate { get; set; }
}