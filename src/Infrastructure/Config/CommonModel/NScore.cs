using Config.Enums;

namespace Config.CommonModel;

public class NScoresStoreInfo
{
    /// <summary>
    /// 类型
    /// </summary>
    public NScoreType Type { get; set; }

    /// <summary>
    /// 积分
    /// </summary>
    public int Score { get; set; }

    /// <summary>
    /// hr还是求职者
    /// </summary>
    public SeekerOrHr UserType { get; set; } = SeekerOrHr.Seeker;

    // /// <summary>
    // /// 开启
    // /// </summary>
    // public bool Active { get; set; } = true;

    /// <summary>
    /// 端口
    /// </summary>
    public int Port { get; set; } = 0;
}

public class GetPushPostSettingsInfo
{
    /// <summary>
    /// Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// 公司
    /// </summary>
    public string? Company { get; set; }
}

public enum NScoreType
{
    注册, 每日登录, 首次报名, 分享, 首次入职, 分享好友登录, 分享好友注册, 分享好友报名, 分享好友入职,
    发布职位 = 50,
    协同项目,
    兑换商品 = 101
}

public class GetSeekerShareQrCodeInfo
{
    /// <summary>
    /// 分享人
    /// </summary>
    public string? ShareUserId { get; set; }

    /// <summary>
    /// 渠道Id
    /// </summary>
    public string? ChannelId { get; set; }

    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// 小程序二维码
    /// </summary>
    public string? QrCode { get; set; }
}
public class GetAdviserShareCerQrCodeInfo
{
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? AdviserId { get; set; }

    /// <summary>
    /// 小程序二维码
    /// </summary>
    public string? QrCode { get; set; }
}

public class GetAdviserSchemelinkInfo
{
    /// <summary>
    /// 页面地址(不以/开头，为空时默认进入小程序首页)
    /// </summary>
    public string? urladdress { get; set; }
    /// <summary>
    /// 参数
    /// </summary>
    public string? query { get; set; }

    /// <summary>
    /// 小程序二维码
    /// </summary>
    public string? Schemelink { get; set; }
}