﻿using System.ComponentModel;

namespace Config.Enums;

//项目级别
public enum ProjectLevel
{
    一般项目, 重点项目
}

//项目类型
public enum ProjectType
{
    人才派遣, 业务外包, 岗位外包, 委托招聘, 猎头服务
}

//项目行业
public enum ProjectIndustry
{
    // 生物医药, 服务终端, 金融服务, 电子信息, 高端制造, 政府事业单位, 其他 = 99
    生物医药, 生活服务, 金融服务, 电子信息, 高端制造, 政府事业单位, 互联网, 房地产, 工程施工, 物流仓储, 教育培训, 能源化工, 农林牧渔, 消费品, 环保行业, 文体娱乐, 社会组织, 企业服务, 个体商户, 其他 = 99
}

//工作周期类型
public enum ProjectWorkCycleType
{
    日结用工, 短期用工, 长期用工, 周末用工, 全职用工, 寒假工, 暑假工
}

//结算方式
public enum ProjectClearingType
{
    日结, 周结, 月结, 完工结算, 其他结算 = 99
}

public enum PostInterviewTimeStatus
{
    取消预约, 可预约
}

//工作性质
public enum PostWorkNature
{
    [Description("全职")]
    全职 = 0,
    [Description("兼职")]
    兼职 = 1,
    [Description("实习")]
    实习 = 2,
    [Description("应届毕业生")]
    应届毕业生 = 3
}

//薪资类型
public enum PostSalaryType
{
    面议 = 0,
    月薪 = 1,
    年薪 = 2,
    周薪 = 3,
    日薪 = 4,
    时薪 = 5
}

public enum PostSettlementType
{
    月结 = 0,
    日结 = 1,
    周结 = 2,
    完工结 = 3
}

//合作模式
public enum ProjectPaymentNode
{
    入职过保, 简历交付, 到面交付, 按天入职过保
}

//打款方式
public enum ProjectPaymentType
{
    周期结算, 实时结算, 内部结算, 对公结算
}

public enum PostRewardType
{
    单次结算, 长期结算
}

public enum PostPaymentCycle
{
    月, 天, 小时, 件
}

//结算渠道
public enum ProjectClearingChannel
{
    诺快聘结算, 银行结算
}

//hr项目类型
public enum HrProjectType
{
    自己, 协同
}

public enum ProjectTeamConfigType
{
    众包, 指定人
}


public enum ProjectTeamIsClues
{
    否, 是
}

public enum ClueTagEnums
{
    [Description("新线索")]
    新线索,
    [Description("指定线索")]
    指定线索,
    [Description("计费过")]
    计费过,
    [Description("简历需审")]
    简历需审,
    [Description("面试通过率高")]
    面试通过率高,
    [Description("调研资料多")]
    调研资料多
}

//项目状态
public enum ProjectStatus
{
    关闭, 已上线, 归档 = 9, 待审核 = 11
}

//职位状态
public enum PostStatus
{
    关闭, 发布中, 待审核
}

//职位优选状态
public enum ExcellentPostStatus
{
    [Description("取消")]
    Canceled = -1,

    [Description("待审核")]
    UnReviewed = 0,

    [Description("审核拒绝")]
    Rejected = 1,

    [Description("审核通过")]
    Active = 6
}

//项目、职位来源
public enum HrProjectSource
{
    [Description("诺快聘")]
    自己项目, 平台协同
}

//领带类型
public enum TieType
{
    蓝领, 白领
}

public enum TeamBountySource
{
    其他,
    抖音小程序,
    微信小程序,
    快招工,
    快手小程序,
    简历上传,
    诺聘平台
}

public enum BountySource
{
    渠道, 协同
}

//项目成员状态
public enum ProjectMemberStatus
{
    待入职, 入职, 离职 = 9
}

//项目成员来源
public enum ProjectMemberSource
{
    手动添加, 招聘流程, 数字诺亚, Excel导入, 扫码进入, 编码进入
}

//用工形式
public enum ProjectMemberEmploymentMode
{
    [Description("合同制")]
    合同制,

    [Description("聘用制")]
    聘用制,

    [Description("临时工")]
    临时工,

    [Description("非全日制")]
    非全日制,

    [Description("其他")]
    其他
}

public enum ProjectMemberImportEntryType
{
    Excel模板, 数字诺亚
}

//项目成员离职状态
public enum ProjectMemberQuitStatus
{
    未离职, 离职, 待离职 = 9,
}

//项目成员记录类型
public enum ProjectMemberRecordType
{
    待入职, 入职, 离职, 待离职, 取消离职
}

//任务类型
public enum TaskHandlingType
{
    项目成员入职, 项目成员离职 = 9,
    职位邀约短信 = 10,
    导出 = 100
}

//项目成员任务状态
public enum TaskHandlingStatus
{
    进行中, 失败 = 9, 已完成 = 10
}

//任务结果
public enum TaskHandlingResult
{
    失败, 成功 = 1
}

public enum TaskHandlingGroupStatus
{
    默认, 重复
}

// public enum ProjectContractSignType
// {
//     销售易, 电子签
// }

public enum ProjectContractSource
{
    销售易, 电子签
}

public enum ProjectContractStatus
{
    创建中, 已完成
}

public enum BountyStatus
{
    交付中,
    结束 = 9
}

public enum GuaranteeStatus
{
    交付中,
    未过保, // 满足交付条件，但还未到过保时间
    过保
}

// 阶段分润状态
public enum BountyStageStatus
{
    交付中,
    交付完成 = 9
}

public enum PostSettleBountyType
{
    内部, 外部, 个人, 平台 = 100
}

public enum PostSettleUserType
{
    销售, 项目经理, 线索, 邀面,
    平台 = 100
}

public enum PostSettleStatus
{
    进行中, 完成 = 10 //暂时不用此状态
}

public enum PostSettleApprovalRole
{
    项目经理审核, 平台审核, 财务审核, 非项目经理审核
}

public enum AuditStatus
{
    通过 = 1, 拒绝
}

public enum PostSettleApprovalStatus
{
    项目经理审核, 平台审核, 财务审核,
    审核拒绝 = 9, 审核完成 = 10, 无效结算单 = 11
}

public enum PaymentStatus
{
    未打款, 打款中, 打款成功, 打款失败 = 9
}

// //分润状态
// public enum ProjectTeambountyStatus
// {
//     交付中, 结算中,
//     结算完成 = 6,
//     交付失败 = 9,
//     // 售后中 = 10,
//     // 计费待确认 = 11
// }

public enum SettlementType
{
    待结算,
    已结算
}

public enum DeliverStatus
{
    [Description("已结束")]
    取消,

    [Description("已报名")]
    报名,

    [Description("待面试")]
    面试,

    [Description("已录取")]
    录取,

    [Description("已签约")]
    签约
}

public enum SeekerDeliveryStatus
{
    未报名, 已报名, 已归档, 已取消
}

public enum SignContractType
{
    线上平台合同签约, 平台承接指定模版, 线上合同上传
}
public enum ContractType
{
    与第三方劳务签订合同,
    与用工单位签订合同
}

public enum AssignedPersonEnum
{
    指定项目经理, 不需要指定
}

public class DeliverStatusTools
{
    public static DeliverStatus RecruitStatusToDeliverStatus(RecruitStatus? st)
    {
        return st switch
        {
            RecruitStatus.HrScreening => DeliverStatus.报名,
            RecruitStatus.InterviewerScreening => DeliverStatus.报名,
            RecruitStatus.Interview => DeliverStatus.面试,
            RecruitStatus.Induction => DeliverStatus.录取,
            RecruitStatus.Offer => DeliverStatus.录取,
            RecruitStatus.Contract => DeliverStatus.签约,
            RecruitStatus.FileAway => DeliverStatus.取消,
            _ => DeliverStatus.报名
        };
    }

    public static SeekerDeliveryStatus RecruitStatusToSeekerDeliveryStatus(RecruitStatus? st, RecruitFileAway? fst)
    {
        return st switch
        {
            RecruitStatus.HrScreening => SeekerDeliveryStatus.已报名,
            RecruitStatus.InterviewerScreening => SeekerDeliveryStatus.已报名,
            RecruitStatus.Interview => SeekerDeliveryStatus.已报名,
            RecruitStatus.Induction => SeekerDeliveryStatus.已报名,
            RecruitStatus.Offer => SeekerDeliveryStatus.已报名,
            RecruitStatus.Contract => SeekerDeliveryStatus.已报名,
            RecruitStatus.FileAway => fst == RecruitFileAway.CandidateAbandonment ? SeekerDeliveryStatus.已取消 : SeekerDeliveryStatus.已归档,
            _ => SeekerDeliveryStatus.未报名
        };
    }
}