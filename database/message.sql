-- ----------------------------
-- 消息常用语
-- ----------------------------
DROP TABLE IF EXISTS `msg_words`;
CREATE TABLE `msg_words` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL,
  `Type` int(2) NOT NULL COMMENT '类型，0=求职者，1=hr',
  `Content` varchar(1024) CHARACTER SET utf8mb4 NOT NULL COMMENT '内容',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`id`),
  KEY `idx_msg_words_UserId` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- Hr站内消息
-- ----------------------------
DROP TABLE IF EXISTS `msg_notify_hr`;
CREATE TABLE `msg_notify_hr` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL,
  `Type` int(2) NOT NULL COMMENT '消息类型',
  `Title` varchar(100) CHARACTER SET utf8mb4 NOT NULL COMMENT '标题',
  `Content` varchar(2048) CHARACTER SET utf8mb4 NOT NULL COMMENT '内容',
  `Data` varchar(5000) NULL COMMENT '数据json',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `idx_msg_notify_hr_UserIdFull` (`UserId`,`Type`,`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 钉钉推送消息记录
-- ----------------------------
DROP TABLE IF EXISTS `msg_dingdingpush`;
CREATE TABLE `msg_dingdingpush` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL COMMENT 'userid',
  `DingUserid` varchar(100) NOT NULL DEFAULT '' COMMENT '用户钉钉userid',
  `CreateProject` int(8) NOT NULL DEFAULT 0 COMMENT '创建项目(进行中）',
  `EnableCollaboration` int(8) NOT NULL DEFAULT 0 COMMENT '开启协同（集团级）',
  `CollaborativeProject` int(8) NOT NULL DEFAULT 0 COMMENT '协同项目',
  `RegistrablePositions` int(8) NOT NULL DEFAULT 0 COMMENT '可报名职位',
  `ScreeningNum` int(8) NOT NULL DEFAULT 0 COMMENT '初筛',
  `InterviewNum` int(8) NOT NULL DEFAULT 0 COMMENT '面试安排',
  `MessageNoReply` int(8) NOT NULL DEFAULT 0 COMMENT '即时通讯未回复',
  `CoordinationDelivery` int(8) NOT NULL DEFAULT 0 COMMENT '协同交付中',
  `CoordinationSuccess` int(8) NOT NULL DEFAULT 0 COMMENT '协同交付成功',
  `CoordinationFail` int(8) NOT NULL DEFAULT 0 COMMENT '协同交付失败',
  `ReserveVirtual` int(8) NOT NULL DEFAULT 0 COMMENT '简历储备',
  `ReservePlatform` int(8) NOT NULL DEFAULT 0 COMMENT '真实用户',
  `CloudOffice` varchar(1000) NOT NULL DEFAULT '' COMMENT '云办公',
  `PushTime` datetime(6) COMMENT '消息推送时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`id`),
  KEY `idx_msg_dingdingpush_UserId` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;