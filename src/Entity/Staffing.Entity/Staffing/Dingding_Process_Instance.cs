﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 钉钉审批流
/// </summary>
[Table("dingding_process_instance")]
public class Dingding_Process_Instancet
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();
    /// <summary>
    /// 审批模板的唯一码
    /// </summary>
    public string ProcessCode { get; set; } = default!;
    /// <summary>
    /// 审批实例id
    /// </summary>
    public string ProcessInstanceId { get; set; } = default!;
    public string? BussinessId { get; set; }
    public List<string>? ApproverUserIds { get; set; }
    public string? BizAction { get; set; }
    public string? BizData { get; set; }
    public List<string>? CcUserIds { get; set; }
    public List<FormComponentValues>? FormComponentValues { get; set; }
    public List<OperationRecords>? OperationRecords { get; set; }
    /// <summary>
    /// 实例创建时间
    /// </summary>
    public string? CreateTime { get; set; }
    public string? OriginatorDeptId { get; set; }
    public string? OriginatorDeptName { get; set; }
    public string? OriginatorUserId { get; set; }
    public string? FinishTime { get; set; }
    public string? Result { get; set; }
    public string? Status { get; set; }
    public List<DingdingTasks>? Tasks { get; set; }
    /// <summary>
    /// 实例标题
    /// </summary>
    public string? Title { get; set; }
    /// <summary>
    /// 事件类型
    /// </summary>
    public string? EventType { get; set; }
    /// <summary>
    /// 审批实例对应的企业
    /// </summary>
    public string? CorpId { get; set; }
    /// <summary>
    /// 类型，type为start表示审批实例开始
    /// </summary>
    public string? Type { get; set; }
    /// <summary>
    /// 审批实例url，可在钉钉内跳转到审批页面
    /// </summary>
    public string? Url { get; set; }
    /// <summary>
    /// 发起审批实例的员工
    /// </summary>
    public string? StaffId { get; set; }

    public DateTime UpdatedTime { get; set; }
    public DateTime CreatedTime { get; set; }
}

public class FormComponentValues
{
    public string? Id { get; set; }
    public string? Name { get; set; }
    public string? Value { get; set; }
}

public class OperationRecords
{
    public string? UserId { get; set; }
    public string? Date { get; set; }
    /// <summary>
    /// 操作类型
    /// EXECUTE_TASK_NORMAL：正常执行任务
    /// EXECUTE_TASK_AGENT：代理人执行任务
    /// APPEND_TASK_BEFORE：前加签任务
    /// APPEND_TASK_AFTER：后加签任务
    /// REDIRECT_TASK：转交任务
    /// START_PROCESS_INSTANCE：发起流程实例
    /// TERMINATE_PROCESS_INSTANCE：终止(撤销)流程实例
    /// FINISH_PROCESS_INSTANCE：结束流程实例
    /// ADD_REMARK：添加评论
    /// REDIRECT_PROCESS：审批退回
    /// PROCESS_CC：抄送
    /// </summary>
    public string? Type { get; set; }
    /// <summary>
    /// 操作结果
    /// AGREE：同意
    /// REFUSE：拒绝
    /// NONE：未处理
    /// </summary>
    public string? Result { get; set; }
    /// <summary>
    /// 评论内容
    /// </summary>
    public string? Remark { get; set; }
    /// <summary>
    /// 评论附件列表
    /// </summary>
    public List<Attachments>? Attachments { get; set; }
    /// <summary>
    /// 抄送人userIds列表
    /// </summary>
    public List<string>? CcUserIds { get; set; }
}

public class Attachments
{
    public string? FileName { get; set; }
    public string? FileSize { get; set; }
    public string? FileId { get; set; }
    public string? FileType { get; set; }
}

public class DingdingTasks
{
    public long? TaskId { get; set; }
    public string? UserId { get; set;}
    /// <summary>
    /// 任务状态
    /// NEW：未启动
    /// RUNNING：处理中
    /// PAUSED：暂停
    /// CANCELED：取消
    /// COMPLETED：完成
    /// TERMINATED：终止
    /// </summary>
    public string? Status { get; set;}
    /// <summary>
    /// 结果
    /// AGREE：同意
    /// REFUSE：拒绝
    /// REDIRECTED：转交
    /// </summary>
    public string? Result { get; set;}
    public string? CreateTime { get; set;}
    public string? FinishTime { get; set;}
    /// <summary>
    /// 移动端任务URL
    /// </summary>
    public string? MobileUrl { get; set;}
    /// <summary>
    /// PC端任务URL
    /// </summary>
    public string? PcUrl { get; set;}
    public string? ProcessInstanceId { get; set;}
    public string? ActivityId { get; set;}
}