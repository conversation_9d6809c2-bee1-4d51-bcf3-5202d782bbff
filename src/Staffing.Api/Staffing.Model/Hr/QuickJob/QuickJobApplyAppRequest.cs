﻿using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;

namespace Staffing.Model.Hr.QuickJob;


public class QuickJobAuditListRequest: QueryRequest
{
    /// <summary>
    /// 用户状态
    /// </summary>
    public AppApplyStatus? AppApplyStatus { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? CompanyInfo { get; set; }

    /// <summary>
    /// 电话号码
    /// </summary>
    public string? Mobile { get; set; }
}

public class QuickJobAuditRequest
{
    /// <summary>
    /// 主键
    /// </summary>
    public string Id { get; set; } = default!;

    /// <summary>
    /// 状态
    /// </summary>
    public AppApplyStatus Status { get; set; } = AppApplyStatus.审核通过;

    /// <summary>
    /// 拒绝理由
    /// </summary>
    public string? RejectReason { get; set; }
}

public class QuickJobApplyAppRequest
{
    /// <summary>
    /// 项目行业 = 项目执行/发布项目（项目行业）/ 归属产业
    /// </summary>
    public ProjectIndustry? Industry { get; set; }

    /// <summary>
    /// 公司所属行业
    /// </summary>
    public int? CompanyIndustry { get; set; }

    /// <summary>
    /// 联系人姓名 = 项目执行 /编辑名片（姓名）
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 担任职位
    /// </summary>
    public string? PositionName { get; set; }

    /// <summary>
    /// 招聘职位名称 - name
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 职位类别 - id
    /// </summary>
    public int? Category { get; set; }

    /// <summary>
    /// 零工市场ID可多选（101,102）
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// 联系人手机 = 项目执行 /编辑名片（有手机号码不可修改）
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 联系人电子邮箱 = 项目执行 /编辑名片（邮箱）
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// 对接单位
    /// </summary>
    public string? ContactCompany { get; set; }

    /// <summary>
    /// 推荐码
    /// </summary>
    public string? ReferralCode { get; set; }

    /// <summary>
    /// 营业执照
    /// </summary>
    public string? CompanyLicense { get; set; }

    /// <summary>
    /// 公司信息 = 项目执行/发布项目（代招企业，有个新增企业，可以看到，展示名称是真实展示）
    /// </summary>
    public string? CompanyInfo { get; set; }

    /// <summary>
    /// 纳税人类型
    /// </summary>
    public TaxpayerType TaxpayerType { get; set; }

    /// <summary>
    /// 企业logo
    /// </summary>
    public string LogoUrl { get; set; } = string.Empty;

    /// <summary>
    /// 公司名称
    /// </summary>
    public string CompanyName { get; set; } = default!;

    /// <summary>
    /// 公司简称
    /// </summary>
    public string CompanyAbbr { get; set; } = string.Empty;

    /// <summary>
    /// 工作地址
    /// </summary>
    public string CompanyAddress { get; set; } = string.Empty;

    /// <summary>
    /// 工作地址坐标 - X
    /// </summary>
    public double? Lat { get; set; }

    /// <summary>
    /// 工作地址坐标 - Y
    /// </summary>
    public double? Lng { get; set; }

    /// <summary>
    /// 企业性质
    /// </summary>
    public EnterpriseNature Nature { get; set; } = EnterpriseNature.个体工商户;

    /// <summary>
    /// 企业规模
    /// </summary>
    public EnterpriseScale Scale { get; set; } = EnterpriseScale.一百至五百;

    /// <summary>
    /// 融资阶段
    /// </summary>
    public EnterpriseCapital Capital { get; set; } = EnterpriseCapital.不需要融资;

    /// <summary>
    /// 地区ID
    /// </summary>
    public string RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    /// 职位信息
    /// </summary>
    public PostInfo? PostInfo { get; set; }
}


public class NewUserInfo
{
    /// <summary>
    /// 主键ID，例：101
    /// </summary>
    public string MainId { get; set; } = default!;

    /// <summary>
    /// 名称，例：张家口
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 手机
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 诺聘ID
    /// </summary>
    public string? NuoId { get; set; }

    /// <summary>
    /// 用户性别
    /// </summary>
    public Sex Sex { get; set; } = Sex.男;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set;}

    /// <summary>
    /// 地区id
    /// </summary>
    public string? RegionId { get; set; }
}
