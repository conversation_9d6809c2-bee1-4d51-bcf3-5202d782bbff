namespace Config.CommonModel.KuaiShou;

public class GetKsDicModel
{
    public string? typeCode { get; set; }
    public string? code { get; set; }
    public string? name { get; set; }
    public string? parentCode { get; set; }
    public List<GetKsDicModel>? children { get; set; }
}

public class GetKsProvince
{
    public string? provinceName { get; set; }
    public string? provinceCode { get; set; }
    public List<GetKsCity>? cityList { get; set; }
}

public class GetKsCity
{
    public string? cityName { get; set; }
    public string? cityCode { get; set; }
    public List<GetKsArea>? areaList { get; set; }
}

public class GetKsArea
{
    public string? areaName { get; set; }
    public string? areaCode { get; set; }
}

public class KsDicTree
{
    /// <summary>
    /// 类型code
    /// </summary>
    public string? typeCode { get; set; }

    /// <summary>
    /// code
    /// </summary>
    public string? code { get; set; }
}

public class KsLocation
{
    /// <summary>
    /// 省份编码
    /// </summary>
    public string? provinceCode { get; set; }

    /// <summary>
    /// 城市编码
    /// </summary>
    public string? cityCode { get; set; }

    /// <summary>
    /// 地区编码
    /// </summary>
    public string? areaCode { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string? address { get; set; }
}

public class KsCompany
{
    /// <summary>
    /// 企业简介
    /// </summary>
    public string? introduction { get; set; }

    /// <summary>
    /// 企业工商code
    /// </summary>
    public string? businessCode { get; set; }

    /// <summary>
    /// 企业工商注册名
    /// </summary>
    public string? businessName { get; set; }

    /// <summary>
    /// 企业规模
    /// </summary>
    public string? scale { get; set; }

    /// <summary>
    /// 企业位置
    /// </summary>
    public KsLocation? location { get; set; }

    /// <summary>
    /// 天眼查企业id
    /// </summary>
    public string? tycEntId { get; set; }
}