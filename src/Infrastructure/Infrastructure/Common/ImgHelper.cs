﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Flurl.Http;
using DocumentFormat.OpenXml.Office.CustomUI;
using SixLabors.Fonts;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Drawing.Processing;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;
using System.Runtime.InteropServices;
using Infrastructure.Extend;
using DocumentFormat.OpenXml.InkML;
using DocumentFormat.OpenXml.Drawing.Charts;
using Config.CommonModel.Certificate;
using static Config.Constants;

namespace Infrastructure.Common
{
    public class ImgHelper
    {
        /// <summary>
        /// 证书模板图配置
        /// </summary>
        public static readonly Certificate_ProImg_SplicingTemplate _Certificate_TemplateImgConfig = new()
        {
            Top_TemplatePath = Path.Combine(AppContext.BaseDirectory, "TempFile", "Certificate", "postertemplateTop.jpg"),
            Bottom_TemplatePath = Path.Combine(AppContext.BaseDirectory, "TempFile", "Certificate", "postertemplateBottom.jpg"),
            Table_TR_TemplatePath = Path.Combine(AppContext.BaseDirectory, "TempFile", "Certificate", "postertemplateTableTr.jpg"),
            FontTTFPath = Path.Combine(AppContext.BaseDirectory, "TempFile", "Certificate", "msyh.ttf"),
            GetCerCount = 50,
            CerFontSize = 18,
            CerFont_HR_Height = 61,
            CerFont_FirstPositionY = 287,
            CerFont_Num_PositionX = 18,
            CerFont_CertificateName_PositionX = 75,
            CerFont_CertificateType_PositionX = 290,
            CerFont_Specs_PositionX = 530,
            CerFont_IssuingAuthority_PositionX = 1000,
            CerFont_Price_PositionX = 1430
        };
        /// <summary>
        /// HR证书推广图配置
        /// </summary>
        public static readonly Certificate_ProImg_ForHR _Certificate_AdviserImgConfigConfig = new()
        {
            QrCodePosition_x = 1340,
            QrCodePosition_y = -207,
            QRSize = 165,
            QYWechatPosition_x = 48,
            QYWechatPosition_y = -210,
            QYWechatTipPosition_x = 0,
            QYWechatSize = 165
        };

        /// <summary>
        /// 根据企微生成证书推广图配置
        /// </summary>
        public static readonly Certificate_ProImg_ForHR_QW _Certificate_QWImgConfigConfig = new()
        {
            QYWechatPosition_x = 67,
            QYWechatPosition_y = 2395,
            QYWechatSize = 290,
            Mobile_FontSize = 35,
            Mobile_PositionX = 530,
            Mobile_PositionY = 2585,
            TemplatePath = null//"https://resources.nuopin.cn/staffing/Certificate/promotionpage/bg1.jpg"
        };

        /// <summary>
        /// 下载图片并返回图片的base64
        /// </summary>
        /// <param name="imageUrl"></param>
        /// <returns></returns>
        public static async Task<string> DownloadImageAsBase64(string imageUrl)
        {
            var imageBytes = await imageUrl.GetAsync().ReceiveBytes();
            return Convert.ToBase64String(imageBytes);
        }

        /// <summary>
        /// 拼接图片 + 添加文字 + 覆盖图片
        /// </summary>
        public static Stream CombineImagesWithOverlay(
            List<ImgHelper_InputPaths> inputPaths,
            List<ImgHelper_TextLayer>? textLayers,     // 文字层配置
            List<ImgHelper_ImageOverlay>? overlays,    // 覆盖图片配置
            bool horizontal = false)
        {
            List<string> inputPathsTemp = [];
            foreach (var item in inputPaths)
            {
                string ImgUrl = item.ImgUrl.ReplaceFilePath();
                if (item.UseCount == 1)
                    inputPathsTemp.Add(ImgUrl);
                else
                    inputPathsTemp.AddRange(Enumerable.Range(0, item.UseCount).Select(s => ImgUrl));
            }
            //inputPathsTemp = inputPaths.Select(x => x.ImgUrl).Distinct().ToList();
            // 1. 加载所有原始图片
            List<Image> images = inputPathsTemp.Select(Image.Load).ToList();

            // 2. 计算画布尺寸
            int canvasWidth = horizontal ? images.Sum(img => img.Width) : images.Max(img => img.Width);
            int canvasHeight = horizontal ? images.Max(img => img.Height) : images.Sum(img => img.Height);

            using var canvas = new Image<Rgba32>(canvasWidth, canvasHeight);
            // 3. 拼接原始图片
            int offset = 0;
            foreach (var img in images)
            {
                var position = horizontal ? new Point(offset, 0) : new Point(0, offset);
                canvas.Mutate(ctx => ctx.DrawImage(img, position, 1f));
                offset += horizontal ? img.Width : img.Height;
                img.Dispose();
            }

            // 4. 添加所有文字层
            if (textLayers != null)
            {
                Dictionary<string, Font> fontDic = [];
                foreach (var textLayer in textLayers)
                {
                    if (string.IsNullOrEmpty(textLayer.Text))
                        continue;

                    var font = LoadFont(fontDic, textLayer.FontPath, textLayer.FontSize, textLayer.FontStyle ?? FontStyle.Regular);
                    var textOptions = new RichTextOptions(font)
                    {
                        Origin = textLayer.Position,
                        HorizontalAlignment = HorizontalAlignment.Left,
                        VerticalAlignment = VerticalAlignment.Top
                    };

                    canvas.Mutate(ctx => ctx.DrawText(
                        textOptions,
                        textLayer.Text.Trim(),
                        textLayer.Color
                    ));
                }
            }

            // 5. 叠加覆盖图片
            if (overlays != null)
                foreach (var overlay in overlays)
                {
                    using var overlayImage = Image.Load(overlay.ImagePath);
                    canvas.Mutate(ctx => ctx.DrawImage(
                        overlayImage,
                        overlay.Position,
                        overlay.Opacity // 0-1透明度
                    ));
                }

            // 6. 保存最终图片
            var memoryStream = new MemoryStream();
            canvas.Save(memoryStream, new JpegEncoder()); // 保存到内存流
            memoryStream.Position = 0; // 重置流位置
            return memoryStream;
        }

        /// <summary>
        /// 合并图片
        /// </summary>
        /// <param name="templateUrl"></param>
        /// <param name="overlayImgUrls"></param>
        /// <returns></returns>
        public static async Task<Stream> MergeImagesAsyncForPath(string templateFilePath, List<ImgHelper_ImageOverlay2>? overlayImgUrls, List<ImgHelper_TextLayer>? textLayers = null)
        {
            // 加载模板图  
            var templateImage = await Image.LoadAsync(templateFilePath);

            return await MergeImagesAsyncFun(templateImage, overlayImgUrls, textLayers);
        }

        /// <summary>
        /// 合并图片
        /// </summary>
        /// <param name="templateUrl"></param>
        /// <param name="overlayImgUrls"></param>
        /// <returns></returns>
        public static async Task<Stream> MergeImagesAsync(string templateUrl, List<ImgHelper_ImageOverlay2>? overlayImgUrls, List<ImgHelper_TextLayer>? textLayers = null)
        {
            if (templateUrl.StartsWith("http://") || templateUrl.StartsWith("https://"))
            {
                // 加载模板图  
                using var templateStream = await templateUrl.GetAsync().ReceiveStream();
                var templateImage = await Image.LoadAsync(templateStream);

                return await MergeImagesAsyncFun(templateImage, overlayImgUrls, textLayers);
            }
            else
                return await MergeImagesAsyncForPath(templateUrl.ReplaceFilePath(), overlayImgUrls, textLayers);
        }

        public static async Task<Stream> MergeImagesAsync_ForBase64Template(string templateBase64, List<ImgHelper_ImageOverlay2> overlayImgUrls, List<ImgHelper_TextLayer>? textLayers = null)
        {
            //// 去除可能的MIME类型前缀
            //templateBase64 = templateBase64.Split(',')[^1];
            var bytes = Convert.FromBase64String(templateBase64);
            var templateImage = Image.Load(bytes);
            return await MergeImagesAsyncFun(templateImage, overlayImgUrls, textLayers);
        }

        private static async Task<Stream> MergeImagesAsyncFun(Image templateImage, List<ImgHelper_ImageOverlay2>? overlayImgUrls, List<ImgHelper_TextLayer>? textLayers)
        {
            var templateHeight = templateImage.Height;

            // 合并图像  
            if (overlayImgUrls != null)
                foreach (var overlay in overlayImgUrls)
                {
                    if (string.IsNullOrEmpty(overlay.ImageUrl))
                        continue;
                    // 加载并缩放贴图  
                    if (overlay.ImageUrl.StartsWith("http://") || overlay.ImageUrl.StartsWith("https://"))
                    {
                        using var overlay1Stream = await overlay.ImageUrl.GetAsync().ReceiveStream();
                        using var overlayImage = await Image.LoadAsync(overlay1Stream);
                        MergeImagesFun(templateImage, overlayImage, overlay, templateHeight);
                    }
                    else
                        using (var overlayImage = await Image.LoadAsync(overlay.ImageUrl.ReplaceFilePath()))
                        {
                            MergeImagesFun(templateImage, overlayImage, overlay, templateHeight);
                        }
                }


            // 添加所有文字层
            if (textLayers != null)
            {
                Dictionary<string, Font> fontDic = [];
                foreach (var textLayer in textLayers)
                {
                    if (string.IsNullOrEmpty(textLayer.Text))
                        continue;

                    var font = LoadFont(fontDic, textLayer.FontPath.ReplaceFilePath(), textLayer.FontSize, textLayer.FontStyle ?? FontStyle.Regular);
                    var textOptions = new RichTextOptions(font)
                    {
                        Origin = textLayer.Position,
                        HorizontalAlignment = HorizontalAlignment.Left,
                        VerticalAlignment = VerticalAlignment.Top
                    };

                    templateImage.Mutate(ctx => ctx.DrawText(
                        textOptions,
                        textLayer.Text.Trim(),
                        textLayer.Color
                    ));
                }
            }

            // 返回内存流  
            var outputStream = new MemoryStream();
            await templateImage.SaveAsPngAsync(outputStream);
            outputStream.Position = 0;
            return outputStream;
        }

        private static void MergeImagesFun(Image templateImage, Image overlayImage, ImgHelper_ImageOverlay2 overlay, int templateHeight)
        {
            if (overlayImage != null)
            {
                // 缩放贴图
                if (overlay.TargetWidth > 0)
                    overlayImage.Mutate(x => x.Resize(overlay.TargetWidth, overlay.TargetHeight));

                templateImage.Mutate(ctx => ctx.DrawImage(
                overlayImage,
                new Point(overlay.Position_X, overlay.Position_Y < 0 ? (templateHeight + overlay.Position_Y) : overlay.Position_Y),
                    overlay.Opacity // 0-1透明度
                ));
            }
        }

        private static Font LoadFont(Dictionary<string, Font> fontDic, string fontPath, float fontSize, FontStyle fontStyle = FontStyle.Regular)
        {
            string key = Md5Helper.Md5(fontPath + fontSize + fontStyle.ToString());
            if (fontDic.TryGetValue(key, out var value))
                return value;
            else
            {
                fontPath = fontPath.ReplaceFilePath();
                var fontCollection = new FontCollection();
                FontFamily fontFamily = fontCollection.Add(fontPath);
                var result = fontFamily.CreateFont(fontSize, fontStyle);
                fontDic.Add(key, result);
                return result;
            }
        }
    }

    public class ImgHelper_InputPaths
    {
        public ImgHelper_InputPaths()
        { }
        public ImgHelper_InputPaths(string imgUrl, int useCount = 1)
        {
            this.ImgUrl = imgUrl;
            this.UseCount = useCount < 1 ? 1 : useCount;
        }
        public string ImgUrl { get; set; } = default!;
        public int UseCount { get; set; }
    }
    /// <summary>
    /// 文字层配置类
    /// </summary>
    public class ImgHelper_TextLayer
    {
        public required string Text { get; set; }
        public required string FontPath { get; set; }
        public float FontSize { get; set; }

        public FontStyle? FontStyle { get; set; }
        public Color Color { get; set; }
        public PointF Position { get; set; }
    }

    /// <summary>
    /// 图片覆盖配置类
    /// </summary>
    public class ImgHelper_ImageOverlay
    {
        public required string ImagePath { get; set; }
        public Point Position { get; set; }
        public float Opacity { get; set; } = 1f; // 默认不透明
    }

    /// <summary>
    /// 图片覆盖配置类
    /// </summary>
    public class ImgHelper_ImageOverlay2
    {
        public ImgHelper_ImageOverlay2() { }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ImageUrl"></param>
        /// <param name="x"></param>
        /// <param name="y">当值小于0时，则从底部开始算起</param>
        /// <param name="Opacity"></param>
        public ImgHelper_ImageOverlay2(string ImageUrl, int x, int y, int TargetSize, float Opacity = 1f)
        {
            this.ImageUrl = ImageUrl;
            this.Position_X = x;
            this.Position_Y = y;
            this.Opacity = Opacity;
            this.TargetWidth = this.TargetHeight = TargetSize;
        }
        public string ImageUrl { get; set; } = default!;
        public int Position_X { get; set; }
        /// <summary>
        /// 当值小于0时，则从底部开始算起
        /// </summary>
        public int Position_Y { get; set; }
        /// <summary>
        /// 缩放宽度
        /// *为0时，则不缩放
        /// </summary>
        public int TargetWidth { get; set; }
        /// <summary>
        /// 缩放高度
        /// </summary>
        public int TargetHeight { get; set; }
        public float Opacity { get; set; } = 1f; // 默认不透明
    }
}
