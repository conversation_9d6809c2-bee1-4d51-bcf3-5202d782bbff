﻿namespace Config.CommonModel.NkpConfig;

internal class NkpConfigModel
{
}


public class ConfigModel
{
    /// <summary>
    /// 主键
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 别名
    /// </summary>
    public string? Alias { get; set; }

    /// <summary>
    /// 父节点Id
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 图标名称
    /// </summary>
    public string? Image { get; set; }

    /// <summary>
    /// 启用状态
    /// </summary>
    public int Status { get; set; }
    
    /// <summary>
    /// 标签
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 子节点
    /// </summary>
    public List<ConfigModel>? Children { get; set; }
}

public class ConfigSaveModel
{
    /// <summary>
    /// 主键
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 修改时只能传一个
    /// </summary>
    public List<string> Names { get; set; } = new List<string>();

    /// <summary>
    /// 标签
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 别名
    /// </summary>
    public string? Alias { get; set; }

    /// <summary>
    /// 父节点Id
    /// </summary>
    public string? ParentId { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? Sort { get; set; }

    /// <summary>
    /// 图标名称
    /// </summary>
    public string? Image { get; set; }

    /// <summary>
    /// 权限值
    /// </summary>
    public string? VerifyValue { get; set; }

    /// <summary>
    /// 权限所属模块
    /// </summary>
    public string? Module { get; set; }

    /// <summary>
    /// 启用状态
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 是否删除
    /// </summary>
    public int Deleted { get; set; } = 0;
}
