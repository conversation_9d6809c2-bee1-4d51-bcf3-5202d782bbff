﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 组织架构
/// </summary>
[Table("enterprise_org")]
public class Enterprise_Org
{
    [Key]
    public string OrgId { get; set; } = EntityTools.SnowflakeId();
    /// <summary>
    /// 企业Id
    /// </summary>
    public string? EntId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    // /// <summary>
    // /// 类型(金字塔、扁平化)
    // /// </summary>
    // public int Type { get; set; } = 0;

    /// <summary>
    /// 父Id
    /// </summary>
    public string? ParentId { get; set; }

    /// <summary>
    /// 层级
    /// </summary>
    public string Level { get; set; } = string.Empty;

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string? Creator { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Enterprise_Org? Parent { get; set; }
    public Enterprise Enterprise { get; set; } = default!;
}
