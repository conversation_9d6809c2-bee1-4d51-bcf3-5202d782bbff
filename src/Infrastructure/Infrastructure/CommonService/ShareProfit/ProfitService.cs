﻿using System.Linq.Dynamic.Core;
using Config;
using Config.CommonModel.Recruit;
using Config.CommonModel.ThirdTalentInfo;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using ServiceStack;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Infrastructure.CommonService.ShareProfit;

[Service(ServiceLifetime.Transient)]
public class ProfitService
{
    private IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _log;
    public ProfitService(IDbContextFactory<StaffingContext> contextFactory, LogManager log)
    {
        _contextFactory = contextFactory;
        _log = log;
    }

    public void CreateOrder(StaffingContext _context, CreateOrderModel model)
    {
        // 新建订单
        var calmoney = CalculateSettleMoney(model.TeamPostId);

        var seekerInfo = _context.User_Seeker.Where(f => f.UserId == model.Recruit.SeekerId)
        .Select(s => new
        {
            s.NickName,
            s.User.Mobile,
            s.Source
        }).First();

        var platType = seekerInfo.Source switch
        {
            RegisterSource.KsTalent => TeamBountySource.快招工,
            RegisterSource.NuoPin => TeamBountySource.诺聘平台,
            RegisterSource.Applet => TeamBountySource.微信小程序,
            RegisterSource.KsApplet => TeamBountySource.快手小程序,
            RegisterSource.Upload => TeamBountySource.简历上传,
            RegisterSource.SeekerDyApplet => TeamBountySource.抖音小程序,
            _ => TeamBountySource.其他
        };
        // 主动查询部分项目信息，后期改为全部查询
        var postBounty = _context.Post_Team.Where(f => f.TeamPostId == model.TeamPostId)
        .Select(s => new Post_Bounty
        {
            ResumeBufferId = model.Recruit.ResumeBufferId,
            SaleUserId = s.Project_Team.Project.SaleUserId,
            RecruitId = model.Recruit.RecruitId,
            TeamPostId = s.TeamPostId,
            PostId = s.PostId,
            PostName = s.Post.Name,
            SeekerId = model.Recruit.SeekerId,
            HrId = s.Project_Team.Project.HrId!,// 主创顾问id
            TeamHrId = s.Project_Team.HrId,// 协同顾问id
            PaymentNode = s.Post.PaymentNode,
            PaymentDays = s.Post.PaymentDays,
            PaymentType = s.Project_Team.Project.PaymentType,
            RewardType = s.Post.RewardType ?? PostRewardType.单次结算,
            PaymentCycle = s.Post.PaymentCycle,
            PaymentDuration = s.Post.PaymentDuration,
            Money = s.Post.Money ?? 0,
            Status = BountyStatus.交付中,
            Source = platType,
            ChannelId = model.ChannelId
        }).First();

        var postStages = _context.Post_Profit_Stage.Where(f => f.PostId == postBounty.PostId)
        .Select(s => new Post_Bounty_Stage
        {
            BountyId = postBounty.Id,
            Status = BountyStageStatus.交付中,
            GuaranteeDays = s.GuaranteeDays,
            Money = s.Amount
        }).ToList();

        // projecTeambounty.SalesBounty = calmoney.SalesBounty;
        postBounty.SalesRate = calmoney.SalesRate;
        // projecTeambounty.ManagerBounty = calmoney.ManagerBounty;
        postBounty.ManagerRate = calmoney.ManagerRate;
        // projecTeambounty.ClueBounty = calmoney.ClueBounty;
        postBounty.ClueRate = calmoney.ClueRate;

        // 渠道商
        var channel = _context.Qd_Hr_Channel.Where(f => f.Id.Equals(model.ChannelId))
        .Select(s => new { s.ChannelUserId, s.User_Seeker.NickName, s.Id })
        .FirstOrDefault();

        postBounty.ChannelId = channel?.Id;

        _context.Add(postBounty);
        _context.AddRange(postStages);
    }

    /// <summary>
    /// 订单金额计算
    /// </summary>
    /// <param name="orderMoney"></param>
    /// <param name="teamHrId"></param>
    /// <returns></returns>
    public CalculateMoney CalculateSettleMoney(string? teamPostId)
    {
        // 计算分佣
        using var _context = _contextFactory.CreateDbContext();

        var settings = _context.Sys_Settings.FirstOrDefault();

        if (settings == null)
            throw new Exception("系统配置缺失");

        // 判断用户是否诺亚公司，采用不同的分佣规则
        var teamPost = _context.Post_Team.Where(f => f.TeamPostId == teamPostId)
        .Select(s => new
        {
            Money = s.Post.Money ?? 0,
            s.Post.IsSalesCommission,
            ClueEntSpecific = s.Project_Team.User_Hr.Enterprise.Specific,
            ManagerEntSpecific = s.Project_Team.Project.User_Hr.Enterprise.Specific,
            SalesEntSpecific = s.Post.Project.SaleUser.Enterprise.Specific
        }).First();

        var managerIsNoah = teamPost.ManagerEntSpecific?.Contains(EntSpecific.Noah) == true;
        var clueIsNoah = teamPost.ClueEntSpecific?.Contains(EntSpecific.Noah) == true;
        var salesIsNoah = teamPost.SalesEntSpecific?.Contains(EntSpecific.Noah) == true;

        BountyConfig? managerConfig;
        BountyConfig? clueConfig;
        BountyConfig? salesConfig;

        if (teamPost.IsSalesCommission)
        {
            salesConfig = salesIsNoah ? settings.NoahFull : settings.PlatformFull;
            managerConfig = managerIsNoah ? settings.NoahFull : settings.PlatformFull;
            clueConfig = clueIsNoah ? settings.NoahFull : settings.PlatformFull;
        }
        else
        {
            salesConfig = salesIsNoah ? settings.NoahNoSale : settings.PlatformNoSale;
            managerConfig = managerIsNoah ? settings.NoahNoSale : settings.PlatformNoSale;
            clueConfig = clueIsNoah ? settings.NoahNoSale : settings.PlatformNoSale;
        }

        return new CalculateMoney
        {
            // SalesBounty = (teamPost.Money * salesConfig!.Sales).ToFixed(2),
            // ManagerBounty = (teamPost.Money * managerConfig!.Manager).ToFixed(2),
            // ClueBounty = (teamPost.Money * clueConfig!.Clue).ToFixed(2),
            // FollowerBounty = (orderMoney * bountyConfig.Follower).ToFixed(2),
            // PlatformBounty = (orderMoney * bountyConfig.Platform).ToFixed(2),
            SalesRate = salesConfig!.Sales,
            ManagerRate = managerConfig!.Manager,
            ClueRate = clueConfig!.Clue,
            // FollowerRate = bountyConfig.Follower,
            // PlatformRate = bountyConfig.Platform
        };
    }

    public async Task RecruitStatusChange(StaffingContext _context, RecruitStatusChangeModel model)
    {
        var needStockOut = false;
        var needStockIn = false;
        DateTime? guaranteeStartDate = null;

        var paymentNode = _context.Post_Bounty.Where(f => f.RecruitId == model.Recruit.RecruitId)
        .Select(s => s.PaymentNode)
        .FirstOrDefault();

        var order = _context.Post_Bounty.FirstOrDefault(f => f.RecruitId == model.Recruit.RecruitId);
        if (order == null)
            throw new Exception($"订单不存在");

        switch (paymentNode)
        {
            case ProjectPaymentNode.入职过保:
                needStockOut = model.Recruit.Status == RecruitStatus.Induction;
                needStockIn = model.Recruit.Status == RecruitStatus.FileAway;
                guaranteeStartDate = model.Recruit.InductionTime;
                break;
            case ProjectPaymentNode.简历交付:
                needStockOut = new List<RecruitStatus?> {
                    RecruitStatus.InterviewerScreening,
                    RecruitStatus.Interview,
                    RecruitStatus.Offer,
                    RecruitStatus.Induction,
                    RecruitStatus.Contract
                }.Contains(model.Recruit.Status);
                if (model.Recruit.Status != RecruitStatus.FileAway)
                {
                    if (guaranteeStartDate == null)
                        guaranteeStartDate = DateTime.Now;
                }
                break;
            default:
                throw new Exception("未知的交付节点");
        }

        // 如果归档，订单结束
        if (model.Recruit.Status == RecruitStatus.FileAway)
        {
            order!.Status = BountyStatus.结束;
            order.FileAwayDate = DateTime.Now;
            order!.Description = model.Recruit.FileRemarks;
            order!.UpdatedTime = DateTime.Now;

            if (order.GuaranteeStatus != GuaranteeStatus.过保)
                needStockIn = true;
        }
        else
        {
            // 如果满足条件，记录下计算过保的时间
            if (guaranteeStartDate.HasValue)
            {
                order.GuaranteeStartDate = guaranteeStartDate;
                if (order.GuaranteeStatus == GuaranteeStatus.交付中)
                    order.GuaranteeStatus = GuaranteeStatus.未过保;
            }
        }

        if (needStockOut && order.IfStockOut != 1)
        {
            // 加全局锁
            var lockerKey = $"{KuaishouConst.POST_ORDER_STOCK_CONST_LOCK_KEY_STR}:{order.PostId}";
            using var locker = await MyRedis.Lock(lockerKey, 15, autoDelay: false);
            if (locker == null)
                throw new Exception("获取库存锁失败");

            // 加锁后重新查询，防止并发问题
            order = _context.Post_Bounty.First(f => f.RecruitId == model.Recruit.RecruitId);

            if (!needStockOut || order.IfStockOut == 1)
                return;

            var post = _context.Post.FirstOrDefault(f => f.PostId == order.PostId);
            if (post == null)
                throw new Exception("职位不存在");

            if (post.LeftStock <= 0)
                throw new BadRequestException("该职位已招满");

            post.LeftStock -= 1;
            order.IfStockOut = 1;

            // // 创建出库明细
            // _context.Post_Stock.Add(new Post_Stock
            // {
            //     OrderId = order.Id,
            //     PostId = order.PostId,
            //     StockType = StockType.订单出库,
            //     StockNum = -1,
            //     LeftStock = post.LeftStock,
            //     CreatedTime = DateTime.Now
            // });
        }
        else if (needStockIn)
        {
            // 加全局锁
            var lockerKey = $"{KuaishouConst.POST_ORDER_STOCK_CONST_LOCK_KEY_STR}:{order.PostId}";
            using var locker = await MyRedis.Lock(lockerKey, 15, autoDelay: false);
            if (locker == null)
                throw new Exception("获取库存锁失败");

            var post = _context.Post.FirstOrDefault(f => f.PostId == order.PostId);
            if (post == null)
                throw new Exception("职位不存在");

            // 加锁后重新查询，防止并发问题
            order = _context.Post_Bounty.First(f => f.RecruitId == model.Recruit.RecruitId);

            // if (order.Status == BountyStatus.交付中)
            // {
            //     order!.Status = BountyStatus.结束;
            //     order!.Description = model.Recruit.FileRemarks;
            //     order!.UpdatedTime = DateTime.Now;
            // }

            if (order.IfStockOut == 1)
            {
                order.IfStockOut = 0;
                post.LeftStock += 1;

                // // 创建入库明细
                // _context.Post_Stock.Add(new Post_Stock
                // {
                //     OrderId = order.Id,
                //     PostId = order.PostId,
                //     StockType = StockType.订单入库,
                //     StockNum = 1,
                //     LeftStock = post.LeftStock,
                //     CreatedTime = DateTime.Now
                // });
            }
        }

        // 是否需要补充邀面人
        var recruit = _context.Recruit.Where(f => f.RecruitId == model.Recruit.RecruitId).Select(s => new
        {
            TeamHrId = s.Post_Delivery.Post_Team.Project_Team.HrId,
            s.RecruitId,
            s.FollowerId
        }).FirstOrDefault();
        if (recruit != null && string.IsNullOrWhiteSpace(recruit.FollowerId))
            AddFollower(_context, recruit.RecruitId, recruit.TeamHrId);
    }

    // 补充邀面人
    public void AddFollower(StaffingContext _context, string recruitId, string followerId)
    {
        // 是否需要补邀面人
        var recruit = _context.Recruit.First(f => f.RecruitId == recruitId);
        if (string.IsNullOrWhiteSpace(recruit.FollowerId))
        {
            //查找协同
            var recruitInfo = _context.Recruit.Where(f => f.RecruitId == recruitId)
            .Select(s => new
            {
                // OrderMoney = s.Post_Delivery.Money,
                s.Post_Delivery.Post.IsSalesCommission
            }).First();

            recruit.FollowerId = followerId;
            recruit.AcceptOrder = RecruitAcceptOrderEnum.已经接单;
            recruit.UpdatedTime = DateTime.Now;
            var settings = _context.Sys_Settings.FirstOrDefault();

            if (settings == null)
                throw new BadRequestException("系统配置缺失");

            var followerEntSpecific = _context.User_Hr.Where(f => f.UserId == recruit.FollowerId)
                .Select(s => new
                {
                    s.Enterprise.Specific
                }).First();

            var followerIsNoah = followerEntSpecific?.Specific?.Contains(EntSpecific.Noah) == true;

            BountyConfig? followerConfig;
            if (recruitInfo.IsSalesCommission)
                followerConfig = followerIsNoah ? settings.NoahFull : settings.PlatformFull;
            else
                followerConfig = followerIsNoah ? settings.NoahNoSale : settings.PlatformNoSale;

            // 查询佣金
            var bountys = _context.Post_Bounty.Where(r => r.RecruitId == recruitId)
            .First();

            // var followerBounty = ((recruitInfo.OrderMoney ?? 0) * followerConfig!.Follower).ToFixed(2);
            var followerRate = followerConfig!.Follower;

            // 计算平台佣金，减去销售佣金和经理佣金和线索佣金和邀面佣金
            // var platformBounty = (recruitInfo.OrderMoney ?? 0) - bountys.SalesBounty - bountys.ManagerBounty - bountys.ClueBounty - followerBounty;
            var platformRate = 1 - bountys.SalesRate - bountys.ManagerRate - bountys.ClueRate - followerRate;
            // var platformRate = (recruitInfo.OrderMoney == null || recruitInfo.OrderMoney <= 0) ? 0 : platformBounty / (recruitInfo.OrderMoney ?? 0);
            // platformBounty = platformBounty.ToFixed(2);
            platformRate = platformRate.ToFixed(2);

            // if (platformBounty < 0 || platformRate < 0)
            // {
            //     _log.Error("接单操作出错", "平台佣金计算为负数", recruitId);
            // }

            if (platformRate < 0 || platformRate >= 1)
            {
                _log.Error("接单操作出错", "平台佣金计算为负数", recruitId);
            }

            // platformBounty = platformBounty < 0 ? 0 : platformBounty;
            platformRate = platformRate < 0 ? 0 : platformRate;
            platformRate = platformRate > 1 ? 1 : platformRate;

            bountys.FollowerId = recruit.FollowerId;
            // bountys.FollowerBounty = followerBounty;
            bountys.FollowerRate = followerRate;
            // bountys.PlatformBounty = platformBounty;
            bountys.PlatformRate = platformRate;
            bountys.UpdatedTime = DateTime.Now;
        }
    }

    // /// <summary>
    // /// 生成结算单
    // /// </summary>
    // public void CreateSettle(StaffingContext _context, Post_Bounty_Stage bountyStages)
    // {
    //     var bounty = bountyStages.Post_Bounty;
    //     var salesSettle = new Post_Settlement
    //     {
    //         BountyStageId = bountyStages.Id,
    //         Status = PostSettleStatus.结算中,
    //         ApprovalStatus = PostSettleApprovalStatus.待审核
    //     };
    //     _context.Add(salesSettle);
    // }

    // public void PaySuccess(string orderStageId)
    // {
    //     using var _context = _contextFactory.CreateDbContext();
    //     var orderStage = _context.Project_Teambounty_Stage.Include(f => f.Project_Teambounty)
    //     .First(f => f.Id == orderStageId);
    //     //交付成功修改为计费待确认，等待项目经理确认交付
    //     orderStage.Status = PostStageStatus.计费待确认;
    //     orderStage.UpdatedTime = DateTime.Now;

    //     //CreateSettle(order);
    //     _log.Info("TranStep_PaySuccess_3", $"step1", $"交付成功-订单生成完成");

    //     // 大改版，暂不走数字诺亚
    //     // try
    //     // {
    //     //     // todo:交付成功，解冻金额(不走数字诺亚)
    //     //     if (order.SettlementActualMoney != null && order.SettlementActualMoney > 0)
    //     //     {
    //     //         var nuoId = _context.Project.Where(w => w.ProjectId == order.ProjectId).Select(s => s.NuoId).FirstOrDefault();
    //     //         // todo:判断NuoId
    //     //         if (!string.IsNullOrWhiteSpace(nuoId))
    //     //         {
    //     //             _ = _ndnService.FreezeBudget(new Config.CommonModel.Ndn.FreezeBudgetByPost
    //     //             {
    //     //                 projectCode = nuoId,
    //     //                 type = Config.CommonModel.Ndn.BudgetFreezeType.消费,
    //     //                 amount = order.SettlementActualMoney.Value,
    //     //                 postId = order.PostId,
    //     //                 HrId = order.HrId,
    //     //                 ProjectId = order.ProjectId
    //     //             }).GetAwaiter().GetResult();
    //     //         }
    //     //         _log.Info("TranStep_PaySuccess_3", $"step2", $"交付成功-消费完成");
    //     //     }
    //     // }
    //     // catch (Exception ex)
    //     // {
    //     //     order!.Status = BountyStatus.交付失败;
    //     //     order!.Description = ex.Message;
    //     // }

    //     _context.SaveChanges();
    //     _log.Info("TranStep_PaySuccess_3", $"step3", $"交付成功-全部完成");
    // }

    // /// <summary>
    // /// 创建结算单
    // /// </summary>
    // /// <param name="orderInfo"></param>
    // private void CreateSettle(Project_Teambounty orderInfo)
    // {
    //     using var _context = _contextFactory.CreateDbContext();
    //     // 创建协同结算单
    //     if (orderInfo.TeamHrId != orderInfo.HrId)
    //     {
    //         var teamSettle = new Project_Settlement
    //         {
    //             OrderId = orderInfo.Id,
    //             Type = SettleType.协同,
    //             Status = SettleStatus.结算中,
    //             UserId = orderInfo.TeamHrId,
    //             SettlementMoney = orderInfo.ClueBounty
    //         };
    //         _context.Add(teamSettle);
    //     }

    //     // 大改版，暂不支持渠道商
    //     // // 创建渠道商结算单
    //     // if (!string.IsNullOrWhiteSpace(orderInfo.ChannelHrId))
    //     // {
    //     //     var channelSettle = new Project_Settlement
    //     //     {
    //     //         OrderId = orderInfo.Id,
    //     //         Type = SettleType.渠道,
    //     //         Status = SettleStatus.结算中,
    //     //         UserId = orderInfo.ChannelHrId,
    //     //         SettlementMoney = orderInfo.ChannelSettlementMoney ?? 0m
    //     //     };
    //     //     _context.Add(channelSettle);
    //     // }
    //     _context.SaveChanges();
    // }
}

public class CreateOrderModel
{
    public string? TeamPostId { get; set; }

    public string? ChannelId { get; set; }

    public Recruit Recruit { get; set; } = default!;
}

public class RecruitStatusChangeModel
{
    public Recruit Recruit { get; set; } = default!;
    // public string? RecruitId { get; set; }

    // public RecruitStatus? RecruitStatus { get; set; }
    // public RecruitFileAway? FileAway { get; set; }

    // public ProjectPaymentNode? PaymentNode { get; set; }

    // // 过保计算开始时间
    // public DateTime? GuaranteeStartDate { get; set; }

    // public string? Description { get; set; }
}