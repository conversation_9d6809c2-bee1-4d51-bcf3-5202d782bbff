using System;
using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;
using Staffing.Entity.Staffing;

namespace Staffing.Model.Hr.Enterprise;

public class GetEnterpriseMerchants : QueryRequest
{
    /// <summary>
    /// 银联状态
    /// </summary>
    public ApplyStatusEnum? UnionPayStatus { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? MerchantName { get; set; }
}

public class GetEnterpriseMerchantsResponse : QueryResponse
{
    /// <summary>
    /// 企业商户列表
    /// </summary>
    public List<EnterpriseMerchantModel> Rows { get; set; } = new();
}

public class GetEnterpriseMerchantDetail
{
    /// <summary>
    /// 商户ID
    /// </summary>
    [System.ComponentModel.DataAnnotations.Required(ErrorMessage = "商户ID不能为空")]
    public string? MerchantId { get; set; }
}
public class EnterpriseMerchantModel
{
    public string? Id { get; set; }

    /// <summary>
    /// 商户名称
    /// </summary>
    public string MerchantName { get; set; } = default!;

    /// <summary>
    /// 商户类型
    /// </summary>
    public MerchantType? MerchantType { get; set; }

    /// <summary>
    /// 商户类型
    /// </summary>
    public string? MerchantTypeText { get; set; }

    /// <summary>
    /// 银联状态
    /// </summary>
    public ApplyStatusEnum? UnionPayStatus { get; set; }

    /// <summary>
    /// 证书状态(0: 未上传, 1: 已上传)
    /// </summary>
    public int CertificateStatus { get; set; }

    /// <summary>
    /// 证书状态
    /// </summary>
    public string? CertificateStatusText { get; set; }

    /// <summary>
    /// 银联状态
    /// </summary>
    public string? UnionPayStatusText { get; set; }

    /// <summary>
    /// 公章（Base64编码）
    /// </summary>
    public string? OfficeSeal { get; set; }

    /// <summary>
    /// 法人章（Base64编码）
    /// </summary>
    public string? LegalSeal { get; set; }

    /// <summary>
    /// 合同章（Base64编码）
    /// </summary>
    public string? ContractSeal { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string CreatedName { get; set; } = default!;

    /// <summary>
    /// 更新人
    /// </summary>
    public string UpdatedName { get; set; } = default!;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 商户作为甲方与平台签署的合同号
    /// </summary>
    public string? PartyAContractNo { get; set; }

    /// <summary>
    /// 商户作为乙方与平台签署的合同号
    /// </summary>
    public string? PartyBContractNo { get; set; }

    public NdnInvoiceInfo? InvoiceInfo { get; set; }
}

// 更新公章
public class UpdateEnterpriseMerchantModel
{
    public string Id { get; set; } = default!;

    /// <summary>
    /// 公章（Base64编码）
    /// </summary>
    public string? OfficeSeal { get; set; }

    /// <summary>
    /// 法人章（Base64编码）
    /// </summary>
    public string? LegalSeal { get; set; }

    /// <summary>
    /// 合同章（Base64编码）
    /// </summary>
    public string? ContractSeal { get; set; }

    /// <summary>
    /// 商户作为甲方与平台签署的合同号
    /// </summary>
    public string? PartyAContractNo { get; set; }

    /// <summary>
    /// 商户作为乙方与平台签署的合同号
    /// </summary>
    public string? PartyBContractNo { get; set; }

    public NdnInvoiceInfo? InvoiceInfo { get; set; }
}