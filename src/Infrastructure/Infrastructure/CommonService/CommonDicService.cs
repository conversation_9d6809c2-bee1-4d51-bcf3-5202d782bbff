using System.Text.RegularExpressions;
using Config;
using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Infrastructure.CommonService;

[Service(ServiceLifetime.Singleton)]
public class CommonDicService
{
    private IDbContextFactory<StaffingContext> _staffingContextFactory;
    private ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CacheHelper _cacheHelper;

    public CommonDicService(IDbContextFactory<StaffingContext> staffingContextFactory,
        IOptionsSnapshot<ConfigManager> config,
        IHostEnvironment hostingEnvironment, LogManager log,
        CacheHelper cacheHelper)
    {
        _staffingContextFactory = staffingContextFactory;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
        _cacheHelper = cacheHelper;
    }

    /// <summary>
    /// 福利
    /// </summary>
    /// <returns></returns>
    public List<WelfareModel> GetWelfare()
    {
        var cacheKey = RedisKey.Dic.Welfare;
        var disable = -1;
        var result = _cacheHelper.GetMemoryCache<List<WelfareModel>>(() =>
        {
            return _cacheHelper.GetRedisCache<List<WelfareModel>>(() =>
            {
                using (var _context = _staffingContextFactory.CreateDbContext())
                {
                    return _context.Dic_Welfare.Where(x => x.Status == ActiveStatus.Active)
                    .Select(s => new WelfareModel
                    {
                        Id = s.Id,
                        Name = s.Name,
                        Logo = s.Logo,
                        Describe = s.Describe
                    }).ToList();
                }
            }, cacheKey, disable);
        }, cacheKey, disable);
        return result;
    }

    /// <summary>
    /// 行业
    /// </summary>
    /// <returns></returns>
    public List<CommonDicModel> GetIndustry()
    {
        var cacheKey = RedisKey.Dic.Industry;
        var disable = -1;
        var result = _cacheHelper.GetMemoryCache<List<CommonDicModel>>(() =>
        {
            return _cacheHelper.GetRedisCache<List<CommonDicModel>>(() =>
            {
                using (var _context = _staffingContextFactory.CreateDbContext())
                {
                    return _context.Dic_Industry.Where(x => x.Status == ActiveStatus.Active)
                    .Select(s => new CommonDicModel
                    {
                        Id = s.Id.ToString(),
                        Name = s.Name,
                        ParentId = s.ParentId == 0 ? null : s.ParentId.ToString(),
                        Level = s.Level
                    }).ToList();
                }
            }, cacheKey, disable);
        }, cacheKey, disable);
        return result;
    }

    /// <summary>
    /// 学校专业
    /// </summary>
    /// <returns></returns>
    public List<CommonDicModel> GetMajor()
    {
        var cacheKey = RedisKey.Dic.Major;
        var disable = -1;
        var result = _cacheHelper.GetMemoryCache<List<CommonDicModel>>(() =>
        {
            return _cacheHelper.GetRedisCache<List<CommonDicModel>>(() =>
            {
                using (var _context = _staffingContextFactory.CreateDbContext())
                {
                    return _context.Dic_Major.Where(x => x.Status == ActiveStatus.Active)
                    .Select(s => new CommonDicModel
                    {
                        Id = s.Id,
                        Name = s.Name
                    }).ToList();
                }
            }, cacheKey, disable);
        }, cacheKey, disable);
        return result;
    }

    /// <summary>
    /// 学校
    /// </summary>
    /// <returns></returns>
    public List<SchoolModel> GetSchool()
    {
        var cacheKey = RedisKey.Dic.School;
        var disable = -1;
        var result = _cacheHelper.GetMemoryCache<List<SchoolModel>>(() =>
        {
            return _cacheHelper.GetRedisCache<List<SchoolModel>>(() =>
            {
                using (var _context = _staffingContextFactory.CreateDbContext())
                {
                    return _context.Dic_School.Where(x => x.Status == ActiveStatus.Active)
                    .Select(s => new SchoolModel
                    {
                        Id = s.Id,
                        Name = s.Name,
                        Logo = s.Logo,
                        Type = s.Type,
                    }).ToList();
                }
            }, cacheKey, disable);
        }, cacheKey, disable);
        return result;
    }

    /// <summary>
    /// 地区
    /// </summary>
    /// <returns></returns>
    public List<RegionModel> GetRegion()
    {
        var cacheKey = RedisKey.Dic.Region;
        var disable = -1;
        var result = _cacheHelper.GetMemoryCache<List<RegionModel>>(() =>
        {
            return _cacheHelper.GetRedisCache<List<RegionModel>>(() =>
            {
                using (var _context = _staffingContextFactory.CreateDbContext())
                {
                    var data = _context.Dic_Region.Where(x => x.Status == ActiveStatus.Active && x.Id.Length <= 6)
                    .Select(s => new RegionModel
                    {
                        Id = s.Id,
                        Name = s.Name,
                        City = s.City,
                        County = s.County,
                        Province = s.Province,
                        ParentId = s.ParentId
                    }).ToList();

                    //暂时排除特别行政区
                    data = data.Where(x => !x.Id.StartsWith("8")).ToList();
                    return data;
                }
            }, cacheKey, disable);
        }, cacheKey, disable);
        return result;
    }

    /// <summary>
    /// 地区tree
    /// </summary>
    /// <returns></returns>
    public List<GeneralRecursionDic> GetRegionTree(int? level = null)
    {
        var cacheKey = $"{RedisKey.Dic.RegionTree}{level}";
        var disable = -1;
        var result = _cacheHelper.GetMemoryCache<List<GeneralRecursionDic>>(() =>
        {
            return _cacheHelper.GetRedisCache<List<GeneralRecursionDic>>(() =>
            {
                var cache = GetRegion();

                if (level == 1)
                    cache = cache.Where(x => string.IsNullOrWhiteSpace(x.Province)).ToList();
                else if (level == 2)
                    cache = cache.Where(x => string.IsNullOrWhiteSpace(x.City)).ToList();
                else if (level == 3)
                    cache = cache.Where(x => string.IsNullOrWhiteSpace(x.County)).ToList();

                var treeNotes = Tools.GetChildrenTreeNode(cache, x => x.Id, y => y.ParentId, string.Empty);
                var data = Tools.ModelConvert<List<GeneralRecursionDic>>(treeNotes);
                return data;
            }, cacheKey, disable);
        }, cacheKey, disable);
        return result;
    }

    /// <summary>
    /// 根据地区id获取地区Tree
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public GetDicTreeResponse GetCityTreeById(List<string> Ids)
    {
        var result = new GetDicTreeResponse();

        if (Ids != null && Ids.Count > 0)
        {
            var region = GetRegion();

            var rgList = new List<RegionModel>();

            var rids = Ids.Where(x => !string.IsNullOrWhiteSpace(x)).Distinct().ToList();
            var allIds = rids;
            while (rids.Count > 0)
            {
                var det = region.Where(x => rids.Contains(x.Id)).ToList();
                if (det.Count == 0)
                    break;

                rids = det.Where(x => !string.IsNullOrWhiteSpace(x.ParentId) && !allIds.Contains(x.ParentId)).Select(x => x.ParentId).Distinct().ToList();
                allIds.AddRange(rids);
                rgList.AddRange(det);
            }

            var treeNotes = Tools.GetChildrenTreeNode(rgList, x => x.Id, y => y.ParentId, string.Empty);
            result.Rows = Tools.ModelConvert<List<GeneralRecursionDic>>(treeNotes);
        }
        return result;
    }

    /// <summary>
    /// 职位
    /// </summary>
    /// <returns></returns>
    public List<CommonDicModel> GetPost()
    {
        var cacheKey = RedisKey.Dic.Post;
        var disable = -1;
        var result = _cacheHelper.GetMemoryCache<List<CommonDicModel>>(() =>
        {
            return _cacheHelper.GetRedisCache<List<CommonDicModel>>(() =>
            {
                using (var _context = _staffingContextFactory.CreateDbContext())
                {
                    return _context.Dic_Post.Where(x => x.Status == ActiveStatus.Active)
                    .Select(s => new CommonDicModel
                    {
                        Id = s.Id.ToString(),
                        Name = s.Name,
                        Level = s.Level,
                        ParentId = s.ParentId == 0 ? null : s.ParentId.ToString(),
                    }).ToList();
                }
            }, cacheKey, disable);
        }, cacheKey, disable);
        return result;
    }

    public CategoryInfo GetCategory(string? category)
    {
        CategoryInfo info = new CategoryInfo();
        if (string.IsNullOrWhiteSpace(category))
            return info;
        // 职位类别
        var categoryLevel = GetPost();
        List<CommonDicModel> stack = new List<CommonDicModel>();
        GetId(category);
        void GetId(string category)
        {
            var current = categoryLevel.FirstOrDefault(f => f.Id == category);
            current ??= new CommonDicModel();
            stack.Insert(0, current);
            var parentId = current.ParentId;
            if (!string.IsNullOrWhiteSpace(parentId))
            {
                GetId(parentId);
            }
        }
        int i = 0;
        var level1 = stack.Skip(i++).Take(1).FirstOrDefault();
        info.CategoryLevel1 = level1?.Id;
        info.CategoryLevel1Name = level1?.Name;
        var level2 = stack.Skip(i++).Take(1).FirstOrDefault();
        info.CategoryLevel2 = level2?.Id;
        info.CategoryLevel2Name = level2?.Name;
        var level3 = stack.Skip(i++).Take(1).FirstOrDefault();
        info.CategoryLevel3 = level3?.Id;
        info.CategoryLevel3Name = level3?.Name;
        return info;
    }

    /// <summary>
    /// 证书
    /// </summary>
    /// <returns></returns>
    public List<CommonDicModel> GetCert()
    {
        var cacheKey = RedisKey.Dic.Cert;
        var disable = -1;
        var result = _cacheHelper.GetMemoryCache<List<CommonDicModel>>(() =>
        {
            return _cacheHelper.GetRedisCache<List<CommonDicModel>>(() =>
            {
                using (var _context = _staffingContextFactory.CreateDbContext())
                {
                    return _context.Dic_Cert.Where(x => x.Status == ActiveStatus.Active)
                    .Select(s => new CommonDicModel
                    {
                        Id = s.Id.ToString(),
                        Name = s.Name,
                        ParentId = s.ParentId == 0 ? null : s.ParentId.ToString(),
                        Level = s.Level
                    }).ToList();
                }
            }, cacheKey, disable);
        }, cacheKey, disable);
        return result;
    }

    /// <summary>
    /// 应用信息
    /// </summary>
    /// <returns></returns>
    public GetAppList GetApps()
    {
        var cacheKey = RedisKey.Dic.Apps;
        var disable = 300;
        var result = _cacheHelper.GetRedisCache<GetAppList>(() =>
        {
            using (var _context = _staffingContextFactory.CreateDbContext())
            {
                var rp = _context.Sys_App.Where(x => x.Status == ActiveStatus.Active && x.Show)
                .Select(s => new GetAppsInfo
                {
                    Id = s.Id,
                    Name = s.Name,
                    ParentId = s.ParentId,
                    AppId = s.Id,
                    Icon = s.Icon,
                    Status = s.Status,
                    Sort = s.Sort,
                    Url = s.Url,
                    ShortAbstract = s.ShortAbstract,
                    Powers = s.Powers
                }).ToList();
                var sez = new GetAppList();
                sez.Category = rp.Where(x => x.ParentId == string.Empty || x.ParentId == null)
                .OrderBy(o => o.Sort).ThenBy(o => o.Id).ToList();

                sez.Apps = rp.Where(x => x.ParentId != string.Empty)
                .OrderBy(o => o.Sort).ThenBy(o => o.Id).ToList();

                return sez;
            }
        }, cacheKey, disable);
        return result;
    }

    /// <summary>
    /// AppId信息
    /// </summary>
    /// <returns></returns>
    public List<GetAppIdInfo> GetAppIds()
    {
        var cacheKey = RedisKey.Dic.AppIds;
        var disable = 3600;
        var result = _cacheHelper.GetRedisCache<List<GetAppIdInfo>>(() =>
        {
            using (var _context = _staffingContextFactory.CreateDbContext())
            {
                var rp = _context.App
                .Select(s => new GetAppIdInfo
                {
                    Id = s.Id,
                    AppId = s.AppId,
                    AppSecret = s.AppSecret,
                    Describe = s.Describe,
                    Type = s.Type
                }).ToList();
                return rp;
            }
        }, cacheKey, disable);
        return result;
    }

    /// <summary>
    /// 根据地区Id获取地区信息
    /// </summary>
    /// <param name="regionId"></param>
    /// <returns></returns>
    public CityModel GetCityById(string regionId)
    {
        var region = GetRegion();

        var rgList = new List<RegionModel>();

        var rid = regionId;
        while (!string.IsNullOrWhiteSpace(rid))
        {
            var det = region.FirstOrDefault(x => x.Id == rid);
            if (det == null)
                break;

            rid = det.ParentId;

            if (rgList.Any(x => x.Id == rid))
                break;

            rgList.Insert(0, det);
        }

        var result = new CityModel();
        var i = 0;
        result.Id = regionId;
        result.ProvinceName = rgList?.Skip(i++).Take(1).FirstOrDefault()?.Name;
        var cityInfo = rgList?.Skip(i++).Take(1).FirstOrDefault();
        result.CityName = cityInfo?.Name;
        result.CityId = cityInfo?.Id;
        result.CountyName = rgList?.Skip(i++).Take(1).FirstOrDefault()?.Name;
        result.TownName = rgList?.Skip(i++).Take(1).FirstOrDefault()?.Name;

        return result;
    }

    /// <summary>
    /// 地址解析
    /// </summary>
    /// <param name="regionId"></param>
    /// <returns></returns>
    public CityModel? GetCityByAddress(string? address)
    {
        if (string.IsNullOrWhiteSpace(address))
            return null;

        var region = GetRegion();

        // var regex = "(?<province>[^省]+自治区|.*?省|.*?行政区|.*?市)?(?<city>[^市]+自治州|.*?地区|.*?行政单位|.+盟|市辖区|.*?市|.*?县)?(?<county>[^县]+县|.+区|.+市|.+旗|.+海域|.+岛)?(?<town>[^区]+区|.+镇|.+办事处)?(?<village>.*)";
        var regex = "(?<province>[^省]+自治区|.*?省|.*?行政区)?(?<city>[^市]+自治州|.*?地区|.*?行政单位|.+盟|市辖区|.*?市)?(?<county>[^县]+县|.+区|.+市|.+旗|.+海域|.+岛)?(?<town>[^区]+区|.+镇|.+办事处)?(?<village>.*)";
        var m = Regex.Match(address, regex, RegexOptions.IgnoreCase);

        var province = m.Groups["province"].Value;//省
        var city = m.Groups["city"].Value;//市
        var county = m.Groups["county"].Value;//县
        var town = m.Groups["town"].Value;//镇
        var village = m.Groups["village"].Value;//详细地址

        if (string.IsNullOrWhiteSpace(province) && string.IsNullOrWhiteSpace(city) && string.IsNullOrWhiteSpace(county))
            return null;

        var result = new CityModel
        {
            ProvinceName = province
        };

        var predicate = PredicateBuilder.New<RegionModel>(x => true);

        RegionModel? data = null;
        if (!string.IsNullOrWhiteSpace(city))
            data = region.FirstOrDefault(x => x.Name == city);

        if (data != null)
        {
            result.Id = data?.Id;
            result.CityId = data?.Id;
            result.CityName = data?.Name;
        }

        if (!string.IsNullOrWhiteSpace(county))
        {
            if (!string.IsNullOrWhiteSpace(result.Id))
                data = region.FirstOrDefault(x => x.Name == county && x.ParentId == result.CityId);
            else
                data = region.FirstOrDefault(x => x.Name == county);
        }

        if (data != null)
        {
            result.Id = data?.Id;
            result.CountyName = county;
        }

        return result;
    }

    /// <summary>
    /// 获取base表信息
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public List<Dic_Base_Info> GetBaseInfo(string type)
    {
        using (var _context = _staffingContextFactory.CreateDbContext())
        {
            return _context.Dic_Base_Info.Where(x => x.Type == type)
            .Select(s => new Dic_Base_Info
            {
                Code = s.Code,
                Value = s.Value,
                Type = s.Type,
                Sort = s.Sort,
            })
            .OrderBy(x => x.Sort)
            .ToList();
        }
    }

    /// <summary>
    /// 获取oss文件地址
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public File_Path_Of_Oss? GetExistFileFromOss(string type, string id)
    {
        using (var _context = _staffingContextFactory.CreateDbContext())
        {
            return _context.File_Path_Of_Oss.FirstOrDefault(x => x.Type == type && x.PrimaryId == id && x.Deleted == 0);
        }
    }

    // /// <summary>
    // /// 根据地区Id获取地区全名
    // /// </summary>
    // /// <param name="regionId"></param>
    // /// <returns></returns>
    // public async Task<string> GetFullCityNameById(string regionId)
    // {
    //     var region = await GetCityById(regionId);

    //     var sb = new StringBuilder();

    //     if (!string.IsNullOrWhiteSpace(region?.ProvinceName))
    //         sb.Append(region?.ProvinceName);
    //     if (!string.IsNullOrWhiteSpace(region?.CityName) && region.CityName != region.ProvinceName)
    //         sb.Append($"-{region.CityName}");
    //     if (!string.IsNullOrWhiteSpace(region?.CountyName))
    //         sb.Append($"-{region.CountyName}");

    //     var result = sb.ToString();

    //     if (!string.IsNullOrWhiteSpace(result))
    //         result = result.Trim('-');

    //     return result;
    // }
}

public class CommonDicModel
{
    /// <summary>
    /// Id
    /// </summary>
    public string Id { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 父Id
    /// </summary>
    public string? ParentId { get; set; }

    /// <summary>
    /// 层级
    /// </summary>
    public string? Level { get; set; }
}

public class RegionModel
{
    public string Id { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 上级Id
    /// </summary>
    public string ParentId { get; set; } = string.Empty;

    /// <summary>
    /// 区
    /// </summary>
    public string County { get; set; } = default!;

    /// <summary>
    /// 市
    /// </summary>
    public string City { get; set; } = default!;

    /// <summary>
    /// 省
    /// </summary>
    public string Province { get; set; } = default!;
}

public class CategoryInfo
{
    public string? CategoryLevel1 { get; set; }
    public string? CategoryLevel1Name { get; set; }
    public string? CategoryLevel2 { get; set; }
    public string? CategoryLevel2Name { get; set; }
    public string? CategoryLevel3 { get; set; }
    public string? CategoryLevel3Name { get; set; }
}
