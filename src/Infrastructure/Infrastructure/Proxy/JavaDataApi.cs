using Config;
using Config.CommonModel.JavaDataApi;
using Flurl;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using ServiceStack.Text;

namespace Infrastructure.Proxy;

[Service(ServiceLifetime.Transient)]
public class JavaDataApi
{
    private readonly ConfigManager _config;
    private readonly CacheHelper _cacheHelper;
    private readonly LogManager _log;
    public JavaDataApi(IOptionsSnapshot<ConfigManager> config, CacheHelper cacheHelper, LogManager log)
    {
        _config = config.Value;
        _cacheHelper = cacheHelper;
        _log = log;
    }

    /// <summary>
    /// 真实人才库检索
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<PlatformExternalResponse> PlatformTalentSearch(PlatformExternalRequest model)
    {
        string url = _config.DataRdsServer + "/datards/linggong/platform/talent/search";
        var result = await url.SetQueryParams(model).GetJsonAsync<PlatformExternalResponse>();
        return result;
    }

    /// <summary>
    /// 虚拟人才库检索
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<ExternalResponse> VirtualTalentSearch(ExternalRequest model)
    {
        var url = _config.DataRdsServer + "/datards/linggong/resume/virtual/search";
        var result = await url.SetQueryParams(model).GetJsonAsync<ExternalResponse>();
        return result;
    }

    /// <summary>
    /// 职位行为数据
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<JavaPostBehaviorDataResponse> GetPostBehaviorData(JavaPostBehaviorData model)
    {
        JavaApiModel<JavaPostBehaviorDataResponse>? result = null;
        try
        {
            var url = _config.DataRdsServer + "/datards/linggong/post/stat/page/detail";
            result = await url.SetQueryParams(model).GetJsonAsync<JavaApiModel<JavaPostBehaviorDataResponse>>();

            if (!result.success && result.code != 1009)
                throw new Exception($"{result.code}_{result.message}");
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            _log.Error("JavaApi:职位行为数据出错", $"httpstatus:{ex.StatusCode}_{error}", JsonSerializer.SerializeToString(model));
        }
        catch (Exception e)
        {
            _log.Error("JavaApi:职位行为数据出错", e.Message, JsonSerializer.SerializeToString(model));
        }

        return result?.data ?? new JavaPostBehaviorDataResponse();
    }

    /// <summary>
    /// 主创职位统计
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<object> GetPostBoard(ProjectBoardReq model)
    {
        var url = $"{_config.DataRdsServer}/datards/linggong/post/stat/board";
        return await Get(url, model);
    }

    /// <summary>
    /// 协同职位统计
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<object> GetTeamPostBoard(ProjectBoardReq model)
    {
        var url = $"{_config.DataRdsServer}/datards/linggong/post/stat/board/team";
        return await Get(url, model);
    }

    /// <summary>
    /// 协同分析看板统计
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<object> GetTeamBoardAnalysis(ProjectBoardReq model)
    {
        var url = $"{_config.DataRdsServer}/datards/linggong/team/stat/analysis/board";
        return await Get(url, model);
    }

    /// <summary>
    /// 协同看板统计
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<object> GetTeamBoardStat(ProjectBoardReq model)
    {
        var url = $"{_config.DataRdsServer}/datards/linggong/team/stat/board";
        return await Get(url, model);
    }

    /// <summary>
    /// 面试官看板统计
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<object> GetInterviewerStat(ProjectBoardReq model)
    {
        var url = $"{_config.DataRdsServer}/datards/linggong/interviewer/stat/board";
        return await Get(url, model);
    }

    /// <summary>
    /// 招聘流程看板表格统计（主创）
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<object> GetRecruitStat(ProjectBoardReq model)
    {
        var url = $"{_config.DataRdsServer}/datards/linggong/recruitment/stat/board";
        return await Get(url, model);
    }

    /// <summary>
    /// 通用get请求
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public async Task<object> Get(string url, object query)
    {
        JavaApiModel<JToken>? result = null;
        try
        {
            result = await url.SetQueryParams(query).GetJsonAsync<JavaApiModel<JToken>>();

            if (!result.success)
                throw new Exception($"{result.code}_{result.message}");

            return result?.data ?? new object();
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
        catch (Exception e)
        {
            _log.Error("JavaApi出错：", e.Message, url);
            throw;
        }
    }
}