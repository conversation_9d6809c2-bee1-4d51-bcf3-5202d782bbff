CREATE TABLE `file_path_of_oss` (
	`Id` INT(10) NOT NULL AUTO_INCREMENT,
	`Type` VARCHAR(100) NOT NULL COMMENT '所属项目类型' COLLATE 'utf8mb4_general_ci',
	`PrimaryId` VARCHAR(100) NOT NULL COMMENT '项目内唯一id' COLLATE 'utf8mb4_general_ci',
	`OssPath` VARCHAR(500) NOT NULL COMMENT 'url' COLLATE 'utf8mb4_general_ci',
	`CreatedTime` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`Deleted` TINYINT(3) NOT NULL DEFAULT '0',
	`UserId` VARCHAR(50) NOT NULL COMMENT '用户Id' COLLATE 'utf8mb4_general_ci',
	PRIMARY KEY (`Id`) USING BTREE,
	INDEX `Type_PrimaryId_Index` (`Type`, `PrimaryId`) USING BTREE
)
COMMENT='oss文件地址表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
AUTO_INCREMENT=85
;
