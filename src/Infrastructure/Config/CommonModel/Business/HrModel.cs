using System.Text.Json.Serialization;
using Config.Enums;

namespace Config.CommonModel.Business;

public class HrModel
{
    public string? HrId { get; set; }
    /// <summary>
    /// 诺聘Id(pk_eaid)
    /// </summary>
    public string NuoId { get; set; } = default!;
    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 腾讯ImId
    /// </summary>
    public string? TencentImId { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// 邀请码
    /// </summary>
    public string? InviteCode { get; set; }

    /// <summary>
    /// 上次登录时间
    /// </summary>
    [JsonIgnore]
    public DateTime? LoginTime { get; set; }

    /// <summary>
    /// 在线状态
    /// </summary>
    public OnlineStatus? OnlineStatus { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 企业简称
    /// </summary>
    public string? EntAbbr { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? EMail { get; set; }

    /// <summary>
    /// 微信
    /// </summary>
    public string? WeChatNo { get; set; }

    /// <summary>
    /// 企业微信
    /// </summary>
    public string? EntWeChatQrCode { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; }

    public double Lat { get; set; }
    public double Lng { get; set; }

    /// <summary>
    /// 是否实名
    /// </summary>
    public bool? Identity { get; set; }

    /// <summary>
    /// 小程序二维码
    /// </summary>
    public string? AppletQrCode { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }

    public CityModel? City { get; set; }
}