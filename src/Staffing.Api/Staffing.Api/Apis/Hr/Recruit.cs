using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using MiniExcelLibs;
using Staffing.Model.Hr.Recruit;
using Staffing.Core.Interfaces.Hr;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class RecruitRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("recruit").WithTags("招聘流程");
        group.MapGet("/recruitauthority/{projectId}", (string projectId, IRecruitService rep) => rep.GetRecruitAuthority(projectId))
            .WithOpenApi(operation => new(operation) { Summary = "查询当前用户是否有权限" });

        group.MapGet("/post/{projectId}", (string projectId, IRecruitService rep) => rep.GetRecruitProjectPostList(projectId))
            .WithOpenApi(operation => new(operation) { Summary = "获取项目职位列表" });

        group.MapPost("/list", (RecruitListRequest model, IRecruitService rep) => rep.GetRecruitListByProjectId(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取招聘流程列表" });

        group.MapGet("/recruitlabellist", (IRecruitService rep) => rep.GetRecruitLabelList())
            .WithOpenApi(operation => new(operation) { Summary = "获取招聘流程标签集合(总标签)" });

        group.MapPost("/recruitlabellist", (AddRecruitLabelRequest model, IRecruitService rep) => rep.AddRecruitLabel(model))
            .WithOpenApi(operation => new(operation) { Summary = "添加招聘流程标签(总标签)" });

        group.MapDelete("/recruitlabellist/{labelId}", (string labelId, IRecruitService rep) => rep.DeleteRecruitLabel(labelId))
            .WithOpenApi(operation => new(operation) { Summary = "删除招聘流程标签(总标签)" });

        group.MapGet("/recruitlabel/{recruitId}", (string recruitId, IRecruitService rep) => rep.GetRecruitLabel(recruitId))
            .WithOpenApi(operation => new(operation) { Summary = "获取招聘流程标签" });

        group.MapPost("/recruitlabel", (EditRecruitLabelRequest model, IRecruitService rep) => rep.EditRecruitLabel(model))
            .WithOpenApi(operation => new(operation) { Summary = "变更招聘流程标签" });

        group.MapGet("/interviewerlist/{projectId}", (string projectId, IRecruitService rep) => rep.GetProjectInterviewerList(projectId))
            .WithOpenApi(operation => new(operation) { Summary = "获取项目面试官列表" });

        group.MapGet("/recruit/resumedetails/{recruitId}", (string recruitId, IRecruitService rep) => rep.GetRecruitResumeDetails(recruitId))
            .WithOpenApi(operation => new(operation) { Summary = "获取招聘流程简历详情" });

        group.MapGet("/recruit/resumedetails/{recruitId}/extend", (string recruitId, IRecruitService rep) => rep.GetRecruitResumeExtend(recruitId))
            .WithOpenApi(operation => new(operation) { Summary = "获取招聘流程简历扩展信息" });

        group.MapPost("/recruit/resumedetails/record", (RecruitmentProcessRequest model, IRecruitService rep) => rep.GetRecruitmentProcessList(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取招聘流程记录列表" });

        group.MapPost("/recruit/resumedetails/commentlist", (GetRecruitResumeCommentRequest model, IRecruitService rep) => rep.GetRecruitResumeComment(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取招聘流程简历评论记录" });

        group.MapPost("/recruit/resumedetails/comment", (AddRecruitResumeCommentRequest model, IRecruitService rep) => rep.AddRecruitResumeComment(model))
            .WithOpenApi(operation => new(operation) { Summary = "添加招聘流程简历评论" });

        group.MapDelete("/recruit/resumedetails/comment/{commentId}", (string commentId, IRecruitService rep) => rep.DeleteRecruitResumeComment(commentId))
            .WithOpenApi(operation => new(operation) { Summary = "删除招聘流程简历评论" });

        group.MapGet("/{recruitId}", (string recruitId, IRecruitService rep) => rep.GetRecruitById(recruitId))
            .WithOpenApi(operation => new(operation) { Summary = "根据id获取招聘进程" });

        group.MapPost("/interviewerscreening", async (RequestEditRecruitInterviewerScreening model, IRecruitService rep) => await rep.EditRecruitInterviewerScreening(model))
            .WithOpenApi(operation => new(operation) { Summary = "修改招聘流程(面试官初筛)" });

        group.MapPost("/interview", async (RequestEditRecruitInterview model, IRecruitService rep) => await rep.EditRecruitInterview(model))
            .WithOpenApi(operation => new(operation) { Summary = "修改招聘流程(面试)" });

        group.MapPost("/offer", async (RequestEditRecruitOffer model, IRecruitService rep) => await rep.EditRecruitOffer(model))
            .WithOpenApi(operation => new(operation) { Summary = "修改招聘流程(Offer)" });

        group.MapGet("/noticesubject/{recruitId}", (string recruitId, IRecruitService rep) => rep.GetNoticeSubjectForOffer(recruitId))
            .WithOpenApi(operation => new(operation) { Summary = "发送offer获取主体集合" });

        group.MapPost("/induction", async (RequestEditRecruitInduction model, IRecruitService rep) => await rep.EditRecruitInduction(model))
            .WithOpenApi(operation => new(operation) { Summary = "修改招聘流程(入职)" });

        group.MapPost("/fileAway", async (RequestEditRecruitFileAway model, IRecruitService rep) => await rep.EditRecruitFileAway(model))
            .WithOpenApi(operation => new(operation) { Summary = "修改招聘流程(归档)" });

        group.MapPost("/interview/cancel", (CancelInterviewRequest model, IRecruitService rep) => rep.CancelInterview(model))
            .WithOpenApi(operation => new(operation) { Summary = "取消面试邀请（hr主动取消）" });

        group.MapPost("/interview/feedback", (InterviewFeedbackRequest model, IRecruitService rep) => rep.InterviewFeedback(model))
            .WithOpenApi(operation => new(operation) { Summary = "hr填写面试反馈" });

        group.MapPost("/interviewscreen/feedback", (InterviewerScreenFeedbackRequest model, IRecruitService rep) => rep.InterviewerScreenFeedback(model))
            .WithOpenApi(operation => new(operation) { Summary = "hr填写面试官筛选反馈" });

        group.MapPost("/interview/examina", (RequestEditRecruitInterview model, IRecruitService rep) => rep.AddRecruitInterview(model))
            .WithOpenApi(operation => new(operation) { Summary = "新增招聘流程面试" });

        group.MapGet("/interviewer", ([AsParameters] GetProjectInterviewerListRequest model, IRecruitService rep) => rep.GetProjectInterviewerList(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取项目面试官列表" });

        group.MapPost("/interviewer", (ChangeProjectInterviewerRequest model, IRecruitService rep) => rep.ChangeProjectInterviewer(model))
            .WithOpenApi(operation => new(operation) { Summary = "变更项目面试官信息（添加、修改）" });

        group.MapDelete("/interviewer/{id}", (string id, IRecruitService rep) => rep.DeleteProjectInterviewer(id))
            .WithOpenApi(operation => new(operation) { Summary = "删除项目面试官信息" });

        group.MapGet("/resume/down/{recruitid}", (string recruitid, IRecruitService rep) => rep.DownLoadResume(recruitid))
            .WithOpenApi(operation => new(operation) { Summary = "下载用户信息简历" });

        group.MapPost("/invalid", async (RequestEditRecruitInvalidResume model, IRecruitService rep) => await rep.EditRecruitInvalidResume(model))
            .WithOpenApi(operation => new(operation) { Summary = "修改招聘流程(无效线索)" });

        group.MapGet("/reason", (CommonDicService commonDicService) =>
        {
            List<EnumInfo> enums = new();
            var baseInfo = commonDicService.GetBaseInfo("shareProfit_invalid_enum");
            baseInfo.ForEach(f =>
            {
                enums.Add(new EnumInfo { name = f.Code, value = f.Value });
            });
            return enums;
        }).WithOpenApi(operation => new(operation) { Summary = "获取无效线索原因" });

        group.MapPost("/resume/url", (UpLoadResumeModel model, IRecruitService rep) => rep.UpLoadResume(model))
            .WithOpenApi(operation => new(operation) { Summary = "上传简历并解析" });

        group.MapGet("/posts", (string TeamProjectId, IRecruitService rep) => rep.GetPostsList(TeamProjectId))
            .WithOpenApi(operation => new(operation) { Summary = "获取职位列表" });

        group.MapPost("/resumedelivery", (UpLoadResumeInfoRequest request, IRecruitService rep) => rep.ResumeDelivery(request))
            .WithOpenApi(operation => new(operation) { Summary = "推荐简历" });

        group.MapPost("/postexists", (UpLoadResumeInfoRequest request, IRecruitService rep) => rep.GetExists(request))
            .WithOpenApi(operation => new(operation) { Summary = "简历查重" });

        group.MapPost("/ksedit", (EditKuaiShouResume request, IRecruitService rep) => rep.EditKuaiShouResume(request))
            .WithOpenApi(operation => new(operation) { Summary = "修改快手简历" });

        group.MapGet("/interview/feedjudge/{id}", (string id, IRecruitService rep) => rep.WaitToFeedback(id))
            .WithOpenApi(operation => new(operation) { Summary = "判断是否存在未反馈面试安排" });

        group.MapPost("/export", (ExportRecruitRequest model, IRecruitService rep) =>
        {
            if (!MyRedis.Client.SetNx("recruit/export", 1, 5))
                throw new BadRequestException("限5秒一次，请稍后再试");

            var result = rep.ExportRecruit(model);

            var memoryStream = new MemoryStream();
            memoryStream.SaveAs(result);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return Results.File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"投递记录.xlsx");
        }).WithOpenApi(operation => new(operation) { Summary = "导出项目简历" });

        group.MapPost("/import/batch", async (BatchInductionRequest request, IRecruitService rep) => await rep.BatchInduction(request))
            .WithOpenApi(operation => new(operation) { Summary = "批量入职导入" });

        group.MapGet("/interviewtime/{recruitid}", async (string recruitid, IRecruitService rep) => await rep.GetInterviewTime(recruitid))
            .WithOpenApi(operation => new(operation) { Summary = "获取可预约面试时间" });
    }
}