﻿using Config.CommonModel;

namespace Staffing.Model.CentreService.System
{
    /// <summary>
    /// 人才配置
    /// </summary>
    public class GetRenCaiConfig : QueryRequest
    {
        /// <summary>
        /// 手机号
        /// </summary>
        public string? Mobile { get; set; }

    }

    public class GetRenCaiConfigResponse : QueryResponse
    {
        public List<RenCaiConfigInfo> Rows { get; set; } = new List<RenCaiConfigInfo>();
    }

    public class RenCaiConfigInfo
    {
        public string? Mobile { get; set; }

    }

    public class AddRenCaiConfig
    {
        /// <summary>
        /// 手机号(支持换行)
        /// </summary>
        public string? Mobile { get; set; }
    }

    public class DeleteRenCaiConfig
    {
        /// <summary>
        /// 手机号(支持换行)
        /// </summary>
        public string? Mobile { get; set; }
    }
}
