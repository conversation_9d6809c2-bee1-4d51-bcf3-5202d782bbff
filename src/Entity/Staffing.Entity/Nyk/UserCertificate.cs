﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Nyk;

/// <summary>
/// 实名认证表
/// </summary>
[Table("UserCertificate")]
public class UserCertificate
{
    /// <summary>
    /// id
    /// </summary>
    [Key]
    public string PK_UCID { get; set; } = default!;

    public string PK_UID { get; set; } = default!;

    public string VerifyKey { get; set; } = default!;

    public string? Content { get; set; }

    public int CertificateType { get; set; } = default!;
    public int Status { get; set; } = default!;

    /// <summary>
    /// 更新时间
    /// </summary>
    public int IsRemove { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
}

public class UserCertificateData
{
    public string? Name { get; set; }
    public string? IDCardNo { get; set; }
}