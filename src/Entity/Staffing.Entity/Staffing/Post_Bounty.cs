﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 职位分润表
/// </summary>
[Table("post_bounty")]
public class Post_Bounty
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 招聘流程Id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// 第三方简历池主键Id
    /// </summary>
    public string? ResumeBufferId { get; set; }

    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string TeamPostId { get; set; } = default!;

    /// <summary>
    /// 原始职位Id
    /// </summary>
    public string PostId { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 求职者Id
    /// </summary>
    public string SeekerId { get; set; } = default!;

    /// <summary>
    /// 销售Id
    /// </summary>
    public string? SaleUserId { get; set; }

    /// <summary>
    /// 主创Id
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 协同HrId
    /// </summary>
    public string TeamHrId { get; set; } = default!;

    /// <summary>
    /// 渠道商关系Id
    /// </summary>
    public string? ChannelId { get; set; }

    /// <summary>
    /// 交付类型
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }

    /// <summary>
    /// 打款天数
    /// </summary>
    public int? PaymentDays { get; set; }

    /// <summary>
    /// 打款方式
    /// </summary>
    public ProjectPaymentType? PaymentType { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal Money { get; set; } = 0.00m;

    /// <summary>
    /// 状态
    /// </summary>
    public BountyStatus Status { get; set; } = BountyStatus.交付中;

    /// <summary>
    ///  过保状态
    /// </summary>
    public GuaranteeStatus GuaranteeStatus { get; set; } = GuaranteeStatus.交付中;

    /// <summary>
    /// 是否减库存，默认0-否，1-是
    /// </summary>
    [ConcurrencyCheck]
    public int IfStockOut { get; set; } = 0;

    /// <summary>
    /// 交付失败原因描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public TeamBountySource Source { get; set; } = 0;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 是否删除，1-删除
    /// </summary>
    public bool Deleted { get; set; } = false;

    /// <summary>
    /// 线索跟进者Id
    /// </summary>
    public string? FollowerId { get; set; }

    /// <summary>
    /// 销售佣金比例
    /// </summary>
    public decimal SalesRate { get; set; } = 0.00m;

    /// <summary>
    /// 项目管理佣金比例
    /// </summary>
    public decimal ManagerRate { get; set; } = 0.00m;

    /// <summary>
    /// 线索佣金比例
    /// </summary>
    public decimal ClueRate { get; set; } = 0.00m;

    /// <summary>
    /// 邀面佣金比例
    /// </summary>
    public decimal FollowerRate { get; set; } = 0.00m;

    /// <summary>
    /// 平台佣金比例
    /// </summary>
    public decimal PlatformRate { get; set; } = 0.00m;

    ///// <summary>
    ///// 销售佣金
    ///// </summary>
    //public decimal SalesBounty { get; set; } = 0.00m;

    ///// <summary>
    ///// 项目管理佣金
    ///// </summary>
    //public decimal ManagerBounty { get; set; } = 0.00m;

    ///// <summary>
    ///// 线索佣金
    ///// </summary>
    //public decimal ClueBounty { get; set; } = 0.00m;

    ///// <summary>
    ///// 邀面佣金
    ///// </summary>
    //public decimal FollowerBounty { get; set; } = 0.00m;

    ///// <summary>
    ///// 平台佣金
    ///// </summary>
    //public decimal PlatformBounty { get; set; } = 0.00m;

    /// <summary>
    /// 奖励时效类型（单次结算, 长期结算）
    /// </summary>
    public PostRewardType RewardType { get; set; } = 0;

    /// <summary>
    /// 结算周期(按小时、天、月)
    /// </summary>
    public PostPaymentCycle? PaymentCycle { get; set; }

    /// <summary>
    /// 返费周期, null代表无限期
    /// </summary>
    public int? PaymentDuration { get; set; }

    /// <summary>
    /// 计算过保开始时间
    /// </summary>
    public DateTime? GuaranteeStartDate { get; set; }

    /// <summary>
    /// 归档日期
    /// </summary>
    public DateTime? FileAwayDate { get; set; }


    [ForeignKey(nameof(RecruitId))]
    public Recruit Recruit { get; set; } = default!;

    [ForeignKey(nameof(SeekerId))]
    public User_Seeker User_Seeker { get; set; } = default!;

    /// <summary>
    /// 项目经理
    /// </summary>
    [ForeignKey(nameof(HrId))]
    public User_Hr User_Hr { get; set; } = default!;

    /// <summary>
    /// 销售经理
    /// </summary>
    [ForeignKey(nameof(SaleUserId))]
    public User_Hr SaleUser { get; set; } = default!;

    /// <summary>
    /// 邀面
    /// </summary>
    [ForeignKey(nameof(FollowerId))]
    public User_Hr FollowerUser { get; set; } = default!;

    /// <summary>
    /// 线索方
    /// </summary>
    [ForeignKey(nameof(TeamHrId))]
    public User_Hr Team_User_Hr { get; set; } = default!;

    [ForeignKey(nameof(PostId))]
    public Post Post { get; set; } = default!;

    [ForeignKey(nameof(TeamPostId))]
    public Post_Team Post_Team { get; set; } = default!;

    [ForeignKey(nameof(ChannelId))]
    public Qd_Hr_Channel Qd_Hr_Channel { get; set; } = default!;

    public List<Post_Bounty_Stage> Post_Bounty_Stage { get; set; } = default!;
}