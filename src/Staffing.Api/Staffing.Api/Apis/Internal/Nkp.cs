using Config.CommonModel.Ndn;
using Infrastructure.CommonService.Ndn;
using Infrastructure.Extend;
using Infrastructure.Proxy;

namespace Staffing.Api.Apis.Internal;

[Service(ServiceLifetime.Transient)]
public class NkpRouter : IInternalRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        // 获取数字诺亚token
        var group = parentGroup.MapGroup("/internal/v1/nkp").WithTags("数字诺亚");

        group.MapGet("/ndn/token", (NdnApi ndnApi) => ndnApi.GetToken())
            .WithOpenApi(operation => new(operation) { Summary = "获取数字诺亚token" });
    }
}