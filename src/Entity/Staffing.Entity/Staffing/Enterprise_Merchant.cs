﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.CommonModel.Business;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 企业商户表
/// </summary>
[Table("enterprise_merchant")]
public class Enterprise_Merchant
{
    /// <summary>
    /// 主键
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 商户名称
    /// </summary>
    public string MerchantName { get; set; } = default!;

    /// <summary>
    /// 商户类型
    /// </summary>
    public MerchantType MerchantType { get; set; }

    /// <summary>
    /// 公章文件url
    /// </summary>
    public string? OfficeSeal { get; set; }

    /// <summary>
    /// 法人章文件url
    /// </summary>
    public string? LegalSeal { get; set; }

    /// <summary>
    /// 合同章文件url
    /// </summary>
    public string? ContractSeal { get; set; }

    /// <summary>
    /// 商户作为甲方与平台签署的合同号
    /// </summary>
    public string? PartyAContractNo { get; set; }

    /// <summary>
    /// 商户作为乙方与平台签署的合同号
    /// </summary>
    public string? PartyBContractNo { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string CreatedBy { get; set; } = default!;

    /// <summary>
    /// 更新人
    /// </summary>
    public string UpdatedBy { get; set; } = default!;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 发票相关配置
    /// </summary>
    public NdnInvoiceInfo? InvoiceInfo { get; set; }


    [ForeignKey(nameof(CreatedBy))]
    public User_Hr CreatedUser { get; set; } = default!;

    [ForeignKey(nameof(UpdatedBy))]
    public User_Hr UpdatedUser { get; set; } = default!;
}