﻿// using Config.CommonModel;
// using Staffing.Model.Common.NScore;

// namespace Staffing.Core.Interfaces.Seeker;

// public interface INScoreService
// {
//     Task<AddScoreResponse> AddScore(AddScore model);
//     GetScoreNotifyResponse GetScoreNotify(GetScoreNotify model);
//     GetScoreResponse GetScore(GetScore model);
//     EmptyResponse UpdateKdAddress(UpdateKdAddress model);
//     GetGoodsListResponse GetGoodsList(GetGoodsList model);
//     Task<BuyGoodsResponse> BuyGoods(BuyGoods model);
//     GetOrdersResponse GetOrders(GetOrders model);
//     GetScoreRecordResponse GetScoreRecord(GetScoreRecord model);
// }