using Config;
using Infrastructure.Extend;
using Staffing.Model.Hr.TalentResume;
using Staffing.Core.Interfaces.Hr;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class TalentResumeRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("talentresume").WithTags("聚合人才库");
        group.MapPost("/posts", (PostTeamPageListRequest request, ITalentResumeService rep) => rep.GetPostsByStr(request))
            .WithOpenApi(operation => new(operation) { Summary = "获取职位列表" });

        group.MapPost("/talents", (TalentResumePageListRequest request, ITalentResumeService rep) => rep.GetRecommendTalentResumeList(request))
            .WithOpenApi(operation => new(operation) { Summary = "获取推荐人才列表" });

        group.MapPost("/talents/test", (ITalentResumeService rep) => rep.GetRecommendTalentResumeListTest())
            .WithOpenApi(operation => new(operation) { Summary = "获取推荐人才列表测试" })
            .WithMetadata(new ApiMeta());

        group.MapGet("/detail/{resumeId}", (string resumeId, ITalentResumeService rep) => rep.GetTalentDetailByMainId(resumeId))
            .WithOpenApi(operation => new(operation) { Summary = "获取人才简历详情" });

        group.MapPost("/sms", (SendMessageRequest request, ITalentResumeService rep) => rep.SendMessage(request))
            .WithOpenApi(operation => new(operation) { Summary = "发送短信" });

        group.MapGet("/getphone/{resumeId}", (string resumeId, ITalentResumeService rep) => rep.GetPhoneNumber(resumeId))
            .WithOpenApi(operation => new(operation) { Summary = "获取手机号" });

        group.MapGet("/process/{resumeId}", (string resumeId, ITalentResumeService rep) => rep.GetProcessRecords(resumeId))
            .WithOpenApi(operation => new(operation) { Summary = "获取处理记录" });

        group.MapGet("/talents/select", (ITalentResumeService rep) => rep.GetSelectedRecords())
            .WithOpenApi(operation => new(operation) { Summary = "获取最近筛选" });

        group.MapGet("/hide/{resumeId}", (string resumeId, ITalentResumeService rep) => rep.HideResume(resumeId))
            .WithOpenApi(operation => new(operation) { Summary = "隐藏简历" });
    }
}