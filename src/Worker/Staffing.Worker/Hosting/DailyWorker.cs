using Config;
using Infrastructure.Common;
using Infrastructure.Extend;
using Staffing.Worker.Service;

namespace Staffing.Worker.Hosting;

public class DailyWorker : HostingBase
{
    private readonly DailyService _dailyService;
    private readonly PostService _postSubService;
    private readonly NykService _nykService;
    private readonly UserService _userSubService;
    private readonly UserTaskService _userTaskService;
    private readonly ProjectBountyService _projectBountyService;
    private readonly ProjectAutomaticService _projectAutomaticService;
    private readonly DingService _dingService;
    private readonly QYWechatService _qYWechatService;
    private readonly DataBoardService _dataBoardService;
    private readonly HrService _hrService;
    private readonly PaChongService _paChongService;
    private readonly SCRMBeschannelsService _sCRMBeschannelsService;
    private readonly LogManager _log;
    private readonly IHostEnvironment _hostingEnvironment;
    public DailyWorker(IHostApplicationLifetime lifetime, DailyService dailyService, ProjectAutomaticService projectAutomaticService, IHostEnvironment hostingEnvironment,
    PostService postSubService, PaChongService paChongService, NykService nykService,
    LogManager log, UserService userSubService, UserTaskService userTaskService, DataBoardService dataBoardService, HrService hrService,
    ProjectBountyService projectBountyService, DingService dingService, QYWechatService qYWechatService, SCRMBeschannelsService sCRMBeschannelsService) : base(lifetime)
    {
        _dailyService = dailyService;
        _postSubService = postSubService;
        _userSubService = userSubService;
        _projectAutomaticService = projectAutomaticService;
        _userTaskService = userTaskService;
        _log = log;
        _projectBountyService = projectBountyService;
        _dingService = dingService;
        _qYWechatService = qYWechatService;
        _dataBoardService = dataBoardService;
        _hostingEnvironment = hostingEnvironment;
        _paChongService = paChongService;
        _nykService = nykService;
        _hrService = hrService;
        _sCRMBeschannelsService = sCRMBeschannelsService;
    }

    public override async Task StartAsync(CancellationToken cancel)
    {
        _bgTasks.Add(await Task.Factory.StartNew(() => Worker1Hour(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => Worker5Min(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => Worker10Min(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => Worker30Seconds(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => Worker10Seconds(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => WorkerSeconds(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _dailyService.KSResumeToPost(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _projectBountyService.InterviewFeedback(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _projectBountyService.FinanceApproval(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _nykService.SubNykResumeSync(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _nykService.SubNykUserLoginNotifySync(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _nykService.SubNykHrUnreadResumeSync(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _nykService.SubNykDeptUnreadResumeSync(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        // _bgTasks.Add(await Task.Factory.StartNew(() => _nykService.SubNykOldResumeSync(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _hrService.SubXsjhToHr(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _hrService.HrAutoTeamProject(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _dingService.RecruitDingTalkRobot(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _dailyService.GetZxerJobDetail(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _dailyService.GetWbMoFangDetail(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _sCRMBeschannelsService.SubSyncToSCRMBeschannelTime(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _sCRMBeschannelsService.SubUpdateHrMobileFollowUp(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _qYWechatService.LoadQWGroupInfo(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _qYWechatService.QYWechatMemUpToCerRegs(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _dailyService.GentoCerTificate(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        //_bgTasks.Add(await Task.Factory.StartNew(() => _dailyService.GentoAdviserCerShare(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
    }

    private async Task Worker1Hour(CancellationToken cancel)
    {
        using var timer = new PeriodicTimer(TimeSpan.FromHours(1));
        try
        {
            while (await timer.WaitForNextTickAsync(cancel))
            {
                //清理日志
                await _dailyService.ClearLogs(cancel);

                //offer状态自动处理
                //await _projectAutomaticService.AutomaticForOffer(cancel);

                // 交付过保到期检测
                _projectBountyService.DeliveryBountyCheck(cancel);

                // 长期结算生成结算单
                _projectBountyService.GenerateSettlementBill(cancel);

                //统计钉钉推送消息
                await _dataBoardService.StatisticsDingDingMessage(cancel);

                //钉钉推送消息
                await _dingService.PushDingDingMessage(cancel);

                //同步销帮帮用户
                await _dailyService.XbbUserSync(cancel);

                //新用户推送
                if (_hostingEnvironment.IsProduction())
                    _dingService.NewUserPush(cancel);

                //钉钉数据同步
                if (_hostingEnvironment.IsProduction())
                    await _dingService.DdAsync(cancel);

                //爬社群
                if (_hostingEnvironment.IsProduction())
                    await _paChongService.WxSheQun(cancel, 6);

                // //分润任务 - 同步数字诺亚帐套, 如果有新帐套并生成待办
                // await _projectBountyService.ProfitSharingTask4(cancel);

                try
                {
                    //await _dailyService.GetZxerJobList(cancel);
                }
                catch { }
                ////自动协同
                //_dailyService.HrAutoTeam(cancel);

                // 首次执行是十月一，假期，暂时不执行
                // //分润任务 - 计算并生成服务奖金相关
                // await _projectBountyService.ProfitSharingTask2(cancel);

                //统计数据拓扑大屏
                await _dataBoardService.StatisticsTopology(cancel);

                //诺优考简历未读提前3天提醒
                await _nykService.SubNykHrUnreadResumeNotifySync(cancel);

                //同步数字诺亚帐套
                await _projectBountyService.SyncNdnBooks(cancel);

                // 切换新服务，避免token冲突，先暂停。
                // // 员工账套关系
                // await _userSubService.UserPartmentUpdateAsync(cancel);
            }
        }
        catch (OperationCanceledException) { }
    }

    private async Task Worker5Min(CancellationToken cancel)
    {
        using var timer = new PeriodicTimer(TimeSpan.FromMinutes(5));
        try
        {
            while (await timer.WaitForNextTickAsync(cancel))
            {
                //处理hr排行榜
                _userSubService.UpdateHrRank(cancel);

                //计算日活
                _dailyService.CountDailyUser(cancel);

                await Task.Delay(1000);

                // //同步销帮帮用户（临时）
                // await _dailyService.XbbUserSync(cancel);

                //首页第三方数据统计
                _ = _dailyService.HomePageThirdPartyData(cancel);
                //自动协同
                _dailyService.HrAutoTeam(cancel);
                await Task.Delay(1000);

                //首页各种数量统计
                _dailyService.PlatformCount(cancel);

                //定期检测缺失数据
                await _dailyService.CheckLoseData(cancel);

                //职位优选报名过期检测
                _dingService.ExcellentPostMsg(cancel);

                //数据大屏首页
                await _dailyService.HrDataCenterHomePage(cancel);

                try
                {
                    MyRedis.Client.Set($"{RedisKey.WorkerKeyPre}_Worker5Min", DateTime.Now.ToYYYY_MM_DD_HH(), TimeSpan.FromDays(1));
                }
                catch (Exception e)
                {
                    _log.Error("Worker检测出错", Tools.GetErrMsg(e));
                }
            }
        }
        catch (OperationCanceledException) { }
    }

    private async Task Worker10Min(CancellationToken cancel)
    {
        using var timer = new PeriodicTimer(TimeSpan.FromMinutes(10));
        try
        {
            while (await timer.WaitForNextTickAsync(cancel))
            {
                // //爬社群
                // if (_hostingEnvironment.IsProduction())
                //     await _paChongService.WxSheQun(cancel, 1);

                // 每月生成凭证
                await _projectBountyService.GenerateVoucher(cancel);
            }
        }
        catch (OperationCanceledException) { }
    }

    private async Task Worker10Seconds(CancellationToken cancel)
    {
        using var timer = new PeriodicTimer(TimeSpan.FromSeconds(10));
        try
        {
            while (await timer.WaitForNextTickAsync(cancel))
            {
                //发送报警邮件
                _dailyService.SendWarningMail(cancel);

                //处理归档项目
                _postSubService.ProjectFileAway(cancel);

                //劳动力采集顾问渠道状态归档
                _postSubService.CounselorChannelStatusArchive(cancel);

                //诺聘企业投递、职位信息同步
                _dailyService.NoahEntync(cancel);

                // //诺优考同步简历
                // await _nykService.NykResumeSync(cancel);

                // //诺优考同步用户登录时间
                // await _nykService.NykUserLoginSync(cancel);

                // 快招工简历同步：1.超过三天未推送 2.关联过诺快聘职位
                await _nykService.KgzResumeSync(cancel);

                // HR初筛主创未处理每日9，11，16点提醒（提醒时间为30分钟，即9:00-9:30,11:00-11:30,16:00-16:30）
                await _dingService.PushDingDingMessageForRecruit9(cancel);
                await _dingService.PushDingDingMessageForRecruit11(cancel);
                await _dingService.PushDingDingMessageForRecruit16(cancel);

                // 钉钉消息已读处理
                //await _dingService.DingDingMessageReadStatus(cancel);
            }
        }
        catch (OperationCanceledException) { }
    }

    private async Task Worker30Seconds(CancellationToken cancel)
    {
        using var timer = new PeriodicTimer(TimeSpan.FromSeconds(30));
        try
        {
            while (await timer.WaitForNextTickAsync(cancel))
            {
                // //定期清理隐私号码绑定
                // _ = _dailyService.PrivatePhoneClean(cancel);

                // 处理预离职员工
                _userTaskService.ProjectMemberQuit(cancel);

                // // 入职过保到期检测
                // _projectBountyService.InductionBountyCheck(cancel);

                // 交付过保到期检测
                if (!_hostingEnvironment.IsProduction())
                    _projectBountyService.DeliveryBountyCheck(cancel);

                // 长期结算生成结算单
                if (!_hostingEnvironment.IsProduction())
                    _projectBountyService.GenerateSettlementBill(cancel);

                // 项目结算单状态更新
                _projectBountyService.PostBountyUpdateStatus(cancel);

                // // 过保检测
                // _projectBountyService.GenerateSingleSettlementBill(cancel);

                // // 分润任务 - 生成转账报销合同
                // await _projectBountyService.ProfitSharingTask(cancel);

                // //分润任务 - 自动处理待办
                // _ = _projectBountyService.ProfitSharingTask3(cancel);

                //同步数字诺亚销售机会
                await _dailyService.NoahXsjhSync(cancel);

                //同步数字诺亚合同
                await _dailyService.XbbSync(cancel);

                // 生成凭证文件
                await _projectBountyService.GenerateVoucherFile(cancel);

                // // 简历交付倒计时7天检测
                // _projectBountyService.ResumeBountyCheck(cancel);

                // // 到面交付倒计时3天检测
                // _projectBountyService.InterviewBountyCheck(cancel);

                // // 按天入职过保到期检测 - 首次
                // _projectBountyService.MonthlyBountyCheck(cancel);

                // // 按天入职过保到期检测 - 每日
                // _projectBountyService.MonthlyBountyCheckByEveryDay(cancel);
            }
        }
        catch (OperationCanceledException) { }
    }

    private async Task WorkerSeconds(CancellationToken cancel)
    {
        using var timer = new PeriodicTimer(TimeSpan.FromSeconds(1));
        try
        {
            while (await timer.WaitForNextTickAsync(cancel))
            {
                // 保存oss地址
                _dailyService.SaveOssUrl(cancel);

                // 删除oss地址 - 逻辑删除
                _dailyService.DeleteOssUrl(cancel);
            }
        }
        catch (OperationCanceledException) { }
    }
}