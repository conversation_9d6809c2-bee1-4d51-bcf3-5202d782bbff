using Infrastructure.Extend;
using Staffing.Model.Hr.WxGroup;
using Staffing.Core.Interfaces.Hr;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class WxGroupRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("wxgroup").WithTags("微信社群");
        group.MapPost("/group/list", (SearchRequest request, IWxGroupService rep) => rep.GetWxGroupList(request))
            .WithOpenApi(operation => new(operation) { Summary = "获取社群列表" });

        group.MapPost("/group/summary", (IWxGroupService rep) => rep.GetGroupSummary())
            .WithOpenApi(operation => new(operation) { Summary = "获取社群汇总" });

        group.MapGet("/detail/view/{groupId}", (long groupId, IWxGroupService rep) => rep.ViewGroup(groupId))
            .WithOpenApi(operation => new(operation) { Summary = "查看群二维码" });

        group.MapGet("/group/select/{type}", (DataType type, IWxGroupService rep) => rep.GetTwoLevelDatas(type))
            .WithOpenApi(operation => new(operation) { Summary = "获取筛选数据" });
    }
}