﻿using System.ComponentModel;

namespace Config.Enums;

/// <summary>
/// 在职状态枚举类
/// </summary>
public enum ExtInfoJobHuntingStatus
{
    [Description("离职，找工作中，随时到岗")]
    离职 = 1,
    [Description("在职，找工作中，月内到岗")]
    在职找工作中月内到岗,
    [Description("在职，看看好的工作机会")]
    在职看看好的工作机会,
    [Description("在职，暂时不找工作")]
    在职暂时不找工作
}

/// <summary>
/// 顾问简历关系表 - 是否回访
/// </summary>
public enum ReturnVisit
{
    未回访,已回访
}

/// <summary>
/// 是否关联诺快聘项目
/// </summary>
public enum BindJob
{
    未绑定, 已绑定
}

/// <summary>
/// 顾问简历关系表 - 简历筛选是否有效
/// </summary>
public enum ResultStatus
{
    无效,有效
}

/// <summary>
/// 二次回访是否转换：0-未转化，1-已转化
/// </summary>
public enum SecondVisit
{
    未转化, 已转化
}

/// <summary>
/// 第三方平台类型
/// </summary>
public enum ThirdPlatType
{
    快招工, 诺聘, 导入, 其他 = 99
}

public enum TalentResumeType
{
    已同步 = 1,
    已推送,
    非诺快聘职位,
    数据不合格 = 99
}

/// <summary>
/// 是否推送
/// </summary>
public enum KuaishouStatus
{
    未推送, 已推送
}

/// <summary>
/// 是否等会再打电话
/// </summary>
public enum WaitToPhone
{
    不是, 是
}

/// <summary>
/// Tab标签页
/// </summary>
public enum TabStatus
{
    待我跟进,待同事跟进,等会再打,无效简历
}

/// <summary>
/// 无效简历类型
/// </summary>
public enum InvalidType
{
    误投 = 1,
    超过50岁,
    重复投递,
    省外异地,
    未毕业,
    身体问题,
    放弃找工作,
    已有工作或不找工作,
    其他 = 99
}

public class KuaishouConst
{
    /// <summary>
    /// 快招工回访锁
    /// </summary>
    public const string RELATION_CONST_LOCK_KEY_STR = "kuaishou_relation_lock_key_str";

    /// <summary>
    /// 快招工订单锁
    /// </summary>
    public const string POST_ORDER_CONST_LOCK_KEY_STR = "kuaishou_post_order_lock_key_str";

    /// <summary>
    /// 快招工导出excel锁
    /// </summary>
    public const string EXPORT_EXCEL_CONST_LOCK_KEY_STR = "kuaishou_post_order_lock_key_str";

    /// <summary>
    /// 职位库存锁前缀
    /// </summary>
    public const string POST_ORDER_STOCK_CONST_LOCK_KEY_STR = "order_stock_key";
}