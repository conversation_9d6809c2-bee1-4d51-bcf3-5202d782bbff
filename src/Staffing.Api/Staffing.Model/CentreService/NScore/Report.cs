// using Config.Enums;
// using Config.CommonModel;

// namespace Staffing.Model.CentreService.NScore;

// public class GetScoreReport : QueryRequest
// {
//     /// <summary>
//     /// hr还是求职者
//     /// </summary>
//     public SeekerOrHr UserType { get; set; } = SeekerOrHr.Seeker;

//     /// <summary>
//     /// 用户姓名
//     /// </summary>
//     public string? UserName { get; set; }

//     /// <summary>
//     /// 用户手机号
//     /// </summary>
//     public string? UserMobile { get; set; }

//     /// <summary>
//     /// 积分小
//     /// </summary>
//     public int? MinScore { get; set; }

//     /// <summary>
//     /// 积分大
//     /// </summary>
//     public int? MaxScore { get; set; }

//     /// <summary>
//     /// 起始时间
//     /// </summary>
//     public DateTime? BeginTime { get; set; }

//     /// <summary>
//     /// 结束时间
//     /// </summary>
//     public DateTime? EndTime { get; set; }
// }

// public class GetScoreReportResponse : QueryResponse
// {
//     /// <summary>
//     /// 累计积分
//     /// </summary>
//     public int TotalScore { get; set; }

//     /// <summary>
//     /// 使用积分
//     /// </summary>
//     public int UsedScore { get; set; }

//     /// <summary>
//     /// 当前积分
//     /// </summary>
//     public int CurrentScore { get; set; }

//     /// <summary>
//     /// 已兑换现金
//     /// </summary>
//     public decimal MoneyScore { get; set; }

//     /// <summary>
//     /// 兑换实物的积分
//     /// </summary>
//     public int PrizeScore { get; set; }

//     /// <summary>
//     /// 提现金额
//     /// </summary>
//     public decimal Withdraw { get; set; }

//     /// <summary>
//     /// 用户列表
//     /// </summary>
//     public List<GetScoreReportInfo>? Rows { get; set; }
// }

// public class GetScoreReportInfo
// {
//     /// <summary>
//     /// 用户Id
//     /// </summary>
//     public string? UserId { get; set; }

//     /// <summary>
//     /// 用户手机号
//     /// </summary>
//     public string? UserMobile { get; set; }

//     /// <summary>
//     /// 用户姓名
//     /// </summary>
//     public string? UserName { get; set; }

//     /// <summary>
//     /// 累计积分
//     /// </summary>
//     public int TotalScore { get; set; }

//     /// <summary>
//     /// 使用积分
//     /// </summary>
//     public int UsedScore { get; set; }

//     /// <summary>
//     /// 当前积分
//     /// </summary>
//     public int CurrentScore { get; set; }

//     /// <summary>
//     /// 兑换现金的积分
//     /// </summary>
//     public decimal MoneyScore { get; set; }

//     /// <summary>
//     /// 兑换实物的积分
//     /// </summary>
//     public int PrizeScore { get; set; }

//     /// <summary>
//     /// 提现金额
//     /// </summary>
//     public decimal Withdraw { get; set; }

//     /// <summary>
//     /// 积分变更时间
//     /// </summary>
//     public DateTime? ScoreChangeTime { get; set; }

//     /// <summary>
//     /// 注册时间
//     /// </summary>
//     public DateTime CreatedTime { get; set; }
// }