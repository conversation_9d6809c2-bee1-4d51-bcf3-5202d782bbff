using Config;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.Extend;
using Magicodes.ExporterAndImporter.Excel;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using MiniExcelLibs;
using MiniExcelLibs.Attributes;
using Newtonsoft.Json.Linq;
using NPOI.XWPF.UserModel;
using ServiceStack.Text;

namespace Infrastructure.NoahCommon;

/// <summary>
/// 解析岗位
/// </summary>
[Service(ServiceLifetime.Transient)]
public class ParseResume
{
    System.Net.Http.IHttpClientFactory HttpClientFactory;
    private readonly IHostEnvironment _hostEnvironment;
    private ConfigManager _config;
    private readonly LogManager _log;
    public ParseResume(System.Net.Http.IHttpClientFactory _HttpClientFactory, IHostEnvironment hostEnvironment,
    IOptionsSnapshot<ConfigManager> config, LogManager log, IWebHostEnvironment hostWebEnvironment)
    {
        HttpClientFactory = _HttpClientFactory;
        _hostEnvironment = hostEnvironment;
        _config = config.Value;
        _log = log;
    }

    /// <summary>
    /// 解析岗位标签
    /// </summary>
    /// <param name="job"></param>
    /// <returns></returns>
    public async Task<XxJobExplainResponse> ParsePosition(XxJobExplain job)
    {
        var result = new XxJobExplainResponse();
        var url = _config.ParseResumeDomain + ":8002/parse_jd";
        try
        {
            var resp = await url.PostJsonAsync(new { job = job }).ReceiveJson<XxJobExplainResultModel>();

            if (resp.errorcode != 0)
                throw new Exception(resp.errormessage);

            var skills = resp.parsing_result?["tags"]?["skills"]?.Value<JArray>();

            result.Skills = skills?.Select(s => s["tag"]!.Value<string>()!)?.ToList() ?? new List<string>();
        }
        catch (Exception e)
        {
            _log.Error("解析岗位出错", e.Message, JsonSerializer.SerializeToString(job));
        }
        return result;
    }

    /// <summary>
    /// 小析服务解析简历（简历全量）
    /// </summary>
    /// <param name="fileUrl"></param>
    /// <param name="fileName"></param>
    /// <returns></returns>
    public async Task<(List<AnalysisResumeResponse> Data, List<FaildResumeResponse> FaildData, List<ExcelResume> Importer)> AnalysisResume(string fileUrl, string fileName)
    {
        var retModel = new List<AnalysisResumeResponse>();
        var faildData = new List<FaildResumeResponse>();
        var import = new List<ExcelResume>();
        try
        {
            var dic = new Dictionary<string, AnalysisResumeItem>();

            //请求前的准备（解析Excel文件）
            (dic, import, _) = await AnalysisResumeFun(fileUrl, fileName);

            (retModel, faildData) = await AnalysisResumeByDic(dic, fileUrl: fileUrl, fileName: fileName);
        }
        catch (Exception ex)
        {
            _log.Error("小析服务解析简历出错", Tools.GetErrMsg(ex), fileUrl + "|||" + fileName);
        }

        return (retModel, faildData, import);
    }

    /// <summary>
    /// 小析服务解析简历（简历全量）
    /// </summary>
    /// <param name="fileUrl"></param>
    /// <param name="fileName"></param>
    /// <returns></returns>
    public async Task<(Dictionary<string, AnalysisResumeItem> Dic, List<ExcelResume> Import, int RepeatCount)> AnalysisResumeFun(string fileUrl, string fileName)
    {
        var import = new List<ExcelResume>();
        var dic = new Dictionary<string, AnalysisResumeItem>();
        int RepeatCount = 0;//重复简历数
        //请求前的准备（解析Excel文件）
        if (Path.GetExtension(fileName) == ".xlsx" || Path.GetExtension(fileName) == ".xls")
        {
            string errInfo = string.Empty;
            // ICollection<ExcelResume>? importList;
            var fileBytes = await fileUrl.GetAsync().ReceiveBytes();
            var importer = new ExcelImporter();
            // var import = await importer.Import<ExcelResume>(new MemoryStream(fileBytes));

            //Magicodes.IE.Excel缺陷，迟迟无法解决重复邮箱导致挂掉的bug，所以换MiniExcel
            import = MiniExcel.Query<ExcelResume>(new MemoryStream(fileBytes)).Select(x => FilterAbnormalData(x)).ToList();
            // if (import is null)
            //     errInfo = "文件不存在或内容格式错误";
            // if (import?.Exception != null)
            //     errInfo = import.Exception.ToString();
            // if (import?.RowErrors.Count > 0)
            //     errInfo = import.RowErrors.ToJsonString();

            // importList = import?.Data;

            if (import.Count > 0)
            {
                DateTime nowYear = new DateTime(DateTime.Now.Year, 1, 1);
                List<string> filterRepeat = [];//防止姓名+手机号重复
                foreach (var item in import)
                {
                    //姓名和手机号都为空的，跳过
                    if (string.IsNullOrEmpty(item.Name) && string.IsNullOrEmpty(item.Phone))
                    {
                        RepeatCount++;
                        continue;
                    }
                    string repeatItem = item.Name ?? string.Empty + item.Phone ?? string.Empty;
                    if (filterRepeat.Contains(repeatItem))
                    {
                        RepeatCount++;
                        _log.Error("小析服务解析简历（简历全量）", $"姓名+手机号({repeatItem})重复了", JsonSerializer.SerializeToString(item));
                        continue;
                    }
                    else
                        filterRepeat.Add(repeatItem);

                    Dictionary<string, string> dicResume = new Dictionary<string, string>();
                    dicResume.Add("UserName", item.Name ?? string.Empty);
                    dicResume.Add("Sex", item.Sex ?? string.Empty);
                    //因业务同事反馈，大多数简历中没有生日，仅有年龄
                    if (!string.IsNullOrEmpty(item.Birthday) && int.TryParse(item.Birthday, out var Birthday) && Birthday > 0)
                        dicResume.Add("Birthday", nowYear.AddYears(-Birthday).ToString("yyyy-MM-ddd"));
                    else
                        dicResume.Add("Birthday", item.Birthday ?? string.Empty);
                    dicResume.Add("Degree", item.Degree ?? string.Empty);
                    dicResume.Add("Email", item.Email ?? string.Empty);
                    dicResume.Add("WorkTime", item.WorkTime ?? string.Empty);
                    dicResume.Add("Present", item.Present ?? string.Empty);
                    dicResume.Add("Phone", item.Phone ?? string.Empty);
                    dicResume.Add("Marriage", item.Marriage ?? string.Empty);
                    dicResume.Add("Politics", item.Politics ?? string.Empty);
                    PositionPurpose ppeModel = new PositionPurpose();
                    // ppeModel.Cityitem.ExpectCity;
                    // ppeModel.Positionitem.ExpectPost;
                    ppeModel.Salary = item.ExpectSalary;
                    ppeModel.CityName = item.ExpectCity;
                    ppeModel.PositionName = item.ExpectPost;
                    dicResume.Add("Purpose", "期望薪水:" + ppeModel.Salary + "  期望职位:" + ppeModel.PositionName + "  期望工作地区：" + ppeModel.CityName);
                    dicResume.Add("Education", item.Education ?? string.Empty);
                    dicResume.Add("Work", item.Work ?? string.Empty);
                    dicResume.Add("Project", item.Project ?? string.Empty);
                    string tempPath = Path.Combine(AppContext.BaseDirectory, "TempFile", "ResumeTemp.docx");
                    string base64Resume = ExportBase64(tempPath, dicResume);

                    dic.Add(base64Resume, new AnalysisResumeItem { FileName = item.Name + "的简历.docx", UserName = item.Name ?? string.Empty, Mobile = item.Phone ?? string.Empty });
                }
            }
        }
        else
        {
            var bt = await fileUrl.GetAsync().ReceiveBytes();
            string base64Str = Convert.ToBase64String(bt);
            dic.Add(base64Str, new AnalysisResumeItem { FileName = fileName });
        }
        return (dic, import, RepeatCount);
    }

    /// <summary>
    /// 过滤异常数据
    /// </summary>
    /// <param name="import"></param>
    /// <returns></returns>
    private ExcelResume FilterAbnormalData(ExcelResume import)
    {
        import.Name = FilterAbnormalString(import.Name);
        import.Phone = FilterAbnormalString(import.Phone);
        import.Sex = FilterAbnormalString(import.Sex);
        import.Birthday = FilterAbnormalString(import.Birthday);
        import.Politics = FilterAbnormalString(import.Politics);
        import.Marriage = FilterAbnormalString(import.Marriage);
        import.Degree = FilterAbnormalString(import.Degree);
        import.WeChatName = FilterAbnormalString(import.WeChatName);
        import.Email = FilterAbnormalString(import.Email);
        import.WorkTime = FilterAbnormalString(import.WorkTime);
        import.ExpectCity = FilterAbnormalString(import.ExpectCity);
        import.ExpectPost = FilterAbnormalString(import.ExpectPost);
        import.ExpectSalary = FilterAbnormalString(import.ExpectSalary);
        import.Present = FilterAbnormalString(import.Present);
        import.Education = FilterAbnormalString(import.Education);
        import.Work = FilterAbnormalString(import.Work);
        import.Project = FilterAbnormalString(import.Project);

        return import;
    }
    private string? FilterAbnormalString(string? str)
    {
        if (!string.IsNullOrEmpty(str))
            str = str.TrimStart('\n').TrimEnd('\n').Trim();
        return str;
    }

    /// <summary>
    /// 小析服务解析简历（简历全量）
    /// </summary>
    /// <param "fileUrl" ></ param >
    /// < returns ></ returns >
    public async Task<(List<AnalysisResumeResponse> AnalysisData, List<FaildResumeResponse> FaildData)> AnalysisResumeByDic(Dictionary<string, AnalysisResumeItem> dic, bool IsThrowException = true, string fileUrl = null, string fileName = null)
    {
        var retModel = new List<AnalysisResumeResponse>();
        var faildData = new List<FaildResumeResponse>();
        foreach (var i in dic)
        {
            var result = await AnalysisResumeFun(i, IsThrowException, fileUrl, fileName);
            if (result.AnalysisData != null)
                retModel.Add(result.AnalysisData);
            else if (result.FaildData != null)
                faildData.Add(result.FaildData);
        }

        return (retModel, faildData);
    }


    /// <summary>
    /// 小析服务解析简历（简历全量）
    /// </summary>
    /// <param name="Dept">每循环几次返回</param>
    /// <param name="dic"></param>
    /// <param name="IsThrowException"></param>
    /// <param name="fileUrl"></param>
    /// <param name="fileName"></param>
    /// <returns></returns>
    public IEnumerable<(List<AnalysisResumeResponse> AnalysisData, List<FaildResumeResponse> FaildData)> AnalysisResumeByDicYield(int Dept, Dictionary<string, AnalysisResumeItem> dic, bool IsThrowException = true, string? fileUrl = null, string? fileName = null)
    {
        if (Dept < 1)
            Dept = 1;
        var retModel = new List<AnalysisResumeResponse>();
        var faildData = new List<FaildResumeResponse>();
        int index = 0;
        foreach (var i in dic)
        {
            if (i.Key == "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")
            { }
            var result = AnalysisResumeFun(i, IsThrowException, fileUrl, fileName).Result;
            if (result.AnalysisData != null)
                retModel.Add(result.AnalysisData);
            else if (result.FaildData != null)
                faildData.Add(result.FaildData);

            index++;
            if (index == Dept)
            {
                index = 0;
                yield return (retModel, faildData);
                retModel = [];
                faildData = [];
            }
        }
        // 返回剩余不足Dept个的元素
        if (index > 0)
            yield return (retModel, faildData);
    }

    private async Task<(AnalysisResumeResponse? AnalysisData, FaildResumeResponse? FaildData)> AnalysisResumeFun(KeyValuePair<string, AnalysisResumeItem> i, bool IsThrowException = true, string? fileUrl = null, string? fileName = null)
    {
        AnalysisResumeResponse? AnalysisData = null;
        FaildResumeResponse? FaildData = null;
        using (HttpClient httpClient = HttpClientFactory.CreateClient())
        {
            AnalysisResumeResponse resultModel = new AnalysisResumeResponse();
            string content = "{ \"resume_base\":\"" + i.Key + "\",\"file_name\":\"" + i.Value.FileName + "\"}";
            StringContent stringContent = new StringContent(content);
            stringContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/json");
            try
            {
                var result = await httpClient.PostAsync(_config.ParseResumeDomain + ":8000/analyze_base?parsing_result=1&handle_image=1&avatar=0&ocr_mode=general&ocr_service=tencent&parse_mode=" + _config.ParseResumeModeType, stringContent);

                string str = await result.Content.ReadAsStringAsync();
                resultModel = JsonSerializer.DeserializeFromString<AnalysisResumeResponse>(str);
                if (resultModel.errorcode != 0)
                {
                    FaildData = new FaildResumeResponse { UserName = i.Value.UserName, Mobile = i.Value.Mobile, FaildMsg = resultModel.errormessage ?? resultModel.ToJsonString() };
                    if (IsThrowException)
                        throw new Exception(resultModel.errormessage);
                    else
                        _log.Error("小析服务解析简历出错", resultModel.errormessage ?? resultModel.ToJsonString(), (fileUrl ?? string.Empty) + "|||" + (fileName ?? string.Empty));
                }
                AnalysisData = resultModel;
            }
            catch (Exception ee)
            {
                FaildData = new FaildResumeResponse { UserName = i.Value.UserName, Mobile = i.Value.Mobile, FaildMsg = ee.Message };
                if (IsThrowException)
                    throw new Exception(resultModel.errormessage);
                else
                    _log.Error("小析服务解析简历引发异常", ee.Message, (fileUrl ?? string.Empty) + "|||" + (fileName ?? string.Empty));
            }
        }
        return (AnalysisData, FaildData);
    }


    #region 私有方法
    /// <summary>
    /// 输出模板base64文档
    /// </summary>
    /// <param name="tempFilePath"></param>
    /// <param name="data"></param>
    /// <returns></returns>
    private string ExportBase64(string tempFilePath, Dictionary<string, string> data)
    {
        string result = string.Empty;
        using (FileStream stream = System.IO.File.OpenRead(tempFilePath))
        {
            XWPFDocument doc = new XWPFDocument(stream);
            //遍历段落                  
            foreach (var para in doc.Paragraphs)
            {
                ReplaceKey(para, data);
            }
            //遍历表格      
            foreach (var table in doc.Tables)
            {
                foreach (var row in table.Rows)
                {
                    foreach (var cell in row.GetTableCells())
                    {
                        foreach (var para in cell.Paragraphs)
                        {
                            ReplaceKey(para, data);
                        }
                    }
                }
            }
            using (MemoryStream ms = new MemoryStream())
            {
                doc.Write(ms);
                result = Convert.ToBase64String(ms.ToArray());
            }
            return result;
        }
    }
    /// <summary>
    /// 替换word文档里的占位符
    /// </summary>
    /// <param name="para"></param>
    /// <param name="data"></param>
    private void ReplaceKey(XWPFParagraph para, Dictionary<string, string> data)
    {
        string text = "";
        List<string> dicList = new List<string>();
        foreach (var run in para.Runs)
        {
            text = run.ToString();
            foreach (var key in data.Keys)
            {
                //$$模板中数据占位符为$KEY$
                if (text.Contains($"${key}$"))
                {
                    text = text.Replace($"${key}$", data[key]);
                    run.SetText(text, 0);
                }
            }
        }
    }
    #endregion
}

//小希解析返回基类
public class XxJobExplainResultModel
{
    public int errorcode { get; set; }
    public string? errormessage { get; set; }
    public JToken parsing_result { get; set; } = default!;
}

//小希解析职位
public class XxJobExplain
{
    public string description { get; set; } = default!;
    public string job_title { get; set; } = default!;
}

//小希解析职位返回
public class XxJobExplainResponse
{
    public List<string>? Skills { get; set; }
}

/// <summary>
/// 小析解析模型
/// </summary>
public class AnalysisResumeResponse
{
    /// <summary>
    /// 返回码
    /// </summary>
    public int errorcode { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? errormessage { get; set; }

    /// <summary>
    /// 简历头像url
    /// </summary>
    public string? avatar_url { get; set; }

    /// <summary>
    /// 简历解析
    /// </summary>
    public parsing_result? parsing_result { get; set; }

    /// <summary>
    /// 画像解析
    /// </summary>
    public predicted_result? predicted_result { get; set; }
}

/// <summary>
/// 小析解析模型
/// </summary>
public class AnalysisResumeItem : FaildResumeResponse
{
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = default!;
}

/// <summary>
/// 解析简历失败信息
/// </summary>
public class FaildResumeResponse
{
    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// 用户手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 失败原因
    /// </summary>
    public string? FaildMsg { get; set; }
}

#region 简历解析
/// <summary>
/// 小析解析简历解析
/// </summary>
public class parsing_result
{
    /// <summary>
    /// 基本信息字段
    /// </summary>
    public basic_info_sub? basic_info { get; set; }

    /// <summary>
    /// 联系方式字段
    /// </summary>
    public contact_info_sub? contact_info { get; set; }

    /// <summary>
    /// 教育背景字段
    /// </summary>
    public List<education_experience_sub>? education_experience { get; set; }

    /// <summary>
    /// 工作经历字段
    /// </summary>
    public List<work_experience_sub>? work_experience { get; set; }

    /// <summary>
    /// 项目经历
    /// </summary>
    public List<project_experience_sub>? project_experience { get; set; }

    /// <summary>
    /// 补充信息
    /// </summary>
    public others_sub? others { get; set; }
}

/// <summary>
/// 基本信息（子类）
/// </summary>
public class basic_info_sub
{
    /// <summary>
    /// 姓名
    /// </summary>
    public string name { get; set; } = string.Empty;

    /// <summary>
    /// 性别
    /// </summary>
    public string gender { get; set; } = string.Empty;

    /// <summary>
    /// 学历
    /// </summary>
    public string degree { get; set; } = string.Empty;

    /// <summary>
    /// 开始工作年限
    /// </summary>
    public string work_start_year { get; set; } = string.Empty;

    /// <summary>
    /// 生日
    /// </summary>
    public string date_of_birth { get; set; } = string.Empty;

    /// <summary>
    /// 所在地
    /// </summary>
    public string current_location { get; set; } = string.Empty;

    /// <summary>
    /// 所在地（标准化）
    /// </summary>
    public string current_location_norm { get; set; } = string.Empty;

    /// <summary>
    /// 详细地址
    /// </summary>
    public string detailed_location { get; set; } = string.Empty;

    /// <summary>
    /// 工作经验
    /// </summary>
    public int num_work_experience { get; set; }

    /// <summary>
    /// 期望职位
    /// </summary>
    public string desired_position { get; set; } = string.Empty;

    /// <summary>
    /// 期望薪水
    /// </summary>
    public string desired_salary { get; set; } = string.Empty;

    /// <summary>
    /// 期望行业
    /// </summary>
    public string desired_industry { get; set; } = string.Empty;

    /// <summary>
    /// 期望工作地区
    /// </summary>
    public string expect_location { get; set; } = string.Empty;

    /// <summary>
    /// 年龄
    /// </summary>
    public int? age { get; set; }
}

/// <summary>
/// 联系方式（子类）
/// </summary>
public class contact_info_sub
{
    /// <summary>
    /// 手机号
    /// </summary>
    public string phone_number { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string email { get; set; } = string.Empty;

    /// <summary>
    /// QQ号
    /// </summary>
    public string QQ { get; set; } = string.Empty;

    /// <summary>
    /// 微信号
    /// </summary>
    public string wechat { get; set; } = string.Empty;
}

/// <summary>
/// 教育背景（子类）
/// </summary>
public class education_experience_sub
{
    /// <summary>
    /// 开始时间年份
    /// </summary>
    public string start_time_year { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间月份
    /// </summary>
    public string start_time_month { get; set; } = string.Empty;

    /// <summary>
    /// 结束时间年份
    /// </summary>
    public string end_time_year { get; set; } = string.Empty;

    /// <summary>
    /// 结束时间月份
    /// </summary>
    public string end_time_month { get; set; } = string.Empty;

    /// <summary>
    /// 学校
    /// </summary>
    public string school_name { get; set; } = string.Empty;

    /// <summary>
    /// 上课模式
    /// </summary>
    public string study_model { get; set; } = string.Empty;

    /// <summary>
    /// 专业
    /// </summary>
    public string major { get; set; } = string.Empty;

    /// <summary>
    /// 学位
    /// </summary>
    public string degree { get; set; } = string.Empty;
}

/// <summary>
/// 工作经历（子类）
/// </summary>
public class work_experience_sub
{
    /// <summary>
    /// 开始时间年份
    /// </summary>
    public string start_time_year { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间月份
    /// </summary>
    public string start_time_month { get; set; } = string.Empty;

    /// <summary>
    /// 结束时间年份
    /// </summary>
    public string end_time_year { get; set; } = string.Empty;

    /// <summary>
    /// 结束时间月份
    /// </summary>
    public string end_time_month { get; set; } = string.Empty;

    /// <summary>
    /// 公司名称
    /// </summary>
    public string company_name { get; set; } = string.Empty;

    /// <summary>
    /// 所属部门
    /// </summary>
    public string department { get; set; } = string.Empty;

    /// <summary>
    /// 职位名
    /// </summary>
    public string job_title { get; set; } = string.Empty;

    /// <summary>
    /// 公司行业
    /// </summary>
    public string industry { get; set; } = string.Empty;

    /// <summary>
    /// 工资水平
    /// </summary>
    public string salary { get; set; } = string.Empty;

    /// <summary>
    /// 工作描述
    /// </summary>
    public string description { get; set; } = string.Empty;
}

/// <summary>
/// 项目经历（子类）
/// </summary>
public class project_experience_sub
{
    /// <summary>
    /// 开始时间年份
    /// </summary>
    public string start_time_year { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间月份
    /// </summary>
    public string start_time_month { get; set; } = string.Empty;

    /// <summary>
    /// 结束时间年份
    /// </summary>
    public string end_time_year { get; set; } = string.Empty;

    /// <summary>
    /// 结束时间月份
    /// </summary>
    public string end_time_month { get; set; } = string.Empty;

    /// <summary>
    /// 项目名称
    /// </summary>
    public string project_name { get; set; } = string.Empty;

    /// <summary>
    /// 职位名
    /// </summary>
    public string job_title { get; set; } = string.Empty;

    /// <summary>
    /// 项目描述
    /// </summary>
    public string description { get; set; } = string.Empty;
}

/// <summary>
/// 补充信息（子类）
/// </summary>
public class others_sub
{
    /// <summary>
    /// 专业技能
    /// </summary>
    public List<string>? skills { get; set; }

    /// <summary>
    /// IT技能
    /// </summary>
    public List<string>? it_skills { get; set; }

    /// <summary>
    /// 商业技能
    /// </summary>
    public List<string>? business_skills { get; set; }

    /// <summary>
    /// 自我评价
    /// </summary>
    public string? self_evaluation { get; set; }
}
#endregion

#region 画像解析
/// <summary>
/// 小析解析画像解析
/// </summary>
public class predicted_result
{
    /// <summary>
    /// 亮点
    /// </summary>
    public highlights? highlights { get; set; }

    /// <summary>
    /// 风险
    /// </summary>
    public risks? risks { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public tags? tags { get; set; }
}

/// <summary>
/// 亮点
/// </summary>
public class highlights
{
    /// <summary>
    /// 工作经历亮点
    /// </summary>
    public List<string>? occupation { get; set; }

    /// <summary>
    /// 学习经历亮点
    /// </summary>
    public List<string>? education { get; set; }

    /// <summary>
    /// 项目经历亮点
    /// </summary>
    public List<string>? project { get; set; }

    /// <summary>
    /// 其他亮点
    /// </summary>
    public List<string>? others { get; set; }

    /// <summary>
    /// 亮点标签
    /// </summary>
    public List<string>? tags { get; set; }
}

/// <summary>
/// 风险
/// </summary>
public class risks
{
    /// <summary>
    /// 工作经历风险
    /// </summary>
    public List<string>? occupation { get; set; }

    /// <summary>
    /// 学习经历风险点
    /// </summary>
    public List<string>? education { get; set; }

    /// <summary>
    /// 风险点标签
    /// </summary>
    public List<string>? tags { get; set; }
}

/// <summary>
/// 简历标签
/// </summary>
public class tags
{
    /// <summary>
    /// 基本信息标签
    /// </summary>
    public List<tag_sub>? basic { get; set; }

    /// <summary>
    /// 教育背景标签
    /// </summary>
    public List<tag_sub>? education { get; set; }

    /// <summary>
    /// 职业标签
    /// </summary>
    public List<tag_sub>? professional { get; set; }

    /// <summary>
    /// 技能标签
    /// </summary>
    public List<tag_sub>? skills { get; set; }

    /// <summary>
    /// 其他信息标签
    /// </summary>
    public List<tag_sub>? others { get; set; }
}

/// <summary>
/// 标签子类
/// </summary>
public class tag_sub
{
    /// <summary>
    /// 标签
    /// </summary>
    public string tag { get; set; } = string.Empty;

    /// <summary>
    /// 类型
    /// </summary>
    public string type { get; set; } = string.Empty;
}
#endregion

#region Excel转word所需模型
public class ExcelResume
{
    [ExcelColumnName("姓名")]
    // [Required(ErrorMessage = "姓名不能为空")]
    public string? Name { get; set; }
    /// <summary>
    /// 性别
    /// </summary>
    [ExcelColumnName("性别")]
    // [Required(ErrorMessage = "性别不能为空")]
    public string? Sex { get; set; }
    /// <summary>
    /// 电话
    /// </summary>
    [ExcelColumnName("电话")]
    // [Required(ErrorMessage = "电话不能为空")]
    public string? Phone { get; set; }
    /// <summary>
    /// 生日
    /// </summary>
    [ExcelColumnName("生日")]
    public string? Birthday { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [ExcelColumnName("政治面貌")]
    public string? Politics { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [ExcelColumnName("婚姻状况")]
    public string? Marriage { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [ExcelColumnName("学历")]
    public string? Degree { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [ExcelColumnName("微信号")]
    public string? WeChatName { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [ExcelColumnName("邮箱")]
    public string? Email { get; set; }
    /// <summary>
    /// 开始工作日期
    /// </summary>
    [ExcelColumnName("开始工作日期")]
    public string? WorkTime { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [ExcelColumnName("期望工作地")]
    public string? ExpectCity { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [ExcelColumnName("期望职位")]
    public string? ExpectPost { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [ExcelColumnName("期望薪资")]
    public string? ExpectSalary { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [ExcelColumnName("自我评价")]
    public string? Present { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [ExcelColumnName("教育经历")]
    public string? Education { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [ExcelColumnName("工作经历")]
    public string? Work { get; set; }
    /// <summary>
    /// 
    /// </summary>
    [ExcelColumnName("项目经历")]
    public string? Project { get; set; }
}

/// <summary>
/// 求职意向
/// </summary>
public class PositionPurpose
{
    public string? Salary { get; set; }
    public string? PositionName { get; set; }
    public string? CityName { get; set; }
}
#endregion