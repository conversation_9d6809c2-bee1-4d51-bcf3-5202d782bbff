﻿using Config;
using Config.CommonModel;
using Config.CommonModel.TalentResume;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Nest;
using Staffing.Entity;
using Staffing.Entity.Elasticsearch;
using Staffing.Entity.Staffing;
using Staffing.Model.Hr.TalentResume;
using Staffing.Core.Interfaces.Hr;
using System.Text.RegularExpressions;

namespace Staffing.Core.Services.Service.Hr;

[Service(ServiceLifetime.Transient)]
public class TalentResumeService : ITalentResumeService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private readonly CommonDicService _commonDicService;
    private readonly EsHelper _esHelper;
    private readonly IHrService _hrService;
    public TalentResumeService(StaffingContext context, RequestContext user, CommonDicService commonDicService, EsHelper esHelper, IHrService hrService)
    {
        _context = context;
        _user = user;
        _commonDicService = commonDicService;
        _esHelper = esHelper;
        _hrService = hrService;
    }

    public PostTeamInfoPageListResponse GetPostsByStr(PostTeamPageListRequest request)
    {
        var response = new PostTeamInfoPageListResponse();
        var predicate = PredicateBuilder.New<Post_Team>(true);

        if (!string.IsNullOrWhiteSpace(request.Search))
        {
            if ((request.Search.StartsWith(Constants.PostIdPre) || request.Search.StartsWith(Constants.TeamPostIdPre)) && int.TryParse(request.Search.Substring(1), out var id))
                predicate = predicate.And(x => x.AutoId == id || x.Post.Name.Contains(request.Search) || x.Project_Team.Project.Agent_Ent.Name.Contains(request.Search) || x.Post.Dic_Region.Dic_Region_City.Name.Contains(request.Search));
            else
                predicate = predicate.And(x => x.Post.Name.Contains(request.Search) || x.Project_Team.Project.Agent_Ent.Name.Contains(request.Search) || x.Post.Dic_Region.Dic_Region_City.Name.Contains(request.Search));
        }
        predicate = predicate.And(x => x.Project_Team.HrId == _user.Id
        && x.Project_Team.Project.User_Hr.UserId != null
        && x.Post.Status != PostStatus.待审核);

        var sql = _context.Post_Team.Where(predicate);
        response.Total = sql.Select(s => new { a = 1 }).Count();
        var result = sql
            .OrderByDescending(o => o.CreatedTime)
            .Skip((request.PageIndex - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(s => new PostTeamInfo
            {
                PostCode = s.AutoId.ToString().PadLeft(2, '0'),
                TeamAutoId = s.AutoId.ToString(),
                hrProjectType = s.Project_Team.Type,
                TeamPostId = s.TeamPostId,
                PostName = s.Post.Name,
                RegionId = s.Post.RegionId,
                CreatedTime = s.CreatedTime,
                City = _commonDicService.GetCityById(s.Post.RegionId).CityName,
                CompanyName = s.Project_Team.Project.Agent_Ent.Name,
                CompanyAddress = s.Post.Address,
                Status = s.Show ? Config.Enums.PostStatus.发布中 : Config.Enums.PostStatus.关闭,
                StatusName = s.Show ? Config.Enums.PostStatus.发布中.GetDescription() : Config.Enums.PostStatus.关闭.GetDescription()
            })
            .ToList();

        // 数据处理
        result.ForEach(f =>
        {
            f.PostCode = $"{(f.hrProjectType == Config.Enums.HrProjectType.自己 ? Constants.PostIdPre : Constants.TeamPostIdPre)}{f.TeamAutoId.PadLeft(2, '0')}";
        });

        response.Rows = result;
        return response;
    }

    public TalentResumeListResponse GetRecommendTalentResumeList(TalentResumePageListRequest request)
    {
        //if (string.IsNullOrWhiteSpace(request.TeamPostId))
        //{
        //    throw new BadRequestException("未获取到岗位信息，请先发布或协同岗位");
        //}

        var client = _esHelper.GetClient();
        var mustQuerys = new List<Func<QueryContainerDescriptor<EsTalentResume>, QueryContainer>>();
        var regionQuerys = new List<Func<QueryContainerDescriptor<EsTalentResume>, QueryContainer>>();
        var nykSpecialCatagoryQuerys = new List<Func<QueryContainerDescriptor<EsTalentResume>, QueryContainer>>();
        //mustQuerys.Add(q => q.Match(t => t.Field(f => f.Id).Query("248454301781944709")));
        // 不能查询已经删除的
        // 不能查询诺优考 - 弃用
        // 不能查询隐藏的
        mustQuerys.Add(q => q.Bool(
            b => b.MustNot(
                m => m.Match(t => t.Field(f => f.Status).Query((int)TalentResumeStatus.Deleted + ""))
                || m.Match(t => t.Field(f => f.Deleted).Query("true"))
                || m.DateRange(r => r.Field(f => f.HideInvalidTime).GreaterThanOrEquals(DateTime.Now))
                // || m.Match(t => t.Field(f => f.Source).Query((int)TalentResumeSource.诺优考 + ""))
                // || m.Match(t => t.Field(f => f.Source).Query((int)TalentResumeSource.诺优考旧 + ""))
                )
            )
        );

        //是否诺快聘管理员，管理员可以查看所有人才
        var isAdmin = _user.Powers?.Contains(Power.NkpAdmin) == true;

        if (!isAdmin)
        {
            //查询当前人的钉钉部门
            // var ddDeptLevels = _context.Dd_User_Dept.Where(w => w.Dd_User.Mobile == _user.Account).Select(s => s.Dd_Dept.Level).ToList();
            var deptIdList = _context.Dd_User_Dept.Where(w => w.Dd_User.Mobile == _user.Account)
            .Select(s => new
            {
                Id1 = (long?)s.Dd_Dept.DdDeptId,
                Id2 = (long?)s.Dd_Dept.Parent.DdDeptId,
                Id3 = (long?)s.Dd_Dept.Parent.Parent.DdDeptId,
                Id4 = (long?)s.Dd_Dept.Parent.Parent.Parent.DdDeptId,
                Id5 = (long?)s.Dd_Dept.Parent.Parent.Parent.Parent.DdDeptId,
                Id6 = (long?)s.Dd_Dept.Parent.Parent.Parent.Parent.Parent.DdDeptId,
            }).ToList();

            //将deptIds内容合并成一个long数组
            var deptIds = deptIdList.SelectMany(s => new long?[] { s.Id1, s.Id2, s.Id3, s.Id4, s.Id5, s.Id6 })
            .Where(w => w.HasValue).Select(s => s!.Value.ToString()).Distinct().ToList() ?? new List<string>();

            //增加and条件，者HrIds为null，或者HrIds包含当前用户，这三个条件满足任意一个即可
            mustQuerys.Add(q => q.Bool(b => b.Should(
                s => s.Bool(b => b.MustNot(m => m.Exists(t => t.Field(f => f.HrIds)))),
                s => s.Bool(b => b.Must(m => m.Term(t => t.Field(f => f.HrIds).Value("[]")))),
                s => s.Bool(b => b.Must(m => m.Term(t => t.Field(f => f.HrIds).Value(_user.Id.ToString()))))
                )));

            var hasDept = deptIdList.Count > 0;
            //如果没有部门，则DeptIds必须不存在或者数量为0
            if (!hasDept)
            {
                mustQuerys.Add(q => q.Bool(b => b.Should(
                    s => s.Bool(b => b.MustNot(m => m.Exists(t => t.Field(f => f.DeptIds)))),
                    s => s.Bool(b => b.Must(m => m.Term(t => t.Field(f => f.DeptIds).Value("[]")))
                    ))));
            }
            else
            {
                //增加and条件，者DeptIds为null，或者deptIdList=DeptIds，这三个条件满足任意一个即可
                var prefixQueries = deptIds.Select(s => (Func<QueryContainerDescriptor<EsTalentResume>, QueryContainer>)
                (q => q.Bool(b => b.Must(m => m.Term(t => t.Field(f => f.DeptIds).Value(s))))));

                mustQuerys.Add(q => q.Bool(b => b.Should(
                    s => s.Bool(b => b.MustNot(m => m.Exists(t => t.Field(f => f.DeptIds)))),
                    s => s.Bool(b => b.Must(m => m.Term(t => t.Field(f => f.DeptIds).Value("[]")))),
                    //prefixQueries数组中的条件任意一个满足即可
                    s => s.Bool(b => b.Should(prefixQueries))
                    )));
            }
        }

        // 获取求职意向
        var postInfo = _context.Post_Team.Where(w => w.TeamPostId == request.TeamPostId).Select(s => new SelectFields
        {
            Category = s.Post.Category,
            Sex = s.Post.Sex,
            RegionId = s.Post.RegionId,
            Education = s.Post.Education,
            MaxAge = s.Post.MaxAge,
            MinAge = s.Post.MinAge
        }).FirstOrDefault();

        if (postInfo != null)
        {
            var categoryQuerys = new List<Func<QueryContainerDescriptor<EsTalentResume>, QueryContainer>>();
            var categoryInfo = _commonDicService.GetCategory(postInfo.Category + "");
            if (request.TabSelect == TabSelect.最准)
            {
                // 职位类别 - 3级 
                string? postCategoryLevel3 = categoryInfo.CategoryLevel3;
                string? postCategoryLevel3Name = categoryInfo.CategoryLevel3Name;
                if (!string.IsNullOrWhiteSpace(postCategoryLevel3))
                {
                    //mustQuerys.Add(q => q.Nested(n => n.Path(x => x.Hopes).Query(q1 => q1.Bool(b => b.Must(m => m.Term(new Field("hopes.categoryLevel1"), postCategoryLevel1))))));
                    //mustQuerys.Add(q => q.Nested(n => n.Path(p => p.Hopes).Query(qt => qt.QueryString(t => t.Query("*" + postCategoryLevel1 + "*").Fields(fs => fs.Field(f => f.Hopes.First().CategoryLevel1))))));
                    //mustQuerys.Add(q => q.Nested(n => n.Path(x => x.Hopes).Query(q1 => q1.Match(m => m.Field(f => f.Hopes.First().CategoryLevel1).Query(postCategoryLevel1)))));
                    categoryQuerys.Add(q => q.Match(t => t.Field(f => f.Hopes.First().CategoryLevel3).Query(postCategoryLevel3)));
                    categoryQuerys.Add(q => q.Match(t => t.Field(f => f.Hopes.First().CategoryLevel3Name).Query(postCategoryLevel3Name)));
                    // 兼容诺优考
                    categoryQuerys.Add(q => q.Match(t => t.Field(f => f.Hopes.First().PositionLevel1Name).Query($"*{postCategoryLevel3Name}*")));
                    mustQuerys.Add(q => q.Nested(n => n.Path(x => x.Hopes).Query(q1 => q1.Bool(b => b.Should(categoryQuerys)))));
                }
            }
            else
            {
                // 职位类别 - 2级 
                string? postCategoryLevel2 = categoryInfo.CategoryLevel2;
                string? postCategoryLevel1Name = categoryInfo.CategoryLevel1Name, postCategoryLevel2Name = categoryInfo.CategoryLevel2Name, postCategoryLevel3Name = categoryInfo.CategoryLevel3Name;
                if (!string.IsNullOrWhiteSpace(postCategoryLevel2))
                {
                    categoryQuerys.Add(q => q.Match(t => t.Field(f => f.Hopes.First().CategoryLevel2).Query(postCategoryLevel2)));
                    //categoryQuerys.Add(q => q.Match(t => t.Field(f => f.Hopes.First().CategoryLevel1Name).Query(postCategoryLevel1Name)));
                    categoryQuerys.Add(q => q.Match(t => t.Field(f => f.Hopes.First().CategoryLevel2Name).Query(postCategoryLevel2Name)));
                    categoryQuerys.Add(q => q.Match(t => t.Field(f => f.Hopes.First().CategoryLevel3Name).Query(postCategoryLevel3Name)));
                    // 兼容诺优考
                    categoryQuerys.Add(q => q.Match(t => t.Field(f => f.Hopes.First().PositionLevel1Name).Query($"*{postCategoryLevel2Name}*")));
                    categoryQuerys.Add(q => q.Match(t => t.Field(f => f.Hopes.First().PositionLevel1Name).Query($"*{postCategoryLevel3Name}*")));
                    mustQuerys.Add(q => q.Nested(n => n.Path(x => x.Hopes).Query(q1 => q1.Bool(b => b.Should(categoryQuerys)))));
                }
            }

            // 诺优考职位特殊处理 - 按名称，且放在catagorylevel1Name
            //nykSpecialCatagoryQuerys.Add(q => q.Bool(
            //    b => b.Must(
            //        m => m.Bool(b => b.Should(s => s.Term(t => t.Source, "1"), s => s.Term(t => t.Source, "3"))),
            //        m => m.Nested(n => n.Path(p => p.Hopes).Query(q => q.Match(m => m.Field(f => f.Hopes.First().CategoryLevel1Name).Query(categoryInfo.CategoryLevel1Name)))))));

        }

        // 筛选条件
        GetSelect(request, mustQuerys, regionQuerys, postInfo);

        // 排序
        var sort = new EsSort
        {
            Field = request.TabSelect switch
            {
                TabSelect.最新 => new Field("createdTime"),
                TabSelect.最准 => new Field("lastLoginTime"),
                _ => new Field("lastLoginTime")
            }
        };

        var resultModel = client.Search<EsTalentResume>(s => s
        .Index(_esHelper.GetIndex().TalentResume)
        .TrackTotalHits()
        .Skip((request.PageIndex - 1) * request.PageSize)
        .Take(request.PageSize)
        .Query(q => q.Bool(b => b.Must(mustQuerys)) && q.Bool(b => b.Should(regionQuerys)) && q.Bool(b => b.Must(nykSpecialCatagoryQuerys)))
        .Sort(sort));

        var result = new TalentResumeListResponse { Rows = new List<TalentResumeListModel>() };

        // 获取联系信息
        var resumeIds = resultModel.Documents.Select(s => s.Id).ToList();
        var connectInfos = new List<Talent_Resume_Connect>();
        if (resumeIds.Any())
        {
            connectInfos = _context.Talent_Resume_Connect.Where(w => resumeIds.Contains(w.ResumeId)).ToList();
        }

        var seekerIds = result.Rows.Select(s => s.SeekerId).ToList();
        var seekers = _context.User_Seeker.Where(w => seekerIds.Contains(w.UserId))
            .Select(s => new { s.UserId, s.TencentImId }).ToList();

        foreach (var i in resultModel.Documents)
        {
            var edu = i.Edus?.OrderByDescending(o => o.StartTime).FirstOrDefault();
            var wk = i.Works?.OrderByDescending(o => o.StartTime).FirstOrDefault();
            var addModel = new TalentResumeListModel()
            {
                Perfection = i.Perfection,
                Id = i.Id,
                Source = i.Source,
                SourceName = i.Source.GetDescription(),
                Name = i.Name,
                HeadPortrait = i.HeadPortrait,
                Sex = i.Sex,
                SexName = i.Sex?.GetDescription(),
                Birthday = i.Birthday,
                Education = i.Education,
                EducationName = i.Education?.GetDescription(),
                City = i.City,
                Address = i.Address,
                HopePosts = (i.Source == TalentResumeSource.诺优考 || i.Source == TalentResumeSource.诺优考旧) ? i.Hopes.Select(s => s.PositionLevel1Name ?? string.Empty).ToList() : i.Hopes.Select(s => s.CategoryLevel3Name ?? s.CategoryLevel2Name ?? s.CategoryLevel1Name ?? string.Empty).Distinct().ToList(),
                LastLoginTime = i.LastLoginTime,
                Label = i.Tags,
                EduExp = $"{edu?.SchoolName}-{edu?.MajorName} {edu?.Education.GetDescription()} {edu?.EndTime:yyyy-MM}",
                WorkExp = $"{wk?.CompanyName}-{wk?.PostName}/{wk?.StartTime:yyyy-MM}-{wk?.EndTime:yyyy-MM}",
                CreatedTime = i.CreatedTime,
                UpdatedTime = i.RegistedTime ?? i.CreatedTime,
                IfConnected = connectInfos.Any(a => a.ResumeId == i.Id),
                TencentImId = seekers.FirstOrDefault(f => f.UserId == i.SeekerId)?.TencentImId,
                Phone = i.Mobile.Length == 11 ? $"{i.Mobile[..3]}****{i.Mobile[7..11]}" : i.Mobile,
                WorkTime = i.WorkTime,
                HideInvalidTime = i.HideInvalidTime
            };
            if (i.Birthday is not null)
                addModel.Age = Tools.GetAgeByBirthdate(DateOnly.FromDateTime(i.Birthday.Value));
            result.Rows.Add(addModel);
        }

        result.Total = new List<int> { (int)resultModel.Total, 5000 }.Min();
        return result;
    }

    /// <summary>
    /// 存储最近筛选，存最近10次
    /// </summary>
    /// <param name="select"></param>
    public void AddSelectToRedis(TalentResumeSelect select)
    {
        // 判空
        if (CheckIfWithoutFields(select))
            return;
        string redidKey = $"st:talentselect:{_user.Id}";
        // 个人加锁
        using var locker = MyRedis.TryLock(redidKey);
        if (locker == null)
            return;

        var count = MyRedis.Client.LPush(redidKey, select);
        if (count > 10)
            MyRedis.Client.RPop(redidKey);
    }

    /// <summary>
    /// 判断对象所有字段为空
    /// </summary>
    /// <param name="select"></param>
    /// <returns></returns>
    private bool CheckIfWithoutFields(TalentResumeSelect select)
    {
        if (select == null)
            return true;

        if (select.MinAge == null && select.MaxAge == null && select.Sex == null && select.MinWorkTime == null
            && select.MaxWorkTime == null && select.ActiveTime == null && (select.EducationType == null || select.EducationType.Count(w => w == EducationType.不限) > 0))
            return true;

        return false;
    }

    /// <summary>
    /// 获取es筛选条件
    /// </summary>
    /// <param name="request"></param>
    /// <param name="mustQuerys"></param>
    /// <param name="regionQuerys"></param>
    /// <param name="postInfo"></param>
    /// <exception cref="Exception"></exception>
    private void GetSelect(TalentResumePageListRequest request, List<Func<QueryContainerDescriptor<EsTalentResume>, QueryContainer>> mustQuerys, List<Func<QueryContainerDescriptor<EsTalentResume>, QueryContainer>> regionQuerys, SelectFields? postInfo)
    {
        int minAge = 0, maxAge = 0, minWorkTime = 0, maxWorkTime = 0;
        bool education = false;
        Sex? sex = null;
        int activeTime = 0;
        string? regionId = string.Empty;

        if (postInfo != null)
        {
            minAge = postInfo.MinAge ?? 0;
            maxAge = postInfo.MaxAge ?? 0;
            sex = postInfo.Sex;
            regionId = postInfo.RegionId;
        }

        // 以自定义筛选为准，覆盖职位要求
        if (request.SelfSelect != null)
        {
            // 添加最近筛选
            AddSelectToRedis(request.SelfSelect);

            // 覆盖部分
            if (request.SelfSelect.MinAge != null && request.SelfSelect.MinAge > 0)
                minAge = request.SelfSelect.MinAge.Value;
            if (request.SelfSelect.MaxAge != null && request.SelfSelect.MaxAge > 0)
                maxAge = request.SelfSelect.MaxAge.Value;
            if (request.SelfSelect.EducationType != null && request.SelfSelect.EducationType.Count > 0)
                education = true;
            if (request.SelfSelect.Sex != null)
                sex = request.SelfSelect.Sex;

            // 非覆盖部分
            if (request.SelfSelect.MinWorkTime != null && request.SelfSelect.MinWorkTime > 0)
                minWorkTime = request.SelfSelect.MinWorkTime.Value;
            if (request.SelfSelect.MaxWorkTime != null && request.SelfSelect.MaxWorkTime > 0)
                maxWorkTime = request.SelfSelect.MaxWorkTime.Value;
            if (request.SelfSelect.ActiveTime != null)
                activeTime = (int)request.SelfSelect.ActiveTime.Value;
        }


        // 活跃时间
        if (activeTime > 0)
        {
            mustQuerys.Add(q => q.DateRange(t => t.Field(f => f.LastLoginTime).GreaterThan(DateTime.Now.AddDays(activeTime * -1).ToYYYY_MM_DD())));
        }

        // 工作年限
        if (minWorkTime > 0)
        {
            mustQuerys.Add(q => q.DateRange(t => t.Field(f => f.WorkTime).LessThanOrEquals(DateTime.Now.AddYears(minWorkTime * -1).ToYYYY_MM_DD())));
        }
        if (maxWorkTime > 0)
        {
            mustQuerys.Add(q => q.DateRange(t => t.Field(f => f.WorkTime).GreaterThan(DateTime.Now.AddYears(maxWorkTime * -1).ToYYYY_MM_DD())));
        }

        // 年龄要求
        if (minAge > 0)
        {
            mustQuerys.Add(q => q.DateRange(t => t.Field(f => f.Birthday).LessThanOrEquals(DateTime.Now.AddYears(minAge * -1).ToYYYY_MM_DD())));
        }
        if (maxAge > 0)
        {
            mustQuerys.Add(q => q.DateRange(t => t.Field(f => f.Birthday).GreaterThan(DateTime.Now.AddYears((maxAge + 1) * -1).ToYYYY_MM_DD())));
        }

        // 学历要求
        // 自定义筛选
        if (education)
        {
            var edus = new List<TalentVirtualEducation>();
            foreach (var item in request.SelfSelect.EducationType)
            {
                if (item == EducationType.不限)
                    continue;
                var edu = item switch
                {
                    EducationType.高中 => TalentVirtualEducation.Senior,
                    EducationType.大专 => TalentVirtualEducation.Professional,
                    EducationType.本科 => TalentVirtualEducation.Undergraduate,
                    EducationType.硕士 => TalentVirtualEducation.Master,
                    EducationType.博士 => TalentVirtualEducation.Doctor,
                    EducationType.其他 => TalentVirtualEducation.Junior,
                    _ => throw new Exception()
                };
                if (edu == TalentVirtualEducation.Senior)
                {
                    edus.Add(TalentVirtualEducation.Specialized);
                }
                edus.Add(edu);
            }

            if (edus.Count > 0)
            {
                var edusQuerys = new List<Func<QueryContainerDescriptor<EsTalentResume>, QueryContainer>>();
                foreach (var item in edus.Distinct())
                {
                    edusQuerys.Add(q => q.Term(t => t.Field(f => f.Education).Value((int)item)));
                }

                mustQuerys.Add(q => q.Bool(b => b.Should(edusQuerys)));
            }
        }
        // 岗位要求
        else if (postInfo?.Education != null && postInfo?.Education != EducationType.不限)
        {
            var eduTop = postInfo.Education switch
            {
                EducationType.高中 => TalentVirtualEducation.Senior,
                EducationType.大专 => TalentVirtualEducation.Professional,
                EducationType.本科 => TalentVirtualEducation.Undergraduate,
                EducationType.硕士 => TalentVirtualEducation.Master,
                EducationType.博士 => TalentVirtualEducation.Doctor,
                EducationType.其他 => TalentVirtualEducation.Junior,
                _ => throw new Exception()
            };
            if (eduTop == TalentVirtualEducation.Senior)
            {
                // 增加 中专 筛选
                //mustQuerys.Add(q => q.Term(t => t.Field(f => f.Education).Value(eduTop)) || q.Term(t => t.Field(f => f.Education).Value(TalentVirtualEducation.Specialized)));
                mustQuerys.Add(q => q.Range(t => t.Field(f => f.Education).GreaterThanOrEquals((int)TalentVirtualEducation.Specialized)));
            }
            else
            {
                mustQuerys.Add(q => q.Range(t => t.Field(f => f.Education).GreaterThanOrEquals((int)eduTop)) || q.Term(t => t.Field(f => f.Education).Value(eduTop)));// GreaterThanOrEquals不是>=,只有>的效果
            }
        }

        // 性别要求
        if (sex != null)
        {
            mustQuerys.Add(q => q.Term(t => t.Field(f => f.Sex).Value(sex)));
        }

        // 地区要求
        if (request.RegionIds != null && request.RegionIds.Any())
        {
            request.RegionIds = request.RegionIds.Take(10).ToList();
            foreach (var item in request.RegionIds)
                regionQuerys.Add(q => q.Prefix(t => t.Field(f => f.RegionId).Value(item)));

        }
        else if (!string.IsNullOrWhiteSpace(regionId))
        {
            var cityInfo = _commonDicService.GetCityById(regionId);
            string? cityId = cityInfo.CityId;
            if (!string.IsNullOrWhiteSpace(cityId))
            {
                mustQuerys.Add(q => q.Wildcard(t => t.Field(f => f.RegionId).Value($"{cityId}*")));
            }
        }

        // 简历来源 - 未启用
        if (request.Source.HasValue)
        {
            mustQuerys.Add(q => q.Match(t => t.Field(f => f.Source).Query(((int)request.Source) + "")));
        }

        // 关键字搜索，es全字段匹配 岗位、公司、学校、技能
        if (!string.IsNullOrEmpty(request.Search))
        {
            // 特殊字符处理 使用QueryString查询时需要处理
            request.Search = EscapeSpecialCharsForQueryString(request.Search);
            mustQuerys.Add(q => q
                .Bool(b => b
                    .Should(
                        // 使用Wildcard查询来替代Match查询，用于Name字段  
                        sh => sh.Wildcard(w => w.Field(p => p.Name).Value($"*{request.Search}*")),
                        sh => sh.Match(m => m.Field(p => p.City).Query($"{request.Search}")),
                        sh => sh.Match(m => m.Field(p => p.Address).Query($"{request.Search}")),
                        sh => sh.Match(m => m.Field(p => p.SelfEvaluation).Query($"{request.Search}")),
                        sh => sh.Match(m => m.Field(p => p.Certificates).Query($"{request.Search}")),
                        sh => sh.Match(m => m.Field(p => p.Tags).Query($"{request.Search}")),
                        // 对工作经历字段进行nested查询
                        sh => sh.Nested(n => n
                            .Path(p => p.Works)
                            .Query(nq => nq
                                .QueryString(qs => qs
                                    .Query($"*{request.Search}*")
                                    .Fields(f => f
                                        .Field("works.companyName")
                                        .Field("works.postName")
                                    )
                                )
                            )
                        ),
                        // 对教育经历字段进行nested查询  
                        sh => sh.Nested(n => n
                            .Path(p => p.Edus)
                            .Query(nq => nq
                                .QueryString(qs => qs
                                    .Query($"*{request.Search}*")
                                    // 如果教育经历中的字段名不是默认的，需要指定字段  
                                    .Fields(f => f
                                        .Field("edus.schoolName")
                                        .Field("edus.majorName")
                                    )
                                )
                            )
                        ),
                        // 对意向工作字段进行nested查询
                        sh => sh.Nested(n => n
                            .Path(p => p.Hopes)
                            .Query(nq => nq
                                .QueryString(qs => qs
                                    .Query($"*{request.Search}*")
                                    // 如果意向工作中的字段名不是默认的，需要指定字段  
                                    .Fields(f => f
                                        .Field("hopes.categoryLevel1Name")
                                        .Field("hopes.categoryLevel2Name")
                                        .Field("hopes.categoryLevel3Name")
                                        .Field("hopes.positionLevel1Name")
                                        .Field("hopes.positionLevel2Name")
                                        .Field("hopes.positionLevel3Name")
                                        .Field("hopes.industryLevel1Name")
                                        .Field("hopes.industryLevel2Name")
                                    )
                                )
                            )
                        ),
                        // 对项目经历字段进行nested查询
                        sh => sh.Nested(n => n
                            .Path(p => p.Projects)
                            .Query(nq => nq
                                .QueryString(qs => qs
                                    .Query($"*{request.Search}*")
                                    .Fields(f => f
                                        .Field("projects.projectName")
                                        .Field("projects.postName")
                                        .Field("projects.projectRemarks")
                                    )
                                )
                            )
                        )
                    )
                    .MinimumShouldMatch(1) // 至少匹配一个should子句  
                )
            );
        }
    }

    public TalentResumeDetailResponse GetTalentDetailByMainId(string resumeId)
    {
        int times = _context.Talent_Resume_Connect.Count(c => c.ResumeId == resumeId && c.Type != ConnectType.查看简历);

        var talentResumeInfo = _context.Talent_Resume.Where(w => w.Id == resumeId)
            .Select(o => new TalentResumeDetailResponse
            {
                Name = o.Name,
                HeadPortrait = o.HeadPortrait,
                Mailbox = o.Mailbox,
                Mobile = o.Mobile,
                OriginalUrl = o.OriginalUrl,
                Perfection = o.Perfection,
                QQ = o.QQ,
                Sex = o.Sex,
                SexName = o.Sex.GetDescription(),
                Status = o.Status,
                StatusName = o.Status.GetDescription(),
                Education = o.Education,
                EducationName = o.Education.GetDescription(),
                SelfEvaluation = o.SelfEvaluation,
                TencentImId = o.User_Seeker.TencentImId,
                Id = o.Id,
                WeChat = o.WeChat,
                Birthday = o.Birthday,
                WorkTime = o.WorkTime,
                City = o.City,
                Highlights = o.Highlights,
                Risks = o.Risks,
                SkillAnalysis = o.SkillAnalysis,
                Edus = o.Edus,
                Projects = o.Projects,
                Hopes = o.Hopes,
                Works = o.Works,
                Products = o.Products,
                Honours = o.Honours,
                Certificates = o.Certificates,
                OtherLabel = o.OtherLabel,
                Tags = o.Tags,
                RegionId = o.RegionId,
                ConnectTimes = times,
                SeekerId = o.SeekerId,
                HrAppletQrCode = o.User_Seeker != null ? o.User_Seeker.HrAppletQrCode : null
            }).FirstOrDefault();

        if (talentResumeInfo is null)
        {
            throw new NotFoundException("内容不存在！");
        }
        else
        {
            if (talentResumeInfo.Birthday is not null)
            {
                talentResumeInfo.Age = Tools.GetAgeByBirthdate(DateOnly.FromDateTime(talentResumeInfo.Birthday.Value));
            }

            if (talentResumeInfo.Edus != null && talentResumeInfo.Edus.Count > 0)
            {
                foreach (var item in talentResumeInfo.Edus)
                {
                    if (item.Education != null)
                    {
                        item.EducationName = item.Education.GetDescription();
                    }
                }
            }

            if (talentResumeInfo.Hopes != null && talentResumeInfo.Hopes.Count > 0)
            {
                foreach (var item in talentResumeInfo.Hopes)
                {
                    if (string.IsNullOrWhiteSpace(item.PositionLevel3Name) && string.IsNullOrWhiteSpace(item.CategoryLevel3Name))
                    {
                        item.CategoryLevel1Name = item.PositionLevel1Name;// 诺优考求职期望放PositionLevel1Name
                    }
                }
            }

            //如果不在人才库，隐藏电话
            if (string.IsNullOrWhiteSpace(talentResumeInfo.SeekerId) || !_context.Talent_Platform.Any(x => x.HrId == _user.Id && x.SeekerId == talentResumeInfo.SeekerId))
            {
                talentResumeInfo.Phone = Tools.MobilToX(talentResumeInfo.Mobile);
                talentResumeInfo.Mailbox = Tools.MailToX(talentResumeInfo.Mailbox);
                talentResumeInfo.WeChat = Tools.WeChatToX(talentResumeInfo.WeChat);
                talentResumeInfo.QQ = Tools.QQToX(talentResumeInfo.QQ);
            }
            else
                talentResumeInfo.Phone = talentResumeInfo.Mobile;

            talentResumeInfo.Mobile = string.Empty;
        }

        // 添加操作记录
        MyRedis.Client.LPush(SubscriptionKey.TalentResumeRecord, new TalentResumeRecordRedis
        {
            HrId = _user.Id,
            ResumeId = resumeId,
            ConnectType = ConnectType.查看简历
        });

        return talentResumeInfo;
    }

    public EmptyResponse SendMessage(SendMessageRequest request)
    {
        var userInfo = _context.Talent_Resume.Where(w => w.Id == request.ResumeId).Select(s => new { s.Name, s.Mobile }).FirstOrDefault();
        if (userInfo == null)
        {
            throw new BadRequestException("用户不存在");
        }

        if (!string.IsNullOrWhiteSpace(request.TeamPostId))
        {
            var postInfo = _context.Post_Team.Where(w => w.TeamPostId == request.TeamPostId).Select(s => new { s.AutoId, s.Show }).FirstOrDefault();
            if (!postInfo.Show)
            {
                throw new BadRequestException("职位已下线");
            }
            _hrService.SendInviteSms(new Model.Hr.User.SendInviteSms
            {
                TeamPostNo = Constants.TeamPostIdPre + postInfo.AutoId,
                ToUsers = new List<Config.CommonModel.Tasks.SendInviteSmsUserInfo>
                {
                    new Config.CommonModel.Tasks.SendInviteSmsUserInfo { Name = userInfo.Name, Mobile = userInfo.Mobile }
                }
            }).GetAwaiter().GetResult();
        }

        // 添加操作记录
        MyRedis.Client.LPush(SubscriptionKey.TalentResumeRecord, new TalentResumeRecordRedis
        {
            HrId = _user.Id,
            ResumeId = request.ResumeId,
            TeamPostId = request.TeamPostId,
            ConnectType = ConnectType.短信邀约
        });
        return new EmptyResponse();
    }

    public GetTelephoneNumber GetPhoneNumber(string resumeId)
    {
        // 20240412 临时新增特定人员电话沟通人数总数不超过1000人限制
        PhonesLimit();
        var phone = _context.Talent_Resume.Where(w => w.Id == resumeId).Select(s => s.Mobile).FirstOrDefault();

        // 添加操作记录
        MyRedis.Client.LPush(SubscriptionKey.TalentResumeRecord, new TalentResumeRecordRedis
        {
            HrId = _user.Id,
            ResumeId = resumeId,
            ConnectType = ConnectType.拨打电话
        });

        return new GetTelephoneNumber { PhoneNumber = phone };// todo:先只获取真实电话号码
    }

    //private readonly List<string> LimitPhones = new List<string>
    //{
    //    "18633046306","18633068730","13933198610",
    //    "18632120961","15031181750","15830133017",
    //    "13473037915","19930545610","17601263256",
    //    "18931925701","17633257763"
    //};

    /// <summary>
    /// 限制用户查看人数
    /// </summary>
    /// <param name="resumeId"></param>
    /// <exception cref="BadRequestException"></exception>
    private void PhonesLimit()
    {
        var info = _commonDicService.GetBaseInfo("user_phone_times_limit");
        if (info == null || info.Count == 0)
            return;
        var LimitPhones = info.Select(s => s.Value).ToList();
        var limitUsers = _context.User.Where(w => LimitPhones.Contains(w.Mobile)).ToList();
        var userIds = limitUsers.Select(s => s.UserId).ToList();
        if (!userIds.Contains(_user.Id))
            return;// 非限制用户直接返回

        var phones = _context.Talent_Resume_Connect.Where(w => w.HrId == _user.Id && w.Type == ConnectType.拨打电话)
            .GroupBy(g => g.ResumeId).ToList();
        if (phones != null && phones.Count > 1000)
            throw new BadRequestException("超过次数限制");
    }

    /// <summary>
    /// 限制用户点击次数
    /// </summary>
    /// <exception cref="NotImplementedException"></exception>
    //private void TimesLimit()
    //{
    //    var limitUsers = _context.User.Where(w => LimitPhones.Contains(w.Mobile)).ToList();
    //    var userIds = limitUsers.Select(s => s.UserId).ToList();
    //    if (!userIds.Contains(_user.Id))
    //        return;// 非限制用户直接返回

    //    string rdsKey_1000 = $"st:talentR:total:{_user.Id}";
    //    string item_name = "times";
    //    // 调用总数限制 - 1000次
    //    if (Convert.ToInt32(MyRedis.Client.HGet(rdsKey_1000, item_name)) > 10)
    //    {
    //        throw new BadRequestException("超过次数限制");
    //    }
    //    MyRedis.Client.HIncrBy(rdsKey_1000, item_name, 1);
    //}

    public ProcessRecordsResponse GetProcessRecords(string resumeId)
    {
        var response = new ProcessRecordsResponse();
        var rows = _context.Talent_Resume_Connect.Where(w => w.ResumeId == resumeId)
            .Select(s => new ProcessRecordModel
            {
                HrName = s.User_Hr.NickName,
                HrPostName = s.User_Hr.Post,
                PostName = s.Post_Team.Post.Name,
                ConnectType = s.Type,
                ConnectTypeName = s.Type.GetDescription(),
                CreatedTime = s.CreatedTime,
            }).OrderByDescending(o => o.CreatedTime).ToList();
        response.Rows = rows;
        response.Total = rows.Count;

        // 短信邀约流程
        var messageInfo = _context.Talent_Resume_Connect
            .Where(w => w.ResumeId == resumeId
            && w.Type == ConnectType.短信邀约
            && w.HrId == _user.Id)
            .Select(s => new
            {
                s.TeamPostId,
                s.Post_Team.Post.Name,
                s.Talent_Resume.SeekerId
            }).FirstOrDefault(); // todo:是否存在给同一个简历发送多个职位的短信邀约待定

        if (messageInfo != null)
        {
            var messageConnect = new MessageConnectionModel
            {
                PostName = messageInfo.Name,
                MessageConnectionStatus = MessageConnectionStatus.短信邀约报名
            };

            if (!string.IsNullOrWhiteSpace(messageInfo.SeekerId))
            {
                var deliveryId = _context.Post_Delivery.Where(w => w.SeekerId == messageInfo.SeekerId && w.TeamPostId == messageInfo.TeamPostId).Select(s => s.DeliveryId).FirstOrDefault();
                if (!string.IsNullOrWhiteSpace(deliveryId))
                {
                    messageConnect.MessageConnectionStatus = MessageConnectionStatus.投递简历;
                    var recruitStatus = _context.Recruit.Where(w => w.DeliveryId == deliveryId).Select(s => s.Status).FirstOrDefault();
                    if (recruitStatus >= RecruitStatus.Interview)
                    {
                        messageConnect.MessageConnectionStatus = MessageConnectionStatus.面试邀请;
                    }
                }
            }

            response.MessageConnection = messageConnect;
        }

        return response;
    }

    public TalentResumeListResponse GetRecommendTalentResumeListTest()
    {
        var client = _esHelper.GetClient();
        var nykSpecialCatagoryQuerys = new List<Func<QueryContainerDescriptor<EsTalentResume>, QueryContainer>>();

        var mustQuerys = new List<Func<QueryContainerDescriptor<EsTalentResume>, QueryContainer>>
        {
            // 不能查询已经删除的
            //q => q.Bool(b => b.MustNot(m => m.Match(t => t.Field(f => f.Status).Query((int)TalentResumeStatus.Deleted + "")))),
            //q => q.Bool(b => b.Must(m => m.Match(t => t.Field(f => f.Name).Query("彩玲")))),

            // 年龄要求
            //q => q.DateRange(t => t.Field(f => f.Birthday).LessThanOrEquals(DateTime.Now.AddYears(-25).ToYYYY_MM_DD())),
            //q => q.DateRange(t => t.Field(f => f.Birthday).GreaterThanOrEquals(DateTime.Now.AddYears(-31).ToYYYY_MM_DD()))
        };


        // 求职意向
        var categoryQuerys = new List<Func<QueryContainerDescriptor<EsTalentResume>, QueryContainer>>
        {
            //q => q.Match(t => t.Field(f => f.Hopes.First().CategoryLevel2).Query("578")),
            q => q.Bool(b => b.Must(q => q.Term(t => t.Source, "1"),q => q.Term(t => t.Hopes.First().CategoryLevel1Name,"教师")))
        };

        //mustQuerys.Add(q => q.Nested(n => n.Path(x => x.Hopes).Query(q1 => q1.Bool(b => b.Should(categoryQuerys)))));

        // 诺优考职位特殊处理 - 按名称，且放在catagorylevel1Name
        nykSpecialCatagoryQuerys.Add(q => q.Bool(
            b => b.Must(
                m => m.Bool(b => b.Should(s => s.Term(t => t.Source, "1"), s => s.Term(t => t.Source, "3"))),
                m => m.Nested(n => n.Path(p => p.Hopes).Query(q => q.Match(m => m.Field(f => f.Hopes.First().CategoryLevel1Name).Query("教师")))))));

        var resultModel = client.Search<EsTalentResume>(s => s
        .Index(_esHelper.GetIndex().TalentResume)
        .TrackTotalHits()
        .Take(10)
        .Query(q => q.Bool(b => b.Must(nykSpecialCatagoryQuerys))));

        var result = new TalentResumeListResponse { Rows = new List<TalentResumeListModel>() };

        return new TalentResumeListResponse();
    }

    public string GetSelectedRecords()
    {
        string responseStr = string.Empty;
        string redidKey = $"st:talentselect:{_user.Id}";
        var lastOne = MyRedis.Client.LRange<TalentResumeSelect>(redidKey, 0, 1);// 取最近一个

        if (lastOne == null || lastOne.Count() == 0)
            return responseStr;

        var select = lastOne[0];

        if (select.MinAge != null && select.MinAge > 0 && select.MaxAge != null && select.MaxAge > 0)
        {
            responseStr += select.MinAge + "-" + select.MaxAge + "岁 | ";
        }
        else if (select.MinAge != null && select.MinAge > 0)
        {
            responseStr += select.MinAge + "岁以上 | ";
        }
        else if (select.MaxAge != null && select.MaxAge > 0)
        {
            responseStr += select.MaxAge + "岁以下 | ";
        }

        if (select.MinWorkTime != null && select.MinWorkTime > 0 && select.MaxWorkTime != null && select.MaxWorkTime > 0)
        {
            responseStr += select.MinWorkTime + "-" + select.MaxWorkTime + "年工作经验 | ";
        }
        else if (select.MinWorkTime != null && select.MinWorkTime > 0)
        {
            responseStr += select.MinWorkTime + "年以上工作经验 | ";
        }
        else if (select.MaxWorkTime != null && select.MaxWorkTime > 0)
        {
            responseStr += select.MaxWorkTime + "年以下工作经验 | ";
        }

        if (select.Sex != null)
        {
            responseStr += select.Sex.GetDescription() + " | ";
        }

        if (select.EducationType != null && select.EducationType.Count > 0)
        {
            foreach (var item in select.EducationType)
            {
                if (item == EducationType.不限)
                    continue;
                responseStr += item.GetDescription() + " | ";
            }
        }

        if (select.ActiveTime != null)
        {
            responseStr += select.ActiveTime.GetDescription() + " | ";
        }

        string regexPattern = $"({Regex.Escape("|")})(?!.*{Regex.Escape("|")})";
        responseStr = Regex.Replace(responseStr, regexPattern, "", RegexOptions.RightToLeft);

        return responseStr;
    }

    public static string EscapeSpecialCharsForQueryString(string queryString)
    {
        // 注意:可能不涵盖所有需要转义的字符   
        return queryString.Replace("+", @"\+")
                          .Replace("-", @"\-")
                          .Replace("&", @"\&")
                          .Replace("|", @"\|")
                          .Replace("!", @"\!")
                          .Replace("(", @"\(")
                          .Replace(")", @"\)")
                          .Replace("{", @"\{")
                          .Replace("}", @"\}")
                          .Replace("[", @"\[")
                          .Replace("]", @"\]")
                          .Replace("^", @"\^")
                          .Replace("~", @"\~")
                          .Replace(":", @"\:")
                          .Replace("\\", @"\\")
                          .Replace("/", @"\/")
                          .Replace("?", @"\?")
                          .Replace("*", @"*") // 注意：'*' 在查询字符串查询中通常是特殊字符，但在这里我们不做转义  
                          .Replace("\"", @"\""").Trim();
    }

    public EmptyResponse HideResume(string resumeId)
    {
        var resume = _context.Talent_Resume.FirstOrDefault(f => f.Id == resumeId);

        if (resume == null)
            throw new BadRequestException("无效的resumeId");

        resume.HideInvalidTime = DateTime.Now.AddDays(180);
        
        _context.SaveChanges();

        // 添加操作记录
        MyRedis.Client.LPush(SubscriptionKey.TalentResumeRecord, new TalentResumeRecordRedis
        {
            HrId = _user.Id,
            ResumeId = resumeId,
            ConnectType = ConnectType.隐藏简历
        });

        return new EmptyResponse();
    }
}
