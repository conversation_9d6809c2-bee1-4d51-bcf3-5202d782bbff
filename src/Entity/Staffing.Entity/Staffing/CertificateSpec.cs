using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 证书规格表实体
/// </summary>
[Table("certificate_specs")]
public class CertificateSpec
{
    /// <summary>
    /// 规格ID
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 关联的证书ID
    /// </summary>
    public string CertificateId { get; set; } = default!;

    /// <summary>
    /// 规格名称（如：初级证书、中级证书）
    /// </summary>
    public string SpecName { get; set; } = default!;

    /// <summary>
    /// 价格（元）
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 库存
    /// </summary>
    public int? Stock { get; set; }

    /// <summary>
    /// 推荐佣金（元）
    /// </summary>
    public decimal? RecommendCommission { get; set; }

    /// <summary>
    /// 自定义排序
    /// </summary>
    public int SortOrder { get; set; } = 0;
    
    /// <summary>
    /// 销量
    /// </summary>
    public int SalesVolume { get; set; } = 0;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 证书学习地址
    /// </summary>
    public List<StudyModel>? StudyList { get; set; }
    
    /// <summary>
    /// 导航属性 - 关联的证书
    /// </summary>
    public CertificateTable Certificate { get; set; } = default!;
    
    
    public class StudyModel
    {
        /// <summary>
        /// 标题
        /// </summary>
        public string? Title { get; set; }
        /// <summary>
        /// 学习地址
        /// </summary>
        public string? Url { get; set; }
    }
}