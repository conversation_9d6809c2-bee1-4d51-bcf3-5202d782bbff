using System.Reflection;
using Microsoft.Extensions.DependencyInjection;

namespace Infrastructure.Extend;

/// <summary>
/// IServiceCollection扩展类
/// </summary>
public static class ServiceCollectionExpand
{
    /// <summary>
    /// 按特性中的生命周期注入业务组件
    /// </summary>
    /// <param name="service"></param>
    public static void AutoDependencyInjection(this IServiceCollection service)
    {
        //获取有ServiceAttribute特性的所有类
        var types = AppDomain.CurrentDomain.GetAssemblies()
            .SelectMany(assembly => assembly.GetTypes())
            .Where(type => !type.IsAbstract)
            .Select(type => type.Assembly)
            .Distinct().ToList()
            .SelectMany(type => type.GetTypes()).Where(t => t.IsClass && !t.IsAbstract
                && t.GetCustomAttributes(typeof(ServiceAttribute), false).Length > 0)
            .Distinct().ToList();

        // var types = AppDomain.CurrentDomain
        //     .GetReferanceAssemblies()
        //     .SelectMany(x =>
        //     {
        //         try
        //         {
        //             return x.GetTypes();
        //         }
        //         catch
        //         {
        //             return new Type[] { };
        //         }
        //     }).Where(t => t.IsClass && !t.IsAbstract
        //     && t.GetCustomAttributes(typeof(ServiceAttribute), false).Length > 0)
        //     .Distinct().ToList();

        types.ForEach(impl =>
        {
            //获取该类所继承的所有接口
            Type[] interfaces = impl.GetInterfaces();
            //获取该类注入的生命周期
            var lifetime = impl.GetCustomAttribute<ServiceAttribute>()!.LifeTime;

            if (interfaces.Count() > 0)
            {
                interfaces.ToList().ForEach(i =>
                {
                    switch (lifetime)
                    {
                        case ServiceLifetime.Singleton:
                            service.AddSingleton(i, impl);
                            break;
                        case ServiceLifetime.Scoped:
                            service.AddScoped(i, impl);
                            break;
                        case ServiceLifetime.Transient:
                            service.AddTransient(i, impl);
                            break;
                    }
                });
            }
            else
            {
                switch (lifetime)
                {
                    case ServiceLifetime.Singleton:
                        service.AddSingleton(impl);
                        break;
                    case ServiceLifetime.Scoped:
                        service.AddScoped(impl);
                        break;
                    case ServiceLifetime.Transient:
                        service.AddTransient(impl);
                        break;
                }
            }
        });
    }

    // public static List<Assembly> GetReferanceAssemblies(this AppDomain domain)
    // {
    //     var list = new List<Assembly>();
    //     list.AddRange(domain.GetAssemblies());

    //     foreach (var item in domain.GetAssemblies())
    //     {
    //         GetReferanceAssemblies(item, list);
    //     }
    //     return list;
    // }
    // static void GetReferanceAssemblies(Assembly assembly, List<Assembly> list)
    // {
    //     foreach (var item in assembly.GetReferencedAssemblies())
    //     {
    //         try
    //         {
    //             var ass = Assembly.Load(item);
    //             if (!list.Contains(ass))
    //             {
    //                 list.Add(ass);
    //                 GetReferanceAssemblies(ass, list);
    //             }
    //         }
    //         catch { }
    //     }
    // }

    /// <summary>
    /// 非一对一实例注入
    /// </summary>
    /// <param name="service"></param>
    public static void AutoDependencyInjectionForOnlyType(this IServiceCollection service)
    {
        //获取有ServiceAttribute特性的所有类
        // List<Type> types = AppDomain.CurrentDomain
        //     .GetAssemblies()
        //     .SelectMany(x => x.GetTypes()).Where(t => t.IsClass && !t.IsAbstract
        //     && t.GetCustomAttributes(typeof(TypeServiceAttribute), false).Length > 0)
        //     .ToList();

        var types = AppDomain.CurrentDomain.GetAssemblies()
            .SelectMany(assembly => assembly.GetTypes())
            .Where(type => !type.IsAbstract)
            .Select(type => type.Assembly)
            .Distinct().ToList()
            .SelectMany(type => type.GetTypes()).Where(t => t.IsClass && !t.IsAbstract
                && t.GetCustomAttributes(typeof(TypeServiceAttribute), false).Length > 0)
            .Distinct().ToList();

        types.ForEach(impl =>
        {
            //获取该类注入的生命周期
            var lifetime = impl.GetCustomAttribute<TypeServiceAttribute>()!.LifeTime;
            switch (lifetime)
            {
                case ServiceLifetime.Singleton:
                    service.AddSingleton(impl);
                    break;
                case ServiceLifetime.Scoped:
                    service.AddScoped(impl);
                    break;
                case ServiceLifetime.Transient:
                    service.AddTransient(impl);
                    break;
            }
        });
    }
}

/// <summary>
/// 属性形式定义生命周期
/// 默认Scoped
/// </summary>
[AttributeUsage(AttributeTargets.Class)]
public class ServiceAttribute : Attribute
{
    public ServiceLifetime LifeTime { get; set; }
    public ServiceAttribute(ServiceLifetime serviceLifetime = ServiceLifetime.Transient)
    {
        LifeTime = serviceLifetime;
    }
}

/// <summary>
/// 注入实例 - 适用于非一对一接口实现形式的实例注入
/// </summary>
[AttributeUsage(AttributeTargets.Class)]
public class TypeServiceAttribute : Attribute
{
    public ServiceLifetime LifeTime { get; set; }
    public TypeServiceAttribute(ServiceLifetime serviceLifetime = ServiceLifetime.Transient)
    {
        LifeTime = serviceLifetime;
    }
}