﻿using Config.CommonModel;
using Config.Enums;

namespace Config;

public class RedisKey
{
    //后台任务心跳检测
    public const string WorkerKeyPre = "st:woker:";

    //企业白名单
    public const string EntWhiteList = "st:entwhitelst";

    // //人才随机头像
    // public const string TalentRandomAvatars = "st:talavts:";

    // //职位报名随机头像
    // public const string PostRandomAvatars = "st:postavts:";

    //顾问项目行业职位数量
    public const string AdviserProjectIndustryCount = "st:adspjind:";

    //Hr排行榜
    public const string HrRank = "st:hrrank";

    //更新登录时间
    public const string UserLoginTime = "st:login:";

    //hr的token缓存key
    public const string UserTokenKey = "st:hrtkkey:";

    //隐私号码清理机制
    public const string PrivatePhoneCleanKey = "priphocn";

    //诺聘公众号订阅状态
    public const string WeChatMpSub = "wechat:mpsub";

    //诺快聘公众号订阅状态
    public const string WeChatMpSub2 = "wechat:mpsub2";

    //Im用户在线状态
    public const string ImOnlineStatus = "im:online";

    //职位文案
    public const string PostCopywriting = "hx:postcopywriting";

    //钉钉部门
    public const string DdDept = "dd:dept";

    //顾问卡片显示设置
    public const string HrCardShow = "st:hrcardshow";

    //分享职位邀约小程序二维码转码
    public const string SeekerShareQrCodeId = "hash:seekershareqrid";

    //分享职位邀约小程序二维码
    public const string SeekerShareQrCode = "hash:seekershareqr";

    //证书PC
    public const string CertificateQrcode = "CertificateQrcode";


    //渠道职位二维码
    public const string Qdewm = "hash:qdewm";

    //企业工商数据
    public const string EntBusinessData = "hash:entbusinessdata";

    //分享蓝领证书邀约小程序二维码
    public const string AdviserShareCertificateQrCodeId = "hash:advisersharecerqrid";

    //配置
    public class Config
    {
        public const string Key = "config";

        //企业线索池权限
        public const string RenCaiMobileIgnore = "rcmobileignore";
    }

    //顾问管理端
    public class HrAdmin
    {
        public const string Key = "hradmin";

        //企业线索池权限
        public const string QyXscAdmin = "qyxscadmin";
    }

    // //诺优考
    // public class Nyk
    // {
    //     //UserApplyArchive表上次时间戳
    //     public const string UaaLastTimeStamp = "nyk:uaalasttimestamp";

    //     //用户上次登录时间
    //     public const string UserLastLoginId = "nyk:userlastloginid";
    // }

    public class NoahEntSync
    {
        //诺快聘企业职位、简历情况投递
        public const string Key = "noahentsync";

        //上次同步时间
        public const string LastTime = "lasttime";
    }

    //检测是否存在
    public class CheckExists
    {
        public const string Key = "ckexists";
    }

    public class ExcellentPost
    {
        //开通的城市
        public const string Citys = "ep:city";

        //面试提醒的职位
        public const string InterviewIn24Hour = "ep:interviewin24hour";

        //入职提醒的职位
        public const string EntryIn72Hour = "ep:entryin72hour";
    }

    //幂等性检测
    public class Idempotency
    {
        //招聘状态统计
        public const string RecruitStatus = "idcy:rcstn";

        //身份证检测
        public const string IdCardCheck = "idcy:ic:";

        //顾问自动协同项目任务
        public const string HrAutoTeam = "idcy:hrautoteam:";

        public const string HrAutoTeamProj = "idcy:hrautoteamproj:";

        //企业线索通知hr
        public const string QixsTzHr = "idcy:qixstzhr";
    }

    public class Lock
    {
        //招聘状态统计
        public const string ProjectMember = "projmem";

        //smstasks
        public const string SmsTasks = "smstasks";
    }

    public class Xbb
    {
        /// <summary>
        /// 任务检测
        /// </summary>
        public const string TaskCk = "xbb:taskck";

        /// <summary>
        /// 任务检测(销售机会)
        /// </summary>
        public const string TaskXsjhCk = "xbb:taskxsjhck";

        /// <summary>
        /// 任务检测(数字诺亚销售机会)
        /// </summary>
        public const string TaskNoahXsjhCk = "xbb:tasknoahxsjhck";

        /// <summary>
        /// 任务检测(用户)
        /// </summary>
        public const string TaskUserCk = "xbb:taskuserck";

        /// <summary>
        /// 销帮帮合同字典
        /// </summary>
        public const string HtDic = "xbb:htdic";
    }

    public class Message
    {
        //hr消息
        public const string HrMsgReadKey = "msgrdforhr";

        //seeker消息
        public const string SeekerMsgReadKey = "msgrdforsk";
    }

    public class PlatformCount
    {
        public const string Key = "st:platform:num";

        //人才数量列
        public const string Talent = "talent";

        //虚拟人才数量列
        public const string VirtualTalent = "virtualtalent";

        //顾问数量列
        public const string Adviser = "adviser";
    }

    public class SeekerBehavior
    {
        public const string Key = "behsk:";

        //投递数据
        public const string Delivery = "delivery";
    }

    public class HomePageTPData
    {
        public const string Key = "home:thirdpartyd";

        //诺聘职位数据看板
        public const string NuoPinPost = "nuopinpost";

        //诺优考项目数据看板
        public const string NuoyoukaoProject = "nuoyoukaoproject";

        //诺聘顾问列表
        public const string NuopinAdvisers = "nuopinadvisers";
    }

    //用户访问的职位类别
    public const string PostCategoryVisited = "vtptcg:";
    public const string PostCategoryVisited2 = "visitptcg";

    public class HrBehavior
    {
        public const string Key = "behhr:";

        //顾问人才职位分布
        public const string HrTalentPost = "hrtalpt:";

        //顾问人才行业分布
        public const string HrTalentIndustry = "hrtalind:";

        //顾问人才职位分布总数
        public const string HrTalentPostNum = "hrtalpt";

        //顾问人才行业分布总数
        public const string HrTalentIndustryNum = "hrtalind";

        //热门职位
        public const string PostHotKeywords = "pthotkey";

        //拥有的项目行业
        public const string ProjectIndustry = "pjindt";

        //职位所在区域
        public const string PostCity = "ptcty";

        //人才随机头像
        public const string TalentAvatars = "talentavas";

        //职位分布
        public const string PostDistribute = "postdist";

        //最近使用的app
        public const string LatelyApps = "latelyapps";
    }

    //微信社群
    public class WxSheQun
    {
        public const string Key = "wxshequn";

        //社群汇总数据
        public const string SummaryData = "datasummary";
    }

    public class HrStatBehavior
    {
        public const string DailyKey = "behhrdaily:";
        public const string FullKey = "behhrfull";
        public const string HyperLogKey = "behhrhylk:";
        // public const string WeeklyKey = "behhrweekly:";
        // public const string MonthlyKey = "behhrmonthly:";
        // public const string YearlyKey = "behhryearly:";

        //访问
        public const string Visits = "vist:";

        //新增人才
        public const string NewTalent = "newtal:";

        //报名
        public const string Delivery = "deliver:";

        //交付
        public const string DeliveryBounty = "deliverybounty:";
    }

    /// <summary>
    /// 数字诺亚
    /// </summary>
    public class Ndn
    {
        //冻结/解冻锁
        public const string FreezeBudgetLocker = "ndn:freezebudget";

        //钱包Wallet
        public const string WalletLocker = "ndn:wallet";

        //待办执行锁
        public const string TodoLocker = "ndn:todolocker";

        //结算
        public const string SettlementLocker = "ndn:settlement";

        // public const string NdnInvoiceTodoMaxId = "ndn:invoicetodomaxid";
        // public const string NdnTransferTodoMaxId = "ndn:transfertodomaxid";
    }

    public class AppShow
    {
        //渠道统计
        public const string ChannelAppShow = "rpt:channelas:";
    }

    public class PostData
    {
        public const string Key = "post:";
        public const string TeamKey = "tmpost:";

        //访问数
        public const string Visit = "visit";

        public const string DeliveryAvatars = "delyavas";

        // //报名
        // public const string Registration = "reg";

        // //面试
        // public const string Interview = "intv";

        // //入职
        // public const string Induction = "induct";

        // //签约
        // public const string Contract = "contract";
    }

    public class DataTopology
    {
        public const string DashBoradKey = "topology:dashboard";

        public const string TopologyTalent = "topology:talent";

        public const string TopologyProject = "topology:project";
    }

    /// <summary>
    /// 数据大屏
    /// </summary>
    public class DataScreen
    {

        public const string Key = "screenstatistics";

        #region 数据大屏 平台级-招聘流程

        public const string ScreenRecruitInfo = "screenstatistics_recruitinfo";

        /// <summary>
        /// 招聘流程 - 处理效率
        /// </summary>
        public const string RecruitXiaoLvInfo = "xiaolv";

        /// <summary>
        /// 招聘流程 - 面试官概况
        /// </summary>
        public const string RecruitInterviewerInfo = "interviewer";

        /// <summary>
        /// 招聘流程 - 投递概况
        /// </summary>
        public const string RecruitDeliveryInfo = "delivery";

        /// <summary>
        /// 招聘流程 - 实时播报
        /// </summary>
        public const string RecruitDeliveryTop30 = "delivery_30";

        #endregion

        #region 数据大屏 平台级-用户概况

        public const string ScreenSeekerInfo = "screenstatistics_seekerinfo";

        /// <summary>
        /// dic_region字典表
        /// </summary>
        public const string ScreenDicRegionInfo = "dicregioninfo";

        /// <summary>
        /// 用户信息缓存
        /// </summary>
        public const string ScreenUserRegionInfo = "userregionInfo";

        #endregion

        #region 数据大屏 平台级-项目看板

        public const string ScreenProjectInfo = "screenstatistics_projectinfo";

        #endregion


        /// <summary>
        /// 对应Model:Staffing.Model.Hr.DataScreen.DataScreenInfo类型相关的字段
        /// </summary>
        public const string InfoCount = "info";

        /// <summary>
        /// 顾问人才储备TOP榜前30人(数据列表，字段包含：顾问ID、顾问姓名、所属公司、人才库数量、简历人才库数量、昨日新增人才储备)
        /// 对应Model:Staffing.Model.Hr.DataScreen.AdvTalRankingInfo
        /// </summary>
        public const string AdvTalRanking = "listadvtalent";

        /// <summary>
        /// 顾问规模最大的机构(前3，数据列表，字段包含：公司Id、公司名、顾问数量)
        /// 对应Model:Staffing.Model.Hr.DataScreen.EntAdvRankingInfo
        /// </summary>
        public const string EntAdvRanking = "listentadv";

        /// <summary>
        /// 人才储备最大的机构(前3，数据列表，字段包含：公司Id、公司名、人才数量)
        /// 对应Model:Staffing.Model.Hr.DataScreen.EntTalRankingInfo
        /// </summary>
        public const string EntTalRanking = "listenttal";

        /// <summary>
        /// 涉及地区(数据列表，字段包含：地区编码、地区名称、公司数量)
        /// 对应Model:Staffing.Model.Hr.DataScreen.RegionEntListInfo
        /// </summary>
        public const string RegionEntList = "listregionent";

        /// <summary>
        /// 各市区项目数量
        /// 对应Model:Staffing.Model.Hr.DataScreen.RegionEntListInfo
        /// </summary>
        public const string CityProjectList = "listcityproject";
    }

    // public class ShuZiNY
    // {
    //     //数字诺亚人员
    //     public const string User = "szny:user";

    //     //数字诺亚项目
    //     public const string Projects = "szny:proj";
    // }

    public class Dic
    {
        //福利
        public const string Welfare = "st:dic:welfare";

        //地区
        public const string Region = "st:dic:region";

        //地区tree
        public const string RegionTree = "st:dic:regiontree";

        //学校
        public const string School = "st:dic:school";

        //专业
        public const string Major = "st:dic:major";

        //职位类别
        public const string Post = "st:dic:post";

        //福利字典
        public const string Industry = "st:dic:industry";

        //证书字典
        public const string Cert = "st:dic:cert";

        //应用列表
        public const string Apps = "st:dic:app";

        //AppId
        public const string AppIds = "st:dic:appid";

        //零工市场应用
        public const string QuickJobApps = "st:dic:quickjobapp";

        //企业工商
        public const string BusiEnt = "st:busient:";

        // base表信息
        public const string BaseInfo = "st:dic:baseInfo";
    }

    public class Canal
    {
        public const string Key = "canal";

        /// <summary>
        /// CurrentBatchId
        /// </summary>
        public readonly static string CurrentBatchId = "currentbatchid";
    }

    public class Balance
    {
        public const string TodayBalance = "balance:todaybalance";
        public const string HrTodayBalance = "balance:todaybalancehr";
        public const string LockKey = "balance:";
    }

    public class NScore
    {
        public const string Key = "nscore:";

        //积分配置
        public const string ScoreConfig = "scoreconfig";

        public const string Register = "register";
        public const string DailyLogin = "dailylogin:";
        public const string FirstDeliver = "firstdeliver";
        public const string DailyShare = "dailyshare";
        public const string FirstInduction = "firstinduction";
        public const string GoodsLocker = "goodslocker";
        // public const string ShareLogin = "sharelogin";
        public const string FirstProjectSync = "firstprojectsync";
        public const string FirstPost = "firstpost";

        //记录投递分享人
        public const string DeliverShareUser = "delivershareuser";

        //上次拉取的时间
        public const string LastNotifyTime = "lastnotifytime";

        //用户邮寄地址
        public const string ScoreAddress = "scoreaddress";

        //用户邮寄地址
        public const string HrScoreAddress = "hrscoreaddress";

        //新用户通知-职位配置
        public const string NewUserPushPosts = "newuserpushposts";
    }

    /// <summary>
    /// 快手redis
    /// </summary>
    public class KuaiShou
    {
        public static readonly string KuaiShouLonginToken = "st:kuaiShou:loginToken";
        public static readonly string KuaiShouCallbackKey = "kzg:cbk:";
    }

    public class Activity
    {
        //活动提交企业线索时 推送的顾问ID
        public const string RecommendAdviserId = "act:recommend:adviserId";

    }

    public const string PostLineLockKey = "uppost:";

    public static readonly string UserHrBookCodeKey = "st:hr:bookcode";
}

public class Constants
{
    /// <summary>
    /// mysql版本
    /// </summary>
    public readonly static Version MySqlVersion = new Version(8, 0, 21);

    /// <summary>
    /// token前缀，用来区分是诺皮token还是灵工
    /// </summary>
    public const string MyTokenPrefix = "stf_";

    public const string JdPostIdTest = "331989267825946374";
    public const string JdPostIdProd = "332128612248106886";

    /// <summary>
    /// AccessToken有效期
    /// </summary>
    public static int AccessTokenExpire = 2592000;

    /// <summary>
    /// RefreshToken有效期
    /// </summary>
    public static int RefreshTokenExpire = 15552000;

    ///// <summary>
    ///// 系统Id
    ///// </summary>
    //public static int SystemId = 0;

    /// <summary>
    /// 短信有效期
    /// </summary>
    public const int SmsExpireSeconds = 300;

    /// <summary>
    /// 系统报警发送间隔(防止错误轰炸)
    /// </summary>
    public static int SystemWarningInterval = 300;

    /// <summary>
    /// 默认时间
    /// </summary>
    public readonly static DateTime DefaultTime = new DateTime(1970, 1, 1);

    /// <summary>
    /// 默认时间
    /// </summary>
    public readonly static DateTime DefaultFutureTime = new DateTime(2099, 12, 31);

    /// <summary>
    /// 简历、到面交付倒计时天数
    /// </summary>
    public const int ResumeDeliveryDays = 7;

    /// <summary>
    /// 平台部门（可协同平台项目）
    /// </summary>
    public readonly static List<string> InternalHrIds = new List<string> { "1" };

    public readonly static RecruitStatus[] InterviewStatus = new RecruitStatus[] { RecruitStatus.Contract, RecruitStatus.Interview, RecruitStatus.Induction };

    /// <summary>
    /// 诺优考OAuth域名
    /// </summary>
    public const string NuoYouKaoOAuthUrl = "https://auth.nuoyoukao.com";

    /// <summary>
    /// 诺优考接口域名
    /// </summary>
    public const string NuoYouKaoUrl = "https://openapi.nuoyoukao.com";

    //钉钉域名
    public const string DingDingDomain = "https://oapi.dingtalk.com";

    /// <summary>
    /// 平台HrId
    /// </summary>
    public const string PlatformHrId = "1";
    public const string PlatformMobile = "4006161135";
    public const string PlatformName = "河北诺聘网络科技有限公司";

    //腾讯Im
    public const string ImAdminId = "administrator";

    /// <summary>
    /// oss域名
    /// </summary>
    public const string OssDomain = "https://resources.nuopin.cn";

    //默认头像地址
    public const string DefaultAvatar = "https://resources.nuopin.cn/staffing/res/avatar.png?1";

    /// <summary>
    /// 腾讯地图Key
    /// </summary>
    //public const string TencentMapKey = "5PSBZ-LDKLJ-3EIF4-DYIBQ-G3GUV-LXFTZ";
    public const string TencentMapKey = "A5BBZ-T4PL3-K3B36-3W4OG-5CNWZ-YGFVO";

    // 诺聘公众号
    public const string WeChatH5AppId = "wx46ee586fbebe8d13";
    public const string WeChatH5Secret = "841508929d793b4a0732ad26853b8162";

    // 诺快聘公众号
    public const string WeChatH5AppId2 = "wx54990b7e7afbf919";
    public const string WeChatH5Secret2 = "34a55a6832790c4d7174e3bbeb29abfe";

    // E签宝
    public const string ESignAppKey = "1";
    public const string ESignAppSecret = "1";

    //钉钉诺快聘系统应用
    public const string DingDingAppKey = "dinglk7ui1pmjdo6a9x0";
    public const string DingDingAppSecret = "g1Kzk4nTyYHjJtzX-RPLAo4jinShWIJZZedvH4ljFnel-0rFofNEEGi6rJXkcV7O";
    public const string DingDingAgent = "2876531899";

    //钉钉诺快聘机器人
    public const string DingDingRootAppKey = "dingqihsahphlyxnlzgq";
    public const string DingDingRootAppSecret = "NjV6RGgWEsQcdN7jEaELZnAvJpVkA58IinHnL5J4IlNCkTnmJEUtMEgm0aecgwuJ";

    //河北诺亚发展集团有限公司
    public const string CorpId = "ding310af7dd98e3ebdc35c2f4657eb6378f";

    //快手
    public const string KuaiShouApiDomain = "https://api-kzhaopin.kuaishou.com";
    public const string KuaiShouAppKey = "ec30a869-6da1-43e0-8c15-d0b1e6d0a0d1";
    public const string KuaiShouSecretKey = "d64994728b20e8b20d7c77aeadfcc5bf";
    public const string KuaiShouEncryptKey = "CB2121AE5AE6122V8KDCC1296F401D7T";

    // 手续费乘数因子
    public const decimal ServiceFeeMultiplier = 0.93m;

    /// <summary>
    /// 证书推广图模板
    /// </summary>
    public static string CertificateTemplaeUrl()
    {
        return "https://resources.nuopin.cn/templateimg_gentocertificate.jpeg?ticket=" + DateTime.Now.ToString("yyMMddHH");
    }

    public class Ndn
    {
        /// <summary>
        /// 分润科目名字
        /// </summary>
        public const string FrKmName = "交付分润";

        /// <summary>
        /// 分润科目编码
        /// </summary>
        public const string FrKmCode = "040515";

        /// <summary>
        /// 默认操作人工号
        /// </summary>
        public const string DefaultHrNo = "001659";
    }

    /// <summary>
    /// 项目Id前缀
    /// </summary>
    public const string ProjectIdPre = "9";


    /// <summary>
    /// 线索Id前缀
    /// </summary>
    public const string CluePre = "8";

    /// <summary>
    /// 职位Id前缀
    /// </summary>
    public const string PostIdPre = "1";

    /// <summary>
    /// 协同职位Id前缀
    /// </summary>
    public const string TeamPostIdPre = "2";

    //求职者Im前缀
    public const string ImSeekerIdPre = "imc";

    //顾问Im前缀
    public const string ImHrIdPre = "imb";

    public class TemplateUrl
    {
        /// <summary>
        /// 项目成员入职
        /// </summary>
        public const string ProjectMemberEntry = "https://resources.nuopin.cn/staffing/template/projectmember/memberentry.xlsx";
    }

    //常用语
    public class CommonWords
    {
        public static readonly List<string> SeekerWords = new List<string>
        {
            "你好，可以聊聊么",
            "您好，可以聊聊吗？您这个职位我很有兴趣，希望进一步了解",
            "看到贵司的这个职位，觉得我挺合适，是否可以聊聊呢？",
            "您好，非常喜欢这家公司，希望可以和您聊聊这个职位",
            "刚刚看了您发布的这个职位，我挺感兴趣的，希望您可以看看我的简历",
            "您好，我很有信心胜任这个职位，希望和您深入沟通",
            "您好！希望您可以看一下我的资料，期待能有更深入地沟通！非常感谢！",
            "刚刚看了您发布的这个职位，我特别喜欢，可否聊聊呢？",
            "您好，不知道这个岗位是否还有在招人，我仔细查看了您发布的职位信息，觉得自己比较适合，希望能得到您的回复~",
            "对不起，我觉得该职位不太适合我，祝您早日找到满意的工作人选",
            "我可以把我的简历发给您看看吗？"
        };

        public static readonly List<string> HrWords = new List<string>
        {
            "你好，请问现在还在看机会吗？",
            "方便相互了解一下吗？很看好您的能力。",
            "Hello,刚看了你的简历，何时方便我们可以电话或者视频沟通下吗？",
            "Hi,看了您的过往经历感觉您比较符合我们的职位要求，方便聊一聊吗？",
            "你好，认真看过你的简历后，觉得很适合这个职位，什么时间方便约个面试吗？",
            "你好，我看了你的简历，与我们的岗位要求非常匹配～希望可以进一步聊聊",
            "Hello，确认过眼神，你是我们在找的人！看了你的简历，想与你沟通下我们正在招聘的这个职位，有兴趣聊聊吗？",
            "您好，最近是否有考虑换工作，有空可以看下我们这边的岗位，要是有兴趣期待进一步沟通？"
        };
    }

    public class RecommendEntRemarkDic
    {
        /// <summary>
        /// 状态
        /// </summary>
        public HrRecommendEntStatus? Status { get; set; }

        /// <summary>
        /// 级别
        /// </summary>
        public HrRecommendEntLevel? Level { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public List<string> Names { get; set; } = default!;
    }

    public class RecommendEnt
    {
        public static readonly List<RecommendEntRemarkDic> RemarkDics = new List<RecommendEntRemarkDic>
        {
            new RecommendEntRemarkDic
            {
                Status = HrRecommendEntStatus.初步跟进,
                Level = HrRecommendEntLevel.一般重要,
                Names = new List<string>
                {
                    "添加微信",
                    "已经电话沟通",
                    "暂无业务合作需求",
                    "邀约参加招聘会",
                    "非河北地市",
                    "诺亚合作单位，有业务合作",
                    "离职，在别家公司，尝试邀约注册",
                    "之前合作过，当前在与别家合作，持续跟进",
                    "已有诺亚销售跟进"
                }
            },
            new RecommendEntRemarkDic
            {
                Status = HrRecommendEntStatus.废弃,
                Level = HrRecommendEntLevel.不重要,
                Names = new List<string>
                {
                    "电话空号",
                    "态度消极不配合等",
                    "接挂",
                    "电话不通",
                    "号码错误",
                    "联系人为诺亚员工"
                }
            },
            new RecommendEntRemarkDic
            {
                Status = HrRecommendEntStatus.方案报价,
                Level = HrRecommendEntLevel.非常重要,
                Names = new List<string>
                {
                    "录入销帮帮，销售机会",
                    "录入销帮帮，有效商机"
                }
            },
            new RecommendEntRemarkDic
            {
                Status = HrRecommendEntStatus.已成单,
                Level = HrRecommendEntLevel.非常重要,
                Names = new List<string>
                {
                    "录入销帮帮，赢单"
                }
            }
        };
    }

    public class NScore
    {
        /// <summary>
        /// 什么时间之后注册的算新用户，加注册积分
        /// </summary>
        public static DateTime NewSeekerDate = new DateTime(2022, 11, 2);

        public static List<NScoresStoreInfo> DefaultNScore = new List<NScoresStoreInfo>
        {
            //seeker
            new NScoresStoreInfo { Type = NScoreType.注册, Score = 1, UserType = SeekerOrHr.Seeker },
            new NScoresStoreInfo { Type = NScoreType.每日登录, Score = 1, UserType = SeekerOrHr.Seeker },
            new NScoresStoreInfo { Type = NScoreType.首次报名, Score = 1, UserType = SeekerOrHr.Seeker },
            new NScoresStoreInfo { Type = NScoreType.分享, Score = 1, UserType = SeekerOrHr.Seeker },
            new NScoresStoreInfo { Type = NScoreType.首次入职, Score = 1, UserType = SeekerOrHr.Seeker },
            new NScoresStoreInfo { Type = NScoreType.分享好友登录, Score = 1, UserType = SeekerOrHr.Seeker },
            new NScoresStoreInfo { Type = NScoreType.分享好友注册, Score = 1, UserType = SeekerOrHr.Seeker },
            new NScoresStoreInfo { Type = NScoreType.分享好友报名, Score = 1, UserType = SeekerOrHr.Seeker },
            new NScoresStoreInfo { Type = NScoreType.分享好友入职, Score = 1, UserType = SeekerOrHr.Seeker },
            //hr
            new NScoresStoreInfo { Type = NScoreType.注册, Score = 1, UserType = SeekerOrHr.Hr },
            new NScoresStoreInfo { Type = NScoreType.每日登录, Score = 1, UserType = SeekerOrHr.Hr },
            new NScoresStoreInfo { Type = NScoreType.发布职位, Score = 1, UserType = SeekerOrHr.Hr },
            new NScoresStoreInfo { Type = NScoreType.协同项目, Score = 1, UserType = SeekerOrHr.Hr },
            new NScoresStoreInfo { Type = NScoreType.首次入职, Score = 1, UserType = SeekerOrHr.Hr },
            new NScoresStoreInfo { Type = NScoreType.分享, Score = 1, UserType = SeekerOrHr.Hr },
            new NScoresStoreInfo { Type = NScoreType.分享好友登录, Score = 1, UserType = SeekerOrHr.Hr },
            new NScoresStoreInfo { Type = NScoreType.分享好友注册, Score = 1, UserType = SeekerOrHr.Hr },
            new NScoresStoreInfo { Type = NScoreType.分享好友报名, Score = 1, UserType = SeekerOrHr.Hr },
            new NScoresStoreInfo { Type = NScoreType.分享好友入职, Score = 1, UserType = SeekerOrHr.Hr }
        };

        // /// <summary>
        // /// 注册积分
        // /// </summary>
        // public const int Register = 100;

        // /// <summary>
        // /// 每日登录积分
        // /// </summary>
        // public const int DailyLogin = 10;

        // /// <summary>
        // /// 每日分享
        // /// </summary>
        // public const int DailyShare = 5;

        // /// <summary>
        // /// 首次报名
        // /// </summary>
        // public const int FirstDeliver = 100;

        // /// <summary>
        // /// 首次入职
        // /// </summary>
        // public const int FirstInduction = 50;

        // /// <summary>
        // /// 分享好友注册
        // /// </summary>
        // public const int FriendShareRegister = 100;

        // /// <summary>
        // /// 分享好友登录
        // /// </summary>
        // public const int FriendShareLogin = 5;

        // /// <summary>
        // /// 分享好友报名
        // /// </summary>
        // public const int FriendShareDeliver = 100;

        // /// <summary>
        // /// 分享好友入职
        // /// </summary>
        // public const int FriendShareInduction = 50;
    }

    public class ApiGroup
    {
        public const string InternalApi = "internal";
        public const string AdminApi = "admin";
        public const string SeekerApi = "seeker";
        public const string HrApi = "hr";
        public const string InterviewerApi = "interviewer";
        public const string CallbackApi = "callback";
        public const string CommonApi = "common";
    }
}

public class OAuthConfig
{
    //public const string HrWeb = "hr_web";
    //public const string HrApplet = "hr_applet";
    public const string JsApplet = "js_applet";

    //public static readonly List<string> ClientIds = new List<string> { HrWeb, JsApplet, HrApplet };

    //public static OAuthClientType? GetClientType(string? clientId)
    //{
    //    OAuthClientType? result = null;

    //    switch (clientId?.ToLower())
    //    {
    //        //case HrWeb:
    //        //case HrApplet:
    //        //    result = OAuthClientType.Hr;
    //        //    break;
    //        case JsApplet:
    //            result = OAuthClientType.JobSeeker;
    //            break;
    //        default:
    //            result = OAuthClientType.Hr;
    //            break;
    //    }

    //    return result;
    //}
}

// public class ApiDomain
// {

// }

public class Power
{
    public const string Admin = "Admin";
    public const string Manager = "Manager";
    public const string Employee = "Employee";
    public const string KuaiShou = "kuaishou";
    public const string KuaiShouAdmin = "ksadmin";
    public const string QiYeXianSuo = "qyxs";
    public const string QiYeXianSuoAdmin = "qyxsadmin";
    //
    public const string NkpAdmin = "nkpadmin";
    //诺快聘销售经理
    public const string NkpSales = "nkpsales";
    //诺快聘财务结算人员
    public const string NkpFinance = "nkpfinance";

    public const string NkpPm = "nkppm";

    public static bool CheckPower(List<string> powers, string needPower)
    {
        return powers.Any(a => a.ToLower() == needPower.ToLower());
    }

    public static List<string> GetAllPowers()
    {
        var powers = new List<string>();

        powers.Add(Admin);
        powers.Add(Manager);
        powers.Add(Employee);
        return powers;
    }

    //管理员角色Id
    public const string AdminRoleId = "1";

    //主管角色Id
    public const string LeaderRoleId = "2";

    //员工角色Id
    public const string EmployeeRoleId = "99";
}

//企业特性
public class EntSpecific
{
    public const string Noah = "noah";
    public const string GeRen = "geren";
}

public static class ClientApps
{
    /// <summary>
    /// 求职者小程序
    /// </summary>
    public static AppClientInfo SeekerApplet = new AppClientInfo { AppID = "wx8b4490f70e538c0e", AppSecret = "eef62b094083343fd25b8d37f472a1ad", NuoPinClientId = "7021113482001090017" };

    /// <summary>
    /// 面试官小程序
    /// </summary>
    public static AppClientInfo InterviewApplet = new AppClientInfo { AppID = "wxe38d12b563661718", AppSecret = "64e77b38b3ba84adac8fde1e22a45036", NuoPinClientId = "7021113482001090017" };

    /// <summary>
    /// Hr小程序
    /// </summary>
    public static AppClientInfo HrApplet = new AppClientInfo { AppID = "wx96b65aafffa82a6b", AppSecret = "36695648cb721126328ca0e57f8e8b44", NuoPinClientId = "7021113482001090018" };

    /// <summary>
    /// 快手小程序求职端
    /// </summary>
    public static AppClientInfo KsAppletSeeker = new AppClientInfo { AppID = "ks654147849006784091", AppSecret = "dDfB0acfdjRKr7cNEORVwA" };
}

// public static class KsConfig
// {
//     public const string AppID = "ks654147849006784091";
//     public const string AppSecret = "dDfB0acfdjRKr7cNEORVwA";
// }

public class AppClientInfo
{
    public string AppID { get; set; } = string.Empty;
    public string AppSecret { get; set; } = string.Empty;
    public string NuoPinClientId { get; set; } = string.Empty;
    public ClientType Type { get; set; }
}

public class ApiMeta
{
    public ApiAuthType? AuthType { get; set; }
}

// public class ExtendApiMeta
// {
//     public ApiAuthExtendType? ExtendType { get; set; }
// }

public enum ApiAuthType
{
    平台端 = 0,
    用户端 = 1,
    企业端 = 2,
    内部服务 = 30,
    诺聘验证 = 50,
    任意登录 = 99 //为了兼容老接口，任何一端登录都可以，但是必须要登录
    // 零工市场验证,
    // 零工市场管理员验证
}

// public enum ApiAuthExtendType
// {
//     诺聘验证 = 1
// }

public class ErrorCodes
{
    /// <summary>
    /// 尚未注册灵活用工
    /// </summary>
    public const string NotRegistered = "NotRegistered";

    /// <summary>
    /// 尚未审核通过
    /// </summary>
    public const string NotAudit = "NotAudit";

    /// <summary>
    /// 没有加入企业
    /// </summary>
    public const string NoEnt = "NoEnt";
}