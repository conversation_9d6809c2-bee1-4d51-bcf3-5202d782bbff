using Config;
using Config.CommonModel.Business;
using Config.CommonModel.Tencent;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Extend;
using Staffing.Model.Seeker.Balance;
using Staffing.Model.Seeker.User;
using Staffing.Model.Service;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Interfaces.Seeker;

namespace Staffing.Api.Apis.Seeker;

[Service(ServiceLifetime.Transient)]
public class SeekerRouter : ISeekerRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
         var group = parentGroup.MapGroup("user").WithTags("用户");
        group.MapPost("/token", async (SeekerLogin model, ISeekerService rep) => await rep.SeekerLogin(model))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "登录注册" });

        group.MapPost("/token2", async (SeekerLoginForDy model, ISeekerService rep) => await rep.SeekerLogin2(model))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "临时登录注册给抖音审核用" });

        group.MapPost("/refresh", async (SeekerRefreshToken model, ISeekerService rep) => await rep.SeekerRefreshToken(model))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "刷新token" });

        group.MapGet("", ([AsParameters] GetSeeker model, ISeekerService rep) => rep.GetSeeker())
        .WithOpenApi(operation => new(operation) { Summary = "用户信息" });

        group.MapGet("/hrbaseinfo", ([AsParameters] GetHrBaseInfo model, ISeekerService rep) => rep.GetHrBaseInfo(model))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "查询顾问基础信息" });

        group.MapPost("/channel/go", (ISeekerService rep) => rep.CheckChannel())
        .WithOpenApi(operation => new(operation) { Summary = "通过渠道进入顾问人才库" });

        group.MapGet("/checktoken", (string? token, ISeekerService rep) => rep.CheckSeekerTokenForNyk(token))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "诺优考解析用户token" });

        group.MapPost("/nyk/rn", async (GetNykRequestNo model, ISeekerService rep, RequestContext user) =>
        {
            model.AccessToken = user.AccessToken;
            return await rep.GetNykRequestNo(model);
        })
        .WithOpenApi(operation => new(operation) { Summary = "获取诺优考隐式授权请求编号" });

        group.MapPut("/location", (UpdateSeekerLocation model, ISeekerService rep) => rep.UpdateSeekerLocation(model))
        .WithOpenApi(operation => new(operation) { Summary = "更新用户位置" });

        group.MapPost("/resume/attch", (UpdateAttchResume model, ISeekerService rep) => rep.UpdateAttchResume(model))
        .WithOpenApi(operation => new(operation) { Summary = "更新附件简历" });

        group.MapDelete("/resume/attch", (ISeekerService rep) => rep.DeleteAttchResume())
        .WithOpenApi(operation => new(operation) { Summary = "删除附件简历" });

        group.MapPost("/mobile/check", (UserCheckMobile model, ICommonService rep) => rep.UserCheckMobile(model))
        .WithOpenApi(operation => new(operation) { Summary = "验证用户手机号" });

        group.MapPost("/resume", (UpdateSeeker model, ISeekerService rep) => rep.UpdateSeeker(model))
        .WithOpenApi(operation => new(operation) { Summary = "更新用户信息" });

        group.MapPost("/resume/campus", (UpdateCampus model, ISeekerService rep) => rep.UpdateCampus(model))
        .WithOpenApi(operation => new(operation) { Summary = "更新校园经历" });

        group.MapPost("/resume/works", (UpdateWorks model, ISeekerService rep) => rep.UpdateWorks(model))
        .WithOpenApi(operation => new(operation) { Summary = "更新工作经历" });

        group.MapDelete("/resume/campus/{id}", (string id, ISeekerService rep) => rep.DeleteCampus(id))
        .WithOpenApi(operation => new(operation) { Summary = "删除校园经历" });

        group.MapDelete("/resume/works/{id}", (string id, ISeekerService rep) => rep.DeleteWorks(id))
        .WithOpenApi(operation => new(operation) { Summary = "删除工作经历" });

        group.MapGet("/resume", (ICommonService rep, RequestContext user) => rep.GetResume(user.Id, false))
        .WithOpenApi(operation => new(operation) { Summary = "简历详情" });

        group.MapGet("/kzg/resume", ([AsParameters] GetKzgResume model, ISeekerService rep) => rep.GetKzgResume(model))
        .WithOpenApi(operation => new(operation) { Summary = "简历详情(支持快手)" });

        group.MapGet("/advisers", ([AsParameters] GetMyAdviser model, ISeekerService rep) => rep.GetMyAdviser(model))
        .WithOpenApi(operation => new(operation) { Summary = "我的顾问列表" });

        group.MapPost("/adviser/change", (SetAdviser model, ISeekerService rep) => rep.SetAdviser(model))
        .WithOpenApi(operation => new(operation) { Summary = "切换顾问" });

        group.MapPost("/adviser/add", (AddAdviser model, ISeekerService rep) => rep.AddAdviser(model))
        .WithOpenApi(operation => new(operation) { Summary = "添加顾问" });

        group.MapGet("/advisers/{adviserId}", (string adviserId, ISeekerService rep) => rep.SeekerGetAdviser(adviserId))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "顾问详情" });

        group.MapGet("/advisers/current", (ISeekerService rep) => rep.GetCurrentAdviser())
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "当前顾问" });

        group.MapGet("/behavior", (ISeekerService rep) => rep.GetSeekerBehavior())
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "用户行为数据" });

        group.MapPost("/app/show", (SeekerAppShow model, ISeekerService rep) => rep.SeekerAppShow(model))
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "用户打开小程序" });

        group.MapGet("/collect/post", ([AsParameters] GetCollectPost model, ISeekerService rep) => rep.GetCollectPost(model))
        .WithOpenApi(operation => new(operation) { Summary = "收藏职位列表" });

        group.MapGet("/follow/industry", ([AsParameters] GetFollowIndustry model, ISeekerService rep) => rep.GetFollowIndustry(model))
        .WithOpenApi(operation => new(operation) { Summary = "关注的行业列表" });

        group.MapPost("/collect/post", (UpdateCollectPost model, ISeekerService rep) => rep.UpdateCollectPost(model))
        .WithOpenApi(operation => new(operation) { Summary = "收藏/取消职位" });

        group.MapPost("/follow/industry", (UpdateFollowIndustry model, ISeekerService rep) => rep.UpdateFollowIndustry(model))
        .WithOpenApi(operation => new(operation) { Summary = "关注/取消行业" });

        group.MapPut("/guidance", (UpdateSeekerGuidance model, ISeekerService rep) => rep.UpdateSeekerGuidance(model))
        .WithOpenApi(operation => new(operation) { Summary = "更新用户就业顾问" });

        group.MapGet("/guidance", (ISeekerService rep) => rep.GetSeekerGuidance())
        .WithOpenApi(operation => new(operation) { Summary = "查询用户就业顾问" });

        group.MapGet("/idcard/check", ([AsParameters] GetIdVerification model, ICommonService rep) => rep.GetIdVerification(model))
        .WithOpenApi(operation => new(operation) { Summary = "检测身份证号是否被其他人使用" });

        group.MapPost("/idcard", async (IdVerificationModel model, ICommonService rep) => await rep.IdVerification(model))
        .WithOpenApi(operation => new(operation) { Summary = "实名认证" });

        group.MapGet("/im/sig", async (ISeekerService rep) => await rep.GetImUserSig())
        .WithOpenApi(operation => new(operation) { Summary = "获取im即时通讯usersig" });

        group.MapPost("/im/msg/read", (ImMsgRead model, TencentImHelper helper, RequestContext user) =>
        {
            var result = helper.ImMsgRead(new ImMsgReadFull
            {
                ImId = $"{Constants.ImSeekerIdPre}{user.Id}",
                ToImId = model.ToImId
            });
            return result;
        })
        .WithOpenApi(operation => new(operation) { Summary = "消息本地已读（非IM）" });

        group.MapPost("im/msg/sendmsg", (ImSendMsgInfo model, TencentImHelper helper, RequestContext user) =>
        {
            var result = helper.ImSendMsg(new ImSendMsgInfo
            {
                SyncOtherMachine = 2,
                From_Account = model.From_Account,
                To_Account = $"{Constants.ImSeekerIdPre}{user.Id}",
                MsgRandom = model.MsgRandom,
                MsgBody = model.MsgBody,
                //CloudCustomData = model.CloudCustomData,                
                SupportMessageExtension = model.SupportMessageExtension
            });
        })
        .WithOpenApi(operation => new(operation) { Summary = "向IM发送消息" });

        group.MapPost("/msg/lastpost", (GetMsgLastPost model, RequestContext user) =>
        {
            var result = new GetMsgLastPostResponse();

            if (string.IsNullOrWhiteSpace(model.TeamPostId) || string.IsNullOrWhiteSpace(model.AdviserImId))
                return result;
            MyRedis.Client.SAdd(SubscriptionKey.ImC2CTeamPostId, new Sub_ImC2CTeamPostId
            {
                UserId = user.Id,
                TeamPostId = model.TeamPostId,
                AdviserImId = model.AdviserImId
            });
            var cacheKey = $"impost:{user.Id}";
            var lastPost = MyRedis.Client.HGet<string?>(cacheKey, model.AdviserImId);

            if (lastPost != model.TeamPostId)
                MyRedis.Client.HSet(cacheKey, model.AdviserImId, model.TeamPostId);
            else
                result.SamePost = true;

            return result;
        })
        .WithOpenApi(operation => new(operation) { Summary = "im上次沟通的职位" });

        group.MapGet("/msg/words", (ICommonService rep) => rep.GetMsgWords(MsgWordsType.求职者))
        .WithOpenApi(operation => new(operation) { Summary = "常用语列表" });

        group.MapPost("/msg/words", (MsgWordsInfo model, ICommonService rep) => rep.UpdateMsgWords(model, MsgWordsType.求职者))
        .WithOpenApi(operation => new(operation) { Summary = "更新常用语" });

        group.MapDelete("/msg/words/{id}", (string id, ICommonService rep) => rep.DeleteMsgWords(id))
        .WithOpenApi(operation => new(operation) { Summary = "删除常用语" });

        group.MapGet("/balance", ([AsParameters] GetBalance model, ISeekerService rep) => rep.GetBalance(model))
        .WithOpenApi(operation => new(operation) { Summary = "我的钱包" });

        group.MapGet("/balance/record", ([AsParameters] GetBalanceRecord model, ISeekerService rep) => rep.GetBalanceRecord(model))
        .WithOpenApi(operation => new(operation) { Summary = "我的钱包流水" });

        group.MapPost("/withdraw", async (Withdraw model, ISeekerService rep) => await rep.Withdraw(model))
        .WithOpenApi(operation => new(operation) { Summary = "提现" });

        group.MapPost("/wx/getschemelink", async (Model.Common.WX.GetWXAPPLink model, WeChatHelper helper) =>
        {
            if (string.IsNullOrEmpty(model.urladdress))
                model.urladdress = "pages/index/index";
            else
                model.urladdress = model.urladdress.TrimStart('/');

            return await helper.GetWxSchemeLinkForJump(ClientApps.SeekerApplet.AppID, model.urladdress, model.query ?? string.Empty);
        })
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "获取小程序Scheme链接" });

        group.MapPost("/advert/getlist", (ICommonService rep) => rep.AdvertGetListByUse())
        .WithMetadata(new ApiMeta())
        .WithOpenApi(operation => new(operation) { Summary = "获取已上架的广告列表" });
    }
}