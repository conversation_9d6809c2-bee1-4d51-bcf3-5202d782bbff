﻿using Config.Enums;

namespace Config.CommonModel.ThirdTalentInfo;

public interface IThirdTalentInfo { }

public interface IProjectPostInfo { }

public class ProjectPostInfo : IProjectPostInfo
{
    public string ProjectId { get; set; } = default!;

    public string? ProjectName { get; set; }

    public string? ProjectCode { get; set; }

    public string TeamProjectId { get; set; } = default!;

    public string? RecruitId { get; set; }

    public string? ResumeBufferId { get; set; }

    public string TeamPostId { get; set; } = default!;

    public string PostId { get; set; } = default!;

    public string SeekerId { get; set; } = default!;

    public string? SeekerName { get; set; }

    public string? SeekerMobile { get; set; }

    public string? HrId { get; set; } = default!;

    public string? HrName { get; set; }

    public string TeamHrId { get; set; } = default!;
    
    public string? TeamHrName { get; set; }

    public string? ChannelHrId { get; set; }

    public string? ChannelHrName { get; set; }

    public string? PostName { get; set; }
    
    public string? Description { get; set; }

    /// <summary>
    /// 职位学历要求
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 职位毕业年限要求
    /// </summary>
    public PostWorkNature? WorkNature { get; set; }
    public int? GraduationYear { get; set; }

    /// <summary>
    /// 职位性别要求
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 职位最小年龄要求
    /// </summary>
    public int? MinAge { get; set; }

    /// <summary>
    /// 职位最大年龄要求
    /// </summary>
    public int? MaxAge { get; set; }

    /// <summary>
    /// 合作模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }

    /// <summary>
    /// 打款天数
    /// </summary>
    public int? PaymentDays { get; set; }

    /// <summary>
    /// 打款方式
    /// </summary>
    public ProjectPaymentType? PaymentType { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Money { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public BountyStatus? Status { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public TeamBountySource Source { get; set; }

    /// <summary>
    /// 结算时间
    /// </summary>
    public DateTime? SettlementTime { get; set; }

    /// <summary>
    /// 结算id
    /// </summary>
    public string? SettlementId { get; set; }

    /// <summary>
    /// 结算金额 - 协同应收佣金
    /// </summary>
    public decimal? SettlementMoney { get; set; }

    /// <summary>
    /// 协同实收佣金
    /// </summary>

    public decimal? SettlementActualMoney { get; set; }

    /// <summary>
    /// 渠道商结算金额
    /// </summary>
    public decimal? ChannelSettlementMoney { get; set; }

    /// <summary>
    /// 渠道商结算比例
    /// </summary>
    public decimal? ChannelSettlementRate { get; set; }

    /// <summary>
    /// 协同平台抽佣比例
    /// </summary>
    public decimal? PlatformSettlementRate { get; set; }

    /// <summary>
    /// 协同平台抽佣金额
    /// </summary>
    public decimal? PlatformSettlementMoney { get; set; }

    /// <summary>
    /// 渠道商合同关联id
    /// </summary>
    public string? ChannelContractId { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// Qd_hr_channel主键Id
    /// </summary>
    public string? ChannelId { get; set; }

    public HrProjectType? ProjectType { get; set; }

    /// <summary>
    /// 职位上线状态
    /// </summary>
    public bool Show { get; set; } = true;
}

/// <summary>
/// 简历质量标签
/// </summary>
public class DataQualityLabel
{
    public string? Age { get; set; }
    public string? IntentionCity { get; set; }
    public string? IntentionJobCategory { get; set; }
    public string? MultiApplyRecently { get; set; }
    public string? LiveCity { get; set; }
}

/// <summary>
/// 投递时的动态简历信息
/// </summary>
public class DataDynamicResumeInfo
{
    /// <summary>
    /// 工作方式
    /// </summary>
    public string? WorkWay { get; set; }
    /// <summary>
    /// 最高学历
    /// </summary>
    public string? HighestDegree { get; set; }
    /// <summary>
    /// 工作经历
    /// </summary>
    public string? WorkExperience { get; set; }
    /// <summary>
    /// 驾驶经历
    /// </summary>
    public string? DriveExperience { get; set; }
    /// <summary>
    /// 基本情况
    /// </summary>
    public string? BasicSituation { get; set; }
    /// <summary>
    /// 技能
    /// </summary>
    public string? Skill { get; set; }
    /// <summary>
    /// 意向城市
    /// </summary>
    public string? IntentionCity { get; set; }
    /// <summary>
    /// 意向岗位
    /// </summary>
    public string? IntentionJobCategory { get; set; }
    /// <summary>
    /// 求职状态
    /// </summary>
    public string? JobHuntingStatus { get; set; }
    /// <summary>
    /// 证书
    /// </summary>
    public string? Certificate { get; set; }
}

#region 接口原始字段
/// <summary>
/// 快手简历原始字段
/// </summary>
public class KuaishouTalentInfosOriginal
{
    /// <summary>
    /// 绑定的租户 id
    /// </summary>
    public string openTenantId { get; set; } = default!;
    /// <summary>
    /// 投递id
    /// </summary>
    public string applicationId { get; set; } = default!;
    /// <summary>
    /// 简历id
    /// </summary>
    public string resumeId { get; set; } = default!;
    /// <summary>
    /// 姓名
    /// </summary>
    public string? name { get; set; }
    /// <summary>
    /// 性别
    /// </summary>
    public Gender? gender { get; set; }
    /// <summary>
    /// 手机号
    /// </summary>
    public string? phone { get; set; }
    /// <summary>
    /// 年龄
    /// </summary>
    public int age { get; set; }
    /// <summary>
    /// 职位id
    /// </summary>
    public Job? job { get; set; }
    /// <summary>
    /// 来源渠道（简历来源，直播、短视频等）
    /// </summary>
    public string? channelName { get; set; }
    /// <summary>
    /// 推荐人（来源主播昵称）
    /// </summary>
    public string? recommender { get; set; }
    /// <summary>
    /// 推荐人（来源主播uid）
    /// </summary>
    public string? recommenderId { get; set; }
    /// <summary>
    /// 申请时间
    /// </summary>
    public long applyTime { get; set; }
    /// <summary>
    /// 企业
    /// </summary>
    public Company? company { get; set; }
    /// <summary>
    /// 0 - 未知的 1 - 自招职位投递 2 - 分销职位投递
    /// </summary>
    public int platform { get; set; }
    /// <summary>
    /// jsonString
    /// </summary>
    public string? extInfo { get; set; }
    public ExtInfo? ExtInfos { get; set; }
    /// <summary>
    /// jsonString
    /// </summary>
    public string? data { get; set; }
    public Data? Datas { get; set; }
}

public class Gender
{
    public int code { get; set; }
    public string? name { get; set; }
}

public class Job
{
    public string? name { get; set; }
    public string? jobId { get; set; }
}

public class Company
{
    public string? companyId { get; set; }
    public string? businessName { get; set; }
    public string? businessCode { get; set; }
}

public class Data
{
    public string? mod { get; set; }
    public string? net { get; set; }
    public string? appver { get; set; }
    public string? version { get; set; }
    public string? deviceId { get; set; }
    public string? channelSourceCode { get; set; }
    public string? channelSourceName { get; set; }
    public QualityLabel? qualityLabel { get; set; }
    public List<DynamicResumeInfo>? dynamicResumeInfo { get; set; }
    public string? trafficSources { get; set; }
    public string? userCity { get; set; }
    public string? locationCity { get; set; }
    public LocationCity? LocationCitys { get; set; }
}

public class DynamicResumeInfo
{
    public string? attributeId { get; set; }
    public string? paramName { get; set; }
    public string? label { get; set; }
    public string? content { get; set; }
}

public class QualityLabel
{
    public string? age { get; set; }
    public string? intentionCity { get; set; }
    public string? intentionJobCategory { get; set; }
    public string? multiApplyRecently { get; set; }
    public string? liveCity { get; set; }
}

public class ExtInfo
{
    public Age? age { get; set; }
    public List<IntentionJob>? intentionJob { get; set; }
    public List<IntentionCity>? intentionCity { get; set; }
    public int? jobHuntingStatus { get; set; }
}

public class Age
{
    public int? max { get; set; }
    public int? min { get; set; }
}

public class IntentionJob
{
    public int id { get; set; }
    public string? content { get; set; }
}

public class IntentionCity
{
    public string? cityCode { get; set; }
    public string? cityName { get; set; }
}

public class LocationCity
{
    public string? latitude { get; set; }
    public string? longitude { get; set; }
    public string? countryName { get; set; }
    public string? provinceName { get; set; }
    public string? cityName { get; set; }
    public string? cityLevel { get; set; }
    public string? countyName { get; set; }
    public string? townName { get; set; }
    public string? townType { get; set; }
    public string? address { get; set; }
    public string? adCode { get; set; }
    public bool? china { get; set; }
    public bool? municipality { get; set; }
    public bool? specialAdministrativeRegion { get; set; }
}
#endregion

/// <summary>
/// 第三方简历原始表信息公共字段
/// </summary>
public class ThirdTalentInfo : UserSeekerInfo, IThirdTalentInfo
{
    /// <summary>
    /// 不校验简历信息
    /// </summary>
    public bool NotCheck { get; set; } = false;
    /// <summary>
    /// 渠道Id
    /// </summary>
    public string? ChannelId { get; set; }
    /// <summary>
    /// 简历来源
    /// </summary>
    public RegisterSource Source { get; set; } = default!;
    /// <summary>
    /// 投递id
    /// </summary>
    public string ApplicationId { get; set; } = default!;
    /// <summary>
    /// 第三方简历池主键id
    /// </summary>
    public string ResumeBufferId { get; set; } = default!;
    /// <summary>
    /// 最终推送职位Id
    /// </summary>
    public string? SendTeamPostId { get; set; }
    /// <summary>
    /// 最终推送职位名称
    /// </summary>
    public string? SendTeamPostName { get; set; }
    /// <summary>
    /// 职位id
    /// </summary>
    public string? JobId { get; set; }
    /// <summary>
    /// 职位名称
    /// </summary>
    public string? JobName { get; set; }
    /// <summary>
    /// 诺快聘职位名称
    /// </summary>
    public string? PostName { get; set; }
    /// <summary>
    /// 申请时间
    /// </summary>
    public DateTime ApplyTime { get; set; }
    /// <summary>
    /// 企业id
    /// </summary>
    public string? CompanyId { get; set; }
    /// <summary>
    /// 企业中文
    /// </summary>
    public string? CompanyBusinessName { get; set; }
    /// <summary>
    /// 企业工商code
    /// </summary>
    public string? CompanyBusinessCode { get; set; }
    
    public string? RecruitId { get; set; }
    public string? RecruitRecordId { get; set; }
    /// <summary>
    /// 是否新注册
    /// </summary>
    public bool newSeeker = false;

    /// <summary>
    /// 渠道hrid
    /// </summary>
    public string? ChannelHrid { get; set; }

    /// <summary>
    /// 渠道名称
    /// </summary>
    public string? ChannelHrName { get; set; }
}

/// <summary>
/// 用户信息
/// </summary>
public class UserSeekerInfo
{
    public string? SeekerId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 性别编码
    /// </summary>
    public int? GenderCode { get; set; }

    /// <summary>
    /// 性别名称
    /// </summary>
    public string? GenderName { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int Age { get; set; }

    /// <summary>
    /// 用户所在城市
    /// </summary>
    public string? DataUserCity { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 位置信息 - 快招工使用
    /// </summary>
    public LocationCity DataLocationCitys { get; set; } = new LocationCity();

    /// <summary>
    /// 年龄范围的最小值
    /// </summary>
    public int? ExtInfoAgeMin { get; set; }

    /// <summary>
    /// 年龄范围的最大值
    /// </summary>
    public int? ExtInfoAgeMax { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 毕业年月
    /// </summary>
    public DateOnly? GraduationDate { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// 微信号
    /// </summary>
    public string? WeChatNo { get; set; }

    /// <summary>
    /// QQ号
    /// </summary>
    public string? Qq { get; set; }

    /// <summary>
    /// 专业
    /// </summary>
    public string? Major { get; set; }

    /// <summary>
    /// 最高学历学校
    /// </summary>
    public string? TopSchool { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 描述/自我评价
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 性格
    /// </summary>
    public List<string> Nature { get; set; } = new List<string>();

    /// <summary>
    /// 技能
    /// </summary>
    public List<string> Skill { get; set; } = new List<string>();

    /// <summary>
    /// 外貌
    /// </summary>
    public List<string> Appearance { get; set; } = new List<string>();
}

/// <summary>
/// kuaishou_talent_infos 表信息
/// </summary>
public class KsTalentInfo : ThirdTalentInfo
{
    /// <summary>
    /// 绑定的租户 id
    /// </summary>
    public string OpenTenantId { get; set; } = default!;

    /// <summary>
    /// 简历id
    /// </summary>
    public string ResumeId { get; set; } = default!;
    
    /// <summary>
    /// 来源渠道（简历来源，直播、短视频等）
    /// </summary>
    public string? ChannelName { get; set; }
    /// <summary>
    /// 推荐人（来源主播昵称）
    /// </summary>
    public string? Recommender { get; set; }
    /// <summary>
    /// 推荐人（来源主播uid）
    /// </summary>
    public string? RecommenderId { get; set; }
    /// <summary>
    /// 0 - 未知的 1 - 自招职位投递 2 - 分销职位投递
    /// </summary>
    public int Platform { get; set; }
    /// <summary>
    /// 来源渠道 英文
    /// </summary>
    public string? DataChannelSourceCode { get; set; }
    /// <summary>
    /// 来源渠道 中文
    /// </summary>
    public string? DataChannelSourceName { get; set; }
    /// <summary>
    /// 简历质量标签
    /// </summary>
    public DataQualityLabel DataQualityLabel { get; set; } = new DataQualityLabel();
    /// <summary>
    /// 投递时的动态简历信息
    /// </summary>
    public DataDynamicResumeInfo DataDynamicResumeInfo { get; set; } = new DataDynamicResumeInfo();
    /// <summary>
    /// 流量来源
    /// </summary>
    public string? DataTrafficSources { get; set; }
    /// <summary>
    /// 意向职位
    /// </summary>
    public List<IntentionJob> ExtInfoIntentionJob { get; set; } = new List<IntentionJob>();
    /// <summary>
    /// 意向城市
    /// </summary>
    public List<IntentionCity> ExtInfoIntentionCity { get; set; } = new List<IntentionCity>();
    /// <summary>
    /// 在职状态
    /// </summary>
    public int? ExtInfoJobHuntingStatus { get; set; }
    /// <summary>
    /// 回访信息
    /// </summary>
    public KSHrTalentRelations? KSHrTalentRelations { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;
    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreatedUser { get; set; }
    /// <summary>
    /// 修改人
    /// </summary>
    public string? UpdatedUser { get; set; }
}

/// <summary>
/// kuaishou_hr_talent_relations 表信息
/// </summary>
public class KSHrTalentRelations
{
    /// <summary>
    /// 诺快聘顾问Id
    /// </summary>
    public string HrId { get; set; } = default!;
    /// <summary>
    /// 诺快聘顾问名称
    /// </summary>
    public string? HrName { get; set;}
    /// <summary>
    /// 快手简历ApplicationId
    /// </summary>
    public string ApplicationId { get; set; } = default!;
    /// <summary>
    /// 是否回访：默认0-未回访，1-已回访
    /// </summary>
    public ReturnVisit ReturnVisit { get; set; } = ReturnVisit.未回访;
    /// <summary>
    /// 回访情况描述
    /// </summary>
    public string? VisitMemo { get; set; }
    /// <summary>
    /// 是否有效：默认1-有效，0-无效
    /// </summary>
    public ResultStatus Result { get; set; } = ResultStatus.有效;
    /// <summary>
    /// 无效简历类型
    /// </summary>
    public string? InvalidType { get; set; }
    /// <summary>
    /// 二次回访是否转换：0-未转化，1-已转化
    /// </summary>
    public SecondVisit SecondVisit { get; set; } = SecondVisit.未转化;
    /// <summary>
    /// 转化情况描述
    /// </summary>
    public string? SecondVisitMemo { get; set; }
    /// <summary>
    /// 是否等会再打电话：默认0-不是，1-是
    /// </summary>
    public WaitToPhone WaitToPhone { get; set; } = WaitToPhone.不是;
    /// <summary>
    /// 是否推送：0-未推送，1-已推送
    /// </summary>
    public KuaishouStatus Status { get; set; } = KuaishouStatus.未推送;
}
