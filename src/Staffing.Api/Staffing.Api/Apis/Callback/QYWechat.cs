﻿using System.Text;
using Config;
using Config.CommonModel.ThirdTalentInfo;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.Hr;
using Microsoft.AspNetCore.Builder;
using System.Xml.Linq;
using Microsoft.AspNetCore.Mvc;
using System.Xml.Serialization;
using Infrastructure.QYWechat;
using Infrastructure.QYWechat.Model;
using Staffing.Core.Interfaces.Common;
using Staffing.Core.Services.Common;
using System.Security.Policy;
using DocumentFormat.OpenXml.Math;
using Staffing.Model.Callback;
using Infrastructure.Exceptions;
using System.Collections.Generic;

namespace Staffing.Api.Apis.Callback;

/// <summary>
/// 企业微信事件回调
/// </summary>
[Service(ServiceLifetime.Transient)]
public class QYWechatRouter : ICallbackRouterBase
{
    public void AddRoutes(RouteGroupBuilder group)
    {
        //HttpMethod.Get.Method, HttpMethod.Post.Method
        group.MapMethods("/thirdabut/qywechat/eventCallBack", [HttpMethod.Post.Method], async ([AsParameters] QYWechatCallbackRequestQuery query, HttpRequest request, LogManager log, IQYWechatService QYWechatService) =>
        {
            string bodyString = string.Empty;
            try
            {
                query.Msg_Signature = string.IsNullOrEmpty(query.Msg_Signature) ? query.Echostr : Uri.UnescapeDataString(query.Msg_Signature);
                query.Nonce = string.IsNullOrEmpty(query.Nonce) ? query.Echostr : Uri.UnescapeDataString(query.Nonce);
                query.Echostr = string.IsNullOrEmpty(query.Echostr) ? query.Echostr : Uri.UnescapeDataString(query.Echostr);
                if (string.IsNullOrEmpty(query.Msg_Signature) || string.IsNullOrEmpty(query.Nonce))
                {
                    log.Error("企业微信事件回调：接收回调数据为空", string.Empty, query);
                    throw new Exception("接收企业微信回调数据出错：内容为空");
                }
                // GET 请求处理（验证URL有效性）
                if (request.Method == HttpMethod.Get.Method)
                {
                    if (string.IsNullOrEmpty(query.Echostr))
                    {
                        log.Error("企业微信事件回调：接收加密字符串参数为空", string.Empty, query);
                        throw new Exception("接收企业微信回调数据出错：加密字符串参数为空");
                    }
                    var verifyResult = QYWechatHelper.VerifyURL(query.Msg_Signature, query.TimeStamp, query.Nonce, query.Echostr);
                    //验证通过，原样返回明文消息内容(不能加引号，不能带bom头，不能带换行符)
                    if (verifyResult.Result)
                        return verifyResult.ReplyEchoStr;
                    else
                        return verifyResult.ErrMsg;
                }
                // POST 请求处理（微信消息推送）
                else if (request.Method == HttpMethod.Post.Method)
                {
                    // 读取 XML 请求体
                    bodyString = RequestParams.GetParams(request.HttpContext);
                    if (string.IsNullOrEmpty(bodyString))
                    {
                        log.Error("企业微信事件回调：Body内容为空", bodyString, query);
                        return bodyString;
                    }
                    var verifyResult = QYWechatHelper.DecryptMsg(query.Msg_Signature, query.TimeStamp, query.Nonce, bodyString);
                    if (!verifyResult.Result)
                    {
                        log.Error("企业微信事件回调：检验消息真实性返回失败", $"{bodyString},验证返回结果={verifyResult.ErrMsg}", query);
                        return bodyString;
                    }
                    else
                        log.Info("企业微信事件回调：检验消息真实性返回成功", $"{bodyString},验证返回结果={verifyResult.Msg}");

                    var requestModel = verifyResult.Msg.ParseXmlToModel<QYWechatCallbackRequestPost_GroupChat>();
                    if (requestModel == null)
                    {
                        log.Error("企业微信事件回调：将XML转为Model失败", $"解密后的XML：{verifyResult.Msg}", query);
                        return bodyString;
                    }

                    //解析事件枚举
                    var eventType = ExchangeEventType(requestModel);

                    bool Result = false; string ErrMsg = string.Empty;
                    switch (eventType)
                    {
                        case QYWechatCallbackEventType.客户群创建事件:
                            (Result, ErrMsg) = await QYWechatService.PushToLoadGroupInfo(requestModel.ChatId!);
                            break;
                        case QYWechatCallbackEventType.客户群变更事件:
                            var UpdateDetail = ExchangeGropChatUpdateDetail(requestModel.UpdateDetail!);
                            (Result, ErrMsg) = await QYWechatService.UpdateGroupChat(requestModel.ChatId!, UpdateDetail, requestModel.MemChangeList);
                            break;
                        case QYWechatCallbackEventType.客户群解散事件:
                            (Result, ErrMsg) = QYWechatService.DismissGroupChat(requestModel.ChatId!);
                            break;
                        default:
                            return bodyString;
                    }

                    if (Result)
                        return string.Empty;
                    else
                    {
                        //被动回复消息
                        var result = QYWechatHelper.EncryptMsg(verifyResult.Msg, query.TimeStamp, query.Nonce);
                        return result.EncryptMsg;
                    }

                }

                return "MethodError:" + request.Method;
            }
            catch (Exception e)
            {
                log.Error("企业微信事件回调出错", Tools.GetErrMsg(e) + $"；bodyString={bodyString}", query);
                return "err";
            }
        }).WithOpenApi(operation => new(operation) { Summary = "企业微信事件回调" });

        group.MapPost("/thirdabut/qywechat/loadallgroupchat", async (HttpRequest request, LogManager log, IQYWechatService QYWechatService) =>
        {
            try
            {
                var result = await QYWechatService.LoadGroupChatByAll();
                if (result.Result)
                    return "导入完成," + result.ErrMsg;
                else
                    return result.ErrMsg;
            }
            catch (Exception e)
            {
                log.Error("导入企业微信全量微信群", Tools.GetErrMsg(e));
                return e.ToJsonString();
            }
        }).WithOpenApi(operation => new(operation) { Summary = "导入企业微信全量微信群" });

        group.MapPost("/thirdabut/qywechat/getemployees", async (HttpRequest request, LogManager log, IQYWechatService QYWechatService) =>
        {
            var result = await QYWechatService.LoadEmploeeByAll();

            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "获取企业微信全量员工信息" });

        group.MapPost("/thirdabut/qywechat/loadgroupchatbyemp", async (QWLoadGroupByEmpRequest Model, HttpRequest request, LogManager log, IQYWechatService QYWechatService) =>
        {
            if (string.IsNullOrEmpty(Model.Mobile))
                return "mobile不能为空";
            var useridResult = await QYWechatHelper.GetUserIdByMobile(Model.Mobile);
            if (useridResult.Result)
            {
                var result = await QYWechatService.LoadGroupChatByOwner(useridResult.Userid!, true);
                return result.ErrMsg;
            }
            return useridResult.ErrMsg;
        }).WithOpenApi(operation => new(operation) { Summary = "导入指定员工的企微群" });
        group.MapPost("/thirdabut/qywechat/getgroupchatbyemp", async (QWLoadGroupByEmpRequest Model, HttpRequest request, LogManager log, IQYWechatService QYWechatService) =>
        {
            if (string.IsNullOrEmpty(Model.Mobile))
                throw new BadRequestException("缺少Mobile");
            var useridResult = await QYWechatHelper.GetUserIdByMobile(Model.Mobile);
            if (!useridResult.Result)
                throw new BadRequestException("请求企微获取手机号关联员工信息失败，返回" + useridResult.ErrMsg);

            QWLoadGroupByEmpResponse result = new() { Groups = new() };
            int limit = 500;
            string? cursor = null;
            do
            {
                //获取群列表
                var GroupList = await QYWechatHelper.GetGroupChatList(limit, cursor, new List<string> { useridResult.Userid! });
                if (!GroupList.Result)
                    throw new BadRequestException("获取企微群列表失败");
                if (GroupList.Group_Chat_List != null)
                {
                    foreach (var item in GroupList.Group_Chat_List)
                    {
                        var groupInfo = await QYWechatHelper.GetGroupChatInfo(item.Chat_Id!, false);
                        if (!groupInfo.Result)
                            throw new BadRequestException($"获取指定企微群详情失败，群ID={item.Chat_Id}，返回错误={groupInfo.ErrMsg}");
                        result.Groups.Add(new QWLoadGroupByEmpResponseItem
                        {
                            GropChatId = groupInfo.Group_Chat!.Chat_Id,
                            Name = groupInfo.Group_Chat!.Name,
                            CreateTime = QYWechatHelper.ExchangeTimeStamp(groupInfo.Group_Chat!.Create_Time),
                        });
                    }
                }
                cursor = GroupList.Next_Cursor;
            } while (!string.IsNullOrEmpty(cursor));
            return result;

        }).WithOpenApi(operation => new(operation) { Summary = "实时从企微拉取指定员工的企微群名称及ID" });
        group.MapPost("/thirdabut/qywechat/loadgroupchatbyid", async (QWLoadGroupByIdRequest Model, HttpRequest request, LogManager log, IQYWechatService QYWechatService) =>
        {
            if (string.IsNullOrEmpty(Model.GropChatId))
                return "GropChatId不能为空";
            var result = await QYWechatService.PushToLoadGroupInfo(Model.GropChatId, false);
            return result.ErrMsg;
        }).WithOpenApi(operation => new(operation) { Summary = "导入指定企微群ID的群信息" });
    }

    /// <summary>
    /// 分析回调事件枚举
    /// </summary>
    /// <param name="Model"></param>
    /// <returns></returns>
    QYWechatCallbackEventType ExchangeEventType(QYWechatCallbackRequestPost_Base Model)
    {
        QYWechatCallbackEventType EventType = QYWechatCallbackEventType.其他;
        if (Model.Event == "change_external_contact")
        {
            switch (Model.ChangeType)
            {
                case "add_external_contact":
                    EventType = QYWechatCallbackEventType.添加企业客户事件;
                    break;
                case "edit_external_contact":
                    EventType = QYWechatCallbackEventType.编辑企业客户事件;
                    break;
                case "add_half_external_contact":
                    EventType = QYWechatCallbackEventType.外部联系人免验证添加成员事件;
                    break;
                case "del_external_contact":
                    EventType = QYWechatCallbackEventType.删除企业客户事件;
                    break;
                case "del_follow_user":
                    EventType = QYWechatCallbackEventType.删除跟进成员事件;
                    break;
                case "transfer_fail":
                    EventType = QYWechatCallbackEventType.客户接替失败事件;
                    break;
            }
        }
        else if (Model.Event == "change_external_chat")
        {
            switch (Model.ChangeType)
            {
                case "create":
                    EventType = QYWechatCallbackEventType.客户群创建事件;
                    break;
                case "update":
                    EventType = QYWechatCallbackEventType.客户群变更事件;
                    break;
                case "dismiss":
                    EventType = QYWechatCallbackEventType.客户群解散事件;
                    break;
            }
        }
        return EventType;
    }

    QYWechatCallbackGropChatUpdateDetail ExchangeGropChatUpdateDetail(string UpdateDetail)
    {
        QYWechatCallbackGropChatUpdateDetail enumUpdateDetail = QYWechatCallbackGropChatUpdateDetail.其他;
        switch (UpdateDetail)
        {
            case "add_member":
                enumUpdateDetail = QYWechatCallbackGropChatUpdateDetail.成员入群;
                break;
            case "del_member":
                enumUpdateDetail = QYWechatCallbackGropChatUpdateDetail.成员退群;
                break;
            case "change_owner":
                enumUpdateDetail = QYWechatCallbackGropChatUpdateDetail.群主变更;
                break;
            case "change_name":
                enumUpdateDetail = QYWechatCallbackGropChatUpdateDetail.群名变更;
                break;
            case "change_notice":
                enumUpdateDetail = QYWechatCallbackGropChatUpdateDetail.群公告变更;
                break;
        }
        return enumUpdateDetail;
    }
}

public class LoadEmployeeInfoRequest
{
    /// <summary>
    /// 顾问填写的企业微信手机号
    /// </summary>
    public string? Mobile { get; set; }
}
