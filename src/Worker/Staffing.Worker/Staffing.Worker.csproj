<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <DockerComposeProjectPath>../../docker-compose.dcproj</DockerComposeProjectPath>
    <UserSecretsId>************************************</UserSecretsId>
  </PropertyGroup>

  <PropertyGroup>
    <NoWarn>1701;1702;1591;NU1803;CS8981;</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(RunConfiguration)' == 'Staffing.Worker' " />

  <ItemGroup>
    <Content Include="TempFile\Certificate\postertemplateBottom_qwtip.png">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Infrastructure\Config\Config.csproj">
    </ProjectReference>
    <ProjectReference Include="..\..\Infrastructure\Infrastructure\Infrastructure.csproj">
    </ProjectReference>
    <ProjectReference Include="..\..\Entity\Staffing.Entity\Staffing.Entity.csproj">
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Update="TempFile\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Update="TempFile\Certificate\msyh.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TempFile\Certificate\postertemplateBottom.jpg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TempFile\Certificate\postertemplateTableTr.jpg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TempFile\Certificate\postertemplateTop.jpg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>