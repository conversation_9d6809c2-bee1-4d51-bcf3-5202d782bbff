﻿using System.Text.Json.Serialization;
using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;

namespace Staffing.Model.Seeker.Recruit;

public class SeekerGetInterview
{
    /// <summary>
    /// 投递Id
    /// </summary>
    public string? DeliveryId { get; set; }
}

public class SeekerGetInterviewInfo
{
    public string? InterviewId { get; set; }

    /// <summary>
    /// 招聘id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// HrId
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// Hr名字
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// Hr电话
    /// </summary>
    public string? HrMobile { get; set; }

    /// <summary>
    /// 面试官Id
    /// </summary>
    public string? InterviewerId { get; set; }

    /// <summary>
    /// 面试官名称
    /// </summary>
    public string? InterviewerName { get; set; }

    /// <summary>
    /// 面试官电话
    /// </summary>
    public string? InterviewerPhone { get; set; }

    /// <summary>
    /// 面试过程
    /// </summary>
    public RecruitInterviewProcess? Process { get; set; }

    /// <summary>
    /// 面试用户反馈
    /// </summary>
    public RecruitInterviewUserFeedBack UserFeedBack { get; set; }

    /// <summary>
    /// 面试形式
    /// </summary>
    public RecruitInterviewForms Forms { get; set; }

    /// <summary>
    /// 面试形式名称
    /// </summary>
    public string? FormsName { get; set; }

    /// <summary>
    /// 面试用户反馈名称
    /// </summary>
    public string? UserFeedBackName { get; set; }

    /// <summary>
    /// 面试时间
    /// </summary>
    public DateTime? InterviewTime { get; set; }

    /// <summary>
    /// 面试时间文本
    /// </summary>
    public string? InterviewTimeText { get; set; }

    /// <summary>
    /// x分钟后超时，0代表超时
    /// </summary>
    public int MinuteLeft { get; set; }

    /// <summary>
    /// 超时文本
    /// </summary>
    public string? OvertimeText { get; set; }

    /// <summary>
    /// 面试地区Id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 面试地区
    /// </summary>
    public CityModel? Region { get; set; }

    /// <summary>
    /// 面试地点地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 面试地点纬度
    /// </summary>
    public decimal Lat { get; set; }

    /// <summary>
    /// 面试地点经度
    /// </summary>  
    public decimal Lng { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remarks { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public GetPostDetail? Post { get; set; }

    /// <summary>
    /// 代招企业
    /// </summary>
    public GetAgentEntDetail? AgentEnt { get; set; }

    /// <summary>
    /// 无需面试
    /// </summary>
    public bool NoInterviewRequired { get; set; }
}

public class SeekerSetInterview
{
    /// <summary>
    /// 投递Id
    /// </summary>
    public string? DeliveryId { get; set; }

    /// <summary>
    /// 操作状态
    /// </summary>
    public RecruitInterviewUserFeedBack? UserFeedBack { get; set; }
}

/// <summary>
/// 用户端获取offer通知返回模型
/// </summary>
public class GetOfferNoticeResponse
{
    /// <summary>
    /// 内容
    /// </summary>
    public string? NoticeContent { get; set; }

    /// <summary>
    /// 携带资料结合字符串
    /// </summary>
    [JsonIgnore]
    public string? CarryInformationText { get; set; }

    /// <summary>
    /// 携带资料结合
    /// </summary>
    public List<string>? CarryInformation { get; set; }
}