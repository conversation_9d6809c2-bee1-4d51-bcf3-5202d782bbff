using Config;
using Config.CommonModel;
using Config.CommonModel.Tencent;
using Config.Enums;
using FreeRedis;
using Infrastructure.Common;
using Infrastructure.CommonRepository.ZhaoPinYun;
using Infrastructure.CommonService;
using Infrastructure.CommonService.Ndn;
using Infrastructure.Extend;
using Infrastructure.NoahCommon;
using Infrastructure.Proxy;
using Microsoft.EntityFrameworkCore;
using Noah.Aliyun.Storage;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;

namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class UserService
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _logger;
    private readonly IObjectStorage _objectStorage;
    private readonly ParseResume _parseResume;
    private readonly CommonDicService _commonDicService;
    private readonly CommonVirtualService _commonVirtualService;
    private readonly WeChatHelper _weChatHelper;
    private readonly TencentImHelper _tencentImHelper;
    private readonly ZPYunRepository _zpyunRepository;
    private readonly CommonUserService _commonUserService;
    private readonly NuoPinApi _nuoPinApi;
    private readonly TencentAdHelper _tencentAdHelper;
    private readonly INdnService _ndnService;
    public UserService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger,
    IObjectStorage objectStorage, ParseResume parseResume, CommonDicService commonDicService, NuoPinApi nuoPinApi,
    CommonVirtualService commonVirtualService, WeChatHelper weChatHelper, TencentImHelper tencentImHelper,
    CommonUserService commonUserService, TencentAdHelper tencentAdHelper, INdnService ndnService, ZPYunRepository zpyunRepository)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _objectStorage = objectStorage;
        _parseResume = parseResume;
        _commonDicService = commonDicService;
        _commonVirtualService = commonVirtualService;
        _weChatHelper = weChatHelper;
        _tencentImHelper = tencentImHelper;
        _commonUserService = commonUserService;
        _nuoPinApi = nuoPinApi;
        _tencentAdHelper = tencentAdHelper;
        _ndnService = ndnService;
        _zpyunRepository = zpyunRepository;
    }
    public static string? TeamPostId;
    public async Task SubHrRegister(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.LPop(SubscriptionKey.HrRegister);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }
                MyRedis.Client.SAdd(SubscriptionKey.AdviserShareCertificate, msg);
                // 处理消息
                using var _context = _contextFactory.CreateDbContext();

                var hr = _context.User_Hr.First(x => x.UserId == msg);

                try
                {
                    await _commonUserService.GetHrImUserSig(msg);
                }
                catch (Exception e)
                {
                    _logger.Error($"{SubscriptionKey.HrRegister}注册im出错", e.Message, $"{msg}/{hr.NickName}");
                }


                var url = await RetryHelper.Do(async () => await _weChatHelper.GetWxaCodeUnlimitForHr(msg));
                // var url = await _weChatHelper.GetWxaCodeUnlimitForHr(msg);
                hr.AppletQrCode = url;

                _context.SaveChanges();

            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.HrRegister}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    public async Task SubSeekerRegister(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.LPop(SubscriptionKey.SeekerRegister);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                // 处理消息
                using var _context = _contextFactory.CreateDbContext();

                var seekerInfo = _context.User_Seeker.Where(x => x.UserId == msg)
                .Select(s => new
                {
                    s.User.Mobile,
                    s.HrAppletQrCode
                }).FirstOrDefault();

                //注册im
                try
                {
                    await _commonUserService.GetSeekerImUserSig(msg);
                }
                catch (Exception e)
                {
                    _logger.Error($"{SubscriptionKey.SeekerRegister}注册im出错", Tools.GetErrMsg(e), msg);
                }

                try
                {
                    if (!string.IsNullOrEmpty(seekerInfo?.Mobile))
                        await _nuoPinApi.SilentRegistration(seekerInfo.Mobile);
                }
                catch (Exception e)
                {
                    _logger.Error($"{SubscriptionKey.SeekerRegister}诺聘静默注册出错", Tools.GetErrMsg(e), msg);
                }

                MyRedis.Client.SAdd(SubscriptionKey.NewSeekerMobile, msg);

                //制作简历二维码
                var seeker = _context.User_Seeker.First(x => x.UserId == msg);
                var url = await RetryHelper.Do(async () => await _weChatHelper.GetWxaCodeUnlimitForSeeker(msg));
                // var url = await _weChatHelper.GetWxaCodeUnlimitForSeeker(msg);
                seeker.HrAppletQrCode = url;
                _context.SaveChanges();

                // 更新人才融合库talent_resume
                MyRedis.Client.RPush(SubscriptionKey.SeekerToTalentResumeChange, msg);
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.SeekerRegister}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 求职者注册或更换手机号后，绑定虚拟人才库
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubNewSeekerMobile(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop(SubscriptionKey.NewSeekerMobile);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                // 处理消息
                using var _context = _contextFactory.CreateDbContext();

                var seeker = _context.User_Seeker.Where(x => x.UserId == msg)
                .Select(s => new
                {
                    s.User.Mobile,
                    s.User.IdentityCard
                }).FirstOrDefault();

                if (seeker == null)
                    continue;

                var talentVirtual = _context.Talent_Virtual.Where(x => x.Mobile == seeker.Mobile && x.Status == TalentVirtualStatus.UnRegistered)
                .ToList();

                if (talentVirtual.Count == 0)
                    continue;

                foreach (var item in talentVirtual)
                {
                    item.SeekerId = msg;
                    if (string.IsNullOrWhiteSpace(seeker.IdentityCard))
                        item.Status = TalentVirtualStatus.Registered;
                    else
                        item.Status = TalentVirtualStatus.RealName;
                }

                _context.SaveChanges();

            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.NewSeekerMobile}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    // /// <summary>
    // /// 顾问数量变化
    // /// </summary>
    // /// <param name="cancel"></param>
    // /// <returns></returns>
    // public async Task SubAdviserCountChange(CancellationToken cancel)
    // {
    //     //处理消息
    //     Sub_AdviserCount_Change? msg = null;
    //     while (!cancel.IsCancellationRequested)
    //     {
    //         var workStartTime = DateTime.Now;
    //         try
    //         {
    //             msg = MyRedis.Client.SPop<Sub_AdviserCount_Change>(SubscriptionKey.AdviserCountChange);

    //             if (msg == null)
    //             {
    //                 await Task.Delay(500);
    //                 continue;
    //             }

    //             //处理消息
    //             using var _context = _contextFactory.CreateDbContext();
    //             //更新用户顾问数量
    //             _context.Database.ExecuteSqlInterpolated($"UPDATE user_num SET Adviser = (SELECT COUNT(1) FROM talent_platform WHERE SeekerId = {msg.SeekerId}) WHERE UserId = {msg.SeekerId}");
    //         }
    //         catch (Exception e)
    //         {
    //             _logger.Error($"{SubscriptionKey.AdviserCountChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
    //             await Task.Delay(1000);
    //         }

    //         await Task.Delay(10);
    //     }
    // }

    /// <summary>
    /// 人才库数量变化
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubTalentCountChange(CancellationToken cancel)
    {
        //处理消息
        Sub_TalentCount_Change? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_TalentCount_Change>(SubscriptionKey.TalentCountChange);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息
                using var _context = _contextFactory.CreateDbContext();

                //更新顾问用户数量
                _context.Database.ExecuteSqlInterpolated($"UPDATE user_num SET Talent = (SELECT COUNT(1) FROM talent_platform WHERE HrId = {msg.HrId} AND Deleted = 0) WHERE UserId = {msg.HrId}");

                //更新用户顾问数量
                if (!string.IsNullOrWhiteSpace(msg.SeekerId))
                    _context.Database.ExecuteSqlInterpolated($"UPDATE user_num SET Adviser = (SELECT COUNT(1) FROM talent_platform WHERE SeekerId = {msg.SeekerId}) WHERE UserId = {msg.SeekerId}");


                //缓存随机头像
                var avatars = _context.Talent_Platform.Where(x => x.HrId == msg.HrId)
                .OrderBy(o => EF.Functions.Random())
                .Take(10)
                .Select(s => s.User_Seeker.Avatar!).ToList();
                var hrCacheKey = $"{RedisKey.HrBehavior.Key}{msg.HrId}";
                MyRedis.Client.HSet(hrCacheKey, RedisKey.HrBehavior.TalentAvatars, avatars);

                //今日新增人才
                var today = DateTime.Today;
                var todayNewTal = _context.Talent_Platform.Count(x => x.HrId == msg.HrId && x.CreatedTime >= today);
                var todayKey = $"{RedisKey.HrStatBehavior.DailyKey}{DateTime.Today.ToString("yyyyMMdd")}";
                var newTalKey = $"{RedisKey.HrStatBehavior.NewTalent}{msg.HrId}";
                MyRedis.Client.HSet(todayKey, newTalKey, todayNewTal);

                //历史新增人才
                MyRedis.Client.HIncrBy(RedisKey.HrStatBehavior.FullKey, $"{RedisKey.HrStatBehavior.NewTalent}{msg.HrId}", 1);

            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.TalentCountChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(20);
        }
    }

    /// <summary>
    /// 虚拟人才库数量变化
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubVirtualTalentCountChange(CancellationToken cancel)
    {
        //处理消息
        Sub_TalentCount_Change? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_TalentCount_Change>(SubscriptionKey.VirtualTalentCountChange);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息
                using var _context = _contextFactory.CreateDbContext();
                _context.Database.ExecuteSqlInterpolated($"UPDATE user_num SET VirtualTalent = (SELECT COUNT(1) FROM talent_virtual WHERE HrId = {msg.HrId}) WHERE UserId = {msg.HrId}");

                //计算行业职位人才分布
                var postKey = $"{RedisKey.HrBehavior.HrTalentPost}{msg.HrId}";
                var indKey = $"{RedisKey.HrBehavior.HrTalentIndustry}{msg.HrId}";

                msg.PostOld = msg.PostOld?.Where(x => !string.IsNullOrWhiteSpace(x))?.Distinct()?.ToList() ?? new List<string>();
                msg.PostNew = msg.PostNew?.Where(x => !string.IsNullOrWhiteSpace(x))?.Distinct()?.ToList() ?? new List<string>();
                msg.IndustryOld = msg.IndustryOld?.Where(x => !string.IsNullOrWhiteSpace(x))?.Distinct()?.ToList() ?? new List<string>();
                msg.IndustryNew = msg.IndustryNew?.Where(x => !string.IsNullOrWhiteSpace(x))?.Distinct()?.ToList() ?? new List<string>();

                if (msg.PostOld.Count > 0 || msg.PostNew.Count > 0 || msg.IndustryOld.Count > 0 || msg.IndustryNew.Count > 0)
                {
                    //职位和行业分别放入2个排行榜
                    using (var tran = MyRedis.Client.Multi())
                    {
                        foreach (var item in msg.PostOld)
                            MyRedis.Client.ZIncrBy(postKey, -1, item);

                        foreach (var item in msg.PostNew)
                            MyRedis.Client.ZIncrBy(postKey, 1, item);

                        foreach (var item in msg.IndustryOld)
                            MyRedis.Client.ZIncrBy(indKey, -1, item);

                        foreach (var item in msg.IndustryNew)
                            MyRedis.Client.ZIncrBy(indKey, 1, item);

                        tran.Exec();

                        var postNum = msg.PostNew.Count - msg.PostOld.Count;
                        var indNum = msg.IndustryNew.Count - msg.IndustryOld.Count;

                        //顺便记录总数，可以用来计算其他项
                        var hrCacheKey = $"{RedisKey.HrBehavior.Key}{msg.HrId}";
                        if (postNum != 0)
                            MyRedis.Client.HIncrBy(hrCacheKey, RedisKey.HrBehavior.HrTalentPostNum, postNum);
                        if (indNum != 0)
                            MyRedis.Client.HIncrBy(hrCacheKey, RedisKey.HrBehavior.HrTalentIndustryNum, indNum);
                    }
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.VirtualTalentCountChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(20);
        }
    }

    /// <summary>
    /// 计算hr排行榜
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubHrRank(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<string>(SubscriptionKey.HrRank);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                //计算hr积分
                using var _context = _contextFactory.CreateDbContext();

                var hr = _context.User_Hr.Where(x => x.UserId == msg)
                .Select(s => new
                {
                    s.Score,
                    s.User_Extend.LoginTime
                }).FirstOrDefault();

                if (hr == null)
                    continue;

                var actScore = Tools.GetOnlineScore(hr.LoginTime);

                MyRedis.Client.ZAdd(RedisKey.HrRank, hr.Score + actScore, msg);
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.HrRank}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    public async Task SubUpdateSeeker(CancellationToken cancel)
    {
        //处理消息
        Sub_UpdateSeeker? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            try
            {
                msg = MyRedis.Client.SPop<Sub_UpdateSeeker?>(SubscriptionKey.UpdateSeeker);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                using var _context = _contextFactory.CreateDbContext();
                if (msg.IsNewSeeker)
                {
                    //求职者注册消息
                    MyRedis.Client.RPush(SubscriptionKey.SeekerRegister, msg.SeekerId);
                }

                // 计算简历完善度
                _commonUserService.CountResumeScore(msg.SeekerId!);
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.UpdateSeeker}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 每晚更新hr排行榜（登录活跃度变化）
    /// </summary>
    public async void UpdateHrRank(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            //每晚1-2点间执行
            if (DateTime.Now < DateTime.Today.AddHours(2) || DateTime.Now > DateTime.Today.AddHours(4))
                return;

            if (!MyRedis.Client.SetNx($"updatehrrank:{DateTime.Today.ToShortDateString()}", 1, 3600 * 24))
                return;

            //计算hr积分
            using var _context = _contextFactory.CreateDbContext();

            var offset = 0;
            var limit = 100;
            while (true)
            {
                var hrs = _context.User_Hr.OrderByDescending(o => o.UserId)
                .Select(s => new
                {
                    s.UserId,
                    s.Score,
                    s.User_Extend.LoginTime
                }).Skip(offset).Take(limit).ToList();

                if (hrs.Count < 1)
                    break;

                offset += limit;

                var ranks = new List<ZMember>();
                foreach (var item in hrs)
                {
                    var actScore = Tools.GetOnlineScore(item.LoginTime);
                    ranks.Add(new ZMember(item.UserId, item.Score + actScore));
                }
                MyRedis.Client.ZAdd(RedisKey.HrRank, ranks.ToArray());

                await Task.Delay(50);
            }
        }
        catch (Exception e)
        {
            _logger.Error("更新hr排行榜出错", Tools.GetErrMsg(e));
        }
    }

    public async Task SubUpdateSkImAct(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop(SubscriptionKey.UpdateSkImAct);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                // 处理消息

                using var _context = _contextFactory.CreateDbContext();
                var seeker = _context.User_Seeker.Where(x => x.UserId == msg)
                .Select(s => new
                {
                    s.NickName,
                    s.Avatar,
                    s.TencentImId,
                    s.User_Resume.Occupation,
                    s.User_Resume.Birthday,
                    s.User_Resume.Sex
                }).First();

                if (!string.IsNullOrWhiteSpace(seeker.TencentImId))
                    await _tencentImHelper.SetImAccount(new SetImAccount
                    {
                        From_Account = seeker.TencentImId,
                        NickName = seeker.NickName,
                        Avatar = seeker.Avatar,
                        Occupation = seeker.Occupation,
                        Birthday = seeker.Birthday,
                        Sex = seeker.Sex
                    });

            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.UpdateSkImAct}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }
    /// <summary>
    /// 云生创建或更新会话Id
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubTeamPostId(CancellationToken cancel)
    {
        //处理消息
        Sub_ImC2CTeamPostId? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_ImC2CTeamPostId>(SubscriptionKey.ImC2CTeamPostId);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                // 处理消息
                using var _context = _contextFactory.CreateDbContext();

                var resumeid = _context.YunSheng_ResumeId_Relation.
                    Where(x => x.UserId == msg.UserId).
                    Select(x => x.ResumeId).FirstOrDefault();

                //var yunshengPostId = _context.YunSheng_JobId_Relation
                //    .Join(_context.Post_Team,
                //          y => y.PostId,
                //          p => p.PostId,
                //          (y, p) => new { y.YunShengPostId, p.TeamPostId })
                //    .Where(x => x.TeamPostId == msg.TeamPostId)
                //    .Select(x => x.YunShengPostId)
                //    .FirstOrDefault();
                _logger.Info($"TeamPostId:{msg.TeamPostId}", "");
                #region 上方获取yunshengPostId是AI从以下代码优化过来的
                var postid = _context.Post_Team.
                    Where(s => s.TeamPostId == msg.TeamPostId).
                    Select(s => s.PostId).FirstOrDefault();
                _logger.Info($"PostId:{postid}", "");
                var yunshengPostId = _context.YunSheng_JobId_Relation.
                    Where(s => s.PostId == postid && 
                    s.TeamPostId == msg.TeamPostId && 
                    s.TeamPostId != null).
                    Select(s => s.YunShengPostId).FirstOrDefault();
                #endregion
                _logger.Info($"resumeid:{resumeid}", "");
                _logger.Info($"yunshengPostId:{yunshengPostId}", "");
                TeamPostId = msg.TeamPostId!;
                if (resumeid != null && yunshengPostId != null)
                {
                    var result = await _zpyunRepository.NewChat(yunshengPostId, resumeid);
                    if (result != null)
                    {
                        var exist = _context.YunSheng_ChatId_Relation.
                            Where(x => x.UserId == msg.UserId && x.TeamPostId == msg.TeamPostId && x.AdviserImId == msg.AdviserImId).
                            FirstOrDefault();
                        _logger.Info($"UserId:{msg.UserId}", "");
                        if (exist == null)
                        {
                            var yunshengChat = new YunSheng_ChatId_Relation
                            {
                                UserId = msg.UserId,
                                TeamPostId = msg.TeamPostId,
                                AdviserImId = msg.AdviserImId,
                                YunshengChatId = result.chatId,
                                CreatedTime = DateTime.Now,
                                UpdatedTime = DateTime.Now
                            };

                            _context.YunSheng_ChatId_Relation.Add(yunshengChat);
                        }
                        else
                        {
                            exist.YunshengChatId = result.chatId;
                            exist.UpdatedTime = DateTime.Now;
                        }
                        _context.SaveChanges();
                    }
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.ImC2CTeamPostId}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }
    public async Task SubUpdateHrImAct(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop(SubscriptionKey.UpdateHrImAct);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                // 处理消息
                using var _context = _contextFactory.CreateDbContext();
                var hr = _context.User_Hr.Where(x => x.UserId == msg)
                .Select(s => new
                {
                    s.NickName,
                    s.Avatar,
                    s.TencentImId,
                    EntName = string.IsNullOrEmpty(s.Enterprise.Abbreviation) ? s.Enterprise.Name : s.Enterprise.Abbreviation,
                    s.Post
                }).First();

                if (!string.IsNullOrWhiteSpace(hr.TencentImId))
                    await _tencentImHelper.SetImAccount(new SetImAccount
                    {
                        From_Account = hr.TencentImId,
                        NickName = hr.NickName,
                        Avatar = hr.Avatar,
                        EntName = hr.EntName,
                        Post = hr.Post
                    });

            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.UpdateHrImAct}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    public async Task SubVisitAdviser(CancellationToken cancel)
    {
        //处理消息
        Sub_SeekerAndHr? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop<Sub_SeekerAndHr>(SubscriptionKey.VisitAdviser);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                // 处理消息
                using var _context = _contextFactory.CreateDbContext();

                //今日访问
                var today = DateTime.Today;
                var todayVist = _context.Talent_Platform.Count(x => x.HrId == msg.HrId && x.SeekerVisitTime >= today);
                var todayKey = $"{RedisKey.HrStatBehavior.DailyKey}{DateTime.Today.ToString("yyyyMMdd")}";
                var todayVistKey = $"{RedisKey.HrStatBehavior.Visits}{msg.HrId}";
                MyRedis.Client.HSet(todayKey, todayVistKey, todayVist);

                //历史访问
                var fullVisitsKey = $"{RedisKey.HrStatBehavior.HyperLogKey}{RedisKey.HrStatBehavior.Visits}{msg.HrId}";
                MyRedis.Client.PfAdd(fullVisitsKey, $"{DateTime.Today.ToString("yyyyMMdd")}{msg.SeekerId}");
                var totalVst = MyRedis.Client.PfCount(fullVisitsKey);
                MyRedis.Client.HSet(RedisKey.HrStatBehavior.FullKey, $"{RedisKey.HrStatBehavior.Visits}{msg.HrId}", totalVst);
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.VisitAdviser}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }

            await Task.Delay(10);
        }
    }

    /// <summary>
    /// 用户浏览职位
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubUserPostVisit(CancellationToken cancel)
    {
        //处理消息
        Sub_UserPostVisit? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.RPop<Sub_UserPostVisit>(SubscriptionKey.UserPostVisit);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                if (!MyRedis.Client.SetNx($"usrvispt:{msg.UserId}_{msg.PostId}", 1, TimeSpan.FromDays(1)))
                {
                    await Task.Delay(10);
                    continue;
                }

                using var _context = _contextFactory.CreateDbContext();
                _context.Add(new User_Post_Visit
                {
                    Post = msg.Post,
                    UserId = msg.UserId,
                    HrId = msg.HrId
                });
                _context.SaveChanges();

                //计算求职意向
                MyRedis.Client.SAdd(SubscriptionKey.CountUserHope, msg.UserId);
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.UserPostVisit}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(500);
            }

            await Task.Delay(20);
        }
    }

    /// <summary>
    /// 计算用户求职意向
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubCountUserHope(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            var workStartTime = DateTime.Now;
            try
            {
                msg = MyRedis.Client.SPop(SubscriptionKey.CountUserHope);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }

                //处理消息
                using var _context = _contextFactory.CreateDbContext();

                //计算求职意向
                var userHope = _context.User_Post_Visit.Where(x => x.UserId == msg)
                .OrderByDescending(o => o.CreatedTime).Take(100)
                .GroupBy(g => g.Post)
                .Select(s => new
                {
                    s.Key,
                    PostName = s.Max(m => m.Dic_Post.Name),
                    Ct = s.Count()
                }).ToList()
                .OrderByDescending(o => o.Ct).Take(3)
                .Select(s => new { s.Key, s.PostName }).ToList();

                var userExt = new User_Extend
                {
                    UserId = msg
                };
                _context.Attach(userExt);

                userExt.DesiredPost = string.Join(',', userHope.Select(s => s.Key));
                userExt.DesiredPostName = string.Join(',', userHope.Select(s => s.PostName));

                _context.SaveChanges();
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.CountUserHope}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(500);
            }

            await Task.Delay(20);
        }
    }

    /// <summary>
    /// 腾讯广告
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SubTencentAdReport(CancellationToken cancel)
    {
        //处理消息
        Sub_TencentAdReport? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            try
            {
                msg = MyRedis.Client.RPop<Sub_TencentAdReport>(SubscriptionKey.TencentAdReport);

                if (msg == null)
                {
                    await Task.Delay(1000, cancel);
                    continue;
                }
                _logger.Info("SubTencentAdReport", $"腾讯广告-开始处理", JsonSerializer.SerializeToString(msg));
                await _tencentAdHelper.Report(msg.ClickId!, msg.SetId, msg.AccountId, msg.At!);
                _logger.Info("SubTencentAdReport", $"腾讯广告-结束处理", JsonSerializer.SerializeToString(msg));
            }
            catch (TaskCanceledException) { }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.TencentAdReport}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(500, cancel);
            }

            await Task.Delay(20, cancel);
        }
    }

    /// <summary>
    /// 注册时更新人才融合库
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task SeekerRegisterToTalentResume(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            try
            {
                msg = MyRedis.Client.LPop(SubscriptionKey.SeekerToTalentResumeChange);

                if (msg == null)
                {
                    await Task.Delay(500);
                    continue;
                }
                // 处理消息
                using var _context = _contextFactory.CreateDbContext();
                try
                {
                    await _commonUserService.SeekerRegisterToTalentResume(msg);
                }
                catch (Exception e)
                {
                    _logger.Error($"{SubscriptionKey.SeekerToTalentResumeChange}更新人才融合库", Tools.GetErrMsg(e), msg);
                }
            }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.SeekerToTalentResumeChange}消息处理出错", Tools.GetErrMsg(e), JsonSerializer.SerializeToString(msg));
                await Task.Delay(1000);
            }
        }
    }

    // public async Task UserPartmentUpdateAsync(CancellationToken cancel)
    // {
    //     try
    //     {
    //         using var _context = _contextFactory.CreateDbContext();
    //         //User_DingDing表查询员工工号
    //         var userNos = _context.User_DingDing.Where(w => !string.IsNullOrWhiteSpace(w.DingJobNo))
    //         .Select(s => new { s.UserId, s.DingJobNo }).ToList()
    //         .Select(s => (s.UserId, s.DingJobNo)).ToList();

    //         var existUserIds = userNos.Select(s => s.UserId).ToList();

    //         //钉钉用户表查询员工工号
    //         var userNos2 = _context.User.Where(x => !string.IsNullOrEmpty(x.Dd_User.DdUserId) && !existUserIds.Contains(x.UserId))
    //         .Select(s => new { s.UserId, s.Dd_User.JobNumber }).ToList()
    //         .Select(s => (UserId: s.UserId, DingJobNo: s.JobNumber)).ToList();

    //         userNos = userNos.Union(userNos2).Distinct().ToList();
    //         var ndnEmployees = new Dictionary<string, UserBookRelation>();
    //         var tableInfo = _context.Ndn_Book_User.ToList();
    //         //查询员工帐套
    //         foreach (var item in userNos)
    //         {
    //             try
    //             {
    //                 if (string.IsNullOrWhiteSpace(item.DingJobNo))
    //                 {
    //                     _logger.Info("更新顾问账套工号为空", item.UserId, item.DingJobNo);
    //                     continue;
    //                 }
    //                 var emp = await _ndnService.GetEmployee(item.DingJobNo!);
    //                 if (emp == null) continue;
    //                 ndnEmployees.TryAdd(item.UserId, new UserBookRelation
    //                 {
    //                     UserId = item.UserId,
    //                     UserNo = item.DingJobNo,
    //                     BookCode = emp.bookCode ?? string.Empty
    //                 });

    //                 // 更新表
    //                 if (tableInfo != null && tableInfo.Count > 0)
    //                 {
    //                     var table = tableInfo.FirstOrDefault(f => f.UserId == item.UserId);
    //                     if (table == null)
    //                     {
    //                         _context.Ndn_Book_User.Add(new Ndn_Book_User
    //                         {
    //                             UserId = item.UserId,
    //                             UserNo = item.DingJobNo,
    //                             BookCode = emp.bookCode ?? string.Empty,
    //                             CreatedTime = DateTime.Now
    //                         });
    //                     }
    //                     else
    //                     {
    //                         table.UserNo = item.DingJobNo;
    //                         table.BookCode = emp.bookCode ?? table.BookCode;
    //                         table.UpdatedTime = DateTime.Now;
    //                     }
    //                 }
    //                 else
    //                 {
    //                     _context.Ndn_Book_User.Add(new Ndn_Book_User
    //                     {
    //                         UserId = item.UserId,
    //                         UserNo = item.DingJobNo,
    //                         BookCode = emp.bookCode ?? string.Empty,
    //                         CreatedTime = DateTime.Now
    //                     });
    //                 }
    //                 await Task.Delay(10);
    //             }
    //             catch (Exception ex)
    //             {
    //                 _logger.Error($"更新顾问账套：数字诺亚员工信息查询失败", Tools.GetErrMsg(ex), item.UserId);
    //             }
    //         }

    //         MyRedis.Client.HSet(RedisKey.UserHrBookCodeKey, ndnEmployees);
    //         _context.SaveChanges();
    //     }
    //     catch (Exception e)
    //     {
    //         _logger.Error($"更新顾问账套出错", Tools.GetErrMsg(e), "UserPartmentUpdate");
    //         await Task.Delay(1000);
    //     }
    // }
}


