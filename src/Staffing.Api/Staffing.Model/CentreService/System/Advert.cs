﻿using Config.CommonModel;
using Staffing.Entity.Staffing;

namespace Staffing.Model.CentreService.System
{
    /// <summary>
    /// 保存广告
    /// </summary>
    public class AdvertSave
    {
        /// <summary>
        /// 广告ID(更新时必填，为空时则添加)
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 轮播图
        /// </summary>
        public string? ImageUrl { get; set; }

        /// <summary>
        /// 链接地址
        /// </summary>
        public string? LinkUrl { get; set; }

        /// <summary>
        /// 链接方式
        /// </summary>
        public Advert_LinkType? LinkType { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public Advert_State? State { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
    }

    /// <summary>
    /// 保存广告状态
    /// </summary>
    public class AdvertUpdateState
    {
        /// <summary>
        /// 广告ID(更新时必填，为空时则添加)
        /// </summary>
        public string? Id { get; set; }


        /// <summary>
        /// 状态
        /// </summary>
        public Advert_State? State { get; set; }
    }

    /// <summary>
    /// 广告列表输出
    /// </summary>
    public class AdvertListRecord
    {
        /// <summary>
        /// 广告ID(更新时必填，为空时则添加)
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 轮播图
        /// </summary>
        public string? ImageUrl { get; set; }

        /// <summary>
        /// 链接地址
        /// </summary>
        public string? LinkUrl { get; set; }

        /// <summary>
        /// 链接方式
        /// </summary>
        public Advert_LinkType? LinkType { get; set; }

        public string? LinkTypeText { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public Advert_State? State { get; set; }

        public string? StateText { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
    }

    public class AdvertPageResponse : QueryResponse
    {
        public List<AdvertListRecord> Rows { get; set; } = new List<AdvertListRecord>();
    }
}
