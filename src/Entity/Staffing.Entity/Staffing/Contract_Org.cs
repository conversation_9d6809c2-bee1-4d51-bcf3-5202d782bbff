﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// E签宝机构账号表
/// </summary>
[Table("contract_org")]
public class Contract_Org
{
    [Key]
    public string OrgId { get; set; } = EntityTools.SnowflakeId();
    
    /// <summary>
    /// 证件类型，默认CRED_ORG_USCC
    /// </summary>
    public string IdType { get; set; } = "CRED_ORG_USCC";

    /// <summary>
    /// 企业证件号，需传入真实存在的证件信息
    /// </summary>
    public string IdNumber { get; set; } = null!;

    /// <summary>
    /// 机构名称
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// E签宝机构Id
    /// </summary>
    public string EOrgId { get; set; } = null!;

    public string CreatedBy { get; set; } = null!;
    public string UpdatedBy { get; set; } = null!;
    public DateTime UpdatedTime { get; set; } = DateTime.Now;
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}
