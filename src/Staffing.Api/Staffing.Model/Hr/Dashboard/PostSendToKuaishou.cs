using Config.Enums;
using Config.CommonModel;

namespace Staffing.Model.Hr.Dashboard;

public class PostSendToKuaishouRequest
{
    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? BeginDate { get; set; }

    /// <summary>
    /// 截止日期
    /// </summary>
    public DateTime? EndDate { get; set; }

    public string? Key { get; set; }
}

public class PostSendToKuaishouResponse
{
    public List<PostSendToKuaishou> Rows { get; set; } = new List<PostSendToKuaishou>();
    public object? Str { get; set; }
}

public class PostSendToKuaishou
{
    /// <summary>
    /// 排序序号
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 职位类型
    /// </summary>
    public string? PostType { get; set; }
    public PostWorkNature WorkNature { get;set;}
    /// <summary>
    /// 用工企业
    /// </summary>
    public string? Company { get; set; }
    /// <summary>
    /// 企业性质（国/私）
    /// </summary>
    public string? CompanyType { get; set; }
    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }
    /// <summary>
    /// 招聘人数
    /// </summary>
    public int? RecruitNumber { get; set; }
    /// <summary>
    /// 学历
    /// </summary>
    public string? Education { get; set; }
    /// <summary>
    /// 年龄
    /// </summary>
    public string? AgeRange { get; set; }
    public int? MinAge { get; set; }
    public int? MaxAge { get; set; }
    /// <summary>
    /// 工作经验
    /// </summary>
    public string? WorkExperience { get; set; }
    /// <summary>
    /// 性别要求
    /// </summary>
    public string? Sex { get; set; }
    /// <summary>
    /// 其他要求
    /// </summary>
    public string? Others { get; set; }
    /// <summary>
    /// 薪酬说明
    /// </summary>
    public string? SalaryName { get; set; }
    /// <summary>
    /// 职位职责
    /// </summary>
    public string? Describe { get; set; }
    /// <summary>
    /// 工作地址（到街道）
    /// </summary>
    public string? Address { get; set; }
    /// <summary>
    /// 岗位亮点
    /// </summary>
    public List<WelfareModel>? Welfares { get; set; }
    public string? Welfare { get; set; }

    public int MinSalary { get; set; }
    public int MaxSalary { get; set; }
    public int Salary { get; set; }
    public PostSalaryType SalaryType { get; set; }
}