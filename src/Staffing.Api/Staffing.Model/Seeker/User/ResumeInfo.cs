﻿namespace Staffing.Model.Seeker.User;

// public class UpdateAttchResume
// {
//     /// <summary>
//     /// 附件地址
//     /// </summary>
//     public string Url { get; set; } = string.Empty;

//     /// <summary>
//     /// 附件大小
//     /// </summary>
//     public long Size { get; set; }

//     /// <summary>
//     /// 语言
//     /// </summary>
//     public ResumeLanguage Language { get; set; } = ResumeLanguage.中文;
// }

// public class SeekerCheckMobile
// {
//     /// <summary>
//     /// 手机号
//     /// </summary>
//     public string? Mobile { get; set; }
// }

// public class UpdateSeeker
// {
//     /// <summary>
//     /// 姓名
//     /// </summary>
//     public string? Name { get; set; }

//     /// <summary>
//     /// 头像
//     /// </summary>
//     public string? Avatar { get; set; }

//     /// <summary>
//     /// 电话
//     /// </summary>
//     public string? Mobile { get; set; }

//     /// <summary>
//     /// 新电话（修改手机号）
//     /// </summary>
//     public string? NewMobile { get; set; }

//     /// <summary>
//     /// 验证码（如果修改手机号必填）
//     /// </summary>
//     public string? SmsCode { get; set; }

//     /// <summary>
//     /// 旧电话（修改手机号需要验证）
//     /// </summary>
//     public string? OldMobile { get; set; }

//     /// <summary>
//     /// 职业
//     /// </summary>
//     public OccupationType? Occupation { get; set; }

//     /// <summary>
//     /// 性别
//     /// </summary>
//     public Sex? Sex { get; set; }

//     /// <summary>
//     /// 学历
//     /// </summary>
//     public EducationType? Education { get; set; }

//     /// <summary>
//     /// 学校
//     /// </summary>
//     public string? School { get; set; }

//     /// <summary>
//     /// 毕业日期
//     /// </summary>
//     public DateOnly? GraduationDate { get; set; }

//     /// <summary>
//     /// 专业
//     /// </summary>
//     public string? Major { get; set; }

//     /// <summary>
//     /// 简历完善度
//     /// </summary>
//     public int Score { get; set; }

//     /// <summary>
//     /// 描述
//     /// </summary>
//     public string? Describe { get; set; }

//     /// <summary>
//     /// 性格
//     /// </summary>
//     public List<string>? Nature { get; set; }

//     /// <summary>
//     /// 技能
//     /// </summary>
//     public List<string>? Skill { get; set; }

//     /// <summary>
//     /// 外貌
//     /// </summary>
//     public List<string>? Appearance { get; set; }

//     /// <summary>
//     /// 证书
//     /// </summary>
//     public List<string>? Certificate { get; set; }

//     /// <summary>
//     /// 生日
//     /// </summary>
//     public DateOnly? Birthday { get; set; }

//     /// <summary>
//     /// 邮箱
//     /// </summary>
//     public string? EMail { get; set; }

//     /// <summary>
//     /// 微信号
//     /// </summary>
//     public string? WeChatNo { get; set; }

//     /// <summary>
//     /// qq号
//     /// </summary>
//     public string? Qq { get; set; }

//     /// <summary>
//     /// 显示
//     /// </summary>
//     public bool? Show { get; set; }

//     /// <summary>
//     /// 匿名（暂不用）
//     /// </summary>
//     public bool? Anonymous { get; set; }

//     /// <summary>
//     /// 公众号通知
//     /// </summary>
//     public bool? H5Notice { get; set; }
// }

// public class ResumeInfo : UpdateSeeker
// {
//     /// <summary>
//     /// 用户Id
//     /// </summary>
//     public string UserId { get; set; } = default!;

//     /// <summary>
//     /// 工作经历
//     /// </summary>
//     public List<ResumeWorkInfo>? Works { get; set; }

//     /// <summary>
//     /// 校园经历
//     /// </summary>
//     public List<ResumeCampusInfo>? Campus { get; set; }

//     /// <summary>
//     /// 职业
//     /// </summary>
//     public string? OccupationName { get; set; }

//     /// <summary>
//     /// 性别
//     /// </summary>
//     public string? SexName { get; set; }

//     /// <summary>
//     /// 学历
//     /// </summary>
//     public string? EducationName { get; set; }
// }

// //校园经历
// public class ResumeCampusInfo : UpdateCampus
// {
//     /// <summary>
//     /// 用户Id
//     /// </summary>
//     public string UserId { get; set; } = default!;
// }

// //工作经历
// public class ResumeWorkInfo : UpdateWorks
// {
//     /// <summary>
//     /// 用户Id
//     /// </summary>
//     public string UserId { get; set; } = default!;
// }

// public class UpdateCampus
// {
//     public string Id { get; set; } = string.Empty;

//     /// <summary>
//     /// 名称
//     /// </summary>
//     public string Name { get; set; } = string.Empty;

//     /// <summary>
//     /// 职位/奖项
//     /// </summary>
//     public string Award { get; set; } = string.Empty;

//     /// <summary>
//     /// 开始时间
//     /// </summary>
//     public DateOnly? BeginDate { get; set; }

//     /// <summary>
//     /// 结束时间
//     /// </summary>
//     public DateOnly? EndDate { get; set; }

//     /// <summary>
//     /// 描述
//     /// </summary>
//     public string? Describe { get; set; }
// }

// public class UpdateCampusResponse
// {
//     public string Id { get; set; } = string.Empty;
// }

// public class UpdateWorks
// {
//     public string Id { get; set; } = string.Empty;

//     /// <summary>
//     /// 公司
//     /// </summary>
//     public string Company { get; set; } = string.Empty;

//     /// <summary>
//     /// 职位
//     /// </summary>
//     public string Post { get; set; } = string.Empty;

//     /// <summary>
//     /// 开始时间
//     /// </summary>
//     public DateOnly? BeginDate { get; set; } = default;

//     /// <summary>
//     /// 结束时间
//     /// </summary>
//     public DateOnly? EndDate { get; set; } = default;

//     /// <summary>
//     /// 描述
//     /// </summary>
//     public string? Describe { get; set; }
// }

// public class UpdateWorksResponse
// {
//     public string Id { get; set; } = string.Empty;
// }