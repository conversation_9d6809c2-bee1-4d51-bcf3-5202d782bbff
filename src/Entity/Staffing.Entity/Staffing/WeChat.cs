﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 微信信息
/// </summary>
[Table("wechat")]
public class WeChat
{
    [Key]
    public string WeChatId { get; set; } = default!;

    /// <summary>
    /// 诺聘公众号OpenId
    /// </summary>
    public string? WeChatH5OpenId { get; set; }

    /// <summary>
    /// 是否订阅诺聘公众号
    /// </summary>
    public bool WeChatH5Subscribe { get; set; }

    /// <summary>
    /// 诺快聘公众号OpenId
    /// </summary>
    public string? WeChatH5OpenId2 { get; set; }

    /// <summary>
    /// 是否订阅诺快聘公众号
    /// </summary>
    public bool WeChatH5Subscribe2 { get; set; }

    public DateTime UpdatedTime { get; set; } = DateTime.Now;
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}