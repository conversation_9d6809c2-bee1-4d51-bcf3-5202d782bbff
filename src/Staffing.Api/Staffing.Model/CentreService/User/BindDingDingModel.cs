﻿namespace Staffing.Model.CentreService.User;

public class BindDingDingModelRequest
{
    public List<string> Phones { get; set; } = default!;
}

public class BindDingDingModelResponse
{
    public Dictionary<string, string> ErrorPhone { get; set; } = default!;
}

public class BindDingDingRequest
{
    /// <summary>
    /// hr手机号
    /// </summary>
    public string HrPhone { get; set; } = default!;

    /// <summary>
    /// 钉钉手机号
    /// </summary>
    public string DingPhone { get; set; } = default!;
}

public class BindDingDingRsponse
{
    /// <summary>
    /// 钉钉姓名
    /// </summary>
    public string? DingName { get; set; }

    /// <summary>
    /// 钉钉手机号
    /// </summary>
    public string? DingMobile { get; set; }

    /// <summary>
    /// 钉钉工号
    /// </summary>
    public string? DingJobNo { get; set; }

    /// <summary>
    /// 钉钉头像
    /// </summary>
    public string? DingAvatar { get; set; }
}

public class DdBindRequest
{
    /// <summary>
    /// hrId
    /// </summary>
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 钉钉手机号
    /// </summary>
    public string DingPhone { get; set; } = default!;
}

public class DdUnBindRequest
{
    /// <summary>
    /// hrId
    /// </summary>
    public string UserId { get; set; } = default!;
}