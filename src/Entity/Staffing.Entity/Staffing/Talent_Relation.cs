﻿// using System;
// using System.Collections.Generic;
// using System.ComponentModel.DataAnnotations;
// using System.ComponentModel.DataAnnotations.Schema;
// using System.Linq;
// using System.Text;
// using System.Threading.Tasks;

// namespace Staffing.Entity.Staffing;

// /// <summary>
// /// 人才库（平台用户和虚拟用户关系）
// /// </summary>
// [Table("talent_relation")]
// public class Talent_Relation
// {
//     /// <summary>
//     /// 主键id
//     /// </summary>
//     [Key]
//     public string Id { get; set; } = EntityTools.SnowflakeId();

//     /// <summary>
//     /// HrId
//     /// </summary>
//     public string HrId { get; set; } = default!;

//     /// <summary>
//     /// 求职者Id
//     /// </summary>
//     public string SeekerId { get; set; } = default!;

//     /// <summary>
//     /// 虚拟人才Id
//     /// </summary>
//     public string TalentId { get; set; } = default!;

//     /// <summary>
//     /// 创建时间
//     /// </summary>
//     public DateTime CreatedTime { get; set; } = DateTime.Now;

//     public User_Hr User_Hr { get; set; } = default!;

//     public User_Seeker User_Seeker { get; set; } = default!;

//     public Talent_Virtual Talent_Virtual { get; set; } = default!; 
// }

