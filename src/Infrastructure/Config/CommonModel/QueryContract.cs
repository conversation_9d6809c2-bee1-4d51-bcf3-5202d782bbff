﻿namespace Config.CommonModel;

public class QueryRequest
{
    private int? _pageSize;
    private int? _pageIndex;

    /// <summary>
    /// 每次查询多少条(默认20)
    /// </summary>
    public int PageSize
    {
        get => _pageSize ?? 20;
        set => _pageSize = value > 10000 ? 10000 : value;
    }

    /// <summary>
    /// 页
    /// </summary>
    public int PageIndex
    {
        get => _pageIndex ?? 1;
        set => _pageIndex = value < 1 ? 1 : value;
    }

    /// <summary>
    /// 最后一条记录的时间戳
    /// </summary>
    public long? LastTimeStamp { get; set; }
}

public class QueryResponse
{
    /// <summary>
    /// 总记录数
    /// </summary>
    public int Total { get; set; }

    /// <summary>
    /// 是否最后一页
    /// </summary>
    public bool IsLast { get; set; } = true;

    /// <summary>
    /// 最后一条记录的时间戳
    /// </summary>
    public long? LastTimeStamp { get; set; }
}

public class MetaData
{
    /// <summary>
    /// 总记录数
    /// </summary>
    public int Total { get; set; }
}


public class EmptyResponse
{

}

public class RowsResponse<T>
{
    public List<T> Rows { get; set; } = default!;
}

public class ErrorType
{
    public string? code { get; set; }
    public string? msg { get; set; }
}

public class QuerySearch
{
    /// <summary>
    /// 模糊查询字符串
    /// </summary>
    public string? Search { get; set; }
}

/// <summary>
/// 地区查询条件
/// </summary>
public class AreaSearch
{
    /// <summary>
    /// 是否全国默认为0-非全国，0 - 非全国，1 - 全国
    /// </summary>
    public int Area { get; set; } = 0;
    /// <summary>
    /// 省份
    /// </summary>
    public string? ProvinceName { get; set; }
    /// <summary>
    /// 地市
    /// </summary>
    public string? CityName { get; set; }
}

