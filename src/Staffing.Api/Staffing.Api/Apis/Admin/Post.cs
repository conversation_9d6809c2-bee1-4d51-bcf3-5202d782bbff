using Staffing.Core.Interfaces.CentreService;
using Staffing.Model.CentreService.Report;
using Infrastructure.Extend;

namespace Staffing.Api.Apis.Admin;

[Service(ServiceLifetime.Transient)]
public class PostRouter : IAdminRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("post").WithTags("职位");
        group.MapGet("/excellent", ([AsParameters] GetExcellentPost model, IPostService rep) => rep.GetExcellentPost(model))
            .WithOpenApi(operation => new(operation) { Summary = "职位优选" });

        group.MapPut("/excellent", (SetExcellentPost model, IPostService rep) => rep.SetExcellentPost(model))
            .WithOpenApi(operation => new(operation) { Summary = "职位更新" });

        group.MapPost("/excellent/overtime", (GetOvertimeExcellentPost model, IPostService rep) => rep.GetOvertimeExcellentPost(model))
            .WithOpenApi(operation => new(operation) { Summary = "超时记录" });

        group.MapPost("/excellent/exportovertime", (GetOvertimeExcellentPostAll model, IPostService rep) =>
        {
            using (var excel = new OfficeOpenXml.ExcelPackage(new FileInfo("wwwroot/TempFile/超时记录.xlsx")))
            {
                var sheet = excel.Workbook.Worksheets.First();
                var beginRow = 2;

                int DataCount = 0,
                    PageIndex = 1,
                    PageSize = 100,
                    rowIndex = beginRow;
                do
                {
                    var result = rep.GetOvertimeExcellentPost(new GetOvertimeExcellentPost
                    {
                        HRName = model.HRName,
                        PostName = model.PostName,
                        SeekerMobile = model.SeekerMobile,
                        InterviewOverTimeHour = model.InterviewOverTimeHour,
                        EntryOverTimeHour = model.EntryOverTimeHour,
                        PageIndex = PageIndex,
                        PageSize = PageSize
                    });

                    if (result.Rows == null || result.Rows.Count == 0)
                        continue;
                    DataCount = result.Rows.Count;

                    sheet.InsertRow(rowIndex + 1, DataCount - 1, beginRow);

                    foreach (var item in result.Rows)
                    {
                        sheet.Row(rowIndex).Height = 20;
                        var col = 1;
                        sheet.Cells[rowIndex, col++].Value = item.PostName;
                        sheet.Cells[rowIndex, col++].Value = item.SeekerName;
                        sheet.Cells[rowIndex, col++].Value = item.SeekerMobile;
                        sheet.Cells[rowIndex, col++].Value = item.HRName;
                        sheet.Cells[rowIndex, col++].Value = item.EnterpriseName;
                        sheet.Cells[rowIndex, col++].Value = item.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss");
                        sheet.Cells[rowIndex, col++].Value = item.RecruitStatusText;
                        sheet.Cells[rowIndex, col++].Value = item.InterviewOverHour;
                        sheet.Cells[rowIndex, col++].Value = item.EntryInOverHour;
                        rowIndex++;
                    }
                    PageIndex++;
                } while (DataCount == PageSize);
                var newName = $"超时数据{DateTime.Today.ToString("yyyyMMdd")}.xlsx";
                using (var stream = new MemoryStream())
                {
                    excel.SaveAs(stream);
                    return Results.File(stream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", newName);
                }
            }
        })
        .WithOpenApi(operation => new(operation) { Summary = "导出超时记录" });
    }
}