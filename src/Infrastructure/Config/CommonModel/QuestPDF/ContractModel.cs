﻿namespace Config.CommonModel.QuestPDF;

public class ContractModel
{
    /// <summary>
    /// 唯一标识
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 文件名称
    /// </summary>
    public string? FileName { get; set; }

    /// <summary>
    /// 合同号码
    /// </summary>
    public string ContractNumber { get; set; } = string.Empty;

    /// <summary>
    /// 卖方公司名称(乙方)
    /// </summary>
    public string SellerCompanyName { get; set; } = default!;

    /// <summary>
    /// 乙方公司地址
    /// </summary>
    public string SellerCompanyAddress { get; set; } = default!;

    /// <summary>
    /// 乙方公司联系人
    /// </summary>
    public string SellerCompanyContacter { get; set; } = default!;

    /// <summary>
    /// 乙方公司联系人电话
    /// </summary>
    public string SellerCompanyContacterNumber { get; set; } = default!;

    /// <summary>
    /// 乙方公司印章
    /// </summary>
    public string SellerCompanyImageUrl { get; set; } = default!;

    /// <summary>
    /// 开户名称
    /// </summary>
    public string AccountName { get; set; } = default!;

    /// <summary>
    /// 开户行
    /// </summary>
    public string AccountAddress { get; set; } = default!;

    /// <summary>
    /// 开户账号
    /// </summary>
    public string Account { get; set; } = default!;

    /// <summary>
    /// 买方公司名称(甲方)
    /// </summary>
    public string CustomerCompanyName { get; set; } = default!;

    /// <summary>
    /// 甲方公司地址
    /// </summary>
    public string CustomerCompanyAddress { get; set; } = default!;

    /// <summary>
    /// 甲方公司联系人
    /// </summary>
    public string CustomerCompanyContacter { get; set; } = default!;

    /// <summary>
    /// 甲方公司联系人电话
    /// </summary>
    public string CustomerCompanyContacterNumber { get; set; } = default!;

    /// <summary>
    /// 甲方公司印章
    /// </summary>
    public string CustomerCompanyImageUrl { get; set; } = default!;

    /// <summary>
    /// 账单信息
    /// </summary>
    public ContractOrderInfo OrderInfo { get; set; } = new ContractOrderInfo();
}

public class ContractOrderInfo
{
    public List<ContractOrderItem> Items { get; set; } = new List<ContractOrderItem>();
}

public class ContractOrderItem
{
    /// <summary>
    /// 月账单编号
    /// </summary>
    public string OrderNumber { get; set; } = default!;

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ProjectName { get; set; } = default!;

    /// <summary>
    /// 交付方式名称
    /// </summary>
    public string BountyName { get; set; } = default!;

    /// <summary>
    /// 账单金额
    /// </summary>
    public decimal BountyAmount { get; set; }

    /// <summary>
    /// 账单时间
    /// </summary>
    public string? BountyTime { get; set; }
}


public class ContractModel2 : ContractModel
{
    /// <summary>
    /// 账单信息
    /// </summary>
    public ContractOrderInfo2 OrderInfo2 { get; set; } = new();
}
public class ContractOrderInfo2
{
    public List<ContractOrderItem2> Items { get; set; } = [];
}
public class ContractOrderItem2 : ContractOrderItem
{
    public string? ParentId { get; set; }

    /// <summary>
    /// 确认成果
    /// </summary>
    public string Achievement { get; set; } = default!;
}