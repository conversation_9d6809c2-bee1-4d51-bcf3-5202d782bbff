﻿namespace Config.CommonModel.DataScreen;

public class ProjectSummaryResponse
{
    /// <summary>
    /// 项目总数量
    /// </summary>
    public int ProjectNum { get; set; }

    /// <summary>
    /// 审核通过项目数量
    /// </summary>
    public int PassedProjectNum { get; set; }

    /// <summary>
    /// 审核通过开启协同项目数量
    /// </summary>
    public int TeamPProjectNum { get; set; }

    /// <summary>
    /// 审核通过未开启协同项目数量
    /// </summary>
    public int UnTeamPProjectNum { get; set; }

    /// <summary>
    /// 未审核通过项目数量
    /// </summary>
    public int UnPassedProjectNum { get; set; }

    /// <summary>
    /// 审核通过率
    /// </summary>
    public decimal PassedRate { get; set; }

    /// <summary>
    /// 未通过原因列表
    /// </summary>
    public List<UnPassedReason>? UnPassedReasonList { get; set; }
}

public class UnPassedReason
{
    /// <summary>
    /// 未通过原因
    /// </summary>
    public string Reason { get; set; } = default!;

    /// <summary>
    /// 未通过原因对应数量
    /// </summary>
    public int Count { get; set; }
}

public class HrTalentRequest: NkpBaobiaoRequest
{
    public List<string> HrIds { get; set; } = new List<string>();
}

public class NkpBaobiaoRequest
{
    public DateTime? StartDate { get; set; }

    public DateTime? EndDate { get; set; }
}

public class DateRange
{
    /// <summary>
    /// 月份开始 yyyy-MM
    /// </summary>
    public string StartMonth { get; set; } = default!;

    /// <summary>
    /// 月份结束 yyyy-MM
    /// </summary>
    public string EndMonth { get; set; } = default!;
}

public class UserSummaryResponse
{
    /// <summary>
    /// c端用户总量
    /// </summary>
    public int? UserTotal { get; set; }

    /// <summary>
    /// c端增长总量
    /// </summary>
    public int? UserAddTotal { get; set; }

    /// <summary>
    /// 蓝领用户总量
    /// </summary>
    public int? BlueUserTotal { get; set; }
    
    /// <summary>
    /// 蓝领增长总量
    /// </summary>
    public int? BlueUserAddTotal { get; set; }

    /// <summary>
    /// 明细数据
    /// </summary>
    public List<DateWithCount>? Details { get; set; }
}

public class DateWithCount
{
    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime BeginDate { get; set; } = default!;

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime EndDate { get; set; } = default!;

    /// <summary>
    /// c端总数
    /// </summary>
    public int UserTotalCount { get; set; }

    /// <summary>
    /// c端新增
    /// </summary>
    public int UserAddTotalCount { get; set; }

    /// <summary>
    /// 蓝领总数
    /// </summary>
    public int BlueUserTotalCount { get; set; }

    /// <summary>
    /// 蓝领新增
    /// </summary>
    public int BlueUserAddTotalCount { get; set; }
}

public class DeliverySummaryResponse
{
    /// <summary>
    /// 投递简历总数量
    /// </summary>
    public int? DeliveryNum { get; set; }

    /// <summary>
    /// 招聘需求总数量
    /// </summary>
    public int? RecruitNum { get; set; }

    /// <summary>
    /// 投递简历的男性人数
    /// </summary>
    public int? DeliveryMaleNum { get; set; }

    /// <summary>
    /// 投递简历的女性人数
    /// </summary>
    public int? DeliveryFemaleNum { get; set; }

    /// <summary>
    /// 性别未知人数
    /// </summary>
    public int? DeliveryUnknownSexNum { get; set; }

    /// <summary>
    /// 明细数据
    /// </summary>
    public List<DeliveryDateWithCount>? Details { get; set; }
}

public class DeliveryDateWithCount
{
    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime BeginDate { get; set; } = default!;

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime EndDate { get; set; } = default!;

    /// <summary>
    /// 招聘人数
    /// </summary>
    public int RecruitNum { get; set; }

    /// <summary>
    /// 简历投递数量
    /// </summary>
    public int DeliveryNum { get; set; }
}

public class ZhiboDeliverySummaryResponse
{
    /// <summary>
    /// 简历完整度80%以上人数
    /// </summary>
    public int? Percent80Num { get; set; }

    /// <summary>
    /// 简历完整度50%-80%人数
    /// </summary>
    public int? Percent80_50Num { get; set; }

    /// <summary>
    /// 简历完整度50%以下人数
    /// </summary>
    public int? Percent0_50Num { get; set; }

    /// <summary>
    /// 明细数据
    /// </summary>
    public List<ZhiboDeliveryDateWithCount>? Details { get; set; }
}

public class ZhiboDeliveryDateWithCount
{
    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime BeginDate { get; set; } = default!;

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime EndDate { get; set; } = default!;

    /// <summary>
    /// 推荐人数
    /// </summary>
    public int SentNum { get; set; }

    /// <summary>
    /// 面试人数
    /// </summary>
    public int InterviewNum { get; set; }

    /// <summary>
    /// 入职人数
    /// </summary>
    public int InductionNum { get; set; }
}

public class HrTalentSummaryResponse
{
    /// <summary>
    /// 求职者总数量
    /// </summary>
    public int TalentNum { get; set; }

    /// <summary>
    /// 明细数据
    /// </summary>
    public List<HrTalentDetail>? Details { get; set; }
}

public class HrTalentDetail
{
    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime BeginDate { get; set; } = default!;

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime EndDate { get; set; } = default!;

    /// <summary>
    /// 总量
    /// </summary>
    public int SumNum { get; set; }

    /// <summary>
    /// 增量
    /// </summary>
    public int AddNum { get; set; }
}



