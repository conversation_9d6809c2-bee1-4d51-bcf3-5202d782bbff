// using Config.CommonModel;
// using Config.CommonModel.Business;
// using Config.Enums;

// namespace Staffing.Model.Common.NScore;

// public class GetScore
// {

// }

// public class GetScoreResponse
// {
//     /// <summary>
//     /// 求职者当前诺积分
//     /// </summary>
//     public int Score { get; set; }

//     /// <summary>
//     /// 求职者历史累计诺积分
//     /// </summary>
//     public int ScoreTotal { get; set; }

//     /// <summary>
//     /// 已使用积分
//     /// </summary>
//     public int ScoreUsed { get; set; }

//     /// <summary>
//     /// 物流地址
//     /// </summary>
//     public KdAddressInfo? Address { get; set; }
// }

// public class UpdateKdAddress : KdAddressInfo
// {

// }

// public class GetScoreRecord : QueryRequest
// {
//     /// <summary>
//     /// 0=正积分，1=负积分
//     /// </summary>
//     public int? Type { get; set; }
// }

// public class GetScoreRecordResponse : QueryResponse
// {
//     /// <summary>
//     /// 积分记录
//     /// </summary>
//     public List<ScoreRecordInfo>? Rows { get; set; }
// }

// public class ScoreRecordInfo
// {
//     /// <summary>
//     /// 当前值
//     /// </summary>
//     public int Amount { get; set; }

//     /// <summary>
//     /// 增量
//     /// </summary>
//     public int Increment { get; set; }

//     /// <summary>
//     /// hr还是求职者
//     /// </summary>
//     public SeekerOrHr UserType { get; set; }

//     /// <summary>
//     /// 类型
//     /// </summary>
//     public NScoreType Type { get; set; }

//     /// <summary>
//     /// 内容
//     /// </summary>
//     public string? Content { get; set; }

//     /// <summary>
//     /// 发生时间
//     /// </summary>
//     public DateTime EventTime { get; set; } = DateTime.Now;
// }