﻿using Config.Enums;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Core.Interfaces.Common;
using Config;
using LinqKit;
using ServiceStack.Text;
using Infrastructure.CommonService;
using Config.CommonModel.Business;
using Infrastructure.Exceptions;
using Staffing.Model.Service;
using TencentCloud.Faceid.V20180301.Models;
using Staffing.Model.Common.System;
using Staffing.Model.Hr.Dashboard;
using Staffing.Model.Hr.Recruit;
using Tea.Utils;

namespace Staffing.Core.Services.Service.Common;

[Service(ServiceLifetime.Transient)]
public class CommonService : ICommonService
{
    private readonly ConfigManager _config;
    private readonly RequestContext _user;
    private LogManager _log;
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly CommonDicService _commonDicService;
    private readonly TencentClient _tencentClient;
    public CommonService(IOptionsSnapshot<ConfigManager> config,
        RequestContext user, LogManager log, TencentClient tencentClient,
        IDbContextFactory<StaffingContext> contextFactory, CommonDicService commonDicService)
    {
        _config = config.Value;
        _user = user;
        _log = log;
        _contextFactory = contextFactory;
        _commonDicService = commonDicService;
        _tencentClient = tencentClient;
    }

    public bool CheckIdempotencyTimes(IdempotencyCheckType pwdType, string key)
    {
        var result = false;

        using (var db = _contextFactory.CreateDbContext())
        {
            var pwdCheck = db.Sys_Idempotency_Check.AsNoTracking()
                .FirstOrDefault(x => x.Type == pwdType && x.Key == key);

            if (pwdCheck != null && pwdCheck.CheckTimes > 50)
                result = false;
            else
                result = true;
        }

        return result;
    }

    public void CheckIdempotencyFaild(IdempotencyCheckType pwdType, string key)
    {
        var today = DateTime.Today;
        using (var db = _contextFactory.CreateDbContext())
        {
            var pwdCheck = db.Sys_Idempotency_Check.AsNoTracking()
                .FirstOrDefault(x => x.Type == pwdType && x.Key == key);

            if (pwdCheck == null)
            {
                pwdCheck = new Sys_Idempotency_Check
                {
                    CreatedTime = DateTime.Now,
                    Type = pwdType,
                    CheckTimes = 0,
                    Key = key
                };
                db.Add(pwdCheck);
            }
            pwdCheck.CheckTimes++;
            db.SaveChanges();
        }
    }

    public TokenInfo CreateRefreshToken(string userId, TokenType type, ClientType? client = null)
    {
        var result = new TokenInfo();

        using var _context = _contextFactory.CreateDbContext();
        var rt = new Token_Refresh
        {
            ExpirationTime = DateTime.Now.AddSeconds(Constants.RefreshTokenExpire),
            Token = Guid.NewGuid().ToString().Replace("-", string.Empty),
            UserId = userId,
            Type = type,
            Client = client
        };
        _context.Add(rt);

        var at = new Token_Access
        {
            ExpirationTime = DateTime.Now.AddSeconds(Constants.AccessTokenExpire),
            Token_Refresh = rt,
            Token = $"{Constants.MyTokenPrefix}{Guid.NewGuid().ToString().Replace("-", string.Empty)}",
            UserId = rt.UserId,
            Type = rt.Type,
            Client = client
        };
        _context.Add(at);

        _context.SaveChanges();

        result.AccessToken = at.Token;
        result.RefreshToken = rt.Token;
        result.TokenExpiresTime = Constants.AccessTokenExpire;
        result.UserId = rt.UserId;

        return result;
    }

    public async Task<TokenInfo> RefreshToken(string refreshToken)
    {
        //加锁刷新token
        var lockerKey = $"reftk:{refreshToken}";
        using var locker = await MyRedis.Lock(lockerKey, 10);

        if (locker == null)
            throw new Exception("刷新token获取锁失败");

        var result = new TokenInfo();

        using var _context = _contextFactory.CreateDbContext();

        var rt = _context.Token_Refresh
            .FirstOrDefault(x => x.Token == refreshToken && x.ExpirationTime > DateTime.Now);

        if (rt == null)
            throw new UnauthorizedException(string.Empty);

        //更新token
        rt.Token = Guid.NewGuid().ToString().Replace("-", string.Empty);

        var at = new Token_Access
        {
            ExpirationTime = DateTime.Now.AddSeconds(Constants.AccessTokenExpire),
            Token_Refresh = rt,
            Token = $"{Constants.MyTokenPrefix}{Guid.NewGuid().ToString().Replace("-", string.Empty)}",
            UserId = rt.UserId,
            Type = rt.Type,
            Client = rt.Client
        };
        _context.Add(at);

        _context.SaveChanges();

        result.AccessToken = at.Token;
        result.RefreshToken = rt.Token;
        result.TokenExpiresTime = Constants.AccessTokenExpire;
        result.UserId = rt.UserId;

        return result;
    }

    public async Task<IdVerificationResponse> IdVerification(IdVerificationModel model)
    {

        var lockerKey = $"idcard:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        var result = new IdVerificationResponse();

        if (string.IsNullOrWhiteSpace(model.Name) || string.IsNullOrWhiteSpace(model.IdCard))
            throw new BadRequestException("身份证号和姓名不能为空");

        using var _context = _contextFactory.CreateDbContext();

        var user = _context.User
        .First(x => x.UserId == _user.Id);

        //   .Include(i => i.User_Seeker)
        //     .Include(i => i.User_Resume)

        if (!string.IsNullOrWhiteSpace(user.IdentityCard))
            throw new BadRequestException("您已实名，无需认证");

        var ckKey = $"{RedisKey.Idempotency.IdCardCheck}_{DateTime.Today.ToYYYY_MM_DD()}_{_user.Id}";
        var todayCt = MyRedis.Client.Get<int>(ckKey);

        if (todayCt > 10)
            throw new BadRequestException("今日失败次数过多，禁止认证");

        var oldUs = _context.User.Where(x => x.IdentityCard == model.IdCard).ToList();

        //如果身份证号存在，不用请求腾讯云，省钱
        if (oldUs.Count == 0)
        {
            var req = new IdCardVerificationRequest
            {
                IdCard = model.IdCard,
                Name = model.Name
            };

            var faceidClient = _tencentClient.GetFaceidClient();

            var rep = await _tencentClient.GetFaceidClient().IdCardVerification(req);

            //如果结果计费，计数
            if (rep.Result.Equals("0") || rep.Result.Equals("-1"))
                MyRedis.Client.Set(ckKey, todayCt + 1, TimeSpan.FromDays(1));

            if (!rep.Result.Equals("0"))
            {
                var msg = string.Empty;
                var needLog = false;
                switch (rep.Result)
                {
                    case "-1":
                        msg = "姓名和身份证号不一致";
                        break;
                    case "-2":
                        msg = "非法身份证号";
                        break;
                    case "-3":
                        msg = "非法姓名";
                        break;
                    case "-5":
                        msg = "证件库中无此身份证记录";
                        break;
                    default:
                        msg = "实名服务暂不可用，请稍后再进行认证";
                        needLog = true;
                        break;
                }

                if (needLog)
                {
                    _log.Error("实名认证出错", JsonSerializer.SerializeToString(rep), JsonSerializer.SerializeToString(req));
                }

                throw new BadRequestException(msg);
            }
        }

        //其他用户自动解绑定
        oldUs.ForEach(x =>
        {
            x.IdentityCard = string.Empty;
            x.IdentityCardName = string.Empty;
        });

        user.IdentityCard = model.IdCard;
        user.IdentityCardName = model.Name;

        var seeker = _context.User_Seeker.Include(i => i.User_Resume).FirstOrDefault(x => x.UserId == user.UserId);
        var hr = _context.User_Hr.FirstOrDefault(x => x.UserId == user.UserId);

        var idInfo = Tools.GetIdCardInfo(model.IdCard);
        if (seeker != null)
        {
            seeker.NickName = model.Name;
            seeker.User_Resume.Sex = idInfo?.Sex ?? Sex.男;
            seeker.User_Resume.Birthday = idInfo?.Birthday != null ? idInfo!.Birthday!.Value : seeker.User_Resume.Birthday;
        }

        if (hr != null)
        {
            hr.NickName = model.Name;
            hr.Sex = idInfo?.Sex ?? Sex.男;
            // hr.Birthday = idInfo?.Birthday != null ? idInfo!.Birthday!.Value : hr.Birthday;
        }

        _context.SaveChanges();

        //更新im资料
        if (!string.IsNullOrWhiteSpace(hr?.TencentImId))
            MyRedis.Client.SAdd(SubscriptionKey.UpdateHrImAct, hr.UserId);

        return result;
    }

    public GetIdVerificationResponse GetIdVerification(GetIdVerification model)
    {
        var result = new GetIdVerificationResponse();

        if (string.IsNullOrWhiteSpace(model.IdCard))
            throw new BadRequestException("身份证号不能为空");

        using var _context = _contextFactory.CreateDbContext();

        result.Mobile = _context.User.Where(x => x.UserId != _user.Id && x.IdentityCard == model.IdCard)
        .Select(s => s.Mobile).FirstOrDefault();

        if (result.Mobile?.Count() >= 11)
            result.Mobile = Tools.MobilToX(result.Mobile);

        return result;
    }

    public ResumeInfo GetResume(string id, bool checkShow)
    {
        using var _context = _contextFactory.CreateDbContext();

        id = Tools.GetUserIdByImId(id)!;

        var predicate = PredicateBuilder.New<User_Resume>(x => x.UserId == id);

        var result = _context.User_Resume.Where(predicate)
        .Select(s => new ResumeInfo
        {
            RegionId = s.User_Seeker.RegionId,
            Mobile = s.User.Mobile,
            Avatar = s.User_Seeker.Avatar,
            Anonymous = s.Anonymous,
            Appearance = s.Appearance,
            Birthday = s.Birthday,
            Certificate = s.Certificate,
            Describe = s.Describe,
            Education = s.Education,
            EMail = s.EMail,
            GraduationDate = s.GraduationDate,
            Major = s.Major,
            Name = s.User_Seeker.NickName,
            Nature = s.Nature,
            Occupation = s.Occupation,
            Qq = s.Qq,
            School = s.School,
            Sex = s.Sex,
            Score = s.Score,
            Show = s.Show,
            Skill = s.Skill,
            UserId = s.UserId,
            WeChatNo = s.WeChatNo,
            Adviser = s.User.User_Num.Adviser,
            LoginTime = s.User.User_Extend.LoginTime,
            IdCard = s.User.IdentityCard,
            Campus = s.User_Campus.OrderByDescending(o => o.BeginDate).Select(s => new ResumeCampusInfo
            {
                Award = s.Award,
                BeginDate = s.BeginDate,
                Describe = s.Describe,
                EndDate = s.EndDate,
                Id = s.Id,
                Name = s.Name,
                UserId = s.UserId
            }).ToList(),
            Works = s.User_Work.OrderByDescending(o => o.BeginDate).Select(s => new ResumeWorkInfo
            {
                BeginDate = s.BeginDate,
                Company = s.Company,
                Describe = s.Describe,
                EndDate = s.EndDate,
                Id = s.Id,
                Post = s.Post,
                UserId = s.UserId
            }).ToList(),
            AttachmentResume = new AttachmentResume
            {
                Title = s.User_Seeker.User_Resume_Attach.Title,
                Url = s.User_Seeker.User_Resume_Attach.Url,
                Language = s.User_Seeker.User_Resume_Attach.Language,
                CreatedTime = s.User_Seeker.User_Resume_Attach.CreatedTime,
                Size = s.User_Seeker.User_Resume_Attach.Size
            }
        }).FirstOrDefault();

        if (result == null) // || (checkShow && result.Show != true)
            throw new NotFoundException("内容不存在");

        //如果不在人才库，隐藏电话
        if (!_context.Talent_Platform.Any(x => x.HrId == _user.Id && x.SeekerId == id))
            result.Mobile = Tools.MobilToX(result.Mobile);

        if (result.Occupation.HasValue)
            result.OccupationName = result.Occupation.GetDescription();

        if (result.Sex.HasValue)
            result.SexName = result.Sex.GetDescription();

        if (result.Education.HasValue)
            result.EducationName = result.Education.GetDescription();

        if (result.Birthday.HasValue)
            result.Age = Tools.GetAgeByBirthdate(result.Birthday);

        if (result.GraduationDate.HasValue && result.GraduationDate.Value.Year >= (DateTime.Now.Year - 1))
        {
            var fry = result.GraduationDate.Value.Year;
            result.FreshYear = fry.ToString();
            if (result.FreshYear.Length >= 2)
                result.FreshYear = result.FreshYear.Substring(result.FreshYear.Length - 2);
        }

        result.Identity = !string.IsNullOrEmpty(result.IdCard);
        result.IdCard = Tools.IdCardToX(result.IdCard);

        if (result.LoginTime.HasValue)
        {
            result.OnlineStatus = Tools.GetOnlineStatus(result.LoginTime);
            result.OnlineStatusName = result.OnlineStatus?.GetDescription();
        }

        if (!string.IsNullOrWhiteSpace(result.RegionId))
            result.Region = _commonDicService.GetCityById(result.RegionId);

        return result;
    }

    public GetMsgWordsResponse GetMsgWords(MsgWordsType type)
    {
        var lockerKey = $"st:usrmsg:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new NotAcceptableException(string.Empty);

        var result = new GetMsgWordsResponse();

        using var _context = _contextFactory.CreateDbContext();

        result.Rows = _context.Msg_Words.Where(x => x.UserId == _user.Id && x.Type == type)
        .OrderByDescending(o => o.CreatedTime)
        .Select(s => new MsgWordsInfo
        {
            Id = s.Id,
            Content = s.Content
        }).ToList();

        var commonWords = type == MsgWordsType.求职者 ? Constants.CommonWords.SeekerWords : Constants.CommonWords.HrWords;
        //如果没有常用语，插入默认常用语
        var now = DateTime.Now;
        if (result.Rows.Count == 0)
        {
            var i = 0;
            var insertWords = commonWords.Select(s => new Msg_Words
            {
                Type = type,
                Content = s,
                UserId = _user.Id,
                CreatedTime = now.AddMilliseconds((i++) * -1)
            }).ToList();

            _context.AddRange(insertWords);
            _context.SaveChanges();

            result.Rows = insertWords.OrderByDescending(o => o.CreatedTime)
            .Select(s => new MsgWordsInfo
            {
                Id = s.Id,
                Content = s.Content
            }).ToList();
        }

        return result;
    }

    public EmptyResponse UpdateMsgWords(MsgWordsInfo model, MsgWordsType type)
    {
        var lockerKey = $"st:upusr:{_user.Id}";

        //redis分布式锁，自动释放
        using var locker = MyRedis.TryLock(lockerKey);

        if (locker == null)
            throw new BadRequestException("操作频繁");

        if (string.IsNullOrWhiteSpace(model.Content))
            throw new BadRequestException("常用语不能为空");

        using var _context = _contextFactory.CreateDbContext();

        if (_context.Msg_Words.Any(x => x.UserId == _user.Id && x.Type == type && x.Content == model.Content && x.Id != _user.Id))
            throw new BadRequestException("内容重复");

        var result = new EmptyResponse();

        Msg_Words? msgWords;

        if (!string.IsNullOrWhiteSpace(model.Id))
        {
            msgWords = _context.Msg_Words.FirstOrDefault(x => x.Id == model.Id && x.UserId == _user.Id);
            if (msgWords == null)
                throw new BadRequestException("内容不存在");
        }
        else
        {
            if (_context.Msg_Words.Count(x => x.UserId == _user.Id && x.Type == type) >= 15)
                throw new BadRequestException("常用语已满");

            msgWords = new Msg_Words
            {
                UserId = _user.Id,
                Type = type
            };
            _context.Add(msgWords);
        }

        msgWords.Content = model.Content;

        _context.SaveChanges();

        return result;
    }

    public EmptyResponse DeleteMsgWords(string id)
    {
        using var _context = _contextFactory.CreateDbContext();

        var msgWords = _context.Msg_Words.FirstOrDefault(x => x.Id == id && x.UserId == _user.Id);
        if (msgWords != null)
        {
            _context.Remove(msgWords);
            _context.SaveChanges();
        }

        return new EmptyResponse();
    }

    public EmptyResponse UserCheckMobile(UserCheckMobile model)
    {
        using var _context = _contextFactory.CreateDbContext();

        var mobile = _context.User.Where(x => x.UserId == _user.Id)
        .Select(s => s.Mobile).First();

        if (model.Mobile?.Trim() != mobile.Trim())
            throw new BadRequestException("手机号码验证失败");

        return new EmptyResponse();
    }

    public GetDeliveryPostsResponse GetDeliveryPosts(GetDeliveryPosts model)
    {
        var result = new GetDeliveryPostsResponse();

        if (string.IsNullOrWhiteSpace(model.SeekerId) || string.IsNullOrWhiteSpace(model.HrId))
            throw new BadRequestException("缺少参数");

        model.SeekerId = Tools.GetUserIdByImId(model.SeekerId);

        using var _context = _contextFactory.CreateDbContext();

        result.TeamPostIds = _context.Post_Delivery
        .Where(x => x.SeekerId == model.SeekerId && x.Post_Team.Project_Team.HrId == model.HrId)
        .Select(s => s.TeamPostId).Distinct().ToList();

        return result;
    }


    /// <summary>
    /// 获取已上架的广告列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public AdvertListResponse AdvertGetListByUse()
    {
        AdvertListResponse result = new AdvertListResponse();
        //读取DB
        using (var db = _contextFactory.CreateDbContext())
        {
            result.Rows = db.Advert_Set.Where(x => x.State == Advert_State.已上架)
              .OrderBy(o => o.Sort)
              .Select(x => new AdvertListRecord()
              {
                  ImageUrl = x.ImageUrl,
                  LinkUrl = x.LinkUrl,
                  LinkType = x.LinkType
              }).ToList();
        }
        return result;
    }

    public void GetRecruitList(GetRecentRecruits model, GetRecentRecruitsResponse result, ExpressionStarter<Recruit> predicate)
    {
        // 从最外层移植过来的逻辑
        if (model.Status.HasValue)
        {
            // todo:邀约面试，跟面试官筛选要互斥，反馈通过的在邀约面试，剩下的在面试官筛选
            if (model.Status == RecruitStatus.Yaoyue)
            {
                predicate = predicate.And(o => o.Status == RecruitStatus.InterviewerScreening
                && o.Recruit_Interviewer_Screen.OrderByDescending(o => o.CreatedTime).FirstOrDefault().Status == RecruitInterviewerScreenStatus.Adopt);
            }
            else if (model.Status == RecruitStatus.InterviewerScreening)
            {
                predicate = predicate.And(o => o.Status == RecruitStatus.InterviewerScreening
                && o.Recruit_Interviewer_Screen.OrderByDescending(o => o.CreatedTime).FirstOrDefault().Status != RecruitInterviewerScreenStatus.Adopt);
            }
            //// 面试通过，跟面试中要互斥，反馈通过的在面试通过，剩下的在面试中
            //// todo:这里是自由发挥的，有可能产品让把面试反馈通过的放进offer阶段，也有可能继续放在面试中阶段，但是主创能看见了
            //else if (model.Status == RecruitStatus.Inductioned)
            //{
            //    predicate = predicate.And(o => o.Status == RecruitStatus.Induction
            //    && o.Recruit_Interview.OrderByDescending(o => o.CreatedTime).FirstOrDefault().Outcome == RecruitInterviewOutcome.Passed);
            //}
            //else if (model.Status == RecruitStatus.Induction)
            //{
            //    predicate = predicate.And(o => o.Status == RecruitStatus.Induction
            //    && o.Recruit_Interview.OrderByDescending(o => o.CreatedTime).FirstOrDefault().Outcome != RecruitInterviewOutcome.Passed);
            //}
            else if (model.Status == RecruitStatus.Jifei)
            {
                // predicate = predicate.And(o =>
                //     !o.Project_Teambounty.Deleted && o.Project_Teambounty.Project_Teambounty_Stage.Any(a => a.Status == PostStageStatus.计费待确认));
                // && o.Project_Teambounty.IfStockOut == 1);
            }
            else if (model.Status == RecruitStatus.Guobao)
            {
                // predicate = predicate.And(o => !o.Project_Teambounty.Deleted && o.Project_Teambounty.Status == BountyStatus.结算中);
            }
            else
            {
                predicate = predicate.And(o => o.Status == model.Status);
            }
        }

        //兼容工作台和招聘进度时间筛选
        if (model.BeginTime.HasValue)
            predicate = predicate.And(o => o.CreatedTime >= model.BeginTime.Value);

        if (model.EndTime.HasValue)
        {
            model.EndTime = model.EndTime.Value.Date.AddDays(1);
            predicate = predicate.And(o => o.CreatedTime < model.EndTime.Value);
        }

        // // 作为协同：所有阶段都能看到，HR初筛阶段，面试阶段可以操作，其余阶段只能看
        // // 作为主创：面试官筛选(简历需审) 可以看可以操作 - 20241030面试官筛选都能看和操作
        // // 作为主创：面试中，只有反馈通过才能看见 - 20241030都能看都能操作
        // // 作为主创：发Offer、入职阶段可以看可以操作，操作为归档的可以看归档阶段，
        // // 作为主创：直投进来的：所有阶段可以看可以操作
        // Ai优化后的逻辑
        // predicate = predicate.And(o =>
        //     (o.Post_Delivery.Post_Team.Project_Team.HrId == _user.Id) ||
        //     (o.HrId == _user.Id && o.Status != RecruitStatus.HrScreening)
        // );

        using var _context = _contextFactory.CreateDbContext();

        // var sql1 = _context.Recruit.Where(predicate.And(x => x.Post_Delivery.Post_Team.Project_Team.HrId == _user.Id));
        // var sql2 = _context.Recruit.Where(predicate.And(x => x.HrId == _user.Id && x.Status != RecruitStatus.HrScreening));

        var predicate1 = PredicateBuilder.New<Recruit>(predicate);
        predicate1 = predicate1.And(x => x.Post_Delivery.Post_Team.Project_Team.HrId == _user.Id);

        var predicate2 = PredicateBuilder.New<Recruit>(predicate);
        predicate2 = predicate2.And(x => x.HrId == _user.Id && x.Status != RecruitStatus.HrScreening);


        // 因为跨表+or查询导致索引失效，数据库查询性能较差，所以改为先查询两个表，然后在数据库层合并查询
        // 先构造两个查询
        var query1 = _context.Recruit.Where(predicate1);
        var query2 = _context.Recruit.Where(predicate2);

        // 在数据库层合并查询
        var unionQuery = query1.Union(query2);

        // 在合并后的查询上应用投影
        var sql = ProjectRecruitInfo(unionQuery);

        // 获取总数
        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(m => m.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .ToList();

        // RecruitLabel = s.Recruit_Label.Select(s => new RecruitLabelClass { Id = s.Dic_Recruit_Label.Id, Name = s.Dic_Recruit_Label.LabelName }).ToList(),

        var ids = result.Rows.Select(s => s.ReceiverId).ToList();
        var invitationMap = _context.User_Hr.Where(x => ids.Contains(x.UserId)).ToDictionary(x => x.UserId);
        var region = _commonDicService.GetRegion();
        // 计算显示佣金 判断当前登录用户是否是集团内部人员，获取当前人员的分成比例
        var settings = _context.Sys_Settings.FirstOrDefault();
        var isNoah = _context.User_Hr.Where(s => s.UserId == _user.Id).Select(s => s.Enterprise.Specific).FirstOrDefault()?.Contains(EntSpecific.Noah) == true;
        BountyConfig? bountyConfig;

        // 获取分页结果中的 RecruitId 列表
        var recruitIds = result.Rows.Select(r => r.Id).ToList();

        // 查询 RecruitLabel 数据
        var recruitLabels = _context.Recruit_Label
            .Where(rl => recruitIds.Contains(rl.RecruitId))
            .Select(rl => new
            {
                RecruitId = rl.RecruitId,
                Label = new RecruitLabelClass
                {
                    Id = rl.Dic_Recruit_Label.Id,
                    Name = rl.Dic_Recruit_Label.LabelName
                }
            }).ToList();

        // 将 RecruitLabel 数据分组
        var recruitLabelMap = recruitLabels
            .GroupBy(rl => rl.RecruitId)
            .ToDictionary(g => g.Key, g => g.Select(x => x.Label).ToList());

        foreach (var item in result.Rows)
        {
            if (recruitLabelMap.TryGetValue(item.Id, out var labels))
                item.RecruitLabel = labels;
            else
                item.RecruitLabel = new List<RecruitLabelClass>();

            //todo 由于邀面人员不确定，无法写入问题，临时计算邀面佣金，如果岗位金额变动后也会计算，后期需要优化
            if (isNoah)
            {
                if ((bool)item.IsSalesCommission!)
                    bountyConfig = settings.NoahFull;
                else
                    bountyConfig = settings.NoahNoSale;
            }
            else
            {
                if ((bool)item.IsSalesCommission!)
                    bountyConfig = settings.PlatformFull;
                else
                    bountyConfig = settings.PlatformNoSale;
            }
            if (item.FollowerBounty == 0)
            {
                item.FollowerBounty = item.PostMoney * bountyConfig.Follower.ToFixed(2);
            }
            if (item.TeamHrId != _user.Id || item.IsClues == ProjectTeamIsClues.否)
            {
                item.AcceptOrder = null;
            }
            item.Location = region.FirstOrDefault(o => o.Id == item.RegionId)?.Name;
            item.Invitation = item.FollowerId != null && invitationMap.TryGetValue(item.FollowerId, out var invitation) ? invitation?.NickName ?? "" : "";
            item.ActiveName = item.Active.GetDescription();
            item.SexName = item.Sex?.GetDescription();
            item.Age = Tools.GetAgeByBirthdate(item.Birthday);
            item.OccupationName = item.Occupation?.GetDescription();
            item.EducationName = item.Education?.GetDescription();
            item.SourceName = item.Source?.GetDescription();
            item.StatusName = item.Status?.GetDescription();

            if (!string.IsNullOrWhiteSpace(item.RegionId))
                item.City = _commonDicService.GetCityById(item.RegionId);

            item.EduExp = $"{item.EduExpSchool}-{item.EduExpMajor} {item.GraduationDate?.ToString("yyyy-MM")}";
            var workTimeStr = string.Empty;
            if (item.WorkExpClass?.BeginDate != null)
                workTimeStr = $"{item.WorkExpClass?.BeginDate?.ToString("yyyy-MM")}~{item.WorkExpClass?.EndDate?.ToString("yyyy-MM")}";
            item.WorkExp = $"{item.WorkExpClass?.Company}-{item.WorkExpClass?.Post} {workTimeStr}";

            if (item.Status == RecruitStatus.FileAway)
                item.FileAwayName = item.FileAway?.GetDescription();

            // 倒计时处理
            if (item.PaymentNode != null)
            {
                switch (item.PaymentNode)
                {
                    case ProjectPaymentNode.入职过保:
                        if (item.BountyStatus == BountyStatus.交付中 && item.IfStockOut == 1)
                        {
                            item.EndCountTime = item.InductionTime!.Value.AddDays(item.PaymentDays!.Value);
                        }
                        break;
                    case ProjectPaymentNode.简历交付:
                        if (item.BountyStatus == BountyStatus.交付中 && item.IfStockOut == 1)
                        {
                            var interviewerScreen = _context.Recruit_Interviewer_Screen.Where(w => w.RecruitId == item.Id)
                            .OrderBy(o => o.CreatedTime).Select(x => new { x.CreatedTime }).FirstOrDefault();
                            if (interviewerScreen?.CreatedTime != null)
                                item.EndCountTime = interviewerScreen.CreatedTime.AddDays(7);
                            // item.EndCountTime = item.CreatedTime!.Value.AddDays(7);
                        }
                        break;
                    case ProjectPaymentNode.到面交付:
                        if (item.BountyStatus == BountyStatus.交付中)
                        {
                            // todo：耗时操作
                            var recruit = _context.Recruit_Interview.Where(w => w.RecruitId == item.Id && w.Outcome == RecruitInterviewOutcome.Waiting).FirstOrDefault();
                            if (recruit != null)
                                item.EndCountTime = recruit.InterviewTime.AddDays(7);
                        }
                        break;
                }
            }

            if (item.Status == RecruitStatus.InterviewerScreening)
            {
                item.InterviewScreenStatusName = item.InterviewScreenStatus?.GetDescription();
            }

            if (item.Status == RecruitStatus.Interview)
            {
                item.InterviewerFeedbackName = item.InterviewerFeedback?.GetDescription();
                item.CandidateFeedbackName = item.CandidateFeedback?.GetDescription();
                item.InterviewProcessName = item.InterviewProcess?.GetDescription();
            }
            if (item.Status == RecruitStatus.FileAway)
            {
                item.FileAwayName = item.FileAway?.GetDescription();
            }
        }
    }

    private IQueryable<Model.Hr.Dashboard.RecruitInfo> ProjectRecruitInfo(IQueryable<Recruit> query)
    {
        return query.Select(s => new Model.Hr.Dashboard.RecruitInfo
        {
            Id = s.RecruitId,
            StatusTime = s.Status == RecruitStatus.InterviewerScreening
            ? (
                s.Post_Delivery.Post_Team.Post.PaymentNode == ProjectPaymentNode.简历交付
                ? s.CreatedTime
                :
                    (
                        s.Recruit_Interviewer_Screen.Any(a => a.Status == RecruitInterviewerScreenStatus.NoFedBack)
                        ? s.StatusTime
                        : null
                    )
              )
            : null,
            CreatedTime = s.CreatedTime,
            ProjectId = s.Post_Delivery.Post.ProjectId,
            Name = s.User_Seeker.NickName,
            Avatar = s.User_Seeker.Avatar,
            IsRealName = !string.IsNullOrEmpty(s.User_Seeker.User.IdentityCard),
            Mobile = s.User_Seeker.User.Mobile,
            Sex = s.User_Seeker.User_Resume.Sex,
            Birthday = s.User_Seeker.User_Resume.Birthday,
            Occupation = s.User_Seeker.User_Resume.Occupation,
            Education = s.User_Seeker.User_Resume.Education,
            RegionId = s.User_Seeker.RegionId,
            HrAppletQrCode = s.User_Seeker.HrAppletQrCode,
            Source = s.Post_Delivery.Post_Team.Project_Team.Source,
            TeamHrId = s.Post_Delivery.Post_Team.Project_Team.HrId,
            Adviser = s.Post_Delivery.Post_Team.Project_Team.User_Hr.NickName,
            LoginTime = s.User_Seeker.User_Extend.LoginTime,
            Status = s.Status,
            Active = Tools.GetOnlineStatus(s.User_Seeker.User_Extend.LoginTime),
            EduExpSchool = s.User_Seeker.User_Resume.School,
            EduExpMajor = s.User_Seeker.User_Resume.Major,
            EduExpEducation = s.User_Seeker.User_Resume.Education,
            GraduationDate = s.User_Seeker.User_Resume.GraduationDate,
            HopeIndustry = s.Post_Delivery.Post.Name,
            ProjectName = s.Post_Delivery.Post.Project.Agent_Ent.Name,
            IsManager = s.HrId == _user.Id,
            WorkExpClass = s.User_Seeker.User_Resume.User_Work.OrderByDescending(s => s.BeginDate).Select(s => new Model.Hr.Recruit.WorkExpClass
            {
                BeginDate = s.BeginDate,
                Company = s.Company,
                EndDate = s.EndDate,
                Post = s.Post,
            }).FirstOrDefault(),
            InvalidDisplay = s.HrId == _user.Id && s.Post_Bounty.Status == BountyStatus.交付中,
            PaymentNode = s.Post_Bounty.PaymentNode,
            RewardType = s.Post_Bounty.RewardType,
            PaymentCycleName = s.Post_Bounty.PaymentCycle.GetDescription(),
            BountyStatus = s.Post_Bounty.Status,
            IfStockOut = s.Post_Bounty.IfStockOut,
            InductionTime = s.InductionTime,
            PaymentDays = s.Post_Bounty.PaymentDays,
            InterviewScreenStatus = s.Recruit_Interviewer_Screen.OrderByDescending(s => s.CreatedTime).Select(s => s.Status).FirstOrDefault(),
            InterviewScreenLaunchTime = s.Recruit_Interviewer_Screen.OrderByDescending(s => s.CreatedTime).Select(s => s.CreatedTime).FirstOrDefault(),
            InterviewLaunchTime = s.Recruit_Interview.OrderByDescending(s => s.CreatedTime).Select(s => s.CreatedTime).FirstOrDefault(),
            InterviewerName = s.Recruit_Interview.OrderByDescending(s => s.CreatedTime).Select(s => s.Project_Interviewer.Name).FirstOrDefault(),
            InterviewerFeedback = s.Recruit_Interview.OrderByDescending(s => s.CreatedTime).Select(s => s.Outcome).FirstOrDefault(),
            CandidateFeedback = s.Recruit_Interview.OrderByDescending(s => s.CreatedTime).Select(s => s.UserFeedBack).FirstOrDefault(),
            InterviewProcess = s.Recruit_Interview.OrderByDescending(s => s.CreatedTime).Select(s => s.Process).FirstOrDefault(),
            // RecruitLabel = s.Recruit_Label.Select(s => new RecruitLabelClass { Id = s.Dic_Recruit_Label.Id, Name = s.Dic_Recruit_Label.LabelName }).ToList(),
            OfferLaunchTime = s.Recruit_Offer.OrderByDescending(s => s.CreatedTime).Select(s => s.CreatedTime).FirstOrDefault(),
            IsNotice = s.Recruit_Offer.OrderByDescending(s => s.CreatedTime).Select(s => s.IsMobileNotice).FirstOrDefault(),
            FileAway = s.FileAway,
            FileTime = s.StatusTime,
            ReceiverId = s.ReceiverId,
            AcceptOrder = s.AcceptOrder,
            FollowerId = s.FollowerId,
            IsClues = s.IsClues,
            //ClueBounty = s.Post_Bounty.ClueBounty,
            //FollowerBounty = s.Post_Bounty.FollowerBounty,
            ClueBounty = (decimal?)(s.Post_Bounty.Money * s.Post_Bounty.ClueRate) ?? 0,
            FollowerBounty = (decimal?)(s.Post_Bounty.Money * s.Post_Bounty.FollowerRate) ?? 0,
            AgentEntName = s.Post_Delivery.Post.Agent_Ent.Name,
            //SalesBounty = (decimal?)s.Post_Bounty.SalesBounty ?? 0,
            //ManagerBounty = (decimal?)s.Post_Bounty.ManagerBounty ?? 0,
            //DeliveriesBounty = ((decimal?)s.Post_Bounty.ClueBounty ?? 0) + ((decimal?)s.Post_Bounty.FollowerBounty ?? 0),
            SalesBounty = (decimal?)(s.Post_Bounty.Money * s.Post_Bounty.SalesRate) ?? 0,
            ManagerBounty = (decimal?)(s.Post_Bounty.Money * s.Post_Bounty.ManagerRate) ?? 0,
            DeliveriesBounty = (decimal?)(s.Post_Bounty.Money * (s.Post_Bounty.ClueRate + s.Post_Bounty.FollowerRate)) ?? 0,
            IsSalesCommission = s.Post_Delivery.Post.IsSalesCommission,
            PostMoney = s.Post_Delivery.Post.Money ?? 0,
        });
    }
}