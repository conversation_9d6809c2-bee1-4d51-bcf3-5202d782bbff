﻿using Flurl.Http;
using Newtonsoft.Json;

namespace Infrastructure.Common.Contract
{
    public class ESignAccessToken
    {
        public static async Task<string> GetAccessToken(string appKey, string appSecret)
        {
            var token = MyRedis.Client.Get("Staffing:ESignToken");
            if (!string.IsNullOrWhiteSpace(token))
            {
                return token;
            }
            //默认缓存2小时
            token = await GetToken("https://openapi.esign.cn/v1/oauth2/access_token?appId=" + appKey + "&secret=" + appSecret + "&grantType=client_credentials");
            if (!string.IsNullOrEmpty(token))
            {
                MyRedis.Client.Set("Staffing:ESignToken", token, 60);
            }
            return token;
        }

        private static async Task<string> GetToken(string apiUrl)
        {
            var token = string.Empty;
            var result = await apiUrl.GetStringAsync();

            if (!string.IsNullOrEmpty(result))
            {
                var obj = JsonConvert.DeserializeObject<Response>(result);
                if ("0".Equals(obj?.code.ToString()))
                {
                    token = obj?.data?.token ?? string.Empty;
                }
                else
                {
                    var code = obj?.code.ToString() ?? "";
                    var msg = obj?.message.ToString() ?? "";
                    return msg;
                }
            }
            return token;
        }

        public class Response
        {
            public int code { get; set; }
            public data? data { get; set; }
            public string message { get; set; } = string.Empty;
        }
        public class data
        {
            public string refreshToken { get; set; } = string.Empty;
            public string expiresIn { get; set; } = string.Empty;
            public string token { get; set; } = string.Empty;
        }
    }
}
