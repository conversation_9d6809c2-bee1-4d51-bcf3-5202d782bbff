﻿using Config.CommonModel.Business;
using Config.CommonModel;
using DocumentFormat.OpenXml.Spreadsheet;
using Infrastructure.QYWechat;
using Microsoft.EntityFrameworkCore.Internal;
using Staffing.Entity.Staffing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Staffing.Model.Callback;

namespace Staffing.Core.Interfaces.Common
{
    public interface IQYWechatService
    {
        /// <summary>
        /// 绑定企业微信
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<EmptyResponse> Binding(BindingQYWechatRequest model);

        /// <summary>
        /// 移除企业微信绑定
        /// </summary>
        /// <returns></returns>
        EmptyResponse RelieveBinding();
        /// <summary>
        /// 根据手机号获取企微二维码 
        /// </summary>
        /// <param name="Mobile"></param>
        /// <returns></returns>
        string GetEntQRCode(string Mobile);
        /// <summary>
        /// 验证企业微信是否绑定
        /// </summary>
        /// <returns></returns>
        GetQWBindingStatsuRsponse CheckIsBinding();

        /// <summary>
        /// 获取我的企微群列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<QWLoadGroupByEmpResponse> GetMyQWGroup(QuerySearch model);

        /// <summary>
        /// 加载群信息
        /// </summary>
        /// <param name="GroupChatId">企微群ID</param>
        /// <param name="IsPushToBackground">true=记录需要加载的群ID，在后台加载群信息；false=直接请求加载群信息并返回加载结果</param>
        /// <returns></returns>
        Task<(bool Result, string ErrMsg)> PushToLoadGroupInfo(string GroupChatId, bool IsPushToBackground = true, List<string>? MemChangeList = null, QYWechatCallbackGropChatUpdateDetail? CallbackUpdateDetail = null);

        /// <summary>
        /// 解散群
        /// </summary>
        /// <param name="ChatId">群ID</param>
        /// <returns></returns>
        (bool Result, string ErrMsg) DismissGroupChat(string ChatId);

        /// <summary>
        /// 客户群变更事件
        /// </summary>
        /// <param name="ChatId">群ID</param>
        /// <returns></returns>
        Task<(bool Result, string ErrMsg)> UpdateGroupChat(string ChatId, QYWechatCallbackGropChatUpdateDetail UpdateDetail, List<string>? MemChangeList);

        /// <summary>
        /// 导入指定群主的群信息
        /// </summary>
        /// <param name="ChatId">群ID</param>
        /// <param name="IsPushToBackground">true=记录需要加载的群ID，在后台加载群信息；false=直接请求加载群信息并返回加载结果</param>
        /// <returns></returns>
        Task<(bool Result, string ErrMsg)> LoadGroupChatByOwner(string OwnerId, bool IsPushToBackground = true);
        /// <summary>
        /// 导入所有企微的群信息
        /// </summary>
        /// <returns></returns>
        Task<(bool Result, string ErrMsg)> LoadGroupChatByAll();

        /// <summary>
        /// 导入所有企微的员工信息
        /// </summary>
        /// <returns></returns>
        Task<(bool Result, string? ErrMsg)> LoadEmploeeByAll();
    }
}
