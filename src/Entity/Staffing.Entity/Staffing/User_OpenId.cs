﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing
{
    /// <summary>
    /// 广告管理
    /// </summary>
    [Table("user_openid")]
    public class User_OpenId
    {
        [Key]
        public string Id { get; set; } = EntityTools.SnowflakeId();

        /// <summary>
        /// UserId
        /// </summary>
        public string UserId { get; set; } = default!;

        /// <summary>
        /// 类型
        /// </summary>
        public ClientType Type { get; set; }

        /// <summary>
        /// AppId
        /// </summary>
        public string AppId { get; set; } = default!;

        /// <summary>
        /// OpenId
        /// </summary>
        public string OpenId { get; set; } = default!;

        /// <summary>
        /// UnionId
        /// </summary>
        public string UnionId { get; set; } = default!;

        public DateTime CreatedTime { get; set; } = DateTime.Now;

        public User User { get; set; } = default!;
        public User_Seeker User_Seeker { get; set; } = default!;
    }
}
