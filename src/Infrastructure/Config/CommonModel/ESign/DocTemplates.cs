namespace Config.CommonModel.ESign;

/// <summary>
/// 获取模板文件上传地址
/// </summary>
public class CreateDocTemplates
{
    /// <summary>
    /// 模板文件md5值，先计算文件md5值，在对该md5值进行base64编码
    /// </summary>
    public string? contentMd5 { get; set; }

    /// <summary>
    /// 目标文件的MIME类型，支持：
    /// （1）application/octet-stream
    /// （2）application/pdf
    /// 注意，后面文件流上传的Content-Type参数要和这里一致，不然就会有403的报错
    /// </summary>
    public string? contentType { get; set; }

    /// <summary>
    /// 文件名称，必须带扩展名如:.pdf,.doc,.docx
    /// 注意：
    /// （1）该字段的文件后缀名称和真实的文件后缀需要一致。比如上传的文件类型是word文件，那该参数需要传“xxx.docx”，不能是“xxx.pdf”
    /// （2）该字段建议直接传入pdf文件，其他类型文件建议本地自行转换成pdf，避免通过接口格式转换引起的格式错误、耗时久等问题。
    /// （3）文件名称不支持以下9个字符：/ \ : * " > | ？
    /// </summary>
    public string? fileName { get; set; }

    /// <summary>
    /// 是否需要转成pdf，默认值false。
    /// 注意：
    /// （1）本身就是PDF文件，该参数必须为false，可以直接不传。
    /// （2）如果模板文件为.doc/.docx等格式需要传true。
    /// </summary>
    public bool? convert2Pdf { get; set; }
}

public class CreateDocTemplatesResponse
{
    /// <summary>
    /// 模板ID
    /// </summary>
    public string? templateId { get; set; }

    /// <summary>
    /// 文件上传地址，链接有效期60分钟。
    /// </summary>
    public string? uploadUrl { get; set; }
}

/// <summary>
/// 查询模板文件上传状态
/// </summary>
public class GetDocTemplatesBaseInfo
{
    /// <summary>
    /// 模板id，该参数需放在请求地址里面，可以参考【请求示例】
    /// </summary>
    public string? templateId { get; set; }
}

public class GetDocTemplatesBaseInfoResponse
{
    /// <summary>
    /// 模板id
    /// </summary>
    public string? templateId { get; set; }

    /// <summary>
    /// 模板名称
    /// </summary>
    public string? templateName { get; set; }

    /// <summary>
    /// 模板文件上传状态。
    /// 0-未上传
    /// 1-未转换成PDF
    /// 2-已上传成功
    /// 3-已转换成PDF
    /// </summary>
    public string? templateFileStatus { get; set; }

    /// <summary>
    /// 模板文件大小
    /// </summary>
    public long fileSize { get; set; }

    /// <summary>
    /// 创建时间，Unix时间戳（毫秒级）
    /// </summary>
    public long createTime { get; set; }

    /// <summary>
    /// 更新时间，Unix时间戳（毫秒级）
    /// </summary>
    public long updateTime { get; set; }
}

/// <summary>
/// 查询模板文件详情
/// </summary>
public class GetDocTemplate
{
    /// <summary>
    /// 模板id
    /// </summary>
    public string? templateId { get; set; }
}

public class GetDocTemplateResponse
{
    /// <summary>
    /// 模板id
    /// </summary>
    public string? templateId { get; set; }

    /// <summary>
    /// 模板名称
    /// </summary>
    public string? templateName { get; set; }

    /// <summary>
    /// 模板文件下载链接，有效期60分钟。
    /// </summary>
    public string? downloadUrl { get; set; }

    /// <summary>
    /// 模板文件大小
    /// </summary>
    public long fileSize { get; set; }

    /// <summary>
    /// 创建时间，Unix时间戳（毫秒级）
    /// </summary>
    public long createTime { get; set; }

    /// <summary>
    /// 更新时间，Unix时间戳（毫秒级）
    /// </summary>
    public long updateTime { get; set; }

    /// <summary>
    /// 文件模板中的填写控件列表
    /// </summary>
    public DocStructComponents[] structComponents = default!;
}

public class DocStructComponents
{
    /// <summary>
    /// 填写控件id，使用时可用id填充，为空时表示添加，不为空时表示修改
    /// </summary>
    public string? id { get; set; }

    /// <summary>
    /// 模板下填写控件唯一标识，使用模板时也可用根据key值填充
    /// </summary>
    public string? key { get; set; }

    /// <summary>
    /// 填写控件类型，
    /// 1-单行文本，2-数字，3-日期，6-签约区，8-多行文本，11-图片
    /// </summary>
    public int type { get; set; }

    /// <summary>
    /// 填写控件上下文信息，包含了名称，填充格式，样式以及坐标
    /// </summary>
    public DocStructComponentsContext? context { get; set; }

    /// <summary>
    /// 是否允许编辑，用于管控页面是否能对该控件进行修改；线下制作的模板（带表单域）此值为false,其余为true
    /// </summary>
    public bool allowEdit { get; set; }
}

public class DocStructComponentsContext
{
    /// <summary>
    /// 填写控件显示名称
    /// </summary>
    public string? label { get; set; }

    /// <summary>
    /// 是否必填，默认true
    /// </summary>
    public bool required { get; set; }

    /// <summary>
    /// 填写控件type=2,type=3时填充格式校验规则：
    /// 数字格式如：#,#00.0 #日期格式如： yyyy-MM-dd
    /// </summary>
    public string? limit { get; set; }

    /// <summary>
    /// 控件结构中的子元素，用于选择控件，如：单选，多选，下拉选项
    /// </summary>
    public string? options { get; set; }

    /// <summary>
    /// 填写控件样式
    /// </summary>
    public StructComponentsContextStyle? style { get; set; }

    /// <summary>
    /// 填写控件坐标
    /// </summary>
    public StructComponentsContextPos? pos { get; set; }
}

public class StructComponentsContextStyle
{
    /// <summary>
    /// 填写控件宽度
    /// </summary>
    public float? width { get; set; }

    /// <summary>
    /// 填写控件高度
    /// </summary>
    public float? height { get; set; }

    /// <summary>
    /// 填充字体,默认1，1-宋体，2-新宋体,4-黑体，5-楷体
    /// </summary>
    public int? font { get; set; }

    /// <summary>
    /// 填充字体大小,默认12
    /// </summary>
    public float? fontSize { get; set; }

    /// <summary>
    /// 字体颜色，默认#000000黑色
    /// </summary>
    public string? textColor { get; set; }
}

public class StructComponentsContextPos
{
    /// <summary>
    /// 页码
    /// </summary>
    public int? page { get; set; }

    /// <summary>
    /// x轴坐标，左下角为原点
    /// </summary>
    public float? x { get; set; }

    /// <summary>
    /// y轴坐标，左下角为原点
    /// </summary>
    public float? y { get; set; }
}

/// <summary>
/// 填充内容生成PDF
/// </summary>
public class CreateFileByTemplate
{
    /// <summary>
    /// 文件名称
    /// 注：文件名称不支持以下9个字符：/ \ : * " > | ？
    /// </summary>
    public string? name { get; set; }

    /// <summary>
    /// 模板编号
    /// （1）正式环境可通过e签宝网站->企业模板下创建和查询
    /// （2）通过上传方式方式创建模板接口获取模板id和上传链接，文件流上传文件成功之后，模板id可用于这里
    /// </summary>
    public string? templateId { get; set; }

    /// <summary>
    /// 输入项填充内容，key:value 传入；可使用输入项组件id+填充内容，也可使用输入项组件key+填充内容方式填充
    /// 注意：E签宝官网获取的模板id，在通过模板创建文件的时候只支持输入项组件id+填充内容
    /// </summary>
    public object? simpleFormFields { get; set; }
}

public class CreateFileByTemplateResponse
{
    /// <summary>
    /// 文件下载地址，有效期一小时
    /// </summary>
    public string? downloadUrl { get; set; }

    /// <summary>
    /// 文件id
    /// </summary>
    public string? fileId { get; set; }

    /// <summary>
    /// 文件名称
    /// </summary>
    public string? fileName { get; set; }
}

/// <summary>
/// 查询PDF文件详情
/// </summary>
public class GetFile
{
    /// <summary>
    /// 文件id
    /// </summary>
    public string? fileId { get; set; }
}

public class GetFileResponse
{
    /// <summary>
    /// 文件id
    /// </summary>
    public string? fileId { get; set; }

    /// <summary>
    /// 文件名称
    /// </summary>
    public string? name { get; set; }

    /// <summary>
    /// 文件大小（预留字段，暂时不会返回任何值，开发者可忽略）
    /// </summary>
    public int size { get; set; }

    /// <summary>
    /// 文件上传状态
    /// 0-文件未上传
    /// 1-文件上传中
    /// 2-文件上传已完成
    /// 3-文件上传失败
    /// 4-文件等待转pdf
    /// 5-文件已转换pdf
    /// 6-加水印中
    /// 7-加水印完毕
    /// 8-文件转换中
    /// 9-文件转换失败
    /// </summary>
    public string? status { get; set; }

    /// <summary>
    /// PDF文件下载链接，有效期60分钟
    /// </summary>
    public string? downloadUrl { get; set; }

    /// <summary>
    /// pdf文件总页数,仅当文件类型为pdf时有值
    /// </summary>
    public int pdfTotalPages { get; set; }
}

/// <summary>
/// 获取模板设置页面地址
/// </summary>
public class GetTemSettingUrl
{
    /// <summary>
    /// 模板id
    /// </summary>
    public string? templateId { get; set; }
}

public class GetTemSettingUrlResponse
{
    /// <summary>
    /// 模板设置地址（链接有效期24小时）
    /// </summary>
    public string? settingUrl { get; set; }
}