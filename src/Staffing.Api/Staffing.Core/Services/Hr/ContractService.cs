﻿using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Core.Interfaces.Hr;
using Config;
using Staffing.Model.Hr.Contract;
using Staffing.Entity.Staffing;
using Infrastructure.Exceptions;
using Config.CommonModel.ESign;
using EntityFrameworkCore.AutoHistory.Extensions;

namespace Staffing.Core.Services.Hr;

[Service(ServiceLifetime.Transient)]
public class ContractService : IContractService
{
    private readonly StaffingContext _context;
    private readonly RequestContext _user;
    private readonly ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly ESignHelper _eSignHelper;
    private readonly LogManager _log;
    public ContractService(StaffingContext context, RequestContext user,
        IOptionsSnapshot<ConfigManager> config, IHostEnvironment hostingEnvironment,
        LogManager log, ESignHelper eSignHelper)
    {
        _user = user;
        _context = context;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
        _eSignHelper = eSignHelper;
    }

    public EmptyResponse DeleteEntOrg(DeleteEntOrg model)
    {
        var orgEnt = _context.Contract_Org_Ent
        .FirstOrDefault(x => x.EntId == _user.EntId && x.Id == model.Id);

        if (orgEnt != null)
        {
            _context.Remove(orgEnt);
            _context.EnsureAutoHistory();
            _context.SaveChanges();
        }

        return new EmptyResponse();
    }

    public async Task<UpdateEntOrgResponse> UpdateEntOrg(UpdateEntOrg model)
    {
        var result = new UpdateEntOrgResponse();

        if (string.IsNullOrWhiteSpace(model.Name))
            throw new BadRequestException("机构名称不能为空");

        if (string.IsNullOrWhiteSpace(_user.EntId))
            throw new BadRequestException("当前用户尚未加入企业");

        // Contract_Org? contractOrg = null;
        // if (!string.IsNullOrWhiteSpace(model.Id))
        // {
        //     contractOrg = _context.Contract_Org.FirstOrDefault(x => x.Id == model.Id && x.EntId == _user.EntId);

        //     if (contractOrg == null)
        //         throw new NotFoundException("内容不存在");

        //     var updateOrgAccountRequest = new UpdateOrgAccount
        //     {
        //         orgId = contractOrg.EOrgId,
        //         name = model.Name
        //     };

        //     var resp = await _eSignHelper.UpdateOrgAccount(updateOrgAccountRequest);
        // }
        // else
        // {

        // }

        if (string.IsNullOrWhiteSpace(model.IdNumber))
            throw new BadRequestException("机构证件号不能为空");

        var contractOrg = _context.Contract_Org.FirstOrDefault(x => x.IdNumber == model.IdNumber);

        if (_context.Contract_Org_Ent.Any(x => x.EntId == _user.EntId && x.Contract_Org.IdNumber == model.IdNumber))
            throw new BadRequestException("重复添加：该机构证件号已存在");

        if (contractOrg == null)
        {
            //E签宝账号是否存在
            GetOrgAccountResponse? eAccount = null;
            var eOrgId = string.Empty;
            try
            {
                eAccount = await _eSignHelper.GetOrgAccountByThirdId(new GetOrgAccount { thirdPartyUserId = model.IdNumber });
                eOrgId = eAccount.orgId;
            }
            catch (Exception e)
            {
                //查询出错继续执行
                _log.Error("查询E签宝机构账号出错", Tools.GetErrMsg(e), model);
            }

            //如果E签宝账号不存在，创建
            if (eAccount == null)
            {
                var createOrgAccountRequest = new CreateOrgAccount
                {
                    thirdPartyUserId = model.IdNumber,
                    idNumber = model.IdNumber,
                    name = model.Name
                };

                var resp = await _eSignHelper.CreateOrgAccount(createOrgAccountRequest);
                eOrgId = resp.orgId;
            }

            contractOrg = new Contract_Org
            {
                IdNumber = model.IdNumber,
                EOrgId = eOrgId!,
                CreatedBy = _user.Id,
                Name = model.Name,
                UpdatedBy = _user.Id,
                UpdatedTime = DateTime.Now
            };
            _context.Add(contractOrg);
        }

        var orgEnt = new Contract_Org_Ent
        {
            CreatedBy = _user.Id,
            EntId = _user.EntId,
            OrgId = contractOrg.OrgId
        };
        _context.Add(orgEnt);

        _context.SaveChanges();

        result.EOrgId = contractOrg.EOrgId;

        return result;
    }

    public GetEntOrgResponse GetEntOrg(GetEntOrg model)
    {
        var result = new GetEntOrgResponse();

        var predicate = PredicateBuilder.New<Contract_Org_Ent>(x => x.EntId == _user.EntId);

        if (!string.IsNullOrWhiteSpace(model.Name))
            predicate = predicate.And(x => x.Contract_Org.Name.Contains(model.Name));

        var sql = _context.Contract_Org_Ent.Where(predicate);

        result.Total = sql.Count();

        result.Rows = sql
        .OrderByDescending(x => x.CreatedTime)
        .Skip((model.PageIndex - 1) * model.PageSize)
        .Take(model.PageSize)
        .Select(s => new EntOrgInfo
        {
            CreatedBy = s.CreatedUser.NickName,
            CreatedTime = s.CreatedTime,
            Id = s.Id,
            IdNumber = s.Contract_Org.IdNumber,
            OrgId = s.OrgId,
            OrgName = s.Contract_Org.Name
        }).ToList();

        return result;
    }
}