using Config.Enums;

namespace Staffing.Model.Seeker.Channel;

/// <summary>
/// 加入渠道商
/// </summary>
public class JoinChannel
{
    /// <summary>
    /// 昵称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 渠道类型
    /// </summary>
    public ChannelType? ChannelType { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }
}

/// <summary>
/// 检测渠道商关系
/// </summary>
public class CheckChannel
{
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? HrId { get; set; }
}

/// <summary>
/// 检测渠道商关系
/// </summary>
public class CheckChannelResponse
{
    /// <summary>
    /// 是否实名
    /// </summary>
    public bool Identity { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 渠道类型
    /// </summary>
    public ChannelType? ChannelType { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }
}