using CanalSharp.Protocol;
using Google.Protobuf.Collections;

namespace Config.Enums;

public class CanalData
{
    /// <summary>
    /// 表名
    /// </summary>
    public string? TableName { get; set; }

    /// <summary>
    /// 更改之前列
    /// </summary>
    public RepeatedField<Column>? BeforeColumns { get; set; }

    /// <summary>
    /// 更改之后列
    /// </summary>
    public RepeatedField<Column>? AfterColumns { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public CanalEventType? EventType { get; set; }
}

public enum CanalEventType
{
    Delate, Insert, Update
}