﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Config.CommonModel.Business
{
    /// <summary>
    /// 绑定企业微信
    /// </summary>
    public class BindingQYWechatRequest
    {
        /// <summary>
        /// 企业微信手机号
        /// </summary>
        public string BindingPhone { get; set; } = default!;
    }

    /// <summary>
    /// 获取企微绑定状态返回模型
    /// </summary>
    public class GetQWBindingStatsuRsponse
    {
        /// <summary>
        /// 绑定状态
        /// </summary>
        public bool BindingStatus { get; set; }

        /// <summary>
        /// 企微手机号
        /// </summary>
        public string? QWMobile { get; set; }

        /// <summary>
        /// 企微姓名
        /// </summary>
        public string? QWName { get; set; }

        /// <summary>
        /// 企微头像
        /// </summary>
        public string? QWAvatar { get; set; }
    }
}
