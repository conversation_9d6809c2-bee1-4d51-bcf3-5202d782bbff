using Config;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;

namespace Infrastructure.CommonService;

[Service(ServiceLifetime.Singleton)]
public class WxGroupService
{
    private IDbContextFactory<StaffingContext> _staffingContextFactory;
    private ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CacheHelper _cacheHelper;
    private readonly RequestContext _user;

    public WxGroupService(IDbContextFactory<StaffingContext> staffingContextFactory,
        IOptionsSnapshot<ConfigManager> config,
        IHostEnvironment hostingEnvironment, LogManager log,
        CacheHelper cacheHelper, 
        RequestContext user)
    {
        _staffingContextFactory = staffingContextFactory;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
        _cacheHelper = cacheHelper;
        _user = user;
    }

    // public EmptyResponse ReceiveWxGroupData(List<RequestModel> datas)
    // {
    //     using var _context = _staffingContextFactory.CreateDbContext();
    //     foreach (var data in datas)
    //     {
    //         var group = new Wx_Group
    //         {
    //             GroupType = data.GroupType,
    //             GroupName = data.GroupName,
    //             Describe = data.Describe,
    //             GroupNumber = data.GroupNumber,
    //             FindTime = data.FindTime,
    //             Time = data.Time,
    //             QRCode = data.QRCode,
    //             QrId = data.QrId
    //         };
    //         _context.Add(group);
    //     }
    //     _context.SaveChanges();
    //     return new EmptyResponse();
    // }

}