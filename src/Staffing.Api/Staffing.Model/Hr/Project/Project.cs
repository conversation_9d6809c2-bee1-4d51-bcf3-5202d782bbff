﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;
using Staffing.Entity.Staffing;

namespace Staffing.Model.Hr.Project;

public class UpdateProject
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 代招企业id
    /// </summary>
    public string AgentEntId { get; set; } = string.Empty;

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? Name { get; set; } = default!;

    // /// <summary>
    // /// 简历是否需免审
    // /// </summary>
    // public bool? IsResumeExempt { get; set; }

    /// <summary>
    /// 签约方式
    /// </summary>
    public ContractType? ContractType { get; set; }

    /// <summary>
    /// 签约方式
    /// </summary>
    public string? ContractTypeText { get; set; }

    /// <summary>
    /// 项目级别
    /// </summary>
    public ProjectLevel Level { get; set; } = ProjectLevel.一般项目;

    /// <summary>
    /// 项目类型
    /// </summary>
    public ProjectType Type { get; set; } = ProjectType.人才派遣;

    /// <summary>
    /// 项目行业
    /// </summary>
    public ProjectIndustry Industry { get; set; } = ProjectIndustry.其他;

    /// <summary>
    /// 工作周期类型
    /// 2023-11-01 郭思成：项目周期默认值改成 长期用工
    /// </summary>
    public ProjectWorkCycleType WorkCycleType { get; set; } = ProjectWorkCycleType.长期用工;

    /// <summary>
    /// 结算方式
    /// </summary>
    public ProjectClearingType ClearingType { get; set; } = ProjectClearingType.日结;

    /// <summary>
    /// 结算渠道
    /// </summary>
    public ProjectClearingChannel ClearingChannel { get; set; } = ProjectClearingChannel.诺快聘结算;

    /// <summary>
    /// 诺亚项目编码
    /// </summary>
    public string NuoId { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; } = default!;

    /// <summary>
    /// 地区Id
    /// </summary>
    public List<string> RegionIds { get; set; } = new List<string>();

    /// <summary>
    /// 项目截止日期
    /// </summary>
    public string? EndTime { get; set; }

    /// <summary>
    /// 项目剩余天数
    /// </summary>
    public int LeftDays { get; set; }

    /// <summary>
    /// 合作模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }

    /// <summary>
    /// 打款方式
    /// </summary>
    public ProjectPaymentType? PaymentType { get; set; }

    // /// <summary>
    // /// 过保付费时间
    // /// </summary>
    // public int? PaymentDays { get; set; }

    /// <summary>
    /// 是否开启员工服务
    /// </summary>
    public bool StaffService { get; set; }
}

public class NewUpdateProject
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 代招企业id
    /// </summary>
    public string? AgentEntId { get; set; }

    /// <summary>
    /// 项目调研表
    /// </summary>
    public ProjectSurveyContent? SurveyContent { get; set; }

    /// <summary>
    /// 名字
    /// </summary>
    [Required(ErrorMessage = "企业名称不能为空")]
    public required string Name { get; set; }

    /// <summary>
    /// 企业性质
    /// </summary>
    [Required(ErrorMessage = "企业性质不能为空")]
    public EnterpriseNature Nature { get; set; }


    /// <summary>
    /// 企业行业
    /// </summary>
    [Required(ErrorMessage = "企业行业不能为空")]
    public ProjectIndustry Industry { get; set; }
    /// <summary>
    /// 描述
    /// </summary>
    [Required]
    public string? Describe { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>

    public string RegionId { get; set; } = default!;

    /// <summary>
    /// 地址
    /// </summary>
    [Required(ErrorMessage = "企业地址不能为空")]
    public string Address { get; set; } = default!;
    /// <summary>
    /// 帐套code
    /// </summary>
    [Required(ErrorMessage = "帐套不能为空")]
    public string? BookCode { get; set; }
    /// <summary>
    /// 诺亚项目编码
    /// </summary>
    public string NuoId { get; set; } = string.Empty;
    /// <summary>
    /// （项目类型）用工形式
    /// </summary>
    [Required(ErrorMessage = "用工形式不能为空")]
    public ProjectType Type { get; set; }

    /// <summary>
    /// （合作）结算模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }

    /// <summary>
    /// 签约方式
    /// </summary>
    public ContractType ContractType { get; set; } = ContractType.与第三方劳务签订合同;

    /// <summary>
    /// 是否指定项目经理
    /// </summary>
    public AssignedPersonEnum IsAssignedPerson { get; set; }

    /// <summary>
    /// 接受人id（项目经理id）
    /// </summary>
    public string? ReceiverId { get; set; }
    /// <summary>
    /// 财务结算人员id
    /// </summary>
    public string? SettlementPersonId { get; set; }

    /// <summary>
    /// 面试轮次
    /// </summary>
    public int InterviewRound { get; set; }
    /// <summary>
    /// 是否支持视频面试
    /// </summary>
    public bool? IsVideoInterviewSupported { get; set; }
    /// <summary>
    /// 面试流程阶段
    /// </summary>
    public string? InterviewProcess { get; set; }
    /// <summary>
    /// 职位人数
    /// </summary>
    public int PositionCount { get; set; }
    /// <summary>
    /// 招聘人数
    /// </summary>
    public int RecruitmentCount { get; set; }
    /// <summary>
    /// 合同金额
    /// </summary>
    public decimal? Price { get; set; }
    /// <summary>
    /// 其他
    /// </summary>
    public string? Remark { get; set; }
    /// <summary>
    /// 必备条件字段
    /// </summary>
    public string? RequiredCondition { get; set; }
    /// <summary>
    /// 项目手册
    /// </summary>
    public string? ProjectManualUrl { get; set; }
    /// <summary>
    /// 合同签约方式
    /// </summary>
    public SignContractType SignContractType { get; set; }

    /// <summary>
    /// 项目经理修改上线状态
    /// </summary>
    public ProjectStatus Status { get; set; }

    /// <summary>
    /// 项目开始日期
    /// </summary>
    public DateTime StartTime { get; set; }
    /// <summary>
    /// 项目截止日期
    /// </summary>
    [Required(ErrorMessage = "项目截止日期")]
    public DateTime EndTime { get; set; }




}

public class UpdateProjectResponse
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }
}

public class GetBountyConfigResponse
{
    /// <summary>
    /// 平台-全部佣金
    /// </summary>
    public BountyConfig? PlatformFull { get; set; }

    /// <summary>
    /// 平台-不带销售佣金
    /// </summary>
    public BountyConfig? PlatformNoSale { get; set; }

    /// <summary>
    /// 诺亚-全部佣金
    /// </summary>
    public BountyConfig? NoahFull { get; set; }

    /// <summary>
    /// 诺亚-不带销售佣金
    /// </summary>
    public BountyConfig? NoahNoSale { get; set; }
}

public class GetProjectDetail : UpdateProject
{
    
    
    /// <summary>
    /// 
    /// </summary>
    public bool? HasDefaultContract { get; set; }
    /// <summary>
    /// hrid
    /// </summary>
    public string? HrId { get; set; }

    public int ProjectAutoId { get; set; }

    /// <summary>
    /// 协同项目Id
    /// </summary>
    public string? TeamProjectId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ProjectStatus? Status { get; set; }

    /// <summary>
    /// 项目地区
    /// </summary>
    public List<CityModel>? Region { get; set; }

    /// <summary>
    /// hr名称（项目经理）
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// hr头像
    /// </summary>
    public string? HrAvatar { get; set; }

    /// <summary>
    /// hr企业名称
    /// </summary>
    public string? HrEntName { get; set; }

    /// <summary>
    /// hr企业地址
    /// </summary>
    public string? HrEntAddress { get; set; }

    /// <summary>
    /// hr手机
    /// </summary>
    public string? HrMobile { get; set; }

    /// <summary>
    /// hr邮箱
    /// </summary>
    public string? HrEmail { get; set; }

    /// <summary>
    /// hr微信
    /// </summary>
    public string? HrWeChat { get; set; }

    /// <summary>
    /// hr职位
    /// </summary>
    public string? HrPost { get; set; }
    /// <summary>
    /// 项目经理企业微信二维码
    /// </summary>
    public string? PmEntWeChatQrCode { get; set; }

    /// <summary>
    /// 项目经理id
    /// </summary>
    public string? ProjectManagerId { get; set; }
    /// <summary>
    /// 项目经理名称
    /// </summary>
    public string? ProjectManagerName { get; set; }
    /// <summary>
    /// 帐套code
    /// </summary>
    public string? BookCode { get; set; }
    /// <summary>
    /// 是否指定项目经理
    /// </summary>
    public AssignedPersonEnum IsAssignedPerson { get; set; }

    /// <summary>
    /// 财务结算人员id
    /// </summary>
    public string? SettlementPersonId { get; set; }
    /// <summary>
    /// 面试轮次
    /// </summary>
    public int InterviewRound { get; set; }
    /// <summary>
    /// 是否支持视频面试
    /// </summary>
    public bool IsVideoInterviewSupported { get; set; }
    /// <summary>
    /// 面试流程阶段
    /// </summary>
    public string? InterviewProcess { get; set; }
    /// <summary>
    /// 职位人数
    /// </summary>
    public int PositionCount { get; set; }
    /// <summary>
    /// 招聘人数
    /// </summary>
    public int RecruitmentCount { get; set; }
    /// <summary>
    /// 合同金额
    /// </summary>
    public decimal? Price { get; set; }
    /// <summary>
    /// 其他
    /// </summary>
    public string? Remark { get; set; }
    /// <summary>
    /// 项目经理头像
    /// </summary>
    public string? PmAvatar { get; set; }
    /// <summary>
    /// 必备条件字段
    /// </summary>
    public string? RequiredCondition { get; set; }
    /// <summary>
    /// 项目经理企业名称
    /// </summary>
    public string? PmEntName { get; set; }

    /// <summary>
    /// 销售经理id
    /// </summary>
    public string? SaleUserId { get; set; }
    /// <summary>
    /// 销售经理名称
    /// </summary>
    public string? SaleUserName { get; set; }

    /// <summary>
    /// 销售经理头像
    /// </summary>
    public string? SaleUserAvatar { get; set; }

    /// <summary>
    /// 销售经理企业名称
    /// </summary>
    public string? SaleUserEntName { get; set; }
    /// <summary>
    /// 销售经理电话
    /// </summary>
    public string? SaleUserMobile { get; set; }
    /// <summary>
    /// 销售经理邮箱
    /// </summary>
    public string? SaleUserEmail { get; set; }
    /// <summary>
    /// 销售经理微信
    /// </summary>
    public string? SaleUserWeChat { get; set; }
    /// <summary>
    /// 销售经理企业微信二维码
    /// </summary>
    public string? SaleUserEntWeChatQrCode { get; set; }

    /// <summary>
    /// 项目开始日期
    /// </summary>
    public DateTime? ProjectStartTime { get; set; }
    /// <summary>
    /// 项目截止日期
    /// </summary>
    public DateTime? ProjectEndTime { get; set; }
    /// <summary>
    /// 项目级别
    /// </summary>
    public string? LevelName { get; set; }

    /// <summary>
    /// 项目类型
    /// </summary>
    public string? TypeName { get; set; }

    /// <summary>
    /// 项目行业
    /// </summary>
    public string? IndustryName { get; set; }

    /// <summary>
    /// 工作周期类型
    /// </summary>
    public string? WorkCycleTypeName { get; set; }

    /// <summary>
    /// 结算方式
    /// </summary>
    public string? ClearingTypeName { get; set; }

    /// <summary>
    /// 结算渠道
    /// </summary>
    public string? ClearingChannelName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public string? StatusName { get; set; }

    // /// <summary>
    // /// 合作模式
    // /// </summary>
    // public string? PaymentNodeName { get; set; }

    /// <summary>
    /// 打款方式
    /// </summary>
    public string? PaymentTypeName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 项目编号
    /// </summary>
    public string? ProjectNo { get; set; }

    /// <summary>
    /// 岗位数量
    /// </summary>
    public int PostCount { get; set; }

    /// <summary>
    /// 招聘人数
    /// </summary>
    public int RecruitNumber { get; set; }

    /// <summary>
    /// 交付数量(总)
    /// </summary>
    public int DeliveryNumber { get; set; }

    /// <summary>
    /// 交付人数
    /// </summary>
    public int DeliveriesNum { get; set; }

    /// <summary>
    /// 报名人数
    /// </summary>
    public int RegistrationNum { get; set; }

    /// <summary>
    /// 签约人数
    /// </summary>
    public int ContractNum { get; set; }

    /// <summary>
    /// 总佣金
    /// </summary>
    public decimal? Bounty { get; set; }

    /// <summary>
    /// 交付佣金
    /// </summary>
    public decimal DeliveriesBounty { get; set; }

    /// <summary>
    /// 代招企业信息
    /// </summary>
    public GetAgentEntDetail? AgentEnt { get; set; }

    /// <summary>
    /// 是否自己项目
    /// </summary>
    public bool SelfProj { get; set; }

    /// <summary>
    /// 参与者
    /// </summary>
    public ProjectParticipant? Participant { get; set; }
}

public class SetProjectPostStatus
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 上架true/下架false
    /// </summary>
    public bool On { get; set; }
}

public class ProjectParticipant
{
    /// <summary>
    /// 参与者头像
    /// </summary>
    public List<string> Avatars { get; set; } = new List<string>();

    /// <summary>
    /// 参与者数量
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    /// 最近Hr名字
    /// </summary>
    public string? FirstHrName { get; set; }
}

public class UpdateProjectSurvey
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 职位Id
    /// </summary>
    public string? PostId { get; set; }

    public ProjectSurveyContent Content { get; set; } = new ProjectSurveyContent();
}

public class GetProjectSurveyResponse
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 项目调研表
    /// </summary>
    public ProjectSurveyContent Content { get; set; } = new ProjectSurveyContent();
}

public class UpdateProjectSurveyResponse
{
    public string? Id { get; set; }

    /// <summary>
    /// 项目调研表
    /// </summary>
    public ProjectSurveyContent Content { get; set; } = new ProjectSurveyContent();
}

public class UpdateProjectAgentEnt
{
    /// <summary>
    /// 项目id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 代招企业Id
    /// </summary>
    public string? AgentEntId { get; set; }
}

public class ProjectTransfer
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 顾问手机
    /// </summary>
    public string? HrMobile { get; set; }
}

// public class GetProjectList : QueryRequest
// {

// }

// public class GetProjectListResponse : QueryResponse
// {
//     public List<GetProjectListDetail> Rows { get; set; } = default!;
// }

// public class GetProjectListDetail : UpdateProject
// {

// }