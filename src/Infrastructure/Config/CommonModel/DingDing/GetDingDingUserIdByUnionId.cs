﻿namespace Config.CommonModel.DingDing;

public class GetDingDingUserIdByUnionId
{
}

/// <summary>
/// 根据unionid获取userid请求模型
/// </summary>
public class GetDingDingUserIdByUnionIdRequest
{
    /// <summary>
    /// 钉钉unionid
    /// </summary>
    public string? unionid { get; set; }
}

/// <summary>
/// 根据unionid获取userid返回模型
/// </summary>
public class GetDingDingUserIdByUnionIdResponse
{
    /// <summary>
    /// 联系类型（0企业内部员工，1企业外部联系人）
    /// </summary>
    public int contact_type { get; set; }

    /// <summary>
    /// 钉钉userid
    /// </summary>
    public string? userid { get; set; }
}

