using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Staffing.Entity;

namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class SettlementService
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _logger;
    private readonly CommonDicService _commonDicService;
    private readonly CommonProjectService _commonProjectService;
    private readonly CommonPostOrder _commonPostOrder;
    public SettlementService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger,
    CommonDicService commonDicService, CommonProjectService commonProjectService, CommonPostOrder commonPostOrder)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _commonDicService = commonDicService;
        _commonProjectService = commonProjectService;
        _commonPostOrder = commonPostOrder;
    }

    /// <summary>
    /// 结算
    /// </summary>
    public async void SettlementTeamBounty(CancellationToken cancel)
    {
        try
        {
            if (cancel.IsCancellationRequested)
                return;

            using var _context = _contextFactory.CreateDbContext();

            await Task.FromResult(1);
        }
        catch (Exception e)
        {
            _logger.Error("入职过保到期检测出错", Tools.GetErrMsg(e));
        }
    }
}