﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 钉钉信息表
/// </summary>
[Table("dingding")]
public class DingDing
{
    /// <summary>
    /// 用户钉钉userid
    /// </summary>
    [Key]
    public string DingUserid { get; set; } = default!;

    /// <summary>
    /// 用户钉钉unionid
    /// </summary>
    public string DingUnionid { get; set; } = string.Empty;

    /// <summary>
    /// 用户钉钉头像
    /// </summary>
    public string DingAvatar { get; set; } = string.Empty;

    /// <summary>
    /// 用户钉钉姓名
    /// </summary>
    public string DingName { get; set; } = string.Empty;

    /// <summary>
    /// 用户钉钉工号
    /// </summary>
    public string DingJobNo { get; set; } = string.Empty;

    /// <summary>
    /// 用户钉钉手机号
    /// </summary>
    public string DingMobile { get; set; } = string.Empty;

    /// <summary>
    /// 用户钉钉职位
    /// </summary>
    public string DingTitle { get; set; } = string.Empty;

    /// <summary>
    /// 用户钉钉部门
    /// </summary>
    public string DingBranch { get; set; } = string.Empty;

    /// <summary>
    /// 消息推送时间
    /// </summary>
    public DateTime? PushTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}

