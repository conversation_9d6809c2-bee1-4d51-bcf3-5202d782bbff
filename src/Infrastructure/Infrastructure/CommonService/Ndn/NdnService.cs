﻿using Config;
using Config.CommonModel;
using Config.CommonModel.Ndn;
using Infrastructure.Aop;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Infrastructure.Proxy;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Entity.Noah;
using Staffing.Entity.Staffing;

namespace Infrastructure.CommonService.Ndn;

public interface INdnService
{
    // /// <summary>
    // /// 冻结/解冻/消费项目资金
    // /// </summary>
    // /// <param name="model"></param>
    // /// <returns></returns>
    // Task<bool> FreezeBudget(FreezeBudgetByPost model);

    // /// <summary>
    // /// 创建数字诺亚项目
    // /// </summary>
    // /// <param name="model"></param>
    // /// <returns></returns>
    // Task<string> CreateProject(CreateNdnProject model);

    // /// <summary>
    // /// 根据项目编码生成服务奖金
    // /// </summary>
    // /// <param name="model"></param>
    // /// <returns></returns>
    // Task GenerateServiceBonus(CreateNdnServiceBonus model);

    /// <summary>
    /// 查询员工信息
    /// </summary>
    /// <param name="hrNo"></param>
    /// <returns></returns>
    Task<NdnEmployee> GetEmployee(string hrNo);

    /// <summary>
    /// 获取帐套信息
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    Task<GetBooksInfo?> GetBookInfo(string? bookCode);

    ProjectContractInfo? GetNoahContractByCode(string contractCode);

    // /// <summary>
    // /// 自动提交的待办并处理
    // /// </summary>
    // /// <param name="model"></param>
    // /// <returns></returns>
    // Task ProcessAutoTasks(string? todoId);

    // /// <summary>
    // /// 申请转账报销
    // /// </summary>
    // /// <param name="model"></param>
    // /// <returns></returns>
    // Task TransferReimbursement(ProcessTasks model);

    // /// <summary>
    // /// 申请开发票
    // /// </summary>
    // /// <param name="model"></param>
    // /// <returns></returns>
    // Task InvoiceIssuance(ProcessTasks model);

    // /// <summary>
    // /// 提交服务奖金
    // /// </summary>
    // /// <param name="model"></param>
    // /// <returns></returns>
    // Task ServiceBonus(ProcessTasks model);

    // /// <summary>
    // /// 通知
    // /// </summary>
    // /// <param name="model"></param>
    // /// <returns></returns>
    // EmptyResponse NdnNotify(NdnNotify model);
}

[Service(ServiceLifetime.Transient)]
public class NdnService : INdnService
{
    private IDbContextFactory<StaffingContext> _staffingContextFactory;
    private IDbContextFactory<NoahContext> _noahContextFactory;
    private ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly NdnApi _ndnApi;
    public NdnService(IDbContextFactory<StaffingContext> staffingContextFactory, IOptionsSnapshot<ConfigManager> config,
    IHostEnvironment hostingEnvironment, LogManager log, NdnApi ndnApi, IDbContextFactory<NoahContext> noahContextFactory)
    {
        _staffingContextFactory = staffingContextFactory;
        _noahContextFactory = noahContextFactory;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
        _ndnApi = ndnApi;
    }

    // public async Task<bool> FreezeBudget(FreezeBudgetByPost model)
    // {
    //     using var context = _staffingContextFactory.CreateDbContext();

    //     //var post = context.Post.Where(x => x.PostId == model.postId)
    //     //.Select(s => new
    //     //{
    //     //    s.Project.HrId,
    //     //    s.ProjectId
    //     //}).FirstOrDefault();

    //     //if (post == null)
    //     //    throw new BadRequestException("职位不存在");

    //     if (model.amount <= 0)
    //         throw new BadRequestException("金额不正确");

    //     //冻结锁
    //     // var lockerKey = $"{RedisKey.Ndn.FreezeBudget}:{model.projectCode}";
    //     var lockerKey = $"{RedisKey.Ndn.FreezeBudgetLocker}";
    //     using var locker = await MyRedis.Lock(lockerKey, 15);
    //     if (locker == null)
    //         throw new Exception("获取数字诺亚冻结锁失败");

    //     //钱包锁
    //     var lockerKey2 = $"{RedisKey.Ndn.WalletLocker}:{model.HrId}";
    //     using var locker2 = await MyRedis.Lock(lockerKey2, 15);
    //     if (locker2 == null)
    //         throw new Exception("获取数字诺亚冻结锁失败");

    //     var wallet = context.Ndn_Hr_Wallet.FirstOrDefault(x => x.UserId == model.HrId);
    //     var freezePost = context.Ndn_Hr_Wallet_Details
    //     .Where(x => x.UserId == model.HrId && x.PostId == model.postId && x.ProjectCode == model.projectCode)
    //     .FirstOrDefault();

    //     //如果是解冻，先判断是否有足够的冻结金额
    //     if (model.type == BudgetFreezeType.解冻 || model.type == BudgetFreezeType.消费)
    //         if (freezePost == null || freezePost.Amount < model.amount)
    //             throw new BadRequestException("余额不足");

    //     //判断数字诺亚预算是否足够
    //     var project = await _ndnApi.GetProjectInfo(model.projectCode);
    //     if (project == null)
    //         throw new BadRequestException("数字诺亚项目不存在");

    //     if (model.type == BudgetFreezeType.冻结)
    //     {
    //         if (project.budget.surplusAmount < model.amount)
    //             throw new BadRequestException("数字诺亚的交付分润科目预算不足");
    //     }
    //     else if (model.type == BudgetFreezeType.解冻)
    //     {
    //         if (project.budget.frozenAmount < model.amount)
    //             throw new BadRequestException("解冻金额不足");
    //     }

    //     // 如果需要数字诺亚
    //     if (model.type == BudgetFreezeType.冻结 || model.type == BudgetFreezeType.解冻)
    //     {
    //         try
    //         {
    //             var result = await _ndnApi.FreezeBudget(new FreezeBudget
    //             {
    //                 projectCode = model.projectCode,
    //                 type = model.type,
    //                 amount = model.amount
    //             });
    //         }
    //         catch (NdnApiException ex)
    //         {
    //             throw new BadRequestException($"数字诺亚：{ex.Message}");
    //         }
    //     }

    //     //成功后更新钱包及明细
    //     if (freezePost == null)
    //     {
    //         freezePost = new Ndn_Hr_Wallet_Details
    //         {
    //             UserId = model.HrId,
    //             PostId = model.postId,
    //             ProjectCode = model.projectCode
    //         };
    //         context.Ndn_Hr_Wallet_Details.Add(freezePost);
    //     }

    //     if (wallet == null)
    //     {
    //         wallet = new Ndn_Hr_Wallet
    //         {
    //             UserId = model.HrId
    //         };
    //         context.Ndn_Hr_Wallet.Add(wallet);
    //     }

    //     if (model.type == BudgetFreezeType.冻结)
    //     {
    //         freezePost.Amount += model.amount;
    //         wallet.Amount += model.amount;
    //     }
    //     else if (model.type == BudgetFreezeType.解冻 || model.type == BudgetFreezeType.消费)
    //     {
    //         freezePost.Amount -= model.amount;
    //         wallet.Amount -= model.amount;
    //     }

    //     //添加钱包流水
    //     var walletTransfer = new Ndn_Hr_Wallet_Transfers
    //     {
    //         HrId = model.HrId,
    //         Amount = model.type == BudgetFreezeType.冻结 ? model.amount : (model.amount * -1),
    //         Type = model.type switch
    //         {
    //             BudgetFreezeType.冻结 => NdnWalletTransfersType.诺快聘冻结,
    //             BudgetFreezeType.解冻 => NdnWalletTransfersType.诺快聘解冻,
    //             BudgetFreezeType.消费 => NdnWalletTransfersType.交付消费,
    //             _ => NdnWalletTransfersType.诺快聘冻结
    //         },
    //         PostId = model.postId,
    //         ProjectId = model.ProjectId,
    //         ProjectCode = model.projectCode
    //     };
    //     context.Add(walletTransfer);

    //     context.SaveChanges();

    //     return true;
    // }

    // public async Task<string> CreateProject(CreateNdnProject model)
    // {
    //     //查询本地项目信息
    //     using var context = _staffingContextFactory.CreateDbContext();
    //     var project = context.Ndn_Project.FirstOrDefault(x => x.Id == model.ProjId);
    //     if (project == null)
    //         throw new BadRequestException($"数据异常:项目不存在");

    //     if (project.Status != NdnProjStatus.待办)
    //         throw new BadRequestException($"数据异常:项目状态不正确");
    //     try
    //     {
    //         var result = await _ndnApi.CreateProject(model);

    //         if (string.IsNullOrWhiteSpace(result?.projectCode))
    //             throw new Exception($"数字诺亚返回projectCode为空");

    //         //保存项目信息
    //         project.ProjectCode = result.projectCode;
    //         project.Status = NdnProjStatus.完成;
    //         project.ContractNo = model.contractCode;

    //         //完成待办
    //         var todo = context.Ndn_Todo.FirstOrDefault(x => x.RelatedEntityId == model.ProjId && x.RelatedEntityType == NdnTodoType.创建数字诺亚项目);
    //         if (todo != null)
    //         {
    //             todo.Status = NdnTodoStatus.完成;
    //             todo.UpdatedTime = DateTime.Now;
    //             todo.ProjectCode = result.projectCode;
    //         }

    //         context.SaveChanges();

    //         return result.projectCode;
    //     }
    //     catch (NdnApiException ex)
    //     {
    //         throw new BadRequestException($"数字诺亚：{ex.Message}");
    //     }
    // }

    // public async Task GenerateServiceBonus(CreateNdnServiceBonus model)
    // {
    //     using var context = _staffingContextFactory.CreateDbContext();

    //     //根据项目编号加锁
    //     var lockerKey = $"{RedisKey.Ndn.SettlementLocker}:{model.projectCode}";
    //     using var locker = await MyRedis.Lock(lockerKey, 60);
    //     if (locker == null)
    //         throw new Exception("获取数字诺亚服务奖金锁失败");

    //     //查询结算单，一批次最多处理500个
    //     var settlements = context.Project_Settlement.Where(x => x.Status == SettleStatus.结算中 && !x.IsServiceBonusInitiated
    //     && x.Type == SettleType.协同 && x.Project_Teambounty.Project.NuoId == model.projectCode)
    //     .OrderBy(x => x.CreatedTime).Take(500).ToList();

    //     //查询项目信息
    //     var project = context.Ndn_Project.FirstOrDefault(x => x.ProjectCode == model.projectCode);

    //     if (project == null)
    //         throw new Exception($"Ndn_Project表项目不存在:{model.projectCode}");

    //     var userIds = settlements.GroupBy(g => g.UserId).Select(s => s.Key).ToList();

    //     //User_DingDing表查询员工工号
    //     var userNos = context.User_DingDing.Where(x => userIds.Contains(x.UserId))
    //     .Select(s => new { s.UserId, s.DingJobNo }).ToList()
    //     .Select(s => (s.UserId, s.DingJobNo)).ToList();

    //     //钉钉用户表查询员工工号
    //     var userNos2 = context.User.Where(x => userIds.Contains(x.UserId) && !string.IsNullOrEmpty(x.Dd_User.DdUserId))
    //     .Select(s => new { s.UserId, s.Dd_User.JobNumber }).ToList()
    //     .Select(s => (UserId: s.UserId, DingJobNo: s.JobNumber)).ToList();

    //     userNos = userNos.Union(userNos2).Distinct().ToList();

    //     var ndnEmployees = new Dictionary<string, NdnEmployee>();
    //     //查询员工帐套
    //     foreach (var userId in userIds)
    //     {
    //         //通过userId 查找DingJobNo
    //         if (!userNos.Any(x => x.UserId == userId))
    //             continue;

    //         var userNo = userNos.FirstOrDefault(x => x.UserId == userId).DingJobNo;
    //         if (string.IsNullOrWhiteSpace(userNo))
    //             continue;

    //         try
    //         {
    //             ndnEmployees.Add(userId, await GetEmployee(userNo!));
    //             await Task.Delay(10);
    //         }
    //         catch (Exception ex)
    //         {
    //             _log.Error($"生成服务奖金：数字诺亚员工信息查询失败", Tools.GetErrMsg(ex), userNo);
    //         }
    //     }

    //     //根据帐套分组，每个帐套生成一个服务奖金发放流程
    //     var employeeGroups = ndnEmployees.Values.GroupBy(g => g.bookCode).ToList();

    //     foreach (var employeeGroup in employeeGroups)
    //     {
    //         //查询该帐套下的所有userid
    //         var groupUserIds = ndnEmployees.Where(x => x.Value.bookCode == employeeGroup.Key).Select(s => s.Key).ToList();

    //         //根据userid查询结算单
    //         var groupSettlements = settlements.Where(x => groupUserIds.Contains(x.UserId)).ToList();

    //         Ndn_Project_Transfers? transfer = null;

    //         //最终发放服务奖金的数字诺亚项目
    //         Ndn_Project? finalProject = null;

    //         var transferStatus = NdnAuditStatus.进行中;
    //         var bonusStatus = NdnAuditStatus.未开始;

    //         if (project.BookCode == employeeGroup.Key) //如果项目主体帐套和员工帐套一致
    //         {
    //             finalProject = project;
    //             transferStatus = NdnAuditStatus.已完成;
    //             bonusStatus = NdnAuditStatus.待提交;
    //         }
    //         else if (employeeGroup.Key == _config.Ndn!.NuoPinBookCode) //是否诺聘项目帐套
    //             finalProject = context.Ndn_Project.FirstOrDefault(x => x.Type != NdnProjType.数字诺亚 && x.BookCode == employeeGroup.Key && x.ClientBookCode == project.BookCode);
    //         else
    //             finalProject = context.Ndn_Project.FirstOrDefault(x => x.Type != NdnProjType.数字诺亚 && x.BookCode == employeeGroup.Key && x.ClientBookCode == _config.Ndn!.NuoPinBookCode);

    //         if (finalProject == null || string.IsNullOrWhiteSpace(finalProject.ProjectCode))
    //         {
    //             _log.Error($"生成服务奖金：Ndn_Project不存在项目", $"{employeeGroup.Key}_{project.BookCode}");
    //             continue;
    //         }

    //         transfer = new Ndn_Project_Transfers
    //         {
    //             Amount = groupSettlements.Sum(s => s.SettlementMoney ?? 0),
    //             Status = transferStatus,
    //             BonusStatus = bonusStatus,
    //             BonusTime = bonusStatus == NdnAuditStatus.待提交 ? DateTime.Now : null,
    //             FromProjectId = project.Id,
    //             FromProjectName = project.ProjectName,
    //             ToProjectId = finalProject.Id,
    //             ToProjectName = finalProject.ProjectName
    //         };
    //         context.Add(transfer);

    //         //如果项目主体帐套和员工帐套不一致
    //         if (project.BookCode != employeeGroup.Key)
    //         {
    //             // 第一步转账
    //             //如果是主创项目是诺聘项目，则第一步比较特殊
    //             Ndn_Project? step1ToProject = null;
    //             if (project.BookCode == _config.Ndn!.NuoPinBookCode)
    //                 step1ToProject = context.Ndn_Project.FirstOrDefault(x => x.Type != NdnProjType.数字诺亚 && x.BookCode == project.BookCode && x.ClientBookCode == _config.Ndn!.NuoPinBookCode);
    //             else
    //                 step1ToProject = context.Ndn_Project.FirstOrDefault(x => x.Type != NdnProjType.数字诺亚 && x.BookCode == _config.Ndn!.NuoPinBookCode && x.ClientBookCode == project.BookCode);

    //             if (step1ToProject == null)
    //             {
    //                 _log.Error($"生成服务奖金第一步转账：Ndn_Project不存在项目", $"主创项目：{project.BookCode}:{project.BookName}");
    //                 continue;
    //             }

    //             var transferDetail1 = new Ndn_Project_Transfer_Details
    //             {
    //                 TransferId = transfer.Id,
    //                 FromProjectId = project.Id!,
    //                 FromProjectName = project.ProjectName,
    //                 ToProjectId = step1ToProject.Id!,
    //                 ToProjectName = step1ToProject.ProjectName,
    //                 Status = NdnAuditStatus.未开始,
    //                 Step = 1
    //             };
    //             context.Add(transferDetail1);

    //             //生成发票待办
    //             var invoice = new Ndn_Project_Invoice
    //             {
    //                 TransferStepId = transferDetail1.Id,
    //                 Status = NdnAuditStatus.待提交
    //             };
    //             context.Add(invoice);

    //             var todo2 = new Ndn_Todo
    //             {
    //                 RelatedEntityId = invoice.Id,
    //                 Title = $"{step1ToProject.BookName}-{project.BookName}",
    //                 RelatedEntityType = NdnTodoType.开发票,
    //                 Status = NdnTodoStatus.待办,
    //                 ProjectCode = step1ToProject.ProjectCode
    //             };
    //             context.Add(todo2);

    //             //如果第一步转账不能满足需求
    //             if (transferDetail1.ToProjectId != finalProject.Id)
    //             {
    //                 //第二步转账
    //                 var step2ToProject = context.Ndn_Project.FirstOrDefault(x => x.Type != NdnProjType.数字诺亚
    //                 && x.BookCode == step1ToProject.BookCode && x.ClientBookCode == finalProject.BookCode);
    //                 if (step2ToProject == null)
    //                 {
    //                     _log.Error($"生成服务奖金第二步转账：Ndn_Project不存在项目", $"{step1ToProject.BookCode}:{finalProject.BookCode}", $"{step1ToProject.BookName}:{finalProject.BookName}");
    //                     continue;
    //                 }

    //                 var transferDetail2 = new Ndn_Project_Transfer_Details
    //                 {
    //                     TransferId = transfer.Id,
    //                     FromProjectId = step1ToProject.Id!,
    //                     FromProjectName = step1ToProject.ProjectName,
    //                     ToProjectId = step2ToProject.Id!,
    //                     ToProjectName = step2ToProject.ProjectName,
    //                     Status = NdnAuditStatus.未开始,
    //                     Step = 2
    //                 };
    //                 context.Add(transferDetail2);

    //                 //第三步转账
    //                 var step3ToProject = context.Ndn_Project.FirstOrDefault(x => x.Type != NdnProjType.数字诺亚
    //                 && x.BookCode == finalProject.BookCode && x.ClientBookCode == step2ToProject.BookCode);
    //                 if (step3ToProject == null)
    //                 {
    //                     _log.Error($"生成服务奖金第三步转账：Ndn_Project不存在项目", $"{finalProject.BookCode}:{step2ToProject.BookCode}", $"{finalProject.BookName}:{step2ToProject.BookName}");
    //                     continue;
    //                 }

    //                 var transferDetail3 = new Ndn_Project_Transfer_Details
    //                 {
    //                     TransferId = transfer.Id,
    //                     FromProjectId = step2ToProject.Id!,
    //                     FromProjectName = step2ToProject.ProjectName,
    //                     ToProjectId = step3ToProject.Id!,
    //                     ToProjectName = step3ToProject.ProjectName,
    //                     Status = NdnAuditStatus.未开始,
    //                     Step = 3
    //                 };
    //                 context.Add(transferDetail3);
    //             }
    //         }
    //         else
    //         {
    //             //生成服务奖金待办
    //             var todo = new Ndn_Todo
    //             {
    //                 RelatedEntityId = transfer.Id,
    //                 Title = $"{finalProject.BookName}",
    //                 RelatedEntityType = NdnTodoType.发放服务奖金,
    //                 Status = NdnTodoStatus.待办,
    //                 ProjectCode = finalProject.ProjectCode
    //             };
    //             context.Add(todo);
    //         }

    //         //生成服务奖金
    //         foreach (var item in groupSettlements)
    //         {
    //             item.IsServiceBonusInitiated = true;

    //             var ndnEmployee = ndnEmployees[item.UserId];

    //             item.UserNo = ndnEmployee.hrNo;

    //             var bonus = new Ndn_Service_Bonus
    //             {
    //                 SettlementId = item.Id,
    //                 HrId = item.UserId,
    //                 TransferId = transfer?.Id,
    //                 BookCode = employeeGroup.Key,
    //                 ProjectCode = finalProject == null ? project.ProjectCode : finalProject.ProjectCode,
    //                 ProjectName = finalProject == null ? project.ProjectName : finalProject.ProjectName,
    //                 // Status = transfer?.Status == NdnAuditStatus.已完成 ? NdnAuditStatus.待提交 : NdnAuditStatus.未开始
    //             };
    //             context.Add(bonus);
    //         }
    //         await Task.Delay(20);
    //     }

    //     context.SaveChanges();
    // }

    // public EmptyResponse NdnNotify(NdnNotify model)
    // {
    //     var result = new EmptyResponse();

    //     using var context = _staffingContextFactory.CreateDbContext();

    //     var todo = context.Ndn_Todo.FirstOrDefault(x => x.RelatedEntityType == model.Type && x.RelatedEntityId == model.OrderId);

    //     if (todo == null)
    //     {
    //         _log.Error($"数字诺亚通知：待办不存在", model.OrderId ?? string.Empty, model.Type);
    //     }
    //     else
    //     {
    //         // 拒绝的话待办状态不变(已和产品经理确认)
    //         if (model.Status == NdnNotifyStatus.成功)
    //         {
    //             todo.Status = NdnTodoStatus.完成;
    //             todo.UpdatedTime = DateTime.Now;
    //         }
    //     }

    //     var ndnAuditStatus = model.Status switch
    //     {
    //         NdnNotifyStatus.成功 => NdnAuditStatus.已完成,
    //         NdnNotifyStatus.失败 => NdnAuditStatus.已驳回,
    //         _ => NdnAuditStatus.已驳回
    //     };

    //     switch (model.Type)
    //     {
    //         case NdnTodoType.转账报销:
    //             HandleTransferReimbursement(model, ndnAuditStatus, context);
    //             break;
    //         case NdnTodoType.开发票:
    //             HandleInvoiceIssuance(model, ndnAuditStatus, context);
    //             break;
    //         case NdnTodoType.发放服务奖金:
    //             HandleServiceBonus(model, ndnAuditStatus, context);
    //             break;
    //         case NdnTodoType.回款:
    //             HandleReceivables(model, ndnAuditStatus, context);
    //             break;
    //     }

    //     context.SaveChanges();

    //     return result;
    // }

    private void HandleReceivables(NdnNotify model, NdnAuditStatus ndnAuditStatus, StaffingContext context)
    {
        // 查找发票
        var invoice = context.Ndn_Project_Invoice.FirstOrDefault(x => x.Id == model.OrderId);
        if (invoice == null)
        {
            _log.Error($"数字诺亚通知：回款明细不存在", model.OrderId!, model);
            throw new Exception($"数字诺亚通知：回款明细不存在");
        }

        if (ndnAuditStatus == NdnAuditStatus.已完成)
        {
            invoice.ReceivedAmount = invoice.ReceivedAmount ?? 0;
            invoice.ReceivedAmount += model.Amount;
        }
    }

    // private void HandleTransferReimbursement(NdnNotify model, NdnAuditStatus ndnAuditStatus, StaffingContext context)
    // {
    //     var transferDetail = context.Ndn_Project_Transfer_Details.FirstOrDefault(x => x.Id == model.OrderId);
    //     if (transferDetail == null)
    //     {
    //         _log.Error($"数字诺亚通知：转账报销明细不存在", model.OrderId!, model);
    //         throw new Exception($"数字诺亚通知：转账报销明细不存在");
    //     }
    //     else
    //     {
    //         if (transferDetail.Status != NdnAuditStatus.已完成)
    //         {
    //             transferDetail.Status = ndnAuditStatus;
    //             transferDetail.UpdatedTime = DateTime.Now;

    //             if (ndnAuditStatus == NdnAuditStatus.已完成)
    //             {
    //                 // 找到下一步
    //                 var nextStep = context.Ndn_Project_Transfer_Details.Where(x => x.TransferId == transferDetail.TransferId
    //                 && x.Step > transferDetail.Step)
    //                 .OrderBy(x => x.Step)
    //                 .Include(i => i.Project_From).Include(i => i.Project_To)
    //                 .FirstOrDefault();

    //                 if (nextStep == null)
    //                 {
    //                     //如果是最后一步
    //                     var transfer = context.Ndn_Project_Transfers
    //                     .Include(i => i.Project_From).Include(i => i.Project_To)
    //                     .First(x => x.Id == transferDetail.TransferId);

    //                     transfer.Status = ndnAuditStatus;
    //                     transfer.UpdatedTime = DateTime.Now;

    //                     //转账完成，发放服务奖金
    //                     if (transfer.BonusStatus == NdnAuditStatus.未开始)
    //                     {
    //                         transfer.BonusStatus = NdnAuditStatus.待提交;
    //                         transfer.BonusTime = DateTime.Now;
    //                         // var bonuses = context.Ndn_Service_Bonus.Where(x => x.TransferId == transferDetail.TransferId).ToList();
    //                         // foreach (var bonus in bonuses)
    //                         // {
    //                         //     bonus.Status = NdnAuditStatus.待提交;
    //                         //     bonus.UpdatedTime = DateTime.Now;
    //                         // }

    //                         //发起待办
    //                         var todo = new Ndn_Todo
    //                         {
    //                             RelatedEntityId = transfer.Id,
    //                             Title = $"{transfer.Project_To.BookName}",
    //                             RelatedEntityType = NdnTodoType.发放服务奖金,
    //                             Status = NdnTodoStatus.待办,
    //                             ProjectCode = transfer.Project_To.ProjectCode
    //                         };
    //                         context.Add(todo);
    //                     }
    //                     else
    //                         throw new Exception($"数字诺亚通知：转账报销状态不正确。transfer.BonusStatus:{(int)transfer.BonusStatus}");
    //                 }
    //                 else if (nextStep.Status != NdnAuditStatus.已完成)
    //                 {
    //                     nextStep.UpdatedTime = DateTime.Now;

    //                     //生成发票待办
    //                     nextStep.Status = NdnAuditStatus.未开始;

    //                     var invoice = new Ndn_Project_Invoice
    //                     {
    //                         TransferStepId = nextStep.Id,
    //                         Status = NdnAuditStatus.待提交
    //                     };
    //                     context.Add(invoice);

    //                     //发票代办
    //                     var todo = new Ndn_Todo
    //                     {
    //                         RelatedEntityId = invoice.Id,
    //                         Title = $"{nextStep.Project_To.BookName}-{nextStep.Project_From.BookName}",
    //                         RelatedEntityType = NdnTodoType.开发票,
    //                         Status = NdnTodoStatus.待办,
    //                         ProjectCode = nextStep.Project_To.ProjectCode
    //                     };
    //                     context.Add(todo);
    //                 }
    //             }
    //         }
    //     }
    // }

    // private void HandleInvoiceIssuance(NdnNotify model, NdnAuditStatus ndnAuditStatus, StaffingContext context)
    // {
    //     var invoice = context.Ndn_Project_Invoice.FirstOrDefault(x => x.Id == model.OrderId);
    //     if (invoice == null)
    //     {
    //         _log.Error($"数字诺亚通知：开发票明细不存在", model.OrderId!, model);
    //         throw new Exception($"数字诺亚通知：开发票明细不存在");
    //     }

    //     if (invoice.Status == NdnAuditStatus.已完成)
    //         return;

    //     invoice.Status = ndnAuditStatus;
    //     invoice.UpdatedTime = DateTime.Now;
    //     invoice.InvoiceUrl = model.InvoiceUrl;

    //     // 如果开票成功，进行下一步待办
    //     if (ndnAuditStatus == NdnAuditStatus.已完成)
    //     {
    //         // 找到转账
    //         var transferDetail = context.Ndn_Project_Transfer_Details
    //         .Include(i => i.Project_From).Include(i => i.Project_To)
    //         .First(x => x.Id == invoice.TransferStepId);

    //         if (transferDetail.Status == NdnAuditStatus.未开始)
    //         {
    //             transferDetail.Status = NdnAuditStatus.待提交;
    //             transferDetail.CreatedTime = DateTime.Now;
    //             transferDetail.UpdatedTime = DateTime.Now;
    //             transferDetail.InvoiceUrl = model.InvoiceUrl;

    //             // 找到待办
    //             var todo2 = context.Ndn_Todo.FirstOrDefault(x => x.RelatedEntityId == invoice.Id && x.RelatedEntityType == NdnTodoType.开发票);
    //             if (todo2 != null)
    //             {
    //                 todo2.Status = NdnTodoStatus.完成;
    //                 todo2.UpdatedTime = DateTime.Now;
    //             }

    //             //开始下一个待办
    //             var todo = new Ndn_Todo
    //             {
    //                 RelatedEntityId = transferDetail.Id,
    //                 Title = $"{transferDetail.Project_From.BookName}-{transferDetail.Project_To.BookName}",
    //                 RelatedEntityType = NdnTodoType.转账报销,
    //                 Status = NdnTodoStatus.待办,
    //                 ProjectCode = transferDetail.Project_From.ProjectCode
    //             };
    //             context.Add(todo);
    //         }
    //         else
    //             throw new Exception($"数字诺亚通知：转账报销状态不正确。transferDetail.Status:{(int)transferDetail.Status}");
    //     }
    // }

    // private void HandleServiceBonus(NdnNotify model, NdnAuditStatus ndnAuditStatus, StaffingContext context)
    // {
    //     var transfer = context.Ndn_Project_Transfers.FirstOrDefault(x => x.Id == model.OrderId);
    //     if (transfer == null)
    //     {
    //         _log.Error($"数字诺亚通知：服务奖金不存在", model.OrderId!, model);
    //         throw new Exception($"数字诺亚通知：服务奖金不存在");
    //     }
    //     else
    //     {
    //         if (transfer.BonusStatus != NdnAuditStatus.已完成)
    //         {
    //             transfer.BonusStatus = ndnAuditStatus;
    //             transfer.UpdatedTime = DateTime.Now;

    //             //查找服务奖金
    //             var bonusesSql = context.Ndn_Service_Bonus.Where(x => x.TransferId == transfer.Id);

    //             var bonuses = bonusesSql.Include(i => i.Project_Settlement).ThenInclude(i => i.Project_Teambounty).ToList();

    //             foreach (var item in bonuses)
    //             {
    //                 // item.Status = ndnAuditStatus;
    //                 // item.UpdatedTime = DateTime.Now;

    //                 //如果服务奖金审核通过，更新结算单状态
    //                 if (ndnAuditStatus == NdnAuditStatus.已完成)
    //                 {
    //                     item.Project_Settlement.ActualSettlementMoney = item.Project_Settlement.SettlementMoney ?? 0;
    //                     item.Project_Settlement.Status = SettleStatus.已结算;
    //                     item.Project_Settlement.UpdatedTime = DateTime.Now;
    //                     item.Project_Settlement.SettlementTime = DateTime.Now;
    //                     item.Project_Settlement.Project_Teambounty.SettlementStatus = Config.Enums.SettlementType.已结算;
    //                     item.Project_Settlement.Project_Teambounty.SettlementTime = DateTime.Now;
    //                 }
    //             }

    //             if (ndnAuditStatus == NdnAuditStatus.已完成)
    //             {
    //                 // 完成待办
    //                 var todo = context.Ndn_Todo.FirstOrDefault(x => x.RelatedEntityId == transfer.Id && x.RelatedEntityType == NdnTodoType.发放服务奖金);
    //                 if (todo != null)
    //                 {
    //                     todo.Status = NdnTodoStatus.完成;
    //                     todo.UpdatedTime = DateTime.Now;
    //                 }
    //             }
    //         }
    //     }
    // }

    // public async Task ProcessAutoTasks(string? todoId)
    // {
    //     using var context = _staffingContextFactory.CreateDbContext();
    //     var todo = context.Ndn_Todo.FirstOrDefault(x => x.Id == todoId);

    //     if (todo?.Status != NdnTodoStatus.待办)
    //         throw new BadRequestException("待办状态不正确");

    //     switch (todo.RelatedEntityType)
    //     {
    //         case NdnTodoType.转账报销:
    //             await TransferReimbursement(new ProcessTasks { Id = todo.RelatedEntityId, HrNo = Constants.Ndn.DefaultHrNo });
    //             break;
    //         case NdnTodoType.开发票:
    //             await InvoiceIssuance(new ProcessTasks { Id = todo.RelatedEntityId, HrNo = Constants.Ndn.DefaultHrNo });
    //             break;
    //     }
    // }

    // public async Task TransferReimbursement(ProcessTasks model)
    // {
    //     var lockerKey = $"{RedisKey.Ndn.TodoLocker}:TransferReimbursement";
    //     using var locker = await MyRedis.Lock(lockerKey, 15);
    //     if (locker == null)
    //         throw new Exception("获取转账锁失败");

    //     using var context = _staffingContextFactory.CreateDbContext();
    //     var transferDetail = context.Ndn_Project_Transfer_Details
    //     .FirstOrDefault(x => x.Id == model.Id);

    //     var transferDetailInfo = context.Ndn_Project_Transfer_Details.Where(x => x.Id == model.Id)
    //     .Select(s => new
    //     {
    //         s.Ndn_Project_Transfers.Amount,
    //         FromProjectCode = s.Project_From.ProjectCode,
    //         ToProjectCode = s.Project_To.ProjectCode,
    //         FromBookCode = s.Project_From.BookCode,
    //         ToBookCode = s.Project_To.BookCode
    //     }).First();

    //     if (transferDetail == null)
    //         throw new BadRequestException("转账明细不存在");

    //     if (transferDetail.Status != NdnAuditStatus.待提交 && transferDetail.Status != NdnAuditStatus.已驳回)
    //         throw new BadRequestException("转账状态不正确");

    //     // //查询对应发票
    //     // var invoice = context.Ndn_Project_Invoice.Where(x => x.TransferStepId == transferDetail.Id)
    //     // .Select(s => new { s.Id, s.InvoiceUrl }).FirstOrDefault();

    //     if (transferDetailInfo.FromBookCode != transferDetailInfo.ToBookCode
    //     && (transferDetail.InvoiceUrl == null || transferDetail.InvoiceUrl.Count == 0))
    //         throw new BadRequestException("没有查到对应发票，无法申请转账");

    //     if (string.IsNullOrWhiteSpace(transferDetail.ContractUrl))
    //         throw new BadRequestException("没有查到对应合同，无法申请转账");

    //     // 查询项目信息，判断是现金流还是项目类
    //     var project = await _ndnApi.GetProjectInfo(transferDetailInfo.FromProjectCode!);

    //     var reimbursement = new Reimbursement
    //     {
    //         projectCode = transferDetailInfo.FromProjectCode,
    //         amount = transferDetailInfo.Amount,
    //         budgetCode = Constants.Ndn.FrKmCode,
    //         feeName = Constants.Ndn.FrKmName,
    //         costName = "诺快聘交付分润",
    //         payType = 2,
    //         agentHrNo = model.HrNo,
    //         account = "6217000110013407664",
    //         orderNumber = transferDetail.Id,
    //         invoice = transferDetail.InvoiceUrl,
    //         contract = transferDetail.ContractUrl
    //     };

    //     // 判断项目类型
    //     if (project == null)
    //         throw new Exception($"数字诺亚项目不存在：{transferDetailInfo.FromProjectCode}");

    //     string? remId = null;

    //     //向数字诺亚申请转账，转账审核通过后再进行下一步
    //     if (string.Equals(project.projectCategory, "项目类"))
    //         remId = await _ndnApi.Reimbursement(reimbursement);
    //     else if (string.Equals(project.projectCategory, "现金流"))
    //         remId = await _ndnApi.ReimbursementCashFlow(reimbursement);
    //     else
    //         throw new Exception($"数字诺亚项目类型不正确：{transferDetailInfo.FromProjectCode},{project.projectCategory}");

    //     //查找对应待办
    //     var todo = context.Ndn_Todo.FirstOrDefault(x => x.RelatedEntityId == transferDetail.Id && x.RelatedEntityType == NdnTodoType.转账报销);

    //     if (todo != null)
    //     {
    //         todo.Status = NdnTodoStatus.进行中;
    //         todo.UpdatedTime = DateTime.Now;
    //     }

    //     transferDetail.Status = NdnAuditStatus.进行中;
    //     transferDetail.UpdatedTime = DateTime.Now;
    //     transferDetail.NdnId = remId;
    //     transferDetail.OperatorName = model.HrNo;

    //     context.SaveChanges();
    // }

    // public async Task InvoiceIssuance(ProcessTasks model)
    // {
    //     var lockerKey = $"{RedisKey.Ndn.TodoLocker}:InvoiceIssuance";
    //     using var locker = await MyRedis.Lock(lockerKey, 15);
    //     if (locker == null)
    //         throw new Exception("获取开发票锁失败");

    //     using var context = _staffingContextFactory.CreateDbContext();
    //     var invoice = context.Ndn_Project_Invoice.FirstOrDefault(x => x.Id == model.Id);

    //     if (invoice == null)
    //         throw new BadRequestException("发票不存在");

    //     if (invoice.Status != NdnAuditStatus.待提交 && invoice.Status != NdnAuditStatus.已驳回)
    //         throw new BadRequestException("发票状态不正确");

    //     var transferDetailInfo = context.Ndn_Project_Transfer_Details.Where(x => x.Id == invoice.TransferStepId)
    //     .Select(s => new
    //     {
    //         s.Ndn_Project_Transfers.Amount,
    //         FromProjectCode = s.Project_From.ProjectCode,
    //         ToProjectCode = s.Project_To.ProjectCode,
    //         s.Status
    //     }).First();

    //     if (transferDetailInfo == null)
    //         throw new BadRequestException("转账明细不存在");

    //     if (transferDetailInfo.Status != NdnAuditStatus.未开始)
    //         throw new BadRequestException("转账状态不正确");

    //     //向数字诺亚申请发票，发票审核通过后再开启转账流程
    //     var invoiceId = await _ndnApi.InitiateInvoice(new InitiateInvoice
    //     {
    //         projectCode = transferDetailInfo.ToProjectCode,
    //         amount = transferDetailInfo.Amount,
    //         orderNumber = invoice.Id,
    //         agentHrNo = model.HrNo
    //     });

    //     //查找对应待办
    //     var todo = context.Ndn_Todo.FirstOrDefault(x => x.RelatedEntityId == invoice.Id && x.RelatedEntityType == NdnTodoType.开发票);
    //     if (todo != null)
    //     {
    //         todo.Status = NdnTodoStatus.进行中;
    //         todo.UpdatedTime = DateTime.Now;
    //     }

    //     invoice.Status = NdnAuditStatus.进行中;
    //     invoice.UpdatedTime = DateTime.Now;
    //     invoice.NdnId = invoiceId;
    //     invoice.OperatorName = model.HrNo;

    //     context.SaveChanges();
    // }

    // public async Task ServiceBonus(ProcessTasks model)
    // {
    //     var lockerKey = $"{RedisKey.Ndn.TodoLocker}:ServiceBonus";
    //     using var locker = await MyRedis.Lock(lockerKey, 15);
    //     if (locker == null)
    //         throw new Exception("获取服务奖金锁失败");

    //     using var context = _staffingContextFactory.CreateDbContext();

    //     var transfer = context.Ndn_Project_Transfers.FirstOrDefault(x => x.Id == model.Id);
    //     if (transfer == null)
    //         throw new BadRequestException("内容不存在");

    //     if (transfer.Status != NdnAuditStatus.已完成)
    //         throw new BadRequestException("转账尚未完成，不允许提交服务奖金");

    //     if (transfer.BonusStatus != NdnAuditStatus.待提交 && transfer.BonusStatus != NdnAuditStatus.已驳回)
    //         throw new BadRequestException("服务奖金状态不正确");

    //     // // 得到原始项目编码
    //     // var projectCode = context.Ndn_Service_Bonus.Where(x => x.TransferId == model.Id)
    //     // .Select(s => s.Project_Settlement.Project_Teambounty.ProjectCode).First();

    //     // // 查询项目信息
    //     // var ndnProj = await _ndnApi.GetProjectInfo(projectCode!);

    //     // //项目回款
    //     // var projectReceivables = ndnProj?.collectionAmount ?? 0;

    //     // // 查询该项目历史提交的服务奖金总和，再加上本次的数量，是否大于项目回款数量，如果回款不够则不允许提交
    //     // var totalBonus = context.Ndn_Service_Bonus.Where(x => x.Project_Settlement.Project_Teambounty.ProjectCode == projectCode
    //     // && (x.Ndn_Project_Transfers.BonusStatus == NdnAuditStatus.已完成 || x.Ndn_Project_Transfers.BonusStatus == NdnAuditStatus.进行中 || x.TransferId == model.Id))
    //     // .Sum(s => s.Project_Settlement.SettlementMoney) ?? 0;

    //     // if (totalBonus > projectReceivables)
    //     //     throw new BadRequestException("项目没有足够回款，不允许提交服务奖金");

    //     var transferInfo = context.Ndn_Project_Transfers.Where(x => x.Id == model.Id)
    //     .Select(s => new
    //     {
    //         ToProjectCode = s.Project_To.ProjectCode,
    //         FromProjectCode = s.Project_From.ProjectCode
    //     }).First();

    //     var bonuses = context.Ndn_Service_Bonus.Where(x => x.TransferId == model.Id && x.Project_Settlement.SettlementMoney > 0)
    //     .Select(s => new IssueDetail
    //     {
    //         hrNo = s.Project_Settlement.UserNo,
    //         issuedAmount = s.Project_Settlement.SettlementMoney ?? 0,
    //         remark = string.Empty
    //     }).ToList();

    //     //向数字诺亚申请发放服务奖金
    //     var bonusId = await _ndnApi.ServiceBonus(new ServiceBonus
    //     {
    //         projectCode = transferInfo.ToProjectCode,
    //         orderNumber = transfer.Id,
    //         agentHrNo = model.HrNo,
    //         feeName = Constants.Ndn.FrKmName,
    //         issueList = bonuses
    //     });

    //     //查找对应待办
    //     var todo = context.Ndn_Todo.FirstOrDefault(x => x.RelatedEntityId == transfer.Id && x.RelatedEntityType == NdnTodoType.发放服务奖金);
    //     if (todo != null)
    //     {
    //         todo.Status = NdnTodoStatus.进行中;
    //         todo.UpdatedTime = DateTime.Now;
    //     }

    //     transfer.BonusStatus = NdnAuditStatus.进行中;
    //     transfer.UpdatedTime = DateTime.Now;
    //     transfer.NdnId = bonusId;
    //     transfer.OperatorName = model.HrNo;

    //     context.SaveChanges();
    // }

    [RedisCache(Expire = 300)]
    public async Task<GetBooksInfo?> GetBookInfo(string? bookCode)
    {
        var books = await _ndnApi.GetBooks(new GetBooks { bookCode = bookCode });
        var book = books?.FirstOrDefault();
        return book;
    }

    [RedisCache(Expire = 3600)]
    public async Task<NdnEmployee> GetEmployee(string hrNo)
    {
        var result = await _ndnApi.GetEmployee(hrNo);
        return result;
    }

    public ProjectContractInfo? GetNoahContractByCode(string contractCode)
    {
        if (string.IsNullOrWhiteSpace(contractCode))
            throw new BadRequestException("合同编号不能为空");

        using var _noahContext = _noahContextFactory.CreateDbContext();

        var linq = from a in _noahContext.Contract
                   join b in _noahContext.ContractProductRelation.GroupBy(g => g.ContractId)
                   .Select(s => new { ContractId = s.Key, ProductId = s.Max(m => m.ProductId) })
                   on a.ContractId equals b.ContractId into c
                   from d in c.DefaultIfEmpty()
                   join e in _noahContext.VSysSetBooks
                   on a.SignDept equals e.XiaobangbangCode into f
                   from g in f.DefaultIfEmpty()
                   where a.ContractCode == contractCode
                   orderby a.UpdateTime
                   select new
                   {
                       a.ContractId,
                       a.Nature,
                       a.State,
                       a.StartTime,
                       a.DueTime,
                       a.Item,
                       a.Price,
                       a.Appendix,
                       a.ContractCode,
                       a.Type,
                       a.SignDate,
                       a.UpdateTime,
                       a.CreateTime,
                       CustomerName = a.Customer.Name,
                       CustomerPhone = a.Customer.Tel,
                       SignerName = a.SignerModel.Name,
                       SignerPhone = a.SignerModel.Phone,
                       SignerEmployeeCode = a.SignerModel.HrNo,
                       a.SznyCode,
                       d.ProductId,
                       a.CustomerSigner,
                       CustomerContact = _noahContext.CustomerContact.Where(x => x.CustomerId == a.CustomerId).Select(s => new { s.Name, s.Tel, s.Tel2, s.Phone, s.Importance }).ToList(),
                       SetBooksName = g.SetBooksName
                   };

        var noahContracts = linq.FirstOrDefault();

        if (noahContracts == null)
            return null;

        var contact = noahContracts.CustomerContact?.Where(x => !string.IsNullOrWhiteSpace(x.Tel) || !string.IsNullOrWhiteSpace(x.Tel2) || !string.IsNullOrWhiteSpace(x.Phone))
        .OrderBy(x => x.Importance)
        .Select(s => new
        {
            Tel = !string.IsNullOrWhiteSpace(s.Tel) ? s.Tel : !string.IsNullOrWhiteSpace(s.Tel2) ? s.Tel2 : s.Phone,
            s.Name
        }).FirstOrDefault();

        var result = new ProjectContractInfo
        {
            ContractId = string.Empty,
            ProjectId = string.Empty,
            Name = noahContracts.Item,
            SignDate = noahContracts.SignDate,
            StartTime = noahContracts.StartTime,
            EndTime = noahContracts.DueTime,
            Initiator = noahContracts.CustomerName,
            Participant = noahContracts.SetBooksName,
            InitiationDate = noahContracts.CreateTime,
            InitiatorPhone = !string.IsNullOrEmpty(contact?.Tel) ? contact?.Tel : noahContracts.CustomerPhone,
            // InitiatorPhone = string.Empty,
            ParticipantPhone = noahContracts.SignerPhone,
            InitiatorContact = contact?.Name,
            // InitiatorContact = noahContracts.CustomerSigner,
            ParticipantContact = noahContracts.SignerName,
            Amount = noahContracts.Price ?? 0,
            ContractNo = noahContracts.ContractCode,
            Attachment = new List<string>(),
            Remark = string.Empty
        };

        try
        {
            var aoe = ServiceStack.Text.JsonSerializer.DeserializeFromString<List<GetProjectListInfoDataFile>>(noahContracts.Appendix);
            result.Attachment = aoe.Select(s => s.attachIndex ?? string.Empty).ToList();
            result.Attachment = result.Attachment.Where(x => !string.IsNullOrWhiteSpace(x)).ToList();
        }
        catch { }

        return result;
    }
}