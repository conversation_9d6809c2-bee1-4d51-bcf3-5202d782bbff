﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 用户余额表
/// </summary>
[Table("user_balance")]
public class User_Balance
{
    [Key]
    public string UserId { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// hr余额
    /// </summary>
    [ConcurrencyCheck]
    public decimal HrBalance { get; set; }

    /// <summary>
    /// hr余额历史总额
    /// </summary>
    public decimal HrBalanceFull { get; set; }

    /// <summary>
    /// hr上次提现时间
    /// </summary>
    public DateTime? HrWithdrawTime { get; set; }

    /// <summary>
    /// Seeker余额
    /// </summary>
    [ConcurrencyCheck]
    public decimal SeekerBalance { get; set; }

    /// <summary>
    /// Seeker余额历史总额
    /// </summary>
    public decimal SeekerBalanceFull { get; set; }

    /// <summary>
    /// Seeker上次提现时间
    /// </summary>
    public DateTime? SeekerWithdrawTime { get; set; }


    public User User { get; set; } = default!;
}