/*
 Navicat Premium Data Transfer

 Source Server         : 诺聘**************（测试环境）
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : **************:3306
 Source Schema         : staffing

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 13/02/2025 13:34:50
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for yunsheng_chat_messages
-- ----------------------------
DROP TABLE IF EXISTS `yunsheng_chat_messages`;
CREATE TABLE `yunsheng_chat_messages`  (
  `Id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `SeekerId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '求职者Id',
  `HrId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '招聘者Id',
  `ChatId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '云生会话Id',
  `Role` int NOT NULL COMMENT '会话角色 1：Seeker  2：Assistant(机器人)',
  `Content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '会话内容',
  `Type` int NOT NULL COMMENT '会话类型 1：消息  2：动作',
  `Commands` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '动作内容     要简历：OBTAIN_RESUME_INFO    要电话：OBTAIN_TEL_INFO     要微信：OBTAIN_WECHAT_INFO',
  `MessageId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息 ID',
  `MessageTime` datetime NULL DEFAULT NULL COMMENT '消息时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `PK_Yunsheng_Chat_Messages`(`Id` ASC) USING BTREE COMMENT '主键索引',
  INDEX `PK2_Yunsheng_Chat_Messages`(`Id` ASC, `SeekerId` ASC, `HrId` ASC, `ChatId` ASC) USING BTREE COMMENT '组合索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '与云生会话聊天内容记录表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
