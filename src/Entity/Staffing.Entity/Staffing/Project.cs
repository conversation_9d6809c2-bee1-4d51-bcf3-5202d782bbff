﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config;
using Config.CommonModel;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 项目表
/// </summary>
[Table("project")]
public class Project
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string ProjectId { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 自增Id
    /// </summary>
    public int AutoId { get; set; }

    /// <summary>
    /// 代招企业id
    /// </summary>
    public string? AgentEntId { get; set; }

    /// <summary>
    /// hrid为了兼容老项目修改为项目经理id
    /// </summary>
    public string? HrId { get; set; } = default!;

    // /// <summary>
    // /// 零工市场代填hrid
    // /// </summary>
    // public string? AgentHrId { get; set; }

    // /// <summary>
    // /// 项目名称。统一修改为企业名称，不在使用
    // /// </summary>
    // public string? Name { get; set; } = default!;

    /// <summary>
    /// 签约方式
    /// </summary>
    public ContractType? ContractType { get; set; }

    /// <summary>
    /// 项目级别
    /// </summary>
    public ProjectLevel Level { get; set; } = ProjectLevel.一般项目;

    /// <summary>
    /// 项目类型/用工形式
    /// </summary>
    public ProjectType Type { get; set; } = ProjectType.人才派遣;

    /// <summary>
    /// 项目行业
    /// </summary>
    public ProjectIndustry Industry { get; set; }

    /// <summary>
    /// 工作周期类型
    /// </summary>
    public ProjectWorkCycleType WorkCycleType { get; set; } = ProjectWorkCycleType.日结用工;

    /// <summary>
    /// 结算方式
    /// </summary>
    public ProjectClearingType ClearingType { get; set; } = ProjectClearingType.日结;

    /// <summary>
    /// 结算渠道
    /// </summary>
    public ProjectClearingChannel ClearingChannel { get; set; } = ProjectClearingChannel.诺快聘结算;

    /// <summary>
    /// 诺亚项目编码
    /// </summary>
    public string NuoId { get; set; } = default!;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    // /// <summary>
    // /// 地区json
    // /// </summary>
    // public List<CityModel>? RegionData { get; set; } = default!;

    /// <summary>
    /// 状态
    /// </summary>
    public ProjectStatus Status { get; set; }


    /// <summary>
    /// 项目开始日期
    /// </summary>
    public DateTime? StartTime { get; set; }
    /// <summary>
    /// 项目截止日期
    /// </summary>
    public DateTime EndTime { get; set; } = Constants.DefaultFutureTime;

    /// <summary>
    /// （合作）结算模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }
    /// <summary>
    /// 打款方式
    /// </summary>
    public ProjectPaymentType? PaymentType { get; set; }

    /// <summary>
    /// 是否有协同的职位（项目大厅检索用）
    /// </summary>
    public bool SharePost { get; set; }

    public bool Deleted { get; set; }

    /// <summary>
    /// 销帮帮合同编号
    /// </summary>
    public string? XbbContractNo { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 是否开启员工服务，默认0-不开启
    /// </summary>
    public bool StaffService { get; set; }

    /// <summary>
    /// 是否指定项目经理
    /// </summary>
    public AssignedPersonEnum IsAssignedPerson { get; set; }

    /// <summary>
    /// 销售id
    /// </summary>
    public string? SaleUserId { get; set; }
    /// <summary>
    /// 财务结算人员id
    /// </summary>
    public string? SettlementPersonId { get; set; }
    /// <summary>
    /// 帐套code
    /// </summary>
    public string? BookCode { get; set; }

    /// <summary>
    /// 合同金额
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 是否接单0 否 1是
    /// </summary>
    public RecruitAcceptOrderEnum? AcceptOrder { get; set; } = RecruitAcceptOrderEnum.未接单;


    public Project_Extend Project_Extend { get; set; } = default!;
    //项目经理
    public User_Hr User_Hr { get; set; } = default!;
    //销售经理
    public User_Hr SaleUser { get; set; } = default!;
    /// <summary>
    /// 财务结算人员
    /// </summary>
    public User_Hr Settlement { get; set; } = default!;

    // public User_Hr Agent_Hr { get; set; } = default!;
    public Agent_Ent Agent_Ent { get; set; } = default!;
    public List<Post> Posts { get; set; } = default!;
    public List<Project_Team> Project_Team { get; set; } = default!;
    public List<Project_Region> Project_Region { get; set; } = default!;
    public Project_Automatic? Project_Automatic { get; set; }

    public Project_Survey? Project_Survey { get; set; }

}

