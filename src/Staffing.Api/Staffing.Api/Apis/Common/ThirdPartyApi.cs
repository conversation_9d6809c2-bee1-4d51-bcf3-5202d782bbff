﻿using Config;
using Config.CommonModel;
using Config.CommonModel.ZhaoPinYun;
using Infrastructure.Common;
using Infrastructure.CommonRepository.ZhaoPinYun;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Infrastructure.Proxy;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.Options;
using ServiceStack.Text;
using Staffing.Core.Interfaces;
using Staffing.Core.Interfaces.Common;
using Staffing.Entity;
using Staffing.Entity.Elasticsearch;
using Staffing.Entity.Staffing;

namespace Staffing.Api.Apis.Common
{
    [Service(ServiceLifetime.Transient)]
    public class ThirdPartyApi : ICommonRouterBase
    {
        private const string skey = "e4b0c4423f8b4e2b8a5f7d8c9a1e6f3d";
        private readonly EsHelper _esHelper;
        private readonly CommonProjectService _commonProjectService;
        public ThirdPartyApi(EsHelper esHelper, CommonProjectService commonProjectService)
        {
            _commonProjectService = commonProjectService;
            _esHelper = esHelper;
        }
        public void AddRoutes(RouteGroupBuilder parentGroup)
        {
            var group = parentGroup.MapGroup("thirdpartyApi").WithTags("第三方接口");
            group.MapPost("/zxejoblist", async (ZhiXiaoErApi zxeapi) =>
            {
                var zxejoblist = await zxeapi.GetZhiXiaoErJobList(1, 300);
                MyRedis.Client.SAdd(SubscriptionKey.ZddJobList, zxejoblist);
            })
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "职小二接口" });

            group.MapPost("/postrelationdelete", async (ZhiXiaoErApi zxeapi) =>
            {
                await zxeapi.DeletePostRelation();
            })
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "清空关系表中职多多数据库" });

            group.MapPost("/wbjobrelationdelete", async (WbMoFangApi wbapi) =>
            {
                await wbapi.DeleteWbPostRelation();
            })
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "清空关系表中58魔方数据库" });

            group.MapPost("/wbjobtemdelete", async (WbMoFangApi wbapi) =>
            {
                await wbapi.DeleteWbMofangJobListTemp();
            })
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "清空58魔方临时表数据" });

            group.MapPost("/wbjoblist/{page}", async (int page, WbMoFangApi wbapi) =>
            {
                await wbapi.GetWbMoFangJobList(page);
            })
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "58魔方上线审核接口" });

            group.MapPost("/wbjobdown", async (WbMoFangApi wbapi) =>
            {
                await wbapi.DownWbMoFangPost();
            })
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "58魔方下架岗位接口" });

            group.MapPost("/asyncjobs", async (ZPYunRepository repository) =>
            {
                await repository.AsyncJobInfo("353422981894092677");
            })
.WithMetadata(new ApiMeta())
.WithOpenApi(operation => new(operation) { Summary = "云生发布/更新/下架职位接口" });


            group.MapPost("/robotconfig", async (ZPYunRepository repository) =>
            {
                await repository.UpdateRobotConfig("yVcO-UfzNsg");
            })
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "云生更新机器人配置" });

            group.MapPost("/addresumes/{userid}", async (string userid, ZPYunRepository repository) =>
            {
                await repository.AsyncResumeInfo(userid);
            })
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "云生新增简历接口" });

            group.MapGet("/addresumes/batch", async (ZPYunRepository repository) =>
            {
                await repository.UploadAllResumes();
                //MyRedis.Client.SAdd(SubscriptionKey.UpdateYunshengPlatform, seekerIds);
            })
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "云生批量新增简历接口" });

            group.MapPost("/newchat", async (ZPYunRepository repository) =>
            {
                await repository.NewChat("yVcO-UfzNsg", "_xDyWiCfk4g");
            })
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "创建聊天会话接口" });

            group.MapPost("/chatmessage", async (ZPYunRepository repository) =>
            {
                ChatMessage chat = new ChatMessage();
                chat.openId = "105060";
                chat.chatId = "-tst_OYFp30";
                chat.messages = new List<chatInfo>()
            {
                new chatInfo(){
                    role = "user",
                    type = 1,
                    content = "你好",
                    messageTime = DateTime.UtcNow.ToUnixTimeMs()
                }
            };
                await repository.GetChatMessage(chat);
            })
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "获取会话回复接口" });

            group.MapPost("/chatmessagehistory", async (ZPYunRepository repository) =>
            {
                GetChatMessageHistory history = new GetChatMessageHistory()
                {
                    openId = "105060",
                    chatId = "-tst_OYFp30",
                    startTime = DateTime.UtcNow.AddDays(-1).ToUnixTimeMs(),
                    endTime = DateTime.UtcNow.ToUnixTimeMs(),
                    pageNo = 1,
                    pageSize = 10
                };

                await repository.GetMessageHistory(history);
            })
            .WithMetadata(new ApiMeta())
            .WithOpenApi(operation => new(operation) { Summary = "创建聊天会话接口" });
        }
    }
}
