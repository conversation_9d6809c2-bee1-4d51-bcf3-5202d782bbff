using Infrastructure.Extend;
using Staffing.Model.Hr.Bounty;
using Staffing.Core.Interfaces.Hr;
using Staffing.Model.Hr.Project;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class BountyRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("bounty").WithTags("结算");
        group.MapPost("/ConfirmSettlement", async (ConfirmSettlementReq request, IBountyService rep) => await rep.ConfirmSettlement(request))
            .WithOpenApi(operation => new(operation) { Summary = "确认结算" });

        group.MapPost("/ApprovalSettlement", async (ApprovalSettlementReq request, IBountyService rep) => await rep.ApprovalSettlement(request))
            .WithOpenApi(operation => new(operation) { Summary = "审批结算" });

        group.MapPost("/getTransactionList", (TransactionListReq request, IBountyService rep) => rep.GetTransactionList(request))
            .WithOpenApi(operation => new(operation) { Summary = "项目订单列表" });

        group.MapPost("/getProjectFundsList", (ProjectFundsListReq request, IBountyService rep) => rep.GetProjectFundsList(request))
            .WithOpenApi(operation => new(operation) { Summary = "项目充值明细列表" });

        group.MapPost("/orderlist", (NewTeamBountyRequest request, IBountyService rep) => rep.GetOrderPageList(request))
            .WithOpenApi(operation => new(operation) { Summary = "订单列表" });

        group.MapPost("/center/orderlist", (CenterOrderListRequest request, IBountyService rep) => rep.GetCenterOrderPageList(request))
            .WithOpenApi(operation => new(operation) { Summary = "结算中心 - 交付订单列表" });

        group.MapGet("/center/companysum", (IBountyService rep) => rep.GetCenterCompanyOrderSumData())
            .WithOpenApi(operation => new(operation) { Summary = "结算中心 - 公司资产汇总" });

        group.MapGet("/center/personalsum", (IBountyService rep) => rep.GetCenterPersonalOrderSumData())
            .WithOpenApi(operation => new(operation) { Summary = "结算中心 - 个人收益汇总" });

        group.MapPost("/center/companylist", (OrderListReqeust request, IBountyService rep) => rep.GetCenterCompanyOrderPageList(request))
            .WithOpenApi(operation => new(operation) { Summary = "结算中心 - 公司资产列表" });

        group.MapPost("/center/personallist", (OrderListReqeust request, IBountyService rep) => rep.GetCenterPersonalOrderPageList(request))
            .WithOpenApi(operation => new(operation) { Summary = "结算中心 - 个人收益列表" });

        group.MapPost("/center/detail", (OrderDetailRequst request, IBountyService rep) => rep.GetOrderDetial(request))
            .WithOpenApi(operation => new(operation) { Summary = "结算中心 - 查看明细列表" });

        group.MapGet("/center/getuser", (IBountyService rep) => rep.GetDeliveryUsers())
            .WithOpenApi(operation => new(operation) { Summary = "结算中心 - 交付人" });

        group.MapPost("/bounty/accounting", (BountyAccountingRequst request, IBountyService rep) => rep.GetAccountingList(request))
            .WithOpenApi(operation => new(operation) { Summary = "计费审核中心" });

        group.MapPost("/bounty/ratio", (ReqModel request, IBountyService rep) => rep.GetBountyRatioList(request))
            .WithOpenApi(operation => new(operation) { Summary = "获取分佣金额" });

        group.MapPost("/bounty/computeratio", async (ReqModel request, IBountyService rep) => await rep.ComputeBountyRatioList(request))
            .WithOpenApi(operation => new(operation) { Summary = "计算分佣金额" });

        group.MapPost("/bounty/settlementinfo", (ReqModel request, IBountyService rep) => rep.GetSettlementInfo(request))
            .WithOpenApi(operation => new(operation) { Summary = "结算单信息" });

        //group.MapPost("/bounty/postprofitpayments", (ReqModel request, IBountyService rep) => rep.GetPostProfitPayments(request))
        //    .WithOpenApi(operation => new(operation) { Summary = "岗位分润支付信息" });

        group.MapPost("/bounty/settlementhistorylist", (ReqModel request, IBountyService rep) => rep.GetSettlementHistoryList(request))
            .WithOpenApi(operation => new(operation) { Summary = "历史结算单列表" });

        group.MapPost("/bounty/getcontractlistbysettlementid", (ReqModel request, IBountyService rep) => rep.GetProjectContractListBySettlementId(request))
            .WithOpenApi(operation => new(operation) { Summary = "获取项目合同列表" });

        group.MapPost("/bounty/settlementdetail", (ReqModel request, IBountyService rep) => rep.GetSettlementHistoryDetail(request))
            .WithOpenApi(operation => new(operation) { Summary = "结算单详情" });

        group.MapPost("/bounty/deliveryreceipt", (DetailRequestModel request, IBountyService rep) => rep.DeliveryReceipt(request))
            .WithOpenApi(operation => new(operation) { Summary = "交付凭证" });

        group.MapGet("/project/summary", ([AsParameters] GetProjectRequest model, IBountyProjectService rep) => rep.GetProjectSummary(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 项目详情简要信息" });

        group.MapGet("/project/info", async ([AsParameters] GetProjectRequest model, IBountyProjectService rep) => await rep.GetProject(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 项目详情" });

        group.MapGet("/project/flowinfo", ([AsParameters] GetFlowInfo model, IBountyProjectService rep) => rep.GetFlowInfo(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 项目详情-流程通知" });

        group.MapGet("/project/invoiceoverview", async ([AsParameters] GetProjectRequest model, IBountyProjectService rep) => await rep.GetInvoiceOverview(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 开票概览" });

        group.MapPut("/project/book/update", async (UpdateBook model, IBountyProjectService rep) => await rep.UpdateBook(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 更新帐套信息" });

        group.MapGet("/project/invoicelist", ([AsParameters] GetInvoiceList model, IBountyProjectService rep) => rep.GetInvoiceList(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 开票记录" });

        group.MapPost("/project/invoice", async (SaveInvoice model, IBountyProjectService rep) => await rep.SaveInvoice(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 开发票" });

        group.MapGet("/project/transferlist", ([AsParameters] GetTransferList model, IBountyProjectService rep) => rep.GetTransferList(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 转账报销列表" });

        group.MapPost("/project/transfer", async (SaveTransfer model, IBountyProjectService rep) => await rep.SaveTransfer(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 申请转账" });

        group.MapGet("/project/servicebonus/batch", ([AsParameters] GetServiceBonusBatch model, IBountyProjectService rep) => rep.GetServiceBonusBatch(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 服务奖金批次列表" });

        group.MapGet("/project/servicebonus/list", ([AsParameters] GetServiceBonusList model, IBountyProjectService rep) => rep.GetServiceBonusList(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 服务奖金列表" });

        group.MapPost("/project/servicebonus", async (SaveServiceBonus model, IBountyProjectService rep) => await rep.SaveServiceBonus(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 发放服务奖金" });

        group.MapGet("/project/fpage/sum", (IBountyProjectService rep) => rep.GetProjectSumData())
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 首页汇总" });

        group.MapPost("/project/fpage/todolist", (TodoListReqeust request, IBountyProjectService rep) => rep.GetTodoList(request))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 首页待办列表" });

        group.MapGet("/project/fpage/rank", (IBountyProjectService rep) => rep.GetRankList())
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 首页成单排行榜" });

        group.MapGet("/project/project/createdetail/{id}", (string id, IBountyProjectService rep) => rep.GetProjectDetail(id))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 获取创建项目明细" });

        group.MapPost("/project/project/save", (ProjectSaveRequest request, IBountyProjectService rep) => rep.SaveProject(request))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 创建项目提交" });

        group.MapPost("/project/project/list", (ProjectListRequest request, IBountyProjectService rep) => rep.GetProjectList(request))
            .WithOpenApi(operation => new(operation) { Summary = "诺亚项目管理 - 获取项目列表" });


    }
}