﻿using Config;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using ServiceStack.Text;
using Staffing.Entity;

namespace Staffing.Worker.Service;

/// <summary>
/// 统计类
/// </summary>
[Service(ServiceLifetime.Transient)]
public class DataBoardService
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _logger;
    private ConfigManager _config;
    private readonly TencentImHelper _tencentImHelper;
    private readonly EsHelper _esHelper;
    private readonly CommonDashboardService _commonDashboardService;

    /// <summary>
    /// 注入
    /// </summary>
    public DataBoardService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger, IOptionsSnapshot<ConfigManager> config,
    TencentImHelper tencentImHelper, EsHelper esHelper, CommonDashboardService commonDashboardService)
    {
        _contextFactory = contextFactory;
        _logger = logger;
        _config = config.Value;
        _tencentImHelper = tencentImHelper;
        _esHelper = esHelper;
        _commonDashboardService = commonDashboardService;
    }

    /// <summary>
    /// 统计钉钉推送消息记录
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task StatisticsDingDingMessage(CancellationToken cancel)
    {
        if (cancel.IsCancellationRequested)
            return;

        var today = DateTime.Today;
        if (DateTime.Now <= today.AddHours(1) || DateTime.Now >= today.AddHours(3))
            return;

        //redis缓存
        string redisKey = "dingding:message" + DateTime.Now.ToString("yyyyMMdd");
        var redisExit = MyRedis.Client.SetNx(redisKey, "1", 86400);
        if (!redisExit)
        {
            return;
        }

        using var _context = _contextFactory.CreateDbContext();

        var pushModel = _context.User_DingDing.Where(o => o.IsPush == true).Select(o => new
        {
            o.UserId,
            o.DingUserid,
            o.DingJobNo,
            TencentImId = o.User_Hr.TencentImId ?? ""
        }).ToList();

        List<Entity.Staffing.Msg_DingDingPush> addList = new List<Entity.Staffing.Msg_DingDingPush>();

        foreach (var i in pushModel)
        {
            try
            {
                Entity.Staffing.Msg_DingDingPush addModel = new Entity.Staffing.Msg_DingDingPush() { DingUserid = i.DingUserid!, UserId = i.UserId };

                #region 项目统计
                addModel.CreateProject = _context.Project.Where(o => o.HrId == i.UserId && o.Status == Config.Enums.ProjectStatus.已上线).Count();//创建项目(进行中）
                addModel.EnableCollaboration = _context.Project.Where(o => o.HrId == i.UserId && o.Status != Config.Enums.ProjectStatus.归档).Count();//开启协同（集团级）
                addModel.CollaborativeProject = _context.Project.Where(o => o.HrId == i.UserId && o.Status == Config.Enums.ProjectStatus.已上线).Count();//协同项目
                addModel.RegistrablePositions = _context.Post_Team.Where(o => o.Show == true && o.Project_Team.HrId == i.UserId).Count();//可报名职位
                #endregion

                #region 待处理
                addModel.ScreeningNum = _context.Recruit.Where(o => o.HrId == i.UserId && o.Status == Config.Enums.RecruitStatus.HrScreening).Count();//初筛
                addModel.InterviewNum = _context.Recruit.Where(o => o.HrId == i.UserId && o.Status == Config.Enums.RecruitStatus.Interview).Count();//面试
                if (!string.IsNullOrEmpty(i.TencentImId))
                {
                    try
                    {
                        addModel.MessageNoReply = (await _tencentImHelper.GetC2CUnreadMsgNum(i.TencentImId))?.AllC2CUnreadMsgNum ?? 0;
                    }
                    catch
                    {

                    }
                }
                #endregion

                #region 协同交付
                addModel.CoordinationDelivery = _context.Post_Bounty.Where(o => o.TeamHrId == i.UserId && o.Status == Config.Enums.BountyStatus.交付中).Count();//协同交付中
                addModel.CoordinationSuccess = _context.Post_Bounty.Where(o => o.TeamHrId == i.UserId && o.Status == Config.Enums.BountyStatus.交付中 && o.GuaranteeStatus == Config.Enums.GuaranteeStatus.过保).Count(); ;//协同交付成功
                addModel.CoordinationFail = _context.Post_Bounty.Where(o => o.TeamHrId == i.UserId && o.Status == Config.Enums.BountyStatus.结束 && o.GuaranteeStatus == Config.Enums.GuaranteeStatus.未过保).Count(); ;//协同交付失败
                #endregion

                #region 人才储备
                addModel.ReserveVirtual = _context.Talent_Virtual.Where(o => o.HrId == i.UserId).Count();//简历储备
                addModel.ReservePlatform = _context.Talent_Platform.Where(o => o.HrId == i.UserId && o.Deleted == false).Count();//真实用户
                #endregion

                #region 销帮帮数字诺亚数据统计
                #endregion

                addList.Add(addModel);

                await Task.Delay(50);
            }
            catch (Exception ex)
            {
                _logger.Error("统计钉钉推送消息记录出错", ex.Message, JsonSerializer.SerializeToString(i));
            }
        }

        _context.AddRange(addList);

        _context.SaveChanges();
    }

    /// <summary>
    /// 统计数据拓扑大屏
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task StatisticsTopology(CancellationToken cancel)
    {
        if (cancel.IsCancellationRequested)
            return;

        try
        {
            //redis缓存
            string redisKey = "StatisticsTopology";
            var redisExit = MyRedis.Client.SetNx(redisKey, "1", 3600 * 2);
            if (!redisExit)
                return;

            //从2022年1月1日开始统计，统计至今所有月份的数据，每个月的单独统计
            var start = new DateTime(2022, 1, 1);
            // var start = new DateTime(2024, 5, 1);
            var end = DateTime.Now;
            var months = (end.Year - start.Year) * 12 + end.Month - start.Month;

            //提前统计每个月的数据
            for (int i = 0; i <= months; i++)
            {
                var month = start.AddMonths(i);
                var nextMonth = month.AddMonths(1).AddDays(-1);
                var topology = _commonDashboardService.GetTopology(month, nextMonth);
                MyRedis.Client.HSet(RedisKey.DataTopology.DashBoradKey, Tools.DateRangeToString(month, nextMonth), topology);

                var topologyTalent = _commonDashboardService.GetTopologyTalent(month, nextMonth);
                MyRedis.Client.HSet(RedisKey.DataTopology.TopologyTalent, Tools.DateRangeToString(month, nextMonth), topologyTalent);

                var topologyProject = _commonDashboardService.GetTopologyProject(month, nextMonth);
                MyRedis.Client.HSet(RedisKey.DataTopology.TopologyProject, Tools.DateRangeToString(month, nextMonth), topologyProject);

                await Task.Delay(50);
            }

            //从2022年1月1日开始统计，统计至今所有年的数据，每年的单独统计
            var startYear = new DateTime(2022, 1, 1);
            var endYear = DateTime.Now;
            var years = endYear.Year - startYear.Year;

            //提前统计每年的数据
            for (int i = 0; i <= years; i++)
            {
                var year = startYear.AddYears(i);
                var nextYear = year.AddYears(1).AddDays(-1);
                var topology = _commonDashboardService.GetTopology(year, nextYear);
                MyRedis.Client.HSet(RedisKey.DataTopology.DashBoradKey, Tools.DateRangeToString(year, nextYear), topology);

                var topologyTalent = _commonDashboardService.GetTopologyTalent(year, nextYear);
                MyRedis.Client.HSet(RedisKey.DataTopology.TopologyTalent, Tools.DateRangeToString(year, nextYear), topologyTalent);

                var topologyProject = _commonDashboardService.GetTopologyProject(year, nextYear);
                MyRedis.Client.HSet(RedisKey.DataTopology.TopologyProject, Tools.DateRangeToString(year, nextYear), topologyProject);

                await Task.Delay(50);
            }

            //从2022年1月1日开始统计，统计至今所有季度的数据，每个季度的单独统计
            var startQuarter = new DateTime(2022, 1, 1);
            var endQuarter = DateTime.Now;
            var quarters = (endQuarter.Year - startQuarter.Year) * 4 + (endQuarter.Month - 1) / 3 - (startQuarter.Month - 1) / 3;

            //提前统计每个季度的数据
            for (int i = 0; i <= quarters; i++)
            {
                var quarter = startQuarter.AddMonths(i * 3);
                var nextQuarter = quarter.AddMonths(3).AddDays(-1);
                var topology = _commonDashboardService.GetTopology(quarter, nextQuarter);
                MyRedis.Client.HSet(RedisKey.DataTopology.DashBoradKey, Tools.DateRangeToString(quarter, nextQuarter), topology);

                var topologyTalent = _commonDashboardService.GetTopologyTalent(quarter, nextQuarter);
                MyRedis.Client.HSet(RedisKey.DataTopology.TopologyTalent, Tools.DateRangeToString(quarter, nextQuarter), topologyTalent);

                var topologyProject = _commonDashboardService.GetTopologyProject(quarter, nextQuarter);
                MyRedis.Client.HSet(RedisKey.DataTopology.TopologyProject, Tools.DateRangeToString(quarter, nextQuarter), topologyProject);

                await Task.Delay(50);
            }
        }
        catch (Exception ex)
        {
            _logger.Error("统计数据拓扑大屏出错", ex.Message, Tools.GetErrMsg(ex));
        }
    }
}
