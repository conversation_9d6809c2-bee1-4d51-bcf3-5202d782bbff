﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 协同项目众包配置表
/// </summary>
[Table("project_team_config")]
public class Project_Team_Config
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public String Id { get; set; } = EntityTools.SnowflakeId();
    
    // /// <summary>
    // /// 协调项目表id
    // /// </summary>
    // public string TeamProjectId { get; set; } = default!;
    //
    // /// <summary>
    // /// 协同岗位id
    // /// </summary>
    // public string TeamPostId { get; set; } = default!;
    // /// <summary>
    // /// 原始岗位id
    // /// </summary>
    // public string PostId { get; set; } = default!;
    //
    // /// <summary>
    // /// 用户Id
    // /// </summary>
    // public string HrId { get; set; } = default!;
    //
    // /// <summary>
    // /// 原始项目Id
    // /// </summary>
    // public string ProjectId { get; set; } = default!;
    

    /// <summary>
    /// 众包类别
    /// </summary>
    public ProjectTeamConfigType ModeType { get; set; }
    
    /// <summary>
    /// 接收人id
    /// </summary>
    public string? ReceiverId { get; set; }
    
    /// <summary>
    /// 创建人
    /// </summary>
    public string Creator { get; set; } = default!;
    /// <summary>
    /// 状态0开启 1关闭
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
    
    //public Project_Team ProjectTeam { get; set; } = default!;
    
}