﻿using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;

namespace Infrastructure.DingTalk;

[Service(ServiceLifetime.Singleton)]
public class DingTalkTokenService
{
    private readonly CacheHelper _cacheHelper;

    public DingTalkTokenService(CacheHelper cacheHelper)
    {
        _cacheHelper = cacheHelper;
    }

    /// <summary>
    /// 初始化Client
    /// </summary>
    /// <returns></returns>
    public static AlibabaCloud.SDK.Dingtalkoauth2_1_0.Client CreateClient()
    {
        AlibabaCloud.OpenApiClient.Models.Config config = new AlibabaCloud.OpenApiClient.Models.Config();
        config.Protocol = "https";
        config.RegionId = "central";
        return new AlibabaCloud.SDK.Dingtalkoauth2_1_0.Client(config);
    }

    /// <summary>
    /// 获取Token 
    /// access token的有效期为7200秒(2小时)，有效期内重复获取会返回相同结果并自动续期，过期后获取会返回新的access token。
    /// 不能频繁调用gettoken接口，否则会受到频率拦截
    /// </summary>
    /// <returns></returns>
    public string GetToken(string AppKey, string AppSecret)
    {
        string token = string.Empty;
        // 获取缓存 todo:
        token = _cacheHelper.GetRedisCache<string>(() =>
        {
            // 获取dingtalktoken
            AlibabaCloud.SDK.Dingtalkoauth2_1_0.Client client = DingTalkTokenService.CreateClient();
            AlibabaCloud.SDK.Dingtalkoauth2_1_0.Models.GetAccessTokenRequest getAccessTokenRequest = new AlibabaCloud.SDK.Dingtalkoauth2_1_0.Models.GetAccessTokenRequest
            {
                AppKey = AppKey,
                AppSecret = AppSecret,
            };
            var response = client.GetAccessToken(getAccessTokenRequest);
            return response.Body.AccessToken;
        }, $"st:dingauthtoken:{AppKey}", 3600);

        return token;
    }
}
