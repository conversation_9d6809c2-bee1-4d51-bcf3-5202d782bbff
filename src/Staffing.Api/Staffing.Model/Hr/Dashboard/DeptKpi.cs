namespace Staffing.Model.Hr.Dashboard;

public class GetDeptKpi
{
    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? BeginDate { get; set; }

    /// <summary>
    /// 截止日期
    /// </summary>
    public DateTime? EndDate { get; set; }

    public string? Key { get; set; }
}

public class GetDeptKpiResponse
{
    public GetNkpDeptKpiNkp? Nkp { get; set; }

    public GetNuoPinDeptKpiNkp? Np { get; set; }
}

public class GetNkpDeptKpiNkp
{
    /// <summary>
    /// B端新增人数
    /// </summary>
    public int? NewB { get; set; }

    /// <summary>
    /// B端使用人数
    /// </summary>
    public int? UseB { get; set; }

    /// <summary>
    /// c端人数
    /// </summary>
    public int? UserC { get; set; }

    /// <summary>
    /// c端累计人数
    /// </summary>
    public int? UserCt { get; set; }

    /// <summary>
    /// 招聘人数
    /// </summary>
    public int? ZhaoPinNum { get; set; }

    /// <summary>
    /// 投递人数
    /// </summary>
    public int? TouDiNum { get; set; }

    /// <summary>
    /// 交付满足率(投递)
    /// </summary>
    public string? JFLv { get; set; }

    /// <summary>
    /// 人才库数量（只查B端使用人数对应的数量）
    /// </summary>
    public int? RenCaiNum { get; set; }

    /// <summary>
    /// 人均人才储备
    /// </summary>
    public int? RenCaiPer { get; set; }

    /// <summary>
    /// b端活跃度
    /// </summary>
    public string? BHyLv { get; set; }
}

public class GetNuoPinDeptKpiNkp
{
    /// <summary>
    /// B端新增人数
    /// </summary>
    public int? NewB { get; set; }

    /// <summary>
    /// c端人数
    /// </summary>
    public int? UserC { get; set; }

    /// <summary>
    /// c端累计人数
    /// </summary>
    public int? UserCt { get; set; }
}