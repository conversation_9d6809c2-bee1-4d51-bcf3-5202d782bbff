﻿using Config.CommonModel;
using Staffing.Entity.Staffing;
using System.ComponentModel;

namespace Staffing.Model.Hr.WxGroup;

internal class WxGroupDTO
{
}

public class SearchRequest : QueryRequest
{
    /// <summary>
    /// 群类型 - 不传就是查全部
    /// </summary>
    public GroupType? GroupType { get; set; }

    /// <summary>
    /// 区域Id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 行业Id
    /// </summary>
    public string? CategoryId { get; set; }

    /// <summary>
    /// 人群
    /// </summary>
    public NumType? NumType { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public UpdateType? UpdateType { get; set; }

    /// <summary>
    /// 关键字
    /// </summary>
    public string? Search { get; set; }
}

/// <summary>
/// 表单数据
/// </summary>
public class SummaryInfoModel
{
    /// <summary>
    /// 顾问名称
    /// </summary>
    public string HrName { get; set; } = default!;

    /// <summary>
    /// 顾问查看群数量
    /// </summary>
    public int HrGroupCount { get; set; }

    /// <summary>
    /// 今日访问总数
    /// </summary>
    public long Sum { get; set; }

    /// <summary>
    /// 群总量
    /// </summary>
    public int GroupSum { get; set; }

    /// <summary>
    /// 微信群数量
    /// </summary>
    public int WxGroupCount { get; set; }

    /// <summary>
    /// 企业微信数量
    /// </summary>
    public int QyWxGroupCount { get; set; }
}

/// <summary>
/// 列表数据
/// </summary>
public class ListResponse : QueryResponse
{
    public List<ListInfoModel> Rows { get; set; } = default!;
}

/// <summary>
/// 列表数据模型
/// </summary>
public class ListInfoModel
{
    /// <summary>
    /// 主键
    /// </summary>
    public long GroupId { get; set; } = default!;
    /// <summary>
    /// 群名称
    /// </summary>
    public string GroupName { get; set;} = default!;

    /// <summary>
    /// 群类型
    /// </summary>
    public string GroupType { get; set;} = default!;

    /// <summary>
    /// 进群率
    /// </summary>
    public string GroupRate { get; set;} = default!;

    /// <summary>
    /// 检测时间
    /// </summary>
    public DateTime FindTime { get; set;}

    /// <summary>
    /// 时间戳
    /// </summary>
    public long Time { get; set; }

    /// <summary>
    /// 群人数
    /// </summary>
    public int GroupNum { get; set;} = default!;

    /// <summary>
    /// 查看次数
    /// </summary>
    public int ViewNum { get; set;} = default!;

    /// <summary>
    /// 当前顾问查看情况
    /// </summary>
    public bool IsView { get; set;} = false;

    /// <summary>
    /// 二维码url
    /// </summary>
    public string Url { get; set;} = default!;
}

public enum NumType
{
    [Description("20人以内")]
    二十人以内,
    [Description("大于20人")]
    大于二十人 = 20,
    [Description("大于50人")]
    大于五十人 = 50,
    [Description("大于100人")]
    大于一百人 = 100
}

public enum UpdateType
{
    [Description("近24小时")]
    OneDay = 24,
    [Description("近48小时")]
    TwoDays = 48,
    [Description("近72小时")]
    ThreeDays = 72,
    [Description("近7天")]
    SevenDays = 168
}

public enum DataType
{
    地区,行业
}