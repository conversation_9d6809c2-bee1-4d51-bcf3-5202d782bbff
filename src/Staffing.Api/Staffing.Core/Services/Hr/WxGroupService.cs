﻿using Config;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Infrastructure.Proxy;
using LinqKit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json.Linq;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using Staffing.Model.Hr.WxGroup;
using Staffing.Core.Interfaces.Hr;

namespace Staffing.Core.Services.Service.Hr;

[Service(ServiceLifetime.Transient)]
public class WxGroupService : IWxGroupService
{
    private readonly StaffingContext _context;
    private RequestContext _user;
    private readonly CommonDicService _commonDicService;
    private readonly EsHelper _esHelper;
    private readonly IHrService _hrService;
    private readonly IHostEnvironment _hostingEnvironment;
    public WxGroupService(StaffingContext context, RequestContext user, CommonDicService commonDicService,
    EsHelper esHelper, IHrService hrService, IHostEnvironment hostingEnvironment)
    {
        _context = context;
        _user = user;
        _commonDicService = commonDicService;
        _esHelper = esHelper;
        _hrService = hrService;
        _hostingEnvironment = hostingEnvironment;
    }

    public EmptyResponse ViewGroup(long groupId)
    {
        var view = new Wx_Group_View
        {
            GroupId = groupId,
            HrId = _user.Id,
            CreatedTime = DateTime.Now
        };

        _context.Add(view);
        _context.SaveChanges();

        return new EmptyResponse();
    }

    public ListResponse GetWxGroupList(SearchRequest request)
    {
        var response = new ListResponse();
        long timeSeconds = DateTimeOffset.Now.AddDays(-7).ToUnixTimeSeconds();
        var predicate = PredicateBuilder.New<Wx_Group>(p => true);

        if (_hostingEnvironment.IsProduction())
            predicate = PredicateBuilder.New<Wx_Group>(p => p.FindTime >= timeSeconds);// 取7天内数据

        if (!string.IsNullOrWhiteSpace(request.RegionId))
        {
            var names = GetAllNames(request.RegionId);
            var regionPredicate = PredicateBuilder.New<Wx_Group>(true);
            foreach (var name in names.Distinct())
            {
                regionPredicate = regionPredicate.Or(r => r.GroupName.Contains(name));
            }
            predicate = predicate.And(regionPredicate);
        }

        if (!string.IsNullOrWhiteSpace(request.CategoryId))
        {
            var names = GetAllNames(request.CategoryId);
            var categoryPredicate = PredicateBuilder.New<Wx_Group>(true);
            foreach (var name in names.Distinct())
            {
                categoryPredicate = categoryPredicate.Or(r => r.GroupName.Contains(name));
            }
            predicate = predicate.And(categoryPredicate);
        }

        if (request.NumType.HasValue)
        {
            if (request.NumType == NumType.二十人以内)
                predicate = predicate.And(p => p.GroupNumber <= 20);
            else
                predicate = predicate.And(p => p.GroupNumber > (int)request.NumType);
        }

        if (request.GroupType != null && request.GroupType.HasValue)
        {
            predicate = predicate.And(p => p.GroupType == request.GroupType);
        }

        if (request.UpdateType != null && request.UpdateType.HasValue)
        {
            long ts = DateTimeOffset.Now.AddHours(-(int)request.UpdateType).ToUnixTimeSeconds();
            predicate = predicate.And(p => p.FindTime >= ts);
        }

        if (!string.IsNullOrWhiteSpace(request.Search))
        {
            predicate = predicate.And(p => p.GroupName.Contains(request.Search));
        }

        var sql = _context.Wx_Group.Where(predicate);
        response.Total = sql.Count();

        response.Rows = sql
        .OrderByDescending(o => o.FindTime)
        .Skip((request.PageIndex - 1) * request.PageSize)
        .Take(request.PageSize)
        .Select(s => new ListInfoModel
        {
            GroupId = s.Id,
            GroupName = s.GroupName,
            GroupType = s.GroupType.GetDescription(),
            Time = s.FindTime,
            GroupNum = s.GroupNumber,
            Url = s.QRCode ?? string.Empty
        }).ToList();

        // 数据处理
        response.Rows.ForEach(row =>
        {
            row.FindTime = DateTimeOffset.FromUnixTimeSeconds(row.Time).LocalDateTime;// 北京时间
            var viewInfo = _context.Wx_Group_View.Where(w => w.GroupId == row.GroupId).ToList();
            row.ViewNum = viewInfo.Count;
            row.IsView = viewInfo.Any(w => w.HrId == _user.Id);
            TimeSpan ts = DateTime.Now - row.FindTime;
            if (ts.TotalMinutes <= 60) // 1小时内
                row.GroupRate = "进群率 ≈ 98%~100%";
            else if (ts.TotalMinutes <= 120) // 2小时内
                row.GroupRate = "进群率 ≥ 95%";
            else if (ts.TotalMinutes <= 360) // 6小时内
                row.GroupRate = "进群率 ≥ 86%";
            else if (ts.TotalMinutes <= 1440) // 24小时内
                row.GroupRate = "进群率 ≥ 80%";
            else
                row.GroupRate = "进群率 ＜ 80%";
        });

        return response;
    }

    /// <summary>
    /// 获取所有配置名称
    /// </summary>
    /// <param name="configId"></param>
    /// <returns></returns>
    private List<string> GetAllNames(string configId)
    {
        var config = _context.Nkp_Config_Info.FirstOrDefault(f => f.Id == configId);
        var obj = new JObject { 
            { "Id", configId }, 
            { "Name", config?.Name }, 
            { "Tags", JToken.FromObject(config?.Tags ?? new List<string>())}, 
            { "Child", new JArray() } 
        };
        GetChilds(null, obj);

        var list = new List<string>();
        if (!string.IsNullOrWhiteSpace(obj["Name"] + "") && obj["Name"] + "" != "全部")
        {
            list.Add(obj["Name"] + ""); // 一级名称加上
        }

        if (obj["Tags"] + "" != "[]")
        {
            var jtags = JToken.FromObject(obj["Tags"]);
            var l = jtags.Value<JArray>(); // jtags.Value<List<string>>() 不能强转
            if (l != null && l.Count > 0)
            {
                list.AddRange(l.ToObject<List<string>>());// 一级标签加上
            }
        }

        var jt = JToken.FromObject(obj["Child"]);
        var jar = jt.Value<JArray>();

        if (jar != null && jar.Count > 0)
        {
            foreach (var item in jar)
            {
                var ob = item.Value<JObject>();
                if (ob["Name"] + "" != "全部")
                    list.Add(ob["Name"] + "");
                var tags = ob["Tags"]?.Value<JArray>();
                if(tags != null && tags.Count > 0)
                    list.AddRange(tags.ToObject<List<string>>());
            }
        }

        return list;
    }

    public SummaryInfoModel GetGroupSummary()
    {
        if (_user == null || string.IsNullOrWhiteSpace(_user.Id))
            throw new BadRequestException("未获取到身份信息");

        var userName = _context.User_Hr.Where(w => w.UserId == _user.Id).Select(s => s.NickName).FirstOrDefault();
        if (string.IsNullOrWhiteSpace(userName))
            throw new BadRequestException("未获取到顾问身份信息");

        // 获取今日访问总数
        var wxsummary = MyRedis.Client.HGet<WxSheQunSummyInfo>(RedisKey.WxSheQun.Key, RedisKey.WxSheQun.SummaryData);

        // 统计7天内的数量
        long timeSeconds = DateTimeOffset.Now.AddDays(-7).ToUnixTimeSeconds();
        int wxGroupCount_7 = _context.Wx_Group.Count(c => c.FindTime >= timeSeconds && c.GroupType == GroupType.微信群);
        int qyWxGroupCount_7 = _context.Wx_Group.Count(c => c.FindTime >= timeSeconds && c.GroupType == GroupType.企业微信群);
        var hrGroup = _context.Wx_Group_View
            .GroupBy(g => new { g.GroupId , g.HrId })
            .Select(s => new { s.Key.GroupId, s.Key.HrId, Count = s.Count() })
            .ToList();

        var hrSum = hrGroup?.Count();
        var currentHrGroupCount = hrGroup?.Where(w => w.HrId == _user.Id).Count();
        int groupNums = _context.Wx_Group.Count();
        return new SummaryInfoModel
        {
            HrName = userName,
            HrGroupCount = currentHrGroupCount ?? 0,
            Sum = wxsummary?.allcheck ?? 0,
            GroupSum = groupNums,
            WxGroupCount = wxGroupCount_7,
            QyWxGroupCount = qyWxGroupCount_7
        };
    }

    public dynamic GetTwoLevelDatas(DataType type)
    {
        string headId = string.Empty;
        var allDatas = _context.Nkp_Config_Info.OrderBy(o => o.Sort).ToList();
        if (type == DataType.地区)
        {
            headId = allDatas.Where(w => w.Name == "社群地区").Select(s => s.Id).FirstOrDefault() ?? string.Empty;
        }
        else if (type == DataType.行业)
        {
            headId = allDatas.Where(w => w.Name == "社群行业").Select(s => s.Id).FirstOrDefault() ?? string.Empty;
        }

        if (headId == string.Empty)
            return "";

        var level1Info = allDatas.Where(w => w.ParentId == headId && w.Deleted == 0).OrderBy(o => o.Sort).ToList();
        var jar = new JArray();
        foreach (var level in level1Info)
        {
            var obj = new JObject { 
                { "Id", level.Id }, 
                { "Name", level.Name },
                { "Tags", JToken.FromObject(level.Tags ?? new List<string>())},
                { "Child", new JArray()} 
            };
            GetChilds(allDatas, obj);
            jar.Add(obj);
        }
        return jar;
    }

    private void GetChilds(List<Nkp_Config_Info>? allDatas, JObject obj)
    {
        allDatas ??= _context.Nkp_Config_Info.OrderBy(o => o.Sort).ToList();

        string Id = obj["Id"] + "";
        var childs = allDatas.Where(w => w.ParentId == Id && w.Deleted == 0).ToList();

        if (childs.Count == 0)
        {
            return;
        }

        var jar = new JArray();
        foreach (var ch in childs)
        {
            var child = new JObject { 
                { "Id", ch.Id }, 
                { "Name", ch.Name },
                { "Tags", JToken.FromObject(ch.Tags ?? new List<string>())},
                { "Child", new JArray() } 
            };
            GetChilds(allDatas, child);
            jar.Add(child);
            obj["Child"] = jar;
        }
    }
}
