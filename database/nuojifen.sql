-- ----------------------------
-- 诺积分
-- ----------------------------
DROP TABLE IF EXISTS `user_nscore`;
CREATE TABLE `user_nscore` (
  `UserId` varchar(18) NOT NULL,
  `Score` int(11) NOT NULL COMMENT '当前诺积分',
  `ScoreTotal` int(11) NOT NULL COMMENT '历史累计诺积分',
  `SeekerScore` int(11) NOT NULL COMMENT '求职者当前诺积分',
  `SeekerScoreTotal` int(11) NOT NULL COMMENT '求职者历史累计诺积分',
  `SeekerMoney` decimal(18,2) NOT NULL COMMENT '求职者兑换现金',
  `SeekerPrize` int(11) NOT NULL COMMENT '求职者兑换奖品',
  `HrScore` int(11) NOT NULL COMMENT 'hr当前诺积分',
  `HrScoreTotal` int(11) NOT NULL COMMENT 'hr历史累计诺积分',
  `HrMoney` decimal(18,2) NOT NULL COMMENT '顾问兑换现金',
  `HrPrize` int(11) NOT NULL COMMENT '顾问兑换奖品',
  PRIMARY KEY (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


-- ----------------------------
-- 诺积分记录
-- ----------------------------
DROP TABLE IF EXISTS `user_nscore_record`;
CREATE TABLE `user_nscore_record` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL,
  `Amount` int(11) NOT NULL COMMENT '当前值',
  `Increment` int(11) NOT NULL COMMENT '增量',
  `UserType` int(2) NOT NULL COMMENT 'hr还是求职者',
  `Type` int(4) NOT NULL COMMENT '类型',
  `Content` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '记录内容',
  `Data` varchar(1024) DEFAULT NULL COMMENT '数据json',
  `EventTime` datetime(6) NOT NULL COMMENT '发生时间',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  KEY `IX_user_nscore_record_UserId` (`UserId`),
  KEY `IX_user_nscore_record_Type` (`Type`),
  KEY `IX_user_nscore_record_UserType` (`UserType`),
  KEY `IX_user_nscore_record_EventTime` (`EventTime` DESC),
  KEY `IX_user_nscore_record_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


-- ----------------------------
-- 提现
-- ----------------------------
DROP TABLE IF EXISTS `user_withdraw`;
CREATE TABLE `user_withdraw` (
  `Id` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL,
  `UserType` int(2) NOT NULL COMMENT 'hr还是求职者',
  `Type` int(11) NOT NULL COMMENT '提现类型',
  `Status` int(11) NOT NULL COMMENT '状态',
  `Amount` decimal(10,2) NOT NULL COMMENT '数值',
  `Account` varchar(50) DEFAULT NULL COMMENT '提现账户',
  `AccountName` varchar(50) DEFAULT NULL COMMENT '提现账户名称',
  `Remark` varchar(500) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '备注',
  `Approver` varchar(18) NULL,
  `ApprovalTime` datetime(6) DEFAULT NULL COMMENT '审批通过时间',
  `WithdrawTime` datetime(6) DEFAULT NULL COMMENT '提现时间',
  `CreatedTime` datetime(6) NOT NULL,
  PRIMARY KEY (`Id`),
  KEY `IX_user_withdraw_UserId` (`UserId`),
  KEY `IX_user_withdraw_Type` (`Type`),
  KEY `IX_user_withdraw_Status` (`Status`),
  KEY `IX_user_withdraw_WithdrawTime` (`WithdrawTime` DESC),
  KEY `IX_user_withdraw_CreatedTime` (`CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- ----------------------------
-- 诺积分商城
-- ----------------------------
DROP TABLE IF EXISTS `user_nscore_goods`;
CREATE TABLE `user_nscore_goods` (
  `Id` varchar(18) NOT NULL,
  `UserType` int(2) NOT NULL COMMENT 'hr还是求职者',
  `Type` int(11) NOT NULL COMMENT '类型',
  `Name` varchar(255) NOT NULL COMMENT '商品名称',
  `Describe` varchar(500) NULL COMMENT '描述',
  `Stock` int(11) NOT NULL COMMENT '库存',
  `Score` int(11) NOT NULL COMMENT '所需积分',
  `Money` decimal(18,2) NOT NULL COMMENT '商品价值',
  `Content` text NOT NULL COMMENT '内容',
  `UpdatedBy` varchar(64) NOT NULL COMMENT '更新人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `Active` bit NOT NULL COMMENT '激活',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


-- ----------------------------
-- 诺积分商城订单
-- ----------------------------
DROP TABLE IF EXISTS `user_nscore_order`;
CREATE TABLE `user_nscore_order` (
  `Id` varchar(18) NOT NULL,
  `GoodsId` varchar(18) NOT NULL,
  `UserId` varchar(18) NOT NULL,
  `Type` int(11) NOT NULL COMMENT '类型',
  `Status` int(11) NOT NULL,
  `Name` varchar(255) NOT NULL COMMENT '商品名称',
  `Score` int(11) NOT NULL COMMENT '所需积分',
  `Money` decimal(10,2) NOT NULL COMMENT '商品价值',
  `Address` varchar(4096) CHARACTER SET utf8mb4 NULL,
  `DeliveryTime` datetime(6) NULL COMMENT '发货时间',
  `Express` varchar(255) NOT NULL,
  `ExpressNo` varchar(255) NOT NULL,
  `UpdatedBy` varchar(18) NOT NULL COMMENT '更新人',
  `UpdatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_user_nscore_order_GoodsId` (`GoodsId`),
  KEY `IX_user_nscore_order_UserId` (`UserId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

ALTER TABLE post_team ADD TopTime datetime(6) NOT NULL DEFAULT '1970-1-1';
CREATE INDEX IX_post_team_TopTime ON post_team (TopTime desc);