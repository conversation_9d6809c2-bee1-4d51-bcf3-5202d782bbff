﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing
{
    /// <summary>
    /// 用户业务短信
    /// </summary>
    [Table("user_sms")]
    public class User_Sms
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [Key]
        public string UserId { get; set; } = default!;

        /// <summary>
        /// 业务短信数量
        /// </summary>
        [ConcurrencyCheck]
        public int BusinessSms { get; set; }
    }
}
