﻿using Config.CommonModel;
using Config.Enums;
using System.Text.Json.Serialization;
using Staffing.Model.Hr.Dashboard;

namespace Staffing.Model.Hr.Recruit;

#region 查询方法模型
/// <summary>
/// 详情返回模型
/// </summary>
public class GetRecruitResponse : GetRecruitInfo
{
}

/// <summary>
/// 招聘流程模型
/// </summary>
public class GetRecruitInfo
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// HrId
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// Hr姓名
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// hr头像
    /// </summary>
    public string? HrHeadPortrait { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// 用户头像
    /// </summary>
    public string? UserHeadPortrait { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public RecruitStatus Status { get; set; }

    /// <summary>
    /// 状态解释
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 状态变更时间
    /// </summary>
    public DateTime StatusTime { get; set; }

    /// <summary>
    /// 面试官筛选子类
    /// </summary>
    public RecruitDetailsInterviewerScreenSub? InterviewerScreenSub { get; set; }

    /// <summary>
    /// 面试子类
    /// </summary>
    public RecruitDetailsInterviewerSub? InterviewerSub { get; set; }

    /// <summary>
    /// Offer子类
    /// </summary>
    public RecruitDetailsOfferSub? OfferSub { get; set; }

    /// <summary>
    /// 归档子类
    /// </summary>
    public RecruitDetailsFileAwaySub? FileAwaySub { get; set; }

    /// <summary>
    /// 是否接单0 否 1是
    /// </summary>
    public RecruitAcceptOrderEnum? AcceptOrder { get; set; }
    /// <summary>
    /// 邀面人
    /// </summary>
    [JsonIgnore]
    public string? FollowerId { get; set; }

    /// <summary>
    /// 线索人
    /// </summary>
    [JsonIgnore]
    public string? TeamHrId { get; set; }

}

/// <summary>
/// 招聘流程详情面试官筛选子类
/// </summary>
public class RecruitDetailsInterviewerScreenSub
{
    /// <summary>
    /// 面试官名称
    /// </summary>
    public string? InterviewerName { get; set; }

    /// <summary>
    /// 面试官筛选反馈状态
    /// </summary>
    public RecruitInterviewerScreenStatus? InterviewerScreenStatus { get; set; }

    /// <summary>
    /// 面试官筛选反馈状态解释
    /// </summary>
    public string? InterviewerScreenStatusName { get; set; }

    /// <summary>
    /// 面试官筛选反馈内容
    /// </summary>
    public string? InterviewerScreenFeedBack { get; set; }

    /// <summary>
    ///  面试官筛选反馈时间
    /// </summary>
    public DateTime? FeedBackTime { get; set; }
}

/// <summary>
/// 招聘流程详情面试子类
/// </summary>
public class RecruitDetailsInterviewerSub
{
    /// <summary>
    /// 面试过程
    /// </summary>
    public RecruitInterviewProcess? InterviewProcess { get; set; }

    /// <summary>
    /// 面试时间
    /// </summary>
    public DateTime? InterviewTime { get; set; }

    /// <summary>
    /// 面试过程解释
    /// </summary>
    public string? InterviewProcessName { get; set; }

    /// <summary>
    /// 面试官名称
    /// </summary>
    public string? InterviewerName { get; set; }

    /// <summary>
    /// 面试官面试反馈结果
    /// </summary>
    public RecruitInterviewOutcome? InterviewOutcome { get; set; }

    /// <summary>
    /// 面试官面试反馈结果解释
    /// </summary>
    public string? InterviewOutcomeName { get; set; }

    /// <summary>
    /// 面试官面试反馈内容
    /// </summary>
    public string? InterviewerFeedBack { get; set; }

    /// <summary>
    /// 反馈时间
    /// </summary>
    public DateTime? FeedBackTime { get; set; }

    /// <summary>
    /// 取消面试用户名
    /// </summary>
    public string? CancelName { get; set; }
}

/// <summary>
/// 招聘流程详情Offer子类
/// </summary>
public class RecruitDetailsOfferSub
{
    /// <summary>
    /// 是否已通知
    /// </summary>
    public bool? IsNotice { get; set; }

    /// <summary>
    /// offer发放时间
    /// </summary>
    public DateTime? OfferTimet { get; set; }
}

/// <summary>
/// 招聘流程详情归档子类
/// </summary>
public class RecruitDetailsFileAwaySub
{
    /// <summary>
    /// 归档详情枚举
    /// </summary>
    public RecruitFileAway? RecruitFileAway { get; set; }

    /// <summary>
    /// 归档详情枚举解释
    /// </summary>
    public string? RecruitFileAwayName { get; set; }

    /// <summary>
    /// 归档备注
    /// </summary>
    public string? FileAwayRemarks { get; set; }

    /// <summary>
    /// 归档时间
    /// </summary>
    public DateTime? FileAwayTime { get; set; }
}
#endregion

#region 修改方法模型
/// <summary>
/// 修改招聘流程(面试官筛选)
/// </summary>
public class RequestEditRecruitInterviewerScreening
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 面试官id
    /// </summary>
    public string? InterviewerId { get; set; }

    /// <summary>
    /// 推荐理由
    /// </summary>
    public string Recommend { get; set; } = string.Empty;

    /// <summary>
    /// 是否跳过
    /// </summary>
    public bool IsSkip { get; set; } = false;
}

/// <summary>
/// 修改招聘流程(面试)
/// </summary>
public class RequestEditRecruitInterview
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 面试方式
    /// </summary>
    public RecruitInterviewForms InterviewForms { get; set; }

    /// <summary>
    /// 面试过程
    /// </summary>
    public RecruitInterviewProcess InterviewProcess { get; set; }

    /// <summary>
    /// 面试时间
    /// </summary>
    public DateTime InterviewTime { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 面试地点地址
    /// </summary>
    public string Address { get; set; } = string.Empty;
    /// <summary>
    /// 详细地址
    /// </summary>
    public string AddressDetail { get; set; } = default!;

    /// <summary>
    /// 面试地点纬度
    /// </summary>
    public decimal Lat { get; set; }

    /// <summary>
    /// 面试地点经度
    /// </summary>
    public decimal Lng { get; set; }

    /// <summary>
    /// 面试官Id
    /// </summary>
    public string? InterviewerId { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Mobile { get; set; } = string.Empty;

    /// <summary>
    /// 短信内容
    /// </summary>
    public string MobileContent { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱地址
    /// </summary>
    public string Mail { get; set; } = string.Empty;

    /// <summary>
    /// 邮件内容
    /// </summary>
    public string MailContent { get; set; } = string.Empty;

    /// <summary>
    /// 是否跳过
    /// </summary>
    public bool IsSkip { get; set; } = false;
}

/// <summary>
/// 修改招聘流程(Offer)
/// </summary>
public class RequestEditRecruitOffer
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 预期入职时间
    /// </summary>
    public DateTime InductionTime { get; set; }

    /// <summary>
    /// 入职薪资
    /// </summary>
    public int InductionSalary { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 入职地点地址
    /// </summary>
    public string? Address { get; set; } = string.Empty;

    /// <summary>
    /// 入职地点纬度
    /// </summary>
    public decimal Lat { get; set; }

    /// <summary>
    /// 入职地点经度
    /// </summary>
    public decimal Lng { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; } = string.Empty;

    /// <summary>
    /// 短信内容
    /// </summary>
    public string? MobileContent { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱地址
    /// </summary>
    public string? Mail { get; set; } = string.Empty;

    /// <summary>
    /// 邮件内容
    /// </summary>
    public string? MailContent { get; set; } = string.Empty;

    /// <summary>
    /// 是否开启通知
    /// </summary>
    public bool IsNotice { get; set; }

    /// <summary>
    /// 通知主体id
    /// </summary>
    public string? NoticeSubjectId { get; set; }

    /// <summary>
    /// 通知主体名称
    /// </summary>
    public string? NoticeSubjectName { get; set; }

    /// <summary>
    /// 通知书内容
    /// </summary>
    public string? NoticeContent { get; set; }

    /// <summary>
    /// 携带资料集合
    /// </summary>
    public List<string>? CarryInformation { get; set; }

    /// <summary>
    /// 是否跳过
    /// </summary>
    public bool IsSkip { get; set; } = false;
}

/// <summary>
/// 发送offer获取主体集合返回类
/// </summary>
public class SendRecruitOfferGetNoticeSubject
{
    public List<SendRecruitOfferSub>? Rows { get; set; }
}

/// <summary>
/// 发送offer获取主体集合
/// </summary>
public class SendRecruitOfferSub
{
    public string? Key { get; set; }

    public string? Value { get; set; }
}

/// <summary>
/// 修改招聘流程(入职)
/// </summary>
public class RequestEditRecruitInduction
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 入职时间
    /// </summary>
    public DateTime? InductionTime { get; set; }
}

/// <summary>
/// 修改招聘流程(归档)
/// </summary>
public class RequestEditRecruitFileAway
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 归档状态
    /// </summary>
    public RecruitFileAway RecruitFileAway { get; set; }

    /// <summary>
    /// 归档原因
    /// </summary>
    public string FileAwayReason { get; set; } = string.Empty;
}

/// <summary>
/// 修改招聘流程(无效简历)
/// </summary>
public class RequestEditRecruitInvalidResume
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 无效原因
    /// </summary>
    public string InvalidReason { get; set; } = string.Empty;

    /// <summary>
    /// 无效详情
    /// </summary>
    public string? InvalidDescribe { get; set; }
}

/// <summary>
/// 取消面试邀请请求模型
/// </summary>
public class CancelInterviewRequest
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 取消备注原因
    /// </summary>
    public string? CancelRemarks { get; set; }
}

/// <summary>
/// hr填写面试反馈请求模型
/// </summary>
public class InterviewFeedbackRequest
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 面试结果
    /// </summary>
    public RecruitInterviewOutcome Outcome { get; set; }

    /// <summary>
    /// 面试备注
    /// </summary>
    public string Remarks { get; set; } = string.Empty;
}

/// <summary>
/// hr填写面试官筛选反馈请求模型
/// </summary>
public class InterviewerScreenFeedbackRequest
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 面试结果
    /// </summary>
    public RecruitInterviewerScreenStatus Status { get; set; }

    /// <summary>
    /// 面试备注
    /// </summary>
    public string Remarks { get; set; } = string.Empty;
}
#endregion

#region 项目页面模型
/// <summary>
/// 面试官管理列表接收模型
/// </summary>
public class GetProjectInterviewerListRequest : QueryRequest
{
    /// <summary>
    /// 项目id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 搜索查询
    /// </summary>
    public string? Name { get; set; }
}

/// <summary>
/// 面试官管理列表返回模型
/// </summary>
public class GetProjectInterviewerListResponse : QueryResponse
{
    public List<GetProjectInterviewerSelect> Rows { get; set; } = default!;
}

/// <summary>
/// 面试官管理列表查询类
/// </summary>
public class GetProjectInterviewerSelect
{
    /// <summary>
    /// 主键id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 项目id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 面试官名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 手机号
    /// </summary>
    public string Phone { get; set; } = string.Empty;

    /// <summary>
    /// 职位
    /// </summary>
    public string Post { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string Mail { get; set; } = string.Empty;

    /// <summary>
    /// 办公地址
    /// </summary>
    public string OfficeAddress { get; set; } = string.Empty;
}

/// <summary>
/// 变更项目面试官信息（添加、修改）接收模型
/// </summary>
public class ChangeProjectInterviewerRequest
{
    /// <summary>
    /// 主键id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 项目id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 面试官名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 手机号
    /// </summary>
    public string Phone { get; set; } = string.Empty;

    /// <summary>
    /// 职位
    /// </summary>
    public string Post { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string Mail { get; set; } = string.Empty;

    /// <summary>
    /// 办公地址
    /// </summary>
    public string OfficeAddress { get; set; } = string.Empty;
}
#endregion

#region 招聘流程列表相关模型
/// <summary>
/// 查询当前用户是否有权限返回模型
/// </summary>
public class GetRecruitAuthorityResponse
{
    public bool IsOperable { get; set; }
}

/// <summary>
/// 获取项目职位返回模型
/// </summary>
public class GetRecruitProjectPostListResponse
{
    public List<RecruitProjectPostList>? Rows { get; set; }
}

/// <summary>
/// 项目职位集合
/// </summary>
public class RecruitProjectPostList
{
    public string? Id { get; set; }

    public string? Name { get; set; }
}

/// <summary>
/// 获取招聘流程列表接收模型
/// </summary>
public class RecruitListRequest : GetRecentRecruits
{
    /// <summary>
    /// 项目id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 岗位id
    /// </summary>
    public string? PostId { get; set; }

    /// <summary>
    /// 招聘流程状态
    /// </summary>
    public RecruitStatus? RecruitStatus { get; set; }

    #region hr初筛条件
    /// <summary>
    /// 标签id
    /// </summary>
    public string? LabelId { get; set; }
    #endregion

    #region 面试官筛选条件
    /// <summary>
    /// 面试官筛选状态
    /// </summary>
    public RecruitInterviewerScreenStatus? ScreenStatus { get; set; }

    /// <summary>
    /// 面试官id
    /// </summary>
    public string? InterviewerId { get; set; }
    #endregion

    #region 面试安排条件
    /// <summary>
    /// 面试过程
    /// </summary>
    public RecruitInterviewProcess? InterviewProcess { get; set; }

    /// <summary>
    /// 面试结果
    /// </summary>
    public RecruitInterviewOutcome? InterviewOutcome { get; set; }
    #endregion

    #region 已发offer条件
    /// <summary>
    /// offer内入职时间筛选开始时间
    /// </summary>
    public DateTime? OfferStartInductionTime { get; set; }

    /// <summary>
    /// offer内入职时间筛选结束时间
    /// </summary>
    public DateTime? OfferEndInductionTime { get; set; }
    #endregion

    #region 归档条件
    /// <summary>
    /// 归档子状态
    /// </summary>
    public RecruitFileAway? FileAway { get; set; }
    #endregion
}

/// <summary>
/// 获取招聘流程列表返回模型
/// </summary>
public class RecruitListResponse : QueryResponse
{
    /// <summary>
    /// 列表内容
    /// </summary>
    public List<RecruitListModel> Rows { get; set; } = new List<RecruitListModel>();
}

/// <summary>
/// 招聘流程列表模型
/// </summary>
public class RecruitListModel
{
    /// <summary>
    /// 流程开始时间
    /// </summary>
    public DateTime? StatusTime { get; set; }
    /// <summary>
    /// 是否显示"无效线索" true:显示，false:不显示
    /// </summary>
    public bool InvalidDisplay { get; set; }

    /// <summary>
    /// 求职者ImId
    /// </summary>
    public string? SeekerTencentImId { get; set; }

    public ThirdPlatType? ResumeType { get; set; }

    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string? Id { get; set; } = default!;

    /// <summary>
    /// 项目id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 用户头像
    /// </summary>
    public string? HeadPortrait { get; set; }

    /// <summary>
    /// 身份证号（不返回前端）
    /// </summary>
    [JsonIgnore]
    public string? IdentityCard { get; set; }

    /// <summary>
    /// 是否实名
    /// </summary>
    public bool? IsRealName { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 性别解释
    /// </summary>
    public string? SexName { get; set; }

    /// <summary>
    /// 生日（不返回前端）
    /// </summary>
    [JsonIgnore]
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public OccupationType? Occupation { get; set; }

    /// <summary>
    /// 职业解释
    /// </summary>
    public string? OccupationName { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 地区id（不返回前端）
    /// </summary>
    [JsonIgnore]
    public string? RegionId { get; set; }

    /// <summary>
    /// 所在地
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// 招聘流程列表工作经验数据库查询模型（不返回前端）
    /// </summary>
    [JsonIgnore]
    public WorkExpClass? WorkExpClass { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    public string? WorkExp { get; set; }

    /// <summary>
    /// 教育经历学校（不返回前端）
    /// </summary>
    [JsonIgnore]
    public string? EduExpSchool { get; set; }

    /// <summary>
    /// 教育经历专业（不返回前端）
    /// </summary>
    [JsonIgnore]
    public string? EduExpMajor { get; set; }

    /// <summary>
    /// 教育经历学历（不返回前端）
    /// </summary>
    [JsonIgnore]
    public EducationType? EduExpEducation { get; set; }

    /// <summary>
    /// 教育经历学历解释（不返回前端）
    /// </summary>
    [JsonIgnore]
    public string? EduExpEducationName { get; set; }

    /// <summary>
    /// 教育经历结束时间（不返回前端）
    /// </summary>
    [JsonIgnore]
    public DateOnly? EduExpEndTime { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public string? EduExp { get; set; }

    /// <summary>
    /// 应聘职位
    /// </summary>
    public string? HopeIndustry { get; set; }

    /// <summary>
    /// 用户来源
    /// </summary>
    public HrProjectSource? Source { get; set; }

    /// <summary>
    /// 顾问名称
    /// </summary>
    public string? Adviser { get; set; }

    /// <summary>
    /// 用户来源解释
    /// </summary>
    public string? SourceName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatTime { get; set; }

    /// <summary>
    /// 最后登录时间
    /// </summary>
    public DateTime? LastTime { get; set; }

    /// <summary>
    /// 招聘流程状态
    /// </summary>
    public RecruitStatus? Status { get; set; }

    /// <summary>
    /// 招聘流程状态解释
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 活跃度
    /// </summary>
    public OnlineStatus Active { get; set; }

    /// <summary>
    /// 活跃度解释
    /// </summary>
    public string ActiveName { get; set; } = default!;

    /// <summary>
    /// 面试流程标签
    /// </summary>
    public List<RecruitLabelClass>? RecruitLabel { get; set; }

    /// <summary>
    /// 简历二维码
    /// </summary>
    public string? HrAppletQrCode { get; set; }

    #region 面试官筛选单独返回字段
    /// <summary>
    /// 面试官筛选发起时间
    /// </summary>
    public DateTime? InterviewScreenLaunchTime { get; set; }

    /// <summary>
    /// 面试管筛选状态
    /// </summary>
    public RecruitInterviewerScreenStatus? InterviewScreenStatus { get; set; }

    /// <summary>
    /// 面试管筛选状态解释
    /// </summary>
    public string? InterviewScreenStatusName { get; set; }
    #endregion

    #region 面试安排单独返回字段
    /// <summary>
    /// 面试发起时间
    /// </summary>
    public DateTime? InterviewLaunchTime { get; set; }

    /// <summary>
    /// 面试官名称
    /// </summary>
    public string? InterviewerName { get; set; }

    /// <summary>
    /// 面试官反馈
    /// </summary>
    public RecruitInterviewOutcome? InterviewerFeedback { get; set; }

    /// <summary>
    /// 面试官反馈解释
    /// </summary>
    public string? InterviewerFeedbackName { get; set; }

    /// <summary>
    /// 候选人反馈
    /// </summary>
    public RecruitInterviewUserFeedBack? CandidateFeedback { get; set; }

    /// <summary>
    /// 候选人反馈解释
    /// </summary>
    public string? CandidateFeedbackName { get; set; }

    /// <summary>
    /// 面试方式
    /// </summary>
    public RecruitInterviewProcess? InterviewProcess { get; set; }

    /// <summary>
    /// 面试方式解释
    /// </summary>
    public string? InterviewProcessName { get; set; }
    #endregion

    #region 发放offer单独返回字段
    /// <summary>
    /// offer发起时间
    /// </summary>
    public DateTime? OfferLaunchTime { get; set; }

    /// <summary>
    /// 是否已通知
    /// </summary>
    public bool? IsNotice { get; set; }
    #endregion

    #region 入职单独返回字段
    /// <summary>
    /// 入职时间
    /// </summary>
    public DateTime? InductionTime { get; set; }
    #endregion

    #region 归档单独返回字段
    /// <summary>
    /// 归档时间
    /// </summary>
    public DateTime? FileTime { get; set; }

    /// <summary>
    /// 归档状态
    /// </summary>
    public RecruitFileAway? FileAway { get; set; }

    /// <summary>
    /// 归档状态解释
    /// </summary>
    public string? FileAwayName { get; set; }
    #endregion
}

/// <summary>
/// 获取招聘流程标签返回模型（总标签）
/// </summary>
public class RecruitLabelListResponse
{
    public List<RecruitLabelClass> Rows { get; set; } = default!;
}

/// <summary>
/// 添加招聘流程标签接收模型（总标签）
/// </summary>
public class AddRecruitLabelRequest
{
    /// <summary>
    /// 标签名称
    /// </summary>
    public string RecruitLabelName { get; set; } = default!;
}

/// <summary>
/// 招聘流程标签返回模型
/// </summary>
public class RecruitLabelResponse
{
    public List<RecruitLabelClass> Rows { get; set; } = default!;
}

/// <summary>
/// 变更招聘流程标签接收模型
/// </summary>
public class EditRecruitLabelRequest
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 招聘流程标签id
    /// </summary>
    public List<string> DicLabelId { get; set; } = new List<string>();
}

/// <summary>
/// 招聘流程标签类
/// </summary>
public class RecruitLabelClass
{
    public string? Id { get; set; }

    public string? Name { get; set; }
}

/// <summary>
/// 招聘流程列表工作经验数据库查询模型
/// </summary>
public class WorkExpClass
{
    public string? Company { get; set; }

    public string? Post { get; set; }

    public DateOnly? BeginDate { get; set; }

    public DateOnly? EndDate { get; set; }
}

/// <summary>
/// 项目面试官返回列表
/// </summary>
public class ProjectInterviewerListResponse
{
    public List<ProjectInterviewerModel> Rows { get; set; } = new List<ProjectInterviewerModel>();
}

/// <summary>
/// 项目面试官模型
/// </summary>
public class ProjectInterviewerModel
{
    public string? Id { get; set; }

    public string? Name { get; set; }

    /// <summary>
    /// 是否是主创
    /// </summary>
    public bool IsMain { get; set; }
}
#endregion

#region 招聘流程详情相关模型
/// <summary>
/// 招聘流程简历详情返回模型
/// </summary>
public class RecruitResumeDetailsResponse
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 腾讯ImId
    /// </summary>
    public string? SeekerTencentImId { get; set; }

    /// <summary>
    /// 简历是否有扩展信息
    /// </summary>
    public bool ResumeExtend { get; set; }

    /// <summary>
    /// 项目id
    /// </summary>
    //[JsonIgnore]
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 用户id
    /// </summary>
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 申请职位次数
    /// </summary>
    public int? DeliveryTimes { get; set; }

    /// <summary>
    /// 申请职位列表
    /// </summary>
    public List<RecruitPostList>? PostList { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 用户头像
    /// </summary>
    public string? HeadPortrait { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    [JsonIgnore]
    public string? _IdentityCard { get; set; }

    /// <summary>
    /// 是否实名
    /// </summary>
    public bool IsRealName { get; set; } = false;

    /// <summary>
    /// 生日
    /// </summary>
    [JsonIgnore]
    public DateOnly? _Birthday { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 性别解释
    /// </summary>
    public string? SexName { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public OccupationType? Occupation { get; set; }

    /// <summary>
    /// 职业解释
    /// </summary>
    public string? OccupationName { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Mailbox { get; set; }

    /// <summary>
    /// 微信
    /// </summary>
    public string? WeChat { get; set; }

    /// <summary>
    /// QQ
    /// </summary>
    public string? QQ { get; set; }

    /// <summary>
    /// 简历完整度
    /// </summary>
    public int Perfection { get; set; } = 0;

    /// <summary>
    /// 标签
    /// </summary>
    public List<string>? Label { get; set; }

    /// <summary>
    /// 性格
    /// </summary>
    [JsonIgnore]
    public List<string> Nature { get; set; } = new List<string>();

    /// <summary>
    /// 技能
    /// </summary>
    [JsonIgnore]
    public List<string> Skill { get; set; } = new List<string>();

    /// <summary>
    /// 外貌
    /// </summary>
    [JsonIgnore]
    public List<string> Appearance { get; set; } = new List<string>();

    /// <summary>
    /// 自我评价
    /// </summary>
    public string? SelfEvaluation { get; set; }

    /// <summary>
    /// 简历附件地址
    /// </summary>
    public string? AttachmentUrl { get; set; }

    /// <summary>
    /// 是否自己项目
    /// </summary>
    public bool SelfProject { get; set; }
    /// <summary>
    /// 是否开启众包
    /// </summary>
    public bool IsCrowdsource { get; set; } = false;
    /// <summary>
    /// 当前线索 是否接单0 否 1是
    /// </summary>
    public RecruitAcceptOrderEnum? AcceptOrder { get; set; }

    /// <summary>
    /// 技能证书
    /// </summary>
    public List<string>? SkillsCert { get; set; }

    /// <summary>
    /// 工作经历
    /// </summary>
    public List<RecruitResumeWorkSub>? WorkSub { get; set; }

    /// <summary>
    /// 教育经历
    /// </summary>
    public RecruitResumeEduSub? EduSub { get; set; }

    /// <summary>
    /// 实践经历
    /// </summary>
    public List<RecruitResumePracticeSub>? PracticeSub { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? Enterprise { get; set; }

    /// <summary>
    /// 简历二维码
    /// </summary>
    public string? HrAppletQrCode { get; set; }

    /// <summary>
    /// 活跃度
    /// </summary>
    public OnlineStatus Active { get; set; }

    /// <summary>
    /// 活跃度解释
    /// </summary>
    public string? ActiveName { get; set; }

    /// <summary>
    /// hr名称
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// hr职务
    /// </summary>
    public string? HrPost { get; set; }

    /// <summary>
    /// 简历是否免审
    /// </summary>
    public bool IsResumeExempt { get; set; } = false;
}

/// <summary>
/// 工作经历（详情子表）
/// </summary>
public class RecruitResumeWorkSub
{
    /// <summary>
    /// 公司名称
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 公司Logo
    /// </summary>
    public string? CompanyLogo { get; set; }

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 公司描述
    /// </summary>
    public string? CompanyRemarks { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateOnly? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateOnly? EndTime { get; set; }

    /// <summary>
    /// 开始时间 - 年月
    /// </summary>
    public string? StartTimeYM { get; set; }

    /// <summary>
    /// 结束时间 - 年月
    /// </summary>
    public string? EndTimeYM { get; set; }
}

/// <summary>
/// 教育经历（详情子表）
/// </summary>
public class RecruitResumeEduSub
{
    /// <summary>
    /// 学校名称
    /// </summary>
    public string? SchoolName { get; set; }

    /// <summary>
    /// 学校logo
    /// </summary>
    public string? SchoolLogo { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }


    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 专业
    /// </summary>
    public string? MajorName { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateOnly? EndTime { get; set; }

    /// <summary>
    /// 毕业时间 - 年月
    /// </summary>
    public string? EndTimeYM { get; set; }
}

/// <summary>
/// 实践经历（详情子表）
/// </summary>
public class RecruitResumePracticeSub
{
    /// <summary>
    /// 实践名称
    /// </summary>
    public string? PracticeName { get; set; }

    /// <summary>
    /// 实践logo
    /// </summary>
    public string? ProjectLogo { get; set; }

    /// <summary>
    /// 职位奖项
    /// </summary>
    public string? PostPrize { get; set; }

    /// <summary>
    /// 经历业绩
    /// </summary>
    public string? Experience { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateOnly? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateOnly? EndTime { get; set; }

    /// <summary>
    /// 开始时间 - 年月
    /// </summary>
    public string? StartTimeYM { get; set; }

    /// <summary>
    /// 结束时间 - 年月
    /// </summary>
    public string? EndTimeYM { get; set; }
}

/// <summary>
/// 申请职位模型
/// </summary>
public class RecruitPostList
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// 招聘流程对应投递表对应岗位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 是否主流程
    /// </summary>
    public bool IsTrue { get; set; }
}
#endregion

#region 招聘流程记录相关模型
/// <summary>
/// 聘流程记录返回模型
/// </summary>
public class RecruitmentProcessResponse
{
    public List<RecruitmentProcessInfo> Rows { get; set; } = default!;
}

/// <summary>
/// 聘流程记录接收模型
/// </summary>
public class RecruitmentProcessRequest
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = default!;
}

/// <summary>
/// 招聘流程模型
/// </summary>
public class RecruitmentProcessInfo
{
    /// <summary>
    /// 用户姓名
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// 招聘流程状态
    /// </summary>
    public RecruitStatus RecruitStatus { get; set; }

    /// <summary>
    /// 招聘流程状态名称
    /// </summary>
    public string RecruitStatusName { get; set; } = string.Empty;

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime OperationTime { get; set; }

    /// <summary>
    /// 操作人
    /// </summary>
    public string OperationPeople { get; set; } = string.Empty;

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string WorkPositionName { get; set; } = string.Empty;

    /// <summary>
    /// 面试模型
    /// </summary>
    public RecruitInterviewModel? RecruitInterviewModel { get; set; }

    /// <summary>
    /// 面试官筛选模型
    /// </summary>
    public RecruitScreenModel? RecruitScreenModel { get; set; }

    /// <summary>
    /// Offer模型
    /// </summary>
    public RecruitOfferModel? RecruitOfferModel { get; set; }

    /// <summary>
    /// 归档模型
    /// </summary>
    public RecruitFileAwayModel? RecruitFileAwayModel { get; set; }

    /// <summary>
    /// 入职模型
    /// </summary>
    public RecruitInductionModel? RecruitInductionModel { get; set; }
}

/// <summary>
/// 招聘流程面试模型
/// </summary>
public class RecruitInterviewModel
{
    /// <summary>
    /// 面试状态
    /// </summary>
    public RecruitInterviewOutcome? InterviewOutcome { get; set; }

    /// <summary>
    /// 面试状态解释
    /// </summary>
    public string? InterviewOutcomeName { get; set; }

    /// <summary>
    /// 面试过程（初试、复试、终试）
    /// </summary>
    public RecruitInterviewProcess? InterviewProcess { get; set; }

    /// <summary>
    /// 面试过程解释
    /// </summary>
    public string? InterviewProcessName { get; set; }

    /// <summary>
    /// 面试时间
    /// </summary>
    public DateTime? InterviewTime { get; set; }

    /// <summary>
    /// 面试官名称
    /// </summary>
    public string? Interviewer { get; set; }

    /// <summary>
    /// 评价
    /// </summary>
    public string? Remarks { get; set; }

    /// <summary>
    /// 反馈时间
    /// </summary>
    public DateTime? TodoTime { get; set; }

    /// <summary>
    /// 候选人的反馈
    /// </summary>
    public RecruitInterviewUserFeedBack? UserFeedBack { get; set; }

    /// <summary>
    /// 候选人的反馈解释
    /// </summary>
    public string? UserFeedBackName { get; set; } = string.Empty;

    /// <summary>
    /// 取消面试用户名
    /// </summary>
    public string? CancelName { get; set; }
}

/// <summary>
/// 招聘流程面试官筛选模型
/// </summary>
public class RecruitScreenModel
{
    /// <summary>
    /// 面试官筛选状态
    /// </summary>
    public RecruitInterviewerScreenStatus? InterviewerScreenStatus { get; set; }

    /// <summary>
    /// 面试官筛选状态解释
    /// </summary>
    public string? InterviewerScreenStatusName { get; set; }

    /// <summary>
    /// 面试官名称
    /// </summary>
    public string? Interviewer { get; set; }

    /// <summary>
    /// 反馈备注
    /// </summary>
    public string? Remarks { get; set; }

    /// <summary>
    /// 反馈时间
    /// </summary>
    public DateTime? TodoTime { get; set; }
}

/// <summary>
/// 招聘流程Offer模型
/// </summary>
public class RecruitOfferModel
{
    /// <summary>
    /// 发放时间
    /// </summary>
    public DateTime? ReleaseTime { get; set; }

    /// <summary>
    /// 是否已通知
    /// </summary>
    public bool? IsNotice { get; set; }
}

public class RecruitInductionModel
{
    /// <summary>
    /// 入职备注
    /// </summary>
    public string? InductionMemo { get; set; }

    /// <summary>
    /// 入职时间
    /// </summary>
    public DateTime? InductionTime { get; set; }
}

/// <summary>
/// 招聘流程归档模型
/// </summary>
public class RecruitFileAwayModel
{
    /// <summary>
    /// 归档类型
    /// </summary>
    public RecruitFileAway? RecruitFileAway { get; set; }

    /// <summary>
    /// 归档类型解释
    /// </summary>
    public string? RecruitFileAwayName { get; set; }

    /// <summary>
    /// 归档原因
    /// </summary>
    public string? FileAwayReason { get; set; }
}
#endregion

#region 招聘流程评论相关模型
/// <summary>
/// 获取招聘流程简历评论接收模型
/// </summary>
public class GetRecruitResumeCommentRequest : QueryRequest
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = string.Empty;
}

/// <summary>
/// 获取招聘流程简历评论返回模型
/// </summary>
public class GetRecruitResumeCommentResponse : QueryResponse
{
    public List<GetRecruitResumeCommentModel> Rows { get; set; } = new List<GetRecruitResumeCommentModel>();
}

/// <summary>
/// 获取招聘流程简历评论模型
/// </summary>
public class GetRecruitResumeCommentModel
{
    /// <summary>
    /// 评论id
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// hr姓名
    /// </summary>
    public string HrName { get; set; } = string.Empty;

    /// <summary>
    /// hr头像
    /// </summary>
    public string HeadPortrait { get; set; } = string.Empty;

    /// <summary>
    /// 评论内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 评论时间
    /// </summary>
    public DateTime CreatedTime { get; set; }
}

/// <summary>
/// 添加招聘流程简历评论模型
/// </summary>
public class AddRecruitResumeCommentRequest
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = string.Empty;

    /// <summary>
    /// 评论内容
    /// </summary>
    public string Content { get; set; } = string.Empty;
}
#endregion

#region 快手简历修改 - 只可以改没有oppenid的

public class EditKuaiShouResume
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }
}

#endregion
