// using Config.CommonModel;

// namespace Staffing.Model.CentreService.NScore;

// public class GetPushPostSettingsResponse
// {
//     public List<GetPushPostSettingsInfo> Rows { get; set; } = new List<GetPushPostSettingsInfo>();
// }

// public class UpdatePushPostSettings : GetPushPostSettingsInfo
// {
// }

// public class DeletePushPostSettings
// {
//     /// <summary>
//     /// Id
//     /// </summary>
//     public string? Id { get; set; }
// }