<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup>
    <NoWarn>1701;1702;1591;NU1803;CS8602;CS8604;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Infrastructure\Infrastructure\Infrastructure.csproj" />
    <ProjectReference Include="..\Staffing.Model\Staffing.Model.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Interface\Hr\" />
    <None Remove="Interface\Seeker\" />
    <None Remove="Interface\Internal\" />
    <None Remove="Interface\Admin\" />
    <None Remove="Service\Admin\" />
    <None Remove="Service\Hr\" />
    <None Remove="Service\Seeker\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Interface\Internal\" />
    <Folder Include="Interface\Admin\" />
    <Folder Include="Service\CentreService\" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
  </ItemGroup>
</Project>