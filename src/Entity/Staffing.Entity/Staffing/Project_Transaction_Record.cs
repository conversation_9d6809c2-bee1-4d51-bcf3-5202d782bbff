using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing
{
    /// <summary>
    /// 项目收支记录表
    /// </summary>
    [Table("settlement_project_transaction_record")]
    public class Project_Transaction_Record
    {
        [Key]
        public string Id { get; set; } = EntityTools.SnowflakeId();

        /// <summary>
        /// 项目ID
        /// </summary>
        public string ProjectId { get; set; } = default!;

        /// <summary>
        /// 流水号
        /// </summary>
        public string FlowNo { get; set; } = default!;

        /// <summary>
        /// 交易时间
        /// </summary>
        public DateTime TransactionTime { get; set; }

        /// <summary>
        /// 交易类型(收入/支出)
        /// </summary>
        public ProjectTransactionType TransactionType { get; set; }

        /// <summary>
        /// 金额（单位：分）
        /// </summary>
        public long Amount { get; set; }

        /// <summary>
        /// 余额（单位：分）
        /// </summary>
        public long Balance { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 交易单号
        /// </summary>
        public string? TransactionNo { get; set; }

        /// <summary>
        /// 对方账户
        /// </summary>
        public string? CounterpartyAccount { get; set; }

        /// <summary>
        /// 汇款人
        /// </summary>
        public string? RemitterName { get; set; }

        /// <summary>
        /// 汇款银行账号
        /// </summary>
        public string? RemitterBankAccount { get; set; }

        /// <summary>
        /// 汇款到账时间
        /// </summary>
        public DateTime? RemittanceArrivalTime { get; set; }

        /// <summary>
        /// 充值到账时间
        /// </summary>
        public DateTime? RechargeArrivalTime { get; set; }

        /// <summary>
        /// 操作人ID
        /// </summary>
        public string? OperatorId { get; set; }

        /// <summary>
        /// 操作人姓名
        /// </summary>
        public string? OperatorName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; } = false;
    }

    public enum ProjectTransactionType
    {

        收入 = 1,
    
        支出 = 2
    }
}