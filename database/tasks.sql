-- ----------------------------
-- 通用任务
-- ----------------------------
DROP TABLE IF EXISTS `tasks`;
CREATE TABLE `tasks` (
  `TaskId` varchar(18) NOT NULL,
  `TargetId` varchar(18) NOT NULL COMMENT '相关Id，根据type区分类型',
  `UserId` varchar(18) NOT NULL,
  `Name` varchar(50) NOT NULL DEFAULT '' COMMENT '名称',
  `Content` text NULL COMMENT '任务内容',
  `Type` int(2) NOT NULL DEFAULT 0 COMMENT '任务类型',
  `Status` int(2) NOT NULL DEFAULT 0 COMMENT '任务状态',
  `ResultText` varchar(1024) NULL COMMENT '结果文本',
  `EndTime` datetime(6) NULL COMMENT '结束时间',
  `Total` int(11) NOT NULL DEFAULT 0 COMMENT '总数',
  `Successful` int(11) NOT NULL DEFAULT 0 COMMENT '成功数',
  `Failed` int(11) NOT NULL DEFAULT 0 COMMENT '失败数',
  `Duplicate` int(11) NOT NULL DEFAULT 0 COMMENT '重复数',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`TaskId`),
  KEY `IX_tasks_TargetId_Type_CreatedTime` (`Type`, `TargetId` DESC, `CreatedTime`),
  KEY `IX_tasks_Status_Type_CreatedTime` (`Status`, `Type`, `CreatedTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- 通用任务明细
-- ----------------------------
DROP TABLE IF EXISTS `tasks_detail`;
CREATE TABLE `tasks_detail` (
  `Id` varchar(18) NOT NULL,
  `TaskId` varchar(18) NOT NULL,
  `Key` varchar(50) NOT NULL DEFAULT '' COMMENT '检索关键字（预留）',
  `Content` text NULL COMMENT '内容',
  `Result` int(2) NOT NULL DEFAULT 0 COMMENT '结果',
  `GroupStatus` int(2) NOT NULL DEFAULT 0 COMMENT '状态分组（用来查询比如重复的）',
  `ResultText` varchar(1024) NULL COMMENT '结果文本',
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`Id`),
  KEY `IX_tasks_detail_full` (`TaskId`, `Result`, `GroupStatus`, `CreatedTime` DESC, `Key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;