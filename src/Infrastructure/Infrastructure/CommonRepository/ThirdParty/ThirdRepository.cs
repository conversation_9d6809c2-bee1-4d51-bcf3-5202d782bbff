﻿using Config;
using Infrastructure.Common;
using Infrastructure.Extend;
using Infrastructure.Proxy;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure.CommonRepository.ThirdParty
{
    [Service(ServiceLifetime.Transient)]
    public class ThirdRepository
    {
        private IDbContextFactory<StaffingContext> _staffingContextFactory;
        private ConfigManager _config;
        private readonly IHostEnvironment _hostingEnvironment;
        private readonly LogManager _log;
        private readonly ZhiXiaoErApi _zhiXiaoErApi;
        public ThirdRepository(IDbContextFactory<StaffingContext> staffingContextFactory, IOptionsSnapshot<ConfigManager> config,
        IHostEnvironment hostingEnvironment, LogManager log, TencentImHelper tencentIm<PERSON>el<PERSON>, ZhiXiaoEr<PERSON><PERSON> zhiXiaoErApi)
        {
            _staffingContextFactory = staffingContextFactory;
            _config = config.Value;
            _hostingEnvironment = hostingEnvironment;
            _log = log;
            _zhiXiaoErApi = zhiXiaoErApi;
        }

        //public async Task<List<T>> GetPostList()
        //{
        //    var _context = _staffingContextFactory.CreateDbContext();
            
        //        var list = await _context.Thirdparty_Scale_Dic_Relation.ToListAsync();
        //        return list;
            
        //}
    }
}
