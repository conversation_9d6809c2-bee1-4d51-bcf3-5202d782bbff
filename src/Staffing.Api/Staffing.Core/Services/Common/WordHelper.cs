﻿using NPOI.OpenXmlFormats.Dml;
using NPOI.OpenXmlFormats.Dml.WordProcessing;
using NPOI.OpenXmlFormats.Wordprocessing;
using NPOI.XWPF.UserModel;

namespace Noah.ImExportTools.Native
{
    public class WordHelper
    {
        const string _DefaultValue = "未配置";
        /// <summary>
        /// 输出模板docx文档(使用字典)
        /// </summary>
        /// <param name="tempFilePath">docx文件路径</param>
        /// <param name="outPath">输出文件路径</param>
        /// <param name="data">字典数据源</param>
        public static void Export(string tempFilePath, string outPath, Dictionary<string, string> data)
        {
            using (FileStream stream = System.IO.File.OpenRead(tempFilePath))
            {
                XWPFDocument doc = new XWPFDocument(stream);
                //遍历段落                  
                foreach (var para in doc.Paragraphs)
                {
                    ReplaceKey(para, data);
                }
                foreach (var item in doc.BodyElements) 
                { 
  
                }

                //遍历表格      
                foreach (var table in doc.Tables)
                {
                    foreach (var row in table.Rows)
                    {
                        foreach (var cell in row.GetTableCells())
                        {
                            foreach (var para in cell.Paragraphs)
                            {
                                ReplaceKey(para, data);
                            }
                        }
                    }
                }
                //写文件
                FileStream outFile = new FileStream(outPath, FileMode.Create);
                doc.Write(outFile);
                outFile.Close();
            }
        }
        /// <summary>
        /// 输出模板base64文档
        /// </summary>
        /// <param name="tempFilePath"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static string ExportBase64(string tempFilePath, Dictionary<string, string> data)
        {
            string result = string.Empty;
            using (FileStream stream = System.IO.File.OpenRead(tempFilePath))
            {
                XWPFDocument doc = new XWPFDocument(stream);
                //遍历段落                  
                foreach (var para in doc.Paragraphs)
                {
                    ReplaceKey(para, data);
                }
                //遍历表格      
                foreach (var table in doc.Tables)
                {
                    foreach (var row in table.Rows)
                    {
                        foreach (var cell in row.GetTableCells())
                        {
                            foreach (var para in cell.Paragraphs)
                            {
                                ReplaceKey(para, data);
                            }
                        }
                    }
                }
                using (MemoryStream ms = new MemoryStream())
                {
                    doc.Write(ms);
                    result = Convert.ToBase64String(ms.ToArray());
                }
                return result;
            }
        }
        //替换word文档里的占位符
        private static void ReplaceKey(XWPFParagraph para, Dictionary<string, string> data)
        {
            string text = "";
            List<string> dicList = new List<string>();
            foreach (var run in para.Runs)
            {
                text = run.ToString();
                foreach (var key in data.Keys)
                {
                    //$$模板中数据占位符为$KEY$
                    if (text.Contains($"${key}$"))
                    {
                        text = text.Replace($"${key}$", data[key]);
                        run.SetText(text, 0);
                    }
                }
            }
        }

        #region 根据模板导出含列表的Word文档
        /// <summary>
        /// 参数筛选正则(不含列表参数)
        /// </summary>
        const string ParamRegist_Text = @"[$]{0}([^($.\s)]+?)[$]";
        /// <summary>
        /// 参数筛选正则(不含列表参数)
        /// </summary>
        const string ParamTitleRegist_Text = @"[\[]([^(\[\].\s)]+?)[:]([^(\[\].\s)]+?)[\]]";
        /// <summary>
        /// 列表参数筛选正则
        /// </summary>
        const string ParamRegist_Arrary = @"[$]([^($\s)]+?)[.]([^($\s)]+?)[$]";

        /// <summary>
        /// 输出模板docx文档(使用字典)
        /// *字典内数据含Array或Img
        /// </summary>
        /// <param name="tempFilePath">docx文件路径</param>
        /// <param name="outPath">输出文件路径</param>
        /// <param name="data">字典数据源</param>
        public static async Task ExportWithArray(string tempFilePath, string outPath, Dictionary<string, object> data)
        {
            bool IsReLoadTable = false;
            using (FileStream stream = System.IO.File.OpenRead(tempFilePath))
            {
                XWPFDocument doc = new XWPFDocument(stream);

                //验证是否存在需要保存列表的套表结构(行中嵌套表结构的列表)      
                for (int i = doc.Tables.Count; i > 0; i--)
                {
                    if (CheckExistsArrayChildTabelAndCreate(doc, doc.Tables[i - 1], data))
                        IsReLoadTable = true;
                }

                //没有“行中嵌套表结构的列表”时，直接替换文档内参数
                if (!IsReLoadTable)
                    await ReplaceSimpleContent(doc, data);
                //写文件
                FileStream outFile = new FileStream(outPath, FileMode.Create);
                doc.Write(outFile);
                outFile.Close();
            }

            //存在“行中嵌套表结构的列表”时，重新加载Word赋值
            if (IsReLoadTable)
            {
                using (FileStream stream = System.IO.File.OpenRead(outPath))
                {
                    stream.Position = 0;
                    XWPFDocument doc = new XWPFDocument(stream);
                    //遍历填充“行中嵌套表结构的列表”参数 
                    foreach (var table in doc.Tables)
                    {
                        await ReplaceKey_ByTabelArrary(doc, table, data);
                    }

                    //填充普通表格或段落参数
                    await ReplaceSimpleContent(doc, data);

                    //写文件
                    FileStream outFile = new FileStream(outPath, FileMode.Create);
                    doc.Write(outFile);
                    outFile.Close();
                }
            }
        }

        /// <summary>
        /// 验证是否存在需要保存列表的套表结构(行中嵌套表结构的列表)
        /// </summary>
        /// <param name="table">主表</param>
        /// <param name="data">数据字典</param>
        /// <returns></returns>
        private static bool CheckExistsArrayChildTabelAndCreate(XWPFDocument doc, XWPFTable table, Dictionary<string, object> data)
        {
            XWPFTable? tempTable = null;
            //行套表的情况
            if (string.IsNullOrEmpty(table.Text) && table.Rows.Count <= 2)
            {
                if (table.Rows.Count == 2)
                {
                    var cells = table.Rows[1].GetTableCells();
                    if (cells.Count() == 1 && cells[0].Tables.Count == 1)
                        tempTable = cells[0].Tables[0];
                }
                else
                {
                    var cells = table.Rows[0].GetTableCells();
                    if (cells.Count() == 1 && cells[0].Tables.Count == 1)
                        tempTable = cells[0].Tables[0];
                }
            }
            else//普通表的情况
                tempTable = table;

            //验证表格是否需加载列表内容
            if (tempTable != null)
            {
                var paramMatches = System.Text.RegularExpressions.Regex.Matches(tempTable.Text, ParamRegist_Arrary);
                //无需填充参数
                if (paramMatches.Count == 0)
                    return false;

                string key = paramMatches[0].Value.Trim('$').Split('.')[0];
                if (key.StartsWith("Arrary."))
                    key = key.Substring(8);

                if (data.ContainsKey(key))
                {
                    var paramsArrary = data[key] as Newtonsoft.Json.Linq.JArray;
                    if (paramsArrary.Count() > 0)
                    {
                        //复制行
                        CT_Row SourceRowTemp;
                        if (table.Rows.Count == 2)
                            SourceRowTemp = table.Rows[1].GetCTRow();
                        else
                            SourceRowTemp = table.Rows[0].GetCTRow();
                        //var SourceTableTemp = tempTable;
                        //SourceTableTemp.SetInsideHBorder(XWPFTable.XWPFBorderType.NONE, 0, 0, "");
                        //SourceTableTemp.SetInsideVBorder(XWPFTable.XWPFBorderType.NONE, 0, 0, "");
                        for (int i = 1; i < paramsArrary.Count; i++)
                        {
                            var newRow = new XWPFTableRow(SourceRowTemp, table);
                            table.AddRow(newRow);
                            ////创建新表，并替换表格参数
                            //var newTable = CopyTable(doc, SourceTableTemp, tableIndex+i);
                            //await ReplaceKey_ByTabel(newTable, paramsArrary[i], key + ".");

                            ////添加行
                            //CT_Row targetRow = SourceRowTemp.Table.AddNewTr();
                            //targetRow.trPr = SourceRowTemp.trPr;
                            //targetRow.rsidR = SourceRowTemp.rsidR;
                            //targetRow.rsidTr = SourceRowTemp.rsidTr;
                            //targetRow.Table = SourceRowTemp.Table;
                            //XWPFTableRow tabRow = new XWPFTableRow(targetRow, para);
                            //var newcell = tabRow.CreateCell();
                            //newcell.InsertTable(0, newTable);
                            //para.AddRow(tabRow);

                        }
                        return true;
                    }
                }

                //没有参数或参数列表为空时，清空列表
                if (table.Rows.Count == 1)
                {
                    doc.RemoveBodyElement(doc.GetPosOfTable(table));
                    //table.RemoveRow(0);
                }
            }
            return false;
        }
        /// <summary>
        /// 填充普通表格或段落参数
        /// </summary>
        /// <param name="doc">XWPFDocument</param>
        /// <param name="data">数据字典</param>
        /// <returns></returns>
        private static async Task ReplaceSimpleContent(XWPFDocument doc, Dictionary<string, object> data)
        {
            //替换普通表格参数
            foreach (var table in doc.Tables)
            {
                foreach (var row in table.Rows)
                {
                    foreach (var cell in row.GetTableCells())
                    {
                        foreach (var para in cell.Paragraphs)
                        {
                            await ReplaceKey_ByParagraphText(doc, para, data);
                        }
                    }
                }
            }
            //遍历段落                  
            for (int i = doc.Paragraphs.Count; i > 0; i--)
            {
                await ReplaceKey_ByParagraphText(doc, doc.Paragraphs[i - 1], data);
            }
        }

        /// <summary>
        /// 遍历段落参数列表并填充
        /// </summary>
        /// <param name="para">XWPFParagraph</param>
        /// <param name="data">数据字典</param>
        /// <param name="Pre">模板中设置参数的前缀，默认为空，列表参数时需传.及点前面的字符</param>
        /// <returns></returns>
        private static async Task ReplaceKey_ByParagraphText(XWPFDocument doc, XWPFParagraph para, Dictionary<string, object> data, string Pre = "")
        {
            //先检查段落内容是否就是占位符
            if (string.IsNullOrEmpty(para.Text.Trim()) && para.Runs.Count == 0)
                return;
            if (string.IsNullOrEmpty(para.Text) && para.Runs.Count > 0)
            {
                if (para.Runs.Count(x => !string.IsNullOrEmpty(x.PictureText)) > 0)
                { }
                if (para.Runs.Count(x => x.GetEmbeddedPictures().Count > 0) > 0)
                {
                    foreach (var run in para.Runs)
                    {
                        List<XWPFPicture> xwpfPictureList = run.GetEmbeddedPictures();

                    }
                }
                if (para.Runs.Count(x => !string.IsNullOrEmpty(x.Text)) > 0)
                { }
                if (para.Runs.Count(x => x.GetEmbeddedPictures().Count > 0) > 0
                    && para.Runs.Count(x => !string.IsNullOrEmpty(x.Text)) > 0)
                { }
                //var run = para.Runs[0];
                //string ss = run.Text;
                //List<XWPFPicture> xwpfPictureList = run.GetEmbeddedPictures();
                //if (xwpfPictureList.Count > 0)
                //{
                //    var dataPic = xwpfPictureList[0].GetPictureData();
                //    var picData = dataPic.Data;
                //    var image = GetImageFromByte(picData);
                //}
                return;
            }

            if (para.Text.Contains(']') && para.Text.Contains('[') && para.Text.Contains(':'))
            {
                if (para.Text.EndsWith(']'))
                {
                    string key = para.Text.Split(':')[1].TrimEnd(']');
                    if (data.ContainsKey(key))
                    {
                        var values = data[key];
                        if (values == null ||
                            (values.GetType() == typeof(string) && string.IsNullOrEmpty(values.ToString())) ||
                            (values.GetType() == typeof(Newtonsoft.Json.Linq.JArray) && (values as Newtonsoft.Json.Linq.JArray).Count == 0) ||
                            (values.GetType().IsArray && Newtonsoft.Json.Linq.JArray.Parse(Newtonsoft.Json.JsonConvert.SerializeObject(values)).Count == 0))
                        {
                            doc.RemoveBodyElement(doc.GetPosOfParagraph(para));
                            return;
                        }
                        else
                        {
                            string newValue = para.Text.Replace(":" + key + "]", "").Replace("[", "");
                            para.ReplaceText(para.Text, newValue);
                        }
                    }
                }
                else
                {
                    var paramMatches = System.Text.RegularExpressions.Regex.Matches(para.Text, ParamTitleRegist_Text);
                    foreach (System.Text.RegularExpressions.Match paramItem in paramMatches)
                    {
                        string key = paramItem.Value.Split(':')[1].TrimEnd(']');
                        if (data.ContainsKey(key))
                        {
                            string newValue;
                            var values = data[key];
                            if (values == null ||
                                (values.GetType() == typeof(string) && string.IsNullOrEmpty(values.ToString())) ||
                                (values.GetType() == typeof(Newtonsoft.Json.Linq.JArray) && (values as Newtonsoft.Json.Linq.JArray).Count == 0) ||
                                (values.GetType().IsArray && Newtonsoft.Json.Linq.JArray.Parse(Newtonsoft.Json.JsonConvert.SerializeObject(values)).Count == 0))
                            {
                                newValue = string.Empty;
                            }
                            else
                                newValue = paramItem.Value.Replace(":" + key + "]", "").Replace("[", "");
                            para.ReplaceText(paramItem.Value, newValue);
                        }
                    }
                }
            }
            if (para.Text.Contains('$'))
            {
                var paramMatches = System.Text.RegularExpressions.Regex.Matches(para.Text, string.Format(ParamRegist_Text, Pre));
                foreach (System.Text.RegularExpressions.Match paramItem in paramMatches)
                {
                    //获取Key值及Value类型
                    string key = paramItem.Value.Trim('$');
                    string valueType = "string";
                    string defaultValue = _DefaultValue;
                    if (key.Contains(':'))
                    {
                        valueType = key.Split(':')[0].ToLower();
                        key = key.Split(':')[1];
                    }
                    else if (key.Contains('?'))
                        (key, defaultValue) = GetDefaultValue(key);

                    string newText = string.Empty;
                    if (data.ContainsKey(key))
                    {
                        if (valueType == "arrary")
                        {
                            var values = data[key] as Newtonsoft.Json.Linq.JArray;
                            if (values == null || values.Count == 0)
                                newText = string.Empty;
                            else
                                newText = string.Join("、", values.Values<string>());
                        }
                        else
                            newText = data[key]?.ToString() ?? string.Empty;
                    }

                    await ReplaceText(para, newText, paramItem.Value, valueType, DefaultValue: defaultValue);
                }
                return;
            }
        }

        //private static System.Drawing.Image GetImageFromByte(byte[] streamByte)
        //{
        //    System.IO.MemoryStream ms = new System.IO.MemoryStream(streamByte);
        //    System.Drawing.Image img = System.Drawing.Image.FromStream(ms);
        //    return img;
        //}

        /// <summary>
        /// 遍历段落参数列表并填充
        /// </summary>
        /// <param name="para">XWPFParagraph</param>
        /// <param name="data">数据JToken</param>
        /// <param name="Pre">模板中设置参数的前缀，默认为空，列表参数时需传.及点前面的字符</param>
        /// <returns></returns>
        private static async Task ReplaceKey_ByParagraphText(XWPFDocument doc, XWPFParagraph para, Newtonsoft.Json.Linq.JToken data, string Pre = "", XWPFTableCell? Cell = null, int CellIndex = 0)
        {
            //先检查段落内容是否就是占位符
            if (string.IsNullOrEmpty(para.Text.Trim()))
                return;
            if (para.Text.EndsWith(']') && para.Text.Contains('[') && para.Text.Contains(':'))
            {
                string key = para.Text.Split(':')[1].TrimEnd(']');
                string relKey = key;
                if (key.StartsWith(Pre))
                    relKey = key.Substring(Pre.Length);

                var values = data[relKey];
                if (values == null ||
                    (values.GetType() == typeof(string) && string.IsNullOrEmpty(values.ToString())) ||
                    (values.GetType() == typeof(Newtonsoft.Json.Linq.JArray) && (values as Newtonsoft.Json.Linq.JArray).Count == 0) ||
                    (values.GetType().IsArray && Newtonsoft.Json.Linq.JArray.Parse(Newtonsoft.Json.JsonConvert.SerializeObject(values)).Count == 0))
                {
                    doc.RemoveBodyElement(doc.GetPosOfParagraph(para));
                    return;
                }
                else
                {
                    string newValue = para.Text.Replace(":" + key + "]", "").Replace("[", "");
                    para.ReplaceText(para.Text, newValue);
                }
            }
            else if (para.Text.Contains('$'))
            {
                var paramMatches = System.Text.RegularExpressions.Regex.Matches(para.Text, string.Format(ParamRegist_Text, Pre));
                foreach (System.Text.RegularExpressions.Match paramItem in paramMatches)
                {
                    if (paramItem.Value == "$工作经历.Img:CompanyLogo$")
                    { }
                    //获取Key值及Value类型
                    string key = paramItem.Value.Trim('$');
                    string valueType = "string";
                    string defaultValue = _DefaultValue;
                    if (!string.IsNullOrEmpty(Pre))
                        key = key.Substring(Pre.Length);

                    if (key.Contains(':'))
                    {
                        valueType = key.Split(':')[0].ToLower();
                        key = key.Split(':')[1];
                    }
                    else if (key.Contains('?'))
                        (key, defaultValue) = GetDefaultValue(key);

                    string newText = data.Value<string>(key) ?? string.Empty;
                    if (!string.IsNullOrEmpty(newText))
                    {
                        if (valueType == "arrary")
                        {
                            var values = data[key] as Newtonsoft.Json.Linq.JArray;
                            if (values.Count == 0)
                                newText = string.Empty;
                            else
                                newText = string.Join("、", values.Values<string>());
                        }
                        else
                            newText = data[key]?.ToString() ?? string.Empty;
                    }

                    await ReplaceText(para, newText, paramItem.Value, valueType, isTable: true, Cell: Cell, CellIndex: CellIndex, defaultValue);
                }
                return;
            }
        }

        private static (string Key,string DefaultValue) GetDefaultValue(string key)
        {
            if (key.EndsWith('?'))
                return (key.TrimEnd('?'), string.Empty);
            else
            {
                string[] strarr = key.Split('?');
                return (strarr[0], strarr.Length == 1 ? string.Empty : strarr[1]);
            }
        }

        /// <summary>
        /// 填充“行中嵌套表结构的列表”参数
        /// </summary>
        /// <param name="mainTable">XWPFTable，主表</param>
        /// <param name="data">数据字典</param>
        /// <returns></returns>
        private static async Task ReplaceKey_ByTabelArrary(XWPFDocument doc, XWPFTable mainTable, Dictionary<string, object> data)
        {
            string tempTableText = string.Empty;
            bool ArrayChildTabel = false;//是否为行套表的情况
            int StartRowIndex = 0;
            //行套表的情况
            if (string.IsNullOrEmpty(mainTable.Text)
                && mainTable.Rows.Count > 0
                && mainTable.Rows[0].GetTableCells().Count == 1
                && mainTable.Rows[0].GetCell(0).Tables.Count == 1
                && mainTable.Rows[0].GetCell(0).Tables[0].Text.Contains('$'))
            {
                tempTableText = mainTable.Rows[0].GetCell(0).Tables[0].Text;
                ArrayChildTabel = true;
            }
            else if (string.IsNullOrEmpty(mainTable.Text)
                && mainTable.Rows.Count > 1
                && mainTable.Rows[1].GetTableCells().Count == 1
                && mainTable.Rows[1].GetCell(0).Tables.Count == 1)
            {
                tempTableText = mainTable.Rows[1].GetCell(0).Tables[0].Text;
                ArrayChildTabel = true;
                StartRowIndex = 1;
            }
            else//普通列表
                tempTableText = mainTable.Text;

            if (!string.IsNullOrEmpty(tempTableText))
            {
                var paramMatches = System.Text.RegularExpressions.Regex.Matches(tempTableText, ParamRegist_Arrary);
                //无需填充参数
                if (paramMatches.Count == 0)
                    return;

                //获取参数列表
                string key = paramMatches[0].Value.Trim('$').Split('.')[0];
                if (key.StartsWith("Arrary."))
                    key = key.Substring(8);
                var paramsArrary = data[key] as Newtonsoft.Json.Linq.JArray;

                if (data.ContainsKey(key))
                {
                    //行套表的情况循环赋值
                    if (ArrayChildTabel)
                    {
                        for (int i = StartRowIndex; i < mainTable.Rows.Count; i++)
                        {
                            //子表
                            var childTable = mainTable.Rows[i].GetCell(0).Tables[0];
                            //替换表格参数
                            foreach (var row in childTable.Rows)
                            {
                                foreach (var cell in row.GetTableCells())
                                {
                                    for (var j = 0; j < cell.Paragraphs.Count; j++)
                                    {
                                        await ReplaceKey_ByParagraphText(doc, cell.Paragraphs[j], paramsArrary[i - StartRowIndex], key + ".", Cell: cell, CellIndex: j);
                                    }
                                }
                            }
                        }
                    }
                    else//普通列表循环赋值
                    {
                        for (int i = 0; i < mainTable.Rows.Count; i++)
                        {
                            var row = mainTable.Rows[i];
                            foreach (var cell in row.GetTableCells())
                            {
                                for (var j = 0; j < cell.Paragraphs.Count; j++)
                                {
                                    await ReplaceKey_ByParagraphText(doc, cell.Paragraphs[j], paramsArrary[i], key + ".", Cell: cell, CellIndex: j);
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 填充参数值
        /// </summary>
        /// <param name="para">XWPFParagraph</param>
        /// <param name="newText">参数值</param>
        /// <param name="oldText">模板配置的参数标签</param>
        /// <param name="valueType">参数值类型：string、array、img</param>
        /// <param name="isTable">段落是否包含在表中</param>
        /// <param name="Cell">可为空，段落所属单元格</param>
        /// <param name="CellIndex">可为空，段落所属单元格的Index</param>
        /// <returns></returns>
        private static async Task ReplaceText(XWPFParagraph para, string newText, string oldText, string valueType, bool isTable = false, XWPFTableCell? Cell = null, int CellIndex = 0,string? DefaultValue = null)
        {
            if (valueType == "string" || valueType == "arrary")
            {
                if (string.IsNullOrWhiteSpace(newText))
                    para.ReplaceText(oldText, DefaultValue ?? _DefaultValue);
                else
                {
                    if ((newText.IndexOf('\n') > -1 || newText.IndexOf('\r') > -1) && oldText == para.Text)
                    {
                        para.IndentationFirstLine = 0;
                        para.ReplaceText(oldText, string.Empty);
                        string[] arr;
                        if (newText.IndexOf('\n') > -1)
                            arr = newText.Split('\n');
                        else
                            arr = newText.Split('\r');
                        para.Runs[0].SetText(arr[0]);

                        if (arr.Length > 1)
                        {
                            string FontFamily = para.Runs[0].FontFamily;
                            var FontSize = para.Runs[0].FontSize;
                            string FontColor = para.Runs[0].GetColor();
                            //对某个段落设置格式
                            if (Cell != null)
                            {
                                for (int i = 1; i < arr.Length; i++)
                                {
                                    var newPara = Cell.AddParagraph();
                                    XWPFRun run = newPara.CreateRun();
                                    run.FontFamily = FontFamily;
                                    run.FontSize = FontSize;
                                    run.SetColor(FontColor);
                                    run.SetText(arr[i]);
                                }
                            }
                            else
                                for (int i = 1; i < arr.Length; i++)
                                {
                                    XWPFRun run = para.CreateRun();
                                    run.AddCarriageReturn();//换行
                                    run.FontFamily = FontFamily;
                                    run.FontSize = FontSize;
                                    run.SetColor(FontColor);
                                    run.SetText(arr[i]);
                                }
                        }
                    }
                    else
                        para.ReplaceText(oldText, newText.Replace("\n", Environment.NewLine));
                }
            }
            else if (valueType == "img")
                await WriteImg(para, newText, Cell: Cell, CellIndex: CellIndex);
        }
        /// <summary>
        /// 填充图片参数
        /// </summary>
        /// <param name="para">XWPFParagraph</param>
        /// <param name="urlPath">图片URL</param>
        /// <param name="Cell">可为空，段落所属单元格</param>
        /// <param name="CellIndex">可为空，段落所属单元格的Index</param>
        /// <returns></returns>
        private static async Task WriteImg(XWPFParagraph para, string urlPath, XWPFTableCell? Cell = null, int CellIndex = 0)
        {
            para.ReplaceText(para.Text, string.Empty);
            if (string.IsNullOrEmpty(urlPath))
            {
                if (Cell != null)
                {
                    CT_Tbl tbl = ((CT_Tbl)((CT_Row)Cell.GetCTTc().Parent).Parent);
                    tbl.tblGrid.gridCol[CellIndex].w = 1;
                    //(new System.Collections.Generic.ICollectionDebugView<CT_TblGridCol>(((CT_Tbl)((CT_Row)Cell.GetCTTc().Parent).Parent).tblGrid.gridCol).Items[0]).w
                    Cell.GetTableRow().GetTable().SetColumnWidth(CellIndex, 1);
                }
                return;
            }

            //验证图片类型
            string extension = Path.GetExtension(urlPath).Trim('.').ToUpper();
            if(string.IsNullOrEmpty(extension))
                if(urlPath.IndexOf("?") >= 0)
                    extension = Path.GetExtension(urlPath.Split('?')[0]).Trim('.').ToUpper();
            
            if (extension == "JPG" || string.IsNullOrWhiteSpace(extension))
                extension = "JPEG";
            var eNames = Enum.GetNames(typeof(PictureType));
            var eIndex = Array.IndexOf(eNames, extension);
            if (eIndex == -1)
                return;

            var eValues = Enum.GetValues(typeof(PictureType));
            if (!Enum.TryParse<PictureType>(eValues.GetValue(eIndex).ToString(), out var ePicType))
                return;

            XWPFRun newRun = para.CreateRun();
            var widthEmus = (int)(100.0);//图片的宽度
            var heightEmus = (int)(100.0);//图片的高度

            var file = await DownLoadFile(urlPath);
            using (Stream stream = new MemoryStream(file.FileData))
            {
                //图片的文件流   图片类型   图片名称   设置的宽度以及高度
                //if (Cell == null)
                //    newRun.AddPicture(stream, (int)ePicType, file.FileName, widthEmus, heightEmus);
                //else
                {
                    string index = para.Document.AddPictureData(stream, (int)ePicType);
                    CreatePicture(newRun, index, widthEmus, heightEmus);
                }
            }
        }

        /// <summary>
        /// NPOI包中插入图片，会导致最终生成的word打不开。使用自定义的插入
        /// </summary>
        /// <param name="run"></param>
        /// <param name="id"></param>
        /// <param name="width"></param>
        /// <param name="height"></param>
        private static void CreatePicture(XWPFRun run, string id, int width, int height)
        {
            int EMU = 9525;
            width *= EMU;
            height *= EMU;

            string picXml = ""
                    //+ "<a:graphic xmlns:a=\"http://schemas.openxmlformats.org/drawingml/2006/main\">"
                    //+ "   <a:graphicData uri=\"http://schemas.openxmlformats.org/drawingml/2006/picture\">"
                    + "      <pic:pic xmlns:pic=\"http://schemas.openxmlformats.org/drawingml/2006/picture\" xmlns:a=\"http://schemas.openxmlformats.org/drawingml/2006/main\">"
                    + "         <pic:nvPicPr>" + "            <pic:cNvPr id=\""
                    + "0"
                    + "\" name=\"Generated\"/>"
                    + "            <pic:cNvPicPr/>"
                    + "         </pic:nvPicPr>"
                    + "         <pic:blipFill>"
                    + "            <a:blip r:embed=\""
                    + id
                    + "\" xmlns:r=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships\"/>"
                    + "            <a:stretch>"
                    + "               <a:fillRect/>"
                    + "            </a:stretch>"
                    + "         </pic:blipFill>"
                    + "         <pic:spPr>"
                    + "            <a:xfrm>"
                    + "               <a:off x=\"0\" y=\"0\"/>"
                    + "               <a:ext cx=\""
                    + width
                    + "\" cy=\""
                    + height
                    + "\"/>"
                    + "            </a:xfrm>"
                    + "            <a:prstGeom prst=\"rect\">"
                    + "               <a:avLst/>"
                    + "            </a:prstGeom>"
                    + "         </pic:spPr>"
                    + "      </pic:pic>";
            //+ "   </a:graphicData>" + "</a:graphic>";
            //var run = doc.CreateParagraph().CreateRun();
            CT_Inline inline = run.GetCTR().AddNewDrawing().AddNewInline();

            inline.graphic = new CT_GraphicalObject();
            inline.graphic.graphicData = new CT_GraphicalObjectData();
            inline.graphic.graphicData.uri = "http://schemas.openxmlformats.org/drawingml/2006/picture";

            // CT_GraphicalObjectData graphicData = inline.graphic.AddNewGraphicData();
            // graphicData.uri = "http://schemas.openxmlformats.org/drawingml/2006/picture";

            //XmlDocument xmlDoc = new XmlDocument();
            try
            {
                //xmlDoc.LoadXml(picXml);
                //var element = xmlDoc.DocumentElement;
                inline.graphic.graphicData.AddPicElement(picXml);

            }
            catch (System.Xml.XmlException xe)
            {
                Console.WriteLine(xe.Message);
            }

            NPOI.OpenXmlFormats.Dml.WordProcessing.CT_PositiveSize2D extent = inline.AddNewExtent();
            extent.cx = width;
            extent.cy = height;

            NPOI.OpenXmlFormats.Dml.WordProcessing.CT_NonVisualDrawingProps docPr = inline.AddNewDocPr();
            docPr.id = 1;
            docPr.name = "Image" + id;
        }

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="FileUrl">文件来源地址</param>
        /// <returns>文件地址</returns>
        private static async Task<(byte[] FileData, string FileName)> DownLoadFile(string FileUrl)
        {
            using (HttpClient httpClient = new HttpClient())
            {
                httpClient.Timeout = TimeSpan.FromSeconds(10);
                var ret = await httpClient.GetAsync(FileUrl);
                if (ret.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    string fileName = System.IO.Path.GetFileName(FileUrl);
                    if (string.IsNullOrEmpty(fileName))
                        fileName = Guid.NewGuid().ToString("N").ToLower();
                    if (string.IsNullOrEmpty(System.IO.Path.GetExtension(fileName)))
                        fileName += ".png";
                    return (await ret.Content.ReadAsByteArrayAsync(), fileName);
                }
            }
            return (new byte[1], string.Empty);
        }

        #endregion
    }
}
