﻿using Config;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.Net.Http.Headers;
using Staffing.Entity;
using Microsoft.AspNetCore.Authorization;
using Infrastructure.CommonService;
using Infrastructure.Proxy;
using Config.CommonModel.Business;

namespace Infrastructure.Filters;

/// <summary>
/// OAuthRequestFilter
/// </summary>
public class RequestFilter : IAsyncActionFilter
{
    private ConfigManager _config;
    private RequestContext _user;
    private readonly CacheHelper _cacheHelper;
    private readonly StaffingContext _context;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly CommonUserService _commonUserService;
    private readonly CommonDicService _commonDicService;
    private readonly NuoPinApi _nuoPinApi;
    public RequestFilter(IOptionsSnapshot<ConfigManager> config, RequestContext user,
        StaffingContext context, NuoPinApi nuoPinApi, IHostEnvironment hostingEnvironment,
        CacheHelper cacheHelper, CommonUserService commonUserService, CommonDicService commonDicService)
    {
        _config = config.Value;
        _context = context;
        _user = user;
        _hostingEnvironment = hostingEnvironment;
        _cacheHelper = cacheHelper;
        _commonUserService = commonUserService;
        _nuoPinApi = nuoPinApi;
        _commonDicService = commonDicService;
    }

    /// <summary>
    /// OnActionExecutionAsync
    /// </summary>
    /// <param name="context"></param>
    /// <param name="next"></param>
    /// <returns></returns>
    public async Task OnActionExecutionAsync(
        ActionExecutingContext context,
        ActionExecutionDelegate next)
    {

        // var syncIOFeature = context.HttpContext.Features.Get<IHttpBodyControlFeature>();
        // if (syncIOFeature != null)
        // {
        //     syncIOFeature.AllowSynchronousIO = true;
        // }

        var apiStartTime = DateTime.Now;
        var requestId = context.HttpContext.TraceIdentifier;

        //拿到真实ip
        var ipAddress = context.HttpContext.Request.Headers["X-Forwarded-For"].ToString();
        if (string.IsNullOrWhiteSpace(ipAddress))
            ipAddress = context.HttpContext?.Connection?.RemoteIpAddress?.ToString().Replace("::ffff:", string.Empty);
        else
        {
            var ips = ipAddress?.Trim().Trim(',').Split(',');
            ipAddress = ips?.FirstOrDefault();
        }

        var method = context.HttpContext!.Request.Method.ToUpper();
        var para = RequestParams.GetParams(context.HttpContext);

        var agent = context.HttpContext?.Request?.Headers?[HeaderNames.UserAgent].ToString() ?? string.Empty;

        _user.UserAgent = agent;
        _user.RequestIpAddress = ipAddress;

        //接口幂等性验证
        if (method != "GET")
        {
            // var lockKey = Md5Helper.Md5($"{_user.ConnectionId}_{method}_{para}");
            var lockKey = Md5Helper.Md5($"{_user.RequestIpAddress}_{_user.UserAgent}_{context.HttpContext!.Request.Path}_{method}_{para}");
            using var lkr = MyRedis.TryLock(lockKey);
            if (lkr == null)
                throw new NotAcceptableException(string.Empty);
        }

        var token = string.Empty;

        //拿到token
        if (context.HttpContext!.Request.Headers.TryGetValue("Authorization", out var headerAuth))
        {
            var headerAuthStr = headerAuth.FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(headerAuthStr) && headerAuthStr.StartsWith("Bearer"))
            {
                token = headerAuthStr.Substring("Bearer".Length).Trim();
                _user.AccessToken = token;
            }
        }

        //clientid
        if (context.HttpContext.Request.Headers.TryGetValue("x-requested-with", out var clientId))
            _user.ClientId = clientId.ToString().Trim();

        //拿到顾问Id
        if (context.HttpContext.Request.Headers.TryGetValue("AdviserId", out var adviserId))
            _user.AdviserId = adviserId.ToString().Trim();

        //拿到渠道Id
        if (context.HttpContext.Request.Headers.TryGetValue("ChannelId", out var channelId))
            _user.ChannelId = channelId.ToString().Trim();

        //拿到渠道来源
        if (context.HttpContext.Request.Headers.TryGetValue("ChannelSource", out var channelSource))
            if (long.TryParse(channelSource.ToString().Trim(), out var csi))
                _user.ChannelSource = csi;

        //如果没有顾问，设置为顾问为平台
        if (string.IsNullOrWhiteSpace(_user.AdviserId))
            _user.AdviserId = Constants.PlatformHrId;
        else
        {
            //顾问id是否存在
            var advExists = _cacheHelper.GetMemoryCache<bool?>(() =>
            {
                var e = _context.User_Hr.Any(x => x.UserId == _user.AdviserId && x.Status == UserStatus.Active);
                return e;
            }, $"AdviserIdExists:{_user.AdviserId}", 300) ?? false;

            if (!advExists)
                _user.AdviserId = Constants.PlatformHrId;
        }

        //判断身份验证标识
        var controllerActionDescriptor = context.ActionDescriptor as ControllerActionDescriptor;

        //查询方法和controller
        var attrs = controllerActionDescriptor!.MethodInfo.GetCustomAttributes(inherit: true)
        .Select(s => s.GetType()).ToList()
        .Union(controllerActionDescriptor!.ControllerTypeInfo.GetCustomAttributes(inherit: true)
        .Select(s => s.GetType()).ToList()).Distinct();

        //服务间通信验证
        var transitAuth = attrs.Any(x => x.Equals(typeof(TransitAuth)));
        var onlyNuoAuth = attrs.Any(x => x.Equals(typeof(OnlyNuoAuth)));
        var quickJobAuth = attrs.Any(x => x.Equals(typeof(QuickJobAuth)));
        var authorize = attrs.Any(x => x.Equals(typeof(MyAuthorizeAttribute)));
        var allowAnonymous = attrs.Any(x => x.Equals(typeof(AllowAnonymousAttribute)));
        // var kuaishouPowersAuth = attrs.Any(x => x.Equals(typeof(KuaishouPowersAuth)));
        // var noAudit = attrs.Any(x => x.Equals(typeof(NoAudit)));

        if (transitAuth)
        {
            if (string.IsNullOrWhiteSpace(token) || !_config.ServiceKeys!.Contains(token))
                throw new UnauthorizedException("认证失败");
            _user.Id = token;
        }

        ShortUserInfo? usr = null;
        //如果有token，解析
        if (!string.IsNullOrWhiteSpace(token) && !transitAuth)
        {
            var namesp = context.Controller.GetType().ToString().ToLower();
            if (namesp.Contains(".controllers.hr."))
                _user.AppType = TokenType.企业端;
            else if (namesp.Contains(".controllers.seeker.") || namesp.Contains(".controllers.interviewer."))
                _user.AppType = TokenType.用户端;
            else if (namesp.Contains(".controllers.internal.") || (namesp.Contains(".controllers.centreservice.") && namesp.EndsWith(".transitcontroller")))
                _user.AppType = TokenType.系统内部服务;
            else if (namesp.Contains(".controllers.centreservice."))
                _user.AppType = TokenType.平台端;

            // var tpint = _user.AppType.HasValue ? (int)_user.AppType.Value : 99;
            var tkmd5 = Md5Helper.Md5(token);
            var usrCacheKey = $"ut:{tkmd5}";
            var disable = _hostingEnvironment.IsProduction() ? 600 : 60;

            usr = MyRedis.Client.Get<ShortUserInfo?>(usrCacheKey);

            //如果缓存不存在
            if (usr == null)
            {
                //加锁获取token
                using (var locker = await MyRedis.Lock($"tklk:{tkmd5}", 10))
                {
                    if (locker == null)
                        throw new Exception("请求获取锁失败");

                    usr = MyRedis.Client.Get<ShortUserInfo?>(usrCacheKey);
                    if (usr == null)
                    {
                        //判断是否自己token
                        var isMyToken = token.StartsWith(Constants.MyTokenPrefix);

                        ClientType? client = null;

                        if (isMyToken)
                        {
                            //走自己认证
                            var at = _context.Token_Access
                            .Where(x => x.Token == token && x.ExpirationTime > DateTime.Now)
                            .Select(s => new
                            {
                                s.Type,
                                s.UserId,
                                s.Client
                            }).FirstOrDefault();

                            if (at != null)
                            {
                                var tokenType = at.Type;
                                client = at.Client ?? ClientType.Other;

                                //查询对应用户信息
                                var id = at?.UserId ?? string.Empty;

                                //目前只支持用户端
                                if (tokenType == TokenType.用户端)
                                {
                                    usr = _context.User_Seeker.Where(x => x.UserId == id)
                                    .Select(s => new ShortUserInfo
                                    {
                                        UserId = s.UserId,
                                        NickName = s.NickName,
                                        Mobile = s.User.Mobile,
                                        Status = s.Status,
                                        AppType = at!.Type,
                                        ClientType = client
                                    }).FirstOrDefault();

                                    //更新用户活跃时间
                                    if (!string.IsNullOrWhiteSpace(usr?.UserId))
                                        _commonUserService.UserActivity(usr.UserId, client ?? ClientType.SeekerApplet, _user.RequestIpAddress);
                                }
                                else if (tokenType == TokenType.企业端)
                                {
                                    var hr = _context.User_Hr
                                        .Where(x => x.UserId == id)
                                        .Select(s => new
                                        {
                                            UserId = s.UserId,
                                            NickName = s.NickName,
                                            Mobile = s.User.Mobile,
                                            Power = s.Enterprise_Role.Powers,
                                            CustomPower = s.Powers,
                                            s.Status,
                                            EntId = s.EntId,
                                            GroupEntId = s.Enterprise.GroupEntId,
                                            GroupType = s.Enterprise.GroupType,
                                            s.NuoId,
                                            EntNuoId = s.Enterprise.NuoId,
                                        }).FirstOrDefault();

                                    if (hr != null)
                                        usr = new ShortUserInfo
                                        {
                                            UserId = hr.UserId,
                                            NickName = hr.NickName,
                                            Mobile = hr.Mobile,
                                            Powers = hr.Power.Union(hr.CustomPower ?? new List<string>()).ToList(),
                                            EntId = hr.EntId,
                                            Status = hr.Status,
                                            GroupEntId = hr.GroupEntId,
                                            GroupType = hr.GroupType,
                                        };

                                    //更新用户活跃时间
                                    if (!string.IsNullOrWhiteSpace(usr?.UserId))
                                        _commonUserService.UserActivity(usr.UserId, client ?? ClientType.HrWeb, _user.RequestIpAddress);

                                    usr = usr ?? new ShortUserInfo();
                                    usr.NuoPinId = hr?.NuoId;
                                    usr.NuoEntId = hr?.EntNuoId ?? string.Empty;
                                    usr.AppType = TokenType.企业端;
                                    usr.ClientType = client;

                                    //企业缓存key存下来，清理token用
                                    if (!string.IsNullOrWhiteSpace(usr.NuoPinId))
                                        MyRedis.Client.Set($"{RedisKey.UserTokenKey}{usr.NuoPinId}", usrCacheKey, 3600 * 12);
                                }
                                else if (tokenType == TokenType.平台端)
                                {
                                    var admin = _context.Admin
                                        .Where(x => x.Id == id)
                                        .Select(s => new
                                        {
                                            s.Id,
                                            s.Name,
                                            s.Powers,
                                            s.Account,
                                            s.Status
                                        }).FirstOrDefault();

                                    if (admin != null)
                                        usr = new ShortUserInfo
                                        {
                                            UserId = admin.Id,
                                            NickName = admin.Name ?? string.Empty,
                                            Mobile = admin.Account,
                                            Powers = admin.Powers,
                                            Status = admin.Status == ActiveStatus.Active ? UserStatus.Active : UserStatus.Rejected
                                        };

                                    usr = usr ?? new ShortUserInfo();
                                    usr.AppType = TokenType.平台端;
                                    usr.ClientType = client;
                                    usr.NuoPinId = id;
                                }
                            }
                        }
                        else
                        {
                            //走诺聘认证
                            if (_user.AppType == TokenType.平台端)
                            {
                                var nuoUser = await _nuoPinApi.GetPlatformUserByToken(token);
                                if (!string.IsNullOrWhiteSpace(nuoUser?.Id))
                                {
                                    if (nuoUser != null)
                                        usr = new ShortUserInfo
                                        {
                                            UserId = nuoUser.Id,
                                            Mobile = nuoUser.Mobile ?? string.Empty
                                        };

                                    usr = usr ?? new ShortUserInfo();
                                    usr.NuoPinId = nuoUser!.Id;
                                    usr.AppType = TokenType.平台端;
                                    usr.ClientType = ClientType.Other;
                                }
                            }
                            else
                            {
                                var nuoUser = await _nuoPinApi.GetUserByToken(token, TokenType.企业端);
                                if (!string.IsNullOrWhiteSpace(nuoUser?.Id))
                                {
                                    var hr = _context.User_Hr
                                        .Where(x => x.NuoId == nuoUser.Id)
                                        .Select(s => new
                                        {
                                            UserId = s.UserId,
                                            NickName = s.NickName,
                                            Mobile = s.User.Mobile,
                                            Power = s.Enterprise_Role.Powers,
                                            CustomPower = s.Powers,
                                            s.Status,
                                            EntId = s.EntId,
                                            GroupEntId = s.Enterprise.GroupEntId,
                                            GroupType = s.Enterprise.GroupType,
                                        }).FirstOrDefault();

                                    if (hr != null)
                                        usr = new ShortUserInfo
                                        {
                                            UserId = hr.UserId,
                                            NickName = hr.NickName,
                                            Mobile = hr.Mobile,
                                            Powers = hr.Power.Union(hr.CustomPower ?? new List<string>()).ToList(),
                                            EntId = hr.EntId,
                                            Status = hr.Status,
                                            GroupEntId = hr.GroupEntId,
                                            GroupType = hr.GroupType
                                        };

                                    //更新用户活跃时间
                                    if (!string.IsNullOrWhiteSpace(usr?.UserId))
                                        _commonUserService.UserActivity(usr.UserId, ClientType.HrWeb, _user.RequestIpAddress);

                                    usr = usr ?? new ShortUserInfo();
                                    usr.NuoPinId = nuoUser.Id;
                                    usr.NuoEntId = nuoUser.LoginPrincipalNo ?? string.Empty;
                                    usr.AppType = TokenType.企业端;
                                    usr.ClientType = ClientType.Other;

                                    //企业缓存key存下来，清理token用
                                    if (!string.IsNullOrWhiteSpace(usr.NuoPinId))
                                        MyRedis.Client.Set($"{RedisKey.UserTokenKey}{usr.NuoPinId}", usrCacheKey, 3600 * 12);

                                    // //如果审核未通过，仅缓存3秒做个样子
                                    // if (string.IsNullOrEmpty(usr.UserId) || usr.Status != UserStatus.Active)
                                    //     disable = 3;
                                }
                            }
                        }

                        MyRedis.Client.Set(usrCacheKey, usr ?? new ShortUserInfo(), disable);
                    }
                }
            }

            _user.Id = usr?.UserId ?? string.Empty;
            _user.Account = usr?.Mobile;
            _user.Name = usr?.NickName;
            _user.NuoPinId = usr?.NuoPinId ?? string.Empty;
            _user.NuoEntId = usr?.NuoEntId ?? string.Empty;
            _user.EntId = usr?.EntId ?? string.Empty;
            _user.GroupEntId = usr?.GroupEntId ?? string.Empty;
            _user.GroupType = usr?.GroupType ?? EnterpriseGroupType.Ordinary;
            _user.Powers = usr?.Powers ?? new List<string>();
            _user.ClientType = usr?.ClientType;
        }

        //如果需要身份验证
        if (authorize && !allowAnonymous)
        {
            if (_user.AppType != null && _user.AppType != usr?.AppType)
                throw new UnauthorizedException("认证失败");

            if (_user.AppType == TokenType.用户端)
            {
                if (string.IsNullOrWhiteSpace(_user.Id))
                    throw new UnauthorizedException("认证失败");

                if (usr?.Status != UserStatus.Active)
                    throw new ForbiddenException("禁止访问，请联系客服人员", ErrorCodes.NotAudit);
            }
            else if (_user.AppType == TokenType.企业端)
            {
                if (string.IsNullOrWhiteSpace(_user.Id) && !onlyNuoAuth)
                    throw new UnauthorizedException("认证失败");

                if (string.IsNullOrWhiteSpace(_user.NuoPinId) && onlyNuoAuth)
                    throw new UnauthorizedException("认证失败");

                if (string.IsNullOrWhiteSpace(usr?.EntId) && !onlyNuoAuth)
                    throw new ForbiddenException("尚未加入企业", ErrorCodes.NoEnt);

                if (!onlyNuoAuth)
                {
                    //诺快聘
                    if (usr?.Status != UserStatus.Active)
                        throw new ForbiddenException("用户尚未审核通过", ErrorCodes.NotAudit);
                }

                //检测权限
                var powerObj = controllerActionDescriptor.MethodInfo.GetCustomAttributes(inherit: true)
                .FirstOrDefault(a => a.GetType().Equals(typeof(Powers)));

                if (powerObj == null)
                {
                    powerObj = controllerActionDescriptor.ControllerTypeInfo.GetCustomAttributes(inherit: true)
                    .FirstOrDefault(a => a.GetType().Equals(typeof(Powers)));
                }

                if (powerObj != null)
                {
                    var power = powerObj as Powers;
                    if (_user.Powers == null || !Power.CheckPower(_user.Powers, power!.Power))
                        throw new ForbiddenException("没有权限");
                }
            }
            else if (_user.AppType == TokenType.平台端)
            {
                if (string.IsNullOrWhiteSpace(_user.NuoPinId))
                    throw new UnauthorizedException("认证失败");
            }
            else
            {
                if (string.IsNullOrWhiteSpace(_user.Id) && string.IsNullOrWhiteSpace(_user.NuoPinId))
                    throw new UnauthorizedException("认证失败");
            }
        }
        var resp = await next();
    }
}