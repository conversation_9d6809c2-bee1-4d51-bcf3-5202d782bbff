using Infrastructure.Extend;
using Staffing.Model.CentreService.User;
using Staffing.Core.Interfaces.CentreService;
using Config;
using Infrastructure.Common;
using Microsoft.Extensions.Options;
using Staffing.Entity;
using Staffing.Model.Common.System;
using Config.CommonModel.Certificate;
using Microsoft.EntityFrameworkCore;
using Infrastructure.QYWechat;

namespace Staffing.Api.Apis.Admin;

[Service(ServiceLifetime.Transient)]
public class HrRouter : IAdminRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("hr").WithTags("顾问");

        group.MapGet("/", ([AsParameters] GetHrList model, IHrService rep) => rep.GetHrList(model))
        .WithOpenApi(operation => new(operation) { Summary = "获取Hr列表" });

        group.MapPost("/dd/bind", async (DdBindRequest model, IHrService rep) => await rep.DdBind(model))
        .WithOpenApi(operation => new(operation) { Summary = "绑定钉钉" });

        group.MapPost("/dd/unbind", (DdUnBindRequest model, IHrService rep) => rep.DdUnBind(model))
        .WithOpenApi(operation => new(operation) { Summary = "解绑钉钉" });

        group.MapPost("/audit", (HrAudit model, IHrService rep) => rep.HrAudit(model))
        .WithOpenApi(operation => new(operation) { Summary = "Hr审批" });

        group.MapGet("/ent/search", ([AsParameters] SearchEntName model, IHrService rep) => rep.SearchEntName(model))
        .WithOpenApi(operation => new(operation) { Summary = "搜索企业名称" });

        group.MapPost("/ent/change", (HrChangeEnt model, IHrService rep) => rep.HrChangeEnt(model))
        .WithOpenApi(operation => new(operation) { Summary = "hr切换企业" });

        group.MapPost("/hr/dingding", async (BindDingDingModelRequest model, IHrService rep) => await rep.BindDingDing(model))
        .WithOpenApi(operation => new(operation) { Summary = "平台端绑定钉钉用户（协助hr绑定）批量" });

        group.MapPost("/hr/dinglist", (GetDingDingListRequest model, IHrService rep) => rep.GetDingDingList(model))
        .WithOpenApi(operation => new(operation) { Summary = "获取钉钉用户列表" });

        group.MapPost("/hr/dinguser", async (AddDingDingUserRequest model, IHrService rep) => await rep.AddDingDingUser(model))
        .WithOpenApi(operation => new(operation) { Summary = "添加钉钉用户" });

        group.MapPost("/hr/dingbind", (BindDingDingByDingUserIdRequest model, IHrService rep) => rep.BindDingDingByDingUserId(model))
        .WithOpenApi(operation => new(operation) { Summary = "绑定钉钉用户" });

        group.MapPost("/hr/dingunbind", (UnBindDingDingByDingUserIdRequest model, IHrService rep) => rep.UnBindDingDingByDingUserId(model))
        .WithOpenApi(operation => new(operation) { Summary = "解除绑定钉钉" });

        group.MapPost("/hr/dingdel", (DeleteDingDingRequest model, IHrService rep) => rep.DeleteDingDing(model))
        .WithOpenApi(operation => new(operation) { Summary = "删除钉钉用户" });

        group.MapGet("/templateimg/gentocertificatetemplate", async (Noah.Aliyun.Storage.IObjectStorage _objectStorageService, StaffingContext context, LogManager logger) =>
        {
            var request = new Certificate_ProImg_SplicingTemplate
            {
                Top_TemplatePath = @".\wwwroot\Certificate\postertemplateTop.jpg",
                Bottom_TemplatePath = @".\wwwroot\Certificate\postertemplateBottom.jpg",
                Table_TR_TemplatePath = @".\wwwroot\Certificate\postertemplateTableTr.jpg",
                FontTTFPath = @".\wwwroot\Certificate\msyh.ttf",
                GetCerCount = ImgHelper._Certificate_TemplateImgConfig.GetCerCount,
                CerFontSize = ImgHelper._Certificate_TemplateImgConfig.CerFontSize,
                CerFont_HR_Height = ImgHelper._Certificate_TemplateImgConfig.CerFont_HR_Height,
                CerFont_FirstPositionY = ImgHelper._Certificate_TemplateImgConfig.CerFont_FirstPositionY,
                CerFont_Num_PositionX = ImgHelper._Certificate_TemplateImgConfig.CerFont_Num_PositionX,
                CerFont_CertificateName_PositionX = ImgHelper._Certificate_TemplateImgConfig.CerFont_CertificateName_PositionX,
                CerFont_CertificateType_PositionX = ImgHelper._Certificate_TemplateImgConfig.CerFont_CertificateType_PositionX,
                CerFont_Specs_PositionX = ImgHelper._Certificate_TemplateImgConfig.CerFont_Specs_PositionX,
                CerFont_IssuingAuthority_PositionX = ImgHelper._Certificate_TemplateImgConfig.CerFont_IssuingAuthority_PositionX,
                CerFont_Price_PositionX = ImgHelper._Certificate_TemplateImgConfig.CerFont_Price_PositionX
            };
            //获取发证机构字典
            var dictDataMap = context.DictData.Where(w => w.DictType == "CertificationAuthority")
            .Select(s => new Staffing.Entity.Staffing.DictData()
                {
                    DictValue = s.DictValue,
                    DictLabel = s.DictLabel,
                }).ToDictionary(
                k => k.DictValue,
                v => v.DictLabel
            ) ?? new Dictionary<string, string>();
            
            var dbcer = context.CertificateTable.Where(x => !x.Deleted)
            .OrderByDescending(x => x.CreateTime)
            .Take(request.GetCerCount)
            .Include(c => c.Specs)
            .ToList();
            //var cerIds = dbcer.Select(x => x.Id).Distinct().ToList();
            //var dbspec = context.CertificateSpec.Where(x => cerIds.Contains(x.CertificateId)).Select(x => new
            //{
            //    x.CertificateId,
            //    x.SpecName,
            //    x.Price
            //}).ToList();
            List<Certificate_ProImg_Data> certificates = dbcer.Select(x => new Certificate_ProImg_Data
            {
                CertificateName = x.CertificateName,
                CertificateType = x.CertificateType.ToString(),
                //更新发证机构名称
                IssuingAuthority = string.IsNullOrEmpty(x.IssuingAuthority) ? string.Empty :
                                (dictDataMap.TryGetValue(x.IssuingAuthority, out var value) ? value : x.IssuingAuthority),
                //Specs = dbspec.Where(s => s.CertificateId == x.Id).Select(s => s.SpecName).ToList(),
                //Price = dbspec.Where(s => s.CertificateId == x.Id).OrderByDescending(m => m.Price).FirstOrDefault()?.Price ?? 0
                Specs = x.Specs == null || x.Specs.Count == 0 ? new() : x.Specs.OrderBy(s => s.Price).Select(s => s.SpecName).ToList(),
                Price = x.Specs == null || x.Specs.Count == 0 ? 0 : x.Specs.Min(s => s.Price)
            }).ToList();

            //组装模板基础图片及出现次数
            List<ImgHelper_InputPaths> inputPaths = new List<ImgHelper_InputPaths>()
            {
                new ImgHelper_InputPaths(request.Top_TemplatePath ?? "https://resources.noahjob.cn/staffing/Certificate/postertemplateTop.jpg")
            };
            if (certificates != null && certificates.Count > 0)
                inputPaths.Add(new ImgHelper_InputPaths(request.Table_TR_TemplatePath ?? "https://resources.noahjob.cn/staffing/Certificate/postertemplateTableTr.jpg", certificates.Count));
            inputPaths.Add(new ImgHelper_InputPaths(request.Bottom_TemplatePath ?? "https://resources.noahjob.cn/staffing/Certificate/postertemplateBottom.jpg"));

            //组装文本
            List<ImgHelper_TextLayer> textLayers = new();
            string FontPath = request.FontTTFPath ?? @".\TemplateFiles\Certificate\msyh.ttf"; //"https://resources.noahjob.cn/staffing/Certificate/postertemplateFont.ttf";
            var color = SixLabors.ImageSharp.Color.Black;
            SixLabors.Fonts.FontStyle fontStyle = SixLabors.Fonts.FontStyle.Bold;
            for (int i = 0; i < certificates!.Count; i++)
            {
                var item = certificates[i];
                var point_Y = request.CerFont_FirstPositionY + i * request.CerFont_HR_Height;
                // 证书表单--编号的起始位置X
                textLayers.Add(new ImgHelper_TextLayer
                {
                    Text = (i + 1).ToString(),
                    FontPath = FontPath,
                    FontSize = request.CerFontSize,
                    Color = color,
                    FontStyle = fontStyle,
                    Position = new SixLabors.ImageSharp.PointF(request.CerFont_Num_PositionX, point_Y)
                });
                // 证书表单--证书名称的起始位置X
                textLayers.Add(new ImgHelper_TextLayer
                {
                    Text = item.CertificateName ?? string.Empty,
                    FontPath = FontPath,
                    FontSize = request.CerFontSize,
                    Color = color,
                    FontStyle = fontStyle,
                    Position = new SixLabors.ImageSharp.PointF(request.CerFont_CertificateName_PositionX, point_Y)
                });
                // 证书表单--证书类别的起始位置X
                textLayers.Add(new ImgHelper_TextLayer
                {
                    Text = item.CertificateType ?? string.Empty,
                    FontPath = FontPath,
                    FontSize = request.CerFontSize,
                    Color = color,
                    FontStyle = fontStyle,
                    Position = new SixLabors.ImageSharp.PointF(request.CerFont_CertificateType_PositionX, point_Y)
                });
                // 证书表单--证书规格的起始位置X
                textLayers.Add(new ImgHelper_TextLayer
                {
                    Text = item.Specs == null ? "[无规格]" : string.Join(@"\", item.Specs),
                    FontPath = FontPath,
                    FontSize = request.CerFontSize,
                    Color = color,
                    FontStyle = fontStyle,
                    Position = new SixLabors.ImageSharp.PointF(request.CerFont_Specs_PositionX, point_Y)
                });

                // 证书表单--颁发机构的起始位置X
                textLayers.Add(new ImgHelper_TextLayer
                {
                    Text = item.IssuingAuthority ?? string.Empty,
                    FontPath = FontPath,
                    FontSize = request.CerFontSize,
                    Color = color,
                    FontStyle = fontStyle,
                    Position = new SixLabors.ImageSharp.PointF(request.CerFont_IssuingAuthority_PositionX, point_Y)
                });
                // 证书表单--报名费的起始位置X
                textLayers.Add(new ImgHelper_TextLayer
                {
                    Text = item.Price.ToString(),
                    FontPath = FontPath,
                    FontSize = request.CerFontSize,
                    Color = SixLabors.ImageSharp.Color.Red,
                    FontStyle = fontStyle,
                    Position = new SixLabors.ImageSharp.PointF(request.CerFont_Price_PositionX, point_Y)
                });
            }

            var stream = ImgHelper.CombineImagesWithOverlay(inputPaths, textLayers, null);
            string newName = System.IO.Path.GetFileName((new Uri(Constants.CertificateTemplaeUrl())).LocalPath);
            var ossurl = await _objectStorageService.OrdinaryUploadFile(stream, Path.GetFileName(newName), newName);
            return ossurl;
        })
        .WithOpenApi(operation => new(operation) { Summary = "根据模板组装证书推广图片" });

        group.MapGet("/templateimg/gentocertificate", async ([AsParameters] Certificate_GentoCerTificateReq request,
            WeChatHelper _weChatHelper,
            IOptions<ConfigManager> _config,
            Noah.Aliyun.Storage.IObjectStorage _objectStorageService,
            StaffingContext context, LogManager logger) =>
        {
            var hr = context.User_Hr.Where(x => x.User.Mobile == request.AdviserMobile).FirstOrDefault();
            if (hr == null)
                throw new Infrastructure.Exceptions.BadRequestException("用户不存在");

            Certificate_ProImg_ForHR imgConfig = ImgHelper._Certificate_AdviserImgConfigConfig;

            var QrCodePath = await _weChatHelper.GetWxaCodeUnlimitForCertificateList(hr.UserId);
            if (string.IsNullOrEmpty(QrCodePath))
                throw new Infrastructure.Exceptions.BadRequestException("未获取到顾问蓝领证书推广小程序二维码");
            //组装拼接图片URL及位置
            var Overlay = new List<ImgHelper_ImageOverlay2>
            {
                new ImgHelper_ImageOverlay2(QrCodePath, imgConfig.QrCodePosition_x, imgConfig.QrCodePosition_y, imgConfig.QRSize)
            };
            
            if (!string.IsNullOrEmpty(hr.EntWeChatQrCode))
            {
                Overlay.Add(new ImgHelper_ImageOverlay2(hr.EntWeChatQrCode, imgConfig.QYWechatPosition_x, imgConfig.QYWechatPosition_y, imgConfig.QYWechatSize));
                Overlay.Add(new ImgHelper_ImageOverlay2(@".\wwwroot\Certificate\postertemplateBottom_qwtip.png", imgConfig.QYWechatTipPosition_x > 0 ? imgConfig.QYWechatTipPosition_x : (imgConfig.QYWechatPosition_x + imgConfig.QYWechatSize), imgConfig.QYWechatPosition_y, 0));
            }

            //合并图片
            //var stream = await ImgHelper.MergeImagesAsync(Constants.CertificateTemplaeUrl(), Overlay);
            var templateBase64 = await ImgHelper.DownloadImageAsBase64(Constants.CertificateTemplaeUrl());
            var stream = await ImgHelper.MergeImagesAsync_ForBase64Template(templateBase64, Overlay);
            // 上传到OSS，并返回生成的图片URL
            string newName = $"{hr.UserId}.jpg";
            string ossUrl = await _objectStorageService.OrdinaryUploadFile(stream, newName, newName, $"{_config.Value.Aliyun!.Oss!.Dir!}/Certificate/promotionimg");
            stream.Close();
            stream.Dispose();

            // oss地址存本地,用set不会重复
            MyRedis.Client.SAdd(SubscriptionKey.OssPathKey,
                new Entity.Staffing.File_Path_Of_Oss { Type = "CertificatePromotion", PrimaryId = hr.UserId, OssPath = ossUrl, UserId = hr.UserId });

            return ossUrl + "?timestamp=" + DateTime.Now.Ticks;
        })
        .WithOpenApi(operation => new(operation) { Summary = "生成顾问的证书推广图" });
    }
}