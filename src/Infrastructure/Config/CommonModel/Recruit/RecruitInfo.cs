﻿using Config.Enums;

namespace Config.CommonModel.Recruit;

public class RecruitInfos
{
    /// <summary>
    /// 最新面试时间
    /// </summary>
    public DateTime? LastInterviewTime { get; set; }

    /// <summary>
    /// 归档类型
    /// </summary>
    public RecruitFileAway FileAwayType { get; set; }

    /// <summary>
    /// 无效原因，点击无效线索按钮时下拉选则
    /// </summary>
    public string? InvalidReason { get; set; }

    /// <summary>
    /// 招聘流程上一个节点
    /// </summary>
    public RecruitStatus LastStatus { get; set; }
}
