namespace Config.CommonModel.JavaDataApi;

public class ProjectBoardReq : JavaQueryRequest
{
    /// <summary>
    /// 协同项目Id
    /// </summary>
    public string? teamProjectId { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public string? projectId { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public int? type { get; set; }
}

public class ExportPostBoard
{
    /// <summary>
    /// 表格数据
    /// </summary>
    public List<object> Rows { get; set; } = new List<object>();
}

public class ExportPostBoardDetail
{
    /// <summary>
    /// 表格数据
    /// </summary>
    public string? postRecruitNum { get; set; }
}