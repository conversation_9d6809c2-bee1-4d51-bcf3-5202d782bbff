﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

// /// <summary>
// /// aaa
// /// </summary>
// [Table("aaa")]
// public class aaa
// {
//     [Key]
//     public string mobile { get; set; } = null!;

//     public string? xingming { get; set; }

//     public string? time { get; set; }

//     public string? shijuan { get; set; }

//     public string? url { get; set; }

//     public string? neirong { get; set; }
// }

// [Table("abcdefg")]
// public class Cas
// {
//     [Key]
//     public string 序列 { get; set; } = null!;

//     public string? 企业名称 { get; set; }

//     public string? 新备注项 { get; set; }
// }

/// <summary>
/// abc
/// </summary>
[Table("abc")]
public class Abc
{
    [Key]
    public string 序号 { get; set; } = null!;

    public string? PK_wpid { get; set; }

    public string? 企业名 { get; set; }

    public string? 企业所属行业 { get; set; }

    public string? 岗位名 { get; set; }

    public string? 岗位类型 { get; set; }

    public string? 招聘人数 { get; set; }

    public string? 工作性质 { get; set; }

    public string? MinSalary { get; set; }

    public string? MaxSalary { get; set; }

    public string? 学历要求 { get; set; }

    public string? 性别要求 { get; set; }

    public string? 工作年限要求 { get; set; }

    public string? 省 { get; set; }
    public string? 市 { get; set; }
    public string? 区 { get; set; }
    public string? 地址 { get; set; }
    public string? 岗位描述 { get; set; }
    public string? 岗位亮点 { get; set; }
    public string? 是否蓝领 { get; set; }

    public string? Lat { get; set; }
    public string? Lng { get; set; }
}