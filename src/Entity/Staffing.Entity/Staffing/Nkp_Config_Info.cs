﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 诺快聘后台配置
/// </summary>
[Table("nkp_config_info")]
public class Nkp_Config_Info
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 别名
    /// </summary>
    public string? Alias { get; set; }

    /// <summary>
    /// 父节点Id
    /// </summary>
    public string? ParentId { get; set; }

    /// <summary>
    /// 全路径
    /// </summary>
    public string Level { get; set; } = default!;
    
    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 权限值
    /// </summary>
    public string? VerifyValue { get; set; }

    /// <summary>
    /// 图标名称
    /// </summary>
    public string? Image { get; set; }

    /// <summary>
    /// 权限所属模块
    /// </summary>
    public string? Module { get; set; }

    /// <summary>
    /// 启用状态
    /// </summary>
    public int Status { get; set; } = 1;
    
    /// <summary>
    /// 是否删除
    /// </summary>
    public int Deleted { get; set; } = 0;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 标签
    /// </summary>
    public List<string>? Tags { get; set; }
}

