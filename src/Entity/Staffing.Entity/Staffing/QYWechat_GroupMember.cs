﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Staffing.Entity.Staffing
{
    /// <summary>
    /// 企业微信群成员表
    /// </summary>
    /// <remarks>
    /// 用于存储群内成员列表，仅用于当顾问反馈问题时，可以在此处查看数据定位问题，于业务无用
    /// </remarks>
    [Table("qywechat_groupmember")]
    public class QYWechat_GroupMember
    {
        /// <summary>
        /// 群ID+群成员ID（主键）
        /// *由于不同群成员的ID可能一样，因此将【群ID+群成员ID】作为主键
        /// </summary>
        [Key]
        public required string GroupMemberID { get; set; }

        /// <summary>
        /// 群成员ID
        /// </summary>
        public required string MemberID { get; set; }

        /// <summary>
        /// 微信UnionId
        /// </summary>
        public required string WeChatUnionId { get; set; }

        /// <summary>
        /// 微信群ID
        /// </summary>
        public required string GroupChatID {  get; set; }

        /// <summary>
        /// 成员类型：1 - 企业成员，2 - 外部联系人
        /// </summary>
        public int UserType { get; set; }

        /// <summary>
        /// 入群时间
        /// </summary>
        public DateTime JoinTime { get; set; }

        /// <summary>
        /// 入群方式
        /// </summary>
        /// <remarks>
        /// 1 - 由群成员直接邀请入群，
        /// 2 - 由群成员通过链接邀请入群，
        /// 3 - 通过扫描群二维码入群
        /// </remarks>
        public int JoinScene { get; set; }

        /// <summary>
        /// 邀请者的企微userid
        /// </summary>
        public string? InvitorWeChatId { get; set; }

        /// <summary>
        /// 在群里的昵称
        /// </summary>
        public string? NickName { get; set; }

        /// <summary>
        /// 当前群成员版本号
        /// </summary>
        public required string MemberVersion { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedTime { get; set; }

        /// <summary>
        /// 退群后此字段为true
        /// </summary>
        public bool IsDel { get; set; } = false;

        public QYWechat_GroupChat QYWechat_GroupChat { get; set; } = default!;
    }
}
