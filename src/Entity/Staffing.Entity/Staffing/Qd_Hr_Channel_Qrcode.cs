﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 顾问渠道二维码
/// </summary>
[Table("qd_hr_channel_qrcode")]
public class Qd_Hr_Channel_Qrcode
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 渠道Id
    /// </summary>
    public string ChannelId { get; set; } = null!;

    /// <summary>
    /// AppId
    /// </summary>
    public string AppId { get; set; } = null!;

    /// <summary>
    /// 渠道二维码
    /// </summary>
    public string ChannelQrCode { get; set; } = null!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}
