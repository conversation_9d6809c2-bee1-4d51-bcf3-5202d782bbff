﻿using Config.CommonModel;
using Staffing.Model.Hr.Bounty;
using Staffing.Model.Hr.Dashboard;
using Staffing.Model.Hr.Project;

namespace Staffing.Core.Interfaces.Hr;

public interface IBountyService
{
    GetTeamBountysResponse GetTeamBountys(GetTeamBountys model);
    ExportTeamBountysResponse ExportTeamBountys(ExportTeamBountys model);
    GetRecentRecruitsResponse GetRecentRecruits(GetRecentRecruits model);
    GetStaffDataResponse GetStaffData(GetStaffData model);

    Task<byte[]> ExportZhaoPinJiaoFuKpi();
    GetDeptKpiResponse GetDeptKpi(GetDeptKpi model);
    NewTeamBounryResponse GetOrderPageList(NewTeamBountyRequest request);
    NewTeamBounryResponse GetCenterOrderPageList(CenterOrderListRequest request);
    CompanyOrderSumData GetCenterCompanyOrderSumData();
    PersonalOrderSumData GetCenterPersonalOrderSumData();
    CompanyOrderListResponse GetCenterCompanyOrderPageList(OrderListReqeust request);
    PersonalOrderListResponse GetCenterPersonalOrderPageList(OrderListReqeust request);
    OrderDetailPageListResponse GetOrderDetial(OrderDetailRequst request);
    List<UserInfo> GetDeliveryUsers();
    BountyAccountingResponse GetAccountingList(BountyAccountingRequst req);
    BountyRatioResponse GetBountyRatioList(ReqModel model);
    ContractListResponse GetProjectContractListBySettlementId(ReqModel model);
    SettlementHistoryDetail GetSettlementHistoryDetail(ReqModel model);
    SettlementHistoryResponse GetSettlementHistoryList(ReqModel model);
    SettlementInfo GetSettlementInfo(ReqModel model);
    Task<BountyRatioResponse> ComputeBountyRatioList(ReqModel model);
    Task<DeliveryReceiptResonse> DeliveryReceipt(DetailRequestModel req);
    //List<PostProfitPayment> GetPostProfitPayments(ReqModel model);
    /// <summary>
    /// 获取项目交易记录
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    TransactionListResp GetTransactionList(TransactionListReq req);
    /// <summary>
    /// 获取充值记录
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    ProjectFundsListResp GetProjectFundsList(ProjectFundsListReq req);
    /// <summary>
    /// 确认结算
    /// </summary>
    /// <returns></returns>
    Task<EmptyResponse> ConfirmSettlement(ConfirmSettlementReq request);
    Task<EmptyResponse> ApprovalSettlement(ApprovalSettlementReq request);
}