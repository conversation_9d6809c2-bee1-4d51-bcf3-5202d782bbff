using Infrastructure.Extend;
using Staffing.Model.Hr.TalentVirtual;
using Staffing.Core.Interfaces.Hr;

namespace Staffing.Api.Apis.Hr;

[Service(ServiceLifetime.Transient)]
public class TalentVirtualRouter : IHrRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("talentvirtual").WithTags("虚拟人才库");
        group.MapPost("/list", (TalentVirtualResumeListRequest model, ITalentVirtualService rep) => rep.GetTalentVirtualResumeList(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取虚拟人才库列表" });

        group.MapPost("/del", async (DeleteTalentVirtualResumeRequest model, ITalentVirtualService rep) => await rep.DeleteTalentVirtualResume(model))
            .WithOpenApi(operation => new(operation) { Summary = "删除人才库简历" });

        group.MapGet("/resume/{virtualId}", (string virtualId, ITalentVirtualService rep) => rep.GetTalentVirtualResumeDetails(virtualId))
            .WithOpenApi(operation => new(operation) { Summary = "获取虚拟人才库简历详情" });

        group.MapPut("/resume/details", (EditVirtualResumeDetailsRequest model, ITalentVirtualService rep) => rep.EditTalentVirtaualResume(model))
            .WithOpenApi(operation => new(operation) { Summary = "修改虚拟人才库简历" });

        group.MapPut("/resume/originalurl", (EditTalentVirtaualOriginalUrlRequest model, ITalentVirtualService rep) => rep.EditTalentVirtaualOriginalUrl(model))
            .WithOpenApi(operation => new(operation) { Summary = "修改虚拟人才库原始简历地址" });

        group.MapPost("/comment/list", (GetTalentVirtualResumeCommentRequest model, ITalentVirtualService rep) => rep.GetTalentVirtualResumeComment(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取虚拟人才库简历评论" });

        group.MapPost("/comment", (AddTalentVirtualResumeCommentRequest model, ITalentVirtualService rep) => rep.AddTalentVirtualResumeComment(model))
            .WithOpenApi(operation => new(operation) { Summary = "添加虚拟人才库简历评论" });

        group.MapDelete("/comment/{commentId}", (string commentId, ITalentVirtualService rep) => rep.DeleteTalentVirtualResumeComment(commentId))
            .WithOpenApi(operation => new(operation) { Summary = "删除虚拟人才库简历评论" });

        group.MapGet("/postlist", (ITalentVirtualService rep) => rep.GetTalentVirtualPostList())
            .WithOpenApi(operation => new(operation) { Summary = "获取虚拟人才库职位列表" });

        group.MapGet("/industrylist", (ITalentVirtualService rep) => rep.GetTalentVirtualIndustryList())
            .WithOpenApi(operation => new(operation) { Summary = "获取虚拟人才库行业列表" });

        group.MapPost("/resume/url", (UpLoadResumeRequest model, ITalentVirtualService rep) => rep.UpLoadResume(model))
            .WithOpenApi(operation => new(operation) { Summary = "上传简历并解析" });

        group.MapPost("/resume/originalurl/analyze", async (AnalyzeOriginalResumeRequest model, ITalentVirtualService rep) => await rep.AnalyzeOriginalResume(model))
            .WithOpenApi(operation => new(operation) { Summary = "解析原始简历" });

        group.MapGet("/uploadrecord", ([AsParameters] GetTalentUploadRecordListRequest model, ITalentVirtualService rep) => rep.GetTalentUploadRecordList(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取虚拟人才库上传简历记录" });

        group.MapGet("/uploadrecordsub", ([AsParameters] GetTalentUploadRecordSubListRequest model, ITalentVirtualService rep) => rep.GetTalentUploadRecordSubList(model))
            .WithOpenApi(operation => new(operation) { Summary = "获取虚拟人才库上传简历记录" });

        group.MapGet("/talentlabel", (ITalentVirtualService rep) => rep.GetTalentLabelList())
            .WithOpenApi(operation => new(operation) { Summary = "获取人才库标签列表（真实虚拟通用）" });

        group.MapPost("/talentlabel", (AddTalentLabelRequest model, ITalentVirtualService rep) => rep.AddTalentLabel(model))
            .WithOpenApi(operation => new(operation) { Summary = "添加人才库标签（真实虚拟通用）" });

        group.MapDelete("/talentlabel/{labelId}", (string labelId, ITalentVirtualService rep) => rep.DeleteTalentLabel(labelId))
            .WithOpenApi(operation => new(operation) { Summary = "删除人才库标签（真实虚拟通用）" });

        group.MapGet("/virtuallabel/{virtualId}", (string virtualId, ITalentVirtualService rep) => rep.GetTalentVirtualLabel(virtualId))
            .WithOpenApi(operation => new(operation) { Summary = "获取虚拟人才库标签" });

        group.MapPost("/virtuallabel", async (EditTalentVirtualLabelRequest model, ITalentVirtualService rep) => await rep.EditTalentVirtualLabel(model))
            .WithOpenApi(operation => new(operation) { Summary = "变更虚拟人才库标签" });
    }
}