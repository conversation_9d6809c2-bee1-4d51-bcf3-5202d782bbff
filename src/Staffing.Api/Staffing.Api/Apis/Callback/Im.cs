using Config;
using Config.CommonModel.Tencent;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using ServiceStack.Text;
using Infrastructure.CommonRepository.ZhaoPinYun;

namespace Staffing.Api.Apis.Callback;

[Service(ServiceLifetime.Transient)]
public class ImCallbackRouter : ICallbackRouterBase
{
    public void AddRoutes(RouteGroupBuilder group)
    {
        group.MapPost("/callback/tim", ([AsParameters] ImCallbackRequestQuery2 query, HttpRequest request, IOptions<ConfigManager> config, LogManager log) =>
        {
            var result = new ImBaseResponse
            {
                ActionStatus = "OK",
                ErrorInfo = string.Empty,
                ErrorCode = 0
            };

            if (query.SdkAppid != config.Value.TencentOptions!.ImAppId)
            {
                log.Error("Im回调出错", "非指定AppId的回调", query);
                return Task.FromResult(result);
            }

            try
            {
                var bodyString = RequestParams.GetParams(request.HttpContext);
                var bodyToken = JToken.Parse(bodyString);
                if (bodyToken.HasValues)
                {
                    log.Info("IM回调", "全部发送成功的消息", bodyString);
                    switch (query.CallbackCommand)
                    {
                        case "State.StateChange"://状态变更回调
                            var changeTime = bodyToken.Value<long>("EventTime");
                            var changInfo = bodyToken.Value<JToken>("Info");
                            var action = changInfo!.Value<string>("Action");
                            var imId = changInfo.Value<string>("To_Account");
                            var imOnlineInfo = new ImUserOnline
                            {
                                ImId = imId,
                                Active = action == "Login" ? 1 : 0,
                                Time = changeTime.FromUnixTimeMs()
                            };
                            MyRedis.Client.HSet(RedisKey.ImOnlineStatus, imId, imOnlineInfo);
                            break;
                        case "C2C.CallbackAfterSendMsg"://发单聊消息之后回调
                            var sendMsg = bodyToken.ToObject<CallbackC2CAfterSendMsg>();

                            if (sendMsg?.SendMsgResult == 0 && sendMsg.UnreadMsgNum > 0)//发送成功且有未读消息
                            {
                                var toUserOnline = MyRedis.Client.HGet<ImUserOnline?>(RedisKey.ImOnlineStatus, sendMsg.To_Account);

                                if (!(toUserOnline?.Active == 1 && toUserOnline.Time > DateTime.Now.AddDays(-1)))
                                    MyRedis.Client.ZAdd(SubscriptionKey.ImC2CMsgUnReadMsg, DateTime.Now.ToUnixTimeMs(), sendMsg.To_Account);
                            }

                            if (sendMsg?.SendMsgResult == 0 && 
                                sendMsg.From_Account?.StartsWith(Constants.ImSeekerIdPre) == true &&
                                sendMsg.From_Account?.StartsWith(Constants.ImHrIdPre) == false)//全部Seeker发送成功的消息
                            {
                                MyRedis.Client.SAdd(SubscriptionKey.ImC2CMsg, new Sub_ImC2CMsg
                                {
                                    FromUserId = sendMsg.From_Account,
                                    ToUserId = sendMsg.To_Account,
                                    MsgSeq = sendMsg.MsgSeq,
                                    MsgRandom = sendMsg.MsgRandom,
                                    MsgKey = sendMsg.MsgKey,
                                    MsgBody = sendMsg.MsgBody,
                                    MsgTime = DateTimeOffset.FromUnixTimeSeconds((long)sendMsg.MsgTime!).DateTime
                                });                              
                            }                            
                            break;
                        default:
                            break;
                    }
                }
                else
                {
                    log.Error("IM回调Body解析异常", bodyString, query);
                }
            }
            catch (Exception e)
            {
                log.Error("Im回调出错", Tools.GetErrMsg(e), query);
            }

            return Task.FromResult(result);
        }).WithOpenApi(operation => new(operation) { Summary = "IM回调" });
    }
}

/// <summary>
/// IM回调参数
/// </summary>
public class ImCallbackRequestQuery2
{
    /// <summary>
    /// App 在即时通信 IM 分配的应用标识
    /// </summary>
    [FromQuery]
    public string? SdkAppid { get; set; }

    /// <summary>
    /// 回调命令字
    /// </summary>
    [FromQuery]
    public string? CallbackCommand { get; set; }

    /// <summary>
    /// 可选，通常值为 JSON
    /// </summary>
    [FromQuery]
    public string? contenttype { get; set; }

    /// <summary>
    /// 客户端 IP 地址
    /// </summary>
    [FromQuery]
    public string? ClientIP { get; set; }

    /// <summary>
    /// 客户端平台，对应不同的平台类型，可能的取值有：RESTAPI（使用 REST API 发送请求）、Web（使用 Web SDK 发送请求）、Android、iOS、Windows、Mac、IPad、Unknown（使用未知类型的设备发送请求）
    /// </summary>
    [FromQuery]
    public string? OptPlatform { get; set; }
}