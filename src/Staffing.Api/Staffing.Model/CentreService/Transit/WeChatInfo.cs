namespace Staffing.Model.CentreService.Transit;

public class GetWeChatH5
{
    /// <summary>
    /// 微信UnionId
    /// </summary>
    public List<string> UnionIds { get; set; } = new List<string>();
}

public class GetWeChatH5Response
{
    public List<GetWeChatH5Info> Rows { get; set; } = new List<GetWeChatH5Info>();
}

public class GetWeChatH5Info
{
    public string WeChatId { get; set; } = default!;

    /// <summary>
    /// 诺聘公众号OpenId
    /// </summary>
    public string? WeChatH5OpenId { get; set; }

    /// <summary>
    /// 是否订阅诺聘公众号
    /// </summary>
    public bool WeChatH5Subscribe { get; set; }

    /// <summary>
    /// 诺快聘公众号OpenId
    /// </summary>
    public string? WeChatH5OpenId2 { get; set; }

    /// <summary>
    /// 是否订阅诺快聘公众号
    /// </summary>
    public bool WeChatH5Subscribe2 { get; set; }

    public DateTime UpdatedTime { get; set; } = DateTime.Now;
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}