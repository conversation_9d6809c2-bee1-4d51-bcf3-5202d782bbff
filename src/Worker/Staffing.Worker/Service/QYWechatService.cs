﻿using Config;
using Config.CommonModel.Business;
using Config.CommonModel.DingDing;
using Config.CommonModel.DingDingRoot;
using Config.CommonModel.FigureNoah;
using Config.CommonModel.QYWechat;
using Config.Enums;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Infrastructure.QYWechat;
using Infrastructure.QYWechat.Model;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using ServiceStack.Text;
using Staffing.Entity;
using Staffing.Entity.Staffing;
using System.Linq;

namespace Staffing.Worker.Service;

[Service(ServiceLifetime.Transient)]
public class QYWechatService
{
    private readonly IDbContextFactory<StaffingContext> _contextFactory;
    private readonly LogManager _logger;
    /// <summary>
    /// 注入
    /// </summary>
    public QYWechatService(IDbContextFactory<StaffingContext> contextFactory, LogManager logger)
    {
        _contextFactory = contextFactory;
        _logger = logger;
    }

    /// <summary>
    /// 导入企微群的信息及群成员
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task LoadQWGroupInfo(CancellationToken cancel)
    {
        //处理消息
        string? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            try
            {
                msg = MyRedis.Client.SPop<string?>(SubscriptionKey.QYWechatLoadGroupIds);

                if (string.IsNullOrWhiteSpace(msg))
                {
                    await Task.Delay(500, cancel);
                    continue;
                }

                var (Result, ErrMsg) = await SaveCreateGroupChat(msg);
                if (!Result)
                    _logger.Error($"{SubscriptionKey.QYWechatLoadGroupIds}消息处理出错", ErrMsg, (msg ?? string.Empty).ToString());
            }
            catch (TaskCanceledException)
            { return; }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.QYWechatLoadGroupIds}消息处理出错", Tools.GetErrMsg(e), (msg ?? string.Empty).ToString());
                await Task.Delay(500, cancel);
            }

            await Task.Delay(100, cancel);
        }
    }

    /// <summary>
    /// 更新指定群成员证书报名关联企业群 进群状态及名称
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task QYWechatMemUpToCerRegs(CancellationToken cancel)
    {
        //处理消息
        QYWechat_MemChangeCacheInfo? msg = null;
        while (!cancel.IsCancellationRequested)
        {
            try
            {
                msg = MyRedis.Client.RPop<QYWechat_MemChangeCacheInfo?>(SubscriptionKey.QYWechatMemUpToCerRegs);//new QYWechat_MemChangeCacheInfo { GroupChatID = "wrpiIkBgAAeb-R9Xim0qq24eppPFdD3Q", WeChatUnionId = "ovpVR0QIWHto1J8PcA-00U2bXnCE", ChangeType = QYWechat_MemChangeType.入群 }; 

                if (msg == null)
                {
                    await Task.Delay(500, cancel);
                    continue;
                }

                var (Result, ErrMsg) = SaveCertificateRegistrationForm(msg);
                if (!Result)
                    _logger.Error($"{SubscriptionKey.QYWechatMemUpToCerRegs}消息处理出错", ErrMsg, (msg?.ToJsonString() ?? string.Empty).ToString());
            }
            catch (TaskCanceledException)
            { return; }
            catch (Exception e)
            {
                _logger.Error($"{SubscriptionKey.QYWechatMemUpToCerRegs}消息处理出错", Tools.GetErrMsg(e), (msg?.ToJsonString() ?? string.Empty).ToString());
                await Task.Delay(500, cancel);
            }

            await Task.Delay(10, cancel);
        }
    }

    /// <summary>
    /// 更新指定群成员证书报名关联企业群 进群状态及名称
    /// </summary>
    /// <param name="GroupChatID">群ID</param>
    /// <param name="WeChatUnionId">群成员的UnionID</param>
    /// <returns></returns>
    private (bool Result, string ErrMsg) SaveCertificateRegistrationForm(QYWechat_MemChangeCacheInfo info)
    {
        //根据UnionID获取加入了哪些群
        using (var db = _contextFactory.CreateDbContext())
        {
            if (string.IsNullOrEmpty(info.WeChatUnionId) && string.IsNullOrEmpty(info.UserID))
                return (false, "WeChatUnionId和UserID均为空，无法定位到用户");

            User_OpenId? seeker;
            if (!string.IsNullOrEmpty(info.UserID))
                seeker = db.User_OpenId.FirstOrDefault(x => x.UserId == info.UserID && !string.IsNullOrEmpty(x.UnionId) && x.Type == ClientType.SeekerApplet);
            else//根据UnionID获取关联的求职者
                seeker = GetSeekersByWechatUnionID(info.WeChatUnionId!, db);
            if (seeker == null)
                return (true, "未找到该用户，无需更新");

            //var tables = db.CertificateTable
            //    .Where(x => !string.IsNullOrEmpty(x.EnterpriseWechatGroupName) && x.EnterpriseWechatGroupName.Contains(GetCheckGroupChatID(info.GroupChatID))
            //    ).ToList();
            //if (tables.Count == 0)
            //    return (true, "无证书关联该企微群");
            //var tableIds = tables.Select(x => x.Id).ToList();
            var predicate = PredicateBuilder.New<CertificateRegistrationForm>(x => x.Creator == seeker.UserId);//&& tableIds.Contains(x.CertificateId)

            if (db.CertificateRegistrationForm.Any(predicate))
            {
                //先全部取关，然后再根据群成员设置关联
                db.CertificateRegistrationForm.Where(x => x.Creator == seeker.UserId //&& tableIds.Contains(x.CertificateId)
                        && x.CertificateGroupType == CertificateGroupTypeEnum.已入群)
                    .ExecuteUpdate(u => u.SetProperty(s => s.WeChatGroupName, string.Empty)
                                     .SetProperty(s => s.CertificateGroupType, CertificateGroupTypeEnum.未入群));
                db.SaveChanges();

                //获取当前用户加入的群名称及加入时间
                var memGroup = db.QYWechat_GroupMember.Where(x => x.WeChatUnionId == seeker.UnionId && !x.IsDel)
                    .Select(x => new
                    {
                        x.WeChatUnionId,
                        x.JoinTime,
                        x.GroupChatID,
                        GroupName = x.QYWechat_GroupChat.Name,
                    })
                    .ToList();

                var cerRegSql = db.CertificateRegistrationForm
                    .Where(predicate)
                    .Include(i => i.CertificateTable)
                    .ToList();
                //更新关联的报名记录
                foreach (var item in cerRegSql)
                {
                    //根据报名的证书 关联的企微群ID，获取用户最后加入的群名称
                    var mg = memGroup.Where(x => !string.IsNullOrEmpty(item.CertificateTable.EnterpriseWechatGroupName) && item.CertificateTable.EnterpriseWechatGroupName.Contains(GetCheckGroupChatID(x.GroupChatID)))
                        .OrderByDescending(o => o.JoinTime)
                        .FirstOrDefault(x => x.WeChatUnionId == seeker.UnionId);
                    if (mg != null)
                    {
                        item.WeChatGroupName = mg.GroupName;
                        item.CertificateGroupType = CertificateGroupTypeEnum.已入群;
                    }
                }
                db.SaveChanges();
            }
            else
                return (true, "该用户尚未报名证书，无需更新");
        }

        return (true, "同步成功");
    }

    private static string GetCheckGroupChatID(string GroupChatID)
    {
        return ":" + GroupChatID + "}";
    }
    /// <summary>
    /// 更新整个群的成员证书报名关联企业群 进群状态及名称
    /// </summary>
    /// <returns></returns>
    private static (bool Result, string? ErrMsg) SaveCertificateRegistrationForm()
    {
        #region 方式一
        ////根据UnionID批量获取关联的求职者ID
        //var seekers = GetSeekersByWechatUnionID(Member_List.Where(x => !string.IsNullOrEmpty(x.UnionId)).Select(x => x.UnionId!).ToList());
        //var seekerUserIds = seekers.Select(x => x.UserId).ToList();

        ////根据UnionID获取加入了哪些群
        //using (var db = _contextFactory.CreateDbContext())
        //{
        //    //先全部取关，然后再根据群成员设置关联
        //    db.CertificateRegistrationForm.Where(x => x.CertificateTable.EnterpriseWechatGroupId.Any(g => g.GropChatId == GroupChatID))
        //        .ExecuteUpdate(u => u.SetProperty(s => s.WeChatGroupName, string.Empty)
        //                             .SetProperty(s => s.CertificateGroupType, CertificateGroupTypeEnum.未入群));
        //    db.SaveChanges();
        //    //获取关联了哪些证书
        //    var cerTab = db.CertificateTable.Where(x => x.EnterpriseWechatGroupId.Any(g => g.GropChatId == GroupChatID)).ToList();
        //    //按证书分组，更新关联的报名记录
        //    foreach (var cerTabItem in cerTab)
        //    {
        //        var cerRegItems = db.CertificateRegistrationForm.Where(x => x.CertificateTable.Id == cerTabItem.Id && !string.IsNullOrEmpty(x.Creator) && seekerUserIds.Contains(x.Creator));
        //        foreach (var cerRegItem in cerRegItems.Chunk(200))
        //        {
        //            var cerRegUnionId = (from cr in cerRegItem
        //                                 join s in seekers on cr.Creator equals s.UserId
        //                                 select s.UnionId).ToList();

        //            var memGroup = db.QYWechat_GroupMember.Where(x => cerTabItem.EnterpriseWechatGroupId.Any(g => g.GropChatId == x.GroupChatID)
        //                && cerRegUnionId.Contains(x.WeChatUnionId))
        //                .GroupBy(x => x.WeChatUnionId)
        //                .Select(x => new
        //                {
        //                    WeChatUnionId = x.Key,
        //                    GroupName = x.OrderByDescending(o => o.JoinTime).FirstOrDefault().QYWechat_GroupChat.Name,
        //                });

        //            foreach (var item in cerRegItem)
        //            {
        //                var mg = memGroup.FirstOrDefault(x => x.WeChatUnionId == seekers.First(s => s.UserId == item.Creator).UnionId);
        //                if (mg != null)
        //                {
        //                    item.WeChatGroupName = mg.GroupName;
        //                    item.CertificateGroupType = CertificateGroupTypeEnum.已入群;
        //                }
        //            }
        //            db.SaveChanges();
        //        }
        //    }
        #endregion

        #region 方式二
        //using (var db = _contextFactory.CreateDbContext())
        //{
        //    //获取关联的证书列表
        //    var cerfIdList = db.CertificateTable.Where(x => x.EnterpriseWechatGroupId.Any(g => g.GropChatId == GroupChatID))
        //        .Select(x => x.Id)
        //        .ToList();
        //    //获取需更新的数据
        //    var subQuery = from cerf in db.CertificateRegistrationForm
        //                   where !string.IsNullOrEmpty(cerf.Creator) && seekerUserIds.Contains(cerf.Creator)
        //                   join cer in db.CertificateTable
        //                       on cerf.CertificateId equals cer.Id
        //                   where cerfIdList.Contains(cerf.Id)
        //                   join uo in db.User_OpenId
        //                       on cerf.Creator equals uo.UserId
        //                   where uo.UnionId != ""
        //                   join qwge in db.QYWechat_GroupMember
        //                       on uo.UnionId equals qwge.WeChatUnionId
        //                   join qwg in db.QYWechat_GroupChat
        //                       on qwge.GroupChatID equals qwg.GroupChatId
        //                   where EF.Functions.Like(
        //                       cer.EnterpriseWechatGroupId,
        //                       $"%\"{qwg.GroupChatId}\"%"
        //                   )
        //                   group new { cerf, qwg, qwge } by cerf.Id into g
        //                   select new
        //                   {
        //                       Id = g.Key,
        //                       QWGroupName = g
        //                           .OrderByDescending(x => x.qwge.JoinTime)
        //                           .Select(x => x.qwg.Name)
        //                           .FirstOrDefault()
        //                   };
        //    //关联主表并过滤需更新的记录
        //    var updateList = from c in db.CertificateRegistrationForm
        //                     join t in subQuery
        //                         on c.Id equals t.Id
        //                     where c.WeChatGroupName != t.QWGroupName
        //                     select new { Certificate = c, t.QWGroupName };

        //    //批量更新实体
        //    foreach (var item in updateList.Chunk(100))
        //    {
        //        foreach (var item2 in item)
        //        {
        //            item2.Certificate.WeChatGroupName = item2.QWGroupName;
        //            item2.Certificate.CertificateGroupType = CertificateGroupTypeEnum.已入群;
        //        }
        //        db.SaveChanges();
        //    }
        //}
        #endregion

        #region 方式三
        //UPDATE certificate_registrationform SET WeChatGroupName = '', CertificateGroupType = 0 WHERE CertificateGroupType<>0

        //UPDATE certificate_registrationform AS C
        //JOIN(
        //    SELECT CERF.Id, UO.UserId, QWG.`Name` AS QWGroupName, QWGE.JoinTime,
        //                 ROW_NUMBER() OVER(PARTITION BY CERF.Id ORDER BY QWGE.JoinTime DESC) AS row_num
        //    FROM certificate_registrationform AS CERF
        //    JOIN certificate_table AS CER ON CERF.CertificateId = CER.Id AND IFNULL(CERF.Creator, '') <> ''
        //    JOIN user_openid UO ON UO.UnionId<>'' AND UO.UserId = CERF.Creator-- AND Type < 3
        //    JOIN qywechat_groupmember AS QWGE ON QWGE.WeChatUnionId = UO.UnionId
        //    JOIN qywechat_groupchat AS QWG ON QWG.GroupChatID = QWGE.GroupChatID AND CER.EnterpriseWechatGroupId LIKE CONCAT('%"', QWG.GroupChatID, '"%')
        //) T ON T.row_num = 1 AND T.Id = C.Id AND T.QWGroupName<> C.WeChatGroupName
        //SET C.WeChatGroupName = T.QWGroupName,C.CertificateGroupType = 1
        #endregion

        return (true, string.Empty);
    }

    /// <summary>
    /// 建群
    /// </summary>
    /// <param name="ChatId">群ID</param>
    /// <returns></returns>
    private async Task<(bool Result, string ErrMsg)> SaveCreateGroupChat(string ChatId)
    {
        //获取群详情
        var groupInfo = await Infrastructure.QYWechat.QYWechatHelper.GetGroupChatInfo(ChatId);
        if (groupInfo == null || groupInfo.Group_Chat == null)
            return (false, "根据群ID未获取到群信息");
        else if (!groupInfo.Result)
            return (false, groupInfo.ErrMsg ?? "根据群ID获取群信息失败");
        else if (string.IsNullOrEmpty(groupInfo.Group_Chat.Owner))
            return (false, "获取的群信息中群主ID为空");

        using (var db = _contextFactory.CreateDbContext())
        {
            //更新群信息到数据库
            var old = db.QYWechat_GroupChat.Find(ChatId);
            if (old == null)
                db.QYWechat_GroupChat.Add(new QYWechat_GroupChat()
                {
                    GroupChatId = groupInfo.Group_Chat.Chat_Id!,
                    Name = groupInfo.Group_Chat.Name ?? string.Empty,
                    GroupCreateTime = Infrastructure.QYWechat.QYWechatHelper.ExchangeTimeStamp(groupInfo.Group_Chat.Create_Time),
                    OwnerId = groupInfo.Group_Chat.Owner!
                });
            else
            {
                old.Name = groupInfo.Group_Chat.Name ?? string.Empty;
                old.GroupCreateTime = Infrastructure.QYWechat.QYWechatHelper.ExchangeTimeStamp(groupInfo.Group_Chat.Create_Time);
                old.OwnerId = groupInfo.Group_Chat.Owner!;
            }

            db.SaveChanges();
        }

        //当有群成员时，更新群成员信息
        SaveGroupMember(groupInfo.Group_Chat.Chat_Id!, groupInfo.Group_Chat.Member_Version!,
            groupInfo.Group_Chat.Member_List);

        return (true, string.Empty);
    }
    /// <summary>
    /// 当有群成员时，更新群成员信息
    /// </summary>
    /// <param name="GroupChatID">群ID</param>
    /// <param name="Member_List">群成员列表</param>
    /// <returns></returns>
    private (bool Result, string? ErrMsg) SaveGroupMember(string GroupChatID, string Member_Version,
        List<GroupChatMember>? Member_List)
    {
        if (Member_List == null || Member_List.Count == 0)
            return (true, "无群成员");

        #region 暂不做群员和Seeker.UserId的关联了
        ////根据UnionID批量获取关联的求职者ID
        //var seekers = GetSeekersByWechatUnionID(Member_List.Where(x => !string.IsNullOrEmpty(x.UnionId)).Select(x => x.UnionId!).ToList());
        ////排除根据UnionID未获取到关联求职者的记录
        //var tempSUnionIDs = seekers.Select(x => x.UnionId).ToList();
        //if (Member_List.Exists(x => !tempSUnionIDs.Contains(x.UnionId)))
        //{
        //    _log.Error("企业微信群：更新群成员", $"存在UnionID不存在的情况，不存在的记录：{Member_List.Where(x => !tempSUnionIDs.Contains(x.UnionId)).ToList().ToJsonString()}");
        //    Member_List.RemoveAll(x => !tempSUnionIDs.Contains(x.UnionId));
        //    if (Member_List.Count == 0)
        //        return (false, "获取HR信息失败");
        //}
        #endregion

        using (var db = _contextFactory.CreateDbContext())
        {
            bool IsUpdateDB = false;//确认是否需更新数据库

            var members = db.QYWechat_GroupMember.Where(x => x.GroupChatID == GroupChatID).ToList();
            var mIds = members.Select(x => x.MemberID).ToList();

            List<QYWechat_MemChangeCacheInfo> cacheData = [];
            //添加不存在的记录
            var newModel = Member_List.Where(x => !string.IsNullOrEmpty(x.UserId) && !mIds.Contains(x.UserId))
                .Select(x => new QYWechat_GroupMember
                {
                    GroupChatID = GroupChatID,
                    GroupMemberID = QYWechatHelper.CreateGroupMemberId(GroupChatID, x.UserId!),
                    MemberID = x.UserId!,
                    MemberVersion = Member_Version ?? string.Empty,
                    WeChatUnionId = x.UnionId ?? string.Empty,
                    UserType = (int)x.Type,
                    JoinTime = Infrastructure.QYWechat.QYWechatHelper.ExchangeTimeStamp(x.Join_Time),
                    JoinScene = (int)x.Join_Scene,
                    InvitorWeChatId = x.Invitor?.UserId ?? string.Empty,
                    NickName = x.Group_Nickname ?? string.Empty
                })
                .ToList();
            if (newModel.Count > 0)
            {
                IsUpdateDB = true;
                db.QYWechat_GroupMember.AddRange(newModel);

                if (newModel.Exists(x => !string.IsNullOrEmpty(x.WeChatUnionId)))
                    cacheData.AddRange(newModel.Where(x => !string.IsNullOrEmpty(x.WeChatUnionId))
                        .Select(x => new QYWechat_MemChangeCacheInfo
                        {
                            //ChangeType = QYWechat_MemChangeType.入群,
                            //GroupChatID = x.GroupChatID,
                            WeChatUnionId = x.WeChatUnionId
                        }));
            }
            //更新已存在的记录
            if (Member_List.Any(x => !string.IsNullOrEmpty(x.UserId) && mIds.Contains(x.UserId)))
            {
                IsUpdateDB = true;
                Member_List.Where(x => !string.IsNullOrEmpty(x.UserId) && mIds.Contains(x.UserId)).ForEach((GroupChatMember x) =>
                {
                    var temp = members.First(t => t.MemberID == x.UserId);

                    if (temp.IsDel && !string.IsNullOrEmpty(x.UnionId))
                        cacheData.Add(new QYWechat_MemChangeCacheInfo
                        {
                            //ChangeType = QYWechat_MemChangeType.入群,
                            //GroupChatID = GroupChatID,
                            WeChatUnionId = x.UnionId
                        });

                    temp.IsDel = false;
                    temp.MemberVersion = Member_Version ?? string.Empty;
                    temp.WeChatUnionId = x.UnionId ?? string.Empty;
                    temp.UserType = (int)x.Type;
                    temp.JoinTime = Infrastructure.QYWechat.QYWechatHelper.ExchangeTimeStamp(x.Join_Time);
                    temp.JoinScene = (int)x.Join_Scene;
                    temp.InvitorWeChatId = x.Invitor?.UserId ?? string.Empty;
                    temp.NickName = x.Group_Nickname ?? string.Empty;
                });
            }
            //删除退群的记录
            var tempids = Member_List.Select(x => x.UserId).ToList();
            if (members.Any(x => !tempids.Contains(x.MemberID)))
            {
                IsUpdateDB = true;
                members.Where(x => !tempids.Contains(x.MemberID)).ForEach((QYWechat_GroupMember x) =>
                {
                    x.IsDel = true;

                    if (!string.IsNullOrEmpty(x.WeChatUnionId))
                        cacheData.AddRange(
                            newModel.Where(x => !string.IsNullOrEmpty(x.WeChatUnionId))
                            .Select(x => new QYWechat_MemChangeCacheInfo
                            {
                                //ChangeType = QYWechat_MemChangeType.退群,
                                //GroupChatID = x.GroupChatID,
                                WeChatUnionId = x.WeChatUnionId
                            }));
                });
            }

            if (IsUpdateDB)
                db.SaveChanges();

            if (cacheData.Count > 0)
                MyRedis.Client.LPush(SubscriptionKey.QYWechatMemUpToCerRegs, cacheData.ToArray());
        }

        return (true, string.Empty);
    }

    /// <summary>
    /// 根据UnionID获取关联的求职者信息
    /// </summary>
    /// <param name="Mobile"></param>
    /// <returns></returns>
    /// <exception cref="BadRequestException"></exception>
    private static User_OpenId? GetSeekersByWechatUnionID(string UnionID, StaffingContext context)
    {
        if (string.IsNullOrEmpty(UnionID))
            return null;
        var uos = context.User_OpenId
            .AsNoTracking()
            .Where(x => x.UnionId == UnionID)
            .FirstOrDefault();
        return uos;
    }
}
