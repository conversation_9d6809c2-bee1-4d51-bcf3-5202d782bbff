/*
 Navicat Premium Data Transfer

 Source Server         : 诺聘**************（测试环境）
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : **************:3306
 Source Schema         : staffing

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 07/03/2025 20:30:29
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for thirdparty_education_dic_relation
-- ----------------------------
DROP TABLE IF EXISTS `thirdparty_education_dic_relation`;
CREATE TABLE `thirdparty_education_dic_relation`  (
  `Id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `ThirdEducation` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方学历要求',
  `NkpEducation` int NULL DEFAULT NULL COMMENT '诺快聘学历要求字典',
  `UpdatedTime` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `CreatedTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `Source` int NOT NULL COMMENT '岗位来源 1：职多多 2：58魔方 3：后续'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '第三方与诺快聘学历字典对照表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
