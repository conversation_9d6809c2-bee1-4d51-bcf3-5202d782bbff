﻿using Config;
using Config.CommonModel.DingDingRoot;
using Infrastructure.Common;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Newtonsoft.Json;
using Staffing.Entity;
namespace Staffing.Api;

public class TestFunc
{
    private readonly StaffingContext _context;
    private readonly CommonDicService _commonDicService;
    private readonly DingDingRootHelper _dingDingRootHelper;
    public TestFunc(StaffingContext context, CommonDicService commonDicService, DingDingRootHelper dingDingRootHelper)
    {
        _context = context;
        _commonDicService = commonDicService;
        _dingDingRootHelper = dingDingRootHelper;
    }
    public void Test()
    {
        //处理消息
        var recruitId = "272361142932736646";
        var recruitInfo = _context.Recruit.Where(x => x.RecruitId == recruitId)
        .Select(s => new
        {
            s.HrId,
            s.CreatedTime, // 投递时间
            ProjectName = s.Post_Delivery.Post_Team.Project_Team.Project.Agent_Ent.Name,               // 项目名称
            CompanyName = s.Post_Delivery.Post_Team.Project_Team.Project.Agent_Ent.Name,// 代招企业名称
            s.Post_Delivery.User_Seeker.NickName,                                            // 求职者名称
            RegionId = s.Post_Delivery.User_Seeker.RegionId,                                 // 求职者地区id
            Education = s.Post_Delivery.User_Seeker.User_Resume.Education.GetDescription(),  // 求职者学历
            Sex = s.Post_Delivery.User_Seeker.User_Resume.Sex.GetDescription(),              // 求职者性别
            Age = Tools.GetAgeByBirthdate(s.Post_Delivery.User_Seeker.User_Resume.Birthday), // 求职者年龄
            Avatar = s.Post_Delivery.User_Seeker.Avatar,                                     // 求职者头像
            Source = s.Post_Delivery.User_Seeker.Source.GetDescription(),                    // 求职者来源
            PostName = s.Post_Delivery.Post.Name,          // 职位名称
            Category = s.Post_Delivery.Post.Category       // 职位类别编码
        }).First();
        if (recruitInfo == null)
            Console.WriteLine("xxx");

        var categoryInfo = _commonDicService.GetCategory(recruitInfo!.Category + "");
        var regionInfo = _commonDicService.GetCityById(recruitInfo.RegionId + "");
        var msgParam = new DingRobotMDRecruitMsgParam
        {
            ApplyTime = recruitInfo.CreatedTime,
            ProjectName = recruitInfo.ProjectName,
            CompanyName = recruitInfo.CompanyName,
            SeekerName = recruitInfo.NickName,
            City = regionInfo.CityName,
            Education = recruitInfo.Education,
            Sex = recruitInfo.Sex,
            Age = recruitInfo.Age,
            Avatar = recruitInfo.Avatar,
            Source = Exchange(recruitInfo.Source),
            PostName = recruitInfo.PostName,
            Category = categoryInfo.CategoryLevel3Name
        };

        // 生成消息内容
        string projectName = string.IsNullOrWhiteSpace(msgParam.PostName) ? "项目名称无" : msgParam.PostName;
        string companyName = string.IsNullOrWhiteSpace(msgParam.CompanyName) ? "代招公司无" : msgParam.CompanyName;
        string postName = string.IsNullOrWhiteSpace(msgParam.PostName) ? "岗位名称无" : msgParam.PostName;
        string category = string.IsNullOrWhiteSpace(msgParam.Category) ? "岗位类别无" : msgParam.Category;
        string seekerName = string.IsNullOrWhiteSpace(msgParam.SeekerName) ? "求职者名称无" : msgParam.SeekerName;
        string seekerAge = msgParam.Age == null ? "年龄无" : (msgParam.Age + "岁");
        string seekerSex = string.IsNullOrWhiteSpace(msgParam.Sex) ? "性别无" : msgParam.Sex;
        string seekerCity = string.IsNullOrWhiteSpace(msgParam.City) ? "地区无" : msgParam.City;
        string seekerEducation = string.IsNullOrWhiteSpace(msgParam.Education) ? "学历无" : msgParam.Education;

        var messageJson = new
        {
            title = "简历投递消息",
            text = $"{msgParam.ApplyTime?.ToString("M月d日 HH:mm")} **求职者通过“{msgParam.Source}”发起了报名** \n " +
            $"*** \n" +
            $"> ##### 项目名称：{projectName}\n " +
            $"> 公司：{companyName}\n >" +
            $"*** \n" +
            $"> ##### 岗位名称：{postName}\n " +
            $"> 岗位类别：{category}\n >" +
            $"*** \n" +
            $"> ##### 求职者姓名：{seekerName}\n " +
            $"> 年龄：{seekerAge}\n" +
            $"> 性别：{seekerSex}\n" +
            $"> 地区：{seekerCity}\n" +
            $"> 学历：{seekerEducation}\n " +
            $"*** \n" +
            $"#### [点击处理]({GetUrl()}) \n"
        };

        var request = new SendDingDingRootMessageRequest
        {
            userId = "16805061646817873",
            msgParam = JsonConvert.SerializeObject(messageJson)
        };

        // 发送钉钉机器人消息
        _dingDingRootHelper.SendDingDingRootMDMessage(request).GetAwaiter().GetResult();
    }

    //public void Test2()
    //{
    //    _dingDingRootHelper.SendDingDingRobotMessageForSummary(new SendDingRobotMDMessageRequest
    //    {
    //        userId = "16805061646817873",
    //        MDMsgParam = new DingRobotMDRecruitMsgParam
    //        {
    //            ProjectNum = 1,
    //            PostNum = 1,
    //            ResumeNum = 3,
    //        }
    //    }).GetAwaiter().GetResult();
    //}

    private string Exchange(string source)
    {
        return source switch
        {
            "微信小程序" => "微信小程序",
            "Web" => "诺快聘web端",
            "快手小程序" => "快手小程序",
            "快招工简历" => "快招工",
            "抖音小程序" => "抖音小程序",
            "简历导入" => "简历导入",
            "诺聘" => "诺聘平台",
            "其他" => "其他平台",
            _ => ""
        };
    }

    private static string GetUrl()
    {
        string redirectUrl = $"https://linggong.nuopin.cn/ddlogin.html?corpId%3D1";//&corpId={Constants.CorpId} source=1是自定义变量，表示来源钉钉机器人，用来做跳转
        string url = $"dingtalk://dingtalkclient/action/openapp?corpid={Constants.CorpId}&container_type=work_platform&app_id=0_{Constants.DingDingAgent}&redirect_type=jump&redirect_url={redirectUrl}";
        return url;
    }
}
