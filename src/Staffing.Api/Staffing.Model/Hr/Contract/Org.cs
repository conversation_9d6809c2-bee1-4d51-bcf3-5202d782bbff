using Config.CommonModel;

namespace Staffing.Model.Hr.Contract;

public class UpdateEntOrg
{
    /// <summary>
    /// 机构名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 机构证件号
    /// </summary>
    public string? IdNumber { get; set; }
}

public class UpdateEntOrgResponse
{
    /// <summary>
    /// E签宝机构Id
    /// </summary>
    public string? EOrgId { get; set; }
}

public class DeleteEntOrg
{
    /// <summary>
    /// 标识
    /// </summary>
    public string? Id { get; set; }
}


public class GetEntOrg : QueryRequest
{
    /// <summary>
    /// 机构名称
    /// </summary>
    public string? Name { get; set; }
}

public class GetEntOrgResponse : QueryResponse
{
    public List<EntOrgInfo> Rows { get; set; } = default!;
}

public class EntOrgInfo
{
    /// <summary>
    /// 标识
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 机构账号Id
    /// </summary>
    public string? OrgId { get; set; }

    /// <summary>
    /// 企业证件号，需传入真实存在的证件信息
    /// </summary>
    public string? IdNumber { get; set; }

    /// <summary>
    /// 机构名称
    /// </summary>
    public string? OrgName { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }
}