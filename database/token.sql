-- ----------------------------
-- refresh-token
-- ----------------------------
DROP TABLE IF EXISTS `token_refresh`;
CREATE TABLE `token_refresh` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `UserId` varchar(18) NOT NULL,
  `Type` int(2) NULL COMMENT '类型',
  `Token` varchar(50) NOT NULL COMMENT 'token',
  `ExpirationTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_token_refresh_Token` (`Token`),
  KEY `idx_token_refresh_Type_UserId` (`Type`, `UserId`),
  KEY `idx_token_refresh_ExpirationTime` (`ExpirationTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- access-token
-- ----------------------------
DROP TABLE IF EXISTS `token_access`;
CREATE TABLE `token_access` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `RefreshTokenId` int(11) NULL COMMENT 'RefreshTokenId',
  `UserId` varchar(18) NOT NULL,
  `Type` int(2) NULL COMMENT '类型',
  `Token` varchar(64) NOT NULL COMMENT 'token',
  `ExpirationTime` datetime(6) NOT NULL DEFAULT NOW(6),
  `CreatedTime` datetime(6) NOT NULL DEFAULT NOW(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_token_access_Token` (`Token`),
  KEY `idx_token_access_Type_UserId` (`Type`, `UserId`),
  KEY `idx_token_access_ExpirationTime` (`ExpirationTime` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;