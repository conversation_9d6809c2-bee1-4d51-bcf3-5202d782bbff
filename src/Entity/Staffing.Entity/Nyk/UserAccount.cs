﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Nyk;

/// <summary>
/// 用户账号表
/// </summary>
[Table("UserAccount")]
public class UserAccount
{
    /// <summary>
    /// id
    /// </summary>
    [Key]
    public string PK_UAID { get; set; } = default!;

    public string PK_UID { get; set; } = default!;

    public string LoginName { get; set; } = default!;

    /// <summary>
    /// 更新时间
    /// </summary>
    public int IsRemove { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
}
