﻿using Config.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// Hr初筛上传简历导入记录
/// </summary>
[Table("recruit_upload_record")]
public class Recruit_Upload_Record
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 协同职位id
    /// </summary>
    public string TeamPostId { get; set; } = default!;

    /// <summary>
    /// 上传人Id
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 文件地址
    /// </summary>
    public string FileUrl { get; set; } = default!;

    /// <summary>
    /// 文件名称
    /// </summary>
    public string FileName { get; set; } = default!;

    /// <summary>
    /// 文件扩展名
    /// </summary>
    public TalentUpLoadFileExtension FileExtension { get; set; }

    /// <summary>
    /// 重复类型
    /// </summary>
    public RecruitUpLoadRepeatType RepeatType { get; set; }

    /// <summary>
    /// 导入状态
    /// </summary>
    public RecruitUpLoadStatus UpLoadStatus { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 求职者姓名
    /// </summary>
    public string SeekerName { get; set; } = default!;

    /// <summary>
    /// 求职者邮箱
    /// </summary>
    public string SeekerEmail { get; set; } = default!;

    /// <summary>
    /// 求职者电话
    /// </summary>
    public string SeekerPhone { get; set; } = default!;

    /// <summary>
    /// 求职者学历
    /// </summary>
    public EducationType? SeekerEducation { get; set; }

    /// <summary>
    /// 小析解析简历内容
    /// </summary>
    public AnalysisResume ResumeInfo { get; set; } = new AnalysisResume();

    public Post_Team Post_Team { get; set; } = default!;

    public User_Hr User_Hr { get; set; } = default!;

}

/// <summary>
/// 小析解析模型
/// </summary>
public class AnalysisResume
{
    /// <summary>
    /// 返回码
    /// </summary>
    public int errorcode { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? errormessage { get; set; }

    /// <summary>
    /// 简历头像url
    /// </summary>
    public string? avatar_url { get; set; }

    /// <summary>
    /// 简历解析
    /// </summary>
    public parsing_result? parsing_result { get; set; }

    /// <summary>
    /// 画像解析
    /// </summary>
    public predicted_result? predicted_result { get; set; }
}

#region 简历解析
/// <summary>
/// 小析解析简历解析
/// </summary>
public class parsing_result
{
    /// <summary>
    /// 基本信息字段
    /// </summary>
    public basic_info_sub? basic_info { get; set; }

    /// <summary>
    /// 联系方式字段
    /// </summary>
    public contact_info_sub? contact_info { get; set; }

    /// <summary>
    /// 教育背景字段
    /// </summary>
    public List<education_experience_sub>? education_experience { get; set; }

    /// <summary>
    /// 工作经历字段
    /// </summary>
    public List<work_experience_sub>? work_experience { get; set; }

    /// <summary>
    /// 项目经历
    /// </summary>
    public List<project_experience_sub>? project_experience { get; set; }

    /// <summary>
    /// 补充信息
    /// </summary>
    public others_sub? others { get; set; }
}

/// <summary>
/// 基本信息（子类）
/// </summary>
public class basic_info_sub
{
    /// <summary>
    /// 姓名
    /// </summary>
    public string name { get; set; } = string.Empty;

    /// <summary>
    /// 性别
    /// </summary>
    public string gender { get; set; } = string.Empty;

    /// <summary>
    /// 学历
    /// </summary>
    public string degree { get; set; } = string.Empty;

    /// <summary>
    /// 开始工作年限
    /// </summary>
    public string work_start_year { get; set; } = string.Empty;

    /// <summary>
    /// 生日
    /// </summary>
    public string date_of_birth { get; set; } = string.Empty;

    /// <summary>
    /// 所在地
    /// </summary>
    public string current_location { get; set; } = string.Empty;

    /// <summary>
    /// 所在地（标准化）
    /// </summary>
    public string current_location_norm { get; set; } = string.Empty;

    /// <summary>
    /// 详细地址
    /// </summary>
    public string detailed_location { get; set; } = string.Empty;

    /// <summary>
    /// 工作经验
    /// </summary>
    public int num_work_experience { get; set; }

    /// <summary>
    /// 期望职位
    /// </summary>
    public string desired_position { get; set; } = string.Empty;

    /// <summary>
    /// 期望薪水
    /// </summary>
    public string desired_salary { get; set; } = string.Empty;

    /// <summary>
    /// 期望行业
    /// </summary>
    public string desired_industry { get; set; } = string.Empty;

    /// <summary>
    /// 期望工作地区
    /// </summary>
    public string expect_location { get; set; } = string.Empty;

    /// <summary>
    /// 年龄
    /// </summary>
    public int? age { get; set; }
}

/// <summary>
/// 联系方式（子类）
/// </summary>
public class contact_info_sub
{
    /// <summary>
    /// 手机号
    /// </summary>
    public string phone_number { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string email { get; set; } = string.Empty;

    /// <summary>
    /// QQ号
    /// </summary>
    public string QQ { get; set; } = string.Empty;

    /// <summary>
    /// 微信号
    /// </summary>
    public string wechat { get; set; } = string.Empty;
}

/// <summary>
/// 教育背景（子类）
/// </summary>
public class education_experience_sub
{
    /// <summary>
    /// 开始时间年份
    /// </summary>
    public string start_time_year { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间月份
    /// </summary>
    public string start_time_month { get; set; } = string.Empty;

    /// <summary>
    /// 结束时间年份
    /// </summary>
    public string end_time_year { get; set; } = string.Empty;

    /// <summary>
    /// 结束时间月份
    /// </summary>
    public string end_time_month { get; set; } = string.Empty;

    /// <summary>
    /// 学校
    /// </summary>
    public string school_name { get; set; } = string.Empty;

    /// <summary>
    /// 上课模式
    /// </summary>
    public string study_model { get; set; } = string.Empty;

    /// <summary>
    /// 专业
    /// </summary>
    public string major { get; set; } = string.Empty;

    /// <summary>
    /// 学位
    /// </summary>
    public string degree { get; set; } = string.Empty;
}

/// <summary>
/// 工作经历（子类）
/// </summary>
public class work_experience_sub
{
    /// <summary>
    /// 开始时间年份
    /// </summary>
    public string start_time_year { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间月份
    /// </summary>
    public string start_time_month { get; set; } = string.Empty;

    /// <summary>
    /// 结束时间年份
    /// </summary>
    public string end_time_year { get; set; } = string.Empty;

    /// <summary>
    /// 结束时间月份
    /// </summary>
    public string end_time_month { get; set; } = string.Empty;

    /// <summary>
    /// 公司名称
    /// </summary>
    public string company_name { get; set; } = string.Empty;

    /// <summary>
    /// 所属部门
    /// </summary>
    public string department { get; set; } = string.Empty;

    /// <summary>
    /// 职位名
    /// </summary>
    public string job_title { get; set; } = string.Empty;

    /// <summary>
    /// 公司行业
    /// </summary>
    public string industry { get; set; } = string.Empty;

    /// <summary>
    /// 工资水平
    /// </summary>
    public string salary { get; set; } = string.Empty;

    /// <summary>
    /// 工作描述
    /// </summary>
    public string description { get; set; } = string.Empty;
}

/// <summary>
/// 项目经历（子类）
/// </summary>
public class project_experience_sub
{
    /// <summary>
    /// 开始时间年份
    /// </summary>
    public string start_time_year { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间月份
    /// </summary>
    public string start_time_month { get; set; } = string.Empty;

    /// <summary>
    /// 结束时间年份
    /// </summary>
    public string end_time_year { get; set; } = string.Empty;

    /// <summary>
    /// 结束时间月份
    /// </summary>
    public string end_time_month { get; set; } = string.Empty;

    /// <summary>
    /// 项目名称
    /// </summary>
    public string project_name { get; set; } = string.Empty;

    /// <summary>
    /// 职位名
    /// </summary>
    public string job_title { get; set; } = string.Empty;

    /// <summary>
    /// 项目描述
    /// </summary>
    public string description { get; set; } = string.Empty;
}

/// <summary>
/// 补充信息（子类）
/// </summary>
public class others_sub
{
    /// <summary>
    /// 专业技能
    /// </summary>
    public List<string>? skills { get; set; }

    /// <summary>
    /// IT技能
    /// </summary>
    public List<string>? it_skills { get; set; }

    /// <summary>
    /// 商业技能
    /// </summary>
    public List<string>? business_skills { get; set; }

    /// <summary>
    /// 自我评价
    /// </summary>
    public string? self_evaluation { get; set; }
}
#endregion

#region 画像解析
/// <summary>
/// 小析解析画像解析
/// </summary>
public class predicted_result
{
    /// <summary>
    /// 亮点
    /// </summary>
    public highlights? highlights { get; set; }

    /// <summary>
    /// 风险
    /// </summary>
    public risks? risks { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public tags? tags { get; set; }
}

/// <summary>
/// 亮点
/// </summary>
public class highlights
{
    /// <summary>
    /// 工作经历亮点
    /// </summary>
    public List<string>? occupation { get; set; }

    /// <summary>
    /// 学习经历亮点
    /// </summary>
    public List<string>? education { get; set; }

    /// <summary>
    /// 项目经历亮点
    /// </summary>
    public List<string>? project { get; set; }

    /// <summary>
    /// 其他亮点
    /// </summary>
    public List<string>? others { get; set; }

    /// <summary>
    /// 亮点标签
    /// </summary>
    public List<string>? tags { get; set; }
}

/// <summary>
/// 风险
/// </summary>
public class risks
{
    /// <summary>
    /// 工作经历风险
    /// </summary>
    public List<string>? occupation { get; set; }

    /// <summary>
    /// 学习经历风险点
    /// </summary>
    public List<string>? education { get; set; }

    /// <summary>
    /// 风险点标签
    /// </summary>
    public List<string>? tags { get; set; }
}

/// <summary>
/// 简历标签
/// </summary>
public class tags
{
    /// <summary>
    /// 基本信息标签
    /// </summary>
    public List<tag_sub>? basic { get; set; }

    /// <summary>
    /// 教育背景标签
    /// </summary>
    public List<tag_sub>? education { get; set; }

    /// <summary>
    /// 职业标签
    /// </summary>
    public List<tag_sub>? professional { get; set; }

    /// <summary>
    /// 技能标签
    /// </summary>
    public List<tag_sub>? skills { get; set; }

    /// <summary>
    /// 其他信息标签
    /// </summary>
    public List<tag_sub>? others { get; set; }
}

/// <summary>
/// 标签子类
/// </summary>
public class tag_sub
{
    /// <summary>
    /// 标签
    /// </summary>
    public string tag { get; set; } = string.Empty;

    /// <summary>
    /// 类型
    /// </summary>
    public string type { get; set; } = string.Empty;
}
#endregion
