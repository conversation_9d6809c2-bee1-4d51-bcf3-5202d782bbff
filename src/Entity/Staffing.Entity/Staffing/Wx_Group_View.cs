﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 微信群
/// </summary>
[Table("wx_group_view")]
public class Wx_Group_View
{
    [Key]
    public long Id { get; set; }

    /// <summary>
    /// 群id
    /// </summary>
    public long GroupId { get; set; } = default!;

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 查看时间
    /// </summary>
    public DateTime? CreatedTime { get; set; } = DateTime.Now;

}

