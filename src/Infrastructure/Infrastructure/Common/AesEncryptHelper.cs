using System.Security.Cryptography;
using System.Text;
using Config;

namespace Infrastructure.Common;

public static class AesEncryptHelper
{
    /// <summary>
    /// 256位AES加密
    /// </summary>
    /// <param name="toEncrypt"></param>
    /// <returns></returns>
    public static string Encrypt(string toEncrypt)
    {
        // 256-AES key    
        byte[] keyArray = Encoding.UTF8.GetBytes(Constants.KuaiShouSecretKey);
        byte[] toEncryptArray = Encoding.UTF8.GetBytes(toEncrypt);

        Aes rDel = Aes.Create();
        rDel.Key = keyArray;
        rDel.Mode = CipherMode.ECB;
        rDel.Padding = PaddingMode.PKCS7;

        ICryptoTransform cTransform = rDel.CreateEncryptor();
        byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);

        return Convert.ToBase64String(resultArray, 0, resultArray.Length);
    }

    /// <summary>
    /// 256位AES解密
    /// </summary>
    /// <param name="toDecrypt"></param>
    /// <returns></returns>
    public static string Decorypt(string toDecrypt)
    {
        byte[] keyArray = Encoding.UTF8.GetBytes(Constants.KuaiShouSecretKey);
        byte[] toEncryptArray = Convert.FromHexString(toDecrypt);

        Aes rDel = Aes.Create();
        rDel.Key = keyArray;
        rDel.Mode = CipherMode.ECB;
        rDel.Padding = PaddingMode.PKCS7;

        ICryptoTransform cTransform = rDel.CreateDecryptor();
        byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);

        return Encoding.UTF8.GetString(resultArray);
    }


    /// <summary>  
    /// 256位AES加密  
    /// </summary>  
    /// <param name="toEncrypt"></param>
    /// <param name="skey"></param>
    /// <param name="IV"></param>
    /// <returns></returns> 
    public static string Encrypt(string toEncrypt, string skey, string? IV)
    {
        //256-AES key      
        //byte[] keyArray = UTF8Encoding.UTF8.GetBytes("********************************");
        byte[] keyArray = Encoding.UTF8.GetBytes(skey);
        byte[] toEncryptArray = Encoding.UTF8.GetBytes(toEncrypt);
        byte[]? ivArray = null;
        if(!string.IsNullOrEmpty(IV))
            ivArray = Encoding.UTF8.GetBytes(IV); //1234567812345678

        Aes rDel = Aes.Create();
        rDel.Key = keyArray;
        rDel.Mode = CipherMode.ECB;
        rDel.Padding = PaddingMode.PKCS7;
        if (ivArray != null)
            rDel.IV = ivArray;

        ICryptoTransform cTransform = rDel.CreateEncryptor();
        byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);

        return Convert.ToBase64String(resultArray, 0, resultArray.Length);
    }

    /// <summary>  
    /// 256位AES解密  
    /// </summary>  
    /// <param name="toDecrypt"></param>
    /// <param name="skey"></param>
    /// <param name="IV"></param>
    /// <returns></returns>  
    public static string Decrypt(string toDecrypt, string skey, string IV)
    {
        //256-AES key
        //byte[] keyArray = UTF8Encoding.UTF8.GetBytes("********************************");
        byte[] keyArray = Encoding.UTF8.GetBytes(skey);
        byte[] toDecryptArray = Convert.FromBase64String(toDecrypt);
        byte[] ivArray = Encoding.UTF8.GetBytes(IV); //1234567812345678

        Aes rDel = Aes.Create();
        rDel.Key = keyArray;
        rDel.Mode = CipherMode.ECB;
        rDel.Padding = PaddingMode.PKCS7;
        rDel.IV = ivArray;

        ICryptoTransform cTransform = rDel.CreateDecryptor();
        byte[] resultArray = cTransform.TransformFinalBlock(toDecryptArray, 0, toDecryptArray.Length);

        return Encoding.UTF8.GetString(resultArray);
    }
}