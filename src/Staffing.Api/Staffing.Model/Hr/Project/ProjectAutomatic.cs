﻿using System.Text.Json.Serialization;

namespace Staffing.Model.Hr.Project;

public class ProjectAutomatic
{
}

/// <summary>
/// 获取项目自流转详情返回模型
/// </summary>
public class GetProjectAutomaticResponse
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 招聘周期
    /// </summary>
    public bool? RecruitmentCycle { get; set; }

    /// <summary>
    /// 招聘人数
    /// </summary>
    public bool? RecruitmentNumber { get; set; }

    /// <summary>
    /// 面试官筛选结果
    /// </summary>
    public bool? InterviewerOutCome { get; set; }

    /// <summary>
    /// 筛选归档类型
    /// </summary>
    public Config.Enums.RecruitFileAway? ScreenFileStatus { get; set; }

    /// <summary>
    /// 筛选归档类型解释
    /// </summary>
    public string? ScreenFileStatusName { get; set; }

    /// <summary>
    /// 筛选归档描述
    /// </summary>
    public string? ScreenFileRemarks { get; set; }

    /// <summary>
    /// offer状态
    /// </summary>
    public bool? OfferStatus { get; set; }

    /// <summary>
    /// offer或入职
    /// </summary>
    public bool? OfferOrInduction { get; set; }

    /// <summary>
    /// 离职操作
    /// </summary>
    public bool? QuitJob { get; set; }

    /// <summary>
    /// 离职归档类型
    /// </summary>
    public Config.Enums.RecruitFileAway? QuitFileStatus { get; set; }

    /// <summary>
    /// 离职归档类型解释
    /// </summary>
    public string? QuitFileStatusName { get; set; }

    /// <summary>
    /// 离职归档描述
    /// </summary>
    public string? QuitFileRemarks { get; set; }

    [JsonIgnore]
    public string? HrId { get; set; }
}

/// <summary>
/// 修改项目自流转接收模型
/// </summary>
public class EditProjectAutomaticRequest
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 招聘周期
    /// </summary>
    public bool? RecruitmentCycle { get; set; }

    /// <summary>
    /// 招聘人数
    /// </summary>
    public bool? RecruitmentNumber { get; set; }

    /// <summary>
    /// 面试官筛选结果
    /// </summary>
    public bool? InterviewerOutCome { get; set; }

    /// <summary>
    /// 筛选归档类型
    /// </summary>
    public Config.Enums.RecruitFileAway? ScreenFileStatus { get; set; }

    /// <summary>
    /// 筛选归档描述
    /// </summary>
    public string? ScreenFileRemarks { get; set; }

    /// <summary>
    /// offer状态
    /// </summary>
    public bool? OfferStatus { get; set; }

    /// <summary>
    /// offer或入职
    /// </summary>
    public bool? OfferOrInduction { get; set; }

    /// <summary>
    /// 离职操作
    /// </summary>
    public bool? QuitJob { get; set; }

    /// <summary>
    /// 离职归档类型
    /// </summary>
    public Config.Enums.RecruitFileAway? QuitFileStatus { get; set; }

    /// <summary>
    /// 离职归档描述
    /// </summary>
    public string? QuitFileRemarks { get; set; }
}

