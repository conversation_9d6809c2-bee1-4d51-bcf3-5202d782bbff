﻿using Config.CommonModel;
using Config.CommonModel.ThirdTalentInfo;
using Staffing.Model.Hr.Dashboard;
using Staffing.Model.Hr.KuaiShouTalentInfo;

namespace Staffing.Core.Interfaces.Hr;

public interface IThirdTalentInfoService<T> where T : IThirdTalentInfo
{
    #region 快招工接口集合

    /// <summary>
    /// 列表 - db查询
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    TalentInfoPageListResponse GetKuaiShouTalentInfoFromDBPageList(TalentInfoPageListRequest request);

    /// <summary>
    /// 列表 - es查询
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    TalentInfoPageListResponse GetKuaiShouTalentInfoFromESPageList(TalentInfoPageListRequest request);

    /// <summary>
    /// 回访
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    EmptyResponse RelationHrAndKuaishouTalent(RelationRequest request);

    /// <summary>
    /// 解析快手数据并入库
    /// </summary>
    /// <param name="infos"></param>
    /// <returns></returns>
    Task<KuaishouTalentInfosOriginal> TryParseTalentInfoAsync(object infos);

    #endregion

    /// <summary>
    /// 获取教育经历
    /// </summary>
    /// <returns></returns>
    ResumeBufferEducations GetEducationFromTalentInfo(T talentInfo);

    /// <summary>
    /// 获取求职意向
    /// </summary>
    /// <param name="talentInfo"></param>
    /// <returns></returns>
    ResumeBufferHopes GetHopesFromTalentInfo(T talentInfo);

    /// <summary>
    /// 获取工作经历
    /// </summary>
    /// <param name="talentInfo"></param>
    /// <returns></returns>
    ResumeBufferWorks GetWorksFromTalentInfo(T talentInfo);

    List<TeamHrResponse> GetTeamWorkers();

    PostTeamInfoPageListResponse GetPostsByStr(PostTeamPageListRequest request);

    TalentInfoEditResponse GetKuaiShouTalentInfoForEditPage(string applicationId);
    List<EnumInfo> GetInValidMemo();
    List<EnumInfo> GetValidMemo();

    /// <summary>
    /// 导出同步到快招工的诺快聘职位列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    PostSendToKuaishouResponse GetPostSendToKuaishou(PostSendToKuaishouRequest model);
    EmptyResponse EditKuaiShouTalent(EditKuaiShouTalent request);
}
