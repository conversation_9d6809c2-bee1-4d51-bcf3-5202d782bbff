﻿using Config.CommonModel;
using Config.Enums;

namespace Staffing.Model.Seeker.Balance;

public class GetBalance
{

}

public class GetBalanceResponse
{
    /// <summary>
    /// 钱包
    /// </summary>
    public decimal Balance { get; set; }

    /// <summary>
    /// 钱包历史总额
    /// </summary>
    public decimal BalanceFull { get; set; }

    /// <summary>
    /// 今日收入
    /// </summary>
    public decimal TodayBalance { get; set; }

}

public class GetBalanceRecord : QueryRequest
{
    /// <summary>
    /// 月
    /// </summary>
    public DateTime? Month { get; set; }
}

public class GetBalanceRecordResponse : QueryResponse
{
    /// <summary>
    /// 钱包记录
    /// </summary>
    public List<BalanceRecordInfo>? Rows { get; set; }
}

public class BalanceRecordInfo
{
    /// <summary>
    /// 当前值
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 增量
    /// </summary>
    public decimal Increment { get; set; }

    /// <summary>
    /// hr还是求职者
    /// </summary>
    public SeekerOrHr UserType { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public BalanceRecordType Type { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 发生时间
    /// </summary>
    public DateTime EventTime { get; set; } = DateTime.Now;
}

public class Withdraw
{
    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount { get; set; }
}

public class WithdrawResponse
{

}