﻿using Config.CommonModel;
using Config.Enums;

namespace Staffing.Model.Hr.User;

public class HrInfo
{
    /// <summary>
    /// 用户Id，未注册=0
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public UserStatus? Status { get; set; }

    /// <summary>
    /// 邀请码
    /// </summary>
    public string? InviteCode { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    /// 权限
    /// </summary>
    public List<string> Powers { get; set; } = new List<string>();

    /// <summary>
    /// 角色Id
    /// </summary>
    public string? RoleId { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    public string? RoleName { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// 人才数量
    /// </summary>
    public int Talent { get; set; }

    /// <summary>
    /// 钉钉绑定手机号
    /// </summary>
    public string? DingMobile { get; set; }

    /// <summary>
    /// 是否需要绑定钉钉
    /// </summary>
    public bool NeedBindDing { get; set; }

    /// <summary>
    /// 虚拟人才数量
    /// </summary>
    public int VirtualTalent { get; set; }

    /// <summary>
    /// 是否内部人员
    /// </summary>
    public bool Insiders { get; set; }

    /// <summary>
    /// 是否诺亚公司人员
    /// </summary>
    public bool IsNoah { get; set; }

    /// <summary>
    /// 公司特性
    /// </summary>
    public List<string>? EntSpecific { get; set; }

    /// <summary>
    /// 是否诺快聘
    /// </summary>
    public bool IsNuoKuaiPin { get; set; }

    public Sex? Sex { get; set; }

    /// <summary>
    /// 公众号通知
    /// </summary>
    public bool H5Notice { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 企业简称
    /// </summary>
    public string? EntAbbr { get; set; }

    /// <summary>
    /// 微信号
    /// </summary>
    public string? WeChatNo { get; set; }

    /// <summary>
    /// 是否关注公众号
    /// </summary>
    public bool WeChatH5Subscribe { get; set; }

    /// <summary>
    /// 业务短信余量
    /// </summary>
    public int BusinessSms { get; set; }
}

public class UpdateHrCard
{
    /// <summary>
    /// 昵称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? EMail { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 微信
    /// </summary>
    public string? WeChatNo { get; set; }

    /// <summary>
    /// 企业微信
    /// </summary>
    public string? EntWeChatQrCode { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 公众号通知
    /// </summary>
    public bool? H5Notice { get; set; }

    /// <summary>
    /// 新电话（修改手机号）
    /// </summary>
    public string? NewMobile { get; set; }

    /// <summary>
    /// 验证码（如果修改手机号必填）
    /// </summary>
    public string? SmsCode { get; set; }

    /// <summary>
    /// 旧电话（修改手机号需要验证）
    /// </summary>
    public string? OldMobile { get; set; }


    public double Lat { get; set; }
    public double Lng { get; set; }

    /// <summary>
    /// 顾问自动协同项目(0=关闭，1=开启)
    /// </summary>
    public int? HrAutoTeam { get; set; }
}

public class HrCardInfo : UpdateHrCard
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 邀请码
    /// </summary>
    public string? InviteCode { get; set; }

    /// <summary>
    /// 小程序二维码
    /// </summary>
    public string? AppletQrCode { get; set; }

    /// <summary>
    /// B端小程序二维码
    /// </summary>
    public string? HrAppletQrCode { get; set; }

    /// <summary>
    /// 完善度
    /// </summary>
    public int Score { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    ///  开通天数
    /// </summary>
    public int? RegistrationDays { get; set; }

    /// <summary>
    /// 是否实名
    /// </summary>
    public bool Identity { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 企业简称
    /// </summary>
    public string? EntAbbr { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }

    public CityModel? City { get; set; }
}

public class HrDataInfo
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 今日访问数量
    /// </summary>
    public int TodayVisits { get; set; }

    /// <summary>
    /// 今日新增人才数量
    /// </summary>
    public int TodayNewTalent { get; set; }

    /// <summary>
    /// 今日报名数量
    /// </summary>
    public int TodayDelivery { get; set; }

    /// <summary>
    /// 真实用户储备
    /// </summary>
    public int RealTalent { get; set; }

    /// <summary>
    /// 用户储备
    /// </summary>
    public int Talent { get; set; }

    /// <summary>
    /// 今日交付收入
    /// </summary>
    public decimal TodayDeliveryBounty { get; set; }

    /// <summary>
    /// 总访问数量
    /// </summary>
    public int FullVisits { get; set; }

    /// <summary>
    /// 总报名数量
    /// </summary>
    public int FullDelivery { get; set; }

    /// <summary>
    /// 总交付收入
    /// </summary>
    public decimal FullDeliveryBounty { get; set; }
}

public class HrCardShow
{
    /// <summary>
    /// 电话
    /// </summary>
    public bool Mobile { get; set; } = true;

    /// <summary>
    /// 邮箱
    /// </summary>
    public bool EMail { get; set; } = true;

    /// <summary>
    /// 地址
    /// </summary>
    public bool Address { get; set; } = true;
}

public class HrSimpleInfo
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }

    /// <summary>
    /// 企业简称
    /// </summary>
    public string? EntAbbr { get; set; }

    /// <summary>
    /// 部门名称
    /// </summary>
    public string? OrgName { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public string? Mobile { get; set; }
}

public class GetHrSimpleResponse
{
    public List<HrSimpleInfo> Rows { get; set; } = default!;
}

public class HrSettingInfo
{
    /// <summary>
    /// 0=关闭，1=开启
    /// </summary>
    public int HrAutoTeam { get; set; }
}