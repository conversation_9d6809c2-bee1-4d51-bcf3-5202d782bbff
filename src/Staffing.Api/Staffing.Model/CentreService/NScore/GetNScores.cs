// using Config.CommonModel;
// using Config.Enums;

// namespace Staffing.Model.CentreService.NScore;

// public class GetNScores
// {
//     /// <summary>
//     /// hr还是求职者
//     /// </summary>
//     public SeekerOrHr UserType { get; set; } = SeekerOrHr.Seeker;

//     /// <summary>
//     /// 类型
//     /// </summary>
//     public string? TypeName { get; set; }

//     /// <summary>
//     /// 端口
//     /// </summary>
//     public int? Port { get; set; }
// }

// public class GetNScoresResponse
// {
//     /// <summary>
//     /// 积分列表
//     /// </summary>
//     public List<NScoresInfo>? Rows { get; set; }
// }

// public class NScoresInfo : NScoresStoreInfo
// {
//     /// <summary>
//     /// 类型名称
//     /// </summary>
//     public string? TypeName { get; set; }
// }

// public class SetNScore
// {
//     /// <summary>
//     /// 类型
//     /// </summary>
//     public NScoreType Type { get; set; }

//     /// <summary>
//     /// hr还是求职者（必填）
//     /// </summary>
//     public SeekerOrHr UserType { get; set; } = SeekerOrHr.Seeker;

//     /// <summary>
//     /// 积分
//     /// </summary>
//     public int? Score { get; set; }
// }

// public class SetNScoreResponse
// {

// }