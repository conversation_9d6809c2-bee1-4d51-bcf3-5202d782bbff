﻿using Staffing.Model.Hr.Project;


namespace Staffing.Core.Interfaces.Hr;

public interface IProjectHallService
{
    Task<GetProjectHallResponse> GetProjectHall(GetProjectHall model);

    GetHallPostResponse GetHallPost(GetHallPost model);

    Task<ProjectSyncResponse> ProjectSync(ProjectSync model);

    GetProjectHallDetail GetHallProject(string id);
    
    Task<GetProjectHallResponse> NewGetProjectHall(GetProjectHall model);
}