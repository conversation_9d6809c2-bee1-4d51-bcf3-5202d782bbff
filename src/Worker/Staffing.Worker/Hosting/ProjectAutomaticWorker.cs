using Staffing.Worker.Service;

namespace Staffing.Worker.Hosting;

public class ProjectAutomaticWorker : HostingBase
{
    private readonly ProjectAutomaticService _projectAutomaticService;
    public ProjectAutomaticWorker(IHostApplicationLifetime lifetime, ProjectAutomaticService projectAutomaticService) : base(lifetime)
    {
        _projectAutomaticService = projectAutomaticService;
    }

    public override async Task StartAsync(CancellationToken cancel)
    {
        _bgTasks.Add(await Task.Factory.StartNew(() => AutomaticForProjectWorkerBy30Seconds(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        //_bgTasks.Add(await Task.Factory.StartNew(() => AutomaticForHrScreenWorkerBy30Seconds(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        // _bgTasks.Add(await Task.Factory.StartNew(() => AutomaticForInterviewerScreenWorkerBy30Seconds(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        // _bgTasks.Add(await Task.Factory.StartNew(() => AutomaticForOfferWorkerBy30Seconds(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _projectAutomaticService.ProjectCallBack(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        _bgTasks.Add(await Task.Factory.StartNew(() => _projectAutomaticService.ContractUpdateProjectCallBack(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
    }

    /// <summary>
    /// 倒计时自流转处理 - 项目自流转
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    private async Task AutomaticForProjectWorkerBy30Seconds(CancellationToken cancel)
    {
        using var timer = new PeriodicTimer(TimeSpan.FromSeconds(30));
        try
        {
            while (await timer.WaitForNextTickAsync(cancel))
            {
                await _projectAutomaticService.AutomaticForProject(cancel);
            }
        }
        catch (OperationCanceledException) { }
    }

    /// <summary>
    /// 倒计时自流转处理 - Hr筛选自流转
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    private async Task AutomaticForHrScreenWorkerBy30Seconds(CancellationToken cancel)
    {
        using var timer = new PeriodicTimer(TimeSpan.FromSeconds(30));
        try
        {
            while (await timer.WaitForNextTickAsync(cancel))
            {
                await _projectAutomaticService.AutomaticForHrScreen(cancel);

                // 提前一天消息提醒
                await _projectAutomaticService.AutomaticForHrScreenMessage(cancel);
            }
        }
        catch (OperationCanceledException) { }
    }

    /// <summary>
    /// 倒计时自流转处理 - 面试官筛选自流转
    /// 20240923,简历需审(经过主创),需要自流转
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    private async Task AutomaticForInterviewerScreenWorkerBy30Seconds(CancellationToken cancel)
    {
        using var timer = new PeriodicTimer(TimeSpan.FromSeconds(30));
        try
        {
            while (await timer.WaitForNextTickAsync(cancel))
            {
                await _projectAutomaticService.AutomaticForInterviewerScreen(cancel);

                // 提前一天消息提醒
                await _projectAutomaticService.AutomaticForInterviewerScreenMessage(cancel);
            }
        }
        catch (OperationCanceledException) { }
    }

    /// <summary>
    /// 倒计时自流转处理 - offer自流转
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    private async Task AutomaticForOfferWorkerBy30Seconds(CancellationToken cancel)
    {
        using var timer = new PeriodicTimer(TimeSpan.FromSeconds(30));
        try
        {
            while (await timer.WaitForNextTickAsync(cancel))
            {
                await _projectAutomaticService.AutomaticForOffer(cancel);

                // 提前一天消息提醒
                await _projectAutomaticService.AutomaticForOfferMessage(cancel);
            }
        }
        catch (OperationCanceledException) { }
    }
}