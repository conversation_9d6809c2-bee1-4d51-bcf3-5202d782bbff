using Config.CommonModel.Ndn;
using Infrastructure.CommonService.Ndn;
using Infrastructure.Extend;

namespace Staffing.Api.Apis.Internal;

[Service(ServiceLifetime.Transient)]
public class NdnRouter : IInternalRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        // var group = parentGroup.MapGroup("/internal/v1/ndn").WithTags("数字诺亚");
        // group.MapPost("/notify", (NdnNotify model, INdnService rep) => rep.NdnNotify(model))
        //     .WithOpenApi(operation => new(operation) { Summary = "通知" });
    }
}