namespace Config.CommonModel.Aliyun;

public class BindAxb
{
    /// <summary>
    /// 电话A
    /// </summary>
    public string PhoneNoA { get; set; } = default!;

    /// <summary>
    /// 电话B
    /// </summary>
    public string PhoneNoB { get; set; } = default!;

    /// <summary>
    /// 过期秒数（默认120）
    /// </summary>
    public int ExpiredSeconds { get; set; } = 120;
}

public class BindAxbResult
{
    /// <summary>
    /// 分机号码。接口BindAxb不涉及分机号码，请忽略该返回参数
    /// </summary>
    public string? Extension { get; set; }

    /// <summary>
    /// 隐私号码，即X号码
    /// </summary>
    public string? SecretNo { get; set; }

    /// <summary>
    /// 绑定关系ID
    /// </summary>
    public string? SubsId { get; set; }
}

public class UnbindSubscription
{

    /// <summary>
    /// 隐私号码，即X号码
    /// </summary>
    public string? SecretNo { get; set; }

    /// <summary>
    /// 绑定关系ID
    /// </summary>
    public string? SubsId { get; set; }
}