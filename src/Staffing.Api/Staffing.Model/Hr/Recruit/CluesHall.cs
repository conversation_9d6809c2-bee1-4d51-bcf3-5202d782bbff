using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;
using Staffing.Model.Hr.Post;
using RecruitInfo = Staffing.Model.Hr.Dashboard.RecruitInfo;

namespace Staffing.Model.Hr.Recruit;

public class CluesHallReq: QueryRequest
{
    /// <summary>
    /// 职位名称或公司简称或pm姓名
    /// </summary>
    public  string? Search { get; set; }
    /// <summary>
    /// 地区Id
    /// </summary>
    public string? City { get; set; }
    /// <summary>
    /// 职位类别
    /// </summary>
    public int? Category { get; set; }
    /// <summary>
    /// 结算方式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }
    /// <summary>
    /// 薪资要求(最低)
    /// </summary>
    public int? MinSalary { get; set; }

    /// <summary>
    /// 薪资要求(最高)
    /// </summary>
    public int? MaxSalary { get; set; }
    /// <summary>
    /// 求职者学历
    /// </summary>
    public EducationType? SeekerEducation { get; set; }
    /// <summary>
    /// 简历免审
    /// </summary>
    public bool? IsResumeExempt { get; set; }
    /// <summary>
    /// 面试开始时间
    /// </summary>
    public DateTime? InterviewStartTime { get; set; }
    /// <summary>
    /// 面试结束时间
    /// </summary>
    public DateTime? InterviewEndTime { get; set; }
    /// <summary>
    /// 线索标签
    /// </summary>
    public int? ClueLabel { get; set; }
    /// <summary>
    /// 招聘流程状态
    /// </summary>
    public RecruitStatus? RecruitStatus { get; set; }
    /// <summary>
    /// 是否倒序
    /// </summary>
    public Boolean IsDescending { get; set; } = true;
}

public class CluesHallListResp: QueryResponse
{
    public List<CluesInfo> Rows { get; set; } = default!;
}

public class CluesInfo:RecruitInfo
{ 
    /// <summary>
    /// 0 众包 1指定人接受
    /// </summary>
    public ProjectTeamConfigType? ModeType { get; set; }
    /// <summary>
    /// 简历投递时间
    /// </summary>
    public DateTime DeliveryTime { get; set; }
    
    /// <summary>
    /// 投递公司
    /// </summary>
    public string? EntName { get; set; }
    /// <summary>
    /// 预计佣金
    /// </summary>
    public decimal? Money { get; set; }
    /// <summary>
    /// 合作模式
    /// </summary>
    public new ProjectPaymentNode? PaymentNode { get; set; }
    
    /// <summary>
    /// 面试看板
    /// </summary>
    public PostRecentInterviews InterviewDashboard { get; set; } = new PostRecentInterviews();
    /// <summary>
    /// 岗位id
    /// </summary>
    public string? PostId { get; set; }
    /// <summary>
    /// 线索编码
    /// </summary>
    public required String ClueCode  { get; set; }
    
    /// <summary>
    /// 主创信息
    /// </summary>
    public HrModel? Hr { get; set; }
    /// <summary>
    /// 线索标签
    /// </summary>
    public List<ClueTagsResp> FirstTags { get; set; } = new List<ClueTagsResp>();
    /// <summary>
    /// 线索标签
    /// </summary>
    public List<string> SecondTags { get; set; } = new List<string>();
    /// <summary>
    /// 线索标签
    /// </summary>
    public List<ClueTagsResp> ThirdTags { get; set; } = new List<ClueTagsResp>();

    /// <summary>
    /// 岗位福利
    /// </summary>
    public List<WelfareModel>? Welfare { get; set; }
    
    /// <summary>
    /// 简历是否需免审
    /// </summary>
    public bool IsResumeExempt { get; set; }
    /// <summary>
    /// 入职人数
    /// </summary>
    public int InductionNum { get; set; }
    /// <summary>
    /// 求职者腾讯IM账号Id
    /// </summary>
    public string? UserSeekerTencentImId { get; set; }
}


public class ClueAcceptOrderReq
{
    /// <summary>
    /// 招聘流程id
    /// </summary>
    public required string Id { get; set; } 
}

public class ClueTagsResp
{
    public required string Name { get; set; }
    
    public ClueTagEnums? Value { get; set; } 
}


