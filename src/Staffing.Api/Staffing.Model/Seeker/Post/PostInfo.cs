﻿using Config.CommonModel.Business;

namespace Staffing.Model.Seeker.Post;

// public class SeekerGetPosts : QueryRequest
// {
//     /// <summary>
//     /// 推荐职位
//     /// </summary>
//     [JsonIgnore]
//     public string? RecommendTeamPostId { get; set; }

//     /// <summary>
//     /// 按照渠道自己的排序展示
//     /// </summary>
//     public string? ChannelId { get; set; }

//     /// <summary>
//     /// 是否优选
//     /// </summary>
//     public bool IsExcellent { get; set; }

//     /// <summary>
//     /// 顾问Id
//     /// </summary>
//     public string? AdviserId { get; set; }

//     /// <summary>
//     /// 检索
//     /// </summary>
//     public string? Search { get; set; }

//     /// <summary>
//     /// 项目行业
//     /// </summary>
//     public ProjectIndustry? ProjectIndustry { get; set; }

//     /// <summary>
//     /// 职位类别
//     /// </summary>
//     public int? Category { get; set; }

//     /// <summary>
//     /// 工作性质
//     /// </summary>
//     public PostWorkNature? WorkNature { get; set; }

//     /// <summary>
//     /// 代招企业Id
//     /// </summary>
//     public string? AgentEntId { get; set; }

//     /// <summary>
//     /// 地区Id（最多10个）
//     /// </summary>
//     public List<string>? Citys { get; set; }

//     /// <summary>
//     /// 排序（1=距离近，2=最新，其他=默认）
//     /// </summary>
//     public int? Sort { get; set; }

//     /// <summary>
//     /// 当前用户纬度，若按距离排序必填
//     /// </summary>
//     public double Lat { get; set; }

//     /// <summary>
//     /// 当前用户经度，若按距离排序必填
//     /// </summary>
//     public double Lng { get; set; }

//     /// <summary>
//     /// 性别
//     /// </summary>
//     public Sex? Sex { get; set; }

//     /// <summary>
//     /// 结算方式（日结、月结）
//     /// </summary>
//     public List<PostSettlementType?>? SettlementType { get; set; }

//     /// <summary>
//     /// 工作周期类型
//     /// </summary>
//     public List<ProjectWorkCycleType>? WorkCycleType { get; set; }

//     /// <summary>
//     /// 企业性质
//     /// </summary>
//     public EnterpriseNature? Nature { get; set; }

//     /// <summary>
//     /// 企业规模
//     /// </summary>
//     public EnterpriseScale? Scale { get; set; }

//     /// <summary>
//     /// 学历
//     /// </summary>
//     public EducationType? Education { get; set; }

//     /// <summary>
//     /// 职位Id精准查找
//     /// </summary>
//     public List<string>? TeamPostIds { get; set; }
// }

// public class SeekerGetPostsResponse : QueryResponse
// {
//     public List<SeekerGetPostInfo> Rows { get; set; } = new List<SeekerGetPostInfo>();
// }

// public class SeekerGetPost
// {
//     /// <summary>
//     /// 协同的职位Id
//     /// </summary>
//     public string? TeamPostId { get; set; }

//     /// <summary>
//     /// 顾问Id
//     /// </summary>
//     public string? AdviserId { get; set; }

//     /// <summary>
//     /// 渠道Id（显示渠道置顶信息）
//     /// </summary>
//     public string? ChannelId { get; set; }
// }

// public class SeekerGetPostInfo : GetPostDetail
// {
//     /// <summary>
//     /// 72小时入职
//     /// </summary>
//     public bool EntryIn72hour { get; set; }

//     /// <summary>
//     /// 24小时面试
//     /// </summary>
//     public bool InterviewIn24Hour { get; set; }

//     /// <summary>
//     /// 是否已收藏
//     /// </summary>
//     public bool Collect { get; set; }

//     /// <summary>
//     /// 前端是否展示
//     /// </summary>
//     public bool Show { get; set; }

//     /// <summary>
//     /// 协同者状态
//     /// </summary>
//     [JsonIgnore]
//     public PostStatus? TeamStatus { get; set; }

//     /// <summary>
//     /// 项目状态
//     /// </summary>
//     [JsonIgnore]
//     public ProjectStatus? ProjectStatus { get; set; }

//     /// <summary>
//     /// 投递Id
//     /// </summary>
//     public string? DeliveryId { get; set; }

//     /// <summary>
//     /// 报名的人员头像
//     /// </summary>
//     public List<string> PostAvatars { get; set; } = new List<string>();

//     /// <summary>
//     /// 新标签(1=精选推荐，2=靠谱好岗)
//     /// </summary>
//     public int NewTagType { get; set; }
// }

public class SeekerGetEnt
{
    /// <summary>
    /// 代招企业Id
    /// </summary>
    public string? AgentEntId { get; set; }
}

public class SeekerGetEntInfo : GetAgentEntDetail
{
    /// <summary>
    /// 法人
    /// </summary>
    public string? LegalPerson { get; set; }

    /// <summary>
    /// 注册资金
    /// </summary>
    public string? RegisteredCapital { get; set; }
}

public class GetRecommendPosts
{
    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }
}

public class GetSeekerShareQrCode
{
    /// <summary>
    /// 协同的职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 渠道Id
    /// </summary>
    public string? ChannelId { get; set; }

    // /// <summary>
    // /// 分享人Id
    // /// </summary>
    // public string? ShareUserId { get; set; }
}

public class GetSeekerShareQrCodeResponse
{
    /// <summary>
    /// 二维码
    /// </summary>
    public string? QrCode { get; set; }
}