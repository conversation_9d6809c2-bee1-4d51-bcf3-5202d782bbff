using Config.CommonModel;
using Staffing.Model.Hr.Channel;

namespace Staffing.Core.Interfaces.Hr;

public interface IChannelService
{
    GetCounselorResponse GetCounselorInfo(SetCounselor model);
    SearchChannelNameResponse SearchChannel(SearchChannelName model);
    EmptyResponse UpdateChannelInfo(CounselorChannel model);
    Task<GetCounselorChannelCodeResponse> GetInviteChannelCode(SetCounselorChannelCode model);
}