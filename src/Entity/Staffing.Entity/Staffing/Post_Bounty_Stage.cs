﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Staffing.Entity.Staffing;

/// <summary>
/// 职位分润阶段表
/// </summary>
[Table("post_bounty_stage")]
public class Post_Bounty_Stage
{
    /// <summary>
    /// 主键
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 分润表主键Id
    /// </summary>
    public string BountyId { get; set; } = default!;

    /// <summary>
    /// 状态
    /// </summary>
    public BountyStageStatus Status { get; set; } = BountyStageStatus.交付中;

    /// <summary>
    ///  过保状态
    /// </summary>
    public GuaranteeStatus GuaranteeStatus { get; set; } = GuaranteeStatus.未过保;

    // /// <summary>
    // ///  过保时间
    // /// </summary>
    // public DateTime? GuaranteeDate { get; set; }

    /// <summary>
    /// 过保天数
    /// </summary>
    public int GuaranteeDays { get; set; }

    /// <summary>
    /// 分润金额
    /// </summary>
    public decimal Money { get; set; }

    /// <summary>
    /// 结算截止日期
    /// </summary>
    public DateTime? SettlementEndTime { get; set; }

    /// <summary>
    /// 关联的分润表
    /// </summary>
    [ForeignKey("BountyId")]
    public Post_Bounty Post_Bounty { get; set; } = default!;
}