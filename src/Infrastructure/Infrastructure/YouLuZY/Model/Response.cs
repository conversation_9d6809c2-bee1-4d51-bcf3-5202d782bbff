﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure.YouLuZY.Model
{
    public class Response<T> where T : class
    {
        public bool Result { get; set; } = false;
        /// <summary>
        /// 返回码0000代表成功
        /// </summary>
        public string Code { get; set; } = default!;
        /// <summary>
        /// 返回信息
        /// </summary>
        public string? Msg { get; set;}

        public T? Data { get; set; }
    }

    public class Response_AccessToken
    {
        /// <summary>
        /// 请求token
        /// </summary>
        public string? Access_Token { get; set; }
        /// <summary>
        /// token有效时长（秒）
        /// </summary>
        public int Expires_In { get; set; }

        public DateTime Expires_Time { get; set; }
    }
    public class Response_AuthCode
    {
        /// <summary>
        /// 一次性Code
        /// </summary>
        public string? Auth_Code { get; set; }
    }
}
