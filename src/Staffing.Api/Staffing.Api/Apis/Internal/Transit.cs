using Config.CommonModel;
using Config.CommonModel.DataScreen;
using Config.CommonModel.TalentResume;
using Config.CommonModel.Tianyancha;
using Infrastructure.CommonService;
using Infrastructure.Extend;
using Staffing.Model.CentreService.Report;
using Staffing.Model.CentreService.Transit;
using Staffing.Core.Interfaces.CentreService;

namespace Staffing.Api.Apis.Internal;

[Service(ServiceLifetime.Transient)]
public class TransitRouter : IInternalRouterBase
{
    public void AddRoutes(RouteGroupBuilder parentGroup)
    {
        var group = parentGroup.MapGroup("centreservice/v1/transit").WithTags("通用内部服务");
        group.MapPost("/wechath5/info", (GetWeChatH5 model, ITransitService rep) => rep.GetWeChatH5(model))
            .WithOpenApi(operation => new(operation) { Summary = "公众号查询" });

        group.MapPost("/advisers", (GetHrs model, ITransitService rep) => rep.GetHrs(model))
            .WithOpenApi(operation => new(operation) { Summary = "顾问列表" });

        group.MapPost("/resume/submit", async (SubmitResume model, ITransitService rep) => await rep.SubmitResume(model))
            .WithOpenApi(operation => new(operation) { Summary = "投递简历" });

        group.MapPost("/ent/recommend", (RecommendEnt model, ITransitService rep) => rep.RecommendEnt(model))
            .WithOpenApi(operation => new(operation) { Summary = "为顾问推荐企业" });

        group.MapPost("/ent/recommend/update", (UpdateRecommendEnt model, ITransitService rep) => rep.UpdateRecommendEnt(model))
            .WithOpenApi(operation => new(operation) { Summary = "更新企业推荐" });

        group.MapGet("/ent/recommend/synctoscrmforsh", (string? OutletId, ITransitService rep) =>
        {
            rep.SyncRecommendEntToSCRMForSH(OutletId);
            return new EmptyResponse();
        }).WithOpenApi(operation => new(operation) { Summary = "同步从网点提交上来的企业线索至 SCRM致趣百川" });

        group.MapPost("/posts/ids", (GetPostsByIds model, ITransitService rep) => rep.GetPostsByIds(model))
            .WithOpenApi(operation => new(operation) { Summary = "职位详情(批量)" });

        group.MapPost("/noah/post/notify", (PostNotify model, ITransitService rep) => rep.PostNotify(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺聘职位通知" });

        group.MapPost("/noah/deliver/notify", (ResumeNotify model, ITransitService rep) => rep.ResumeNotify(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺聘投递通知" });

        group.MapPost("/dashboard/xqdp/nkp", (GetXqdpNkp model, ITransitService rep) => rep.GetXqdpNkp(model))
            .WithOpenApi(operation => new(operation) { Summary = "新区大屏统计数据" });

        group.MapPost("/hr/ysc", (GetHrYsc model, ITransitService rep) => rep.GetHrYsc(model))
            .WithOpenApi(operation => new(operation) { Summary = "查询顾问是否有云市场权限" });

        group.MapPost("/talent/resume", (List<TalentResumeRequestModelExtends> model, CommonTalentResumeService rep) => rep.TalentResumeReceive(model))
            .WithOpenApi(operation => new(operation) { Summary = "人才融合接收" });

        group.MapPost("/nkp/tycent", (TycRequestModel request, CommonCacheService rep) => rep.GetEntFromTyc(request.CompanyName, request.CheckTimes))
            .WithOpenApi(operation => new(operation) { Summary = "根据公司名称获取天眼查信息" });

        group.MapPost("/nkp/resume/notify", (List<NykTalentResume> model, ITransitService rep) => rep.NykResumeNotify(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺优考简历通知" });

        group.MapPost("/nkp/user/login/notify", (List<NykTalentUpdateLoginTime> model, ITransitService rep) => rep.NykUserLoginNotify(model))
            .WithOpenApi(operation => new(operation) { Summary = "诺优考用户登录通知" });

        group.MapPost("/topology/dashboard", (TopologyDateRequest model, ITransitService rep) => rep.GetTopology(model))
            .WithOpenApi(operation => new(operation) { Summary = "数据拓扑大屏首页" });

        group.MapPost("/topology/talent", (TopologyDateRequest model, ITransitService rep) => rep.TopologyTalent(model))
            .WithOpenApi(operation => new(operation) { Summary = "数据拓扑-人才线索池" });

        group.MapPost("/topology/project", (TopologyDateRequest model, ITransitService rep) => rep.TopologyProject(model))
            .WithOpenApi(operation => new(operation) { Summary = "数据拓扑-项目信息" });

        group.MapPost("/datascreenexpansion/contractamount", (List<GetContractAmountBySponsorsRequest> Sponsors, ITransitService rep) =>
        {
            DataScreenExpansion_ContractAmount result = new();
            result.ContractAmount = rep.Noah_GetContractAmountBySponsors(Sponsors);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "诺聘平台数据拓扑_从数字诺亚获取合同金额" });

        group.MapPost("/datascreenexpansion/contractamountbyent", (List<string> EntName, ITransitService rep) =>
        {
            DataScreenExpansion_ContractAmount result = new();
            result.ContractAmount = rep.Noah_GetContractAmountByEnt(EntName);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "诺聘平台数据拓扑_从数字诺亚获取合同金额" });

        group.MapPost("/datascreenexpansion/contractcountbyent", (List<string> EntName, ITransitService rep) =>
        {
            DataScreenExpansion_ContractCount result = new();
            result.ContractCount = rep.Noah_GetContractCountByEnt(EntName);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "诺聘平台数据拓扑_从数字诺亚获取合同数量" });

        group.MapPost("/datascreenexpansion/havecontractentcount", (List<string> EntName, ITransitService rep) =>
        {
            DataScreenExpansion_ContractCount result = new();
            result.ContractCount = rep.Noah_GetHaveContractEntCount(EntName);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "诺聘平台数据拓扑_从数字诺亚获取有合同的企业数量" });

        group.MapPost("/datascreenexpansion/havenewcontractentcount", (TopologyDateRequest_ForEntNames Data, ITransitService rep) =>
        {
            DataScreenExpansion_ContractCount result = new();
            result.ContractCount = rep.Noah_GetHaveNewContractEntCount(Data.EntName, Data.BeginTime);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "诺聘平台数据拓扑_从数字诺亚获取有新签合同的企业数量" });

        group.MapPost("/datascreenexpansion/opportunitycount", (List<string> EntName, ITransitService rep) =>
        {
            DataScreenExpansion_OpportunityCount result = new();
            result.OpportunityCount = rep.Noah_GetOpportunityCount(EntName);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "诺聘平台数据拓扑_从数字诺亚获取销售机会企业数量" });

        group.MapPost("/datascreenexpansion/recruitopportunity", (List<string> EntName, ITransitService rep) =>
        {
            DataScreenExpansion_RecruitOpportunity result = new();
            result.ChartData = rep.Noah_GetRecruitOpportunity(EntName);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "诺聘平台数据拓扑_从数字诺亚获取招聘需求线索" });

        group.MapPost("/datascreenexpansion/operatingpro", (List<string> EntName, ITransitService rep) =>
        {
            DataScreenExpansion_RecruitOpportunity result = new();
            result.ChartData = rep.Noah_GetOperatingPro(EntName);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "诺聘平台数据拓扑_从数字诺亚获取销售产品分类" });

        group.MapPost("/datascreenexpansion/contractamountbydepart", (DataScreenExpansion_TimeParam_Depart Time, ITransitService rep) =>
        {
            DataScreenExpansion_ContractAmount result = new();
            result.ContractAmount = rep.Noah_GetContractAmountByDepart(Time.DepartName, Time.BeginTime, Time.EndTime);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "获取部门营收" });

        group.MapPost("/datascreenexpansion/contractamountbypro", (DataScreenExpansion_TimeParam Time, ITransitService rep) =>
        {
            List<DataScreenExpansion_TimeDetail> result = rep.Noah_GetContractAmountByPro(Time.BeginTime, Time.EndTime);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "获取产品收入" });

        group.MapPost("/datascreenexpansion/contractamountbytime", (DataScreenExpansion_TimeParam Time, ITransitService rep) =>
        {
            DataScreenExpansion_ContractAmount result = new();
            result.ContractAmount = rep.Noah_GetContractAmount(Time.BeginTime, Time.EndTime);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "获取产品收入" });

        group.MapPost("/datascreenexpansion/contractamountbynyk", (DataScreenExpansion_TimeParam Time, ITransitService rep) =>
        {
            DataScreenExpansion_ContractAmount result = new();
            result.ContractAmount = rep.Noah_GetNYKContractAmount(Time.BeginTime, Time.EndTime);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "获取诺优考合同金额" });

        group.MapPost("/datascreene/nkp/projectsummary", (NkpBaobiaoRequest model, CommonNKPjingyingbaobiao rep) =>
        {
            var result = rep.GetProjectSummary(model.StartDate, model.EndDate);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "数据大屏 - 诺快聘项目信息汇总" });

        group.MapPost("/datascreene/nkp/usersummary", (NkpBaobiaoRequest model, CommonNKPjingyingbaobiao rep) =>
        {
            var result = rep.GetUserSummary(model.StartDate, model.EndDate);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "数据大屏 - 诺快聘用户信息汇总" });

        group.MapPost("/datascreene/nkp/deliverysummary", (NkpBaobiaoRequest model, CommonNKPjingyingbaobiao rep) =>
        {
            var result = rep.GetDeliverySummary(model.StartDate, model.EndDate);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "数据大屏 - 诺快聘简历投递信息汇总" });

        group.MapPost("/datascreene/nkp/zhibodeliverysummary", (NkpBaobiaoRequest model, CommonNKPjingyingbaobiao rep) =>
        {
            var result = rep.GetZhiboDeliverySummary(model.StartDate, model.EndDate);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "数据大屏 - 快手直播简历投递信息汇总" });

        group.MapPost("/datascreene/nkp/hrtalent", (HrTalentRequest model, CommonNKPjingyingbaobiao rep) =>
        {
            var result = rep.GetHrTalentSummary(model);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "数据大屏 - 顾问人才库信息汇总" });

        group.MapPost("/datascreene/nkp/zhibodeliverypercent", (NkpBaobiaoRequest model, CommonNKPjingyingbaobiao rep) =>
        {
            var result = rep.GetZhiboDeliveryPercentSummary(model.StartDate, model.EndDate);
            return result;
        }).WithOpenApi(operation => new(operation) { Summary = "数据大屏 - 快手直播简历投递简历完整度汇总" });
    }
}
