using System.Net;
using Config.CommonModel;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using ServiceStack.Text;

namespace Infrastructure.Middleware;

public class ExceptionHandler
{
    private readonly RequestDelegate _next;
    private readonly LogManager _logger;
    private readonly IHostEnvironment _hostEnvironment;

    public ExceptionHandler(RequestDelegate next, LogManager logger, IHostEnvironment hostEnvironment)
    {
        _next = next;
        _logger = logger;
        _hostEnvironment = hostEnvironment;
    }

    public async Task Invoke(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception ex)
    {
        if (ex == null) return;
        await WriteExceptionAsync(context, ex).ConfigureAwait(false);
    }

    private async Task WriteExceptionAsync(HttpContext context, Exception e)
    {
        var errorCode = string.Empty;
        var status = HttpStatusCode.InternalServerError;

        if (e is BadHttpRequestException)
        {
            status = HttpStatusCode.BadRequest;
        }
        else if (e is NotFoundException)
        {
            status = HttpStatusCode.NotFound;
            errorCode = ((CustomException)e).ErrorCode;
        }
        else if (e is BadRequestException)
        {
            status = HttpStatusCode.BadRequest;
            errorCode = ((CustomException)e).ErrorCode;
        }
        else if (e is ForbiddenException)
        {
            status = HttpStatusCode.Forbidden;
            errorCode = ((CustomException)e).ErrorCode;
        }
        else if (e is UnauthorizedException)
        {
            status = HttpStatusCode.Unauthorized;
            errorCode = ((CustomException)e).ErrorCode;
        }
        else if (e is NotAcceptableException)
        {
            status = HttpStatusCode.NotAcceptable;
            errorCode = ((CustomException)e).ErrorCode;
        }

        var para = RequestParams.GetParams(context);

        if (status == HttpStatusCode.InternalServerError && e is not InternalServerException)
            _logger.Error(context.Request.Path, Tools.GetErrMsg(e), para);

        var showErr = e.Message;

        if (e is DbUpdateConcurrencyException)
            showErr = "操作太快了，休息一下再试";

        if (status == HttpStatusCode.InternalServerError && _hostEnvironment.IsProduction())
            showErr = "出错啦，等会再试试看吧";

        context.Response.ContentType = "application/json";
        context.Response.StatusCode = (int)status;

        var jm = new ErrorType();
        jm.code = errorCode;
        jm.msg = showErr;
        await context.Response.WriteAsync(JsonSerializer.SerializeToString(jm)).ConfigureAwait(false);
    }
}
