// using Config.CommonModel;
// using Config.Enums;

// namespace Staffing.Model.CentreService.NScore;

// public class GetScoreRecord : QueryRequest
// {
//     /// <summary>
//     /// hr还是求职者
//     /// </summary>
//     public SeekerOrHr UserType { get; set; } = SeekerOrHr.Seeker;

//     /// <summary>
//     /// 类型
//     /// </summary>
//     public NScoreType? Type { get; set; }

//     /// <summary>
//     /// 用户姓名
//     /// </summary>
//     public string? UserName { get; set; }

//     /// <summary>
//     /// 用户手机
//     /// </summary>
//     public string? Mobile { get; set; }

//     /// <summary>
//     /// 起始时间
//     /// </summary>
//     public DateTime? BeginTime { get; set; }

//     /// <summary>
//     /// 结束时间
//     /// </summary>
//     public DateTime? EndTime { get; set; }
// }

// public class GetScoreRecordResponse : QueryResponse
// {
//     /// <summary>
//     /// 积分记录
//     /// </summary>
//     public List<ScoreRecordInfo>? Rows { get; set; }
// }

// public class ScoreRecordInfo
// {
//     /// <summary>
//     /// 用户姓名
//     /// </summary>
//     public string? UserName { get; set; }

//     /// <summary>
//     /// 用户手机
//     /// </summary>
//     public string? Mobile { get; set; }

//     /// <summary>
//     /// 当前值
//     /// </summary>
//     public int Amount { get; set; }

//     /// <summary>
//     /// 增量
//     /// </summary>
//     public int Increment { get; set; }

//     /// <summary>
//     /// hr还是求职者
//     /// </summary>
//     public SeekerOrHr UserType { get; set; }

//     /// <summary>
//     /// 类型
//     /// </summary>
//     public NScoreType Type { get; set; }

//     /// <summary>
//     /// 类型名称
//     /// </summary>
//     public string? TypeName { get; set; }

//     /// <summary>
//     /// 内容
//     /// </summary>
//     public string? Content { get; set; }

//     /// <summary>
//     /// 发生时间
//     /// </summary>
//     public DateTime EventTime { get; set; } = DateTime.Now;
// }