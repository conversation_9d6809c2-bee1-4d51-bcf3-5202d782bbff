﻿using Config.CommonModel;
using Config.CommonModel.Business;
using Config.Enums;
using Staffing.Model.Hr.Post;

namespace Staffing.Model.Hr.Project;

public class GetProjectHall : QueryRequest
{
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// 尚未协同
    /// </summary>
    public bool NotShareYet { get; set; }

    /// <summary>
    /// 项目名称或编码
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 项目级别
    /// </summary>
    public ProjectLevel? Level { get; set; }

    /// <summary>
    /// 项目类型
    /// </summary>
    public ProjectType? ProjectType { get; set; }

    /// <summary>
    /// 项目行业
    /// </summary>
    public ProjectIndustry? Industry { get; set; }

    /// <summary>
    /// 工作周期类型
    /// </summary>
    public ProjectWorkCycleType? WorkCycleType { get; set; }

    /// <summary>
    /// 结算方式
    /// </summary>
    public ProjectClearingType? ClearingType { get; set; }

    // /// <summary>
    // /// 合作方式
    // /// </summary>
    // public ProjectPaymentNode? PaymentNode { get; set; }

    /// <summary>
    /// 打款方式
    /// </summary>
    public ProjectPaymentType? PaymentType { get; set; }

    /// <summary>
    /// 结算渠道
    /// </summary>
    public ProjectClearingChannel? ClearingChannel { get; set; }
}

public class GetProjectHallResponse : QueryResponse
{
    public List<GetProjectHallDetail> Rows { get; set; } = default!;
}

public class GetProjectHallDetail : GetProjectDetail
{
    /// <summary>
    /// 大厅项目Id
    /// </summary>
    public string? HallProjectId { get; set; }

    /// <summary>
    /// 是否已协同
    /// </summary>
    public bool HasShare { get; set; }

    /// <summary>
    /// 是否内部项目
    /// </summary>
    public bool Internal { get; set; }
    /// <summary>
    /// 是否接单0 否 1是
    /// </summary>
    public RecruitAcceptOrderEnum? AcceptOrder { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public bool? HasDefaultContract { get; set; }
}

public class GetHallPost : QueryRequest
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 职位名称或编码
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// 职位类别
    /// </summary>
    public int? Category { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public List<string>? Citys { get; set; }
}

public class GetHallPostResponse : QueryResponse
{
    public List<GetHallPostDetail> Rows { get; set; } = default!;
}

public class GetHallPostDetail : GetPostDetail
{
    /// <summary>
    /// 面试看板
    /// </summary>
    public PostRecentInterviews InterviewDashboard { get; set; } = new PostRecentInterviews();
}

public class ProjectSync
{
    // /// <summary>
    // /// 大厅的项目Id
    // /// </summary>
    // public string? HallProjectId { get; set; }
    /// <summary>
    /// 项目id
    /// </summary>
    public string? ProjectId { get; set; }
}

public class ProjectSyncResponse
{
    /// <summary>
    /// 协同的项目Id
    /// </summary>
    public string? TeamProjectId { get; set; }
}